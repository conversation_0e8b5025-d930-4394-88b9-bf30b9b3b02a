<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["6"] = {
                name: "Reasoning",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1.  Select the sentence which has no spelling error.</p>",
                    question_hi: "<p>1. Select the sentence which has no spelling error.</p>",
                    options_en: [" <p> I was licking my lips for delicoius ice cream.</p>", " <p> There was deleceous aroma coming from the kitchen.</p>", 
                                " <p>  My mother prepares delicious chocolate cakes.</p>", " <p> Granny said that red apples are very dilicious.</p>"],
                    options_hi: ["<p>I was licking my lips for delicoius ice cream.</p>", "<p>There was deleceous aroma coming from the kitchen.</p>",
                                "<p>My mother prepares delicious chocolate cakes.</p>", "<p>Granny said that red apples are very dilicious.</p>"],
                    solution_en: " <p>1.(c)</p> <p>It can be clearly observed that option c does not have any spelling errors. However, the word ‘delicious’ is incorrectly written in all the other options.</p>",
                    solution_hi: "<p>1.(c)</p>\r\n<p>It can be clearly observed that option \'c\' does not have any spelling errors. However, the word &lsquo;delicious&rsquo; is incorrectly written in all the other options.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p>2. Select the INCORRECTLY spelt word.</p>",
                    question_hi: " <p>2. Select the INCORRECTLY spelt word.</p>",
                    options_en: [" <p> Nuptial</p>", " <p> Decaffenated</p>", 
                                " <p> Supplementary</p>", " <p> Noticeable</p>"],
                    options_hi: [" <p> Nuptial</p>", " <p> Decaffenated</p>",
                                " <p> Supplementary</p>", " <p> Noticeable</p>"],
                    solution_en: " <p>2.(b)</p> <p>Decaffeinated is the correct spelling.</p>",
                    solution_hi: " <p>2.(b)</p> <p>Decaffeinated is the correct spelling.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the option that expresses the given sentence in active voice.</p>\r\n<p>The lock has not yet been repaired by the locksmith.</p>",
                    question_hi: "<p>3. Select the option that expresses the given sentence in active voice.</p>\r\n<p>The lock has not yet been repaired by the locksmith.</p>",
                    options_en: ["<p>The locksmith does not repairs the lock.</p>", "<p>The locksmith has not repaired the lock yet.</p>", 
                                "<p>The locksmith is not repairing the lock yet.</p>", "<p>The locksmith had not repaired the lock till then.</p>"],
                    options_hi: ["<p>The locksmith does not repairs the lock.</p>", "<p>The locksmith has not repaired the lock yet.</p>",
                                "<p>The locksmith is not repairing the lock yet.</p>", "<p>The locksmith had not repaired the lock till then.</p>"],
                    solution_en: "<p>3.(b)</p>\r\n<p>The locksmith does not <span style=\"text-decoration: underline;\">repairs</span> the lock. (Incorrect Verb)</p>\r\n<p>The locksmith has not repaired the lock yet. (Correct)</p>\r\n<p>The locksmith <span style=\"text-decoration: underline;\">is not repairing </span>the lock yet. (Incorrect Tense)</p>\r\n<p>The locksmith <span style=\"text-decoration: underline;\">had not repaired</span> the lock till then. (Incorrect Tense)</p>",
                    solution_hi: "<p>3.(b)</p>\r\n<p>The locksmith does not <span style=\"text-decoration: underline;\">repairs</span> the lock. (Incorrect Verb)</p>\r\n<p>The locksmith has not repaired the lock yet. (Correct)</p>\r\n<p>The locksmith <span style=\"text-decoration: underline;\">is not repairing</span> the lock yet. (Incorrect Tense)</p>\r\n<p>The locksmith <span style=\"text-decoration: underline;\">had not repaired</span> the lock till then. (Incorrect Tense)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate meaning of the given idiom.</p>\r\n<p><strong>A fly on the wall</strong></p>",
                    question_hi: " <p>4. Select the most appropriate meaning of the given idiom.</p> <p>A fly on the wall</p>",
                    options_en: ["<p>A vigilant security guard</p>", "<p>An unperceived observer</p>", 
                                "<p>Very intelligent person</p>", "<p>An unwelcome guest</p>"],
                    options_hi: [" <p> A vigilant security guard</p>", " <p> An unperceived observer</p>",
                                " <p> Very intelligent person</p>", " <p> An unwelcome guest</p>"],
                    solution_en: "<p>4.(b)</p>\r\n<p>A fly on the wall- an unperceived observer.</p>\r\n<p>E.g.- I will love to be a fly on the wall when the admissions officer reads my application.</p>",
                    solution_hi: " <p>4.(b)</p> <p>A fly on the wall- an unperceived observer.</p> <p>E.g.- I will love to be a fly on the wall when the admissions officer reads my application.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate option to substitute the underline segment in the given sentence.</p>\r\n<p>If we do nothing to curb this pollution, I guarantee we will <strong><span style=\"text-decoration: underline;\">face the wall</span></strong> in the future.</p>",
                    question_hi: "<p>5. Select the most appropriate option to substitute the underline segment in the given sentence.</p>\r\n<p>If we do nothing to curb this pollution, I guarantee we will face the wall in the future.</p>",
                    options_en: ["<p>face the wind</p>", "<p>face the music</p>", 
                                "<p>face the dark</p>", "<p>face the stick</p>"],
                    options_hi: ["<p>face the wind</p>", "<p>face the music</p>",
                                "<p>face the dark</p>", "<p>face the stick</p>"],
                    solution_en: "<p>5.(b)</p>\r\n<p>&lsquo;Face the music&rsquo; is an idiom that means to accept the unpleasant consequences of your actions.</p>\r\n<p>Similarly, the given sentence states that we have to accept the unpleasant consequences of pollution.</p>",
                    solution_hi: "<p>5.(b)</p>\r\n<p>&lsquo;Face the music&rsquo; is an idiom that means to accept the unpleasant consequences of your actions.</p>\r\n<p>Similarly, the given sentence states that we have to accept the unpleasant consequences of pollution.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate synonym of the given word.</p>\r\n<p><strong>Amble</strong></p>\n",
                    question_hi: "<p>6. Select the most appropriate synonym of the given word.</p>\r\n<p>Amble</p>",
                    options_en: ["<p>Bustle</p>\n", "<p>Stare</p>\n", 
                                "<p>Wander</p>\n", "<p>Trot</p>\n"],
                    options_hi: ["<p>Bustle</p>", "<p>Stare</p>",
                                "<p>Wander</p>", "<p>Trot</p>"],
                    solution_en: "<p>6.(c)</p>\r\n<p>Amble - to walk at a slow relaxed speed</p>\r\n<p>Wander- to walk somewhere slowly with no particular sense of direction or purpose</p>\r\n<p>Bustle- to move in a busy, noisy, or exciting way</p>\r\n<p>Stare- to look at somebody or something for a long time because you are surprised, shocked, etc.</p>\r\n<p>Trot- to move forward at a speed that is faster than a walk</p>\n",
                    solution_hi: "<p>6.(c)</p>\r\n<p><strong>Amble</strong> to walk at a slow relaxed speed</p>\r\n<p><strong>Wander </strong>- to walk somewhere slowly with no particular sense of direction or purpose</p>\r\n<p><strong>Bustle </strong>- to move in a busy, noisy, or exciting way</p>\r\n<p><strong>Stare </strong>- to look at somebody or something for a long time because you are surprised, shocked, etc.</p>\r\n<p><strong>Trot </strong>- to move forward at a speed that is faster than a walk</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate synonym of the given word.</p>\r\n<p><strong>Benevolence</strong></p>",
                    question_hi: "<p>7. Select the most appropriate synonym of the given word.</p>\r\n<p>Benevolence</p>",
                    options_en: ["<p>Severity</p>", "<p>Malevolence</p>", 
                                "<p>Compassion</p>", "<p>Impassion</p>"],
                    options_hi: ["<p>Severity</p>", "<p>Malevolence</p>",
                                "<p>Compassion</p>", "<p>Impassion</p>"],
                    solution_en: "<p>7.(c)</p>\r\n<p>Benevolence- the quality of being kind and helpful to others</p>\r\n<p>Compassion- understanding or pity for somebody who is suffering</p>\r\n<p>Severity- the quality of being very unkind and unpleasant</p>\r\n<p>Malevolence- the quality of causing harm or evil</p>\r\n<p>Impassion- make passionate.</p>",
                    solution_hi: "<p>7.(c)</p>\r\n<p><strong>Benevolence </strong>- the quality of being kind and helpful to others</p>\r\n<p><strong>Compassion</strong> - understanding or pity for somebody who is suffering</p>\r\n<p><strong>Severity </strong>- the quality of being very unkind and unpleasant</p>\r\n<p><strong>Malevolence </strong>- the quality of causing harm or evil</p>\r\n<p><strong>Impassion</strong> - make passionate.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate option to fill in the blank.</p>\r\n<p>Everyone knows that the future of all life on earth will be _________ if people don&rsquo;t control the contamination of the environment.</p>",
                    question_hi: "<p>8. Select the most appropriate option to fill in the blank.</p>\r\n<p>Everyone knows that the future of all life on earth will be _________ if people don&rsquo;t control the contamination of the environment.</p>",
                    options_en: ["<p>dangerous</p>", "<p>endangered</p>", 
                                "<p>dangered</p>", "<p>danger</p>"],
                    options_hi: ["<p>dangerous</p>", "<p>endangered</p>",
                                "<p>dangered</p>", "<p>danger</p>"],
                    solution_en: "<p>8.(b)</p>\r\n<p>&lsquo;Endangered&rsquo; means in danger of disappearing from the world. The given sentence states that everyone knows that the future of all life on earth will be endangered if people don&rsquo;t control the contamination of the environment. Hence, &lsquo;endangered&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(b)</p>\r\n<p>&lsquo;Endangered&rsquo; means in danger of disappearing from the world. The given sentence states that everyone knows that the future of all life on earth will be endangered if people don&rsquo;t control the contamination of the environment. Hence, &lsquo;endangered&rsquo; is the most appropriate answer.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the option that will improve the underlined part of the given sentence.</p>\r\n<p>Do <strong><span style=\"text-decoration: underline;\">you mind me to being</span></strong> here while you are working?</p>",
                    question_hi: "<p>9. Select the option that will improve the underlined part of the given sentence.</p>\r\n<p>Do <span style=\"text-decoration: underline;\"><strong>you mind me to being</strong></span> here while you are working?</p>",
                    options_en: ["<p>you can mind me to be</p>", "<p>you minds me to be</p>", 
                                "<p>you mind me being</p>", "<p>you will mind me to being</p>"],
                    options_hi: ["<p>you can mind me to be</p>", "<p>you minds me to be</p>",
                                "<p>you mind me being</p>", "<p>you will mind me to being</p>"],
                    solution_en: "<p>9.(c)</p>\r\n<p>The given sentence needs a gerund(V-ing) and not an infinitive(preposition to + V1). Hence, &lsquo;you mind me being(V-ing)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(c)</p>\r\n<p>The given sentence needs a gerund(V-ing) and not an infinitive(preposition to + V1). Hence, &lsquo;you mind me being(V-ing)&rsquo; is the most appropriate answer.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate synonym of the given word.</p>\r\n<p><strong>Renounce</strong></p>",
                    question_hi: "<p>10. Select the most appropriate synonym of the given word.</p>\r\n<p>Renounce</p>",
                    options_en: ["<p>Condone</p>", "<p>Hamper</p>", 
                                "<p>Prevent</p>", "<p>Forsake</p>"],
                    options_hi: ["<p>Condone</p>", "<p>Hamper</p>",
                                "<p>Prevent</p>", "<p>Forsake</p>"],
                    solution_en: "<p>10.(d)</p>\r\n<p>Renounce- to say formally that you no longer want to have something or to be connected with something</p>\r\n<p>Forsake- to leave a person or a place forever</p>\r\n<p>Condone- to accept or agree with something that most people think is wrong</p>\r\n<p>Hamper- to make something difficult</p>\r\n<p>Prevent- to stop something happening or to stop somebody doing something</p>",
                    solution_hi: "<p>10.(d)</p>\r\n<p><strong>Renounce</strong>- to say formally that you no longer want to have something or to be connected with something</p>\r\n<p><strong>Forsake</strong>- to leave a person or a place forever</p>\r\n<p><strong>Condone</strong>- to accept or agree with something that most people think is wrong</p>\r\n<p><strong>Hamper</strong>- to make something difficult</p>\r\n<p><strong>Prevent</strong>- to stop something happening or to stop somebody doing something</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the option that will improve the underlined part of the given sentence.</p>\r\n<p>Peter is studying hard <strong><span style=\"text-decoration: underline;\">to have score</span></strong> in the exams.</p>",
                    question_hi: "<p>11. Select the option that will improve the underlined part of the given sentence.</p>\r\n<p>Peter is studying hard <span style=\"text-decoration: underline;\"><strong>to have score</strong></span> in the exams.</p>",
                    options_en: ["<p>to scoring</p>", "<p>to have scored</p>", 
                                "<p>to score</p>", "<p>to scores</p>"],
                    options_hi: ["<p>to scoring</p>", "<p>to have scored</p>",
                                "<p>to score</p>", "<p>to scores</p>"],
                    solution_en: "<p>11.(c)</p>\r\n<p>The given sentence needs an infinitive(preposition to + V1) so the verb &lsquo;have&rsquo; will be removed from the sentence. Hence, &lsquo;to score(V1)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(c)</p>\r\n<p>The given sentence needs an infinitive(preposition to + V1) so the verb &lsquo;have&rsquo; will be removed from the sentence. Hence, &lsquo;to score(V1)&rsquo; is the most appropriate answer.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.</p>\r\n<p>Each member / of the team / receive a memorabilia / after the match is over.</p>",
                    question_hi: "<p>12. The following sentence has been split into four segments. Identify the segment that</p>\r\n<p>contains a grammatical error.</p>\r\n<p>Each member / of the team / receive a memorabilia / after the match is over.</p>",
                    options_en: ["<p>of the team</p>", "<p>Each member</p>", 
                                "<p>receive a memorabilia</p>", "<p>after the match is over</p>"],
                    options_hi: ["<p>of the team</p>", "<p>Each member</p>",
                                "<p>receive a memorabilia</p>", "<p>after the match is over</p>"],
                    solution_en: "<p>12.(c)</p>\r\n<p>According to the <span style=\"text-decoration: underline;\">&ldquo;Subject-Verb Agreement Rule&rdquo;</span>, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;member&rsquo; is a singular subject that will take &lsquo;receives&rsquo; as a singular verb. Hence, &lsquo;receives a memorabilia&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(c)</p>\r\n<p>According to the <span style=\"text-decoration: underline;\">&ldquo;Subject-Verb Agreement Rule&rdquo;</span>, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;member&rsquo; is a singular subject that will take &lsquo;receives&rsquo; as a singular verb. Hence, &lsquo;receives a memorabilia&rsquo; is the most appropriate answer.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13. Select the most appropriate option to fill in the blanks.</p> <p>Besides being ______ multifaceted artist, he was _____ poet, novelist, and a painter.</p>",
                    question_hi: " <p>13. Select the most appropriate option to fill in the blanks.</p> <p>Besides being ______ multifaceted artist, he was _____ poet, novelist, and a painter.</p>",
                    options_en: [" <p> a, an</p>", " <p> an, an</p>", 
                                " <p> a, a</p>", " <p> an, the</p>"],
                    options_hi: [" <p> a, an</p>", " <p> an, an</p>",
                                " <p> a, a</p>", " <p> an, the</p>"],
                    solution_en: " <p>13.(c)</p> <p>The ‘artist & poet’ mentioned in the given sentence are not specific and we generally use the indefinite article ‘a’ before non–specific nouns. Hence, ‘a, a’ is the most appropriate answer.</p>",
                    solution_hi: " <p>13.(c)</p> <p>The ‘artist & poet’ mentioned in the given sentence are not specific and we generally use the indefinite article ‘a’ before non–specific nouns. Hence, ‘a, a’ is the most appropriate answer.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14. Select the INCORRECTLY spelt word.</p>",
                    question_hi: " <p>14. Select the INCORRECTLY spelt word.</p>",
                    options_en: [" <p> Xylophone</p>", " <p> Oligopoly</p>", 
                                " <p> Robbust</p>", " <p> Zenith</p>"],
                    options_hi: [" <p> Xylophone</p>", " <p> Oligopoly</p>",
                                " <p> Robbust</p>", " <p> Zenith</p>"],
                    solution_en: " <p>14.(c)</p> <p>Robust is the correct spelling. </p>",
                    solution_hi: " <p>14.(c)</p> <p>Robust is the correct spelling. </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.</p>\r\n<p>Even though / we are quite different from each other / in our food preferences, / we both loves South Indian delicacies.</p>",
                    question_hi: "<p>15. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.</p>\r\n<p>Even though / we are quite different from each other / in our food preferences, / we both loves South Indian delicacies.</p>",
                    options_en: ["<p>Even though</p>", "<p>we are quite different from each other</p>", 
                                "<p>we both loves South Indian delicacies</p>", "<p>in our food preferences</p>"],
                    options_hi: ["<p>Even though</p>", "<p>we are quite different from each other</p>",
                                "<p>we both loves South Indian delicacies</p>", "<p>in our food preferences</p>"],
                    solution_en: "<p>15.(c)</p>\r\n<p>According to the <span style=\"text-decoration: underline;\">&ldquo;Subject-Verb Agreement Rule&rdquo;</span>, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;we&rsquo; is a plural subject that will take &lsquo;love&rsquo; as a plural verb. Hence, &lsquo;we both love South Indian delicacies&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(c)</p>\r\n<p>According to the <span style=\"text-decoration: underline;\">&ldquo;Subject-Verb Agreement Rule&rdquo;</span>, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;we&rsquo; is a plural subject that will take &lsquo;love&rsquo; as a plural verb. Hence, &lsquo;we both love South Indian delicacies&rsquo; is the most appropriate answer.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: " <p>16. Select the INCORRECTLY spelt word.</p>",
                    question_hi: " <p>16. Select the INCORRECTLY spelt word.</p>",
                    options_en: [" <p> Nosy</p>", " <p> Vennison</p>", 
                                " <p> Yearn</p>", " <p> Ruminate</p>"],
                    options_hi: [" <p> Nosy</p>", " <p> Vennison</p>",
                                " <p> Yearn</p>", " <p> Ruminate</p>"],
                    solution_en: " <p>16.(b)</p> <p>Venison is the correct spelling.</p>",
                    solution_hi: " <p>16.(b)</p> <p>Venison is the correct spelling.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.</p>\r\n<p>A. Because the educational institution\'s teaching staff is actively involved.</p>\r\n<p>B. Curricular or academic activities refer to activities that cover the specified courses of study.</p>\r\n<p>C. These exercises are an essential component of the overall educational curriculum.</p>\r\n<p>D. In layman\'s terms, &lsquo;curricular activities&rsquo; are activities that take place within the classroom.</p>",
                    question_hi: "<p>17. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.</p>\r\n<p>A. Because the educational institution\'s teaching staff is actively involved.</p>\r\n<p>B. Curricular or academic activities refer to activities that cover the specified courses of study.</p>\r\n<p>C. These exercises are an essential component of the overall educational curriculum.</p>\r\n<p>D. In layman\'s terms, &lsquo;curricular activities&rsquo; are activities that take place within the classroom.</p>",
                    options_en: ["<p>CDBA</p>", "<p>BDCA</p>", 
                                "<p>DABC</p>", "<p>DBCA</p>"],
                    options_hi: ["<p>CDBA</p>", "<p>BDCA</p>",
                                "<p>DABC</p>", "<p>DBCA</p>"],
                    solution_en: "<p>17.(b)</p>\r\n<p>Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Curricular or academic activities. However, Sentence D states that &lsquo;curricular activities&rsquo; are activities that take place within the classroom. So, D will follow B. Further, Sentence C states that these exercises are an essential component of the overall educational curriculum and sentence A states that the educational institution\'s teaching staff is actively involved. So, A will follow C. Going through the options, option b has the correct sequence.</p>",
                    solution_hi: "<p>17.(b)</p>\r\n<p>Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Curricular or academic activities. However, Sentence D states that &lsquo;curricular activities&rsquo; are activities that take place within the classroom. So, D will follow B. Further, Sentence C states that these exercises are an essential component of the overall educational curriculum and sentence A states that the educational institution\'s teaching staff is actively involved. So, A will follow C. Going through the options, option b has the correct sequence.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18. Select the most appropriated option to fill in the blank.</p> <p>The __________ crowd gave their newly elected leader a magnificent welcome.</p>",
                    question_hi: " <p>18. Select the most appropriated option to fill in the blank.</p> <p>The __________ crowd gave their newly elected leader a magnificent welcome.</p>",
                    options_en: [" <p> noisy</p>", " <p> jubilant</p>", 
                                " <p> troublesome</p>", " <p>melancholic</p>"],
                    options_hi: [" <p>noisy</p>", " <p>jubilant</p>",
                                " <p> troublesome</p>", " <p>melancholic</p>"],
                    solution_en: " <p>18.(b)</p> <p>‘Jubilant’ means extremely happy, especially because of success. The given sentence states that the jubilant crowd gave their newly elected leader a magnificent welcome. Hence, ‘jubilant’ is the most appropriate answer.</p>",
                    solution_hi: " <p>18.(b)</p> <p>‘Jubilant’ means extremely happy, especially because of success. The given sentence states that the jubilant crowd gave their newly elected leader a magnificent welcome. Hence, ‘jubilant’ is the most appropriate answer.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate synonym of the highlighted word.</p>\r\n<p>The <span style=\"text-decoration: underline;\"><strong>equivocal</strong></span> nature of his comments was criticised by the officials.</p>",
                    question_hi: "<p>19. Select the most appropriate synonym of the highlighted word.</p>\r\n<p>The equivocal nature of his comments was criticised by the officials.</p>",
                    options_en: ["<p>certain</p>", "<p>ambiguous</p>", 
                                "<p>manageable</p>", "<p>vicious</p>"],
                    options_hi: ["<p>certain</p>", "<p>ambiguous</p>",
                                "<p>manageable</p>", "<p>vicious</p>"],
                    solution_en: "<p>19.(b)</p>\r\n<p>Equivocal- able to be understood in more than one way</p>\r\n<p>Ambiguous- having more than one possible meaning</p>\r\n<p>Certain- sure to happen or to do something</p>\r\n<p>Manageable- not too big or too difficult to deal with</p>\r\n<p>Vicious- done in order to hurt somebody/something</p>",
                    solution_hi: "<p>19.(b)</p>\r\n<p><strong>Equivocal</strong>- able to be understood in more than one way</p>\r\n<p><strong>Ambiguous</strong>- having more than one possible meaning</p>\r\n<p><strong>Certain</strong>- sure to happen or to do something</p>\r\n<p><strong>Manageable</strong>- not too big or too difficult to deal with</p>\r\n<p><strong>Vicious</strong>- done in order to hurt somebody/something</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20. Select the most appropriate meaning of the given idiom.</p> <p>In the same boat</p>",
                    question_hi: " <p>20. Select the most appropriate meaning of the given idiom.</p> <p>In the same boat</p>",
                    options_en: [" <p> To go in the same direction as someone else</p>", " <p> To be in the same difficult situation as someone else</p>", 
                                " <p> To share profits with someone else</p>", " <p> To gain undue advantage with others</p>"],
                    options_hi: [" <p> To go in the same direction as someone else</p>", " <p> To be in the same difficult situation as someone else</p>",
                                " <p> To share profits with someone else</p>", " <p> To gain undue advantage with others</p>"],
                    solution_en: " <p>20.(b)</p> <p>In the same boat- to be in the same difficult situation as someone else.</p> <p>E.g.- Neeraj & Deepak are in the same boat as they both lost money in the share market.</p>",
                    solution_hi: " <p>20.(b)</p> <p>In the same boat- to be in the same difficult situation as someone else.</p> <p>E.g.- Neeraj & Deepak are in the same boat as they both lost money in the share market.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Cloze Test:</p>\r\n<p>In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</p>\r\n<p>Human beings have three basic needs: food, shelter, and (1)______. All of these requirements can be met satisfactorily only if an individual has money, and for earning this money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______, there are a lot of people throughout the globe who have been unable to obtain a job. As a direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term used to describe this condition of joblessness.</p>\r\n<p>Select the most appropriate option to fill in blank No.1.</p>",
                    question_hi: " <p>21. Cloze Test:</p> <p>In the following passage, some words have been deleted. Fill in the blanks with the help of the</p> <p>options given. Select the most appropriate option for each blank.</p> <p>Human beings have three basic needs: food, shelter, and (1)______. All of these</p> <p>requirements can be met satisfactorily only if an individual has money, and for earning this</p> <p>money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______,</p> <p>there are a lot of people throughout the globe who have been unable to obtain a job. As a</p> <p>direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term</p> <p>used to describe this condition of joblessness.</p> <p>Select the most appropriate option to fill in blank No.1.</p>",
                    options_en: ["<p>power</p>", "<p>water</p>", 
                                "<p>clothing</p>", "<p>money</p>"],
                    options_hi: [" <p> power</p>", " <p> water</p>",
                                " <p> clothing</p>", " <p> money</p>"],
                    solution_en: "<p>21.(c)</p>\r\n<p>&lsquo;Clothing&rsquo; means the clothes that you wear. The given passage states that human beings have three basic needs: food, shelter, and clothing. Hence, &lsquo;clothing&rsquo; is the most appropriate answer.</p>",
                    solution_hi: " <p>21.(c)</p> <p>‘Clothing’ means the clothes that you wear. The given passage states that human beings have three basic needs: food, shelter, and clothing. Hence, ‘clothing’ is the most appropriate answer.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. Cloze Test:</p>\r\n<p>In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</p>\r\n<p>Human beings have three basic needs: food, shelter, and (1)______. All of these requirements can be met satisfactorily only if an individual has money, and for earning this money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______, there are a lot of people throughout the globe who have been unable to obtain a job. As a direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term used to describe this condition of joblessness.</p>\r\n<p>Select the most appropriate option to fill in blank No.2.</p>",
                    question_hi: " <p>22. Cloze Test:</p> <p>In the following passage, some words have been deleted. Fill in the blanks with the help of the</p> <p>options given. Select the most appropriate option for each blank.</p> <p>Human beings have three basic needs: food, shelter, and (1)______. All of these</p> <p>requirements can be met satisfactorily only if an individual has money, and for earning this</p> <p>money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______,</p> <p>there are a lot of people throughout the globe who have been unable to obtain a job. As a</p> <p>direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term</p> <p>used to describe this condition of joblessness.</p> <p>Select the most appropriate option to fill in blank No.2.</p>",
                    options_en: ["<p>suitable</p>", "<p>authoritative</p>", 
                                "<p>decent</p>", "<p>temporary</p>"],
                    options_hi: [" <p> suitable</p>", " <p> authoritative</p>",
                                " <p> decent</p>", " <p> temporary</p>"],
                    solution_en: "<p>22.(a)</p>\r\n<p>&lsquo;Suitable&rsquo; means right or appropriate for somebody/something. The given passage states that for earning this money, the individual must be employed, i.e, have a suitable profession. Hence, &lsquo;suitable&rsquo; is the most appropriate answer.</p>",
                    solution_hi: " <p>22.(a)</p> <p>‘Suitable’ means right or appropriate for somebody/something. The given passage states that for earning this money, the individual must be employed, i.e, have a suitable profession. Hence, ‘suitable’ is the most appropriate answer.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. Cloze Test:</p>\r\n<p>In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</p>\r\n<p>Human beings have three basic needs: food, shelter, and (1)______. All of these requirements can be met satisfactorily only if an individual has money, and for earning this money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______, there are a lot of people throughout the globe who have been unable to obtain a job. As a direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term used to describe this condition of joblessness.</p>\r\n<p>Select the most appropriate option to fill in blank No.3.</p>",
                    question_hi: " <p>23. Cloze Test:</p> <p>In the following passage, some words have been deleted. Fill in the blanks with the help of the</p> <p>options given. Select the most appropriate option for each blank.</p> <p>Human beings have three basic needs: food, shelter, and (1)______. All of these</p> <p>requirements can be met satisfactorily only if an individual has money, and for earning this</p> <p>money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______,</p> <p>there are a lot of people throughout the globe who have been unable to obtain a job. As a</p> <p>direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term</p> <p>used to describe this condition of joblessness.</p> <p>Select the most appropriate option to fill in blank No.3.</p>",
                    options_en: ["<p>Rightfully</p>", "<p>Regressively</p>", 
                                "<p>Repressively</p>", "<p>Regrettably</p>"],
                    options_hi: [" <p> Rightfully</p>", " <p> Regressively</p>",
                                " <p> Repressively</p>", " <p> Regrettably</p>"],
                    solution_en: "<p>23.(d)</p>\r\n<p>&lsquo;Regrettably&rsquo; means in a way that makes you feel sad and sorry about something. The given passage states that regrettably there are a lot of people throughout the globe who have been unable to obtain a job. Hence, &lsquo;regrettably&rsquo; is the most appropriate answer.</p>",
                    solution_hi: " <p>23.(d)</p> <p>‘Regrettably’ means in a way that makes you feel sad and sorry about something. The given passage states that regrettably there are a lot of people throughout the globe who have been unable to obtain a job. Hence, ‘regrettably’ is the most appropriate answer.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Cloze Test:</p>\r\n<p>In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</p>\r\n<p>Human beings have three basic needs: food, shelter, and (1)______. All of these requirements can be met satisfactorily only if an individual has money, and for earning this money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______, there are a lot of people throughout the globe who have been unable to obtain a job. As a direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term used to describe this condition of joblessness.</p>\r\n<p>Select the most appropriate option to fill in blank No.4.</p>",
                    question_hi: "<p>24. Cloze Test:</p>\r\n<p>In the following passage, some words have been deleted. Fill in the blanks with the help of the</p>\r\n<p>options given. Select the most appropriate option for each blank.</p>\r\n<p>Human beings have three basic needs: food, shelter, and (1)______. All of these</p>\r\n<p>requirements can be met satisfactorily only if an individual has money, and for earning this</p>\r\n<p>money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______,</p>\r\n<p>there are a lot of people throughout the globe who have been unable to obtain a job. As a</p>\r\n<p>direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term</p>\r\n<p>used to describe this condition of joblessness.</p>\r\n<p>Select the most appropriate option to fill in blank No.4.</p>",
                    options_en: ["<p>attack</p>", "<p>consequence</p>", 
                                "<p>sequence</p>", "<p>confrontation</p>"],
                    options_hi: ["<p>attack</p>", "<p>consequence</p>",
                                "<p>sequence</p>", "<p>confrontation</p>"],
                    solution_en: "<p>24.(b)</p>\r\n<p>&lsquo;Consequence&rsquo; means something that happens or follows as a result of something else. The given passage states that as a direct consequence, they have a meagre amount of income. Hence, consequence&lsquo;&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(b)</p>\r\n<p>&lsquo;Consequence&rsquo; means something that happens or follows as a result of something else. The given passage states that as a direct consequence, they have a meagre amount of income. Hence, consequence&lsquo;&rsquo; is the most appropriate answer.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. Cloze Test:</p>\r\n<p>In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</p>\r\n<p>Human beings have three basic needs: food, shelter, and (1)______. All of these requirements can be met satisfactorily only if an individual has money, and for earning this money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______, there are a lot of people throughout the globe who have been unable to obtain a job. As a direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term used to describe this condition of joblessness.</p>\r\n<p>Select the most appropriate option to fill in blank No.5.</p>",
                    question_hi: " <p>25. Cloze Test:</p> <p>In the following passage, some words have been deleted. Fill in the blanks with the help of the</p> <p>options given. Select the most appropriate option for each blank.</p> <p>Human beings have three basic needs: food, shelter, and (1)______. All of these</p> <p>requirements can be met satisfactorily only if an individual has money, and for earning this</p> <p>money, the individual must be employed, i. e., have a/an (2)______ profession. (3)______,</p> <p>there are a lot of people throughout the globe who have been unable to obtain a job. As a</p> <p>direct (4)______, they have a/an (5)______ amount of income, and unemployment is the term</p> <p>used to describe this condition of joblessness.</p> <p>Select the most appropriate option to fill in blank No.5.</p>",
                    options_en: ["<p>average</p>", "<p>plentiful</p>", 
                                "<p>meagre</p>", "<p>minor</p>"],
                    options_hi: [" <p> average</p>", " <p> plentiful</p>",
                                " <p> meagre</p>", " <p> minor</p>"],
                    solution_en: "<p>25.(c)</p>\r\n<p>&lsquo;Meagre&rsquo; means too small in amount. The given passage states that as a direct consequence, they have a meagre amount of income. Hence, &lsquo;meagre&rsquo; is the most appropriate answer.</p>",
                    solution_hi: " <p>25.(c)</p> <p>‘Meagre’ means too small in amount. The given passage states that as a direct consequence, they have a meagre amount of income. Hence, ‘meagre’ is the most appropriate answer.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. In a certain code language, \'APPROACH\' is written as \'116161815138\' and &lsquo;APPROVAL&rsquo; is written as &lsquo;11616181522112&rsquo;. How will \'POOL\' be written in that language?</p>",
                    question_hi: "<p>26. <span style=\"font-family: Palanquin Dark;\">एक निश्चित कोड भाषा में, \'APPROACH\' को \'116161815138\' और \'APPROVAL\' को \'11616181522112\' लिखा जाता है। उसी भाषा में \'POOL\' कैसे लिखा जाएगा?</span></p>",
                    options_en: ["<p>16141412</p>", "<p>16171712</p>", 
                                "<p>16151512</p>", "<p>16131312</p>"],
                    options_hi: ["<p>16141412</p>", "<p>16171712</p>",
                                "<p>16151512</p>", "<p>16131312</p>"],
                    solution_en: "<p>26.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Logic </strong>: Each letter is replaced by its place value in the alphabet.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">APPROACH </span><span style=\"font-family: Palanquin Dark;\"> 1-16-16-18-15-1-3-8 = 116161815138</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">APPROVAL </span><span style=\"font-family: Palanquin Dark;\"> 1-16-16-18-15-22-1-12 = 11616181522112</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Similarly, POOL </span><span style=\"font-family: Palanquin Dark;\"> 16-15-15-12 = 16151512</span></p>",
                    solution_hi: "<p>26.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">तर्क : वर्णमाला में प्रत्येक अक्षर को उसके स्थानीय मान के रूप में लिखा गया है। </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">APPROACH &rArr;</span><span style=\"font-family: Palanquin Dark;\"> 1-16-16-18-15-1-3-8 = 116161815138</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">APPROVAL &rArr;</span><span style=\"font-family: Palanquin Dark;\"> 1-16-16-18-15-22-1-12 = 11616181522112</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इसी तरह, POOL &rArr;&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> 16-15-15-12 = 16151512</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: "<p>27.<span style=\"font-family: Palanquin Dark;\"> Select the correct combination of mathematical signs to replace the * signs and to </span><span style=\"font-family: Palanquin Dark;\">balance the given equation.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">14 * 5 *41 * 76 * 35&nbsp;</span></p>",
                    question_hi: "<p>27.<span style=\"font-family: Palanquin Dark;\"> * चिह्नों को बदलने और दिए गए समीकरण को संतुलित करने के लिए गणितीय चिह्नों के सही संयोजन का चयन करें।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">14 * 5 *41 * 76 * 35</span></p>",
                    options_en: ["<p>&times;,&minus;,=,+</p>", "<p>&times;,+,&minus;,=</p>", 
                                "<p>&minus;,+,=,&times;</p>", "<p>+,&times;,=,&minus;</p>"],
                    options_hi: ["<p>&times;,&minus;,=,+</p>", "<p>&times;,+,&minus;,=</p>",
                                "<p>&minus;,+,=,&times;</p>", "<p>+,&times;,=,&minus;</p>"],
                    solution_en: "<p>27.(b)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">In this type of question, we will check by putting options one by one and doing so option (b) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">14 * 5 * 41 * 76 * 35</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Putting value of option (b) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">14 <strong>&times;</strong></span><span style=\"font-family: Arial Unicode MS;\">5 + 41 - 76 = 35</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">LHS </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 14 <strong>&times; </strong></span><span style=\"font-family: Arial Unicode MS;\">5 + 41 - 76 </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 70 + 41 - 76 </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 111 - 76</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 35 = RHS</span></p>",
                    solution_hi: "<p>27.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इस प्रकार के प्रश्न में हम एक-एक करके विकल्प की जांच करेंगे और ऐसा करने से विकल्प (b) संतुष्ट हो जाता है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">14 * 5 * 41 * 76 * 35</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">उपरोक्त व्यंजक में विकल्प (b) का मान रखने पर, हमें प्राप्त होता है,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">14 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 5 + 41 - 76 = 35</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">LHS </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 14 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">5 + 41 - 76 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 70 + 41 - 76 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 111 - 76</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 35 = RHS</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p>28. <span style=\"font-family: Palanquin Dark;\">Select the correct combination of mathematical signs to replace the * signs and to </span><span style=\"font-family: Palanquin Dark;\">balance the given equation.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">78*3*12*5*59*17*44</span></p>",
                    question_hi: "<p>28.<span style=\"font-family: Palanquin Dark;\"> * चिह्नों को बदलने और दिए गए समीकरण को संतुलित करने के लिए गणितीय चिह्नों के सही संयोजन का चयन करें।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">78*3*12*5*59*17*44</span></p>",
                    options_en: ["<p>&divide;,+, &times;,&minus;,+,=</p>", "<p>+,=,&minus;,+,&times;,&divide;</p>", 
                                "<p>&divide;,&minus;,=,+,&times;,&minus;</p>", "<p>=,&minus;,&times;,+,&minus;,&times;</p>"],
                    options_hi: ["<p>&divide;,+, &times;,&minus;,+,=</p>", "<p>+,=,&minus;,+,&times;,&divide;</p>",
                                "<p>&divide;,&minus;,=,+,&times;,&minus;</p>", "<p>=,&minus;,&times;,+,&minus;,&times;</p>"],
                    solution_en: "<p>28.(a)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">In this type of question, we will check by putting options one by one and doing so option (a) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">78*3*12*5*59*17*44</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Putting value of option (a) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">78 &divide; </span><span style=\"font-family: Arial Unicode MS;\">3 + 12 &times; </span><span style=\"font-family: Arial Unicode MS;\">5 - 59 + 17 = 44</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">LHS </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 78 &divide; </span><span style=\"font-family: Arial Unicode MS;\">3 + 12 &times; </span><span style=\"font-family: Arial Unicode MS;\">5 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 26 + 12 &times; </span><span style=\"font-family: Arial Unicode MS;\">5 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 26 + 60 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 103 - 59 </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">= 44 = RHS</span></p>",
                    solution_hi: "<p>28.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इस प्रकार के प्रश्न में हम एक-एक करके विकल्प की जांच करेंगे और ऐसा करने से विकल्प (a) संतुष्ट हो जाता है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">78*3*12*5*59*17*44</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">उपरोक्त व्यंजक में विकल्प (a) का मान रखने पर, हमें प्राप्त होता है</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">78 &divide;</span><span style=\"font-family: Palanquin Dark;\"> 3 + 12 &times; </span><span style=\"font-family: Palanquin Dark;\">5 - 59 + 17 = 44</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">LHS </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 78 &divide; </span><span style=\"font-family: Palanquin Dark;\">3 + 12 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">5 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 26 + 12<strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 5 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 26 + 60 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 103 - 59 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 44 = RHS</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: " <p>29. </span><span style=\"font-family:Palanquin Dark\">Which of the following numbers will replace the question mark (?) in the given series?</span></p> <p><span style=\"font-family:Palanquin Dark\">78, 112, 64, ?, 50, 140</span></p>",
                    question_hi: " <p>29. </span><span style=\"font-family:Palanquin Dark\">निम्नलिखित में से कौन सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी ?</span></p> <p><span style=\"font-family:Palanquin Dark\">78, 112, 64, ?, 50, 140</span></p>",
                    options_en: [" <p> 134</span></p>", " <p> 126</span></p>", 
                                " <p> 108</span></p>", " <p> 116</span></p>"],
                    options_hi: [" <p> 134</span></p>", " <p> 126</span></p>",
                                " <p> 108</span></p>", " <p> 116</span></p>"],
                    solution_en: " <p>29.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image8.png\"/></p>",
                    solution_hi: " <p>29.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image8.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "6",
                    question_en: "<p>30. <span style=\"font-family: Palanquin Dark;\">In a certain code language, &ldquo;ARE&rdquo; is coded as &ldquo;87&rdquo;, and &ldquo;NOT&rdquo; is coded as &ldquo;4197&rdquo;. </span><span style=\"font-family: Palanquin Dark;\">How will &ldquo;CAT&rdquo; be coded in that language?</span></p>",
                    question_hi: "<p>30.<span style=\"font-family: Palanquin Dark;\"> &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306;, \"ARE\" &#2325;&#2379; \"87\" &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2324;&#2352; \"NOT\" &#2325;&#2379; \"4197\" &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2360;&#2368; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; &ldquo;CAT&rdquo; &#2325;&#2379; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>54</p>", "<p>60</p>", 
                                "<p>57</p>", "<p>63</p>"],
                    options_hi: ["<p>54</p>\n", "<p>60</p>\n",
                                "<p>57</p>\n", "<p>63</p>\n"],
                    solution_en: "<p>30.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Logic :</strong> Product of numerical position of letters in alphabet less number of letters in given word.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">ARE <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 1<strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 18 <strong>&times;</strong> </span><span style=\"font-family: Palanquin Dark;\">5 = 90 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 90 - 3 = 87</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">NOT <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 14 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\">15 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">20 = 4200 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 4200 - 3 = 4197</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Similarly, CAT <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 3 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">1 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">20 = 60&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 60 - 3 = 57</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    solution_hi: "<p>30.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2352;&#2381;&#2325; : ( &#2358;&#2348;&#2381;&#2342; &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344;&#2368;&#2351; &#2350;&#2366;&#2344; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2366; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354; ) - &#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">ARE <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 1<strong>&times;</strong>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> 18 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 5 = 90<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> 90 - 3 = 87</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">NOT <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 14 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 15<strong>&times;</strong>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> 20 = 4200 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 4200 - 3 = 4197</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2368; &#2340;&#2352;&#2361;, CAT <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 3 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 1<strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\">20 = 60 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 60 - 3 = 57</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "6",
                    question_en: "<p>31. <span style=\"font-family: Palanquin Dark;\">Select the correct combination of mathematical signs to replace the * signs and to </span><span style=\"font-family: Palanquin Dark;\">balance the given equation.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">13 * 5 * 3 * 3 * 2</span></p>",
                    question_hi: "<p>31.<span style=\"font-family: Palanquin Dark;\"> * चिह्नों को बदलने और दिए गए समीकरण को संतुलित करने के लिए गणितीय चिह्नों के सही संयोजन का चयन करें।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">13 * 5 * 3 * 3 * 2</span></p>",
                    options_en: ["<p>&ndash;, =, +, &ndash;</p>", "<p>=, +, +, &times;</p>", 
                                "<p>&times;, =, &times;, &times;</p>", "<p>+, =, &times;, &times;</p>"],
                    options_hi: ["<p>&ndash;, =, +, &ndash;</p>", "<p>=, +, +, &times;</p>",
                                "<p>&times;, =, &times;, &times;</p>", "<p>+, =, &times;, &times;</p>"],
                    solution_en: "<p>31.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">In this type of question, we will check by putting options one by one and doing so option (d) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">13 * 5 * 3 * 3 * 2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Putting value of option (d) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">13 + 5 = 3 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">3 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">LHS = 13 + 5 = 18</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">RHS = 3 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">3 <strong>&times;</strong>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> 2 = 18 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, LHS = RHS</span></p>",
                    solution_hi: "<p>31.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इस प्रकार के प्रश्न में हम एक-एक करके विकल्प रखकर जाँच करेंगे और ऐसा करने से विकल्प (d) संतुष्ट हो जाता है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">13 * 5 * 3 * 3 * 2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">उपरोक्त व्यंजक में विकल्प (d) का मान रखने पर, हमें प्राप्त होता है</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">13 + 5 = 3 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">3 <strong>&times;</strong> </span><span style=\"font-family: Palanquin Dark;\">2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">LHS = 13 + 5 = 18</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">RHS = 3 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 3 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\"> 2 = 18 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, LHS = RHS</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "6",
                    question_en: "<p>32.<span style=\"font-family: Palanquin Dark;\"> Select the option that is related to the third word in the same way as the second word </span><span style=\"font-family: Palanquin Dark;\">is related to the first word. (The words must be considered as meaningful English </span><span style=\"font-family: Palanquin Dark;\">words and must NOT be related to each other based on the number of letters/number </span><span style=\"font-family: Palanquin Dark;\">of consonants/vowels in the word)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Courage : Bravery :: Sinister : ?</span></p>",
                    question_hi: " <p>32. </span><span style=\"font-family:Palanquin Dark\">उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को अर्थपूर्ण अंग्रेजी शब्दों के रूप में माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)</span></p> <p><span style=\"font-family:Palanquin Dark\">साहस : शौर्य :: अशुभ : ?</span></p>",
                    options_en: ["<p>Auspicious</p>", "<p>Benevolent</p>", 
                                "<p>Ominous</p>", "<p>Kind</p>"],
                    options_hi: [" <p> शुभ</span></p>", " <p> परोपकारी</span></p>",
                                " <p> मनहूस </span></p>", " <p> दयालु</span></p>"],
                    solution_en: "<p>32.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Synonym of Courage is Bravery. Similarly, the synonym of Sinister is Ominous.</span></p>",
                    solution_hi: " <p>32.(c)</span></p> <p><span style=\"font-family:Palanquin Dark\">साहस का पर्यायवाची शौर्य है। इसी प्रकार अशुभ (Sinister) का पर्यायवाची शब्द मनहूस (Ominous) होता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "6",
                    question_en: " <p>33.</span><span style=\"font-family:Palanquin Dark\"> A paper is folded and cut as shown. How will it appear when unfolded?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image9.png\"/></p>",
                    question_hi: " <p>33. </span><span style=\"font-family:Palanquin Dark\">एक कागज को मोड़ा जाता है और दिखाए गए अनुसार काटा जाता है। प्रकट होने पर यह कैसे दिखाई देगा?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image9.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image16.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image5.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image2.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image3.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image16.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image5.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image2.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image3.png\"/></p>"],
                    solution_en: " <p>33.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image5.png\"/></p>",
                    solution_hi: " <p>33.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image5.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "6",
                    question_en: "<p>34. <span style=\"font-family: Palanquin Dark;\">Which of the following numbers will replace the question mark (?) in the given number </span><span style=\"font-family: Palanquin Dark;\">series?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">458, 462, 446, 482, ?, 518</span></p>\n",
                    question_hi: " <p>34. </span><span style=\"font-family:Palanquin Dark\">निम्नलिखित में से कौन सी संख्या दी गई संख्या श्रृंखला में प्रश्नवाचक चिह्न (?) का स्थान लेगी?</span></p> <p><span style=\"font-family:Palanquin Dark\">458, 462, 446, 482, ?, 518</span></p>",
                    options_en: ["<p>428</p>\n", "<p>432</p>\n", 
                                "<p>546</p>\n", "<p>418</p>\n"],
                    options_hi: [" <p> 428</span></p>", " <p> 432</span></p>",
                                " <p> 546</span></p>", " <p> 418</span></p>"],
                    solution_en: "<p>34.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_99769322911670387615754.png\" width=\"327\" height=\"131\"></p>\n",
                    solution_hi: " <p>34.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image22.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "6",
                    question_en: "<p>35.<span style=\"font-family: Palanquin Dark;\"> Select the option that represents the correct order of the given words as they would </span><span style=\"font-family: Palanquin Dark;\">appear in an English dictionary.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1.Potential</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2.Posthumous</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3.Postbox</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">4.Polymer</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">5.Popular</span></p>",
                    question_hi: "<p>35. <span style=\"font-family: Palanquin Dark;\">उस विकल्प का चयन करें जो दिए गए शब्दों के सही क्रम का प्रतिनिधित्व करता है जैसा कि वे एक अंग्रेजी शब्दकोश में दिखाई देंगे।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1.Potential</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2.Posthumous</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3.Postbox</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">4.Polymer</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">5.Popular</span></p>",
                    options_en: ["<p>5,4,3,2,1</p>", "<p>4,5,2,3,1</p>", 
                                "<p>4,5,3,2,1</p>", "<p>1,2,3,4,5</p>"],
                    options_hi: ["<p>5,4,3,2,1</p>", "<p>4,5,2,3,1</p>",
                                "<p>4,5,3,2,1</p>", "<p>1,2,3,4,5</p>"],
                    solution_en: "<p>35.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Correct order will be 4,5,3,2,1 :</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Polymer <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> Popular<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> Postbox <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> Posthumous <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> Potential </span></p>",
                    solution_hi: "<p>35.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">सही क्रम होगा:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Polymer &rarr;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;Popular &rarr; </span><span style=\"font-family: Palanquin Dark;\">Postbox &rarr;&nbsp;</span><span style=\"font-family: Palanquin Dark;\">Posthumous &rarr; </span><span style=\"font-family: Palanquin Dark;\">Potential </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "6",
                    question_en: " <p>36. </span><span style=\"font-family:Palanquin Dark\">Select the figure from among the given options that can replace the question mark (?) in the following series.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image17.png\"/></p>",
                    question_hi: " <p>36.</span><span style=\"font-family:Palanquin Dark\"> दिए गए विकल्पों में से उस आकृति का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image17.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image23.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image18.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image12.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image20.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image23.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image18.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image12.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image20.png\"/></p>"],
                    solution_en: " <p>36.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image20.png\"/></p>",
                    solution_hi: " <p>36.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image20.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "6",
                    question_en: "<p>37.<span style=\"font-family: Palanquin Dark;\"> Select the option that represents the correct order of the given words as they would </span><span style=\"font-family: Palanquin Dark;\">appear in an English dictionary.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1.Sourced</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2.Sorrow</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3.Soulful</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">4.Soaking</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">5.Somewhere</span></p>",
                    question_hi: "<p>37.<span style=\"font-family: Palanquin Dark;\"> उस विकल्प का चयन करें जो दिए गए शब्दों के सही क्रम का प्रतिनिधित्व करता है जैसा कि वे एक अंग्रेजी शब्दकोश में दिखाई देंगे।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1.Sourced</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2.Sorrow</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3.Soulful</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">4.Soaking</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">5.Somewhere</span></p>",
                    options_en: ["<p>5,4,3,2,1</p>", "<p>4,5,2,3,1</p>", 
                                "<p>5,4,1,2,3</p>", "<p>4,5,3,2,1</p>"],
                    options_hi: ["<p>5,4,3,2,1</p>", "<p>4,5,2,3,1</p>",
                                "<p>5,4,1,2,3</p>", "<p>4,5,3,2,1</p>"],
                    solution_en: "<p>37.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Correct order will be&nbsp;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Soaking <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> Somewhere <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> Sorrow <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> Soulful <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> Sourced</span></p>",
                    solution_hi: "<p>37.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">सही क्रम होगा:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Soaking &rarr; </span><span style=\"font-family: Palanquin Dark;\">Somewhere &rarr;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;Sorrow &rarr; </span><span style=\"font-family: Palanquin Dark;\">Soulful &rarr; </span><span style=\"font-family: Palanquin Dark;\">Sourced</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "6",
                    question_en: "<p>38.<span style=\"font-family: Palanquin Dark;\"> Select the correct combination of mathematical signs to sequentially replace the * </span><span style=\"font-family: Palanquin Dark;\">signs and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">57*35*44*11*13*40</span></p>",
                    question_hi: "<p>38.<span style=\"font-family: Palanquin Dark;\"> * चिह्नों को क्रमानुसार बदलने और दिए गए समीकरण को संतुलित करने के लिए गणितीय चिह्नों के सही संयोजन का चयन करें।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">57*35*44*11*13*40</span></p>",
                    options_en: ["<p>+,&ndash;, &times;,&divide;, =</p>", "<p>&times;, +, &ndash;,&divide;, =</p>", 
                                "<p>&ndash;,+,&times;,&divide;, =</p>", "<p>+, &ndash;, &divide;,&times;, =</p>"],
                    options_hi: ["<p>+,&ndash;, &times;,&divide;, =</p>", "<p>&times;, +, &ndash;,&divide;, =</p>",
                                "<p>&ndash;,+,&times;,&divide;, =</p>", "<p>+, &ndash;, &divide;,&times;, =</p>"],
                    solution_en: "<p>38.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">In this type of question, we will check by putting options one by one and doing so option (d) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">57*35*44*11*13*40</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Putting value of option (d) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">57 + 35 - 44 &divide; </span><span style=\"font-family: Palanquin Dark;\"> 11 <strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">13 = 40</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">LHS </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 57 + 35 - 44 &divide; </span><span style=\"font-family: Palanquin Dark;\"> 11<strong>&times; </strong></span><span style=\"font-family: Palanquin Dark;\">13 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 57 + 35 - 4 &times;</span><span style=\"font-family: Palanquin Dark;\">13 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 57 + 35 - 52</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 92 - 52</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 40 = RHS </span></p>",
                    solution_hi: "<p>38.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इस प्रकार के प्रश्न में हम एक-एक करके विकल्प रखकर जाँच करेंगे और ऐसा करने से विकल्प (d) संतुष्ट हो जाता है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">57*35*44*11*13*40</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">उपरोक्त व्यंजक में विकल्प (d) का मान रखने पर, हमें प्राप्त होता है</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">57 + 35 - 44 &divide; </span><span style=\"font-family: Palanquin Dark;\"> 11 <strong>&times;</strong> </span><span style=\"font-family: Palanquin Dark;\">13 = 40</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">LHS </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 57 + 35 - 44 &divide; </span><span style=\"font-family: Palanquin Dark;\"> 11 <strong>&times;</strong></span><span style=\"font-family: Palanquin Dark;\">13 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 57 + 35 - 4 </span><span style=\"font-family: Palanquin Dark;\"> 13 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 57 + 35 - 52</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 92 - 52</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 40 = RHS </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "6",
                    question_en: "<p>39. <span style=\"font-family: Palanquin Dark;\">Two statements are given followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.</span></p>\r\n<p><strong><span style=\"font-family: Palanquin Dark;\">Statements:</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Some potatoes are onions.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Some onions are tomatoes.</span></p>\r\n<p><strong><span style=\"font-family: Palanquin Dark;\">Conclusions:</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">I. Some tomatoes are potatoes.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">II. No tomato is a potato.</span></p>\n",
                    question_hi: "<p>39.<span style=\"font-family: Palanquin Dark;\"> &#2342;&#2379; &#2325;&#2341;&#2344;&#2379;&#2306; &#2325;&#2375; &#2348;&#2366;&#2342; &#2342;&#2379; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; I &#2324;&#2352; II &#2342;&#2367;&#2319; &#2327;&#2319; &#2361;&#2376;&#2306;&#2404; &#2325;&#2341;&#2344;&#2379;&#2306; &#2325;&#2379; &#2360;&#2340;&#2381;&#2351; &#2350;&#2366;&#2344;&#2340;&#2375; &#2361;&#2369;&#2319;, &#2349;&#2354;&#2375; &#2361;&#2368; &#2357;&#2375; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; &#2332;&#2381;&#2334;&#2366;&#2340; &#2340;&#2341;&#2381;&#2351;&#2379;&#2306; &#2360;&#2375; &#2349;&#2367;&#2344;&#2381;&#2344; &#2346;&#2381;&#2352;&#2340;&#2368;&#2340; &#2361;&#2379;&#2340;&#2375; &#2361;&#2379;&#2306;, &#2344;&#2367;&#2352;&#2381;&#2339;&#2351; &#2354;&#2375;&#2306; &#2325;&#2367; &#2325;&#2380;&#2344; &#2360;&#2366; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; &#2325;&#2341;&#2344;&#2379;&#2306; &#2325;&#2366; &#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325; &#2352;&#2370;&#2346; &#2360;&#2375; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Palanquin Dark;\">&#2325;&#2341;&#2344;:</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2369;&#2331; &#2310;&#2354;&#2370; , &#2346;&#2381;&#2351;&#2366;&#2332; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2369;&#2331; &#2346;&#2381;&#2351;&#2366;&#2332; , &#2335;&#2350;&#2366;&#2335;&#2352; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;:</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">I. &#2325;&#2369;&#2331; &#2335;&#2350;&#2366;&#2335;&#2352; , &#2310;&#2354;&#2370; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">II. &#2325;&#2379;&#2312; &#2349;&#2368; &#2335;&#2350;&#2366;&#2335;&#2352; , &#2310;&#2354;&#2370; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Only conclusion II follows</p>\n", "<p>Only conclusion I follows</p>\n", 
                                "<p>Either conclusion I or II follows</p>\n", "<p>Both conclusions I and II follow</p>\n"],
                    options_hi: ["<p>&#2325;&#2375;&#2357;&#2354; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; II &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</p>\n", "<p>&#2325;&#2375;&#2357;&#2354; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; I &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</p>\n",
                                "<p>&#2351;&#2366; &#2340;&#2379; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; I &#2351;&#2366; II &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</p>\n", "<p>&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; I &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</p>\n"],
                    solution_en: "<p>39.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_80990073211670387847393.png\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Clearly, we can see that either conclusion 1 or 2 follows.</span></p>\n",
                    solution_hi: "<p>39.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_56311128511670387903264.png\" width=\"280\" height=\"67\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335; &#2352;&#2370;&#2346; &#2360;&#2375;, &#2361;&#2350; &#2342;&#2375;&#2326; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2351;&#2366; &#2340;&#2379; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; 1 &#2351;&#2366; 2 &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "6",
                    question_en: " <p>40.</span><span style=\"font-family:Palanquin Dark\"> Select the correct mirror image of the given combination when the mirror is placed at MN as shown.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image10.png\"/></p>",
                    question_hi: " <p>40.</span><span style=\"font-family:Palanquin Dark\"> दिए गए संयोजन की सही दर्पण छवि का चयन करें जब दर्पण को MN पर दिखाया गया है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image10.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image28.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image34.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image11.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image35.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image28.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image34.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image11.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image35.png\"/></p>"],
                    solution_en: " <p>40.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image35.png\"/></p>",
                    solution_hi: " <p>40.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image35.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "6",
                    question_en: "<p>41.<span style=\"font-family: Palanquin Dark;\"> Which of the following letter-clusters will replace the question mark (?) in the given </span><span style=\"font-family: Palanquin Dark;\">series to make it logically complete?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">XUR, WTQ, VSP, URO, ?</span></p>",
                    question_hi: "<p>41.<span style=\"font-family: Palanquin Dark;\"> निम्नलिखित में से कौन सा अक्षर-समूह दी गई श्रृंखला में प्रश्न चिह्न (?) को तार्किक रूप से पूर्ण बनाने के लिए प्रतिस्थापित करेगा ?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">XUR , WTQ , VSP , URO ,&nbsp; ?</span></p>",
                    options_en: ["<p>TRP</p>", "<p>TQN</p>", 
                                "<p>SQO</p>", "<p>SPM</p>"],
                    options_hi: ["<p>TRP</p>", "<p>TQN</p>",
                                "<p>SQO</p>", "<p>SPM</p>"],
                    solution_en: "<p>41.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">For first letter of each word : X - 1 = W, W - 1 = V, V - 1 = U, U - 1 = </span><span style=\"font-family: Palanquin Dark;\">T</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">For second letter of each word : U - 1 = T, T - 1 = S, S - 1 = R, R - 1 = </span><span style=\"font-family: Palanquin Dark;\">Q</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">For third letter of each word ; R - 1 = Q, Q - 1 = P, P - 1 = O, O - 1 = </span><span style=\"font-family: Palanquin Dark;\">N</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Hence, we get TQN.</span></p>",
                    solution_hi: "<p>41.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रत्येक शब्द के पहले अक्षर के लिए :- X - 1 = W, W - 1 = V, V - 1 = U, U - 1 = </span><strong><span style=\"font-family: Palanquin Dark;\">T</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रत्येक शब्द के दूसरे अक्षर के लिए : U -1 = T, T-1 = S, S-1 = R, R-1 = </span><strong><span style=\"font-family: Palanquin Dark;\">Q</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रत्येक शब्द के तीसरे अक्षर के लिए; R - 1 = Q, Q - 1 = P, P - 1 = O, O - 1 = </span><strong><span style=\"font-family: Palanquin Dark;\">N</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">अतः हमें TQN प्राप्त होता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "6",
                    question_en: "<p>42.<span style=\"font-family: Palanquin Dark;\"> Select the option that is related to the third number in the same way as the second number is related to the first number and the sixth number is related to the fifth number.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">10 : 0 :: 12 : ? :: 16 : 3</span></p>",
                    question_hi: "<p>42.<span style=\"font-family: Palanquin Dark;\"> उस विकल्प का चयन करें जो तीसरी संख्या से उसी प्रकार संबंधित है जैसे दूसरी संख्या पहली संख्या से संबंधित है और छठी संख्या पांचवीं संख्या से संबंधित है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">10 : 0 :: 12 : ? :: 16 : 3</span></p>",
                    options_en: ["<p>4</p>", "<p>5</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>4</p>", "<p>5</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>42.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>0</mn></mrow><mrow><mo>&#160;</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>2</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Similarly </span><span style=\"font-family: Palanquin Dark;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></math> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Palanquin Dark;\">42.(d) </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>0</mn></mrow><mrow><mo>&#160;</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>2</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इसीप्रकर </span><span style=\"font-family: Palanquin Dark;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></math> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "6",
                    question_en: " <p>43. </span><span style=\"font-family:Palanquin Dark\">Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image14.png\"/></p>",
                    question_hi: " <p>43.</span><span style=\"font-family:Palanquin Dark\"> उस विकल्प का चयन करें जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image14.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image13.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image25.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image29.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image38.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image13.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image25.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image29.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image38.png\"/></p>"],
                    solution_en: " <p>43.(b)</span></p> <p><span style=\"font-family:Palanquin Dark\">=</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image25.png\"/></p>",
                    solution_hi: " <p>43.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image25.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "6",
                    question_en: "<p>44. <span style=\"font-family: Palanquin Dark;\">Select the correct mirror image of the given combination when the mirror is placed at MN as shown.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image40.png\" /></p>",
                    question_hi: "<p>44. <span style=\"font-family: Palanquin Dark;\">दिए गए संयोजन की सही दर्पण छवि का चयन करें जब दर्पण को MN पर दिखाया गया है।</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image40.png\" /></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image24.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image30.png\" /></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_48691366411666790935467.png\" width=\"161\" height=\"47\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image1.png\" /></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image24.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image30.png\" /></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_89335489811666791135707.png\" width=\"174\" height=\"51\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image1.png\" /></p>"],
                    solution_en: "<p>44.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_90343429421666790951308.png\" width=\"164\" height=\"48\" /></p>",
                    solution_hi: "<p>44.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_81361504321666791148651.png\" /></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "6",
                    question_en: " <p>45.</span><span style=\"font-family:Palanquin Dark\"> Which of the following numbers will replace the question mark (?) in the given series?</span></p> <p><span style=\"font-family:Palanquin Dark\">11, 20, 43, 94, 187, 336, 555, ?</span></p>",
                    question_hi: " <p>45. </span><span style=\"font-family:Palanquin Dark\">निम्नलिखित में से कौन सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) का स्थान लेगी?</span></p> <p><span style=\"font-family:Palanquin Dark\">11, 20, 43, 94, 187, 336, 555, ?</span></p>",
                    options_en: [" <p> 777</span></p>", " <p> 888</span></p>", 
                                " <p> 858</span></p>", " <p> 758</span></p>"],
                    options_hi: [" <p> 777</span></p>", " <p> 888</span></p>",
                                " <p> 858</span></p>", " <p> 758</span></p>"],
                    solution_en: " <p>45.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image37.png\"/></p>",
                    solution_hi: " <p>45.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image37.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "6",
                    question_en: "<p>46.<span style=\"font-family: Palanquin Dark;\"> Study the given pattern carefully and select the number that can replace the question mark (?) in it.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">First row: 73, 52, 75</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Second row:64, 41, 63</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Third row: 68, 47, ?</span></p>",
                    question_hi: "<p>46.<span style=\"font-family: Palanquin Dark;\"> दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">पहली पंक्ति : 73, 52, 75</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">दूसरी पंक्ति : 64, 41, 63</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">तीसरी पंक्ति : 68, 47, ?</span></p>",
                    options_en: ["<p>71</p>", "<p>69</p>", 
                                "<p>63</p>", "<p>65</p>"],
                    options_hi: ["<p>71</p>", "<p>69</p>",
                                "<p>63</p>", "<p>65</p>"],
                    solution_en: "<p>46.(b)</p>\r\n<p><strong>Logic</strong><span style=\"font-weight: 400;\"><strong> </strong>- In row , [ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>I</mi><mi>s</mi><mi>t</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>n</mi><mi>d</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mo>&#160;</mo></mrow><mn>5</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;] &times; 3 = 3rd number&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">For 1st Row = 73 + 52 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>5</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 25 = 25 &times; </span>3<strong> </strong><span style=\"font-weight: 400;\">= 75</span></p>\r\n<p><span style=\"font-weight: 400;\">For 2nd Row = 64 + 41 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>105</mn><mn>5</mn></mfrac><mo>&#160;</mo></math></span><span style=\"font-weight: 400;\">= 21= 21 &times;</span><strong> </strong>3<span style=\"font-weight: 400;\"> = 63</span></p>\r\n<p><span style=\"font-weight: 400;\">For 3rd&nbsp; Row = 68 + 47&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>115</mn><mrow><mn>5</mn><mo>&#160;</mo></mrow></mfrac></math></span><span style=\"font-weight: 400;\">= 23 =&nbsp; 23 &times;</span><strong> </strong>3<span style=\"font-weight: 400;\"> = 69</span></p>",
                    solution_hi: "<p>46.(b)</p>\r\n<p><strong>Logic</strong><span style=\"font-weight: 400;\"> - In row , [</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>I</mi><mi>s</mi><mi>t</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>n</mi><mi>d</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mo>&#160;</mo></mrow><mn>5</mn></mfrac></math><span style=\"font-weight: 400;\"> ] &times; 3 = 3rd number&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Palanquin Dark;\">पहली पंक्ति </span>&nbsp;= 73 + 52 = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>5</mn></mfrac></math><span style=\"font-weight: 400;\">= 25 = 25 &times; </span><strong>3 </strong><span style=\"font-weight: 400;\">= 75</span></p>\r\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Palanquin Dark;\">दूसरी पंक्ति </span>&nbsp;= 64 + 41 = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>5</mn></mfrac></math><span style=\"font-weight: 400;\">= 21= 21 &times;</span><strong> 3</strong><span style=\"font-weight: 400;\"> = 63</span></p>\r\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Palanquin Dark;\">तीसरी पंक्ति</span> = 68 + 47&nbsp; = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>5</mn></mfrac></math><span style=\"font-weight: 400;\">= 23 =&nbsp; 23 &times;</span><strong> 3</strong><span style=\"font-weight: 400;\"> = 69</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "6",
                    question_en: " <p>47.</span><span style=\"font-family:Palanquin Dark\"> Which letter cluster will replace the question mark (?) to complete the given series?</span></p> <p><span style=\"font-family:Palanquin Dark\">ANGE, DSBB, GXWY, ?</span></p>",
                    question_hi: "<p>47.<span style=\"font-family: Palanquin Dark;\"> दी गई श्रृंखला को पूरा करने के लिए कौन सा अक्षर समूह प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">ANGE, DSBB, GXWY, ?</span></p>",
                    options_en: [" <p> JBRV</span></p>", " <p> JCSV</span></p>", 
                                " <p> JBSV</span></p>", " <p> JCRV</span></p>"],
                    options_hi: ["<p>JBRV</p>", "<p>JCSV</p>",
                                "<p>JBSV</p>", "<p>JCRV</p>"],
                    solution_en: " <p>47.(d)</span></p> <p><span style=\"font-family:Palanquin Dark\">For first letter of each word : A + 3 = D, D + 3 = G, G + 3 = </span><span style=\"font-family:Palanquin Dark\">J</span></p> <p><span style=\"font-family:Palanquin Dark\">For second letter of each word : N + 5 = S, S + 5 = X, X + 5 = </span><span style=\"font-family:Palanquin Dark\">C</span></p> <p><span style=\"font-family:Palanquin Dark\">For third letter of each word : G - 5 = B, B - 5 = W, W - 5 = </span><span style=\"font-family:Palanquin Dark\">R</span></p> <p><span style=\"font-family:Palanquin Dark\">For fourth letter of each word : E - 3 = B, B - 3 = Y, Y - 3 = </span><span style=\"font-family:Palanquin Dark\">V</span></p> <p><span style=\"font-family:Palanquin Dark\">Hence, we get JCRV.</span></p>",
                    solution_hi: "<p>47.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रत्येक शब्द के पहले अक्षर के लिए: A + 3 = D, D + 3 = G, G + 3 = </span><strong><span style=\"font-family: Palanquin Dark;\">J</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रत्येक शब्द के दूसरे अक्षर के लिए : N + 5 = S, S + 5 = X, X + 5 = </span><strong><span style=\"font-family: Palanquin Dark;\">C</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रत्येक शब्द के तीसरे अक्षर के लिए : G - 5 = B, B - 5 = W, W - 5 = </span><strong><span style=\"font-family: Palanquin Dark;\">R</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रत्येक शब्द के चौथे अक्षर के लिए : E - 3 = B, B - 3 = Y, Y - 3 = </span><strong><span style=\"font-family: Palanquin Dark;\">V</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इसलिए, हमें JCRV मिलता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "6",
                    question_en: "<p>48. <span style=\"font-family: Palanquin Dark;\">In a certain code language, \'RAIN\' is written as \'182915\' and \'SUN\' is written as \'192214\'. How will \'MOON\' be written in that language?</span></p>\n",
                    question_hi: "<p>48.<span style=\"font-family: Palanquin Dark;\"> एक निश्चित कोड भाषा में, \'RAIN\' को \'182915\' और \'SUN\' को \'192214\' लिखा जाता है। उस भाषा में \'MOON\' कैसे लिखा जाएगा?</span></p>",
                    options_en: ["<p>13161415</p>\n", "<p>13161616</p>\n", 
                                "<p>13161515</p>\n", "<p>13161414</p>\n"],
                    options_hi: ["<p>13161415</p>", "<p>13161616</p>",
                                "<p>13161515</p>", "<p>13161414</p>"],
                    solution_en: "<p>48.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Logic : Letters at odd places are replaced by their place value in alphabet while letters at even places are replaced by their place value in alphabet + 1 .</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">RAIN <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> (18) , (1+</span><span style=\"font-family: Palanquin Dark;\">1</span><span style=\"font-family: Palanquin Dark;\">) , (9) , (14+</span><span style=\"font-family: Palanquin Dark;\">1</span><span style=\"font-family: Palanquin Dark;\">) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 18-2-9-15</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SUN <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> (19) , (21+</span><span style=\"font-family: Palanquin Dark;\">1</span><span style=\"font-family: Palanquin Dark;\">) , (14)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> 19-22-14</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Similarly, MOON<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> (13) , (15+</span><span style=\"font-family: Palanquin Dark;\">1</span><span style=\"font-family: Palanquin Dark;\">) , (15) , (14+</span><span style=\"font-family: Palanquin Dark;\">1</span><span style=\"font-family: Palanquin Dark;\">) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 13-16-15-15 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                    solution_hi: "<p>48.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">तर्क:-</span><span style=\"font-family: Palanquin Dark;\"> अक्षरों के विषम स्थान &rarr;</span><span style=\"font-family: Palanquin Dark;\"> वर्णमाला का स्थानीय मान , अक्षरों के सम स्थान &rarr;</span><span style=\"font-family: Palanquin Dark;\"> वर्णमाला का स्थानीय मान +1 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">RAIN <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> (18) , (1+</span><strong><span style=\"font-family: Palanquin Dark;\">1</span></strong><span style=\"font-family: Palanquin Dark;\">) , (9) , (14+</span><strong><span style=\"font-family: Palanquin Dark;\">1</span></strong><span style=\"font-family: Palanquin Dark;\">) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><strong><span style=\"font-family: Palanquin Dark;\">18-2-9-15</span></strong></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SUN <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> (19) , (21+</span><strong><span style=\"font-family: Palanquin Dark;\">1</span></strong><span style=\"font-family: Palanquin Dark;\">) , (14) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> <strong>19-22-14</strong></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इसी प्रकार, MOON&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> (13) , (15+</span><strong><span style=\"font-family: Palanquin Dark;\">1</span></strong><span style=\"font-family: Palanquin Dark;\">) , (15) , (14+</span><strong><span style=\"font-family: Palanquin Dark;\">1</span></strong><span style=\"font-family: Palanquin Dark;\">) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> <strong>13-16-15-15 </strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "6",
                    question_en: " <p>49. </span><span style=\"font-family:Palanquin Dark\">The second number in the given number pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are followed in all the number pairs, EXCEPT one. Find that odd number pair.</span></p>",
                    question_hi: " <p>49.</span><span style=\"font-family:Palanquin Dark\"> दी गई संख्या युग्मों में दूसरी संख्या पहली संख्या पर कुछ गणितीय संक्रियाएँ करके प्राप्त की जाती है। एक को छोड़कर सभी संख्या युग्मों में समान संक्रिया का अनुसरण किया जाता है। वह विषम संख्या युग्म ज्ञात कीजिए।</span></p>",
                    options_en: [" <p> 21 : 431</span></p>", " <p> 15 : 225</span></p>", 
                                " <p> 13 : 169</span></p>", " <p> 12 : 144</span></p>"],
                    options_hi: [" <p> 21 : 431</span></p>", " <p> 15 : 225</span></p>",
                                " <p> 13 : 169</span></p>", " <p> 12 : 144</span></p>"],
                    solution_en: " <p>49.(a)</span></p> <p><span style=\"font-family:Palanquin Dark\">Logic : Except option (a), in all other options the second number is the square of the first number.</span></p>",
                    solution_hi: " <p>49.(a)</span></p> <p><span style=\"font-family:Palanquin Dark\">तर्क: विकल्प (a) को छोड़कर, अन्य सभी विकल्पों में दूसरी संख्या , पहली संख्या का वर्ग है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: " <p>50. </span><span style=\"font-family:Palanquin Dark\">Two different positions of the same dice are shown, the six faces of which are numbered from 1 to 6. Select the number that will be on the top if the dice is resting on ‘6’.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image21.png\"/></p>",
                    question_hi: " <p>50.</span><span style=\"font-family:Palanquin Dark\"> एक ही पासे के दो अलग-अलग स्थान स्थिति दिखाए गए हैं, जिनमें से छह फलक 1 से 6 तक संख्यांकित हैं। यदि पासा \'6\' पर टिका हुआ है तो उस संख्या का चयन कीजिए जो पसे के शीर्ष पर होगी ?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image21.png\"/></p>",
                    options_en: [" <p> 3</span></p>", " <p> 2</span></p>", 
                                " <p> 1</span></p>", " <p> 4</span></p>"],
                    options_hi: [" <p> 3</span></p>", " <p> 2</span></p>",
                                " <p> 1</span></p>", " <p> 4</span></p>"],
                    solution_en: " <p><span style=\"font-family:Palanquin Dark\">50.(c)</span></p> <p><span style=\"font-family:Palanquin Dark\">From both the dice given in question, option (a), (b) and (d) get eliminated, so the number that will be on the top if the dice is resting on ‘6’ is ‘1’.</span></p>",
                    solution_hi: " <p>50.(c)</span></p> <p><span style=\"font-family:Palanquin Dark\">प्रश्न में दिए गए दोनों पासों में से विकल्प (a) , (b) और (d)  छट जाते हैं , इसलिए यदि पासा \'6\' पर टिका हुआ है तो वह संख्या जो सबसे ऊपर होगी वह \'1\' है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51.<span style=\"font-family: Palanquin Dark;\"> Production of different types of cars (in thousands) in a company for years 1992 to 1996 is as follows :</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_28884236611656303517347.jpg\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">What was the percentage of production of type-A car in 1995 to its total production over the years ?</span></p>\n",
                    question_hi: "<p>51.<span style=\"font-family: Palanquin Dark;\"> &#2319;&#2325; &#2325;&#2306;&#2346;&#2344;&#2368; &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2359; 1992 &#2360;&#2375; 1996 &#2340;&#2325; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2325;&#2366;&#2352;&#2379;&#2306; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; (&#2361;&#2332;&#2366;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306;) &#2311;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2361;&#2376;:</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_88581036711656303539472.jpg\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2357;&#2352;&#2381;&#2359; 1995 &#2350;&#2375;&#2306; &#2335;&#2366;&#2311;&#2346;-A &#2325;&#2366;&#2352; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; . </span><span style=\"font-family: Palanquin Dark;\">&#2360;&#2349;&#2368; &#2357;&#2352;&#2381;&#2359;&#2379; &#2350;&#2375;&#2306;</span><span style=\"font-family: Palanquin Dark;\"> &#2313;&#2344;&#2325;&#2375; &#2325;&#2369;&#2354; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2366; &#2325;&#2367;&#2340;&#2344;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2341;&#2366; ?</span></p>\n",
                    options_en: ["<p>18%</p>\n", "<p>20%</p>\n", 
                                "<p>24%</p>\n", "<p>15%</p>\n"],
                    options_hi: ["<p>18%</p>\n", "<p>20%</p>\n",
                                "<p>24%</p>\n", "<p>15%</p>\n"],
                    solution_en: "<p>51.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Production of type-A car in 1995 = 190</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Production of type- A car over the years = 160 + 210 + 130 + 190 + 260 = 950</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Required percentage =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>190</mn><mn>950</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = 20%</span></p>\n",
                    solution_hi: "<p>51.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1995 &#2350;&#2375;&#2306; &#2335;&#2366;&#2311;&#2346;-A &#2325;&#2366;&#2352; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; = 190</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2350;&#2375;&#2306; &#2335;&#2366;&#2311;&#2346;- A &#2325;&#2366;&#2352; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; = 160 + 210 + 130 + 190 + 260 = 950</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>190</mn><mn>950</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = 20%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52.<span style=\"font-family: Palanquin Dark;\"> If the volume of a sphere is 972&pi; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">, then find its radius.</span></p>",
                    question_hi: "<p>52.<span style=\"font-family: Arial Unicode MS;\"> यदि एक गोले का आयतन 972&pi;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> है, तो इसकी त्रिज्या ज्ञात कीजिए ?</span></p>",
                    options_en: ["<p>7 cm</p>", "<p>6 cm</p>", 
                                "<p>9 cm</p>", "<p>8 cm</p>"],
                    options_hi: ["<p>7 cm</p>", "<p>6 cm</p>",
                                "<p>9 cm</p>", "<p>8 cm</p>"],
                    solution_en: "<p>52.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Volume of sphere =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><msup><mrow><mi>r</mi><mo>&#160;</mo></mrow><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">972</span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><msup><mrow><mi>r</mi><mo>&#160;</mo></mrow><mn>3</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>r</mi><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>972</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 729</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">r = 9 cm</span></p>",
                    solution_hi: "<p>52.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">गोले का आयतन =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><msup><mrow><mi>r</mi><mo>&#160;</mo></mrow><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">972</span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><msup><mrow><mi>r</mi><mo>&#160;</mo></mrow><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>r</mi><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>972</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 729</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">r = 9 cm</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53.<span style=\"font-family: Palanquin Dark;\"> 15 boys and a certain number of girls appeared for a test. The average score of the boys was 26; the average score of the girls was 36, while the combined average score was 30. How many girls appeared for the test?</span></p>\n",
                    question_hi: "<p>53.<span style=\"font-family: Palanquin Dark;\"> &#2319;&#2325; &#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; 15 &#2354;&#2337;&#2364;&#2325;&#2375; &#2324;&#2352; &#2325;&#2369;&#2331; &#2350;&#2375;&#2306; &#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2366;&#2306; &#2313;&#2346;&#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2369;&#2312;&#2306;&#2404; &#2354;&#2337;&#2364;&#2325;&#2379;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; &#2360;&#2381;&#2325;&#2379;&#2352; 26 &#2340;&#2341;&#2366; &#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; &#2360;&#2381;&#2325;&#2379;&#2352; 36 &#2341;&#2366; , &#2332;&#2348;&#2325;&#2367; &#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340; &#2324;&#2360;&#2340; &#2360;&#2381;&#2325;&#2379;&#2352; 30 &#2341;&#2366;&#2404; &#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2368; &#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2366;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2369;&#2312;&#2306; ?</span></p>\n",
                    options_en: ["<p>10</p>\n", "<p>8</p>\n", 
                                "<p>12</p>\n", "<p>9</p>\n"],
                    options_hi: ["<p>10</p>\n", "<p>8</p>\n",
                                "<p>12</p>\n", "<p>9</p>\n"],
                    solution_en: "<p>53.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_2507546711669791144301.png\" width=\"212\" height=\"220\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">No of boys = 3 unit = 15</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2 unit =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times; 2 = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So the no of Girls appeared for the test = 10 </span></p>\n",
                    solution_hi: "<p>53.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_46948477611669791195683.png\" width=\"172\" height=\"178\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2354;&#2337;&#2364;&#2325;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 3 &#2311;&#2325;&#2366;&#2312; = 15</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2 &#2311;&#2325;&#2366;&#2312; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 2 = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;: &#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366; &#2350;&#2375;&#2306; &#2360;&#2350;&#2381;&#2350;&#2367;&#2354;&#2367;&#2340; &#2361;&#2369;&#2312; &#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 10</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. <span style=\"font-family: Palanquin Dark;\">A metallic solid cuboid of dimensions 36 cm &times; 18 cm &times; 12 cm is melted and recast in the form of cubes </span><span style=\"font-family: Palanquin Dark;\">of side 6</span><span style=\"font-family: Palanquin Dark;\"> cm. Find the number of cubes so formed.</span></p>",
                    question_hi: "<p>54. <span style=\"font-family: Palanquin Dark;\">36 cm &times; 18 cm &times; 12 cm विमाओं वाले एक धातु के ठोस घनाभ को पिघलाकर 6 cm भुजा वाले घनों के रूप में ढाला जाता है। इस प्रकार बने घनों की संख्या ज्ञात कीजिए ?</span></p>",
                    options_en: ["<p>38</p>", "<p>40</p>", 
                                "<p>34</p>", "<p>36</p>"],
                    options_hi: ["<p>38</p>", "<p>40</p>",
                                "<p>34</p>", "<p>36</p>"],
                    solution_en: "<p>54.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the no of cubes formed = N</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Volume of cuboid = </span><span style=\"font-family: Palanquin Dark;\">N &times;</span><span style=\"font-family: Palanquin Dark;\"> Volume of cube </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">36 &times; 18 &times; 12 = N &times; 6 &times; 6 &times; 6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">N = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>12</mn></mrow><mrow><mn>6</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn></mrow></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 36</span></p>",
                    solution_hi: "<p>54.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">माना , घनों की संख्या = N</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">घनाभ का आयतन = N &times; घन का आयतन</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">36 &times; 18 &times; 12 = N &times; 6 &times; 6 &times; 6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">N =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>12</mn></mrow><mrow><mn>6</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn></mrow></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 36</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55.<span style=\"font-family: Palanquin Dark;\"> A pillar 11 m in radius is 21 m high. How much material was used to construct it?(use <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</span></p>",
                    question_hi: "<p>55.<span style=\"font-family: Palanquin Dark;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">11m त्रिज्या वाला एक स्तंभ 21 m ऊंचा है। इसे बनाने में कितनी सामग्री का उपयोग किया गया था ?</span></p>\r\n<p><span style=\"font-weight: 400;\">(&pi; = 22/7 का प्रयोग करें)</span></p>",
                    options_en: ["<p>7996</p>", "<p>7886</p>", 
                                "<p>7989</p>", "<p>7986</p>"],
                    options_hi: ["<p>7996</p>", "<p>7886</p>",
                                "<p>7989</p>", "<p>7986</p>"],
                    solution_en: "<p>55.(d)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Since, Pillar is cylindrical in shape. </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">So, to reach the required answer we have to find the volume of pillar.</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Volume of pillar = </span><span style=\"font-family: Arial Unicode MS;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Arial Unicode MS;\"> &times; 11 &times; 11 &times; 21 = 7986&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>3</mn></msup></math></span></p>",
                    solution_hi: "<p>55.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">चूंकि, स्तंभ आकार में बेलनाकार है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">अतः, अपेक्षित उत्तर तक पहुँचने के लिए हमें स्तंभ का आयतन ज्ञात करना होगा।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">स्तंभ का आयतन = </span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 11 &times; 11 &times; 21 = 7986 </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: " <p>56.</span><span style=\"font-family:Palanquin Dark\"> From the following numbers, find the number which is exactly divisible by 42.</span></p>",
                    question_hi: " <p>56. </span><span style=\"font-family:Palanquin Dark\">निम्नलिखित संख्याओं में से वह संख्या ज्ञात कीजिए जो 42 से पूर्णतः विभाज्य है।</span></p>",
                    options_en: [" <p> 25232</span></p>", " <p> 25242</span></p>", 
                                " <p> 25244</span></p>", " <p> 25212</span></p>"],
                    options_hi: [" <p> 25232</span></p>", " <p> 25242</span></p>",
                                " <p> 25244</span></p>", " <p> 25212</span></p>"],
                    solution_en: " <p>56.(b)</span></p> <p><span style=\"font-family:Palanquin Dark\">42 = 2 × 3 × 7</span></p> <p><span style=\"font-family:Palanquin Dark\">To get the required answer , we have to check the options one by one. </span></p> <p><span style=\"font-family:Palanquin Dark\">All options is divisible by 2 but option (b) and (c) is divisible by 3(divisibility of 3)</span></p> <p><span style=\"font-family:Palanquin Dark\">On checking options (b) we have it is the only no , divisible by 2, 3, 7</span></p> <p><span style=\"font-family:Palanquin Dark\">So, the correct option is (b)</span></p>",
                    solution_hi: " <p>56.(b)</span></p> <p><span style=\"font-family:Palanquin Dark\">42 = 2 × 3 × 7</span></p> <p><span style=\"font-family:Palanquin Dark\">आवश्यक उत्तर प्राप्त करने के लिए, हमें एक-एक करके विकल्पों की जांच करनी होगी।</span></p> <p><span style=\"font-family:Palanquin Dark\">सभी विकल्प 2 से विभाज्य हैं लेकिन विकल्प (b) और (c) 3 से विभाज्य हैं। (3 की विभाज्यता)</span></p> <p><span style=\"font-family:Palanquin Dark\">विकल्प (b) की जांच करने पर हमारे पास यह एकमात्र संख्या है, जो 2, 3, 7 से विभाज्य है।</span></p> <p><span style=\"font-family:Palanquin Dark\">इसलिए, सही विकल्प (b) है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p><span style=\"font-weight: 400;\">Simplify&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mo>-</mo><mo>[</mo><mn>10</mn><mo>&#8211;</mo><mo>{</mo><mo>&#160;</mo><mrow><mn>20</mn><mo>&#8211;</mo><mo>(</mo><mn>15</mn><mo>&#8211;</mo><mover><mrow><mn>9</mn><mo>-</mo><mn>3</mn><mo>&#160;</mo></mrow><mo>&#175;</mo></mover><mo>)</mo></mrow><mo>}</mo><mo>]</mo></math></span></p>",
                    question_hi: "<p>57. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mo>-</mo><mo>[</mo><mn>10</mn><mo>&#8211;</mo><mo>{</mo><mo>&#160;</mo><mrow><mn>20</mn><mo>&#8211;</mo><mo>(</mo><mn>15</mn><mo>&#8211;</mo><mover><mrow><mn>9</mn><mo>-</mo><mn>3</mn><mo>&#160;</mo></mrow><mo>&#175;</mo></mover><mo>)</mo></mrow><mo>}</mo><mo>]</mo></math><span style=\"font-family: Palanquin Dark;\"> को सरल कीजिए ?</span></p>",
                    options_en: ["<p>10</p>", "<p>8</p>", 
                                "<p>7</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>8</p>",
                                "<p>7</p>", "<p>12</p>"],
                    solution_en: "<p>57.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>&#8658;</mo><mn>9</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>[</mo><mn>10</mn><mo>-</mo><mo>{</mo><mn>20</mn><mo>-</mo><mo>(</mo><mn>15</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>}</mo><mo>]</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>9</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>[</mo><mn>10</mn><mo>&#160;</mo><mo>-</mo><mo>{</mo><mn>20</mn><mo>-</mo><mn>9</mn><mo>&#160;</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>&#160;</mo><mn>9</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>[</mo><mn>10</mn><mo>-</mo><mn>11</mn><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>9</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>10</mn></math></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>57.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>&#8658;</mo><mn>9</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>[</mo><mn>10</mn><mo>-</mo><mo>{</mo><mn>20</mn><mo>-</mo><mo>(</mo><mn>15</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>}</mo><mo>]</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>9</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>[</mo><mn>10</mn><mo>&#160;</mo><mo>-</mo><mo>{</mo><mn>20</mn><mo>-</mo><mn>9</mn><mo>&#160;</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>&#160;</mo><mn>9</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>[</mo><mn>10</mn><mo>-</mo><mn>11</mn><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>9</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>10</mn></math></p>\r\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58.<span style=\"font-family: Palanquin Dark;\"> A man purchased a cell phone for ₹24,500 and sold it at a gain of 12.5% calculated on the selling price. The selling price of the cell phone was:</span></p>",
                    question_hi: "<p>58.&nbsp;<span style=\"font-weight: 400;\">एक व्यक्ति ने एक सेल फोन ₹ 24,500 में खरीदा और उसे ( विक्रय मूल्य पर गणना किये जाने पर ) 12.5% ​​के लाभ पर बेच दिया। सेल फोन का विक्रय मूल्य क्या था ?</span></p>",
                    options_en: ["<p>₹25,000</p>", "<p>₹28,000</p>", 
                                "<p>₹27,500</p>", "<p>₹25,500</p>"],
                    options_hi: ["<p>₹25,000</p>", "<p>₹28,000</p>",
                                "<p>₹27,500</p>", "<p>₹25,500</p>"],
                    solution_en: "<p>58.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">CP of cell phone = ₹24,500</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Gain = 12.5% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> , calculated on SP</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, SP = 8 unit , CP = 8 - 1 = 7 unit</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">7 unit = 24500</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24500</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = ₹3500</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">8 unit = 3500 &times; 8 = ₹28,000</span></p>",
                    solution_hi: "<p>58.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">सेल फोन का क्रय मूल्य = ₹24,500</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">लाभ = 12.5% ​​=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">लाभ विक्रय मूल्य के अनुसार ज्ञात किया गया है , इसलिए </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">विक्रय मूल्य = 8 इकाई, क्रय मूल्य = 8 - 1 = 7 इकाई</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">7 इकाई = 24500</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24500</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = ₹3500</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">8 इकाई = 3500 &times; 8 = ₹28,000</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. <span style=\"font-family: Palanquin Dark;\">The foot of a ladder 25 m long is 7 m from the base of the building. If the top of the ladder slips by 4 m, then by how much distance will the foot of the ladder slide ?</span></p>\n",
                    question_hi: "<p>59. <span style=\"font-family: Palanquin Dark;\">25 m &#2354;&#2306;&#2348;&#2368; &#2360;&#2368;&#2338;&#2364;&#2368; &#2325;&#2366; &#2346;&#2376;&#2352; &#2311;&#2350;&#2366;&#2352;&#2340; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2360;&#2375; 7 m &#2342;&#2370;&#2352; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2360;&#2368;&#2338;&#2364;&#2368; &#2325;&#2366; &#2358;&#2368;&#2352;&#2381;&#2359; 4 m &#2347;&#2367;&#2360;&#2354; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2340;&#2379; &#2360;&#2368;&#2338;&#2364;&#2368; &#2325;&#2366; &#2346;&#2376;&#2352; &#2325;&#2367;&#2340;&#2344;&#2368; &#2342;&#2370;&#2352;&#2368; &#2340;&#2325; &#2326;&#2367;&#2360;&#2325;&#2375;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>9 m</p>\n", "<p>7 m</p>\n", 
                                "<p>6 m</p>\n", "<p>8 m</p>\n"],
                    options_hi: ["<p>9 m</p>\n", "<p>7 m</p>\n",
                                "<p>6 m</p>\n", "<p>8 m</p>\n"],
                    solution_en: "<p>59.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_65538074911670388510239.png\" width=\"179\" height=\"128\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Length of the ladder AE = DC = 25m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Distance of foot from the base (BE) = 7m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">In triangle ABE,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>A</mi><mi>B</mi></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mi>B</mi><mi>E</mi></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mi>A</mi><mi>E</mi></mrow><mn>2</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mn>25</mn><mn>2</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>49</mn></msqrt></math></span><span style=\"font-family: Palanquin Dark;\"> = 24 m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">DB = 24 - 4 = 20 m </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Now in triangle DBC,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>D</mi><msup><mi>B</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>D</mi><msup><mi>C</mi><mn>2</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>20</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>25</mn><mn>2</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></math>= 625 - 400</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">BC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math></span><span style=\"font-family: Palanquin Dark;\"> = 15 m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">EC= BC - BE = 15 - 7 = 8m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Hence, the foot of the ladder will slide by 8m </span></p>\n",
                    solution_hi: "<p>59.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_93323339711670388566328.png\" width=\"200\" height=\"143\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2368;&#2338;&#2364;&#2368; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; AE = DC = 25m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2310;&#2343;&#2366;&#2352; &#2360;&#2375; &#2360;&#2368;&#2397;&#2368; &#2325;&#2375; &#2346;&#2376;&#2352; &#2325;&#2368; &#2342;&#2370;&#2352;&#2368; (BE) = 7m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; ABE &#2350;&#2375;&#2306;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>A</mi><mi>B</mi></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mi>B</mi><mi>E</mi></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mi>A</mi><mi>E</mi></mrow><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mn>25</mn><mn>2</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>49</mn></msqrt></math></span><span style=\"font-family: Palanquin Dark;\"> = 24 m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">DB = 24 - 4 = 20 m </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2348; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; DBC &#2350;&#2375;&#2306;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>D</mi><msup><mi>B</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>D</mi><msup><mi>C</mi><mn>2</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>20</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>25</mn><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></math>= 625 - 400</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">BC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math> </span><span style=\"font-family: Palanquin Dark;\"> = 15 m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">EC = BC - BE = 15 - 7 = 8m</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;: &#2360;&#2368;&#2338;&#2364;&#2368; &#2325;&#2366; &#2346;&#2376;&#2352; 8 m &#2326;&#2367;&#2360;&#2325;&#2375;&#2327;&#2366;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. <span style=\"font-family: Palanquin Dark;\">What is the third proportion to 15 and 24?</span></p>",
                    question_hi: "<p>60. <span style=\"font-family: Palanquin Dark;\">15 &#2324;&#2352; 24 &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>38<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>", "<p>37<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>", 
                                "<p>38<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>", "<p>37<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>"],
                    options_hi: ["<p>38<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\n", "<p>37<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>\n",
                                "<p>38<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>\n", "<p>37<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\n"],
                    solution_en: "<p>60.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Third proportion to 15 and 24 = </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>b</mi><mn>2</mn></msup><mi>a</mi></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>24</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>24</mn></mrow><mn>15</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>576</mn><mn>15</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> = 38<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    solution_hi: "<p>60.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">15 &#2324;&#2352; 24 &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; = </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>b</mi><mn>2</mn></msup><mi>a</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>24</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>24</mn></mrow><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>576</mn><mn>15</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> = 38<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. <span style=\"font-family: Palanquin Dark;\">Simplify the expression&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>49</mn><mo>&#160;</mo><msup><mi>q</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mo>)</mo><mo>(</mo><mn>6</mn><mi>p</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mi>q</mi><mo>)</mo><mo>(</mo><mn>6</mn><mi>p</mi><mo>&#160;</mo><mo>&#8211;</mo><mo>&#160;</mo><mn>7</mn><mi>q</mi><mo>)</mo></math></span></p>",
                    question_hi: "<p>61.<span style=\"font-family: Palanquin Dark;\"> निम्नलिखित व्यंजक को सरल कीजिए।&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>49</mn><mo>&#160;</mo><msup><mi>q</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mo>)</mo><mo>(</mo><mn>6</mn><mi>p</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mi>q</mi><mo>)</mo><mo>(</mo><mn>6</mn><mi>p</mi><mo>&#160;</mo><mo>&#8211;</mo><mo>&#160;</mo><mn>7</mn><mi>q</mi><mo>)</mo></math></span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1296</mn><msup><mi>p</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2401</mn><msup><mi>q</mi><mn>4</mn></msup></math></p>", "<p>36<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>4</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> &ndash; 49&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>q</mi><mn>4</mn></msup></math></span></p>", 
                                "<p>1296<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>4</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> &ndash; 2401<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>q</mi><mn>4</mn></msup></math></span></p>", "<p>36<span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>4</mn></msup></math>+ 49<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>q</mi><mn>4</mn></msup></math> </span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1296</mn><msup><mi>p</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2401</mn><msup><mi>q</mi><mn>4</mn></msup></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>36</mn><msup><mi>p</mi><mn>4</mn></msup><mo>-</mo><mn>49</mn><msup><mi>q</mi><mn>4</mn></msup></math></p>",
                                "<p>1296<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>4</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> &ndash; 2401<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>q</mi><mn>4</mn></msup></math></span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>36</mn><mo>&#160;</mo><msup><mi>p</mi><mn>4</mn></msup><mo>+</mo><mo>&#160;</mo><mn>49</mn><msup><mi>q</mi><mn>4</mn></msup></math></p>"],
                    solution_en: "<p>61.(c)</p>\r\n<p><span style=\"font-weight: 400;\">Formula used :&nbsp; (x - y) (x + y) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>49</mn><msup><mi>q</mi><mn>2</mn></msup><mo>)</mo><mo>&#160;</mo><mo>(</mo><msup><mrow><mo>(</mo><mn>6</mn><mi>p</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>7</mn><mi>q</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>)</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>49</mn><msup><mi>q</mi><mn>2</mn></msup><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>49</mn><msup><mi>q</mi><mn>2</mn></msup><mo>)</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>49</mn><msup><mi>q</mi><mn>2</mn></msup><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1296</mn><mo>&#160;</mo><msup><mi>p</mi><mn>4</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2401</mn><msup><mi>q</mi><mn>4</mn></msup></math></p>",
                    solution_hi: "<p>61.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रयुक्त सूत्र : (x - y) (x + y)&nbsp; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>49</mn><msup><mi>q</mi><mn>2</mn></msup><mo>)</mo><mo>&#160;</mo><mo>{</mo><msup><mrow><mo>&#160;</mo><mo>(</mo><mn>6</mn><mi>p</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>7</mn><mi>q</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#8201;</mo><mo>}</mo></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>49</mn><msup><mi>q</mi><mn>2</mn></msup><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>49</mn><msup><mi>q</mi><mn>2</mn></msup><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>36</mn><msup><mi>p</mi><mn>2</mn></msup><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>49</mn><msup><mi>q</mi><mn>2</mn></msup><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1296</mn><mo>&#160;</mo><msup><mi>p</mi><mn>4</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2401</mn><msup><mi>q</mi><mn>4</mn></msup></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62.<span style=\"font-family: Palanquin Dark;\"> A retailer offers a discount scheme on shirts, that is, buy 3, get 2 free. What is the discount percentage?</span></p>",
                    question_hi: "<p>62.<span style=\"font-family: Palanquin Dark;\"> एक फुटकर विक्रेता शर्ट पर छूट प्रदान करता है, अर्थात 3 खरीदें, 2 मुफ्त पाएं । छूट प्रतिशत क्या है?</span></p>",
                    options_en: ["<p>35%</p>", "<p>50%</p>", 
                                "<p>30%</p>", "<p>40%</p>"],
                    options_hi: ["<p>35%</p>", "<p>50%</p>",
                                "<p>30%</p>", "<p>40%</p>"],
                    solution_en: "<p>62.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the MP of an article be ₹1</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">MP of 5 articles = ₹5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SP of 3 articles = ₹3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Discount% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = 40%</span></p>",
                    solution_hi: "<p>62.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">माना , एक वस्तु का अंकित मूल्य = ₹1 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">5 वस्तुओं का अंकित मूल्य = ₹5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3 वस्तुओं का विक्रय मूल्य = ₹3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">छूट% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math>= </span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = 40%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. <span style=\"font-family: Palanquin Dark;\">A can complete a piece of work in 12 days, and B can complete the same work in 36 days. In how many days will both, together, complete the work?</span></p>\n",
                    question_hi: "<p>63. <span style=\"font-family: Palanquin Dark;\">A &#2319;&#2325; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; 12 &#2342;&#2367;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;, &#2324;&#2352; B &#2313;&#2360;&#2368; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; 36 &#2342;&#2367;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; &#2342;&#2379;&#2344;&#2379;&#2306; &#2350;&#2367;&#2354;&#2325;&#2352; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; &#2325;&#2367;&#2340;&#2344;&#2375; &#2342;&#2367;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375;?</span></p>\n",
                    options_en: ["<p>7</p>\n", "<p>8</p>\n", 
                                "<p>6</p>\n", "<p>9</p>\n"],
                    options_hi: ["<p>7</p>\n", "<p>8</p>\n",
                                "<p>6</p>\n", "<p>9</p>\n"],
                    solution_en: "<p>63.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_94206536611670388734526.png\" width=\"199\" height=\"168\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the total work be 36 unit i.e LCM of (12, 36)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Efficiency of (A+B) = 3 + 1 = 4 unit</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Time taken by (A+B) to complete the work = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 9 days</span></p>\n",
                    solution_hi: "<p>63.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_12838733211670388799677.png\" width=\"211\" height=\"178\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; , &#2325;&#2369;&#2354; &#2325;&#2366;&#2352;&#2381;&#2351; = 36 &#2311;&#2325;&#2366;&#2312; &#2309;&#2352;&#2381;&#2341;&#2366;&#2340; (12, 36) &#2325;&#2366; LCM</span></p>\r\n<p><span style=\"font-weight: 400;\">(A+B) </span>&#2325;&#2368; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366; = 3 + 1 = 4 &#2311;&#2325;&#2366;&#2312;</p>\r\n<p><span style=\"font-weight: 400;\">(A+B) </span>&#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2366;&#2352;&#2381;&#2351; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2354;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2360;&#2350;&#2351; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>4</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> = 9 &#2342;&#2367;&#2344;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64.<span style=\"font-family: Palanquin Dark;\"> A pupil multiplied a figure by<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> rather than<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> . What is the percentage error in the calculation ?</span></p>",
                    question_hi: "<p>64.<span style=\"font-family: Palanquin Dark;\"> एक छात्र ने एक अंक को <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> के बजाय <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> से गुणा कर दिया । गणना में प्रतिशत त्रुटि क्या है ?</span></p>",
                    options_en: ["<p>64</p>", "<p>47</p>", 
                                "<p>58</p>", "<p>52</p>"],
                    options_hi: ["<p>64</p>", "<p>47</p>",
                                "<p>58</p>", "<p>52</p>"],
                    solution_en: "<p>64.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the no be 15 unit i.e. LCM of (5, 3)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, 15 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">= 9 , 15 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> = 25</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Required percentage error = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>9</mn></mrow><mn>25</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = 64%</span></p>",
                    solution_hi: "<p>64.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">माना , संख्या = 15 इकाई , अर्थात (5, 3) का LCM</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इसलिए, 15 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">= 9 , 15 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 25</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">आवश्यक प्रतिशत त्रुटि = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>9</mn></mrow><mn>25</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = 64%</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65.<span style=\"font-family: Palanquin Dark;\"> If x + y +z = 0, then what will be the value of </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><msup><mi>x</mi><mn>2</mn></msup><mrow><mi>y</mi><mi>z</mi><mo>&#160;</mo></mrow></mfrac><mo>)</mo><mo>+</mo><mo>&#160;</mo><mo>(</mo><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mi>z</mi><mi>x</mi></mrow></mfrac><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>(</mo><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mi>x</mi><mi>y</mi></mrow></mfrac><mo>)</mo></math><span style=\"font-family: Palanquin Dark;\"> ?</span></p>",
                    question_hi: "<p>65. <span style=\"font-family: Palanquin Dark;\">यदि x + y + z = 0 है, तो </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><msup><mi>x</mi><mn>2</mn></msup><mrow><mi>y</mi><mi>z</mi><mo>&#160;</mo></mrow></mfrac><mo>)</mo><mo>+</mo><mo>&#160;</mo><mo>(</mo><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mi>z</mi><mi>x</mi></mrow></mfrac><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>(</mo><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mi>x</mi><mi>y</mi></mrow></mfrac><mo>)</mo></math><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> का मान क्या होगा?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>z</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>z</mi><mn>2</mn></msup></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><msup><mi>z</mi><mn>2</mn></msup></mrow><mi>x</mi></mfrac></math></p>", "<p>3</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>z</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>z</mi><mn>2</mn></msup></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><msup><mi>z</mi><mn>2</mn></msup></mrow><mi>x</mi></mfrac></math></p>", "<p>3</p>"],
                    solution_en: "<p>65.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Formula used: </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>F</mi><mi>o</mi><mi>r</mi><mi>m</mi><mi>u</mi><mi>l</mi><mi>a</mi><mo>&#160;</mo><mi>u</mi><mi>s</mi><mi>e</mi><mi>d</mi><mo>:</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>z</mi><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi>w</mi><mi>h</mi><mi>e</mi><mi>n</mi><mo>&#160;</mo><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>z</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mspace linebreak=\"newline\"/><mfrac><mrow><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup></mrow><mrow><mi>y</mi><mi>z</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mi>z</mi><mi>x</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mi>x</mi><mi>y</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>z</mi><mn>3</mn></msup></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn></math></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>65.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रयुक्त सूत्र:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>z</mi><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi>w</mi><mi>h</mi><mi>e</mi><mi>n</mi><mo>&#160;</mo><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>z</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mspace linebreak=\"newline\"/><mfrac><mrow><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup></mrow><mrow><mi>y</mi><mi>z</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mi>z</mi><mi>x</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mi>x</mi><mi>y</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>z</mi><mn>3</mn></msup></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn></math></p>\r\n<p>&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66.<span style=\"font-family: Palanquin Dark;\"> The marked price of every item being sold by a wholesaler was ₹300. The wholesaler was offering a stock-clearance sale under which, for every three items paid for, one item was being given free. In addition to this, a further 10% discount on the amount payable on the &lsquo;Buy 3, Get 1 free&rsquo; scheme price was being offered to anyone making purchases worth more than ₹10,000. Ramesh made purchases for which this amount payable was ₹18,000. What was the effective percentage discount that was offered to Ramesh during this transaction?</span></p>",
                    question_hi: "<p>66.<span style=\"font-family: Palanquin Dark;\"> एक थोक व्यापारी द्वारा बेची जा रही प्रत्येक वस्तु का अंकित मूल्य ₹300 था। थोक व्यापारी एक स्टॉक - निकासी बिक्री की पेशकश कर रहा था, जिसके तहत भुगतान की गई प्रत्येक तीन वस्तुओं के लिए, एक वस्तु मुफ्त दी जा रही थी। इसके अलावा, 10,000 रुपये से अधिक की खरीदारी करने वाले किसी भी व्यक्ति को \'3 खरीदें, 1 मुफ़्त पाएं\' योजना की देय राशि पर 10% की छूट दी जा रही थी। रमेश ने खरीदारी की जिसके लिए देय राशि ₹18,000 थी। इस लेन-देन के दौरान रमेश को कितनी प्रभावी छूट की पेशकश की गई थी?</span></p>",
                    options_en: ["<p>32%</p>", "<p>31.5%</p>", 
                                "<p>32.5%</p>", "<p>32.75%</p>"],
                    options_hi: ["<p>32%</p>", "<p>31.5%</p>",
                                "<p>32.5%</p>", "<p>32.75%</p>"],
                    solution_en: "<p>66.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let , MP of items be ₹1</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">MP of 4 items = ₹4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SP of 3 items = ₹3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Discount % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = 25%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Since ,an additional discount of 10% is given .</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, effective discount% = 25 + 10 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> = 35 - 2.5 = 32.5 %</span></p>",
                    solution_hi: "<p>66.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">माना , वस्तुओं का अंकित मूल्य = ₹1</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">4 वस्तुओं का अंकित मूल्य = ₹4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3 वस्तुओं का विक्रय मूल्य = ₹3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">छूट% =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times; 100 = 25%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">चूंकि, 10% की अतिरिक्त छूट दी जाती है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इसलिए, प्रभावी छूट% = 25 + 10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 35 - 2.5 = 32.5 %</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67.<span style=\"font-family: Palanquin Dark;\"> The average age of a group is increased by 4 years when a person of whose age is 32 years was replaced by a person whose age is 56. Find the number of people in the group.</span></p>",
                    question_hi: "<p>67. <span style=\"font-family: Palanquin Dark;\">एक समूह की औसत आयु में 4 वर्ष की वृद्धि होती है जब 32 वर्ष की आयु वाले व्यक्ति को 56 वर्ष की आयु के व्यक्ति द्वारा प्रतिस्थापित किया जाता है। समूह में लोगों की संख्या ज्ञात कीजिए ?</span></p>",
                    options_en: ["<p>6</p>", "<p>7</p>", 
                                "<p>8</p>", "<p>9</p>"],
                    options_hi: ["<p>6</p>", "<p>7</p>",
                                "<p>8</p>", "<p>9</p>"],
                    solution_en: "<p>67.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Increased sum of the ages of the people = 56 - 32 = 24</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Average =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>u</mi><mi>m</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>a</mi><mi>g</mi><mi>e</mi><mi>s</mi></mrow><mrow><mo>&#160;</mo><mi>n</mi><mi>o</mi><mo>.</mo><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>p</mi><mi>e</mi><mi>o</mi><mi>p</mi><mi>l</mi><mi>e</mi></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Since average age of group is increased by 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, No of people = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 6</span></p>",
                    solution_hi: "<p>67.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">लोगों की आयु का बढ़ा हुआ योग = 56 - 32 = 24</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">औसत = <span style=\"font-weight: 400;\">आयु का योग <strong>&divide; </strong></span><span style=\"font-weight: 400;\">लोगों की संख्या</span></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">चूँकि समूह की औसत आयु में 4 की वृद्धि होती है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">अत: लोगों की संख्या =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 6</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68.<span style=\"font-family: Palanquin Dark;\"> If </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>m</mi><msup><mi>x</mi><mi>m</mi></msup><mo>&#8211;</mo><mo>&#160;</mo><mi>n</mi><msup><mi>x</mi><mi>n</mi></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math><span style=\"font-family: Palanquin Dark;\"> then what is the value of </span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mi>m</mi></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mi>n</mi></msup></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mi>m</mi></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>x</mi><mi>n</mi></msup></mrow></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">in terms of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mi>n</mi></msup></math></span><span style=\"font-family: Palanquin Dark;\">?</span></p>",
                    question_hi: "<p>68.<span style=\"font-family: Palanquin Dark;\"> यदि </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>m</mi><msup><mi>x</mi><mi>m</mi></msup><mo>&#8211;</mo><mo>&#160;</mo><mi>n</mi><msup><mi>x</mi><mi>n</mi></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math><span style=\"font-family: Palanquin Dark;\"> है तो </span><span style=\"font-family: Palanquin Dark;\"> के पदों में&nbsp;</span><span style=\"font-family: \'Palanquin Dark\';\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mi>m</mi></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mi>n</mi></msup></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mi>m</mi></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>x</mi><mi>n</mi></msup></mrow></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">का मान क्या है?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>m</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>)</mo></mrow></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>)</mo></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>m</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>)</mo></mrow></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>)</mo></mrow></mfrac></math></p>"],
                    solution_en: "<p>68.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>m</mi><msup><mi>x</mi><mi>m</mi></msup><mo>=</mo><mo>&#160;</mo><mi>n</mi><msup><mi>x</mi><mi>n</mi></msup><mo>&#160;</mo><mo>&#8658;</mo><mfrac><mi>m</mi><mi>n</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><msup><mi>x</mi><mi>n</mi></msup><msup><mi>x</mi><mi>m</mi></msup></mfrac><mspace linebreak=\"newline\"/><mo>=</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mi>m</mi></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mi>n</mi></msup></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mi>m</mi></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>x</mi><mi>n</mi></msup><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mi>n</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>m</mi><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mi>n</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>m</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><mi>n</mi></mrow><mrow><msup><mi>n</mi><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup></mrow></mfrac><mspace linebreak=\"newline\"/><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><mi>m</mi><mo>(</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>",
                    solution_hi: "<p>68.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>m</mi><msup><mi>x</mi><mi>m</mi></msup><mo>=</mo><mo>&#160;</mo><mi>n</mi><msup><mi>x</mi><mi>n</mi></msup><mo>&#160;</mo><mo>&#8658;</mo><mfrac><mi>m</mi><mi>n</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><msup><mi>x</mi><mi>n</mi></msup><msup><mi>x</mi><mi>m</mi></msup></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mi>m</mi></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mi>n</mi></msup></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mi>m</mi></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>x</mi><mi>n</mi></msup><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mi>n</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>m</mi><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mi>n</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>m</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><mi>n</mi></mrow><mrow><msup><mi>n</mi><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><mi>m</mi><mo>(</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mi>m</mi><mi>n</mi></mrow><mrow><msup><mi>x</mi><mi>n</mi></msup><mo>(</mo><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: " <p>69.</span><span style=\"font-family:Palanquin Dark\"> 70% of students in a class are girls and 70% of the girls have chosen soccer as their </span><span style=\"font-family:Palanquin Dark\">favourite</span><span style=\"font-family:Palanquin Dark\"> sport. If 98 girls have chosen soccer as their favourite sport, what is the total number of students in the class?</span></p>",
                    question_hi: " <p>69.</span><span style=\"font-family:Palanquin Dark\"> एक कक्षा में 70% छात्र लड़कियां हैं और 70% लड़कियों ने सॉकर को अपने पसंदीदा खेल के रूप में चुना है। यदि 98 लड़कियों ने सॉकर को अपने पसंदीदा खेल के रूप में चुना है, तो कक्षा में विद्यार्थियों की कुल संख्या कितनी है?</span></p>",
                    options_en: [" <p> 180</span></p>", " <p> 225</span></p>", 
                                " <p> 175</span></p>", " <p> 200</span></p>"],
                    options_hi: [" <p> 180</span></p>", " <p> 225</span></p>",
                                " <p> 175</span></p>", " <p> 200</span></p>"],
                    solution_en: " <p>69.(d)</span></p> <p><span style=\"font-family:Palanquin Dark\">Let the no of student in the class = 100</span></p> <p><span style=\"font-family:Palanquin Dark\">No of girls = 70</span></p> <p><span style=\"font-family:Palanquin Dark\">No of boys = 30</span></p> <p><span style=\"font-family:Palanquin Dark\">No of girls chose soccer = 70 × 70% = 49</span></p> <p><span style=\"font-family:Palanquin Dark\">49 unit = 98 (Given)</span></p> <p><span style=\"font-family:Palanquin Dark\">1 unit = 2</span></p> <p><span style=\"font-family:Palanquin Dark\">100 unit = 2 × 100 = 200</span></p>",
                    solution_hi: " <p>69.(d)</span></p> <p><span style=\"font-family:Palanquin Dark\">माना , कक्षा में विद्यार्थियों की संख्या = 100</span></p> <p><span style=\"font-family:Palanquin Dark\">लड़कियों की संख्या = 70</span></p> <p><span style=\"font-family:Palanquin Dark\">लड़कों की संख्या = 30</span></p> <p><span style=\"font-family:Palanquin Dark\">फ़ुटबॉल चुनने वाली लड़कियों की संख्या = 70 × 70% = 49</span></p> <p><span style=\"font-family:Palanquin Dark\">49 इकाई = 98 (दिया गया है) </span></p> <p><span style=\"font-family:Palanquin Dark\">1 इकाई = 2</span></p> <p><span style=\"font-family:Palanquin Dark\">100 इकाई = 2 × 100 = 200</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. <span style=\"font-family: Palanquin Dark;\">Study the given table carefully to answer the question that follows.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">The following table gives the information about the populations of five different cities in the year 2020.</span></p>\r\n<table style=\"border-collapse: collapse; width: 76.9847%; height: 326.25px;\" border=\"1\">\r\n<tbody>\r\n<tr style=\"height: 54.375px;\">\r\n<td style=\"width: 9.38068%; text-align: center; height: 54.375px;\">\r\n<p><strong>City&nbsp;</strong></p>\r\n</td>\r\n<td style=\"width: 27.8778%; text-align: center; height: 54.375px;\">\r\n<p><strong>Total Population&nbsp;</strong></p>\r\n</td>\r\n<td style=\"width: 17.5723%; text-align: center; height: 54.375px;\">\r\n<p><strong>% of men&nbsp;</strong></p>\r\n</td>\r\n<td style=\"width: 22.0644%; text-align: center; height: 54.375px;\">\r\n<p><strong>&nbsp; &nbsp;% of women&nbsp;</strong></p>\r\n</td>\r\n<td style=\"width: 23.1214%; text-align: center; height: 54.375px;\">\r\n<p><strong>% of children&nbsp;</strong></p>\r\n</td>\r\n</tr>\r\n<tr style=\"height: 54.375px;\">\r\n<td style=\"width: 9.38068%; text-align: center; height: 54.375px;\">\r\n<p><strong>P</strong></p>\r\n</td>\r\n<td style=\"width: 27.8778%; text-align: center; height: 54.375px;\">\r\n<p><strong>46800</strong></p>\r\n</td>\r\n<td style=\"width: 17.5723%; text-align: center; height: 54.375px;\">\r\n<p><strong>45 %</strong></p>\r\n</td>\r\n<td style=\"width: 22.0644%; text-align: center; height: 54.375px;\">\r\n<p><strong>35 %</strong></p>\r\n</td>\r\n<td style=\"width: 23.1214%; text-align: center; height: 54.375px;\">\r\n<p><strong>20 %</strong></p>\r\n</td>\r\n</tr>\r\n<tr style=\"height: 54.375px;\">\r\n<td style=\"width: 9.38068%; text-align: center; height: 54.375px;\">\r\n<p><strong>Q</strong></p>\r\n</td>\r\n<td style=\"width: 27.8778%; text-align: center; height: 54.375px;\">\r\n<p><strong>45400</strong></p>\r\n</td>\r\n<td style=\"width: 17.5723%; text-align: center; height: 54.375px;\">\r\n<p><strong>35 %</strong></p>\r\n</td>\r\n<td style=\"width: 22.0644%; text-align: center; height: 54.375px;\">\r\n<p><strong>50 %</strong></p>\r\n</td>\r\n<td style=\"width: 23.1214%; text-align: center; height: 54.375px;\">\r\n<p><strong>15 %</strong></p>\r\n</td>\r\n</tr>\r\n<tr style=\"height: 54.375px;\">\r\n<td style=\"width: 9.38068%; text-align: center; height: 54.375px;\">\r\n<p><strong>R</strong></p>\r\n</td>\r\n<td style=\"width: 27.8778%; text-align: center; height: 54.375px;\">\r\n<p><strong>42000</strong></p>\r\n</td>\r\n<td style=\"width: 17.5723%; text-align: center; height: 54.375px;\">\r\n<p><strong>48 %</strong></p>\r\n</td>\r\n<td style=\"width: 22.0644%; text-align: center; height: 54.375px;\">\r\n<p><strong>42 %</strong></p>\r\n</td>\r\n<td style=\"width: 23.1214%; text-align: center; height: 54.375px;\">\r\n<p><strong>10 %</strong></p>\r\n</td>\r\n</tr>\r\n<tr style=\"height: 54.375px;\">\r\n<td style=\"width: 9.38068%; text-align: center; height: 54.375px;\">\r\n<p><strong>S</strong></p>\r\n</td>\r\n<td style=\"width: 27.8778%; text-align: center; height: 54.375px;\">\r\n<p><strong>39250</strong></p>\r\n</td>\r\n<td style=\"width: 17.5723%; text-align: center; height: 54.375px;\">\r\n<p><strong>42 %</strong></p>\r\n</td>\r\n<td style=\"width: 22.0644%; text-align: center; height: 54.375px;\">\r\n<p><strong>42 %</strong></p>\r\n</td>\r\n<td style=\"width: 23.1214%; text-align: center; height: 54.375px;\">\r\n<p><strong>16 %</strong></p>\r\n</td>\r\n</tr>\r\n<tr style=\"height: 54.375px;\">\r\n<td style=\"width: 9.38068%; text-align: center; height: 54.375px;\">\r\n<p><strong>T</strong></p>\r\n</td>\r\n<td style=\"width: 27.8778%; text-align: center; height: 54.375px;\">\r\n<p><strong>46200</strong></p>\r\n</td>\r\n<td style=\"width: 17.5723%; text-align: center; height: 54.375px;\">\r\n<p><strong>40 %</strong></p>\r\n</td>\r\n<td style=\"width: 22.0644%; text-align: center; height: 54.375px;\">\r\n<p><strong>45 %</strong></p>\r\n</td>\r\n<td style=\"width: 23.1214%; text-align: center; height: 54.375px;\">\r\n<p><strong>15 %</strong></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n<p><span style=\"font-family: Palanquin Dark;\">In city R, among the children, the ratio of boys to girls was 2 : 3 and in city P, among the children, the ratio of boys to girls was 5 : 4. What was the total number of girls (among the children) in city P and city R together?</span></p>",
                    question_hi: "<p>70. <span style=\"font-family: Palanquin Dark;\">नीचे दिए गए प्रश्न का उत्तर देने के लिए दी गई तालिका का ध्यानपूर्वक अध्ययन करें।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">निम्न तालिका वर्ष 2020 में पांच अलग-अलग शहरों की आबादी के बारे में जानकारी देती है।</span></p>\r\n<table style=\"border-collapse: collapse; width: 81.3869%; height: 508px;\" border=\"1\">\r\n<tbody>\r\n<tr>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>शहर</strong></p>\r\n</td>\r\n<td style=\"width: 27.2629%; text-align: center;\">\r\n<p><strong>कुल जनसंख्या</strong></p>\r\n<p><strong>%&nbsp;</strong></p>\r\n</td>\r\n<td style=\"width: 18.4581%; text-align: center;\">\r\n<p><strong>पुरुषों का</strong></p>\r\n<p><strong>%&nbsp;</strong></p>\r\n</td>\r\n<td style=\"width: 20.3676%; text-align: center;\">\r\n<p><strong>औरतों का </strong></p>\r\n<p><strong>%&nbsp;</strong></p>\r\n</td>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>बच्चों का</strong></p>\r\n<p><strong> %</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>P</strong></p>\r\n</td>\r\n<td style=\"width: 27.2629%; text-align: center;\">\r\n<p><strong>46800</strong></p>\r\n</td>\r\n<td style=\"width: 18.4581%; text-align: center;\">\r\n<p><strong>45 %</strong></p>\r\n</td>\r\n<td style=\"width: 20.3676%; text-align: center;\">\r\n<p><strong>35 %</strong></p>\r\n</td>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>20 %</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>Q</strong></p>\r\n</td>\r\n<td style=\"width: 27.2629%; text-align: center;\">\r\n<p><strong>45400</strong></p>\r\n</td>\r\n<td style=\"width: 18.4581%; text-align: center;\">\r\n<p><strong>35 %</strong></p>\r\n</td>\r\n<td style=\"width: 20.3676%; text-align: center;\">\r\n<p><strong>50 %</strong></p>\r\n</td>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>15 %</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>R</strong></p>\r\n</td>\r\n<td style=\"width: 27.2629%; text-align: center;\">\r\n<p><strong>42000</strong></p>\r\n</td>\r\n<td style=\"width: 18.4581%; text-align: center;\">\r\n<p><strong>48 %</strong></p>\r\n</td>\r\n<td style=\"width: 20.3676%; text-align: center;\">\r\n<p><strong>42 %</strong></p>\r\n</td>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>10 %</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>S</strong></p>\r\n</td>\r\n<td style=\"width: 27.2629%; text-align: center;\">\r\n<p><strong>39250</strong></p>\r\n</td>\r\n<td style=\"width: 18.4581%; text-align: center;\">\r\n<p><strong>42 %</strong></p>\r\n</td>\r\n<td style=\"width: 20.3676%; text-align: center;\">\r\n<p><strong>42 %</strong></p>\r\n</td>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>16 %</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>T</strong></p>\r\n</td>\r\n<td style=\"width: 27.2629%; text-align: center;\">\r\n<p><strong>46200</strong></p>\r\n</td>\r\n<td style=\"width: 18.4581%; text-align: center;\">\r\n<p><strong>40 %</strong></p>\r\n</td>\r\n<td style=\"width: 20.3676%; text-align: center;\">\r\n<p><strong>45 %</strong></p>\r\n</td>\r\n<td style=\"width: 16.973%; text-align: center;\">\r\n<p><strong>15 %</strong></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n<p><span style=\"font-family: Palanquin Dark;\">शहर R में, बच्चों में लड़कों का लड़कियों से अनुपात 2 : 3 था और शहर P में, बच्चों में लड़कों का लड़कियों से अनुपात 5 : 4 था। शहर P और शहर R में मिलाकर लड़कियों (बच्चों के बीच) की कुल संख्या कितनी थी?</span></p>",
                    options_en: ["<p>6680</p>", "<p>4160</p>", 
                                "<p>6860</p>", "<p>4610</p>"],
                    options_hi: ["<p>6680</p>", "<p>4160</p>",
                                "<p>6860</p>", "<p>4610</p>"],
                    solution_en: "<p>70.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total population in city R = 42000</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Percentage of children in city R = 10%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">No of children = 42000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 4200</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">No of girls in city R =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 4200 = 2520</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total population in city P = 46800 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Percentage of children in city P = 20%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">No of children = 46800 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 9360</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">No of girls in city P = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 9360 = 4160</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total no of girls in city P and R = 2520 + 4160 = 6680</span></p>",
                    solution_hi: "<p>70.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">शहर R में कुल जनसंख्या = 42000</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">शहर R में बच्चों का प्रतिशत = 10%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">बच्चों की संख्या = 42000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> = 4200</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">शहर R में लड़कियों की संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 4200 = 2520</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">शहर P में कुल जनसंख्या = 46800</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">शहर P में बच्चों का प्रतिशत = 20%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">बच्चों की संख्या = 46800 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> = 9360</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">शहर P में लड़कियों की संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 9360 = 4160</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">शहर P और R में लड़कियों की कुल संख्या = 2520 + 4160 = 6680</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. <span style=\"font-family: Palanquin Dark;\">A loan of ₹8,925 is to be paid back in two equal half-yearly installments. How much is </span><span style=\"font-family: Palanquin Dark;\">each instalment if the interest is compounded half-yearly at 8% per annum?</span></p>",
                    question_hi: "<p>71.<span style=\"font-family: Arial Unicode MS;\"> ₹8,925 का ऋण दो समान अर्धवार्षिक किश्तों में चुकाया जाना है। यदि ब्याज अर्धवार्षिक रूप से 8% प्रति वर्ष की दर से संयोजित किया जाता है, तो प्रत्येक किस्त कितनी होगी ?</span></p>",
                    options_en: ["<p>₹4,372</p>", "<p>₹4,732</p>", 
                                "<p>₹4,654</p>", "<p>₹4,564</p>"],
                    options_hi: ["<p>₹4,372</p>", "<p>₹4,732</p>",
                                "<p>₹4,654</p>", "<p>₹4,564</p>"],
                    solution_en: "<p>71.(b)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Rate for half year = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Arial Unicode MS;\"> = 4% =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math> </span><span style=\"font-family: Arial Unicode MS;\">, No of cycle = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>6</mn></mfrac></math></span><span style=\"font-family: Arial Unicode MS;\"> = 2</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Principal&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;Amount</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&#160;</mo><mn>25</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mn>25</mn><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mn>26</mn><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: \'Arial Unicode MS\';\">__________________________</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>25</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mo>)</mo><mo>+</mo><mo>&#160;</mo><msup><mn>25</mn><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>26</mn><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>1275</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>1352</mn></math></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\"> 1275 : 1352</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">1275 unit &rarr; ₹8925</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">1 unit &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8925</mn><mn>1275</mn></mfrac></math></span><span style=\"font-family: Arial Unicode MS;\"> = 7</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">676 unit &rarr; 7 &times; 676 = ₹4732</span></p>",
                    solution_hi: "<p>71.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">अर्ध वर्ष के लिए दर = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 4% =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">, चक्र की संख्या =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>6</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">मूलधन&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; मिश्रधन</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&#160;</mo><mn>25</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mn>25</mn><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mn>26</mn><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: \'Palanquin Dark\';\">________________________&nbsp;&nbsp;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>26</mn><mo>&#160;</mo><mo>)</mo><mo>+</mo><msup><mn>25</mn><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>26</mn><mo>&#215;</mo><mn>26</mn><mo>&#160;</mo><mo>)</mo><mo>+</mo><msup><mn>26</mn><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>1275</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>1352</mn></math><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> 1275 : 1352</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">1275 unit &rarr; ₹8925</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">1 unit &rarr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8925</mn><mn>1275</mn></mfrac></math> </span><span style=\"font-family: Arial Unicode MS;\"> = 7</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">676 unit &rarr; 7 &times; 676 = ₹4732</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72.<span style=\"font-family: Arial Unicode MS;\"> Ramesh drives from his home at a speed of 40 km/h and reaches his college 25 minutes late. The next day he increases his speed by 10 km/h, yet he is late by 10 minutes. How far is his college from his home?</span></p>",
                    question_hi: "<p>72. <span style=\"font-family: Palanquin Dark;\">रमेश अपने घर से 40 km/h की गति से ड्राइव करता है और 25 मिनट देरी से अपने कॉलेज पहुंचता है। अगले दिन वह अपनी गति 10 km/h बढ़ा देता है, फिर भी वह 10 मिनट लेट हो जाता है। उसका कॉलेज उसके घर से कितनी दूर है?</span></p>",
                    options_en: ["<p>50 km</p>", "<p>55 km</p>", 
                                "<p>45 km</p>", "<p>60 km</p>"],
                    options_hi: ["<p>50 km</p>", "<p>55 km</p>",
                                "<p>45 km</p>", "<p>60 km</p>"],
                    solution_en: "<p>72.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">Distance =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>50</mn></mrow><mn>10</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>10</mn><mo>)</mo></mrow><mn>60</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\">= 200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>60</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 50 km</span></p>",
                    solution_hi: "<p>72.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">दूरी = </span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>50</mn></mrow><mn>10</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>10</mn><mo>)</mo></mrow><mn>60</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>60</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 50 km</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73.<span style=\"font-family: Palanquin Dark;\"> The following pie chart shows the percentage population of different states in the year 2021. The total population of the given states is 4879000.</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image36.png\" /></p>\n<p><span style=\"font-family: Palanquin Dark;\">The following table shows the sex wise population ratio of different states :</span></p>\n<p><span style=\"font-family: Palanquin Dark;\"><img src=\"https://ssccglpinnacle.com/images/mceu_96402640911656306676751.png\" /></span></p>\n<p><span style=\"font-family: Palanquin Dark;\">Study the above data and answer the following question :</span></p>\n<p><span style=\"font-family: Palanquin Dark;\">What is the ratio of the number of females in state P to the number of females in state S?</span></p>",
                    question_hi: "<p>73. <span style=\"font-family: Palanquin Dark;\">निम्न पाई चार्ट वर्ष 2021 में विभिन्न राज्यों की जनसंख्या का प्रतिशत दर्शाता है। दिए गए राज्यों की कुल जनसंख्या 4879000 है।</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1656138137/word/media/image36.png\" /></p>\n<p><span style=\"font-family: Palanquin Dark;\">निम्न तालिका विभिन्न राज्यों के लिंगवार जनसंख्या अनुपात को दर्शाती है:</span></p>\n<p><span style=\"font-family: Palanquin Dark;\"><img src=\"https://ssccglpinnacle.com/images/mceu_21036548111656306705522.png\" /></span></p>\n<p><span style=\"font-family: Palanquin Dark;\">उपरोक्त डेटा का अध्ययन करें और निम्नलिखित प्रश्न का उत्तर दें:</span></p>\n<p><span style=\"font-family: Palanquin Dark;\">राज्य P में महिलाओं की संख्या का राज्य S में महिलाओं की संख्या से अनुपात कितना है?</span></p>",
                    options_en: ["<p>2 : 7</p>", "<p>3 : 5</p>", 
                                "<p>6 : 11</p>", "<p>6 : 13</p>"],
                    options_hi: ["<p>2 : 7</p>", "<p>3 : 5</p>",
                                "<p>6 : 11</p>", "<p>6 : 13</p>"],
                    solution_en: "<p>73.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total population of the given states =&nbsp;4879000</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Percentage population of state P = 6%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Percentage population of state S = 13%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Ratio of males to female in both state = 4 : 5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Required ratio =&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\">&times; 4879000 &times; 6% : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 4879000 &times; 13% = 6 : 13</span></p>",
                    solution_hi: "<p>73.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">दिए गए राज्यों की कुल जनसंख्या = 4879000</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">राज्य P की प्रतिशत जनसंख्या = 6%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">राज्य S की प्रतिशत जनसंख्या = 13%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">दोनों राज्यों में पुरुषों का महिलाओं से अनुपात = 4 : 5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">आवश्यक अनुपात = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times; 4879000 &times; 6% : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 4879000 &times; 13% = 6 : 13</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74.<span style=\"font-family: Palanquin Dark;\"> In a triangle ABC, if the three sides are a=5, b=7 and c=3, what is angle B?</span></p>\n",
                    question_hi: "<p>74. <span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; ABC &#2350;&#2375;&#2306;, &#2351;&#2342;&#2367; &#2340;&#2368;&#2344; &#2349;&#2369;&#2332;&#2366;&#2319;&#2305; a = 5, b = 7 &#2324;&#2352; c =3 &#2361;&#2376;&#2306;, &#2340;&#2379; &#2325;&#2379;&#2339; B &#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2352;&#2375; ?</span></p>\n",
                    options_en: ["<p>1200</p>\n", "<p>600</p>\n", 
                                "<p>900</p>\n", "<p>1500</p>\n"],
                    options_hi: ["<p>1200</p>\n", "<p>600</p>\n",
                                "<p>900</p>\n", "<p>1500</p>\n"],
                    solution_en: "<p>74.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_6857424311670390117357.png\" width=\"149\" height=\"113\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Using cosine law,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mn>7</mn><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup></math><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> = 2 &times; 3 &times; 5 cosB</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">25 + 9 - 49 = 30 cosB</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">-15 = 30 cosB</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">CosB = -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = cos(180&deg; - 60&deg;)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Angle B = 120&deg;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Conditions apply - (360&times;3+120= 1200)</span></p>\n",
                    solution_hi: "<p>74.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_19786090411670390271442.png\" width=\"170\" height=\"129\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">cosine law &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352; ,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mn>7</mn><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup></math><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> = 2 &times; 3 &times; 5 cosB</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">25 + 9 - 49 = 30 cosB</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">-15 = 30 cosB</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">CosB = -&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-family: \'Palanquin Dark\';\">= cos(180&deg; - 60&shy;&deg;)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2379;&#2339; B = 120&deg;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2358;&#2352;&#2381;&#2340;&#2375;&#2306; &nbsp;- (360&deg;&times;3 +120&deg;= 1200&deg;)</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "18",
                    question_en: "<p>75.<span style=\"font-family: Palanquin Dark;\"> In the class, there are 29 boys and 21 girls. The average score of boys in Maths is 89, whereas the average score of girls in </span><span style=\"font-family: Palanquin Dark;\">maths</span><span style=\"font-family: Palanquin Dark;\"> is 91. What is the average of the whole class in maths?</span></p>",
                    question_hi: "<p>75.<span style=\"font-family: Palanquin Dark;\"> कक्षा में 29 लड़के और 21 लड़कियां हैं। गणित में लड़कों का औसत अंक 89 है, जबकि गणित में लड़कियों का औसत अंक 91 है। गणित में पूरी कक्षा का औसत क्या है?</span></p>",
                    options_en: ["<p>91</p>", "<p>89</p>", 
                                "<p>91.84</p>", "<p>89.84</p>"],
                    options_hi: ["<p>91</p>", "<p>89</p>",
                                "<p>91.84</p>", "<p>89.84</p>"],
                    solution_en: "<p><span style=\"font-family: Palanquin Dark;\">75.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> Boys : Girls </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">No of students &rarr; 29 : 21</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Average score &rarr; 89 : 89 + 2</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Average of whole class = 89 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn></mrow><mrow><mn>29</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>21</mn></mrow></mfrac></math></span><span style=\"font-family: Arial Unicode MS;\"> = 89 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>50</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Arial Unicode MS;\"> = 89 + 0.84 = 89.84</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\"> </span></p>",
                    solution_hi: "<p>75.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;लड़के : लड़कियाँ</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">छात्रों की संख्या </span><span style=\"font-family: Arial Unicode MS;\">&rarr; 29 : 21</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">औसत स्कोर &rarr; 89 : 89 + 2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">पूरी कक्षा का औसत = 89 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn></mrow><mrow><mn>29</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>21</mn></mrow></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 89 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>50</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 89 + 0.84 = 89.84</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "18",
                    question_en: "<p>76. <span style=\"font-family: Palanquin Dark;\">Which of the following dances is dedicated to temples and was earlier known as Sadir?</span></p>",
                    question_hi: "<p>76.<span style=\"font-family: Palanquin Dark;\"> निम्नलिखित में से कौन सा नृत्य मंदिरों को समर्पित है और इसे पहले सादिर (Sadir) के नाम से जाना जाता था ?</span></p>",
                    options_en: ["<p>Bharatanatyam</p>", "<p>Kathakali</p>", 
                                "<p>Kathak</p>", "<p>Manipuri</p>"],
                    options_hi: ["<p>भरतनाट्यम</p>", "<p>कथकली</p>",
                                "<p>कथक</p>", "<p>मणिपुरी</p>"],
                    solution_en: "<p>76.(a) <span style=\"font-family: Palanquin Dark;\">Bharatnatyam was earlier known as</span><span style=\"font-family: Palanquin Dark;\"> Sadir</span><span style=\"font-family: Palanquin Dark;\"> or </span><span style=\"font-family: Palanquin Dark;\">Dasi Attam</span><span style=\"font-family: Palanquin Dark;\">. It is strictly based on the </span><span style=\"font-family: Palanquin Dark;\">Natya Shastra</span><span style=\"font-family: Palanquin Dark;\">, originated from within the temple complexes. This is a dance that encompasses Bhav, Rag, Ras and Taal. The music of Bharatnatyam belongs to the</span><span style=\"font-family: Palanquin Dark;\"> Carnatic System</span><span style=\"font-family: Palanquin Dark;\"> of southern India. </span><span style=\"font-family: Palanquin Dark;\">Krishna Iyer</span><span style=\"font-family: Palanquin Dark;\"> first coined the term Bharatanatyam for the Sadir dance&rdquo;.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Extra Points :-&nbsp;&nbsp;</strong></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Artists associated with dance and other performing arts</strong></span></p>\r\n<table style=\"border-collapse: collapse; width: 100.021%; height: 67.1874px;\" border=\"1\">\r\n<tbody>\r\n<tr>\r\n<td style=\"width: 46.6842%; text-align: center;\"><strong>Dance and performing arts</strong></td>\r\n<td style=\"width: 46.6842%; text-align: center;\"><strong>Artists</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3958px;\">\r\n<td style=\"width: 46.6842%; height: 22.3958px;\">Kathak</td>\r\n<td style=\"width: 46.6842%; height: 22.3958px;\">Birju Maharaj, Gopi Krishna, Shambhu Maharaj, Sitara Devi, Prerna Shrimali, Sunayana Hazarilal, Kumudini Lakhia</td>\r\n</tr>\r\n<tr style=\"height: 22.3958px;\">\r\n<td style=\"width: 46.6842%; height: 22.3958px;\">Bharata Natyam</td>\r\n<td style=\"width: 46.6842%; height: 22.3958px;\">T. Balasaraswati, Rukmini Devi Arundel, Yamini Krishnamurthy, Vyjayanthimala, Ananda Shankar Jayant, C.V. Chandrashekhar, Guru (Ms.) M.K. Saroja, Shantha and VP Dhananjayan</td>\r\n</tr>\r\n<tr style=\"height: 22.3958px;\">\r\n<td style=\"width: 46.6842%; height: 22.3958px;\">Manipuri&nbsp;</td>\r\n<td style=\"width: 46.6842%; height: 22.3958px;\">Amubi Singh, Bino Devi, Rajkumar Singhjit Singh</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Mohini Attam</td>\r\n<td style=\"width: 46.6842%;\">Smt. Kalamandalam Kshemawati Pavithan, Dr.(Mrs.) Kanak Relay</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Chhau&nbsp;</td>\r\n<td style=\"width: 46.6842%;\">Makardhwaj Inspector, Pandit Gopal Prasad Dubey</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Odissi&nbsp;</td>\r\n<td style=\"width: 46.6842%;\">Kelucharan Mohapatra, Sonal Mansingh, Geeta Mahlik, Dr. Minati Mishra</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Kathakali</td>\r\n<td style=\"width: 46.6842%;\">PK Kunju Kurup, Kalamandalam Rajan, Madavur Vasudevan Nair, Kalamandalam Gopi, Kalamandalam Ramankutty Nair</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Kuchipudi&nbsp;</td>\r\n<td style=\"width: 46.6842%;\">Raja Reddy, Radha Reddy, Vyjayanthi Kashi, Vempati Chinna Satyam</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Yakshagana</td>\r\n<td style=\"width: 46.6842%;\">&nbsp;Ramachandra Subraya Hegde Chittani</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Sattriya&nbsp;</td>\r\n<td style=\"width: 46.6842%;\">Ghankanta Bora Borbayan</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Kudiyattam&nbsp;</td>\r\n<td style=\"width: 46.6842%;\">Ammannur Madhav Chakyar</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Pandavani&nbsp;</td>\r\n<td style=\"width: 46.6842%;\">Teejan Bai</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Kalbelia</td>\r\n<td style=\"width: 46.6842%;\">Gulabo snake charmer</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 46.6842%;\">Creative Dance / Choreography&nbsp;</td>\r\n<td style=\"width: 46.6842%;\">Uday Shankar</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>76.(a) <span style=\"font-family: Palanquin Dark;\">भरतनाट्यम को पहले </span><span style=\"font-family: Palanquin Dark;\">सदिर</span><span style=\"font-family: Palanquin Dark;\"> या </span><span style=\"font-family: Palanquin Dark;\">दासी अट्टम</span><span style=\"font-family: Palanquin Dark;\"> के नाम से जाना जाता था। यह वास्तव में </span><span style=\"font-family: Palanquin Dark;\">नाट्य शास्त्र</span><span style=\"font-family: Palanquin Dark;\"> पर आधारित है, जो मंदिर परिसर के भीतर से उत्पन्न हुआ है। यह एक ऐसा नृत्य है जिसमें भाव, राग, रास और ताल शामिल हैं। भरतनाट्यम का संगीत दक्षिणी भारत की </span><span style=\"font-family: Palanquin Dark;\">कर्नाटक प्रणाली </span><span style=\"font-family: Palanquin Dark;\">से संबंधित है। </span><span style=\"font-family: Palanquin Dark;\">कृष्ण अय्यर</span><span style=\"font-family: Palanquin Dark;\"> ने सबसे पहले सदिर नृत्य के लिए भरतनाट्यम शब्द गढ़ा था।</span></p>\r\n<p><span style=\"color: #236fa1;\"><strong><span style=\"font-family: Palanquin Dark;\">Extra Points :-&nbsp;&nbsp;</span></strong></span></p>\r\n<p><strong>भरतनाट्यम का अर्थ शब्द</strong></p>\r\n<ul>\r\n<li>भाव : भाव जिसका अर्थ है भावनाएँ</li>\r\n<li>रा : राग का अर्थ है संगीतमय नोट्स।</li>\r\n<li>ता : ताल का अर्थ है ताल।</li>\r\n<li>नाट्यम : नाट्यम नाटक के लिए संस्कृत शब्द।</li>\r\n</ul>\r\n<p><strong>कथकली के प्रसिद्ध कलाकार</strong></p>\r\n<ul>\r\n<li>कलामंडलम कृष्ण प्रसाद</li>\r\n<li>कलामंडलम केसवन नंबूदिरी</li>\r\n<li>कलामंडलम गोपी</li>\r\n</ul>\r\n<p><strong>मणिपुरी नृत्य की शैलियाँ</strong></p>\r\n<ul>\r\n<li>रास</li>\r\n<li>नाटा-संकीर्तन</li>\r\n<li>पुंग चोलम</li>\r\n<li>ढोला चोलम</li>\r\n<li>करतल चोलम</li>\r\n<li>थांग टा (मणिपुरी की एक मार्शल आर्ट) आदि।</li>\r\n</ul>\r\n<p><strong>कुचिपुड़ी के प्रसिद्ध नर्तक</strong></p>\r\n<ul>\r\n<li>राजा-राधा रेड्डी</li>\r\n<li>यामिनी रेड्डी</li>\r\n<li>वैजयंती काशी</li>\r\n<li>उमा राम राव आदि</li>\r\n</ul>\r\n<h4>भारत के शास्त्रीय व लोक नृत्य</h4>\r\n<table style=\"border-collapse: collapse; width: 45.203%; height: 857px;\" border=\"1\">\r\n<tbody>\r\n<tr>\r\n<th style=\"width: 48.5449%; text-align: center;\">शास्त्रीय नृत्य</th>\r\n<th style=\"width: 51.4841%; text-align: center;\">राज्य</th>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">भरतनाट्यम</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">तमिलनाडु</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">कथकली</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">केरल</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">मोहिनीअट्टम</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">केरल</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">ओडिसी</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">उड़ीसा</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">कुचिपुड़ी</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">आंध्र प्रदेश</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">मणिपुरी</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">मणिपुर</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">कथक</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">उत्तर भारत मुख्य रूप से यू.पी.</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">सत्त्रिया नृत्य</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">असम</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">झूमर</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">राजस्थान</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">गरबा</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">गुजरात</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">गिद्धा</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">पंजाब</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">भांगड़ा</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">पंजाब</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">यक्षगान</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">कर्नाटक</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">मयूरभंज छाउ</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">उड़ीसा</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">पुरुलिया छाउ</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">पश्चिम बंगाल</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">तमाशा</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">महाराष्ट्र</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">लावणी</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">महाराष्ट्र</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">कालबेलिया</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">राजस्थान</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">बिहु</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">असम</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">कछी घोड़ी</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">राजस्थान</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">रौफ</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">जम्मू और कश्मीर</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">राउत नच</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">छत्तीसगढ़</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">करकट्टम</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">तमिलनाडु</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 48.5449%; text-align: center;\">होजागिरी</td>\r\n<td style=\"width: 51.4841%; text-align: center;\">त्रिपुरा</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n<ul>\r\n<li><strong>नृत्यों और अन्य प्रदर्शन कलाओं से जुड़े कलाकार</strong></li>\r\n</ul>\r\n<table style=\"border-collapse: collapse; width: 55.6179%; height: 580px;\" border=\"1\">\r\n<tbody>\r\n<tr>\r\n<th style=\"width: 50.0065%; text-align: center;\">नृत्य एवं प्रदर्शन कला</th>\r\n<th style=\"width: 50.0065%; text-align: center;\">जुड़े कलाकार</th>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">कथक</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">बिर्जु महाराज, गोपी कृष्ण, शम्भू महाराज, सितारा देवी, प्रेरणा श्रीमाली, सुनयना हज़ारीलाल , कुमुदिनि लखिया</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">भरत नाट्यम</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">टी. बालसरस्वति, रुक्मिणी देवी अरूंडेल, यामिनि कृष्णमूर्ति, वैजयंतीमाला, आनंद शंकर जयंत, सी.वी. चंद्रशेखर, गुरु (सुश्री) एम.के. सरोजा, शांता और वीपी धनंजयंन</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">मणिपुरी</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">अमुबि सिंह, बिनो देवी, राजकुमार सिंहजीत सिंह</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">मोहिनी अट्टम</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">श्रीमती कलामंडलम क्षेमावती पवित्रन, डॉ.(श्रीमती) कनक रेले , <span style=\"font-weight: 400;\">डॉ. सुनंदा नायर</span></td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">छाऊ</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">मकरध्वज दरोघा, पंडित गोपाल प्रसाद दुबे</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">ओडिसी</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">केलुचरण महापात्र, सोनल मानसिंह, गीता महलिक, डॉ. मिनाती मिश्रा</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">कथकली</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">पी० के० कुंजु कुरुप, कलामंडलम राजन, मदवुर वासुदेवन नायर, कलामंडलम गोपी, कलामंडलम रमणकुट्टी नायर</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">कुचीपुड़ी</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">राजा रेड्डी, राधा रेड्डी, वैजयंती काशी, वेम्पति चिन्ना सत्यम</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">यक्षगान</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">रामचंद्र सुब्रया हेगड़े चित्तनि</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">सात्त्रिया</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">घनकंता बोरा बोरबयाँ</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">कुड़िअट्टम</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">अम्मन्नुर माधव चक्यर</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">पांडवनी</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">तीजन बाई</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">कलबेलिया</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">गुलाबो सपेरा</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 50.0065%; text-align: center;\">क्रिएटिव नृत्य / कोरियोग्राफी</td>\r\n<td style=\"width: 50.0065%; text-align: center;\">उदय शंकर</td>\r\n</tr>\r\n</tbody>\r\n</table>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "18",
                    question_en: " <p>77.</span><span style=\"font-family:Palanquin Dark\"> Which level of the food chain includes millipede, springtails, woodlice, dung flies and slugs that feed on the dead or decaying plants or animals?</span></p>",
                    question_hi: "<p>77. <span style=\"font-family: Palanquin Dark;\">खाद्य श्रृंखला के किस स्तर में मिलीपेड, स्प्रिंगटेल, वुडलाइस, गोबर मक्खियाँ और घोंघा शामिल हैं जो मृत या सड़ने वाले पौधों या जानवरों को खाते हैं?</span></p>",
                    options_en: [" <p> Carnivores</span></p>", " <p> Detritivores</span></p>", 
                                " <p> Omnivores</span></p>", " <p> Herbivores</span></p>"],
                    options_hi: ["<p>मांसाहारी</p>", "<p>अपरदाहारी</p>",
                                "<p>सर्वाहारी</p>", "<p>शाकाहारी</p>"],
                    solution_en: " <p>77.(b) Detritivores </span><span style=\"font-family:Palanquin Dark\">an organism (such as an earthworm or a fungus) that feeds on dead and decomposing organic matter. A </span><span style=\"font-family:Palanquin Dark\">carnivore </span><span style=\"font-family:Palanquin Dark\">(Lion, Wolf) is an organism that mostly eats meat, or the flesh of animals. An </span><span style=\"font-family:Palanquin Dark\">omnivore </span><span style=\"font-family:Palanquin Dark\">(Pigs, Dogs, Bear) is an organism that eats plants and animals. An </span><span style=\"font-family:Palanquin Dark\">herbivore </span><span style=\"font-family:Palanquin Dark\">(Cows, </span><span style=\"font-family:Palanquin Dark\">buffalo</span><span style=\"font-family:Palanquin Dark\">) is an organism that mostly feeds on plants.</span></p>",
                    solution_hi: "<p>77.(b) <span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"><strong>अपरदाहारी (Detritivores</strong>)</span><span style=\"font-family: Palanquin Dark;\"> - ऐसे जीव हैं जो मृत और विघटित कार्बनिक पदार्थों को खाते हैं , जैसे एक केंचुआ या कवक। </span></p>\r\n<p><strong><span style=\"font-family: Palanquin Dark;\">मांसाहारी</span><span style=\"font-family: Palanquin Dark;\"> (</span></strong><span style=\"font-family: Palanquin Dark;\"><strong>carnivore) </strong>-</span><span style=\"font-family: Palanquin Dark;\"> ऐसे जीव हैं जो ज्यादातर मांस या जानवरों का मांस खाते हैं, जैसे शेर और भेड़िया । </span></p>\r\n<p><strong><span style=\"font-family: Palanquin Dark;\">सर्वभक्षी</span><span style=\"font-family: Palanquin Dark;\"> (</span></strong><span style=\"font-family: Palanquin Dark;\"><strong>omnivore) </strong>- </span><span style=\"font-family: Palanquin Dark;\">ऐसे जीव हैं जो पौधों और जानवरों को खाते हैं , जैसे सूअर, कुत्ते और भालू। </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><strong>शाकाहारी (herbivore) </strong>- </span><span style=\"font-family: Palanquin Dark;\">ऐसे जीव हैं जो ज्यादातर पौधों को खाते हैं , जैसे गाय और भैंस।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "18",
                    question_en: " <p>78.</span><span style=\"font-family:Palanquin Dark\"> Which of the following metals having atomic number 3 is a soft, silvery metal with very low density that reacts vigorously with water and corrodes quickly in air?</span></p>",
                    question_hi: "<p>78. <span style=\"font-family: Palanquin Dark;\">निम्नलिखित में से कौन सी धातु जिसका परमाणु क्रमांक 3 है, बहुत कम घनत्व वाली एक नरम , चांदी जैसी धातु है जो पानी के साथ तेजी से प्रतिक्रिया करती है और हवा में जल्दी से खराब हो जाती है?</span></p>",
                    options_en: [" <p> Potassium</span></p>", " <p> Sodium</span></p>", 
                                " <p> Lithium</span></p>", " <p> Rubidium</span></p>"],
                    options_hi: ["<p>पोटेशियम</p>", "<p>सोडियम</p>",
                                "<p>लिथियम</p>", "<p>रूबिडियम</p>"],
                    solution_en: " <p>78.(c) Lithium </span><span style=\"font-family:Palanquin Dark\">is a soft, silvery metal. It reacts vigorously with water.  It has the lowest density of all metals. Lithium is the first of the alkalis in the periodic table. It is highly reactive and does not occur freely in nature.Lithium is the only alkali metal that reacts with nitrogen. Lithium was discovered by </span><span style=\"font-family:Palanquin Dark\">Johan Arfvedson</span><span style=\"font-family:Palanquin Dark\"> in 1817 Potassium Atomic Number is 19. Sodium (11). Rubidium (37).</span></p>",
                    solution_hi: "<p>78.(c) <strong>लिथियम</strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>एक नरम धातु है। यह पानी के साथ तीव्रता से प्रतिक्रिया करता है। सभी धातुओं में इसका घनत्व सबसे कम है। लिथियम आवर्त सारणी में सबसे पहला क्षार है। यह अत्यधिक प्रतिक्रियाशील है और प्रकृति में स्वतंत्र रूप से नहीं होता है। लिथियम एकमात्र क्षार धातु है जो नाइट्रोजन के साथ प्रतिक्रिया करता है। लिथियम की खोज 1817 में </span><span style=\"font-family: Palanquin Dark;\">जोहान आरफवेडसन</span><span style=\"font-family: Palanquin Dark;\"> ने की थी। पोटेशियम , सोडियम तथा रूबिडियम का परमाणु संख्या क्रमश: 19,11,37 है ।</span></p>\r\n<p><strong><span style=\"font-family: Palanquin Dark;\">Extra Point :-</span></strong></p>\r\n<ul>\r\n<li><span style=\"font-family: Palanquin Dark;\">धातु + &nbsp;ऑक्सीजन &rarr; धातु ऑक्साइड</span></li>\r\n<li><span style=\"font-family: Palanquin Dark;\">धातु + जल &nbsp;&rarr; धातु ऑक्साइड + हाइड्रोजन </span></li>\r\n<li><span style=\"font-family: Palanquin Dark;\">धातु ऑक्साइड + जल &nbsp;&rarr; धातु हाइड्रोक्साइड </span></li>\r\n<li><span style=\"font-family: Palanquin Dark;\">धातु + तनु अम्ल &rarr; लवण + हाइड्रोजन&nbsp;</span></li>\r\n</ul>\r\n<p><span style=\"font-family: Palanquin Dark;\">⦁ <strong>सक्रियता श्रेणी </strong>- &nbsp;वह श्रेणी जिसमें तत्वों को उनकी क्रियाशीलता के आधार पर व्यवस्थित किया जाता है ,उसे &nbsp;सक्रियता श्रेणी कहते है ।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><img src=\"https://ssccglpinnacle.com/images/mceu_50995290411656309700328.png\" width=\"470\" height=\"340\" /></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "18",
                    question_en: " <p>79. </span><span style=\"font-family:Palanquin Dark\">The Article of the Indian Constitution that deals with all the executive powers of the Governor is ________.</span></p>",
                    question_hi: "<p>79.<span style=\"font-family: Palanquin Dark;\"> भारतीय संविधान का ________ अनुच्छेद जो राज्यपाल की सभी कार्यकारी शक्तियों से संबंधित है।</span></p>",
                    options_en: [" <p> Article 150</span></p>", " <p> Article 157</span></p>", 
                                " <p> Article 154</span></p>", " <p> Article 156</span></p>"],
                    options_hi: ["<p>अनुच्छेद 150</p>", "<p>अनुच्छेद 157</p>",
                                "<p>अनुच्छेद 154</p>", "<p>अनुच्छेद 156</p>"],
                    solution_en: " <p>79.(c)</span><span style=\"font-family:Palanquin Dark\"> </span><span style=\"font-family:Palanquin Dark\">Article 154-</span><span style=\"font-family:Palanquin Dark\"> Executive power of Governor.  </span><span style=\"font-family:Palanquin Dark\">Article 150</span><span style=\"font-family:Palanquin Dark\">- Form of accounts of the Union and of the States. </span><span style=\"font-family:Palanquin Dark\">Article 157</span><span style=\"font-family:Palanquin Dark\">- Qualifications for appointment as Governor.</span><span style=\"font-family:Palanquin Dark\"> Article 156</span><span style=\"font-family:Palanquin Dark\">- Term of office of Governor.</span></p>",
                    solution_hi: "<p>79.(c)<span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">अनुच्छेद 154</span><span style=\"font-family: Palanquin Dark;\">- राज्यपाल की कार्यपालिका शक्ति। </span><span style=\"font-family: Palanquin Dark;\">अनुच्छेद 150</span><span style=\"font-family: Palanquin Dark;\">- संघ और राज्यों के लेखाओं का प्रारूप। </span><span style=\"font-family: Palanquin Dark;\">अनुच्छेद 157</span><span style=\"font-family: Palanquin Dark;\">- राज्यपाल के रूप में नियुक्ति के लिए योग्यताएँ। </span><span style=\"font-family: Palanquin Dark;\">अनुच्छेद 156</span><span style=\"font-family: Palanquin Dark;\">- राज्यपाल का कार्यकाल।</span></p>\r\n<p><strong>राष्ट्रपति -&nbsp;</strong></p>\r\n<ul>\r\n<li>अनुच्छेद - 53 संघ की कार्यपालिका शक्ति राष्ट्रपति में निहित होगी जिसका प्रयोग<br />वह प्रधानमंत्री और मंत्रिपरिषद की सलाह के अनुसार करेगा।</li>\r\n<li>अनु़च्छेद - 54 राष्ट्रपति का निर्वाचक मण्डल इसमें संसद और विधानसंभाओं के<br />निर्वाचित सदस्य भाग लेते है। 70 वां संविधान संशोधन 1992 द्वारा दिल्ली और<br />पांडिचेरी को इसके अन्तर्गत शामिल किया गया है।<br />राष्ट्रपति के निर्वाचन में मनोनीत तथा विधान परिषदों के सदस्य भाग नहीं लेते है।</li>\r\n<li>अनुच्छेद - 55 निर्वाचन की विधि</li>\r\n<li>अनुच्छेद - 57 राष्ट्रपति पुर्ननिर्वाचित होने की योग्यता</li>\r\n<li>अनुच्छेद -58 राष्ट्रपति बनने की योग्यता<br />वह भारत का नागरिक हो<br />आयु 35 वर्ष<br />वह किसी लाभ के पद पर कार्यरत न हो उपराष्ट्रपति, राज्यपाल, मंत्री लाभ<br />के पद नहीं है।<br />उसमें लोकसभा सदस्य बनने की योग्यता हो।</li>\r\n<li>अनुच्छेद - 59 राष्ट्रपति पद के लिए शर</li>\r\n<li>अनुच्छेद - 60 राष्ट्रपति की शपथ<br />सर्वोच्च न्यायलय के मुख्य न्यायधीश अनुपस्थिति में वरिष्टम न्यायधिश द्वारा शपथ<br />दिलाई जाती है।</li>\r\n<li>अनुच्छेद - 61 महावियोग&nbsp;</li>\r\n<li>अनुच्छेद 79 के अन्तर्गत राष्ट्रपति संसद या व्यवस्थापिका का अभिन्न अंग है।</li>\r\n</ul>\r\n<p><strong><u>भारतीय गणतंत्र का उपराष्ट्रपति राज्य सभा के सभापति के रूप में</u></strong></p>\r\n<ul>\r\n<li>भारत के संविधान का <strong>अनुच्छेद 63 </strong>यह उपबंध करता है कि भारत का एक उपराष्ट्रपति होगा।</li>\r\n<li><strong>अनुच्छेद 64 और 89</strong> यह उपबंध करते हैं कि भारत का उपराष्ट्रपति राज्य सभा का पदेन सभापति होगा और अन्य कोई लाभ का पद धारण नहीं करेगा ।</li>\r\n<li>संवैधानिक व्यवस्था में उपराष्ट्रपति पद का धारक कार्यपालिका का भाग है परंतु राज्य सभा के सभापति के रूप में वह संसद का भाग है। इस प्रकार उपराष्ट्रपति की दोहरी भूमिका होती है और वह दो अलग-अलग और पृथक पद धारण करता है।</li>\r\n</ul>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "18",
                    question_en: "<p>80. <span style=\"font-family: Palanquin Dark;\">Which water body covers an area of 1,55,58,000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>k</mi><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> and makes up only 4.3% of the global ocean?</span></p>",
                    question_hi: "<p>80.<span style=\"font-family: Palanquin Dark;\"> कौन सा जल निकाय 1,55,58,000&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>k</mi><msup><mi>m</mi><mn>2</mn></msup></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> के क्षेत्र को कवर करता है और वैश्विक महासागर का केवल 4.3% हिस्सा बनाता है?</span></p>",
                    options_en: ["<p>Atlantic Ocean</p>", "<p>Arctic Ocean</p>", 
                                "<p>Southern Ocean</p>", "<p>Indian Ocean</p>"],
                    options_hi: ["<p>अटलांटिक महासागर</p>", "<p>आर्कटिक महासागर</p>",
                                "<p>दक्षिणी महासागर</p>", "<p>हिंद महासागर</p>"],
                    solution_en: "<p>80.(b) <span style=\"font-family: Palanquin Dark;\">The </span><span style=\"font-family: Palanquin Dark;\">Arctic Ocean</span><span style=\"font-family: Palanquin Dark;\"> is the smallest and shallowest of the world\'s five major oceans. The Arctic Ocean is centered approximately on the North Pole. The</span><span style=\"font-family: Palanquin Dark;\"> Pacific Ocean</span><span style=\"font-family: Palanquin Dark;\"> represents 45 percent of the global ocean coverage. The </span><span style=\"font-family: Palanquin Dark;\">Atlantic Ocean</span><span style=\"font-family: Palanquin Dark;\"> is the world\'s second-largest ocean, with 22 percent of the global sea area. The Indian Ocean occupies 20 percent of the global sea area. The </span><span style=\"font-family: Palanquin Dark;\">Southern Ocean</span><span style=\"font-family: Palanquin Dark;\"> circles Antarctica and connects the Indian, Atlantic, and Pacific oceans.</span></p>",
                    solution_hi: "<p>80.(b) आर्कटिक महासागर<span style=\"font-family: Palanquin Dark;\"> दुनिया के पांच प्रमुख महासागरों में सबसे छोटा और उथला है। आर्कटिक महासागर लगभग उत्तरी ध्रुव पर केंद्रित है। </span><span style=\"font-family: Palanquin Dark;\">प्रशांत महासागर</span><span style=\"font-family: Palanquin Dark;\"> वैश्विक महासागर कवरेज के 45 प्रतिशत का प्रतिनिधित्व करता है। </span><span style=\"font-family: Palanquin Dark;\">अटलांटिक महासागर </span><span style=\"font-family: Palanquin Dark;\">दुनिया का दूसरा सबसे बड़ा महासागर है, जिसमें वैश्विक समुद्री क्षेत्र का 22 प्रतिशत हिस्सा है। </span><span style=\"font-family: Palanquin Dark;\">हिंद महासागर</span><span style=\"font-family: Palanquin Dark;\"> वैश्विक समुद्री क्षेत्र के 20 प्रतिशत हिस्से पर पाया जाता है। </span><span style=\"font-family: Palanquin Dark;\">दक्षिणी महासागर </span><span style=\"font-family: Palanquin Dark;\">अंटार्कटिका को घेरता है और हिंद महासागर, अटलांटिक और प्रशांत महासागरों को जोड़ता है।</span></p>\r\n<div class=\"co8aDb\" role=\"heading\" aria-level=\"3\"><strong>प्रमुख&nbsp;महासागर निम्नलिखित हैं:-</strong></div>\r\n<div class=\"RqBzHd\">\r\n<ul class=\"i8Z77e\">\r\n<li class=\"TrT0Xe\">प्रशान्त&nbsp;<strong>महासागर</strong> ( Pacific Ocean)</li>\r\n<li class=\"TrT0Xe\">अटलांटिक&nbsp;<strong>महासागर</strong> ( Atlantic Ocean)</li>\r\n<li class=\"TrT0Xe\">हिन्द&nbsp;<strong>महासागर</strong> ( Indian Ocean)</li>\r\n<li class=\"TrT0Xe\">आर्कटिक&nbsp;<strong>महासागर</strong> ( Arctic Ocean )</li>\r\n<li class=\"TrT0Xe\">अंटार्कटिका ( Antarctica Ocean )</li>\r\n</ul>\r\n</div>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "18",
                    question_en: " <p>81.</span><span style=\"font-family:Palanquin Dark\"> In May 2021, in the MR Vijayabhaskar vs ___________ case the Supreme Court observed that freedom of speech and expression also extends to reporting the proceedings that happen in courts, including oral observations made by judges.</span></p>",
                    question_hi: " <p>81. </span><span style=\"font-family:Palanquin Dark\">मई 2021 में, एम.आर. विजयभास्कर बनाम ___________ मामले में , सुप्रीम कोर्ट ने देखा कि भाषण और अभिव्यक्ति की स्वतंत्रता भी अदालतों में होने वाली कार्यवाही की रिपोर्टिंग तक फैली हुई है, जिसमें न्यायाधीशों द्वारा की गई मौखिक टिप्पणियां भी शामिल हैं।</span></p>",
                    options_en: [" <p> Comptroller and Auditor General</span></p>", " <p> Chairperson of the Union Public Service Commission</span></p>", 
                                " <p> Chief Election Commissioner</span></p>", " <p> Speaker of the Lok Sabha</span></p>"],
                    options_hi: [" <p> नियंत्रक और महालेखा परीक्षक</span></p>", " <p> संघ लोक सेवा आयोग के अध्यक्ष</span></p>",
                                " <p> मुख्य चुनाव आयुक्त</span></p>", " <p> लोकसभा अध्यक्ष</span></p>"],
                    solution_en: " <p>81.(c) </span><span style=\"font-family:Palanquin Dark\">Chief Election Commissioner. As of June 2022, Chief Election Commissioner of India- Shri Rajiv Kumar, Comptroller and Auditor General - Shri Girish Chandra Murmu, Chairperson of the Union Public Service Commission - Manoj Soni, Speaker of the Lok Sabha - Om Birla. </span></p>",
                    solution_hi: " <p>81.(c) </span><span style=\"font-family:Palanquin Dark\">मुख्य चुनाव आयुक्त। भारत के मुख्य चुनाव आयुक्त- श्री राजीव कुमार, नियंत्रक और महालेखा परीक्षक-श्री गिरीश चंद्र मुर्मू, संघ लोक सेवा आयोग के अध्यक्ष- मनोज सोनी, लोकसभा अध्यक्ष- ओम बिरला (जून 2022 तक)।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "18",
                    question_en: " <p>82. </span><span style=\"font-family:Palanquin Dark\">In February 2022, who was appointed as director of National Council of Educational Research and Training (NCERT)?</span></p>",
                    question_hi: " <p>82.</span><span style=\"font-family:Palanquin Dark\"> फरवरी 2022 में, राष्ट्रीय शैक्षिक अनुसंधान और प्रशिक्षण परिषद (NCERT) के निदेशक के रूप में किसे नियुक्त किया गया था?</span></p>",
                    options_en: [" <p> Professor Hrushikesh Senapaty</span></p>", " <p> Professor Dinesh Prasad Saklani</span></p>", 
                                " <p> Professor MC Sharma</span></p>", " <p> Professor J S Rajput</span></p>"],
                    options_hi: [" <p> प्रोफेसर हृषिकेश सेनापति</span></p>", " <p> प्रोफेसर दिनेश प्रसाद सकलानी</span></p>",
                                " <p> प्रोफेसर एम.सी. शर्मा</span></p>", " <p> प्रोफेसर जे.एस. राजपूत</span></p>"],
                    solution_en: " <p><span style=\"font-family:Palanquin Dark\">82.(b) Director of NCERT- </span><span style=\"font-family:Palanquin Dark\">Professor Dinesh Prasad Saklani. The National Council of Educational Research and Training is an autonomous </span><span style=\"font-family:Palanquin Dark\">organisation</span><span style=\"font-family:Palanquin Dark\"> of the Government of India which was established in 1961 as a literary, scientific and charitable Society under the Societies Registration Act. Headquarter is at New Delhi. </span></p>",
                    solution_hi: " <p>82.(b)  NCERT के निदेशक</span><span style=\"font-family:Palanquin Dark\">- प्रोफेसर दिनेश प्रसाद सकलानी। राष्ट्रीय शैक्षिक अनुसंधान और प्रशिक्षण परिषद भारत सरकार का एक स्वायत्त ( autonomous ) संगठन है जिसे 1961 में सोसायटी पंजीकरण अधिनियम के तहत एक साहित्यिक, वैज्ञानिक और धर्मार्थ सोसायटी के रूप में स्थापित किया गया था। मुख्यालय नई दिल्ली में है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "18",
                    question_en: " <p>83.</span><span style=\"font-family:Palanquin Dark\"> The Nooran Sisters are renowned for which of the following genres of Indian Music?</span></p>",
                    question_hi: "<p>83. <span style=\"font-family: Palanquin Dark;\">नूरां बहनें भारतीय संगीत की निम्नलिखित में से किस विधा के लिए प्रसिद्ध हैं?</span></p>",
                    options_en: [" <p> Qawwali</span></p>", " <p> Sufi</span></p>", 
                                " <p> Ghazals</span></p>", " <p> Hindustani Classical</span></p>"],
                    options_hi: ["<p>कव्वाली</p>", "<p>सूफी</p>",
                                "<p>ग़ज़ल</p>", "<p>हिंदुस्तानी शास्त्रीय</p>"],
                    solution_en: " <p>83.(b)</span><span style=\"font-family:Palanquin Dark\"> </span><span style=\"font-family:Palanquin Dark\">The Nooran Sisters</span><span style=\"font-family:Palanquin Dark\"> (Jyoti Nooran and Sultana Nooran) are a Sufi singing duo from Jalandhar, India. They perform Sham Chaurasia gharana classical music.</span></p>",
                    solution_hi: "<p>83.(b) <span style=\"font-family: Palanquin Dark;\">नूरां</span> सिस्टर्स<span style=\"font-family: Palanquin Dark;\"> (ज्योति नूरन और सुल्ताना नूरन) भारत के जालंधर की एक सूफी गायन जोड़ी हैं। वे शाम चौरसिया घराना शास्त्रीय संगीत करते हैं।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "18",
                    question_en: " <p>84.</span><span style=\"font-family:Palanquin Dark\"> Muriatic acid is another name of which compound used in the production of chlorides, fertilisers, and dyes, in electroplating, and in the photographic, textile and rubber industries?</span></p>",
                    question_hi: " <p>84.</span><span style=\"font-family:Palanquin Dark\"> म्यूरिएटिक एसिड किस यौगिक का दूसरा नाम है जिसका उपयोग क्लोराइड, उर्वरक और रंगों के उत्पादन में, इलेक्ट्रोप्लेटिंग में, और फोटोग्राफिक, कपड़ा और रबर उद्योगों में किया जाता है?</span></p>",
                    options_en: [" <p> Perchloric Acid</span></p>", " <p> Sulfuric acid</span></p>", 
                                " <p> Hydrochloric acid</span></p>", " <p> Nitric acid</span></p>"],
                    options_hi: [" <p> परक्लोरिक एसिड</span></p>", " <p> सल्फ्यूरिक एसिड</span></p>",
                                " <p> हाइड्रोक्लोरिक एसिड</span></p>", " <p> नाइट्रिक एसिड</span></p>"],
                    solution_en: " <p>84.(c)</span><span style=\"font-family:Palanquin Dark\"> Muriatic acid is another name for </span><span style=\"font-family:Palanquin Dark\">Hydrochloric acid (HCl)</span><span style=\"font-family:Palanquin Dark\">. It is a component of the gastric acid in the digestive systems of most animal species, including humans. </span><span style=\"font-family:Palanquin Dark\">Perchloric acid </span><span style=\"font-family:Palanquin Dark\">(</span><span style=\"font-family:Palanquin Dark\">HClO4) </span><span style=\"font-family:Palanquin Dark\">is also called </span><span style=\"font-family:Palanquin Dark\">Hyperchloric</span><span style=\"font-family:Palanquin Dark\"> acid (HClO4) or </span><span style=\"font-family:Palanquin Dark\">hydroxidotrioxidochlorine</span><span style=\"font-family:Palanquin Dark\">. </span><span style=\"font-family:Palanquin Dark\">Sulphuric Acid </span><span style=\"font-family:Palanquin Dark\">(H2SO4), also called oil of vitriol, or hydrogen sulfate. </span><span style=\"font-family:Palanquin Dark\">Nitric Acid</span><span style=\"font-family:Palanquin Dark\"> is a strong acid with chemical formula HNO3. It is also known as the spirit of niter and aqua fortis.</span></p>",
                    solution_hi: " <p>84.(c) हाइड्रोक्लोरिक एसिड (HCl)</span><span style=\"font-family:Palanquin Dark\"> का दूसरा नाम </span><span style=\"font-family:Palanquin Dark\">म्यूरिएटिक एसिड</span><span style=\"font-family:Palanquin Dark\"> है। यह मनुष्यों सहित अधिकांश पशु प्रजातियों के पाचन तंत्र में गैस्ट्रिक एसिड का एक घटक है। </span><span style=\"font-family:Palanquin Dark\">परक्लोरिक एसिड</span><span style=\"font-family:Palanquin Dark\"> (</span><span style=\"font-family:Palanquin Dark\">HClO₄)</span><span style=\"font-family:Palanquin Dark\"> को हाइपरक्लोरिक एसिड (</span><span style=\"font-family:Palanquin Dark\">HClO₄)</span><span style=\"font-family:Palanquin Dark\"> या हाइड्रॉक्सीडोट्रायॉक्सिडोक्लोरिन भी कहा जाता है। </span><span style=\"font-family:Palanquin Dark\">सल्फ्यूरिक एसिड</span><span style=\"font-family:Palanquin Dark\"> (</span><span style=\"font-family:Palanquin Dark\">H₂SO₄)</span><span style=\"font-family:Palanquin Dark\">,</span><span style=\"font-family:Palanquin Dark\"> जिसे विट्रियल का तेल (oil of vitriol) या हाइड्रोजन सल्फेट भी कहा जाता है। </span><span style=\"font-family:Palanquin Dark\">नाइट्रिक अम्ल </span><span style=\"font-family:Palanquin Dark\">एक प्रबल अम्ल है जिसका रासायनिक सूत्र </span><span style=\"font-family:Palanquin Dark\">HNO₃</span><span style=\"font-family:Palanquin Dark\"> है। इसे स्पिरिट ऑफ़ निटर और एक्‍वा फ़ोर्टिस के रूप में भी जाना जाता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "18",
                    question_en: "<p>85.<span style=\"font-family: Palanquin Dark;\"> Who among the following Indian Players wrote the Autobiography named &lsquo;Playing to </span><span style=\"font-family: Palanquin Dark;\">Win&rsquo;?</span></p>",
                    question_hi: " <p>85.</span><span style=\"font-family:Palanquin Dark\"> निम्नलिखित में से किस भारतीय खिलाड़ी ने \'प्लेइंग टू विन\' नामक आत्मकथा लिखी है?</span></p>",
                    options_en: ["<p>Sania Mirza</p>", "<p>P V Sindhu</p>", 
                                "<p>Saina Nehwal</p>", "<p>Karnam Malleshwari</p>"],
                    options_hi: [" <p> सानिया मिर्जा</span></p>", " <p> पी वी सिंधु</span></p>",
                                " <p> साइना नेहवाल</span></p>", " <p> कर्णम मल्लेश्वरी</span></p>"],
                    solution_en: "<p>85.(c) <span style=\"font-family: Palanquin Dark;\"> &lsquo;Playing to Win&rsquo;- Saina Nehwal, Ace against Odds- Sania Mirza, V. Krishnaswamy, authored a book titled &ldquo;Shuttling to the Top: The Story of P.V. Sindhu&rdquo;. </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Some Autobiography of Players- &lsquo;Playing It My Way&rsquo;- Sachin Tendulkar, &lsquo;The Test of My Life&rsquo;- Yuvraj Singh, &lsquo;A Shot at History&rsquo;- Abhinav Bindra, &lsquo;Unbreakable&rsquo;- Mary Kom, &lsquo;The Race of My Life&rsquo;- Milkha Singh, &lsquo;Straight from the Heart&rsquo;- Kapil Dev. </span></p>",
                    solution_hi: " <p>85.(c) </span><span style=\"font-family:Palanquin Dark\">‘Playing to Win’- साइना नेहवाल, Ace against Odds- सानिया मिर्जा, वी. कृष्णास्वामी ने  “Shuttling to the Top: The Story of P.V. Sindhu”।</span></p> <p><span style=\"font-family:Palanquin Dark\">खिलाड़ियों की कुछ आत्मकथा हैं - ‘Playing It My Way’- सचिन तेंदुलकर, ‘The Test of My Life’- युवराज सिंह, ‘A Shot at History’- अभिनव बिंद्रा ,‘Unbreakable\'- मैरी कॉम, ‘The Race of My Life’ - मिल्खा सिंह, ‘Straight from the Heart’- कपिल देव।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "18",
                    question_en: "<p>86. <span style=\"font-family: Palanquin Dark;\">Yog Sunder Desai, a pioneer who ventured into the world of Indian Dance in the pre- </span><span style=\"font-family: Palanquin Dark;\">independence era, was born on July 16, 1921 in __________.</span></p>",
                    question_hi: "<p>86.<span style=\"font-family: Palanquin Dark;\"> योग सुंदर देसाई, एक अग्रणी जिन्होंने स्वतंत्रता-पूर्व युग में भारतीय नृत्य की दुनिया में कदम रखा, का जन्म 16 जुलाई, 1921 को __________ में हुआ था।</span></p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Rajasthan</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Gujarat</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>राजस्थान</p>",
                                "<p>मध्य प्रदेश</p>", "<p>गुजरात</p>"],
                    solution_en: "<p>86.(d) <span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">Yog Sunder Desai</span><span style=\"font-family: Palanquin Dark;\"> (born in Gujarat) directed the</span><span style=\"font-family: Palanquin Dark;\"> \'Ram Lila\'</span><span style=\"font-family: Palanquin Dark;\"> for Shriram Bharatiya Kala Kendra in 1966. Yog Sunder founded his dance ensemble in 1948 in Calcutta and called it the Indian Revival Group in the spirit of revivalism that was sweeping the country.</span></p>",
                    solution_hi: "<p>86.(d) योग सुंदर देसाई<span style=\"font-family: Palanquin Dark;\"> (गुजरात में पैदा हुए) ने 1966 में श्रीराम भारतीय कला केंद्र के लिए </span><span style=\"font-family: Palanquin Dark;\">\'राम लीला\' </span><span style=\"font-family: Palanquin Dark;\">का निर्देशन किया था। योग सुंदर ने 1948 में कलकत्ता में अपने नृत्य कलाकारों की टुकड़ी की स्थापना की और इसे भारतीय पुनरुद्धार समूह का नाम दिया, जो देश में पुनरुत्थानवाद की भावना से चल रहा था।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "18",
                    question_en: " <p>87.</span><span style=\"font-family:Palanquin Dark\">  ____________ along with his wife, herself a dancer, and their son built Srjan in 1993.</span></p>",
                    question_hi: " <p>87. </span><span style=\"font-family:Palanquin Dark\">____________ ने अपनी पत्नी ( जो खुद एक नर्तकी है ) और बेटे के साथ 1993 में ‘सृजन (Srjan)’ का निर्माण किया।</span></p>",
                    options_en: [" <p> Pankaj Charan Das</span></p>", " <p> Kelucharan Mohapatra</span></p>", 
                                " <p> Raghunath Dutta</span></p>", " <p> Deba Prasad Das</span></p>"],
                    options_hi: [" <p> पंकज चरण दास</span></p>", " <p> केलुचरण महापात्र</span></p>",
                                " <p> रघुनाथ दत्ता</span></p>", " <p> देबा प्रसाद दास</span></p>"],
                    solution_en: " <p>87.(b)</span><span style=\"font-family:Palanquin Dark\"> </span><span style=\"font-family:Palanquin Dark\">Kelucharan Mohapatra</span><span style=\"font-family:Palanquin Dark\"> along with his wife, herself a dancer, and their son built Srjan in 1993. He is an exponent of Odissi Dance. he choreographed a number of dance - dramas in Odissi style, including \"</span><span style=\"font-family:Palanquin Dark\">Panchapuspa</span><span style=\"font-family:Palanquin Dark\">\", \"Krushna Gatha\", \"Geeta Govinda\", \"</span><span style=\"font-family:Palanquin Dark\">Urbashi</span><span style=\"font-family:Palanquin Dark\">\", \"Krushna Leela\", \"Sakhigopal\", \"Konark\" and \"Sri Kshetra\".</span></p>",
                    solution_hi: " <p>87.(b)</span><span style=\"font-family:Palanquin Dark\"> </span><span style=\"font-family:Palanquin Dark\">केलुचरण महापात्र</span><span style=\"font-family:Palanquin Dark\"> ने अपनी पत्नी जो एक नर्तकी हैं और उनके बेटे ने 1993 में सृजन (Srjan) का निर्माण किया। वह ओडिसी नृत्य के प्रतिपादक हैं। उन्होंने ओडिसी शैली में \"पंचपुष्पा\", \"कृष्ण गाथा\", \"गीता गोविंदा\", \"उरबाशी\", \"कृष्ण लीला\", \"सखीगोपाल\", \"कोणार्क\" और \"श्रीक्षेत्र\" सहित कई नृत्य-नाटकों को कोरियोग्राफ किया।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "18",
                    question_en: " <p>88.</span><span style=\"font-family:Palanquin Dark\"> What happens when an acid or a base is mixed with water?</span></p>",
                    question_hi: "<p>88.<span style=\"font-family: Palanquin Dark;\"> क्या होता है जब अम्ल या क्षार को पानी में मिलाया जाता है?</span></p>",
                    options_en: [" <p> Decrease in the concentration of ions (</span><span style=\"font-family:Palanquin Dark\">+/OH–) per unit volume</span></p>", " <p> Decrease in the concentration of ions (</span><span style=\"font-family:Palanquin Dark\">+/OH–) per litre volume</span></p>", 
                                " <p> increase in the concentration of ions (</span><span style=\"font-family:Palanquin Dark\">+/OH–) per unit volume</span></p>", " <p> Decrease in the concentration of ions (</span><span style=\"font-family:Palanquin Dark\">+/OH–) per unit volume</span></p>"],
                    options_hi: ["<p>प्रति इकाई आयतन में आयनों (<span style=\"font-family: Palanquin Dark;\">+/OH&ndash;) की सांद्रता में कमी</span></p>", "<p>प्रति लीटर मात्रा में आयनों (<span style=\"font-family: Palanquin Dark;\">+/OH&ndash;) की सांद्रता में कमी</span></p>",
                                "<p>प्रति इकाई आयतन में आयनों (<span style=\"font-family: Palanquin Dark;\">+/OH&ndash;) की सांद्रता में वृद्धि</span></p>", "<p>प्रति इकाई आयतन में आयनों (<span style=\"font-family: Palanquin Dark;\">+/OH&ndash;) की सांद्रता में कमी</span></p>"],
                    solution_en: " <p>88.(d)  </span><span style=\"font-family:Palanquin Dark\">When an acid or a base is mixed with water it decreases  in the concentration of ions (H3O+/OH–) per unit volume. Acids in water dissociate H+ ions. When an acidic solution is diluted with water, the concentration of H+ ions decreases and the pH of the solution increases towards 7. When an alkali is diluted with water, the concentration of OH– ions decreases, and the pH of the alkali fall towards 7</span></p>",
                    solution_hi: "<p>88.(d)<span style=\"font-family: Palanquin Dark;\"> जब किसी अम्ल या क्षार को पानी में मिलाया जाता है तो प्रति इकाई आयतन में आयनों (</span><span style=\"font-family: Palanquin Dark;\">H₃O</span><span style=\"font-family: Palanquin Dark;\"> /OH&ndash;) की सांद्रता कम हो जाती है। पानी में मौजूद एसिड H+ आयनों को अलग कर देता है। जब एक अम्लीय घोल को पानी से </span><span style=\"font-family: Palanquin Dark;\">तनु</span><span style=\"font-family: Palanquin Dark;\"> (dilute) किया जाता है, तो H+ आयनों की सांद्रता कम हो जाती है और घोल का pH, 7 की ओर बढ़ जाता है। जब एक क्षार को पानी से </span><span style=\"font-family: Palanquin Dark;\">तनु</span><span style=\"font-family: Palanquin Dark;\"> (dilute) किया जाता है, तो OH&ndash; आयनों की सांद्रता कम हो जाती है और क्षार का pH, 7 की ओर कम हो जाता है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">क्षारक + अम्ल &rarr; लवण + जल&nbsp;</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "18",
                    question_en: " <p>89.</span><span style=\"font-family:Palanquin Dark\"> As per CBI submission in Supreme Court in November 2021, eight Indian states have withdrawn their ‘general consent’ to the CBI for conducting investigations in their territory. Which of the following states is NOT one of them?</span></p>",
                    question_hi: " <p>89. </span><span style=\"font-family:Palanquin Dark\">नवंबर 2021 में सुप्रीम कोर्ट में CBI की प्रस्तुति के अनुसार, आठ भारतीय राज्यों ने अपने क्षेत्र में जांच करने के लिए CBI को अपनी \'सामान्य सहमति\' वापस ले ली है। निम्नलिखित में से कौन सा राज्य उनमें से एक नहीं है?</span></p>",
                    options_en: [" <p> Kerala</span></p>", " <p> Mizoram</span></p>", 
                                " <p> Jharkhand</span></p>", " <p> Karnataka</span></p>"],
                    options_hi: [" <p> केरल</span></p>", " <p> मिजोरम</span></p>",
                                " <p> झारखंड</span></p>", " <p> कर्नाटक</span></p>"],
                    solution_en: " <p>89.(d) Eight States</span><span style=\"font-family:Palanquin Dark\">- West Bengal, Maharashtra, Kerala, Punjab, Rajasthan, Jharkhand, Chhattisgarh and Mizoram have withdrawn consent to the CBI for launching investigations in their territory.The establishment of the CBI was recommended by the Santhanam Committee. It was established in 1963. </span></p>",
                    solution_hi: " <p>89.(d) आठ राज्यों</span><span style=\"font-family:Palanquin Dark\">- पश्चिम बंगाल, महाराष्ट्र, केरल, पंजाब, राजस्थान, झारखंड, छत्तीसगढ़ और मिजोरम ने अपने क्षेत्र में जांच शुरू करने के लिए CBI से सहमति वापस ले ली है। संथानम समिति द्वारा CBI की स्थापना की सिफारिश की गई थी। इसकी स्थापना 1963 में हुई थी।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "18",
                    question_en: " <p>90.</span><span style=\"font-family:Palanquin Dark\"> Narthaki Nataraj, _________, becomes the first trans person to receive Padma Shri.</span></p>",
                    question_hi: "<p>90.<span style=\"font-family: Palanquin Dark;\"> नर्तकी नटराज, _________, पद्म श्री प्राप्त करने वाले पहले ट्रांस व्यक्ति बन गए हैं।</span></p>",
                    options_en: [" <p> Kathak dancer</span></p>", " <p> Odissi dancer</span></p>", 
                                " <p> Manipuri dancer</span></p>", " <p> Bharatanatyam dancer</span></p>"],
                    options_hi: ["<p>कथक नर्तकी</p>", "<p>ओडिसी नर्तकी</p>",
                                "<p>मणिपुरी नर्तकी</p>", "<p>भरतनाट्यम नर्तकी</p>"],
                    solution_en: " <p>90.(d) Narthaki Nataraj</span><span style=\"font-family:Palanquin Dark\">, Bharatanatyam dancer , becomes the first trans person to receive Padma Shri.</span></p> <p><span style=\"font-family:Palanquin Dark\">The Padma Awards were instituted in 1954 by the Government of India. It is awarded annually in the event of Republic Day. In 2022 </span><span style=\"font-family:Palanquin Dark\">list comprises four Padma Vibhushan, 17 Padma Bhushan and 107 Padma Shri awards.</span></p>",
                    solution_hi: "<p>90.(d)<span style=\"font-family: Palanquin Dark;\"> भरतनाट्यम नर्तक, </span><span style=\"font-family: Palanquin Dark;\">नर्तकी नटराज</span><span style=\"font-family: Palanquin Dark;\"> पद्म श्री प्राप्त करने वाले पहले ट्रांस व्यक्ति बने।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">भारत सरकार द्वारा 1954 में पद्म पुरस्कारों की शुरुआत की गई थी। यह गणतंत्र दिवस के अवसर पर प्रतिवर्ष प्रदान किया जाता है। 2022 की सूची में चार पद्म विभूषण, 17 पद्म भूषण और 107 पद्म श्री पुरस्कार शामिल हैं।</span></p>\r\n<p><span style=\"color: #236fa1;\"><strong><span style=\"font-family: Palanquin Dark;\">Extra Point :- </span></strong></span></p>\r\n<p><span style=\"color: #000000;\"><span style=\"font-family: Palanquin Dark;\">नर्तकी नटराज&nbsp;भारत में एक प्रसिद्ध भरतनाट्यम&nbsp;नर्तकी&nbsp;है। उनका जन्म तमिलनाडु के मदुरई के पास एक गाँव में हुआ था। उन्हें 2019 में&nbsp;पद्म श्री&nbsp;पुरस्कार से सम्मानित किया गया था। वह भारत में&nbsp;पद्म श्री&nbsp;से सम्मानित होने वाली पहली ट्रांसजेंडर महिला&nbsp;हैं।</span></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "18",
                    question_en: " <p>91. </span><span style=\"font-family:Palanquin Dark\">Which of the following may be called </span><span style=\"font-family:Palanquin Dark\">as fiscal</span><span style=\"font-family:Palanquin Dark\"> deficit?</span></p>",
                    question_hi: " <p>91. </span><span style=\"font-family:Palanquin Dark\">निम्नलिखित में से किसे राजकोषीय घाटा कहा जा सकता है?</span></p>",
                    options_en: [" <p> Revenue expenditure − Revenue receipts</span></p>", " <p> Capital expenditure – Capital receipts</span></p>", 
                                " <p> Total expenditure – Total receipts other than borrowings</span></p>", " <p> Revenue expenditure + Capital expenditure − Revenue receipts</span></p>"],
                    options_hi: [" <p> राजस्व व्यय – राजस्व प्रप्तियाँ</span></p>", " <p> पूंजीगत व्यय – पूंजीगत प्रप्तियाँ</span></p>",
                                " <p> कुल व्यय – उधार के अलावा कुल प्रप्तियाँ</span></p>", " <p> राजस्व व्यय + पूंजीगत व्यय – राजस्व प्रप्तियाँ</span></p>"],
                    solution_en: " <p>91.(c) </span><span style=\"font-family:Palanquin Dark\">Total expenditure – Total receipts other than borrowings is equal to </span><span style=\"font-family:Palanquin Dark\">Fiscal deficit</span><span style=\"font-family:Arial Unicode MS\">. Revenue expenditure − Revenue receipts </span><span style=\"font-family:Arial Unicode MS\">= Revenue Deficit.</span><span style=\"font-family:Arial Unicode MS\"> </span><span style=\"font-family:Arial Unicode MS\">Capital receipts</span><span style=\"font-family:Arial Unicode MS\"> are loans taken by the government from the public, borrowings from foreign countries and institutes, and borrowings from the RBI. </span><span style=\"font-family:Arial Unicode MS\">Capital expenditure</span><span style=\"font-family:Arial Unicode MS\"> is the money spent by the government on the development of machinery, equipment, building, health facilities, education, etc.</span></p>",
                    solution_hi: " <p>91.(c) </span><span style=\"font-family:Palanquin Dark\">कुल व्यय – उधार के अलावा कुल प्राप्तियां </span><span style=\"font-family:Palanquin Dark\">राजकोषीय घाटे </span><span style=\"font-family:Palanquin Dark\">(</span><span style=\"font-family:Palanquin Dark\">Fiscal deficit)</span><span style=\"font-family:Palanquin Dark\"> के बराबर है। राजस्व व्यय – राजस्व प्राप्तियां = </span><span style=\"font-family:Palanquin Dark\">राजस्व घाटा</span><span style=\"font-family:Palanquin Dark\">।</span><span style=\"font-family:Palanquin Dark\"> पूंजी प्राप्तियां</span><span style=\"font-family:Palanquin Dark\"> सरकार द्वारा जनता से लिए गए ऋण, विदेशों और संस्थानों से उधार और RBI से उधार हैं। </span><span style=\"font-family:Palanquin Dark\">पूंजीगत व्यय </span><span style=\"font-family:Palanquin Dark\">वह धन है जो सरकार द्वारा मशीनरी, उपकरण, भवन, स्वास्थ्य सुविधाओं, शिक्षा आदि के विकास पर खर्च किया जाता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "18",
                    question_en: " <p>92.</span><span style=\"font-family:Palanquin Dark\"> Who is the author of the controversial novel ‘Lajja’?</span></p>",
                    question_hi: " <p>92. </span><span style=\"font-family:Palanquin Dark\">विवादास्पद उपन्यास \'लज्जा\' के लेखिका कौन हैं?</span></p>",
                    options_en: [" <p> Arundhati Roy</span></p>", " <p> Taslima Nasrin</span></p>", 
                                " <p> Shobhaa De</span></p>", " <p> Kiran Bedi</span></p>"],
                    options_hi: [" <p> अरुंधति रॉय</span></p>", " <p> तस्लीमा नसरीन</span></p>",
                                " <p> शोभा दे</span></p>", " <p> किरण बेदी</span></p>"],
                    solution_en: " <p>92.(b)  </span><span style=\"font-family:Palanquin Dark\">The author of the controversial novel ‘Lajja(Shame)’ is </span><span style=\"font-family:Palanquin Dark\">Taslima Nasrin</span><span style=\"font-family:Palanquin Dark\">. Some of her famous books are - ‘My Girlhood’, ‘French Lover’, ‘Revenge’, ‘Selected Columns’ etc. </span></p>",
                    solution_hi: " <p>92.(b) </span><span style=\"font-family:Palanquin Dark\">विवादास्पद उपन्यास ‘Lajja(Shame)’ की लेखिका </span><span style=\"font-family:Palanquin Dark\">तसलीम नसरीन</span><span style=\"font-family:Palanquin Dark\"> हैं। उनकी कुछ प्रसिद्ध पुस्तकें हैं -  ‘My Girlhood’, ‘French Lover’, ‘Revenge’, ‘Selected Columns’ आदि।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "18",
                    question_en: " <p>93.</span><span style=\"font-family:Palanquin Dark\"> A card of which colour is used to warn a player in Football?</span></p>",
                    question_hi: " <p>93. </span><span style=\"font-family:Palanquin Dark\">फुटबॉल में खिलाड़ी को चेतावनी देने के लिए किस रंग का कार्ड इस्तेमाल किया जाता है?</span></p>",
                    options_en: [" <p> Red</span></p>", " <p> Blue</span></p>", 
                                " <p> Green</span></p>", " <p> Yellow</span></p>"],
                    options_hi: [" <p> लाल</span></p>", " <p> नीला</span></p>",
                                " <p> हरा</span></p>", " <p> पीला</span></p>"],
                    solution_en: " <p>93.(d) </span><span style=\"font-family:Palanquin Dark\">A card of which yellow </span><span style=\"font-family:Palanquin Dark\">colour</span><span style=\"font-family:Palanquin Dark\"> is used to warn a player in Football. A red card results in the player\'s dismissal from the field of play. In football there are 11 players on each side. A standard football match is 90 minutes made up of two 45-minute halves. FIFA recommendations for field dimensions in professional football are 105 metres in length and 68 metres in width.</span></p>",
                    solution_hi: " <p>93.(d) </span><span style=\"font-family:Palanquin Dark\">फुटबॉल में किसी खिलाड़ी को चेतावनी देने के लिए पीले रंग का कार्ड प्रयोग किया जाता है। एक लाल कार्ड के परिणामस्वरूप खिलाड़ी खेल के मैदान से बाहर हो जाता है। फुटबॉल में हर तरफ 11 खिलाड़ी होते हैं। एक मानक फुटबॉल मैच 90 मिनट का होता है जो 45 मिनट के दो हिस्सों से बना होता है। पेशेवर फ़ुटबॉल में फ़ील्ड आयामों के लिए फीफा की अनुशंसा लंबाई में 105 m और चौड़ाई में 68 m हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "18",
                    question_en: "<p>94.<span style=\"font-family: Palanquin Dark;\"> Who among the following is the recipient of top four civilian awards namely, Padma </span><span style=\"font-family: Palanquin Dark;\">Shri, Padma Bhushan, Padma Vibhushan and Bharat Ratna?</span></p>\n",
                    question_hi: "<p>94. <span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2358;&#2368;&#2352;&#2381;&#2359; &#2330;&#2366;&#2352; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;&#2379;&#2306; &#2346;&#2342;&#2381;&#2350; &#2358;&#2381;&#2352;&#2368;, &#2346;&#2342;&#2381;&#2350; &#2349;&#2370;&#2359;&#2339;, &#2346;&#2342;&#2381;&#2350; &#2357;&#2367;&#2349;&#2370;&#2359;&#2339; &#2324;&#2352; &#2349;&#2366;&#2352;&#2340; &#2352;&#2340;&#2381;&#2344; &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366; &#2361;&#2376;&#2306;?</span></p>\n",
                    options_en: ["<p>Ustad Bade Ghulam Ali Khan</p>\n", "<p>Ustad Bismillah Khan</p>\n", 
                                "<p>Pandit Bhimsen Joshi</p>\n", "<p>both b and c&nbsp;</p>\n"],
                    options_hi: ["<p>&#2313;&#2360;&#2381;&#2340;&#2366;&#2342; &#2348;&#2337;&#2364;&#2375; &#2327;&#2369;&#2354;&#2366;&#2350; &#2309;&#2354;&#2368; &#2326;&#2366;&#2344;</p>\n", "<p>&#2313;&#2360;&#2381;&#2340;&#2366;&#2342; &#2348;&#2367;&#2360;&#2381;&#2350;&#2367;&#2354;&#2381;&#2354;&#2366;&#2361; &#2326;&#2366;&#2344;</p>\n",
                                "<p>&#2346;&#2306;&#2337;&#2367;&#2340; &#2349;&#2368;&#2350;&#2360;&#2375;&#2344; &#2332;&#2379;&#2358;&#2368;</p>\n", "<p>b &#2324;&#2352; c &#2342;&#2379;&#2344;&#2379;&#2306;</p>\n"],
                    solution_en: "<p>94.(d) Ustad Bismillah <span style=\"font-family: Palanquin Dark;\">Khan is the recipient of top four civilian awards namely, Padma </span><span style=\"font-family: Palanquin Dark;\">Shri, Padma Bhushan, Padma Vibhushan and </span><span style=\"font-family: Palanquin Dark;\">Bharat Ratna in 2001</span><span style=\"font-family: Palanquin Dark;\">. He was the shehnai player. Pandit </span><span style=\"font-family: Palanquin Dark;\">Bhimsen Joshi </span><span style=\"font-family: Palanquin Dark;\">is also the recipient of top four civilian awards. He got Bharat Ratna in 2009. </span><span style=\"font-family: Palanquin Dark;\">Ravi Shankar </span><span style=\"font-family: Palanquin Dark;\">(Bharat Ratna in 1999).</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>94.(d)<span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">&#2313;&#2360;&#2381;&#2340;&#2366;&#2342; &#2348;&#2367;&#2360;&#2381;&#2350;&#2367;&#2354;&#2381;&#2354;&#2366;&#2361; &#2326;&#2366;&#2344;</span><span style=\"font-family: Palanquin Dark;\"> &#2358;&#2368;&#2352;&#2381;&#2359; &#2330;&#2366;&#2352; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;&#2379;&#2306;, &#2346;&#2342;&#2381;&#2350; &#2358;&#2381;&#2352;&#2368;, &#2346;&#2342;&#2381;&#2350; &#2349;&#2370;&#2359;&#2339;, &#2346;&#2342;&#2381;&#2350; &#2357;&#2367;&#2349;&#2370;&#2359;&#2339; &#2324;&#2352; &#2349;&#2366;&#2352;&#2340; &#2352;&#2340;&#2381;&#2344; (2001) &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366; &#2361;&#2376;&#2306;&#2404; &#2357;&#2361; &#2358;&#2361;&#2344;&#2366;&#2312; &#2357;&#2366;&#2342;&#2325; &#2341;&#2375;&#2404; </span><span style=\"font-family: Palanquin Dark;\">&#2346;&#2306;&#2337;&#2367;&#2340; &#2349;&#2368;&#2350;&#2360;&#2375;&#2344; &#2332;&#2379;&#2358;&#2368;</span><span style=\"font-family: Palanquin Dark;\"> &#2358;&#2368;&#2352;&#2381;&#2359; &#2330;&#2366;&#2352; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;&#2379;&#2306; &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366; &#2349;&#2368; &#2361;&#2376;&#2306;&#2404; 2009 &#2350;&#2375;&#2306; &#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2352;&#2340;&#2381;&#2344; &#2350;&#2367;&#2354;&#2366;&#2404; </span><span style=\"font-family: Palanquin Dark;\">&#2352;&#2357;&#2367; &#2358;&#2306;&#2325;&#2352;</span><span style=\"font-family: Palanquin Dark;\"> (1999 &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2352;&#2340;&#2381;&#2344;)&#2404;</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "18",
                    question_en: "<p>95.<span style=\"font-family: Palanquin Dark;\"> &lsquo;Train to Pakistan&rsquo; was written by ___________, who also served as member of Rajya </span><span style=\"font-family: Palanquin Dark;\">Sabha.</span></p>",
                    question_hi: " <p>95. </span><span style=\"font-family:Palanquin Dark\">\'ट्रेन टू पाकिस्तान\' ___________ द्वारा लिखा गया था, जिन्होंने राज्यसभा के सदस्य के रूप में भी काम किया था।</span></p>",
                    options_en: ["<p>Mulk Raj Anand</p>", "<p>Anita Desai</p>", 
                                "<p>Khushwant Singh</p>", "<p>Vikram Seth</p>"],
                    options_hi: [" <p> मुल्क राज आनंद</span></p>", " <p> अनीता देसाई</span></p>",
                                " <p> खुशवंत सिंह</span></p>", " <p> विक्रम सेठ</span></p>"],
                    solution_en: "<p>95.(c) Khushwant Singh- <span style=\"font-family: Palanquin Dark;\">&lsquo;Train to Pakistan&rsquo;, &lsquo;Delhi&rsquo;, &lsquo;I Shall never Hear a Nightingale&rsquo;, &lsquo;Why I Supported The Emergency&rsquo;.</span><span style=\"font-family: Palanquin Dark;\"> Anita Desai</span><span style=\"font-family: Palanquin Dark;\"> (</span><span style=\"font-family: Palanquin Dark;\">&lsquo;Fasting, Feasting&rsquo;, &lsquo;In Custody&rsquo;, &lsquo;Clear Light of Day&rsquo;, &lsquo;The Village by the Sea&rsquo;, &lsquo;Fire On The Mountain&rsquo;). </span><span style=\"font-family: Palanquin Dark;\">Vikram Seth</span><span style=\"font-family: Palanquin Dark;\"> (&lsquo;A Suitable Boy&rsquo;, &lsquo;The Golden Gate&rsquo;), </span><span style=\"font-family: Palanquin Dark;\">Mulk Raj Anand </span><span style=\"font-family: Palanquin Dark;\">(&lsquo;Untouchable&rsquo;, &lsquo;Coolie&rsquo;).</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    solution_hi: " <p><span style=\"font-family:Palanquin Dark\">95.(c) खुशवंत सिंह</span><span style=\"font-family:Palanquin Dark\">-‘Train to Pakistan’, ‘Delhi’, ‘I Shall never Hear a Nightingale’, ‘Why I Supported The Emergency’। </span><span style=\"font-family:Palanquin Dark\">अनीता देसाई</span><span style=\"font-family:Palanquin Dark\"> (</span><span style=\"font-family:Palanquin Dark\">‘Fasting, Feasting’, ‘In Custody’, ‘Clear Light of Day’, ‘The Village by the Sea’, ‘Fire On The Mountain’</span><span style=\"font-family:Palanquin Dark\">)। </span><span style=\"font-family:Palanquin Dark\">विक्रम सेठ</span><span style=\"font-family:Palanquin Dark\"> (</span><span style=\"font-family:Palanquin Dark\">‘A Suitable Boy’, ‘The Golden Gate’</span><span style=\"font-family:Palanquin Dark\">), </span><span style=\"font-family:Palanquin Dark\">मुल्क राज आनंद</span><span style=\"font-family:Palanquin Dark\"> (</span><span style=\"font-family:Palanquin Dark\">‘Untouchable’, ‘Coolie’</span><span style=\"font-family:Palanquin Dark\">)।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "18",
                    question_en: " <p>96.</span><span style=\"font-family:Palanquin Dark\"> What is the dimension of the Hockey field?</span></p>",
                    question_hi: " <p>96. </span><span style=\"font-family:Palanquin Dark\">हॉकी मैदान का आयाम क्या है?</span></p>",
                    options_en: [" <p> 91.4 × 55 m</span></p>", " <p> 100 × 55 m</span></p>", 
                                " <p> 90 × 50 m</span></p>", " <p> 100.3 × 50 m</span></p>"],
                    options_hi: [" <p> 91.4 × 55 m</span></p>", " <p> 100 × 55 m</span></p>",
                                " <p> 90 × 50 m</span></p>", " <p> 100.3 × 50 m</span></p>"],
                    solution_en: " <p>96.(a) </span><span style=\"font-family:Palanquin Dark\">The dimension of the Hockey field is</span><span style=\"font-family:Palanquin Dark\"> 91.4 × 55 m</span><span style=\"font-family:Palanquin Dark\">. There are 11 players on each side. A regular game consists of three 20-minute periods, with a 15-minute intermission after the first and second periods. Dhyan Chand is known as the “Hockey Wizard”.</span></p>",
                    solution_hi: " <p>96.(a) </span><span style=\"font-family:Palanquin Dark\">हॉकी मैदान का आयाम </span><span style=\"font-family:Palanquin Dark\">91.4 × 55 m </span><span style=\"font-family:Palanquin Dark\">है। प्रत्येक पक्ष में 11 खिलाड़ी होते हैं। एक नियमित खेल में तीन 20 मिनट की अवधि होती है, जिसमें पहली और दूसरी अवधि के बाद 15 मिनट का मध्यांतर होता है।  ध्यानचंद को \"हॉकी जादूगर\" के रूप में जाना जाता है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "18",
                    question_en: " <p>97.</span><span style=\"font-family:Palanquin Dark\"> Which vegetable contains a chemical called anthocyanin that turns pink/reddish in the presence of acid, turns purple when neutral and turns blue or green when mixed with alkaline substances?</span></p>",
                    question_hi: " <p>97.</span><span style=\"font-family:Palanquin Dark\"> किस सब्जी में एंथोसायनिन नामक रसायन होता है जो अम्ल की उपस्थिति में गुलाबी/लाल हो जाता है, उदासीन होने पर बैंगनी हो जाता है और क्षारीय पदार्थों के साथ मिलाने पर नीला या हरा हो जाता है?</span></p>",
                    options_en: [" <p> Pomegranate</span></p>", " <p> Red leaf lettuce</span></p>", 
                                " <p> Radish</span></p>", " <p> Red cabbage</span></p>"],
                    options_hi: [" <p> अनार</span></p>", " <p> लाल पत्ता सलाद</span></p>",
                                " <p> मूली</span></p>", " <p> लाल गोभी</span></p>"],
                    solution_en: " <p>97.(d)</span><span style=\"font-family:Palanquin Dark\"> Dietary sources of </span><span style=\"font-family:Palanquin Dark\">anthocyanins </span><span style=\"font-family:Palanquin Dark\">are generally easy to identify due to their red, blue, or purple color. Examples include red/purple cabbage, berries and red-skinned grapes, pears, and apples and various vegetables such as radishes etc. Purple fruits and vegetables are high in anthocyanin.</span><span style=\"font-family:Palanquin Dark\"> Red cabbage</span><span style=\"font-family:Palanquin Dark\"> contains a water-soluble pigment called anthocyanin that changes color when it is mixed with an acid or a base. The pigment turns red in acidic environments with a pH less than 7 and the pigment turns bluish-green in alkaline (basic), environments with a pH greater than 7.</span></p>",
                    solution_hi: " <p>97.(d) एंथोसायनिन</span><span style=\"font-family:Palanquin Dark\"> के आहार स्रोतों को आमतौर पर उनके लाल, नीले या बैंगनी रंग के कारण पहचानना आसान होता है। उदाहरणों में लाल/बैंगनी गोभी, जामुन और लाल चमड़ी वाले अंगूर, नाशपाती, और सेब और विभिन्न सब्जियां जैसे मूली आदि शामिल हैं। बैंगनी फल और सब्जियां एंथोसायनिन में उच्च होती हैं। </span><span style=\"font-family:Baloo\">लाल गोभी</span><span style=\"font-family:Baloo\"> में एंथोसायनिन नामक एक पानी में घुलनशील वर्णक होता है जो एसिड या बेस के साथ मिश्रित होने पर रंग बदलता है। वर्णक अम्लीय वातावरण में 7 से कम </span><span style=\"font-family:Baloo\">pH</span><span style=\"font-family:Baloo\"> के साथ लाल हो जाता है और वर्णक क्षारीय में नीला-हरा हो जाता है, वातावरण में </span><span style=\"font-family:Baloo\">pH </span><span style=\"font-family:Baloo\">7 से अधिक होता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "18",
                    question_en: "<p>98.<span style=\"font-family: Baloo;\"> Which of the following Hindustani vocalists from Allahabad, along with her famed </span><span style=\"font-family: Baloo;\">contemporary Gauhar Jaan, had the privilege of performing for Emperor George V at </span><span style=\"font-family: Baloo;\">the Delhi Darbar in 1911?</span></p>",
                    question_hi: "<p>98. <span style=\"font-family: Palanquin Dark;\">इलाहाबाद के निम्नलिखित में से किस हिंदुस्तानी गायक को उनके प्रसिद्ध समकालीन गौहर जान के साथ, 1911 ई. में दिल्ली दरबार में सम्राट जॉर्ज पंचम के लिए प्रदर्शन करने का सौभाग्य प्राप्त हुआ था?</span></p>",
                    options_en: ["<p><span style=\"font-weight: 400;\">Shobha Abhyankar</span></p>", "<p><span style=\"font-weight: 400;\">Annupamaa</span></p>", 
                                "<p><span style=\"font-weight: 400;\">&nbsp;Allah Jilai Bai</span></p>", "<p><span style=\"font-weight: 400;\">Janki Bai</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">शोभा अभ्यंकर</span></p>", "<p><span style=\"font-weight: 400;\">अनुपमा</span></p>",
                                "<p><span style=\"font-weight: 400;\">अल्लाह जिलाई बाई</span></p>", "<p><span style=\"font-weight: 400;\">जानकी बाई</span></p>"],
                    solution_en: "<p>98.(d) <span style=\"font-family: Palanquin Dark;\">Janki Bai. She was popularly known as &ldquo;Chhappan chhuri&rdquo; or the \'woman with 56 stab wounds\'. Shobha Abhyankar (Pune)- Mewati Gharana. Annupamaa (Chennai)- Carnatic Music, Allah Jilai Bai (Rajasthan)- Folk Music. </span></p>",
                    solution_hi: "<p>98.(d) <span style=\"font-family: Palanquin Dark;\">जानकी बाई लोकप्रिय रूप से \"छप्पन छुरी\" या \'56 छुरा घाव वाली महिला\' के रूप में जानी जाती थीं। शोभा अभ्यंकर (पुणे)- मेवाती घराना। अनुपमा (चेन्नई) - कर्नाटक संगीत, अल्लाह जिला बाई (राजस्थान) - लोक संगीत।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "18",
                    question_en: " <p>99.</span><span style=\"font-family:Palanquin Dark\"> Mame Khan is an Indian </span><span style=\"font-family:Palanquin Dark\">playback and</span><span style=\"font-family:Palanquin Dark\"> folk singer from ________.</span></p>",
                    question_hi: " <p>99. </span><span style=\"font-family:Palanquin Dark\">मामे खान ________ के एक भारतीय पार्श्व और लोक गायक हैं।</span></p>",
                    options_en: [" <p> Rajasthan</span></p>", " <p> Uttar Pradesh</span></p>", 
                                " <p> Kerala</span></p>", " <p> Andhra Pradesh</span></p>"],
                    options_hi: [" <p> राजस्थान</span></p>", " <p> उत्तर प्रदेश</span></p>",
                                " <p> केरल</span></p>", " <p> आंध्र प्रदेश</span></p>"],
                    solution_en: " <p>99.(a)</span><span style=\"font-family:Palanquin Dark\"> Some famous </span><span style=\"font-family:Palanquin Dark\">folk singers</span><span style=\"font-family:Palanquin Dark\"> from Rajasthan are </span><span style=\"font-family:Palanquin Dark\">Ila Arun,</span><span style=\"font-family:Palanquin Dark\"> Mame Khan</span><span style=\"font-family:Palanquin Dark\">, Sartaj Khan, Sarwar Khan, Swaroop Khan, Allah Jilai Bai. </span><span style=\"font-family:Palanquin Dark\">Some popular</span><span style=\"font-family:Palanquin Dark\"> folk songs</span><span style=\"font-family:Palanquin Dark\"> of Rajasthan- Panihari, Kesariya Balam, Pabuji Ki Phach, and Maand.</span></p>",
                    solution_hi: " <p>99.(a) </span><span style=\"font-family:Palanquin Dark\">राजस्थान के कुछ प्रसिद्ध </span><span style=\"font-family:Palanquin Dark\">लोक गायक</span><span style=\"font-family:Palanquin Dark\"> इला अरुण, </span><span style=\"font-family:Palanquin Dark\">मामे खान</span><span style=\"font-family:Palanquin Dark\">, सरताज खान, सरवर खान, स्वरूप खान, अल्लाह जिला बाई हैं। राजस्थान के कुछ लोकप्रिय </span><span style=\"font-family:Palanquin Dark\">लोक गीत </span><span style=\"font-family:Palanquin Dark\">हैं- पनिहारी, केसरिया बालम, पाबूजी की फाच, और मांड।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: " <p>100.</span><span style=\"font-family:Palanquin Dark\"> In which state of India is the Seshachalam biosphere reserve located?</span></p>",
                    question_hi: "<p>100.<span style=\"font-family: Palanquin Dark;\"> शेषाचलम बायोस्फीयर रिजर्व भारत के किस राज्य में स्थित है ?</span></p>",
                    options_en: [" <p> Tamil Nadu</span></p>", " <p> Karnataka</span></p>", 
                                " <p> Kerala</span></p>", " <p> Andhra Pradesh</span></p>"],
                    options_hi: ["<p>तमिलनाडु</p>", "<p>कर्नाटक</p>",
                                "<p>केरल</p>", "<p>आंध्र प्रदेश</p>"],
                    solution_en: " <p>100.(d) Seshachalam</span><span style=\"font-family:Palanquin Dark\"> biosphere reserve was designated as a Biosphere Reserve in 2010. There is one tiger reserve, one</span><span style=\"font-family:Palanquin Dark\"> Elephant Reserve (Kaundinya Sanctuary and Rayala Elephant Reserve). National Parks - 1. Sri Venkateswara National Park,  2. Mahavir Harina Vanasthali 3. Mrugavani. 4. Papikondalu 5. Nagarjunasagar wildlife sanctuary  etc.</span></p>",
                    solution_hi: "<p>100.(d) शेषचलम <span style=\"font-family: Palanquin Dark;\">बायोस्फीयर रिजर्व को 2010 में बायोस्फीयर रिजर्व के रूप में नामित किया गया था। एक टाइगर रिजर्व, एक हाथी रिजर्व (कौंडिन्य अभयारण्य और रायला हाथी रिजर्व) है। राष्ट्रीय उद्यान - 1. श्री वेंकटेश्वर राष्ट्रीय उद्यान, 2. महावीर हरिना वनस्थली 3. मृगवनी 4. पापिकोंडालु 5. नागार्जुनसागर वन्यजीव अभयारण्य आदि।</span></p>\r\n<p><span style=\"color: #236fa1;\"><strong><span style=\"font-family: \'Palanquin Dark\';\">Extra Point :- </span></strong></span></p>\r\n<p><span style=\"color: #000000;\"><span style=\"font-family: \'Palanquin Dark\';\">भारत में बाघ की आबादी दुनिया की लगभग 80% है. 2022 में, हमारे भारत में कुल 53 टाइगर रिजर्व हैं<span style=\"font-family: Palanquin Dark;\">।</span>अभी अभी छत्तीसगढ़ के गुरु घासीदास राष्ट्रीय उद्यान और तमोर पिंगला वन्यजीव अभयारण्य के संयुक्त क्षेत्र को 53 वां टाइगर रिजर्व घोषित किया गया है <span style=\"font-family: Palanquin Dark;\">।</span></span></span></p>\r\n<table style=\"border-collapse: collapse; width: 87.8291%;\" border=\"1\">\r\n<tbody>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">\r\n<p><strong><strong>क्रम संख्या</strong></strong></p>\r\n</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p><strong><strong>टाइगर रिजर्व के नाम</strong></strong><strong><strong>&nbsp;</strong></strong></p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p><strong><strong>राज्य</strong></strong></p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p><strong><strong>कुल क्षेत्रफल(वर्ग किमी. में)</strong></strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">1.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>नागार्जुन सागर श्रीशैलम (भाग) *&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>आंध्र प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>3296.31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">2.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>नमदाफा</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>अरुणाचल प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2052.82</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">3.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>कमलंग टाइगर रिजर्व</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>अरुणाचल प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>783</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">4.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>पक्के</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>अरुणाचल प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1198.45</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">5.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>मानस</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>असम</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>3150.92</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">6.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>नामेरी</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>असम</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>344</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">7.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>ओरंग टाइगर रिजर्व&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>असम&nbsp;</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>492.46</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">8.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>काजीरंगा</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>असम</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1173.58</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">9.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>वाल्मीकि&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>बिहार</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>899.38</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">10.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>उदंती-सीतानदी</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>छत्तीसगढ़</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1842.54</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">11.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>अचानकमार&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>छत्तीसगढ़</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>914.01</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">12.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>इंद्रावती</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>छत्तीसगढ़</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2799.07</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">13.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>पलामू</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>झारखंड</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1129.93</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">14.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>बांदीपुर</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>कर्नाटक</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1456.3</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">15.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>भद्रा</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>कर्नाटक</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1064.29</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">16.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>दांदेली-अंशी</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>कर्नाटक</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1097.51</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">17.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>नागरहोल</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>कर्नाटक</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1205.76</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">18.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>बिलिगिरी रंगनाथ मंदिर</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>कर्नाटक</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>574.82</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">19.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>पेरियार</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>केरल</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>925</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">20.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>परम्बिकुलम</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>केरल</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>643.66</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">21.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>कान्हा</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>मध्य प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2051.79</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">22.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>पेंच</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>मध्य प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1179.63</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">23.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>बांधवगढ़</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>मध्य प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1598.1</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">24.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>पन्ना</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>मध्य प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1578.55</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">25.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>सतपुड़ा</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>मध्य प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2133.30</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">26.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>संजय-दुबरी</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>मध्य प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1674.50</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">27.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>मेलघाट</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>महाराष्ट्र</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2768.52</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">28.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>ताडोबा-अंधारी</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>महाराष्ट्र</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1727.59</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">29.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>पेंच</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>महा राष्ट्र</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>741.22</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">30.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>सहयाद्रि&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>महाराष्ट्र</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1165.57</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">31.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>नवेगाँव-नागज़ीरा</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>महाराष्ट्र</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>653.67</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">32.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>बोर</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>महाराष्ट्र</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>138.12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">33.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>दम्पा</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>मिजोरम</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>988</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">34.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>सिमलीपाल</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>ओडिशा</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2750</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">35.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>सतकोसिया</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>ओडिशा</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>963.87</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">36.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>रणथंभौर</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>राजस्थान</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1411.29</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">37.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>सरिस्का</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>राजस्थान</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1213.34</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">38.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>मुकुंदरा हिल्स&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>राजस्थान</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>759.99</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">39.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>कलाकड़-मुंडनथुराई</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>तमिलनाडु</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1601.54</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">40.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>अन्नामलाई&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>तमिलनाडु&nbsp;&nbsp;</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1479.87</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">41.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>मुदुमलाई</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>तमिलनाडु</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>688.59</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">42.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>सत्यमंगलम</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>तमिलनाडु</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1408.4</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">43.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>कवल&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>तेलंगाना</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2019।12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">44.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>अमराबाद</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>तेलंगाना</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2611.39</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">45.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>दुधवा&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>उत्तर प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2201.77</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">46.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>पीलीभीत&nbsp;</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>उत्तर प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>730.24</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\" rowspan=\"2\">47.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>अमानगढ़ (कॉर्बेट टाइगर रिजर्व का बफर)</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>उत्तर प्रदेश</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>80.6</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>कॉर्बेट</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>उत्तराखंड</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1288.31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">48.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>राजाजी टाइगर रिजर्व</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>उत्तराखंड</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1075।</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">49.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>सुंदरबन</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>पश्चिम बंगाल</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>2584.89</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">50.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>बक्सा</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>पश्चिम बंगाल</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>757.90</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">51.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>श्रीविलिपुथुर मेगामलाई</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>तमिलनाडु</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>1016.57</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">52.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>रामगढ़ विषधारी वन्यजीव अभ्यारण्य</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>राजस्थान</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>252</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"width: 10.6466%; text-align: center;\">53.</td>\r\n<td style=\"width: 31.1869%; text-align: center;\">\r\n<p>गुरु घासीदास राष्ट्रीय उद्यान एवं तमोर पिंगला वन्यजीव अभ्यारण्य</p>\r\n</td>\r\n<td style=\"width: 21.7233%; text-align: center;\">\r\n<p>छत्तीसगढ़</p>\r\n</td>\r\n<td style=\"width: 36.3488%; text-align: center;\">\r\n<p>466.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n<p>&nbsp;</p>\r\n<ul>\r\n<li style=\"font-weight: 400;\"><strong>विश्व में सबसे ज्यादा बाघों की आबादी वाला देश -&nbsp;</strong>&nbsp;इंडिया/भारत</li>\r\n<li style=\"font-weight: 400;\"><strong>भारत में कितने टाइगर रिजर्व हैं - &nbsp;53</strong></li>\r\n<li style=\"font-weight: 400;\"><strong>भारत के किस राज्य में बाघों की आबादी सर्वाधिक है - </strong>मध्य प्रदेश</li>\r\n<li style=\"font-weight: 400;\"><strong>किस टाइगर रिजर्व में बाघों की आबादी सबसे ज्यादा है -&nbsp;</strong>कॉर्बेट टाइगर रिजर्व</li>\r\n<li style=\"font-weight: 400;\"><strong>भारत का पहला टाइगर रिजर्व कौन सा था -&nbsp;</strong>&nbsp;बांदीपुर टाइगर रिजर्व</li>\r\n<li style=\"font-weight: 400;\"><strong>भारत का</strong><strong>&nbsp;&nbsp;</strong><strong>नवीनतम (सबसे नया) टाइगर रिजर्व कौन सा है -&nbsp;</strong> गुरु घासीदास राष्ट्रीय उद्यान एवं तमोर पिंगला वन्यजीव अभयारण्य।</li>\r\n<li style=\"font-weight: 400;\"><strong>भारत के किस संरक्षित क्षेत्र में बाघों का घनत्व सबसे अधिक है - </strong>कॉर्बेट टाइगर रिजर्व</li>\r\n<li style=\"font-weight: 400;\"><strong>भारत के किस राज्य में सबसे ज्यादा टाइगर रिजर्व हैं - </strong>&nbsp;मध्य प्रदेश</li>\r\n</ul>\r\n<p style=\"font-weight: 400;\">&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>