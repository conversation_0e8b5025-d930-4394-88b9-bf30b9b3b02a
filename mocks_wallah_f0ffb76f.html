<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. Which ruler of the Chahamana dynasty made Ajmer his capital?</p>",
                    question_hi: "<p>1. चाहमान (चौहान) वंश के किस शासक ने अजमेर को अपनी राजधानी बनाया ?</p>",
                    options_en: ["<p>Prithviraj III</p>", "<p>Prithviraj II</p>", 
                                "<p>Arnoraj</p>", "<p>Ajayaraja</p>"],
                    options_hi: ["<p>पृथ्वीराज III</p>", "<p>पृथ्वीराज II</p>",
                                "<p>अर्णोराज</p>", "<p>अजयराज</p>"],
                    solution_en: "<p>1.(d) <strong>Ajayaraja. Chahamanas or Chauhans dynasty</strong>: Chauhans of Delhi and Ajmer, also known as the Chahamanas of Shakambhari, ruled over part of modern-day Rajasthan. Important Kings : Vasudeva, Ajayaraja, Arnoraja, Vigraharaja IV, Prithviraj Chauhan III, and Hariraja. Battles: Battle of Tarain (1191) - Prithviraj Chauhan defeated Muhammad Ghori, Second battle of Tarain (1192) - Muhammad Ghori defeated Prithviraj.</p>",
                    solution_hi: "<p>1.(d) <strong>अजयराज। चाहमान या चौहान वंश:</strong> दिल्ली और अजमेर के चौहान, जिन्हें शाकम्भरी के चाहमान भी कहा जाता है, इन्होंने आधुनिक राजस्थान के हिस्से पर शासन किया। महत्वपूर्ण राजा: वासुदेव, अजयराज, अर्नोराज, विग्रहराज चतुर्थ, पृथ्वीराज चौहान तृतीय और हरिराज। युद्ध: तराइन का प्रथम युद्ध (1191) - पृथ्वीराज चौहान ने मुहम्मद गोरी को हराया, तराइन का द्वितीय युद्ध (1192) - मुहम्मद गोरी ने पृथ्वीराज को हराया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. The sum of two numbers is 5 times their difference. If the smaller number is 24, ﬁnd the larger number.</p>",
                    question_hi: "<p>2. दो संख्याओं का योग उनके अंतर का 5 गुना है। यदि छोटी संख्या 24 है, तो बड़ी संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>32</p>", "<p>48</p>", 
                                "<p>30</p>", "<p>36</p>"],
                    options_hi: ["<p>32</p>", "<p>48</p>",
                                "<p>30</p>", "<p>36</p>"],
                    solution_en: "<p>2.(d) Let the numbers are x, y (x &gt; y)<br>The sum of two numbers is 5 times their difference.<br>So, <math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi></mrow><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> = 5 [using componendo and dividendo method]<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mrow><mn>2</mn><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>4</mn></mfrac></math><br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; y&nbsp;<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; 24<br>&rArr; x = 36</p>",
                    solution_hi: "<p>2.(d) मान लीजिए कि संख्याएँ x, y (x &gt; y) हैं।<br>दो संख्याओं का योग उनके अंतर का 5 गुना है।<br>तो, <math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi></mrow><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> = 5 [componendo और dividendo विधि का उपयोग करने पर]<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mrow><mn>2</mn><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>4</mn></mfrac></math><br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; y&nbsp;<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; 24<br>&rArr; x = 36</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Select the word that will come in the middle of the given words that are arranged in the order in which they would appear in the English dictionary?<br>Sports&rarr;Spoil-&rarr;-Spouse-&rarr;Spit-&rarr;Sparrow</p>",
                    question_hi: "<p>3. दिए गए शब्दों के बीच में आने वाले शब्द का चयन कीजिये जो उसी क्रम में है जिस क्रम में वे अंग्रेजी शब्दकोश में आयेंगे ?<br>Sports&rarr;Spoil-&rarr;-Spouse-&rarr;Spit-&rarr;Sparrow</p>",
                    options_en: ["<p>Sports</p>", "<p>Spit</p>", 
                                "<p>Spoil</p>", "<p>Spouse</p>"],
                    options_hi: ["<p>Sports</p>", "<p>Spit</p>",
                                "<p>Spoil</p>", "<p>Spouse</p>"],
                    solution_en: "<p>3.(c)<br>Sparrow &gt; Spit &gt; Spoil &gt; Sports &gt; Spouse</p>",
                    solution_hi: "<p>3.(c)<br>Sparrow &gt; Spit &gt; Spoil &gt; Sports &gt; Spouse</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. The structure that produces and holds sperm cells in bryophytes (non-vascular plants) and ferns is called:</p>",
                    question_hi: "<p>4. ब्रायोफाइटा (गैर-संवहनी पौधों) और फर्न में शुक्राणु कोशिकाओं का निर्माण और उन्हें होल्ड करने वाली संरचना क्या कहलाती है ?</p>",
                    options_en: ["<p>megasporangia</p>", "<p>antheridium</p>", 
                                "<p>archegonium</p>", "<p>protonema</p>"],
                    options_hi: ["<p>मेगास्पोरेंजिया (megasporangia)</p>", "<p>एंथेरिडियम (antheridium)</p>",
                                "<p>आर्कगोनियम (archegonium)</p>", "<p>प्रोटोनिमा (protonema)</p>"],
                    solution_en: "<p>4.(b) <strong>Antheridium </strong>- A saclike structure and male sex organ that produces and stores gametes or sex cells called sperm.They produce biflagellate antherozoids. Located on: A thin stalk attached to the gametophyte. <strong>Archegonium</strong> - The female reproductive organ in ferns and mosses. <strong>Megasporangia </strong>- The structure in plants that produces megaspores. <strong>Protonema</strong> - A thread-like chain of cells that forms the earliest stage of development of the gametophyte (the haploid phase) in the life cycle of mosses.</p>",
                    solution_hi: "<p>4.(b) <strong>एंथेरिडियम</strong> - एक थैलीनुमा संरचना और पुरुष लैंगिक अंग जो शुक्राणु नामक युग्मक या लैंगिक कोशिकाओं का उत्पादन और भंडारण करता है। वे बाइफ्लैगेलेट एथेरोज़ॉइड का उत्पादन करते हैं। यह गैमेटोफाइट से जुड़े एक पतले डंठल पर स्थित होता है। <strong>आर्कगोनियम -</strong> फर्न और काई में मादा प्रजनन अंग है । <strong>मेगास्पोरेंजिया </strong>- पौधों में वह संरचना जो मेगास्पोर पैदा करती है। <strong>प्रोटोनिमा</strong> - कोशिकाओं की एक धागे जैसी श्रृंखला जो काई के जीवन चक्र में गैमेटोफाइट (अगुणित चरण) के विकास के प्रारंभिक चरण का निर्माण करती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. Select the word from the options, which is similar to the given words in a certain manner:<br>Stable, Burrow, Nest</p>",
                    question_hi: "<p>5. विकल्पों में से उस शब्द का चयन कीजिए जो एक निश्चित तरीके से दिए गए शब्दों के समान है।<br>अस्तबल, मांद, घोंसला</p>",
                    options_en: ["<p>Den</p>", "<p>Slum</p>", 
                                "<p>City</p>", "<p>Herd</p>"],
                    options_hi: ["<p>मांद</p>", "<p>गंदी बस्ती</p>",
                                "<p>शहर</p>", "<p>झुण्ड</p>"],
                    solution_en: "<p>5.(a) Stable, burrow, nest all are the places where animals stay. Similarly from the given options, den means wild mammal&rsquo;s home.</p>",
                    solution_hi: "<p>5.(a) अस्तबल, मांद, घोसला सभी ऐसे स्थान हैं जहां जानवर रहते हैं। इसी प्रकार दिए गए विकल्पों में से खोह का अर्थ है जंगली स्तनपायी का घर।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. The sum of two numbers is 1500. Their LCM is 16379. Two numbers are</p>",
                    question_hi: "<p>6. दो संख्याओं का योग 1500 है। उनका LCM 16379 है। संख्याएँ ज्ञात कीजिये ?</p>",
                    options_en: ["<p>1489, 11</p>", "<p>1479, 21</p>", 
                                "<p>1453, 47</p>", "<p>1053, 447</p>"],
                    options_hi: ["<p>1489, 11</p>", "<p>1479, 21</p>",
                                "<p>1453, 47</p>", "<p>1053, 447</p>"],
                    solution_en: "<p>6.(a)<br>Let the two numbers are x and y,<br>i.e. x&nbsp;+ y = 1500&hellip;..(i)<br>and xy&nbsp;= 16379 (LCM)<br>(x - y)<sup>2</sup> = (x + y)<sup>2</sup> - 4xy<br>x - y = 1478 &hellip;.(ii)<br>Adding (i) and (ii)<br>(x + y) + (x - y) = 1500 + 1478 = 2978<br>2x = 2978,<br>x = 1489, <br>y = 11 <br><strong>Short Tricks:-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132279.png\" alt=\"rId4\" width=\"73\" height=\"132\"><br>Given that , <br>x + y = 1500 and (L.C.M.) xy = 16379<br>In this type of question , make a factor of L.C.M. <br>&rArr; 16379 = 11 &times; 1489<br>Clearly , condition satisfy , <br>x + y = 11 + 1489&nbsp;<br>= 1500</p>",
                    solution_hi: "<p>6.(a)<br>माना दो संख्याएं x और y हैं,<br>अर्थात x + y = 1500&hellip;..(i)<br>और xy = 16379 (LCM)<br>(x - y)<sup>2</sup> = (x + y)<sup>2</sup> - 4xy <br>x - y = 1478&hellip;.(ii)<br>(i) और (ii) जोड़ने पर, <br>(x + y) + (x - y) = 1500 + 1478 = 2978<br>2x = 2978,<br>x = 1489, <br>y = 11 <br><strong>Short Tricks:-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132279.png\" alt=\"rId4\" width=\"78\" height=\"141\"><br>दिया गया है ,<br>x + y = 1500 और (L.C.M.) xy = 16379<br>इस प्रकार के प्रश्नों में L.C.M का एक गुणनखण्ड बनाइए। <br>&rArr; 16379 = 11 &times; 1489<br>स्पष्ट रूप से, शर्त पूरी होती है, <br>x + y = 11 + 1489&nbsp;<br>= 1500</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. What is Kalbaisakhi ?</p>",
                    question_hi: "<p>7. कालबैसाखी क्या है ?</p>",
                    options_en: ["<p>Snowfall and hail</p>", "<p>Heatwaves and loo</p>", 
                                "<p>Pre monsoon showers and thunderstorms</p>", "<p>Cold waves and rains</p>"],
                    options_hi: ["<p>हिमपात और ओलावृष्टि</p>", "<p>गरम हवाएं और लू</p>",
                                "<p>मानसून से पहले की वर्षा और तड़ित् झंझावात</p>", "<p>शीत लहर और वर्षा</p>"],
                    solution_en: "<p>7.(c) Kaal Baisakhi (Norwester) - Dreaded evening thunderstorms in Bengal and Assam. It is also known as Bardoli cheerha in Assam. Other famous Local Storms of Hot Weather Season : <strong>Mango Shower</strong> - pre-monsoon showers are common especially in Kerala and Karnataka. They help in the early ripening of mangoes, and are often referred to as &lsquo;mango showers&rsquo;. <strong>Blossom Shower </strong>- With this shower, coffee flowers blossom in Kerala. <strong>Loo:</strong> These are strong, gusty, hot, dry winds blowing during the day over the north and northwestern India.</p>",
                    solution_hi: "<p>7.(c) कालबैसाखी (नॉर्वेस्टर) - बंगाल और असम में भयानक शाम की आंधी। इसे असम में बारदोली चीरहा के नाम से भी जाना जाता है। ग्रीष्म ऋतु के अन्य प्रसिद्ध स्थानीय तूफान : <strong>मैंगो शॉवर</strong> - प्री-मानसून बारिश विशेष रूप से केरल और कर्नाटक में आम है। वे आमों को जल्दी पकाने में मदद करते हैं, और इन्हें अक्सर \'आम वर्षा\' के रूप में जाना जाता है। <strong>ब्लॉसम शावर -</strong> इस शावर से केरल में कॉफी के फूल खिलते हैं। <strong>लू: </strong>ये उत्तर और उत्तर-पश्चिमी भारत में दिन के दौरान चलने वाली तीव्र, गर्म, शुष्क हवाएँ हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding/deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on I and 3 is not allowed)<br>(33, 56)<br>(44, 67)</p>",
                    question_hi: "<p>8. उस समुच्चय का चयन कीजिए, जिसकी संख्याओं के बीच वही संबंध है, जो नीचे दिए गए समुच्चयों की संख्याओं के बीच है।<br>(नोट: संक्रियाएं संख्याओं को उनके संघटक अंकों में विभक्त किए बिना, संपूर्ण संख्याओं पर की जानी चाहिए। उदाहरणार्थ 13- 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना / घटाना / गुणा करना इत्यादि 13 पर ही की जानी - चाहिए। 13 को 1 और 3 में विभक्त करने, और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(33, 56)<br>(44, 67)</p>",
                    options_en: ["<p>(5,28)</p>", "<p>(11, 36)</p>", 
                                "<p>(7,38)</p>", "<p>(12, 36)</p>"],
                    options_hi: ["<p>(5,28)</p>", "<p>(11, 36)</p>",
                                "<p>(7,38)</p>", "<p>(12, 36)</p>"],
                    solution_en: "<p>8.(a) <strong>Logic:</strong> (a, a + 23) i.e.<br>33, (33 + 23) = 56<br>44, (44 + 23) = 67<br>Similarly, <strong>5, (5 + 23) = 28</strong></p>",
                    solution_hi: "<p>8.(a) <strong>तर्क: </strong>(a, a + 23) i.e.<br>33, (33 + 23) = 56<br>44, (44 + 23) = 67<br>इसी प्रकार, <strong>5, (5 + 23) = 28</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. How many Members of Parliament were elected from Punjab to the 18th Lok Sabha ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132407.png\" alt=\"rId5\" width=\"139\" height=\"148\"> <br>The 2024 Indian general election was held in Punjab on 1 June 2024 to elect 13 members of the 18th Lok Sabha.<br>INC (7) , AAP (3)<br>Others (3) - SAD (1) , IND (2)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132588.png\" alt=\"rId6\" width=\"337\" height=\"258\"></p>",
                    question_hi: "<p>9. 18वीं लोकसभा में पंजाब से कितने सांसद (Members of Parliament) चुने गए ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132407.png\" alt=\"rId5\" width=\"147\" height=\"157\"> <br>The 2024 Indian general election was held in Punjab on 1 June 2024 to elect 13 members of the 18th Lok Sabha.<br>INC (7) , AAP (3)<br>Others (3) - SAD (1) , IND (2)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132588.png\" alt=\"rId6\" width=\"337\" height=\"258\"></p>",
                    options_en: ["<p>15</p>", "<p>14</p>", 
                                "<p>13</p>", "<p>17</p>"],
                    options_hi: ["<p>15</p>", "<p>14</p>",
                                "<p>13</p>", "<p>17</p>"],
                    solution_en: "<p>9.(c) 13</p>",
                    solution_hi: "<p>9.(c) 13</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. Solve the following:<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>21</mn></mfrac></math> = ?</p>",
                    question_hi: "<p>10. निम्नलिखित को हल कीजिये:<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>21</mn></mfrac></math> = ?</p>",
                    options_en: ["<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(d)<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>21</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 1 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>21</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>21</mn></mfrac></math></p>",
                    solution_hi: "<p>10.(d) <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>21</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 1 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>21</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>21</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. Bhutia dance is performed in which of the following states ?</p>",
                    question_hi: "<p>11. भूटिया नृत्य (Bhutia dance) निम्नलिखित में से किस राज्य में किया जाता है?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Odisha</p>", 
                                "<p>West Bengal</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>नागालैंड</p>", "<p>ओडिशा</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>11.(d) <strong>Sikkim</strong>. Other dances: Sikkim - Singhi Chham, Tamang Selo, Sherpa Dance, Ghantu, Gayley-Yang Dance, Sangey Chham Dance and Chu Faat. West Bengal - Purulia Chhau, Baul, and Gambhira. Odisha - Gotipua Dance, Dhap, and Paika. Nagaland - Modse, Agurshikukula, Aaluyattu, Sadal Kekai, and Changai Dance.</p>",
                    solution_hi: "<p>11.(d) <strong>सिक्किम</strong>। अन्य नृत्य: सिक्किम - सिंघी छम, तमांग सेलो, शेरपा नृत्य, घंटू, गेले-यांग नृत्य, संगे छम नृत्य और चू-फाट। पश्चिम बंगाल - पुरुलिया छऊ, बाउल और गंभीरा। ओडिशा - गोटीपुआ नृत्य, ढप, और पाइका। नागालैंड - मोडसे, अगुर्शिकुकुला, आलुयट्टू, सदल केकई और चंगई नृत्य।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. In the following questions, four letter pairs are given. The letters on the left side of (-) is related to the letters on the right side of (-) with some logic/rule/relation. Three are similar on the basis of same Logic/Rule/Relation. Select the odd one out from the given alternatives.</p>",
                    question_hi: "<p>12. निम्नलिखित प्रश्नों में चार अक्षर युग्म दिए गए हैं। (-) के बायीं ओर के अक्षर (-) के दायीं ओर के अक्षरों से कुछ तर्क/नियम/संबंध से संबंधित हैं। तीन समान तर्क/नियम/संबंध के आधार पर समान हैं। दिए गए विकल्पों में से विजातीय का चयन कीजिये ?</p>",
                    options_en: ["<p>SJF -- MPX</p>", "<p>ZBN -- EHU</p>", 
                                "<p>PSD -- KYU</p>", "<p>NRB -- IXS</p>"],
                    options_hi: ["<p>SJF -- MPX</p>", "<p>ZBN -- EHU</p>",
                                "<p>PSD -- KYU</p>", "<p>NRB -- IXS</p>"],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132713.png\" alt=\"rId7\" width=\"155\" height=\"58\"><br>Same pattern followed by option (C) and (D) also.</p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132713.png\" alt=\"rId7\" width=\"155\" height=\"58\"><br>विकल्प (C और (D) में भी वही पैटर्न है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. Which of the following statements is correct regarding the climate of the Indian subcontinent ?<br>A. The climate of North India remains similar throughout the year<br>B. The Himalayas protect the subcontinent from extremely cold winds from central Asia.</p>",
                    question_hi: "<p>13. भारतीय उपमहाद्वीप की जलवायु के संबंध में निम्नलिखित में से कौन-से कथन सही हैं ? <br>A. उत्तर भारत की जलवायु वर्ष भर एक समान रहती है। <br>B. हिमालय, मध्य एशिया से आने वाली अत्यधिक ठंडी हवाओं से उपमहाद्वीप की रक्षा करता है।</p>",
                    options_en: ["<p>Only Statement A is correct</p>", "<p>Both Statements A and B incorrect</p>", 
                                "<p>Only Statement B is correct</p>", "<p>Both Statements A and B correct</p>"],
                    options_hi: ["<p>केवल कथन A सही है</p>", "<p>कथन A और B दोनों गलत हैं</p>",
                                "<p>केवल कथन B सही है</p>", "<p>कथन A और B दोनों सही हैं</p>"],
                    solution_en: "<p>13.(c) South Asia experiences two monsoons, Southwest monsoon (summer monsoon) during June to September and the Northeast Monsoon (winter monsoon) during October to December. The Indian subcontinent receives most of its rainfall from the Southwest monsoon. Continentality - It refers to a climatic effect that emerges because of the different range of temperature that exists at places lying in the interior of the continent. Continental climates are common between 40&deg; and 70&deg; north latitude.</p>",
                    solution_hi: "<p>13.(c) दक्षिण एशिया में दो मानसून का अनुभव होता है, जून से सितंबर के दौरान दक्षिण-पश्चिम मानसून (ग्रीष्मकालीन मानसून) और अक्टूबर से दिसंबर के दौरान पूर्वोत्तर मानसून (शीतकालीन मानसून)। भारतीय उपमहाद्वीप में अधिकांश वर्षा दक्षिण-पश्चिम मानसून से होती है। महाद्वीपता - यह एक जलवायु प्रभाव को संदर्भित करता है जो महाद्वीप के आंतरिक भाग में स्थित स्थानों पर मौजूद ताप की विभिन्न सीमाओं के कारण उभरता है। महाद्वीपीय जलवायु 40&deg; और 70&deg; उत्तरी अक्षांश के बीच सामान्य है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. The value of 5 sin14&deg; sec76&deg; + 3cot 15&deg; &times; cot75&deg; + 2 tan45&deg; is</p>",
                    question_hi: "<p>14. 5 sin14&deg; sec76&deg; + 3cot 15&deg; &times; cot75&deg; + 2 tan45&deg; का मान क्या है?</p>",
                    options_en: ["<p>1</p>", "<p>8</p>", 
                                "<p>10</p>", "<p>0</p>"],
                    options_hi: ["<p>1</p>", "<p>8</p>",
                                "<p>10</p>", "<p>0</p>"],
                    solution_en: "<p>14.(c)&nbsp;[ In the question paper there is a printing mistake: in the middle instead of &lsquo;-&rsquo; sign it will be = &times; ]<br>As we know,<br>cot 15&deg; = 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> and cot75&deg; = 2 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> , tan45&deg; = 1&nbsp;<br>So,<br>5sin14&deg; sec76&deg; + 3cot 15&deg; &times; cot75&deg; + 2tan45&deg;&nbsp;<br>= 5cos 76&deg; sec76&deg; + 3cot 15&deg; &times; cot75&deg; + 2tan45&deg;&nbsp;<br>= 5 &times; 1 + 3 &times; (2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> ) &times; (2 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> ) + 2 &times; 1 <br>= 5 + 3 + 2 <br>= 10</p>",
                    solution_hi: "<p>14.(c)<br>[प्रश्न पत्र में छपाई की गलती है: बीच में - चिह्न के बजाय &times; यह होगा ]<br>जैसा कि हम जानते हैं :<br>cot 15&deg; = 2 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> और cot75&deg; = 2 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> , tan45&deg; = 1&nbsp;&nbsp;<br>इसलिए,<br>5sin14&deg; sec76&deg; + 3cot 15&deg; &times; cot75&deg; + 2tan45&deg;&nbsp;<br>= 5cos 76&deg; sec76&deg; + 3cot 15&deg; &times; cot75&deg; + 2tan45&deg;&nbsp;<br>= 5 &times; 1 + 3 &times; (2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> ) &times; (2 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> ) + 2 &times; 1 <br>= 5 + 3 + 2 <br>= 10</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. Which is the most widely accepted model to explain the formation and evaluation of the solar system ?</p>",
                    question_hi: "<p>15. सौरमंडल के निर्माण (formation) और निरूपण (evaluation) की व्याख्या करने के लिए सर्वाधिक व्यापक रूप से स्वीकृत मॉडल कौन-सा है ?</p>",
                    options_en: ["<p>Cloud hypothesis</p>", "<p>Gas hypothesis</p>", 
                                "<p>Nebular hypothesis</p>", "<p>Solar hypothesis</p>"],
                    options_hi: ["<p>क्लाउड परिकल्पना (Cloud hypothesis)</p>", "<p>गैसीय परिकल्पना (Gas hypothesis)</p>",
                                "<p>निहारिका परिकल्पना (Nebular hypothesis)</p>", "<p>सौर परिकल्पना (Solar hypothesis)</p>"],
                    solution_en: "<p>15.(c) <strong>Nebular hypothesis</strong> - The planets were formed out of a cloud of material associated with a youthful sun, which was slowly rotating. The theory was developed by Immanuel Kant. But Mathematician Laplace revised it in 1796. Other Hypothesis: Big Bang Theory -Propounded by George Lamaitre related to the origin of the universe . </p>",
                    solution_hi: "<p>15.(c) <strong>निहारिका परिकल्पना -</strong> ग्रहों का निर्माण युवा सूर्य से जुड़े पदार्थ के एक बादल से हुआ था, जो धीरे-धीरे घूम रहा था। सिद्धांत इमैनुएल कांट द्वारा विकसित किया गया था। लेकिन गणितज्ञ लाप्लास ने 1796 में इसे संशोधित किया। अन्य परिकल्पना: बिग बैंग सिद्धांत - ब्रह्मांड की उत्पत्ति से संबंधित जॉर्ज लामैत्रे द्वारा प्रतिपादित किया गया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. In a certain code, LITTLE is coded as 24, PARIS is coded as 23, BOX is coded as 12 and PIN is coded as 12. How will DONE be written as in that order?</p>",
                    question_hi: "<p>16. एक निश्चित कोड में, LITTLE को 24 के रूप में कोडित किया गया है, PARIS को 23 के रूप में कोड किया गया है, BOX को 12 के रूप में कोड किया गया है और PIN को 12 के रूप में कोड किया गया है। उसी क्रम में DONE को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>22</p>", "<p>27</p>", 
                                "<p>29</p>", "<p>38</p>"],
                    options_hi: ["<p>22</p>", "<p>27</p>",
                                "<p>29</p>", "<p>38</p>"],
                    solution_en: "<p>16.(a)</p>\n<p>Here we can see in case of LITTLE number of alphabets = 6, now its code = 24 &rArr; 2+4 = 6<br>In case of PARIS, number of alphabets = 5, now its code = 23 &rArr; 2 + 3 = 5<br>In case of both BOX &amp; PIN, number of alphabets = 3, now its code = 12 &rArr; 1 + 2 = 3<br>So, in case of DONE, number of alphabets = 4, now its code will be 4 &rArr; 2 + 2 = 22</p>",
                    solution_hi: "<p>16.(a) यहाँ हम LITTLE के मामले में देख सकते हैं, इसका कोड = 24 &rArr; 2 + 4 = 6<br>PARIS के मामले में अक्षरों की संख्या = 5, अब इसका कोड = 23 &rArr; 2 + 3 = 5<br>BOX और PIN दोनों के मामले में अक्षरों की संख्या = 3, अब इसका कोड = 12 &rArr; 1 + 2 = 3<br>अत:, DONE अक्षरों की संख्या = 4 होने की स्थिति में, अब इसका कोड 4 &rArr; 2 + 2 = 22 होगा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. Dr.Teejan Bai, a Padma Shri, Padma Bhushan and Padma Vibhushan awardee is globally recognised for her contribution to which of the following art forms?</p>",
                    question_hi: "<p>17. पद्म श्री, पद्म भूषण और पद्म विभूषण पुरस्कार से सम्मानित, डॉ. तीजन बाई, निम्नलिखित में से किस कला शैली में उनके योगदान के लिए विश्वप्रसिद्ध हैं ?</p>",
                    options_en: ["<p>Pandavani</p>", "<p>Raut Nacha</p>", 
                                "<p>Jhirliti</p>", "<p>Gendi</p>"],
                    options_hi: ["<p>पंडवानी</p>", "<p>राउत नाच</p>",
                                "<p>झिरलिटि</p>", "<p>गेंडी </p>"],
                    solution_en: "<p>17.(a) <strong>Pandavani</strong> (a traditional performing art form of chattisgarh). Dr.Teejan Bai (Chhattisgarh) awarded: Padma Vibhushan Award (2019), Padma Bhushan (2003), Padma Shri (1988), and M. S. Subbulakshmi Centenary Award (2016). Other folk dances of Chhattisgarh: <strong>Raut Nacha</strong> - It is a traditional dance of the Yadav community which is performed on Deepawali. <strong>Jhirliti</strong> - One of the famous Bastar dances. <strong>Gendi</strong> - It is a tribal dance of Bastar and completely a dance of balance.</p>",
                    solution_hi: "<p>17.(a) <strong>पंडवानी</strong> (छत्तीसगढ़ का एक पारंपरिक प्रदर्शन कला रूप)। डॉ. तीजन बाई (छत्तीसगढ़) को पद्म विभूषण (2019), पद्म भूषण (2003), पद्म श्री (1988) पुरस्कार , और एम. एस. सुब्बुलक्ष्मी शताब्दी पुरस्कार (2016) से सम्मानित किया गया। छत्तीसगढ़ के अन्य लोक नृत्य: <strong>राउत नाच </strong>- यह यादव समुदाय का पारंपरिक नृत्य है जो दीपावली पर किया जाता है। <strong>झिरलिटि-</strong> बस्तर के प्रसिद्ध नृत्यों में से एक है। <strong>गेंडी</strong> - यह बस्तर का आदिवासी नृत्य और पूर्णतया संतुलन का नृत्य है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. In any triangle ABC, a + b + c = 2s with usual notation, then sin<math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>A</mi></mrow><mrow><mn>2</mn></mrow></mfrac><mo>)</mo></math> = ?</p>",
                    question_hi: "<p>18. किसी भी त्रिभुज ABC में, a + b + c = 2s सामान्य संकेतन के साथ, तो sin<math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>A</mi></mrow><mrow><mn>2</mn></mrow></mfrac><mo>)</mo></math> = ?</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mfrac><mrow><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>c</mi></mrow></mfrac></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mrow><mi>b</mi><mi>c</mi></mrow></mfrac></msqrt></math></p>", 
                                "<p><math display=\"inline\"><msqrt><mfrac><mrow><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mrow><mi>s</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow></mfrac></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mfrac><mrow><mi>s</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mrow><mi>b</mi><mi>c</mi></mrow></mfrac></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mfrac><mrow><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>c</mi></mrow></mfrac></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mrow><mi>b</mi><mi>c</mi></mrow></mfrac></msqrt></math></p>",
                                "<p><math display=\"inline\"><msqrt><mfrac><mrow><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mrow><mi>s</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow></mfrac></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mfrac><mrow><mi>s</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mrow><mi>b</mi><mi>c</mi></mrow></mfrac></msqrt></math></p>"],
                    solution_en: "<p>18.(b)<br>a + b + c = 2s<br>a + b = 2s - c<br>a + c = 2s - b<br>From cosine rule <br>Cos A = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math><br>2 sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math> = 1 - cos A<br>&rArr; 2 sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math>&nbsp;= 1 - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math>)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>2</mn><mi>b</mi><mi>c</mi><mo>-</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math><br>&rArr; 2 sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><mi>a</mi><mo>+</mo><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo><mo>]</mo><mo>[</mo><mi>a</mi><mo>-</mo><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo><mo>]</mo></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math><br>&rArr; 2 sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mi>s</mi><mo>-</mo><mn>2</mn><mi>c</mi><mo>)</mo><mo>(</mo><mn>2</mn><mi>s</mi><mo>-</mo><mn>2</mn><mi>b</mi><mo>)</mo></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math><br>&rArr; sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mrow><mn>4</mn><mi>b</mi><mi>c</mi></mrow></mfrac><mi>&#160;</mi></math><br>&rArr; sin<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mrow><mi>b</mi><mi>c</mi></mrow></mfrac></msqrt></math></p>",
                    solution_hi: "<p>18.(b)<br>a + b + c = 2s<br>a + b = 2s - c<br>a + c = 2s - b<br>cosine नियम से<br>Cos A = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math><br>2 sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math> = 1 - cos A<br>&rArr; 2 sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math>&nbsp;= 1 - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math>)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>2</mn><mi>b</mi><mi>c</mi><mo>-</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math><br>&rArr; 2 sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><mi>a</mi><mo>+</mo><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo><mo>]</mo><mo>[</mo><mi>a</mi><mo>-</mo><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo><mo>]</mo></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math><br>&rArr; 2 sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mi>s</mi><mo>-</mo><mn>2</mn><mi>c</mi><mo>)</mo><mo>(</mo><mn>2</mn><mi>s</mi><mo>-</mo><mn>2</mn><mi>b</mi><mo>)</mo></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac></math><br>&rArr; sin<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mrow><mn>4</mn><mi>b</mi><mi>c</mi></mrow></mfrac><mi>&#160;</mi></math><br>&rArr; sin<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mrow><mi>b</mi><mi>c</mi></mrow></mfrac></msqrt></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. In how many phases did the Indian Economy experience the success of the Green Revolution ?</p>",
                    question_hi: "<p>19. भारतीय अर्थव्यवस्था ने हरित क्रांति की सफलता का अनुभव कितने चरणों में किया ?</p>",
                    options_en: ["<p>Three</p>", "<p>Two</p>", 
                                "<p>Five</p>", "<p>Four</p>"],
                    options_hi: ["<p>तीन</p>", "<p>दो</p>",
                                "<p>पांच</p>", "<p>चार</p>"],
                    solution_en: "<p>19.(b) <strong>Two.</strong> The stagnation in agriculture during the colonial rule was permanently broken by the green revolution. This refers to the large increase in production of food grains resulting from the use of high yielding variety (HYV) seeds especially for wheat and rice. In the first phase (the mid-60s to mid-70s), the use of HYV seeds was restricted to a more affluent state like Punjab, Andhra Pradesh, and Tamil Nadu. In the second phase (the mid-70s to mid-80s), the HYV technology spread to other states and benefitted more variety of crops.</p>",
                    solution_hi: "<p>19.(b) <strong>दो। </strong>औपनिवेशिक शासन के दौरान कृषि में आया ठहराव हरित क्रांति से स्थायी रूप से टूट गया। यह विशेष रूप से गेहूं और चावल के लिए उच्च उपज वाले किस्म (HYV) बीजों के उपयोग के परिणामस्वरूप खाद्यान्न उत्पादन में बड़ी वृद्धि को संदर्भित करता है। प्रथम चरण में (60 के दशक के मध्य से 70 के दशक के मध्य तक), HYV बीजों का उपयोग पंजाब, आंध्र प्रदेश और तमिलनाडु जैसे अधिक समृद्ध राज्यों तक ही सीमित था। द्वितीय चरण में (70 के दशक के मध्य से 80 के दशक के मध्य तक), HYV तकनीक अन्य राज्यों में फैल गई और अधिक विविधता वाली फसलों को लाभ हुआ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20.If 351 means \'students like maths\'; 197 means \'we are students\' and 748 means \'they are parents\' what is code for the word \'we\'?</p>",
                    question_hi: "<p>20.यदि 351 का मतलब \'students like maths\' है, 197 का मतलब \'we are students\' और 748 का मतलब \'they are parents\' है, तो किसका मतलब \'we\' होगा?</p>",
                    options_en: ["<p>7</p>", "<p>8</p>", 
                                "<p>1</p>", "<p>9</p>"],
                    options_hi: ["<p>7</p>", "<p>8</p>",
                                "<p>1</p>", "<p>9</p>"],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132849.png\" alt=\"rId8\" width=\"232\" height=\"73\"><br>From the above diagram it is clearly seen that the code for &lsquo;we&rsquo; is 9.</p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132849.png\" alt=\"rId8\" width=\"232\" height=\"73\"><br>उपरोक्त आरेख से यह स्पष्ट रूप से देखा जा सकता है कि \'we\' के लिए कोड 9 है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. Shore temple of Mamallapuram was built by:</p>",
                    question_hi: "<p>21. मामल्लपुरम का तटीय मंदिर (Shore temple of Mamallapuram) किसके द्वारा बनवाया गया था ?</p>",
                    options_en: ["<p>Cholas</p>", "<p>Chalukyas</p>", 
                                "<p>Pallavas</p>", "<p>Nagas</p>"],
                    options_hi: ["<p>चोल</p>", "<p>चालुक्य</p>",
                                "<p>पल्लव</p>", "<p>नाग </p>"],
                    solution_en: "<p>21.(c) <strong>Pallavas</strong> (Narasimhavarman II). The famous Shore Temple is located at Mahabalipuram or Mamallapuram (Tamil Nadu), located along southeastern India&rsquo;s Coromandel Coast. It was declared a UNESCO World Heritage Site in 1984. <strong>Pallavas Dynasty</strong> (275 - 897 CE) - Founder - Simhavishnu, Capital - Kanchipuram. Famous rulers of Pallavas dynasty - Sivaskandavarman, Mahendravarman.</p>",
                    solution_hi: "<p>21.(c) <strong>पल्लव</strong> (नरसिम्हावर्मन द्वितीय)। प्रसिद्ध तट मंदिर महाबलीपुरम या मामल्लपुरम (तमिलनाडु) में स्थित है, जो दक्षिणपूर्वी भारत के कोरोमंडल तट पर स्थित है। इसे 1984 में UNESCO द्वारा विश्व धरोहर स्थल घोषित किया गया था। <strong>पल्लव वंश</strong> (275 - 897 ई.) : संस्थापक - सिंहविष्णु, राजधानी - कांचीपुरम। पल्लव वंश के प्रसिद्ध शासक - शिवस्कंदवर्मन, महेंद्रवर्मन।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. From the top of a building 60 m high, the angles of depression of the top and the bottom of a tower are 30&deg; and 60&deg; respectively. The height of the tower is:</p>",
                    question_hi: "<p>22. 60 मीटर ऊँचे एक भवन के शिखर से एक मीनार के ऊपर और नीचे के अवनमन कोण क्रमशः 30&deg; और 60&deg; हैं। टावर की ऊंचाई कितनी है?</p>",
                    options_en: ["<p>18 m</p>", "<p>40 m</p>", 
                                "<p>30 m</p>", "<p>42 m</p>"],
                    options_hi: ["<p>18 m</p>", "<p>40 m</p>",
                                "<p>30 m</p>", "<p>42 m</p>"],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132969.png\" alt=\"rId9\" width=\"135\" height=\"132\"><br>From the top of a building 60m high the angles of depression of the top and the bottom of a tower are 30&deg; and 60&deg; respectively. <br>Let the height of the tower is = H and base = B,<br>so, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mi>B</mi></mfrac></math> = tan 60&deg;<br>&rArr; B = 20<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mi>H</mi></mrow><mi>B</mi></mfrac></math> =&nbsp;tan 60&deg;&nbsp;<br>&rArr; H = (60 - 20) = 40<br>So, the height of the tower is 40m.</p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290132969.png\" alt=\"rId9\" width=\"136\" height=\"133\"><br>एक 60 मीटर ऊँचे भवन के शिखर से एक मीनार के ऊपर और नीचे के अवनमन कोण क्रमशः 30&deg; और 60&deg; हैं।<br>माना टावर की ऊंचाई = H और आधार = B है;<br>इसलिए, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mi>B</mi></mfrac></math> = tan 60&deg;<br>&rArr; B = 20<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>अभी, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mi>H</mi></mrow><mi>B</mi></mfrac></math> =&nbsp;tan 60&deg;&nbsp;<br>&rArr; H = (60 - 20) = 40<br>तो, टावर की ऊंचाई 40m है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. Which of the following books is an autobiography of the former American president Bill Clinton ?</p>",
                    question_hi: "<p>23. निम्नलिखित पुस्तकों में से कौन-सी पूर्व अमेरिकी राष्ट्रपति बिल क्लिंटन की आत्मकथा है ?</p>",
                    options_en: ["<p>Long Walk to Freedom</p>", "<p>My Country My Life</p>", 
                                "<p>My Life</p>", "<p>Becoming</p>"],
                    options_hi: ["<p>लॉन्ग वॉक टू फ्रीडम</p>", "<p>माई कंट्री माई लाइफ</p>",
                                "<p>माइ लाइफ</p>", "<p>बिकमिंग</p>"],
                    solution_en: "<p>23.(c)<strong> My Life. Famous Autobiographies </strong>- Long Walk to Freedom (Nelson Mandela), Becoming (Michelle Obama), Wings Of Fire (A.P.J. Abdul Kalam), Changing India (Dr. Manmohan Singh), An American Life (Ronald Reagan), Daughter of Destiny (Benazir Bhutto), Courage and Conviction (V K Singh).</p>",
                    solution_hi: "<p>23.(c) <strong>माइ लाइफ। प्रसिद्ध आत्मकथाएँ -</strong> लॉन्ग वॉक टू फ्रीडम (नेल्सन मंडेला), बिकमिंग (मिशेल ओबामा), विंग्स ऑफ फायर (ए.पी.जे. अब्दुल कलाम), चेंजिंग इंडिया (डॉ. मनमोहन सिंह), एन अमेरिकन लाइफ (रोनाल्ड रीगन), डॉटर ऑफ डेस्टिनी (बेनजीर भुट्टो) ), करेज एंड कन्विक्शन (वी. के सिंह)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. Complete the series.<br>1, 2, 6, 24, 120, (...)</p>",
                    question_hi: "<p>24. श्रेणी को पूर्ण करें।<br>1, 2, 6, 24, 120, (...)</p>",
                    options_en: ["<p>725</p>", "<p>711</p>", 
                                "<p>720</p>", "<p>715</p>"],
                    options_hi: ["<p>725</p>", "<p>711</p>",
                                "<p>720</p>", "<p>715</p>"],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133080.png\" alt=\"rId10\" width=\"213\" height=\"42\"></p>",
                    solution_hi: "<p>24(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133080.png\" alt=\"rId10\" width=\"213\" height=\"42\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. The Guru Kelucharan Mohapatra Yuva Pratibha Samman 2021 was presented to Arushi Mudgal for _________ dance.</p>",
                    question_hi: "<p>25. आरुषि मुद्गल को __________ नृत्य के लिए गुरु केलुचरण महापात्र युवा प्रतिभा सम्मान, 2021 से सम्मानित किया गया।</p>",
                    options_en: ["<p>Mohiniyattam</p>", "<p>Kuchipudi</p>", 
                                "<p>Kathak</p>", "<p>Odissi</p>"],
                    options_hi: ["<p>मोहिनीअट्टम</p>", "<p>कुचिपुड़ी</p>",
                                "<p>कथक</p>", "<p>ओडिसी</p>"],
                    solution_en: "<p>25.(d) <strong>Odissi </strong>- Classical dance forms which originated in the Hindu temples of the eastern coastal state (Odisha). Other maestros - Kelucharan Mohapatra, Raghunath Dutta, Deba Prasad Das, Pankaj Charan Das, Gangadhar Pradhan, Leana Citaristi, Sonal Mansingh, Jhelum Paranjape, and Mayadhar Raut.</p>",
                    solution_hi: "<p>25.(d) <strong>ओडिसी </strong>- शास्त्रीय नृत्य रूप जिनकी उत्पत्ति पूर्वी तटीय राज्य (ओडिशा) के हिंदू मंदिरों में हुई थी। अन्य कलाकार - केलुचरण महापात्र, रघुनाथ दत्ता, देबा प्रसाद दास, पंकज चरण दास, गंगाधर प्रधान, इलियाना सिटारिस्टी, सोनल मानसिंह, झेलम परांजपे और मायाधर राउत।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. The total cost of flooring a room at Rs.12.50 per m<sup>2</sup> is Rs.3,675. If the breadth and length are in the ratio of 2 : 3, find its length.</p>",
                    question_hi: "<p>26. 12.50 रुपये प्रति वर्ग मीटर पर एक कमरे के फर्श को मरम्मत करने की कुल लागत 3,675 रुपये है। यदि चौड़ाई और लंबाई का अनुपात 2:3 है, तो इसकी लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>21 m</p>", "<p>15 m</p>", 
                                "<p>14 m</p>", "<p>18 m</p>"],
                    options_hi: ["<p>21 m</p>", "<p>15 m</p>",
                                "<p>14 m</p>", "<p>18 m</p>"],
                    solution_en: "<p>26.(a) Area of the floor = <math display=\"inline\"><mfrac><mrow><mn>36750</mn></mrow><mrow><mn>12</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> m<sup>2</sup> = 294 m<sup>2</sup><br>breadth and length are in the ratio of 2 : 3,<br>&rArr; 6x<sup>2</sup> = 294 <br>&rArr; x = 7 <br>i.e. length of the floor = 7 &times; 3 m <br>= 21 m</p>",
                    solution_hi: "<p>26.(a) फर्श का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>36750</mn></mrow><mrow><mn>12</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> m<sup>2</sup> = 294 m<sup>2</sup><br>चौड़ाई और लंबाई 2 : 3 के अनुपात में हैं,<br>&rArr; 6x<sup>2</sup> = 294 <br>&rArr; x = 7<br>अर्थात फर्श की लंबाई, = 7 &times; 3 m&nbsp;<br>= 21 m</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. Which of the following was established by Jyotirao Govindrao Phule in 1873 to fight against idolatry and the caste system ?</p>",
                    question_hi: "<p>27. ज्योतिराव गोविंदराव फुले ने 1873 में मूर्तिपूजा और जाति व्यवस्था के खिलाफ लड़ने के लिए निम्नलिखित में से किसकी स्थापना की थी ?</p>",
                    options_en: ["<p>Young Bengal Movement</p>", "<p>Satyashodhak Samaj</p>", 
                                "<p>Shree Narayan Guru Dharma Paripalana (SNDP) Movement</p>", "<p>Arya Samaj</p>"],
                    options_hi: ["<p>यंग बंगाल आंदोलन</p>", "<p>सत्यशोधक समाज</p>",
                                "<p>श्री नारायण गुरु धर्म परिपालन (SNDP) आंदोलन</p>", "<p>आर्य समाज</p>"],
                    solution_en: "<p>27.(b) <strong>Satyashodhak Samaj </strong>was established in Pune district of Maharashtra. Other Reform movements and their founders : Young Bengal Movement - Henry Vivian Derozio; Shree Narayan Guru Dharma Paripalana (SNDP) Movement (1903) - Dr. Padmanabhan Palpu; Arya Samaj (1875) - Swami Dayanand Saraswati; Tattvabodhini sabha (1839) - Debendranath Tagore. Amitya sabha (1814) - Raja Rammohan Roy.</p>",
                    solution_hi: "<p>27.(b) <strong>सत्यशोधक समाज </strong>की स्थापना महाराष्ट्र के पुणे जिले में हुई थी। अन्य सुधार आंदोलन और उनके संस्थापक: यंग बंगाल आंदोलन - हेनरी विवियन डेरोजियो; श्री नारायण गुरु धर्म परिपालन (SNDP) आंदोलन (1903) - डॉ. पद्मनाभन पालपू; आर्य समाज (1875) - स्वामी दयानंद सरस्वती। तत्त्वबोधिनी सभा (1839) - देवेन्द्रनाथ टैगोर; अमित्य सभा (1814) - राजा राममोहन राय।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. Select the combination of letters that when sequentially placed in the blanks will create a repetitive pattern.<br>bb_cc_bb_bcccc_b</p>",
                    question_hi: "<p>28. अक्षरों के संयोजन का चयन कीजिये जिसे क्रमिक रूप से रिक्त स्थान में रखा जाए तो एक दोहराव पैटर्न बन जाएगा।<br>bb_cc_bb_bcccc_b</p>",
                    options_en: ["<p>b; c; c; b</p>", "<p>b; b; c; c</p>", 
                                "<p>c; b; c; b</p>", "<p>c; c; b; b</p>"],
                    options_hi: ["<p>b; c; c; b</p>", "<p>b; b; c; c</p>",
                                "<p>c; b; c; b</p>", "<p>c; c; b; b</p>"],
                    solution_en: "<p>28.(d)<br>bb/_cc_ /bb_b /cccc /_b<br>In above arrangement we observe that 2 times b then 4 times c <br>And then 4 times b and 4 times c then again 2 times b.</p>",
                    solution_hi: "<p>28.(d)<br>bb/_cc_ /bb_b /cccc /_b<br>उपरोक्त व्यवस्था में हम देखते हैं कि 2 बार b फिर 4 बार c<br>और फिर 4 बार b और 4 बार c फिर 2 बार b</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. Prithviraj Raso, an epic poem about the life of the 12th century Indian king, Prithviraj Chauhan, was written by:</p>",
                    question_hi: "<p>29. 12वीं शताब्दी के भारतीय राजा पृथ्वीराज चौहान के जीवन पर लिखित महाकाव्य पृथ्वीराज रासो की रचना _______ ने की थी।</p>",
                    options_en: ["<p>Chand Bardai</p>", "<p>Banabhatta</p>", 
                                "<p>Bharavi</p>", "<p>Ashvaghosha</p>"],
                    options_hi: ["<p>चंदबरदाई</p>", "<p>बाणभट्ट</p>",
                                "<p>भारवि</p>", "<p>अश्वघोष</p>"],
                    solution_en: "<p>29.(a) <strong>Chand Bardai. Indian Epics and writers</strong> - Mahabharata (Sage Ved Vyasa), Ramayana (Valmiki), Raghuvamsa (Kalidasa), Buddhacharita (Asvoghosha), Silapadikaram (Ilango Adigal in Tamil), Ramcharitmanas (Tulsidas in Awadhi), Kumarasambava (Kalidasa in Sanskrit). Banabhatta wrote Harshacharita and Bharavi wrote Kiratarjuniya.</p>",
                    solution_hi: "<p>29.(a)<strong> चंदबरदाई। भारतीय महाकाव्य और लेखक </strong>- महाभारत (ऋषि वेद व्यास), रामायण (वाल्मीकि), रघुवंश (कालिदास), बुद्धचरित (अस्वघोष), सिलप्पादिकाराम (तमिल में इलंगो आदिगल), रामचरितमानस (अवधी में तुलसीदास), कुमारसंभव (संस्कृत में कालिदास)। बाणभट्ट ने हर्षचरित और भारवि ने किरातार्जुनीयम् लिखा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. The adjacent sides of a parallelogram are 4a and 3a. If the angle between them is 60&deg; , then one of the diagonals of the parallelogram will be:</p>",
                    question_hi: "<p>30. एक समांतर चतुर्भुज की आसन्न भुजाओं के माप 4a और 3a हैं। यदि उनके बीच का कोण 60&deg; है, तो समांतर चतुर्भुज के किसी एक विकर्ण का मान इनमें में किसके बराबर होगा?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math>a</p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>a</p>", 
                                "<p>5<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>a</p>", "<p>3<math display=\"inline\"><msqrt><mn>3</mn><mi>a</mi></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math>a</p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>a</p>",
                                "<p>5<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>a</p>", "<p>3<math display=\"inline\"><msqrt><mn>3</mn><mi>a</mi></msqrt></math></p>"],
                    solution_en: "<p>30.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133202.png\" alt=\"rId11\" width=\"144\" height=\"84\"><br>Using Cosine Rule :- <br>d<sup>2</sup> = (3a)<sup>2</sup> + (4a)<sup>2</sup> - 2 &times; 3a &times; 4a &times; cos 60&deg;<br>d<sup>2</sup> = 9a<sup>2</sup> + 16a<sup>2</sup> - 12a<sup>2</sup><br>d<sup>2</sup> = 13a<sup>2</sup><br>d = <math display=\"inline\"><msqrt><mn>13</mn></msqrt></math>a</p>",
                    solution_hi: "<p>30.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133202.png\" alt=\"rId11\"><br>Cosine नियम का प्रयोग करने पर :-<br>d<sup>2</sup> = (3a)<sup>2</sup> + (4a)<sup>2</sup> - 2 &times; 3a &times; 4a &times; cos 60&deg;<br>d<sup>2</sup> = 9a<sup>2</sup> + 16a<sup>2</sup> - 12a<sup>2</sup><br>d<sup>2</sup> = 13a<sup>2</sup><br>d = <math display=\"inline\"><msqrt><mn>13</mn></msqrt></math>a</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. Atal Tunnel connects which two places ?</p>",
                    question_hi: "<p>31. अटल सुरंग किन दो स्थानों को जोड़ती है ?</p>",
                    options_en: ["<p>Manali and Lahaul-Spiti valley</p>", "<p>Shimla and Nainital</p>", 
                                "<p>Manali and Nainital</p>", "<p>Shimla and Mansoori</p>"],
                    options_hi: ["<p>मनाली और लाहौल-स्पीति घाटी</p>", "<p>शिमला और नैनीताल</p>",
                                "<p>मनाली और नैनीताल</p>", "<p>शिमला और मंसूरी</p>"],
                    solution_en: "<p>31.(a) <strong>Manali and Lahaul-Spiti valley.</strong> Atal tunnel built under the Rohtang Pass in the eastern Pir Panjal range of the Himalayas on the Leh-Manali Highway in Himachal Pradesh, India. It is 9.02 km long. It is the world\'s longest highway single tube tunnel above 10,000 feet. <strong>Built by:</strong> Border Roads Organisation (BRO). <strong>Other Tunnels</strong> <strong>(India): </strong>Trivandrum Port Railway Tunnel (Kerala), Banihal Qazigund Road Tunnel (Jammu and Kashmir), and Karbude Railway Tunnel (Maharashtra). India&rsquo;s longest electrified tunnel (6.6 km) constructed between Cherlopalli and Rapuru railway stations.</p>",
                    solution_hi: "<p>31.(a) <strong>मनाली और लाहौल-स्पीति घाटी। </strong>अटल सुरंग भारत के हिमाचल प्रदेश में लेह मनाली राजमार्ग पर हिमालय की पूर्वी पीर पंजाल श्रृंखला में रोहतांग दर्रे के नीचे बनी है। यह 9.02 किमी लंबा है। यह 10,000 फीट से ऊंची विश्व की सबसे लंबी हाईवे सिंगल ट्यूब सुरंग है। <strong>निर्मित:</strong> सीमा सड़क संगठन (BRO)। <strong>अन्य सुरंगें (भारत):</strong> त्रिवेन्द्रम पोर्ट रेलवे सुरंग (केरल), बनिहाल काजीगुंड रोड सुरंग (जम्मू और कश्मीर), और करबुडे रेलवे सुरंग (महाराष्ट्र)। भारत की सबसे लंबी विद्युतीकृत सुरंग (6.6 किमी) चेरलोपल्ली और रापूरू रेलवे स्टेशनों के बीच बनाई गई है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. Solve the following:<br>(1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></mstyle></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac></mstyle></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math>) = ?</p>",
                    question_hi: "<p>32. निम्नलिखित को हल करें:<br>(1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></mstyle></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac></mstyle></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math>) = ?</p>",
                    options_en: ["<p>1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math></p>", "<p>x + 4</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math></p>"],
                    options_hi: ["<p>1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math></p>", "<p>x + 4</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math></p>"],
                    solution_en: "<p>32.(d)<br>(1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></mstyle></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac></mstyle></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math>)<br>=(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>1</mn></mrow><mi>x</mi></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mi>x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></mstyle></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow><mrow><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac></mstyle></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math>)<br>= <math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math></p>",
                    solution_hi: "<p>32.(d)<br>(1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></mstyle></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac></mstyle></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math>)<br>=(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>1</mn></mrow><mi>x</mi></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mi>x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></mstyle></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow><mrow><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac></mstyle></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math>)<br>= <math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Who among the following founded the Kalakshetra Foundation in Chennai that is an important centre for the study and performance of fine arts ?</p>",
                    question_hi: "<p>33. निम्नलिखित में से किसने चेन्नई में कलाक्षेत्र फाउंडेशन की स्थापना की, जो ललित कलाओं के अध्ययन और प्रदर्शन के लिए एक महत्त्वपूर्ण केंद्र है ?</p>",
                    options_en: ["<p>Alarmel Valli</p>", "<p>Mrinalini Sarabhai</p>", 
                                "<p>Yamini Krishnamurthy</p>", "<p>Rukmini Devi Arundale</p>"],
                    options_hi: ["<p>अलार्मेल वल्ली (Alarmel Valli)</p>", "<p>मृणालिनी साराभाई (Mrinalini Sarabhai)</p>",
                                "<p>यामिनी कृष्णमूर्ति (Yamini Krishnamurthy)</p>", "<p>रुक्मिणी देवी अरुंडेल (Rukmini Devi Arundale)</p>"],
                    solution_en: "<p>33.(d) <strong>Rukmini Devi Arundale</strong> founded the Kalakshetra Foundation in Chennai in 1936. Foundation was declared as an institution of national importance by an Act of Parliament in 1993. It is an important centre for the study and performance of fine arts, especially Bharatanatyam dance and Carnatic music. Exponents and their dances - Alarmel Valli (Bharatanatyam), Yamini Krishnamurthy (Kuchipudi and Bharatanatyam), Mrinalini Sarabhai (Kathakali and Bharatnatayam).</p>",
                    solution_hi: "<p>33.(d) <strong>रुक्मिणी देवी अरुंडेल </strong>ने 1936 में चेन्नई में कलाक्षेत्र फाउंडेशन की स्थापना की। फाउंडेशन को 1993 में संसद के एक अधिनियम द्वारा राष्ट्रीय महत्व की संस्था घोषित किया गया था। यह ललित कलाओं के अध्ययन और प्रदर्शन के लिए एक महत्वपूर्ण केंद्र है। विशेषकर भरतनाट्यम नृत्य और कर्नाटक संगीत। प्रतिपादक और उनके नृत्य - अलार्मेल वल्ली (भरतनाट्यम), यामिनी कृष्णमूर्ति (कुचिपुड़ी और भरतनाट्यम), मृणालिनी साराभाई (कथकली और भरतनाट्यम)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. Study the given pattern carefully and select the number that can replace the question mark (?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133350.png\" alt=\"rId12\" width=\"226\" height=\"75\"></p>",
                    question_hi: "<p>34. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस संख्या का चयन कीजिये जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सकता है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133350.png\" alt=\"rId12\" width=\"226\" height=\"75\"></p>",
                    options_en: ["<p>8</p>", "<p>12</p>", 
                                "<p>10</p>", "<p>6</p>"],
                    options_hi: ["<p>8</p>", "<p>12</p>",
                                "<p>10</p>", "<p>6</p>"],
                    solution_en: "<p>34.(d)<br>8 + 4&nbsp; + 2 + 12 = 26 &divide;&nbsp;2 = 13<br>16 + 8 + 4 + 24 = 52 &divide; 2 = 26<br>14 + 6 + 2 + 22 = 44&nbsp;&divide; 2 = 22</p>",
                    solution_hi: "<p>34.(d)<br>8 + 4&nbsp; + 2 + 12 = 26 &divide;&nbsp;2 = 13<br>16 + 8 + 4 + 24 = 52 &divide; 2 = 26<br>14 + 6 + 2 + 22 = 44 &divide; 2 = 22</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. According to the Global Hunger Index 2022, which neighbouring country is only behind India with a rank of 109 out of 121 countries ?</p>",
                    question_hi: "<p>35. वैश्विक भुखमरी सूचकांक (Global Hunger Index) 2022 के अनुसार, इनमें से कौन-सा एकमात्र पड़ोसी देश 121 देशों में से 109 रैंक के साथ भारत से पीछे है ?</p>",
                    options_en: ["<p>Afghanistan</p>", "<p>Pakistan</p>", 
                                "<p>Nepal</p>", "<p>Bangladesh</p>"],
                    options_hi: ["<p>अफगानिस्तान</p>", "<p>पाकिस्तान</p>",
                                "<p>नेपाल</p>", "<p>बांग्लादेश</p>"],
                    solution_en: "<p>35.(a) <strong>Afghanistan. The Global Hunger Index (GHI)</strong> is a tool that attempts to measure and track hunger globally as well as by region and by country. It is calculated annually and published by Concern Worldwide and Welthungerhilfe, Non-Government Organisations from Ireland and Germany respectively. The GHI is a composite index that is based on four indicators: Undernourishment, Child stunting, Child wasting, Child mortality.</p>",
                    solution_hi: "<p>35.(a) <strong>अफगानिस्तान। वैश्विक भुखमरी सूचकांक (GHI)</strong> एक उपकरण है जो विश्व स्तर के साथ-साथ क्षेत्र और देश के अनुसार भूख को मापने और ट्रैक करने का प्रयास करता है। इसकी गणना प्रतिवर्ष की जाती है और इसे क्रमशः आयरलैंड और जर्मनी के असरकारी संगठनों, कंसर्न वर्ल्डवाइड और वेल्थुंगरहिल्फे द्वारा प्रकाशित किया जाता है। GHI एक समग्र सूचकांक है जो चार संकेतकों पर आधारित है: अल्पपोषण, चाइल्ड स्टंटिंग, चाइल्ड वेस्टिंग, शिशु मृत्यु दर।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. If y = 26, then what is the value of y<math display=\"inline\"><msqrt><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math>?</p>",
                    question_hi: "<p>36. यदि y = 26, तो y<math display=\"inline\"><msqrt><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> का मान ज्ञात कीजिये ?</p>",
                    options_en: ["<p>3280</p>", "<p>3380</p>", 
                                "<p>3580</p>", "<p>3480</p>"],
                    options_hi: ["<p>3280</p>", "<p>3380</p>",
                                "<p>3580</p>", "<p>3480</p>"],
                    solution_en: "<p>36.(b)<br>y<math display=\"inline\"><msqrt><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>=y<math display=\"inline\"><msqrt><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>(</mo><mi>y</mi><mo>-</mo><mn>1</mn><mo>)</mo></msqrt></math><br>= y<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi>y</mi><mo>-</mo><mn>1</mn><mo>)</mo></msqrt></math><br>Put y = 26<br>= 26<sup>2</sup>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>26</mn><mo>-</mo><mn>1</mn></msqrt></math>)<br>= 676 &times; 5 = 3380</p>",
                    solution_hi: "<p>36.(b)<br>y<math display=\"inline\"><msqrt><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>=y<math display=\"inline\"><msqrt><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>(</mo><mi>y</mi><mo>-</mo><mn>1</mn><mo>)</mo></msqrt></math><br>= y<sup>2</sup><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi>y</mi><mo>-</mo><mn>1</mn><mo>)</mo></msqrt></math><br>y = 26 रखने पर <br>= 26<sup>2</sup>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>26</mn><mo>-</mo><mn>1</mn></msqrt></math>)<br>= 676 &times; 5 = 3380</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. What is the full form of PPLO with respect to bacteria ?</p>",
                    question_hi: "<p>37. बैक्टीरिया के संबंध में PPLO का पूर्ण रूप क्या है ?</p>",
                    options_en: ["<p>Pleuro Platelet Like Organisms</p>", "<p>Platelet Prone Like Organisms</p>", 
                                "<p>Pleuro Pneumonia Like Organisms</p>", "<p>Pneumonia Platelet Like Organisms</p>"],
                    options_hi: ["<p>Pleuro Platelet Like Organisms (प्लुरो प्लेटलेट लाइक ऑर्गेनिज़्म)</p>", "<p>Platelet Prone Like Organisms (प्लेटलेट प्रोन लाइक ऑर्गेनिज़्म)</p>",
                                "<p>Pleuro Pneumonia Like Organisms (प्लुरो निमोनिया लाइक ऑर्गेनिज़्म)</p>", "<p>Pneumonia Platelet Like Organisms (निमोनिया प्लेटलेट लाइक ऑर्गेनिज़्म)</p>"],
                    solution_en: "<p>37.(c) <strong>Pleuro Pneumonia Like Organisms (PPLO) </strong>are a group of bacteria that belong to the class Mollicutes, which are characterised by their small size, lack of a cell wall, and ability to survive without oxygen. The prokaryotic cells are represented by bacteria, blue-green algae, mycoplasma and PPLO.</p>",
                    solution_hi: "<p>37.(c) <strong>प्लुरो निमोनिया लाइक ऑर्गेनिज़्म (PPLO)</strong> जीवाणु का एक समूह है जो मोलिक्यूट्स वर्ग से संबंधित है, जो कि उनके सूक्ष्म आकार, कोशिका दीवार की कमी और ऑक्सीजन के बिना जीवित रहने की क्षमता की विशेषता है। प्रोकैरियोटिक कोशिकाओं का प्रतिनिधित्व जीवाणु, नीले-हरे शैवाल, माइकोप्लाज्मा और PPLO द्वारा किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. The speeds of three cars are in the ratio of 5 : 6 : 10. The time taken by each of them to travel the same distance is in the ratio of:</p>",
                    question_hi: "<p>38. तीन कारों की गति 5 : 6 : 10 के अनुपात में है। उनमें से प्रत्येक द्वारा समान दूरी तय करने में लिया गया समय अनुपात कितना है?</p>",
                    options_en: ["<p>6 : 5 : 10</p>", "<p>10 : 6 : 5</p>", 
                                "<p>10 : 5 : 6</p>", "<p>12 : 10 : 6</p>"],
                    options_hi: ["<p>6 : 5 : 10</p>", "<p>10 : 6 : 5</p>",
                                "<p>10 : 5 : 6</p>", "<p>12 : 10 : 6</p>"],
                    solution_en: "<p>38.(d) The speeds of three cars are in the ratio = 5 : 6 : 10. <br>Total distance = LCM of ( 5 , 6 ,10 ) = 30<br>So that , Ratio of the distance = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> = 12 : 10 : 6</p>",
                    solution_hi: "<p>38.(d) तीन कारों की गति का अनुपात = 5: 6: 10 <br>कुल दूरी = LCM ( 5 , 6 ,10 ) = 30<br>अत: दूरी का अनुपात = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> = 12 : 10 : 6</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. Select the assumption that can be drawn from the given statement.<br>&lsquo;Doctors who charge high consultation fees are good&rsquo;.</p>",
                    question_hi: "<p>39. उस धारणा का चयन करें जिसे दिए गए कथन से प्राप्त की जा सकती है।<br>\'उच्च परामर्श शुल्क लेने वाले डॉक्टर अच्छे हैं\'।</p>",
                    options_en: ["<p>The doctor is a good practitioner</p>", "<p>A doctor who charges less consultation fee is unpopular</p>", 
                                "<p>A doctor\'s proﬁciency is directly related to consultation fees.</p>", "<p>The doctor has many patients.</p>"],
                    options_hi: ["<p>डॉक्टर एक अच्छा व्यवसायी है।</p>", "<p>एक डॉक्टर जो कम परामर्श शुल्क लेता है, वह अलोकप्रिय है।</p>",
                                "<p>एक डॉक्टर की दक्षता सीधे परामर्श शुल्क से संबंधित है।</p>", "<p>डॉक्टर के पास कई मरीज हैं।</p>"],
                    solution_en: "<p>39.(c) From the given statement, it is clear that a doctor\'s proﬁciency is directly related to consultation fees.</p>",
                    solution_hi: "<p>39.(c) दिए गए कथन से यह स्पष्ट है कि एक डॉक्टर की दक्षता सीधे परामर्श शुल्क से संबंधित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. John is twice as old as Jean. After 3 years, the sum of the ages will be 66 years. What are the present ages of Jean and John, respectively?</p>",
                    question_hi: "<p>40. जॉन की आयु जीन से दोगुनी है। 3 वर्ष बाद, आयु का योग 66 वर्ष होगा। जीन और जॉन की वर्तमान आयु क्रमशः क्या है?</p>",
                    options_en: ["<p>40 and 20 years</p>", "<p>42 and 84 years</p>", 
                                "<p>20 and 40 years</p>", "<p>24 and 48 years</p>"],
                    options_hi: ["<p>40 और 20 साल</p>", "<p>42 और 84 वर्ष</p>",
                                "<p>20 और 40 साल</p>", "<p>24 और 48 वर्ष</p>"],
                    solution_en: "<p>40.(c)<br>Let the age of Jean = x years <br>Age of John = 2x<br>A/Q, <br>x + 3 + 2x + 3 = 66<br>&rArr; 3x + 6 = 66<br>&rArr; 3x = 60<br>&rArr; x = 20<br>Age of Jean = 20 years<br>Age of John = 40 years<br><strong>Short Tricks:-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133466.png\" alt=\"rId13\" width=\"209\" height=\"56\"><br>3 unit = 60 yrs <br>Then , Age of Jean ( 1 unit) = 20 years<br>Age of John (2 units) = 40 years</p>",
                    solution_hi: "<p>40.(c)<br>माना जीन की आयु = x वर्ष<br>जॉन की आयु = 2x<br>प्रश्नाअनुसार, <br>x + 3 + 2x + 3 = 66<br>&rArr; 3x + 6 = 66<br>&rArr; 3x = 60<br>&rArr; x = 20<br>जीन की आयु = 20 वर्ष<br>जॉन की आयु = 40 वर्ष<br><strong>Short Tricks:-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133466.png\" alt=\"rId13\" width=\"209\" height=\"56\"><br>3 unit = 60 yrs <br>तब, जीन की आयु (1 इकाई) = 20 वर्ष<br>जॉन की आयु (2 इकाई) = 40 वर्ष</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. If it was Monday on 12 July 1965, then which day of the week was it on 14 August 1966?</p>",
                    question_hi: "<p>41. यदि 12 जुलाई 1965 को सोमवार था, तो 14 अगस्त 1966 को सप्ताह का कौन सा दिन था?</p>",
                    options_en: ["<p>Tuesday</p>", "<p>Sunday</p>", 
                                "<p>Wednesday</p>", "<p>Monday</p>"],
                    options_hi: ["<p>मंगलवार</p>", "<p>रविवार</p>",
                                "<p>बुधवार</p>", "<p>सोमवार</p>"],
                    solution_en: "<p>41.(b)<br>Total number of day between 12 July 1965 and 14 August 1966<br>= 365 + 19 + 14 = 398 days<br>Total remaining odd days = <math display=\"inline\"><mfrac><mrow><mn>398</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 6<br>? = Monday + 6 = Sunday</p>",
                    solution_hi: "<p>41.(b)<br>12 जुलाई 1965 और 14 अगस्त 1966 के बीच दिनों की कुल संख्या<br>= 365 + 19 + 14 = 398 दिन <br>कुल शेष विषम दिन = <math display=\"inline\"><mfrac><mrow><mn>398</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 6<br>? = सोमवार + 6 =<strong> रविवार</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. Sao Joao festival is celebrated primarily by the Catholic Christians in which of the following Indian states ?</p>",
                    question_hi: "<p>42. साओ जोआओ त्योहार (Sao Joao festival) मुख्य रूप से निम्नलिखित में से किस भारतीय राज्य में कैथोलिक ईसाइयों द्वारा मनाया जाता है ?</p>",
                    options_en: ["<p>Goa</p>", "<p>Assam</p>", 
                                "<p>Manipur</p>", "<p>Nagaland</p>"],
                    options_hi: ["<p>गोवा</p>", "<p>असम</p>",
                                "<p>मणिपुर</p>", "<p>नागालैंड</p>"],
                    solution_en: "<p>42.(a) <strong>Goa.</strong> During the Sao Joao festivities, Goans present fruits to each other. It is dedicated to St. John the Baptist which is celebrated on 24th June. <strong>Other Festivals</strong> : Carnival festival, Shigmo Festival, Feast of St. Francis Xavier, Mando Festival, and Three Kings Feast.</p>",
                    solution_hi: "<p>42.(a) <strong>गोवा।</strong> साओ जोआओ त्योहार के दौरान, गोवावासी एक-दूसरे को फल भेंट करते हैं। यह सेंट जॉन द बैपटिस्ट को समर्पित है जो 24 जून को मनाया जाता है। <strong>अन्य त्यौहार:</strong> कार्निवल त्योहार, शिग्मो त्योहार, सेंट फ्रांसिस जेवियर का पर्व, मांडो त्योहार, और तीन राजाओं का पर्व।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. From 10 litres of a solution 2 litres of water evaporated. The remaining solution was found to have 6% salt. What was the percentage of salt in the original solution?</p>",
                    question_hi: "<p>43. दस लीटर विलयन से 2 लीटर पानी वाष्पित हो गया। शेष विलयन में 6% नमक पाया गया। वास्तविक विलयन में नमक का प्रतिशत कितना था?</p>",
                    options_en: ["<p>5.4%</p>", "<p>5.6%</p>", 
                                "<p>5%</p>", "<p>4.8%</p>"],
                    options_hi: ["<p>5.4%</p>", "<p>5.6%</p>",
                                "<p>5%</p>", "<p>4.8%</p>"],
                    solution_en: "<p>43.(d) Let there is x litre of salt in the initial solution of 10 litres <br>From this solution 2 litres of water evaporated, so the new amount of the solution = (10 - 2) = 8<br>The remaining solution was found to have 6% salt = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = 6<br>&rArr; 100x = 48 <br>&rArr; x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>100</mn></mfrac></math> = 0.48<br>So, the percentage of salt in the original solution = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>48</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = 4.8%</p>",
                    solution_hi: "<p>43.(d) माना 10 लीटर पानी के प्रारंभिक घोल में x लीटर नमक है <br>इस 10 लीटर घोल से 2 लीटर पानी वाष्पित हो गया, इसलिए, घोल की नई मात्रा = 8<br>तो, शेष घोल में 6% नमक पाया गया, = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = 6<br>&rArr; 100x = 48 <br>&rArr; x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>100</mn></mfrac></math> = 0.48<br>अतः मूल विलयन में नमक का प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>48</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = 4.8%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. Methyl ethyl ketone is also known as:</p>",
                    question_hi: "<p>44. मेथिल एथिल कीटोन (Methyl ethyl ketone) को ________ के नाम से भी जाना जाता है।</p>",
                    options_en: ["<p>methyl pentanone</p>", "<p>2-butanol</p>", 
                                "<p>2-butanone</p>", "<p>propanone</p>"],
                    options_hi: ["<p>मेथिल पेंटानोन</p>", "<p>2-ब्यूटेनॉल</p>",
                                "<p>2-ब्यूटेनोन</p>", "<p>प्रोपेनोन</p>"],
                    solution_en: "<p>44.(c) <strong>2-butanone</strong>. It is a colourless liquid with a sharp, sweet odour. It is a common solvent and is used in a variety of industrial and commercial applications. <strong>Chemical Names of Common Compounds </strong>- Blue vitriol (Copper sulphate), Battery acid / oil of vitriol (Sulfuric acid), Baking soda (Sodium bicarbonate), Bleaching powder (Calcium hypochlorite), Borax (Sodium tetraborate), Dry ice (Carbon dioxide), Gypsum (Calcium sulphate dihydrate).</p>",
                    solution_hi: "<p>44.(c) <strong>2-ब्यूटेनोन</strong>। यह तीक्ष्ण, मीठी गंध वाला रंगहीन द्रव है। यह एक सामान्य विलायक है और इसका उपयोग विभिन्न औद्योगिक और वाणिज्यिक अनुप्रयोगों में किया जाता है। <strong>सामान्य यौगिकों के रासायनिक नाम</strong> - ब्लू विट्रियल (कॉपर सल्फेट), बैटरी अम्ल / विट्रियल का तेल (सल्फ्यूरिक अम्ल), बेकिंग सोडा (सोडियम बाइकार्बोनेट), ब्लीचिंग पाउडर (कैल्शियम हाइपोक्लोराइट), बोरेक्स (सोडियम टेट्राबोरेट), शुष्क बर्फ (कार्बन डाइऑक्साइड) , जिप्सम (कैल्शियम सल्फेट डाइहाइड्रेट)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br>Statements:<br>1. All the cars are four-wheelers.<br>2. All the four-wheelers are vehicles.<br>Conclusions:<br>I All the vehicles are four wheelers.<br>II. All the cars are vehicles.</p>",
                    question_hi: "<p>45. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है। भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होता हो। बताइये कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>1. सभी कार चौपहिया हैं।<br>2. सभी चौपहिया वाहन हैं।<br><strong>निष्कर्ष:</strong><br>I सभी वाहन चौपहिया हैं।<br>II.सभी कार वाहन हैं।</p>",
                    options_en: ["<p>Only I</p>", "<p>Both I and II</p>", 
                                "<p>Only II</p>", "<p>Either I or II</p>"],
                    options_hi: ["<p>केवल I</p>", "<p>I और II दोनों</p>",
                                "<p>केवल II</p>", "<p>या तो I अथवा II</p>"],
                    solution_en: "<p>45.(c) From the given statements we can draw the following venn diagram<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133567.png\" alt=\"rId14\" width=\"138\" height=\"91\"><br>So, from the diagram we can say that only conclusion II follows.</p>",
                    solution_hi: "<p>45.(c) दिए गए कथनों से हम निम्नलिखित वेन आरेख बना सकते हैं<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133666.png\" alt=\"rId15\" width=\"156\" height=\"103\"><br>अत: आरेख से हम कह सकते हैं कि केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. How many controls are there globally that influence the climate of a particular zone ?</p>",
                    question_hi: "<p>46. विश्व स्तर पर कितने नियंत्रण हैं, जो किसी विशेष क्षेत्र की जलवायु को प्रभावित करते हैं ?</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>5</p>", "<p>3</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>5</p>", "<p>3</p>"],
                    solution_en: "<p>46.(a) <strong>6</strong>. There are six major controls of the climate of any place. They are: latitude, altitude, pressure and wind system, distance from the sea (continentality), ocean currents and relief features. Climate is the long-term average weather conditions in a place. Weather refers to the state of the atmosphere over an area at any point of time.</p>",
                    solution_hi: "<p>46.(a) <strong>6</strong>। किसी भी स्थान की जलवायु के छह प्रमुख नियंत्रण होते हैं। वे हैं: अक्षांश, ऊंचाई, दाब और पवन प्रणाली, समुद्र से दूरी (महाद्वीपीयता), समुद्री धाराएं और राहत विशेषताएं। जलवायु किसी स्थान पर दीर्घकालिक औसत मौसम की स्थिति है। मौसम किसी भी समय किसी क्षेत्र के वायुमंडल की स्थिति को दर्शाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. Shweta and Harish completed a project with an income of Rs 28,000. In this project Shweta worked for 20 days and Harish worked for 30 days. If their daily wages are in the ratio of 5 : 6, then Shweta&rsquo;s share is:</p>",
                    question_hi: "<p>47. श्वेता और हरीश ने 28,000 रुपये की आय के साथ एक परियोजना पूरी की। इस प्रोजेक्ट में श्वेता ने 20 दिन और हरीश ने 30 दिनों तक काम किया। यदि उनकी दैनिक मजदूरी 5:6 के अनुपात में है, तो श्वेता का हिस्सा कितना है ?</p>",
                    options_en: ["<p>Rs. 10,000</p>", "<p>Rs. 12,000</p>", 
                                "<p>Rs. 18,000</p>", "<p>Rs. 16,000</p>"],
                    options_hi: ["<p>Rs. 10,000</p>", "<p>Rs. 12,000</p>",
                                "<p>Rs. 18,000</p>", "<p>Rs. 16,000</p>"],
                    solution_en: "<p>47.(a) In the project Shweta worked for 20 days and Harish worked for 30 days and their daily wages are in the ratio of 5 : 6,<br>Then Shweta&rsquo;s share : Harish&rsquo;s share = 5 &times; 20 : 6 &times; 30 = 5 : 9 <br>As Shweta and Harish completed the project with an income of Rs 28,000 <br>So, from the total income, Shweta&rsquo;s share is = 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>14</mn></mfrac></math> = 10,000Rs.</p>",
                    solution_hi: "<p>47.(a) परियोजना में श्वेता ने 20 दिनों के लिए काम किया और हरीश ने 30 दिनों के लिए काम किया और उनकी दैनिक मजदूरी 5: 6 के अनुपात में है,<br>तो श्वेता का हिस्सा : हरीश का हिस्सा = 5 &times; 20 : 6 &times; 30 = 5 : 9 <br>जैसा कि श्वेता और हरीश ने 28,000 रुपये की आय के साथ परियोजना को पूरा किया।<br>तो, कुल आय में से श्वेता का हिस्सा है = 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>14</mn></mfrac></math> = 10,000 रुपये</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. Six students P, Q, R, S, T and U each are of different heights. P is taller than only two students. T is taller than only one student but shorter than exactly four students. S is taller than only four students but is not the tallest. Q is taller than none of the students. R is not the tallest. Who is the tallest student?</p>",
                    question_hi: "<p>48. छह छात्र P, Q, R, S, T और U प्रत्येक की लंबाई अलग-अलग है। P केवल दो विद्यार्थियों से लम्बा है। T केवल एक विद्यार्थी से लम्बा है लेकिन ठीक चार विद्यार्थियों से छोटा है। S केवल चार विद्यार्थियों से लम्बा है लेकिन सबसे लम्बा नहीं है। Q किसी भी विद्यार्थी से लम्बा नहीं है। R सबसे लम्बा नहीं है। सबसे लम्बा विद्यार्थी कौन है?</p>",
                    options_en: ["<p>P</p>", "<p>U</p>", 
                                "<p>S</p>", "<p>T</p>"],
                    options_hi: ["<p>P</p>", "<p>U</p>",
                                "<p>S</p>", "<p>T</p>"],
                    solution_en: "<p>48.(b)<br>Possible arrangement will be : <strong>U ＞S＞R＞P＞T＞Q</strong><br>Therefore, we can see that, U is the tallest.</p>",
                    solution_hi: "<p>48.(b)<br>संभावित क्रम होगा : <strong>U ＞S＞R＞P＞T＞Q</strong><br>इसलिए, हम देख सकते हैं कि U सबसे लंबा है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. Moatsu festival of Nagaland is celebrated by which of the following tribes ?</p>",
                    question_hi: "<p>49. नागालैंड का मोत्सु त्यौहार (Moatsu festival) निम्नलिखित में से किस जनजाति द्वारा मनाया जाता है ?</p>",
                    options_en: ["<p>Bodo tribe</p>", "<p>Munda tribe</p>", 
                                "<p>Bhil tribe</p>", "<p>Ao tribe</p>"],
                    options_hi: ["<p>बोडो जनजाति (Bodo tribe)</p>", "<p>मुण्डा जनजाति (Munda tribe)</p>",
                                "<p>भील जनजाति (Bhil tribe)</p>", "<p>आओ जनजाति (Ao tribe)</p>"],
                    solution_en: "<p>49.(d) <strong>Ao tribe</strong>. Moatsu Festival: Celebrated in the first week of May every year for 3 days. During this festival one of the symbolic celebrations is Sangpangtu, where a big fire is lit and men and women sit around it putting on their complete best attire. Other Tribal festivals of Nagaland - Hornbill Festival (Naga tribe), Sekrenyi (Angami tribe), and Aoleang (Konyak Nagas).</p>",
                    solution_hi: "<p>49.(d) <strong>आओ जनजाति (Ao tribe)</strong>। मोत्सु त्योहार: प्रत्येक मई के पहले सप्ताह में 3 दिनों के लिए मनाया जाता है। इस त्योहार के दौरान प्रतीकात्मक त्योहार में से एक संगपंगटू है, जहां एक बड़ी आग जलाई जाती है और पुरुष और महिलाएं अपनी पूरी पोशाक पहनकर उसके चारों ओर बैठते हैं। नागालैंड के अन्य जनजातीय त्योहार - हॉर्नबिल त्योहार (नागा जनजाति), सेक्रेनी (अंगामी जनजाति), और आओलंग (कोन्याक नागा)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. Each of A, B, C, D and E has an exam on a different day of a week, starting from Monday and ending on Friday of the same week. A has the exam on Thursday. Only one person has the exam between D and E. D has the exam on the day immediately before B. Who has the exam on Friday.?</p>",
                    question_hi: "<p>50. A, B, C, D और E में से प्रत्येक की परीक्षा, सोमवार से शुरू होकर शुक्रवार को समाप्त होने वाले एक सप्ताह के अलग-अलग दिन होती है। A की परीक्षा गुरुबार को है। D और E के बीच केवल एक व्यक्ति की परीक्षा है। D की परीक्षा, B की परीक्षा से ठीक एक दिन पहले है। शुक्रवार को किसकी परीक्षा है?</p>",
                    options_en: ["<p>C</p>", "<p>E</p>", 
                                "<p>D</p>", "<p>B</p>"],
                    options_hi: ["<p>C</p>", "<p>E</p>",
                                "<p>D</p>", "<p>B</p>"],
                    solution_en: "<p>50.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133813.png\" alt=\"rId16\" width=\"158\" height=\"117\"><br>From the above table we can clearly see that,<br><strong>C</strong> has the exam on<strong> Friday.</strong></p>",
                    solution_hi: "<p>50.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290133922.png\" alt=\"rId17\" width=\"137\" height=\"115\"><br>उपरोक्त तालिका से हम स्पष्ट रूप से देख सकते हैं कि,<br><strong>शुक्रवार</strong> को <strong>C</strong> की परीक्षा है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. Which part of the Indian Constitution deals with fundamental rights ?</p>",
                    question_hi: "<p>51. भारतीय संविधान का कौन सा भाग मौलिक अधिकारों से संबंधित है ?</p>",
                    options_en: ["<p>Part IV</p>", "<p>Part II</p>", 
                                "<p>Part III</p>", "<p>Part V</p>"],
                    options_hi: ["<p>भाग IV</p>", "<p>भाग II</p>",
                                "<p>भाग III</p>", "<p>भाग V</p>"],
                    solution_en: "<p>51.(c) <strong>Part III.</strong> Articles 12 to 35 of the Constitution guarantee six fundamental rights to all citizens of India. Right to equality (Article 14- Article18), Right to freedom (Article 19- Article 22), Right against exploitation (Article 23 - Article 24), Right to freedom of religion (Article 25 - Article 28), Cultural and educational rights (Article 29- Article 30), Right to constitutional remedies (Article 32 - Article 35).</p>",
                    solution_hi: "<p>51.(c) <strong>भाग III.</strong> संविधान के अनुच्छेद 12 से 35 भारत के सभी नागरिकों को छह मौलिक अधिकारों की गारंटी देते हैं। समानता का अधिकार (अनुच्छेद 14-अनुच्छेद18), स्वतंत्रता का अधिकार (अनुच्छेद 19-अनुच्छेद 22), शोषण के विरुद्ध अधिकार (अनुच्छेद 23 - अनुच्छेद 24), धार्मिक स्वतंत्रता का अधिकार (अनुच्छेद 25 - अनुच्छेद 28), सांस्कृतिक और शैक्षिक अधिकार ( अनुच्छेद 29-अनुच्छेद 30), संवैधानिक उपचारों का अधिकार (अनुच्छेद 32-अनुच्छेद 35)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. Study the following digit-letter-symbol series carefully and answer the questions that follow. <br>Np4@8cQ9%6TkF3=5g4&amp;RwJX <br>How many such numbers are there in the sequence, each of which is immediately preceded by a letter and immediately followed by a symbol?</p>",
                    question_hi: "<p>52. निम्नलिखित अंक-अक्षर-प्रतीक श्रृंखला का ध्यानपूर्वक अध्ययन कीजिये और उसके बाद आने वाले प्रश्नों के उत्तर दें<br>Np4@8cQ9%6TkF3=5g4&amp;RwJX <br>श्रृंखला में ऐसी कितनी संख्याएँ हैं, जिनमें से प्रत्येक के ठीक पहले एक अक्षर और ठीक बाद एक प्रतीक है?</p>",
                    options_en: ["<p>4</p>", "<p>6</p>", 
                                "<p>7</p>", "<p>5</p>"],
                    options_hi: ["<p>4</p>", "<p>6</p>",
                                "<p>7</p>", "<p>5</p>"],
                    solution_en: "<p>52.(a) N<span style=\"text-decoration: underline;\">p4@</span>8c<span style=\"text-decoration: underline;\">Q9%</span>6Tk<span style=\"text-decoration: underline;\">F3=</span>5<span style=\"text-decoration: underline;\">g4&amp;</span>RwJX<br>4 numbers are there in the sequence, each of which is immediately preceded by a letter and immediately followed by a symbol.</p>",
                    solution_hi: "<p>52.(a) N<span style=\"text-decoration: underline;\">p4@</span>8c<span style=\"text-decoration: underline;\">Q9%</span>6Tk<span style=\"text-decoration: underline;\">F3=</span>5<span style=\"text-decoration: underline;\">g4&amp;</span>RwJX<br>क्रम में 4 संख्याएँ हैं, जिनमें से प्रत्येक के ठीक पहले एक अक्षर और ठीक बाद एक प्रतीक है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. Tap A can fill a tank in 40 min and Tap B can empty the tank in 60 min. If the taps are opened at the same time, then the time taken to fill the tank will be.</p>",
                    question_hi: "<p>53. नल A एक टंकी को 40 मिनट में भर सकता है और नल B टंकी को 60 मिनट में खाली कर सकता है। यदि उसी समय नलों को खोल दिया जाए, तो टंकी को भरने में लगने वाला समय कितना होगा ?</p>",
                    options_en: ["<p>120 min</p>", "<p>100 min</p>", 
                                "<p>125 min</p>", "<p>150 min</p>"],
                    options_hi: ["<p>120 मिनट</p>", "<p>100 मिनट</p>",
                                "<p>125 मिनट</p>", "<p>150 मिनट</p>"],
                    solution_en: "<p>53.(a)<br>&nbsp; &nbsp; &nbsp; &nbsp;Time&nbsp; &nbsp; &nbsp; &nbsp;T/W&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Efficiency<br>A -&nbsp; &nbsp; &nbsp;40&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;120<br>B -&nbsp; &nbsp;(60)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (-2)<br>Total work = 120<br>When both taps are open in same time <br>Then , time taken = <math display=\"inline\"><mfrac><mrow><mi>T</mi><mo>/</mo><mi>W</mi></mrow><mrow><mi>e</mi><mi>f</mi><mi>f</mi><mi>i</mi><mi>c</mi><mi>i</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mrow><mo>(</mo><mn>3</mn><mo>-</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math>= 120 min.</p>",
                    solution_hi: "<p>53.(a)&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp;समय&nbsp; &nbsp; &nbsp; &nbsp;कुल कार्य&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;क्षमता <br>A -&nbsp; &nbsp; &nbsp;40&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 120<br>B -&nbsp; &nbsp; (60)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (-2)<br>कुल कार्य = 120<br>जब दोनों नल एक ही समय में खुले हों<br>फिर, लिया गया समय = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2325;&#2366;&#2352;&#2381;&#2351;</mi></mrow><mi>&#2325;&#2381;&#2359;&#2350;&#2340;&#2366;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mrow><mo>(</mo><mn>3</mn><mo>-</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math> = 120 min.</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. Who among the following has given extraordinary powers during an emergency ?</p>",
                    question_hi: "<p>54. निम्न में से किसे आपातकाल के दौरान असाधारण शक्तियाँ प्रदान की गई हैं ?</p>",
                    options_en: ["<p>Prime Minister</p>", "<p>The President</p>", 
                                "<p>The Chief Justice</p>", "<p>The Governor</p>"],
                    options_hi: ["<p>प्रधान मंत्री</p>", "<p>राष्ट्रपति</p>",
                                "<p>मुख्य न्यायाधीश</p>", "<p>राज्यपाल</p>"],
                    solution_en: "<p>54.(b) <strong>The President</strong>. The President of India is empowered to proclaim three types of emergencies to handle extraordinary situations in the country. National Emergency (Article 352), State Emergency or President rule (Article 356), Financial Emergency (Article 360). The President can proclaim National Emergency only after receiving a written recommendation from the cabinet.</p>",
                    solution_hi: "<p>54.(b) <strong>राष्ट्रपति।</strong> भारत के राष्ट्रपति को देश में असाधारण स्थितियों को संभालने के लिए तीन प्रकार की आपात स्थितियों की घोषणा करने का अधिकार है। राष्ट्रीय आपातकाल (अनुच्छेद 352), राज्य आपातकाल या राष्ट्रपति शासन (अनुच्छेद 356), वित्तीय आपातकाल (अनुच्छेद 360)। कैबिनेट से लिखित अनुशंसा प्राप्त होने के बाद ही राष्ट्रपति राष्ट्रीय आपातकाल की घोषणा कर सकते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. A statement is given followed by two arguments. Decide which of the arguments is/are strong with respect to the statement.<br><strong>Statement:</strong><br>Life expectancy of Indians is increasing.<br><strong>Arguments:</strong><br>I. Yes. People are getting better medical facilities.<br>II. Yes. People are doing more physical exercises now.</p>",
                    question_hi: "<p>55. एक कथन के बाद दो तर्क दिए गए हैं। बताइये कि कथन के संबंध में कौन सा तर्क अनुसरण करता हैं।<br><strong>कथन:</strong><br>भारतीयों की जीवन प्रत्याशा बढ़ रही है।<br><strong>तर्क:</strong><br>I. हां, लोगों को बेहतर चिकित्सा सुविधा मिल रही है।<br>II. हां, लोग अब अधिक शारीरिक व्यायाम कर रहे हैं।</p>",
                    options_en: ["<p>Neither I nor II is strong</p>", "<p>Both arguments I and II are strong</p>", 
                                "<p>Only argument II is strong.</p>", "<p>Only argument I is strong.</p>"],
                    options_hi: ["<p>न तो I और न ही II अनुसरण करते है</p>", "<p>तर्क I और II दोनों अनुसरण करते हैं।</p>",
                                "<p>केवल तर्क II अनुसरण करता है।</p>", "<p>केवल तर्क I अनुसरण करता है।</p>"],
                    solution_en: "<p>55.(b) Here both the conclusions i.e. medical facilities and physical exercises directly influence the life expectancy of a person. So, both arguments I and II are strong.</p>",
                    solution_hi: "<p>55.(b) यहाँ दोनों निष्कर्ष, अर्थात चिकित्सा सुविधाएं और शारीरिक व्यायाम, एक व्यक्ति की जीवन प्रत्याशा को सीधे प्रभावित करते हैं। अत: तर्क I और II दोनों प्रबल हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. The First Commonwealth Games were organised in which of the following years ?</p>",
                    question_hi: "<p>56. प्रथम राष्ट्रमंडल खेलों का आयोजन निम्नलिखित में से किस वर्ष किया गया था ?</p>",
                    options_en: ["<p>1926</p>", "<p>1930</p>", 
                                "<p>1920</p>", "<p>1922</p>"],
                    options_hi: ["<p>1926</p>", "<p>1930</p>",
                                "<p>1920</p>", "<p>1922</p>"],
                    solution_en: "<p>56.(b) <strong>1930. Commonwealth Games-</strong> 1st Event (1930) - Host city - Hamilton, Canada. Nations - 11. XXII<sup>th</sup> Event (In 2022) - Host city - Birmingham, England. Moto - Games for Everyone. Nations - 72. Rank - 1st- Australia (67 Gold), 2nd - England (58 Gold), 4th - India (22 Gold). XXIII<sup>th</sup> Event - Host- Victoria, Australia.</p>",
                    solution_hi: "<p>56.(b) <strong>1930। राष्ट्रमंडल खेल</strong>- प्रथम (1930) - मेजबान शहर - हैमिल्टन, कनाडा। राष्ट्र - 11. XXIIवां संस्करण (2022 में) - मेजबान शहर - बर्मिंघम, इंग्लैंड। मोटो -गेम ऑफ एव्रीवन। राष्ट्र - 72. रैंक - पहला - ऑस्ट्रेलिया (67 गोल्ड), दूसरा - इंग्लैंड (58 गोल्ड), चौथा - भारत (22 गोल्ड)। तेईसवां आयोजन - मेजबान- विक्टोरिया, ऑस्ट्रेलिया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57. A student takes 1.5 hours from home to school at a speed of 5 km/h. By what percent should he increase his speed to reduce the time by 20% and cover the same distance from school to home?</p>",
                    question_hi: "<p>57. एक छात्र 5 km/h की गति से घर से विद्यालय तक आने में 1.5 घंटे का समय लेता है। समय को 20% कम करने और विद्यालय से घर तक समान दूरी तय करने के लिए उसे अपनी गति कितने प्रतिशत बढ़ानी चाहिए?</p>",
                    options_en: ["<p>25%</p>", "<p>16%</p>", 
                                "<p>20%</p>", "<p>15%</p>"],
                    options_hi: ["<p>25%</p>", "<p>16%</p>",
                                "<p>20%</p>", "<p>15%</p>"],
                    solution_en: "<p>57.(a) D = S &times;&nbsp;T<br>Distance in both cases are same and it depends on multiplication of speed and time. <br>20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> <br>time reduced by 20% means that time changes from 5 to 4,<br>So, speed must be increased from 4 to 5,<br>% increase in speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>57.(a) दूरी = गति &times; समय<br>दोनों स्थितियों में दूरी समान है और यह गति और समय के गुणन पर निर्भर करती है,<br>20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> <br>समय 20% कम होने का मतलब है कि समय 5 से 4 में बदल जाता है,<br>इसलिए स्पीड 4 से बढ़ाकर 5 करनी होगी,<br>गति में % वृद्धि= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; 100 = 25%</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134003.png\" alt=\"rId18\" width=\"258\" height=\"53\"></p>",
                    question_hi: "<p>58. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134003.png\" alt=\"rId18\" width=\"238\" height=\"49\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134100.png\" alt=\"rId19\" width=\"70\" height=\"71\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134190.png\" alt=\"rId20\" width=\"70\" height=\"71\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134296.png\" alt=\"rId21\" width=\"70\" height=\"71\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134396.png\" alt=\"rId22\" width=\"71\" height=\"72\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134100.png\" alt=\"rId19\" width=\"71\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134190.png\" alt=\"rId20\" width=\"70\" height=\"71\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134296.png\" alt=\"rId21\" width=\"71\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134396.png\" alt=\"rId22\" width=\"71\" height=\"72\"></p>"],
                    solution_en: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134296.png\" alt=\"rId21\" width=\"70\" height=\"71\"></p>",
                    solution_hi: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134296.png\" alt=\"rId21\" width=\"70\" height=\"71\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. With which neighbouring country did India sign the Farakka Treaty in 1996 for sharing of the Ganga Waters and on augmenting its flows ?</p>",
                    question_hi: "<p>59. भारत ने किस पड़ोसी देश के साथ 1996 में गंगा जल को साझा करने (बँटवारे) और इसके प्रवाह को बढ़ाने के लिए फरक्का संधि पर हस्ताक्षर किए थे ?</p>",
                    options_en: ["<p>Afghanistan</p>", "<p>Bhutan</p>", 
                                "<p>Nepal</p>", "<p>Bangladesh</p>"],
                    options_hi: ["<p>अफगानिस्तान</p>", "<p>भूटान</p>",
                                "<p>नेपाल</p>", "<p>बांग्लादेश</p>"],
                    solution_en: "<p>59.(d) <strong>Bangladesh</strong>. Farakka water-sharing treaty - It was signed by then PM H. D. Deve Gowda (India) and PM Sheikh Hasina Wajed (Bangladesh). India&rsquo;s treaty with its Neighbours: Nepal Treaty - Mahakali treaty (1996), and Pakistan - Indus Waters Treaty (1960).</p>",
                    solution_hi: "<p>59.(d) <strong>बांग्लादेश</strong>। फरक्का जल-बंटवारा संधि - इस पर तत्कालीन प्रधानमंत्री एच. डी. देवेगौड़ा (भारत) और प्रधानमंत्री शेख हसीना वाजेद (बांग्लादेश) ने हस्ताक्षर किए थे। भारत की अपने पड़ोसियों के साथ संधि: नेपाल संधि - महाकाली संधि (1996), और पाकिस्तान - सिंधु जल संधि (1960)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. Select the option that is embedded in the given figure as its part (rotation is not allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134522.png\" alt=\"rId23\" width=\"113\" height=\"101\"></p>",
                    question_hi: "<p>60. उस विकल्प का चयन कीजिए जो दी गई आकृति में इसके भाग के रूप में अंतर्निहित है (आकृति को घूमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134522.png\" alt=\"rId23\" width=\"100\" height=\"89\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134611.png\" alt=\"rId24\" width=\"91\" height=\"117\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134729.png\" alt=\"rId25\" width=\"90\" height=\"94\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134831.png\" alt=\"rId26\" width=\"94\" height=\"103\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134920.png\" alt=\"rId27\" width=\"91\" height=\"87\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134611.png\" alt=\"rId24\" width=\"89\" height=\"115\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134729.png\" alt=\"rId25\" width=\"91\" height=\"95\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134831.png\" alt=\"rId26\" width=\"90\" height=\"99\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290134920.png\" alt=\"rId27\" width=\"92\" height=\"88\"></p>"],
                    solution_en: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135016.png\" alt=\"rId28\" width=\"96\" height=\"91\"></p>",
                    solution_hi: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135016.png\" alt=\"rId28\" width=\"96\" height=\"91\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Which of the following does NOT belong to the family of organic compounds ?</p>",
                    question_hi: "<p>61. निम्नलिखित में से कौन-सा कार्बनिक यौगिकों के परिवार से संबंधित नहीं है ?</p>",
                    options_en: ["<p>Furan</p>", "<p>Decane</p>", 
                                "<p>Ammonia</p>", "<p>Nonane</p>"],
                    options_hi: ["<p>फ्यूरान (Furan)</p>", "<p>डेकेन (Decane)</p>",
                                "<p>अमोनिया (Ammonia)</p>", "<p>नोनेन (Nonane)</p>"],
                    solution_en: "<p>61.(c) <strong>Ammonia (NH<sub>3</sub>)</strong> is an inorganic compound of nitrogen and Hydrogen. Organic compounds are a diverse group of chemicals that contain carbon. Furan is a heterocyclic compound with the chemical formula C<sub>4</sub>H<sub>4</sub>O.Decane is an alkane hydrocarbon with the chemical formula C<sub>10</sub>H<sub>22</sub>. Nonane is an alkane with the chemical formula C<sub>9</sub>H<sub>20</sub>.</p>",
                    solution_hi: "<p>61.(c) <strong>अमोनिया (NH<sub>3</sub>)</strong> नाइट्रोजन और हाइड्रोजन का एक अकार्बनिक यौगिक है। कार्बनिक यौगिक रसायनों का एक विविध समूह है जिसमें कार्बन होता है। फ्यूरान एक हेटरोसाइक्लिक यौगिक है जिसका रासायनिक सूत्र C<sub>4</sub>H<sub>4</sub>O है। डेकेन एक अल्केन हाइड्रोकार्बन है जिसका रासायनिक सूत्र C<sub>10</sub>H<sub>22</sub> है। नोनेन एक अल्केन है जिसका रासायनिक सूत्र C<sub>9</sub>H<sub>20</sub> है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. A steamer goes downstream and covers the distance between two ports in 6 h. It covers the same distance upstream in 8 h. If the speed of the stream is 3 km/h, then the speed of the streamer in still water is:</p>",
                    question_hi: "<p>62. एक स्टीमर धारा के अनुकूल जाता है और दो बंदरगाहों के बीच की दूरी को 6 घंटे में तय करता है। यह समान दूरी धारा के प्रतिकूल 8 घंटे में तय करती है। यदि धारा की गति 3 किमी/घंटा है, तो शांत जल में धारा की गति है:</p>",
                    options_en: ["<p>10 km/h</p>", "<p>21 km/h</p>", 
                                "<p>10.375 km/h</p>", "<p>21.5 km/h</p>"],
                    options_hi: ["<p>10 km/h</p>", "<p>21 km/h</p>",
                                "<p>10.375 km/h</p>", "<p>21.5 km/h</p>"],
                    solution_en: "<p>62.(b)<br>Let the speed of the streamer in still water is x km/h. And the distance between two points is D ;<br>Then D = 6 &times; (x + 3)&nbsp;<br>also D = 8 &times; (x - 3)&nbsp;<br>&rArr; 6 &times; (x + 3) = 8 &times; (x - 3)<br>&rArr; x =&nbsp; 21<br>Then the speed of the streamer in still water is 21 km/h.</p>",
                    solution_hi: "<p>62.(b)<br>माना शांत जल में स्टीमर की गति x km/h है। और दो बिंदुओं के बीच की दूरी D है;<br>D = 6 &times; (x + 3)&nbsp;<br>एवं D = 8 &times; (x - 3) ;<br>&rArr; 6 &times; (x + 3) = 8 &times; (x - 3)<br>&rArr; x =&nbsp; 21<br>तब स्थिर जल में स्टीमर की गति 21 km/h है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. Identify whether the given statements about p-block elements are correct or incorrect.<br><strong>Statement A: </strong>In p-block elements, the last electron enters the outermost p orbital.<br><strong>Statement B:</strong> The non-metals and metalloids exist only in the p-block of the periodic table.</p>",
                    question_hi: "<p>63. p-ब्लॉक के तत्वों के बारे में दिए गए सही या गलत कथनों की पहचान कीजिए। <br><strong>कथन A:</strong> p-ब्लॉक तत्वों में, अंतिम इलेक्ट्रॉन वाह्यतम p कक्षक में प्रवेश करता है। <br><strong>कथन B: </strong>अधातुएं और उपधातुएं केवल आवर्त सारणी के p-ब्लॉक में मौजूद होती हैं।</p>",
                    options_en: ["<p>Both Statements A and B are correct</p>", "<p>Only Statement A is correct</p>", 
                                "<p>Only Statement B is correct</p>", "<p>Both Statements A and B are incorrect</p>"],
                    options_hi: ["<p>कथन A और B दोनों सही हैं</p>", "<p>केवल कथन A सही है</p>",
                                "<p>केवल कथन B सही है</p>", "<p>कथन A और B दोनों गलत हैं</p>"],
                    solution_en: "<p>63.(a) There are six groups of p&ndash;block elements in the periodic table numbering from 13 to 18. Boron, carbon, nitrogen, oxygen, fluorine and helium head the groups. Their valence shell electronic configuration is ns<sup>2</sup> np<sup>1-6</sup> (except for He). The maximum oxidation state shown by a p-block element is equal to the total number of valence electrons. In the P block of periodic table the non-metallic character of elements decreases down the group. In fact the heaviest element in each p-block group is the most metallic in nature.</p>",
                    solution_hi: "<p>63.(a) आवर्त सारणी में p-ब्लॉक तत्वों के छह समूह हैं जिनकी संख्या 13 से 18 तक है। बोरॉन, कार्बन, नाइट्रोजन, ऑक्सीजन, फ्लोरीन और हीलियम समूहों में प्रमुख हैं। उनका संयोजी कोश इलेक्ट्रॉनिक विन्यास ns<sup>2</sup> np<sup>1-6</sup> (He को छोड़कर) है। p-ब्लॉक तत्व द्वारा दर्शाई गई अधिकतम ऑक्सीकरण अवस्था संयोजी इलेक्ट्रॉनों की कुल संख्या के बराबर है। आवर्त सारणी के p ब्लॉक में तत्वों का अधात्विक गुण समूह में नीचे की ओर घटता जाता है। वास्तव में प्रत्येक p-ब्लॉक समूह में सबसे भारी तत्व प्रकृति में सबसे अधिक धात्विक है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. Manoj is the husband of Sapna, whose mother is Neelam. Neelam&rsquo;s grandson is Aditya, who is the brother of Shefali. Shefali is the daughter of Vijay who is the brother of Manoj&rsquo;s wife. How is Sapna related to Shefali?</p>",
                    question_hi: "<p>64. मनोज सपना का पति हैं, जिनकी मां नीलम हैं। नीलम का पोता आदित्य है, जो शेफाली का भाई है। शेफाली विजय की पुत्री है जो मनोज की पत्नी का भाई है। सपना का शेफाली से क्या संबंध है?</p>",
                    options_en: ["<p>Sister</p>", "<p>Maternal Aunt</p>", 
                                "<p>Paternal Aunt</p>", "<p>Daughter</p>"],
                    options_hi: ["<p>बहन</p>", "<p>मामी</p>",
                                "<p>बुआ</p>", "<p>बहन</p>"],
                    solution_en: "<p>64.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135116.png\" alt=\"rId29\" width=\"192\" height=\"123\"><br>From the above diagram, we can say that Sapna is the Paternal Aunt of Shefali.</p>",
                    solution_hi: "<p>64.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135227.png\" alt=\"rId30\" width=\"175\" height=\"112\"><br>उपरोक्त आरेख से हम कह सकते हैं कि सपना शेफाली की बुआ हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. For Kharif crops, the temperature required for farming is above 25 degrees Celsius with high humidity and annual rainfall above ________ cm.</p>",
                    question_hi: "<p>65. खरीफ फसलों के लिए, उच्च आर्द्रता और ________ cm से अधिक वार्षिक वर्षा के साथ खेती के लिए आवश्यक तापमान 25 डिग्री सेल्सियस से अधिक होता है।</p>",
                    options_en: ["<p>16</p>", "<p>50</p>", 
                                "<p>100</p>", "<p>35</p>"],
                    options_hi: ["<p>16</p>", "<p>50</p>",
                                "<p>100</p>", "<p>35</p>"],
                    solution_en: "<p>65.(c) <strong>100. Kharif crops</strong> are sown during the monsoon season, which lasts from June to September in India. Examples : Rice, jowar, Cotton, Groundnut, Soybean, Jute, Sugarcane. <strong>Rabi crops</strong> are sown in winter from October to December and harvested in summer from April to June. Some of the important rabi crops are wheat, barley, peas, gram and mustard. Sesamum and castor seeds is a kharif crop in north and rabi crop in south India.</p>",
                    solution_hi: "<p>65.(c) <strong>100। ख़रीफ़ फ़सलें</strong> मानसून के मौसम के दौरान बोई जाती हैं, जो भारत में जून से सितंबर तक रहता है। उदाहरण: चावल, ज्वार, कपास, मूंगफली, सोयाबीन, जूट, गन्ना। <strong>रबी की फसलें </strong>सर्दियों में अक्टूबर से दिसंबर तक बोई जाती हैं और गर्मियों में अप्रैल से जून तक काटी जाती हैं। रबी की कुछ महत्वपूर्ण फसलें गेहूं, जौ, मटर, चना और सरसों हैं। तिल और अरंडी के बीज उत्तर में ख़रीफ़ की फ़सल और दक्षिण भारत में रबी की फ़सल है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. If the price of sugar falls by 25%, by how much percentage must a household increase its consumption so that the budget remains the same ?</p>",
                    question_hi: "<p>66. यदि चीनी की कीमत में 25% की गिरावट आती है, तो एक परिवार को अपनी खपत में कितने प्रतिशत की वृद्धि करनी चाहिए ताकि बजट समान रहे?</p>",
                    options_en: ["<p>33<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>", 
                                "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>33<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>33<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>",
                                "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>33<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>66.(c) Let initial price of sugar = 100<br>After discount of 25% price = 75<br>In ratio<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Initial&nbsp; &nbsp;:&nbsp; &nbsp; final<br>Price&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; 100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 75<br>Consumption&nbsp; &rarr;&nbsp; 75&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 100 <br>(the product of two variable will be always constant)<br>increase its consumption % =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>25</mn></mrow><mrow><mn>75</mn></mrow></mfrac></math> &times; 100&nbsp;<br>= 33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>66.(c) माना चीनी का प्रारंभिक मूल्य = 100<br>25% मूल्य की छूट के बाद = 75<br>अनुपात में<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;प्रारंभिक&nbsp; &nbsp;:&nbsp; &nbsp;अंतिम<br>कीमत&nbsp; &nbsp;&rarr;&nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 75<br>उपभोग &rarr;&nbsp; &nbsp; &nbsp; 75&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 100 <br>(दो चर का गुणनफल हमेशा स्थिर रहेगा)<br>इसकी खपत में % वृद्धि =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>25</mn></mrow><mrow><mn>75</mn></mrow></mfrac></math> &times; 100&nbsp;<br>= 33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. Identify the figure that completes the pattern (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135328.png\" alt=\"rId31\" width=\"136\" height=\"81\"></p>",
                    question_hi: "<p>67. पैटर्न को पूरा करने वाली आकृति को पहचानिए (घूर्णन की अनुमति नहीं हैं)। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135328.png\" alt=\"rId31\" width=\"150\" height=\"90\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135547.png\" alt=\"rId32\" width=\"117\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135671.png\" alt=\"rId33\" width=\"99\" height=\"65\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135798.png\" alt=\"rId34\" width=\"122\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135920.png\" alt=\"rId35\" width=\"126\" height=\"75\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135547.png\" alt=\"rId32\" width=\"114\" height=\"71\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135671.png\" alt=\"rId33\" width=\"116\" height=\"76\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135798.png\" alt=\"rId34\" width=\"129\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135920.png\" alt=\"rId35\" width=\"122\" height=\"73\"></p>"],
                    solution_en: "<p>67.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135547.png\" alt=\"rId32\" width=\"121\" height=\"75\"></p>",
                    solution_hi: "<p>67.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290135547.png\" alt=\"rId32\" width=\"121\" height=\"75\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. Continental Shelf which is the shallowest part of the ocean has an average gradient of:</p>",
                    question_hi: "<p>68. समुद्र के सबसे उथले हिस्से, महाद्वीपीय शेल्फ की औसत ढाल _______ होती है।</p>",
                    options_en: ["<p>1&deg; or even less</p>", "<p>2&deg; or even less</p>", 
                                "<p>8&deg; or even less</p>", "<p>5&deg; or even less</p>"],
                    options_hi: ["<p>1&deg; या उससे भी कम</p>", "<p>2&deg; या उससे भी कम</p>",
                                "<p>8&deg; या उससे भी कम</p>", "<p>5&deg; या उससे भी कम</p>"],
                    solution_en: "<p>68.(a)<strong> 1&deg; or even less. </strong>The <strong>Continental Shelf</strong> is the extended margin of each continent occupied by relatively shallow seas and gulfs. About Continental Shelf : The shelf typically ends at a very steep slope, called the shelf break. Their width varies globally, with an average of about 80 km, but can be narrower or even absent in some regions, such as the coasts of Chile and the west coast of Sumatra. </p>",
                    solution_hi: "<p>68.(a) <strong>1&deg; या उससे भी कम। महाद्वीपीय शेल्फ </strong>अपेक्षाकृत उथले समुद्री और खाड़ियों द्वारा व्याप्त प्रत्येक महाद्वीप का विस्तारित मार्जिन है। महाद्वीपीय शेल्फ के बारे में : शेल्फ आमतौर पर बहुत तीव्र ढलान पर समाप्त होता है, जिसे शेल्फ ब्रेक कहा जाता है। उनकी चौड़ाई विश्व स्तर पर भिन्न-भिन्न होती है, औसतन लगभग 80 किमी, लेकिन कुछ क्षेत्रों में, जैसे कि चिली के तट और सुमात्रा के पश्चिमी तट, संकीर्ण या अनुपस्थित भी हो सकती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. A paper is folded and cut as shown below. How will it appear when unfolded ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136033.png\" alt=\"rId36\" width=\"291\" height=\"85\"></p>",
                    question_hi: "<p>69. एक कागज को नीचे दिखाए अनुसार मोड़ा और काटा जाता है। खोले जाने पर यह कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136033.png\" alt=\"rId36\" width=\"291\" height=\"85\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136184.png\" alt=\"rId37\" width=\"92\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136302.png\" alt=\"rId38\" width=\"93\" height=\"98\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136384.png\" alt=\"rId39\" width=\"90\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136520.png\" alt=\"rId40\" width=\"89\" height=\"94\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136184.png\" alt=\"rId37\" width=\"92\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136302.png\" alt=\"rId38\" width=\"91\" height=\"95\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136384.png\" alt=\"rId39\" width=\"91\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136520.png\" alt=\"rId40\" width=\"93\" height=\"98\"></p>"],
                    solution_en: "<p>69.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136302.png\" alt=\"rId38\" width=\"91\" height=\"95\"></p>",
                    solution_hi: "<p>69.(b)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136302.png\" alt=\"rId38\" width=\"91\" height=\"95\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. The deliberations of the representatives at the Third Round Table Conference was held during the later months of which year ?</p>",
                    question_hi: "<p>70. तीसरे गोलमेज सम्मेलन में प्रतिनिधियों के विचार-विमर्श किस वर्ष के आखिरी महीनों में हुए थे ?</p>",
                    options_en: ["<p>1932</p>", "<p>1929</p>", 
                                "<p>1930</p>", "<p>1931</p>"],
                    options_hi: ["<p>1932</p>", "<p>1929</p>",
                                "<p>1930</p>", "<p>1931</p>"],
                    solution_en: "<p>70.(a) <strong>1932. Round Table Conferences -</strong> 1st round (November 1930 - January 1931) - It was commissioned by George V in the Royal Gallery House of Lords in London. Chaired by - British Prime Minister, Ramsay MacDonald. 2nd round (September 1931- December 1931) - Mahatma Gandhi is the sole representative of congress. 3rd round (November 1932&ndash; December 1932). B.R. Ambedkar and Tej Bahadur Sapru took part in all the Three Round Table Conferences.</p>",
                    solution_hi: "<p>70.(a) <strong>1932। गोलमेज सम्मेलन - </strong>प्रथम (नवंबर 1930 - जनवरी 1931) - इसे लंदन में रॉयल गैलरी हाउस ऑफ लॉर्ड्स में जॉर्ज पंचम द्वारा शुरू किया गया था। अध्यक्षता - ब्रिटिश प्रधान मंत्री, रैमसे मैकडोनाल्ड ने की। द्वितीय (सितंबर 1931-दिसंबर 1931) - महात्मा गांधी कांग्रेस के एकमात्र प्रतिनिधि हैं। तृतीय (नवंबर 1932-दिसंबर 1932)। बी.आर. अम्बेडकर और तेज बहादुर सप्रू ने तीनों गोलमेज़ सम्मेलनों में भाग लिया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. A survey of 40 students showed that 25% children liked playing football. How many children did not like playing football?</p>",
                    question_hi: "<p>71. 40 छात्रों के एक सर्वेक्षण से पता चला कि 25% बच्चों को फुटबॉल खेलना पसंद था। कितने बच्चों को फुटबॉल खेलना पसंद नहीं था?</p>",
                    options_en: ["<p>38</p>", "<p>30</p>", 
                                "<p>35</p>", "<p>26</p>"],
                    options_hi: ["<p>38</p>", "<p>30</p>",
                                "<p>35</p>", "<p>26</p>"],
                    solution_en: "<p>71.(b) Total number = 40 <br>25% of children liked playing football, which means 75% of students did not like playing football.<br>75% of students = 40 &times;<math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>= 30 student</p>",
                    solution_hi: "<p>71.(b) कुल संख्या = 40<br>25% बच्चों को फ़ुटबॉल खेलना पसंद था, यानी 75% छात्रों को फ़ुटबॉल खेलना पसंद नहीं था।<br>75% छात्र = 40 &times;<math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>= 30 छात्र</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. Priorities of the government in the fiscal area and its related policies and deviations are mentioned in which part of the budget document ?</p>",
                    question_hi: "<p>72. राजकोषीय क्षेत्र में सरकार की प्राथमिकताओं तथा उससे संबंधित नीतियों एवं विचलनों का उल्लेख बजट दस्तावेज के किस भाग में किया जाता है ?</p>",
                    options_en: ["<p>Medium-term Fiscal Policy Statement</p>", "<p>Fiscal Policy Strategy Statement</p>", 
                                "<p>Macroeconomic Framework Statement</p>", "<p>Appropriation Bill</p>"],
                    options_hi: ["<p>मध्&zwj;यावधिक राजकोषीय नीति विवरण</p>", "<p>राजकोषीय नीति कार्ययोजना विवरण</p>",
                                "<p>वृहद आर्थिक रूपरेखा विवरण</p>", "<p>विनियोग विधेयक</p>"],
                    solution_en: "<p>72.(b) <strong>The Fiscal Policy Strategy Statement </strong>is presented to Parliament along with the Union Budget. It outlines the government\'s strategic priorities in the fiscal area for the ensuing financial year, including its policies on taxation, expenditure, lending and investments, administered pricing, borrowings, and guarantees. It is one of the three policy statements that are mandated by the Fiscal Responsibility and Budget Management Act, 2003 (FRBMA). The other two statements are the Medium-term Fiscal Policy Statement and the Macroeconomic Framework Statement.</p>",
                    solution_hi: "<p>72.(b) <strong>राजकोषीय नीति कार्ययोजना</strong> विवरण केंद्रीय बजट के साथ संसद में प्रस्तुत किया जाता है। यह आगामी वित्तीय वर्ष के लिए वित्तीय क्षेत्र में सरकार की रणनीतिक प्राथमिकताओं को रेखांकित करता है, जिसमें कराधान, व्यय, उधार और निवेश, प्रशासित मूल्य निर्धारण, उधार और गारंटी पर इसकी नीतियां शामिल हैं। यह उन तीन नीति वक्तव्यों में से एक है जो राजकोषीय उत्तरदायित्व और बजट प्रबंधन अधिनियम, 2003 (FRBMA) द्वारा अनिवार्य हैं। अन्य दो कथन मध्यम अवधि की राजकोषीय नीति वक्तव्य और व्यापक आर्थिक रूपरेखा वक्तव्य हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136660.png\" alt=\"rId41\" width=\"257\" height=\"87\"> <br>The diagram represents the number of students in a school, who can speak different languages?<br>How many students can speak French?</p>",
                    question_hi: "<p>73.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136785.png\" alt=\"rId42\" width=\"207\" height=\"78\"> <br>आरेख एक स्कूल में छात्रों की संख्या को दर्शाता है, जो विभिन्न भाषाएं बोल सकते हैं?<br>कितने छात्र फ्रेंच बोल सकते हैं?</p>",
                    options_en: ["<p>12</p>", "<p>7</p>", 
                                "<p>15</p>", "<p>4</p>"],
                    options_hi: ["<p>12</p>", "<p>7</p>",
                                "<p>15</p>", "<p>4</p>"],
                    solution_en: "<p>73.(b)<br>the student who speaks french = 4 + 3 = 7&nbsp;<br>Number present in the circle.</p>",
                    solution_hi: "<p>73.(b)<br>जो विद्यार्थी फ्रेंच बोलते है = 4 + 3 = 7&nbsp;<br>वह नंबर वृत्त में मौजूद है ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. Select the correct mirror image of the given figure, when the mirror is placed at line MN as shown<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136882.png\" alt=\"rId43\" width=\"106\" height=\"110\"></p>",
                    question_hi: "<p>74. यदि दर्पण को नीचे दिखाए गए अनुसार रेखा MN पर रखा गया हो, तो दी गई आकृति के सही दर्पण प्रतिबिम्ब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290136882.png\" alt=\"rId43\" width=\"106\" height=\"110\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137010.png\" alt=\"rId44\" width=\"100\" height=\"98\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137104.png\" alt=\"rId45\" width=\"98\" height=\"96\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137195.png\" alt=\"rId46\" width=\"100\" height=\"107\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137361.png\" alt=\"rId47\" width=\"100\" height=\"98\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137010.png\" alt=\"rId44\" width=\"99\" height=\"97\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137104.png\" alt=\"rId45\" width=\"104\" height=\"102\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137195.png\" alt=\"rId46\" width=\"100\" height=\"107\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137361.png\" alt=\"rId47\" width=\"100\" height=\"98\"></p>"],
                    solution_en: "<p>74.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137195.png\" alt=\"rId46\" width=\"110\" height=\"117\"></p>",
                    solution_hi: "<p>74.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137195.png\" alt=\"rId46\" width=\"110\" height=\"117\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75. Vallabhbhai Patel and ________ helped Gandhi in Kheda by organising his tour of the villages and urging the peasants to stand firm against the government.</p>",
                    question_hi: "<p>75. वल्लभभाई पटेल और ________ने खेड़ा में गांधी जी के गांवों के दौरे की व्&zwj;यवस्&zwj;था करके तथा किसानों को सरकार के खिलाफ मजबूती से खड़े होने के लिए प्रोत्साहित करके गांधी जी की सहायता की।</p>",
                    options_en: ["<p>Motilal Nehru</p>", "<p>Mohammad Ali Jinnah</p>", 
                                "<p>Indulal Yajnik</p>", "<p>Jamunalal Bajaj</p>"],
                    options_hi: ["<p>मोतीलाल नेहरू</p>", "<p>मोहम्मद अली जिन्ना</p>",
                                "<p>इंदुलाल याग्निक</p>", "<p>जमनालाल बजाज</p>"],
                    solution_en: "<p>75.(c) <strong>Indulal Yajnik.</strong> Mahatma Gandhi returned to India on 9 January 1915. Movement launched by Gandhi ji during the freedom struggle - Champaran Satyagraha (1917), Kheda Movement (1918), Non-cooperation Movement (1920), and Quit India Movement (1942).</p>",
                    solution_hi: "<p>75.(c) <strong>इंदुलाल याग्निक।</strong> महात्मा गांधी 9 जनवरी 1915 को भारत लौट आए। स्वतंत्रता संग्राम के दौरान गांधी जी द्वारा चलाए गए आंदोलन - चंपारण सत्याग्रह (1917), खेड़ा आंदोलन (1918), असहयोग आंदोलन (1920), और भारत छोड़ो आंदोलन (1942)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. A merchant sold his goods for Rs.171 at a profit percent equal to cost price. What was the cost price(in rs.)?</p>",
                    question_hi: "<p>76. एक व्यापारी ने अपना माल 171 रुपये में लागत मूल्य के बराबर लाभ प्रतिशत पर बेचा। लागत मूल्य (रुपयों में) क्या था?</p>",
                    options_en: ["<p>98</p>", "<p>70</p>", 
                                "<p>90</p>", "<p>78</p>"],
                    options_hi: ["<p>98</p>", "<p>70</p>",
                                "<p>90</p>", "<p>78</p>"],
                    solution_en: "<p>76.(c)<br>Let , CP = x rupees<br>profit % = x %<br>x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>100</mn></mfrac></math> &times; x = 171<br>x<sup>2</sup> + 100x - 17100 = 0<br>After solving , we get x = 90<br>CP = 90 rupees</p>",
                    solution_hi: "<p>76.(c)<br>माना, क्रय मूल्य = x रुपये<br>लाभ% = x%<br>x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>100</mn></mfrac></math> &times; x = 171<br>x<sup>2</sup> + 100x - 17100 = 0<br>हल करने के बाद, हमें x = 90 . मिलता है<br>क्रय मूल्य = 90 रुपये</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. Tansen, the famous musician, often sang in the court of which ruler ?</p>",
                    question_hi: "<p>77. प्रसिद्ध संगीतकार तानसेन, प्रायः किस शासक के दरबार में गाया करते थे ?</p>",
                    options_en: ["<p>Akbar</p>", "<p>Sher Shah Suri</p>", 
                                "<p>Feroz Shah Tughlaq</p>", "<p>Allaudin Khilji</p>"],
                    options_hi: ["<p>अकबर</p>", "<p>शेर शाह सूरी</p>",
                                "<p>फिरोज शाह तुगलक</p>", "<p>अलाउद्दीन खिलजी</p>"],
                    solution_en: "<p>77.(a) <strong>Akbar</strong> was the third Mughal emperor, and he ruled over India from 1556 to 1605. Tansen was born into a Hindu Gaur Brahmin family in Behat, near Gwalior, India. Ramtanu was his birth name and was a disciple of Swami Haridas in earlier days and later on, he learned music from Hazrat Muhammad Ghaus. Navratnas of Akbar - Abul Fazl, Faizi, Tansen, Birbal,Todar Mal, Raja Man Singh, Abdul Rahim Khan-I-Khana, Fakir Aziao-Din, Mullah Do Piaza.</p>",
                    solution_hi: "<p>77.(a) <strong>अकबर </strong>तीसरा मुगल सम्राट था, और उसने 1556 से 1605 तक भारत पर शासन किया। तानसेन का जन्म भारत के ग्वालियर के पास बेहट में एक हिंदू गौड़ ब्राह्मण परिवार में हुआ था। उनका जन्म नाम रामतनु था और वे पहले स्वामी हरिदास के शिष्य थे और बाद में उन्होंने हजरत मुहम्मद गौस से संगीत सीखा। अकबर के नवरत्न - अबुल फजल, फैजी, तानसेन, बीरबल, टोडर मल, राजा मान सिंह, अब्दुल रहीम खान-ए-खाना, फकीर अज़ियाओ-दीन, मुल्ला दो पियाज़ा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. Three different positions of the same dice (figures 1,2 and 3) are shown. Which digit is on the face opposite to the one having 5?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137474.png\" alt=\"rId48\" width=\"158\" height=\"97\"></p>",
                    question_hi: "<p>78. एक ही पासे की तीन अलग-अलग स्तिथियों (आकृति 1,2 और 3) को दर्शाया गया है। 5 अंक वाले फलक के विपरीत फलक पर कौनसा अंक है ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137474.png\" alt=\"rId48\" width=\"158\" height=\"97\"></p>",
                    options_en: ["<p>1</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>1</p>", "<p>6</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>78.(d)<br>On the given dice <br>2,3,1,6 are not possible because they are adjacent to 5. Therefore, 4 is opposite to 5.</p>",
                    solution_hi: "<p>78.(d)<br>दिए गए पासे पर<br>2,3,1,6 संभव नहीं है क्योंकि वे 5 के सन्निकट हैं। इसलिए, 4 , 5 के विपरीत है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. In India all loans that are below __________ are considered as micro loans.</p>",
                    question_hi: "<p>79. भारत में __________ से कम के सभी ऋणों को सूक्ष्म ऋण (micro loans) माना जाता है।</p>",
                    options_en: ["<p>one lakh rupees</p>", "<p>four lakh rupees</p>", 
                                "<p>two lakh rupees</p>", "<p>three lakh rupees</p>"],
                    options_hi: ["<p>एक लाख रुपये</p>", "<p>चार लाख रुपये</p>",
                                "<p>दो लाख रुपये</p>", "<p>तीन लाख रुपये</p>"],
                    solution_en: "<p>79.(a) <strong>one lakh rupees</strong>. Non-Banking Financial Company - Micro Finance Institution (NBFC-MFI): A type of financial institution that provides financial services, including microloans and other financial products, to low-income and economically vulnerable individuals, especially in rural and underserved areas. The minimum requirement of microfinance loans for NBFC - MFIs is 75% of the total assets. The regulator of Micro Finance in India is the Reserve Bank of India. The different types of Microfinance institutions (MFI) are: Credit unions, Non-governmental organisations, Commercial banks.</p>",
                    solution_hi: "<p>79.(a) <strong>एक लाख रुपये।</strong> गैर-बैंकिंग वित्तीय कंपनी - माइक्रो फाइनेंस इंस्टीट्यूशन (NBFC-MFI): एक प्रकार की वित्तीय संस्था जो कम आय और आर्थिक रूप से कमजोर व्यक्तियों, विशेष रूप से ग्रामीण और वंचित क्षेत्रों में, सूक्ष्म ऋण और अन्य वित्तीय उत्पादों सहित वित्तीय सेवाएं प्रदान करती है। NBFC-MFI के लिए माइक्रोफाइनेंस ऋण की न्यूनतम आवश्यकता कुल संपत्ति का 75% है। भारत में माइक्रो फाइनेंस का नियामक भारतीय रिजर्व बैंक है। विभिन्न प्रकार के माइक्रोफाइनेंस संस्थान (MFI) हैं: क्रेडिट यूनियन, गैर-सरकारी संगठन, वाणिज्यिक बैंक।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. The sum of cost prices of two bikes is Rs.1,00,000. One bike was sold at a profit of 20% and the second one was sold at a loss of 20%. If the selling prices are same, what is the cost price of the first bike sold?</p>",
                    question_hi: "<p>80. दो बाइक के लागत मूल्य का योग 1,00,000 रुपये है। एक बाइक को 20% के लाभ पर और दूसरी को 20% की हानि पर बेचा गया। यदि विक्रय मूल्य समान हैं, तो बेची गई पहली बाइक का क्रय मूल्य क्या है?</p>",
                    options_en: ["<p>Rs. 60,000</p>", "<p>Rs. 35,000</p>", 
                                "<p>Rs. 50,000</p>", "<p>Rs. 40,000</p>"],
                    options_hi: ["<p>Rs. 60,000</p>", "<p>Rs. 35,000</p>",
                                "<p>Rs. 50,000</p>", "<p>Rs. 40,000</p>"],
                    solution_en: "<p>80.(d)<br>The sum of cost prices of two bikes = 100000 Rs<br>Let CP of 1st bike = a , CP of 2nd bike = b<br>120% of a = 80% of b<br>a : b = 2 : 3<br>CP of 1st bike = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> &times; 100000<br>= 40000 Rs.</p>",
                    solution_hi: "<p>80.(d)<br>दो बाइक के क्रय मूल्य का योग = 100000 रुपये<br>प्रथम बाइक का क्रय मूल्य= a और दूसरी बाइक का क्रय मूल्य = b<br>120% of a = 80% of b<br>a : b = 2 : 3<br>पहली बाइक का क्रय मूल्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> &times; 100000<br>= 40000 रुपये</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. Which of the following algae is also called kelp and found in the deep sea ?</p>",
                    question_hi: "<p>81. निम्नलिखित में से किस शैवाल को केल्प (kelp) भी कहा जाता है, और यह गहरे समुद्र में पाया जाता है ?</p>",
                    options_en: ["<p>Laminaria</p>", "<p>Fucus</p>", 
                                "<p>Nostoc</p>", "<p>Chlamydomona</p>"],
                    options_hi: ["<p>लैमिनारिया (Laminaria)</p>", "<p>फ्यूकस (Fucus)</p>",
                                "<p>नॉसटॉक (Nostoc)</p>", "<p>क्लैमाइडोमोना (Chlamydomona)</p>"],
                    solution_en: "<p>81.(a) <strong>Laminaria</strong> is a genus of brown algae that belongs to the class Phaeophyceae. It is used in the production of potassium and iodine. The algae are divided into three main classes: Chlorophyceae (Green algae), Phaeophyceae (brown algae) and Rhodophyceae (red algae).</p>",
                    solution_hi: "<p>81.(a) <strong>लैमिनारिया</strong> भूरे शैवाल की एक प्रजाति है जो फियोफाइसी वर्ग से संबंधित है। इसका उपयोग पोटेशियम और आयोडीन के उत्पादन में किया जाता है। शैवाल को तीन मुख्य वर्गों में विभाजित किया गया है: क्लोरोफाइसी (हरा शैवाल), फियोफाइसी (भूरा शैवाल) और रोडोफाइसी (लाल शैवाल)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. To gain 25% after announcing a discount of 10%. the shopkeeper must mark the price of the article with cost price Rs 360 as:</p>",
                    question_hi: "<p>82. दुकानदार को 10% की छूट की घोषणा के बाद 25% लाभ प्राप्त करने के लिए 360 रुपये के लागत मूल्य वाली वस्तु का अंकित मूल्य कितना तय करना चाहिए ?</p>",
                    options_en: ["<p>Rs.500</p>", "<p>Rs.486</p>", 
                                "<p>Rs.450</p>", "<p>Rs.460</p>"],
                    options_hi: ["<p>Rs.500</p>", "<p>Rs.486</p>",
                                "<p>Rs.450</p>", "<p>Rs.460</p>"],
                    solution_en: "<p>82.(a)<br>Profit = 25% =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> , Discount = 10% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math><br>&nbsp; CP&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; SP&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;MP<br>&nbsp; &nbsp;4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10<br>-----------------------------------------<br>4 &times; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 &times; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5 &times; 10<br>&nbsp; &nbsp;36&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 45&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 50<br>The CP = 36 in ratio whose value is given Rs360<br>&there4;MP = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>36</mn></mfrac></math> &times; 50 = Rs500</p>",
                    solution_hi: "<p>82.(a)<br>लाभ= 25%= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> ,छूट = 10% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math><br>लागत मूल्य&nbsp; विक्रय मूल्य&nbsp; अंकित मूल्य<br>&nbsp; &nbsp;4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10<br>-----------------------------------------<br>4 &times; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 &times; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5 &times; 10<br>&nbsp; &nbsp;36&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 45&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 50<br>लागत मूल्य = 36 अनुपात में जिसका मूल्य 360 रुपये दिया गया है<br>&there4; अंकित मूल्य=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>36</mn></mfrac></math> &times; 50 = Rs500.</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "83. Which Indian state became the first to launch the \'Mukhyamantri Ladli Bahna Yojana\' for women empowerment?",
                    question_hi: "83. कौन सा भारतीय राज्य महिलाओं के सशक्तिकरण के लिए \'मुख्यमंत्री लाड़ली बहना योजना\' शुरू करने वाला पहला राज्य बना?",
                    options_en: [" Madhya Pradesh", " Uttar Pradesh", 
                                " Rajasthan", " Bihar"],
                    options_hi: [" मध्य प्रदेश", " उत्तर प्रदेश",
                                " राजस्थान", " बिहार"],
                    solution_en: "<p>83.(a) <strong>Madhya Pradesh</strong>. The then Chief Minister Shivraj Singh Chouhan announced the scheme on January 28, 2023. Benefits: Eligible women receive a monthly financial aid of INR 1,250 in their Aadhaar-linked DBT-enabled bank account. Eligibility: Married women between 21 and 60 years of age, including divorced, widowed, and abandoned women. Shri Mohan Yadav is the current (October 2024) Chief Minister of Madhya Pradesh.</p>",
                    solution_hi: "<p>83.(a) <strong>मध्य प्रदेश।</strong> तत्कालीन मुख्यमंत्री शिवराज सिंह चौहान ने 28 जनवरी, 2023 को इस योजना की घोषणा की थी। लाभ: पात्र महिलाओं को उनके आधार से जुड़े डीबीटी-सक्षम बैंक खाते में प्रति माह 1,250 रुपये की वित्तीय सहायता प्राप्त होती है। पात्रता: 21 से 60 वर्ष की विवाहित महिलाएँ, जिनमें तलाकशुदा, विधवा और परित्यक्ता महिलाएँ भी शामिल हैं। श्री मोहन यादव वर्तमान (अक्टूबर 2024) में मध्य प्रदेश के मुख्यमंत्री हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. Five boys A, B, C, D and E five girls P, Q, R, S and T are sitting in two rows opposite each other such that the boys are in one row and the girls are in one row. C is sitting in the center and A is sitting on his left. D is sitting between B and C. T who is to the left of S is sitting opposite B who is two seats away from E. P is sitting somewhere between Q and R. Who is sitting opposite E?</p>",
                    question_hi: "<p>84. पाँच लड़के A, B, C, D और E तथा पाँच लड़कियाँ P, Q, R, S और T एक दूसरे के सामने दो पंक्तियों में इस प्रकार बैठे हैं कि लड़के एक पंक्ति में हैं और लड़कियाँ एक पंक्ति में हैं। C केंद्र में बैठा है और A उसके बायीं ओर बैठा है। D, B और C के बीच बैठा है। T, जो S के बाईं ओर बैठा है, B के सामने है, जो E से दो स्थान दूर है। P, Q और R के बीच कहीं बैठा है। E के सामने कौन बैठा है?</p>",
                    options_en: ["<p>Q</p>", "<p>R</p>", 
                                "<p>P</p>", "<p>S</p>"],
                    options_hi: ["<p>Q</p>", "<p>R</p>",
                                "<p>P</p>", "<p>S</p>"],
                    solution_en: "<p>84.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137577.png\" alt=\"rId49\" width=\"149\" height=\"89\"><br>So, P is sitting opposite to E.</p>",
                    solution_hi: "<p>84.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137577.png\" alt=\"rId49\" width=\"149\" height=\"89\"><br>अत: P, E के विपरीत बैठा है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. Dhruvasena II is related to which of the following ruling dynasties ?</p>",
                    question_hi: "<p>85. ध्रुवसेन द्वितीय, निम्नलिखित में से किस शासक राजवंश से संबंधित है ?</p>",
                    options_en: ["<p>Vakataka</p>", "<p>Maitraka</p>", 
                                "<p>Pushyabhuti</p>", "<p>Maukhari</p>"],
                    options_hi: ["<p>वाकाटक</p>", "<p>मैत्रक</p>",
                                "<p>पुष्यभूति</p>", "<p>मौखरि</p>"],
                    solution_en: "<p>85.(b) <strong>Maitraka</strong> dynasty, which ruled over Gujarat and Saurashtra from the 5th to the 8th centuries CE. Vakataka dynasty : Founded by Vindhyashakti. Ajanta caves (numbers ⅩⅥ, ⅩⅦ, ⅩⅨ) are the best examples of Vakataka excellence in the field of painting. Other dynasties and their founders - Vardhana dynasty (Pushyabhuti), Reddi kingdom (Prolaya Vema Reddi).</p>",
                    solution_hi: "<p>85.(b) <strong>मैत्रक राजवंश,</strong> जिसने 5वीं से 8वीं शताब्दी ईस्वी तक गुजरात और सौराष्ट्र पर शासन किया। वाकाटक राजवंश: विंध्यशक्ति द्वारा स्थापित किया गया। अजंता की गुफाएँ (संख्या ⅩⅥ, ⅩⅦ, ⅩⅨ) चित्रकला के क्षेत्र में वाकाटक उत्कृष्टता का सर्वोत्तम उदाहरण हैं। अन्य राजवंश और उनके संस्थापक - वर्धन राजवंश (पुष्यभूति), रेड्डी साम्राज्य (प्रोलाया वेमा रेड्डी)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. Which of the following is correct, when a formula has its symbolic meaning<br>(A - Amount, P- Principal, SI - simple interest)?</p>",
                    question_hi: "<p>86. निम्न में से कौन सा सही है, जब एक सूत्र का अपना प्रतीकात्मक अर्थ होता है<br>(A - राशि, P- मूलधन,SI - साधारण ब्याज)?</p>",
                    options_en: ["<p>A = P + SI</p>", "<p>P = A + SI</p>", 
                                "<p>A + P = SI</p>", "<p>P - A = 2SI</p>"],
                    options_hi: ["<p>A = P + SI</p>", "<p>P = A + SI</p>",
                                "<p>A + P = SI</p>", "<p>P - A = 2SI</p>"],
                    solution_en: "<p>86.(a) Amount = Principal + simple interest<br>A = P + SI</p>",
                    solution_hi: "<p>86.(a) मिश्रधन = मूलधन + साधारण ब्याज<br>A = P + SI</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. Which of the following is NOT an Equestrian discipline ?</p>",
                    question_hi: "<p>87. निम्नलिखित में से कौन-सा घुड़सवारी प्रतियोगिता नहीं है ?</p>",
                    options_en: ["<p>Dressage</p>", "<p>Vaulting</p>", 
                                "<p>Sprint</p>", "<p>Show jumping</p>"],
                    options_hi: ["<p>ड्रेसेज (Dressage)</p>", "<p>वॉल्टिंग (Vaulting)</p>",
                                "<p>स्प्रिंट (Sprint)</p>", "<p>शो जंपिंग (Show jumping)</p>"],
                    solution_en: "<p>87.(c) <strong>Sprint. Equestrian</strong> is the broad umbrella term for sport involving riding on horseback. There are numerous equestrian disciplines ranging from horse racing and vaulting (gymnastics on horseback) to polo and rodeo. There are three disciplines in the Olympic Games: Dressage, Eventing, and jumping (also known as show jumping).</p>",
                    solution_hi: "<p>87.(c)<strong> स्प्रिंट। Equestrian</strong> (घुड़सवारी) उस खेल के लिए व्यापक शब्द है जिसमें घुड़दौड़ शामिल है। इसमे घुड़दौड़ और वॉल्टिंग (घोड़े पर जिमनास्टिक) से लेकर पोलो और रोडियो तक कई घुड़सवारी विधाएं हैं। ओलंपिक खेलों में तीन विधाएं हैं: ड्रेसेज, इवेंटिंग और जंपिंग (जिसे शो जंपिंग भी कहा जाता है)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. The difference between the compound interest and the simple interest on a sum of money&nbsp;at 10% for 2 years (compounded annually) is Rs.50. The sum of money is</p>",
                    question_hi: "<p>88. एक राशि पर 10% की दर से 2 वर्षों के लिए चक्रवृद्धि ब्याज और साधारण ब्याज के बीच का अंतर (वार्षिक रूप से संयोजित) 50 रूपये है, धन का योग कितना होगा ?</p>",
                    options_en: ["<p>Rs. 5,000</p>", "<p>Rs. 4,000</p>", 
                                "<p>Rs. 2,500</p>", "<p>Rs. 4,500</p>"],
                    options_hi: ["<p>Rs. 5,000</p>", "<p>Rs. 4,000</p>",
                                "<p>Rs. 2,500</p>", "<p>Rs. 4,500</p>"],
                    solution_en: "<p>88.(a) We know, the difference between the compound interest and the simple interest on a sum of money P, at R% for 2 years (compounded annually) is = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>R</mi><mn>2</mn></msup><mn>100</mn></mfrac></math> &times; 1% of P<br>Given, R =&nbsp; 10%,<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>R</mi><mn>2</mn></msup><mn>100</mn></mfrac></math> &times; 1% of P =&nbsp; 50<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mn>100</mn></mfrac></math> = 50<br>&rArr; P = 5000<br>The sum of money is 5000 Rs.</p>",
                    solution_hi: "<p>88.(a) जैसा कि हम जानते हैं, एक राशि P पर 2 वर्ष के लिए R% की दर से चक्रवृद्धि ब्याज और साधारण ब्याज के बीच का अंतर (वार्षिक रूप से संयोजित) है,<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>R</mi><mn>2</mn></msup><mn>100</mn></mfrac></math> &times; 1% of P<br>दिया गया R = 10%,<br>प्रश्न के अनुसार, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>R</mi><mn>2</mn></msup><mn>100</mn></mfrac></math> &times; 1% of P =&nbsp; 50<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mn>100</mn></mfrac></math> = 50<br>&rArr; P = 5000<br>अतः राशि 5000 रुपये है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. Who among the following was honored Padma Vibhushan 2024 posthumously for their contribution to Social Work in India?</p>",
                    question_hi: "<p>89. निम्नलिखित में से किसे भारत में सामाजिक कार्य में उनके योगदान के लिए मरणोपरांत पद्म विभूषण 2024 से सम्मानित किया गया?</p>",
                    options_en: ["<p>Ms. Vyjayantimala Bali - Art</p>", "<p>Shri Konidela Chiranjeevi - Art</p>", 
                                "<p>Shri Bindeshwar Pathak - Social Work</p>", "<p>Ms. Padma Subrahmanyam - Art</p>"],
                    options_hi: ["<p>सुश्री वैजयंतीमाला बाली - कला</p>", "<p>श्री कोनिदेला चिरंजीवी - कला</p>",
                                "<p>श्री बिंदेश्वर पाठक - सामाजिक कार्य</p>", "<p>सुश्री पद्मा सुब्रह्मण्यम - कला</p>"],
                    solution_en: "<p>89.(c) <strong>Shri Bindeshwar Pathak</strong>. Shri Bindeshwar Pathak, a prominent social worker from Bihar, was posthumously recognized for his significant contributions to social work, particularly for his work in improving sanitation and hygiene in India. He was the founder of Sulabh International, an organization that played a crucial role in promoting low-cost sanitation solutions, eradicating manual scavenging, and improving public health. His efforts led to significant social reforms and better sanitation practices across the country. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137683.png\" alt=\"rId50\" width=\"304\" height=\"113\"></p>",
                    solution_hi: "<p>89.(c) <strong>श्री बिंदेश्वर पाठक। </strong>श्री बिंदेश्वर पाठक, बिहार के एक प्रमुख सामाजिक कार्यकर्ता, को मरणोपरांत सामाजिक कार्य में उनके महत्वपूर्ण योगदान के लिए सम्मानित किया गया, विशेष रूप से भारत में स्वच्छता और सफाई को सुधारने के उनके कार्यों के लिए। वह सुलभ इंटरनेशनल के संस्थापक थे, एक संगठन जिसने कम लागत वाले स्वच्छता समाधान को बढ़ावा देने, मैनुअल स्कैवेंजिंग को समाप्त करने और सार्वजनिक स्वास्थ्य में सुधार करने में महत्वपूर्ण भूमिका निभाई। उनके प्रयासों ने देशभर में महत्वपूर्ण सामाजिक सुधार और बेहतर स्वच्छता प्रथाओं को जन्म दिया।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137805.png\" alt=\"rId51\" width=\"340\" height=\"117\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. Six people K, L, M, N, O and P are sitting around a circular table facing the center. Only two people are sitting between N and P. K and M are immediate neighbours of P. L. and O are immediate neighbours of N. N is sitting second to the right of K. M is sitting to the immediate left of P and immediate right of L. Who is sitting third to the right of O?</p>",
                    question_hi: "<p>90. छ: व्यक्ति K, L, M, N, O और P एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हुए हैं। N और P के बीच केवल दो व्यक्ति बैठे हैं। K और M, P के निकटतम पड़ोसी हैं। L और O, N के निकटतम पड़ोसी हैं। N, K के दायें से दूसरे स्थान पर बैठा है। M, P के ठीक बायें और L ठीक दायें बैठा है। O के दायें से तीसरे स्थान पर कौन बैठा है?</p>",
                    options_en: ["<p>L</p>", "<p>K</p>", 
                                "<p>M</p>", "<p>N</p>"],
                    options_hi: ["<p>L</p>", "<p>K</p>",
                                "<p>M</p>", "<p>N</p>"],
                    solution_en: "<p>90.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137936.png\" alt=\"rId52\" width=\"88\" height=\"98\"><br>From the above sitting arrangement, we can clearly see that, <br>M is sitting third to the right of O.</p>",
                    solution_hi: "<p>90.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290137936.png\" alt=\"rId52\" width=\"88\" height=\"98\"><br>उपरोक्त बैठने की व्यवस्था से, हम स्पष्ट रूप से देख सकते हैं कि, O के दायें से तीसरे स्थान पर M बैठा है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. Who among the following Indian authors has written the autobiography titled &lsquo;My Story&rsquo; ?</p>",
                    question_hi: "<p>91. निम्नलिखित में से किस भारतीय लेखक ने \'माई स्टोरी\' नामक आत्मकथा लिखी है ?</p>",
                    options_en: ["<p>Kiran Desai</p>", "<p>Arundhati Roy</p>", 
                                "<p>Shobha Dey</p>", "<p>Kamala Das</p>"],
                    options_hi: ["<p>किरण देसाई</p>", "<p>अरुंधति रॉय</p>",
                                "<p>शोभा डे</p>", "<p>कमला दास</p>"],
                    solution_en: "<p>91.(d) <strong>Kamala Das. Autobiographies by Indians - </strong>Why I Am An Atheist (Bhagat Singh), Waiting for a Visa (B. R. Ambedkar), My Experiments with Truth (Mohandas Karamchand Gandhi), Atmavrittanta (Manilal Dwivedi), How I Became a Hindu (Sita Ram Goel), My Country My Life (L. K. Advani), One Life Is Not Enough (K. Natwar Singh), Justice for the Judge (Ranjan Gogoi).</p>",
                    solution_hi: "<p>91.(d) <strong>कमला दास। भारतीयों की आत्मकथाएँ - </strong>व्हाई आई ऍम एन एथिस्ट (भगत सिंह), वैटिंग फॉर ए वीजा (बी.आर. अम्बेडकर), माय एक्सपेरिमेंट विद ट्रुथ (मोहनदास करमचंद गांधी), आत्मवृत्तांत (मणिलाल द्विवेदी), हाउ आइ बिकम ए हिन्दू (सीता राम गोयल), माई कंट्री माई लाइफ (एल.के.आडवाणी), वन लाइफ इज़ नॉट इनफ (के. नटवर सिंह), जस्टिस फॉर द जज (रंजन गोगोई)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. The average weight of P, Q and R is 58 kg. If the average weight of P and Q is 54 kg and that of Q and R is 48 kg, then the weight of Q is</p>",
                    question_hi: "<p>92. P, Q और R का औसत वजन 58 kg है। यदि P और Q का औसत वजन 54 kg है और Q और R का औसत वजन 48 kg है। तो Q का वजन कितना होगा ?</p>",
                    options_en: ["<p>26 kg</p>", "<p>32 kg</p>", 
                                "<p>30 kg</p>", "<p>28 kg</p>"],
                    options_hi: ["<p>26 kg</p>", "<p>32 kg</p>",
                                "<p>30 kg</p>", "<p>28 kg</p>"],
                    solution_en: "<p>92.(c) Sum of weight of P, Q, R = 3 &times; 58 = 174 kg<br>Sum of weight of P and Q = 2 &times; 54 = 108 kg<br>Sum of weight of Q and R = 2 &times; 48 = 96 kg<br>Weight of Q = (108 + 96) - 174 = &gt; 204 - 174 = 30 kg.</p>",
                    solution_hi: "<p>92.(c) P, Q और R के वजन का योग = 3 &times; 58 =174 किलो<br>P और Q के वजन का योग = 2 &times; 54 = 108 किलो<br>Q और R के वजन का योग = 2 &times; 48 = 96 किलो<br>Q का वजन = (108 + 96) - 174 किलो = &gt; 204 - 174 = 30 किलो</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. In a volleyball game, each team comprise of how many players ?</p>",
                    question_hi: "<p>93. वॉलीबॉल खेल में, प्रत्येक टीम में कितने खिलाड़ी होते हैं ?</p>",
                    options_en: ["<p>5</p>", "<p>8</p>", 
                                "<p>6</p>", "<p>7</p>"],
                    options_hi: ["<p>5</p>", "<p>8</p>",
                                "<p>6</p>", "<p>7</p>"],
                    solution_en: "<p>93.(c) <strong>6. Volleyball</strong> (Originally called Mintonette) is a net game that involves two teams of 12 players (six playing and six substitutes). William G. Morgan is credited with creating the game of volleyball in 1895 in Holyoke, Massachusetts. <strong>Sports (Number of players)</strong> - Basketball (5), Football (11), Kabaddi (7), Kho Kho (9), Water Polo (7).</p>",
                    solution_hi: "<p>93.(c) <strong>6 । वॉलीबॉल </strong>(मूल रूप से मिंटोनेट कहा जाता है) एक नेट गेम है जिसमें 12 खिलाड़ियों (छह खिलाड़ी और छह स्थानापन्न) की दो टीमें शामिल होती हैं। विलियम जी मॉर्गन को 1895 में होलोके, मैसाचुसेट्स में वॉलीबॉल का खेल शुरू करने का श्रेय दिया जाता है। <strong>खेल (खिलाड़ियों की संख्या)</strong> - बास्केटबॉल (5), फुटबॉल (11), कबड्डी (7), खो खो (9), वाटर पोलो (7)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. The table given below gives data for wheat production (in lakh tonnes) for 5 states,<br>from the year 2015 to 2019. Study the data given in the table and answer the question<br>below it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290138013.png\" alt=\"rId53\" width=\"242\" height=\"109\"> <br>In which state was there a continuous and ﬁxed increase in production, from the year 2015 to 2017?</p>",
                    question_hi: "<p>94. नीचे दी गई तालिका में वर्ष 2015 से 2019 तक 5 राज्यों के लिए गेहूं उत्पादन (लाख टन में) के आंकड़े दिए गए हैं। तालिका में दिए गए आंकड़ों का अध्ययन कीजिये और नीचे दिए गए प्रश्न का उत्तर दीजिये ।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290138115.png\" alt=\"rId54\" width=\"261\" height=\"119\"> <br>वर्ष 2015 से 2017 तक किस राज्य में उत्पादन में निरंतर और निश्चित वृद्धि हुई थी?</p>",
                    options_en: ["<p>B</p>", "<p>D</p>", 
                                "<p>A</p>", "<p>C</p>"],
                    options_hi: ["<p>B</p>", "<p>D</p>",
                                "<p>A</p>", "<p>C</p>"],
                    solution_en: "<p>94.(d)<br>From the table we can observe that from 2015 to 2017, only State C has a continuous and fixed increase in production.</p>",
                    solution_hi: "<p>94.(d)<br>तालिका से हम देख सकते हैं कि 2015 से 2017 तक केवल राज्य C में उत्पादन में निरंतर और निश्चित वृद्धि हुई है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. At normal temperature and atmospheric pressure, ______ has a density of 1.87 kg/m<sup>3</sup>, which is 1.5 times heavier than air and exists as a liquid below the critical temperature of 31&deg;C.</p>",
                    question_hi: "<p>95. सामान्य तापमान और वायुमंडलीय दबाव पर, ______ का घनत्व 1.87 kg/m<sup>3</sup> है, जो हवा से 1.5 गुना भारी है और 31&deg;C के महत्वपूर्ण तापमान से नीचे तरल के रूप में मौजूद होता है।</p>",
                    options_en: ["<p>nitrogen</p>", "<p>carbon dioxide</p>", 
                                "<p>lithium</p>", "<p>hydrogen</p>"],
                    options_hi: ["<p>नाइट्रोजन</p>", "<p>कार्बन डाइऑक्साइड</p>",
                                "<p>लिथियम</p>", "<p>हाइड्रोजन</p>"],
                    solution_en: "<p>95.(b) <strong>Carbon dioxide</strong> is a colorless, odorless, and tasteless gas. It is non-flammable and non-toxic. It is essential for plant life, but it is also a greenhouse gas that contributes to climate change. Carbon dioxide (CO<sub>2</sub> ) is used as a refrigerant, in fire extinguishers, for inflating life rafts and life jackets, blasting coal, foaming rubber and plastics, promoting the growth of plants in greenhouses, immobilizing animals before slaughter, and in carbonated beverages.</p>",
                    solution_hi: "<p>95.(b) <strong>कार्बन डाइऑक्साइड</strong> एक रंगहीन, गंधहीन और स्वादहीन गैस है। यह अज्वलनशील और अविषैला होता है। यह पौधों के जीवन के लिए आवश्यक है, लेकिन यह एक ग्रीनहाउस गैस भी है जो जलवायु परिवर्तन में योगदान करती है। कार्बन डाइऑक्साइड (CO<sub>2</sub>) का उपयोग रेफ्रिजरेंट के रूप में, आग बुझाने वाले यंत्रों में, लाइफ राफ्ट और लाइफ जैकेट को फुलाने के लिए, कोयले को फोड़ने, रबर और प्लास्टिक को फोम करने, ग्रीनहाउस में पौधों के विकास को बढ़ावा देने, वध से पहले जंतुओं को स्थिर करने और कार्बोनेटेड पेय पदार्थों में किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. According to the time by Rohan\'s watch, it is half past 6 and the watch&rsquo;s hands are pointing to the South. In which of the given directions will the minute-hand point AFTER exactly 24 hours?</p>",
                    question_hi: "<p>96. रोहन की घड़ी में समय के अनुसार साढ़े 6 बज रहे हैं और घड़ी की सुई दक्षिण दिशा की ओर इंगित कर रहे हैं। मिनट की सुई ठीक 24 घंटे के बाद किस दिशा में होगी?</p>",
                    options_en: ["<p>East</p>", "<p>West</p>", 
                                "<p>North</p>", "<p>South</p>"],
                    options_hi: ["<p>पूर्व</p>", "<p>पश्चिम</p>",
                                "<p>उत्तर</p>", "<p>दक्षिण</p>"],
                    solution_en: "<p>96.(d)<br>The time on Rohan\'s watch is half past 6 and the watch&rsquo;s hands are pointing to the South.<br>Now after 24 hours, the watch will repeat the same time and same direction of both the hour and minute hands.<br>So, the minute-hand will point towards the South- Direction after exactly 24 hours.</p>",
                    solution_hi: "<p>96.(d)<br>रोहन की घड़ी में साढ़े 6 बजे हैं और घड़ी की सुई दक्षिण दिशा की ओर इंगित कर रही है।<br>अब 24 घंटे के बाद घड़ी में घंटे और मिनट दोनों सुईयों एक ही समय और एक ही दिशा दोहराएगी।<br>तो, मिनट की सुई ठीक 24 घंटे के बाद दक्षिण दिशा की ओर इंगित करेगी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. Who was the first chairman of Finance Commission of India ?</p>",
                    question_hi: "<p>97. भारत के वित्त आयोग के पहले अध्यक्ष (chairman) कौन थे ?</p>",
                    options_en: ["<p>K Santhanam</p>", "<p>KC Neogy</p>", 
                                "<p>Ashok Kumar Chanda</p>", "<p>PV Rajamannar</p>"],
                    options_hi: ["<p>के. संथानम</p>", "<p>के.सी. नियोगी</p>",
                                "<p>अशोक कुमार चंदा</p>", "<p>पी.वी. राजमन्नार</p>"],
                    solution_en: "<p>97.(b) <strong>KC Neogy.</strong> The Finance Commission of India is an independent constitutional body that is set up by the President of India after every five years or earlier to make recommendations on the distribution of financial resources between the Union and the states. The recommendations of FCs are advisory in nature and not binding on the Union government. Article 280 - Finance Commission. Chairman of 15th Finance Commission of India - Shri N.K. Singh.</p>",
                    solution_hi: "<p>97.(b) <strong>के.सी. नियोगी</strong>। भारत का वित्त आयोग एक स्वतंत्र संवैधानिक निकाय है जिसकी स्थापना भारत के राष्ट्रपति द्वारा प्रत्येक पांच वर्ष या उससे पहले संघ और राज्यों के बीच वित्तीय संसाधनों के वितरण पर सिफारिशें करने के लिए की जाती है। FC की सिफारिशें प्रकृति में सलाहकारी हैं और केंद्र सरकार पर बाध्यकारी नहीं हैं। अनुच्छेद 280 - वित्त आयोग। भारत के 15वें वित्त आयोग के अध्यक्ष - श्री एन.के. सिंह.</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. The line graph shows the number of students who appeared in XYZ exam from various states during the period 2012-2018. What is the average number of students who appeared in the XYZ exam in the year 2012 from all the states together?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290138230.png\" alt=\"rId55\" width=\"253\" height=\"152\"></p>",
                    question_hi: "<p>98. रेखीय आलेख 2012-2018 की अवधि के दौरान विभिन्न राज्यों से XYZ परीक्षा में बैठने वाले छात्रों की संख्या को दर्शाता है। वर्ष 2012 में सभी राज्यों से XYZ परीक्षा में बैठने वाले छात्रों की औसत संख्या कितनी है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728290138230.png\" alt=\"rId55\" width=\"267\" height=\"160\"></p>",
                    options_en: ["<p>275</p>", "<p>270</p>", 
                                "<p>285</p>", "<p>280</p>"],
                    options_hi: ["<p>275</p>", "<p>270</p>",
                                "<p>285</p>", "<p>280</p>"],
                    solution_en: "<p>98.(a)<br>Average no. of students who appeared in the XYZ exam in the year 2012 <br>= <math display=\"inline\"><mfrac><mrow><mn>300</mn><mo>+</mo><mn>200</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= 275</p>",
                    solution_hi: "<p>98.(a)<br>वर्ष 2012 में XYZ परीक्षा में बैठने वाले छात्रों की औसत संख्या <br>= <math display=\"inline\"><mfrac><mrow><mn>300</mn><mo>+</mo><mn>200</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= 275</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. Which of the following is considered as a good indicator of economic growth ?</p>",
                    question_hi: "<p>99. निम्नलिखित में से किसे आर्थिक विकास का एक अच्छा संकेतक माना जाता है ?</p>",
                    options_en: ["<p>Steady increase in loan</p>", "<p>Steady increase in population</p>", 
                                "<p>Steady increase in the GDP</p>", "<p>Steady increase in international trade</p>"],
                    options_hi: ["<p>ऋृण में स्थिर वृद्धि</p>", "<p>जनसंख्या में स्थिर वृद्धि</p>",
                                "<p>सकल घरेलू उत्पाद (GDP) में स्थिर वृद्धि</p>", "<p>अंतर्राष्ट्रीय व्यापार में स्थिर वृद्धि</p>"],
                    solution_en: "<p>99.(c) <strong>Steady increase in the GDP</strong>. Economic growth means the increase in real national income of a country. <strong>Indicators of Economic Growth</strong>: Industrial Production, Real Gross Domestic Product, Per-Capita Income, Health, Education etc. GDP calculated at some constant set of prices is called Real GDP. GDP that takes into account the costs in terms of environmental pollution and exploitation of natural resources is called Green GDP. GDP deflator = (Nominal GDP / Real GDP) &times;&nbsp;100.</p>",
                    solution_hi: "<p>99.(c)<strong> सकल घरेलू उत्पाद (GDP) में स्थिर वृद्धि</strong>। आर्थिक वृद्धि का अर्थ है किसी देश की वास्तविक राष्ट्रीय आय में वृद्धि। <strong>आर्थिक विकास के संकेतक:</strong> औद्योगिक उत्पादन, वास्तविक सकल घरेलू उत्पाद, प्रति व्यक्ति आय, स्वास्थ्य, शिक्षा आदि। कीमतों के कुछ स्थिर सेट पर गणना की गई GDP को वास्तविक GDP कहा जाता है। GDP जो पर्यावरण प्रदूषण और प्राकृतिक संसाधनों के दोहन के संदर्भ में लागत को ध्यान में रखती है उसे ग्रीन GDP कहा जाता है। GDP डिफ्लेटर = (नॉमिनल GDP / वास्तविक GDP ) &times; 100।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. This question is based on the five, three-digit numbers given below.<br>(Example- 123 - First digit = 1, second digit = 2 and third digit = 3)<br>NOTE - All operations to be done from left to right.<br>What will be the resultant if the first digit of the second greatest number is added to the third digit of the smallest number?</p>",
                    question_hi: "<p>100. यह प्रश्न नीचे दी गई पांच, तीन अंकीय संख्याओं पर आधारित है।<br>(बाएं) 567 243 186 689 427 (दाएं)<br>(उदाहरण के लिए - 123 में पहला अंक = 1, दूसरा अंक = 2 और तीसरा अंक = 3 है) -<br>नोट- सभी संक्रियाएं बाएं से दाएं की ओर की जानी चाहिए।<br>उपरोक्त श्रेणी की दूसरी सबसे बड़ी संख्या के पहले अंक को सबसे छोटी संख्या के तीसरे अंक में जोड़ने पर प्राप्त परिणाम ज्ञात कीजिए ।</p>",
                    options_en: ["<p>12</p>", "<p>11</p>", 
                                "<p>10</p>", "<p>13</p>"],
                    options_hi: ["<p>12</p>", "<p>11</p>",
                                "<p>10</p>", "<p>13</p>"],
                    solution_en: "<p>100.(b) According to question, <br>the second greatest number = 567<br>the smallest number = 186<br>first digit of the second greatest number = 5<br>third digit of the smallest number = 6<br>Required sum = 5 + 6 = 11</p>",
                    solution_hi: "<p>100.(b) प्रश्न के अनुसार,<br>दूसरी सबसे बड़ी संख्या = 567<br>सबसे छोटी संख्या = 186<br>दूसरी सबसे बड़ी संख्या का पहला अंक = 5<br>सबसे छोटी संख्या का तीसरा अंक = 6<br>आवश्यक योग = 5 + 6 = 11</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>