<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;, <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the father of B&rsquo;, <br>&lsquo;A $ B&rsquo; means &lsquo;A is the son of B&rsquo;, <br>&lsquo;A % B&rsquo; means &lsquo;A is the sister of B&rsquo;, <br>&lsquo;A / B&rsquo; means &lsquo;A is the wife of B&rsquo; <br>&lsquo;A = B&rsquo; means &lsquo;A is the brother of B&rsquo;. <br>Which of the following means A is the daughter of K?<br>i. A + K / J &ndash; P = D <br>ii. J % P = K + A = D <br>iii. K + A / P &ndash; D % J <br>iv. D + K = A / P &ndash; J</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, <br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माँ है&rsquo;, <br>&lsquo;A &ndash; B&rsquo; का अर्थ है &lsquo;A, B का पिता है&rsquo;, <br>&lsquo;A $ B&rsquo; का अर्थ है &lsquo;A, B का पुत्र है&rsquo;, <br>&lsquo;A % B&rsquo; का अर्थ है &lsquo;A, B की बहन है&rsquo;, <br>&lsquo;A / B&rsquo; का अर्थ है &lsquo;A, B की पत्नी है&rsquo; <br>&lsquo;A = B&rsquo; का अर्थ है &lsquo;A, B का भाई है&rsquo;।<br>निम्नलिखित में से किसका अर्थ है कि A, K की पुत्री है? <br>i. A + K / J &ndash; P = D <br>ii. J % P = K + A = D <br>iii. K + A / P &ndash; D % J <br>iv. D + K = A / P &ndash; J</p>",
                    options_en: ["<p>ii</p>", "<p>i</p>", 
                                "<p>iii</p>", "<p>iv</p>"],
                    options_hi: ["<p>ii</p>", "<p>i</p>",
                                "<p>iii</p>", "<p>iv</p>"],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698203981.png\" alt=\"rId4\" width=\"139\" height=\"122\"><br>A is the daughter of K.</p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698203981.png\" alt=\"rId4\" width=\"139\" height=\"122\"><br>A, K की बेटी है.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements. <br><strong>Statements:</strong><br>No card is a parcel. <br>All cards are envelopes. <br>Some envelopes are bags.<br><strong>Conclusions:</strong> <br>I. All envelopes can never be parcels. <br>II. No card is a bag. <br>III. At least some bags are postcards.</p>",
                    question_hi: "<p>2. तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, हों तय कीजिए कि कौन-सा/से निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>कोई भी कार्ड, पार्सल नहीं है। <br>सभी कार्ड, लिफाफे हैं। <br>कुछ लिफाफे, बैग हैं। <br><strong>निष्कर्ष:</strong> <br>I. सभी लिफाफे कभी भी पार्सल नहीं हो सकते। <br>II. कोई भी कार्ड, बैग नहीं है। <br>III. कम से कम कुछ बैग, पोस्टकार्ड हैं।</p>",
                    options_en: ["<p>Only I follows</p>", "<p>Only II and III follow</p>", 
                                "<p>Only II follows</p>", "<p>Only I and III follow</p>"],
                    options_hi: ["<p>केवल निष्&zwj;कर्ष I अनुसरण करता हैं।</p>", "<p>केवल निष्&zwj;कर्ष II और III अनुसरण करते हैं।</p>",
                                "<p>केवल निष्&zwj;कर्ष II अनुसरण करता हैं।</p>", "<p>केवल निष्&zwj;कर्ष I और III अनुसरण करते हैं।</p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204121.png\" alt=\"rId5\" width=\"225\" height=\"63\"><br>Only I follows.</p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204242.png\" alt=\"rId6\" width=\"253\" height=\"71\"><br>केवल I अनुसरण करता है.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, &lsquo;what where how&rsquo; is written as &lsquo;aa dd ff&rsquo;; &lsquo;where there that&rsquo; is written as &lsquo;dd zz pp&rsquo; and &lsquo;which what here&rsquo; is written as &lsquo;ff kk ll&rsquo;. How is &lsquo;how&rsquo; written in that language?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में &lsquo;what where how&rsquo; को \'aa dd ff\' के रूप में लिखा जाता है; &lsquo;where there that&rsquo; को &lsquo;dd zz pp&rsquo; और \'which what here\' को \'ff kk ll\' लिखा जाता है। उसी भाषा में \'how\' कैसे लिखा जाता है?</p>",
                    options_en: ["<p>kk</p>", "<p>aa</p>", 
                                "<p>ff</p>", "<p>zz</p>"],
                    options_hi: ["<p>kk</p>", "<p>aa</p>",
                                "<p>ff</p>", "<p>zz</p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204370.png\" alt=\"rId7\" width=\"234\" height=\"97\"><br>The code of &lsquo;how&rsquo; = &lsquo;aa&rsquo;.</p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204370.png\" alt=\"rId7\" width=\"234\" height=\"97\"><br>\'how\' का कूट = \'aa\'।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204513.png\" alt=\"rId8\" width=\"115\" height=\"116\"></p>",
                    question_hi: "<p>4. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204513.png\" alt=\"rId8\" width=\"127\" height=\"128\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204613.png\" alt=\"rId9\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204719.png\" alt=\"rId10\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204854.png\" alt=\"rId11\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204957.png\" alt=\"rId12\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204613.png\" alt=\"rId9\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204719.png\" alt=\"rId10\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204854.png\" alt=\"rId11\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698205063.png\" alt=\"rId13\"></p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204854.png\" alt=\"rId11\"></p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698204854.png\" alt=\"rId11\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster. <br>MEK: NVP :: GYT: TBG :: JWH:?</p>",
                    question_hi: "<p>5. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है। <br>MEK: NVP :: GYT: TBG :: JWH:?</p>",
                    options_en: ["<p>QDS</p>", "<p>QDT</p>", 
                                "<p>PCR</p>", "<p>PCS</p>"],
                    options_hi: ["<p>QDS</p>", "<p>QDT</p>",
                                "<p>PCR</p>", "<p>PCS</p>"],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698205167.png\" alt=\"rId14\" width=\"126\" height=\"103\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698205340.png\" alt=\"rId15\" width=\"126\" height=\"103\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698205485.png\" alt=\"rId16\" width=\"127\" height=\"95\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698205579.png\" alt=\"rId17\" width=\"132\" height=\"111\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698205724.png\" alt=\"rId18\" width=\"132\" height=\"109\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698205860.png\" alt=\"rId19\" width=\"125\" height=\"97\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which of the following numbers will replace the question mark (?) in the given series? <br>1, 3, 10, 41, ?, 1237</p>",
                    question_hi: "<p>6.निम्नलिखित में से कौन सी संख्&zwj;या दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्&zwj;थान लेगी?<br>1, 3, 10, 41, ?, 1237</p>",
                    options_en: ["<p>210</p>", "<p>202</p>", 
                                "<p>206</p>", "<p>200</p>"],
                    options_hi: ["<p>210</p>", "<p>202</p>",
                                "<p>206</p>", "<p>200</p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206007.png\" alt=\"rId20\" width=\"251\" height=\"59\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206007.png\" alt=\"rId20\" width=\"251\" height=\"59\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>5 &ndash; 4 &divide; 9 + 80 &times; 10 = ?</p>",
                    question_hi: "<p>7. यदि \'+\' और \'&ndash;\' को परस्पर बदल दिया जाए और \'&times;\' और \'&divide;\' को परस्पर बदल दिया जाए, तो निम्नलिखित समीकरण में \'?\' के स्थान पर कितना मान आएगा? <br>5 &ndash; 4 &divide; 9 + 80 &times; 10 = ?</p>",
                    options_en: ["<p>33</p>", "<p>30</p>", 
                                "<p>35</p>", "<p>31</p>"],
                    options_hi: ["<p>33</p>", "<p>30</p>",
                                "<p>35</p>", "<p>31</p>"],
                    solution_en: "<p>7.(a)<br><strong>Given :</strong>- 5 &ndash; 4 &divide; 9 + 80 &times; 10<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get, <br>5 + 4 &times; 9 - 80 &divide; 10<br>5 + 36 - 8<br>41 - 8 = 33</p>",
                    solution_hi: "<p>7.(a)<br><strong>दिया गया है:- </strong>5 - 4 &divide; 9 + 80 &times; 10<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने के बाद हमें प्राप्त होता है।<br>5 + 4 &times; 9 - 80 &divide; 10<br>5 + 36 - 8<br>41 - 8 = 33</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option that represents the letters that when placed from left to right in the blanks below will complete the letter series. <br>L_P_QS_K_P_S_KP_Q_L_ _PQS</p>",
                    question_hi: "<p>8. उस विकल्प का चयन कीजिए जो उन अक्षरों को निरूपित करता है जिन्हें नीचे रिक्त स्थानों में बाएँ से दाएँ रखने पर अक्षर श्रृंखला पूरी हो जाएगी। <br>L_P_QS_K_P_S_KP_Q_L_ _PQS</p>",
                    options_en: ["<p>KPLPQLPSKP</p>", "<p>KPLPLQLSPK</p>", 
                                "<p>KPLPQQLPSK</p>", "<p>PKLPQPLSKP</p>"],
                    options_hi: ["<p>KPLPQLPSKP</p>", "<p>KPLPLQLSPK</p>",
                                "<p>KPLPQQLPSK</p>", "<p>PKLPQPLSKP</p>"],
                    solution_en: "<p>8.(a) L<span style=\"text-decoration: underline;\"><strong>K</strong></span>P<span style=\"text-decoration: underline;\"><strong>P</strong></span>QS/ <span style=\"text-decoration: underline;\"><strong>L</strong></span>K<span style=\"text-decoration: underline;\"><strong>P</strong></span>P<span style=\"text-decoration: underline;\"><strong>Q</strong></span>S/ <span style=\"text-decoration: underline;\"><strong>L</strong></span>KP<span style=\"text-decoration: underline;\"><strong>P</strong></span>Q<span style=\"text-decoration: underline;\"><strong>S</strong></span> / L<span style=\"text-decoration: underline;\"><strong>KP</strong></span>PQS</p>",
                    solution_hi: "<p>8.(a) L<span style=\"text-decoration: underline;\"><strong>K</strong></span>P<span style=\"text-decoration: underline;\"><strong>P</strong></span>QS/ <span style=\"text-decoration: underline;\"><strong>L</strong></span>K<span style=\"text-decoration: underline;\"><strong>P</strong></span>P<span style=\"text-decoration: underline;\"><strong>Q</strong></span>S/ <span style=\"text-decoration: underline;\"><strong>L</strong></span>KP<span style=\"text-decoration: underline;\"><strong>P</strong></span>Q<span style=\"text-decoration: underline;\"><strong>S</strong></span> / L<span style=\"text-decoration: underline;\"><strong>KP</strong></span>PQS</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;NIGHTMARES&rsquo; is re-arranged in the English alphabetical order from left to right?</p>",
                    question_hi: "<p>9. यदि शब्द &lsquo;NIGHTMARES&rsquo; के प्रत्येक अक्षर को अंग्रेजी वर्णमाला के क्रम में बाएँ से दाएँ पुनर्व्यवस्थित किया जाए तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी?</p>",
                    options_en: ["<p>One</p>", "<p>Three</p>", 
                                "<p>Two</p>", "<p>Four</p>"],
                    options_hi: ["<p>एक</p>", "<p>तीन</p>",
                                "<p>दो</p>", "<p>चार</p>"],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206101.png\" alt=\"rId21\" width=\"168\" height=\"71\"><br>The position of 4 letters remain unchanged.</p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206101.png\" alt=\"rId21\" width=\"168\" height=\"71\"><br>4 अक्षरों का स्थान अपरिवर्तित रहता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Which of the following terms will replace the question mark (?) in the given series? <br>YZUW, ?, QTQU, MQOT, INMS</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन-सा पद दी गई शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित करेगा? <br>YZUW, ?, QTQU, MQOT, INMS</p>",
                    options_en: ["<p>UWSV</p>", "<p>WUSV</p>", 
                                "<p>UWVS</p>", "<p>USWV</p>"],
                    options_hi: ["<p>UWSV</p>", "<p>WUSV</p>",
                                "<p>UWVS</p>", "<p>USWV</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206220.png\" alt=\"rId22\" width=\"236\" height=\"93\"></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206220.png\" alt=\"rId22\" width=\"236\" height=\"93\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Identify the option figure that when put in place of the question mark (?) will logically complete the series. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206364.png\" alt=\"rId23\" width=\"326\" height=\"78\"></p>",
                    question_hi: "<p>11. उस विकल्प आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206364.png\" alt=\"rId23\" width=\"326\" height=\"78\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206465.png\" alt=\"rId24\" width=\"79\" height=\"76\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206572.png\" alt=\"rId25\" width=\"86\" height=\"79\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206662.png\" alt=\"rId26\" width=\"90\" height=\"88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206789.png\" alt=\"rId27\" width=\"83\" height=\"82\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206465.png\" alt=\"rId24\" width=\"82\" height=\"79\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206572.png\" alt=\"rId25\" width=\"83\" height=\"76\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206662.png\" alt=\"rId26\" width=\"82\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206789.png\" alt=\"rId27\" width=\"83\" height=\"82\"></p>"],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206465.png\" alt=\"rId24\" width=\"81\" height=\"78\"></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206465.png\" alt=\"rId24\" width=\"81\" height=\"78\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong> <br>Some tears are drops. <br>Some drops are streams. <br>All streams are rivers.<br><strong>Conclusions :</strong> <br>I. All tears can never be rivers. <br>II. Some drops are rivers. <br>III. All tears being streams is a possibility.</p>",
                    question_hi: "<p>12. तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन-सा/से निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन :</strong> <br>कुछ आंसू, बूंदें हैं। <br>कुछ बूंदें, धाराएं हैं। <br>सभी धाराएं, नदियां हैं। <br><strong>निष्कर्ष :</strong> <br>I. सभी आंसू कभी भी नदियां नहीं हो सकते हैं। <br>II. कुछ बूंदें, नदियां हैं। <br>III. सभी आंसुओं के धाराएं होने की संभावना हैं।</p>",
                    options_en: ["<p>Both I and II conclusion follow</p>", "<p>Only conclusion I follows</p>", 
                                "<p>Only conclusion III follows</p>", "<p>Both II and III conclusion follow</p>"],
                    options_hi: ["<p>निष्&zwj;कर्ष I और II दोनों अनुसरण करते हैं।</p>", "<p>केवल निष्&zwj;कर्ष I अनुसरण करता है।</p>",
                                "<p>केवल निष्&zwj;कर्ष III अनुसरण करता हैं।</p>", "<p>निष्&zwj;कर्ष II और III दोनों अनुसरण करते हैं।</p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698206915.png\" alt=\"rId28\" width=\"272\" height=\"71\"><br>Both II and III conclusion follow.</p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207044.png\" alt=\"rId29\" width=\"282\" height=\"78\"><br>निष्कर्ष II और III दोनों अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the correct mirror image of the given figure when the mirror is placed at MN as shown.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207176.png\" alt=\"rId30\" width=\"97\" height=\"96\"></p>",
                    question_hi: "<p>13. जब दर्पण को दर्शाए गए अनुसार MN पर रखा जाता है, तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207176.png\" alt=\"rId30\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207273.png\" alt=\"rId31\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207378.png\" alt=\"rId32\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207534.png\" alt=\"rId33\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207627.png\" alt=\"rId34\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207273.png\" alt=\"rId31\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207378.png\" alt=\"rId32\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207534.png\" alt=\"rId33\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207627.png\" alt=\"rId34\"></p>"],
                    solution_en: "<p>13.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207627.png\" alt=\"rId34\"></p>",
                    solution_hi: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207627.png\" alt=\"rId34\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, \'NAME&rsquo; is written as \'FNBO\' and \'NANO\' is written as \'POBO&rsquo;. How will \'NAIL&rsquo; be written in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'NAME&rsquo; को \'FNBO\' लिखा जाता है और &lsquo;NANO\' को \'POBO&rsquo; लिखा जाता है। इसी कूट भाषा में \'NAIL&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>MJOB</p>", "<p>MJBO</p>", 
                                "<p>MOJB</p>", "<p>MOBJ</p>"],
                    options_hi: ["<p>MJOB</p>", "<p>MJBO</p>",
                                "<p>MOJB</p>", "<p>MOBJ</p>"],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207756.png\" alt=\"rId35\" width=\"101\" height=\"113\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207873.png\" alt=\"rId36\" width=\"107\" height=\"118\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208023.png\" alt=\"rId37\" width=\"103\" height=\"116\"></p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207756.png\" alt=\"rId35\" width=\"105\" height=\"117\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698207873.png\" alt=\"rId36\" width=\"107\" height=\"118\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208023.png\" alt=\"rId37\" width=\"103\" height=\"116\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Which of the four options will replace the question mark (?) in the following series? <br>UE 88, VG 84, WI 80, XK76, ?</p>",
                    question_hi: "<p>15.चार विकल्पों में से कौन सा विकल्प निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आएगा?<br>UE 88, VG 84, WI 80, XK76, ?</p>",
                    options_en: ["<p>YN73</p>", "<p>YM72</p>", 
                                "<p>ZN71</p>", "<p>ZM72</p>"],
                    options_hi: ["<p>YN73</p>", "<p>YM72</p>",
                                "<p>ZN71</p>", "<p>ZM72</p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208170.png\" alt=\"rId38\" width=\"294\" height=\"88\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208170.png\" alt=\"rId38\" width=\"294\" height=\"88\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. 19 is related to 209 following a certain logic. Following the same logic, 27 is related to 297. To which of the following is 61 related, following the same logic? <br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>16. एक निश्चित तर्क का अनुसरण करते हुए 19, 209 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 27, 297 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 61 निम्नलिखित में से किससे संबंधित है? <br>(<strong>नोट: </strong>संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>653</p>", "<p>671</p>", 
                                "<p>571</p>", "<p>600</p>"],
                    options_hi: ["<p>653</p>", "<p>671</p>",
                                "<p>571</p>", "<p>600</p>"],
                    solution_en: "<p>16.(b) <strong>Logic:-</strong> 1st number &times; 11 = 2nd number<br>(19, 209) :- 19 &times; 11 = 209<br>(27, 297) :- 27 &times; 11 = 297<br>Similarly,<br>(61 , 671) :- 61 &times; 11 = 671</p>",
                    solution_hi: "<p>16.(b) <strong>तर्क:-</strong> पहली संख्या &times; 11 = दूसरी संख्या<br>(19, 209) :- 19 &times; 11 = 209<br>(27, 297) :- 27 &times; 11 = 297<br>इसी प्रकार,<br>(61 , 671) :- 61 &times; 11 = 671</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Six letters Q, Z, V, T, L and A are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to Q. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208291.png\" alt=\"rId39\" width=\"173\" height=\"83\"></p>",
                    question_hi: "<p>17. एक पासे के विभिन्न फलकों पर छह अक्षर Q, Z, V, T, L और A लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। Q के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208291.png\" alt=\"rId39\" width=\"173\" height=\"83\"></p>",
                    options_en: ["<p>T</p>", "<p>V</p>", 
                                "<p>Z</p>", "<p>A</p>"],
                    options_hi: ["<p>T</p>", "<p>V</p>",
                                "<p>Z</p>", "<p>A</p>"],
                    solution_en: "<p>17.(a) From both the dice the opposite face are <br>Z &harr; L , Q &harr; T , V &harr; A</p>",
                    solution_hi: "<p>17.(a) दोनों पासों के विपरीत फलक हैं<br>Z &harr; L , Q &harr; T , V &harr; A</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In the following number-pairs, the second number is obtained by applying certain mathematical operations to the first number. In three of the four pairs, the same pattern is applied and hence they form a group. Select the number-pair that DOES NOT belong to this group. <br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>18. निम्नलिखित संख्या-युग्मों में, पहली संख्या पर कुछ गणितीय संक्रियाएँ लागू करके दूसरी संख्या प्राप्त की जाती है। चार में से तीन युग्मों में, समान स्वरूप लागू किया गया है और इसलिए वे एक समूह बनाते हैं। उस संख्या-युग्म का चयन कीजिए जो इस समूह से संबंधित नहीं है। <br>(<strong>नोट: </strong>संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियााऍं की जानी चाहिए। उदाहरण के लिए, 13 को लीजिए &ndash; 13 पर संक्रियाऍं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में विभक्त करने और फिर 1 और 3 पर गणितीय संक्रियाऍं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>335 - 119</p>", "<p>358 - 152</p>", 
                                "<p>317 - 101</p>", "<p>308 - 92</p>"],
                    options_hi: ["<p>335 - 119</p>", "<p>358 - 152</p>",
                                "<p>317 - 101</p>", "<p>308 - 92</p>"],
                    solution_en: "<p>18.(b)<strong> Logic:- </strong>(1st number - 2nd number) = 216<br>(335 , 119) :- (335 - 119) = 216<br>(317 , 101) :- (317 - 101) = 216<br>(308 , 92) :- (308 - 92) = 216<br>But,<br>(358 , 152) :- (358 - 152) = 206</p>",
                    solution_hi: "<p>18.(b) <strong>तर्क:- </strong>(पहली संख्या - दूसरी संख्या) = 216<br>(335 , 119) :- (335 - 119) = 216<br>(317 , 101) :- (317 - 101) = 216<br>(308 , 92) :- (308 - 92) = 216<br>लेकिन,<br>(358 , 152) :- (358 - 152) = 206</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Identify the figure from the given options, which when put in place of the question mark (?), will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208442.png\" alt=\"rId40\" width=\"335\" height=\"76\"></p>",
                    question_hi: "<p>19. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए, जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर,शृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208442.png\" alt=\"rId40\" width=\"335\" height=\"76\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208540.png\" alt=\"rId41\" width=\"99\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208641.png\" alt=\"rId42\" width=\"106\" height=\"101\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208759.png\" alt=\"rId43\" width=\"100\" height=\"101\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208897.png\" alt=\"rId44\" width=\"102\" height=\"102\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208540.png\" alt=\"rId41\" width=\"103\" height=\"96\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208641.png\" alt=\"rId42\" width=\"100\" height=\"95\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208759.png\" alt=\"rId43\" width=\"101\" height=\"102\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208897.png\" alt=\"rId44\" width=\"101\" height=\"101\"></p>"],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208641.png\" alt=\"rId42\" width=\"103\" height=\"98\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698208641.png\" alt=\"rId42\" width=\"103\" height=\"98\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged? <br>4515 &times; 5 &ndash; 431 &divide; 3 + 821 = ?</p>",
                    question_hi: "<p>20. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा? <br>4515 &times; 5 &ndash; 431 &divide; 3 + 821 = ?</p>",
                    options_en: ["<p>1575</p>", "<p>1335</p>", 
                                "<p>1375</p>", "<p>1775</p>"],
                    options_hi: ["<p>1575</p>", "<p>1335</p>",
                                "<p>1375</p>", "<p>1775</p>"],
                    solution_en: "<p>20.(c) <strong>Given:-</strong> 4515 &times; 5 - 431 &divide; 3 + 821<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get,<br>4515 &divide; 5 + 431 &times; 3 - 821<br>903 + 1293 - 821<br>2196 - 821 = 1375</p>",
                    solution_hi: "<p>20.(c) <strong>दिया गया है:-</strong> 4515 &times; 5 - 431 &divide; 3 + 821<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने के बाद हमें प्राप्त होता है,<br>4515 &divide; 5 + 431 &times; 3 - 821<br>903 + 1293 - 821<br>2196 - 821 = 1375</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. How many triangles are there in the given figure? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209063.png\" alt=\"rId45\" width=\"123\" height=\"113\"></p>",
                    question_hi: "<p>21. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209063.png\" alt=\"rId45\" width=\"123\" height=\"113\"></p>",
                    options_en: ["<p>21</p>", "<p>20</p>", 
                                "<p>19</p>", "<p>18</p>"],
                    options_hi: ["<p>21</p>", "<p>20</p>",
                                "<p>19</p>", "<p>18</p>"],
                    solution_en: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209220.png\" alt=\"rId46\" width=\"173\" height=\"163\"><br>There are 21 triangles<br>ABD, BCH, DEF, FGH, B&rsquo;FH, HIJ, IJK,HJK, PLN, LPO, LMN, LON, PQO , QOR, RTS,RSU, TUS, USV, WXY WXC&rsquo;, XZA&rsquo; .</p>",
                    solution_hi: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209220.png\" alt=\"rId46\" width=\"173\" height=\"163\"><br>21 त्रिभुज हैं<br>ABD, BCH, DEF, FGH, B&rsquo;FH, HIJ, IJK,HJK, PLN, LPO, LMN, LON, PQO , QOR, RTS,RSU, TUS, USV, WXY WXC&rsquo;, XZA&rsquo; .</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. 31 is related to 152 by certain logic. Following the same logic, 47 is related to 168. To which of the following is 66 related, following the same logic? (<strong>NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>22. 31 एक निश्चित तर्क का अनुसरण करते हुए 152 से संबंधित है। इसी तर्क का अनुसरण करते हुए 47, 168 से संबंधित है। समान तर्क का अनुसरण करते हुए, 66 निम्नलिखित में से किससे संबंधित है? <br><strong>नोट:</strong> संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>180</p>", "<p>190</p>", 
                                "<p>185</p>", "<p>187</p>"],
                    options_hi: ["<p>180</p>", "<p>190</p>",
                                "<p>185</p>", "<p>187</p>"],
                    solution_en: "<p>22.(d) <strong>Logic:-</strong> (1st number - 2nd number) = 121<br>(152 , 31):- (152 - 31) = 121<br>(168, 47):- (168 - 47) = 121<br>Similarly,<br>(187 , 66):- (187 - 66) = 121</p>",
                    solution_hi: "<p>22.(d) <strong>तर्क:-</strong> (पहली संख्या - दूसरी संख्या) = 121<br>(152 , 31):- (152 - 31) = 121<br>(168, 47):- (168 - 47) = 121<br>इसी प्रकार,<br>(187 , 66):- (187 - 66) = 121</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. The question contains pairs of words that are related to each other in a certain way. Three of the following four word pairs are alike as these have the same relationship and thus form a group. Which word pair is the one that DOES NOT belong to that group?<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>23. प्रश्न में ऐसे शब्द युग्म हैं जो एक निश्चित तरीके से एक-दूसरे से संबंधित हैं। निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म एक समान हैं क्योंकि इनमें संबंध समान है और इस प्रकार वे एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है? <br>(शब्दों को अर्थपूर्ण अंग्रेजी/हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों / व्यंजनों / स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं होने चाहिए।)</p>",
                    options_en: ["<p>Triangle - 3 sides</p>", "<p>Pentagon - 5 sides</p>", 
                                "<p>Square - 4 sides</p>", "<p>Hexagon - 8 sides</p>"],
                    options_hi: ["<p>त्रिभुज - 3 भुजाएँ</p>", "<p>पंचभुज - 5 भुजाएँ</p>",
                                "<p>वर्ग - 4 भुजाएँ</p>", "<p>षट्भुज - 8 भुजाएँ</p>"],
                    solution_en: "<p>23.(d) Triangles have 3 sides , Pentagon has 5 sides, Square has 4 sides, but Hexagon has 6 sides, not 8.</p>",
                    solution_hi: "<p>23.(d) त्रिभुज की 3 भुजाएँ होती हैं, पंचकोण की 5 भुजाएँ होती हैं, वर्ग की 4 भुजाएँ होती हैं, लेकिन षट्भुज की 6 भुजाएँ होती हैं, 8 नहीं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers. <br>(149, 213) <br>(168, 232)<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>24. उस विकल्प का चयन कीजिए, जिसमें संख्याएँ वही संबंध साझा करती हैं, जो संख्याओं के दिए गए युग्म द्वारा साझा किया गया है। <br>(149, 213) <br>(168, 232) <br>(नोट: पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(162, 222)</p>", "<p>(137, 211)</p>", 
                                "<p>(153, 217)</p>", "<p>(144, 198)</p>"],
                    options_hi: ["<p>(162, 222)</p>", "<p>(137, 211)</p>",
                                "<p>(153, 217)</p>", "<p>(144, 198)</p>"],
                    solution_en: "<p>24.(c)<strong> Logic :- </strong>(2nd number - 1st number) = 64<br>(149, 213) :- (213 - 149) = 64<br>(168 , 232):- (232 - 168) = 64<br>Similarly,<br>(153, 217):- (217 - 153) = 64</p>",
                    solution_hi: "<p>24.(c)<strong> तर्क :-</strong> (दूसरी संख्या - पहली संख्या) = 64<br>(149, 213) :- (213 - 149) = 64<br>(168 , 232):- (232 - 168) = 64<br>इसी प्रकार,<br>(153, 217):- (217 - 153) = 64</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Six numbers 21, 22, 23, 24, 25 and 26 are written on different faces of a dice. Three positions of this dice are shown in the figure. Find the number on the face opposite to 24. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209408.png\" alt=\"rId47\" width=\"272\" height=\"96\"></p>",
                    question_hi: "<p>25. एक पासे के विभिन्न फलकों पर छ: संख्याएँ 21, 22, 23, 24, 25 और 26 लिखी गई हैं। नीचे चित्र में इस पासे की तीन स्थितियाँ दिखाई गई है। संख्या 24 के विपरीत फलक पर कौन-सी संख्या है? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209408.png\" alt=\"rId47\" width=\"272\" height=\"96\"></p>",
                    options_en: ["<p>22</p>", "<p>21</p>", 
                                "<p>26</p>", "<p>25</p>"],
                    options_hi: ["<p>22</p>", "<p>21</p>",
                                "<p>26</p>", "<p>25</p>"],
                    solution_en: "<p>25.(b) From 2nd and 3rd dice 25 and 22 are common, so the opposite face of 24 is 21.</p>",
                    solution_hi: "<p>25.(b) दूसरे और तीसरे पासे से 25 और 22 उभयनिष्ठ हैं, इसलिए 24 का विपरीत फलक 21 है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Who among the following has authored the play &lsquo;Nil Darpan&rsquo;?</p>",
                    question_hi: "<p>26. निम्नलिखित में से किसने \'नील दर्पण\' नाटक लिखा है?</p>",
                    options_en: ["<p>Motilal Nehru</p>", "<p>Chittaranjan Das</p>", 
                                "<p>Dinabandhu Mitra</p>", "<p>Sarojini Naidu</p>"],
                    options_hi: ["<p>मोतीलाल नेहरू</p>", "<p>चितरंजन दास</p>",
                                "<p>दीनबंधु मित्र</p>", "<p>सरोजिनी नायडू</p>"],
                    solution_en: "<p>26.(c) <strong>Dinabandhu Mitra.</strong> Nil Darpan is a Bengali-language play written in 1858-59 by Dinabandhu Mitra, depicting the oppression of indigo farmers by British planters in Bengal. His other works include &lsquo;Nabin Tapasvini&rsquo;, &lsquo;Biye Pagla Budo&rsquo;, &lsquo;Sadhabar Ekadashi&rsquo;, and &lsquo;Kamale Kamini&rsquo;. Other authors and their works : Sarojini Naidu - &lsquo;The Golden Threshold&rsquo;, &lsquo;The Bird of Time&rsquo;. Chittaranjan Das - &lsquo;India for Indians&rsquo;, &lsquo;Freedom Through Disobedience&rsquo;.</p>",
                    solution_hi: "<p>26.(c) <strong>दीनबंधु मित्र ।</strong> नील दर्पण 1858-59 में दीनबंधु मित्रा द्वारा लिखा गया एक बंगाली भाषा का नाटक है, जिसमें बंगाल में ब्रिटिश बागान मालिकों द्वारा नील की खेती करने वाले किसानों पर किये गए अत्याचार को दर्शाया गया है। उनकी अन्य कृतियों में &lsquo;नबीन तपस्विनी&rsquo;, &lsquo;बीये पगला बुड़ो&rsquo;, &lsquo;सदाबर एकादशी&rsquo; और &lsquo;कामले कामिनी&rsquo; शामिल हैं। अन्य लेखक एवं उनकी कृतियाँ: सरोजिनी नायडू - &lsquo;द गोल्डन थ्रेशोल्ड&rsquo;, &lsquo;द बर्ड ऑफ़ टाइम&rsquo;। चित्तरंजन दास - &lsquo;इंडिया फॉर इंडियंस&rsquo;, &lsquo;फ्रीडम थ्रू डिसओबिडिएंस&rsquo;।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following awards was won by Lata Mangeshkar in the year 2001?</p>",
                    question_hi: "<p>27. लता मंगेशकर को वर्ष 2001 में निम्नलिखित में से किस पुरस्कार से सम्&zwj;मानित किया गया था?</p>",
                    options_en: ["<p>Filmfare Lifetime Achievement Award</p>", "<p>Padma Vibhushan</p>", 
                                "<p>Dadasaheb Phalke Award</p>", "<p>Bharat Ratna</p>"],
                    options_hi: ["<p>फिल्मफेयर लाइफटाइम अचीवमेंट अवार</p>", "<p>पद्म विभूषण</p>",
                                "<p>दादा साहेब फाल्के पुरस्कार</p>", "<p>भारत रत्न</p>"],
                    solution_en: "<p>27.(d) <strong>Bharat Ratna.</strong> Lata Mangeshkar was an Indian playback singer born in Indore. She received the Padma Bhushan in 1969, the Padma Vibhushan in 1999, the Dadasaheb Phalke Award in 1989, and the \'Officier de la Legion d\'Honneur,\' France\'s highest civilian award, in 2009. She is also renowned for singing the famous song \'Aye mere watan ke logo&rsquo;.</p>",
                    solution_hi: "<p>27.(d) <strong>भारत रत्न।</strong> लता मंगेशकर इंदौर में जन्मी एक भारतीय पार्श्व गायिका थीं। उन्हें 1969 में पद्म भूषण, 1999 में पद्म विभूषण, 1989 में दादा साहब फाल्के पुरस्कार और 2009 में फ्रांस के सर्वोच्च नागरिक सम्मान \'ऑफिसियर डे ला लीजन डी\'होनूर\' से सम्मानित किया गया था। वह प्रसिद्ध गीत \'ऐ मेरे वतन के लोगों\' गाने के लिए भी प्रसिद्ध हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following is a correct order of basicity?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सा क्षारकता का सही क्रम है?</p>",
                    options_en: ["<p>LiOH&gt;NaOH&gt;KOH&gt;CsOH</p>", "<p>LiOH&gt;KOH&gt;CsOH&gt;NaOH</p>", 
                                "<p>KOH&gt;CsOH&gt;NaOH&gt;LiOH</p>", "<p>CsOH&gt;KOH&gt;NaOH&gt;LiOH</p>"],
                    options_hi: ["<p>LiOH&gt;NaOH&gt;KOH&gt;CsOH</p>", "<p>LiOH&gt;KOH&gt;CsOH&gt;NaOH</p>",
                                "<p>KOH&gt;CsOH&gt;NaOH&gt;LiOH</p>", "<p>CsOH&gt;KOH&gt;NaOH&gt;LiOH</p>"],
                    solution_en: "<p>28.(d) <strong>CsOH&gt;KOH&gt;NaOH&gt;LiOH.</strong> This order is due to the increasing size of the alkali metal ions, which leads to better solvation and greater basicity. As you move down the group, the basicity increases because the larger alkali metal ions (like Cs⁺) can more easily dissociate and interact with water compared to the smaller ions (like Li⁺).</p>",
                    solution_hi: "<p>28.(d) <strong>CsOH&gt;KOH&gt;NaOH&gt;LiOH.</strong> यह क्रम क्षारीय धातु आयनों के बढ़ते आकार के कारण है, जो बेहतर विलयन और अधिक क्षारीयता की ओर ले जाता है। जैसे-जैसे समूह में नीचे की ओर जाते हैं, क्षारीयता बढ़ती जाती है क्योंकि बड़े क्षारीय धातु आयन (जैसे Cs⁺) छोटे आयनों (जैसे Li⁺) की तुलना में अधिक आसानी से विघटित हो सकते हैं और जल के साथ अंतःक्रिया कर सकते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who founded the Prarthana Samaj in Mumbai in 1867?</p>",
                    question_hi: "<p>29. 1867 में मुंबई में प्रार्थना-समाज की स्थापना किसने की थी?</p>",
                    options_en: ["<p>Atmaram Pandurang</p>", "<p>Gopal Krishna Gokhale</p>", 
                                "<p>Shri Ram Bajpai</p>", "<p>Ram Mohan Roy</p>"],
                    options_hi: ["<p>आत्माराम पांडुरंग</p>", "<p>गोपाल कृष्ण गोखल</p>",
                                "<p>श्री राम बाजपेय</p>", "<p>राम मोहन राय</p>"],
                    solution_en: "<p>29.(a) <strong>Atmaram Pandurang</strong>. Prarthana Samaj was founded with an aim to make people believe in one God and worship only one God. Other Societies and Founders: Brahmo Samaj (1828) - Ram Mohan Roy, Servants of India Society (1905) - Gopal Krishna Gokhale, Seva Samiti Boy Scouts Association (1914) - Shri Ram Bajpai.</p>",
                    solution_hi: "<p>29.(a) <strong>आत्माराम पांडुरंग ।</strong> प्रार्थना समाज की स्थापना लोगों को एक ईश्वर में विश्वास करने और केवल एक ईश्वर की पूजा करने के उद्देश्य से की गई थी। अन्य समाज एवं संस्थापक : ब्रह्म समाज (1828) - राम मोहन रॉय, सर्वेंट्स ऑफ इंडिया सोसाइटी (1905) - गोपाल कृष्ण गोखले, सेवा समिति बॉय स्काउट्स एसोसिएशन (1914) - श्री राम बाजपेयी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who among the following formed the Bihar Provincial Kisan Sabha in 1929?</p>",
                    question_hi: "<p>30. निम्नलिखित में से किसके द्वारा 1929 में बिहार प्रांतीय किसान सभा का गठन किया गया था?</p>",
                    options_en: ["<p>Kunwar Singh</p>", "<p>JM Sengupta</p>", 
                                "<p>Jayprakash Narayan</p>", "<p>Swami Sahajanand Saraswati</p>"],
                    options_hi: ["<p>कुँवर सिंह</p>", "<p>जे.एम. सेनगुप्ता</p>",
                                "<p>जयप्रकाश नारायण</p>", "<p>स्वामी सहजानंद सरस्वती</p>"],
                    solution_en: "<p>30.(d) <strong>Swami Sahajanand Saraswati. </strong>The Bihar Provincial Kisan Sabha (BPKS) emerged in response to the agricultural crisis, worsened by the Great Depression of 1929. Swami Sahajanand Saraswati wrote \"The Other Side of the Shield\" and \"Rent Reduction in Bihar,\" addressing peasant struggles and land reforms. Jayaprakash Narayan founded the Congress Socialist Party (CSP) in 1934 and the Praja Socialist Party (PSP) in 1952, while Kunwar Singh was a key leader in the Indian Rebellion of 1857 from Bihar.</p>",
                    solution_hi: "<p>30.(d) <strong>स्वामी सहजानंद सरस्वती।</strong> बिहार प्रांतीय किसान सभा (BPKS) का उदय 1929 की महामंदी से उत्पन्न कृषि संकट के प्रति हुआ था। स्वामी सहजानंद सरस्वती ने किसान संघर्ष और भूमि सुधारों को संबोधित करते हुए \"द अदर साइड ऑफ़ द शील्ड\" और \"रेंट रिडक्शन इन बिहार\" नामक पुस्तकें लिखीं। जयप्रकाश नारायण ने 1934 में कांग्रेस सोशलिस्ट पार्टी (CSP) और 1952 में प्रजा सोशलिस्ट पार्टी (PSP) की स्थापना की, जबकि कुंवर सिंह बिहार से 1857 के भारतीय विद्रोह में एक प्रमुख नेता थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. In which city was the first golf club of India situated?</p>",
                    question_hi: "<p>31. भारत का पहला गोल्फ क्लब किस शहर में स्थित था?</p>",
                    options_en: ["<p>Shimla</p>", "<p>Gulmarg</p>", 
                                "<p>Mysore</p>", "<p>Kolkata </p>"],
                    options_hi: ["<p>शिमला</p>", "<p>गुलमर्ग</p>",
                                "<p>मैसूर</p>", "<p>कोलकाता</p>"],
                    solution_en: "<p>31.(d) <strong>Kolkata. </strong>The Royal Calcutta Golf Club, established in 1829, is the oldest golf club in India and the first outside Great Britain. Other Golf Clubs in India : Bombay Presidency Golf Club (1927), Patna Golf Club (1916), Coimbatore Golf Club (1985), Chandigarh Golf Club (1962). Golf terminologies : Ace, Break, Par, Fade, Slice, Hook.</p>",
                    solution_hi: "<p>31.(d) <strong>कोलकाता। </strong>1829 में स्थापित रॉयल कलकत्ता गोल्फ़ क्लब भारत का सबसे पुराना गोल्फ़ क्लब है और यह ग्रेट ब्रिटेन के बाहर पहला गोल्फ़ क्लब है। भारत में अन्य गोल्फ़ क्लब: बॉम्बे प्रेसीडेंसी गोल्फ़ क्लब (1927), पटना गोल्फ़ क्लब (1916), कोयंबटूर गोल्फ़ क्लब (1985), चंडीगढ़ गोल्फ़ क्लब (1962)। गोल्फ़ शब्दावली: ऐस, ब्रेक, पार, फ़ेड, स्लाइस, हुक।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which article has a similar provision to that of Article 32 and deals with writ jurisdiction?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन-सा अनुच्छेद रिट अधिकारिता से संबंधित है और अनुच्छेद 32 के समान प्रावधान रखता है?</p>",
                    options_en: ["<p>Article 227</p>", "<p>Article 228</p>", 
                                "<p>Article 225</p>", "<p>Article 226</p>"],
                    options_hi: ["<p>अनुच्छेद 227</p>", "<p>अनुच्छेद 228</p>",
                                "<p>अनुच्छेद 225</p>", "<p>अनुच्छेद 226</p>"],
                    solution_en: "<p>32.(d) <strong>Article 226</strong>. In India, writs are issued by the Supreme Court under Article 32 of the Constitution of India and by the High Courts under Article 226 of the Constitution of India. There are 5 types of writs issued in India - Habeas Corpus (to have the body of), Mandamus (we command), Certiorari (to be certified or to be informed), Prohibition (to forbid) and Quo-Warranto (by what authority or warrant).</p>",
                    solution_hi: "<p>32.(d) <strong>अनुच्छेद 226</strong>. भारत में, भारतीय संविधान के अनुच्छेद 32 के तहत सर्वोच्च न्यायालय द्वारा और भारतीय संविधान के अनुच्छेद 226 के तहत उच्च न्यायालयों द्वारा रिट जारी की जाती हैं। भारत में 5 प्रकार की रिट जारी की जाती हैं - बंदी प्रत्यक्षीकरण (शरीर प्रस्तुत करना), परमादेश (आदेश देने के लिए), उत्प्रेषण (प्रमाणित किया जाना या सूचित किया जाना), निषेध (मना करना) और अधिकार-पृच्छा (किस अधिकार या वारंट द्वारा)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Mahendravarman I was the ruler of which of the following dynasties?</p>",
                    question_hi: "<p>33. महेंद्रवर्मन प्रथम, निम्नलिखित में से किस राजवंश का शासक था?</p>",
                    options_en: ["<p>Pandya</p>", "<p>Chola</p>", 
                                "<p>Chalukya</p>", "<p>Pallava</p>"],
                    options_hi: ["<p>पंड्या</p>", "<p>चोल</p>",
                                "<p>चालुक्य</p>", "<p>पल्लव</p>"],
                    solution_en: "<p>33.(d) <strong>Pallava. </strong>Mahendravarman I ruled the southern portions of present-day Andhra region and northern regions of what forms present-day Tamil Nadu in India. He took up the title &lsquo;Chitrakarapuli&rsquo;, &lsquo;Matavilasa&rsquo;, and &lsquo;Vichirachita&rsquo;. The Pallava Dynasty was founded by Simhavishnu.</p>",
                    solution_hi: "<p>33.(d) <strong>पल्लव। </strong>महेंद्रवर्मन प्रथम ने वर्तमान आंध्र क्षेत्र के दक्षिणी भागों और भारत में वर्तमान तमिलनाडु के उत्तरी क्षेत्रों पर शासन किया। उन्होंने &lsquo;चित्रकारपुली&rsquo;, &lsquo;मातविलास&rsquo; और &lsquo;विचिरचिता&rsquo; की उपाधि धारण की थी। पल्लव राजवंश की स्थापना सिंहविष्णु ने की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What challenge does foreign investment often face in India?</p>",
                    question_hi: "<p>34. भारत में विदेशी निवेश को अधिकतर किस चुनौती का सामना करना पड़ता है?</p>",
                    options_en: ["<p>Excessive foreign competition</p>", "<p>Inconsistent regulatory environment</p>", 
                                "<p>Lack of skilled labour</p>", "<p>Lack of consumer base</p>"],
                    options_hi: ["<p>अत्यधिक विदेशी प्रतिस्पर</p>", "<p>अस्थिर विनियामक परिवेश</p>",
                                "<p>कुशल श्रमिक अभाव</p>", "<p>उपभोक्ता आधार अभाव</p>"],
                    solution_en: "<p>34.(b) <strong>Inconsistent regulatory environment </strong>can lead to difficulties in compliance and planning for investors, ultimately affecting their confidence and willingness to invest in the country. Foreign investment refers to when an investor from one country purchases ownership of an asset or business in another country.</p>",
                    solution_hi: "<p>34.(b) <strong>अस्थिर विनियामक परिवेश</strong> निवेशकों के लिए अनुपालन और योजना बनाने में कठिनाइयों का कारण बन सकता है, जो अंततः उनके आत्मविश्वास और देश में निवेश करने की इच्छा को प्रभावित कर सकता है। विदेशी निवेश से तात्पर्य तब होता है जब एक देश का निवेशक किसी दूसरे देश में किसी परिसंपत्ति या व्यवसाय का स्वामित्व खरीदता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who is the Union Minister of State (Independent Charge) for Science and Technology as of July 2023?</p>",
                    question_hi: "<p>35. जुलाई 2023 तक की स्थिति के अनुसार, केंद्रीय विज्ञान एवं प्रौद्योगिकी राज्य मंत्री (स्वतंत्र प्रभार) कौन हैं?</p>",
                    options_en: ["<p>Ramesh Pokhriyal</p>", "<p>Dharmendra Pradhan</p>", 
                                "<p>Ashwini Vaishnaw</p>", "<p>Jitendra Singh</p>"],
                    options_hi: ["<p>रमेश पोखरियाल</p>", "<p>धर्मेन्द्र प्रधान</p>",
                                "<p>अश्विनी वैष्णव</p>", "<p>जितेंद्र सिंह</p>"],
                    solution_en: "<p>35.(d) <strong>Jitendra Singh.</strong> Ministers of State (Independent Charge) as of August 2024 : Rao Inderjit Singh - Ministry of Statistics and Programme Implementation. Arjun Ram Meghwal - Ministry of Law and Justice. Jayant Chaudhary - Ministry of Skill Development and Entrepreneurship. Jadhav Prataprao Ganpatrao - Ministry of Ayurveda, Yoga and Naturopathy, Unani, Siddha and Homoeopathy (AYUSH).</p>",
                    solution_hi: "<p>35.(d) <strong>जितेंद्र सिंह।</strong> अगस्त 2024 तक राज्य मंत्री (स्वतंत्र प्रभार): राव इंद्रजीत सिंह - सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय। अर्जुन राम मेघवाल - कानून और न्याय मंत्रालय। जयंत चौधरी - कौशल विकास और उद्यमिता मंत्रालय। जाधव प्रतापराव गणपतराव - आयुर्वेद, योग और प्राकृतिक चिकित्सा, यूनानी, सिद्ध और होम्योपैथी (आयुष) मंत्रालय।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Lathmar Holi is primarily celebrated in the state of:</p>",
                    question_hi: "<p>36. लठमार होली मुख्य रूप से किस राज्य में खेली जाती है?</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Arunachal Pradesh</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Himachal Pradesh</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>अरुणाचल प्रदेश</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>हिमाचल प्रदेश</p>"],
                    solution_en: "<p>36.(c) <strong>Uttar Pradesh.</strong> Lathmar Holi is celebrated in the twin towns of Barsana and Nandgaon, also known as the towns of Radha and Krishna respectively. Famous festivals of Uttar Pradesh - Gaurav Mahotsav, Ramotsav, Taj Mahotsav, Ayodhya Deepotsav, Ganga Mahotsav. Arunachal Pradesh - Sangken, Mopin, Pongtu. Himachal Pradesh - Halda, Doongri, Chet Festival, Kullu Dussehra. Karnataka - Ugadi, Mysuru Dasara, Kambala, Gowri Habba.</p>",
                    solution_hi: "<p>36.(c) <strong>उत्तर प्रदेश।</strong> लठमार होली बरसाना और नंदगांव कस्बों में मनाई जाती है, जिन्हें क्रमशः राधा और कृष्ण की नगरी के रूप में भी जाना जाता है। उत्तर प्रदेश के प्रसिद्ध त्यौहार - गौरव महोत्सव, रामोत्सव, ताज महोत्सव, अयोध्या दीपोत्सव, गंगा महोत्सव। अरुणाचल प्रदेश - सांगकेन, मोपिन, पोंगटू। हिमाचल प्रदेश - हल्दा, डूंगरी, चेत महोत्सव, कुल्लू दशहरा। कर्नाटक - उगादि, मैसूर दशहरा, कंबाला, गौरी हब्बा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following is NOT a condition for the President&rsquo;s office in India?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन-सी भारत में राष्ट्रपति के पद के लिए एक शर्त नहीं है?</p>",
                    options_en: ["<p>He shall not be entitled, without payment of rent, to use his official residence</p>", "<p>He shall not be a member of either House of the Parliament.</p>", 
                                "<p>The allowances shall not be diminished during his term of office.</p>", "<p>He shall not hold any office of profit.</p>"],
                    options_hi: ["<p>वह किराए के भुगतान के बिना अपने आधिकारिक निवास का उपयोग करने का हकदार नहीं होगा।</p>", "<p>वह संसद के किसी भी सदन का सदस्य नहीं होगा।</p>",
                                "<p>उनके कार्यकाल के दौरान भत्तों में कमी नहीं की जाएगी।</p>", "<p>वह किसी लाभ के पद पर नहीं होगा।</p>"],
                    solution_en: "<p>37.(a) Article 59 of the Constitution of India outlines the conditions for the office of the President of India. Article 59 (3) - The President shall be entitled without payment of rent to the use of his official residences and shall be also entitled to such emoluments, allowances and privileges as may be determined by Parliament by law. Important Articles related to the President: Article 52 - The President of India. Article 54 - Election of President. Article 55 - Manner of election of President. Article 56 - Term of office of President.</p>",
                    solution_hi: "<p>37.(a) भारतीय संविधान के अनुच्छेद 59 में भारत के राष्ट्रपति के पद के लिए शर्तें बताई गई हैं। अनुच्छेद 59 (3) - राष्ट्रपति अपने आधिकारिक आवासों के उपयोग के लिए बिना किराया भुगतान के हकदार होंगे और ऐसी परिलब्धियों, भत्तों और विशेषाधिकारों के भी हकदार होंगे जो संसद द्वारा कानून द्वारा निर्धारित किए जा सकते। राष्ट्रपति से संबंधित महत्वपूर्ण अनुच्छेद: अनुच्छेद 52 - भारत का राष्ट्रपति । अनुच्छेद 54 - राष्ट्रपति का निर्वाचन । अनुच्छेद 55 - राष्ट्रपति के निर्वाचन की रीति । अनुच्छेद 56 - राष्ट्रपति की पदावधि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which Indian among the following has his name in Time Magazine&rsquo;s list of &lsquo;100 most influential people of 2021&rsquo;?</p>",
                    question_hi: "<p>38. टाइम मैगज़ीन की \'2021 के 100 सबसे प्रभावशाली लोगों\' की सूची में निम्नलिखित में से किस भारतीय का नाम है?</p>",
                    options_en: ["<p>Neeraj Chopra</p>", "<p>Narendra Modi</p>", 
                                "<p>Virat Kohli</p>", "<p>Amit Shah</p>"],
                    options_hi: ["<p>नीरज चोपड़ा</p>", "<p>नरेंद्र मोदी</p>",
                                "<p>विराट कोहली</p>", "<p>अमित शाह</p>"],
                    solution_en: "<p>38.(b) <strong>Narendra Modi</strong> is the 14th and current prime minister of India since 26 May 2014. He was the chief minister of Gujarat from 2001 to 2014. His awards : Order of Abdulaziz Al Saud (the highest Civilian Award of Saudi Arabia) in 2016; State Order of Ghazi Amir Amanullah Khan (the highest Civilian honor of Afghanistan) in 2016; Champions of the Earth Award in 2018; Order of St. Andrew award (the highest civilian award in Russia) in 2019 etc.</p>",
                    solution_hi: "<p>38.(b) <strong>नरेन्द्र मोदी </strong>26 मई 2014 से भारत के 14वें और वर्तमान प्रधान मंत्री हैं। वे 2001 से 2014 तक गुजरात के मुख्यमंत्री थे। उनको प्राप्त हुए पुरस्कार: 2016 में ऑर्डर ऑफ अब्दुलअजीज अल सऊद (सऊदी अरब का सर्वोच्च नागरिक पुरस्कार); 2016 में स्टेट ऑर्डर ऑफ गाजी अमीर अमानुल्लाह खान (अफगानिस्तान का सर्वोच्च नागरिक सम्मान); 2018 में चैंपियंस ऑफ द अर्थ अवार्ड; 2019 में ऑर्डर ऑफ सेंट एंड्रयू अवार्ड (रूस का सर्वोच्च नागरिक पुरस्कार) आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. When the analysis of population density is done by calculating it through net cultivated area, then the measure is termed as:</p>",
                    question_hi: "<p>39. जब जनसंख्या घनत्व का विश्लेषण निवल खेती योग्य क्षेत्र के माध्यम से गणना करके किया जाता है, तो उस माप को क्या कहा जाता है?</p>",
                    options_en: ["<p>Agricultural density</p>", "<p>Physiological density</p>", 
                                "<p>Gross density</p>", "<p>Net density</p>"],
                    options_hi: ["<p>कृषि घनत्व (Agricultural density)</p>", "<p>कार्यिकीय घनत्व (Physiological density)</p>",
                                "<p>सकल घनत्व (Gross density)</p>", "<p>निवल घनत्व (Net density)</p>"],
                    solution_en: "<p>39.(b) Physiological density. Density of population is expressed as the number of persons per unit area. Physiological density = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>p</mi><mi>o</mi><mi>p</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi></mrow><mrow><mi>n</mi><mi>e</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>t</mi><mi>i</mi><mi>v</mi><mi>a</mi><mi>t</mi><mi>e</mi><mi>d</mi><mi>&#160;</mi><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi></mrow></mfrac></math>. Agricultural density is the number of farmers per unit of arable land. Agricultural population includes cultivators and agricultural labourers and their family members.</p>",
                    solution_hi: "<p>39.(b) कार्यिकीय घनत्व (Physiological density)। जनसंख्या घनत्व को प्रति इकाई क्षेत्र में व्यक्तियों की संख्या के रूप में व्यक्त किया जाता है। कार्यिकीय घनत्व =<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mrow><mi>&#2358;&#2369;&#2342;&#2381;&#2343;</mi><mo>&#160;</mo><mi>&#2326;&#2375;&#2340;&#2368;</mi><mo>&#160;</mo><mi>&#2351;&#2379;&#2327;&#2381;&#2351;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</mi></mrow></mfrac></math> । कृषि घनत्व प्रति इकाई कृषि योग्य भूमि पर किसानों की संख्या है। कृषि जनसंख्या में किसान और कृषि मजदूर तथा उनके परिवार के सदस्य शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Mohan Veena player, Pandit Vishwa Mohan Bhatt won the ____________ Award in the year 1994.</p>",
                    question_hi: "<p>40. मोहन वीणा वादक, पंडित विश्व मोहन भट्ट ने वर्ष 1994 में ____________ पुरस्कार जीता।</p>",
                    options_en: ["<p>Sangita Kalanidhi</p>", "<p>Oscar</p>", 
                                "<p>Grammy</p>", "<p>Sangeet Natak Akademi</p>"],
                    options_hi: ["<p>संगीत कलानिधि</p>", "<p>ऑस्कर</p>",
                                "<p>ग्रैमी</p>", "<p>संगीत नाटक अकादमी</p>"],
                    solution_en: "<p>40.(c) <strong>Grammy.</strong> His music album with Ry Cooder, A Meeting by the River, won him a Grammy Award. He also received the Sangeet Natak Akademi Award in 1998, the Padma Shri in 2002, and the Padma Bhushan in 2017. Famous Mohan Veena players: Pandit Joydeep Mukherjee, Poly Varghese.</p>",
                    solution_hi: "<p>40.(c) <strong>ग्रैमी</strong>। राय कूडर के साथ उनके संगीत एल्बम, ए मीटिंग बाय द रिवर ने उन्हें ग्रैमी पुरस्कार दिलाया। उन्हें 1998 में संगीत नाटक अकादमी पुरस्कार, 2002 में पद्म श्री और 2017 में पद्म भूषण भी मिला। प्रसिद्ध मोहन वीणा वादक: पंडित जॉयदीप मुखर्जी, पॉली वर्गीस।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which plateaus are very fertile because they are rich in black soil that is very good for farming?</p>",
                    question_hi: "<p>41. कौन-से पठार बहुत उपजाऊ होते हैं क्योंकि वे काली मृदा में समृद्ध होते हैं जो खेती के लिए बहुत अच्छी है?</p>",
                    options_en: ["<p>African plateau</p>", "<p>Ethiopian plateau</p>", 
                                "<p>Katanga plateau</p>", "<p>Deccan lava plateau</p>"],
                    options_hi: ["<p>अफ्रीकी पठार</p>", "<p>इथियोपिया का पठार</p>",
                                "<p>कटंगा पठार</p>", "<p>दक्कन लावा पठार</p>"],
                    solution_en: "<p>41.(d) <strong>Deccan lava plateau</strong> is a volcanic plateau in west-central India that was formed by the solidification of lava from deep within the Earth. It is a triangular landmass that lies to the south of the river Narmada. The African plateau is famous for gold and diamond mining. The Katanga Plateau in the Democratic Republic of the Congo is known for its rich deposits of copper and uranium.</p>",
                    solution_hi: "<p>41.(d) <strong>दक्कन लावा पठार </strong>पश्चिम-मध्य भारत में एक ज्वालामुखीय पठार है जिसका निर्माण पृथ्वी के भीतर लावा के जमने से हुआ था। यह एक त्रिकोणीय भूभाग है जो नर्मदा नदी के दक्षिण में स्थित है। अफ्रीकी पठार सोने और हीरे के खनन के लिए प्रसिद्ध है। कांगो लोकतांत्रिक गणराज्य में कटंगा पठार तांबे और यूरेनियम के समृद्ध भंडार के लिए जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following pairs is INCORRECT regarding the grade of organisation and its example?</p>",
                    question_hi: "<p>42. संगठन के ग्रेड और उसके उदाहरण के संबंध में निम्नलिखित में से कौन सी जोड़ी गलत है?</p>",
                    options_en: ["<p>Cellular grade organisation - Sycon</p>", "<p>Protoplasmic grade organisation - Paramecium</p>", 
                                "<p>Cell-tissue grade organisation - Jellyfish</p>", "<p>Tissue-organ grade organisation - Euplectella</p>"],
                    options_hi: ["<p>कोशिकीय ग्रेड संगठन - साइकोन</p>", "<p>प्रोटोप्लाज्मिक ग्रेड संगठन - पैरामीशियम</p>",
                                "<p>कोशिका-ऊतक ग्रेड संगठन - जेलीफ़िश</p>", "<p>ऊतक-अंग ग्रेड संगठन - यूप्लेक्टेला</p>"],
                    solution_en: "<p>42.(d) Euplectella, is a genus of glass sponges which includes the well-known Venus\' Flower Basket, exhibits a cellular grade of organization rather than a tissue-organ grade. A cellular grade of organisation is an aggregation of cells that are functionally differentiated.</p>",
                    solution_hi: "<p>42.(d) यूप्लेक्टेला, ग्लास स्पंज की एक प्रजाति है जिसमें वीनस फ्लावर बास्केट शामिल है, जो ऊतक-अंग ग्रेड के बजाय सेलुलर ग्रेड का संगठन प्रदर्शित करता है। सेलुलर ग्रेड का संगठन कोशिकाओं का एक समूह है जो कार्यात्मक रूप से विभेदित होते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Who is the Chief Minister of Tamil Nadu as of July 2023?</p>",
                    question_hi: "<p>43. जुलाई 2023 तक की स्थिति के अनुसार, तमिलनाडु के मुख्यमंत्री कौन हैं?</p>",
                    options_en: ["<p>Pinarayi Vijayan</p>", "<p>M Yedurappa</p>", 
                                "<p>KN Nehru</p>", "<p>MK Stalin</p>"],
                    options_hi: ["<p>पिनाराई विजयन</p>", "<p>एम. येदुरप्पा</p>",
                                "<p>के.एन. नेहरू</p>", "<p>एम.के. स्टालिन</p>"],
                    solution_en: "<p>43.(d) <strong>MK Stalin</strong> is the 8th Chief Minister of Tamil Nadu. As of August 2024, the chief ministers of Indian states are: Pinarayi Vijayan (Kerala), Mamata Banerjee (West Bengal), Siddaramaiah (Karnataka), Pramod Sawant (Goa), Nara Chandrababu Naidu (Andhra Pradesh), and Pema Khandu (Arunachal Pradesh).</p>",
                    solution_hi: "<p>43.(d) <strong>एमके स्टालिन</strong> तमिलनाडु के 8वें मुख्यमंत्री हैं। अगस्त 2024 तक, भारतीय राज्यों के मुख्यमंत्री: पिनाराई विजयन (केरल), ममता बनर्जी (पश्चिम बंगाल), सिद्धारमैया (कर्नाटक), प्रमोद सावंत (गोवा), नारा चंद्रबाबू नायडू (आंध्र प्रदेश), और पेमा खांडू (अरुणाचल प्रदेश)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following states is the biggest producer of Pulses?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सा राज्य दालों का सबसे बड़ा उत्पादक है?</p>",
                    options_en: ["<p>Madhya Pradesh</p>", "<p>Haryana</p>", 
                                "<p>Punjab</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>मध्य प्रदेश</p>", "<p>हरियाण</p>",
                                "<p>पंजाब</p>", "<p>बिहार</p>"],
                    solution_en: "<p>44.(a) <strong>Madhya Pradesh.</strong> Based on the production estimates for the year 2022-23, Madhya Pradesh, Maharashtra and Rajasthan are the top three pulses producing states in the country. India is the largest producer of pulses in the world.</p>",
                    solution_hi: "<p>44.(a) <strong>मध्य प्रदेश। </strong>वर्ष 2022-23 के उत्पादन अनुमान के आधार पर मध्य प्रदेश, महाराष्ट्र और राजस्थान देश के शीर्ष तीन दलहन उत्पादक राज्य हैं। भारत दुनिया में दालों का सबसे बड़ा उत्पादक देश है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. In which year was Project Tiger launched in India?</p>",
                    question_hi: "<p>45. भारत में प्रोजेक्ट टाइगर (Project Tiger) किस वर्ष शुरू किया गया था?</p>",
                    options_en: ["<p>1985</p>", "<p>1973</p>", 
                                "<p>1972</p>", "<p>1970</p>"],
                    options_hi: ["<p>1985 में</p>", "<p>1973 में</p>",
                                "<p>1972 में</p>", "<p>1970 में</p>"],
                    solution_en: "<p>45.(b) On April 1, 1973, Project Tiger was officially launched at the Corbett Tiger Reserve, initially including nine tiger reserves. International Tiger Day is celebrated annually on July 29. The Wildlife Protection Act was passed in 1972. Projects in India related to animals :Project Hangul (1970), Project Crocodile (1975), Project Elephant (1992), Project Rhino (2005), Project Gangetic Dolphin - (2009), Project Snow Leopard (2009), Project Great Indian Bustard (2012).</p>",
                    solution_hi: "<p>45.(b) 1 अप्रैल, 1973 को कॉर्बेट टाइगर रिज़र्व में आधिकारिक तौर पर प्रोजेक्ट टाइगर की शुरुआत की गई थी, जिसमें शुरुआत में नौ टाइगर रिज़र्व शामिल थे। अंतर्राष्ट्रीय बाघ दिवस प्रत्येक वर्ष 29 जुलाई को मनाया जाता है। वन्यजीव संरक्षण अधिनियम 1972 ईस्वी में पारित किया गया था। भारत में जानवरों से संबंधित परियोजनाएँ: प्रोजेक्ट हंगुल (1970), प्रोजेक्ट क्रोकोडाइल (1975), प्रोजेक्ट एलीफेंट (1992), प्रोजेक्ट राइनो (2005), प्रोजेक्ट गैंगेटिक डॉल्फिन (2009), प्रोजेक्ट स्नो लेपर्ड (2009), प्रोजेक्ट ग्रेट इंडियन बस्टर्ड (2012) ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. The head office of Board of Control for Cricket in India (BCCI) is located in ________.</p>",
                    question_hi: "<p>46. भारतीय क्रिकेट कंट्रोल बोर्ड (BCCI) का मुख्यालय ________ में स्थित है।</p>",
                    options_en: ["<p>Mumbai</p>", "<p>Kolkata</p>", 
                                "<p>New Delhi</p>", "<p>Chennai</p>"],
                    options_hi: ["<p>मुंबई</p>", "<p>कोलकाता</p>",
                                "<p>नई दिल्ली</p>", "<p>चेन्नई</p>"],
                    solution_en: "<p>46.(a) <strong>Mumbai. </strong>BCCI is the principal national governing body of the sport of cricket in India established in 1928. RE Grant Govan was the first president and De Mello was the first Secretary of the BCCI. International Cricket Council (ICC) - Headquarters (Dubai, United Arab Emirates).</p>",
                    solution_hi: "<p>46.(a)<strong> मुंबई। </strong>BCCI भारत में क्रिकेट के खेल का प्रमुख राष्ट्रीय शासी निकाय है जिसकी स्थापना 1928 में हुई थी। आर.ई ग्रांट गोवन BCCI के पहले अध्यक्ष और डी मेलो पहले सचिव थे। अंतर्राष्ट्रीय क्रिकेट परिषद (ICC) - मुख्यालय (दुबई, संयुक्त अरब अमीरात)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following decomposition reactions is NOT a redox reaction?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन-सी अपघटन अभिक्रिया रेडॉक्स अभिक्रिया नहीं है?</p>",
                    options_en: ["<p>Decomposition of dihydrogen monoxide</p>", "<p>Decomposition of sodium hydride</p>", 
                                "<p>Decomposition of potassium chlorate</p>", "<p>Decomposition of calcium carbonate</p>"],
                    options_hi: ["<p>डाइहाइड्रोजन मोनोऑक्साइड का अपघटन</p>", "<p>सोडियम हाइड्राइड का अपघटन</p>",
                                "<p>पोटैशियम क्लोरेट का अपघटन</p>", "<p>कैल्शियम कार्बोनेट का अपघटन</p>"],
                    solution_en: "<p>47.(d) <strong>Decomposition of calcium carbonate.</strong> A redox reaction is defined as a reaction in which oxidation and reduction take place simultaneously. The decomposition of calcium carbonate (CaCO<sub>3</sub>) into calcium oxide (CaO) and carbon dioxide (CO<sub>2</sub>) does not involve a change in the oxidation states of the elements involved, making it a non-redox reaction.</p>",
                    solution_hi: "<p>47.(d) <strong>कैल्शियम कार्बोनेट का अपघटन।</strong> रेडॉक्स अभिक्रिया को ऐसी अभिक्रिया के रूप में परिभाषित किया जाता है जिसमें ऑक्सीकरण और अपचयन एक साथ होता है। कैल्शियम कार्बोनेट (CaCO<sub>3</sub>) का कैल्शियम ऑक्साइड (CaO) और कार्बन डाइऑक्साइड (CO<sub>2</sub>) में अपघटन होने पर इसमें शामिल तत्वों की ऑक्सीकरण अवस्था में कोई परिवर्तन नहीं होता है, जिससे यह एक नॉन-रेडॉक्स अभिक्रिया बन जाती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following statements best defines the monoecious?</p>",
                    question_hi: "<p>48. निम्नलिखित में से कौन सा कथन उभयलिंगाश्रयी (monoecious) को सर्वोत्तम रूप से परिभाषित करता है?</p>",
                    options_en: ["<p>A flower with both androecium and gynoecium</p>", "<p>A flower with dithecous</p>", 
                                "<p>A flower with gynoecium only</p>", "<p>A flower with androecium only</p>"],
                    options_hi: ["<p>पुमंग और जायांग दोनों से युक्&zwj;त फूल</p>", "<p>द्विकोष्ठी फूल</p>",
                                "<p>केवल जायांग वाला फूल</p>", "<p>केवल पुमंग वाला फूल</p>"],
                    solution_en: "<p>48.(a) Monoecious plants bear both purely male and purely female flowers. Examples include castor, cucumber, and maize. In contrast, dioecious plants have male and female flowers on separate plants, such as papaya.</p>",
                    solution_hi: "<p>48.(a) उभयलिंगाश्रयी पौधों में नर और मादा दोनों प्रकार के फूल होते हैं। इसके उदाहरणों में अरंडी, खीरा, और मक्का शामिल हैं। इसके विपरीत, द्विलिंगी पौधों में नर और मादा फूल अलग-अलग पौधों पर होते हैं, जैसे कि पपीता।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. ___________ is an industry association and self-regulatory organisation (SRO) whose primary objective is to work towards the robust development of the microfinance sector.</p>",
                    question_hi: "<p>49. ___________ एक उद्योग संघ और स्व-विनियामक संगठन (SRO) है जिसका प्राथमिक उद्देश्य सूक्ष्&zwj;मवित्&zwj;त क्षेत्र के सुदृढ़ विकास की दिशा में काम करना है।</p>",
                    options_en: ["<p>Self-help Group Association</p>", "<p>Microfinance Institutions Network</p>", 
                                "<p>Microfinance and Investments Regulatory Authority</p>", "<p>NABARD</p>"],
                    options_hi: ["<p>स्वयं सहायता समूह संघ</p>", "<p>सूक्ष्म वित्त संस्था नेटवर्क</p>",
                                "<p>सूक्ष्म वित्त एवं निवेश विनियामक प्राधिकरण</p>", "<p>नाबार्ड (NABARD)</p>"],
                    solution_en: "<p>49.(b) <strong>Microfinance Institutions Network</strong> (MFIN) was established in 2009. A microfinance institution is a provider of credit. However, the size of the loans are smaller than those granted by traditional banks. Reserve Bank of India (RBI) oversees microfinance institutions in India. MFIN examples Equitas Small Finance, ESAF Microfinance and Investments (P) Ltd, Fusion Microfinance Pvt Ltd, Annapurna Microfinance Pvt Ltd, BSS Microfinance Limited, Asirvad Microfinance Limited, Cashpor Micro Credit, Bandhan Financial Services Limited.</p>",
                    solution_hi: "<p>49.(b) <strong>माइक्रोफाइनेंस इंस्टीट्यूशंस नेटवर्क</strong> (MFIN) की स्थापना 2009 में की गई थी। माइक्रोफाइनेंस संस्थान ऋण प्रदाता है। हालाँकि, ऋणों का आकार पारंपरिक बैंकों द्वारा दिए जाने वाले ऋणों से छोटा होता है। भारतीय रिज़र्व बैंक (RBI) भारत में माइक्रोफाइनेंस संस्थानों की देखरेख करता है। MFIN के उदाहरण इक्विटास स्मॉल फाइनेंस, ESAF माइक्रोफाइनेंस एंड इन्वेस्टमेंट्स (P) लिमिटेड, फ्यूजन माइक्रोफाइनेंस प्राइवेट लिमिटेड, अन्नपूर्णा माइक्रोफाइनेंस प्राइवेट लिमिटेड, BSS माइक्रोफाइनेंस लिमिटेड, आशीर्वाद माइक्रोफाइनेंस लिमिटेड, कैशपोर माइक्रो क्रेडिट, बंधन फाइनेंशियल सर्विसेज लिमिटेड।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Details about Sudarshana lake is given in a rock inscription at Girnar (Junagarh), which was composed to record the achievements of the Shaka ruler _________.</p>",
                    question_hi: "<p>50. सुदर्शन झील का विवरण गिरनार (जूनागढ़) के एक शिलालेख में उल्लिखित हैं जिसकी रचना शक शासक _________की उपलब्धियों को दर्ज करने के लिए की गई थी।</p>",
                    options_en: ["<p>Rudrasimha III</p>", "<p>Rudradaman I</p>", 
                                "<p>Chashtana</p>", "<p>Maues</p>"],
                    options_hi: ["<p>रुद्रसिम्हा तृतीय</p>", "<p>रुद्रदामन प्रथम</p>",
                                "<p>चश्ताना</p>", "<p>माउज़</p>"],
                    solution_en: "<p>50.(b) <strong>Rudradaman I </strong>belonged to the line of the Saka rulers and was the grandson of Chastana, the founder of the Kardamaka dynasty. Junagadh inscription is written in the Sanskrit​ language. It is located near Girnar hill near Junagadh in Gujarat. The Junagadh rock contains inscriptions of Ashoka (one of the fourteen Major Rock Edicts of Ashoka), Rudradaman I and Skandagupta.</p>",
                    solution_hi: "<p>50.(b) <strong>रुद्रदामन प्रथम, </strong>शक शासकों की वंश से संबंधित थे और कार्दमक वंश के संस्थापक चष्टन के पौत्र थे। जूनागढ़ अभिलेख संस्कृत भाषा में लिखा गया है। यह गुजरात के जूनागढ़ के पास गिरनार पहाड़ी के निकट स्थित है। जूनागढ़ चट्टान में अशोक (अशोक के चौदह प्रमुख शिलालेखों में से एक), रुद्रदामन प्रथम और स्कंदगुप्त के अभिलेख शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Which of the following statements is sufficient to conclude that two triangles are congruent?</p>",
                    question_hi: "<p>51. निम्नलिखित में से कौन-सा कथन यह निष्कर्ष निकालने के लिए पर्याप्त है कि दो त्रिभुज सर्वांगसम हैं?</p>",
                    options_en: ["<p>These have two equal sides and the same perimeter</p>", "<p>These have the same area and the same base.</p>", 
                                "<p>One side and one angle of both triangles are equal.</p>", "<p>These have the same base and the same height.</p>"],
                    options_hi: ["<p>इनकी दो भुजाएँ बराबर हैं और परिमाप समान है।</p>", "<p>इनके क्षेत्रफल और आधार समान हैं।</p>",
                                "<p>दोनों त्रिभुजों की एक भुजा और एक कोण बराबर हैं।</p>", "<p>इनके आधार और ऊँचाई समान हैं।</p>"],
                    solution_en: "<p>51.(a) If two sides of both triangles are equal, and the perimeter is the same, the third side must also be equal because the perimeter is the sum of all sides. Therefore, all three sides of both triangles would be equal, satisfying the SSS (Side-Side-Side) congruence condition.<br>So, Option (a) is sufficient to conclude that the two triangles are congruent.</p>",
                    solution_hi: "<p>51.(a) यदि दोनों त्रिभुजों की दो भुजाएँ बराबर तथा परिमाप समान है , तो तीसरी भुजा भी बराबर होंगी क्योंकि परिमाप सभी भुजाओं का योग है। इसलिए, SSS (भुजा-भुजा-भुजा) सर्वांगसमता की स्थिति को संतुष्ट करते हुए, दोनों त्रिकोणों की सभी तीन भुजाएं बराबर होंगी।<br>इसलिए, विकल्प (a) यह निष्कर्ष निकालने के लिए पर्याप्त है कि दोनों त्रिभुज सर्वांगसम हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The average marks (out of 100) of boys and girls in an examination are 75 and 80, respectively. If the average marks of all the students in that examination are 78. Find the ratio of the number of boys to the number of girls.</p>",
                    question_hi: "<p>52. एक परीक्षा में लड़कों और लड़कियों के औसत अंक (100 में से) क्रमशः 75 और 80 हैं। यदि उस परीक्षा में सभी विद्यार्थियों के औसत अंक 78 हों तो लड़कों की संख्या और लड़कियों की संख्या का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>2 : 3</p>", "<p>3 : 4</p>", 
                                "<p>1 : 3</p>", "<p>1 : 2</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>3 : 4</p>",
                                "<p>1 : 3</p>", "<p>1 : 2</p>"],
                    solution_en: "<p>52.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209563.png\" alt=\"rId48\" width=\"86\" height=\"114\"><br>Required ratio = 2 : 3</p>",
                    solution_hi: "<p>52.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209742.png\" alt=\"rId49\" width=\"129\" height=\"146\"><br>लड़कों तथा लड़कियों की संख्या का अनुपात. = 2 : 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. If the sum of two sides of an equilateral triangle is 16 cm, then find the third side.</p>",
                    question_hi: "<p>53. यदि एक समबाहु त्रिभुज की दो भुजाओं का योग 16 cm है, तो तीसरी भुजा ज्ञात कीजिए।</p>",
                    options_en: ["<p>1. 4 cm</p>", "<p>16 cm</p>", 
                                "<p>Cannot be found</p>", "<p>8 cm</p>"],
                    options_hi: ["<p>1. 4 cm</p>", "<p>16 cm</p>",
                                "<p>ज्ञात नहीं किया जा सकता है</p>", "<p>8 cm</p>"],
                    solution_en: "<p>53.(d) Let the side of an equilateral triangle be x<br>According to the question,<br>2x&nbsp;= 16<br>x = 8 cm<br>We know that an equilateral triangle all side equal to each other so the third side will be 8 cm.</p>",
                    solution_hi: "<p>53.(d) <br>माना , समबाहु त्रिभुज की भुजा = x <br>प्रश्न के अनुसार,<br>2x = 16<br>x = 8 सेमी<br>हम जानते हैं कि एक समबाहु त्रिभुज की सभी भुजाएँ एक दूसरे के बराबर होती हैं इसलिए तीसरी भुजा 8 सेमी होगी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The following table shows the total candidates appeared and number of candidates present, in different exam centres &ndash; P, Q and R. Study the table and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698209886.png\" alt=\"rId50\" width=\"352\" height=\"143\"> <br>&lsquo;Total&rsquo; denotes total candidates applied for the centre,&lsquo;Present&rsquo; denotes the candidates appeared. In which year was the number of absentees the second highest in total of all centres?</p>",
                    question_hi: "<p>54. निम्नलिखित तालिका विभिन्न परीक्षा केंद्रों P, Q और R में कुल उपस्थित उम्मीदवारों और उपस्थित उम्मीदवारों की संख्या को दर्शाती है। तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210019.png\" alt=\"rId51\" width=\"305\" height=\"137\"> <br>\'कुल\' केंद्र के लिए आवेदन करने वाले कुल उम्मीदवारों को दर्शाता है, \'उपस्थित\' उपस्थित उम्मीदवारों को दर्शाता है।<br>किस वर्ष में सभी केन्द्रों में अनुपस्थित रहने वालों की संख्या दूसरी सबसे अधिक थी?</p>",
                    options_en: ["<p>2018</p>", "<p>2017</p>", 
                                "<p>2020</p>", "<p>2019</p>"],
                    options_hi: ["<p>2018</p>", "<p>2017</p>",
                                "<p>2020</p>", "<p>2019</p>"],
                    solution_en: "<p>54.(d)<br>The number of absentees in 2018 = (64 + 65 + 65) - (60 + 45 + 55) = 34<br>The number of absentees in 2017 = (50 + 75 + 45) - (45 + 55 + 40) = 30 <br>The number of absentees in 2020 = (55 + 80 + 90) - (44 + 66 + 85) = 30<br>The number of absentees in 2019 = (80 + 84 + 70) - (69 + 72 + 62) = 31<br>It is clear from the above expression that in 2019 the number of absentees is the second highest in total of all centres.</p>",
                    solution_hi: "<p>54.(d)<br>2018 में अनुपस्थितों की संख्या = (64 + 65 + 65) - (60 + 45 + 55) = 34<br>2017 में अनुपस्थितों की संख्या = (50 + 75 + 45) - (45 + 55 + 40) = 30 <br>2020 में अनुपस्थितों की संख्या= (55 + 80 + 90) - (44 + 66 + 85) = 30<br>2019 में अनुपस्थितों की संख्या= (80 + 84 + 70) - (69 + 72 + 62) = 31<br>उपरोक्त अभिव्यक्ति से यह स्पष्ट है कि 2019 में अनुपस्थितों की संख्या सभी केंद्रों के कुल योग में दूसरी सबसे अधिक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Which of the following can be the value of &lsquo;k&rsquo; so that the number 217924k is divisible by 6?</p>",
                    question_hi: "<p>55. निम्नलिखित में से कौन-सा \'k\' का मान हो सकता है जिससे कि संख्या 217924k, 6 से विभाज्य हो?</p>",
                    options_en: ["<p>4</p>", "<p>6</p>", 
                                "<p>2</p>", "<p>0</p>"],
                    options_hi: ["<p>4</p>", "<p>6</p>",
                                "<p>2</p>", "<p>0</p>"],
                    solution_en: "<p>55.(c) A number is divisible by 6 if it is divisible by both 2 and 3:<br>Divisibility of 2 : A number is divisible by 2 if it is an even number, <br>Divisibility of 3 : A number is divisible by 3 if the sum of all digits of that number is divisible by 3.<br>Now, 217924k <br>&rArr; 2 + 1 + 7 + 9 + 2 + 4 + k = 25 + k<br>The possible value of k can be 0, 2 , 4 , 6 , 8<br>But the sum of number must be multiple of 3 so the value of k will be 2</p>",
                    solution_hi: "<p>55.(c) एक संख्या 6 से विभाज्य है यदि वह 2 और 3 दोनों से विभाज्य होगा । <br>2 की विभाज्यता: एक संख्या 2 से विभाज्य होती है यदि वह एक सम संख्या है। <br>3 की विभाज्यता: एक संख्या 3 से विभाज्य होती है यदि उस संख्या के सभी अंकों का योग 3 से विभाज्य हो।<br>अब, 217924k <br>&rArr; 2 + 1 + 7 + 9 + 2 + 4 + k = 25 + k<br>K का संभावित मान = 0, 2, 4, 6, 8 <br>लेकिन संख्या का योग 3 का गुणज होना चाहिए इसलिए k का मान 2 होगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Read the given information and answer the question that follows.<br>The following table gives the percentage of marks obtained by seven students in six different subjects in an examination.<br>The number in the brackets give the maximum marks in each subject.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210160.png\" alt=\"rId52\" width=\"419\" height=\"151\"> <br>If someone secured all the highest scores that have been obtained by some student or the other in the six subjects as given in the table above, what would be the exact overall percentage score obtained by that student?</p>",
                    question_hi: "<p>56. दी गई जानकारी को ध्यानपूर्वक पढ़ें और आगे दिए गए प्रश्न का उत्तर दें।<br>नीचे दी गई तालिका में एक परीक्षा के छह अलग-अलग विषयों में सात विद्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाया गया है।<br>कोष्ठक में दी गई संख्या प्रत्येक विषय के अधिकतम अंक को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210328.png\" alt=\"rId53\" width=\"330\" height=\"166\"> <br>यदि किसी ने उपरोक्त तालिका में दिए गए छह विषयों में किसी भी विद्यार्थी या अन्य को प्राप्त अंकों की तुलना में उच्चतम अंक प्राप्त किए हैं, तो उस विद्यार्थी को प्राप्त कुल प्रतिशत अंक ज्ञात करें।</p>",
                    options_en: ["<p>91 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>", "<p>95.16 %</p>", 
                                "<p>91<math display=\"inline\"><mi>%</mi></math></p>", "<p>90 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>91 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>", "<p>95.16 %</p>",
                                "<p>91<math display=\"inline\"><mi>%</mi></math></p>", "<p>90 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>56.(a)<br>Highest marks in maths = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 150<br>Highest marks in chemistry = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 104<br>Highest marks in Physics = 120 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 108<br>Highest marks in Geography = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 95<br>Highest marks in History = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 54<br>Highest marks in Computer science = 40 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>104</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>108</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>95</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>54</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>36</mn></mrow><mrow><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>130</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>547</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> &times; 100 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>547</mn><mn>6</mn></mfrac></math> = 91<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>%</p>",
                    solution_hi: "<p>56.(a)<br>गणित में उच्चतम अंक = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 150<br>रसायन विज्ञान में उच्चतम अंक = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 104<br>भौतिकी में उच्चतम अंक = 120 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 108<br>भूगोल में उच्चतम अंक = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 95<br>इतिहास में उच्चतम अंक = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 54<br>कंप्यूटर विज्ञान में उच्चतम अंक = 40 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>104</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>108</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>95</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>54</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>36</mn></mrow><mrow><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>130</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>547</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> &times; 100 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>547</mn><mn>6</mn></mfrac></math> = 91<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The value of sin<sup>2</sup>30&deg; - sin<sup>2</sup>40&deg; + sin<sup>2</sup>45&deg; - sin<sup>2</sup>55&deg; - sin<sup>2</sup>35&deg; + sin<sup>2</sup>45&deg; - sin<sup>2</sup>50&deg; + sin<sup>2</sup>60&deg; is:</p>",
                    question_hi: "<p>57. sin<sup>2</sup>30&deg; - sin<sup>2</sup>40&deg; + sin<sup>2</sup>45&deg; - sin<sup>2</sup>55&deg; - sin<sup>2</sup>35&deg; + sin<sup>2</sup>45&deg; - sin<sup>2</sup>50&deg; + sin<sup>2</sup>60&deg; का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>0</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>4</p>", "<p>0</p>"],
                    solution_en: "<p>57.(d) sin<sup>2</sup>30&deg; - sin<sup>2</sup>40&deg; + sin<sup>2</sup>45&deg; - sin<sup>2</sup>55&deg; - sin<sup>2</sup>35&deg; + sin<sup>2</sup>45&deg; - sin<sup>2</sup>50&deg; + sin<sup>2</sup>60&deg;<br>= (sin<sup>2</sup>30&deg; + sin<sup>2</sup>60&deg;) - (sin<sup>2</sup>40&deg; + sin<sup>2</sup>50&deg;) + (sin<sup>2</sup>45&deg; + sin<sup>2</sup>45&deg;) - (sin<sup>2</sup>55&deg;+ sin<sup>2</sup>35&deg;)<br>= 1 - 1 + 1 - 1 (∵ sin<sup>2</sup>&theta; + sin<sup>2</sup>(90&deg; - &theta;) = 1)<br>= 0</p>",
                    solution_hi: "<p>57.(d) sin<sup>2</sup>30&deg; - sin<sup>2</sup>40&deg; + sin<sup>2</sup>45&deg; - sin<sup>2</sup>55&deg; - sin<sup>2</sup>35&deg; + sin<sup>2</sup>45&deg; - sin<sup>2</sup>50&deg; + sin<sup>2</sup>60&deg;<br>= (sin<sup>2</sup>30&deg; + sin<sup>2</sup>60&deg;) - (sin<sup>2</sup>40&deg; + sin<sup>2</sup>50&deg;) + (sin<sup>2</sup>45&deg; + sin<sup>2</sup>45&deg;) - (sin<sup>2</sup>55&deg;+ sin<sup>2</sup>35&deg;)<br>= 1 - 1 + 1 - 1 (∵ sin<sup>2</sup>&theta; + sin<sup>2</sup>(90&deg; - &theta;) = 1)<br>= 0</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. The value of cos<sup>2</sup>29&deg; + cos<sup>2</sup>61&deg; is:</p>",
                    question_hi: "<p>58. cos<sup>2</sup>29&deg; + cos<sup>2</sup>61&deg; का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>", "<p>2</p>", 
                                "<p>1</p>", "<p>0</p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>", "<p>2</p>",
                                "<p>1</p>", "<p>0</p>"],
                    solution_en: "<p>58.(c) cos<sup>2</sup>29&deg; + cos<sup>2</sup>61&deg;<br>= cos<sup>2</sup>29&deg; + cos<sup>2</sup>(90&deg; - 29&deg;)<br>= cos<sup>2</sup>29&deg; + sin<sup>2</sup>29&deg;<br>= 1</p>",
                    solution_hi: "<p>58.(c) cos<sup>2</sup>29&deg; + cos<sup>2</sup>61&deg;<br>= cos<sup>2</sup>29&deg; + cos<sup>2</sup>(90&deg; - 29&deg;)<br>= cos<sup>2</sup>29&deg; + sin<sup>2</sup>29&deg;<br>= 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. An article is marked at ₹550. If it is sold at a discount of 40%, then the selling price becomes 10% more than its cost price. What is the cost price (in ₹)?</p>",
                    question_hi: "<p>59. एक वस्तु पर ₹550 अंकित है। यदि इसे 40% की छूट पर बेचा जाता है, तो विक्रय मूल्य इसके क्रय मूल्य से 10% अधिक हो जाता है। क्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>220</p>", "<p>330</p>", 
                                "<p>300</p>", "<p>200</p>"],
                    options_hi: ["<p>220</p>", "<p>330</p>",
                                "<p>300</p>", "<p>200</p>"],
                    solution_en: "<p>59.(c) Let the CP of an article be 100 units <br>Given: MP of an article = ₹ 550<br>SP of an article = 550 &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 330<br>According to the question,<br>110 units = 330<br>100 units = <math display=\"inline\"><mfrac><mrow><mn>330</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> &times; 100 = ₹ 300</p>",
                    solution_hi: "<p>59.(c) माना , वस्तु का क्र.मू. = 100 इकाई <br>दिया गया है: एक वस्तु का अंकित मूल्य = ₹ 550<br>एक वस्तु का विक्रय मूल्य = 550 &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 330<br>प्रश्न के अनुसार,<br>110 units = 330<br>100 units = <math display=\"inline\"><mfrac><mrow><mn>330</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> &times; 100 = ₹ 300</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The given table shows the percentage of marks obtained by three students in three different subjects in an institute. (maximum marks are given in brackets)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210479.png\" alt=\"rId54\" width=\"248\" height=\"86\"> <br>If in order to pass the exam, a minimum of 105 marks are needed in History, how many students pass in the exam?</p>",
                    question_hi: "<p>60. दी गई तालिका में एक संस्थान में तीन अलग-अलग विषयों में तीन विद्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210659.png\" alt=\"rId55\" width=\"255\" height=\"109\"> <br>यदि परीक्षा उत्तीर्ण करने के लिए इतिहास में न्यूनतम 105 अंक चाहिए, तो कितने विद्यार्थी परीक्षा में उत्तीर्ण हुए हैं?</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p>60.(b) According to the question,<br>Passing marks of Ram in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 94.5<br>Passing marks of Mohan in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 89.25<br>Passing marks of Shyam in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 131.25<br>Hence, only 1 student passed the examination.</p>",
                    solution_hi: "<p>60.(b) प्रश्न के अनुसार,<br>इतिहास विषय में राम के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 94.5<br>इतिहास विषय में मोहन के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 89.25<br>इतिहास विषय में श्याम के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 131.25<br>अतः, केवल 1 छात्र परीक्षा में उत्तीर्ण हुआ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Fill pipe P is 21 times faster than fill pipe Q. If Q can fill a cistern in 110 minutes, find the time it takes to fill the cistern when both fill pipes are opened together.</p>",
                    question_hi: "<p>61. भरण पाइप P, भरण पाइप Q से 21 गुना तेज है। यदि Q एक टंकी को 110 मिनट में भर सकता है, तो दोनों भरण पाइपों को एक साथ खोलने पर टंकी को भरने में कितना समय लगेगा?</p>",
                    options_en: ["<p>5 minutes</p>", "<p>4 minutes</p>", 
                                "<p>3 minutes</p>", "<p>6 minutes</p>"],
                    options_hi: ["<p>5 मिनट</p>", "<p>4 मिनट</p>",
                                "<p>3 मिनट</p>", "<p>6 मिनट</p>"],
                    solution_en: "<p>61.(a) <br>Ratio of pipe &rarr;&nbsp; P&nbsp; :&nbsp; &nbsp;Q<br>Efficiency&nbsp; &nbsp; &nbsp; &rarr; 21 :&nbsp; &nbsp;1<br>Now,<br>Total time to fill the cistern = 110 &times; 1 = 110 units<br>Time taken by both pipe to fill the cistern = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mrow><mn>21</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 5 minutes</p>",
                    solution_hi: "<p>61.(a) <br>पाइप का अनुपात&nbsp; &rarr;&nbsp; P&nbsp; &nbsp; :&nbsp; Q<br>क्षमता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp;21&nbsp; :&nbsp; &nbsp;1<br>अब,<br>टंकी को भरने में लगा कुल समय = 110 &times; 1 = 110 इकाई<br>दोनों पाइपों द्वारा टंकी भरने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mrow><mn>21</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 5 मिनट</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The radii of the two cones are in the ratio of 2 : 5 and their volumes are in the ratio of 3 : 5. What is the ratio of their heights?</p>",
                    question_hi: "<p>62. दो शंकुओं की त्रिज्&zwj;याओं का अनुपात 2 : 5 और उनके आयतन का अनुपात 3 : 5 है। इनकी ऊंचाइयों का अनुपात क्या है?</p>",
                    options_en: ["<p>15 : 4</p>", "<p>11 : 15</p>", 
                                "<p>13 : 11</p>", "<p>4 : 11</p>"],
                    options_hi: ["<p>15 : 4</p>", "<p>11 : 15</p>",
                                "<p>13 : 11</p>", "<p>4 : 11</p>"],
                    solution_en: "<p>62.(a) Let the height of the two cone be h<sub>1</sub>&nbsp;and h<sub>2</sub>, <br>According to the question,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle><mi>&#960;</mi><msubsup><mi>r</mi><mn>1</mn><mn>2</mn></msubsup><msub><mi>h</mi><mn>1</mn></msub></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#960;</mi><msubsup><mi>r</mi><mn>2</mn><mn>2</mn></msubsup><msub><mi>h</mi><mn>2</mn></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><msub><mi>h</mi><mn>1</mn></msub></mrow><mrow><mn>25</mn><mo>&#215;</mo><msub><mi>h</mi><mn>2</mn></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>h</mi><mn>1</mn></msub><msub><mi>h</mi><mn>2</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>20</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>4</mn></mfrac></math><br>&rArr; h<sub>1</sub> : h<sub>2</sub> = 15 : 4</p>",
                    solution_hi: "<p>62.(a) माना कि दोनों शंकुओं की ऊंचाई h<sub>1</sub>&nbsp;और h<sub>2</sub> है, <br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle><mi>&#960;</mi><msubsup><mi>r</mi><mn>1</mn><mn>2</mn></msubsup><msub><mi>h</mi><mn>1</mn></msub></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#960;</mi><msubsup><mi>r</mi><mn>2</mn><mn>2</mn></msubsup><msub><mi>h</mi><mn>2</mn></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><msub><mi>h</mi><mn>1</mn></msub></mrow><mrow><mn>25</mn><mo>&#215;</mo><msub><mi>h</mi><mn>2</mn></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>h</mi><mn>1</mn></msub><msub><mi>h</mi><mn>2</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>20</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>4</mn></mfrac></math><br>&rArr; h<sub>1</sub> : h<sub>2</sub> = 15 : 4</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Simplify<br><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>15</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>75</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>125</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>25</mn></mrow></mfrac></math>(x - 5)</p>",
                    question_hi: "<p>63. सरल कीजिए :<br><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>15</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>75</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>125</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>25</mn></mrow></mfrac></math>(x - 5)</p>",
                    options_en: ["<p>x<sup>2</sup> + 25</p>", "<p>x<sup>2 </sup>+ 5x + 25</p>", 
                                "<p>x<sup>2</sup> + 10x + 25</p>", "<p>x<sup>2 </sup>- 25</p>"],
                    options_hi: ["<p>x<sup>2 </sup>+ 25</p>", "<p>x<sup>2 </sup>+ 5x + 25</p>",
                                "<p>x<sup>2&nbsp;</sup> + 10x + 25</p>", "<p>x<sup>2 </sup>- 25</p>"],
                    solution_en: "<p>63.(c) <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>15</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>75</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>125</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>25</mn><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= (x + 5)<sup>2</sup><br>= x<sup>2</sup> + 25 + 10x or x<sup>2</sup> + 10x + 25</p>",
                    solution_hi: "<p>63.(c) <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>15</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>75</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>125</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>25</mn><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= (x + 5)<sup>2</sup><br>= x<sup>2</sup> + 25 + 10x or x<sup>2</sup> + 10x + 25</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Which of the following can be the value of k, if <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>88</mn><mi>&#160;</mi><mo>&#247;</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>k</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>k</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo></mrow></mfrac></math> = 1 ?</p>",
                    question_hi: "<p>64. यदि <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>88</mn><mi>&#160;</mi><mo>&#247;</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>k</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>k</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo></mrow></mfrac></math> = 1 है तो निम्नलिखित में से k का मान क्या हो सकता है?</p>",
                    options_en: ["<p>1,10</p>", "<p>4,7</p>", 
                                "<p>3,10</p>", "<p>2,7</p>"],
                    options_hi: ["<p>1,10</p>", "<p>4,7</p>",
                                "<p>3,10</p>", "<p>2,7</p>"],
                    solution_en: "<p>64.(a) <math display=\"inline\"><mfrac><mrow><mn>88</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">k</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn></mrow><mrow><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>7</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">k</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = 1<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>&#215;</mo><mi>k</mi><mo>-</mo><mn>9</mn></mrow><mrow><mn>36</mn><mo>-</mo><mn>35</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></mrow></mfrac></math> = 1<br>&rArr; 11k - 9 = 1 + k<sup>2</sup><br>&rArr; k<sup>2</sup> - 11k + 10 = 0<br>&rArr; k<sup>2</sup> - 10k - k + 10 = 0<br>&rArr; k(k - 10) - 1(k - 10) = 0<br>&rArr; (k - 10)(k - 1) = 0<br>&rArr; k = 10 and 1</p>",
                    solution_hi: "<p>64.(a) <math display=\"inline\"><mfrac><mrow><mn>88</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">k</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn></mrow><mrow><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>7</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">k</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = 1<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>&#215;</mo><mi>k</mi><mo>-</mo><mn>9</mn></mrow><mrow><mn>36</mn><mo>-</mo><mn>35</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></mrow></mfrac></math> = 1<br>&rArr; 11k - 9 = 1 + k<sup>2</sup><br>&rArr; k<sup>2</sup> - 11k + 10 = 0<br>&rArr; k<sup>2</sup> - 10k - k + 10 = 0<br>&rArr; k(k - 10) - 1(k - 10) = 0<br>&rArr; (k - 10)(k - 1) = 0<br>&rArr; k = 10 and 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Let O be the centre of the circle and AB and CD are two parallel chords on the same side of the radius. OP is perpendicular to AB and OQ is perpendicular to CD. If AB = 10 cm, CD = 24 cm and PQ = 7 cm, then the diameter (in cm) of the circle is equal to:</p>",
                    question_hi: "<p>65. मान लीजिए कि, O वृत्त का केंद्र है और AB और CD, त्रिज्या की समान भुजा पर दो समानांतर जीवाएं हैं। OP, AB के लंबवत है और OQ, CD के लंबवत है। यदि AB = 10 cm है, CD = 24 cm और PQ = 7 cm है, तो वृत्त का व्यास (cm में) _______ होगा।</p>",
                    options_en: ["<p>26</p>", "<p>13</p>", 
                                "<p>12</p>", "<p>24</p>"],
                    options_hi: ["<p>26</p>", "<p>13</p>",
                                "<p>12</p>", "<p>24</p>"],
                    solution_en: "<p>65.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210816.png\" alt=\"rId56\" width=\"138\" height=\"125\"><br>In &Delta;OQC, <br>By using pythagoras theorem,<br>OQ<sup>2</sup> + CQ<sup>2</sup> = OC<sup>2</sup><br>OQ<sup>2</sup> + 12<sup>2</sup> = OC<sup>2</sup> &hellip;&hellip;.(i)<br>In &Delta;OPA, <br>OP<sup>2</sup> + AP<sup>2</sup> = OA<sup>2</sup><br>(OQ + 7)<sup>2</sup> + 5<sup>2</sup> = OA<sup>2</sup> &hellip;&hellip;.(ii)<br>Equating the equation (i) and (ii) because OC = OA (radii of the circle)<br>OQ<sup>2</sup> + 12<sup>2</sup> = (OQ + 7)<sup>2</sup> + 5<sup>2</sup><br>OQ<sup>2</sup> + 144 = OQ<sup>2</sup> + 49 + 14OQ + 25<br>144 - (49 + 25) = 14OQ<br>70 = 14OQ<br>OQ = 5 cm<br>Put the value of OQ in equation (i)<br>5<sup>2</sup> + 12<sup>2</sup> = OC<sup>2</sup><br>OC<sup>2</sup> = 25 + 144 = 169<br>OC = 13<br>So, the diameter of the circle = 2 &times; 13 = 26 cm</p>",
                    solution_hi: "<p>65.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210816.png\" alt=\"rId56\" width=\"161\" height=\"146\"><br>In &Delta;OQC,<br>पाइथागोरस प्रमेय का उपयोग करके,<br>OQ<sup>2</sup> + CQ<sup>2</sup> = OC<sup>2</sup><br>OQ<sup>2</sup> + 12<sup>2</sup> = OC<sup>2</sup> &hellip;&hellip;.(i)<br>In &Delta;OPA, <br>OP<sup>2</sup> + AP<sup>2</sup> = OA<sup>2</sup><br>(OQ + 7)<sup>2</sup> + 5<sup>2</sup> = OA<sup>2</sup> &hellip;&hellip;.(ii)<br>समीकरण (i) और (ii) को बराबर होगा क्योंकि OC = OA (वृत्त की त्रिज्या)<br>OQ<sup>2</sup> + 12<sup>2</sup> = (OQ + 7)<sup>2</sup> + 5<sup>2</sup><br>OQ<sup>2</sup> + 144 = OQ<sup>2</sup> + 49 + 14OQ + 25<br>144 - (49 + 25) = 14OQ<br>70 = 14OQ<br>OQ = 5 cm<br>OQ का मान समीकरण (i) में रखने पर <br>5<sup>2</sup> + 12<sup>2</sup> = OC<sup>2</sup><br>OC<sup>2</sup> = 25 + 144 = 169<br>OC = 13<br>अतः , वृत्त का व्यास = 2 &times; 13 = 26 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If cosec &theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>, then evaluate (sec<sup>2</sup>&theta;-1) &times; cot<sup>2</sup>&theta; &times;(1 + cot<sup>2</sup>&theta;)</p>",
                    question_hi: "<p>66.यदि cosec &theta; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> है , तो (sec<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (1 + cot<sup>2</sup>&theta;)&nbsp;का मान ज्ञात कीजिए</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>66.(c) <strong>Given </strong>: cosec &theta; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>&rArr; (sec<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (1 + cot<sup>2</sup>&theta;)<br>We know,<br>sec<sup>2</sup>&theta; = 1 + tan<sup>2</sup>&theta;, cosec<sup>2</sup>&theta; = 1 + cot<sup>2</sup>&theta; and tan<sup>2</sup>&theta; &times; cot<sup>2</sup>&theta; = 1<br>Then, (1 + tan<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (cosec<sup>2</sup>&theta; )<br>= tan<sup>2</sup>&theta; &times; cot<sup>2</sup>&theta; &times; cosec<sup>2</sup>&theta;<br>= cosec<sup>2</sup>&theta; = (<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>)<sup>2</sup> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>9</mn></mfrac></math></p>",
                    solution_hi: "<p>66.(c) <strong>Given :</strong> cosec &theta; = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>&rArr; (sec<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (1 + cot<sup>2</sup>&theta;)<br>हम जानते हैं,<br>sec<sup>2</sup>&theta; = 1 + tan<sup>2</sup>&theta;, cosec<sup>2</sup>&theta; = 1 + cot<sup>2</sup>&theta; and tan<sup>2</sup>&theta; &times; cot<sup>2</sup>&theta; = 1<br>Then, (1 + tan<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (cosec<sup>2</sup>&theta; )<br>= tan<sup>2</sup>&theta; &times; cot<sup>2</sup>&theta; &times; cosec<sup>2</sup>&theta;<br>= cosec<sup>2</sup>&theta; = (<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>)<sup>2</sup> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>9</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The distance between the centres of two circles of radii 22 cm and 10 cm is 37 cm. If the points of contact of a direct common tangent to these circles are M and Q, then find the length of the line segment MQ.</p>",
                    question_hi: "<p>67. 22 cm और 10 cm त्रिज्या वाले दो वृत्तों के केंद्रों के बीच की दूरी 37 cm है। यदि इन वृत्तों की सीधी उभयनिष्&zwj;ठ अनुस्पर्श रेखा के स्पर्श बिंदु (points of contact) M और Q हैं, तो रेखाखंड MQ की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>35 cm</p>", "<p>39 cm</p>", 
                                "<p>29 cm</p>", "<p>25 cm</p>"],
                    options_hi: ["<p>35 cm</p>", "<p>39 cm</p>",
                                "<p>29 cm</p>", "<p>25 cm</p>"],
                    solution_en: "<p>67.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210936.png\" alt=\"rId57\" width=\"166\" height=\"82\"><br>Length of the common tangent(MQ) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mi mathvariant=\"bold-italic\">d</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">b</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">w</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">f</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">w</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">l</mi><mi mathvariant=\"bold-italic\">e</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mrow><mi mathvariant=\"bold-italic\">c</mi></mrow><mrow><mn>1</mn></mrow></msub><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msub><mrow><mi mathvariant=\"bold-italic\">c</mi></mrow><mrow><mn>2</mn></mrow></msub><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>MQ = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>22</mn><mi>&#160;</mi><mo>-</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>MQ = <math display=\"inline\"><msqrt><mn>1369</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>144</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1225</mn></msqrt></math> = 35 cm</p>",
                    solution_hi: "<p>67.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698210936.png\" alt=\"rId57\" width=\"194\" height=\"96\"><br>उभयनिष्ठ स्पर्शरेखा की लंबाई(MQ) = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>&#2342;&#2379;</mi><mo>&#160;</mo><mi>&#2357;&#2371;&#2340;&#2381;&#2340;</mi><mo>&#160;</mo><mi>&#2325;&#2375;</mi><mo>&#160;</mo><mi>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</mi><mo>&#160;</mo><mi>&#2325;&#2375;</mi><mo>&#160;</mo><mi>&#2348;&#2368;&#2330;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"bold-italic\">c</mi><mn>1</mn></msub><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msub><mi mathvariant=\"bold-italic\">c</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>MQ = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>22</mn><mi>&#160;</mi><mo>-</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>MQ = <math display=\"inline\"><msqrt><mn>1369</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>144</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1225</mn></msqrt></math> = 35 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. In a circular race of 840 m, A and B start running in the same direction at the same time from the same point at the speeds of 6 m/s and 12 m/s, respectively. After how much time will they meet next?</p>",
                    question_hi: "<p>68. 840 m की एक वृत्ताकार दौड़ में A और B समान समय पर समान दिशा में समान बिंदु से क्रमशः 6 m/s और 12 m/s की चाल से दौड़ना आरम्भ करते हैं। वे अगली बार कितने समय बाद मिलेंगे?</p>",
                    options_en: ["<p>140 s</p>", "<p>20 s</p>", 
                                "<p>40 s</p>", "<p>70 s</p>"],
                    options_hi: ["<p>140 s</p>", "<p>20 s</p>",
                                "<p>40 s</p>", "<p>70 s</p>"],
                    solution_en: "<p>68.(a) Distance of the circular track = 840 m<br>Relative speed = 12 - 6 = 6 m/s<br>Time (they will meet next time) = <math display=\"inline\"><mfrac><mrow><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>840</mn><mn>6</mn></mfrac></math> = 140 sec.</p>",
                    solution_hi: "<p>68.(a) वृत्ताकार ट्रैक की दूरी = 840 मीटर<br>सापेक्ष गति = 12 - 6 = 6 मी/से<br>समय ( जब वे अगली बार मिलेंगे) = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#2330;&#2366;&#2354;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>840</mn><mn>6</mn></mfrac></math> = 140 सेकंड।.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. R pays ₹100 to P with ₹5, ₹2 and ₹1 coins. The total number of coins used for paying are 40. What is the number of coins of denomination ₹5 in the payment?</p>",
                    question_hi: "<p>69. R, P को ₹5, ₹2 और ₹1 के सिक्कों के साथ ₹100 का भुगतान करता है। भुगतान के लिए उपयोग किए गए सिक्कों की कुल संख्या 40 है। भुगतान में ₹5 मूल्यवर्ग के सिक्कों की संख्या कितनी है?</p>",
                    options_en: ["<p>16</p>", "<p>17</p>", 
                                "<p>18</p>", "<p>13</p>"],
                    options_hi: ["<p>16</p>", "<p>17</p>",
                                "<p>18</p>", "<p>13</p>"],
                    solution_en: "<p>69.(d) <br>Let ₹5, ₹2, ₹1 coins be x,y,z&nbsp;respectively.<br>According to question, <br>x + y + z = 40 &hellip;.. (i)<br>5x + 2y + z = 100 &hellip; (ii)<br>On subtracting equation (i) from (ii) we get<br>4x + y = 60<br>Now on satisf<math display=\"inline\"><mi>y</mi></math>ing the options we find that only option (d) gives positive value of y and no. of coins must be positive.<br>Hence, no. of ₹5 coins = 13</p>",
                    solution_hi: "<p>69.(d) <br>माना ₹5, ₹2, ₹1 के सिक्के क्रमशः x,y,z&nbsp;हैं।<br>प्रश्न के अनुसार, <br>x + y + z = 40 &hellip;.. (i)<br>5x + 2y + z = 100 &hellip; (ii)<br>समीकरण (i) को (ii) से घटाने पर हमें प्राप्त होता है<br>4x + y = 60<br>अब विकल्पों से संतुष्ट करने पर हम पाते हैं कि केवल विकल्प (d) y&nbsp;का सकारात्मक मान देता है और सिक्कों का मान सकारात्मक होना चाहिए । <br>अत:, ₹5 के सिक्कों की संख्या = 13</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. In an election between two candidates, y% of the voters did not vote. 10% of the votes cast were declared invalid, while all the valid votes were cast in favour of either of the two candidates. The candidate who got 59.375% of the valid votes cast was declared elected by 2484 votes. If the number of people eligible to vote in that election was 16,000, what is the value of y?</p>",
                    question_hi: "<p>70. दो उम्मीदवारों के बीच हुए एक चुनाव में, y% मतदाताओं ने मत नहीं डाला। डाले गए मतों में से 10% मत अवैध घोषित कर दिए गए, जबकि सभी वैध मत दोनों उम्मीदवारों में से किसी एक के पक्ष में पड़े। जिस उम्मीदवार को कुल वैध मतों में से 59.375% मत मिले, उसे 2484 मतों से निर्वाचित घोषित किया गया। यदि उस चुनाव में मतदान करने के पात्र लोगों की संख्या 16,000 थी, तो y का मान ज्ञात करें।</p>",
                    options_en: ["<p>7.2</p>", "<p>8.4</p>", 
                                "<p>8</p>", "<p>7.5</p>"],
                    options_hi: ["<p>7.2</p>", "<p>8.4</p>",
                                "<p>8</p>", "<p>7.5</p>"],
                    solution_en: "<p>70.(c) <br>59.375% = <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> <br>Valid vote difference between two candidate = 6 unit&nbsp;<br>according to question,<br>16000(1 - <math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math>) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>32</mn></mfrac></math> = 2484<br>100 - y = 92 <br>Y = 8</p>",
                    solution_hi: "<p>70.(c) <br>59.375% =<math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> <br>दो उम्मीदवारो के बीच वैध मतो का अंतर = 6 इकाई <br>प्रश्न के अनुसार,<br>16000(1 - <math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math>) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>32</mn></mfrac></math> = 2484<br>100 - y = 92 <br>Y = 8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Mohan borrows a sum of ₹4,22,092 at the rate of 20% per annum simple interest. At the end of the first year, he repays ₹21,679 towards the return of principal amount borrowed. If Mohan clears all pending dues at the end of the second year, including interest payment that accrued during the first year, how much does he pay (in ₹) at the end of the second year?</p>",
                    question_hi: "<p>71. मोहन ने 20% वार्षिक साधारण ब्याज की दर से ₹4,22,092 की राशि उधार ली। प्रथम वर्ष की समाप्ति पर, वह उधार ली गई मूल राशि की वापसी के लिए ₹21,679 चुकाता है। यदि मोहन दूसरे वर्ष की समाप्ति पर सभी लंबित बकाया राशि का भुगतान करता है, जिसमें प्रथम वर्ष के दौरान अर्जित ब्याज भुगतान भी शामिल है, तो वह दूसरे वर्ष की समाप्ति पर कितना भुगतान (₹ में) करेगा ?</p>",
                    options_en: ["<p>556367</p>", "<p>558380</p>", 
                                "<p>561347</p>", "<p>564914</p>"],
                    options_hi: ["<p>556367</p>", "<p>558380</p>",
                                "<p>561347</p>", "<p>564914</p>"],
                    solution_en: "<p>71.(d) SI = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>c</mi><mi>i</mi><mi>p</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>r</mi><mi>a</mi><mi>t</mi><mi>e</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>SI for first year = <math display=\"inline\"><mfrac><mrow><mn>422092</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 84418.40<br>Remaining the amount after repays = 422092 - 21679 = ₹ 400413<br>SI for second year = <math display=\"inline\"><mfrac><mrow><mn>400413</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 80082.60<br>Mohan total pay the amount end of the second years = 84418.40 + 400413 + 80082.60 = ₹ 564914</p>",
                    solution_hi: "<p>71.(d) SI = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>प्रथम वर्ष के लिए साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>422092</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 84418.40<br>21679 रुपये चुकाने के बाद शेष राशि = 422092 - 21679 = ₹ 400413<br>दूसरे वर्ष के लिए साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>400413</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 80082.60<br>दूसरे वर्ष के अंत में मोहन को कुल भुगतान राशि = 84418.40 + 400413 + 80082.60 = ₹ 564914</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Simplify <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><mo>)</mo></mrow><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow></mfrac></math></p>",
                    question_hi: "<p>72. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><mo>)</mo></mrow><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow></mfrac></math> का मान क्या होगा?</p>",
                    options_en: ["<p>x + 3</p>", "<p>x - 3</p>", 
                                "<p><math display=\"inline\"><msqrt><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>9</mn></msqrt></math></p>", "<p>x - 9</p>"],
                    options_hi: ["<p>x + 3</p>", "<p>x - 3</p>",
                                "<p><math display=\"inline\"><msqrt><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>9</mn></msqrt></math></p>", "<p>x - 9</p>"],
                    solution_en: "<p>72.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><mo>)</mo></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mi>&#160;</mi><msup><mn>3</mn><mn>2</mn></msup><mo>)</mo></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> = (x - 3)</p>",
                    solution_hi: "<p>72.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mi>&#160;</mi><mn>9</mn><mo>)</mo></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mi>&#160;</mi><msup><mn>3</mn><mn>2</mn></msup><mo>)</mo></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> = (x - 3)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Rajesh, in his printing press, got an order to print some books, out of which he completed <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> of the order in 34 days. In how many days did he complete the entire order of printing?</p>",
                    question_hi: "<p>73. राजेश को उसके प्रिंटिंग प्रेस में कुछ किताबें छापने का ऑर्डर मिला, जिसमें से उसने ऑर्डर के <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> को 34 दिनों में पूरा किया। उसने छपाई का पूरा ऑर्डर कितने दिनों में पूरा किया ?</p>",
                    options_en: ["<p>60</p>", "<p>20</p>", 
                                "<p>54</p>", "<p>56</p>"],
                    options_hi: ["<p>60</p>", "<p>20</p>",
                                "<p>54</p>", "<p>56</p>"],
                    solution_en: "<p>73.(c) According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> units = 34 days<br>1 units = <math display=\"inline\"><mfrac><mrow><mn>34</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>27</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> = 54 days</p>",
                    solution_hi: "<p>73.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> इकाइयाँ = 34 दिन<br>1 इकाई = <math display=\"inline\"><mfrac><mrow><mn>34</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>27</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> = 54 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A grocer professes to sell rice at the cost price, but uses a fake weight of 870 g for 1 kg. Find his profit percentage (correct to two decimal places).</p>",
                    question_hi: "<p>74. एक पंसारी लागत मूल्य पर चावल बेचने का दावा करता है, लेकिन 1 kg के लिए 870 g के नकली वजन का उपयोग करता है। उसका लाभ प्रतिशत (दो दशमलव स्थानों तक सही) ज्ञात कीजिए।</p>",
                    options_en: ["<p>15.11%</p>", "<p>14.94%</p>", 
                                "<p>18.21%</p>", "<p>11.11%</p>"],
                    options_hi: ["<p>15.11%</p>", "<p>14.94%</p>",
                                "<p>18.21%</p>", "<p>11.11%</p>"],
                    solution_en: "<p>74.(b) As per question,<br>original weight = 1000 g<br>New weight = 870 g<br>Required Profit % = <math display=\"inline\"><mfrac><mrow><mn>1000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>870</mn></mrow><mrow><mn>870</mn></mrow></mfrac></math> &times; 100 = 14.94 %</p>",
                    solution_hi: "<p>74.(b) प्रश्न के अनुसार,<br>मूल वजन = 1000 ग्राम <br>नया वजन = 870 ग्राम<br>आवश्यक लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>870</mn></mrow><mrow><mn>870</mn></mrow></mfrac></math> &times; 100 = 14.94 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The following table shows the marks (out of 100) obtained by five students in five different subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698211141.png\" alt=\"rId58\" width=\"385\" height=\"113\"> <br>Who obtained 79% marks in all the subjects taken together?</p>",
                    question_hi: "<p>75. निम्नलिखित तालिका पांच अलग-अलग विषयों में पांच छात्रों द्वारा प्राप्त अंक (100 में से) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730698211289.png\" alt=\"rId59\" width=\"303\" height=\"121\"> <br>सभी विषयों को मिलाकर 79% अंक किसने प्राप्त किए?</p>",
                    options_en: ["<p>Sumit</p>", "<p>Mohit</p>", 
                                "<p>Rohit</p>", "<p>Tarun</p>"],
                    options_hi: ["<p>सुमित</p>", "<p>मोहित</p>",
                                "<p>रोहित</p>", "<p>तरुण</p>"],
                    solution_en: "<p>75.(d) <br>Sumit obtained percentage = <math display=\"inline\"><mfrac><mrow><mn>90</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>78</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>68</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>406</mn><mn>5</mn></mfrac></math> = 81.2%<br>Mohit obtained percentage = <math display=\"inline\"><mfrac><mrow><mn>73</mn><mo>+</mo><mn>84</mn><mo>+</mo><mn>77</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>95</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>419</mn><mn>5</mn></mfrac></math> = 83.8%<br>Rohit obtained percentage = <math display=\"inline\"><mfrac><mrow><mn>69</mn><mo>+</mo><mn>76</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>94</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>407</mn><mn>5</mn></mfrac></math> = 81.4%<br>Tarun obtained percentage = <math display=\"inline\"><mfrac><mrow><mn>65</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>75</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>90</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>395</mn><mn>5</mn></mfrac></math> = 79%<br>Hence, Tarun has obtained 79% marks in all the subjects</p>",
                    solution_hi: "<p>75.(d) <br>सुमित को प्राप्त प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>90</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>78</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>68</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>406</mn><mn>5</mn></mfrac></math> = 81.2%<br>मोहित को प्राप्त प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>73</mn><mo>+</mo><mn>84</mn><mo>+</mo><mn>77</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>95</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>419</mn><mn>5</mn></mfrac></math> = 83.8%<br>रोहित को प्राप्त प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>69</mn><mo>+</mo><mn>76</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>94</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>407</mn><mn>5</mn></mfrac></math> = 81.4%<br>तरुण को प्राप्त प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>65</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>75</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>90</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>395</mn><mn>5</mn></mfrac></math> = 79%<br>अतः, तरुण ने सभी विषयों में 79% अंक प्राप्त किए हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the option that expresses the given sentence in passive voice. <br>Ishika saw the tiger in the forest.</p>",
                    question_hi: "<p>76. Select the option that expresses the given sentence in passive voice. <br>Ishika saw the tiger in the forest.</p>",
                    options_en: ["<p>The tiger saw by Ishika in the forest</p>", "<p>The tiger was seen by the forest in Ishika.</p>", 
                                "<p>The tiger was seen by Ishika in the forest.</p>", "<p>The tiger sees Ishika in the forest.</p>"],
                    options_hi: ["<p>The tiger saw by Ishika in the forest</p>", "<p>The tiger was seen by the forest in Ishika.</p>",
                                "<p>The tiger was seen by Ishika in the forest.</p>", "<p>The tiger sees Ishika in the forest.</p>"],
                    solution_en: "<p>76.(c) The tiger was seen by Ishika in the forest.(Corect)<br>(a) The tiger <span style=\"text-decoration: underline;\">saw</span> by Ishika in the forest.(Incorrect Verb)<br>(b) The tiger was seen by the forest in Ishika.(Incorrect Sentence Structure)<br>(d) The tiger <span style=\"text-decoration: underline;\">sees</span> Ishika in the forest.(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>76.(c) The tiger was seen by Ishika in the forest.(Corect)<br>(a) The tiger <span style=\"text-decoration: underline;\">saw</span> by Ishika in the forest.(गलत Verb)<br>(b) The tiger was seen by the forest in Ishika.(गलत Sentence Structure)<br>(d) The tiger <span style=\"text-decoration: underline;\">sees</span> Ishika in the forest.(गलत Sentence Structure)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate synonym of the given word. <br>Innuendo</p>",
                    question_hi: "<p>77. Select the most appropriate synonym of the given word. <br>Innuendo</p>",
                    options_en: ["<p>Prose</p>", "<p>Crude</p>", 
                                "<p>Ragged</p>", "<p>Insinuation</p>"],
                    options_hi: ["<p>Prose</p>", "<p>Crude</p>",
                                "<p>Ragged</p>", "<p>Insinuation</p>"],
                    solution_en: "<p>77.(d) <strong>Insinuation- </strong>to suggest, without being direct, that something unpleasant is true.<br><strong>Innuendo-</strong> indirect reference to something rude or unpleasant.<br><strong>Prose</strong>- written language in its ordinary form rather than poetry.<br><strong>Crude-</strong> rude and offensive.<br><strong>Ragged-</strong> (of clothes) torn and not in good condition.</p>",
                    solution_hi: "<p>77.(d) <strong>Insinuation (कटाक्ष) </strong>- to suggest, without being direct, that something unpleasant is true.<br><strong>Innuendo (व्यंग्य) -</strong> indirect reference to something rude or unpleasant.<br><strong>Prose (गद्य) -</strong> written language in its ordinary form rather than poetry.<br><strong>Crude (असभ्य) </strong>- rude and offensive.<br><strong>Ragged (फटे-पुराने वस्त्र)</strong> - (of clothes) torn and not in good condition.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>&lsquo;Spy Family&rsquo; is a graphic novel that is a narrative work in which the story is conveyed to the reader using <span style=\"text-decoration: underline;\">uninterrupted art in a traditional </span>comics format.</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>&lsquo;Spy Family&rsquo; is a graphic novel that is a narrative work in which the story is conveyed to the reader using <span style=\"text-decoration: underline;\">uninterrupted art in a traditional</span> comics format.</p>",
                    options_en: ["<p>sequential art in a traditional</p>", "<p>existential art in a traditional</p>", 
                                "<p>sedimental art in a traditional</p>", "<p>longitudinal art in a traditional</p>"],
                    options_hi: ["<p>sequential art in a traditional</p>", "<p>existential art in a traditional</p>",
                                "<p>sedimental art in a traditional</p>", "<p>longitudinal art in a traditional</p>"],
                    solution_en: "<p>78.(a) sequential art in a traditional<br>&lsquo;Sequential&rsquo; means in a logical order. The given sentence states that &lsquo;Spy Family&rsquo; is a graphic novel that is a narrative work in which the story is conveyed to the reader using sequential art in a traditional comics format. Hence, \'sequential art in a traditional\' is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) sequential art in a traditional<br>&lsquo;Sequential&rsquo; का अर्थ है तार्किक क्रम (logical order) में। दिए गए sentence में बताया गया है कि &lsquo;Spy Family&rsquo; एक graphic novel है जो एक कथात्मक कृति(narrative work) है जिसमें कहानी पारंपरिक comics format में sequential art का प्रयोग करके reader को अवगत करायी जाती है। अतः, \'sequential art in a traditional\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate synonym of the given word. <br>Fatal</p>",
                    question_hi: "<p>79. Select the most appropriate synonym of the given word. <br>Fatal</p>",
                    options_en: ["<p>Deadly</p>", "<p>Additional</p>", 
                                "<p>Jovial</p>", "<p>Easy</p>"],
                    options_hi: ["<p>Deadly</p>", "<p>Additional</p>",
                                "<p>Jovial</p>", "<p>Easy</p>"],
                    solution_en: "<p>79.(a) <strong>Deadly- </strong>causing or able to cause death.<br><strong>Fatal- </strong>resulting in death.<br><strong>Additional- </strong>added or extra.<br><strong>Jovial- </strong>cheerful and friendly.</p>",
                    solution_hi: "<p>79.(a)<strong> Deadly (जानलेवा)</strong> - causing or able to cause death.<br><strong>Fatal (घातक) -</strong> resulting in death.<br><strong>Additional (अतिरिक्त) -</strong> added or extra.<br><strong>Jovial (हसमुख) -</strong> cheerful and friendly.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. The following sentence has been divided into four segments. Identify the segment that contains an error. <br>Mr. Abhilash and his family / have received / no informations / about the incident.</p>",
                    question_hi: "<p>80. The following sentence has been divided into four segments. Identify the segment that contains an error. <br>Mr. Abhilash and his family / have received / no informations / about the incident.</p>",
                    options_en: ["<p>have received</p>", "<p>no informations</p>", 
                                "<p>about the incident</p>", "<p>Mr. Abhilash and his family</p>"],
                    options_hi: ["<p>have received</p>", "<p>no informations</p>",
                                "<p>about the incident</p>", "<p>Mr. Abhilash and his family</p>"],
                    solution_en: "<p>80.(b)<strong> no informations</strong><br>&lsquo;Informations&rsquo; must be replaced with &lsquo;information&rsquo; as &lsquo;information&rsquo; is an uncountable noun and its plural cannot be formed. Hence, \'no information\' is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(b) <strong>no informations</strong><br>&lsquo;Informations&rsquo; के स्थान पर &lsquo;information&rsquo; का प्रयोग होगा क्योंकि &lsquo;information&rsquo; एक uncountable noun है और इसका plural नहीं बनाया जा सकता है। अतः, \'no information\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the opposite meaning of the underlined word. <br>The explosive used is of my own formulation, and I can <span style=\"text-decoration: underline;\">vouch</span> for its efficiency.</p>",
                    question_hi: "<p>81. Select the option that expresses the opposite meaning of the underlined word. <br>The explosive used is of my own formulation, and I can <span style=\"text-decoration: underline;\">vouch</span> for its efficiency.</p>",
                    options_en: ["<p>Maintain</p>", "<p>Certify</p>", 
                                "<p>Invalidate</p>", "<p>Witness</p>"],
                    options_hi: ["<p>Maintain</p>", "<p>Certify</p>",
                                "<p>Invalidate</p>", "<p>Witness</p>"],
                    solution_en: "<p>81.(c) <strong>Invalidate-</strong> to make something not true.<br><strong>Vouch- </strong>to confirm or assert the truth of something.<br><strong>Maintain- </strong>to keep something in good condition.<br><strong>Certify- </strong>to officially recognize or confirm something.<br><strong>Witness-</strong> to see or observe an event happening.</p>",
                    solution_hi: "<p>81.(c) <strong>Invalidate (अमान्य करना) </strong>- to make something not true.<br><strong>Vouch (प्रमाणित करना) -</strong> to confirm or assert the truth of something.<br><strong>Maintain (बनाए रखना) -</strong> to keep something in good condition.<br><strong>Certify (प्रमाणित करना) -</strong> to officially recognize or confirm something.<br><strong>Witness (साक्षी होना) -</strong> to see or observe an event happening.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the most appropriate option to fill in the blank. <br>Vinod had a ________ escape in the car accident</p>",
                    question_hi: "<p>82. Select the most appropriate option to fill in the blank. <br>Vinod had a ________ escape in the car accident</p>",
                    options_en: ["<p>comfortable</p>", "<p>full</p>", 
                                "<p>narrow</p>", "<p>wide</p>"],
                    options_hi: ["<p>comfortable</p>", "<p>full</p>",
                                "<p>narrow</p>", "<p>wide</p>"],
                    solution_en: "<p>82.(c)<strong> narrow</strong><br>The phrase &lsquo;narrow escape&rsquo; means a situation in which an accident or other unfortunate incident is only just avoided. The given sentence states that Vinod had a narrow escape in the car accident. Hence, \'narrow\' is the most appropriate answer.</p>",
                    solution_hi: "<p>82.(c) <strong>narrow</strong><br>Phrase &lsquo;narrow escape&rsquo; का अर्थ ऐसी स्थिति से है जिसमें कोई दुर्घटना या अन्य दुर्भाग्यपूर्ण घटना बस टल ही जाती है। दिए गए sentence में बताया गया है कि विनोद कार दुर्घटना में बाल-बाल बच गया। अतः, \'narrow\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>83. Select the INCORRECTLY spelt word.</p>",
                    options_en: ["<p>Embarrass</p>", "<p>Connoisseur</p>", 
                                "<p>Relevent</p>", "<p>Bureaucracy</p>"],
                    options_hi: ["<p>Embarrass</p>", "<p>Connoisseur</p>",
                                "<p>Relevent</p>", "<p>Bureaucracy</p>"],
                    solution_en: "<p>83.(c) <strong>Relevent</strong><br>\'Relevant\' is the correct spelling.</p>",
                    solution_hi: "<p>83.(c)<strong> Relevent</strong><br>\'Relevant\' सही spelling है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate synonym of the word in bold. <br>We&rsquo;d better watch our step and not give him any excuse to <strong>harass</strong> us further.</p>",
                    question_hi: "<p>84. Select the most appropriate synonym of the word in bold. <br>We&rsquo;d better watch our step and not give him any excuse to <strong>harass</strong> us further.</p>",
                    options_en: ["<p>betray</p>", "<p>relish</p>", 
                                "<p>soothe</p>", "<p>intimidate</p>"],
                    options_hi: ["<p>betray</p>", "<p>relish</p>",
                                "<p>soothe</p>", "<p>intimidate</p>"],
                    solution_en: "<p>84.(d) <strong>Intimidate-</strong> to frighten or make someone feel afraid.<br><strong>Harass- </strong>to disturb or bother someone repeatedly.<br><strong>Betray-</strong> to reveal or disclose information that is meant to be kept secret.<br><strong>Relish-</strong> to enjoy or take great pleasure in something.<br><strong>Soothe-</strong> to calm or alleviate pain or discomfort.</p>",
                    solution_hi: "<p>84.(d) <strong>Intimidate (डराना/धमकाना) -</strong> to frighten or make someone feel afraid.<br><strong>Harass (उत्पीड़ित करना) -</strong> to disturb or bother someone repeatedly.<br><strong>Betray (धोखा देना) -</strong> to reveal or disclose information that is meant to be kept secret.<br><strong>Relish (आनंद लेना) -</strong> to enjoy or take great pleasure in something.<br><strong>Soothe (शांत करना) -</strong> to calm or alleviate pain or discomfort.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the option that can be used as a one-word substitute for the given phrase. <br>A short interesting story about a real person or event</p>",
                    question_hi: "<p>85. Select the option that can be used as a one-word substitute for the given phrase. <br>A short interesting story about a real person or event</p>",
                    options_en: ["<p>Poem</p>", "<p>Sketch</p>", 
                                "<p>Anecdote</p>", "<p>Narrative</p>"],
                    options_hi: ["<p>Poem</p>", "<p>Sketch</p>",
                                "<p>Anecdote</p>", "<p>Narrative</p>"],
                    solution_en: "<p>85.(c) <strong>Anecdote-</strong> a short interesting story about a real person or event.<br><strong>Poem- </strong>a piece of writing in which the words are arranged in separate lines, often ending in rhyme, and are chosen for their sound and for the images and ideas they suggest.<br><strong>Sketch-</strong> a simple, quickly-made drawing that does not have many details.<br><strong>Narrative-</strong> a spoken or written account of connected events.</p>",
                    solution_hi: "<p>85.(c) <strong>Anecdote (किस्सा) -</strong> a short interesting story about a real person or event.<br><strong>Poem (कविता) - </strong>a piece of writing in which the words are arranged in separate lines, often ending in rhyme, and are chosen for their sound and for the images and ideas they suggest.<br><strong>Sketch (रेखाचित्र) -</strong> a simple, quickly-made drawing that does not have many details.<br><strong>Narrative (कथा) -</strong> a spoken or written account of connected events.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that can be used as a one-word substitute for the underlined group of words. <br>She is <span style=\"text-decoration: underline;\">proficient in speaking many languages.</span></p>",
                    question_hi: "<p>86. Select the option that can be used as a one-word substitute for the underlined group of words. <br>She is <span style=\"text-decoration: underline;\">proficient in speaking many languages.</span></p>",
                    options_en: ["<p>Monolithic</p>", "<p>Multilingual</p>", 
                                "<p>Heterolinguistic</p>", "<p>Bilingual</p>"],
                    options_hi: ["<p>Monolithic</p>", "<p>Multilingual</p>",
                                "<p>Heterolinguistic</p>", "<p>Bilingual</p>"],
                    solution_en: "<p>(b)&nbsp;<strong>Multilingual-</strong> proficient in speaking many languages.<br><strong>Monolithic-</strong> formed of a single large block of stone.<br><strong>Heterolinguistic-</strong> pertaining to or characterized by the use of different languages.<br><strong>Bilingual-</strong> a person who speaks two languages equally well.</p>",
                    solution_hi: "<p>86.(b) <strong>Multilingual (बहुभाषी) -</strong> proficient in speaking many languages.<br><strong>Monolithic (विशालकाय) </strong>- formed of a single large block of stone.<br><strong>Heterolinguistic (विषमभाषिक) -</strong> pertaining to or characterized by the use of different languages.<br><strong>Bilingual (द्विभाषी) -</strong> a person who speaks two languages equally well.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the given idiom. <br>To have bigger fish to fry</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the given idiom. <br>To have bigger fish to fry</p>",
                    options_en: ["<p>To have an interest in cooking</p>", "<p>To know different kinds of fishing techniques</p>", 
                                "<p>To take calculated risks</p>", "<p>To have bigger things to take care of than the menial task at hand</p>"],
                    options_hi: ["<p>To have an interest in cooking</p>", "<p>To know different kinds of fishing techniques</p>",
                                "<p>To take calculated risks</p>", "<p>To have bigger things to take care of than the menial task at hand</p>"],
                    solution_en: "<p>87.(d) <strong>To have bigger fish to fry-</strong> to have bigger things to take care of than the menial task at hand.<br>E.g.- I can\'t worry about that small project right now. I have bigger fish to fry.</p>",
                    solution_hi: "<p>87.(d) <strong>To have bigger fish to fry-</strong> to have bigger things to take care of than the menial task at hand./अधिक महत्वपूर्ण काम होना।<br>E.g.- I can\'t worry about that small project right now. I have bigger fish to fry.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>My brother performed / extremely good / in the class test / held yesterday.</p>",
                    question_hi: "<p>88. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>My brother performed / extremely good / in the class test / held yesterday.</p>",
                    options_en: ["<p>held yesterday</p>", "<p>in the class test</p>", 
                                "<p>My brother performed</p>", "<p>extremely good</p>"],
                    options_hi: ["<p>held yesterday</p>", "<p>in the class test</p>",
                                "<p>My brother performed</p>", "<p>extremely good</p>"],
                    solution_en: "<p>88.(d) <strong>extremely good</strong><br>&lsquo;Good&rsquo; must be replaced with \'well\' as the given sentence needs an adverb to modify the verb &lsquo;performed&rsquo;, and not an adjective &lsquo;good&rsquo;. Hence, \'extremely well\' is the most appropriate answer.</p>",
                    solution_hi: "<p>88.(d) <strong>extremely good</strong><br>&lsquo;Good&rsquo; के स्थान पर \'well\' का प्रयोग होगा क्योंकि दिए गए sentence में verb &lsquo;performed&rsquo; को modify करने के लिए adjective &lsquo;good&rsquo; की नहीं बल्कि adverb की आवश्यकता है। अतः, \'extremely well\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate synonym of the given word. <br>Toxic</p>",
                    question_hi: "<p>89. Select the most appropriate synonym of the given word. <br>Toxic</p>",
                    options_en: ["<p>Laudatory</p>", "<p>Lanky</p>", 
                                "<p>Lethal</p>", "<p>Lethal</p>"],
                    options_hi: ["<p>Laudatory</p>", "<p>Lanky</p>",
                                "<p>Lethal</p>", "<p>Lethal</p>"],
                    solution_en: "<p>89.(c) <strong>Lethal- </strong>capable of causing death.<br><strong>Toxic-</strong> poisonous or harmful to living beings.<br><strong>Laudatory-</strong> expressing praise or commendation.<br><strong>Lanky- </strong>tall and thin, often in an awkward way.<br><strong>Licit</strong>- permitted by law.</p>",
                    solution_hi: "<p>89.(c) <strong>Lethal (घातक) -</strong> capable of causing death.<br><strong>Toxic (विषैला) -</strong> poisonous or harmful to living beings.<br><strong>Laudatory (प्रशंसनीय) -</strong> expressing praise or commendation.<br><strong>Lanky (दुबला-पतला) -</strong> tall and thin, often in an awkward way.<br><strong>Licit (वैध) </strong>- permitted by law.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one-word substitute for the given group of words. A person who likes to argue about anything</p>",
                    question_hi: "<p>90. Select the option that can be used as a one-word substitute for the given group of words. A person who likes to argue about anything</p>",
                    options_en: ["<p>Reticent</p>", "<p>Coward</p>", 
                                "<p>Veracious</p>", "<p>Contentious</p>"],
                    options_hi: ["<p>Reticent</p>", "<p>Coward</p>",
                                "<p>Veracious</p>", "<p>Contentious</p>"],
                    solution_en: "<p>90.(d) <strong>Contentious- </strong>a person who likes to argue about anything.<br><strong>Reticent-</strong> unwilling to speak about your thoughts or feelings.<br><strong>Coward-</strong> a person who is not brave, is easily frightened, or tries to avoid danger or difficulties.<br><strong>Veracious-</strong> speaking or representing the truth.</p>",
                    solution_hi: "<p>90.(d) <strong>Contentious (विवादास्पद) -</strong> a person who likes to argue about anything.<br><strong>Reticent (मितभाषी) -</strong> unwilling to speak about your thoughts or feelings.<br><strong>Coward (कायर) -</strong> a person who is not brave, is easily frightened, or tries to avoid danger or difficulties.<br><strong>Veracious (सत्यप्रिय) -</strong> speaking or representing the truth.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>She has been <span style=\"text-decoration: underline;\">studying for two o&rsquo;clock .</span></p>",
                    question_hi: "<p>91. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>She has been <span style=\"text-decoration: underline;\">studying for two o&rsquo;clock .</span></p>",
                    options_en: ["<p>study from two o&rsquo;clock</p>", "<p>studying since two o&rsquo;clock</p>", 
                                "<p>study for two o&rsquo;clock</p>", "<p>studying two o&rsquo;clock</p>"],
                    options_hi: ["<p>study from two o&rsquo;clock</p>", "<p>studying since two o&rsquo;clock</p>",
                                "<p>study for two o&rsquo;clock</p>", "<p>studying two o&rsquo;clock</p>"],
                    solution_en: "<p>91.(b) studying since two o&rsquo;clock<br>&lsquo;Since&rsquo; is used to indicate the point of time when something started. Similarly, the given sentence states the time when she started studying. Hence, &ldquo;studying since two o&rsquo; clock&rdquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>91.(b) studying since two o&rsquo;clock<br>&lsquo;Since&rsquo; का प्रयोग किसी चीज़ के प्रारंभ होने के समय को इंगित करने के लिए किया जाता है। इसी तरह, दिए गए sentence में वह समय बताया गया है जब उसने पढ़ाई शुरू की थी। अतः, &ldquo;studying since two o&rsquo; clock&rdquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. In the following sentence, four words are underlined out of which one word is misspelt. Identify the INCORRECTLY spelt word.<br>After the <span style=\"text-decoration: underline;\">recapture</span> (A) of Tololing and the adjacent features, <span style=\"text-decoration: underline;\">evacting</span> (B) the enemy from this <span style=\"text-decoration: underline;\">well-fortified</span> (C) position became a <span style=\"text-decoration: underline;\">priority</span>.(D)</p>",
                    question_hi: "<p>92. In the following sentence, four words are underlined out of which one word is misspelt. Identify the INCORRECTLY spelt word.<br>After the <span style=\"text-decoration: underline;\">recapture</span> (A) of Tololing and the adjacent features, <span style=\"text-decoration: underline;\">evacting</span> (B) the enemy from this <span style=\"text-decoration: underline;\">well-fortified</span> (C) position became a <span style=\"text-decoration: underline;\">priority</span>.(D)</p>",
                    options_en: ["<p>B</p>", "<p>D</p>", 
                                "<p>A</p>", "<p>C</p>"],
                    options_hi: ["<p>B</p>", "<p>D</p>",
                                "<p>A</p>", "<p>C</p>"],
                    solution_en: "<p>92.(a) (B) evacting<br>\'Evicting\' is the correct spelling.</p>",
                    solution_hi: "<p>92.(a) (B) evacting<br>\'Evicting\' सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the option that expresses the given sentence in active voice. <br>Lovely tunes are composed by Domnica.</p>",
                    question_hi: "<p>93. Select the option that expresses the given sentence in active voice. <br>Lovely tunes are composed by Domnica.</p>",
                    options_en: ["<p>Domnica composed lovely tunes</p>", "<p>Domnica composes tunes lovely.</p>", 
                                "<p>Domnica will compose lovely tunes.</p>", "<p>Domnica composes lovely tunes.</p>"],
                    options_hi: ["<p>Domnica composed lovely tunes</p>", "<p>Domnica composes tunes lovely.</p>",
                                "<p>Domnica will compose lovely tunes.</p>", "<p>Domnica composes lovely tunes.</p>"],
                    solution_en: "<p>93.(d) Domnica composes lovely tunes. (Correct)<br>(a) Domnica <span style=\"text-decoration: underline;\">composed</span> lovely tunes. (Incorrect Tense)<br>(b) Domnica composes tunes lovely. (Incorrect Sentence Structure)<br>(c) Domnica <span style=\"text-decoration: underline;\">will compose</span> lovely tunes. (Incorrect Tense)</p>",
                    solution_hi: "<p>93.(d) Domnica composes lovely tunes. (Correct)<br>(a) Domnica <span style=\"text-decoration: underline;\">composed</span> lovely tunes. (गलत Tense)<br>(b) Domnica composes tunes lovely. (गलत Sentence Structure)<br>(c) Domnica <span style=\"text-decoration: underline;\">will compose</span> lovely tunes. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the word which means the same as the group of words given. <br>Unit of weight for precious stones</p>",
                    question_hi: "<p>94. Select the word which means the same as the group of words given. <br>Unit of weight for precious stones</p>",
                    options_en: ["<p>Pure</p>", "<p>Reliable</p>", 
                                "<p>Carat</p>", "<p>Accurate</p>"],
                    options_hi: ["<p>Pure</p>", "<p>Reliable</p>",
                                "<p>Carat</p>", "<p>Accurate</p>"],
                    solution_en: "<p>94.(c) <strong>Carat</strong>- unit of weight for precious stones.<br><strong>Pure-</strong> not mixed or adulterated with any other substance or material.<br><strong>Reliable- </strong>able to be trusted.<br><strong>Accurate</strong>- correct, exact, and without any mistakes.</p>",
                    solution_hi: "<p>94.(c) <strong>Carat (कैरेट) -</strong> unit of weight for precious stones.<br><strong>Pure (शुद्ध) </strong>- not mixed or adulterated with any other substance or material.<br><strong>Reliable (विश्वसनीय) </strong>- able to be trusted.<br><strong>Accurate (सटीक)</strong> - correct, exact, and without any mistakes.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate synonym to replace the underlined word in the given sentence. <br>No<span style=\"text-decoration: underline;\"> altruistic</span> act is truly sincere.</p>",
                    question_hi: "<p>95. Select the most appropriate synonym to replace the underlined word in the given sentence. <br>No<span style=\"text-decoration: underline;\"> altruistic</span> act is truly sincere.</p>",
                    options_en: ["<p>philanthropic</p>", "<p>phantasmal</p>", 
                                "<p>phenomenal</p>", "<p>phonotypical</p>"],
                    options_hi: ["<p>philanthropic</p>", "<p>phantasmal</p>",
                                "<p>phenomenal</p>", "<p>phonotypical</p>"],
                    solution_en: "<p>95.(a) <strong>Philanthropic-</strong> seeking to promote the welfare of others, especially through the donation of money to good causes.<br><strong>Altruistic- </strong>showing selfless concern for the well-being of others.<br><strong>Phantasmal-</strong> not existing in nature or subject to explanation according to natural laws.<br><strong>Phenomenal- </strong>remarkable or exceptional, especially exceptionally good.<br><strong>Phonotypical-</strong> relating to or denoting a system of writing in which each symbol represents a spoken sound.</p>",
                    solution_hi: "<p>95.(a) <strong>Philanthropic (परोपकारी) -</strong> seeking to promote the welfare of others, especially through the donation of money to good causes.<br><strong>Altruistic (परोपकारी) </strong>- showing selfless concern for the well-being of others.<br><strong>Phantasmal (काल्पनिक) </strong>- not existing in nature or subject to explanation according to natural laws.<br><strong>Phenomenal (असाधारण) -</strong> remarkable or exceptional, especially exceptionally good.<br><strong>Phonotypical (ध्वन्यात्मक) -</strong> relating to or denoting a system of writing in which each symbol represents a spoken sound.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96.<strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>Moreover</p>", "<p>Therefore</p>", 
                                "<p>Furthermore</p>", "<p>However</p>"],
                    options_hi: ["<p>Moreover</p>", "<p>Therefore</p>",
                                "<p>Furthermore</p>", "<p>However</p>"],
                    solution_en: "<p>96.(d) <strong>However</strong><br>&lsquo;However&rsquo; is used to introduce a statement that contrasts with the previous information. The given passage states that coming events cast their shadows before and then states a contrasting idea that it is not universally true. Hence, \'however\' is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) <strong>However</strong><br>&lsquo;However&rsquo; का प्रयोग किसी statement को introduce करने के लिए किया जाता है जो पिछली जानकारी के विपरीत होता है। दिए गए passage में बताया गया है कि आने वाली घटनाएँ पहले अपनी छाया डालती हैं और फिर एक विपरीत विचार दिया गया है कि यह हमेशा सही नहीं होता है। अतः, \'however\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>legalise</p>", "<p>foresee</p>", 
                                "<p>rescind</p>", "<p>affect</p>"],
                    options_hi: ["<p>legalise</p>", "<p>foresee</p>",
                                "<p>rescind</p>", "<p>affect</p>"],
                    solution_en: "<p>97.(b) <strong>foresee</strong><br>&lsquo;Foresee&rsquo; means to realize or understand something in advance or before it happens. The given passage states that something can happen within a second, and one may not foresee it. Hence, \'foresee\' is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) <strong>foresee</strong><br>&lsquo;Foresee&rsquo; का अर्थ है किसी चीज़ को पहले से या उसके घटित होने से पहले महसूस करना या समझना। दिए गए passage में बताया गया है कि एक सेकंड के अंदर-अंदर कुछ हो सकता है, और कोई इसका पूर्वानुमान नहीं लगा सकता है। अतः, \'foresee\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 98</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 98</p>",
                    options_en: ["<p>Therefore</p>", "<p>Moreover</p>", 
                                "<p>Nevertheless</p>", "<p>However</p>"],
                    options_hi: ["<p>Therefore</p>", "<p>Moreover</p>",
                                "<p>Nevertheless</p>", "<p>However</p>"],
                    solution_en: "<p>98.(b) <strong>Moreover</strong><br>&lsquo;Moreover&rsquo; is used to provide additional information about something. Similarly, the given passage provides additional information that predictions based on certain signs have gone wrong. Hence, \'Moreover\' is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) <strong>Moreover</strong><br>&lsquo;Moreover&rsquo; का प्रयोग किसी चीज़ के बारे में अतिरिक्त जानकारी (additional information) प्रदान करने के लिए किया जाता है। इसी तरह, दिया गया passage अतिरिक्त जानकारी प्रदान करता है कि कुछ संकेतों पर आधारित भविष्यवाणियाँ गलत हो गई हैं। अतः, \'Moreover\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99.<strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 99</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 99</p>",
                    options_en: ["<p>Besides</p>", "<p>Secondly</p>", 
                                "<p>Therefore</p>", "<p>Despite</p>"],
                    options_hi: ["<p>Besides</p>", "<p>Secondly</p>",
                                "<p>Therefore</p>", "<p>Despite</p>"],
                    solution_en: "<p>99.(a) <strong>Besides</strong><br>&lsquo;Besides&rsquo; means furthermore or in addition to. The given passage further states that some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. Hence, \'Besides\' is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a)<strong> Besides</strong><br>&lsquo;Besides&rsquo; का अर्थ है इसके अलावा या इसके अतिरिक्त। दिए गए passage में आगे बताया गया है कि कुछ अप्राकृतिक आपदाएँ जो आने वाली हैं, वे कुछ बुरे संकेतों द्वारा अपनी छाया डालती हैं। अतः, \'Besides\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 100</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 100</p>",
                    options_en: ["<p>However</p>", "<p>Nevertheless</p>", 
                                "<p>Moreover</p>", "<p>Therefore</p>"],
                    options_hi: ["<p>However</p>", "<p>Nevertheless</p>",
                                "<p>Moreover</p>", "<p>Therefore</p>"],
                    solution_en: "<p>100.(d) <strong>Therefore</strong><br>&lsquo;Therefore&rsquo; is used to introduce a logical conclusion or result. The given passage states a conclusion that we should not completely cancel out the possibilities that animals can sense certain unnatural happenings. Hence, \'Therefore\' is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) <strong>Therefore</strong><br>&lsquo;Therefore&rsquo; का प्रयोग logical conclusion या result को बताने के लिए किया जाता है। दिए गए passage में निष्कर्ष दिया गया है कि हमें इस संभावना को पूरी तरह से नकारना नहीं चाहिए कि जानवर कुछ अप्राकृतिक घटनाओं को महसूस कर सकते हैं। अतः, \'Therefore\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>