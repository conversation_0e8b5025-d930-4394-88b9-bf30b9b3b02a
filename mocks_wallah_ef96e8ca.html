<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Identify the figure given in the options which when put in place of \'?\' will logically complete the series.<br><strong id=\"docs-internal-guid-ff25c064-7fff-3184-52fb-76149e3732e8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfkJ-KyTcan-6zR--G2LjpdVDB8AUu8VivxPuF9o7dkahVVOJgS0G8SKiSeK4wnomz5f1c9umlQH1O8ERpoSoiJLAgD92AxEfuaEDjXs8tUBOiouBGpkh7yFiuKXO5-GukpLK09aju38V4dW-0wrg4Cx6KL?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"413\" height=\"73\"></strong></p>",
                    question_hi: "<p>1. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए, जिसे\'?\' के स्थान पर रखने पर शृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><strong id=\"docs-internal-guid-a09f663d-7fff-41bc-de78-86ca9aef6381\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfkJ-KyTcan-6zR--G2LjpdVDB8AUu8VivxPuF9o7dkahVVOJgS0G8SKiSeK4wnomz5f1c9umlQH1O8ERpoSoiJLAgD92AxEfuaEDjXs8tUBOiouBGpkh7yFiuKXO5-GukpLK09aju38V4dW-0wrg4Cx6KL?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"413\" height=\"73\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-b2ee4fd6-7fff-5890-d847-9693cae01ee1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfsokLeiG8uRJtRyyYUPdAFulo-gmyDvgIf9MfUi115v_wHXKs5MzhaQEVLEChwXDrSdgUkHsIbZTEp6Q70k8n-oKq5Eil83o9A9SQfuguVCXIWCh_Nue3uZB54x1y7_VEtwHj7c9IDhKFveVR9eC8tRfG-?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"131\" height=\"131\"></strong></p>", "<p><strong id=\"docs-internal-guid-cf3afd0c-7fff-887d-5510-83e332de6f00\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe3riGvr13m0WxLpJkStCH3jHz7ExMMRJYWW3io74eLn9_gyJbF0ohjL5Vm4VlrM66Vu9FB0kBJpScOy-SZb28_bQzg0NR3rQOX4ms5LKR17rBdTg1gpJV3Yta5awuc6wBwsiWhSrEljQUxXvVMdglsA3Zb?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"131\" height=\"132\"></strong></p>", 
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQ2gOxag8C0k-A6vyXr8DoCt5XS08WgPQNum_NDdpS0jZ7Klh3U-DDNDXiWt4qoe8J9fzKMb6xGjiULRDgC2qlzZP_dXOWaUnmwI3BL_jWztkoMyuARFJojadBb4FTBmgNocNJMzYUvHt94iQg14di9Dkv?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"133\" height=\"133\"></p>", "<p><strong id=\"docs-internal-guid-8eb61eaa-7fff-0c00-03bb-417fca89743c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdKUfWuPzgDRJfqWHwQuJVIu0g0bOu3kkeprpmbG90666Ylu1qVrFIJrxm_eMm81bmtzi3hMubPq9UK0EIp2FrwDDTekc4rdPB6Ss2pCWtKvAHgyTis8urAZhqrYmcBN_-RXfMNNwT0cdvnBjYT_oG3MqE?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"136\" height=\"137\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-64a9688c-7fff-670c-e8df-b5d01caaa3e6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfsokLeiG8uRJtRyyYUPdAFulo-gmyDvgIf9MfUi115v_wHXKs5MzhaQEVLEChwXDrSdgUkHsIbZTEp6Q70k8n-oKq5Eil83o9A9SQfuguVCXIWCh_Nue3uZB54x1y7_VEtwHj7c9IDhKFveVR9eC8tRfG-?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"131\" height=\"131\"></strong></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe3riGvr13m0WxLpJkStCH3jHz7ExMMRJYWW3io74eLn9_gyJbF0ohjL5Vm4VlrM66Vu9FB0kBJpScOy-SZb28_bQzg0NR3rQOX4ms5LKR17rBdTg1gpJV3Yta5awuc6wBwsiWhSrEljQUxXvVMdglsA3Zb?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"131\" height=\"132\"></p>",
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQ2gOxag8C0k-A6vyXr8DoCt5XS08WgPQNum_NDdpS0jZ7Klh3U-DDNDXiWt4qoe8J9fzKMb6xGjiULRDgC2qlzZP_dXOWaUnmwI3BL_jWztkoMyuARFJojadBb4FTBmgNocNJMzYUvHt94iQg14di9Dkv?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"133\" height=\"133\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdKUfWuPzgDRJfqWHwQuJVIu0g0bOu3kkeprpmbG90666Ylu1qVrFIJrxm_eMm81bmtzi3hMubPq9UK0EIp2FrwDDTekc4rdPB6Ss2pCWtKvAHgyTis8urAZhqrYmcBN_-RXfMNNwT0cdvnBjYT_oG3MqE?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"136\" height=\"137\"></p>"],
                    solution_en: "<p>1.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfmhj3Qan4odioEl8bFdEViYdJKyHdjAe35uKWcbjQZzof8XJhUoqfcJ8QzbIjN_mTtgpOPla1Ml0CcEYtA6JpIX4BaBD7mu-_UlwqXFq0AB5aWb58IlgVm7qO1G53p2HdZhcRlmHj71-pcHpsXxrH_pL2k?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"136\" height=\"137\"></p>",
                    solution_hi: "<p>1.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfmhj3Qan4odioEl8bFdEViYdJKyHdjAe35uKWcbjQZzof8XJhUoqfcJ8QzbIjN_mTtgpOPla1Ml0CcEYtA6JpIX4BaBD7mu-_UlwqXFq0AB5aWb58IlgVm7qO1G53p2HdZhcRlmHj71-pcHpsXxrH_pL2k?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"136\" height=\"137\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. 34 is related to 191 by certain logic. Following the same logic, 56 is related to 301. To which of the following is 63 related, following the same logic?<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>2. 34 एक निश्चित तर्क का अनुसरण करते हुए 191 से संबंधित है। इसी तर्क का अनुसरण करते हुए 56, 301 से संबंधित है। समान तर्क का अनुसरण करते हुए, 63 निम्नलिखित में से किससे संबंधित है?<br>(<strong>नोट</strong>: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें-13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>340</p>", "<p>386</p>", 
                                "<p>336</p>", "<p>319</p>"],
                    options_hi: ["<p>340</p>", "<p>386</p>",
                                "<p>336</p>", "<p>319</p>"],
                    solution_en: "<p>2.(c) <strong>Logic</strong>: (1st number &times; 5) + 21 = 2nd number<br>(34 : 191) :- (34 &times; 5) + 21 = 191 <br>(56 : 301) :- (56 &times; 5) + 21 = 301 <br>Similarly<br>(63 : <math display=\"inline\"><mi>x</mi></math>) :- (63 &times; 5) + 21 = 336</p>",
                    solution_hi: "<p>2.(c) <strong>तर्क</strong>: (पहली संख्या &times; 5) + 21 = दूसरी संख्या<br>(34 : 191) :- (34 &times; 5) + 21 = 191 <br>(56 : 301) :- (56 &times; 5) + 21 = 301 <br>इसी प्रकार<br>(63 : <math display=\"inline\"><mi>x</mi></math>) :- (63 &times; 5) + 21 = 336</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language,<br>\'A + B\' means \'A is the mother of B\';<br>\'A- B\' means \'A is the brother of B\';<br>\'A ✕ B\' means \'A is the wife of B\' and<br>\'A &divide; B\' means \'A is the father of B\'.<br>Based on the above, how is 5 related to 2 if \'5 &divide; 1 ✕ 3 &divide; 4 - 2\'?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है \'A, B की माता है\';<br>\'A - B\' का अर्थ है \'A, B का भाई है;<br>\'A ✕ B\' का अर्थ है \'A, B की पत्नी है और<br>\'A &divide; B\' का अर्थ है \'A, B का पिता है।<br>उपर्युक्त के आधार पर, यदि \'5 &divide; 1 ✕ 3 &divide; 4 - 2\' है, तो 5, 2 से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Father\'s mother</p>", "<p>Father\'s father</p>", 
                                "<p>Mother\'s mother</p>", "<p>Mother\'s father</p>"],
                    options_hi: ["<p>पिता की माता</p>", "<p>पिता का पिता</p>",
                                "<p>माता की माता</p>", "<p>माता का पिता</p>"],
                    solution_en: "<p>3.(d)<br><strong id=\"docs-internal-guid-52e1b26d-7fff-1bf2-9acb-6243f1d9cee4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQzjsm4mY7VsFTLXUWVue7orLdN_TXZJjqqBab-dBqhjb2tDj3H4WMGDCU_o8NVlJoVQ19ydhloG_xIq0kQigpblUm_XyzxKEQptUlFMZq5kkmkAJq7uhaBJEY8Gg1dInanIArsmYhiZIZTef4r5m4PJBU?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"133\" height=\"186\"></strong><br>&lsquo;5&rsquo; is the Mother\'s father of &lsquo;2&rsquo;</p>",
                    solution_hi: "<p>3.(d)<br><strong id=\"docs-internal-guid-52e1b26d-7fff-1bf2-9acb-6243f1d9cee4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQzjsm4mY7VsFTLXUWVue7orLdN_TXZJjqqBab-dBqhjb2tDj3H4WMGDCU_o8NVlJoVQ19ydhloG_xIq0kQigpblUm_XyzxKEQptUlFMZq5kkmkAJq7uhaBJEY8Gg1dInanIArsmYhiZIZTef4r5m4PJBU?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"133\" height=\"186\"></strong><br>\'5\', \'2\' की माता का पिता है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. 27 is related to 119 following a certain logic. Following the same logic, 33 is related to 143. To which of the following is 42 related, following the same logic? (<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>4. एक निश्चित तर्क के अनुसार 27 का संबंध 119 से है। उसी तर्क का अनुसरण करते हुए, 33 का संबंध 143 से है। उसी तर्क का अनुसरण करते हुए, 42 का संबंध निम्नलिखित में से किससे है? <br><strong>नोट </strong>: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें-13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>153</p>", "<p>188</p>", 
                                "<p>179</p>", "<p>162</p>"],
                    options_hi: ["<p>153</p>", "<p>188</p>",
                                "<p>179</p>", "<p>162</p>"],
                    solution_en: "<p>4.(c) <strong>Logic</strong>: (1st number &times; 4) + 11 = 2nd number.<br>(27 : 119) :- 27 &times; 4 + 11 = 119 <br>(33 : 143) :- 33 &times; 4 + 11 = 143<br>Similarly<br>(42 : <math display=\"inline\"><mi>x</mi></math>) :- 42 &times; 4 + 11 = 179</p>",
                    solution_hi: "<p>4.(c) <strong>तर्क</strong>: (पहली संख्या &times; 4 )+ 11 = दूसरी संख्या<br>(27 : 119) :- 27 &times; 4 + 11 = 119 <br>(33 : 143) :- 33 &times; 4 + 11 = 143<br>इसी प्रकार<br>(42 : <math display=\"inline\"><mi>x</mi></math>) :- 42 &times; 4 + 11 = 179</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different.<br><strong>Note </strong>: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>5. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस अक्षर-समूह का चयन करें जो भिन्न हो।<br><strong>नोट </strong>: अक्षर समूह में, भिन्न व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।.</p>",
                    options_en: ["<p>HLJ</p>", "<p>GIK</p>", 
                                "<p>LPN</p>", "<p>BFD</p>"],
                    options_hi: ["<p>HLJ</p>", "<p>GIK</p>",
                                "<p>LPN</p>", "<p>BFD</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdP-_eRLjC8cvKGGSioLBNOJWzoTxOsYReTlEi0yIHI7cbodtxFiZxQWBN4vLpfNOxxcX59leCG3wJcxG8zc9AZo6pHZBLyRiY-4-4d8R4KkegORyjwfb_CzvZYMpVWPHWEZjBeWBmbVSnEoXn4bG5FD2HU?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"109\" height=\"57\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd1t6RvxQE_NX2Ca8w_t3T07C9iEwfyR6nQMLixkOCY2vz21ieyq-BlG4JE39DVHsCoyi_QVaXWK9AHxrOf2-EEdFs7Z4t176TtJ1W3qcicYyurv0cjbQTutOASwzXgDqw28ziUXLqM2SnRyQVcrQkVbcM?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"114\" height=\"42\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEW4QOVRAGfS5Tuyg2MPRVHeUoW-PAqSPPRX7jYHIuawblMbisVkZftaXt8Z7RrWkP4V1xszi3dov-hIcrOAyjhEa3mIhC-xPPypRulsNLtl4tjfZGr5DZNcu430R6lf5El58UoIdoKVR1NQV2KO82Xvkl?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"110\" height=\"48\"><br>But<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeG2T0AGAVf3e1aXHDt-OpPnN20DCvzmMepRccRNzxQh6D1Xv0BUv2QJu0sqFVHpvmXyVp7xGJWbdhNkOgJJdMold6TBglRBwxmthHsaUgg3nYJKff6K_sZhWZ3SbP-DT5yJOaQ950d3VXDQbF7YsPUYo3Y?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"112\" height=\"58\"></p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdP-_eRLjC8cvKGGSioLBNOJWzoTxOsYReTlEi0yIHI7cbodtxFiZxQWBN4vLpfNOxxcX59leCG3wJcxG8zc9AZo6pHZBLyRiY-4-4d8R4KkegORyjwfb_CzvZYMpVWPHWEZjBeWBmbVSnEoXn4bG5FD2HU?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"114\" height=\"60\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd1t6RvxQE_NX2Ca8w_t3T07C9iEwfyR6nQMLixkOCY2vz21ieyq-BlG4JE39DVHsCoyi_QVaXWK9AHxrOf2-EEdFs7Z4t176TtJ1W3qcicYyurv0cjbQTutOASwzXgDqw28ziUXLqM2SnRyQVcrQkVbcM?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"114\" height=\"42\"></p>\n<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEW4QOVRAGfS5Tuyg2MPRVHeUoW-PAqSPPRX7jYHIuawblMbisVkZftaXt8Z7RrWkP4V1xszi3dov-hIcrOAyjhEa3mIhC-xPPypRulsNLtl4tjfZGr5DZNcu430R6lf5El58UoIdoKVR1NQV2KO82Xvkl?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"110\" height=\"48\"><br>लेकिन <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeG2T0AGAVf3e1aXHDt-OpPnN20DCvzmMepRccRNzxQh6D1Xv0BUv2QJu0sqFVHpvmXyVp7xGJWbdhNkOgJJdMold6TBglRBwxmthHsaUgg3nYJKff6K_sZhWZ3SbP-DT5yJOaQ950d3VXDQbF7YsPUYo3Y?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"115\" height=\"60\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What will come in place of \'?\' in the following equation, if \'+\' and \'-\' are interchanged and \'✕\' and \'&divide;\' are interchanged?<br>63 ✕ 9 + 21 - 4 &divide; 5 = ?</p>",
                    question_hi: "<p>6. यदि \'+\' और \'-\' को परस्पर बदल दिया जाए और \'✕\' और \'&divide;\' को परस्पर बदल दिया जाए, तो निम्नलिखित समीकरण में \'?\' के स्थान पर कितना मान आएगा?<br>63 ✕ 9 + 21 - 4 &divide; 5 = ?</p>",
                    options_en: ["<p>4</p>", "<p>7</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>4</p>", "<p>7</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>6.(c) <strong>Given</strong>: 63 ✕ 9 + 21 - 4 &divide; 5 = ?<br>As per the instruction given in the question, after interchanging the symbol \'+\' and \'-\' and \'✕\' and \'&divide;\' we get <br>63 &divide; 9 - 21 + 4 &times; 5 = ?<br>7 - 21 + 4 &times; 5 <br>7 - 21 + 20 <br>27 - 21 = 6</p>",
                    solution_hi: "<p>6.(c) <strong>दिया गया है</strong>: 63 ✕ 9 + 21 - 4 &divide; 5 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीक \'+\' और \'-\' तथा \'✕\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>63 &divide; 9 - 21 + 4 &times; 5 = ?<br>7 - 21 + 4 &times; 5 <br>7 - 21 + 20 <br>27 - 21 = 6</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><strong id=\"docs-internal-guid-073d5899-7fff-d82a-ed1c-9f2651671886\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc9FUhc14cMOEDIRBEU6bD84WJygEWnITa14mZez5JX9PM041O6NkqXwRtI_QnNLmKD827pNSsuouYMX5nrWRY1IcvAHYTw-eAPO2d5J1EmUHAAJp1ucCJEsx5s4RavnhAASRrtcQjZnjq4LD5tg9xLu9i_?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"384\" height=\"77\"></strong></p>",
                    question_hi: "<p>7. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><strong id=\"docs-internal-guid-ef70e367-7fff-848f-10fe-076378068bfa\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc9FUhc14cMOEDIRBEU6bD84WJygEWnITa14mZez5JX9PM041O6NkqXwRtI_QnNLmKD827pNSsuouYMX5nrWRY1IcvAHYTw-eAPO2d5J1EmUHAAJp1ucCJEsx5s4RavnhAASRrtcQjZnjq4LD5tg9xLu9i_?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"384\" height=\"77\"></strong></p>",
                    options_en: ["<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQfphpNS2BXOexQrKy_V7efsqGvlu9zVWSY2yBPT-YHKebgBhXvJYCn2GQb5dXGrcV_qUQ8By-W3XIDpvZNQ46yu2VPRstlp8-JlVtoAzt-ybFwCwKwUKYfBLqH05cnJDi_IckFAZr0C5CW6SUPKKDEtVk?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"145\" height=\"146\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcJUo_XfuOB3H53bAyQl9isFP40FVdScxZOAeJxIWfMvSXGHeVhRsIjZfUBG5CrJfhoaWhJTznpUdTJJDokAulcJn0bSfG1QIYE-FQGWAFhQnrrlZvrYaAwp05i7qL2GN2p0qqiw3GecJaAm4gMrpxO9bHK?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"147\" height=\"148\"></p>", 
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdYJmnV6LKensYREvni4DlmmasFToXXf-6MzYJeQnnRTerA-dH6VA29Yl46F8_iSCOrPPv6vGh-2_Thz-_4XmWOV0J231I1IjcsvxFyMYAsMVOZDxBFzFjiZoOeee62iDZWcJjy4vhRqltxvE-z0jchmcUE?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"150\" height=\"152\"></p>", "<p><strong id=\"docs-internal-guid-a79c02a0-7fff-39ac-7a6c-669dbf1488e4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeegxRtWHVuQnX184T7VU0StgzlxhfkdHCjXnLdvHUkmSzEvRmRghD6CMkVcTI-IFyBy0_CwCDfgVsA6AYJ_y5eHzDDuvTGXjFlPYQIHN1Anh8ln7KvSoQMbSw8iBYjFcXoQb_lcnka39jIvYHqmF6waOVf?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"150\" height=\"151\"></strong></p>"],
                    options_hi: ["<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQfphpNS2BXOexQrKy_V7efsqGvlu9zVWSY2yBPT-YHKebgBhXvJYCn2GQb5dXGrcV_qUQ8By-W3XIDpvZNQ46yu2VPRstlp8-JlVtoAzt-ybFwCwKwUKYfBLqH05cnJDi_IckFAZr0C5CW6SUPKKDEtVk?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"145\" height=\"146\"></p>", "<p><strong id=\"docs-internal-guid-0e9bb3f4-7fff-dadf-9e33-79d792317f56\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcJUo_XfuOB3H53bAyQl9isFP40FVdScxZOAeJxIWfMvSXGHeVhRsIjZfUBG5CrJfhoaWhJTznpUdTJJDokAulcJn0bSfG1QIYE-FQGWAFhQnrrlZvrYaAwp05i7qL2GN2p0qqiw3GecJaAm4gMrpxO9bHK?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"147\" height=\"148\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-f306c5e2-7fff-0e1b-f29b-e0f38eccd422\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdYJmnV6LKensYREvni4DlmmasFToXXf-6MzYJeQnnRTerA-dH6VA29Yl46F8_iSCOrPPv6vGh-2_Thz-_4XmWOV0J231I1IjcsvxFyMYAsMVOZDxBFzFjiZoOeee62iDZWcJjy4vhRqltxvE-z0jchmcUE?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"150\" height=\"152\"></strong></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeegxRtWHVuQnX184T7VU0StgzlxhfkdHCjXnLdvHUkmSzEvRmRghD6CMkVcTI-IFyBy0_CwCDfgVsA6AYJ_y5eHzDDuvTGXjFlPYQIHN1Anh8ln7KvSoQMbSw8iBYjFcXoQb_lcnka39jIvYHqmF6waOVf?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"150\" height=\"151\"></p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeegxRtWHVuQnX184T7VU0StgzlxhfkdHCjXnLdvHUkmSzEvRmRghD6CMkVcTI-IFyBy0_CwCDfgVsA6AYJ_y5eHzDDuvTGXjFlPYQIHN1Anh8ln7KvSoQMbSw8iBYjFcXoQb_lcnka39jIvYHqmF6waOVf?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"150\" height=\"151\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeegxRtWHVuQnX184T7VU0StgzlxhfkdHCjXnLdvHUkmSzEvRmRghD6CMkVcTI-IFyBy0_CwCDfgVsA6AYJ_y5eHzDDuvTGXjFlPYQIHN1Anh8ln7KvSoQMbSw8iBYjFcXoQb_lcnka39jIvYHqmF6waOVf?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"150\" height=\"151\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the number from among the given options that can replace the question mark (?) in the following series.<br>949, 1006, 1066, 1129, 1195, ?</p>",
                    question_hi: "<p>8. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित श्रृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>949, 1006, 1066, 1129, 1195, ?</p>",
                    options_en: ["<p>1233</p>", "<p>1264</p>", 
                                "<p>1244</p>", "<p>1254</p>"],
                    options_hi: ["<p>1233</p>", "<p>1264</p>",
                                "<p>1244</p>", "<p>1254</p>"],
                    solution_en: "<p>8.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdEAbNyt77-BybJn03EkJqb3diXimVszvgws10u-c6kvlnB0oU6YlAiiIJFfDOh8Z3dd4GEdlHm9VtnAbBjyfupquJZQQ3BlenVClPH8_vJaTv0mBOjxuU1jH-rpFpnRkiONaklD10M2Hu05a7l-XW3NY4?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"313\" height=\"95\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdEAbNyt77-BybJn03EkJqb3diXimVszvgws10u-c6kvlnB0oU6YlAiiIJFfDOh8Z3dd4GEdlHm9VtnAbBjyfupquJZQQ3BlenVClPH8_vJaTv0mBOjxuU1jH-rpFpnRkiONaklD10M2Hu05a7l-XW3NY4?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"313\" height=\"95\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In this question, three statements are given, followed by three conclusions numbered I, Il and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All computers are printers.<br>Some printers are laptops.<br>All laptops are mobiles.<br><strong>Conclusions </strong>:<br>I. Some printers are computers.<br>II. Some laptops are printers.<br>III. Some mobiles are laptops.</p>",
                    question_hi: "<p>9. इस प्रश्न में, तीन कथन और उसके बाद तीन निष्कर्ष क्रमांक।, ॥ और II। दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/ हैं।<br><strong>कथन </strong>:<br>सभी कंप्यूटर, प्रिंटर हैं।<br>कुछ प्रिंटर, लैपटॉप हैं।<br>सभी लैपटॉप, मोबाइल हैं।<br><strong>निष्कर्ष </strong>:<br>I. कुछ प्रिंटर, कंप्यूटर हैं।<br>II. कुछ लैपटॉप, प्रिंटर हैं।<br>III. कुछ मोबाइल, लैपटॉप हैं।</p>",
                    options_en: ["<p>Only conclusion III follows.</p>", "<p>Only conclusions II and III follow.</p>", 
                                "<p>All the conclusions follow.</p>", "<p>Only conclusions I and III follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष III अनुसरण करता है।</p>", "<p>केवल निष्कर्ष II और III अनुसरण करते हैं।</p>",
                                "<p>सभी निष्कर्ष अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष। और III अनुसरण करते हैं।</p>"],
                    solution_en: "<p>9.(c)</p>\n<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQ8lmr6YX-hS92nMr_WgNKfE6yt9SehA2uC1680M5ikg2vvStEuGAkU10RMPPvrNCT-nBdIWWQxCllfytRfz0TlGyA6ni34fffTCMYwCHF8RRTg_0A_AUYFcbmW0QNj8CRjr_E9lL5bJ0JXayd8WszoLbA?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"237\" height=\"111\"><br>All the conclusions follow.</p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe0qED2gQgtad_3ZJuBEdaZuVeVrHgR_BXKw0NX6Nxmouj_E9BE4-1Puo1Qf-wcV2eaTOyba2uaUgRmXMrjtkSbUK2ZkaYWYi7GEXYiEs5pIJWtESZoJSs1Ta0lpeKg3y7QXgr_EwYehhP5oXA4J_zan45R?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"237\" height=\"108\"><br>सभी निष्कर्ष अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. \'ACKO\' is related to \'ZXPL\' in a certain way based on the English alphabetical order. In the same way, \'HIND\' is related to \'SRMW\'. To which of the following is \'PUKE\' related, following the same logic?</p>",
                    question_hi: "<p>10. अँग्रेजी वर्णमाला-क्रम के आधार पर \'ACKO\' एक निश्चित तरीके से \'ZXPL\' से संबंधित है। उसी तरह \'HIND\' का संबंध \'SRMW\' से है। उसी तर्क के अनुसार \'PUKE\' का संबंध निम्नलिखित में से किससे है?</p>",
                    options_en: ["<p>JGQW</p>", "<p>JGPW</p>", 
                                "<p>LFQV</p>", "<p>KFPV</p>"],
                    options_hi: ["<p>JGQW</p>", "<p>JGPW</p>",
                                "<p>LFQV</p>", "<p>KFPV</p>"],
                    solution_en: "<p>10.(d)<br><strong id=\"docs-internal-guid-3876e8f3-7fff-a9e8-a6a3-2eda1db8976e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXej3fAoeSq76Hwj-oYSJJsdoBSAkVT5Nm78qQmyXahs3tVxh8GHNGvJpCqfPSAWzXCEfEdUzTQ_wfhUKuCEh4EzHA-Bg8rRUUTE2kJ2S1n_akGqYNTKbGqq2HhbHHFyVowXJVtPpvkFAnxKlzmaSYQ16qTX?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"110\" height=\"102\"></strong><br><strong id=\"docs-internal-guid-c32ccb3a-7fff-c022-d7dc-8d9d43134129\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXceYH8gBEa-SvXIrGj3KeD_6FEeQ84rf-nGR7Z9cGHQzD4JJ5tjB2QACv-p33M25vhzpdv8ZEC8xNhF0ZGQ8uLBOY5g5WZ90JpLEZ32T_UstroSqi7wwpV3lpYy_cZVJu4Y81RuylZQPk9MD0ET4Gejc3nj?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"102\" height=\"99\"></strong><br>Similarly<br><strong id=\"docs-internal-guid-f93d2953-7fff-f085-2a20-470cbbacc53c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcCc2wbE4ATv2oJRzbLMZ9TOxu92zrhpv8SY0VnmA793Vhrfm_4G64Uq4Ly9jbo1I65LkSMjh3lH0SvITn5iJj_mcGMuyCPFXSujdLEqxJjB2mmPWlqfjb--yZKCA0UlmditwfCCMpZadTFBsdxjSL1Xesf?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"100\" height=\"93\"></strong></p>",
                    solution_hi: "<p>10.(d)<br><strong id=\"docs-internal-guid-f4be2507-7fff-74ea-272e-af29019cd9ca\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf4Qr8A9_sAz0R9AK6AokuIYn_RG7mcQwNxA4M989HjeRm_xjcqkgEs0fcD_Cf88-UL1WWmcZwiq4otVNt0hKAUIMjhT_RAOIlWhIIWkbbECkkN7HahrI7lupg7vGmOTE0jEYX_qNC58hhPIrJIyy2YG5Vb?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"121\" height=\"114\"></strong><br><strong id=\"docs-internal-guid-55dea3c5-7fff-8b2b-0c8c-196037fb2078\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeyuvvaLeNdX5byb_1liUGQB1BQ8pmwOoonz8vjbBg5DGsA4V08NI6Hi53tFvA-djrVFrjXbkVye3biJKFU7Nph0pMJLW_5fUFlVQQ3zaRISvJgoGchx9ejI5YpxKLOMWNGsT6u7qNDvjzau18FjZI7wWws?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"117\" height=\"115\"></strong><br>इसी प्रकार <br><strong id=\"docs-internal-guid-a7d64a7a-7fff-9ba0-150a-4d2d34dea097\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc_d42s4G7RWymGk7-jUus8bfvIFtYpGncmREnvel4j3mLw68jrLos7SzK6Rh_E5rebY5BUpib0PQwR4wke20UvXQuS-watvln5DhoPetRv_SpUGAVVfrr_r7C0rX-RikuZgTPdBUk8nCG7851UP4oEiwP2?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"115\" height=\"109\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. In a certain code language, \'What you think you become\' is written as \'Fe Mo Su Ta Za\' and \'What you think you achieve\' is written as \'Mo Su Te Fe Za\'. How will \'achieve\' be written in that language?",
                    question_hi: "11. एक निश्चित कूट भाषा में, \'What you think you become\' को \'Fe Mo Su Ta Za\' के रूप में लिखा जाता है और \'What you think you achieve\' को \'Mo Su Te Fe Za\' के रूप में लिखा जाता है। उसी भाषा में \'achieve\' को किस प्रकार लिखा जाएगा?",
                    options_en: [" Za", " Mo ", 
                                " Te", " Fe"],
                    options_hi: [" Za", " Mo ",
                                " Te", " Fe"],
                    solution_en: "11.(c)  \'What you think you become\' <math display=\"inline\"><mo>→</mo></math> \'Fe Mo Su Ta Za\' ……….. (i)<br />\'What you think you achieve\' <math display=\"inline\"><mo>→</mo></math> \'Mo Su Te Fe Za  ……….(ii)<br />From (i) and (ii)  \'What you think you’ is common <br />So, the code of ‘achieve’ is ‘Te’",
                    solution_hi: "11.(c)  \'What you think you become\' <math display=\"inline\"><mo>→</mo></math> \'Fe Mo Su Ta Za\' ……….. (i)<br />\'What you think you achieve\' <math display=\"inline\"><mo>→</mo></math> \'Mo Su Te Fe Za  ……….(ii)<br />(i) और (ii) से  \'What you think you’ उभयनिष्ट है<br />तो, \'achieve\' का कोड \'te\' है<br /> ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. \'Noise\' is related to \'Silence\' in the same way as \'Light\' is related to \' _________ .",
                    question_hi: "12. \'शोर\' का संबंध \'मौन\' से उसी प्रकार है, जैसे \'प्रकाश\' का संबंध \' ____________ ‘ से है।",
                    options_en: ["  Radiant", "  Warmth", 
                                "  Brightness", " Darkness<br /> "],
                    options_hi: [" दीप्तिमान ", "  गरमाहट",
                                "  चमक", " अंधकार"],
                    solution_en: "12.(d)<br />‘Noise’ and ‘Silence’ are opposites. Similarly, ‘Light’ and ‘Darkness’ are opposites. ",
                    solution_hi: "12.(d)<br />\'शोर\' और \'मौन\'  विपरीत हैं। इसी प्रकार, \'प्रकाश\' और \'अंधकार\' विपरीत हैं।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Which of the following terms will replace the question mark (?) in the given series?<br>EQBY, GPDX, ?, KNHV, MMJU</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सा पद दी गई शृंखला में प्रश्न-चिह्न (?) का स्थान लेगा?<br>EQBY, GPDX, ?, KNHV, MMJU</p>",
                    options_en: ["<p>IOFW</p>", "<p>IPFW</p>", 
                                "<p>HOFW</p>", "<p>IOEW</p>"],
                    options_hi: ["<p>IOFW</p>", "<p>IPFW</p>",
                                "<p>HOFW</p>", "<p>IOEW</p>"],
                    solution_en: "<p>13.(a).<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcB37v4KgE-14GP8CROEsZCpw-eKbBH-wdh28Gn1BEfqeUeW_Z7e5Xl-crcWOZmHdeA7-N2LJXz2KRdy1G6Ix3mni1CY-MemJPUfufvg8QtekU0FsGmquta4XahMg6qZ5TO1wpgiS_o85GV02Jb2UTWbIx7?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"316\" height=\"106\"></p>",
                    solution_hi: "<p>13.(a).<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcB37v4KgE-14GP8CROEsZCpw-eKbBH-wdh28Gn1BEfqeUeW_Z7e5Xl-crcWOZmHdeA7-N2LJXz2KRdy1G6Ix3mni1CY-MemJPUfufvg8QtekU0FsGmquta4XahMg6qZ5TO1wpgiS_o85GV02Jb2UTWbIx7?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"316\" height=\"106\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the number that will come in the place of the question mark(?), if \'+\' and \' -\' are interchanged and \'✕\' and \'&divide;\' are interchanged<br>31 &divide; 3 - 186 ✕ 6 + 17 = ?</p>",
                    question_hi: "<p>14. यदि निम्नलिखित समीकरण में \'+\' और &lsquo;-&rsquo; को आपस में बदल दिया जाए और \'✕\' और \'&divide;\' को आपस में बदल दिया जाए, तो प्रश्न चिह्न (?) के स्थान पर कौन-सी संख्या आएगी ?<br>31 &divide; 3 - 186 ✕ 6 + 17 = ?</p>",
                    options_en: ["<p>103</p>", "<p>96</p>", 
                                "<p>118</p>", "<p>107</p>"],
                    options_hi: ["<p>103</p>", "<p>96</p>",
                                "<p>118</p>", "<p>107</p>"],
                    solution_en: "<p>14.(d) <strong>Given</strong>: 31 &divide; 3 - 186 ✕ 6 + 17 = ?<br>As per instruction given in question, after interchanging the symbol \'+\' and \' -\' and \'✕\' and \'&divide;\' we get<br>31 &times; 3 + 186 &divide; 6 - 17 = ?<br>31 &times; 3 + 31 - 17<br>93 + 31 - 17<br>124 - 17 = 107</p>",
                    solution_hi: "<p>14.(d) <strong>दिया गया है</strong>: 31 &divide; 3 - 186 ✕ 6 + 17 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीक \'+\' और \'-\' और \'✕\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>31 &times; 3 + 186 &divide; 6 - 17 = ?<br>31 &times; 3 + 31 - 17<br>93 + 31 - 17<br>124 - 17 = 107</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Read the given statements and conclusions carefully. You have to take the given statements to be true even if they seem to be at variance from commonly known facts. You have to decide which conclusion/s logically follow/s from the given statements.<br><strong>Statements </strong>:<br>Some telephones are curtains.<br>All curtains are lamps.<br>No lamps is books.<br><strong>Conclusions </strong>:<br>(I) All lamps are telephones.<br>(II) No books is lamps.</p>",
                    question_hi: "<p>15. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। आपको दिए गए कथनों को सत्य मानना है भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों। आपको यह तय करना है कि कौन सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता/करते है/ हैं।<br><strong>कथन </strong>:<br>कुछ टेलीफोन, पर्दे हैं।<br>सभी पर्दे, लैम्प हैं।<br>कोई भी लैम्प, पुस्तक नहीं हैं।<br><strong>निष्कर्ष </strong>:<br>(I) सभी लैम्प, टेलीफोन हैं।<br>(II) कोई भी पुस्तक, लैम्प नहीं हैं।</p>",
                    options_en: ["<p>Only conclusion (I) is follow</p>", "<p>Only conclusion (II) is follow</p>", 
                                "<p>Neither conclusion (I) nor (II) is follow</p>", "<p>Both conclusions (I) and (II) are follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                                "<p>न तो निष्कर्ष (I) और न ही (II) अनुसरण करता है।</p>", "<p>निष्कर्ष (I) और (II), दोनों अनुसरण करते हैं।</p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdRhg-v1Y7KZKrXmGkI9KlDqgWXau_6r1tmcciKvJWJCYZk217r2puqOo_tysxEybBJCrGdKROInnqcAhPNO-PNV2bDrcn8vn0xkz2rJUSUOYrHixyYSGQ0Cpn04b312Uk4d1FvtHNGdilN-2zsSDi9QIs?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"337\" height=\"87\"><br>Only conclusion (II) is follow</p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeZyKbaXnbI3ibikJWnsuth30U8sC21s4gim2W6Zfh2NwgiAeEEKvcAmyCLyDrCzx0p-G596RTcJs-duhwh-2hGUM9CDuN-MROTz_mUiFF-M29KxOJDJ-MVC4YaylTpWN026indnaQeBDmN8tGHPlVawv5_?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"348\" height=\"92\"><br>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. How many squares are there in the given figure?<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdPQz7z7GnsEuYUGB6_GfeajFdKM-KXM5BeTirYoPpwWI8b1em3ydd4ObV6AYs5ohiAyKIvR_BrCkw6TAKtZj41fte8bOgaSjmCF0xtdiOxTXrBENkMJFQSssFKYfklzic0npuvrSAEUJgDAem5NPoYAepx?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"139\" height=\"150\"></p>",
                    question_hi: "<p>16. दी गई आकृति में कितने वर्ग हैं?<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdPQz7z7GnsEuYUGB6_GfeajFdKM-KXM5BeTirYoPpwWI8b1em3ydd4ObV6AYs5ohiAyKIvR_BrCkw6TAKtZj41fte8bOgaSjmCF0xtdiOxTXrBENkMJFQSssFKYfklzic0npuvrSAEUJgDAem5NPoYAepx?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"139\" height=\"150\"></p>",
                    options_en: ["<p>17</p>", "<p>20</p>", 
                                "<p>18</p>", "<p>19</p>"],
                    options_hi: ["<p>17</p>", "<p>20</p>",
                                "<p>18</p>", "<p>19</p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfWtzidpm0VDVB632nfnADHK5ohQtGKu1wsxsYk0O0PjMkefP1x1tNtTarSdAhYBuZ2Ps57_y8POL6adJtXMoU8aTHBsdiGLXeByJn8XVTnh_4pPqKZH_GQrpeD8u8QymEOZdhQzHoY71Y8NVpMtBYr3tY?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"165\" height=\"175\"><br>WXYV, ABYU, ACZT, AD59, EFY4, G23Y, GHIY, IYKJ, Y3LK , I1QJ, H2LJ, 1YNP, Y45N, LMNK, KJON, QJOP , QRSP, RJ78, JL67, I3MO.<br>There are 20 squares.</p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfWtzidpm0VDVB632nfnADHK5ohQtGKu1wsxsYk0O0PjMkefP1x1tNtTarSdAhYBuZ2Ps57_y8POL6adJtXMoU8aTHBsdiGLXeByJn8XVTnh_4pPqKZH_GQrpeD8u8QymEOZdhQzHoY71Y8NVpMtBYr3tY?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"165\" height=\"175\"><br>WXYV, ABYU, ACZT, AD59, EFY4, G23Y, GHIY, IYKJ, Y3LK , I1QJ, H2LJ, 1YNP, Y45N, LMNK, KJON, QJOP , QRSP, RJ78, JL67, I3MO.<br>20 वर्ग हैं.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair.<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>17. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए।<br>(<strong>नोट </strong>: संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13-13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: ["<p>40:52</p>", "<p>45:54</p>", 
                                "<p>35:42</p>", "<p>55:66</p>"],
                    options_hi: ["<p>40:52</p>", "<p>45:54</p>",
                                "<p>35:42</p>", "<p>55:66</p>"],
                    solution_en: "<p>17.(a) <strong>Logic</strong>: except option (a), all option have ratio ( 5 : 6)<br>45:54 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>9</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>9</mn></mfrac></math> = 5 : 6<br>35:42 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>7</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>7</mn></mfrac></math> = 5 : 6<br>55:66 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>11</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>66</mn><mn>11</mn></mfrac></math> = 5 : 6<br>But<br>40:52 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>4</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>4</mn></mfrac></math> = 10: 13</p>",
                    solution_hi: "<p>17.(a) <strong>तर्क </strong>: विकल्प (a) को छोड़कर, सभी विकल्पों का अनुपात (5:6) है । <br>45:54 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>9</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>9</mn></mfrac></math> = 5 : 6<br>35:42 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>7</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>7</mn></mfrac></math> = 5 : 6<br>55:66 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>11</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>66</mn><mn>11</mn></mfrac></math> = 5 : 6<br>लेकिन <br>40:52 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>4</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>4</mn></mfrac></math> = 10: 13</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. A paper is folded and cut as shown below. How will it appear when unfolded?<br><strong id=\"docs-internal-guid-2cf7c5a1-7fff-5814-2708-4c98afc4e5e5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeH02zd3ASmiKf7yKIKOTV2yGSxn277-pk5S6Q2Gn_sHOznpyU8tDXbkD0IZ55DT6FfhHFSf6l8gpkiCi5j08iQMP60erD9VboULqyJmp46_3tonda7-r5Yzn65ogNWOIouJfK4zB_P0G2PELyjl8AJb2s?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"403\" height=\"95\"></strong></p>",
                    question_hi: "<p>18. एक कागज को निम्नानुसार मोड़ा और काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><strong id=\"docs-internal-guid-eb9bfb73-7fff-a938-97aa-9f1f88d522ec\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeH02zd3ASmiKf7yKIKOTV2yGSxn277-pk5S6Q2Gn_sHOznpyU8tDXbkD0IZ55DT6FfhHFSf6l8gpkiCi5j08iQMP60erD9VboULqyJmp46_3tonda7-r5Yzn65ogNWOIouJfK4zB_P0G2PELyjl8AJb2s?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"403\" height=\"95\"></strong></p>",
                    options_en: ["<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdR06P5t8Jq3uBVgew1ahFucd1wo3Z9q6ccZQ21jBcxdM5WVMxBUoWJWTk9GRECGzyl0y-DnCAVwXUel27Py0WrWKckpFcm3_fGPQBc55zkUCEVcv6xFzagGDQZdvVnJtR2gitaVi3WDQFOVaaHlKJpxdo6?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"150\" height=\"141\"></p>", "<p><strong id=\"docs-internal-guid-401f0ec4-7fff-ff1b-b922-9eaa4b30534c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUBv7cTg-Rnbzy8boCIJffhuyPWVq1aBuhZN6Mmhy9irHGO29aMk5y0JE5Q3uYNbsjjht4N9HkekImUaYGnoRhvftzYvW6a-35XGW3UOwG9OWwfUr5mnn-qWSqHY0yPeWZt98iY0yQFDOWJHh4HMQaQd9H?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"149\" height=\"140\"></strong></p>", 
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdRo6sJZ6hXkcRLJLEWRLp0nzex5FqdNGH-z5nOIr7Vt5BXPKqoo9EROjk_OxZT4UJ4MWQlHGQYd9QWQJoxTKL-5AM89wQCA1TeJmn_SSE-uZbvz3BsWAxJJP-2TJoJRi8io6WXJckiWkPq5oYe3rGRJX1C?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"152\" height=\"144\"></p>", "<p><strong id=\"docs-internal-guid-4723ffd8-7fff-6408-8d58-57ad5b9ff527\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcj2q9ppCU1u_JLjeBqmO1i1quiwiPuHvVCRHAb2rahWwZLHQYtc0jjSBwpb0jPhPTbT7MmubGrlgHHDmovk6HhBtXsQEmECn2EbZcfhh5iPZxIfIGiEty0i_yUfixJVKzw_2nkXxKxZlDpKTrdBeQmeG8Z?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"157\" height=\"149\"></strong></p>"],
                    options_hi: ["<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdR06P5t8Jq3uBVgew1ahFucd1wo3Z9q6ccZQ21jBcxdM5WVMxBUoWJWTk9GRECGzyl0y-DnCAVwXUel27Py0WrWKckpFcm3_fGPQBc55zkUCEVcv6xFzagGDQZdvVnJtR2gitaVi3WDQFOVaaHlKJpxdo6?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"150\" height=\"141\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUBv7cTg-Rnbzy8boCIJffhuyPWVq1aBuhZN6Mmhy9irHGO29aMk5y0JE5Q3uYNbsjjht4N9HkekImUaYGnoRhvftzYvW6a-35XGW3UOwG9OWwfUr5mnn-qWSqHY0yPeWZt98iY0yQFDOWJHh4HMQaQd9H?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"149\" height=\"140\"></p>",
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdRo6sJZ6hXkcRLJLEWRLp0nzex5FqdNGH-z5nOIr7Vt5BXPKqoo9EROjk_OxZT4UJ4MWQlHGQYd9QWQJoxTKL-5AM89wQCA1TeJmn_SSE-uZbvz3BsWAxJJP-2TJoJRi8io6WXJckiWkPq5oYe3rGRJX1C?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"152\" height=\"144\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcj2q9ppCU1u_JLjeBqmO1i1quiwiPuHvVCRHAb2rahWwZLHQYtc0jjSBwpb0jPhPTbT7MmubGrlgHHDmovk6HhBtXsQEmECn2EbZcfhh5iPZxIfIGiEty0i_yUfixJVKzw_2nkXxKxZlDpKTrdBeQmeG8Z?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"157\" height=\"149\"></p>"],
                    solution_en: "<p>18.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcj2q9ppCU1u_JLjeBqmO1i1quiwiPuHvVCRHAb2rahWwZLHQYtc0jjSBwpb0jPhPTbT7MmubGrlgHHDmovk6HhBtXsQEmECn2EbZcfhh5iPZxIfIGiEty0i_yUfixJVKzw_2nkXxKxZlDpKTrdBeQmeG8Z?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"157\" height=\"149\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcj2q9ppCU1u_JLjeBqmO1i1quiwiPuHvVCRHAb2rahWwZLHQYtc0jjSBwpb0jPhPTbT7MmubGrlgHHDmovk6HhBtXsQEmECn2EbZcfhh5iPZxIfIGiEty0i_yUfixJVKzw_2nkXxKxZlDpKTrdBeQmeG8Z?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"157\" height=\"149\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. In a certain code language, \'TIDE\' is coded as \'9457\' and \'DENT\' is coded as \'7593\'. What is the code for \'N\' in that language?",
                    question_hi: "19. एक निश्चित कूट भाषा में \'TIDE\' को \'9457\' के रूप में कूटबद्ध किया जाता है और \'DENT\' को \'7593\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'N\' के लिए कूट क्या है?",
                    options_en: [" 5 ", "  7", 
                                "  3", " 9"],
                    options_hi: [" 5 ", "  7",
                                "  3", " 9"],
                    solution_en: "19.(c) TIDE\' <math display=\"inline\"><mo>→</mo></math> \'9457\' …… (i)<br /> \'DENT\' <math display=\"inline\"><mo>→</mo></math>  \'7593 …….. (ii)<br />From (i) and (ii)   (T , D, E) is common<br />So, the code of ‘N’ is ‘3’",
                    solution_hi: "19.(c) TIDE\' <math display=\"inline\"><mo>→</mo></math> \'9457\' …… (i)<br /> \'DENT\' <math display=\"inline\"><mo>→</mo></math>  \'7593 …….. (ii)<br />(i) और (ii) से, (T, D, E) उभयनिष्ठ है.<br />तो, \'N\' का कोड \'3\' है",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Each of the letters in the word \'LASTING\' is arranged in the English alphabetical order. How many letters are there in the English alphabetical series between the letter which is second from the left end and the one which is fourth from the right end in the new letter-cluster thus formed?</p>",
                    question_hi: "<p>20. शब्द \'LASTING\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर-समूह में बाएँ छोर से दूसरे अक्षर और दाएँ छोर से चौथे अक्षर के बीच अँग्रेजी वर्णमाला शृंखला में कितने अक्षर हैं?</p>",
                    options_en: ["<p>Five</p>", "<p>Six</p>", 
                                "<p>Three</p>", "<p>Four</p>"],
                    options_hi: ["<p>पाँच</p>", "<p>छः</p>",
                                "<p>तीन</p>", "<p>चार</p>"],
                    solution_en: "<p>20.(d) <strong>Given</strong>: LASTING<br>After arranging in english alphabetical order - AGILNST<br>Letter second from the left is &lsquo;G&rsquo; and fourth from the right end is &lsquo;L&rsquo;<br>Letter between &lsquo;G&rsquo; and &lsquo;L&rsquo; in english alphabet = 4(H,I,J,K)</p>",
                    solution_hi: "<p>20.(d) <strong>Given</strong>: LASTING<br>अंग्रेजी वर्णमाला क्रम में व्यवस्थित करने के बाद - AGILNST<br>बायीं ओर से दूसरा अक्षर \'G\' है और दायें छोर से चौथा अक्षर \'L\' है<br>अंग्रेजी वर्णमाला में \'G\' और \'L\' के बीच का अक्षर = 4(H,I,J,K)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. A transparent sheet with a pattern is given in the following figure. Identify from amongst the options as to how the pattern would appear when the transparent sheet is folded at the middle horizontal line.<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeNSAtepdGAEV2XGV-X5Huf67T-te_qDOicU7bPhKXg4ZMXbft-dmhJtjj7NCSVDick93xajCwZUjXssGcGrsw0MWgvH_XFzKoIEUPGo6uOx2Fj6RXGrjMtEXx4jlUyUh5uT6qEisAY6IapqLBbKaDNIWnA?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"171\" height=\"124\"></p>",
                    question_hi: "<p>21. निम्नलिखित आकृति में पैटर्न वाली एक पारदर्शी शीट दी गई है। विकल्पों में से पहचानें कि मध्य क्षैतिज रेखा पर पारदर्शी शीट को मोड़ने पर पैटर्न कैसा दिखाई देगा।<br><strong id=\"docs-internal-guid-4228cd8d-7fff-6b34-9998-8e12e91b74f4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeNSAtepdGAEV2XGV-X5Huf67T-te_qDOicU7bPhKXg4ZMXbft-dmhJtjj7NCSVDick93xajCwZUjXssGcGrsw0MWgvH_XFzKoIEUPGo6uOx2Fj6RXGrjMtEXx4jlUyUh5uT6qEisAY6IapqLBbKaDNIWnA?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"171\" height=\"124\"></strong></p>",
                    options_en: ["<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfXxN49Cy8v0MNTyvcHDHU8u8j9E0A6rW_k860ozHEumjF_MSaNRxRmYginAD6by0UbutL34Vty_98iNJVihJwvy3Z395dX19cSSmyCpY223VecUqfbPBm4ntJJORZx3gF-fNs4nAvkrMqj_QpGVVqF9oNi?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"166\" height=\"118\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXecWcUkYwN6GqHLcekSO93dP4oZQgxeFMwR9oo1N4J5-AYcRRHVlkzObtabo658EQJKy-SySmj7xRcpZU16w8K1vk_-YVLJcuJFwU5DyCNhufHBdGX1wXxu56PQuTRu2GGtkC6gbyWVHUXsDC_9xzIujNqu?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"166\" height=\"118\"></p>", 
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeW9owCAOukQQ4e9X-IYLbrB_VQNXAwpZa7MpgIDPjyDgXdKwAPS8UuW_4RjbIBgeD0hi6G03_yNs_I5TNAfO0Ib3a5pMd4VY-MOx9cn3l2cP2nFInuQqiN2f_292iAphFROP5sEaJcnhO4gEEMWDHOyrHd?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"162\" height=\"114\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdy-RLkpTKkjh43VJQjq27yHkKyf0La2alNbNPbIZV9qxdfAWXqgchBqhLynpJcDUdcKfxBAR44PWd1GAMX1MMXxlgTgZIAG3H_qXh63Z0CTSMcckbwtPxTvSKssk1ZCFsgTLbAvgLHA20N8cUrxVkf6E_S?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"163\" height=\"116\"></p>"],
                    options_hi: ["<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfXxN49Cy8v0MNTyvcHDHU8u8j9E0A6rW_k860ozHEumjF_MSaNRxRmYginAD6by0UbutL34Vty_98iNJVihJwvy3Z395dX19cSSmyCpY223VecUqfbPBm4ntJJORZx3gF-fNs4nAvkrMqj_QpGVVqF9oNi?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"166\" height=\"118\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXecWcUkYwN6GqHLcekSO93dP4oZQgxeFMwR9oo1N4J5-AYcRRHVlkzObtabo658EQJKy-SySmj7xRcpZU16w8K1vk_-YVLJcuJFwU5DyCNhufHBdGX1wXxu56PQuTRu2GGtkC6gbyWVHUXsDC_9xzIujNqu?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"166\" height=\"118\"></p>",
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeW9owCAOukQQ4e9X-IYLbrB_VQNXAwpZa7MpgIDPjyDgXdKwAPS8UuW_4RjbIBgeD0hi6G03_yNs_I5TNAfO0Ib3a5pMd4VY-MOx9cn3l2cP2nFInuQqiN2f_292iAphFROP5sEaJcnhO4gEEMWDHOyrHd?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"162\" height=\"114\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdy-RLkpTKkjh43VJQjq27yHkKyf0La2alNbNPbIZV9qxdfAWXqgchBqhLynpJcDUdcKfxBAR44PWd1GAMX1MMXxlgTgZIAG3H_qXh63Z0CTSMcckbwtPxTvSKssk1ZCFsgTLbAvgLHA20N8cUrxVkf6E_S?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"163\" height=\"116\"></p>"],
                    solution_en: "<p>21.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeW9owCAOukQQ4e9X-IYLbrB_VQNXAwpZa7MpgIDPjyDgXdKwAPS8UuW_4RjbIBgeD0hi6G03_yNs_I5TNAfO0Ib3a5pMd4VY-MOx9cn3l2cP2nFInuQqiN2f_292iAphFROP5sEaJcnhO4gEEMWDHOyrHd?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"162\" height=\"114\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeW9owCAOukQQ4e9X-IYLbrB_VQNXAwpZa7MpgIDPjyDgXdKwAPS8UuW_4RjbIBgeD0hi6G03_yNs_I5TNAfO0Ib3a5pMd4VY-MOx9cn3l2cP2nFInuQqiN2f_292iAphFROP5sEaJcnhO4gEEMWDHOyrHd?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"162\" height=\"114\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><img src=\"data:image/png;base64,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\" width=\"132\" height=\"139\"></p>",
                    question_hi: "<p>22. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति (X) सन्निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"data:image/png;base64,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\" width=\"132\" height=\"139\"></p>",
                    options_en: ["<p><img src=\"data:image/png;base64,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\" width=\"130\" height=\"126\"><br><br></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdtHgWdK-pbca6S-7kj_6E5w9hra96kdl3C4n5uF6Gy7w2K_eMTucvzRYoWuoDolgK4x9ICN4aEJx28iRKyUC0SZzMBiZlRl22-3aGpvVldlKZU3ii45hBmp3xy8ZuGxTJumcB3Mt1DqtEV-_pwE019SaFn?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"133\" height=\"130\"></p>", 
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrvS-JQ7qx18zqM4-k0jhb7uCIkS6jorOt6KVaZ3vsXe_Ax6j7F0jutNq6HGd0puZ1YluvmQ656cBNRGcwMLf91Es5LySkNLf49dJWpJKisyhxWGAUforKpcSZCyfSYL3SVuoPCirCCjWY88MI8pIj9qY_?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"130\" height=\"127\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXePtWxWe0L8MW1vmFXJ6B3DyY9wKU0Zd3wZIokgp-MIlMSdwkgCG0Pe73GT2zzVMpYslMejnhpGE_HxrLfsK1JnqArj8R_3ckLk6pXNjdn3hrIAc8MZqZxZTO80vZXtEB_En0-0wquutT2ORdSop02obcDC?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"128\" height=\"124\"></p>"],
                    options_hi: ["<p><img src=\"data:image/png;base64,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\" width=\"130\" height=\"126\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdtHgWdK-pbca6S-7kj_6E5w9hra96kdl3C4n5uF6Gy7w2K_eMTucvzRYoWuoDolgK4x9ICN4aEJx28iRKyUC0SZzMBiZlRl22-3aGpvVldlKZU3ii45hBmp3xy8ZuGxTJumcB3Mt1DqtEV-_pwE019SaFn?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"133\" height=\"130\"></p>",
                                "<p><strong id=\"docs-internal-guid-517c34b1-7fff-1a6a-faec-9b0ab87a4c8c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrvS-JQ7qx18zqM4-k0jhb7uCIkS6jorOt6KVaZ3vsXe_Ax6j7F0jutNq6HGd0puZ1YluvmQ656cBNRGcwMLf91Es5LySkNLf49dJWpJKisyhxWGAUforKpcSZCyfSYL3SVuoPCirCCjWY88MI8pIj9qY_?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"130\" height=\"127\"></strong></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXePtWxWe0L8MW1vmFXJ6B3DyY9wKU0Zd3wZIokgp-MIlMSdwkgCG0Pe73GT2zzVMpYslMejnhpGE_HxrLfsK1JnqArj8R_3ckLk6pXNjdn3hrIAc8MZqZxZTO80vZXtEB_En0-0wquutT2ORdSop02obcDC?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"130\" height=\"126\"></p>"],
                    solution_en: "<p>22.(c)<br><strong id=\"docs-internal-guid-517c34b1-7fff-1a6a-faec-9b0ab87a4c8c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrvS-JQ7qx18zqM4-k0jhb7uCIkS6jorOt6KVaZ3vsXe_Ax6j7F0jutNq6HGd0puZ1YluvmQ656cBNRGcwMLf91Es5LySkNLf49dJWpJKisyhxWGAUforKpcSZCyfSYL3SVuoPCirCCjWY88MI8pIj9qY_?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"130\" height=\"127\"></strong></p>",
                    solution_hi: "<p>22.(c)<br><strong id=\"docs-internal-guid-517c34b1-7fff-1a6a-faec-9b0ab87a4c8c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrvS-JQ7qx18zqM4-k0jhb7uCIkS6jorOt6KVaZ3vsXe_Ax6j7F0jutNq6HGd0puZ1YluvmQ656cBNRGcwMLf91Es5LySkNLf49dJWpJKisyhxWGAUforKpcSZCyfSYL3SVuoPCirCCjWY88MI8pIj9qY_?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"130\" height=\"127\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option that represents the letters that when placed from left to right in the blanks below will complete the given letter series.<br>MN_OJKM_QOJK_NQ_JKMNQO_KMN</p>",
                    question_hi: "<p>23. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर दी गई अक्षर श्रृंखला पूरी हो जाएगी।<br>MN_OJKM_QOJK_NQ_JKMNQO_KMN</p>",
                    options_en: ["<p>QMNJO</p>", "<p>QNJMO</p>", 
                                "<p>QNMJO</p>", "<p>QNMOJ</p>"],
                    options_hi: ["<p>QMNJO</p>", "<p>QNJMO</p>",
                                "<p>QNMJO</p>", "<p>QNMOJ</p>"],
                    solution_en: "<p>23.(d)<br>MN<span style=\"text-decoration: underline;\"><strong>Q</strong></span>OJK/M<span style=\"text-decoration: underline;\"><strong>N</strong></span>QOJK/<span style=\"text-decoration: underline;\"><strong>M</strong></span>NQ<span style=\"text-decoration: underline;\"><strong>O</strong></span>JK/MNQO<span style=\"text-decoration: underline;\"><strong>J</strong></span>K/MN</p>",
                    solution_hi: "<p>23.(d)<br>MN<span style=\"text-decoration: underline;\"><strong>Q</strong></span>OJK/M<span style=\"text-decoration: underline;\"><strong>N</strong></span>QOJK/<span style=\"text-decoration: underline;\"><strong>M</strong></span>NQ<span style=\"text-decoration: underline;\"><strong>O</strong></span>JK/MNQO<span style=\"text-decoration: underline;\"><strong>J</strong></span>K/MN</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>8 : 79<br>6 : 51</p>",
                    question_hi: "<p>24. उस विकल्प का चयन कीजिए जिसमें संख्याओं के मध्य वही संबंध है जो नीचे दिए गए युग्म की संख्याओं के मध्य है।<br>(<strong>ध्यान दें</strong> : संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें-13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>8 : 79<br>6 : 51</p>",
                    options_en: ["<p>9:96</p>", "<p>5:39</p>", 
                                "<p>4:32</p>", "<p>7:62</p>"],
                    options_hi: ["<p>9:96</p>", "<p>5:39</p>",
                                "<p>4:32</p>", "<p>7:62</p>"],
                    solution_en: "<p>24.(a) <strong>Logic </strong>: (1st number)<sup>2</sup> + 15 = 2nd number<br>8 : 79 :- 8<sup>2 </sup>+ 15 <math display=\"inline\"><mo>&#8658;</mo></math> 64 + 15 = 79<br>6 : 51 :- 6<sup>2</sup> + 15 <math display=\"inline\"><mo>&#8658;</mo></math> 36 + 15 = 51<br>Similarly<br>9:96 :- 9<sup>2</sup> + 15 <math display=\"inline\"><mo>&#8658;</mo></math> 81 + 15 = 96</p>",
                    solution_hi: "<p>24.(a) <strong>तर्क </strong>: (पहली संख्या)2 + 15 = दूसरी संख्या<br>8 : 79 :- 8<sup>2</sup> + 15 <math display=\"inline\"><mo>&#8658;</mo></math> 64 + 15 = 79<br>6 : 51 :- 6<sup>2</sup> + 15 <math display=\"inline\"><mo>&#8658;</mo></math> 36 + 15 = 51<br>इसी प्रकार<br>9:96 :- 9<sup>2</sup> + 15 <math display=\"inline\"><mo>&#8658;</mo></math> 81 + 15 = 96</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><strong id=\"docs-internal-guid-96123aee-7fff-8798-f82d-b8cba6e9c4a3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHnNarCyLPPcG6G_mQJt52VzTfhcJ6T1Z2o1i0b6yH0gfPfxfe68_O3SejdT1s-tZCFKv9I98MQAEsvxvJaiVmM2Mlb_eRLrHh41lMU0ethhdBV8f-IBDZrq0LlJdrHg31ia1UKZpH9GvGL8kmSj47Oa4?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"152\" height=\"175\"></strong></p>",
                    question_hi: "<p>25. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति (X) सन्निहित है (घुमाने की अनुमति नहीं है)।<br><strong id=\"docs-internal-guid-9b13d8ec-7fff-ee33-e397-a6487cd010c1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHnNarCyLPPcG6G_mQJt52VzTfhcJ6T1Z2o1i0b6yH0gfPfxfe68_O3SejdT1s-tZCFKv9I98MQAEsvxvJaiVmM2Mlb_eRLrHh41lMU0ethhdBV8f-IBDZrq0LlJdrHg31ia1UKZpH9GvGL8kmSj47Oa4?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"152\" height=\"175\"></strong></p>",
                    options_en: ["<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf8WGwcoUl9GqMWsP7Gh4nOTbjW5rOBiWHTr_j-yzRM5YyRwdK6WapPgQK6GP9dD8mbjpVLJAl7VWY0wITpVLD5Ys4jEdhwwA_9i2nJDESu2eF4U29AavFSHbb0oMYsbWMARrXIZioGPde7IbTOQULD93mK?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"155\" height=\"155\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdM8PQenjHHl5Ri00AmgW-ErpEmFRsYbMDm_GICuAe-xq2pWv0WHbg68eHx4oam0ECZ-SkzpOZCeQC7KYDlKhzgKqXmzY4Nir96kqIDaMIsLXYPaFKqaxVzym0h2P-4zn7G3qrAGS9RB9rFuxGW6ge5qZhG?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"156\" height=\"156\"></p>", 
                                "<p><strong id=\"docs-internal-guid-1da76549-7fff-1ddf-010d-8eb0ff095d71\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfuB2vpQcRlBMbr8K39hgE9gdwkxmYJR7GmjgboL9Jl7S1crDawc1qgQRpbm55SNATir9DwvFqYlZndmNesIR6gSP9hXK_Nws2t7rv5B1fXWzaQ6zuHpYoDBJtv31vCkfPZNiYRiR9iolxfaKvKoIxLNepv?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"151\" height=\"151\"></strong></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfDyokfxZjvRCmjyrJJuBytYnx4Ci_LKf0JwAhrxGaeedWOI1WZrLt9z4B01DCaExRaWy8TXGKscDRvOUOLGwPG0LCjcp5P4czapfy3gM5xHEvXaNwyYBxYIDo_8_RGqt37xWvcr7IA4H_KQ4KfLAKDnnZC?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"152\" height=\"152\"></p>"],
                    options_hi: ["<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf8WGwcoUl9GqMWsP7Gh4nOTbjW5rOBiWHTr_j-yzRM5YyRwdK6WapPgQK6GP9dD8mbjpVLJAl7VWY0wITpVLD5Ys4jEdhwwA_9i2nJDESu2eF4U29AavFSHbb0oMYsbWMARrXIZioGPde7IbTOQULD93mK?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"155\" height=\"155\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdM8PQenjHHl5Ri00AmgW-ErpEmFRsYbMDm_GICuAe-xq2pWv0WHbg68eHx4oam0ECZ-SkzpOZCeQC7KYDlKhzgKqXmzY4Nir96kqIDaMIsLXYPaFKqaxVzym0h2P-4zn7G3qrAGS9RB9rFuxGW6ge5qZhG?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"156\" height=\"156\"></p>",
                                "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfuB2vpQcRlBMbr8K39hgE9gdwkxmYJR7GmjgboL9Jl7S1crDawc1qgQRpbm55SNATir9DwvFqYlZndmNesIR6gSP9hXK_Nws2t7rv5B1fXWzaQ6zuHpYoDBJtv31vCkfPZNiYRiR9iolxfaKvKoIxLNepv?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"151\" height=\"151\"></p>", "<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfDyokfxZjvRCmjyrJJuBytYnx4Ci_LKf0JwAhrxGaeedWOI1WZrLt9z4B01DCaExRaWy8TXGKscDRvOUOLGwPG0LCjcp5P4czapfy3gM5xHEvXaNwyYBxYIDo_8_RGqt37xWvcr7IA4H_KQ4KfLAKDnnZC?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"152\" height=\"152\"></p>"],
                    solution_en: "<p>25.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfuB2vpQcRlBMbr8K39hgE9gdwkxmYJR7GmjgboL9Jl7S1crDawc1qgQRpbm55SNATir9DwvFqYlZndmNesIR6gSP9hXK_Nws2t7rv5B1fXWzaQ6zuHpYoDBJtv31vCkfPZNiYRiR9iolxfaKvKoIxLNepv?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"151\" height=\"151\"></p>",
                    solution_hi: "<p>25.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfuB2vpQcRlBMbr8K39hgE9gdwkxmYJR7GmjgboL9Jl7S1crDawc1qgQRpbm55SNATir9DwvFqYlZndmNesIR6gSP9hXK_Nws2t7rv5B1fXWzaQ6zuHpYoDBJtv31vCkfPZNiYRiR9iolxfaKvKoIxLNepv?key=qFMCgnqB0KsWNqxk5ikpVcQ0\" width=\"151\" height=\"151\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>