<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">15:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1.<span style=\"font-family: Times New Roman;\"> Select the figure from among the given options that can replace the question mark (?) in the following series.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image28.png\" width=\"269\" height=\"66\"></p>",
                    question_hi: "<p>1. <span style=\"font-family: Baloo;\">दिए गए विकल्पों में से उस आकृति का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image28.png\" width=\"269\" height=\"66\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image20.png\" width=\"63\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image31.png\" width=\"63\" height=\"60\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image3.png\" width=\"63\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_97096244211674883066228.jpg\" width=\"63\" height=\"60\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image20.png\" width=\"63\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image31.png\" width=\"63\" height=\"60\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image3.png\" width=\"63\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_97096244211674883066228.jpg\" width=\"63\" height=\"60\"></p>"],
                    solution_en: "<p>1.(d)<br><img src=\"https://ssccglpinnacle.com/images/mceu_97096244211674883066228.jpg\" width=\"70\" height=\"66\"></p>",
                    solution_hi: "<p>1.(d)<br><img src=\"https://ssccglpinnacle.com/images/mceu_97096244211674883066228.jpg\" width=\"70\" height=\"66\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2.<span style=\"font-family: Times New Roman;\"> Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.</span><br><strong><span style=\"font-family: Times New Roman;\">Statements:</span></strong><br><span style=\"font-family: Times New Roman;\">No introvert is an extrovert.</span><br><span style=\"font-family: Times New Roman;\">All extroverts are ambiverts.</span><br><strong><span style=\"font-family: Times New Roman;\">Conclusions:</span></strong><br><span style=\"font-family: Times New Roman;\">I. No introvert is an ambivert.</span><br><span style=\"font-family: Times New Roman;\">II. No ambivert is an introvert.</span><br><span style=\"font-family: Times New Roman;\">III. Some ambiverts are extroverts.</span><br><span style=\"font-family: Times New Roman;\">IV. All ambiverts are extroverts. </span></p>",
                    question_hi: "<p>2. <span style=\"font-family: Baloo;\">दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।</span><br><strong><span style=\"font-family: Baloo;\">कथन:</span></strong><br><span style=\"font-family: Baloo;\">कोई अंतर्मुखी बहिर्मुखी नहीं है।</span><br><span style=\"font-family: Baloo;\">सभी बहिर्मुखी उभयचर हैं।</span><br><strong><span style=\"font-family: Baloo;\">निष्कर्ष:</span></strong><br><span style=\"font-family: Baloo;\">I. कोई अंतर्मुखी उभयचर नहीं है।</span><br><span style=\"font-family: Baloo;\">II. कोई उभयमुखी अंतर्मुखी नहीं है।</span><br><span style=\"font-family: Baloo;\">III. कुछ उभयचर बहिर्मुखी हैं।</span><br><span style=\"font-family: Baloo;\">IV. सभी महत्वाकांक्षी बहिर्मुखी हैं।</span></p>",
                    options_en: ["<p>Both conclusions I and IV follow</p>", "<p>Both conclusions I and II follow</p>", 
                                "<p>Only conclusion I follows</p>", "<p>Only conclusion III follows</p>"],
                    options_hi: ["<p>निष्कर्ष I और IV दोनों अनुसरण करते हैं</p>", "<p>निष्कर्ष I और II दोनों अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष I अनुसरण करता है</p>", "<p>केवल निष्कर्ष III अनुसरण करता है</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image14.png\" width=\"210\" height=\"70\"><br><span style=\"font-family: Times New Roman;\">Some ambiverts are </span><span style=\"font-family: Times New Roman;\">extroverts is true</span><span style=\"font-family: Times New Roman;\"> and rest all other conclusions are false.</span></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image14.png\" width=\"210\" height=\"70\"><br><span style=\"font-family: Baloo;\">कुछ महत्वाकांक्षी बहिर्मुखी सत्य हैं और बाकी सभी निष्कर्ष गलत हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3.<span style=\"font-family: Times New Roman;\"> Pointing towards Sumit, Anuj said, &ldquo;He is my mother&rsquo;s father&rsquo;s son&rsquo;s brother&rsquo;s son&rdquo;. If Vinita, who is married to Kartik, is the mother of Anuj, then how is Vinita related to </span><span style=\"font-family: Times New Roman;\">Sumit</span><span style=\"font-family: Times New Roman;\">? </span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Baloo;\">सुमित की ओर इशारा करते हुए अनुज ने कहा, \"वह मेरी माता के पिता के पुत्र के भाई का पुत्र है\"। यदि विनीता, जो कार्तिक से विवाहित है, अनुज की माता है, तो विनीता सुमित से किस प्रकार संबंधित है?</span></p>",
                    options_en: ["<p>Mother</p>", "<p>Mother\'s sister</p>", 
                                "<p>Sister</p>", "<p>Father\'s sister</p>"],
                    options_hi: ["<p>मां</p>", "<p>मां की बहन</p>",
                                "<p>बहन</p>", "<p>पिता की बहन</p>"],
                    solution_en: "<p>3.(d)<br><span style=\"font-family: Times New Roman;\">Mother&rsquo;s </span><span style=\"font-family: Times New Roman;\">father&rsquo;s is</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">maternal</span><span style=\"font-family: Times New Roman;\"> grandmother. His son&rsquo;s son is Anuj&rsquo;s cousin. Vinita is mother of Anuj and Kartik is his brother&rsquo;s son so Vinita is Sumit\'s father&rsquo;s sister. </span></p>",
                    solution_hi: "<p>3.(d)<br><span style=\"font-family: Baloo;\">माता के पिता की नानी है। उनके पुत्र का पुत्र अनुज का चचेरा भाई है। विनीता अनुज की माँ है और कार्तिक उसके भाई का पुत्र है इसलिए विनीता सुमित के पिता की बहन है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4.<span style=\"font-family: Times New Roman;\"> Select the correct image of the given combination when the mirror is placed at MN as shown.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image2.png\" width=\"159\" height=\"90\"></p>",
                    question_hi: "<p>4. <span style=\"font-family: Baloo;\">दिए गए संयोजन की सही छवि का चयन करें जब दर्पण को MN पर रखा गया है जैसा कि दिखाया गया है।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image2.png\" width=\"159\" height=\"90\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image27.png\" width=\"119\" height=\"15\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image1.png\" width=\"119\" height=\"15\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image10.png\" width=\"119\" height=\"15\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image4.png\" width=\"119\" height=\"15\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image27.png\" width=\"119\" height=\"15\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image1.png\" width=\"119\" height=\"15\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image10.png\" width=\"119\" height=\"15\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image4.png\" width=\"119\" height=\"15\"></p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image29.png\" width=\"250\" height=\"87\"></p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image29.png\" width=\"250\" height=\"87\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. <span style=\"font-family: Times New Roman;\">Select the option in which the given figure is embedded (rotation is NOT allowed).</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image21.png\" width=\"80\" height=\"99\"></p>",
                    question_hi: "<p>5. <span style=\"font-family: Baloo;\">उस विकल्प का चयन करें जिसमें दी गई आकृति सन्निहित है (रोटेशन की अनुमति नहीं है)।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image21.png\" width=\"57\" height=\"70\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image7.png\" width=\"63\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image12.png\" width=\"62\" height=\"60\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image24.png\" width=\"56\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image18.png\" width=\"54\" height=\"60\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image7.png\" width=\"63\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image12.png\" width=\"63\" height=\"60\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image24.png\" width=\"56\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image18.png\" width=\"54\" height=\"60\"></p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image19.png\" width=\"62\" height=\"60\"></p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image19.png\" width=\"62\" height=\"60\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6.<span style=\"font-family: Times New Roman;\"> Select the option in which the numbers are related in the same way as are the numbers of the following set.</span><br>(3, 2, 3)</p>",
                    question_hi: "<p>6. <span style=\"font-family: Baloo;\">उस विकल्प का चयन करें जिसमें संख्याएँ उसी प्रकार संबंधित हैं जैसे निम्नलिखित सेट की संख्याएँ हैं।</span><br>(3, 2, 3)</p>",
                    options_en: ["<p>(4, 9, 12)</p>", "<p>(5, 18, 27)</p>", 
                                "<p>(12, 136, 204)</p>", "<p>(7, 44, 66)</p>"],
                    options_hi: ["<p>(4, 9, 12)</p>", "<p>(5, 18, 27)</p>",
                                "<p>(12, 136, 204)</p>", "<p>(7, 44, 66)</p>"],
                    solution_en: "<p>6.(b)<br><span style=\"font-family: Times New Roman;\"><strong>Logic :</strong> a : 2</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\"> : 3</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math><br><span style=\"font-family: Times New Roman;\">Following the same pattern</span><br><span style=\"font-family: Times New Roman;\">5 : 2</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\"> : 3</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></math><span style=\"font-family: Cardo;\">&rarr; 5 : 18 : 27</span></p>",
                    solution_hi: "<p>6.(b)<br><span style=\"font-family: Baloo;\"><strong>तर्क :</strong> a : 2</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\">: 3</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math><br><span style=\"font-family: Baloo;\">एक ही पैटर्न के बाद</span><br><span style=\"font-family: Times New Roman;\">5 : </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>2</mn><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\">: 3</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></math><span style=\"font-family: Cardo;\">&rarr; 5 : 18 : 27</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7.<span style=\"font-family: Times New Roman;\"> Select the option that is related to the third number in the same way as the second number is related to the first number.</span><br><span style=\"font-family: Times New Roman;\">14 : 112 : : 18 : ? </span></p>",
                    question_hi: "<p>7.<span style=\"font-family: Baloo;\"> उस विकल्प का चयन करें जो तीसरी संख्या से उसी प्रकार संबंधित है जैसे दूसरी संख्या पहली संख्या से संबंधित है।</span><br><span style=\"font-family: Times New Roman;\">14 : 112 : : 18 : ? </span></p>",
                    options_en: ["<p>181 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>183</p>", 
                                "<p>184</p>", "<p>180</p>"],
                    options_hi: ["<p>181 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>183</p>",
                                "<p>184</p>", "<p>180</p>"],
                    solution_en: "<p>7.(d)<br><span style=\"font-family: Times New Roman;\"><strong>Logic is :</strong> n : </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">n</mi><mo>(</mo><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac><mo>+</mo><mn>1</mn><mo>)</mo></math><br><span style=\"font-family: Cardo;\">For 18&rarr; 18(9 + 1) = 18</span><span style=\"font-family: Cardo;\">0</span></p>",
                    solution_hi: "<p>7.(d)<br><span style=\"font-family: Baloo;\"><strong>तर्क है : </strong>n : n(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">+1)</span><br><span style=\"font-family: Cardo;\">18&rarr; 18(9 + 1) = 180 </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. <span style=\"font-family: Times New Roman;\">A cube is made by folding the given sheet. In the cube so formed, what would be the number on the face opposite the face showing the number \'3\'?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image16.png\" width=\"114\" height=\"129\"></p>",
                    question_hi: "<p>8.<span style=\"font-family: Baloo;\"> दी गई शीट को मोड़कर एक घन बनाया जाता है। इस प्रकार बने घन में, संख्या \'3\' दर्शाने वाले फलक के विपरीत फलक पर कौन सी संख्या होगी?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image16.png\" width=\"109\" height=\"124\"></p>",
                    options_en: ["<p>9</p>", "<p>7</p>", 
                                "<p>1</p>", "<p>5</p>"],
                    options_hi: ["<p>9</p>", "<p>7</p>",
                                "<p>1</p>", "<p>5</p>"],
                    solution_en: "<p>8.(b)<br><span style=\"font-family: Times New Roman;\">Opposite pair are 1 and 9, 7 and 3, 5 and 2</span><br><span style=\"font-family: Times New Roman;\">Therefore 7 is opposite to 3.</span></p>",
                    solution_hi: "<p>8.(b)<br><span style=\"font-family: Baloo;\">विपरीत जोड़ी 1 और 9, 7 और 3, 5 और 2 . हैं ।</span><br><span style=\"font-family: Baloo;\">अत: 7, 3 के विपरीत है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\"> In a certain code language, \'INHALE\' is written as \'CNYJLK\'. How will \'GUILTY\' be written in that language? </span></p>",
                    question_hi: "<p>.9. <span style=\"font-family: Baloo;\">एक निश्चित कूट भाषा में, \'INHALE\' को \'CNYJLK\' लिखा जाता है। उसी भाषा में \'GUILTY\' कैसे लिखा जाएगा?</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\">ARNGWE</span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>IJKSVW</p>", 
                                "<p>WVJKSI</p>", "<p><span style=\"font-family: Times New Roman;\">WRNKSE</span><span style=\"font-family: Times New Roman;\"> </span></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\">ARNGWE</span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>IJKSVW</p>",
                                "<p>WVJKSI</p>", "<p><span style=\"font-family: Times New Roman;\">WRNKSE</span></p>"],
                    solution_en: "<p>9.(c)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>reverse the word and then use -2, +2, -2, +2... pattern</span><br><span style=\"font-family: Times New Roman;\">For GUILTY after reversing it becomes </span><span style=\"font-family: Times New Roman;\">YTILUG</span><span style=\"font-family: Times New Roman;\"> and now after applying the pattern WVJKSI</span></p>",
                    solution_hi: "<p>9.(c)<br><span style=\"font-family: Baloo;\"><strong>तर्क: </strong>शब्द को उलट दें और फिर -2, +2, -2, +2.... पैटर्न का उपयोग करें</span><br><span style=\"font-family: Baloo;\">GUILTY के लिए उलटने के बाद यह </span><span style=\"font-family: Times New Roman;\">YTILUG</span><span style=\"font-family: Baloo;\"> हो जाता है और अब </span><span style=\"font-family: Times New Roman;\">WVJKSI</span><span style=\"font-family: Baloo;\"> पैटर्न लागू करने के बाद</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10.<span style=\"font-family: Times New Roman;\"> Select the letter-cluster from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Times New Roman;\">CUPBOARD, </span><span style=\"font-family: Times New Roman;\">DVPBOARD</span><span style=\"font-family: Times New Roman;\">, </span><span style=\"font-family: Times New Roman;\">DVQCOARD</span><span style=\"font-family: Times New Roman;\">, </span><span style=\"font-family: Times New Roman;\">?, </span><span style=\"font-family: Times New Roman;\">DVQCPBSE</span></p>",
                    question_hi: "<p>10. <span style=\"font-family: Baloo;\">दिए गए विकल्पों में से उस अक्षर-समूह को चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।</span><br><span style=\"font-family: Times New Roman;\">CUPBOARD, </span><span style=\"font-family: Times New Roman;\">DVPBOARD</span><span style=\"font-family: Times New Roman;\">, </span><span style=\"font-family: Times New Roman;\">DVQCOARD</span><span style=\"font-family: Times New Roman;\">, </span><span style=\"font-family: Times New Roman;\">?, </span><span style=\"font-family: Times New Roman;\">DVQCPBSE</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\">DVQCPBSD</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\">DVQCOASE</span><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\">DVQCPBRD</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>DVPBPBRD</p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\">DVQCPBSD</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\">DVQCOASE</span><span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\">DVQCPBRD</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>DVPBPBRD</p>"],
                    solution_en: "<p>10.(c)<br><span style=\"font-family: Times New Roman;\">Pattern followed is that in each step two alphabets are replaced with their next letter</span><br><span style=\"font-family: Times New Roman;\">So ? will be replaced after changing the fifth and sixth letter from the previous word. So, </span><span style=\"font-family: Times New Roman;\">DVQCPBRD</span><span style=\"font-family: Times New Roman;\"> is the correct answer. </span></p>",
                    solution_hi: "<p>10.(c)<br><span style=\"font-family: Baloo;\">पैटर्न का अनुसरण किया जाता है कि प्रत्येक चरण में दो अक्षर उनके अगले अक्षर से बदल दिए जाते हैं</span><br><span style=\"font-family: Baloo;\">इसलिए ? पिछले शब्द से पांचवें और छठे अक्षर को बदलने के बाद बदल दिया जाएगा। तो, DVQCPBRD सही उत्तर है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11.<span style=\"font-family: Times New Roman;\"> The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown in the following figures. How would this paper look when unfolded?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image8.png\" width=\"249\" height=\"57\"></p>",
                    question_hi: "<p>11.<span style=\"font-family: Baloo;\"> कागज के एक टुकड़े को मोड़ने का क्रम और जिस तरीके से मुड़े हुए कागज को काटा गया है, उसे निम्नलिखित आकृतियों में दिखाया गया है। खोलने पर यह पेपर कैसा दिखेगा?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image8.png\" width=\"228\" height=\"52\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image5.png\" width=\"62\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image13.png\" width=\"62\" height=\"60\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image11.png\" width=\"62\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image9.png\" width=\"62\" height=\"60\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image5.png\" width=\"62\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image13.png\" width=\"62\" height=\"60\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image11.png\" width=\"62\" height=\"60\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image9.png\" width=\"62\" height=\"60\"></p>"],
                    solution_en: "<p>11.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image23.png\" width=\"62\" height=\"60\"></p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image23.png\" width=\"62\" height=\"60\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12.<span style=\"font-family: Times New Roman;\"> Find the number of triangles in the given figure.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image15.png\" width=\"90\" height=\"90\"></p>",
                    question_hi: "<p>12. <span style=\"font-family: Baloo;\">दी गई आकृति में त्रिभुजों की संख्या ज्ञात कीजिए।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image15.png\" width=\"90\" height=\"90\"></p>",
                    options_en: ["<p>23</p>", "<p>17</p>", 
                                "<p>21</p>", "<p>19</p>"],
                    options_hi: ["<p>23</p>", "<p>17</p>",
                                "<p>21</p>", "<p>19</p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image26.png\" width=\"99\" height=\"100\"><br><span style=\"font-family: Times New Roman;\">Triangles are (19): AFC, AFI, EJI, BCG, IAC, ELN, BHD, ALN, ALD, AND, PND, LNP, LPD, LNO, LOP, LMP, LMD, ADP, and ANP</span></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image26.png\" width=\"99\" height=\"100\"><br><span style=\"font-family: Baloo;\">त्रिभुज (19): AFC, AFI, EJI, BCG, IAC, ELN, BHD, ALN, ALD, AND, PND, LNP, LPD, LNO, LOP, LMP, LMD, ADP, और ANP है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. <span style=\"font-family: Times New Roman;\">Four words have been given, out of which three are alike in some manner and one is different. Select the word that is different.&nbsp;</span></p>",
                    question_hi: "<p>13. <span style=\"font-family: Baloo;\">चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। उस शब्द का चयन करें जो भिन्न है।</span></p>",
                    options_en: ["<p>Loti</p>", "<p>Naira</p>", 
                                "<p>Euro</p>", "<p>Pint</p>"],
                    options_hi: ["<p>लोटी</p>", "<p>नायर</p>",
                                "<p>यूरो</p>", "<p>पिंटो</p>"],
                    solution_en: "<p>13.(d)<br><span style=\"font-family: Times New Roman;\">Loti, Naira and Euro is the currency of Lesotho, Nigeria and European union countries but pint is not a currency.</span></p>",
                    solution_hi: "<p>13.(d)<br><span style=\"font-family: Baloo;\">लोटी, नायरा और यूरो लेसोथो, नाइजीरिया और यूरोपीय संघ के देशों की मुद्रा है लेकिन पिंट मुद्रा नहीं है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. <span style=\"font-family: Times New Roman;\">In a certain code language, \'LIBERTY\' is coded as \'4221824364050\'. How will \'SLAVERY\' be coded in that language? </span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Baloo;\">एक निश्चित कूट भाषा में, \'LIBERTY\' को \'4221824364050\' के रूप में कोडित किया जाता है। उसी भाषा में \'SLAVERY\' को किस प्रकार कोडित किया जाएगा?</span></p>",
                    options_en: ["<p>22262438365040</p>", "<p>22242636384050</p>", 
                                "<p>26224233684540</p>", "<p>26222436384450</p>"],
                    options_hi: ["<p>22262438365040</p>", "<p>22242636384050</p>",
                                "<p>26224233684540</p>", "<p>26222436384450</p>"],
                    solution_en: "<p>14.(d)<br><span style=\"font-family: Times New Roman;\"><strong>Note :</strong> This question is quite tricky, try to skip it in the first attempt.</span><br><span style=\"font-family: Times New Roman;\"><strong>Logic :</strong> Arrange the word in the alphabetical order then code vowels as reverse alphabetical order and consonant as double the alphabetical positioning.</span><br><span style=\"font-family: Times New Roman;\">Applying the same logic for SLAVERY, after arranging AELRSVY and code for AE becomes 2622 and for consonant becomes LRSVY is 2436384450</span><br><span style=\"font-family: Times New Roman;\">Complete code is 26222436384450</span></p>",
                    solution_hi: "<p>14.(d)<br><span style=\"font-family: Baloo;\"><strong>नोट:</strong> यह प्रश्न काफी पेचीदा है, पहले प्रयास में इसे छोड़ देने का प्रयास करें।</span><br><span style=\"font-family: Baloo;\"><strong>तर्क:</strong> शब्द को वर्णानुक्रम में व्यवस्थित करें, फिर स्वरों को उल्टे वर्णानुक्रम में और व्यंजन को वर्णानुक्रम की स्थिति से दोगुना करें।</span><br><span style=\"font-family: Baloo;\">SLAVERY के लिए समान तर्क को लागू करने के बाद, AELRSVY और AE के लिए कोड 2622 हो जाता है और व्यंजन बनने के लिए LRSVY, 2436384450 हो जाता है</span><br><span style=\"font-family: Baloo;\">पूरा कोड है 26222436384450</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.</span><br><span style=\"font-family: Times New Roman;\">b _ e _ d k _ _ c e m _ k k b c _ m _ k k </span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Baloo;\"> अक्षरों के उस संयोजन का चयन करें जिसे दी गई श्रंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रंखला पूरी हो जाएगी।</span><br><span style=\"font-family: Times New Roman;\">b _ e _ d k _ _ c e m _ k k b c _ m _ k k </span></p>",
                    options_en: ["<p>c, m, k, b, d, e, d</p>", "<p>c, k, k, b, d, e, d</p>", 
                                "<p>c, k, m, b, d, e, d v</p>", "<p>c, m, k, b, c, c, d</p>"],
                    options_hi: ["<p>c, m, k, b, d, e, d</p>", "<p>c, k, k, b, d, e, d</p>",
                                "<p>c, k, m, b, d, e, d</p>", "<p>c, m, k, b, c, c, d</p>"],
                    solution_en: "<p>15.(a)<br><span style=\"font-family: Times New Roman;\">Repetitive unit is &lsquo;</span><span style=\"font-family: Times New Roman;\">BCEMDKK</span><span style=\"font-family: Times New Roman;\">&rsquo;</span><br><span style=\"font-family: Times New Roman;\">Complete series is&nbsp;<span style=\"font-family: Baloo;\"> </span>b <strong><span style=\"text-decoration: underline;\">c</span></strong> e <strong><span style=\"text-decoration: underline;\">m</span></strong><span style=\"text-decoration: underline;\"> </span>d k <strong><span style=\"text-decoration: underline;\">k</span></strong> <strong><span style=\"text-decoration: underline;\">b</span></strong> c e m <strong><span style=\"text-decoration: underline;\">d</span></strong><span style=\"text-decoration: underline;\"> </span>k k b c<span style=\"text-decoration: underline;\"> </span><strong><span style=\"text-decoration: underline;\">e</span></strong> m<span style=\"text-decoration: underline;\"> </span><strong><span style=\"text-decoration: underline;\">d</span></strong><strong><span style=\"text-decoration: underline;\"> </span></strong>k<span style=\"font-family: Baloo;\"> k.</span></span><span style=\"font-family: Times New Roman;\">.</span><br><span style=\"font-family: Times New Roman;\">cmkbded is the correct answer.</span></p>",
                    solution_hi: "<p>15.(a)<br><span style=\"font-family: Baloo;\">दोहराई जाने वाली इकाई &lsquo;</span><span style=\"font-family: Times New Roman;\">BCEMDKK</span><span style=\"font-family: Baloo;\">&rsquo; है</span><br><span style=\"font-family: Baloo;\">पूरी श्रृंखला </span><span style=\"font-family: Times New Roman;\">b </span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">c</span></span></strong><span style=\"font-family: Times New Roman;\"> e </span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">m</span></span></strong><span style=\"font-family: Times New Roman;\"><span style=\"text-decoration: underline;\"> </span>d k </span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">k</span></span></strong><span style=\"font-family: Times New Roman;\"> </span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">b</span></span></strong><span style=\"font-family: Times New Roman;\"> c e m </span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">d</span></span></strong><span style=\"font-family: Times New Roman;\"><span style=\"text-decoration: underline;\"> </span>k k b c<span style=\"text-decoration: underline;\"> </span></span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">e</span></span></strong><span style=\"font-family: Times New Roman;\"> m<span style=\"text-decoration: underline;\"> </span></span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">d</span></span></strong><span style=\"font-family: Times New Roman;\"><strong><span style=\"text-decoration: underline;\"> </span></strong>k</span><span style=\"font-family: Baloo;\"> k. है।</span><br><span style=\"font-family: Baloo;\">cmkbded सही उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16.<span style=\"font-family: Times New Roman;\"> Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different. </span></p>",
                    question_hi: "<p>16. <span style=\"font-family: Baloo;\">चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस अक्षर-समूह का चयन करें जो भिन्न हो।</span></p>",
                    options_en: ["<p>JLP</p>", "<p>CEH</p>", 
                                "<p>SUX</p>", "<p>LNQ</p>"],
                    options_hi: ["<p>JLP</p>", "<p>CEH</p>",
                                "<p>SUX</p>", "<p>LNQ</p>"],
                    solution_en: "<p>16.(a)<br><span style=\"font-family: Times New Roman;\">Except JLP all others follow the logic of +2, +3. But in JLP the difference between L and P is of 4 not of 3.</span></p>",
                    solution_hi: "<p>16.(a)<br><span style=\"font-family: Baloo;\">JLP को छोड़कर अन्य सभी +2, +3 के तर्क का पालन करते हैं। लेकिन JLP में L और P के बीच का अंतर 4 का है न कि 3 का।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17.<span style=\"font-family: Times New Roman;\"> Four friends, Priya, Kavya, Gaurvi and Shalini, have different amounts of money with them. If Priya takes ₹88 from Kavya, then she will have an amount equal to what Gaurvi has. Shalini and Kavya together have a total of ₹550. If Gaurvi takes ₹25 from Shalini, she will have an amount equal to what Kavya has. If the total amount with Shalini, Kavya and Gaurvi is ₹840, how much money does Priya have? </span></p>",
                    question_hi: "<p>17. <span style=\"font-family: Baloo;\">चार दोस्तों प्रिया, काव्या, गौरवी और शालिनी के पास अलग-अलग पैसे हैं। यदि प्रिया काव्या से ₹88 लेती है, तो उसके पास गौरवी के बराबर राशि होगी। शालिनी और काव्या के पास कुल मिलाकर ₹550 हैं। यदि गौरवी शालिनी से ₹25 लेती है, तो उसके पास काव्या के बराबर राशि होगी। यदि शालिनी, काव्या और गौरवी के पास कुल राशि ₹840 है, तो प्रिया के पास कितनी धनराशि है?</span></p>",
                    options_en: ["<p>₹315</p>", "<p>₹280</p>", 
                                "<p>₹224</p>", "<p>₹202</p>"],
                    options_hi: ["<p>₹315</p>", "<p>₹280</p>",
                                "<p>₹224</p>", "<p>₹202</p>"],
                    solution_en: "<p>17.(d)<br><span style=\"font-family: Times New Roman;\">Let the amount Priya = p, kavya = k, Gaurvi =g, Shalini = s</span><br><span style=\"font-family: Times New Roman;\">According to the question</span><br><span style=\"font-family: Times New Roman;\">p + 88 = k - 88 = g</span><br><span style=\"font-family: Times New Roman;\">s + k = 550 and g + 25 = k</span><br><span style=\"font-family: Times New Roman;\">S + k + g = 840 and g = 840 - 550 = 290</span><br><span style=\"font-family: Times New Roman;\">P = g - 88 = 290 - 88 = 202 </span></p>",
                    solution_hi: "<p>17.(d)<br><span style=\"font-family: Baloo;\">मान लीजिए राशि प्रिया = p, काव्या = k, गौरवी = g, शालिनी = s</span><br><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span><br><span style=\"font-family: Times New Roman;\">p + 88 = k - 88 = g</span><br><span style=\"font-family: Baloo;\">s + k = 550 और g + 25 = k</span><br><span style=\"font-family: Baloo;\">S + k + g = 840 और g = 840 - 550 = 290</span><br><span style=\"font-family: Times New Roman;\">P = g - 88 = 290 - 88 = 202 </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18.<span style=\"font-family: Times New Roman;\"> Select the option in which the words share the same relationship as that shared by the given pair of words.</span><br><span style=\"font-family: Times New Roman;\">Electricity : Television </span></p>",
                    question_hi: "<p>18. <span style=\"font-family: Baloo;\">उस विकल्प का चयन करें जिसमें शब्द वही संबंध साझा करते हैं जो दिए गए शब्दों के जोड़े द्वारा साझा किए गए हैं।</span><br><span style=\"font-family: Baloo;\">बिजली : टेलीविजन</span></p>",
                    options_en: ["<p>Crops : Farmer</p>", "<p>Diesel : Fuel</p>", 
                                "<p>Temperature : Refrigerator</p>", "<p>Petrol : Car</p>"],
                    options_hi: ["<p>फसल: किसान</p>", "<p>डीजल: ईंधन</p>",
                                "<p>तापमान: रेफ्रिजरेटर</p>", "<p>पेट्रोल : कार</p>"],
                    solution_en: "<p>18.(d)<br><span style=\"font-family: Times New Roman;\">Television runs on electricity in the same way </span><span style=\"font-family: Times New Roman;\">car</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">runs</span><span style=\"font-family: Times New Roman;\"> on petrol.</span></p>",
                    solution_hi: "<p>18.(d)<br><span style=\"font-family: Baloo;\">जिस तरह कार पेट्रोल से चलती है उसी तरह टेलीविजन बिजली से चलता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. <span style=\"font-family: Times New Roman;\">Select the correct option that indicates the arrangement of the given words in the order in which they appear in an English dictionary.</span><br><span style=\"font-family: Times New Roman;\">1. Illegitimate&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Times New Roman;\">2. Illegality</span><br><span style=\"font-family: Times New Roman;\">3. Illuminate&nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Times New Roman;\">4. Illiterate</span><br><span style=\"font-family: Times New Roman;\">5. Illegible </span></p>",
                    question_hi: "<p>19. <span style=\"font-family: Baloo;\">सही विकल्प का चयन करें जो दिए गए शब्दों की व्यवस्था को उसी क्रम में इंगित करता है जिस क्रम में वे अंग्रेजी शब्दकोश में दिखाई देते हैं।</span><br><span style=\"font-family: Times New Roman;\">1. Illegitimate&nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Times New Roman;\">2. Illegality</span><br><span style=\"font-family: Times New Roman;\">3. Illuminate&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Times New Roman;\">4. Illiterate</span><br><span style=\"font-family: Times New Roman;\">5. Illegible </span></p>",
                    options_en: ["<p>1, 2, 3, 4, 5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3, 5, 2, 4, 1</p>", 
                                "<p>3, 5, 1, 2, 4 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2, 5, 1, 4, 3</p>"],
                    options_hi: ["<p>1, 2, 3, 4, 5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3, 5, 2, 4, 1</p>",
                                "<p>3, 5, 1, 2, 4 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2, 5, 1, 4, 3</p>"],
                    solution_en: "<p>19.(d)<br><span style=\"font-family: Times New Roman;\">The correct alphabetical order is</span><br><span style=\"font-family: Times New Roman;\">&nbsp;2, 5, 1, 4, 3</span><br><span style=\"font-family: Cardo;\">Illegality&rarr;Illegible&rarr;Illegitimate&rarr;Illiterate&rarr;Illuminate.</span></p>",
                    solution_hi: "<p>19.(d)<br><span style=\"font-family: Baloo;\">सही वर्णानुक्रम है 2, 5, 1, 4, 3</span><br><span style=\"font-family: Cardo;\">Illegality&rarr;Illegible&rarr;Illegitimate&rarr;Illiterate&rarr;Illuminate</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20.<span style=\"font-family: Times New Roman;\"> Select the correct combination of mathematical signs that can sequentially replace the * signs and balance the equation.</span><br><span style=\"font-family: Times New Roman;\">5 * 468 * 18 * 70 * 180 * 4 * 5 </span></p>",
                    question_hi: "<p>20.<span style=\"font-family: Baloo;\"> गणितीय संकेतों के सही संयोजन का चयन करें जो क्रमिक रूप से * चिह्नों को प्रतिस्थापित कर सकता है और समीकरण को संतुलित कर सकता है।</span><br><span style=\"font-family: Times New Roman;\">5 * 468 * 18 * 70 * 180 * 4 * 5 </span></p>",
                    options_en: ["<p>&times;, &divide;, -, +, =, &times;</p>", "<p>&times;, &divide;, +, -, =, &times;</p>", 
                                "<p>&times;, &divide;, +, =, -, &times;</p>", "<p>&divide;, &times;, +, -, =, &times;</p>"],
                    options_hi: ["<p>&times;, &divide;, -, +, =, &times;</p>", "<p>&times;, &divide;, +, -, =, &times;</p>",
                                "<p>&times;, &divide;, +, =, -, &times;</p>", "<p>&divide;, &times;, +, -, =, &times;</p>"],
                    solution_en: "<p>20.(b)<br><span style=\"font-family: Times New Roman;\">Using option b</span><br><span style=\"font-family: Times New Roman;\">5 &times; 468 &divide; 18 + 70 - 180 = 4 &times; 5</span><br><span style=\"font-family: Times New Roman;\">130 + 70 - 180 = 20</span><br><span style=\"font-family: Times New Roman;\">20 = 20 Hence Proved</span></p>",
                    solution_hi: "<p>20.(b)<br><span style=\"font-family: Baloo;\">विकल्प b का उपयोग करने पर</span><br><span style=\"font-family: Times New Roman;\">5 &times; 468 &divide; 18 + 70 - 180 = 4 &times; 5</span><br><span style=\"font-family: Times New Roman;\">130 + 70 - 180 = 20</span><br><span style=\"font-family: Baloo;\">अत: 20 = 20 (सिद्ध)</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21.<span style=\"font-family: Times New Roman;\"> Select the option that is related to the third word in the same way as the second word is related to the first word.</span><br><span style=\"font-family: Times New Roman;\">Hen : Rooster : : Duck : ? </span></p>",
                    question_hi: "<p>21. <span style=\"font-family: Baloo;\">उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है।</span><br><span style=\"font-family: Baloo;\">मुर्गी : मुर्गा : : बत्तख : ?</span></p>",
                    options_en: ["<p>Monk</p>", "<p>Drake</p>", 
                                "<p>Stag</p>", "<p>Drone</p>"],
                    options_hi: ["<p>भिक्षु</p>", "<p>ड्रेक</p>",
                                "<p>हरिण</p>", "<p>ड्रोन</p>"],
                    solution_en: "<p>21.(b)<br><span style=\"font-family: Times New Roman;\">As hen is female chicken and Rooster is a male chicken in the same way Duck is the female and Drake is a male. </span></p>",
                    solution_hi: "<p>21.(b)<br><span style=\"font-family: Baloo;\">जैसे मुर्गी मादा है और मुर्गा नर है उसी तरह बतख मादा है और ड्रेक नर है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22.<span style=\"font-family: Times New Roman;\"> Select the Venn diagram that best illustrates the relationship among the following classes.</span><br><span style=\"font-family: Times New Roman;\">Highlighter, Stationary, Eraser</span></p>",
                    question_hi: "<p>22. <span style=\"font-family: Baloo;\">वेन आरेख का चयन करें जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।</span><br><span style=\"font-family: Baloo;\">हाइलाइटर, स्टेशनरी, इरेज़र</span></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image32.png\" width=\"76\" height=\"70\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image30.png\" width=\"72\" height=\"70\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image25.png\" width=\"73\" height=\"70\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image6.png\" width=\"96\" height=\"70\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image32.png\" width=\"76\" height=\"70\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image30.png\" width=\"72\" height=\"70\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image25.png\" width=\"73\" height=\"70\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646721962/word/media/image6.png\" width=\"96\" height=\"70\"></p>"],
                    solution_en: "<p>22.(c)<br><span style=\"font-family: Times New Roman;\">Both highlighter and eraser are Stationary items but they do not have anything in common.</span></p>",
                    solution_hi: "<p>22.(c)<br><span style=\"font-family: Baloo;\">हाइलाइटर और इरेज़र दोनों स्टेशनरी आइटम हैं लेकिन उनमें कुछ भी समान नहीं है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23.<span style=\"font-family: Times New Roman;\"> Four number-pairs have been given, out of which three are alike in some manner and one is different. Select the number-pair that is different. </span></p>",
                    question_hi: "<p>23. <span style=\"font-family: Baloo;\">चार संख्या-जोड़े दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस संख्या-युग्म का चयन करें जो भिन्न है।</span></p>",
                    options_en: ["<p>54 : 152 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>36 : 98</p>", 
                                "<p>82 : 236 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>68 : 196</p>"],
                    options_hi: ["<p>54 : 152 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>36 : 98</p>",
                                "<p>82 : 236 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>68 : 196</p>"],
                    solution_en: "<p>23.(d)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>a : 3a-10</span><br><span style=\"font-family: Times New Roman;\">But, 68:196 does not follow the above logic.</span></p>",
                    solution_hi: "<p>23.(d)<br><span style=\"font-family: Baloo;\"><strong>तर्क :</strong> a : 3a-10</span><br><span style=\"font-family: Baloo;\">लेकिन, 68:196 उपरोक्त तर्क का पालन नहीं करता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24.<span style=\"font-family: Times New Roman;\"> Study the given pattern carefully and select the number that can replace the question mark (?) in it.</span><br><span style=\"font-family: Times New Roman;\"><img src=\"data:image/png;base64,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\"></span></p>",
                    question_hi: "<p>24.<span style=\"font-family: Baloo;\"> दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।</span><br><span style=\"font-family: Times New Roman;\"><img src=\"data:image/png;base64,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\"></span></p>",
                    options_en: ["<p>126</p>", "<p>124</p>", 
                                "<p>122</p>", "<p>128</p>"],
                    options_hi: ["<p>126</p>", "<p>124</p>",
                                "<p>122</p>", "<p>128</p>"],
                    solution_en: "<p>24.(a)<br><strong><span style=\"font-family: Times New Roman;\">Logic:</span></strong><br><strong><span style=\"font-family: Times New Roman;\">Column:-</span></strong><br><span style=\"font-family: Times New Roman;\">Square of 1st number - 4 = 2nd number</span><br><span style=\"font-family: Times New Roman;\">2nd number </span><span style=\"font-family: Times New Roman;\">&nbsp;4 - 2 = 3rd number</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>2</mn></msup></math>-4=5</span><br><span style=\"font-family: Times New Roman;\">5&times;</span><span style=\"font-family: Times New Roman;\">4-2=18</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>2</mn></msup></math>-4=32</span><br><span style=\"font-family: Times New Roman;\">32&times;</span><span style=\"font-family: Times New Roman;\">4-2=126</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math>-4=60</span><br><span style=\"font-family: Times New Roman;\">60&times;</span><span style=\"font-family: Times New Roman;\">4-2=238</span></p>",
                    solution_hi: "<p>24.(a)<br><strong><span style=\"font-family: Times New Roman;\">Logic:</span></strong><br><strong><span style=\"font-family: Baloo;\">कॉलम:-</span></strong><br><span style=\"font-family: Baloo;\">पहली संख्या का वर्ग - 4 = दूसरी संख्या</span><br><span style=\"font-family: Baloo;\">दूसरी संख्या 4 - 2 = तीसरी संख्या</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>2</mn></msup></math>-4=5</span><br><span style=\"font-family: Times New Roman;\">5&times;</span><span style=\"font-family: Times New Roman;\">4-2=18</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>2</mn></msup></math>-4=32</span><br><span style=\"font-family: Times New Roman;\">32&times;</span><span style=\"font-family: Times New Roman;\">4-2=126</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math>-4=60</span><br><span style=\"font-weight: 400;\">60<span style=\"font-family: Times New Roman;\">&times;</span></span><span style=\"font-weight: 400;\">4-2=238</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> Select the number from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Times New Roman;\">13, 26, 52, 104, ? </span></p>",
                    question_hi: "<p>25. <span style=\"font-family: Baloo;\">दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।</span><br><span style=\"font-family: Times New Roman;\">13, 26, 52, 104, ? </span></p>",
                    options_en: ["<p>152</p>", "<p>230</p>", 
                                "<p>196</p>", "<p>208</p>"],
                    options_hi: ["<p>152</p>", "<p>230</p>",
                                "<p>196</p>", "<p>208</p>"],
                    solution_en: "<p>25.(d)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>&times; 2 pattern is followed</span><br><span style=\"font-family: Times New Roman;\">104 &times; 2 is 208.</span></p>",
                    solution_hi: "<p>25.(d)<br><span style=\"font-family: Baloo;\"><strong>तर्क :</strong> &times;2 पैटर्न का पालन किया जाता है</span><br><span style=\"font-family: Baloo;\">104 &times; 2, 208 है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>