<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1. </span><span style=\"font-family:Cambria Math\">Select the most appropriate homophone to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The shareholders are planning to ___________ control of the company from the current directors.</span></p>",
                    question_hi: " <p>1. </span><span style=\"font-family:Cambria Math\">Select the most appropriate homophone to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The shareholders are planning to ___________ control of the company from the current directors.</span></p>",
                    options_en: [" <p> roost</span></p>", " <p> roast</span></p>", 
                                " <p> wrest</span></p>", " <p> rest</span></p>"],
                    options_hi: [" <p> roost</span></p>", " <p> roast</span></p>",
                                " <p> wrest</span></p>", " <p> rest</span></p>"],
                    solution_en: " <p>1.(c)</span><span style=\"font-family:Cambria Math\"> wrest</span></p> <p><span style=\"font-family:Cambria Math\">‘Wrest’ means to forcibly take or obtain something. The given sentence states that the shareholders are planning to forcibly take control of the company from the current directors. Hence, ‘wrest’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>1.(c)</span><span style=\"font-family:Cambria Math\"> wrest</span></p> <p><span style=\"font-family:Cambria Math\">‘Wrest’ </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अर्थ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चीज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जबरन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लेना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गए</span><span style=\"font-family:Cambria Math\"> sentence </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कहा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शेयरधारक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्तमान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निदेशकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नियंत्रण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जबरन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लेने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">योजना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रहे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अतः</span><span style=\"font-family:Cambria Math\">, ‘wrest’ </span><span style=\"font-family:Kokila\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयुक्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p>2. </span><span style=\"font-family:Cambria Math\">Identify the INCORRECTLY spelt word in the following sentence. </span></p> <p><span style=\"font-family:Cambria Math\">Research has proved that meteors have largely destructed and impackted earth.</span></p>",
                    question_hi: " <p>2. </span><span style=\"font-family:Cambria Math\">Identify the INCORRECTLY spelt word in the following sentence. </span></p> <p><span style=\"font-family:Cambria Math\">Research has proved that meteors have largely destructed and impackted earth.</span></p>",
                    options_en: [" <p> impackted</span></p>", " <p> Research</span></p>", 
                                " <p> proved</span></p>", " <p> destructed</span></p>"],
                    options_hi: [" <p> impackted</span></p>", " <p> Research</span></p>",
                                " <p> proved</span></p>", " <p> destructed</span></p>"],
                    solution_en: " <p>2.(a) </span><span style=\"font-family:Cambria Math\">impackted</span></p> <p><span style=\"font-family:Cambria Math\">‘Impacted’ is the correct spelling.</span></p>",
                    solution_hi: " <p>2.(a) </span><span style=\"font-family:Cambria Math\">impackted</span></p> <p><span style=\"font-family:Cambria Math\">‘Impacted’ </span><span style=\"font-family:Kokila\">सही</span><span style=\"font-family:Cambria Math\"> spelling </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined words in the given sentence.</span></p>\\r\\n<p><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">No less than</span></span><span style=\"font-family: Cambria Math;\"> eight pedestrians were killed in the accident.</span></p>\\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined words in the given sentence.</span></p>\\r\\n<p><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">No less than</span></span><span style=\"font-family: Cambria Math;\"> eight pedestrians were killed in the accident.</span></p>\\n",
                    options_en: ["<p>No few than</p>\\n", "<p>None less than</p>\\n", 
                                "<p>No fewer than</p>\\n", "<p>No lesser than</p>\\n"],
                    options_hi: ["<p>No few than</p>\\n", "<p>None less than</p>\\n",
                                "<p>No fewer than</p>\\n", "<p>No lesser than</p>\\n"],
                    solution_en: "<p>3.(c) <span style=\"font-family: Cambria Math;\">No fewer than</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Fewer&rsquo; is used when we are comparing countable objects i.e. pedestrians. Hence, &lsquo;No fewer than&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>3.(c) <span style=\"font-family: Cambria Math;\">No fewer than</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Fewer&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> countable objects </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> pedestrians(</span><span style=\"font-family: Kokila;\">&#2346;&#2376;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2369;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;No fewer than&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word from the following sentence. Leisure </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He was relaxing and enjoying last week in Spain but now he is working hard for the firm.</span></p>\\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word from the following sentence. Leisure </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He was relaxing and enjoying last week in Spain but now he is working hard for the firm.</span></p>\\n",
                    options_en: ["<p>enjoying</p>\\n", "<p>working</p>\\n", 
                                "<p>relaxing</p>\\n", "<p>hard</p>\\n"],
                    options_hi: ["<p>enjoying</p>\\n", "<p>working</p>\\n",
                                "<p>relaxing</p>\\n", "<p>hard</p>\\n"],
                    solution_en: "<p>4.(b) <strong>Working</strong><span style=\"font-family: Cambria Math;\">- the act of being engaged in a job, task, or activity.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Leisure</span></strong><span style=\"font-family: Cambria Math;\">- free time or time available for relaxation and enjoyment.</span></p>\\n",
                    solution_hi: "<p>4.(b) <strong>Working</strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\">) - the act of being engaged in a job, task, or activity.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Leisure</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2326;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Kokila;\">&#2347;&#2369;&#2352;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\">) - free time or time available for relaxation and enjoyment.</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Select the option that expresses the following sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A remarkable discovery was made by the scientist.</span></p>\\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">Select the option that expresses the following sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A remarkable discovery was made by the scientist.</span></p>\\n",
                    options_en: ["<p>The scientist made a remarkable discovery.</p>\\n", "<p>The discovery made by the scientist was remarkable.</p>\\n", 
                                "<p>The remarkable scientist made a discovery.</p>\\n", "<p>A remarkable discovery is made by the scientist.</p>\\n"],
                    options_hi: ["<p>The scientist made a remarkable discovery.</p>\\n", "<p>The discovery made by the scientist was remarkable.</p>\\n",
                                "<p>The remarkable scientist made a discovery.</p>\\n", "<p>A remarkable discovery is made by the scientist.</p>\\n"],
                    solution_en: "<p>5.(a) <span style=\"font-family: Cambria Math;\">The scientist made a remarkable discovery.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(Correct)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) The discovery made by the scientist was remarkable. (Incorrect Sentence Structure)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c)<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">The remarkable scientist</span></span><span style=\"font-weight: 400;\"> made a discovery. (Incorrect Subject)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) A remarkable discovery </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is made</span></span><span style=\"font-weight: 400;\"> by the scientist. (Incorrect Tense)</span></span></p>\\n",
                    solution_hi: "<p>5.(a) <span style=\"font-family: Cambria Math;\">The scientist made a remarkable discovery.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(Correct)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) The discovery made by the scientist was remarkable. (&#2327;&#2354;&#2340; Sentence Structure)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c)<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">The remarkable scientist</span></span><span style=\"font-weight: 400;\"> made a discovery. (&#2327;&#2354;&#2340; Subject)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) A remarkable discovery </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is made</span></span><span style=\"font-weight: 400;\"> by the scientist. (&#2327;&#2354;&#2340; Tense)</span></span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6. </span><span style=\"font-family:Cambria Math\">The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If you don\'t find any error, mark \'No error\' as your answer.</span></p> <p><span style=\"font-family:Cambria Math\">He speaks pleasantly / with her yesterday / evening but not today.</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Cambria Math\">The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If you don\'t find any error, mark \'No error\' as your answer.</span></p> <p><span style=\"font-family:Cambria Math\">He speaks pleasantly / with her yesterday / evening but not today.</span></p>",
                    options_en: [" <p> He speaks pleasantly</span></p>", " <p> with her yesterday</span></p>", 
                                " <p> No error</span></p>", " <p> evening but not today.</span></p>"],
                    options_hi: [" <p> He speaks pleasantly</span></p>", " <p> with her yesterday</span></p>",
                                " <p> No error</span></p>", " <p> evening but not today.</span></p>"],
                    solution_en: " <p>6.(a)</span><span style=\"font-family:Cambria Math\"> He speaks pleasantly</span></p> <p><span style=\"font-family:Cambria Math\">‘Speaks’ must be replaced with ‘spoke’ as we generally use ‘simple past tense(V</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">)’ to describe actions, events, or states that occurred and were completed in the past. Similarly, in the sentence, the action was completed yesterday.  Hence, ‘he spoke(V</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">) pleasantly’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>6.(a)</span><span style=\"font-family:Cambria Math\"> He speaks pleasantly</span></p> <p><span style=\"font-family:Cambria Math\">‘Speaks’ </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> ‘spoke’ </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होगा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आमतौर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कार्यों</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">घटनाओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्थितियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्णन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> ‘simple past tense(V</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">)’ </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> past </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">घटित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पूर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चुकी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">थीं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रकार</span><span style=\"font-family:Cambria Math\"> sentence </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">घटना</span><span style=\"font-family:Cambria Math\">/action </span><span style=\"font-family:Kokila\">कल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पूर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">थी।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अतः</span><span style=\"font-family:Cambria Math\">, ‘he spoke(V</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">) pleasantly’ </span><span style=\"font-family:Kokila\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयुक्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Given below are four jumbled sentences. Select the option that gives their correct logical sequence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>A.</strong> The young lion tore through the formation and decimated the enemy lines.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>B. </strong>The enemy soldiers panicked and started killing each other.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>C.</strong> It is said that he used a weapon with a hallucinating agent that created a chimera of hundreds of Abhimanyus.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>D. </strong>Seeing the rout they were facing, young Abhimanyu led a powerful counterattack.</span></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">Given below are four jumbled sentences. Select the option that gives their correct logical sequence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>A.</strong> The young lion tore through the formation and decimated the enemy lines.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>B. </strong>The enemy soldiers panicked and started killing each other.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>C.</strong> It is said that he used a weapon with a hallucinating agent that created a chimera of hundreds of Abhimanyus.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>D. </strong>Seeing the rout they were facing, young Abhimanyu led a powerful counterattack.</span></p>\\n",
                    options_en: ["<p>CDAB</p>\\n", "<p>DACB</p>\\n", 
                                "<p>ACBD</p>\\n", "<p>ABCD</p>\\n"],
                    options_hi: ["<p>CDAB</p>\\n", "<p>DACB</p>\\n",
                                "<p>ACBD</p>\\n", "<p>ABCD</p>\\n"],
                    solution_en: "<p>7.(b) <span style=\"font-family: Cambria Math;\">DACB</span><span style=\"font-family: Cambria Math;\">Sentence D will be the starting line as it contains the main subject of the parajumble i.e. young abhimanyu. And, Sentence A states Abhimanyu\'s actions during the counterattack. So, A will follow D. Further, Sentence C states additional information about Abhimanyu\'s tactics &amp; Sentence B states the outcome of Abhimanyu\'s tactics. So, B will follow C. Going through the options, option (b) has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>7.(b) <span style=\"font-family: Cambria Math;\">DACB</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Sentence D </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> parajumble </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2359;&#2351;</span><span style=\"font-family: Cambria Math;\">, &lsquo;young abhimanyu&rsquo; </span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\">, sentence A, </span><span style=\"font-family: Kokila;\">&#2332;&#2357;&#2366;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2350;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2350;&#2344;&#2381;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, D </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, sentence C </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2350;&#2344;&#2381;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2339;&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2367;&#2352;&#2367;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> sentence B </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2350;&#2344;&#2381;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2339;&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, C </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> Options </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, option (b) </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A person who does not believe in the existence of God</span></p>\\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A person who does not believe in the existence of God</span></p>\\n",
                    options_en: ["<p>Atheist</p>\\n", "<p>Priest</p>\\n", 
                                "<p>Theist</p>\\n", "<p>Satan</p>\\n"],
                    options_hi: ["<p>Atheist</p>\\n", "<p>Priest</p>\\n",
                                "<p>Theist</p>\\n", "<p>Satan</p>\\n"],
                    solution_en: "<p>8.(a) <strong>Atheist</strong><span style=\"font-family: Cambria Math;\">- a person who does not believe in the existence of God.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Priest </span></strong><span style=\"font-family: Cambria Math;\">- a religious leader who performs sacred rituals and ceremonies in various faiths.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Theist</span></strong><span style=\"font-family: Cambria Math;\">- a person who believes in the existence of God or gods.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Satan</span></strong><span style=\"font-family: Cambria Math;\">- a supernatural being associated with evil or the adversary of God.</span></p>\\n",
                    solution_hi: "<p>8.(a) <strong>Atheist </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2344;&#2366;&#2360;&#2381;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">) - a person who does not believe in the existence of God.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Priest </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2332;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) - a religious leader who performs sacred rituals and ceremonies in various faiths.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Theist</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2310;&#2360;&#2381;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">) - a person who believes in the existence of God or gods.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Satan </span></strong><span style=\"font-family: Cambria Math;\">( </span><span style=\"font-family: Kokila;\">&#2358;&#2376;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Kokila;\">&#2346;&#2367;&#2358;&#2366;&#2330;</span><span style=\"font-family: Cambria Math;\">) - a supernatural being associated with evil or the adversary of God.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate active form of the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The movie was directed by the famous filmmaker and produced by the studio.</span></p>\\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate active form of the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The movie was directed by the famous filmmaker and produced by the studio.</span></p>\\n",
                    options_en: ["<p>The famous filmmaker directed the movie and the studio produced it.</p>\\n", "<p>The movie was being directed by the famous filmmaker and produced by the studio.</p>\\n", 
                                "<p>The movie was directed and produced by the famous filmmaker and the studio.</p>\\n", "<p>The famous filmmaker and the studio collaborated to make the movie.</p>\\n"],
                    options_hi: ["<p>The famous filmmaker directed the movie and the studio produced it.</p>\\n", "<p>The movie was being directed by the famous filmmaker and produced by the studio.</p>\\n",
                                "<p>The movie was directed and produced by the famous filmmaker and the studio.</p>\\n", "<p>The famous filmmaker and the studio collaborated to make the movie.</p>\\n"],
                    solution_en: "<p>9.(a) &#8203;&#8203;<span style=\"font-family: Cambria Math;\">The famous filmmaker directed the movie and the studio produced it.(Correct)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) The movie was being directed by the famous filmmaker and produced by the studio. (Incorrect Sentence structure)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c) The movie was directed and produced by the famous filmmaker and the studio. (Incorrect Sentence structure)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) The famous filmmaker and the studio collaborated to make the movie.(Incorrect Sentence structure)</span></span></p>\\n",
                    solution_hi: "<p>9.(a) &#8203;&#8203;<span style=\"font-family: Cambria Math;\">The famous filmmaker directed the movie and the studio produced it.(Correct)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) The movie was being directed by the famous filmmaker and produced by the studio. (&#2327;&#2354;&#2340; Sentence structure)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c) The movie was directed and produced by the famous filmmaker and the studio. (&#2327;&#2354;&#2340; Sentence structure)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) The famous filmmaker and the studio collaborated to make the movie.(&#2327;&#2354;&#2340; Sentence structure)</span></span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Though Ravi is silent and reserved, he has got </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">a finger in every pie</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    question_hi: "<p>10. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Though Ravi is silent and reserved, he has got </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">a finger in every pie</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    options_en: ["<p>special interest in certain things</p>\\n", "<p>involved in many things</p>\\n", 
                                "<p>an opinion of his own</p>\\n", "<p>a cold attitude toward everything</p>\\n"],
                    options_hi: ["<p>special interest in certain things</p>\\n", "<p>involved in many things</p>\\n",
                                "<p>an opinion of his own</p>\\n", "<p>a cold attitude toward everything</p>\\n"],
                    solution_en: "<p>10.(b) <span style=\"font-family: Cambria Math;\">A finger in every pie - involved in many things.</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>10.(b) <span style=\"font-family: Cambria Math;\">A finger in every pie - involved in many things. / </span><span style=\"font-family: Kokila;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2368;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2350;&#2367;&#2354;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the option with the correct spellings to replace the underlined words in the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">exhibitad</span></span><span style=\"font-family: Cambria Math;\"> his </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">senseles</span></span><span style=\"font-family: Cambria Math;\"> behaviour at that conference.</span></p>\\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">Select the option with the correct spellings to replace the underlined words in the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">exhibitad</span></span><span style=\"font-family: Cambria Math;\"> his </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">senseles</span></span><span style=\"font-family: Cambria Math;\"> behaviour at that conference.</span></p>\\n",
                    options_en: ["<p>exhibited; sanseless</p>\\n", "<p>exhibited; senseless</p>\\n", 
                                "<p>exibited; senseless</p>\\n", "<p>exhebited; sanseless</p>\\n"],
                    options_hi: ["<p>exhibited; sanseless</p>\\n", "<p>exhibited; senseless</p>\\n",
                                "<p>exibited; senseless</p>\\n", "<p>exhebited; sanseless</p>\\n"],
                    solution_en: "<p>11.(b) <span style=\"font-family: Cambria Math;\">exhibited; senseless</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Exhibited&rsquo; and &lsquo;senseless&rsquo; are the correct spellings.</span></p>\\n",
                    solution_hi: "<p>11.(b) <span style=\"font-family: Cambria Math;\">exhibited; senseless</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Exhibited&rsquo; </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;senseless&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spellings </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">She became </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">frantic</span></span><span style=\"font-family: Cambria Math;\"> as she lost her purse on the busy streets of the markets.</span></p>\\n",
                    question_hi: "<p>12. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">She became </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">frantic</span></span><span style=\"font-family: Cambria Math;\"> as she lost her purse on the busy streets of the markets.</span></p>\\n",
                    options_en: ["<p>Hectic</p>\\n", "<p>Composed</p>\\n", 
                                "<p>Audacious</p>\\n", "<p>Agitated</p>\\n"],
                    options_hi: ["<p>Hectic</p>\\n", "<p>Composed</p>\\n",
                                "<p>Audacious</p>\\n", "<p>Agitated</p>\\n"],
                    solution_en: "<p>12.(d) <strong>Agitated</strong><span style=\"font-family: Cambria Math;\">- feeling emotionally disturbed or upset.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Frantic</span></strong><span style=\"font-family: Cambria Math;\">- in a state of extreme panic or desperation.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Hectic</span></strong><span style=\"font-family: Cambria Math;\">- characterized by a lot of activity and chaos.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Composed</span></strong><span style=\"font-family: Cambria Math;\">- calm and collected, in control of one\'s emotions.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Audacious</span></strong><span style=\"font-family: Cambria Math;\">- willing to take bold risks, fearless and confident.</span></p>\\n",
                    solution_hi: "<p>12.(d) <strong>Agitated</strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2375;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2375;&#2358;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">) - feeling emotionally disturbed or upset.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Frantic </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2375;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - in a state of extreme panic or desperation.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Hectic </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2367;&#2357;&#2381;&#2351;&#2360;&#2381;&#2340;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) - characterized by a lot of activity and chaos.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Composed</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2306;&#2340;&#2330;&#2367;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) - calm and collected, in control of one\'s emotions.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Audacious </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2342;&#2369;&#2307;&#2360;&#2366;&#2361;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\">) - willing to take bold risks, fearless and confident.</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined words in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">How do you think the internet </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">has influenced how we utilise</span></span><span style=\"font-family: Cambria Math;\"> news and media?</span></p>\\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined words in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">How do you think the internet </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">has influenced how we utilise</span></span><span style=\"font-family: Cambria Math;\"> news and media?</span></p>\\n",
                    options_en: ["<p>has managed the patterns we ingest</p>\\n", "<p>has revoked the way we interact</p>\\n", 
                                "<p>has maintained how we receive</p>\\n", "<p>has impacted the way we consume</p>\\n"],
                    options_hi: ["<p>has managed the patterns we ingest</p>\\n", "<p>has revoked the way we interact</p>\\n",
                                "<p>has maintained how we receive</p>\\n", "<p>has impacted the way we consume</p>\\n"],
                    solution_en: "<p>13.(d) <span style=\"font-family: Cambria Math;\">has impacted the way we consume</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The word \"consume\" means to take in information or media and the phrase \"has impacted the way we consume\" states the idea of change in the way we take in digital content. Hence, &lsquo;has impacted the way we consume&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>13.(d) <span style=\"font-family: Cambria Math;\">has impacted the way we consume</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">\"consume\" </span><span style=\"font-family: Kokila;\">&#2358;&#2348;&#2381;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> information </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> media </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> phrase \"has impacted the way we consume\", digital content </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2352;&#2368;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2342;&#2354;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;has impacted the way we consume&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>P. </strong>and the challenges faced in its</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Q. </strong>the report discusses</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>R.</strong> the state of renewable energy&nbsp;</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>S. </strong>implementation along with potential solutions</span></span></p>\\n",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\">Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>P. </strong>and the challenges faced in its</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Q. </strong>the report discusses</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>R.</strong> the state of renewable energy&nbsp;</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>S. </strong>implementation along with potential solutions</span></span></p>\\n",
                    options_en: ["<p>QSRP</p>\\n", "<p>QRPS</p>\\n", 
                                "<p>RQPS</p>\\n", "<p>PQRS</p>\\n"],
                    options_hi: ["<p>QSRP</p>\\n", "<p>QRPS</p>\\n",
                                "<p>RQPS</p>\\n", "<p>PQRS</p>\\n"],
                    solution_en: "<p>14.(b) <span style=\"font-family: Cambria Math;\">QRPS</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The given sentence starts with Part Q as it contains the main subject of the sentence i.e. the report. Part Q will be followed by Part R as it states the topic of the report. Further, Part P connects the previous part with the conjunction &lsquo;and&rsquo; &amp; Part S completes the sentence. So, S will follow P. Going through the options, option (b) has the correct sequence. </span></p>\\n",
                    solution_hi: "<p>14.(b) <span style=\"font-family: Cambria Math;\">QRPS</span></p>\\r\\n<p><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> Part Q </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2359;&#2351;</span><span style=\"font-family: Cambria Math;\"> &lsquo;the report&rsquo; </span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> Part Q </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> Part R </span><span style=\"font-family: Kokila;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> report </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> topic </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, Part P </span><span style=\"font-family: Kokila;\">&#2346;&#2367;&#2331;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> part </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> conjunction &lsquo;and&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2379;&#2337;&#2364;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Part S, sentence </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> complete </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> S </span><span style=\"font-family: Kokila;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> Options </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, option (b) </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Meenakshi </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">called out for the neighbourhood</span></span><span style=\"font-family: Cambria Math;\"> to get support for her campaign.</span></p>\\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Meenakshi <span style=\"text-decoration: underline;\">called out for the neighbourhood</span> to get support for her campaign.</span></p>\\n",
                    options_en: ["<p>called by the neighbourhood</p>\\n", "<p>called of the neighbourhood</p>\\n", 
                                "<p>called in the neighbourhood</p>\\n", "<p>called around the neighbourhood</p>\\n"],
                    options_hi: ["<p>called by the neighbourhood</p>\\n", "<p>called of the neighbourhood</p>\\n",
                                "<p>called in the neighbourhood</p>\\n", "<p>called around the neighbourhood</p>\\n"],
                    solution_en: "<p>15.(d) <span style=\"font-family: Cambria Math;\">called around the neighbourhood</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The phrase &lsquo;call around&rsquo; means making calls or contacting different individuals or locations. The given sentence states that Meenakshi contacted or reached out to various people or places within her neighborhood to seek support for her campaign. Hence, &lsquo;called around the neighbourhood&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>15.(d) <span style=\"font-family: Cambria Math;\">called around the neighbourhood</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Phrase &lsquo;call around&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2346;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2368;&#2344;&#2366;&#2325;&#2381;&#2359;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2352;&#2381;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2306;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2337;&#2364;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2346;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2369;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2344;&#2366;&#2312;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;called around the neighbourhood&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Indispensable</span></p>\\n",
                    question_hi: "<p>16. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Indispensable</span></p>\\n",
                    options_en: ["<p>Destructive</p>\\n", "<p>Imperative</p>\\n", 
                                "<p>Drastic</p>\\n", "<p>Invariable</p>\\n"],
                    options_hi: ["<p>Destructive</p>\\n", "<p>Imperative</p>\\n",
                                "<p>Drastic</p>\\n", "<p>Invariable</p>\\n"],
                    solution_en: "<p>16.(b) <strong>Imperative</strong><span style=\"font-family: Cambria Math;\">- absolutely necessary or of vital importance.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Indispensable</span></strong><span style=\"font-family: Cambria Math;\">- absolutely necessary.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Destructive</span></strong><span style=\"font-family: Cambria Math;\">- causing harm, damage, or destruction.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Drastic</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>- extremely severe or extreme measures taken for significant change.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Invariable </span></strong><span style=\"font-family: Cambria Math;\">- constant and unchanging.</span></p>\\n",
                    solution_hi: "<p>16.(b) <strong>Imperative </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2367;&#2357;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- absolutely necessary or of vital importance.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Indispensable</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2381;&#2351;&#2366;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\">) - absolutely necessary.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Destructive</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2344;&#2366;&#2358;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) - causing harm, damage, or destruction.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Drastic </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2325;&#2336;&#2379;&#2352;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Kokila;\">&#2360;&#2358;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) - extremely severe or extreme measures taken for significant change.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Invariable </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\">) - constant and unchanging.</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Soothe</span></p>\\n",
                    question_hi: "<p>17. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Soothe</span></p>\\n",
                    options_en: ["<p>Move</p>\\n", "<p>Pacify</p>\\n", 
                                "<p>Instigate</p>\\n", "<p>Escalate</p>\\n"],
                    options_hi: ["<p>Move</p>\\n", "<p>Pacify</p>\\n",
                                "<p>Instigate</p>\\n", "<p>Escalate</p>\\n"],
                    solution_en: "<p>17.(b) <strong>Pacify</strong><span style=\"font-family: Cambria Math;\">- to calm to restore peace.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Soothe</span></strong><span style=\"font-family: Cambria Math;\">- to provide comfort or relief to someone in distress.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Move</span></strong><span style=\"font-family: Cambria Math;\">- to change one\'s position, location, or emotional state.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Instigate</span></strong><span style=\"font-family: Cambria Math;\">- to provoke or initiate, often causing trouble.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Escalate</span></strong><span style=\"font-family: Cambria Math;\">- to increase in intensity or severity, especially in a conflict or problem.</span></p>\\n",
                    solution_hi: "<p>17.(b) <strong>Pacify </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to calm to restore peace.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Soothe </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2361;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to provide comfort or relief to someone in distress.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Move </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to change one\'s position, location, or emotional state.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Instigate </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2313;&#2325;&#2360;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to provoke or initiate, often causing trouble.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Escalate</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2348;&#2397;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to increase in intensity or severity, especially in a conflict or problem.</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined words in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">After he </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">let the cat out of the bag</span></span><span style=\"font-family: Cambria Math;\"> about the surprise party, it was no longer a secret.</span></p>\\n",
                    question_hi: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined words in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">After he </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">let the cat out of the bag</span></span><span style=\"font-family: Cambria Math;\"> about the surprise party, it was no longer a secret.</span></p>\\n",
                    options_en: ["<p>murmur in the ears</p>\\n", "<p>reveal a confidential information</p>\\n", 
                                "<p>show off to his friends</p>\\n", "<p>take the gifts and brag</p>\\n"],
                    options_hi: ["<p>murmur in the ears</p>\\n", "<p>reveal a confidential information</p>\\n",
                                "<p>show off to his friends</p>\\n", "<p>take the gifts and brag</p>\\n"],
                    solution_en: "<p>18.(b) <span style=\"font-family: Cambria Math;\">let the cat out of the bag - reveal a confidential information.</span></p>\\n",
                    solution_hi: "<p>18.(b) <span style=\"font-family: Cambria Math;\">let the cat out of the bag - reveal a confidential information. / </span><span style=\"font-family: Kokila;\">&#2327;&#2379;&#2346;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2332;&#2366;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Fervour</span></p>\\n",
                    question_hi: "<p>19. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Fervour</span></p>\\n",
                    options_en: ["<p>Apathy</p>\\n", "<p>Hatred</p>\\n", 
                                "<p>Enthusiasm</p>\\n", "<p>Disgust</p>\\n"],
                    options_hi: ["<p>Apathy</p>\\n", "<p>Hatred</p>\\n",
                                "<p>Enthusiasm</p>\\n", "<p>Disgust</p>\\n"],
                    solution_en: "<p>19.(c) <strong>Enthusiasm </strong><span style=\"font-family: Cambria Math;\">- passionate and eager excitement or interest.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Fervour</span></strong><span style=\"font-family: Cambria Math;\">- intense and passionate enthusiasm or zeal.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Apathy</span></strong><span style=\"font-family: Cambria Math;\">- lack of interest, enthusiasm, or concern.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Hatred</span></strong><span style=\"font-family: Cambria Math;\">- intense and deep dislike.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Disgust</span></strong><span style=\"font-family: Cambria Math;\">- a strong feeling of dislike.</span></p>\\n",
                    solution_hi: "<p>19.(c) <strong>Enthusiasm </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2360;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\">) - passionate and eager excitement or interest.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Fervour </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2360;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\">) - intense and passionate enthusiasm or zeal.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Apathy</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2313;&#2342;&#2366;&#2360;&#2368;&#2344;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) - lack of interest, enthusiasm, or concern.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Hatred</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2328;&#2371;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\">) - intense and deep dislike.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Disgust</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2328;&#2371;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\">) - a strong feeling of dislike.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He tends to </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">put off doing his work</span></span><span style=\"font-family: Cambria Math;\"> until the last minute, leaving important tasks unfinished and causing himself unnecessary stress.</span></p>\\n",
                    question_hi: "<p>20. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He tends to </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">put off doing his work</span></span><span style=\"font-family: Cambria Math;\"> until the last minute, leaving important tasks unfinished and causing himself unnecessary stress.</span></p>\\n",
                    options_en: ["<p>decline</p>\\n", "<p>adjourn</p>\\n", 
                                "<p>procrastinate</p>\\n", "<p>dwindle</p>\\n"],
                    options_hi: ["<p>decline</p>\\n", "<p>adjourn</p>\\n",
                                "<p>procrastinate</p>\\n", "<p>dwindle</p>\\n"],
                    solution_en: "<p>20.(c)<span style=\"font-family: Cambria Math;\"> procrastinate</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Procrastinate&rsquo; means to keep delaying something that must be done. The phrase \"put off doing his work until the last minute\" is similar in meaning to &lsquo;procrastinate&rsquo;. Hence, &lsquo;procrastinate&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>20.(c)<span style=\"font-family: Cambria Math;\"> procrastinate</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Procrastinate&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2361;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> Phrase \"put off doing his work until the last minute\" </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> &lsquo;procrastinate&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;procrastinate&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.&nbsp; <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    question_hi: "<p>21. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    options_en: ["<p>contender</p>\\n", "<p>compete</p>\\n", 
                                "<p>contend</p>\\n", "<p>quickly</p>\\n"],
                    options_hi: ["<p>contender</p>\\n", "<p>compete</p>\\n",
                                "<p>contend</p>\\n", "<p>quickly</p>\\n"],
                    solution_en: "<p>21.(a) <span style=\"font-family: Cambria Math;\">Contender</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Contender&rsquo; means a person or group competing with others to achieve something. The given passage states if there was such a thing as perfect food, eggs would be a contender. Hence, &lsquo;contender&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>21.(a) <span style=\"font-family: Cambria Math;\">Contender</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Contender&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2370;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2360;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2346;&#2352;&#2381;&#2343;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2376;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2368;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2337;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2366;&#2357;&#2375;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;contender&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\\n",
                    question_hi: "<p>22.&nbsp; <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\\n",
                    options_en: ["<p>for</p>\\n", "<p>with</p>\\n", 
                                "<p>on</p>\\n", "<p>at</p>\\n"],
                    options_hi: ["<p>for</p>\\n", "<p>with</p>\\n",
                                "<p>on</p>\\n", "<p>at</p>\\n"],
                    solution_en: "<p>22.(b) <span style=\"font-family: Cambria Math;\">With</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;With&rsquo; means having something. The given passage states that they are readily available, easy to cook, affordable and packed with protein. Hence, &lsquo;with&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>22.(b) <span style=\"font-family: Cambria Math;\">With</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;With&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2360;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2354;&#2348;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2346;&#2325;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2360;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2347;&#2366;&#2351;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2379;&#2335;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2352;&#2346;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;with&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    question_hi: "<p>23. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol </span><span style=\"font-family: Cambria Math;\">content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    options_en: ["<p>interrogate</p>\\n", "<p>abate</p>\\n", 
                                "<p>questioned</p>\\n", "<p>help</p>\\n"],
                    options_hi: ["<p>interrogate</p>\\n", "<p>abate</p>\\n",
                                "<p>questioned</p>\\n", "<p>help</p>\\n"],
                    solution_en: "<p>23.(d) <span style=\"font-family: Cambria Math;\">Help</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Help&rsquo; means to make something easier. The given passage states that eating eggs along with other food makes it easier for the body to absorb more vitamins. Hence, &lsquo;help&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>23.(d) <span style=\"font-family: Cambria Math;\">Help</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Help&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2368;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2360;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2344;&#2366;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2337;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2352;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2335;&#2366;&#2350;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2357;&#2358;&#2379;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2360;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;help&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    question_hi: "<p>24.<span style=\"font-family: Cambria Math;\"> <strong>Cloze Test:</strong></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    options_en: ["<p>low</p>\\n", "<p>medium</p>\\n", 
                                "<p>ideal</p>\\n", "<p>high</p>\\n"],
                    options_hi: ["<p>low</p>\\n", "<p>medium</p>\\n",
                                "<p>ideal</p>\\n", "<p>high</p>\\n"],
                    solution_en: "<p>24.(d) <span style=\"font-family: Cambria Math;\">High</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The given passage states that eating eggs has also been controversial due to their high cholesterol content. Hence, &lsquo;high&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>24.(d) <span style=\"font-family: Cambria Math;\">High</span></p>\\r\\n<p><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2337;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2330;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2354;&#2375;&#2360;&#2381;&#2335;&#2381;&#2352;&#2377;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2357;&#2366;&#2342;&#2366;&#2360;&#2381;&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;high&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\\n",
                    question_hi: "<p>25. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If there was such a thing as perfect food, eggs would be a __________ (21) . They are readily available, easy to cook, affordable and packed ___________ (22) protein. An egg is meant to be something that has all the right ingredients to grow an organism, so obviously, it is very nutrient-dense. Eating eggs alongside other food can _____________ (23) our bodies absorb more vitamins too. For example, one study found that adding an egg to a salad can increase how much vitamin E we get from the salad. But for decades, eating eggs has also been controversial due to their ___________ (24) cholesterol content-which some studies have linked to an increased risk of heart disease. Cholesterol, a yellowish fat produced in our liver and intestines, can be found in every one of our body\'s cells. We ___________ (25) think of it as \'bad\'. But cholesterol is a crucial building block in our cell membranes. It also is needed for the body to make vitamin D and the hormones testosterone and oestrogen.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\\n",
                    options_en: ["<p>normally</p>\\n", "<p>remotely</p>\\n", 
                                "<p>excitedly</p>\\n", "<p>intermittently</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>normally</p>\\n", "<p>remotely</p>\\n",
                                "<p>excitedly</p>\\n", "<p>intermittently</p>\\n"],
                    solution_en: "<p>25.(a)<span style=\"font-family: Cambria Math;\"> Normally</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Normally&rsquo; is used to indicate the typical or common perception or understanding. The given passage states that we normally think of it as bad. Hence, &lsquo;normally&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>25.(a)<span style=\"font-family: Cambria Math;\"> Normally</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Normally&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2343;&#2366;&#2352;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2333;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2350;&#2340;&#2380;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2369;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;normally&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>