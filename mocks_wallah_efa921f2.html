<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. An amount of ₹8,000 was invested for 2 years, partly in scheme 1 at the rate of 5%&nbsp;simple interest per annum and the rest in scheme 2 at the rate of 4% simple interest&nbsp;per annum. The total interest received at the end was ₹720. The amount of money&nbsp;invested in scheme 1 is:</p>",
                    question_hi: "<p>1. ₹8,000 की धनराशि का आंशिक रूप से 2 वर्ष के लिए योजना 1 में 5% साधारण ब्याज की दर से और शेष धनराशि का योजना 2 में 4% साधारण ब्याज की दर से निवेश किया जाता हैं। अवधि के अंत में प्राप्त कुल ब्याज ₹720 था। योजना 1 में निवेश की गई धनराशि कितनी है?</p>",
                    options_en: ["<p>₹7,200</p>", "<p>₹4,400</p>", 
                                "<p>₹3,640</p>", "<p>₹4,000</p>"],
                    options_hi: ["<p>₹7,200</p>", "<p>₹4,400</p>",
                                "<p>₹3,640</p>", "<p>₹4,000</p>"],
                    solution_en: "<p>1.(d)<br>Overall rate = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 9%<br>Rate on scheme 1st = 5 &times; 2 = 10%<br>Rate on scheme 2nd = 4 &times; 2 = 8%<br>By alligation method : - <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736832728067.png\" alt=\"rId5\" width=\"191\" height=\"100\"><br>So the amount in both schemes is equal.<br>Hence, the amount of money invested in scheme 1st is 4000.</p>",
                    solution_hi: "<p>1.(d)<br>कुल दर = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 9%<br>योजना 1 पर दर = 5 &times; 2 = 10%<br>योजना 2 पर दर = 4 &times; 2 = 8%<br>पृथकीकरण विधि द्वारा :- <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736832728242.png\" alt=\"rId6\" width=\"153\" height=\"103\"><br>इसलिए दोनों योजनाओं में धनराशि बराबर है.<br>अतः, योजना 1 में निवेश की गई धनराशि 4000 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A sum of ₹18,000 gives a simple interest of ₹ 3,240 in 2 years and 3 months. The rate of interest per annum is:</p>",
                    question_hi: "<p>2. ₹18,000 की धनराशि पर 2 वर्ष और 3 माह में ₹3,240 का साधारण ब्याज प्राप्त होता है। वार्षिक ब्याज दर कितनी है?</p>",
                    options_en: ["<p>12%</p>", "<p>8%</p>", 
                                "<p>7%</p>", "<p>10%</p>"],
                    options_hi: ["<p>12%</p>", "<p>8%</p>",
                                "<p>7%</p>", "<p>10%</p>"],
                    solution_en: "<p>2.(b) SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> 3240 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18000</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>27</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mrow><mn>15</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mrow><mn>15</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math> = 8%</p>",
                    solution_hi: "<p>2.(b) साधारण ब्याज = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> 3240 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18000</mn><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mn>27</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> दर(R) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mrow><mn>15</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math>&nbsp;<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mrow><mn>15</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math>&nbsp;= 8%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. An equal sum of money was lent to P and Q at 8.6% per annum for a period of 7 years and 8 years, respectively. If the difference in interest paid by them was ₹172, then the sum lent to each was:</p>",
                    question_hi: "<p>3. P और Q को समान धनराशि क्रमशः 7 वर्ष और 8 वर्ष की अवधि के लिए 8.6% वार्षिक दर पर ऋण पर दी गई थी। यदि उनके द्वारा भुगतान किए गए ब्याज में ₹172 का अंतर था, तो प्रत्येक को ऋण पर दी गई धनराशि कितनी थी?</p>",
                    options_en: ["<p>₹2,000</p>", "<p>₹3,000</p>", 
                                "<p>₹2,150</p>", "<p>₹185</p>"],
                    options_hi: ["<p>₹2,000</p>", "<p>₹3,000</p>",
                                "<p>₹2,150</p>", "<p>₹185</p>"],
                    solution_en: "<p>3.(a)<br>For P = 7 &times; 8.6%<br>For Q = 8 &times; 8.6%<br>difference (8.6 &times; 1 units) = ₹172<br>Required sum (100 units) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>172</mn><mpadded lspace=\"+1px\"><mn>8.6</mn></mpadded></mfrac></math>&nbsp;&times; 100 = ₹2000</p>",
                    solution_hi: "<p>3.(a)<br>P के लिए = 7 &times; 8.6%<br>Q के लिए = 8 &times; 8.6%<br>अंतर (8.6 &times; 1 इकाई) = ₹172<br>आवश्यक योग (100 इकाई) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>172</mn><mpadded lspace=\"+1px\"><mn>8.6</mn></mpadded></mfrac></math>&nbsp;&times; 100 = ₹2000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If the ratio of principal and simple interest for 5 years is 10 : 7, then the rate of interest per annum is:</p>",
                    question_hi: "<p>4. यदि 5 वर्ष के लिए मूलधन और साधारण ब्याज का अनुपात 10 : 7 है, तो वार्षिक ब्याज की दर क्या है?</p>",
                    options_en: ["<p>10%</p>", "<p>11%</p>", 
                                "<p>15%</p>", "<p>14%</p>"],
                    options_hi: ["<p>10%</p>", "<p>11%</p>",
                                "<p>15%</p>", "<p>14%</p>"],
                    solution_en: "<p>4.(d) <br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">P</mi></mrow><mrow><mi mathvariant=\"bold-italic\">S</mi><mi mathvariant=\"bold-italic\">I</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mpadded voffset=\"-1px\"><mfrac><mn>10</mn><mn>7</mn></mfrac></mpadded></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"bold-italic\">P</mi><mpadded lspace=\"+1px\" voffset=\"+1px\"><mfrac><mrow><mpadded lspace=\"+2px\" voffset=\"+1px\"><mi mathvariant=\"bold\">P</mi><mpadded><mo mathvariant=\"bold\">&#215;</mo></mpadded><mi mathvariant=\"bold\">&#160;</mi></mpadded><mi mathvariant=\"bold-italic\">R</mi><mo mathvariant=\"bold\">&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></mpadded></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mpadded voffset=\"-1px\"><mfrac><mn>10</mn><mn>7</mn></mfrac></mpadded></math><br><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mpadded voffset=\"-1px\"><mfrac><mn>10</mn><mn>7</mn></mfrac></mpadded></math><br>R = 14%</p>",
                    solution_hi: "<p>4.(d) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mpadded><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi></mpadded><mrow><mi>&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</mi><mpadded></mpadded><mi>&#2348;&#2381;&#2351;&#2366;&#2332;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mpadded voffset=\"-1px\"><mfrac><mn>10</mn><mn>7</mn></mfrac></mpadded></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mpadded lspace=\"+1px\"><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi></mpadded><mfrac><mpadded><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mn>5</mn></mpadded><mn>100</mn></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mpadded voffset=\"-1px\"><mfrac><mn>10</mn><mn>7</mn></mfrac></mpadded></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mpadded lspace=\"+1px\"><mn>5</mn></mpadded><mo>&#215;</mo><mpadded><mi mathvariant=\"bold\">&#2342;&#2352;</mi></mpadded></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mpadded voffset=\"-1px\"><mfrac><mn>10</mn><mn>7</mn></mfrac></mpadded></math><br>दर = 14%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Find the amount to be paid at the end if ₹4,500 is invested for a period of 4 years at the rate of 8% simple interest per annum.</p>",
                    question_hi: "<p>5. यदि ₹4,500 की धनराशि का 8% वार्षिक साधारण ब्याज की दर से 4 वर्ष की अवधि के लिए निवेश किया जाता है, तो अवधि के अंत में भुगतान की जाने वाली धनराशि ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹6,080</p>", "<p>₹5,940</p>", 
                                "<p>₹6,300</p>", "<p>₹5,490</p>"],
                    options_hi: ["<p>₹6,080</p>", "<p>₹5,940</p>",
                                "<p>₹6,300</p>", "<p>₹5,490</p>"],
                    solution_en: "<p>5.(b) let the principal = 100<br>Amount after 4 year with rate of 8% = 132<br>Now,<br>100 unit = ₹ 4500<br>132 unit = 45 &times; 132 = ₹ 5940</p>",
                    solution_hi: "<p>5.(b) माना मूलधन = 100<br>4 वर्ष के बाद 8% की दर से राशि = 132<br>अब,<br>100 इकाई = ₹ 4500<br>132 इकाई = 45 &times; 132 = ₹ 5940</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A person borrowed some money at the rate of 8% per annum for the first two years, at<br>the rate of 11% per annum for the next three years, and at the rate of 16% per annum for the period beyond five years. If he pays a total interest of ₹21,400 at the end of nine years, how much money did he borrow (round to the nearest unit) ?</p>",
                    question_hi: "<p>6. एक व्यक्ति ने पहले दो वर्षों के लिए 8% वार्षिक की दर से, अगले तीन वर्षों के लिए 11% वार्षिक की दर<br>से और पांच वर्षों से अधिक की अवधि के लिए 16% वार्षिक की दर से कुछ धनराशि ऋण पर ली। यदि वह नौ वर्ष के अंत में ब्याज के रूप में कुल ₹21,400 का भुगतान करता है, तो उसने कितनी धनराशि ऋण पर ली थी (निकटतम इकाई तक पूर्णांकित)?</p>",
                    options_en: ["<p>₹15,938</p>", "<p>₹11,938</p>", 
                                "<p>₹18,938</p>", "<p>₹18,738</p>"],
                    options_hi: ["<p>₹15,938</p>", "<p>₹11,938</p>",
                                "<p>₹18,938</p>", "<p>₹18,738</p>"],
                    solution_en: "<p>6.(c)<br>SI of 2 years = 2 &times; 8 = 16%<br>SI of next 3 years = 3 &times; 11 = 33%<br>SI of next 4 years = 4 &times; 16 = 64%<br>Total interest = 16% + 33% + 64% = 113%<br>113% = ₹ 21400<br>(borrowed money) 100% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mpadded lspace=\"-2px\" voffset=\"-1px\"><mfrac><mn>21400</mn><mrow><mpadded><mn>113</mn></mpadded><mi>&#160;</mi></mrow></mfrac></mpadded></math> &times; 100 = ₹ 18,938 (approx)</p>",
                    solution_hi: "<p>6.(c)<br>दो वर्ष का साधारण ब्याज = 2 &times; 8 = 16%<br>अगले तीन वर्ष का साधारण ब्याज= 3 &times; 11 = 33%<br>अगले चार वर्ष का साधारण ब्याज = 4 &times; 16 = 64%<br>कुल ब्याज = 16% + 33% + 64% = 113%<br>113% = ₹ 21400 <br>(उधार ली गई धनराशि ) 100% = <math display=\"inline\"><mfrac><mrow><mn>21400</mn></mrow><mrow><mn>113</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = ₹ 18,938 (लगभग)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. What will be the amount due on ₹12,000 in 2 years when the rate of simple interest on<br>successive years is 9% and 10%, respectively?</p>",
                    question_hi: "<p>7. 2 वर्षों में ₹12,000 पर देय धनराशि क्या होगी जब क्रमिक वर्षों में साधारण ब्याज की दर क्रमशः 9%<br>और 10% है ?</p>",
                    options_en: ["<p>₹14,250</p>", "<p>₹14,350</p>", 
                                "<p>₹14,150</p>", "<p>₹14,280</p>"],
                    options_hi: ["<p>₹14,250</p>", "<p>₹14,350</p>",
                                "<p>₹14,150</p>", "<p>₹14,280</p>"],
                    solution_en: "<p>7.(d)<br>Amount due = 12000 &times; <math display=\"inline\"><mfrac><mrow><mn>119</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= ₹14,280</p>",
                    solution_hi: "<p>7.(d)<br>देय राशि = 12000 &times; <math display=\"inline\"><mfrac><mrow><mn>11</mn><mn>9</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= ₹14,280</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A sum of money becomes five times its original value in 15 years when invested at a certain simple interest rate. If the sum was invested twice the time at the same rate of interest, what would be the final amount?</p>",
                    question_hi: "<p>8. एक निश्चित साधारण ब्याज दर पर निवेश करने पर एक धनराशि 15 वर्षों में अपने मूल मूल्य से पांच गुना हो जाती है। यदि धनराशि को समान ब्याज दर पर दुगने समय के लिए निवेश किया जाए, तो अंतिम धनराशि क्या होगी?</p>",
                    options_en: ["<p>The money becomes 9 times its original value</p>", "<p>The money becomes 7 times its original value</p>", 
                                "<p>The money becomes 6 times its original value</p>", "<p>The money becomes 8 times its original value</p>"],
                    options_hi: ["<p>धनराशि अपने मूल मूल्य से 9 गुना हो जाएगी</p>", "<p>धनराशि अपने मूल मूल्य से 7 गुना हो जाएगी</p>",
                                "<p>धनराशि अपने मूल मूल्य से 6 गुना हो जाएगी</p>", "<p>धनराशि अपने मूल मूल्य से 8 गुना हो जाएगी</p>"],
                    solution_en: "<p>8.(a)<br>SI = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mi>t</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>4 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded lspace=\"+1px\"><mn>1</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-1px\"><mi>r</mi></mpadded><mo>&#215;</mo><mpadded lspace=\"-1px\"><mn>15</mn></mpadded></mrow><mn>100</mn></mfrac></math><br>r = <math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math><br>Now,<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded><mn>1</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-1px\"><mn>400</mn></mpadded><mo>&#215;</mo><mpadded><mn>30</mn></mpadded></mrow><mpadded lspace=\"+1px\"><mn>100</mn><mo>&#215;</mo><mn>15</mn></mpadded></mfrac></math> = 8<br>So the final amount will be 1 + 8 = 9 units<br>That means the amount will be 9 times of its original value.</p>",
                    solution_hi: "<p>8.(a)<br>साधारण ब्याज = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>4 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mpadded lspace=\"+1px\"><mi>&#2342;&#2352;</mi></mpadded><mo>&#215;</mo><mpadded><mn>15</mn></mpadded></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mi>&#2342;</mi><mi>&#2352;</mi><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mn>15</mn></mfrac></math><br>अब,<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded><mn>1</mn></mpadded><mo>&#215;</mo><mn>400</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>15</mn></mrow></mfrac></math> = 8<br>तो अंतिम राशि 1 + 8 = 9 इकाई होगी<br>यानी रकम अपनी मूल कीमत से 9 गुना होगी.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Ram invested a certain sum of money at simple interest. It amounted to <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> of itself in 3 years. What will be its rate per cent per annum?</p>",
                    question_hi: "<p>9. राम ने साधारण ब्याज पर एक निश्चित धनराशि निवेश की। 3 वर्षों में वह अपने आप का <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> गुना हो जाती है। इसकी वार्षिक प्रतिशत दर क्या होगी?</p>",
                    options_en: ["<p>8.20%</p>", "<p>9.12%</p>", 
                                "<p>7.25%</p>", "<p>8.33%</p>"],
                    options_hi: ["<p>8.20%</p>", "<p>9.12%</p>",
                                "<p>7.25%</p>", "<p>8.33%</p>"],
                    solution_en: "<p>9.(d)<br>Let simple interest = 100 unit<br>Amount after 3 year = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 125 unit<br>Interest in 3 year = 25 unit<br>So, rate % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#215;</mo><mn>25</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 8.33%</p>",
                    solution_hi: "<p>9.(d)<br>माना साधारण ब्याज = 100 इकाई<br>3 वर्ष के बाद राशि = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 125 इकाई<br>3 वर्ष में ब्याज = 25 इकाई<br>तो, दर % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mo>&#215;</mo><mn>25</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 8.33%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. In how much time (in years) will ₹7,200 amount to ₹8,928 at simple interest at the rate of 8% per annum?</p>",
                    question_hi: "<p>10. ₹7,200 की धनराशि 8% वार्षिक दर से साधारण ब्याज पर कितने समय में (वर्षों में) ₹8,928 हो जाएगी?</p>",
                    options_en: ["<p>3</p>", "<p>4</p>", 
                                "<p>5</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>4</p>",
                                "<p>5</p>", "<p>2</p>"],
                    solution_en: "<p>10.(a) <br>SI = 8928 - 7200 = ₹1728<br>Principal = ₹7,200 , rate = 8%<br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded lspace=\"+2px\"><mn>100</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-2px\"><mi>S</mi><mi>I</mi></mpadded></mrow><mrow><mpadded lspace=\"+2px\"><mi>P</mi></mpadded><mo>&#215;</mo><mpadded lspace=\"-1px\"><mi>R</mi></mpadded></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded lspace=\"+2px\"><mn>100</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-2px\"><mn>1728</mn></mpadded></mrow><mrow><mpadded lspace=\"+2px\"><mn>7200</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-1px\"><mn>8</mn></mpadded></mrow></mfrac></math> = 3 year</p>",
                    solution_hi: "<p>10.(a) <br>साधारण ब्याज = 8928 - 7200 = ₹1728<br>मूलधन = ₹7,200 , दर = 8%<br>समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded lspace=\"+2px\"><mpadded lspace=\"+3px\"><mn>100</mn></mpadded><mo>&#160;</mo></mpadded><mo>&#215;</mo><mpadded><mi>&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</mi><mo>&#160;</mo><mpadded lspace=\"+1px\"><mi>&#2348;&#2381;&#2351;&#2366;&#2332;</mi></mpadded></mpadded></mrow><mrow><mpadded lspace=\"+5px\"><mpadded lspace=\"+1px\"><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi></mpadded><mo>&#160;</mo><mo>&#160;</mo></mpadded><mo>&#215;</mo><mpadded lspace=\"-2px\"><mo>&#160;</mo><mpadded lspace=\"-2px\"><mi>&#2342;&#2352;</mi></mpadded></mpadded></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded lspace=\"+2px\"><mn>100</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-2px\"><mn>1728</mn></mpadded></mrow><mrow><mpadded lspace=\"+2px\"><mn>7200</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-1px\"><mn>8</mn></mpadded></mrow></mfrac></math> = 3 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If a sum of ₹3,260 on simple interest amounts to ₹5,420 in 6 years, then what will this<br>sum amount to in 4 years at the same rate of interest?</p>",
                    question_hi: "<p>11. यदि साधारण ब्याज पर ₹3,260 की धनराशि 6 वर्ष में ₹5,420 हो जाती है, तो यह धनराशि समान ब्याज दर पर 4 वर्ष में कितनी होगी?</p>",
                    options_en: ["<p>₹4,500</p>", "<p>₹3,700</p>", 
                                "<p>₹4,700</p>", "<p>₹3,900</p>"],
                    options_hi: ["<p>₹4,500</p>", "<p>₹3,700</p>",
                                "<p>₹4,700</p>", "<p>₹3,900</p>"],
                    solution_en: "<p>11.(c)<br>SI in 6 years = 5420 - 3260 = ₹2160<br>SI in 4 years = <math display=\"inline\"><mfrac><mrow><mn>2160</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 4 = ₹1440<br>Sum amount to in 4 years = 3260 + 1440 = ₹4700</p>",
                    solution_hi: "<p>11.(c)<br>6 वर्षों में साधारण ब्याज = 5420 - 3260 = ₹2160<br>4 वर्ष में साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>2160</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 4 = ₹1440<br>4 वर्ष में कुल राशि = 3260 + 1440 = ₹4700</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. An amount invested fetched a total simple interest of ₹4,050 at the rate of 9% per annum in 5 years. What is the amount invested (in ₹)?</p>",
                    question_hi: "<p>12. निवेश की गई एक धनराशि पर 9% वार्षिक की दर से 5 वर्ष में कुल ₹4,050 का साधारण ब्याज प्राप्त हुआ। निवेश की गई धनराशि (₹ में) क्या है?</p>",
                    options_en: ["<p>9,050</p>", "<p>7,500</p>", 
                                "<p>8,300</p>", "<p>9,000</p>"],
                    options_hi: ["<p>9,050</p>", "<p>7,500</p>",
                                "<p>8,300</p>", "<p>9,000</p>"],
                    solution_en: "<p>12.(d)<br>SI = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mi>t</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>4050 = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>p = ₹ 9,000</p>",
                    solution_hi: "<p>12.(d)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mpadded><mi>&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</mi><mo>(</mo><mi>p</mi><mo>)</mo><mpadded lspace=\"-1px\"><mo>&#215;</mo></mpadded><mpadded lspace=\"-1px\"><mpadded lspace=\"-3px\"><mo>&#160;</mo><mi>&#2342;&#2352;</mi></mpadded><mo>(</mo><mi>r</mi><mo>)</mo></mpadded><mo>&#215;</mo><mpadded lspace=\"-2px\"><mi>&#2360;&#2350;&#2351;</mi><mo>(</mo><mi>t</mi><mo stretchy=\"true\">)</mo></mpadded></mpadded><mn>100</mn></mfrac></math><br>4050 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math><br>(धनराशि) p = ₹ 9,000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. What will be the amount due on ₹36,000 in 2 years when the rate of simple interest for successive years is 8% and 6%, respectively ?</p>",
                    question_hi: "<p>13. 2 वर्षों में ₹36,000 पर देय राशि क्या होगी जब क्रमिक वर्षों के लिए साधारण ब्याज की दर क्रमशः 8% और 6% है ?</p>",
                    options_en: ["<p>₹38,880</p>", "<p>₹40,320</p>", 
                                "<p>₹41,760</p>", "<p>₹41,040</p>"],
                    options_hi: ["<p>₹38,880</p>", "<p>₹40,320</p>",
                                "<p>₹41,760</p>", "<p>₹41,040</p>"],
                    solution_en: "<p>13.(d) <br>Interest for two years = (8 + 6)% = 14%<br>Hence, required amount = 36000 &times; <math display=\"inline\"><mfrac><mrow><mn>114</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹41,040</p>",
                    solution_hi: "<p>13.(d) <br>दो वर्षों के लिए ब्याज = (8 + 6)% = 14%<br>अतः, आवश्यक राशि = 36000 &times; <math display=\"inline\"><mfrac><mrow><mn>114</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹41,040</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A sum of ₹2,430 gives a simple interest of ₹1,093.50 in 3 years. The rate of interest per annum is :</p>",
                    question_hi: "<p>14. ₹2,430 की धनराशि पर 3 वर्षों में ₹1,093.50 का साधारण ब्याज प्राप्त होता है। वार्षिक ब्याज दर कितनी है?</p>",
                    options_en: ["<p>20%</p>", "<p>10%</p>", 
                                "<p>15%</p>", "<p>25%</p>"],
                    options_hi: ["<p>20%</p>", "<p>10%</p>",
                                "<p>15%</p>", "<p>25%</p>"],
                    solution_en: "<p>14.(c)<br>S.I. = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>1093.50 = <math display=\"inline\"><mfrac><mrow><mn>2430</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>R =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>109350</mn></mrow><mrow><mn>2430</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math></p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36450</mn><mn>2430</mn></mfrac></math> = 15%</p>",
                    solution_hi: "<p>14.(c)<br>साधारण ब्याज = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math> <br>1093.50 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded lspace=\"+3px\"><mn>2430</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-2px\"><mi>&#2342;&#2352;</mi></mpadded><mpadded lspace=\"-2px\"><mo>&#215;</mo><mpadded lspace=\"-2px\"><mn>3</mn></mpadded></mpadded></mrow><mn>100</mn></mfrac></math><br>दर = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>109350</mn><mrow><mpadded lspace=\"+2px\"><mn>2430</mn></mpadded><mo>&#215;</mo><mpadded lspace=\"-2px\"><mn>3</mn></mpadded></mrow></mfrac></math></p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36450</mn><mn>2430</mn></mfrac></math> = 15%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. A certain sum of money is lent out at simple interest. If that money becomes ₹24,000 in<br>2 years and ₹32,000 in 4 years, the annual rate of interest is:</p>",
                    question_hi: "<p>15. साधारण ब्याज पर एक निश्चित धनराशि ऋण पर दी जाती है। यदि वह धनराशि 2 वर्षों में ₹24,000 और 4 वर्षों में ₹32,000 हो जाती है, तो वार्षिक ब्याज दर क्या है?</p>",
                    options_en: ["<p>20%</p>", "<p>25%</p>", 
                                "<p>16%</p>", "<p>30%</p>"],
                    options_hi: ["<p>20%</p>", "<p>25%</p>",
                                "<p>16%</p>", "<p>30%</p>"],
                    solution_en: "<p>15.(b)<br>Simple interest for 1 year = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded lspace=\"+3px\"><mn>32000</mn></mpadded><mo>-</mo><mpadded lspace=\"-2px\"><mn>24000</mn></mpadded></mrow><mn>2</mn></mfrac></math></p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8000</mn><mn>2</mn></mfrac></math> = ₹4000<br>Principal = 24000 - 2 &times; 4000 = ₹16000</p>\n<p>Hence, rate =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>4000</mn></mrow><mrow><mn>16000</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>15.(b)<br>1 वर्ष का साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mpadded lspace=\"+3px\"><mn>32000</mn></mpadded><mo>-</mo><mpadded lspace=\"-2px\"><mn>24000</mn></mpadded></mrow><mn>2</mn></mfrac></math>&nbsp;</p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8000</mn><mn>2</mn></mfrac></math> = ₹4000<br>मूलधन = 24000 - 2 &times; 4000 = ₹16000</p>\n<p>अत: दर =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>4000</mn></mrow><mrow><mn>16000</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>