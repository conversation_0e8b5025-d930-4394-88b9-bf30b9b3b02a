<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If cot&theta; = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>28</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> , then find the value of ( sin&theta; - cos&theta; ).</p>",
                    question_hi: "<p>1. यदि cot&theta; = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>28</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> है, तो ( sin&theta; - cos&theta; ) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>45</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>53</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>28</mn></mrow><mrow><mn>53</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>17</mn></mrow><mrow><mn>53</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>45</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>53</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>28</mn></mrow><mrow><mn>53</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>17</mn></mrow><mrow><mn>53</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>1.(d)<br>According to question, we have given triangle : <br><strong id=\"docs-internal-guid-594dada9-7fff-6c2f-26c9-14e49f034fbf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdAQt4ZasDyN6oTEvIAVhN8Qee-QXoaNz-HzXUzVb8XARnO0keIbHHr3lbBqDupgnKlF7rFWgDk3PuLIP8jh-j84j0Yl8za9iWkVMoviPBLuAdEWNBlvaYesbJ_X5xGOyTUyZfG?key=kJHpzvOCg-rrNAQzLSLO7qvs\" width=\"148\" height=\"137\"></strong><br>AC = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><msup><mn>5</mn><mn>2</mn></msup><mo>+</mo><mn>2</mn><msup><mn>8</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2809</mn><mo>&#160;</mo></msqrt></math> = 53<br>Now, sin<math display=\"inline\"><mi>&#952;</mi></math> - cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>45</mn><mo>&#160;</mo></mrow><mn>53</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>28</mn></mrow><mrow><mn>53</mn><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>53</mn></mfrac></math></p>",
                    solution_hi: "<p>1.(d)<br>प्रश्न के अनुसार, दिया गया त्रिभुज है,<br><strong id=\"docs-internal-guid-28db07d2-7fff-174b-e57f-6aad51c7cbf3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdAQt4ZasDyN6oTEvIAVhN8Qee-QXoaNz-HzXUzVb8XARnO0keIbHHr3lbBqDupgnKlF7rFWgDk3PuLIP8jh-j84j0Yl8za9iWkVMoviPBLuAdEWNBlvaYesbJ_X5xGOyTUyZfG?key=kJHpzvOCg-rrNAQzLSLO7qvs\" width=\"148\" height=\"137\"></strong><br>AC = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><msup><mn>5</mn><mn>2</mn></msup><mo>+</mo><mn>2</mn><msup><mn>8</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2809</mn><mo>&#160;</mo></msqrt></math> = 53<br>अब, sin<math display=\"inline\"><mi>&#952;</mi></math> - cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>45</mn><mo>&#160;</mo></mrow><mn>53</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>28</mn></mrow><mrow><mn>53</mn><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>53</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If sec 6A = cosec (A - 29&deg;), where 2A is an acute angle, then the measure of &ang;A = _____&deg;.</p>",
                    question_hi: "<p>2. यदि sec 6A = cosec (A - 29&deg;) है, जहां 2A न्यून कोण है, तो &ang;A = ______ &deg; होगा।</p>",
                    options_en: ["<p>18</p>", "<p>17</p>", 
                                "<p>21</p>", "<p>19</p>"],
                    options_hi: ["<p>18</p>", "<p>17</p>",
                                "<p>21</p>", "<p>19</p>"],
                    solution_en: "<p>2.(b)<br>sec 6A = cosec (A - 29&deg;)<br>cosec(90&deg; - 6A) = cosec (A - 29&deg;)<br><math display=\"inline\"><mo>&#8658;</mo></math> 90&deg; - 6A = A - 29&deg;<br>7A = 119<br>A = <math display=\"inline\"><mfrac><mrow><mn>119</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 17&deg;</p>",
                    solution_hi: "<p>2.(b)<br>sec 6A = cosec (A - 29&deg;)<br>cosec(90&deg; - 6A) = cosec (A - 29&deg;)<br><math display=\"inline\"><mo>&#8658;</mo></math> 90&deg; - 6A = A - 29&deg;<br>7A = 119<br>A = <math display=\"inline\"><mfrac><mrow><mn>119</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 17&deg;</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Simplify: (1 + tan<sup>2</sup>A) + (1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> )</p>",
                    question_hi: "<p>3. सरल कीजिए : (1 + tan<sup>2</sup>A) + (1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> )</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(a)<br><strong>Formula used :</strong> <math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></math> + cos<sup>2</sup>A = 1<br><math display=\"inline\"><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A - tan<sup>2</sup>A = 1<br><math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A - cot<sup>2</sup>A = 1<br>Now, (1 + tan<sup>2</sup>A) +(1 + co<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A ) = sec<sup>2</sup>A + cosec<sup>2</sup>A<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>Now, solving option (a) we get ;<br><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>(</mo><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>So, the correct option is (a).</p>",
                    solution_hi: "<p>3.(a)<br><strong id=\"docs-internal-guid-f00d23a7-7fff-4107-572b-a64096a85c99\">प्रयुक्त सूत्र :</strong> <math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></math> + cos<sup>2</sup>A = 1<br><math display=\"inline\"><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A - tan<sup>2</sup>A = 1<br><math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A - cot<sup>2</sup>A = 1<br>अब, (1 + tan<sup>2</sup>A) +(1 + co<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A ) = sec<sup>2</sup>A + cosec<sup>2</sup>A<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>अब, विकल्प (a) को हल करने पर;<br><strong id=\"docs-internal-guid-70c7a61c-7fff-9f53-e62c-a938f93bba5d\"></strong><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>(</mo><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>अतः, सही विकल्प (a) है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If tan(t) + cot(t) = 1, then one of the values of the expression <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> is _____ .</p>",
                    question_hi: "<p>4. यदि tan(t) + cot(t) = 1, तो व्यंजक <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> का एक मान _______ है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>4. (a)<br>tan(t) + cot(t) = 1<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> = 1<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mo>(</mo><mi>t</mi><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> = 1<br>1 = <math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></math> &hellip;&hellip;(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo><msup><mo>]</mo><mn>2</mn></msup></math> = sin<sup>2</sup>(t) + cos<sup>2</sup>(t) + 2sin(t)cos(t)<br><math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>sin</mi><mn>2</mn></msup><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>+</mo><mn>2</mn><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1</mn><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><mn>1</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>Then, <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>",
                    solution_hi: "<p>4. (a)<br>tan(t) + cot(t) = 1<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> = 1<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mo>(</mo><mi>t</mi><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> = 1<br>1 = <math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></math> &hellip;&hellip;(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo><msup><mo>]</mo><mn>2</mn></msup></math> = sin<sup>2</sup>(t) + cos<sup>2</sup>(t) + 2sin(t)cos(t)<br><math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>sin</mi><mn>2</mn></msup><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>+</mo><mn>2</mn><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1</mn><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><mn>1</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>तब ,&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If (sin <math display=\"inline\"><mi>x</mi></math> + sin y) = a and (cos x + cos y) = b, then find the value of (sin x sin y + cos x cos y).</p>",
                    question_hi: "<p>5. यदि (sin <math display=\"inline\"><mi>x</mi></math> + sin y) = a और (cos x + cos y) = b है, तो (sin x sin y + cos x cos y) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>5.(c)<br>(sin x + sin y) = a ---------- (i)<br>Squaring both side, <br><math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + sin<sup>2</sup>y + 2sinxsiny = a<sup>2</sup> ---------- (ii)<br>(cos x + cos y) = b ---------- (iii)<br>Squaring both side, <br><math display=\"inline\"><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>y + 2cosxcosy = b<sup>2</sup> --------- (iv)<br>Adding eqn (ii) &amp; (iv) we get ;<br>(<math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>x) + (sin<sup>2</sup>y + cos<sup>2</sup>y) + 2sinxsiny + 2cosxcosy = a<sup>2</sup>+ b<sup>2</sup><br>1 + 1 + 2(sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>2(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>sinxsiny + cosxcosy = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>5.(c)<br>(sin x + sin y) = a ---------- (i)<br>दोनों पक्षों का वर्ग करने पर,<br><math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + sin<sup>2</sup>y + 2sinxsiny = a<sup>2</sup> ---------- (ii)<br>(cos x + cos y) = b ---------- (iii)<br>दोनों पक्षों का वर्ग करने पर,<br><strong id=\"docs-internal-guid-d1406ac6-7fff-c729-9679-cb089dfaaa8f\"></strong><math display=\"inline\"><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>y + 2cosxcosy = b<sup>2</sup> --------- (iv)<br>समीकरण (ii) और (iv) जोड़ने पर हमें प्राप्त होता है;<br><strong id=\"docs-internal-guid-ce772e39-7fff-0961-f9c8-23d338b6ad5f\"></strong>(<math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>x) + (sin<sup>2</sup>y + cos<sup>2</sup>y) + 2sinxsiny + 2cosxcosy = a<sup>2</sup>+ b<sup>2</sup><br>1 + 1 + 2(sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>2(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>sinxsiny + cosxcosy = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If sin <math display=\"inline\"><mi>&#945;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>13</mn><mo>&#160;</mo></mrow></mfrac></math>, then the value of cos &alpha; . cosec &alpha; . cot &alpha; is ______ .</p>",
                    question_hi: "<p>6. यदि sin <math display=\"inline\"><mi>&#945;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>13</mn><mo>&#160;</mo></mrow></mfrac></math> है, तो cos &alpha; . cosec &alpha; . cot &alpha; का मान क्या होगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>144</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>144</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>6.(b)<br>According to question,<br><strong id=\"docs-internal-guid-7cf57238-7fff-bae0-8e5b-70d64357e3a7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeDzzwEJHfmw2WalTKl-XGXMGaUiJcUVw2XxpljT3376CuDt5uPw4Pw75Ig60yCnkb4O-Z0eAbILEo8JscGPbqnMiktd6QrDc9_yOU3G2xLICoTEqgrK04gIlEi1XTGqjeQL0rgvw?key=kJHpzvOCg-rrNAQzLSLO7qvs\" width=\"136\" height=\"120\"></strong><br>cos <math display=\"inline\"><mi>&#945;</mi></math>.cosec &alpha;.cot &alpha; = cot<sup>2 </sup>&alpha; = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>25</mn></mfrac></math></p>",
                    solution_hi: "<p>6.(b)<br>प्रश्न के अनुसार<br><strong id=\"docs-internal-guid-7cf57238-7fff-bae0-8e5b-70d64357e3a7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeDzzwEJHfmw2WalTKl-XGXMGaUiJcUVw2XxpljT3376CuDt5uPw4Pw75Ig60yCnkb4O-Z0eAbILEo8JscGPbqnMiktd6QrDc9_yOU3G2xLICoTEqgrK04gIlEi1XTGqjeQL0rgvw?key=kJHpzvOCg-rrNAQzLSLO7qvs\" width=\"136\" height=\"120\"></strong><br>cos <math display=\"inline\"><mi>&#945;</mi></math>.cosec &alpha;.cot &alpha; = cot<sup>2 </sup>&alpha; = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>25</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Find the value of (1 + cotA - cosecA)(1 + tanA + secA) - 3(sin<sup>2</sup>A + cos<sup>2</sup>A).</p>",
                    question_hi: "<p>7. (1 + cotA - cosecA)(1 + tanA + secA) - 3(sin<sup>2</sup>A + cos<sup>2</sup>A) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>-1</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>-2</p>"],
                    options_hi: ["<p>-1</p>", "<p>1</p>",
                                "<p>2</p>", "<p>-2</p>"],
                    solution_en: "<p>7.(a)<br>(1 + cotA - cosecA)(1 + tanA + secA) - 3(sin<sup>2</sup>A + cos<sup>2</sup>A).<br>(1 + <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac></math>)(1 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>sin</mi><mi>A</mi><mo>&#160;</mo></mrow><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac></math>) - 3 &times; 1<br>(<math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>A</mi><mo>+</mo><mi>sin</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac></math>) - 3<br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> - 3<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> - 3 <br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> - 3 = 2 - 3 = - 1</p>",
                    solution_hi: "<p>7.(a)<br>(1 + cotA - cosecA)(1 + tanA + secA) - 3(sin<sup>2</sup>A + cos<sup>2</sup>A).<br>(1 + <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac></math>)(1 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>sin</mi><mi>A</mi><mo>&#160;</mo></mrow><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac></math>) - 3 &times; 1<br>(<math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>A</mi><mo>+</mo><mi>sin</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac></math>) - 3<br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> - 3<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> - 3 <br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> - 3 = 2 - 3 = - 1</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Find the value of cosec&theta; (1 &minus; cos&theta;) (cosec&theta; + cot&theta;).</p>",
                    question_hi: "<p>8. cosec&theta; (1 &minus; cos&theta;) (cosec&theta; + cot&theta;) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>-1</p>", "<p>2</p>", 
                                "<p>0</p>", "<p>1</p>"],
                    options_hi: ["<p>-1</p>", "<p>2</p>",
                                "<p>0</p>", "<p>1</p>"],
                    solution_en: "<p>8.(d)<br>cosec&theta; (1 &minus; cos&theta;) (cosec&theta; + cot&theta;)<br>(cosec<math display=\"inline\"><mi>&#952;</mi></math> - cot&theta;)(cosec&theta; + cot&theta;)<br><math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></math> - cot<sup>2</sup>&theta; = 1</p>",
                    solution_hi: "<p>8.(d)<br>cosec&theta; (1 &minus; cos&theta;) (cosec&theta; + cot&theta;)<br>(cosec<math display=\"inline\"><mi>&#952;</mi></math> - cot&theta;)(cosec&theta; + cot&theta;)<br><math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></math> - cot<sup>2</sup>&theta; = 1</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Find the maximum value of<br>(19 sin <math display=\"inline\"><mi>&#952;</mi></math> + 6 cot &theta; sin&theta;).</p>",
                    question_hi: "<p>9. (19 sin <math display=\"inline\"><mi>&#952;</mi></math> + 6 cot &theta; sin&theta;). का अधिकतम मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>397</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>197</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\"><msqrt><mn>297</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>497</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>397</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>197</mn></msqrt></math></p>",
                                "<p><math display=\"inline\"><msqrt><mn>297</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>497</mn></msqrt></math></p>"],
                    solution_en: "<p>9.(a) <strong>Given:</strong> <br>(19 sin <math display=\"inline\"><mi>&#952;</mi></math> + 6 cot &theta; sin&theta;) = (19 sin &theta; + 6 cos &theta;)<br>We know that,<br><math display=\"inline\"><mi>y</mi></math> = (A sin &theta; + B cos &theta;) where, A &gt; B<br><math display=\"inline\"><msub><mrow><mi>y</mi></mrow><mrow><mi>m</mi><mi>a</mi><mi>x</mi></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>A</mi><mn>2</mn></msup><mo>+</mo><msup><mi>B</mi><mn>2</mn></msup></msqrt></math><br>So,<br>Maximum value of (19 sin <math display=\"inline\"><mi>&#952;</mi></math> + 6 cos &theta;) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>19</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>361</mn><mo>+</mo><mn>36</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>397</mn></msqrt></math></p>",
                    solution_hi: "<p>9.(a) <strong>दिया गया&nbsp;:</strong> <br>(19 sin <math display=\"inline\"><mi>&#952;</mi></math> + 6 cot &theta; sin&theta;) = (19 sin &theta; + 6 cos &theta;)<br>हम जानते हैं की,<br><math display=\"inline\"><mi>y</mi></math> = (A sin &theta; + B cos &theta;) where, A &gt; B<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>y</mi><mi>&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;</mi></msub><mo>&#160;</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>A</mi><mn>2</mn></msup><mo>+</mo><msup><mi>B</mi><mn>2</mn></msup></msqrt></math><br>इसलिए,<br>&nbsp;(19 sin <math display=\"inline\"><mi>&#952;</mi></math> + 6 cos &theta;) का अधिकतम मान<strong id=\"docs-internal-guid-6e80e4d6-7fff-7f7f-dc61-5de2d666806a\"> </strong>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>19</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>361</mn><mo>+</mo><mn>36</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>397</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If tan<math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn></mrow><mn>24</mn></mfrac></math> then find the value of (cos<sup>2</sup>&theta; - Sin<sup>2</sup>&theta;).</p>",
                    question_hi: "<p>10. यदि tan<math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn></mrow><mn>24</mn></mfrac></math> है, तो (cos<sup>2</sup>&theta; - Sin<sup>2</sup>&theta;) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>576</mn></mrow><mrow><mn>550</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mn>1</mn></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>527</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>520</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>576</mn></mrow><mrow><mn>550</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mn>1</mn></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>527</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>520</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(c)<br>tan<math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn></mrow><mn>24</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>B</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math><br>Hence, Hypotenuse = 25 <br>cos<sup>2</sup><math display=\"inline\"><mi>&#952;</mi></math> - Sin<sup>2</sup>&theta;<br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>B</mi></mrow><mrow><mi>H</mi></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>H</mi></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>25</mn></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><mfrac><mrow><mn>576</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>49</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>527</mn><mn>625</mn></mfrac></math></p>",
                    solution_hi: "<p>10.(c)<br>tan<math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn></mrow><mn>24</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&#160;</mo></mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math><br>अत, कर्ण = 25<br>cos<sup>2</sup><math display=\"inline\"><mi>&#952;</mi></math> - Sin<sup>2</sup>&theta;<br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>B</mi></mrow><mrow><mi>H</mi></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>H</mi></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>25</mn></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><mfrac><mrow><mn>576</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>49</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>527</mn><mn>625</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>10</mn><mo>&#160;</mo></mrow></mfrac></math> then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math></p>",
                    question_hi: "<p>11. यदि tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>10</mn><mo>&#160;</mo></mrow></mfrac></math> है, तो&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(c) <strong>Given:</strong> tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><msup><mo>)</mo><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mo>(</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><msup><mo>)</mo><mn>2</mn></msup></mrow><mrow><mo>(</mo><mn>1</mn><msup><mo>)</mo><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mo>(</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><msup><mo>)</mo><mn>2</mn></msup></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mi>S</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>9</mn></mfrac></math></p>",
                    solution_hi: "<p>11.(c) <strong id=\"docs-internal-guid-5be44e4d-7fff-76e4-f11a-b7274e10255f\">दिया गया है</strong><strong>:</strong> tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><msup><mo>)</mo><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mo>(</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><msup><mo>)</mo><mn>2</mn></msup></mrow><mrow><mo>(</mo><mn>1</mn><msup><mo>)</mo><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mo>(</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><msup><mo>)</mo><mn>2</mn></msup></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mi>S</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>9</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Which of the following is true when <math display=\"inline\"><mi>x</mi></math> = SinA + CosA; y = SecA + CosecA?</p>",
                    question_hi: "<p>12. <math display=\"inline\"><mi>x</mi></math> = SinA + CosA; y = SecA + CosecA होने पर निम्नलिखित में से कौन-सा विकल्प सत्य होगा?</p>",
                    options_en: ["<p>y(1 + <math display=\"inline\"><mi>x</mi></math><sup>2 </sup>) = 2x</p>", "<p>y - 2<math display=\"inline\"><mi>x</mi></math> = x<sup>2 </sup>&nbsp;y</p>", 
                                "<p>y + 2<math display=\"inline\"><mi>x</mi></math> = x<sup>2 </sup>&nbsp;y</p>", "<p>y(1 - 2<math display=\"inline\"><mi>x</mi></math><sup>2 </sup>) = x </p>"],
                    options_hi: ["<p>y(1 + <math display=\"inline\"><mi>x</mi></math><sup>2 </sup>&nbsp;) = 2x</p>", "<p>y - 2<math display=\"inline\"><mi>x</mi></math> = x<sup>2 </sup>&nbsp;y</p>",
                                "<p>y + 2<math display=\"inline\"><mi>x</mi></math> = x<sup>2 </sup>&nbsp;y</p>", "<p>y(1- 2<math display=\"inline\"><mi>x</mi></math><sup>2 </sup>) = x</p>"],
                    solution_en: "<p>12.(c)<br><math display=\"inline\"><mi>x</mi></math> = SinA + CosA &hellip; (i)<br>On squaring both side <br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = Sin<sup>2 </sup>A + Cos<sup>2 </sup>A + 2SinA.CosA<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 1 + 2SinA.CosA<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = SinA.CosA &hellip; (ii)<br>Now,<br><math display=\"inline\"><mi>y</mi></math> = SecA + CosecA <br><math display=\"inline\"><mi>y</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi><mo>+</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi><mo>.</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> <br><math display=\"inline\"><mi>y</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mstyle displaystyle=\"true\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>2</mn></mfrac></mstyle></mfrac></math> from (i) and (ii)<br><math display=\"inline\"><mi>y</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></math>) y = 2x<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mi>y</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi></math>) = 2x<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>y</mi><mi>&#160;</mi></math> = 2x + y<br><br><strong>Short trick : </strong>Put A = 45&deg;</p>",
                    solution_hi: "<p>12.(c)<br><math display=\"inline\"><mi>x</mi></math> = SinA + CosA &hellip; (i)<br>दोनों तरफ वर्ग करने पर<strong id=\"docs-internal-guid-d37a6246-7fff-73f3-57e5-0df2935c45f8\"> </strong><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = Sin<sup>2 </sup>A + Cos<sup>2 </sup>A + 2SinA.CosA<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 1 + 2SinA.CosA<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = SinA.CosA &hellip; (ii)<br>अब,<br><math display=\"inline\"><mi>y</mi></math> = SecA + CosecA <br><math display=\"inline\"><mi>y</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi><mo>+</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi><mo>.</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> <br><math display=\"inline\"><mi>y</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mstyle displaystyle=\"true\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>2</mn></mfrac></mstyle></mfrac></math> (i) और (ii) से<br><strong id=\"docs-internal-guid-ea2a9ea0-7fff-ce41-386e-b1ee77a0233a\"></strong><math display=\"inline\"><mi>y</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></math>) y = 2x<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mi>y</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi></math>) = 2x<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>y</mi><mi>&#160;</mi></math> = 2x + y<br><br><strong>शार्ट ट्रिक :&nbsp;</strong>A = 45&deg; रखने पर</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Solve the equation for A (in degrees):<br>2 cos&sup2;A + 3 cosA - 2 = 0, 0 &lt; A &lt; 90&deg;</p>",
                    question_hi: "<p>13. A के लिए समीकरण हल (डिग्री में) कीजिए:<br>2 cos&sup2;A + 3 cosA - 2 = 0, 0 &lt; A &lt; 90&deg;</p>",
                    options_en: ["<p>30&deg;</p>", "<p>80&deg;</p>", 
                                "<p>60&deg;</p>", "<p>45\"</p>"],
                    options_hi: ["<p>30&deg;</p>", "<p>80&deg;</p>",
                                "<p>60&deg;</p>", "<p>45\"</p>"],
                    solution_en: "<p>13.(c)<br>2 cos&sup2;A + 3 cosA - 2 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 cos&sup2;A + 4 cosA - cosA - 2 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 cosA (cosA + 2) - 1 (cosA + 2) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (cosA + 2) (2 cosA - 1) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (cosA + 2) = 0 and (2 cosA - 1) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> cosA = - 2 and cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>CosA = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rArr; CosA = Cos 60&deg;<br>A = <math display=\"inline\"><msup><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow></mrow></msup></math></p>",
                    solution_hi: "<p>13.(c)<br>2 cos&sup2;A + 3 cosA - 2 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 cos&sup2;A + 4 cosA - cosA - 2 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 cosA (cosA + 2) - 1 (cosA + 2) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (cosA + 2) (2 cosA - 1) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (cosA + 2) = 0 और (2 cosA - 1) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> cosA = - 2 और cosA =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>CosA = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rArr; CosA = Cos 60&deg;<br>A = <math display=\"inline\"><msup><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow></mrow></msup></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Find the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>",
                    question_hi: "<p>14. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>tan<sup>2</sup><math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cot <math display=\"inline\"><mi>&#952;</mi></math></p>", 
                                "<p>tan <math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cot<sup>2</sup><math display=\"inline\"><mi>&#952;</mi></math></p>"],
                    options_hi: ["<p>tan<sup>2</sup><math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cot <math display=\"inline\"><mi>&#952;</mi></math></p>",
                                "<p>tan <math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cot<sup>2</sup><math display=\"inline\"><mi>&#952;</mi></math></p>"],
                    solution_en: "<p>14.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></mrow><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> = cot&sup2;&theta;</p>",
                    solution_hi: "<p>14.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></mrow><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#178;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> = cot&sup2;&theta;</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>79</mn></mfrac></math> then the value of sin A is equal to:</p>",
                    question_hi: "<p>15. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>79</mn></mfrac></math> है, तो sin A का मान कितना होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>61</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>87</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>61</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>87</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>15.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>79</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>209</mn><mn>79</mn></mfrac></math><br>By applying componendo and dividendo<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>209</mn><mo>+</mo><mn>79</mn></mrow><mrow><mn>209</mn><mo>-</mo><mn>79</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>130</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>130</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>65</mn></mfrac></math><br>SinA = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>15.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>79</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>209</mn><mn>79</mn></mfrac></math><br>कंपोनेंडो और डिविडेंडो को लागू करके<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>209</mn><mo>+</mo><mn>79</mn></mrow><mrow><mn>209</mn><mo>-</mo><mn>79</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>130</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>130</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>65</mn></mfrac></math><br>SinA = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>