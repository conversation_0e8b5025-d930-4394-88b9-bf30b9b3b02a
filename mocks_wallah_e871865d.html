<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">9:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 9 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">Variance</span></span></strong></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">Variance</span></strong></span></p>\n",
                    options_en: ["<p>Convention</p>\n", "<p>Equal</p>\n", 
                                "<p>Harmony</p>\n", "<p>Intellectual</p>\n"],
                    options_hi: ["<p>Convention</p>\n", "<p>Equal</p>\n",
                                "<p>Har<span style=\"font-family: Cambria Math;\">mony</span></p>\n", "<p>Intellectual</p>\n"],
                    solution_en: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Variance- the amount by which something changes or is different from something else</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Harmony- </span><span style=\"font-family: Cambria Math;\">a state of agreement or of peaceful existence together</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Convention- </span><span style=\"font-family: Cambria Math;\">a traditional way of behaving or of doing something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Equal- </span><span style=\"font-family: Cambria Math;\">the same in size, amount, value, number, level, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Intellectual- </span><span style=\"font-family: Cambria Math;\">connected with a person&rsquo;s ability to think in a logical way and</span><span style=\"font-family: Cambria Math;\"> to understand things</span></p>\n",
                    solution_hi: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Variance- the amount by which something changes or is different from something else</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Harmony- </span><span style=\"font-family: Cambria Math;\">a state of agreement or of peaceful existence together</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Convention- </span><span style=\"font-family: Cambria Math;\">a traditional way of behaving or of doing something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Equal- </span><span style=\"font-family: Cambria Math;\">th</span><span style=\"font-family: Cambria Math;\">e same in size, amount, value, number, level, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Intellectual- </span><span style=\"font-family: Cambria Math;\">connected with a person&rsquo;s ability to think in a logical way and to understand things</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the highlighted word</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">He is inclined to stay with the present organisation.</span></strong></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the highlighted word</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">He is inclined to stay with the present organisation.</span></strong></p>\n",
                    options_en: ["<p>struggled</p>\n", "<p>mystified</p>\n", 
                                "<p>motivated</p>\n", "<p>declined</p>\n"],
                    options_hi: ["<p>struggled</p>\n", "<p>mystified</p>\n",
                                "<p>motivated</p>\n", "<p>declined</p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Inclined- </span><span style=\"font-family: Cambria Math;\">likely to do something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Motivated- </span><span style=\"font-family: Cambria Math;\">very enthusiastic or determined because you really want to do something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Struggled- </span><span style=\"font-family: Cambria Math;\">to try very hard to do something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mystified- to make somebody confused because he/she cannot understand something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Declined- </span><span style=\"font-family: Cambria Math;\">to become weaker, smaller or less good</span></p>\n",
                    solution_hi: "<p>2.(c<span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Inclined- </span><span style=\"font-family: Cambria Math;\">likely to do something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Motivated- </span><span style=\"font-family: Cambria Math;\">very enthusiastic or determined because you really want to do something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Struggled- </span><span style=\"font-family: Cambria Math;\">to try very hard to do something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mystified- to make somebody confused because he/she cannot understand something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Declined- </span><span style=\"font-family: Cambria Math;\">to become weaker, smaller or less good</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">One should work hard than others to succeed.</span></strong></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">One should work hard than others to succeed.</span></strong></p>\n",
                    options_en: ["<p>harder than</p>\n", "<p>hardest that</p>\n", 
                                "<p>harder to</p>\n", "<p>hard to</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>harder than</p>\n", "<p>hardest that</p>\n",
                                "<p>harder to</p>\n", "<p>hard to</p>\n"],
                    solution_en: "<p>3.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">We always use a com</span><span style=\"font-family: Cambria Math;\">parative degree adjective like better, worse, greater, etc. when we do a comparison between two things. However, we also use &lsquo;than&rsquo; after a comparative degree adjective. Similarly, &lsquo;harder&rsquo; is the comparative degree in the given sentence. Hence, &lsquo;harder th</span><span style=\"font-family: Cambria Math;\">an&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>3.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">We always use a comparative degree adjective like better, worse, greater, etc. when we do a comparison between two things. However, we also use &lsquo;than&rsquo; after a comparative degree adjective. Similarly, &lsquo;harder</span><span style=\"font-family: Cambria Math;\">&rsquo; is the comparative degree in the given sentence. Hence, &lsquo;harder than&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in active voice.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Pygmalion was written by GB Shaw.</span></strong></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in active voice.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Pygmalion was written by GB Shaw.</span></strong></p>\n",
                    options_en: ["<p>GB Shaw was writing Pygmalion.</p>\n", "<p>GB Shaw was written by Pygmalion.</p>\n", 
                                "<p>Pygmalion wrote GB Shaw.</p>\n", "<p>GB Shaw wrote Pygmalion.</p>\n"],
                    options_hi: ["<p>GB Shaw was writing Pygmalion.</p>\n", "<p>GB Shaw was written by Pygmalion.</p>\n",
                                "<p>Pygmalion wrote GB Shaw.</p>\n", "<p>GB Shaw wrote Pygmalion.</p>\n"],
                    solution_en: "<p>4.(d)</p>\r\n<p><span style=\"font-weight: 400;\">(a) GB Shaw </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was writing</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>Pygmalion. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) GB Shaw </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was written</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>by Pygmalion. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><span style=\"text-decoration: underline;\">Pygmalion</span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>wrote </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">GB Shaw</span></span><span style=\"font-weight: 400;\">. (Incorrect Placement of Subject &amp; Object)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) GB Shaw wrote Pygmalion. (Correct)</span></p>\n",
                    solution_hi: "<p>4.(d)</p>\r\n<p><span style=\"font-weight: 400;\">(a) GB Shaw </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was writing</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>Pygmalion. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) GB Shaw </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was written</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>by Pygmalion. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><span style=\"text-decoration: underline;\">Pygmalion</span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>wrote </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">GB Shaw</span></span><span style=\"font-weight: 400;\">. (Incorrect Placement of Subject &amp; Object)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) GB Shaw wrote Pygmalion. (Correct)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">The following sentence has been split into four segments. Identify the segment that contains a grammatical error.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">There is many ways / to make / a stranger feel / welcome in the community.</span></strong></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">The following sentence has been split into four segments. Identify the segment that contains a grammatical error.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">There is many ways / to make / a stranger feel </span><span style=\"font-family: Cambria Math;\">/ welcome in the community.</span></strong></p>\n",
                    options_en: ["<p>welcome in the community</p>\n", "<p>There is many ways</p>\n", 
                                "<p>a stranger feel</p>\n", "<p>to make</p>\n"],
                    options_hi: ["<p>welcome in the community</p>\n", "<p>There is many ways</p>\n",
                                "<p>a stranger feel</p>\n", "<p>to make</p>\n"],
                    solution_en: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the &ldquo;</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">Subject-Verb Agreement Rule</span></span><span style=\"font-family: Cambria Math;\">&rdquo;, a singular subject always takes a singular verb and a plural subject alw</span><span style=\"font-family: Cambria Math;\">ays takes a plural verb. In the given sentence, &lsquo;ways&rsquo; is a plural subject that will take &lsquo;are&rsquo; as a plural verb. Hence, &lsquo;There are many ways&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the &ldquo;</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">Subject-Verb Agreement Rule</span></span><span style=\"font-family: Cambria Math;\">&rdquo;, a singular subject a</span><span style=\"font-family: Cambria Math;\">lways takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;ways&rsquo; is a plural subject that will take &lsquo;are&rsquo; as a plural verb. Hence, &lsquo;There are many ways&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Select the INCORRECTLY</span><span style=\"font-family: Cambria Math;\"> spelt word.</span></p>\n",
                    question_hi: " <p>6.</span><span style=\"font-family:Cambria Math\"> Select the INCORRECTLY spelt word.</span></p>",
                    options_en: ["<p>Acquaint</p>\n", "<p>Mamorial</p>\n", 
                                "<p>Guarantee</p>\n", "<p>Monotonous</p>\n"],
                    options_hi: [" <p> Acquaint</span></p>", " <p> Mamorial</span></p>",
                                " <p> Guarantee</span></p>", " <p> Monotonous</span></p>"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Memorial is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Acquaint - make someone aware of or familiar with</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Monotonous - dull, boring</span></p>\n",
                    solution_hi: " <p>6.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Memorial is the correct spelling.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Select the option that will improve the underlined part of the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Robin has</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">less marbles</span></strong><span style=\"font-family: Cambria Math;\"> than George.</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">Select the option </span><span style=\"font-family: Cambria Math;\">that will improve the underlined part of the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Robin has</span><strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">less marbles</span></strong><span style=\"font-family: Cambria Math;\"> than George.</span></p>\n",
                    options_en: ["<p>enough marbles</p>\n", "<p>lower marbles</p>\n", 
                                "<p>few marbles</p>\n", "<p>fewer marbles</p>\n"],
                    options_hi: ["<p>enough marbles</p>\n", "<p>lower marbles</p>\n",
                                "<p>few marbles</p>\n", "<p>fewer marbles</p>\n"],
                    solution_en: "<p>7.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">We use &lsquo;few/fewer&rsquo; before plural countable nouns &amp; &lsquo;less&rsquo; is used before uncountable nouns. Similarly, &lsquo;marbles&rsquo; in the given sentence are countable. Hence, &lsquo;fewer marbles&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>7.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">We use &lsquo;few/fewer&rsquo; before plural count</span><span style=\"font-family: Cambria Math;\">able nouns &amp; &lsquo;less&rsquo; is used before uncountable nouns. Similarly, &lsquo;marbles&rsquo; in the given sentence are countable. Hence, &lsquo;fewer marbles&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> In the following passage, some words have been deleted. Read the passage carefully </span><span style=\"font-family: Cambria Math;\">and select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The minute you start _______ and tolerating __________, it becomes the new norm and the slide begins and therefore, we must constantly __________ to give our best and be recognised, not simply</span><span style=\"font-family: Cambria Math;\"> aim to do just enough to ___________ that we don&rsquo;t get found out.</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> In the following passage, some words have been deleted. Read the passage carefully </span><span style=\"font-family: Cambria Math;\">and select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The minute you start _______ and tolerating __________, it becomes the new norm and the slide begins and therefore, we must constantly __________ to give our best and be recognised, not simply aim to do just enough to ___________ that we don&rsquo;t get found ou</span><span style=\"font-family: Cambria Math;\">t.</span></p>\n",
                    options_en: ["<p>compelling, excellence, strike, encourage</p>\n", "<p>compromising, mediocrity, strive, ensure</p>\n", 
                                "<p>conjecturing, transcendence, strum, envision</p>\n", "<p>contradicting, distinction, stink, endeav<span style=\"font-family: Cambria Math;\">our</span></p>\n"],
                    options_hi: ["<p>compelling, excellence, strike, encourage</p>\n", "<p>compromising, mediocrity, strive, ensure</p>\n",
                                "<p>conjecturing, transcendence, strum, envision</p>\n", "<p>contradicting, distinction, stink, endeavour</p>\n"],
                    solution_en: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">It can be inferred from the given sentence that only option b has all the correct words that will fit in the blanks.</span></p>\n",
                    solution_hi: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">It can be inferred from the given sentence that only option b has all the correct words that will fit in the blanks.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Se</span><span style=\"font-family: Cambria Math;\">lect the correct homonym from the given options to fill in the blank.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Which________does the minister take to reach the Assembly?</span></strong></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">Select the correct homonym from the given options to fill in the blank.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Which___</span><span style=\"font-family: Cambria Math;\">_____does the minister take to reach the Assembly?</span></strong></p>\n",
                    options_en: ["<p>riot</p>\n", "<p>rot</p>\n", 
                                "<p>root</p>\n", "<p>route</p>\n"],
                    options_hi: ["<p>riot</p>\n", "<p>rot</p>\n",
                                "<p>root</p>\n", "<p>route</p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Route&rsquo; means </span><span style=\"font-family: Cambria Math;\">a way from one place to another</span><span style=\"font-family: Cambria Math;\">. The given sentence talks about the route the minister takes to reach the assembly. Hence, &lsquo;route&rsquo; </span><span style=\"font-family: Cambria Math;\">is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Route&rsquo; means </span><span style=\"font-family: Cambria Math;\">a way from one place to another</span><span style=\"font-family: Cambria Math;\">. The given sentence talks about the route the minister takes to reach the assembly. Hence, &lsquo;route&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p>10.</span><span style=\"font-family:Cambria Math\"> Select the sentence which h</span><span style=\"font-family:Cambria Math\">as no spelling error.</span></p>",
                    question_hi: " <p>10.</span><span style=\"font-family:Cambria Math\"> Select the sentence which has no spelling error.</span></p>",
                    options_en: [" <p> Gupta empire posed a competetoin to the invaders in ancient India.</span></p>", " <p> Neeta represented Global School in the state level debate competition.</span></p>", 
                                " <p> There is a high market compettion due to the advent of online shopping.</span></p>", " <p> The cooking compitition was judged by the head chef of Garland Hotels.</span></p>"],
                    options_hi: [" <p> Gupta empire posed a competetoin to the invaders in ancient India.</span></p>", " <p> Neeta represented Global School in the state l</span><span style=\"font-family:Cambria Math\">evel debate competition.</span></p>",
                                " <p> There is a high market compettion due to the advent of online shopping.</span></p>", " <p> The cooking compitition was judged by the head chef of Garland Hotels.</span></p>"],
                    solution_en: " <p>10.(b)</span></p> <p><span style=\"font-family:Cambria Math\">‘Competition’ is the correct spelling which is given only</span><span style=\"font-family:Cambria Math\"> in option b.</span></p>",
                    solution_hi: " <p>10.(b)</span></p> <p><span style=\"font-family:Cambria Math\">‘Competition’ is the correct spelling which is given only in option b.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of words.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">One who is well-versed in the knowledge of plants</span></strong></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of words.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">One who is well-versed in the knowledge of plants</span></strong></p>\n",
                    options_en: ["<p>Florist</p>\n", "<p>Gardener</p>\n", 
                                "<p>Vegetarian</p>\n", "<p>Botanist</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Florist</p>\n", "<p>Gardener</p>\n",
                                "<p>Vegetarian</p>\n", "<p>Botanist</p>\n"],
                    solution_en: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Botanist- one who is well-versed in the knowledge of plants</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Florist- </span><span style=\"font-family: Cambria Math;\">a person who has or works in a shop where flowers are sold</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gardener- a person who works in a garden as a job or for pleasure</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vegetarian- a person who do</span><span style=\"font-family: Cambria Math;\">es not eat meat or fish</span></p>\n",
                    solution_hi: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Botanist- one who is well-versed in the knowledge of plants</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Florist- </span><span style=\"font-family: Cambria Math;\">a person who has or works in a shop where flowers are sold</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gardener- a person who works in a garden as a job or for pleasure</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vegetarian- a person who</span><span style=\"font-family: Cambria Math;\"> does not eat meat or fish</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12. </span><span style=\"font-family:Cambria Math\">Select the INCORRECTLY spelt word.</span></p>",
                    question_hi: " <p>12. </span><span style=\"font-family:Cambria Math\">Select the INCORRECTLY spelt word.</span></p>",
                    options_en: [" <p> Opportunity</span></p>", " <p> Warrant</span></p>", 
                                " <p> Conversation</span></p>", " <p> Collaegue</span></p>"],
                    options_hi: [" <p> Opportunity</span></p>", " <p> Warrant</span></p>",
                                " <p> Conversation</span></p>", " <p> Collaegue</span></p>"],
                    solution_en: " <p>12.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Colleague is the correct spelling.</span></p>",
                    solution_hi: " <p>12.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Colleague is the correct spelling.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Win laurels</span></strong></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Win laurels</span></strong></p>\n",
                    options_en: ["<p>To gain honour</p>\n", "<p>To be decisive</p>\n", 
                                "<p>To change</p>\n", "<p>To punish</p>\n"],
                    options_hi: ["<p>To gain honour</p>\n", "<p>To be decisive</p>\n",
                                "<p>To change</p>\n", "<p>To punish</p>\n"],
                    solution_en: "<p>13.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Win laurels-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">to gain honour.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g.- Kunal is a hard-working boy so he will surely win laurels in life.</span></p>\n",
                    solution_hi: "<p>13.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Win laurels-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">to gain honour.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g.- Kunal is a hard-working boy so he will surely win laurels in life.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the highlighted idio</span><span style=\"font-family: Cambria Math;\">m.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">The youth involved in the accident escaped by the skin of his teeth.</span></strong></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the highlighted idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">The youth involved in the accident escaped by the skin of his teeth.</span></strong></p>\n",
                    options_en: ["<p>Barely managed to escape</p>\n", "<p>Very arrogant</p>\n", 
                                "<p>Always taking calculated decisions</p>\n", "<p>Deficiency of funds</p>\n"],
                    options_hi: ["<p>Barely managed to escape</p>\n", "<p>Very arrogant</p>\n",
                                "<p>Always taking calculated decisions</p>\n", "<p>Deficiency of funds</p>\n"],
                    solution_en: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Skin of his teeth- barely managed to escape</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g.- A</span><span style=\"font-family: Cambria Math;\">nand passed the final examination by the skin of his teeth.</span></p>\n",
                    solution_hi: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Skin of his teeth- barely managed to escape</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g.- Anand passed the final examination by the skin of his teeth.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">The teacher said, &ldquo;the sun raises in the east every day&rdquo;.</span></strong></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">The teacher said, &ldquo;the sun raises in the east every day&rdquo;.</span></strong></p>\n",
                    options_en: ["<p>the sun will rise i<span style=\"font-family: Cambria Math;\">n the east</span></p>\n", "<p>the sun rises in the east</p>\n", 
                                "<p>the sun rises at the east</p>\n", "<p>No substitution required</p>\n"],
                    options_hi: ["<p>the sun will rise i<span style=\"font-family: Cambria Math;\">n the east</span></p>\n", "<p>the sun rises in the east</p>\n",
                                "<p>the sun rises at the east</p>\n", "<p>No substitution required</p>\n"],
                    solution_en: "<p>15.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The word &lsquo;raise&rsquo; means to elevate something to a higher level/position. However, the word &lsquo;rise&rsquo; means to get up or go to a higher </span><span style=\"font-family: Cambria Math;\">position from a lower position. Similarly, the given sentence states that the sun goes to a higher position from a lower position </span><span style=\"font-family: Cambria Math;\">in the east every day. Hence, &lsquo;the sun rises in the east&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>15.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The word &lsquo;raise&rsquo; means</span><span style=\"font-family: Cambria Math;\"> to elevate something to a higher level/position. However, the word &lsquo;rise&rsquo; means to get up or go to a higher position from a lower position. Similarly, the given sentence states that the sun goes to a higher position from a lower position </span><span style=\"font-family: Cambria Math;\">in the east ever</span><span style=\"font-family: Cambria Math;\">y day. Hence, &lsquo;the sun rises in the east&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word.</span></p>\n",
                    question_hi: " <p>16. </span><span style=\"font-family:Cambria Math\">Select the INCORRECTLY spelt word.</span></p>",
                    options_en: ["<p>Equine</p>\n", "<p>Equilateral</p>\n", 
                                "<p>Equillibrium</p>\n", "<p>Equanimity</p>\n"],
                    options_hi: [" <p> Equine</span></p>", " <p> Equilateral</span></p>",
                                " <p> Equillibrium</span></p>", " <p> Equanimity</span></p>"],
                    solution_en: "<p>16.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Equilibrium is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Equine - relating to or affecting horses or other members of the horse family</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Equanimity - calmness and composure</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: " <p>16.(c)</span></p> <p><span style=\"font-family:Cambria Math\">Equilibrium is the correct spelling.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17.</span><span style=\"font-family:Cambria Math\"> Select the INCORRECTLY spelt word.</span></p>",
                    question_hi: " <p>17.</span><span style=\"font-family:Cambria Math\"> Select the INCORRECTLY spelt word.</span></p>",
                    options_en: [" <p> Encouregement</span></p>", " <p> Achievement</span></p>", 
                                " <p> Advertisement</span></p>", " <p> Environmen</span><span style=\"font-family:Cambria Math\">t</span></p>"],
                    options_hi: [" <p> Encouregement</span></p>", " <p> Achievement</span></p>",
                                " <p> Advertisement</span></p>", " <p> Environment</span></p>"],
                    solution_en: " <p>17.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Encouragement is the correct spelling.</span></p>",
                    solution_hi: " <p>17.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Encouragement is the correct spelling.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Ramu went with his friends on a trip to ___________ new paths.</span></strong></p>\n",
                    question_hi: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Ramu went with his </span><span style=\"font-family: Cambria Math;\">friends on a trip to ___________ new paths.</span></strong></p>\n",
                    options_en: ["<p>determine</p>\n", "<p>deploy</p>\n", 
                                "<p>invent</p>\n", "<p>explore</p>\n"],
                    options_hi: ["<p>determine</p>\n", "<p>deploy</p>\n",
                                "<p>invent</p>\n", "<p>explore</p>\n"],
                    solution_en: "<p>18.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Explore&rsquo; means </span><span style=\"font-family: Cambria Math;\">to travel around a place, etc. in order to learn about it</span><span style=\"font-family: Cambria Math;\">. The given sentence states that Ramu went with his friends on a t</span><span style=\"font-family: Cambria Math;\">rip to explore new paths. Hence, &lsquo;explore&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>18.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Explore&rsquo; means </span><span style=\"font-family: Cambria Math;\">to travel around a place, etc. in order to learn about it</span><span style=\"font-family: Cambria Math;\">. The given sentence states that Ramu went with his friends on a trip to explore new paths. Hence, &lsquo;explore&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option to substitute the underline segment in the given sentence.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">I was carried</span><span style=\"font-family: Cambria Math;\"> by her rapturous song.</span></strong></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option to substitute the underline segment in the given sentence.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">I was carried by her rapturous song.</span></strong></p>\n",
                    options_en: ["<p>carried off</p>\n", "<p>carried away</p>\n", 
                                "<p>carried of</p>\n", "<p>carried to</p>\n"],
                    options_hi: ["<p>carried off</p>\n", "<p>carr<span style=\"font-family: Cambria Math;\">ied away</span></p>\n",
                                "<p>carried of</p>\n", "<p>carried to</p>\n"],
                    solution_en: "<p>19.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Carried away&rsquo; means </span><span style=\"font-family: Cambria Math;\">to be so excited that one is no longer in control of one\'s behavior. The given sentence states that </span><span style=\"font-family: Cambria Math;\">the narrator was got excited by her rapturous song</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">carried away</span><span style=\"font-family: Cambria Math;\">&rsquo;</span><span style=\"font-family: Cambria Math;\"> is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>19.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Carried away&rsquo; means </span><span style=\"font-family: Cambria Math;\">to be so excited that one is no longer in control of one\'s behavior. The given sentence states that </span><span style=\"font-family: Cambria Math;\">the narrator was got excited by her rapturous song</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">carried away</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most ap</span><span style=\"font-family: Cambria Math;\">propriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20.</span><span style=\"font-family:Cambria Math\"> Identify the option that arranges the given parts in the correct order to form a meaningful and coherent paragraph.</span></p> <p><span style=\"font-family:Cambria Math\">a) Non-verbal communication is a means</span></p> <p><span style=\"font-family:Cambria Math\">b) This is possible by using visual clues</span></p> <p><span style=\"font-family:Cambria Math\">c) By which you are able to communicate without speaking</span></p> <p><span style=\"font-family:Cambria Math\">d) Such as facial expressions, eye-contact, postures, gestures etc.</span></p>",
                    question_hi: " <p>20.</span><span style=\"font-family:Cambria Math\"> Identify the option that arranges the given parts in the correct order to form a meaningful and coherent paragraph.</span></p> <p><span style=\"font-family:Cambria Math\">a) Non-verbal communication is a means</span></p> <p><span style=\"font-family:Cambria Math\">b) This is possible by using visual clues</span></p> <p><span style=\"font-family:Cambria Math\">c) By which you are able to communicate without speaking</span></p> <p><span style=\"font-family:Cambria Math\">d</span><span style=\"font-family:Cambria Math\">) Such as facial expressions, eye-contact, postures, gestures etc.</span></p>",
                    options_en: [" <p> a,c,b,d</span></p>", " <p> b,d,c,a</span></p>", 
                                " <p> d,c,a,b</span></p>", " <p> a,b,c,d</span></p>"],
                    options_hi: [" <p> a,c,b,d</span></p>", " <p> b,d,c,a</span></p>",
                                " <p> d,c,a,b</span></p>", " <p> a,b,c,d</span></p>"],
                    solution_en: " <p>20.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Sentence A will be the starting line as it contains the main idea of the parajumble i.e. Non-verbal communication. H</span><span style=\"font-family:Cambria Math\">owever, Sentence C states that it is a means by which you are able to communicate without speaking. So, C will follow A. Further, Sentence B states that this is possible by using visual clues and sentence D states that such as facial expressions, eye- cont</span><span style=\"font-family:Cambria Math\">act, postures, gestures etc. So, D will follow B. Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: " <p>20.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Sentence A will be the starting line as it contains the main idea of the parajumble i.e. Non-verbal communication. However, Senten</span><span style=\"font-family:Cambria Math\">ce C states that it is a means by which you are able to communicate without speaking. So, C will follow A. Further, Sentence B states that this is possible by using visual clues and sentence D states that such as facial expressions, eye- contact, postures,</span><span style=\"font-family:Cambria Math\"> gestures etc. So, D will follow B. Going through the options, option a has the correct sequence.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong>Comprehension:</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropria</span><span style=\"font-family: Cambria Math;\">te option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seeds to maximise the amount of greenery and focus on promoting (22)______. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this planet,</span><span style=\"font-family: Cambria Math;\"> but it also contributes to helping create an environmentally friendly place to live, as trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank No.21.</span></p>\n",
                    question_hi: "<p>21.<strong>Comprehen<span style=\"font-family: Cambria Math;\">sion:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seeds to maximise the am</span><span style=\"font-family: Cambria Math;\">ount of greenery and focus on promoting (22)______. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this planet, but it also contributes to helping create an environmentally friendly place to live, a</span><span style=\"font-family: Cambria Math;\">s trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank No.21.</span></p>\n",
                    options_en: ["<p>Aspect</p>\n", "<p>Process</p>\n", 
                                "<p>Tool</p>\n", "<p>Mechanism</p>\n"],
                    options_hi: ["<p>Aspect</p>\n", "<p>Process</p>\n",
                                "<p>Tool</p>\n", "<p>Mechanism</p>\n"],
                    solution_en: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Process&rsquo; means </span><span style=\"font-family: Cambria Math;\">a series of actions that you do for a particular purpose. </span><span style=\"font-family: Cambria Math;\">The give</span><span style=\"font-family: Cambria Math;\">n passage states that tree plantation is the process of transplanting saplings or seeds to maximise the amount of greenery. Hence, &lsquo;process&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Process&rsquo; means </span><span style=\"font-family: Cambria Math;\">a series of actions that you do for a particular purpose. </span><span style=\"font-family: Cambria Math;\">The given passage states that tree plantation is the process of transplanting saplings or seeds to maximise the amount of greenery. Hence, &lsquo;process&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Comprehension:</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seeds to</span><span style=\"font-family: Cambria Math;\"> maximise the amount of greenery and focus on promoting (22)______. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this planet, but it also contributes to helping create an environmentally friendly </span><span style=\"font-family: Cambria Math;\">place to live, as trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank No.22.</span></p>\n",
                    question_hi: "<p>22. <strong>Comprehension:</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words ha</span><span style=\"font-family: Cambria Math;\">ve been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seeds to maximise the amount of greenery and focus on promoting (22)_</span><span style=\"font-family: Cambria Math;\">_____. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this planet, but it also contributes to helping create an environmentally friendly place to live, as trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Se</span><span style=\"font-family: Cambria Math;\">lect the most appropriate option to fill in blank No.22.</span></p>\n",
                    options_en: ["<p>vegetation</p>\n", "<p>afforestation</p>\n", 
                                "<p>land reservation</p>\n", "<p>deforestation</p>\n"],
                    options_hi: ["<p>vegetation</p>\n", "<p>afforestation</p>\n",
                                "<p>land reservation</p>\n", "<p>deforestation</p>\n"],
                    solution_en: "<p>22.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Afforestation&rsquo; means planting trees on an area of land in order to form a forest. The given passage s</span><span style=\"font-family: Cambria Math;\">tates that tree plantation is the process of transplanting saplings or seeds to maximise the amount of greenery and focus on promoting afforestation. Hence, &lsquo;afforestation&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>22.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Afforestation&rsquo; means planting trees on an area of land in order to form a forest. The given passage states that tree plantation is the process of transplanting saplings or seeds to maximise the amount of greenery and focus on promoting afforestation. Henc</span><span style=\"font-family: Cambria Math;\">e, &lsquo;afforestation&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.<strong>Comprehension:</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is t</span><span style=\"font-family: Cambria Math;\">he (21)______ of transplanting saplings or seeds to maximise the amount of greenery and focus on promoting (22)______. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this planet, but it also contrib</span><span style=\"font-family: Cambria Math;\">utes to helping create an environmentally friendly place to live, as trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank No.23.</span></p>\n",
                    question_hi: "<p>23.<strong>Comprehension:</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following </span><span style=\"font-family: Cambria Math;\">passage, some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seeds to maximise the amount of greenery and fo</span><span style=\"font-family: Cambria Math;\">cus on promoting (22)______. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this planet, but it also contributes to helping create an environmentally friendly place to live, as trees help reduce pol</span><span style=\"font-family: Cambria Math;\">lutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank No.23.</span></p>\n",
                    options_en: ["<p>plan</p>\n", "<p>Labour</p>\n", 
                                "<p>Order</p>\n", "<p>Endeavour</p>\n"],
                    options_hi: ["<p>plan</p>\n", "<p>Labour</p>\n",
                                "<p>Order</p>\n", "<p>Endeavour</p>\n"],
                    solution_en: "<p>23.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Endeavour&rsquo; means </span><span style=\"font-family: Cambria Math;\">to try hard. </span><span style=\"font-family: Cambria Math;\">The given passage states that panting trees can be a very rewarding and fruitful endeavour. Hence, &lsquo;endeavour&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>23.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Endeavour&rsquo; means </span><span style=\"font-family: Cambria Math;\">to try hard. </span><span style=\"font-family: Cambria Math;\">The given passage states that panting trees can be a very rewarding </span><span style=\"font-family: Cambria Math;\">and fruitful endeavour. Hence, &lsquo;endeavour&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Comprehension:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each bla</span><span style=\"font-family: Cambria Math;\">nk.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seeds to maximise the amount of greenery and focus on promoting (22)______. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this pla</span><span style=\"font-family: Cambria Math;\">net, but it also contributes to helping create an environmentally friendly place to live, as trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank No.24.</span></p>\n",
                    question_hi: "<p>24.<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Comprehension:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seed</span><span style=\"font-family: Cambria Math;\">s to maximise the amount of greenery and focus on promoting (22)______. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this planet, but it also contributes to helping create an environmentally frien</span><span style=\"font-family: Cambria Math;\">dly place to live, as trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank No.24.</span></p>\n",
                    options_en: ["<p>ecologically</p>\n", "<p>physically</p>\n", 
                                "<p>Socially</p>\n", "<p>economical<span style=\"font-family: Cambria Math;\">ly</span></p>\n"],
                    options_hi: ["<p>ecologically</p>\n", "<p>physically</p>\n",
                                "<p>Socially</p>\n", "<p>economically</p>\n"],
                    solution_en: "<p>24.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Ecologically&rsquo; means </span><span style=\"font-family: Cambria Math;\">in a way that relates to ecology or the environment.</span><span style=\"font-family: Cambria Math;\"> The given passage states that it is ecologically beneficial for this planet. Hence, &lsquo;ecologically&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>24.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Ecologically&rsquo; means </span><span style=\"font-family: Cambria Math;\">in a way that relates to ecology or t</span><span style=\"font-family: Cambria Math;\">he environment.</span><span style=\"font-family: Cambria Math;\"> The given passage states that it is ecologically beneficial for this planet. Hence, &lsquo;ecologically&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Comprehension:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the he</span><span style=\"font-family: Cambria Math;\">lp of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seeds to maximise the amount of greenery and focus on promoting (22)______. Planting trees can be a very rewarding a</span><span style=\"font-family: Cambria Math;\">nd fruitful (23)______! Not only is it (24)______ beneficial for this planet, but it also contributes to helping create an environmentally friendly place to live, as trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in bla</span><span style=\"font-family: Cambria Math;\">nk No.25.</span></p>\n",
                    question_hi: "<p>25.<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Comprehension:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Cambria Math;\">options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tree plantation is the (21)______ of transplanting saplings or seeds to maximise the amount of greenery and focus on promoting (22)______. Planting trees can be a very rewarding and fruitful (23)______! Not only is it (24)______ beneficial for this planet</span><span style=\"font-family: Cambria Math;\">, but it also contributes to helping create an environmentally friendly place to live, as trees help reduce pollutant (25)______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank No.25.</span></p>\n",
                    options_en: ["<p>problem</p>\n", "<p>concentration</p>\n", 
                                "<p>method</p>\n", "<p>calculation</p>\n"],
                    options_hi: ["<p>problem</p>\n", "<p>concentration</p>\n",
                                "<p>method</p>\n", "<p>calculation</p>\n"],
                    solution_en: "<p>25.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Concentration&rsquo; means </span><span style=\"font-family: Cambria Math;\">a large amount of people or things in one place</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> The given passage states that it also contributes to helping create an environmentally friendly place to live, as trees help reduce pollutant concentration. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">concentration&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>25.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Concentration&rsquo; means </span><span style=\"font-family: Cambria Math;\">a large amount of people or things in one place</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> The given passage states that it also contributes to helping create an environmentally friendly place to live, as trees help</span><span style=\"font-family: Cambria Math;\"> reduce pollutant concentration. Hence, &lsquo;concentration&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>