<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. 57% of Ranita\'s weekly income is equal to 76% of Bhaskar\'s weekly income. If Ranita\'s weekly income was reduced by ₹400, while Bhaskar\'s weekly income did not change, the ratio of the weekly incomes of Ranita and Bhaskar, respectively, would have been 6 : 5. What is Bhaskar\'s weekly income (in ₹)?</p>",
                    question_hi: "<p>1. रनिता की साप्ताहिक आय का 57% भास्कर की साप्ताहिक आय के 76% के बराबर है। यदि रनिता की साप्ताहिक आय ₹400 कम कर दी जाती, जबकि भास्कर की साप्ताहिक आय अपरिवर्तित रहती, तो रनिता और भास्कर की साप्ताहिक आय का अनुपात क्रमशः 6 : 5 है। भास्कर की साप्ताहिक आय (₹ में) क्या है?</p>",
                    options_en: ["<p>2800</p>", "<p>4000</p>", 
                                "<p>3000</p>", "<p>3200</p>"],
                    options_hi: ["<p>2800</p>", "<p>4000</p>",
                                "<p>3000</p>", "<p>3200</p>"],
                    solution_en: "<p>1.(c)<br>Let the weekly income of Ranita and Bhaskar be R and B respectively<br>According to question,<br>57% of R = 76% of B<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">R</mi><mi mathvariant=\"normal\">B</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>76</mn><mn>57</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><br>Let R = 4x&nbsp;and B = 3x<br>Now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>400</mn></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math><br>&rArr; 20x&nbsp;- 2000 = 18x<br>&rArr; 2x&nbsp;= 2000<br>&rArr; x = 1000<br>So, weekly Income of Bhaskar = 3x = 3 &times; 1000 = ₹3000</p>",
                    solution_hi: "<p>1.(c)<br>माना रनिता और भास्कर की साप्ताहिक आय क्रमशः R और B है<br>प्रश्न के अनुसार, <br>R का 57% = B का 76%<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">R</mi><mi mathvariant=\"normal\">B</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>76</mn><mn>57</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><br>माना R = 4<math display=\"inline\"><mi>x</mi></math> और B = 3x<br>अब , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>400</mn></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math><br>&rArr; 20x&nbsp;- 2000 = 18x<br>&rArr; 2x&nbsp;= 2000<br>&rArr; x = 1000<br>अतः, भास्कर की साप्ताहिक आय = 3x = 3 &times; 1000 = ₹3000</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. In an election between two candidates, one got 60% of the total valid votes. 25% of the votes were invalid. If the total number of votes was 15,000, then find the number of valid votes that the other candidate got.</p>",
                    question_hi: "<p>2. दो उम्मीदवारों के बीच एक चुनाव में, एक को कुल वैध मतों का 60% प्राप्त हुआ। 25% मत अवैध थे। यदि कुल मतों की संख्या 15,000 थी, तो दूसरे उम्मीदवार को मिले वैध मतों की संख्या ज्ञात करें।</p>",
                    options_en: ["<p>5,550</p>", "<p>4,500</p>", 
                                "<p>6,750</p>", "<p>5,000</p>"],
                    options_hi: ["<p>5,550</p>", "<p>4,500</p>",
                                "<p>6,750</p>", "<p>5,000</p>"],
                    solution_en: "<p>2.(b)<br>No. of valid votes received by other candidate = 15000 &times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>25</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>60</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math><br>= 15000 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> = 4500</p>",
                    solution_hi: "<p>2.(b)<br>अन्य उम्मीदवार को प्राप्त वैध मतों की संख्या = 15000 &times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>25</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>60</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math><br>= 15000 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> = 4500</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. In an election, 12% of the voters in the voter list did not cast their votes, whereas 84 voters cast their ballot papers blank. There were only two candidates: Ramagya and Shravan. The winner, Ramagya, was supported by 54% of all the voters in the list. He got 1456 more votes than his rival, Shravan. Find the total number of voters in the list.</p>",
                    question_hi: "<p>3. एक चुनाव में, मतदाता सूची में शामिल 12% मतदाताओं ने अपना वोट नहीं डाला, जबकि 84 मतदाताओं ने अपने मतपत्र खाली डाले। केवल दो उम्मीदवार, रामाज्ञा और श्रवण थे। विजेता रामाज्ञा को सूची के सभी मतदाताओं में से 54% का समर्थन प्राप्त हुआ। उसे अपने प्रतिद्वंदी श्रवण से 1456 वोट अधिक मिले। सूची में मतदाताओं की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>5690</p>", "<p>6670</p>", 
                                "<p>7860</p>", "<p>6860</p>"],
                    options_hi: ["<p>5690</p>", "<p>6670</p>",
                                "<p>7860</p>", "<p>6860</p>"],
                    solution_en: "<p>3.(d)<br>Let the total no. of voters in the list be 100%<br>Then the no. of valid votes = 88% - 84<br>No. of votes received by winner = 54%<br>No. of votes received by loser = (88% - 84) - 54% = 34% - 84<br>According to question,<br>54% - (34% - 84) = 1456<br>20% + 84 = 1456<br>20% = 1372<br>Then, 100% = 1372 &times; 5 = 6860</p>",
                    solution_hi: "<p>3.(d)<br>बता दें कि सूची में मतदाताओं की कुल संख्या 100% है ।<br>तब वैध मतों की संख्या = 88% - 84<br>विजेता को प्राप्त मतों की संख्या = 54%<br>हारने वाले को प्राप्त वोटों की संख्या = (88% - 84) - 54% = 34% - 84<br>प्रश्न के अनुसार,<br>54% - (34% - 84) = 1456<br>20% + 84 = 1456<br>20% = 1372<br>तब, 100% = 1372 &times; 5 = 6860</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. On account of corona virus, 5% of the population of a village died. Out of the remaining population, 20% fled due to panic. If the present population is 4655, then what was the population of the village before the corona attack?</p>",
                    question_hi: "<p>4. कोरोना वायरस के कारण एक गाँव की 5% जनसंख्या की मृत्यु हो गई। शेष जनसंख्या में से 20% भय के कारण भाग गई। यदि वर्तमान जनसंख्या 4655 है, तो कोरोना आक्रमण से पहले गाँव की जनसंख्या कितनी थी?</p>",
                    options_en: ["<p>5995</p>", "<p>6125</p>", 
                                "<p>6000</p>", "<p>5985</p>"],
                    options_hi: ["<p>5995</p>", "<p>6125</p>",
                                "<p>6000</p>", "<p>5985</p>"],
                    solution_en: "<p>4.(b)<br>Let the population of a village initially be &lsquo;x&rsquo;<br>According to question,<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 4655<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>76</mn><mn>100</mn></mfrac></math> = 4655<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4655</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>76</mn></mfrac></math> = 6125</p>",
                    solution_hi: "<p>4.(b)<br>माना कि प्रारंभ में एक गाँव की जनसंख्या &lsquo;x&rsquo; थी<br>प्रश्न के अनुसार,<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 4655<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>76</mn><mn>100</mn></mfrac></math> = 4655<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4655</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>76</mn></mfrac></math> = 6125</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. In a class, the numbers of boys and girls are in the ratio of 3 : 5. What is the percentage of boys in the class?</p>",
                    question_hi: "<p>5. एक कक्षा में लड़कों और लड़&zwnj;कियों की संख्या का अनुपात 3 : 5 है। कक्षा में लड़कों का प्रतिशत कितना है?</p>",
                    options_en: ["<p>37.5 %</p>", "<p>60 %</p>", 
                                "<p>42.5 %</p>", "<p>12.5 %</p>"],
                    options_hi: ["<p>37.5 %</p>", "<p>60 %</p>",
                                "<p>42.5 %</p>", "<p>12.5 %</p>"],
                    solution_en: "<p>5.(a)<br>Required % of boys = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>3</mn><mo>+</mo><mn>5</mn></mrow></mfrac><mi>&#160;</mi></math>&times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>8</mn></mfrac></math> = 37.5%</p>",
                    solution_hi: "<p>5.(a)<br>लड़कों का आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>3</mn><mo>+</mo><mn>5</mn></mrow></mfrac><mi>&#160;</mi></math>&times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>8</mn></mfrac></math> = 37.5%</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. In an election, the winner was supported by 46% of all the voters in the list, and he got 410 votes more than his only rival.&nbsp;10% of the voters on the voters&rsquo; list did not cast their votes, and 60 voters cast their ballot papers blank. How many voters were on the list?</p>",
                    question_hi: "<p>6. एक चुनाव में सूची में सभी मतदाताओं के 46% द्वारा विजेता का समर्थन किया गया था और उसे अपने एकमात्र प्रतिद्वंद्वी से 410 वोट अधिक मिले। मतदाता सूची के 10% मतदाताओं ने वोट नहीं डाले और 60 मतदाताओं ने अपने मतपत्र खाली डाल दिए। सूची में कितने मतदाता थे?</p>",
                    options_en: ["<p>17445</p>", "<p>16550</p>", 
                                "<p>17500</p>", "<p>15750</p>"],
                    options_hi: ["<p>17445</p>", "<p>16550</p>",
                                "<p>17500</p>", "<p>15750</p>"],
                    solution_en: "<p>6.(c)<br>Let the no of voters on the list be 100x<br>No of valid votes = 100x&nbsp;&times; 90% - 60 = (90x - 60)<br>Votes received by winner = 100x&nbsp;&times; 46% = 46x<br>Votes received by loser = (90x&nbsp;- 60) - 46x = (44x - 60)<br>According to question,<br>46x&nbsp;- (44x - 60) = 410<br>2x&nbsp;+ 60 = 410<br>2x&nbsp;= 350<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>2</mn></mfrac></math> = 175<br>So, the no of voters on the list = 100x&nbsp;= 100 &times; 175 = 17500</p>",
                    solution_hi: "<p>6.(c)<br>माना मतदाताओं की संख्या 100<math display=\"inline\"><mi>x</mi></math> है<br>वैध मतों की संख्या = 100x&nbsp;&times; 90% - 60 = (90x - 60)<br>विजेता को प्राप्त वोट = 100x&nbsp;&times; 46% = 46x<br>हारने वाले को प्राप्त वोट = (90x&nbsp;- 60) - 46x = (44x - 60)<br>प्रश्न के अनुसार,<br>46x&nbsp;- (44x - 60) = 410<br>2x&nbsp;+ 60 = 410<br>2x&nbsp;= 350<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>2</mn></mfrac></math> = 175<br>अतः, सूची में मतदाताओं की संख्या = 100x&nbsp;= 100 &times; 175 = 17500</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The strength of a school increases and decreases every alternate year by 15%. If it started with an increase in 2012, then the strength of the school in the beginning of 2015, as compared to that in 2012, had (correct to two decimal place</p>",
                    question_hi: "<p>7. एक स्कूल की छात्र संख्&zwj;या प्रत्येक एकांतर वर्ष में 15% बढ़ती और घटती है। यदि वृद्धि की शुरुआत 2012 में हुई, तो 2012 की तुलना में, 2015 की शुरुआत में स्कूल की छात्र संख्&zwj;या में ________(दो दशमलव स्थान तक सही) हुई।</p>",
                    options_en: ["<p>Decreased by 12.41%</p>", "<p>Increased by 12.41 %</p>", 
                                "<p>Decreased by 13.85 %</p>", "<p>Increased by 13.85 %</p>"],
                    options_hi: ["<p>12.41% की कमी</p>", "<p>12.41 % की वृद्धि</p>",
                                "<p>13.85 % की कमी</p>", "<p>13.85 % की वृद्धि</p>"],
                    solution_en: "<p>7.(b)<br><strong id=\"docs-internal-guid-06ec767f-7fff-5971-4262-eb19e957a2f4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEZO2lGkKsuyT-s_1ltql0YCiz3H06DF8q3-9reJUwf9_niLqAdKoNnCBceo3OWfh9n2Gq6aNjQSRLtXE1O_qKGcOLIHhSWtCDJNLntI7C6AzdRyw-H6nMjupDYP3YINTMbx8FoQ?key=rdDRrjikuAwwjY3SNNMVAJ6w\" width=\"231\" height=\"40\"></strong><br>&nbsp; &nbsp; &nbsp; &nbsp; +15 %&nbsp; &nbsp; &nbsp; &nbsp;- 15 %&nbsp; &nbsp; &nbsp; + 15%<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> <br>Let the total student increased in school in 2012 to 2015 be x<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8993</mn><mn>8000</mn></mfrac></math><br>Student increase % = <math display=\"inline\"><mfrac><mrow><mn>993</mn><mi>&#160;</mi></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 12.41%</p>",
                    solution_hi: "<p>7.(b)<br><strong id=\"docs-internal-guid-06ec767f-7fff-5971-4262-eb19e957a2f4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEZO2lGkKsuyT-s_1ltql0YCiz3H06DF8q3-9reJUwf9_niLqAdKoNnCBceo3OWfh9n2Gq6aNjQSRLtXE1O_qKGcOLIHhSWtCDJNLntI7C6AzdRyw-H6nMjupDYP3YINTMbx8FoQ?key=rdDRrjikuAwwjY3SNNMVAJ6w\" width=\"231\" height=\"40\"></strong><br>&nbsp; &nbsp; &nbsp; &nbsp; +15 %&nbsp; &nbsp; &nbsp; &nbsp;- 15 %&nbsp; &nbsp; &nbsp; + 15%<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math><br>माना 2012 से 2015 तक स्कूल में कुल विद्यार्थियों की संख्या में वृद्धि <math display=\"inline\"><mi>x</mi></math> है।<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8993</mn><mn>8000</mn></mfrac></math><br>छात्र वृद्धि% = <math display=\"inline\"><mfrac><mrow><mn>993</mn><mi>&#160;</mi></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 12.41%</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The population of a town increases 8% annually. If its present population is 1,42,560, what was the population one year ago?</p>",
                    question_hi: "<p>8. एक कश्बे की जनसंख्या में प्रति वर्ष 8% की वृद्धि होती है। यदि इसकी वर्तमान जनसंख्या 1,42,560 है, तो एक वर्ष पूर्व जनसंख्या कितनी थी?</p>",
                    options_en: ["<p>1,34,000</p>", "<p>1,33,000</p>", 
                                "<p>1,32,000</p>", "<p>1,31,000</p>"],
                    options_hi: ["<p>1,34,000</p>", "<p>1,33,000</p>",
                                "<p>1,32,000</p>", "<p>1,31,000</p>"],
                    solution_en: "<p>8.(c)<br>Population one year ago = 1,42,560 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>108</mn></mfrac></math> = 1,32,000</p>",
                    solution_hi: "<p>8.(c)<br>एक वर्ष पहले की जनसंख्या = 1,42,560 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>108</mn></mfrac></math> = 1,32,000</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. In an election between two candidates, 10% of the voters did not cast their votes and 75 votes were found invalid. The winner got 50% of the total votes expected and won by 170 votes. How many voters were enrolled in the voters\' list?</p>",
                    question_hi: "<p>9. दो उम्मीदवारों के बीच एक चुनाव में 10% मतदाताओं ने वोट नहीं डाले और 75 वोट अमान्य पाए गए। विजेता को अपेक्षित कुल वोटों के 50% वोट मिले और वह 170 वोटों से जीत गया। मतदाता सूची में कितने मतदाताओं का नाम दर्ज है?</p>",
                    options_en: ["<p>800</p>", "<p>855</p>", 
                                "<p>850</p>", "<p>950</p>"],
                    options_hi: ["<p>800</p>", "<p>855</p>",
                                "<p>850</p>", "<p>950</p>"],
                    solution_en: "<p>9.(d)<br>Let total votes = 100x%<br>Voters cast their vote = 90x%<br>Valid votes = 90x% - 75<br>Votes winner got = 50x%<br>Votes Loser got = (90x% - 75) - 50x% = (40x% - 75)<br>According to question,<br>50x% - (40x% - 75) = 170<br>(10x% + 75) = 170 votes<br>100% (vote cast) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>170</mn><mo>-</mo><mn>75</mn></mrow><mn>10</mn></mfrac></math> &times; 100 = 950</p>",
                    solution_hi: "<p>9.(d)<br>माना कुल वोट = 100x%<br>मतदाताओं ने अपना वोट डाला = 90x%<br>वैध वोट = 90x% - 75<br>विजेता को मिले वोट = 50x%<br>हारने वाले को मिले वोट = (90x% - 75) - 50x% = (40x% - 75)<br>प्रश्न के अनुसार,<br>50x% - (40x% - 75) = 170<br>(10x% + 75) = 170 वोट<br>100% (वोट पड़े) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>170</mn><mo>-</mo><mn>75</mn></mrow><mn>10</mn></mfrac></math>&nbsp;&times; 100 = 950</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. In an election, A received 27% of the votes and B received 125,982 votes. 19% did not cast their votes. Find the number of votes received by A.</p>",
                    question_hi: "<p>10. एक चुनाव में, A को 27% मत मिले और B को 125,982 मत मिले। 19% ने मतदान नहीं किया। A द्वारा प्राप्त मतों की संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>44,327</p>", "<p>1,88,973</p>", 
                                "<p>57,827</p>", "<p>62,991</p>"],
                    options_hi: ["<p>44,327</p>", "<p>1,88,973</p>",
                                "<p>57,827</p>", "<p>62,991</p>"],
                    solution_en: "<p>10.(d)<br>Votes received by B = 100 - 27 - 19 = 54%<br>54% = 125,982<br>Votes received by A (27%) = <math display=\"inline\"><mfrac><mrow><mn>125</mn><mo>,</mo><mn>982</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math> &times; 27 = 62,991</p>",
                    solution_hi: "<p>10.(d)<br>B को प्राप्त वोट = 100 - 27 - 19 = 54%<br>54% = 125,982<br>A को प्राप्त वोट (27%) = <math display=\"inline\"><mfrac><mrow><mn>125</mn><mo>,</mo><mn>982</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math> &times; 27 = 62,991</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. In an election between two candidates, the candidate who gets 35% of the votes polled is defeated by 15,900 votes. What is the total number of votes polled?</p>",
                    question_hi: "<p>11. दो उम्मीदवारों के बीच एक चुनाव में, 35% मत प्राप्त करने वाला उम्मीदवार 15,900 मतों से हार जाता है। डाले गए मतों की कुल संख्या ज्ञात करें।</p>",
                    options_en: ["<p>53,000</p>", "<p>45,000</p>", 
                                "<p>35,000</p>", "<p>43,000</p>"],
                    options_hi: ["<p>53,000</p>", "<p>45,000</p>",
                                "<p>35,000</p>", "<p>43,000</p>"],
                    solution_en: "<p>11.(a)<br>% of winners vote - % of losers vote = 65% - 35% = 30% <br>30% = 15,900 votes<br>100 % (total votes polled) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>,</mo><mn>900</mn></mrow><mn>30</mn></mfrac></math> &times;&nbsp;100 = 53,000</p>",
                    solution_hi: "<p>11.(a)<br>विजेताओं का वोट % - हारने वालों का वोट% = 65% - 35% = 30% <br>30% = 15,900 वोट<br>100 % (कुल वोट पड़े) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>,</mo><mn>900</mn></mrow><mn>30</mn></mfrac></math> &times; 100 = 53,000</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Bhavani spends 68% of her income. Her income increases by 15% and her expenditure increases by 10%. How much is the increase in the percentage of saving? (Use decimal up to 2 digits)</p>",
                    question_hi: "<p>12. भवानी अपनी आय का 68% खर्च करती है। उसकी आय में 15% की वृद्धि होती है और उसके व्यय में 10% की वृद्धि होती है। उसकी बचत में कितने प्रतिशत की वृद्धि हुई? (दो दशमलव अंकों तक ज्ञात कीजिए)</p>",
                    options_en: ["<p>20.57%</p>", "<p>25.62%</p>", 
                                "<p>28.42%</p>", "<p>24.80%</p>"],
                    options_hi: ["<p>20.57%</p>", "<p>25.62%</p>",
                                "<p>28.42%</p>", "<p>24.80%</p>"],
                    solution_en: "<p>12.(b)<br>Let income of Bhavani = 100<br>Ratio &rarr; income&nbsp; &nbsp; :&nbsp; &nbsp; expenditure&nbsp; &nbsp; :&nbsp; &nbsp; saving<br>Before &rarr;&nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;68&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 32<br>After &rarr;&nbsp; &nbsp; &nbsp; &nbsp; 115&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;74.8&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 40.2<br>Percentage change in saving <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>32</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>2</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 25.62%</p>",
                    solution_hi: "<p>12.(b)<br>माना कि भवानी की आय = 100<br>अनुपात &rarr;&nbsp;आय&nbsp; &nbsp; :&nbsp; &nbsp; व्यय&nbsp; &nbsp; :&nbsp; &nbsp; बचत<br>पहले &rarr;&nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 68&nbsp; &nbsp; :&nbsp; &nbsp; 32<br>बाद मे &rarr;&nbsp; &nbsp;115&nbsp; &nbsp; :&nbsp; &nbsp; 74.8&nbsp; &nbsp; :&nbsp; &nbsp; 40.2<br>बचत में प्रतिशत परिवर्तन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>32</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>2</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 25.62%</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A number is first decreased by 10%, then increased by 30% and then further it is decreased by 20%. What is the net decrease percentage in the number ?</p>",
                    question_hi: "<p>13. एक संख्या पहले 10% घटाई जाती है, फिर 30% बढ़ाई जाती है और फिर 20% घटाई जाती है। संख्या में शुद्ध कमी प्रतिशत कितना है?</p>",
                    options_en: ["<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", 
                                "<p>5<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>",
                                "<p>5<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>13.(b)<br>Initial&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; final<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 9<br>&nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;13<br>&nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 4<br>&nbsp; &nbsp; ----------------------<br>&nbsp; &nbsp; &nbsp;500&nbsp; &nbsp; :&nbsp; &nbsp; 468<br>Net decrease = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>-</mo><mn>468</mn></mrow><mn>500</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>5</mn></mfrac></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>%</p>",
                    solution_hi: "<p>13.(b)<br>प्रारंभिक&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;अंतिम<br>&nbsp; &nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;9<br>&nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; :&nbsp; &nbsp; 13<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;4<br>&nbsp; &nbsp; &nbsp;----------------------<br>&nbsp; &nbsp; &nbsp; 500&nbsp; &nbsp; :&nbsp; &nbsp; 468<br>शुद्ध कमी = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>-</mo><mn>468</mn></mrow><mn>500</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>5</mn></mfrac></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Ramnarayan\'s salary was reduced by 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% and then, the reduced salary was increased by 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>%. By what percentage (rounded off to two decimal places) is his new salary more/less as compared to his original salary?</p>",
                    question_hi: "<p>14. रामनारायण के वेतन में 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% की कमी की गई, और फिर, कम किए गए वेतन में 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>% की वृद्धि की गई। उसका नया वेतन उसके मूल वेतन की तुलना में कितने प्रतिशत (दशमलव के दो स्थानों तक पूर्णांकित) अधिक/कम है?</p>",
                    options_en: ["<p>3.03% less</p>", "<p>4.04% more</p>", 
                                "<p>4.04% less</p>", "<p>3.03% more</p>"],
                    options_hi: ["<p>3.03% कम</p>", "<p>4.04% अधिक</p>",
                                "<p>4.04% कम</p>", "<p>3.03% अधिक</p>"],
                    solution_en: "<p>14.(b) <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>5</mn></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>500</mn></mfrac></math><br>Ratio &rarr;&nbsp; &nbsp; &nbsp; Initial&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Final<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;32&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;31<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;500&nbsp; &nbsp; :&nbsp; &nbsp; 537 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;---------------------------<br>Net change &rarr;&nbsp;16000 : 16647<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>647</mn></mrow><mrow><mn>16000</mn></mrow></mfrac></math> &times; 100 = 4.04% increase</p>",
                    solution_hi: "<p>14.(b) <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>5</mn></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>500</mn></mfrac></math><br>अनुपात &rarr;&nbsp; &nbsp; &nbsp; &nbsp; आरंभिक&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;अंतिम<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;32&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;31<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 500&nbsp; &nbsp; :&nbsp; &nbsp; 537 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;----------------------------<br>शुद्ध परिवर्तन &rarr;&nbsp; &nbsp; &nbsp; &nbsp;16000 : 16647<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>647</mn></mrow><mrow><mn>16000</mn></mrow></mfrac></math> &times; 100 = 4.04% वृद्धि</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In an election between A and B, every fifth vote polled was marked as invalid by the machine. In the remaining votes, A wins the election with a margin of 2500 votes or 10% of the total votes polled, over B. If 90% of the invalid votes would have been in favour of B, then which of the following would have been the result of the election?</p>",
                    question_hi: "<p>15. A और B के बीच एक चुनाव में, डाले गए प्रत्येक पांचवें मत को मशीन द्वारा अमान्य के रूप में चिह्नित किया गया। शेष मतों में, A, 2500 मतों के अंतर से या डाले गए कुल मतों के 10% के अंतर से B से चुनाव जीत जाता है। यदि अमान्य मतों में से 90%, B के पक्ष में होते, तो निम्नलिखित में से कौन सा विकल्प चुनाव का नतीजा होता</p>",
                    options_en: ["<p>B would have won by 8%</p>", "<p>B would have won by 3%</p>", 
                                "<p>A would have won by 3%</p>", "<p>A would have won by 8%</p>"],
                    options_hi: ["<p>B, 8% से जीत जाता</p>", "<p>B, 3% से जीत जाता</p>",
                                "<p>A, 3% से जीत जाता</p>", "<p>A, 8% से जीत जाता</p>"],
                    solution_en: "<p>15.(a)<br>Let the total no of votes be 100 unit<br>No of Invalid votes = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>= 20 unit<br>No of valid votes = 100 - 20 = 80 unit<br>A + B = 80 --------------- (i)<br>A - B = 10 --------------- (ii)<br>Solving eqn (i) and (ii) we get ;<br>A = 45 unit , B = 35 unit<br>Now, If 90% of invalid votes have been in favour of B. then, <br>No of votes would have received by B = 35 unit + 90% of 20 = 35 + 18 = 53 unit<br>% of votes by which B won = 53 - 45 = 8%</p>",
                    solution_hi: "<p>15.(a)<br>माना मतों की कुल संख्या 100 इकाई है<br>अवैध मतों की संख्या = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 20 इकाई<br>वैध मतों की संख्या = 100 - 20 = 80 इकाई<br>A + B = 80 --------------- (i)<br>A - B = 10 --------------- (ii)<br>समीकरण (i) और (ii) को हल करने पर हमें प्राप्त होता है;<br>A = 45 इकाई, B = 35 इकाई<br>अब, यदि 90% अवैध मत B के पक्ष में हैं, तो, <br>B को प्राप्त मतों की संख्या = 35 इकाई + 20 का 90% = 35 + 18 = 53 इकाई<br>B द्वारा जीते गए मतों का % = 53 - 45 = 8%</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>