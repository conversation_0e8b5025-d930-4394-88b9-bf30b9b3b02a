<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Rs. 7200 is divided among P, Q and R in the ratio of <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>: <strong>4</strong>: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> respectively. What is the share of R?</p>",
                    question_hi: "<p>1. 7200 रुपए को P, Q और R के बीच में क्रमशः <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>: <strong>4</strong>: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>के अनुपात में बांटा जाता है। R का हिस्सा कितना है?</p>",
                    options_en: ["<p>Rs. 2400</p>", "<p>Rs. 4800</p>", 
                                "<p>Rs. 600</p>", "<p>Rs. 1800</p>"],
                    options_hi: ["<p>2400 रुपए</p>", "<p>4800 रुपए</p>",
                                "<p>600 रुपए</p>", "<p>1800 रुपए</p>"],
                    solution_en: "<p>1.(d)<br>P : Q : R = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> : <strong>4 </strong>: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> = 1 : 8 : 3<br>Share of R = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> &times; 7200 = 1800</p>",
                    solution_hi: "<p>1.(d)<br>P : Q : R = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> : <strong>4 </strong>: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> = 1 : 8 : 3<br>R का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> &times; 7200 = 1800</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "2. If M : N= 9 : 6, then what is the value of (M + N) : (M - N) ?",
                    question_hi: "2. यदि M : N = 9 : 6 है, तो (M + N) : (M - N) का मान कितना है?",
                    options_en: [" 5 : 1", " 1 : 5", 
                                " 5 : 4", " 5 : 2"],
                    options_hi: [" 5 : 1", " 1 : 5",
                                " 5 : 4", " 5 : 2<br /> "],
                    solution_en: "2.(a)<br /> (M + N) : (M - N) = (9 + 6) : (9 - 6)= 15 : 3 = 5 : 1",
                    solution_hi: "2.(a)<br /> (M + N) : (M - N) = (9 + 6) : (9 - 6)= 15 : 3 = 5 : 1",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Rs. 8400 is divided among P, Q and R in the ratio of <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> respectively. What is the share of R ?</p>",
                    question_hi: "<p>3. P, Q और R के बीच 8400 रुपए को क्रमशः <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> के अनुपात में बांटा जाता है। R का हिस्सा कितना है ?</p>",
                    options_en: ["<p>Rs. 700</p>", "<p>Rs. 1400</p>", 
                                "<p>Rs. 900</p>", "<p>Rs. 18000</p>"],
                    options_hi: ["<p>700 रुपए</p>", "<p>1400 रुपए</p>",
                                "<p>900 रुपए</p>", "<p>18000 रुपए</p>"],
                    solution_en: "<p>3.(a)<br>P : Q : R = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 2 : 9 : 1<br>Share of R = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> &times; 8400 = ₹ 700</p>",
                    solution_hi: "<p>3.(a)<br>P : Q : R = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 2 : 9 : 1<br>R का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> &times; 8400 = ₹ 700</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If 45 percent of X is equal to 15 percent of Y, then what is the value of X : Y ?</p>",
                    question_hi: "<p>4. यदि X का 45 प्रतिशत, Y के 15 प्रतिशत के बराबर है, तो X : Y का मान कितना है ?</p>",
                    options_en: ["<p>3 : 1</p>", "<p>1 : 4</p>", 
                                "<p>3 : 2</p>", "<p>1 : 3</p>"],
                    options_hi: ["<p>3 : 1</p>", "<p>1 : 4</p>",
                                "<p>3 : 2</p>", "<p>1 : 3</p>"],
                    solution_en: "<p>4.(d) <br>45% of X = 15% of Y<br><math display=\"inline\"><mfrac><mrow><mi>X</mi></mrow><mrow><mi>Y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>45</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>4.(d) <br>X का 45% = Y का 15%<br><math display=\"inline\"><mfrac><mrow><mi>X</mi></mrow><mrow><mi>Y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>45</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. If P: Q: R = 9 :10 : 11, then what is the value of (P+Q): (Q+R): (R+P)?",
                    question_hi: "5. यदि P : Q: R = 9 : 10 : 11 है, तो (P + Q): (Q + R) : (R + P) का मान कितना है?",
                    options_en: [" 19:21:20", " 19:21:19 ", 
                                " 19:21:22", " 19:21:23"],
                    options_hi: [" 19:21:20", " 19:21:19 ",
                                " 19:21:22", " 19:21:23"],
                    solution_en: "5.(a)<br />(P+Q): (Q+R): (R+P) = (9+10) : (10+11) : (11+9) = 19 : 21 : 20",
                    solution_hi: "5.(a)<br />(P+Q): (Q+R): (R+P) = (9+10) : (10+11) : (11+9) = 19 : 21 : 20",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "6. The present ages of three people in proportions is 3 : 5 : 7. Six years ago, the sum of their ages was 87. Find their present ages (in years).",
                    question_hi: "6. तीन व्यक्तियों की वर्तमान आयु का अनुपात 3 : 5 : 7 है। छः वर्ष पहले, उनकी आयु का योग 87 था। उनकी वर्तमान आयु (वर्षों में) ज्ञात कीजिए।",
                    options_en: [" 24 years, 40 years, 56 years", " 18 years, 30 years, 42 years", 
                                " 21 years, 35 years, 49 years", " 27 years, 45 years, 63 years"],
                    options_hi: [" 24 वर्ष, 40 वर्ष, 56 वर्ष", " 18 वर्ष, 30 वर्ष, 42 वर्ष",
                                " 21 वर्ष, 35 वर्ष, 49 वर्ष", " 27 वर्ष, 45 वर्ष, 63 वर्ष"],
                    solution_en: "6.(c)<br />Let the present ages of 3 people be 3x, 5x and 7x <br />Sum of their present ages = 87+6×3 = 105 yrs<br />ATQ,<br />3x+5x+7x = 105<br />15x = 105<br />x = 7<br />Present ages of 3 people = 3x = 3×7 = 21 yrs, 5x = 5×7 = 35 yrs, 7x = 7×7 = 49 yrs",
                    solution_hi: "6.(c)<br />माना कि 3 व्यक्तियों की वर्तमान आयु 3x, 5x और 7x है<br />उनकी वर्तमान आयु का योग = 87+ 6×3 = 105 वर्ष<br />प्रश्नानुसार <br />3x+ 5x +7x = 105<br />15x = 105<br />  x   = 7<br />3 व्यक्तियों की वर्तमान आयु = 3x = 3×7 = 21 वर्ष, 5x = 5×7 = 35 वर्ष, 7x = 7×7 = 49 वर्ष",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Prithvi has pears and peaches in the ratio of 10 : 13. Celine has pears and peaches in the ratio of 7 : 9. Vikram has pears and peaches in the ratio of 5 : 7. Who has a greater ratio of pears to peaches ?</p>",
                    question_hi: "<p>7. पृथ्वी के पास नाशपाती और आड़ू 10 : 13 के अनुपात में हैं। सेलीन के पास नाशपाती और आड़ू 7 : 9 के अनुपात हैं। विक्रम के पास नाशपाती और आड़ू 5 : 7 के अनुपात में है। किसके पास नाशपाती और आडू अधिक अनुपात में हैं ?</p>",
                    options_en: ["<p>Vikram</p>", "<p>Prithvi</p>", 
                                "<p>All are equal</p>", "<p>Celine</p>"],
                    options_hi: ["<p>विक्रम</p>", "<p>पृथ्वी</p>",
                                "<p>सभी के पास समान अनुपात में हैं</p>", "<p>सेलीन</p>"],
                    solution_en: "<p>7.(d)<br>LCM of(13,9,7) = 819<br>Prithvi &rarr; <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>63</mn></mrow><mrow><mn>13</mn><mo>&#215;</mo><mn>63</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>630</mn><mn>819</mn></mfrac></math><br>Celine &rarr; <math display=\"inline\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>91</mn></mrow><mrow><mn>9</mn><mo>&#215;</mo><mn>91</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>637</mn><mn>819</mn></mfrac></math><br>Vikram &rarr; <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>117</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mn>117</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>585</mn><mn>819</mn></mfrac></math><br>Clearly, we can see that Celine has greater ratio of pears to peaches.</p>",
                    solution_hi: "<p>7.(d)<br>(13,9,7) का ल<math display=\"inline\"><mo>&#8728;</mo></math>स = 819<br>पृथ्वी &rarr; <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>63</mn></mrow><mrow><mn>13</mn><mo>&#215;</mo><mn>63</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>630</mn><mn>819</mn></mfrac></math><br>सेलीन &rarr; <math display=\"inline\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>91</mn></mrow><mrow><mn>9</mn><mo>&#215;</mo><mn>91</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>637</mn><mn>819</mn></mfrac></math><br>विक्रम &rarr; <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>117</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mn>117</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>585</mn><mn>819</mn></mfrac></math><br>स्पष्ट रूप से, हम देख सकते हैं कि सेलीन के पास नाशपाती और आड़ू का अनुपात अधिक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The ratio of the income to the expenditure of a family is 19 : 13. If the family\'s expenses are ₹ 29,510, then find the savings of the family.</p>",
                    question_hi: "<p>8. एक परिवार की आय और व्यय का अनुपात 19: 13 है। यदि परिवार का व्यय ₹ 29,510 है, तो उस परिवार की बचत ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹13,620</p>", "<p>₹15,550</p>", 
                                "<p>₹14,440</p>", "<p>₹12,240</p>"],
                    options_hi: ["<p>₹ 13,620</p>", "<p>₹15,550</p>",
                                "<p>₹ 14,440</p>", "<p>₹ 12,240</p>"],
                    solution_en: "<p>8.(a)<br>Income : Expenditure : Saving = 19 : 13 : 6<br>13 unit --------------- ₹29,510<br>6 unit --------------- <math display=\"inline\"><mfrac><mrow><mn>29510</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>&times;6 = ₹13,620</p>",
                    solution_hi: "<p>8.(a)<br>आय : व्यय : बचत = 19 : 13 : 6<br>13 इकाई = ₹29,510<br>6 इकाई = <math display=\"inline\"><mfrac><mrow><mn>29510</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>&times;6 = ₹13,620</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Find the fourth proportional to 30, 120 and 15.</p>",
                    question_hi: "<p>9. 30, 120 और 15 का चतुर्थ समानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>60</p>", "<p>90</p>", 
                                "<p>100</p>", "<p>120</p>"],
                    options_hi: ["<p>60</p>", "<p>90</p>",
                                "<p>100</p>", "<p>120</p>"],
                    solution_en: "<p>9.(a) Let the fourth proportion be <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>120</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mi>x</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 60</p>",
                    solution_hi: "<p>9.(a) माना कि चौथा अनुपात <math display=\"inline\"><mi>x</mi></math> है<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>120</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mi>x</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 60</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>th of A is equal to<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>th of B, then find the value of A : B.</p>",
                    question_hi: "<p>10. यदि A का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>वां भाग, B के <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> भाग के बराबर है, तो A : B का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>2 : 3</p>", "<p>1 : 2</p>", 
                                "<p>2 : 1</p>", "<p>3 : 4</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>1 : 2</p>",
                                "<p>2 : 1</p>", "<p>3 : 4</p>"],
                    solution_en: "<p>10.(b) <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>of B&nbsp;<br>So,<br><math display=\"inline\"><mo>&#8658;</mo></math> AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &rArr; A : B = 1 : 2</p>",
                    solution_hi: "<p>10.(b) A का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = B का<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math><br>इसलिए,<br><math display=\"inline\"><mo>&#8658;</mo></math> AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &rArr; A : B = 1 : 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. In a school there are total 560 students. The ratio of boys and girls is 13: 15. If 40 more boys gets admitted into the school, then what will be the new ratio of boys and girls?</p>",
                    question_hi: "<p>11. एक विद्यालय में कुल 560 विद्यार्थी हैं। विद्यालय में लड़के और लड़कियों का अनुपात 13 : 15 है। यदि विद्यालय में 40 और लड़कों का दाखिला हो जाता है, तो लड़के और लड़कियों का नया अनुपात कितना होगा?</p>",
                    options_en: ["<p>2 : 3</p>", "<p>1 : 1</p>", 
                                "<p>3 : 1</p>", "<p>2 : 1</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>1 : 1</p>",
                                "<p>3 : 1</p>", "<p>2 : 1</p>"],
                    solution_en: "<p>11.(b)<br>No of boys = <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>13</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math> &times; 560 = 260<br>No of girls = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>13</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math> &times; 560 = 300<br>Now, New no of boys = 260 + 40 = 300<br>Required ratio = 300 : 300 = 1 : 1</p>",
                    solution_hi: "<p>11.(b)<br>लड़कों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>13</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math> &times; 560 = 260<br>लड़कियों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>13</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math> &times; 560 = 300<br>अब, लड़कों की नई संख्या = 260 + 40 = 300<br>आवश्यक अनुपात = 300 : 300 = 1 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If 4M = 5N = 20C, then what is the value of M : N : C ?</p>",
                    question_hi: "<p>12. यदि 4M = 5N = 20C है, तो M : N : C का मान कितना है?</p>",
                    options_en: ["<p>5 : 4 : 3</p>", "<p>5 : 4 : 1</p>", 
                                "<p>7 : 5 : 4</p>", "<p>9 : 3 : 1</p>"],
                    options_hi: ["<p>5 : 4 : 3</p>", "<p>5 : 4 : 1</p>",
                                "<p>7 : 5 : 4</p>", "<p>9 : 3 : 1</p>"],
                    solution_en: "<p>12.(b)<br>LCM of (4,5,20) = 20<br>Let 4M = 5N = 20C = 20<br><math display=\"inline\"><mo>&#8658;</mo></math> M : N : C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>20</mn></mfrac></math> = 5 : 4 : 1</p>",
                    solution_hi: "<p>12.(b)<br>(4,5,20) का LCM = 20<br>माना 4M = 5N = 20C = 20<br><math display=\"inline\"><mo>&#8658;</mo></math> M : N : C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>20</mn></mfrac></math> = 5 : 4 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If Rs. 70, 000 is distributed among A, B and C such that A : B = 2 : 3 and B : C = 4 : 5, then find the share of A.</p>",
                    question_hi: "<p>13. यदि 70,000 रुपए को A, B और C के बीच इस प्रकार बांटा जाता है कि A : B = 2 : 3 और B : C = 4 : 5 होता है, तो इसमें A का हिस्सा कितना होगा।</p>",
                    options_en: ["<p>Rs. 18,500</p>", "<p>Rs. 14,500</p>", 
                                "<p>Rs. 15,800</p>", "<p>Rs. 16,000</p>"],
                    options_hi: ["<p>18,500 रुपए</p>", "<p>14,500 रुपए</p>",
                                "<p>15,800 रुपए</p>", "<p>16,000 रुपए</p>"],
                    solution_en: "<p>13.(d)<br>Balancing the given ratio, we have : <br><strong>A B C</strong><br>2 : 3 : <strong>3</strong><br><strong>4 </strong>: 4 : 5<br>_______________________<br>8 : 12 : 15<br>Share of A = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>8</mn><mo>+</mo><mn>12</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math>&times;70,000 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>35</mn></mfrac></math>&times;70,000 = ₹16,000</p>",
                    solution_hi: "<p>13.(d)<br>दिए गए अनुपात को संतुलित करने पर, हमें प्राप्त होता है<br><strong>A&nbsp; B&nbsp; C</strong><br>2 : 3 : <strong>3</strong><br><strong>4 </strong>: 4 : 5<br>_______________________<br>8 : 12 : 15<br>A का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>8</mn><mo>+</mo><mn>12</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math>&times;70,000 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>35</mn></mfrac></math>&times;70,000 = ₹16,000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. If M : N : P = 3 : 7 : 5 and P : Q = 1 : 2, then what is the value of M : Q ?</p>",
                    question_hi: "<p>14. यदि M : N : P = 3 : 7 : 5 और P : Q = 1 : 2 है, तो M : Q का मान कितना है?</p>",
                    options_en: ["<p>2 : 1</p>", "<p>2 : 3</p>", 
                                "<p>1 : 3</p>", "<p>3 : 10</p>"],
                    options_hi: ["<p>2 : 1</p>", "<p>2 : 3</p>",
                                "<p>1 : 3</p>", "<p>3 : 10</p>"],
                    solution_en: "<p>14.(d)<br><strong>M&nbsp; N&nbsp; P&nbsp; Q</strong><br>3 : 7 : 5 : <strong>5</strong> <br><strong>1 </strong>: <strong>1 </strong>: <strong>1 </strong>: 2<br>__________________<br>3 : 7 : 5 : 10<br>So, M:Q = 3 : 10</p>",
                    solution_hi: "<p>14.(d)<br><strong>M&nbsp; N&nbsp; P&nbsp; Q</strong><br>3 : 7 : 5 : <strong>5</strong> <br><strong>1 </strong>: <strong>1 </strong>: <strong>1 </strong>: 2<br>__________________<br>3 : 7 : 5 : 10<br>तो, M : Q = 3 : 10</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If the difference between the two numbers is 30 percent of their sum, then what is the ratio of larger number and smaller number?</p>",
                    question_hi: "<p>15. यदि दो संख्याओं के बीच का अंतर, उन संख्याओं के योग का 30 प्रतिशत है, तो बड़ी संख्या और छोटी संख्या का अनुपात क्या है?</p>",
                    options_en: ["<p>14 : 3</p>", "<p>12 : 5</p>", 
                                "<p>13 : 7</p>", "<p>15 : 7</p>"],
                    options_hi: ["<p>14 : 3</p>", "<p>12 : 5</p>",
                                "<p>13 : 7</p>", "<p>15 : 7</p>"],
                    solution_en: "<p>15.(c)<br>Let the two no&rsquo;s be a and b<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow><mrow><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math><br>Using componendo dividendo rule,<br><math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>3</mn></mrow><mrow><mn>10</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>15.(c)<br>माना कि दो संख्याएँ a और b हैं<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow><mrow><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math><br>योगांतरानुपात नियम का उपयोग करने पर ,<br><math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>3</mn></mrow><mrow><mn>10</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>7</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>