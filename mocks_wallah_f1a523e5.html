<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">9:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 9 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\">Select</span><span style=\"font-family: Cambria Math;\"> the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Meagre</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\">Select</span><span style=\"font-family: Cambria Math;\"> the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Meagre</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Civilised</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Miserable<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>Plentiful<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Sparse</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Civilised</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Miserable<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>Plentiful<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Sparse</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Meagre</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>is lacking in quantity or quality. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> c plentiful is the most appropriate answer.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Civilised</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">means polite and well mannered.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Miserable</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>means to feel unhappy and sad.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Sparse</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>means in short supply.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Meagre</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>is lacking in quantity or quality. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> c plentiful is the most appropriate answer.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Civilised</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">means polite and well mannered.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Miserable</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>means to feel unhappy and sad.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Sparse</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>means in short supply.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Slender</span></strong></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Slender</span></strong></p>\n",
                    options_en: ["<p>Lively<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Energetic</p>\n", 
                                "<p>Defame</p>\n", "<p>Frail</p>\n"],
                    options_hi: ["<p>Lively<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Energetic</p>\n",
                                "<p>Defame</p>\n", "<p>Frail</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Slender </strong></span><span style=\"font-family: Cambria Math;\">means thin. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> d frail which means weak and delicate is the most appropriate answer. </span><span style=\"font-family: Cambria Math;\">Lively and Energetic are </span><span style=\"font-family: Cambria Math;\">synonyms.They</span><span style=\"font-family: Cambria Math;\"> mean full of energy.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Slender </strong></span><span style=\"font-family: Cambria Math;\">means thin. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> d frail which means weak and delicate is the most appropriate answer. </span><span style=\"font-family: Cambria Math;\">Lively and Energetic are </span><span style=\"font-family: Cambria Math;\">synonyms.They</span><span style=\"font-family: Cambria Math;\"> mean full of energy.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">To draw a blank</span></strong></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">To draw a blank</span></strong></p>\n",
                    options_en: ["<p>To win by chance</p>\n", "<p>To fake something</p>\n", 
                                "<p>To be unsuccessful at eliciting a response</p>\n", "<p>To get revenge</p>\n"],
                    options_hi: ["<p>To win by chance</p>\n", "<p>To fake something</p>\n",
                                "<p>To be unsuccessful at eliciting a</p>\r\n<p><span style=\"font-family: Cambria Math;\"> response</span></p>\n", "<p>To get revenge</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) <strong>To draw a blank</strong></span><span style=\"font-family: Cambria Math;\"><strong>-</strong> to fail to get a desired outcome.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">eg</span><span style=\"font-family: Cambria Math;\">:-</span><span style=\"font-family: Cambria Math;\"> He asked me for my phone number and I drew blank.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) <strong>To draw a blank</strong></span><span style=\"font-family: Cambria Math;\">- to fail to get a desired outcome.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">eg</span><span style=\"font-family: Cambria Math;\">:-</span><span style=\"font-family: Cambria Math;\"> He asked me for my phone number and I drew blank.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the given group of words</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Take and use the thoughts, inventions, etc. of another person as one&rsquo;s own.</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the given group of words</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Take and use the thoughts, inventions, etc. of another person as one&rsquo;s own.</span></p>\n",
                    options_en: ["<p>Copyright<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Compose<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>Produce<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Plagiarise</span></p>\n"],
                    options_hi: ["<p>Copyright<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Compose<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>Produce<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Plagiarise</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong><span style=\"font-family: Cambria Math;\">Plagiarise</span></strong><span style=\"font-family: Cambria Math;\">- Take and use the thoughts, inventions, etc. of another person as one&rsquo;s own.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong><span style=\"font-family: Cambria Math;\">Plagiarise</span></strong><span style=\"font-family: Cambria Math;\">- Take and use the thoughts, inventions, etc. of another person as one&rsquo;s own.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in direct speech.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Kavi asked Gina if French was her mother tongue.</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in direct speech.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Kavi asked Gina if French was her mother tongue.</span></p>\n",
                    options_en: ["<p>&ldquo;<span style=\"font-family: Cambria Math;\">Whether French is your mother tongue?&rdquo; Kavi asked Gina.</span></p>\n", "<p>&ldquo;<span style=\"font-family: Cambria Math;\">Your mother tongue is French,&rdquo; Kavi enquired Gina.</span></p>\n", 
                                "<p>&ldquo;<span style=\"font-family: Cambria Math;\">French is your mother tongue,&rdquo; Kavi told Gina.</span></p>\n", "<p>&ldquo;<span style=\"font-family: Cambria Math;\">Is French your mother tongue?&rdquo; </span><span style=\"font-family: Cambria Math;\">Kavi asked</span><span style=\"font-family: Cambria Math;\"> Gina.</span></p>\n"],
                    options_hi: ["<p>&ldquo;<span style=\"font-family: Cambria Math;\">Whether French is your mother tongue?&rdquo; Kavi asked Gina.</span></p>\n", "<p>&ldquo;<span style=\"font-family: Cambria Math;\">Your mother tongue is French,&rdquo; Kavi enquired Gina.</span></p>\n",
                                "<p>&ldquo;<span style=\"font-family: Cambria Math;\">French is your mother tongue,&rdquo; Kavi told Gina.</span></p>\n", "<p>&ldquo;<span style=\"font-family: Cambria Math;\">Is French your mother tongue?&rdquo; Kavi asked Gina.</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) &ldquo;</span><strong>Whether French is</strong><span style=\"font-weight: 400;\"> your mother tongue?&rdquo; Kavi asked Gina.&nbsp; (Incorrect word)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b)&nbsp; &ldquo;Your mother tongue is French,&rdquo; Kavi enquired Gina. (It is not interrogative)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c)&nbsp; &ldquo;French is your mother tongue,&rdquo; Kavi told Gina. (It is not&nbsp; interrogative)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d)&nbsp; &ldquo;Is French your mother tongue?&rdquo; Kavi asked Gina. (Correct)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) &ldquo;</span><strong>Whether French is</strong><span style=\"font-weight: 400;\"> your mother tongue?&rdquo; Kavi asked Gina.&nbsp; (Incorrect word)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b)&nbsp; &ldquo;Your mother tongue is French,&rdquo; Kavi enquired Gina. (It is not interrogative)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c)&nbsp; &ldquo;French is your mother tongue,&rdquo; Kavi told Gina. (It is not&nbsp; interrogative)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d)&nbsp; &ldquo;Is French your mother tongue?&rdquo; Kavi asked Gina. (Correct)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6.</span><span style=\"font-family:Cambria Math\"> In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</span></p> <p><span style=\"font-family:Cambria Math\">A major ________ of modern times is that we have come to assume that all creation is ________ to the material interests of us, the homo-sapiens inhabiting earth, which has cut the _________ relationship we otherwise enjoyed with other constituents of creation</span></p>",
                    question_hi: " <p>6.</span><span style=\"font-family:Cambria Math\"> In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</span></p> <p><span style=\"font-family:Cambria Math\">A major ________ of modern times is that we have come to assume that all creation is ________ to the material interests of us, the homo-sapiens inhabiting earth, which has cut the _________ relationship we otherwise enjoyed with other constituents of creation</span></p>",
                    options_en: [" <p> fallacy, subservient, symbiotic</span></p>", " <p> frankness, superior, disadvantageous</span></p>", 
                                " <p> accuracy, domineering, encumbering</span></p>", " <p> felicity, disobedient, hindering</span></p>"],
                    options_hi: [" <p> fallacy, subservient, symbiotic</span></p>", " <p> frankness, superior, disadvantageous</span></p>",
                                " <p> accuracy, domineering, encumbering</span></p>", " <p> felicity, disobedient, hindering</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">6.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">The first blank will be filled by the word fallacy. Fallacy means a mistaken belief. The word assume is used in the first part of the sentence which hints at the word fallacy. Assume means to suppose something without proper facts and this leads to a </span><span style=\"font-family:Cambria Math\">fallacy.So</span><span style=\"font-family:Cambria Math\"> a is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">6.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">The first blank will be filled by the word fallacy. Fallacy means a mistaken belief. The word assume is used in the first part of the sentence which hints at the word fallacy. Assume means to suppose something without proper facts and this leads to a </span><span style=\"font-family:Cambria Math\">fallacy.So</span><span style=\"font-family:Cambria Math\"> a is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p>7. </span><span style=\"font-family:Cambria Math\"> In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</span></p> <p><span style=\"font-family:Cambria Math\">We have already said that inconsistency is not a matter of ________education or lack of logical ________.</span></p>",
                    question_hi: " <p>7. </span><span style=\"font-family:Cambria Math\"> In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</span></p> <p><span style=\"font-family:Cambria Math\">We have already said that inconsistency is not a matter of ________education or lack of logical ________.</span></p>",
                    options_en: [" <p> indefinite, stupor</span></p>", " <p> institutional, shiver</span></p>", 
                                " <p> inadequate, </span><span style=\"font-family:Cambria Math\">rigour</span></p>", " <p> inadvertent, snigger</span></p>"],
                    options_hi: [" <p> indefinite, stupor</span></p>", " <p> institutional, shiver</span></p>",
                                " <p> inadequate, </span><span style=\"font-family:Cambria Math\">rigour</span></p>", " <p> inadvertent, snigger</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">c) </span><span style=\"font-family:Cambria Math\">Inadequate means insufficient for a </span><span style=\"font-family:Cambria Math\">purpose.Inadequate</span><span style=\"font-family:Cambria Math\"> education is the most appropriate. Hence option c will be the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">c) </span><span style=\"font-family:Cambria Math\">Inadequate means insufficient for a </span><span style=\"font-family:Cambria Math\">purpose.Inadequate</span><span style=\"font-family:Cambria Math\"> education is the most appropriate. Hence option c will be the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: " <p>8.</span><span style=\"font-family:Cambria Math\"> Select the correct homonym from the given options to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Nadia had an apple and </span><span style=\"font-family:Cambria Math\">a________for</span><span style=\"font-family:Cambria Math\"> breakfast.</span></p>",
                    question_hi: " <p>8.</span><span style=\"font-family:Cambria Math\"> Select the correct homonym from the given options to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Nadia had an apple and </span><span style=\"font-family:Cambria Math\">a________for</span><span style=\"font-family:Cambria Math\"> breakfast.</span></p>",
                    options_en: [" <p> pear    </span></p>", " <p> prier    </span></p>", 
                                " <p> pair    </span></p>", " <p> pare</span></p>"],
                    options_hi: [" <p> pear    </span></p>", " <p> prier    </span></p>",
                                " <p> pair    </span></p>", " <p> pare</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Pear is a fruit. The sentence mentions about something being eaten so pear is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Pear is a fruit. The sentence mentions about something being eaten so pear is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Identify the option that arranges the given parts in the correct order to form a</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">meaningful and coherent paragraph.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a. Though she was a woman of slim </span><span style=\"font-family: \'Cambria Math\';\">build</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b. Mrs. Hima Raman used to teach us </span><span style=\"font-family: \'Cambria Math\';\">geography at school</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">c. she was remarkably strong and </span><span style=\"font-family: \'Cambria Math\';\">energetic</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">d. She had beautiful eyes and a sharp </span><span style=\"font-family: \'Cambria Math\';\">nose</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> Identify the option that arranges the given parts in the correct order to form a</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">meaningful and coherent paragraph.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a. Though she was a woman of slim </span><span style=\"font-family: \'Cambria Math\';\">build</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b. Mrs. Hima Raman used to teach us </span><span style=\"font-family: \'Cambria Math\';\">geography at school</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">c. she was remarkably strong and </span><span style=\"font-family: \'Cambria Math\';\">energetic</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">d. She had beautiful eyes and a sharp </span><span style=\"font-family: \'Cambria Math\';\">nose</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">c,d</span><span style=\"font-family: Cambria Math;\">,a,b</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">a,c</span><span style=\"font-family: Cambria Math;\">,d,b</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">a,b</span><span style=\"font-family: Cambria Math;\">,c,d</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">b,d</span><span style=\"font-family: Cambria Math;\">,a,c</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">c,d</span><span style=\"font-family: Cambria Math;\">,a,b</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">a,c</span><span style=\"font-family: Cambria Math;\">,d,b</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">a,b</span><span style=\"font-family: Cambria Math;\">,c,d</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">b,d</span><span style=\"font-family: Cambria Math;\">,a,c</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Part b will be the first sentence as it mentions </span><span style=\"font-family: Cambria Math;\">Mrs</span><span style=\"font-family: Cambria Math;\"> Hima Raman. In the other three parts the pronoun &ldquo;she&rdquo; has been used for </span><span style=\"font-family: Cambria Math;\">Mrs</span><span style=\"font-family: Cambria Math;\"> Hima Raman. The noun is used before the pronoun. So b will be the first </span><span style=\"font-family: Cambria Math;\">sentence.With</span><span style=\"font-family: Cambria Math;\"> b in the beginning we have only option d. Hence d is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Part b will be the first sentence as it mentions </span><span style=\"font-family: Cambria Math;\">Mrs</span><span style=\"font-family: Cambria Math;\"> Hima Raman. In the other three parts the pronoun &ldquo;she&rdquo; has been used for </span><span style=\"font-family: Cambria Math;\">Mrs</span><span style=\"font-family: Cambria Math;\"> Hima Raman. The noun is used before the pronoun. So b will be the first </span><span style=\"font-family: Cambria Math;\">sentence.With</span><span style=\"font-family: Cambria Math;\"> b in the beginning we have only option d. Hence d is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. Which word in the given sentence is the ANTONYM of &ndash; </span><strong><span style=\"font-family: Cambria Math;\">doubtful</span></strong><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The poverty of his childhood played a decisive role in his adult life.</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. Which word in the given sentence is the ANTONYM of &ndash; </span><strong><span style=\"font-family: Cambria Math;\">doubtful</span></strong><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The poverty of his childhood played a decisive role in his adult life.</span></p>\n",
                    options_en: ["<p>decisive<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>played<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>adult<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>poverty</p>\n"],
                    options_hi: ["<p>decisive<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>played<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>adult<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>poverty</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Doubtful</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>means something which is uncertain. Decisive is something which produces a definite result. Hence a is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Doubtful</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>means something which is uncertain. Decisive is something which produces a definite result. Hence a is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the correct homonym from the given options to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I wonder if the committee </span><span style=\"font-family: Cambria Math;\">members changed _______ plans.</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">Select the correct homonym from the given options to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I wonder if the committee&nbsp; </span><span style=\"font-family: Cambria Math;\">members changed _______ plans.</span></p>\n",
                    options_en: ["<p>they&rsquo;re</p>\n", "<p><span style=\"font-family: Cambria Math;\">their</span></p>\n", 
                                "<p>dare</p>\n", "<p>there</p>\n"],
                    options_hi: ["<p>they&rsquo;re</p>\n", "<p><span style=\"font-family: Cambria Math;\">their</span></p>\n",
                                "<p>dare</p>\n", "<p>there</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">The pronoun </span><span style=\"font-family: Cambria Math;\">their</span><span style=\"font-family: Cambria Math;\"> will be used. &ldquo;Their&rdquo; is used with the people or things previously mentioned or easily identified (committee members</span><span style=\"font-family: Cambria Math;\">).So</span><span style=\"font-family: Cambria Math;\"> b is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">The pronoun </span><span style=\"font-family: Cambria Math;\">their</span><span style=\"font-family: Cambria Math;\"> will be used. &ldquo;Their&rdquo; is used with the people or things previously mentioned or easily identified (committee members</span><span style=\"font-family: Cambria Math;\">).So</span><span style=\"font-family: Cambria Math;\"> b is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Chastise</span></strong></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Chastise</span></strong></p>\n",
                    options_en: ["<p>Discipline</p>\n", "<p>Order</p>\n", 
                                "<p>Sever<span style=\"font-family: Cambria Math;\"> ties</span></p>\n", "<p>Chase</p>\n"],
                    options_hi: ["<p>Discipline</p>\n", "<p>Order</p>\n",
                                "<p>Sever<span style=\"font-family: Cambria Math;\"> ties</span></p>\n", "<p>Chase</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">a) </span><strong><span style=\"font-family: Cambria Math;\">Chastise </span></strong><span style=\"font-family: Cambria Math;\">means to rebuke or reprimand severely.</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">a) </span><strong><span style=\"font-family: Cambria Math;\">Chastise </span></strong><span style=\"font-family: Cambria Math;\">means to rebuke or reprimand severely.</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">We are waiting to the arrival of our cousins from the West.</span></p>",
                    question_hi: " <p>13. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">We are waiting to the arrival of our cousins from the West.</span></p>",
                    options_en: [" <p>  at</span></p>", " <p>  towards</span></p>", 
                                " <p>  since</span></p>", " <p>  for</span></p>"],
                    options_hi: [" <p>  at</span></p>", " <p>  towards</span></p>",
                                " <p>  since</span></p>", " <p>  for</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">13.(</span><span style=\"font-family:Cambria Math\">d) </span><span style=\"font-family:Cambria Math\">Waiting for is used when we are expecting someone or </span><span style=\"font-family:Cambria Math\">something.Eg</span><span style=\"font-family:Cambria Math\">- Waiting for the results/ a friend/ the food/ teacher/bus etc.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">13.(</span><span style=\"font-family:Cambria Math\">d) </span><span style=\"font-family:Cambria Math\">Waiting for is used when we are expecting someone or </span><span style=\"font-family:Cambria Math\">something.Eg</span><span style=\"font-family:Cambria Math\">- Waiting for the results/ a friend/ the food/ teacher/bus etc.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The news of the robbery spread faster than ________.</span></p>",
                    question_hi: " <p>14.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The news of the robbery spread faster than ________.</span></p>",
                    options_en: [" <p>  fire</span><span style=\"font-family:Cambria Math\">  </span></p>", " <p>  wind</span><span style=\"font-family:Cambria Math\">     </span></p>", 
                                " <p> water     </span></p>", " <p>  air</span></p>"],
                    options_hi: [" <p>  fire</span><span style=\"font-family:Cambria Math\">  </span></p>", " <p>  wind</span><span style=\"font-family:Cambria Math\">     </span></p>",
                                " <p> water     </span></p>", " <p>  air</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Spread like wildfire is an idiom which means to spread with great speed. </span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> a is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Spread like wildfire is an idiom which means to spread with great speed. </span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> a is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Man of letters</span></strong></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Man of letters</span></strong></p>\n",
                    options_en: ["<p>Dancer</p>\n", "<p>Scholar</p>\n", 
                                "<p>Singer</p>\n", "<p>Actor</p>\n"],
                    options_hi: ["<p>Dancer</p>\n", "<p>Scholar</p>\n",
                                "<p>Singer</p>\n", "<p>Actor</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">To draw a blank means to be unsuccessful at eliciting a response</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eg</span><span style=\"font-family: Cambria Math;\">- The boy asked the girl for her phone number and she drew a blank.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">To draw a blank means to be unsuccessful at eliciting a response</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eg</span><span style=\"font-family: Cambria Math;\">- The boy asked the girl for her phone number and she drew a blank.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Ignominy</span></strong></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Ignominy</span></strong></p>\n",
                    options_en: ["<p>Beneficial</p>\n", "<p>Restorative</p>\n", 
                                "<p>Pejorative</p>\n", "<p>Glory</p>\n"],
                    options_hi: ["<p>Beneficial</p>\n", "<p>Restorative</p>\n",
                                "<p>Pejorative</p>\n", "<p>Glory</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Ignominy</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>is public shame or disgrace. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> glory that is option d is the most appropriate answer. </span><strong><span style=\"font-family: Cambria Math;\">Pejorative</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>means expressing contempt or disapproval.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Ignominy</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>is public shame or disgrace. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> glory that is option d is the most appropriate answer. </span><strong><span style=\"font-family: Cambria Math;\">Pejorative</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>means expressing contempt or disapproval.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Jump the gun</span></strong></p>\n",
                    question_hi: "<p>17.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Jump the gun</span></strong></p>\n",
                    options_en: ["<p>Start<span style=\"font-family: Cambria Math;\"> something too soon or act </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> hastily</span></p>\n", "<p>Watch<span style=\"font-family: Cambria Math;\"> a person carefully</span></p>\n", 
                                "<p>There<span style=\"font-family: Cambria Math;\"> is always a cost to do </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> something</span></p>\n", "<p>Intentionally<span style=\"font-family: Cambria Math;\"> raise a false alarm</span></p>\n"],
                    options_hi: ["<p>Start<span style=\"font-family: Cambria Math;\"> something too soon or act </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> hastily</span></p>\n", "<p>Watch<span style=\"font-family: Cambria Math;\"> a person carefully</span></p>\n",
                                "<p>There<span style=\"font-family: Cambria Math;\"> is always a cost to do </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> something</span></p>\n", "<p>Intentionally<span style=\"font-family: Cambria Math;\"> raise a false alarm</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">Jump the gun means to start something too soon or act hastily.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eg</span><span style=\"font-family: Cambria Math;\">- These days the media jumps the gun and gives out the verdict of controversial cases even before the investigation completes.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">Jump the gun means to start something too soon or act hastily.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eg</span><span style=\"font-family: Cambria Math;\">- These days the media jumps the gun and gives out the verdict of controversial cases even before the investigation completes.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in passive voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">They propose to build a dam for irrigation purposes</span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in passive voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">They propose to build a dam for irrigation purposes</span></p>\n",
                    options_en: ["<p>Build<span style=\"font-family: Cambria Math;\"> a dam for irrigation purposes.</span></p>\n", "<p>For<span style=\"font-family: Cambria Math;\"> the irrigation purposes a dam has been built.</span></p>\n", 
                                "<p>Irrigation<span style=\"font-family: Cambria Math;\"> dam is built.</span></p>\n", "<p>It<span style=\"font-family: Cambria Math;\"> is proposed to build a dam for irrigation purposes.</span></p>\n"],
                    options_hi: ["<p>Build<span style=\"font-family: Cambria Math;\"> a dam for irrigation purposes.</span></p>\n", "<p>For<span style=\"font-family: Cambria Math;\"> the irrigation purposes a dam has been built.</span></p>\n",
                                "<p>Irrigation<span style=\"font-family: Cambria Math;\"> dam is built.</span></p>\n", "<p>It<span style=\"font-family: Cambria Math;\"> is proposed to build a dam for irrigation purposes.</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) Build a dam for irrigation purposes.(Tone has changed to authoritative)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) For the irrigation purposes a dam has been built.(Meaning has changed)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) Irrigation dam is built.(Meaning has changed)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) It is proposed to build a dam for irrigation purposes.(Correct)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) Build a dam for irrigation purposes.(Tone has changed to authoritative)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) For the irrigation purposes a dam has been built.(Meaning has changed)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) Irrigation dam is built.(Meaning has changed)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) It is proposed to build a dam for irrigation purposes.(Correct)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Bestial</span></strong></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Bestial</span></strong></p>\n",
                    options_en: ["<p>Brutish</p>\n", "<p>Greedy<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>High<span style=\"font-family: Cambria Math;\">-minded</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Noble</p>\n"],
                    options_hi: ["<p>Brutish</p>\n", "<p>Greedy<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>High<span style=\"font-family: Cambria Math;\">-minded</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Noble</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Bestial </strong></span><span style=\"font-family: Cambria Math;\">means animal like, like a beast.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Brutish </span></strong><span style=\"font-family: Cambria Math;\">means very violent like a brute. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option a is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Bestial </strong></span><span style=\"font-family: Cambria Math;\">means animal like, like a beast.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Brutish </span></strong><span style=\"font-family: Cambria Math;\">means very violent like a brute. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option a is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20.</span><span style=\"font-family:Cambria Math\"> The following sentence has been split into four segments. Identify the segment that contains a grammatical error.</span></p> <p><span style=\"font-family:Cambria Math\">The students stopped / their chatter / as soon as them noticed / the teacher enter the classroom.</span></p>",
                    question_hi: " <p>20.</span><span style=\"font-family:Cambria Math\"> The following sentence has been split into four segments. Identify the segment that contains a grammatical error.</span></p> <p><span style=\"font-family:Cambria Math\">The students stopped / their chatter / as soon as them noticed / the teacher enter the classroom.</span></p>",
                    options_en: [" <p> their chatter</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> The students stopped</span></p>", 
                                " <p> the teacher </span><span style=\"font-family:Cambria Math\">enter</span><span style=\"font-family:Cambria Math\"> the classroom</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> as soon as them noticed</span></p>"],
                    options_hi: [" <p> their chatter</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> The students stopped</span></p>",
                                " <p> the teacher </span><span style=\"font-family:Cambria Math\">enter</span><span style=\"font-family:Cambria Math\"> the classroom</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> as soon as them noticed</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">20.(</span><span style=\"font-family:Cambria Math\">d) </span><span style=\"font-family:Cambria Math\">“As soon as them noticed” will be replaced by “as soon as they noticed”. Use of objective pronoun is incorrect here and a nominative pronoun will be used.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">20.(</span><span style=\"font-family:Cambria Math\">d) </span><span style=\"font-family:Cambria Math\">“As soon as them noticed” will be replaced by “as soon as they noticed”. Use of objective pronoun is incorrect here and a nominative pronoun will be used.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (21) </span></p>\n",
                    question_hi: "<p>21.<strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (21) </span></p>\n",
                    options_en: ["<p>stagnation<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>recovery<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>setback<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>improvement</p>\n"],
                    options_hi: ["<p>stagnation</p>\n", "<p>recovery</p>\n",
                                "<p>setback<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>improvement</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">In the first sentence the word easier has been used. Easier is a positive word. Except improvement all the other words are negative so option d is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">In the first sentence the word easier has been used. Easier is a positive word. Except improvement all the other words are negative so option d is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22.<strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (22) </span></p>\n",
                    question_hi: "<p>22.<span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (22) </span></p>\n",
                    options_en: ["<p>traffic<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>delivery<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>shipment<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>transportation</p>\n"],
                    options_hi: ["<p>traffic<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>delivery<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>shipment<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>transportation</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Roads transport goods and </span><span style=\"font-family: Cambria Math;\">people.Transport</span><span style=\"font-family: Cambria Math;\"> means to take or carry (people or goods) from one place to another. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option d is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Roads transport goods and </span><span style=\"font-family: Cambria Math;\">people.Transport</span><span style=\"font-family: Cambria Math;\"> means to take or carry (people or goods) from one place to another. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option d is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.<span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (23) </span></p>\n",
                    question_hi: "<p>23.<strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (23) </span></p>\n",
                    options_en: ["<p>donated<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>supported<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>promoted<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>invested</p>\n"],
                    options_hi: ["<p>donated<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>supported<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>promoted<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>invested</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Invested in is a phrasal verb which means to use money for (something) in order to earn more </span><span style=\"font-family: Cambria Math;\">money.So</span><span style=\"font-family: Cambria Math;\"> option d is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Invested in is a phrasal verb which means to use money for (something) in order to earn more </span><span style=\"font-family: Cambria Math;\">money.So</span><span style=\"font-family: Cambria Math;\"> option d is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.<strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (24) </span></p>\n",
                    question_hi: "<p>24. <strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: \'Cambria Math\';\">Select the most appropriate option to fill in blank number (24)</span></p>\n",
                    options_en: ["<p>credulous<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>fascinated</p>\n", 
                                "<p>attentive<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>remarkable</p>\n"],
                    options_hi: ["<p>credulous<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>fascinated</p>\n",
                                "<p>attentive<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>remarkable</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Remarkable means something/someone who is worthy of attention.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Remarkable means something/someone who is worthy of attention.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (25)</span></p>\n",
                    question_hi: "<p>25.<span style=\"font-family: Cambria Math;\"> &nbsp;</span><strong>Cloze Test :</strong> <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Fill in the blanks with the help of the options given. Select the most appropriate option for each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ of roads in the 1700s and 1800s made the (22)_________ of goods and people much easier. Many nobles and businessmen (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">__________ their wealth in building bridges and tunnels. John Metcalfe was one of the most (</span><span style=\"font-family: Cambria Math;\">24)_</span><span style=\"font-family: Cambria Math;\">_________ road builders of this period. He was blind and felt the surface of the road. This was done to make sure that it met his (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (25)</span></p>\n",
                    options_en: ["<p>desire<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>requirements<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>wish<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>condition</p>\n"],
                    options_hi: ["<p>desire<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>requirements</p>\n",
                                "<p>wish<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>condition</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">To meet someone&rsquo;s requirements means to satisfy certain conditions.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">To meet someone&rsquo;s requirements means to satisfy certain conditions.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>