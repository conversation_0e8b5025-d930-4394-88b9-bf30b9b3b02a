<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The intergovernmental-treaty-based organisation having its headquarters in India is:</p>",
                    question_hi: "<p>1. अंतर्राष्ट्रीय सरकारी संधि आधारित संगठन जिसका मुख्यालय भारत में है?</p>",
                    options_en: ["<p>OECD</p>", "<p>ISA</p>", 
                                "<p>NATO</p>", "<p>ATS</p>"],
                    options_hi: ["<p>OECD</p>", "<p>ISA</p>",
                                "<p>NATO</p>", "<p>ATS</p>"],
                    solution_en: "<p>1.(b) <strong>ISA - </strong>The International Solar Alliance (founded in 2015) has become the first treaty-based international government organization to be based in India. <strong>Headquarter </strong>- Gurugram, Haryana. <strong>Objective </strong>- Scale up solar energy, reduce the cost of solar power generation through aggregation of demand for solar finance, technologies, innovation, research and development, and capacity building. Organization for Economic Co-operation and Development <strong>(OECD) -</strong> Paris (France). The North Atlantic Treaty Organization <strong>(NATO) - </strong>Brussels, Belgium.</p>",
                    solution_hi: "<p>1.(b) <strong>ISA - </strong>अंतर्राष्ट्रीय सौर गठबंधन (2015 में स्थापित) भारत में स्थित होने वाला पहला संधि-आधारित अंतर्राष्ट्रीय सरकारी संगठन बन गया है। <strong>मुख्यालय</strong>-गुरुग्राम, हरियाणा। <strong>उद्देश्य </strong>- सौर ऊर्जा को बढ़ाना, सौर लागत, प्रौद्योगिकियों, नवाचार, अनुसंधान और विकास और क्षमता निर्माण की मांग के एकत्रीकरण के माध्यम से सौर ऊर्जा उत्पादन की लागत को कम करना। आर्थिक सहयोग और विकास संगठन (<strong>OECD</strong>) - पेरिस (फ्रांस)। उत्तरी अटलांटिक संधि संगठन (<strong>NATO</strong>) - ब्रुसेल्स, बेल्जियम।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. INTERPOL has its headquarters in ________</p>",
                    question_hi: "<p>2. INTERPOL का मुख्यालय__________ में है।</p>",
                    options_en: ["<p>Spain</p>", "<p>France</p>", 
                                "<p>Switzerland</p>", "<p>Germany</p>"],
                    options_hi: ["<p>स्पेन</p>", "<p>फ्रांस</p>",
                                "<p>स्विट्ज़रलैंड</p>", "<p>जर्मनी</p>"],
                    solution_en: "<p>2.(b)<strong> France (Lyon). INTERPOL -</strong> International Criminal Police Organization (Founded in 1923) facilitates worldwide police cooperation and crime control. Interpol is the world&rsquo;s largest international police organization, with 194 member countries. India joined Interpol in 1949. The Central Bureau of Investigation (India) collaborates with INTERPOL. <strong>Other international organization :</strong> Organization for Economic Cooperation and Development (OECD) - Paris, France. United Nations Educational Scientific and Cultural Organisation (UNESCO) - Paris, France.</p>",
                    solution_hi: "<p>2.(b) <strong>फ़्रांस (ल्योन)। INTERPOL - </strong>अंतर्राष्ट्रीय आपराधिक पुलिस संगठन (1923 में स्थापित) दुनिया भर में पुलिस सहयोग और अपराध नियंत्रण की सुविधा प्रदान करता है। INTERPOL , 194 सदस्य देशों के साथ दुनिया का सबसे बड़ा अंतरराष्ट्रीय पुलिस संगठन है। भारत 1949 में इंटरपोल में शामिल हुआ। केंद्रीय जांच ब्यूरो (भारत) INTERPOL के साथ सहयोग करता है। <strong>अन्य अंतर्राष्ट्रीय संगठन: </strong>आर्थिक सहयोग और विकास संगठन (OECD) - पेरिस, फ्रांस। संयुक्त राष्ट्र शैक्षिक वैज्ञानिक और सांस्कृतिक संगठन (UNESCO) - पेरिस, फ्रांस।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following is one of the founding countries of ASEAN?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन ASEAN के संस्थापक देशों में से एक है?</p>",
                    options_en: ["<p>Australia</p>", "<p>Cambodia</p>", 
                                "<p>Malaysia</p>", "<p>India</p>"],
                    options_hi: ["<p>ऑस्ट्रेलिया</p>", "<p>कंबोडिया</p>",
                                "<p>मलेशिया</p>", "<p>भारत</p>"],
                    solution_en: "<p>3.(c) <strong>Malaysia</strong>. The Association of Southeast Asian Nations (ASEAN) is a regional grouping that promotes economic, political, and security cooperation among its members. Other founding members - Indonesia, Philippines, Singapore and Thailand. It has a total of 1<strong>0 Members,</strong> Other are - Brunei, Cambodia, Laos, Myanmar and Vietnam. It was founded in 1967, Bangkok (Thailand). Motto - One Vision, One Identity, One Community. <strong>Headquarter </strong>- Jakarta, Indonesia.</p>",
                    solution_hi: "<p>3.(c) <strong>मलेशिया</strong>। दक्षिण पूर्व एशियाई देशों का संगठन (ASEAN) एक क्षेत्रीय समूह है जो अपने सदस्यों के बीच आर्थिक, राजनीतिक और सुरक्षा सहयोग को बढ़ावा देता है। अन्य संस्थापक सदस्य - इंडोनेशिया, फिलीपींस, सिंगापुर और थाईलैंड। इसके कुल <strong>10 सदस्य</strong> हैं, अन्य - ब्रुनेई, कंबोडिया, लाओस, म्यांमार और वियतनाम। इसकी स्थापना 1967 में बैंकॉक (थाईलैंड) में हुई थी। <strong>आदर्श वाक्य -</strong> एक दृष्टि, एक पहचान, एक समुदाय(One Vision, One Identity, One Community)। <strong>मुख्यालय </strong>- जकार्ता, इंडोनेशिया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Nobel Laureate Kailash Satyarthi is associated with which of the following organizations?</p>",
                    question_hi: "<p>4. नोबेल पुरस्कार विजेता, कैलाश सत्यार्थी निम्नलिखित में से किस संगठन से जुड़े हैं?</p>",
                    options_en: ["<p>SOS village</p>", "<p>Child Relief and you</p>", 
                                "<p>Bachpan Bachao Andolan</p>", "<p>Beti Bachao, Beti Padhao</p>"],
                    options_hi: ["<p>SOS गांव</p>", "<p>बाल राहत और आप</p>",
                                "<p>बचपन बचाओ आंदोलन</p>", "<p>बेटी बचाओ, बेटी पढ़ाओ</p>"],
                    solution_en: "<p>4.(c) <strong>Bachpan Bachao Andolan. Kailash Satyarthi </strong>is an Indian social reformer who campaigned against child labor in India and advocated the universal right to education. Bachpan Bachao Andolan was started in 1980. He won the Nobel peace prize in 2014. <strong>Beti Bachao Beti Padhao</strong> was launched by the Prime Minister on 22 January 2015 in Panipat, Haryana.</p>",
                    solution_hi: "<p>4.(c) <strong>बचपन बचाओ आंदोलन। कैलाश सत्यार्थी</strong> एक भारतीय समाज सुधारक हैं जिन्होंने भारत में बाल श्रम के खिलाफ अभियान चलाया और शिक्षा के सार्वभौमिक अधिकार की वकालत की। बचपन बचाओ आंदोलन 1980 में शुरू किया गया था। उन्होंने 2014 में नोबेल शांति पुरस्कार भी जीता। <strong>बेटी बचाओ बेटी पढ़ाओ की शुरुआत</strong> प्रधानमंत्री नरेंद्र मोदी के द्वारा 22 जनवरी 2015 को हरियाणा के पानीपत से शुरू की गई थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Where is the headquarters of OPEC located?</p>",
                    question_hi: "<p>5. OPEC का मुख्यालय कहाँ स्थित है?</p>",
                    options_en: ["<p>Zurich</p>", "<p>Geneva</p>", 
                                "<p>Brussels</p>", "<p>Vienna</p>"],
                    options_hi: ["<p>ज्यूरिख</p>", "<p>जेनेवा</p>",
                                "<p>ब्रुसेल्स</p>", "<p>वियना</p>"],
                    solution_en: "<p>5.(d)<strong> Vienna. OPEC</strong> (Organization of Petroleum Exporting Countries): Founded - 1960. Member countries - 13. <br><strong>International Organizations and Headquarters</strong>: International Atomic Energy Agency (IAEA) - Vienna (Austria). World Health Organisation (WHO) - Geneva, (Switzerland). North Atlantic Treaty Organisation (NATO) - Brussels (Belgium). The Federation Internationale de Football Association (FIFA) - Zurich (Switzerland).</p>",
                    solution_hi: "<p>5.(d) <strong>वियना। OPEC</strong> (पेट्रोलियम निर्यातक देशों का संगठन): स्थापना - 1960, सदस्य देश - 13 । <strong>अंतर्राष्ट्रीय संगठन और मुख्यालय: </strong>अंतर्राष्ट्रीय परमाणु ऊर्जा एजेंसी (IAEA) - वियना (ऑस्ट्रिया)। विश्व स्वास्थ्य संगठन (WHO) - जिनेवा, (स्विट्जरलैंड)। उत्तर अटलांटिक संधि संगठन (NATO) - ब्रुसेल्स (बेल्जियम)। फेडरेशन इंटरनेशनेल डी फुटबॉल एसोसिएशन (FIFA) - ज्यूरिख (स्विट्जरलैंड)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. The International Institute of Educational Planning in Paris is a part of which organization of the United Nations ?</p>",
                    question_hi: "<p>6. पेरिस में अंतर्राष्ट्रीय शैक्षिक योजना संस्थान संयुक्त राष्ट्र के किस संगठन का एक हिस्सा है?</p>",
                    options_en: ["<p>UNICEF</p>", "<p>UNESCO</p>", 
                                "<p>ILO</p>", "<p>UNO</p>"],
                    options_hi: ["<p>UNICEF</p>", "<p>UNESCO</p>",
                                "<p>ILO</p>", "<p>UNO</p>"],
                    solution_en: "<p>6.(b) <strong>UNESCO </strong>(United Nations Educational, Scientific and Cultural Organization): It seeks to build peace through international cooperation in Education, the Sciences and Culture. Founded - 1945. <strong>International Organizations: </strong>ILO (International Labor Organization) - 1919, Headquarter (Geneva, Switzerland). <strong>UNICEF </strong>(United Nations International Children\'s Emergency Fund) - 1946, Headquarter (New York).</p>",
                    solution_hi: "<p>6.(b) <strong>UNESCO </strong>(संयुक्त राष्ट्र शैक्षिक, वैज्ञानिक और सांस्कृतिक संगठन): इसका उद्देश्य शिक्षा, विज्ञान और संस्कृति में अंतर्राष्ट्रीय सहयोग के माध्यम से शांति स्थापित करना है, स्थापना - 1945 । <strong>अंतर्राष्ट्रीय संगठन:</strong> ILO (अंतर्राष्ट्रीय श्रम संगठन) - 1919, मुख्यालय (जिनेवा, स्विट्जरलैंड)। <strong>UNICEF </strong>(संयुक्त राष्ट्र अंतर्राष्ट्रीय बाल आपातकालीन कोष) - 1946, मुख्यालय (न्यूयॉर्क)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The UN was founded as a successor to the League of Nations in 1945_________ .</p>",
                    question_hi: "<p>7. संयुक्त राष्ट्र संघ की स्थापना राष्ट्र संघ के उत्तराधिकारी के रूप में 1945 में की गई थी।</p>",
                    options_en: ["<p>immediately after the Second World War</p>", "<p>right before the Second World War</p>", 
                                "<p>immediately after the First World War</p>", "<p>right before the First World War</p>"],
                    options_hi: ["<p>द्वितीय विश्व युद्ध के तुरंत बाद</p>", "<p>द्वितीय विश्व युद्ध के ठीक पहले</p>",
                                "<p>प्रथम विश्व युद्ध के तुरंत बाद</p>", "<p>प्रथम विश्व युद्ध से ठीक पहले</p>"],
                    solution_en: "<p>7.(a)<strong> immediately after the Second World War. United Nations (UN) -</strong> established on <strong>24 October 1945</strong> is an intergovernmental organisation working to maintain international peace and security, give humanitarian assistance to those in need, protect human rights, and uphold international law. <strong>Headquarter </strong>- NewYork, USA. <strong>Main bodies of UN - </strong>General Assembly, Security Council, Economic and Social Council, Trusteeship Council, International Court of Justice, Secretariat.</p>",
                    solution_hi: "<p>7.(a) <strong>द्वितीय विश्व युद्ध के तुरंत बाद। संयुक्त राष्ट्र (UN) - 24 अक्टूबर 1945</strong> को स्थापित एक अंतरसरकारी संगठन है जो अंतरराष्ट्रीय शांति और सुरक्षा बनाए रखने, जरूरतमंद लोगों को मानवीय सहायता प्रदान करने, मानवाधिकारों की रक्षा करने और अंतरराष्ट्रीय कानून को बनाए रखने के लिए कार्य करता है। <strong>मुख्यालय </strong>- न्यूयॉर्क, USA। <strong>संयुक्त राष्ट्र के मुख्य निकाय -</strong> महासभा, सुरक्षा परिषद, आर्थिक और सामाजिक परिषद, ट्रस्टीशिप परिषद, अंतर्राष्ट्रीय न्यायालय, सचिवालय।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which country is NOT a member of OPEC?</p>",
                    question_hi: "<p>8. कौन सा देश OPEC का सदस्य नहीं है?</p>",
                    options_en: ["<p>Kenya</p>", "<p>Algeria</p>", 
                                "<p>Venezuela</p>", "<p>Nigeria</p>"],
                    options_hi: ["<p>केन्या</p>", "<p>अल्जीरिया</p>",
                                "<p>वेनेजुएला</p>", "<p>नाइजीरिया</p>"],
                    solution_en: "<p>8.(a) <strong>Kenya</strong>. The <strong>Organization of the Petroleum Exporting Countries</strong> (OPEC) is a permanent, intergovernmental organization, created at the Baghdad Conference in 1960, by Iran, Iraq, Kuwait, Saudi Arabia, and Venezuela. <strong>Headquarter </strong>- Vienna, Austria. <strong>Member Countries &ndash;</strong> Algeria, Angola, Congo, Equatorial Guinea, Gabon, Iran, Iraq, Kuwait, Libya, Nigeria, Saudi Arabia, United Arab Emirates and Venezuela.</p>",
                    solution_hi: "<p>8.(a) <strong>केन्या । पेट्रोलियम निर्यातक देशों का संगठन</strong> (Organization of the Petroleum Exporting Countries - OPEC) एक स्थायी, अन्तर्शासकीय संगठन है, जिसे 1960 में बगदाद सम्मेलन में ईरान, इराक, कुवैत, सऊदी अरब और वेनेजुएला द्वारा स्थापित किया गया था। <strong>मुख्यालय </strong>- वियना, ऑस्ट्रिया। <strong>सदस्य देश -</strong> अल्जीरिया, अंगोला, कांगो, इक्वेटोरियल गिनी, गैबॉन, ईरान, इराक, कुवैत, लीबिया, नाइजीरिया, सऊदी अरब, संयुक्त अरब अमीरात और वेनेजुएला।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Clearance of snow in high altitude areas is undertaken by:</p>",
                    question_hi: "<p>9. ऊंचाई वाले क्षेत्रों में बर्फ की सफाई किसके द्वारा की जाती है:</p>",
                    options_en: ["<p>The Border Road Organisation</p>", "<p>Public Works Department</p>", 
                                "<p>Inland Waterways Authority</p>", "<p>Snow Authority of India</p>"],
                    options_hi: ["<p>सीमा सड़क संगठन</p>", "<p>लोक निर्माण विभाग</p>",
                                "<p>अंतर्देशीय जलमार्ग प्राधिकरण</p>", "<p>भारतीय बर्फ प्राधिकरण</p>"],
                    solution_en: "<p>9.(a) <strong>The Border Road Organisation - </strong>Founded in 1960, Headquarters - New Delhi. Role - (In PeaceTime) Develop &amp; Maintain the Operational Road Infrastructure of General Staff in the Border Areas, Contribute to the Socio-Economic Development of the Border States. (In War Time) - To Develop &amp; Maintain Roads to Keep the Line of Control through Original Sectors and Re-Deployed Sectors, to Execute Additional Tasks as laid down by the Govt Contributing to the War Effort.</p>",
                    solution_hi: "<p>9.(a) <strong>सीमा सड़क संगठन -</strong> 1960 में स्थापित, मुख्यालय - नई दिल्ली। भूमिका - (शांति के समय में) सीमावर्ती क्षेत्रों में जनरल स्टाफ के परिचालन सड़क बुनियादी ढांचे का विकास और रखरखाव, सीमावर्ती राज्यों के सामाजिक-आर्थिक विकास में योगदान करना। (युद्ध के समय में) - मूल सेक्टरों और पुनः तैनात सेक्टरों के माध्यम से नियंत्रण रेखा को बनाए रखने के लिए सड़कों का विकास और रख रखाव करना, युद्ध प्रयासों में योगदान करते हुए सरकार द्वारा निर्धारित अतिरिक्त कार्यों को निष्पादित करना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. In the UN Security Council there are:</p>",
                    question_hi: "<p>10. संयुक्त राष्ट्र सुरक्षा परिषद में _______हैं।</p>",
                    options_en: ["<p>10 permanent and 5 non-permanent members</p>", "<p>10 permanent and 10 non-permanent members</p>", 
                                "<p>5 permanent and 5 non-permanent members</p>", "<p>5 permanent and 10 non-permanent members</p>"],
                    options_hi: ["<p>10 स्थायी और 5 अस्थायी सदस्य</p>", "<p>10 स्थायी और 10 अस्थायी सदस्य</p>",
                                "<p>5 स्थायी और 5 अस्थायी सदस्य</p>", "<p>5 स्थायी और 10 अस्थायी सदस्य</p>"],
                    solution_en: "<p>10.(d) <strong>5 permanent and 10 non-permanent members. </strong>The Security Council consists of <strong>fifteen members,</strong> of which five are permanent - People\'s Republic of China, French Republic, Russian Federation, United Kingdom of Great Britain and Northern Ireland, and United States of America and ten non permanent members are elected by the General Assembly for a term of two years. 10 members are elected in a given manner: Five from African and Asian States, One from Eastern European States, Two from Latin American States, Two from Western European and other States.</p>",
                    solution_hi: "<p>10.(d) <strong>5 स्थायी और 10 अस्थायी सदस्य</strong>। सुरक्षा परिषद में 15 सदस्य होते हैं, जिनमें से 5 स्थायी होते हैं - पीपुल्स रिपब्लिक ऑफ चाइना, फ्रांसीसी गणराज्य, रूसी संघ, ग्रेट ब्रिटेन और उत्तरी आयरलैंड का यूनाइटेड किंगडम और संयुक्त राज्य अमेरिका तथा 10 अस्थायी सदस्यों को दो साल की अवधि के लिए महासभा द्वारा चुना जाता है। 10 सदस्य एक निश्चित प्रक्रिया से चुने जाते हैं: अफ्रीकी और एशियाई राज्यों से पांच, पूर्वी यूरोपीय राज्यों से एक, लैटिन अमेरिकी राज्यों से दो, पश्चिमी यूरोपीय और अन्य राज्यों से दो।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. World Health Organisation (WHO) is headquartered in :</p>",
                    question_hi: "<p>11. विश्व स्वास्थ्य संगठन (WHO) का मुख्यालय कहाँ है?</p>",
                    options_en: ["<p>Geneva, Switzerland</p>", "<p>Vienna, Austria</p>", 
                                "<p>Amsterdam, Netherlands</p>", "<p>New York City, America</p>"],
                    options_hi: ["<p>जिनेवा, स्विट्जरलैंड</p>", "<p>वियना, ऑस्ट्रिया</p>",
                                "<p>एम्स्टर्डम, नीदरलैंड</p>", "<p>न्यूयॉर्क शहर, अमेरिका</p>"],
                    solution_en: "<p>11.(a) <strong>Geneva, Switzerland. World Health Organisation (WHO) - </strong>Founded on 7 April (World Health Day) 1948. The World Health Assembly is the decision-making body of WHO. India became a part of the WHO Constitution on 12 January 1948.</p>",
                    solution_hi: "<p>11.(a) <strong>जिनेवा, स्विट्जरलैंड। विश्व स्वास्थ्य संगठन (WHO) - </strong>स्थापना 7 अप्रैल (विश्व स्वास्थ्य दिवस) 1948 को हुई थी। विश्व स्वास्थ्य सभा WHO की निर्णय लेने वाली संस्था है। भारत 12 जनवरी 1948 को WHO संविधान का एक पक्ष बन गया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. When was the WTO (World Trade Organization) established?</p>",
                    question_hi: "<p>12. WTO (विश्व व्यापार संगठन) की स्थापना कब हुई थी?</p>",
                    options_en: ["<p>1983</p>", "<p>1999</p>", 
                                "<p>1948</p>", "<p>1995</p>"],
                    options_hi: ["<p>1983</p>", "<p>1999</p>",
                                "<p>1948</p>", "<p>1995</p>"],
                    solution_en: "<p>12.(d) <strong>1995 (1 January). </strong>WTO (World Trade Organization): It is an intergovernmental organization that regulates and facilitates international trade. <strong>Headquarters </strong>- Geneva, Switzerland.<strong> Director-General - </strong>Ngozi Okonjo-Iweala (Nigeria, since 1 March 2021).</p>",
                    solution_hi: "<p>12.(d) <strong>1995 (1 जनवरी)।</strong> WTO (विश्व व्यापार संगठन) : यह एक अंतर्शासकीय संगठन है जो अंतर्राष्ट्रीय व्यापार को विनियमित और सुविधाजनक बनाता है। <strong>मुख्यालय </strong>- जिनेवा, स्विट्जरलैंड। <strong>महानिदेशक </strong>- न्गोजी ओकोन्जो-इवेला (नाइजीरिया, 1 मार्च 2021 से)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. When was the border road organisation established in India?</p>",
                    question_hi: "<p>13. भारत में सीमा सड़क संगठन की स्थापना कब हुई थी?</p>",
                    options_en: ["<p>September 1960</p>", "<p>May 1962</p>", 
                                "<p>September 1962</p>", "<p>May 1960</p>"],
                    options_hi: ["<p>सितंबर 1960</p>", "<p>मई 1962</p>",
                                "<p>सितंबर 1962</p>", "<p>मई 1960</p>"],
                    solution_en: "<p>13.(d) <strong>May 1960.</strong> Border Road Organisation: <strong>Headquarters </strong>- New Delhi, Founder - Jawaharlal Nehru. The<strong> main motive</strong> of BRO was to construct motorable roads in border areas and friendly neighboring countries (such as Afghanistan, Bhutan, Myanmar, and Sri Lanka). It is a statutory body. Initially, BRO was functional under the Ministry of Road Transport and Highways. But since 2015, it is being managed and is functional under the Ministry of Defence.</p>",
                    solution_hi: "<p>13.(d) <strong>मई 1960 ।</strong> सीमा सड़क संगठन: <strong>मुख्यालय </strong>- नई दिल्ली, <strong>संस्थापक </strong>- जवाहरलाल नेहरू। BRO का मुख्य उद्देश्य सीमावर्ती क्षेत्रों और मित्र पड़ोसी देशों (जैसे अफगानिस्तान, भूटान, म्यांमार और श्रीलंका) में मोटर योग्य सड़कों का निर्माण करना था। यह एक वैधानिक निकाय है। प्रारंभ में, BRO सड़क परिवहन और राजमार्ग मंत्रालय के तहत कार्यरत था। लेकिन 2015 से इसका प्रबंधन और कार्य रक्षा मंत्रालय के अधीन किया जा रहा है।।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. League of nations was replaced by:</p>",
                    question_hi: "<p>14. राष्ट्र संघ (लीग ऑफ़ नेशन्स) को किसके द्वारा प्रतिस्थापित किया गया ?</p>",
                    options_en: ["<p>World Bank</p>", "<p>Amnesty International</p>", 
                                "<p>International Monetary Fund</p>", "<p>United Nations</p>"],
                    options_hi: ["<p>विश्व बैंक</p>", "<p>एमनेस्टी इंटरनेशनल</p>",
                                "<p>अंतर्राष्ट्रीय मुद्रा कोष</p>", "<p>संयुक्त राष्ट्र</p>"],
                    solution_en: "<p>14.(d) <strong>United Nations. Founded </strong>- 24 October 1945, <strong>Headquarters </strong>- New York (USA). The United Nations and its then Secretary-General Kofi Annan were awarded the Nobel Peace Prize &ldquo;for their work for a better organized and more peaceful world&rdquo; in 2001. <strong>Amnesty International: </strong>Headquartered in London. <strong>International Monetary Fund </strong>headquarters at Washington D.C, United States. World Bank headquarters at Washington D.C, United States. <strong>The League of Nations</strong> formed in 1920.</p>",
                    solution_hi: "<p>14.(d) <strong>संयुक्त राष्ट्र। स्थापित </strong>- 24 अक्टूबर 1945, <strong>मुख्यालय </strong>- न्यूयॉर्क (USA)। संयुक्त राष्ट्र और उसके तत्कालीन महासचिव कोफ़ी अन्नान को 2001 में \"बेहतर संगठित और अधिक शांतिपूर्ण विश्व के उनके काम के लिए\" नोबेल शांति पुरस्कार से सम्मानित किया गया था। <strong>एमनेस्टी इंटरनेशनल: </strong>मुख्यालय - लंदन। <strong>अंतर्राष्ट्रीय मुद्रा कोष </strong>का मुख्यालय वाशिंगटन डी.सी., संयुक्त राज्य अमेरिका में है। विश्व बैंक का मुख्यालय वाशिंगटन डी.सी., संयुक्त राज्य अमेरिका में है। <strong>राष्ट्र संघ </strong>का गठन 1920 में हुआ था ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Amnesty International is an organisation focused on protection of:</p>",
                    question_hi: "<p>15. एमनेस्टी इंटरनेशनल एक संगठन है जो___________के संरक्षण पर केंद्रित है</p>",
                    options_en: ["<p>human rights</p>", "<p>endangered species</p>", 
                                "<p>children from malnutrition</p>", "<p>environment</p>"],
                    options_hi: ["<p>मानवाधिकार</p>", "<p>लुप्तप्राय प्रजातियां</p>",
                                "<p>कुपोषित बच्चे</p>", "<p>पर्यावरण</p>"],
                    solution_en: "<p>15.(a) <strong>Human rights. Founded -</strong> July 1961, <strong>Headquarter </strong>- London. In 1977, Amnesty International was awarded the Nobel Peace Prize for \"having contributed to securing the ground for freedom, for justice, and thereby also for peace in the world\". <strong>The United Nations Human Rights Council (UNHRC) -</strong> Formation - 15 March 2006, Headquarters - Geneva, Switzerland.</p>",
                    solution_hi: "<p>15.(a) <strong>मानवाधिकार</strong>। <strong>स्थापना </strong>- जुलाई 1961, <strong>मुख्यालय </strong>- लंदन। 1977 में, एमनेस्टी इंटरनेशनल को \"स्वतंत्रता, न्याय और इस प्रकार विश्व में शांति के लिए जमीन सुरक्षित करने में योगदान देने\" के लिए नोबेल शांति पुरस्कार से सम्मानित किया गया था। <strong>संयुक्त राष्ट्र मानवाधिकार परिषद</strong> <strong>(UNHRC)</strong> - गठन - 15 मार्च 2006, मुख्यालय - जिनेवा, स्विट्जरलैंड।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>