<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(6, 12, 72)<br>(8, 16, 96)</p>",
                    question_hi: "<p>1. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं।<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(6, 12, 72)<br>(8, 16, 96)</p>",
                    options_en: ["<p>(7, 28, 144)</p>", "<p>(12, 24, 140)</p>", 
                                "<p>(12, 24, 144)</p>", "<p>(11, 24, 144)</p>"],
                    options_hi: ["<p>(7, 28, 144)</p>", "<p>(12, 24, 140)</p>",
                                "<p>(12, 24, 144)</p>", "<p>(11, 24, 144)</p>"],
                    solution_en: "<p>1.(c) <strong>Logic :-</strong> (1st number + 2nd number) &times; 4 = 3rd number<br>(6, 12, 72) :- (6 + 12) &times; 4 &rArr; (18) &times; 4 = 72<br>(8, 16, 96) :- (8 + 16) &times; 4 &rArr; (24) &times; 4 = 96<br>Similarly,<br>(12, 24, 144) :- (12 + 24) &times; 4 &rArr; (36) &times; 4 = 144</p>",
                    solution_hi: "<p>1.(c) <strong>तर्क:-</strong> (पहली संख्या + दूसरी संख्या)&times;4 = तीसरी संख्या<br>(6, 12, 72) :- (6 + 12) &times; 4 &rArr; (18) &times; 4 = 72<br>(8, 16, 96) :- (8 + 16) &times; 4 &rArr; (24) &times; 4 = 96 <br>इसी प्रकार,<br>(12, 24, 144) :- (12 + 24) &times; 4 &rArr; (36) &times; 4 = 144</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, &lsquo;time and again&rsquo; is coded as &lsquo;zx df mo&rsquo; and &lsquo;value your time&rsquo; is coded as &lsquo;df pn tv&rsquo;. How is &lsquo;time&rsquo; coded in the given language ?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में, &lsquo;time and again&rsquo; को &lsquo;zx df mo&rsquo; लिखा जाता है और &lsquo;value your time&rsquo; को &lsquo;df pn tv&rsquo; लिखा जाता है। उसी कूट भाषा में &lsquo;time&rsquo; को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>zx</p>", "<p>tv</p>", 
                                "<p>mo</p>", "<p>df</p>"],
                    options_hi: ["<p>zx</p>", "<p>tv</p>",
                                "<p>mo</p>", "<p>df</p>"],
                    solution_en: "<p>2.(d) time and again &rarr; zx df mo&hellip;&hellip;.(i)<br>&nbsp; &nbsp; &nbsp; &nbsp; value your time &rarr; df pn tv&hellip;&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;time&rsquo; and &lsquo;df&rsquo; are common. The code of &lsquo;time&rsquo; = &lsquo;df&rsquo;.</p>",
                    solution_hi: "<p>2.(d) time and again &rarr; zx df mo&hellip;&hellip;.(i)<br>&nbsp; &nbsp; &nbsp; &nbsp; value your time &rarr; df pn tv&hellip;&hellip;.(ii)<br>(i) और (ii) से \'time\' और \'df\' उभयनिष्ठ हैं। \'time\' का कूट = \'df\'।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, \'BACK\' is coded as \'4162\' and \'CAB\' is coded as \'214. What is the code for \'K\' in that language ?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में, \'BACK\' को \'4162\' लिखा जाता है और \'CAB\' को \'214\' लिखा जाता है। तो उस कूट भाषा में \'K\' को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>1</p>", "<p>6</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>1</p>", "<p>6</p>"],
                    solution_en: "<p>3.(d) BACK &rarr; 4162&hellip;..(i)<br>&nbsp; &nbsp; &nbsp; &nbsp; CAB &rarr; 214&hellip;..(ii) <br>From (i) and (ii) &lsquo;BAC&rsquo; and &lsquo;214&rsquo; are common. The code of &lsquo;K&rsquo; = &lsquo;6&rsquo;</p>",
                    solution_hi: "<p>3.(d) BACK &rarr; 4162&hellip;..(i)<br>&nbsp; &nbsp; &nbsp; &nbsp; CAB &rarr; 214&hellip;..(ii) <br>(i) और (ii) से \'BAC\' और \'214\' उभयनिष्ठ हैं। \'K\' का कोड = \'6\'</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)<br>Referee : Field</p>",
                    question_hi: "<p>4. उस शब्द -युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या /व्यंजन/ स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>रेफरी : मैदान</p>",
                    options_en: ["<p>Judge : Courtroom</p>", "<p>Gladiator : Ring</p>", 
                                "<p>Experimenter : Result</p>", "<p>Arbitrator : Deadlock</p>"],
                    options_hi: ["<p>जज : कोर्टरूम</p>", "<p>पैशेवर लड़ाका : गोल अखाड़ा</p>",
                                "<p>प्रयोगकर्ता : परिणाम</p>", "<p>मध्यस्थ : गतिरोध</p>"],
                    solution_en: "<p>4.(a) As the Referee is responsible for maintaining rules during matches on field. Similarly the Judge is responsible for maintaining rules in the courtroom.</p>",
                    solution_hi: "<p>4.(a) जैसे मैदान पर मैच के दौरान नियमों को बनाए रखने की जिम्मेदारी रेफरी की होती है। इसी प्रकार जज कोर्टरूम में नियमों को बनाए रखने के लिए जिम्मेदार होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Which of the following numbers will replace the question mark (?) in the given series?<br>112, 113, 122, 147, 196, ?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>112, 113, 122, 147, 196, ?</p>",
                    options_en: ["<p>294</p>", "<p>253</p>", 
                                "<p>282</p>", "<p>277</p>"],
                    options_hi: ["<p>294</p>", "<p>253</p>",
                                "<p>282</p>", "<p>277</p>"],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173114861.png\" alt=\"rId6\" width=\"366\" height=\"130\"></p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173114861.png\" alt=\"rId6\" height=\"130\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>14 - 15 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 56 &times; 14 = ?</p>",
                    question_hi: "<p>6. यदि \'+\' और \' - \' को आपस में बदल दिया जाए तथा \'<math display=\"inline\"><mo>&#215;</mo></math>\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>14 - 15 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 56 &times; 14 = ?</p>",
                    options_en: ["<p>40</p>", "<p>30</p>", 
                                "<p>35</p>", "<p>45</p>"],
                    options_hi: ["<p>40</p>", "<p>30</p>",
                                "<p>35</p>", "<p>45</p>"],
                    solution_en: "<p>6.(a)<strong> Given :-</strong> 14 - 15 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 56 &times; 14<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>14 + 15 &times; 2 - 56 <math display=\"inline\"><mo>&#247;</mo></math> 14<br>14 + 30 - 4<br>44 - 4 = 40</p>",
                    solution_hi: "<p>6.(a) <strong>दिया गया :-</strong> 14 - 15 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 56 &times; 14<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>14 + 15 &times; 2 - 56 <math display=\"inline\"><mo>&#247;</mo></math> 14<br>14 + 30 - 4<br>44 - 4 = 40</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?<br>CEG, FHJ, IKM, LNP, ?</p>",
                    question_hi: "<p>7. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>CEG, FHJ, IKM, LNP, ?</p>",
                    options_en: ["<p>OQR</p>", "<p>OQS</p>", 
                                "<p>QTV</p>", "<p>QRS</p>"],
                    options_hi: ["<p>OQR</p>", "<p>OQS</p>",
                                "<p>QTV</p>", "<p>QRS</p>"],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173115521.png\" alt=\"rId7\" width=\"393\" height=\"120\"></p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173115521.png\" alt=\"rId7\" height=\"120\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>15 <math display=\"inline\"><mo>&#247;</mo></math> 4 - 128 +123 &times; 3 = ?</p>",
                    question_hi: "<p>8. यदि &lsquo;+&rsquo; और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>15 <math display=\"inline\"><mo>&#247;</mo></math> 4 - 128 +123 &times; 3 = ?</p>",
                    options_en: ["<p>167</p>", "<p>127</p>", 
                                "<p>187</p>", "<p>147</p>"],
                    options_hi: ["<p>167</p>", "<p>127</p>",
                                "<p>187</p>", "<p>147</p>"],
                    solution_en: "<p>8.(d) <strong>Given :- </strong>15 <math display=\"inline\"><mo>&#247;</mo></math> 4 - 128 + 123 &times; 3 <br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>15 &times; 4 + 128 - 123 <math display=\"inline\"><mo>&#247;</mo></math> 3<br>60 + 128 - 41 <br>188 - 41= 147</p>",
                    solution_hi: "<p>8.(d) <strong>दिया गया :-</strong> 15<math display=\"inline\"><mo>&#247;</mo></math> 4 - 128 + 123 &times; 3<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>15 &times; 4 + 128 - 123 <math display=\"inline\"><mo>&#247;</mo></math> 3<br>60 + 128 - 41 <br>188 - 41= 147</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. How many rectangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173115708.png\" alt=\"rId8\" width=\"347\" height=\"125\"></p>",
                    question_hi: "<p>9. दी गई आकृति में कितने आयत हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173115708.png\" alt=\"rId8\" height=\"125\"></p>",
                    options_en: ["<p>11</p>", "<p>9</p>", 
                                "<p>8</p>", "<p>10</p>"],
                    options_hi: ["<p>11</p>", "<p>9</p>",
                                "<p>8</p>", "<p>10</p>"],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116077.png\" alt=\"rId9\" width=\"395\" height=\"135\"><br>There are 10 rectangle<br>ABDC, IHGF, NOED, OPFE, NPFD, JKLM, MLON, JKON, MLED, JKED</p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116077.png\" alt=\"rId9\" width=\"395\" height=\"135\"><br>10 आयत हैं<br>ABDC, IHGF, NOED, OPFE, NPFD, JKLM, MLON, JKON, MLED, JKED</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Based on the alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group?<br>(Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>10. अंग्रेेजी वर्णमाला क्रम केआधार पर, निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>(ध्यान दें: असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>TVY</p>", "<p>HJL</p>", 
                                "<p>PRT</p>", "<p>RTV</p>"],
                    options_hi: ["<p>TVY</p>", "<p>HJL</p>",
                                "<p>PRT</p>", "<p>RTV</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116352.png\" alt=\"rId10\" height=\"115\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116557.png\" alt=\"rId11\" height=\"115\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116760.png\" alt=\"rId12\" height=\"115\"></p>\n<p>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116949.png\" alt=\"rId13\" height=\"115\"></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116352.png\" alt=\"rId10\" height=\"115\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116557.png\" alt=\"rId11\" height=\"115\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116760.png\" alt=\"rId12\" height=\"115\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173116949.png\" alt=\"rId13\" height=\"115\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117100.png\" alt=\"rId14\" width=\"182\" height=\"105\"></p>",
                    question_hi: "<p>11. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) उसके भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117100.png\" alt=\"rId14\" height=\"105\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117223.png\" alt=\"rId15\" width=\"143\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117340.png\" alt=\"rId16\" height=\"110\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117451.png\" alt=\"rId17\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117555.png\" alt=\"rId18\" height=\"110\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117223.png\" alt=\"rId15\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117340.png\" alt=\"rId16\" height=\"110\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117451.png\" alt=\"rId17\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117555.png\" alt=\"rId18\" height=\"110\"></p>"],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117738.png\" alt=\"rId19\" height=\"135\"></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117738.png\" alt=\"rId19\" height=\"135\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117848.png\" alt=\"rId20\" width=\"179\" height=\"135\"></p>",
                    question_hi: "<p>12. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117848.png\" alt=\"rId20\" height=\"135\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117976.png\" alt=\"rId21\" width=\"176\" height=\"25\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118090.png\" alt=\"rId22\" width=\"182\" height=\"25\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118294.png\" alt=\"rId23\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118421.png\" alt=\"rId24\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173117976.png\" alt=\"rId21\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118090.png\" alt=\"rId22\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118294.png\" alt=\"rId23\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118421.png\" alt=\"rId24\"></p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118294.png\" alt=\"rId23\"></p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118294.png\" alt=\"rId23\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. What should come in place of the question mark (?) in the given series ? <br>3, 7, 15, 31, 63, ?</p>",
                    question_hi: "<p>13. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>3, 7, 15, 31, 63, ?</p>",
                    options_en: ["<p>126</p>", "<p>127</p>", 
                                "<p>128</p>", "<p>125</p>"],
                    options_hi: ["<p>126</p>", "<p>127</p>",
                                "<p>128</p>", "<p>125</p>"],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118797.png\" alt=\"rId25\" height=\"120\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118797.png\" alt=\"rId25\" height=\"120\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six letters A, R, T, W, Y and Z are written on different faces of a dice. Two positions of this dice are shown in the figure, Which is the letter on the face opposite to the face containing A?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118949.png\" alt=\"rId26\" width=\"250\" height=\"125\"></p>",
                    question_hi: "<p>14. एक पासे के अलग-अलग फलकों पर छः अक्षर A, R, T, W, Y और Z अंकित हैं। निम्न आकृति में इसी पासे की दो अलग-अलग स्थितियां दर्शाई गई हैं। A अक्षर वाले फलक के विपरीत फलक पर कौन-सा अक्षर है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173118949.png\" alt=\"rId26\" height=\"125\"></p>",
                    options_en: ["<p>Y</p>", "<p>R</p>", 
                                "<p>W</p>", "<p>Z</p>"],
                    options_hi: ["<p>Y</p>", "<p>R</p>",
                                "<p>W</p>", "<p>Z</p>"],
                    solution_en: "<p>14.(a) From the two dice the opposite face are <br>A &harr; Y, R &harr; T</p>",
                    solution_hi: "<p>14.(a) दोनों पासों से विपरीत फलक हैं<br>A &harr; Y, R &harr; T</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. &lsquo;T + W&rsquo; means &lsquo;T is the father of W&rsquo;<br>&lsquo;T &ndash; W&rsquo; means &lsquo;T is the wife of W&rsquo;<br>&lsquo;T &times; W&rsquo; means &lsquo;T is the brother of W&rsquo;<br>&lsquo;T &divide; W&rsquo; means &lsquo;T is the daughter of W&rsquo;.<br>What does &lsquo;S + T &divide; U&rsquo; mean ?</p>",
                    question_hi: "<p>15. T + W\' का अर्थ है \'T, W का पिता है\'<br>\'T - W\' का अर्थ है \'T, W की पत्नी है\'<br>\'T &times; W\' का अर्थ है \'T, W का भाई है\'<br>\'T &divide; W\' का अर्थ है \'T, W की पुत्री है\'।<br>&lsquo;S + T &divide; U&rsquo; का क्या अर्थ है ?</p>",
                    options_en: ["<p>S is the brother of U</p>", "<p>S is the husband of U</p>", 
                                "<p>S is the son of U</p>", "<p>S is the uncle of U</p>"],
                    options_hi: ["<p>S, U का भाई है</p>", "<p>S, U का पति है</p>",
                                "<p>S, U का पुत्र है</p>", "<p>S, U का अंकल है</p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173119208.png\" alt=\"rId27\" height=\"175\"><br>S is the husband of U.</p>",
                    solution_hi: "<p>15.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173119208.png\" alt=\"rId27\" height=\"175\"><br>S, U का पति है.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some sweaters are jackets.<br>Some jackets are pullovers.<br>No pullover is a glove.<br><strong>Conclusions:</strong><br>(I) No jacket is a glove.<br>(II) Some sweaters are pullovers.</p>",
                    question_hi: "<p>16. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>कुछ स्वेटर, जैकेटे हैं।<br>कुछ जैकेटे, पुलओवर हैं।<br>कोई भी पुलओवर, दस्ताना नहीं है।<br><strong>निष्कर्ष:</strong><br>(I) कोई भी जैकेट, दस्ताना नहीं है।<br>(II) कुछ स्वेटर, पुलओवर हैं।</p>",
                    options_en: ["<p>Only conclusion I follows</p>", "<p>Both conclusions I and II follow</p>", 
                                "<p>None of the conclusions follow</p>", "<p>Only conclusion II follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I अनुसरण करता है</p>", "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>",
                                "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>", "<p>केवल निष्कर्ष II अनुसरण करता है</p>"],
                    solution_en: "<p>16.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173119790.png\" alt=\"rId28\" width=\"642\" height=\"95\"><br>None of the conclusions follow.</p>",
                    solution_hi: "<p>16.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173120685.png\" alt=\"rId29\" height=\"95\"><br>कोई भी निष्कर्ष अनुसरण नहीं करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173120932.png\" alt=\"rId30\" width=\"156\" height=\"125\"></p>",
                    question_hi: "<p>17. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) उसके भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173120932.png\" alt=\"rId30\" height=\"125\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121039.png\" alt=\"rId31\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121192.png\" alt=\"rId32\" height=\"110\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121368.png\" alt=\"rId33\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121495.png\" alt=\"rId34\" height=\"110\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121039.png\" alt=\"rId31\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121192.png\" alt=\"rId32\" height=\"110\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121368.png\" alt=\"rId33\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121495.png\" alt=\"rId34\" height=\"110\"></p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121908.png\" alt=\"rId35\" height=\"140\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173121908.png\" alt=\"rId35\" height=\"140\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Identify the figure in the options that when put in place of the question mark (?) will logically complete the series ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122115.png\" alt=\"rId36\" width=\"428\" height=\"100\"></p>",
                    question_hi: "<p>18. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122115.png\" alt=\"rId36\" height=\"100\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122279.png\" alt=\"rId37\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122442.png\" alt=\"rId38\" height=\"105\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122593.png\" alt=\"rId39\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122780.png\" alt=\"rId40\" height=\"105\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122279.png\" alt=\"rId37\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122442.png\" alt=\"rId38\" height=\"105\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122593.png\" alt=\"rId39\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122780.png\" alt=\"rId40\" height=\"105\"></p>"],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122593.png\" alt=\"rId39\" height=\"115\"></p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173122593.png\" alt=\"rId39\" height=\"115\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. DFIK is related to EGJL in a certain way based on the English alphabetical order. In the same way, GILN is related to HJMO. To which of the following is MORT related to, following the same logic ?</p>",
                    question_hi: "<p>19. अंग्रेजी वर्णमाला क्रम के आधार पर DFIK, EGJL से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, GILN, HJMO से संबंधित है। समान तर्क का अनुसरण करते हुए, MORT निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>NPVS</p>", "<p>NQSU</p>", 
                                "<p>NPSU</p>", "<p>NPSV</p>"],
                    options_hi: ["<p>NPVS</p>", "<p>NQSU</p>",
                                "<p>NPSU</p>", "<p>NPSV</p>"],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173123050.png\" alt=\"rId41\" height=\"105\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173123377.png\" alt=\"rId42\" height=\"105\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173123812.png\" alt=\"rId43\" height=\"105\"></p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173123050.png\" alt=\"rId41\" height=\"105\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173123377.png\" alt=\"rId42\" height=\"105\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173123812.png\" alt=\"rId43\" height=\"105\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124021.png\" alt=\"rId44\"></p>",
                    question_hi: "<p>20. नीचे दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124021.png\" alt=\"rId44\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124135.png\" alt=\"rId45\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124245.png\" alt=\"rId46\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124360.png\" alt=\"rId47\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124487.png\" alt=\"rId48\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124135.png\" alt=\"rId45\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124245.png\" alt=\"rId46\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124360.png\" alt=\"rId47\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124487.png\" alt=\"rId48\"></p>"],
                    solution_en: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124360.png\" alt=\"rId47\"></p>",
                    solution_hi: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173124360.png\" alt=\"rId47\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &rsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>16 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 24 &times; 6 + 3 = ?</p>",
                    question_hi: "<p>21. यदि &lsquo;+&rsquo; और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &rsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा <br>16 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 24 &times; 6 + 3 = ?</p>",
                    options_en: ["<p>85</p>", "<p>82</p>", 
                                "<p>81</p>", "<p>80</p>"],
                    options_hi: ["<p>85</p>", "<p>82</p>",
                                "<p>81</p>", "<p>80</p>"],
                    solution_en: "<p>21.(c) <strong>Given :- </strong>16 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 24 &times; 6 + 3 <br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>16 &times; 5 + 24 <math display=\"inline\"><mo>&#247;</mo></math> 6 - 3<br>80 + 4 - 3 = 81</p>",
                    solution_hi: "<p>21.(c) <strong>दिया गया :-</strong> 16 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 24 &times; 6 + 3<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>16 &times; 5 + 24 <math display=\"inline\"><mo>&#247;</mo></math> 6 - 3<br>80 + 4 - 3 = 81</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. 33 is related to 726 by certain logic. Following the same logic, 43 is related to 946. To which of the following is 53 related, following the same logic ?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>22. 33 का संबंध एक निश्चित तर्क से 726 से है। उसी तर्क का अनुसरण करते हुए, 43 का संबंध 946 से है। उसी तर्क का अनुसरण करते हुए, 53 का संबंध निम्नलिखित में से किससे है?<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>846</p>", "<p>980</p>", 
                                "<p>1060</p>", "<p>1166</p>"],
                    options_hi: ["<p>846</p>", "<p>980</p>",
                                "<p>1060</p>", "<p>1166</p>"],
                    solution_en: "<p>22.(d) <strong>Logic :-</strong> (1st number &times; 22) = 2nd number<br>(33 , 726) :- (33 &times; 22) = 726<br>(43, 946) :- (43 &times; 22) = 946<br>Similarly,<br>(53, 1166) :- (53 &times; 22) = 1166</p>",
                    solution_hi: "<p>22.(d) <strong>तर्क :-</strong> (पहली संख्या &times; 22) = दूसरी संख्या<br>(33 , 726) :- (33 &times; 22) = 726<br>(43, 946) :- (43 &times; 22) = 946<br>इसी प्रकार,<br>(53, 1166) :- (53 &times; 22) = 1166</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. The position of how many letters will remain unchanged if each of the letters in the word WANDERLUST is arranged from left to right in alphabetical order ?</p>",
                    question_hi: "<p>23. यदि शब्द WANDERLUST के प्रत्येक अक्षर को बाएं से दाएं वर्णमाला क्रम में व्यवस्थित किया जाता है, तो कितने अक्षरों का स्थान परिवर्तित नहीं होगा ?</p>",
                    options_en: ["<p>Three</p>", "<p>One</p>", 
                                "<p>None</p>", "<p>Two</p>"],
                    options_hi: ["<p>तीन</p>", "<p>एक</p>",
                                "<p>किसी का भी नहीं</p>", "<p>दो</p>"],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173125245.png\" alt=\"rId49\" height=\"115\"><br>The position of only one letter remains unchanged.</p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173125245.png\" alt=\"rId49\" height=\"115\"><br>केवल एक अक्षर का स्थान अपरिवर्तित रहता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?<br>RPM, USP, XVS, AYV, ?</p>",
                    question_hi: "<p>24. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>RPM, USP, XVS, AYV, ?</p>",
                    options_en: ["<p>BYD</p>", "<p>DYB</p>", 
                                "<p>BDY</p>", "<p>DBY</p>"],
                    options_hi: ["<p>BYD</p>", "<p>DYB</p>",
                                "<p>BDY</p>", "<p>DBY</p>"],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173127659.png\" alt=\"rId50\" height=\"115\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734173127659.png\" alt=\"rId50\" height=\"115\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. If 10 days before was a Sunday and 6 days after would be a Tuesday, then what day is it today ?</p>",
                    question_hi: "<p>25. यदि आज से 10 दिन पहले रविवार था और 6 दिन बाद मंगलवार होगा, तो आज कौन-सा दिन है?</p>",
                    options_en: ["<p>Wednesday</p>", "<p>Monday</p>", 
                                "<p>Tuesday</p>", "<p>Friday</p>"],
                    options_hi: ["<p>बुधवार</p>", "<p>सोमवार</p>",
                                "<p>मंगलवार</p>", "<p>शुक्रवार</p>"],
                    solution_en: "<p>25.(a) 6 days before Tuesday = Wednesday. 10 days before Sunday will also be Wednesday.The correct answer is Wednesday.</p>",
                    solution_hi: "<p>25.(a) मंगलवार से 6 दिन पहले = बुधवार. रविवार से 10 दिन पहले भी बुधवार होगा। सही उत्तर बुधवार है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>