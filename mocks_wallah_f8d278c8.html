<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. What would be the word on the opposite side of Eat if the given sheet is folded to form a cube?<br><strong id=\"docs-internal-guid-f7000918-7fff-c460-15b5-5739a1da04a7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddgNX-icm4LelaxvuU93bLG3AcPjXf9eUir7q6sQOEyEheQ6To269_UWgrxzrFbVbbU_9Wo1iIvQMaK1WR1Z1EdpaUHKeneZ0imQ0JKVqvF-0WIcmhNMHtRX5kyyy8B4Q0Dk5xCQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"154\" height=\"189\"></strong></p>",
                    question_hi: "<p>1. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो Eat\' शब्द के विपरीत फलक पर कौन-सा शब्द होगा?<br><strong id=\"docs-internal-guid-f7000918-7fff-c460-15b5-5739a1da04a7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddgNX-icm4LelaxvuU93bLG3AcPjXf9eUir7q6sQOEyEheQ6To269_UWgrxzrFbVbbU_9Wo1iIvQMaK1WR1Z1EdpaUHKeneZ0imQ0JKVqvF-0WIcmhNMHtRX5kyyy8B4Q0Dk5xCQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"154\" height=\"189\"></strong></p>",
                    options_en: ["<p>Pray</p>", "<p>Fly</p>", 
                                "<p>Play</p>", "<p>Cry</p>"],
                    options_hi: ["<p>Pray</p>", "<p>Fly</p>",
                                "<p>Play</p>", "<p>Cry</p>"],
                    solution_en: "<p>1.(b)<br><strong id=\"docs-internal-guid-764a94f4-7fff-736b-d22b-************\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUkSCet5bHqm-q1dF6HtsIkNNsQtryZxGrzE5wgJ2qVP6N0bWnYoHkxPS3Zn_2o-nyxbX-Cc3Jhd3HH0YacgBbrjp_Wv15FFA1BcVnMXNv_aZw2ZLVTKyBJBD--R23cFJQl7-rmg?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"150\" height=\"187\"></strong><br>The opposite face are <br>Eat &harr; Fly , Run &harr; Pray , Play &harr; Cry.</p>",
                    solution_hi: "<p>1.(b)<br><strong id=\"docs-internal-guid-764a94f4-7fff-736b-d22b-************\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUkSCet5bHqm-q1dF6HtsIkNNsQtryZxGrzE5wgJ2qVP6N0bWnYoHkxPS3Zn_2o-nyxbX-Cc3Jhd3HH0YacgBbrjp_Wv15FFA1BcVnMXNv_aZw2ZLVTKyBJBD--R23cFJQl7-rmg?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"150\" height=\"187\"></strong><br>विपरीत फलक हैं<br>Eat &harr; Fly , Run &harr; Pray , Play &harr; Cry.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. What would be the symbol on the opposite side of &lsquo;*&rsquo; if the given sheet is folded to form a cube?<br><strong id=\"docs-internal-guid-579a7e2d-7fff-e2ce-4c07-056eccc4388d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTz0dxiNuyRp0y-Whs7rZdP3LrvOpniAs8MPBttzzcbeNQeyY-9in2pNg1Rct5qxKJAElWgbqchjZDDh7v-WGtBsqOTSHv6ER6ePjlZ1XXKDjji5TVzPIYEQytxcfjK7lWa2fTlQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"133\" height=\"155\"></strong></p>",
                    question_hi: "<p>2. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो &lsquo;*&rsquo; के विपरीत फलक पर कौन-सा चिन्ह होगा?<br><strong id=\"docs-internal-guid-579a7e2d-7fff-e2ce-4c07-056eccc4388d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTz0dxiNuyRp0y-Whs7rZdP3LrvOpniAs8MPBttzzcbeNQeyY-9in2pNg1Rct5qxKJAElWgbqchjZDDh7v-WGtBsqOTSHv6ER6ePjlZ1XXKDjji5TVzPIYEQytxcfjK7lWa2fTlQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"133\" height=\"155\"></strong></p>",
                    options_en: ["<p>&yen;</p>", "<p>%</p>", 
                                "<p>#</p>", "<p>$</p>"],
                    options_hi: ["<p>&yen;</p>", "<p>%</p>",
                                "<p>#</p>", "<p>$</p>"],
                    solution_en: "<p>2.(b)<br><strong id=\"docs-internal-guid-a4d3a668-7fff-4862-6e78-6df95740ab71\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdtXmcbxBjmVn9O1Kn42RrvMdFAr6Cu56ufK495CqY_kUwzo2ybchj2VFo1pEvzxIUaN1FnEqSyDQNTSTK-H582MBBjQr4v9VvqjznTkdqGEoC9SFYN-uIsmVFXpxJq_nZxyqgfnA?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"148\" height=\"173\"></strong><br>The opposite face are :- @ &harr; $ , &yen; &harr; # , * &harr; %</p>",
                    solution_hi: "<p>2.(b) <br><strong id=\"docs-internal-guid-a4d3a668-7fff-4862-6e78-6df95740ab71\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdtXmcbxBjmVn9O1Kn42RrvMdFAr6Cu56ufK495CqY_kUwzo2ybchj2VFo1pEvzxIUaN1FnEqSyDQNTSTK-H582MBBjQr4v9VvqjznTkdqGEoC9SFYN-uIsmVFXpxJq_nZxyqgfnA?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"148\" height=\"173\"></strong><br>विपरीत फलक हैं:- @ &harr; $ , &yen; &harr; # , * &harr; %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. What would be the symbol on the opposite side of &lsquo;&divide;&rsquo; if the given sheet is folded to form a cube ?<br><strong id=\"docs-internal-guid-2e7f32c6-7fff-5b0e-0cf6-0c3096b15f4d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcwa9i6xJOIsycEvATPEs8Xf_EjnwoSpSeWSXw02qCrlUWk0HlU0CUqR1Yhgh6JyqyM0n-6pdne-SVti_CSVGp9wxdaJz4iWWGCuIQ34DDHcXHmYF6KZDg6ayCci1woMknzMsB-?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"125\" height=\"155\"></strong></p>",
                    question_hi: "<p>3. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो &lsquo;&divide;&rsquo; के विपरीत फलक पर कौन-सा चिन्ह होगा?<br><strong id=\"docs-internal-guid-2e7f32c6-7fff-5b0e-0cf6-0c3096b15f4d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcwa9i6xJOIsycEvATPEs8Xf_EjnwoSpSeWSXw02qCrlUWk0HlU0CUqR1Yhgh6JyqyM0n-6pdne-SVti_CSVGp9wxdaJz4iWWGCuIQ34DDHcXHmYF6KZDg6ayCci1woMknzMsB-?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"125\" height=\"155\"></strong></p>",
                    options_en: ["<p>*</p>", "<p>+</p>", 
                                "<p>%</p>", "<p>&ne;</p>"],
                    options_hi: ["<p>*</p>", "<p>+</p>",
                                "<p>%</p>", "<p>&ne;</p>"],
                    solution_en: "<p>3.(a)<br><strong id=\"docs-internal-guid-8bf466cd-7fff-f7f0-1119-526ff4d850ed\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXflaFJg3fI9ItulAiijBiOznd0UqMmV89WNEwbWfXpfa_9lgZBT-E6p4xnGoVFGUlk9bztQwMYDZjCu5vkE6bfJwW4UU8zWXyLrRfjENd0Um3jWJGYpUGL_SSWV2WOpB8-vyvIXtw?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"135\" height=\"162\"></strong><br>The opposite face are % &harr; @ , <math display=\"inline\"><mo>&#247;</mo></math> &harr; * , + &harr; &ne;</p>",
                    solution_hi: "<p>3.(a)<br><strong id=\"docs-internal-guid-8bf466cd-7fff-f7f0-1119-526ff4d850ed\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXflaFJg3fI9ItulAiijBiOznd0UqMmV89WNEwbWfXpfa_9lgZBT-E6p4xnGoVFGUlk9bztQwMYDZjCu5vkE6bfJwW4UU8zWXyLrRfjENd0Um3jWJGYpUGL_SSWV2WOpB8-vyvIXtw?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"135\" height=\"162\"></strong><br>विपरीत फलक हैं % &harr; @ , <math display=\"inline\"><mo>&#247;</mo></math> &harr; * , + &harr; &ne;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. What would be the symbol on the opposite side of \'&le;\' if the given sheet is folded to form a cube?<br><strong id=\"docs-internal-guid-b801540f-7fff-854d-98e9-43f1fb63c1e5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddYex3B05MNNbQv9SW7W22s7rcIXknAsymQ4WF-KQrjKX9yTrHGAyfPm85fAwjepb3Q3mVlUQBykaVflGpeXH_5YLXUJPst4XW_SN3xT0LNC9HTUxru0cQS0effzAAzBp3wsOiXQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"116\" height=\"136\"></strong></p>",
                    question_hi: "<p>4. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो &lsquo;&le;&rsquo; के विपरीत फलक पर कौन-सा चिन्ह होगा?<br><strong id=\"docs-internal-guid-b801540f-7fff-854d-98e9-43f1fb63c1e5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddYex3B05MNNbQv9SW7W22s7rcIXknAsymQ4WF-KQrjKX9yTrHGAyfPm85fAwjepb3Q3mVlUQBykaVflGpeXH_5YLXUJPst4XW_SN3xT0LNC9HTUxru0cQS0effzAAzBp3wsOiXQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"116\" height=\"136\"></strong></p>",
                    options_en: ["<p>&ge;</p>", "<p>*</p>", 
                                "<p>&divide;</p>", "<p>+</p>"],
                    options_hi: ["<p>&ge;</p>", "<p>*</p>",
                                "<p>&divide;</p>", "<p>+</p>"],
                    solution_en: "<p>4.(c)<br><strong id=\"docs-internal-guid-dbf177ac-7fff-f01d-ba2b-5e6d266326db\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJjM3kEwb6hOFxTehMdduEXQcqDac9iuotn2gDowo-Zgv2C42twv3wTU_hUDkLDrnjxNFgdjamf6LwXHnCoMD-ACiFSrN46E_CXhRX53N4qFwPybB8wXn9vyBPHLg7-x0zlnorog?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"136\" height=\"185\"></strong><br>The opposite face are * &harr; <math display=\"inline\"><mo>&#8800;</mo></math> , &le; &harr; &divide; , + &harr; &ge;</p>",
                    solution_hi: "<p>4.(c)<br><strong id=\"docs-internal-guid-dbf177ac-7fff-f01d-ba2b-5e6d266326db\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJjM3kEwb6hOFxTehMdduEXQcqDac9iuotn2gDowo-Zgv2C42twv3wTU_hUDkLDrnjxNFgdjamf6LwXHnCoMD-ACiFSrN46E_CXhRX53N4qFwPybB8wXn9vyBPHLg7-x0zlnorog?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"136\" height=\"185\"></strong><br>विपरीत फलक * &harr; <math display=\"inline\"><mo>&#8800;</mo></math> , &le; &harr; &divide; , + &harr; &ge; हैं</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. What would be the symbol on the opposite side of &lsquo;*&rsquo; if the given sheet is folded to form a cube ?<br><strong id=\"docs-internal-guid-95908f7e-7fff-0391-5d98-36ee3637ee29\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfwobG5M4INVAXawq21DmNxW1KpnnUFjz_SxV241xMr5JoXyVysayqYt9hGIYWgkVeXdNMtPJxKVbsWZR5IJGGhFl7k6AlHJdAUR0N-FFzytUpczZUkpQNeo02GAPtI82oHSsCTfA?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"123\" height=\"161\"></strong></p>",
                    question_hi: "<p>5. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो &lsquo;*&rsquo; के विपरीत फलक पर कौन-सा चिन्ह होगा ?<br><strong id=\"docs-internal-guid-95908f7e-7fff-0391-5d98-36ee3637ee29\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfwobG5M4INVAXawq21DmNxW1KpnnUFjz_SxV241xMr5JoXyVysayqYt9hGIYWgkVeXdNMtPJxKVbsWZR5IJGGhFl7k6AlHJdAUR0N-FFzytUpczZUkpQNeo02GAPtI82oHSsCTfA?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"123\" height=\"161\"></strong></p>",
                    options_en: ["<p>^</p>", "<p>@</p>", 
                                "<p>}</p>", "<p>&amp;</p>"],
                    options_hi: ["<p>^</p>", "<p>@</p>",
                                "<p>}</p>", "<p>&amp;</p>"],
                    solution_en: "<p>5.(c)<br><strong id=\"docs-internal-guid-848f9767-7fff-86ae-bb97-2e7ec23e047f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcNlgIWy6dUjuj-NRxx1dBGUDWkFHbzOl4gwKCOOfC8dT5Gl9E_1Vek_LRSqCGEX1JkUIh_gtt1E27XS8F2MYlWSAoZt__6HWrHRh4PaGiU3VGucGR04siRt6SbXcxyWMYC6KxM0Q?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"118\" height=\"143\"></strong><br><strong>The opposite face are</strong> :- * &harr; } , # &harr; ^ , &amp; &harr; @</p>",
                    solution_hi: "<p>5.(c) <br><strong id=\"docs-internal-guid-848f9767-7fff-86ae-bb97-2e7ec23e047f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcNlgIWy6dUjuj-NRxx1dBGUDWkFHbzOl4gwKCOOfC8dT5Gl9E_1Vek_LRSqCGEX1JkUIh_gtt1E27XS8F2MYlWSAoZt__6HWrHRh4PaGiU3VGucGR04siRt6SbXcxyWMYC6KxM0Q?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"118\" height=\"143\"></strong><br><strong>विपरीत फलक हैं :-</strong> * &harr; } , # &harr; ^ , &amp; &harr; @</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What would be the Roman numeral on the opposite side of \'VI\' if the given sheet is folded to form a cube ?<br><strong id=\"docs-internal-guid-4f6ffa70-7fff-cae9-d582-b7e6f20d7ecb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd8YtlFwW_vmFNTp4ZdqUuEot1v0AYotV-5GksvmJDFnNXfx86eX6-qVAn2EOYQhtvvmHL8mfAbmbIKSjjlL58DI5FT8KAPU2_SgSCsa7tf1MtSzsRy84cNwyZFzPze2Tff_woXOQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"89\" height=\"174\"></strong></p>",
                    question_hi: "<p>6. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो &rsquo;VI\' के विपरीत फलक पर कौन-सी रोमन संख्या होगी ?<br><strong id=\"docs-internal-guid-4f6ffa70-7fff-cae9-d582-b7e6f20d7ecb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd8YtlFwW_vmFNTp4ZdqUuEot1v0AYotV-5GksvmJDFnNXfx86eX6-qVAn2EOYQhtvvmHL8mfAbmbIKSjjlL58DI5FT8KAPU2_SgSCsa7tf1MtSzsRy84cNwyZFzPze2Tff_woXOQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"89\" height=\"174\"></strong></p>",
                    options_en: ["<p>VII</p>", "<p>V</p>", 
                                "<p>X</p>", "<p>VIII</p>"],
                    options_hi: ["<p>VII</p>", "<p>V</p>",
                                "<p>X</p>", "<p>VIII</p>"],
                    solution_en: "<p>6.(d)<br><strong id=\"docs-internal-guid-93bb10de-7fff-25c9-3baf-85dc6c98938a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXci11Hk4A6KEgKGsdd6gpKPMzH_wfudSSPmvkagaT_z6-icyvuTciTDMLnkpjtWUrb5E9s-GapSpea2si1uv7zDQOjnIIVP6GW3M8nTIsnkJJ93RGv6INPeRSa-sgg7Dlw9KCxi3A?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"112\" height=\"156\"></strong><br><strong>The opposite faces are</strong> VII &harr; V , X &harr; IX , VI &harr; VIII</p>",
                    solution_hi: "<p>6.(d)<br><strong id=\"docs-internal-guid-93bb10de-7fff-25c9-3baf-85dc6c98938a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXci11Hk4A6KEgKGsdd6gpKPMzH_wfudSSPmvkagaT_z6-icyvuTciTDMLnkpjtWUrb5E9s-GapSpea2si1uv7zDQOjnIIVP6GW3M8nTIsnkJJ93RGv6INPeRSa-sgg7Dlw9KCxi3A?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"112\" height=\"156\"></strong><br><strong>विपरीत फलक हैं</strong> VII &harr; V , X &harr; IX , VI &harr; VIII</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What would be the number on the opposite side of \'8\' if the given sheet is folded to form a cube?<br><strong id=\"docs-internal-guid-a2d3bb2d-7fff-b9fb-6682-a6c65f2e6b04\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXchMsE6xJDdcDmzk1Vzm3HyvSN0-MB4y4zohJDTsxuu5Jp_NtoMaGbVisbOWL5ST2_2YtbsjPbqKTG7pJQmuBHYWcusKLvSGg_N6qbqGSL1mLumXFsbU1OX-dxO6td0hBSqrUGBdw?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"115\" height=\"135\"></strong></p>",
                    question_hi: "<p>7. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो 8\' के विपरीत फ़्लक पर कौन-सी संख्या होगी?<br><strong id=\"docs-internal-guid-a2d3bb2d-7fff-b9fb-6682-a6c65f2e6b04\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXchMsE6xJDdcDmzk1Vzm3HyvSN0-MB4y4zohJDTsxuu5Jp_NtoMaGbVisbOWL5ST2_2YtbsjPbqKTG7pJQmuBHYWcusKLvSGg_N6qbqGSL1mLumXFsbU1OX-dxO6td0hBSqrUGBdw?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"115\" height=\"135\"></strong></p>",
                    options_en: ["<p>10</p>", "<p>6</p>", 
                                "<p>7</p>", "<p>5</p>"],
                    options_hi: ["<p>10</p>", "<p>6</p>",
                                "<p>7</p>", "<p>5</p>"],
                    solution_en: "<p>7.(c)<br><strong id=\"docs-internal-guid-b2d53436-7fff-2d3e-7768-2c2a0aa982e2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFpfbx2sW1QCbdzF6p_YsyvEMlJOGt1Pzw8ISCKXJLk5OblgI1wDgGAhaHIR-2pmlqZtuCXYNezoCps5sGSmgZH8yXoHnHAFZkslMQQk6nBhPwICbG95PynvGBWPlsbf7oTSjC1g?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"146\" height=\"151\"></strong><br><strong>The opposite face are</strong> :- 6 &harr; 5 , 8 &harr; 7 , 9 &harr; 10</p>",
                    solution_hi: "<p>7.(c)<br><strong id=\"docs-internal-guid-b2d53436-7fff-2d3e-7768-2c2a0aa982e2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFpfbx2sW1QCbdzF6p_YsyvEMlJOGt1Pzw8ISCKXJLk5OblgI1wDgGAhaHIR-2pmlqZtuCXYNezoCps5sGSmgZH8yXoHnHAFZkslMQQk6nBhPwICbG95PynvGBWPlsbf7oTSjC1g?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"146\" height=\"151\"></strong><br><strong>विपरीत फलक हैं:- </strong>6 &harr; 5 , 8 &harr; 7 , 9 &harr; 10</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What would be the word on the opposite side of \'Feb\' if the given sheet is folded to form a cube ?<br><strong id=\"docs-internal-guid-6864875c-7fff-42d7-feaa-2f0527064f04\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeTfF7oSCbO_C3zgd08ha3ekQ4RZa131ztArkvouO0JGhyV2RaE8-xhWrNhlqX-D3D3sxKGdFvbI_88OY5IrFcixGi5mx7mLT7yxOoTM2JF9OCAQLcrEw7WNeIOT0Z4YThV-M8sWg?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"151\" height=\"182\"></strong></p>",
                    question_hi: "<p>8. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो Feb के विपरीत फलक पर कौन-सा शब्द होगा? <br><strong id=\"docs-internal-guid-6864875c-7fff-42d7-feaa-2f0527064f04\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeTfF7oSCbO_C3zgd08ha3ekQ4RZa131ztArkvouO0JGhyV2RaE8-xhWrNhlqX-D3D3sxKGdFvbI_88OY5IrFcixGi5mx7mLT7yxOoTM2JF9OCAQLcrEw7WNeIOT0Z4YThV-M8sWg?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"151\" height=\"182\"></strong></p>",
                    options_en: ["<p>Jan</p>", "<p>June</p>", 
                                "<p>May</p>", "<p>Mar</p>"],
                    options_hi: ["<p>Jan</p>", "<p>June</p>",
                                "<p>May</p>", "<p>Mar</p>"],
                    solution_en: "<p>8.(c)<br><strong id=\"docs-internal-guid-431daeae-7fff-3589-d4ce-0f2bb12d0a9a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfR_CKjQjZIWQJEPgztnXPekAATarwrJsiH5QnHD6idLQuC2j9vGUQkmcuAQUMSSreaTv4prx-qY6SIBto36_jGSZzr9khd32wrs4tOaifF6zEBgxaUYT2ovMLIgypdb7MCuljz?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"164\" height=\"206\"></strong><br><strong>The opposite faces are </strong>:- March &harr; April, May &harr; February , Jan &harr; June.</p>",
                    solution_hi: "<p>8.(c) <br><strong id=\"docs-internal-guid-431daeae-7fff-3589-d4ce-0f2bb12d0a9a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfR_CKjQjZIWQJEPgztnXPekAATarwrJsiH5QnHD6idLQuC2j9vGUQkmcuAQUMSSreaTv4prx-qY6SIBto36_jGSZzr9khd32wrs4tOaifF6zEBgxaUYT2ovMLIgypdb7MCuljz?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"164\" height=\"206\"></strong><br><strong>विपरीत फलक हैं:-</strong> March &harr; April, May &harr; February , Jan &harr; June.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What would be the word on the opposite side of \'Kiwi\' if the given sheet is folded to form a cube?<br><strong id=\"docs-internal-guid-c565f010-7fff-b3da-8d05-fd1931e30d61\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXctHRYn6IUhpN1xSN-15LUzxq1MofwOYhwXtpvMmsM0hh3A3n8fgVMPgSBykFghdkILzlJrkV5uJ3G7Mg0QsKlFI82pkzlY2svd_wlCmjsaHSZimfZpQ35ApZ8V5U5SZUgeLv8h?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"130\" height=\"154\"></strong></p>",
                    question_hi: "<p>9. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो Kiwi\' शब्द के विपरीत फलक पर कौन-सा शब्द होगा?<br><strong id=\"docs-internal-guid-c565f010-7fff-b3da-8d05-fd1931e30d61\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXctHRYn6IUhpN1xSN-15LUzxq1MofwOYhwXtpvMmsM0hh3A3n8fgVMPgSBykFghdkILzlJrkV5uJ3G7Mg0QsKlFI82pkzlY2svd_wlCmjsaHSZimfZpQ35ApZ8V5U5SZUgeLv8h?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"130\" height=\"154\"></strong></p>",
                    options_en: ["<p>Melon</p>", "<p>Plum</p>", 
                                "<p>Lime</p>", "<p>Mango</p>"],
                    options_hi: ["<p>Melon</p>", "<p>Plum</p>",
                                "<p>Lime</p>", "<p>Mango</p>"],
                    solution_en: "<p>9.(d)<br><strong id=\"docs-internal-guid-929288df-7fff-315a-16ff-c570ab47b519\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfrnGCS1pzN9ydEekljBsYKqL6m_BncrLOGKK50m7CRTU0InyJXWHbJLuyKqxX4_7HG2iCKcc8IoTVmboJvPt0NKvo4KoQFmHkwD4HKIHlL1qlzEpXKz3Rl3UOF29n8fOkwHkqloQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"137\" height=\"179\"></strong><br><strong>The opposite faces are :-</strong> Plum &harr; Pear, Mango &harr; Kiwi , Lime &harr; Melon</p>",
                    solution_hi: "<p>9.(d) <br><strong id=\"docs-internal-guid-929288df-7fff-315a-16ff-c570ab47b519\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfrnGCS1pzN9ydEekljBsYKqL6m_BncrLOGKK50m7CRTU0InyJXWHbJLuyKqxX4_7HG2iCKcc8IoTVmboJvPt0NKvo4KoQFmHkwD4HKIHlL1qlzEpXKz3Rl3UOF29n8fOkwHkqloQ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"137\" height=\"179\"></strong><br><strong>विपरीत फलक हैं:- </strong>Plum &harr; Pear, Mango &harr; Kiwi , Lime &harr; Melon</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What would be the symbol on the opposite side of \'@\' if the given sheet is folded to form a cube ?<br><strong id=\"docs-internal-guid-f734babb-7fff-3e82-fbd8-dda924661e15\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd1ifzJeXTzQYZTfuGToNze52xCt9NqLMFZefU33xB-KcI4yU-LjcPpI-sp9b-_Ism-Nnh2qpsY7EPJuJ6J_CbmfgZN2QHnuUkGKEBTKZgtZgLXuzD3gdekdrfvj0sSod2BfWxa6Q?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"106\" height=\"178\"></strong></p>",
                    question_hi: "<p>10. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो &lsquo;@\' के विपरीत फ़लक पर कौन-सा चिन्ह होगा ?<br><strong id=\"docs-internal-guid-f734babb-7fff-3e82-fbd8-dda924661e15\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd1ifzJeXTzQYZTfuGToNze52xCt9NqLMFZefU33xB-KcI4yU-LjcPpI-sp9b-_Ism-Nnh2qpsY7EPJuJ6J_CbmfgZN2QHnuUkGKEBTKZgtZgLXuzD3gdekdrfvj0sSod2BfWxa6Q?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"106\" height=\"178\"></strong></p>",
                    options_en: ["<p>#</p>", "<p>^</p>", 
                                "<p>*</p>", "<p>%</p>"],
                    options_hi: ["<p>#</p>", "<p>^</p>",
                                "<p>*</p>", "<p>%</p>"],
                    solution_en: "<p>10.(c)<br><strong id=\"docs-internal-guid-b2fc7e22-7fff-9b56-0b6e-dd38bde1e502\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdUIyIVeZonAkDB7MIQUM8U58d-ZY-yqYL7i6ZWiq_XcuxuKbp_QsTbEwhuTBd5yLWSx-r3Eiqb22FGZ0by2Zlk_xq3iQKwBBNuWUr8Tast0TmM8YJKp-08gIXKY94lcMFk4PUvfw?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"119\" height=\"167\"></strong><br><strong>The opposite faces are</strong> :- $ &harr; % , @ &harr; * , ^ &harr; #</p>",
                    solution_hi: "<p>10.(c)<br><strong id=\"docs-internal-guid-b2fc7e22-7fff-9b56-0b6e-dd38bde1e502\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdUIyIVeZonAkDB7MIQUM8U58d-ZY-yqYL7i6ZWiq_XcuxuKbp_QsTbEwhuTBd5yLWSx-r3Eiqb22FGZ0by2Zlk_xq3iQKwBBNuWUr8Tast0TmM8YJKp-08gIXKY94lcMFk4PUvfw?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"119\" height=\"167\"></strong><br><strong>विपरीत फलक हैं :</strong>- $ &harr; % , @ &harr; * , ^ &harr; #</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Six letters E, J, K, L, M and P are written on different faces of a dice. Two positions of this dice are shown in the figure, Which is the letter on the face opposite to the face containing M?<br><strong id=\"docs-internal-guid-66114090-7fff-4564-405a-0059e05d2706\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdR2ESck3KAt9h8MD0y57351XNPxyYzOgWkci214IU1qCOQF2SehRyCFvTDkZCKuaN3smtoebOr5DomM-UDi-S5Cj2Nq5Y2NDtcbC-oMvMfKo90FhL0KVqSZXxH0U6rsn9wVgZV1A?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"147\" height=\"74\"></strong></p>",
                    question_hi: "<p>11. एक पासे के अलग-अलग फलकों पर छः अक्षर - E, J, K, L, M और P अंकित है। निम्न आकृति में इसी पासे की दो अलग-अलग स्थितियां दर्शाई गई हैं। अक्षर M वाले फलक के विपरीत फलक पर कौन-सा अक्षर है?<br><strong id=\"docs-internal-guid-66114090-7fff-4564-405a-0059e05d2706\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdR2ESck3KAt9h8MD0y57351XNPxyYzOgWkci214IU1qCOQF2SehRyCFvTDkZCKuaN3smtoebOr5DomM-UDi-S5Cj2Nq5Y2NDtcbC-oMvMfKo90FhL0KVqSZXxH0U6rsn9wVgZV1A?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"147\" height=\"74\"></strong></p>",
                    options_en: [" J ", " P ", 
                                " K ", " E"],
                    options_hi: ["<p>J</p>", "<p>P</p>",
                                "<p>K</p>", "<p>E</p>"],
                    solution_en: "<p>11.(a) From the two dice the opposite face are :- P &harr; E , L &harr; K , J &harr; M</p>",
                    solution_hi: "<p>11.(a) दो पासों से विपरीत फलक हैं:- P &harr; E , L &harr; K , J &harr; M</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. What would be the number on the opposite side of &lsquo;16&rsquo; if the given sheet is folded to form a cube?<br><strong id=\"docs-internal-guid-981e6494-7fff-0af4-b66e-0717d0ef2f25\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdI5Cmlh_KZAiYoEc8FtPBIPxmRpIbObV8krbMQbBVMDm16uO-lkZxECCBtASOwxgC6QRmgXuPx48c3969BWiiuONvG7AKihwJHFJbrWEu5R7_9CqfgFuq35k_DmWzUjHmnLXTYmg?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"116\" height=\"142\"></strong></p>",
                    question_hi: "<p>12. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो &lsquo;16&rsquo; के विपरीत फलक पर कौन-सी संख्या होगी?<br><strong id=\"docs-internal-guid-981e6494-7fff-0af4-b66e-0717d0ef2f25\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdI5Cmlh_KZAiYoEc8FtPBIPxmRpIbObV8krbMQbBVMDm16uO-lkZxECCBtASOwxgC6QRmgXuPx48c3969BWiiuONvG7AKihwJHFJbrWEu5R7_9CqfgFuq35k_DmWzUjHmnLXTYmg?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"116\" height=\"142\"></strong></p>",
                    options_en: ["<p>11</p>", "<p>14</p>", 
                                "<p>15</p>", "<p>13</p>"],
                    options_hi: ["<p>11</p>", "<p>14</p>",
                                "<p>15</p>", "<p>13</p>"],
                    solution_en: "<p>12.(c)<br><strong id=\"docs-internal-guid-0ea5b000-7fff-ef9a-6009-6f6b58b20d49\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeWWTDUDkJ0WhjnZREKqXuBZNN6McauwQ7lh5c3sUKx7QqRAABYqa3sTKylLQ2M4Ne7iISYPR6noGfv9LjAv6yC4KqKW6ZTvx6vWgtk0ppYQ2Fxv6o3GpUjwdaHxfYUZ31LE2CB?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"133\" height=\"180\"></strong><br><strong>The opposite face are </strong>:- 11 &harr; 12 , 15 &harr; 16 , 13 &harr; 14</p>",
                    solution_hi: "<p>12.(c) <br><strong id=\"docs-internal-guid-0ea5b000-7fff-ef9a-6009-6f6b58b20d49\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeWWTDUDkJ0WhjnZREKqXuBZNN6McauwQ7lh5c3sUKx7QqRAABYqa3sTKylLQ2M4Ne7iISYPR6noGfv9LjAv6yC4KqKW6ZTvx6vWgtk0ppYQ2Fxv6o3GpUjwdaHxfYUZ31LE2CB?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"133\" height=\"180\"></strong><br><strong>विपरीत फलक हैं:-</strong> 11 &harr; 12 , 15 &harr; 16 , 13 &harr; 14</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. What would be the word on the opposite side of \'Green\' if the given sheet is folded to form a cube?<br><strong id=\"docs-internal-guid-dbea9519-7fff-cf77-2c2b-1986106eb2ad\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXemtOOoPFqqXZaC0CkeRiqp89Ojle1Qb_lsnE7VxtK_dPBqJ-4cSRQoJ4l9Gv35z57uKxWNg-StwzQThY0IaVQlByv8syDaUoZ1lKWNdDnGiJ2UhkBgdg3eOLj2mFSp3qtGLpJHqg?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"126\" height=\"165\"></strong></p>",
                    question_hi: "<p>13. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो \'Green\' शब्द के विपरीत फ़लक पर कौन-सा शब्द होगा?<br><strong id=\"docs-internal-guid-dbea9519-7fff-cf77-2c2b-1986106eb2ad\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXemtOOoPFqqXZaC0CkeRiqp89Ojle1Qb_lsnE7VxtK_dPBqJ-4cSRQoJ4l9Gv35z57uKxWNg-StwzQThY0IaVQlByv8syDaUoZ1lKWNdDnGiJ2UhkBgdg3eOLj2mFSp3qtGLpJHqg?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"126\" height=\"165\"></strong></p>",
                    options_en: ["<p>Pink</p>", "<p>Red</p>", 
                                "<p>White</p>", "<p>Black</p>"],
                    options_hi: ["<p>Pink</p>", "<p>Red</p>",
                                "<p>White</p>", "<p>Black</p>"],
                    solution_en: "<p>13.(b)<br><strong id=\"docs-internal-guid-1bb05f02-7fff-4c29-d802-693d074b1d28\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcynfY4I8uXx_DSQE31RwIhrMsFUcwwaFG-S49yzG85Z8xaAsJC61Vil51pUiMQiAwiyg2qOqq3eHUj3TmvvgUeB23TGHt2HlxZ9pe1gUWBi11ML6xpdiRWyFqk6bMRxiqHIwcJ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"149\" height=\"218\"></strong><br><strong>The opposite face are :</strong>- Black &harr; Blue , Pink &harr; White , Red &harr; Green</p>",
                    solution_hi: "<p>13.(b)<br><strong id=\"docs-internal-guid-1bb05f02-7fff-4c29-d802-693d074b1d28\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcynfY4I8uXx_DSQE31RwIhrMsFUcwwaFG-S49yzG85Z8xaAsJC61Vil51pUiMQiAwiyg2qOqq3eHUj3TmvvgUeB23TGHt2HlxZ9pe1gUWBi11ML6xpdiRWyFqk6bMRxiqHIwcJ?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"149\" height=\"218\"></strong><br><strong>विपरीत फलक हैं:- </strong>Black &harr; Blue , Pink &harr; White , Red &harr; Green</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six letters A, R, T, W, Y and Z are written on different faces of a dice. Two positions of this dice are shown in the figure, Which is the letter on the face opposite to the face containing A?<br><strong id=\"docs-internal-guid-151ae701-7fff-206b-1567-7a4efb0524e3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQocax-MfljI3vv2aYoSOcxq4WJMjXYu7N5MwQr3hCngUk6fAopK5KrYzd_dmbnUTfYCaiOUUMlDMbKY5wZ42jH10f_emJPP2aEMDqe1YBVS-zzGMH-gkRgC8WLx0KyzHYfOZL?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"145\" height=\"72\"></strong></p>",
                    question_hi: "<p>14. एक पासे के अलग-अलग फलकों पर छः अक्षर A, R, T, W, Y और Z अंकित हैं। निम्न आकृति में इसी पासे की दो अलग-अलग स्थितियां दर्शाई गई हैं। A अक्षर वाले फलक के विपरीत फलक पर कौन-सा अक्षर है?<br><strong id=\"docs-internal-guid-151ae701-7fff-206b-1567-7a4efb0524e3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQocax-MfljI3vv2aYoSOcxq4WJMjXYu7N5MwQr3hCngUk6fAopK5KrYzd_dmbnUTfYCaiOUUMlDMbKY5wZ42jH10f_emJPP2aEMDqe1YBVS-zzGMH-gkRgC8WLx0KyzHYfOZL?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"145\" height=\"72\"></strong></p>",
                    options_en: ["<p>Y</p>", "<p>R</p>", 
                                "<p>W</p>", "<p>Z</p>"],
                    options_hi: ["<p>Y</p>", "<p>R</p>",
                                "<p>W</p>", "<p>Z</p>"],
                    solution_en: "<p>14.(a) From the two dice the opposite face are <br>A &harr; Y , R &harr; T</p>",
                    solution_hi: "<p>14.(a) दोनों पासों से विपरीत फलक हैं<br>A &harr; Y , R &harr; T</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Six letters N, P, R, S, T and U are written on different faces of a dice, Two positions of this dice are shown in the figure. Which is the letter on the face opposite to the face containing U?<br><strong id=\"docs-internal-guid-27df4dac-7fff-cc77-dda2-fd625ae5dda0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0D3Hk0z9npCj2LRMHvamw-QzYH74a9xFMQZ6ai3iNNqgN5ZXWvBZxuBLnjfl5StThfbGfasBLfOTPeV-9SY_24mHzj9HxZf4IxbAOmjJT7ygULf0HENXjkpSAk1yvIYRCv3kekw?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"155\" height=\"74\"></strong></p>",
                    question_hi: "<p>15. एक पासे के अलग-अलग फलकों पर छः अक्षर-N,P,R,S,T और U अंकित हैं। निम्न आकृति में इसी पासे की दो अलग-अलग स्थितियां दर्शाई गई हैं। अक्षर U वाले फलक के विपरीत फलक पर कौन-सा अक्षर है?<br><strong id=\"docs-internal-guid-27df4dac-7fff-cc77-dda2-fd625ae5dda0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0D3Hk0z9npCj2LRMHvamw-QzYH74a9xFMQZ6ai3iNNqgN5ZXWvBZxuBLnjfl5StThfbGfasBLfOTPeV-9SY_24mHzj9HxZf4IxbAOmjJT7ygULf0HENXjkpSAk1yvIYRCv3kekw?key=yYoLouHZiRj8C5vZX4wPUaAG\" width=\"155\" height=\"74\"></strong></p>",
                    options_en: ["<p>N</p>", "<p>R</p>", 
                                "<p>P</p>", "<p>S</p>"],
                    options_hi: ["<p>N</p>", "<p>R</p>",
                                "<p>P</p>", "<p>S</p>"],
                    solution_en: "<p>15.(b) From the two dice the opposite face are :- P &harr; S , U &harr; R</p>",
                    solution_hi: "<p>15.(b) दो पासों से विपरीत फलक हैं:- P &harr; S , U &harr; R</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>