<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 50</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">50</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 47
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 48,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Times New Roman;\">Sachin alone can complete a piece of work for Rs. 8,500 in 8</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>days. But with the help of Vishnu, the work is complete in 6 days. The share to be paid to Vishnu is:</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>1.<span style=\"font-family: Baloo;\"> अकेले सचिन, 8,500 रुपये के लिए किसी कार्य को 8.5 दिनों में पूरा कर सकता है। लेकिन विष्णु की मदद से, कार्य 6 दिन में पूरा हो जाता है। विष्णु को भुगतान किया जाने वाला हिस्सा ज्ञात करे ?</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>3200</p>", "<p>2500</p>", 
                                "<p>2400</p>", "<p>2000</p>"],
                    options_hi: ["<p>3200</p>", "<p>2500</p>",
                                "<p>2400</p>", "<p>2000</p>"],
                    solution_en: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the total work = 102 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of Sachin = 12</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of Sachin + Vishnu = 17</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of Vishnu = 17-12 = 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total amount = 8500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total Efficiency = 12+5 = 17 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">17 unit = 8500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5 unit = 2500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">The share to be paid to Vishnu = 2500</span></p>",
                    solution_hi: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">माना कुल कार्य = 102</span></p>\r\n<p><span style=\"font-family: Baloo;\">सचिन की क्षमता = 12</span></p>\r\n<p><span style=\"font-family: Baloo;\">सचिन + विष्णु की क्षमता = 17</span></p>\r\n<p><span style=\"font-family: Baloo;\">विष्णु की क्षमता = 17-12 = 5</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल राशि = 8500</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल दक्षता = 12+ 5 = 17 इकाई </span></p>\r\n<p><span style=\"font-family: Baloo;\">17 इकाई = 8500</span></p>\r\n<p><span style=\"font-family: Baloo;\">5 इकाई = 2500</span></p>\r\n<p><span style=\"font-family: Baloo;\">विष्णु को दिया जाने वाला हिस्सा = 2500 Rs.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Times New Roman;\">Sachin alone can complete a piece of work for Rs. 8,500 in 8</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>days. But with the help of Vishnu, the work is complete in 6 days. The share to be paid to Vishnu is:</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>1.<span style=\"font-family: Baloo;\"> अकेले सचिन, 8,500 रुपये के लिए किसी कार्य को 8.5 दिनों में पूरा कर सकता है। लेकिन विष्णु की मदद से, कार्य 6 दिन में पूरा हो जाता है। विष्णु को भुगतान किया जाने वाला हिस्सा ज्ञात करे ?</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>3200</p>", "<p>2500</p>", 
                                "<p>2400</p>", "<p>2000</p>"],
                    options_hi: ["<p>3200</p>", "<p>2500</p>",
                                "<p>2400</p>", "<p>2000</p>"],
                    solution_en: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the total work = 102 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of Sachin = 12</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of Sachin + Vishnu = 17</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of Vishnu = 17-12 = 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total amount = 8500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total Efficiency = 12+5 = 17 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">17 unit = 8500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5 unit = 2500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">The share to be paid to Vishnu = 2500</span></p>",
                    solution_hi: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">माना कुल कार्य = 102</span></p>\r\n<p><span style=\"font-family: Baloo;\">सचिन की क्षमता = 12</span></p>\r\n<p><span style=\"font-family: Baloo;\">सचिन + विष्णु की क्षमता = 17</span></p>\r\n<p><span style=\"font-family: Baloo;\">विष्णु की क्षमता = 17-12 = 5</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल राशि = 8500</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल दक्षता = 12+ 5 = 17 इकाई </span></p>\r\n<p><span style=\"font-family: Baloo;\">17 इकाई = 8500</span></p>\r\n<p><span style=\"font-family: Baloo;\">5 इकाई = 2500</span></p>\r\n<p><span style=\"font-family: Baloo;\">विष्णु को दिया जाने वाला हिस्सा = 2500 Rs.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Times New Roman;\"> If x-y = 4 and x&sup3;-y&sup3; = 316, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup></math></span><span style=\"font-family: Times New Roman;\">is:</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>2.<span style=\"font-family: Baloo;\"> यदि x-y = 4 और x&sup3;-y&sup3; = 316, </span><span style=\"font-family: Baloo;\">तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup></math></span><span style=\"font-family: Baloo;\"> का मान है |</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>2248<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2482</p>", 
                                "<p>2428<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2284</p>"],
                    options_hi: ["<p>2248<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2482</p>",
                                "<p>2428</p>", "<p>2284</p>"],
                    solution_en: "<p>2.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Go through hit and trial</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put x = 7, y = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">X-y = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7-3 = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 = 4 (satisfy)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> X&sup3; - y&sup3; = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7&sup3; - 3&sup3; = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">343 - 27 = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">316 = 316 (satisfy)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>4</mn></msup><mo>=</mo><mo>&#160;</mo><msup><mn>7</mn><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>3</mn><mn>4</mn></msup></math> =2401 +81 =2482</p>",
                    solution_hi: "<p>2.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">x = 7, y = 3 रखें। </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x-y = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7-3 = 4</span></p>\r\n<p><span style=\"font-family: Baloo;\">4 = 4 (सभी शर्तों को पूरा करता है)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x&sup3; - y&sup3; = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7&sup3; - 4&sup3; = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">343 - 27 = 316</span></p>\r\n<p><span style=\"font-family: Baloo;\">316 = 316 (सभी शर्तों को पूरा करता है)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>4</mn></msup><mo>=</mo><mo>&#160;</mo><msup><mn>7</mn><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#160;</mo><mo>=</mo><mn>2401</mn><mo>+</mo><mn>81</mn><mo>=</mo><mn>2482</mn></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Times New Roman;\"> If x-y = 4 and x&sup3;-y&sup3; = 316, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup></math></span><span style=\"font-family: Times New Roman;\">is:</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>2.<span style=\"font-family: Baloo;\"> यदि x-y = 4 और x&sup3;-y&sup3; = 316, </span><span style=\"font-family: Baloo;\">तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup></math></span><span style=\"font-family: Baloo;\"> का मान है |</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>2248<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2482</p>", 
                                "<p>2428<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2284</p>"],
                    options_hi: ["<p>2248<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2482</p>",
                                "<p>2428</p>", "<p>2284</p>"],
                    solution_en: "<p>2.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Go through hit and trial</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put x = 7, y = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">X-y = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7-3 = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 = 4 (satisfy)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> X&sup3; - y&sup3; = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7&sup3; - 3&sup3; = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">343 - 27 = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">316 = 316 (satisfy)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>4</mn></msup><mo>=</mo><mo>&#160;</mo><msup><mn>7</mn><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>3</mn><mn>4</mn></msup></math> =2401 +81 =2482</p>",
                    solution_hi: "<p>2.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">x = 7, y = 3 रखें। </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x-y = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7-3 = 4</span></p>\r\n<p><span style=\"font-family: Baloo;\">4 = 4 (सभी शर्तों को पूरा करता है)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x&sup3; - y&sup3; = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7&sup3; - 4&sup3; = 316</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">343 - 27 = 316</span></p>\r\n<p><span style=\"font-family: Baloo;\">316 = 316 (सभी शर्तों को पूरा करता है)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>4</mn></msup><mo>=</mo><mo>&#160;</mo><msup><mn>7</mn><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#160;</mo><mo>=</mo><mn>2401</mn><mo>+</mo><mn>81</mn><mo>=</mo><mn>2482</mn></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Times New Roman;\"> An article was sold at a loss of 24%. If it were sold for Rs. 1,596 more, then there would have been a gain of 18%. The cost price of the article is:</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Baloo;\">एक वस्तु को 24% की हानि पर बेचा जाता है । यदि इसे 1,596 रुपये ज्यादा में बेचा जाता तो 18% का फायदा होता। वस्तु का लागत मूल्य है:</span></p>",
                    options_en: ["<p>3400</p>", "<p>3600</p>", 
                                "<p>3800</p>", "<p>4200</p>"],
                    options_hi: ["<p>3400<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3600</p>",
                                "<p>3800</p>", "<p>4200</p>"],
                    solution_en: "<p>3. (c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Difference between loss of 24% and gain of 18% = 42%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">42% = 1596</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">100% = 3800</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">The cost price of the article = 3800</span></p>",
                    solution_hi: "<p>3 (c)</p>\r\n<p><span style=\"font-family: Baloo;\">24% हानि और 18% लाभ के बीच अंतर = 42%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">42% = 1596</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">100% = 3800</span></p>\r\n<p><span style=\"font-family: Baloo;\">वस्तु का क्रय मूल्य = 3800</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Times New Roman;\"> An article was sold at a loss of 24%. If it were sold for Rs. 1,596 more, then there would have been a gain of 18%. The cost price of the article is:</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Baloo;\">एक वस्तु को 24% की हानि पर बेचा जाता है । यदि इसे 1,596 रुपये ज्यादा में बेचा जाता तो 18% का फायदा होता। वस्तु का लागत मूल्य है:</span></p>",
                    options_en: ["<p>3400</p>", "<p>3600</p>", 
                                "<p>3800</p>", "<p>4200</p>"],
                    options_hi: ["<p>3400<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3600</p>",
                                "<p>3800</p>", "<p>4200</p>"],
                    solution_en: "<p>3. (c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Difference between loss of 24% and gain of 18% = 42%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">42% = 1596</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">100% = 3800</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">The cost price of the article = 3800</span></p>",
                    solution_hi: "<p>3 (c)</p>\r\n<p><span style=\"font-family: Baloo;\">24% हानि और 18% लाभ के बीच अंतर = 42%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">42% = 1596</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">100% = 3800</span></p>\r\n<p><span style=\"font-family: Baloo;\">वस्तु का क्रय मूल्य = 3800</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Times New Roman;\"> If a:b = 3:5, b:c = 7:8 and c:d = 2:3, then 2a : 3d is equal to:</span></p>",
                    question_hi: "<p>.4.<span style=\"font-family: Baloo;\"> यदि a:b = 3:5, b:c = 7:8 और c:d = 2:3, तो 2a : 3d बराबर है:</span></p>",
                    options_en: ["<p>1 : 2</p>", "<p>7 : 30</p>", 
                                "<p>7 : 15</p>", "<p>7 : 20</p>"],
                    options_hi: ["<p>1 : 2</p>", "<p>7 : 30</p>",
                                "<p>7 : 15</p>", "<p>7 : 20</p>"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">A : B : C : D</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 : 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; 7 : 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2 : 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">____________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">42 : 70 : 80 : 120 or 21 : 35 : 40 : 60</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Now, 2a : 3d = 2(21) : 3(60) = 7 : 30</span></p>",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">A : B : C : D</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 : 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp;7 : 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2 : 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">____________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">42 : 70 : 80 : 120 or 21 : 35 : 40 : 60</span></p>\r\n<p><span style=\"font-family: Baloo;\">अब, 2a : 3d = 2(21) : 3(60) = 7 : 30</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Times New Roman;\"> If a:b = 3:5, b:c = 7:8 and c:d = 2:3, then 2a : 3d is equal to:</span></p>",
                    question_hi: "<p>.4.<span style=\"font-family: Baloo;\"> यदि a:b = 3:5, b:c = 7:8 और c:d = 2:3, तो 2a : 3d बराबर है:</span></p>",
                    options_en: ["<p>1 : 2</p>", "<p>7 : 30</p>", 
                                "<p>7 : 15</p>", "<p>7 : 20</p>"],
                    options_hi: ["<p>1 : 2</p>", "<p>7 : 30</p>",
                                "<p>7 : 15</p>", "<p>7 : 20</p>"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">A : B : C : D</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 : 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; 7 : 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2 : 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">____________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">42 : 70 : 80 : 120 or 21 : 35 : 40 : 60</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Now, 2a : 3d = 2(21) : 3(60) = 7 : 30</span></p>",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">A : B : C : D</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 : 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp;7 : 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2 : 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">____________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">42 : 70 : 80 : 120 or 21 : 35 : 40 : 60</span></p>\r\n<p><span style=\"font-family: Baloo;\">अब, 2a : 3d = 2(21) : 3(60) = 7 : 30</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Times New Roman;\"> Study the following graph and answer the question that follows.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of students that appeared and qualified, from various colleges, at a scholarship examination.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image5.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">What is the difference between the average number of students that appeared for the scholarship examination from all the given colleges, and the average number of students that qualified from all the colleges together?</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Baloo;\"> निम्नलिखित आलेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।</span></p>\r\n<p><span style=\"font-family: Baloo;\">किसी छात्रवृत्ति परीक्षा में विभिन्न कॉलेजों से भाग लेने वाले और इसमें उत्तीर्ण होने वाले छात्रों की संख्या इस प्रकार।</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image4.png\" /></p>\r\n<p><span style=\"font-family: Baloo;\">दिए गए सभी कॉलेजों से छात्रवृत्ति परीक्षा में भाग लेने वाले छात्रों की औसत संख्या और सभी कॉलेजों से कुल मिलाकर उत्तीर्ण हुए छात्रों की औसत संख्या में अंतर कितना है?</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>920</p>", "<p>940</p>", 
                                "<p>930</p>", "<p>910</p>"],
                    options_hi: ["<p>920</p>", "<p>940</p>",
                                "<p>930</p>", "<p>910</p>"],
                    solution_en: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Average number of students that appeared for the scholarship examination = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2800</mn><mo>+</mo><mn>1800</mn><mo>+</mo><mn>3000</mn><mo>+</mo><mn>2000</mn><mo>+</mo><mn>2400</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 2400</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average number of students that qualified = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1400</mn><mo>+</mo><mn>1000</mn><mo>+</mo><mn>1800</mn><mo>+</mo><mn>1600</mn><mo>+</mo><mn>1600</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 1480</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required Difference = 2400-1480 = 920</span></p>",
                    solution_hi: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">छात्रवृत्ति परीक्षा में बैठने वाले छात्रों की औसत संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2800</mn><mo>+</mo><mn>1800</mn><mo>+</mo><mn>3000</mn><mo>+</mo><mn>2000</mn><mo>+</mo><mn>2400</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 2400</span></p>\r\n<p><span style=\"font-family: Baloo;\">उत्तीर्ण छात्रों की औसत संख्या =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1400</mn><mo>+</mo><mn>1000</mn><mo>+</mo><mn>1800</mn><mo>+</mo><mn>1600</mn><mo>+</mo><mn>1600</mn></mrow><mn>5</mn></mfrac></math>= 1480</span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक अंतर = 2400-1480 = 920</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Times New Roman;\"> Study the following graph and answer the question that follows.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of students that appeared and qualified, from various colleges, at a scholarship examination.</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image5.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">What is the difference between the average number of students that appeared for the scholarship examination from all the given colleges, and the average number of students that qualified from all the colleges together?</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Baloo;\"> निम्नलिखित आलेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।</span></p>\r\n<p><span style=\"font-family: Baloo;\">किसी छात्रवृत्ति परीक्षा में विभिन्न कॉलेजों से भाग लेने वाले और इसमें उत्तीर्ण होने वाले छात्रों की संख्या इस प्रकार।</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image4.png\" /></p>\r\n<p><span style=\"font-family: Baloo;\">दिए गए सभी कॉलेजों से छात्रवृत्ति परीक्षा में भाग लेने वाले छात्रों की औसत संख्या और सभी कॉलेजों से कुल मिलाकर उत्तीर्ण हुए छात्रों की औसत संख्या में अंतर कितना है?</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>920</p>", "<p>940</p>", 
                                "<p>930</p>", "<p>910</p>"],
                    options_hi: ["<p>920</p>", "<p>940</p>",
                                "<p>930</p>", "<p>910</p>"],
                    solution_en: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Average number of students that appeared for the scholarship examination = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2800</mn><mo>+</mo><mn>1800</mn><mo>+</mo><mn>3000</mn><mo>+</mo><mn>2000</mn><mo>+</mo><mn>2400</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 2400</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average number of students that qualified = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1400</mn><mo>+</mo><mn>1000</mn><mo>+</mo><mn>1800</mn><mo>+</mo><mn>1600</mn><mo>+</mo><mn>1600</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 1480</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required Difference = 2400-1480 = 920</span></p>",
                    solution_hi: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">छात्रवृत्ति परीक्षा में बैठने वाले छात्रों की औसत संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2800</mn><mo>+</mo><mn>1800</mn><mo>+</mo><mn>3000</mn><mo>+</mo><mn>2000</mn><mo>+</mo><mn>2400</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 2400</span></p>\r\n<p><span style=\"font-family: Baloo;\">उत्तीर्ण छात्रों की औसत संख्या =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1400</mn><mo>+</mo><mn>1000</mn><mo>+</mo><mn>1800</mn><mo>+</mo><mn>1600</mn><mo>+</mo><mn>1600</mn></mrow><mn>5</mn></mfrac></math>= 1480</span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक अंतर = 2400-1480 = 920</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Times New Roman;\">The number of students enrolled in different streams at Senior secondary level in the five schools is shown in the given bar graph.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_70852996211667472598463.jpg\" width=\"404\" height=\"338\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">The number of students enrolled in the Humanities stream in schools A, C and D is what percentage of the number of students enrolled in the vocational stream in schools C, D and E?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 5/8/2021 (Evening)</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Baloo;\">पांच स्कूलों में उच्तर माध्यमिक स्तर पर विभिन्न संकायों में नामांकित छात्रों की संख्या को दंडालेख में दर्शाया गया है।</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_70852996211667472598463.jpg\" width=\"419\" height=\"351\" /></p>\r\n<p><span style=\"font-family: Baloo;\">स्कूल A, C और D में मानविकी वर्ग में नामांकित छात्रों की संख्या, स्कूल C, D और E में व्यावसायिक वर्ग में नामांकित छात्रों की संख्या का कितना प्रतिशत है?</span></p>",
                    options_en: ["<p>75</p>", "<p>80.64</p>", 
                                "<p>115</p>", "<p>124</p>"],
                    options_hi: ["<p>75</p>", "<p>80.64</p>",
                                "<p>115</p>", "<p>124</p>"],
                    solution_en: "<p>6.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of students enrolled in Humanities stream in A, C and D = 70+40+45 = 155</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of students enrolled in Vocational stream in schools C, D and E = 35+40+50 = 125</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x% of 125 = 155</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 124</span></p>",
                    solution_hi: "<p>6.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">A, C और D में मानविकी स्ट्रीम में नामांकित छात्रों की संख्या = 70+40+45 = 155</span></p>\r\n<p><span style=\"font-family: Baloo;\">स्कूल C, D और E में व्यावसायिक स्ट्रीम में नामांकित छात्रों की संख्या =35+40+50 = 125</span></p>\r\n<p><span style=\"font-family: Baloo;\">125 का x% = 155</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x= 124</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Times New Roman;\">The number of students enrolled in different streams at Senior secondary level in the five schools is shown in the given bar graph.</span></p>\r\n<p><img src=\"images/mceu_70852996211667472598463.jpg\" width=\"404\" height=\"338\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">The number of students enrolled in the Humanities stream in schools A, C and D is what percentage of the number of students enrolled in the vocational stream in schools C, D and E?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 5/8/2021 (Evening)</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Baloo;\">पांच स्कूलों में उच्तर माध्यमिक स्तर पर विभिन्न संकायों में नामांकित छात्रों की संख्या को दंडालेख में दर्शाया गया है।</span></p>\r\n<p><img src=\"images/mceu_70852996211667472598463.jpg\" width=\"419\" height=\"351\" /></p>\r\n<p><span style=\"font-family: Baloo;\">स्कूल A, C और D में मानविकी वर्ग में नामांकित छात्रों की संख्या, स्कूल C, D और E में व्यावसायिक वर्ग में नामांकित छात्रों की संख्या का कितना प्रतिशत है?</span></p>",
                    options_en: ["<p>75</p>", "<p>80.64</p>", 
                                "<p>115</p>", "<p>124</p>"],
                    options_hi: ["<p>75</p>", "<p>80.64</p>",
                                "<p>115</p>", "<p>124</p>"],
                    solution_en: "<p>6.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of students enrolled in Humanities stream in A, C and D = 70+40+45 = 155</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of students enrolled in Vocational stream in schools C, D and E = 35+40+50 = 125</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x% of 125 = 155</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 124</span></p>",
                    solution_hi: "<p>6.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">A, C और D में मानविकी स्ट्रीम में नामांकित छात्रों की संख्या = 70+40+45 = 155</span></p>\r\n<p><span style=\"font-family: Baloo;\">स्कूल C, D और E में व्यावसायिक स्ट्रीम में नामांकित छात्रों की संख्या =35+40+50 = 125</span></p>\r\n<p><span style=\"font-family: Baloo;\">125 का x% = 155</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x= 124</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Times New Roman;\"> The in-radius and circumradius of a right-angled triangle is 3 cm and 12.5 cm, respectively. The area of the triangle is:</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>7. <span style=\"font-family: Baloo;\">एक समकोण त्रिभुज की अन्तः त्रिज्या और परित्रिज्या क्रमशः 3 सेमी और 12.5 सेमी है। त्रिभुज का क्षेत्रफल है| </span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>64 cm&sup2;</p>", "<p>48 cm&sup2;</p>", 
                                "<p>88 cm&sup2;</p>", "<p>84 cm&sup2;</p>"],
                    options_hi: ["<p>64 cm&sup2;</p>", "<p>48 cm&sup2;</p>",
                                "<p>88 cm&sup2;</p>", "<p>84 cm&sup2;</p>"],
                    solution_en: "<p>7.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Circumradius = 12.5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">It means, Hypotenuse = 25 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Inradius = 3 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We know, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7, 24 and 25 are triplets and give inradius = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>24</mn></math> </span><span style=\"font-family: Times New Roman;\">= 84</span></p>",
                    solution_hi: "<p>7.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">परित्रिज्या = 12.5</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसका अर्थ है, कर्ण = 25 cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">अन्तः त्रिज्या = 3 cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">हम जानते है की,</span></p>\r\n<p><span style=\"font-family: Baloo;\">7, 24 और 25 त्रिक हैं और अन्तःत्रिज्या देते हैं = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>24</mn></math></span><span style=\"font-family: Times New Roman;\">= 84</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Times New Roman;\"> The in-radius and circumradius of a right-angled triangle is 3 cm and 12.5 cm, respectively. The area of the triangle is:</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>7. <span style=\"font-family: Baloo;\">एक समकोण त्रिभुज की अन्तः त्रिज्या और परित्रिज्या क्रमशः 3 सेमी और 12.5 सेमी है। त्रिभुज का क्षेत्रफल है| </span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>64 cm&sup2;</p>", "<p>48 cm&sup2;</p>", 
                                "<p>88 cm&sup2;</p>", "<p>84 cm&sup2;</p>"],
                    options_hi: ["<p>64 cm&sup2;</p>", "<p>48 cm&sup2;</p>",
                                "<p>88 cm&sup2;</p>", "<p>84 cm&sup2;</p>"],
                    solution_en: "<p>7.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Circumradius = 12.5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">It means, Hypotenuse = 25 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Inradius = 3 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We know, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">7, 24 and 25 are triplets and give inradius = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>24</mn></math> </span><span style=\"font-family: Times New Roman;\">= 84</span></p>",
                    solution_hi: "<p>7.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">परित्रिज्या = 12.5</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसका अर्थ है, कर्ण = 25 cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">अन्तः त्रिज्या = 3 cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">हम जानते है की,</span></p>\r\n<p><span style=\"font-family: Baloo;\">7, 24 और 25 त्रिक हैं और अन्तःत्रिज्या देते हैं = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>24</mn></math></span><span style=\"font-family: Times New Roman;\">= 84</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Times New Roman;\"> If a+b = 24 and a&sup2;+b&sup2; = 306, where a&gt;b, then the value of 4a-5b is:</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>8.<span style=\"font-family: Baloo;\"> यदि a+b = 24 और a&sup2;+b&sup2; = 30, </span></p>\r\n<p><span style=\"font-family: Baloo;\">जहाँ a&gt;b, तो 4a - 5b का मान है |</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>18<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>20<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>12</p>", "<p>15</p>"],
                    options_hi: ["<p>18</p>", "<p>20</p>",
                                "<p>12</p>", "<p>15</p>"],
                    solution_en: "<p>8.(d)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Given that, a + b = 24 and a&sup2;+b&sup2; = 306</span></p>\r\n<p>(a+b)&sup2; = a&sup2;+b&sup2; + 2ab</p>\r\n<p><span style=\"font-family: Times New Roman;\">576 = 306 + 2ab</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">ab = 135 = 15 &times; 9 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">and a + b = 15 + 9 = 24</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 15 and b = 9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 4a - 5b = 4(15) - 5(9) = 15</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">You can directly put the value of a = 15 and b = 9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 4a - 5b = 4(15) - 5(9) = 15</span></p>",
                    solution_hi: "<p>8.(d) <span style=\"font-family: Baloo;\">दिया गया है,</span></p>\r\n<p><span style=\"font-family: Baloo;\"> a + b = 24 और a&sup2;+b&sup2; = 306</span></p>\r\n<p><span style=\"font-family: Baloo;\">(a + b) </span>&sup2; = a&sup2;+b&sup2; + 2ab</p>\r\n<p><span style=\"font-family: Times New Roman;\">576 = 306 + 2ab</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">ab = 135 = 15 &times; 9 </span></p>\r\n<p><span style=\"font-family: Baloo;\">और a + b = 15 + 9 = 24</span></p>\r\n<p><span style=\"font-family: Baloo;\">a = 15 और b = 9</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, 4a - 5b = 4(15) - 5(9) = 15</span></p>\r\n<p><span style=\"font-family: Baloo;\">वैकल्पिक विधि, :- </span></p>\r\n<p><span style=\"font-family: Baloo;\">आप सीधे a = 15 और b = 9 . का मान रख सकते हैं। </span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, 4a - 5b = 4(15) - 5(9) = 15</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Times New Roman;\"> If a+b = 24 and a&sup2;+b&sup2; = 306, where a&gt;b, then the value of 4a-5b is:</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>8.<span style=\"font-family: Baloo;\"> यदि a+b = 24 और a&sup2;+b&sup2; = 30, </span></p>\r\n<p><span style=\"font-family: Baloo;\">जहाँ a&gt;b, तो 4a - 5b का मान है |</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>18<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>20<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>12</p>", "<p>15</p>"],
                    options_hi: ["<p>18</p>", "<p>20</p>",
                                "<p>12</p>", "<p>15</p>"],
                    solution_en: "<p>8.(d)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Given that, a + b = 24 and a&sup2;+b&sup2; = 306</span></p>\r\n<p>(a+b)&sup2; = a&sup2;+b&sup2; + 2ab</p>\r\n<p><span style=\"font-family: Times New Roman;\">576 = 306 + 2ab</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">ab = 135 = 15 &times; 9 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">and a + b = 15 + 9 = 24</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 15 and b = 9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 4a - 5b = 4(15) - 5(9) = 15</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">You can directly put the value of a = 15 and b = 9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 4a - 5b = 4(15) - 5(9) = 15</span></p>",
                    solution_hi: "<p>8.(d) <span style=\"font-family: Baloo;\">दिया गया है,</span></p>\r\n<p><span style=\"font-family: Baloo;\"> a + b = 24 और a&sup2;+b&sup2; = 306</span></p>\r\n<p><span style=\"font-family: Baloo;\">(a + b) </span>&sup2; = a&sup2;+b&sup2; + 2ab</p>\r\n<p><span style=\"font-family: Times New Roman;\">576 = 306 + 2ab</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">ab = 135 = 15 &times; 9 </span></p>\r\n<p><span style=\"font-family: Baloo;\">और a + b = 15 + 9 = 24</span></p>\r\n<p><span style=\"font-family: Baloo;\">a = 15 और b = 9</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, 4a - 5b = 4(15) - 5(9) = 15</span></p>\r\n<p><span style=\"font-family: Baloo;\">वैकल्पिक विधि, :- </span></p>\r\n<p><span style=\"font-family: Baloo;\">आप सीधे a = 15 और b = 9 . का मान रख सकते हैं। </span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, 4a - 5b = 4(15) - 5(9) = 15</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\">. If the number 87m6203m is divisible by 6, then find the sum of all possible values of &lsquo;m&rsquo;.</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>9. <span style=\"font-family: Baloo;\">यदि संख्या 87m6203m, 6 से विभाज्य है, तो \'m\' के सभी संभावित मानों का योग ज्ञात कीजिए।</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>10</p>", "<p>20</p>", 
                                "<p>16</p>", "<p>15</p>"],
                    options_hi: ["<p>10</p>", "<p>20</p>",
                                "<p>16</p>", "<p>15</p>"],
                    solution_en: "<p>9.(a)<span style=\"font-family: Times New Roman;\"> 6 = 2</span><span style=\"font-family: Times New Roman;\">3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For divisibility of 6, number should be divisibility by 2 and 3 both</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For 2, last digit should be divisible by 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore, possible values of m = 0, 2, 4, 6, and 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For 3, sum of digits should be divisible by 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 0</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+0+6+2+0+3+0 = 26 (not divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+2+6+2+0+3+2 = 30 (divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+4+6+2+0+3+4 = 34 (not divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 6</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+6+6+2+0+3+6 = 38 (not divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+8+6+2+0+3+8 = 42 (not divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore, possible value of m = 2+8 = 10</span></p>",
                    solution_hi: "<p>9.(a)<span style=\"font-family: Times New Roman;\"> 6 = 2</span><span style=\"font-family: Times New Roman;\">3</span></p>\r\n<p><span style=\"font-family: Baloo;\">6 की विभाज्यता के लिए, संख्या 2 और 3 दोनों से विभाज्य होनी चाहिए</span></p>\r\n<p><span style=\"font-family: Baloo;\">2 के लिए, अंतिम अंक 2 से विभाज्य होना चाहिए</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, m के संभावित मान = 0, 2, 4, 6, और 8 </span></p>\r\n<p><span style=\"font-family: Baloo;\">3 के लिए, अंकों का योग 3 से विभाज्य होना चाहिए |</span></p>\r\n<p><span style=\"font-family: Baloo;\">m = 0 रखें |</span></p>\r\n<p><span style=\"font-family: Baloo;\">8+7+0+6+2+0+3+0 = 26 (3 से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Baloo;\">m = 2 रखें |</span></p>\r\n<p><span style=\"font-family: Baloo;\">8+7+2+6+2+0+3+2 = 30 (3 से विभाज्य)</span></p>\r\n<p><span style=\"font-family: Baloo;\">m = 4 रखें |</span></p>\r\n<p><span style=\"font-family: Baloo;\">8+7+4+6+2+0+3+4 = 34 (3 से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Baloo;\">m = 6 रखें | </span></p>\r\n<p><span style=\"font-family: Baloo;\">8 + 7 + 6 + 6 + 2 + 0 + 3 + 6 = 38 (3 से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Baloo;\"> m = 8 रखें |</span></p>\r\n<p><span style=\"font-family: Baloo;\">8 + 7 + 8 + 6 + 2 + 0 + 3 + 8 = 42 (3 से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, m का संभावित मान = 2 + 8 = 10</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\">. If the number 87m6203m is divisible by 6, then find the sum of all possible values of &lsquo;m&rsquo;.</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>9. <span style=\"font-family: Baloo;\">यदि संख्या 87m6203m, 6 से विभाज्य है, तो \'m\' के सभी संभावित मानों का योग ज्ञात कीजिए।</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>10</p>", "<p>20</p>", 
                                "<p>16</p>", "<p>15</p>"],
                    options_hi: ["<p>10</p>", "<p>20</p>",
                                "<p>16</p>", "<p>15</p>"],
                    solution_en: "<p>9.(a)<span style=\"font-family: Times New Roman;\"> 6 = 2</span><span style=\"font-family: Times New Roman;\">3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For divisibility of 6, number should be divisibility by 2 and 3 both</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For 2, last digit should be divisible by 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore, possible values of m = 0, 2, 4, 6, and 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For 3, sum of digits should be divisible by 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 0</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+0+6+2+0+3+0 = 26 (not divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+2+6+2+0+3+2 = 30 (divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+4+6+2+0+3+4 = 34 (not divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 6</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+6+6+2+0+3+6 = 38 (not divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, m = 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8+7+8+6+2+0+3+8 = 42 (not divisible by 3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore, possible value of m = 2+8 = 10</span></p>",
                    solution_hi: "<p>9.(a)<span style=\"font-family: Times New Roman;\"> 6 = 2</span><span style=\"font-family: Times New Roman;\">3</span></p>\r\n<p><span style=\"font-family: Baloo;\">6 की विभाज्यता के लिए, संख्या 2 और 3 दोनों से विभाज्य होनी चाहिए</span></p>\r\n<p><span style=\"font-family: Baloo;\">2 के लिए, अंतिम अंक 2 से विभाज्य होना चाहिए</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, m के संभावित मान = 0, 2, 4, 6, और 8 </span></p>\r\n<p><span style=\"font-family: Baloo;\">3 के लिए, अंकों का योग 3 से विभाज्य होना चाहिए |</span></p>\r\n<p><span style=\"font-family: Baloo;\">m = 0 रखें |</span></p>\r\n<p><span style=\"font-family: Baloo;\">8+7+0+6+2+0+3+0 = 26 (3 से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Baloo;\">m = 2 रखें |</span></p>\r\n<p><span style=\"font-family: Baloo;\">8+7+2+6+2+0+3+2 = 30 (3 से विभाज्य)</span></p>\r\n<p><span style=\"font-family: Baloo;\">m = 4 रखें |</span></p>\r\n<p><span style=\"font-family: Baloo;\">8+7+4+6+2+0+3+4 = 34 (3 से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Baloo;\">m = 6 रखें | </span></p>\r\n<p><span style=\"font-family: Baloo;\">8 + 7 + 6 + 6 + 2 + 0 + 3 + 6 = 38 (3 से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Baloo;\"> m = 8 रखें |</span></p>\r\n<p><span style=\"font-family: Baloo;\">8 + 7 + 8 + 6 + 2 + 0 + 3 + 8 = 42 (3 से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, m का संभावित मान = 2 + 8 = 10</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Times New Roman;\"> What is the coefficient of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\"> in the expansion of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>&#160;</mi><mn>5</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">?</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>10.<span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msqrt><mn>2</mn></msqrt><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>&#160;</mi><mn>5</mn><msqrt><mn>3</mn></msqrt><msup><mo>)</mo><mn>3</mn></msup></math></span><span style=\"font-family: Baloo;\">के प्रसार में <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">का गुणांक क्या है?</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>-225<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>-30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", 
                                "<p>225<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>30</p>"],
                    options_hi: ["<p>-225<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>-30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p>225<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>10.(c)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>(a+b)&sup3; = a&sup3;-b&sup3;+3a&sup2;b-3ab&sup2;</p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore, we need to find only 3ab&sup2; term to get the coefficient of y&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For the coefficient of y&sup2;, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#178;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>)</mo><mo>(</mo><mn>5</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mn>225</mn><msqrt><mn>2</mn></msqrt></math></span></p>",
                    solution_hi: "<p>10.(c)</p>\r\n<p>(a-b)&sup3; = a&sup3;-b&sup3;+3a&sup2;b-3ab&sup2;</p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, हमें y&sup2; का गुणांक प्राप्त करने के लिए केवल 3ab&sup2; पद खोजने की आवश्यकता है। </span></p>\r\n<p><span style=\"font-family: Baloo;\">y&sup2; के गुणांक के लिए,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3ab&sup2; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>)</mo><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>225</mn><msqrt><mn>2</mn></msqrt></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Times New Roman;\"> What is the coefficient of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\"> in the expansion of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>&#160;</mi><mn>5</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">?</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>10.<span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msqrt><mn>2</mn></msqrt><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>&#160;</mi><mn>5</mn><msqrt><mn>3</mn></msqrt><msup><mo>)</mo><mn>3</mn></msup></math></span><span style=\"font-family: Baloo;\">के प्रसार में <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">का गुणांक क्या है?</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>-225<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>-30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", 
                                "<p>225<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>30</p>"],
                    options_hi: ["<p>-225<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>-30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p>225<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>10.(c)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>(a+b)&sup3; = a&sup3;-b&sup3;+3a&sup2;b-3ab&sup2;</p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore, we need to find only 3ab&sup2; term to get the coefficient of y&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For the coefficient of y&sup2;, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#178;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>)</mo><mo>(</mo><mn>5</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mn>225</mn><msqrt><mn>2</mn></msqrt></math></span></p>",
                    solution_hi: "<p>10.(c)</p>\r\n<p>(a-b)&sup3; = a&sup3;-b&sup3;+3a&sup2;b-3ab&sup2;</p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, हमें y&sup2; का गुणांक प्राप्त करने के लिए केवल 3ab&sup2; पद खोजने की आवश्यकता है। </span></p>\r\n<p><span style=\"font-family: Baloo;\">y&sup2; के गुणांक के लिए,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3ab&sup2; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>)</mo><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>225</mn><msqrt><mn>2</mn></msqrt></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Times New Roman;\">Study the given bar graph and answer the question that follows.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">The bar graph shows the production of table fans in a factory during one week</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image10.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">The average production of table fans on Friday and Saturday exceeds the average production of table fans during the week by:</span></p>",
                    question_hi: "<p>11.<span style=\"font-family: Baloo;\">निम्नांकित दंड आलेख का अध्ययन करें और उसके बाद दिए गए प्रश्न का उत्तर दीजिए।</span></p>\r\n<p><span style=\"font-family: Baloo;\">दंड आलेख का एक कारखाने में एक सप्ताह के दौरान टेबल पंखे के उत्पादन को दर्शाता है |</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image14.png\" /></p>\r\n<p><span style=\"font-family: Baloo;\">शुक्रवार और शनिवार को टेबल फैन का औसत उत्पादन सप्ताह के दौरान टेबल फैन के औसत उत्पादन से कितना अधिक है |</span></p>",
                    options_en: ["<p>45</p>", "<p>30</p>", 
                                "<p>35</p>", "<p>40</p>"],
                    options_hi: ["<p>45</p>", "<p>30</p>",
                                "<p>35</p>", "<p>40</p>"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Average production of table fans on Friday and Saturday =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>+</mo><mn>450</mn></mrow><mn>2</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 375</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average production of table fans during the week =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>+</mo><mn>510</mn><mo>+</mo><mn>390</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>450</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 340</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required Difference = 375-340 = 35</span></p>",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">शुक्रवार और शनिवार को टेबल फैन का औसत उत्पादन = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>+</mo><mn>450</mn></mrow><mn>2</mn></mfrac></math>= 375</span></p>\r\n<p><span style=\"font-family: Baloo;\">सप्ताह के दौरान टेबल पंखे का औसत उत्पादन =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>+</mo><mn>510</mn><mo>+</mo><mn>390</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>450</mn></mrow><mn>6</mn></mfrac></math>= 340</span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक अंतर = 375-340 = 35</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Times New Roman;\">Study the given bar graph and answer the question that follows.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">The bar graph shows the production of table fans in a factory during one week</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image10.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">The average production of table fans on Friday and Saturday exceeds the average production of table fans during the week by:</span></p>",
                    question_hi: "<p>11.<span style=\"font-family: Baloo;\">निम्नांकित दंड आलेख का अध्ययन करें और उसके बाद दिए गए प्रश्न का उत्तर दीजिए।</span></p>\r\n<p><span style=\"font-family: Baloo;\">दंड आलेख का एक कारखाने में एक सप्ताह के दौरान टेबल पंखे के उत्पादन को दर्शाता है |</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image14.png\" /></p>\r\n<p><span style=\"font-family: Baloo;\">शुक्रवार और शनिवार को टेबल फैन का औसत उत्पादन सप्ताह के दौरान टेबल फैन के औसत उत्पादन से कितना अधिक है |</span></p>",
                    options_en: ["<p>45</p>", "<p>30</p>", 
                                "<p>35</p>", "<p>40</p>"],
                    options_hi: ["<p>45</p>", "<p>30</p>",
                                "<p>35</p>", "<p>40</p>"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Average production of table fans on Friday and Saturday =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>+</mo><mn>450</mn></mrow><mn>2</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 375</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average production of table fans during the week =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>+</mo><mn>510</mn><mo>+</mo><mn>390</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>450</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 340</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required Difference = 375-340 = 35</span></p>",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">शुक्रवार और शनिवार को टेबल फैन का औसत उत्पादन = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>+</mo><mn>450</mn></mrow><mn>2</mn></mfrac></math>= 375</span></p>\r\n<p><span style=\"font-family: Baloo;\">सप्ताह के दौरान टेबल पंखे का औसत उत्पादन =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>+</mo><mn>510</mn><mo>+</mo><mn>390</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>450</mn></mrow><mn>6</mn></mfrac></math>= 340</span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक अंतर = 375-340 = 35</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Gungsuh;\">In a triangle ABC, if BD and CD bisect &ang;B and &ang;C, respectively, and &ang;A = 100&deg;, then find &ang;BDC.</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>12.<span style=\"font-family: Arial Unicode MS;\"> एक त्रिभुज ABC में, यदि BD और CD क्रमशः &ang;B और &ang;C को समद्विभाजित करते हैं, और &ang;A = 100&deg;, तो &ang;BDC ज्ञात कीजिए।</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>130&deg;</p>", "<p>100&deg;</p>", 
                                "<p>110&deg;</p>", "<p>140&deg;</p>"],
                    options_hi: ["<p>130&deg;</p>", "<p>100&deg;</p>",
                                "<p>110&deg;</p>", "<p>140&deg;</p>"],
                    solution_en: "<p>12.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image9.png\" /></p>\r\n<p><span style=\"font-family: Gungsuh;\">&ang;BAC =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>90</mn><mo>&#176;</mo><mo>+</mo><mfrac><mi>A</mi><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Gungsuh;\">&ang;BAC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>90</mn><mo>&#176;</mo><mo>+</mo><mfrac><mn>100</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 140&deg;</span></p>",
                    solution_hi: "<p>12.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image11.png\" /></p>\r\n<p><span style=\"font-family: Gungsuh;\">&ang;BAC =<span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>90</mn><mo>&#176;</mo><mo>+</mo><mfrac><mi>A</mi><mn>2</mn></mfrac></math></span></span></p>\r\n<p><span style=\"font-family: Gungsuh;\">&ang;BAC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>90</mn><mo>&#176;</mo><mo>+</mo><mfrac><mn>100</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 140&deg;</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Gungsuh;\">In a triangle ABC, if BD and CD bisect &ang;B and &ang;C, respectively, and &ang;A = 100&deg;, then find &ang;BDC.</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>12.<span style=\"font-family: Arial Unicode MS;\"> एक त्रिभुज ABC में, यदि BD और CD क्रमशः &ang;B और &ang;C को समद्विभाजित करते हैं, और &ang;A = 100&deg;, तो &ang;BDC ज्ञात कीजिए।</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>130&deg;</p>", "<p>100&deg;</p>", 
                                "<p>110&deg;</p>", "<p>140&deg;</p>"],
                    options_hi: ["<p>130&deg;</p>", "<p>100&deg;</p>",
                                "<p>110&deg;</p>", "<p>140&deg;</p>"],
                    solution_en: "<p>12.(d)</p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image9.png\" /></p>\r\n<p><span style=\"font-family: Gungsuh;\">&ang;BAC =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>90</mn><mo>&#176;</mo><mo>+</mo><mfrac><mi>A</mi><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Gungsuh;\">&ang;BAC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>90</mn><mo>&#176;</mo><mo>+</mo><mfrac><mn>100</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 140&deg;</span></p>",
                    solution_hi: "<p>12.(d)</p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image11.png\" /></p>\r\n<p><span style=\"font-family: Gungsuh;\">&ang;BAC =<span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>90</mn><mo>&#176;</mo><mo>+</mo><mfrac><mi>A</mi><mn>2</mn></mfrac></math></span></span></p>\r\n<p><span style=\"font-family: Gungsuh;\">&ang;BAC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>90</mn><mo>&#176;</mo><mo>+</mo><mfrac><mn>100</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 140&deg;</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> Two circles of radii 4 cm and 3 cm respectively, touch each other externally. What is the distance(in cm) between their centres?</span></p>",
                    question_hi: "<p>13.<span style=\"font-family: Baloo;\"> क्रमशः 4 सेमी और 3 सेमी त्रिज्या वाले दो वृत्त , एक दूसरे को बाहय रूप से स्पर्श करते हैं। उनके केंद्रों के बीच की दूरी (सेमी में) क्या है?</span></p>",
                    options_en: ["<p>5</p>", "<p>16</p>", 
                                "<p>7</p>", "<p>9</p>"],
                    options_hi: ["<p>5</p>", "<p>16</p>",
                                "<p>7</p>", "<p>9</p>"],
                    solution_en: "<p>13.(c)Short-Trick:</p>\r\n<p><span style=\"font-family: Times New Roman;\">Distance between their centres = 4+3 = 7 cm </span></p>",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">उनके केंद्रों के बीच की दूरी = 4+3 = 7 cm </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> Two circles of radii 4 cm and 3 cm respectively, touch each other externally. What is the distance(in cm) between their centres?</span></p>",
                    question_hi: "<p>13.<span style=\"font-family: Baloo;\"> क्रमशः 4 सेमी और 3 सेमी त्रिज्या वाले दो वृत्त , एक दूसरे को बाहय रूप से स्पर्श करते हैं। उनके केंद्रों के बीच की दूरी (सेमी में) क्या है?</span></p>",
                    options_en: ["<p>5</p>", "<p>16</p>", 
                                "<p>7</p>", "<p>9</p>"],
                    options_hi: ["<p>5</p>", "<p>16</p>",
                                "<p>7</p>", "<p>9</p>"],
                    solution_en: "<p>13.(c)Short-Trick:</p>\r\n<p><span style=\"font-family: Times New Roman;\">Distance between their centres = 4+3 = 7 cm </span></p>",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">उनके केंद्रों के बीच की दूरी = 4+3 = 7 cm </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Times New Roman;\"> A number P is 20% more than a number Q but 10% less than a number R. What percentage is number Q of number R?</span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Baloo;\">एक संख्या P, एक अन्य संख्या Q से 20% अधिक है लेकिन संख्या R से 10% कम है। संख्या Q, संख्या R का कितना प्रतिशत है?</span></p>",
                    options_en: ["<p>80<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>85<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>75</p>", "<p>9</p>"],
                    options_hi: ["<p>80<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>85<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>75<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>90</p>"],
                    solution_en: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Q = 100-(10% of 100) = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P = 75+(20% of 75) = 90</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R = 100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x% of 100 = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 75%</span></p>",
                    solution_hi: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Q = 100-(10% of 100) = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P = 75+(20% of 75) = 90</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R = 100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x% of 100 = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 75%</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Times New Roman;\"> A number P is 20% more than a number Q but 10% less than a number R. What percentage is number Q of number R?</span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Baloo;\">एक संख्या P, एक अन्य संख्या Q से 20% अधिक है लेकिन संख्या R से 10% कम है। संख्या Q, संख्या R का कितना प्रतिशत है?</span></p>",
                    options_en: ["<p>80<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>85<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>75</p>", "<p>9</p>"],
                    options_hi: ["<p>80<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>85<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>75<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>90</p>"],
                    solution_en: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Q = 100-(10% of 100) = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P = 75+(20% of 75) = 90</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R = 100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x% of 100 = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 75%</span></p>",
                    solution_hi: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Q = 100-(10% of 100) = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P = 75+(20% of 75) = 90</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R = 100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x% of 100 = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 75%</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mrow><mn>3</mn><msqrt><mn>6</mn></msqrt></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\"> and&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">is an acute angle, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">is </span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Baloo;\"> यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>=</mo><mfrac><mn>7</mn><mrow><mn>3</mn><msqrt><mn>6</mn></msqrt></mrow></mfrac></math></span><span style=\"font-family: Baloo;\"> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Baloo;\"> न्यून कोण है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\"> का मान ज्ञात करे |</span></p>",
                    options_en: ["<p>12</p>", "<p>15</p>", 
                                "<p>1</p>", "<p>9</p>"],
                    options_hi: ["<p>12<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>15</p>",
                                "<p>1</p>", "<p>9</p>"],
                    solution_en: "<p>15.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>=</mo><mfrac><mn>7</mn><mrow><mn>3</mn><msqrt><mn>6</mn></msqrt></mrow></mfrac><mo>=</mo><mfrac><mi>b</mi><mi>h</mi></mfrac></math></p>\r\n<p><span style=\"font-family: Gungsuh;\">P&sup2; = (3&radic;6)&sup2; - (7)&sup2;</span></p>\r\n<p><span style=\"font-family: Gungsuh;\">P = &radic;5</span></p>\r\n<p><span style=\"font-family: Gungsuh;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#952;</mi><mi>&#160;</mi><mo>=</mo><mo>(</mo><mfrac><mi>P</mi><mi>h</mi></mfrac><mo>)</mo><mo>&#178;</mo><mo>=</mo><mfrac><mn>5</mn><mn>54</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">27<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>27</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>54</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>1</mn></math></span></p>",
                    solution_hi: "<p>15.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>=</mo><mfrac><mn>7</mn><mrow><mn>3</mn><msqrt><mn>6</mn></msqrt></mrow></mfrac><mo>=</mo><mfrac><mi>b</mi><mi>h</mi></mfrac></math></p>\r\n<p><span style=\"font-family: Gungsuh;\">P&sup2; = (3&radic;6)&sup2; - (7)&sup2;</span></p>\r\n<p><span style=\"font-family: Gungsuh;\">P = &radic;5</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>27</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>54</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>1</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mrow><mn>3</mn><msqrt><mn>6</mn></msqrt></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\"> and&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">is an acute angle, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">is </span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Baloo;\"> यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>=</mo><mfrac><mn>7</mn><mrow><mn>3</mn><msqrt><mn>6</mn></msqrt></mrow></mfrac></math></span><span style=\"font-family: Baloo;\"> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Baloo;\"> न्यून कोण है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\"> का मान ज्ञात करे |</span></p>",
                    options_en: ["<p>12</p>", "<p>15</p>", 
                                "<p>1</p>", "<p>9</p>"],
                    options_hi: ["<p>12<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>15</p>",
                                "<p>1</p>", "<p>9</p>"],
                    solution_en: "<p>15.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>=</mo><mfrac><mn>7</mn><mrow><mn>3</mn><msqrt><mn>6</mn></msqrt></mrow></mfrac><mo>=</mo><mfrac><mi>b</mi><mi>h</mi></mfrac></math></p>\r\n<p><span style=\"font-family: Gungsuh;\">P&sup2; = (3&radic;6)&sup2; - (7)&sup2;</span></p>\r\n<p><span style=\"font-family: Gungsuh;\">P = &radic;5</span></p>\r\n<p><span style=\"font-family: Gungsuh;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#178;</mo><mi>&#952;</mi><mi>&#160;</mi><mo>=</mo><mo>(</mo><mfrac><mi>P</mi><mi>h</mi></mfrac><mo>)</mo><mo>&#178;</mo><mo>=</mo><mfrac><mn>5</mn><mn>54</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">27<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>27</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>54</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>1</mn></math></span></p>",
                    solution_hi: "<p>15.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>=</mo><mfrac><mn>7</mn><mrow><mn>3</mn><msqrt><mn>6</mn></msqrt></mrow></mfrac><mo>=</mo><mfrac><mi>b</mi><mi>h</mi></mfrac></math></p>\r\n<p><span style=\"font-family: Gungsuh;\">P&sup2; = (3&radic;6)&sup2; - (7)&sup2;</span></p>\r\n<p><span style=\"font-family: Gungsuh;\">P = &radic;5</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>27</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>54</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>1</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.<span style=\"font-family: Times New Roman;\"> During his morning walk, Atul walks for 45 minutes at a speed of 8km/h and takes 15 rounds of a park. Shekhar takes 10 rounds of the same park in 40 minutes. What is the speed of Shekhar in km/h?</span></p>",
                    question_hi: "<p>16.<span style=\"font-family: Baloo;\"> सुबह की सैर के दौरान, अतुल 8 किमी/घंटा की चाल से 45 मिनट तक चलता है और एक पार्क के 15 चक्कर लगाता है। शेखर उसी पार्क के 10 चक्कर 40 मिनट में लगाता है। किमी/घंटा में शेखर की चाल क्या है?</span></p>",
                    options_en: ["<p>7.2<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>6 <span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>6.75</p>", "<p>7.5</p>"],
                    options_hi: ["<p>7.2<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>6 <span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>6.75</p>", "<p>7.5</p>"],
                    solution_en: "<p>16.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total distance covered by him =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>60</mn></mfrac><mo>&#215;</mo><mn>8</mn></math> </span><span style=\"font-family: Times New Roman;\"> = 6 km</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1 round or circumference of park = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6000</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 400 m</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total distance covered by Sekhar = 400 &times;10 = 4000 m = 4 km</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Speed = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>40</mn></mfrac><mo>&#215;</mo><mn>60</mn></math></span><span style=\"font-family: Times New Roman;\">= 6 km/h</span></p>",
                    solution_hi: "<p>16.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">उसके द्वारा तय की गई कुल दूरी =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>60</mn></mfrac><mo>&#215;</mo><mn>8</mn></math> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">= 6 किमी</span></p>\r\n<p><span style=\"font-family: Baloo;\">पार्क का 1 चक्कर या परिधि =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6000</mn><mn>15</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\"> = 400 मीटर</span></p>\r\n<p><span style=\"font-family: Baloo;\">शेखर द्वारा तय की गई कुल दूरी = 400&times;10 = 4000 मीटर = 4 किमी</span></p>\r\n<p><span style=\"font-family: Baloo;\">गति = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>40</mn></mfrac><mo>&#215;</mo><mn>60</mn></math></span><span style=\"font-family: Baloo;\">= 6 किमी/घंटा</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.<span style=\"font-family: Times New Roman;\"> During his morning walk, Atul walks for 45 minutes at a speed of 8km/h and takes 15 rounds of a park. Shekhar takes 10 rounds of the same park in 40 minutes. What is the speed of Shekhar in km/h?</span></p>",
                    question_hi: "<p>16.<span style=\"font-family: Baloo;\"> सुबह की सैर के दौरान, अतुल 8 किमी/घंटा की चाल से 45 मिनट तक चलता है और एक पार्क के 15 चक्कर लगाता है। शेखर उसी पार्क के 10 चक्कर 40 मिनट में लगाता है। किमी/घंटा में शेखर की चाल क्या है?</span></p>",
                    options_en: ["<p>7.2<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>6 <span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>6.75</p>", "<p>7.5</p>"],
                    options_hi: ["<p>7.2<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>6 <span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>6.75</p>", "<p>7.5</p>"],
                    solution_en: "<p>16.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total distance covered by him =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>60</mn></mfrac><mo>&#215;</mo><mn>8</mn></math> </span><span style=\"font-family: Times New Roman;\"> = 6 km</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1 round or circumference of park = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6000</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 400 m</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total distance covered by Sekhar = 400 &times;10 = 4000 m = 4 km</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Speed = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>40</mn></mfrac><mo>&#215;</mo><mn>60</mn></math></span><span style=\"font-family: Times New Roman;\">= 6 km/h</span></p>",
                    solution_hi: "<p>16.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">उसके द्वारा तय की गई कुल दूरी =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>60</mn></mfrac><mo>&#215;</mo><mn>8</mn></math> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">= 6 किमी</span></p>\r\n<p><span style=\"font-family: Baloo;\">पार्क का 1 चक्कर या परिधि =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6000</mn><mn>15</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\"> = 400 मीटर</span></p>\r\n<p><span style=\"font-family: Baloo;\">शेखर द्वारा तय की गई कुल दूरी = 400&times;10 = 4000 मीटर = 4 किमी</span></p>\r\n<p><span style=\"font-family: Baloo;\">गति = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>40</mn></mfrac><mo>&#215;</mo><mn>60</mn></math></span><span style=\"font-family: Baloo;\">= 6 किमी/घंटा</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17.<span style=\"font-family: Times New Roman;\"> For A = 30&deg;, find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>3</mn><mi>A</mi></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">is:</span></p>",
                    question_hi: "<p>17.<span style=\"font-family: Baloo;\"> A=30&deg; होने पर, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>3</mn><mi>A</mi></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi></mrow></mfrac></math></span><span style=\"font-family: Baloo;\"> का मान ज्ञात कीजिए</span></p>",
                    options_en: ["<p>-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math> <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>36</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>57</mn><mn>4</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>7</mn><mn>36</mn></mfrac></math> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>4</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math></p>"],
                    solution_en: "<p>17.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>3</mn><mi>A</mi></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>60</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mn>30</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>90</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>90</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mo>-</mo><mn>3</mn><mo>&#215;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><msup><mrow><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mfrac><mrow><mo>-</mo><mn>9</mn></mrow><mn>4</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>8</mn><mn>3</mn></mfrac><msup><mrow><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mfrac><mrow><mo>-</mo><mn>27</mn><mo>+</mo><mn>32</mn><mo>-</mo><mn>12</mn></mrow><mn>12</mn></mfrac><mi>&#160;</mi></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mo>=</mo><mo>-</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math></span></p>",
                    solution_hi: "<p>17.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>3</mn><mi>A</mi></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>60</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mn>30</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>90</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>90</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mo>-</mo><mn>3</mn><mo>&#215;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><msup><mrow><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mfrac><mrow><mo>-</mo><mn>9</mn></mrow><mn>4</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>8</mn><mn>3</mn></mfrac><msup><mrow><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mfrac><mrow><mo>-</mo><mn>27</mn><mo>+</mo><mn>32</mn><mo>-</mo><mn>12</mn></mrow><mn>12</mn></mfrac><mi>&#160;</mi></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>4</mn></mfrac><mspace linebreak=\"newline\"/></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17.<span style=\"font-family: Times New Roman;\"> For A = 30&deg;, find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>3</mn><mi>A</mi></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">is:</span></p>",
                    question_hi: "<p>17.<span style=\"font-family: Baloo;\"> A=30&deg; होने पर, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>3</mn><mi>A</mi></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi></mrow></mfrac></math></span><span style=\"font-family: Baloo;\"> का मान ज्ञात कीजिए</span></p>",
                    options_en: ["<p>-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math> <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>36</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>57</mn><mn>4</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>7</mn><mn>36</mn></mfrac></math> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>4</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math></p>"],
                    solution_en: "<p>17.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>3</mn><mi>A</mi></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>60</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mn>30</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>90</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>90</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mo>-</mo><mn>3</mn><mo>&#215;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><msup><mrow><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mfrac><mrow><mo>-</mo><mn>9</mn></mrow><mn>4</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>8</mn><mn>3</mn></mfrac><msup><mrow><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mfrac><mrow><mo>-</mo><mn>27</mn><mo>+</mo><mn>32</mn><mo>-</mo><mn>12</mn></mrow><mn>12</mn></mfrac><mi>&#160;</mi></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mo>=</mo><mo>-</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math></span></p>",
                    solution_hi: "<p>17.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>3</mn><mi>A</mi></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mn>60</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>e</mi><msup><mrow><msup><mi>c</mi><mn>2</mn></msup><mn>30</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mfrac><mrow><mn>90</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></mrow><mrow/></msup></mrow><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>s</mi><mi>i</mi><mi>n</mi><mn>90</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mo>-</mo><mn>3</mn><mo>&#215;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><msup><mrow><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mfrac><mrow><mo>-</mo><mn>9</mn></mrow><mn>4</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>8</mn><mn>3</mn></mfrac><msup><mrow><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mfrac><mrow><mo>-</mo><mn>27</mn><mo>+</mo><mn>32</mn><mo>-</mo><mn>12</mn></mrow><mn>12</mn></mfrac><mi>&#160;</mi></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>4</mn></mfrac><mspace linebreak=\"newline\"/></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. <span style=\"font-family: Times New Roman;\">Find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">, if&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>(</mo><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mn>0</mn><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\">where <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\"> is an acute angle.</span></p>",
                    question_hi: "<p>18.<span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Baloo;\"> का मान ज्ञात कीजिए, यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mo>(</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>(</mo><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mn>0</mn></math></span><span style=\"font-family: Baloo;\">, जहां <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Baloo;\"> एक न्यून कोण है ।</span></p>",
                    options_en: ["<p>60&deg;</p>", "<p>30&deg;</p>", 
                                "<p>45&deg;</p>", "<p>15&deg;</p>"],
                    options_hi: ["<p>60&deg;<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>30&deg;<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>45&deg;</p>", "<p>15&deg;</p>"],
                    solution_en: "<p>18.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mo>(</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>(</mo><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"/><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><msqrt><mn>3</mn></msqrt><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>(</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msqrt><mn>3</mn></msqrt><mo>(</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>0</mn><mspace linebreak=\"newline\"/><mo>(</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mi>&#160;</mi><msqrt><mn>3</mn></msqrt><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>0</mn><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>=</mo><mi>&#160;</mi><mo>-</mo><mn>1</mn><mo>,</mo><mi>&#160;</mi><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"/><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>18.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mo>(</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>(</mo><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"/><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>&#8730;</mo><mn>3</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn><mo>-</mo><mo>&#8730;</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>tan</mi><mi>&#952;</mi><mo>(</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>3</mn><mo>(</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mspace linebreak=\"newline\"/><mo>(</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>-</mo><mo>&#160;</mo><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>tan</mi><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>,</mo><mo>&#160;</mo><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. <span style=\"font-family: Times New Roman;\">Find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">, if&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>(</mo><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mn>0</mn><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\">where <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\"> is an acute angle.</span></p>",
                    question_hi: "<p>18.<span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Baloo;\"> का मान ज्ञात कीजिए, यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mo>(</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>(</mo><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mn>0</mn></math></span><span style=\"font-family: Baloo;\">, जहां <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Baloo;\"> एक न्यून कोण है ।</span></p>",
                    options_en: ["<p>60&deg;</p>", "<p>30&deg;</p>", 
                                "<p>45&deg;</p>", "<p>15&deg;</p>"],
                    options_hi: ["<p>60&deg;<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>30&deg;<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>45&deg;</p>", "<p>15&deg;</p>"],
                    solution_en: "<p>18.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mo>(</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>(</mo><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"/><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><msqrt><mn>3</mn></msqrt><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>(</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msqrt><mn>3</mn></msqrt><mo>(</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>0</mn><mspace linebreak=\"newline\"/><mo>(</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mi>&#160;</mi><msqrt><mn>3</mn></msqrt><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>0</mn><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>=</mo><mi>&#160;</mi><mo>-</mo><mn>1</mn><mo>,</mo><mi>&#160;</mi><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"/><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>18.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mo>(</mo><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>(</mo><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"/><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mo>&#8730;</mo><mn>3</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn><mo>-</mo><mo>&#8730;</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>tan</mi><mi>&#952;</mi><mo>(</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>3</mn><mo>(</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mspace linebreak=\"newline\"/><mo>(</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>-</mo><mo>&#160;</mo><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>tan</mi><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>,</mo><mo>&#160;</mo><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19.<span style=\"font-family: Times New Roman;\"> How many spherical bullets, each bullet being 7 cm in diameter, can be made out of a cube of lead whose edge measures 77 cm? (Take&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">)</span></p>",
                    question_hi: "<p>19. <span style=\"font-family: Baloo;\">उस सीसे के घन से 7 सेमी व्यास कितनी गोलाकार गोलियां बनाई जा सकती हैं, जिसकी कोर का माप 77 सेमी है? ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Baloo;\">लें)</span></p>",
                    options_en: ["<p>1452</p>", "<p>4521</p>", 
                                "<p>2451</p>", "<p>2541</p>"],
                    options_hi: ["<p>1452</p>", "<p>4521</p>",
                                "<p>2451</p>", "<p>2541</p>"],
                    solution_en: "<p>19.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of sphere = volume of cube</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mi>&#160;</mi><mi>n</mi><mo>=</mo><mn>77</mn><mo>&#215;</mo><mn>77</mn><mo>&#215;</mo><mn>77</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">n = 2541</span></p>",
                    solution_hi: "<p>19.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">गोले का आयतन = घन का आयतन</span></p>\r\n<p><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mi>&#160;</mi><mi>n</mi><mo>=</mo><mn>77</mn><mo>&#215;</mo><mn>77</mn><mo>&#215;</mo><mn>77</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">n = 2541</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19.<span style=\"font-family: Times New Roman;\"> How many spherical bullets, each bullet being 7 cm in diameter, can be made out of a cube of lead whose edge measures 77 cm? (Take&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">)</span></p>",
                    question_hi: "<p>19. <span style=\"font-family: Baloo;\">उस सीसे के घन से 7 सेमी व्यास कितनी गोलाकार गोलियां बनाई जा सकती हैं, जिसकी कोर का माप 77 सेमी है? ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Baloo;\">लें)</span></p>",
                    options_en: ["<p>1452</p>", "<p>4521</p>", 
                                "<p>2451</p>", "<p>2541</p>"],
                    options_hi: ["<p>1452</p>", "<p>4521</p>",
                                "<p>2451</p>", "<p>2541</p>"],
                    solution_en: "<p>19.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of sphere = volume of cube</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mi>&#160;</mi><mi>n</mi><mo>=</mo><mn>77</mn><mo>&#215;</mo><mn>77</mn><mo>&#215;</mo><mn>77</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">n = 2541</span></p>",
                    solution_hi: "<p>19.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">गोले का आयतन = घन का आयतन</span></p>\r\n<p><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&#215;</mo><mi>&#160;</mi><mi>n</mi><mo>=</mo><mn>77</mn><mo>&#215;</mo><mn>77</mn><mo>&#215;</mo><mn>77</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">n = 2541</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20.<span style=\"font-family: Times New Roman;\"> The distance between two equal parallel chords of a circle is 10cm. If the chords are 24 cm long, then what is the length of the radius?</span></p>",
                    question_hi: "<p>20.<span style=\"font-family: Baloo;\"> एक वृत्त की दो समान समानांतर जीवाओं के बीच की दूरी 10 सेमी है। यदि जीवाएँ 24 सेमी लंबी हैं, तो त्रिज्या की लंबाई क्या है?</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\">2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>61</mn></msqrt></math>cm </span></p>", "<p>17 cm</p>", 
                                "<p>26 cm</p>", "<p>13 cm</p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>61</mn></msqrt></math>cm </span></p>", "<p>17 cm</p>",
                                "<p>26 cm</p>", "<p>13 cm</p>"],
                    solution_en: "<p>20.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image3.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">AB = 24 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AM = MB = 12 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let O be the center, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OM = ON = 5 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AO&sup2; = 12&sup2; + 5&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AO = 13</span></p>",
                    solution_hi: "<p>20.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image6.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">AB = 24 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AM = MB = 12 cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">माना O केंद्र है, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OM = ON = 5 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AO&sup2; = 12&sup2; + 5&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AO = 13</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20.<span style=\"font-family: Times New Roman;\"> The distance between two equal parallel chords of a circle is 10cm. If the chords are 24 cm long, then what is the length of the radius?</span></p>",
                    question_hi: "<p>20.<span style=\"font-family: Baloo;\"> एक वृत्त की दो समान समानांतर जीवाओं के बीच की दूरी 10 सेमी है। यदि जीवाएँ 24 सेमी लंबी हैं, तो त्रिज्या की लंबाई क्या है?</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\">2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>61</mn></msqrt></math>cm </span></p>", "<p>17 cm</p>", 
                                "<p>26 cm</p>", "<p>13 cm</p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>61</mn></msqrt></math>cm </span></p>", "<p>17 cm</p>",
                                "<p>26 cm</p>", "<p>13 cm</p>"],
                    solution_en: "<p>20.(d)</p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image3.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">AB = 24 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AM = MB = 12 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let O be the center, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OM = ON = 5 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AO&sup2; = 12&sup2; + 5&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AO = 13</span></p>",
                    solution_hi: "<p>20.(d)</p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647846429/word/media/image6.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">AB = 24 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AM = MB = 12 cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">माना O केंद्र है, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OM = ON = 5 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AO&sup2; = 12&sup2; + 5&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AO = 13</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21.<span style=\"font-family: Times New Roman;\"> The numbers 2, 3, 4 and 5 occur (2+5k), (5k-7), (2k-3) and (k+2) times, respectively. The average of the number is 2.85. Later on, the number 2 was replaced by 6 in all the places. What is the average of the new numbers?</span></p>",
                    question_hi: "<p>21.<span style=\"font-family: Baloo;\"> संख्या 2, 3, 4 और 5 क्रमशः (2+5k), (5k-7), (2k-3) और (k+2) बार आयी है। संख्याओं का औसत 2.85 है। बाद में, सभी स्थानों में संख्या 2 को 6 से प्रतिस्थापित किया जाता है। नई संख्याओं का औसत ज्ञात करे?</span></p>",
                    options_en: ["<p>2.4</p>", "<p>5.25</p>", 
                                "<p>3.85</p>", "<p>4.75</p>"],
                    options_hi: ["<p>2.4</p>", "<p>5.25<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>3.85</p>", "<p>4.75</p>"],
                    solution_en: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">2(2+5k) + 3(5k-7) + 4(2k-3) + 5(k+2) = (13k-6) &times; 2.85</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 + 10k + 15k - 21 + 8k - 12 + 5k + 10 = 37.05k - 17.1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">38k - 19 = 37.05k - 17.1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">0.95k = 1.9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">K = 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">No. of times 2 occurred = (2+5k) = 12</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total number = (2+5k) + (5k-7) + (2k-3) + (k+2) = 20</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">When 2 is replaced by 6, 12 number is increased by 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total increment = 12 </span><span style=\"font-family: Times New Roman;\"> 4 = 48</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Increase in average =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>20</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\"> = 2.4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Final average = 2.85 + 2.4 = 5.25</span></p>",
                    solution_hi: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">2(2+5k) + 3(5k-7) + 4(2k-3) + 5(k+2) = (13k-6) &times; 2.85</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 + 10k + 15k - 21 + 8k - 12 + 5k + 10 = 37.05k - 17.1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">38k - 19 = 37.05k - 17.1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">0.95k = 1.9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">K = 2</span></p>\r\n<p><span style=\"font-family: Baloo;\">2 आने की कुल संख्या = (2+5k) = 12से बदल दिया जाता है, तो</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल संख्या = (2+5k) + (5k-7) + (2k-3) + (k+2) = 20</span></p>\r\n<p><span style=\"font-family: Baloo;\">जब 2 को 6 12 संख्या में 4 की वृद्धि हो जाती है। </span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल वेतन वृद्धि = 12 &times;</span><span style=\"font-family: Times New Roman;\"> 4 = 48</span></p>\r\n<p><span style=\"font-family: Baloo;\">औसत में वृद्धि = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 2.4</span></p>\r\n<p><span style=\"font-family: Baloo;\">अंतिम औसत = 2.85 + 2.4 = 5.25</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21.<span style=\"font-family: Times New Roman;\"> The numbers 2, 3, 4 and 5 occur (2+5k), (5k-7), (2k-3) and (k+2) times, respectively. The average of the number is 2.85. Later on, the number 2 was replaced by 6 in all the places. What is the average of the new numbers?</span></p>",
                    question_hi: "<p>21.<span style=\"font-family: Baloo;\"> संख्या 2, 3, 4 और 5 क्रमशः (2+5k), (5k-7), (2k-3) और (k+2) बार आयी है। संख्याओं का औसत 2.85 है। बाद में, सभी स्थानों में संख्या 2 को 6 से प्रतिस्थापित किया जाता है। नई संख्याओं का औसत ज्ञात करे?</span></p>",
                    options_en: ["<p>2.4</p>", "<p>5.25</p>", 
                                "<p>3.85</p>", "<p>4.75</p>"],
                    options_hi: ["<p>2.4</p>", "<p>5.25<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>3.85</p>", "<p>4.75</p>"],
                    solution_en: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">2(2+5k) + 3(5k-7) + 4(2k-3) + 5(k+2) = (13k-6) &times; 2.85</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 + 10k + 15k - 21 + 8k - 12 + 5k + 10 = 37.05k - 17.1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">38k - 19 = 37.05k - 17.1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">0.95k = 1.9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">K = 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">No. of times 2 occurred = (2+5k) = 12</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total number = (2+5k) + (5k-7) + (2k-3) + (k+2) = 20</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">When 2 is replaced by 6, 12 number is increased by 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total increment = 12 </span><span style=\"font-family: Times New Roman;\"> 4 = 48</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Increase in average =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>20</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\"> = 2.4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Final average = 2.85 + 2.4 = 5.25</span></p>",
                    solution_hi: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">2(2+5k) + 3(5k-7) + 4(2k-3) + 5(k+2) = (13k-6) &times; 2.85</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 + 10k + 15k - 21 + 8k - 12 + 5k + 10 = 37.05k - 17.1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">38k - 19 = 37.05k - 17.1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">0.95k = 1.9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">K = 2</span></p>\r\n<p><span style=\"font-family: Baloo;\">2 आने की कुल संख्या = (2+5k) = 12से बदल दिया जाता है, तो</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल संख्या = (2+5k) + (5k-7) + (2k-3) + (k+2) = 20</span></p>\r\n<p><span style=\"font-family: Baloo;\">जब 2 को 6 12 संख्या में 4 की वृद्धि हो जाती है। </span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल वेतन वृद्धि = 12 &times;</span><span style=\"font-family: Times New Roman;\"> 4 = 48</span></p>\r\n<p><span style=\"font-family: Baloo;\">औसत में वृद्धि = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 2.4</span></p>\r\n<p><span style=\"font-family: Baloo;\">अंतिम औसत = 2.85 + 2.4 = 5.25</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22.<span style=\"font-family: Times New Roman;\"> Krishan invested a sum of ₹ 25,000 in two parts. He earned 11% p.a simple interest on part 1 and 10% p.a compound interest compounded annually on part 2. If the total interest received by him after 2 years is ₹ 5,370, then find the sum invested on simple interest.</span></p>",
                    question_hi: "<p>22.<span style=\"font-family: Baloo;\"> कृष्ण ने ₹25,000 की राशि को दो भागों में निवेशित किया। उसे पहले भाग पर 11% वार्षिक साधारण ब्याज मिला और दूसरे भाग पर वार्षिक रूप से चक्रवृद्धि होने वाला 10% चक्रवृद्धि ब्याज मिला। यदि 2 वर्ष बाद उसके द्वारा प्राप्त कुल ब्याज ₹5,370 है, तो साधारण ब्याज पर निवेशित की गई राशि ज्ञात करे।</span></p>",
                    options_en: ["<p>₹ 11,000</p>", "<p>₹ 12,000</p>", 
                                "<p>₹ 12,500</p>", "<p>₹ 13,000</p>"],
                    options_hi: ["<p>₹11,000</p>", "<p>₹12,000</p>",
                                "<p>₹12,500</p>", "<p>₹13,000</p>"],
                    solution_en: "<p>22.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the second part = x, first part = 25,000 - x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10% = 1/10</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Compound interest in 2 years = 10 + 10 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Times New Roman;\"> = 21%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25000</mn><mo>-</mo><mi>x</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn></mrow><mn>100</mn></mfrac><mo>+</mo><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>21</mn></mrow><mn>100</mn></mfrac></math>= 5370</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">550000-22x+21x = 537000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 13000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Amount invested on simple interest = 25000-13000 = 12000</span></p>",
                    solution_hi: "<p>22.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">माना कि दूसरा भाग = x, पहला भाग = 25,000-x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10% = 1/10</span></p>\r\n<p><span style=\"font-family: Baloo;\"> 2 वर्षो में चक्रवृद्धि ब्याज = 10 + 10 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 21%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25000</mn><mo>-</mo><mi>x</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn></mrow><mn>100</mn></mfrac><mo>+</mo><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>21</mn></mrow><mn>100</mn></mfrac></math>= 5370</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">550000-22x+21x = 537000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 13000</span></p>\r\n<p><span style=\"font-family: Baloo;\">साधारण ब्याज पर निवेश की गई राशि = 25000-13000 = 12000</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22.<span style=\"font-family: Times New Roman;\"> Krishan invested a sum of ₹ 25,000 in two parts. He earned 11% p.a simple interest on part 1 and 10% p.a compound interest compounded annually on part 2. If the total interest received by him after 2 years is ₹ 5,370, then find the sum invested on simple interest.</span></p>",
                    question_hi: "<p>22.<span style=\"font-family: Baloo;\"> कृष्ण ने ₹25,000 की राशि को दो भागों में निवेशित किया। उसे पहले भाग पर 11% वार्षिक साधारण ब्याज मिला और दूसरे भाग पर वार्षिक रूप से चक्रवृद्धि होने वाला 10% चक्रवृद्धि ब्याज मिला। यदि 2 वर्ष बाद उसके द्वारा प्राप्त कुल ब्याज ₹5,370 है, तो साधारण ब्याज पर निवेशित की गई राशि ज्ञात करे।</span></p>",
                    options_en: ["<p>₹ 11,000</p>", "<p>₹ 12,000</p>", 
                                "<p>₹ 12,500</p>", "<p>₹ 13,000</p>"],
                    options_hi: ["<p>₹11,000</p>", "<p>₹12,000</p>",
                                "<p>₹12,500</p>", "<p>₹13,000</p>"],
                    solution_en: "<p>22.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the second part = x, first part = 25,000 - x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10% = 1/10</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Compound interest in 2 years = 10 + 10 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Times New Roman;\"> = 21%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25000</mn><mo>-</mo><mi>x</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn></mrow><mn>100</mn></mfrac><mo>+</mo><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>21</mn></mrow><mn>100</mn></mfrac></math>= 5370</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">550000-22x+21x = 537000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 13000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Amount invested on simple interest = 25000-13000 = 12000</span></p>",
                    solution_hi: "<p>22.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">माना कि दूसरा भाग = x, पहला भाग = 25,000-x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10% = 1/10</span></p>\r\n<p><span style=\"font-family: Baloo;\"> 2 वर्षो में चक्रवृद्धि ब्याज = 10 + 10 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 21%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25000</mn><mo>-</mo><mi>x</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn></mrow><mn>100</mn></mfrac><mo>+</mo><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>21</mn></mrow><mn>100</mn></mfrac></math>= 5370</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">550000-22x+21x = 537000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 13000</span></p>\r\n<p><span style=\"font-family: Baloo;\">साधारण ब्याज पर निवेश की गई राशि = 25000-13000 = 12000</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23.<span style=\"font-family: Times New Roman;\"> The value of 18</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>-</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo></math>is:</span></p>",
                    question_hi: "<p>23.<span style=\"font-family: Times New Roman;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>-</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo></math></span><span style=\"font-family: Baloo;\">का मान है:</span></p>",
                    options_en: ["<p>4</p>", "<p>5</p>", 
                                "<p>2</p>", "<p>8</p>"],
                    options_hi: ["<p>4</p>", "<p>5</p>",
                                "<p>2</p>", "<p>8</p>"],
                    solution_en: "<p>23.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>-</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mfrac><mn>31</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>10</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>6</mn></mfrac><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mfrac><mn>13</mn><mn>3</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>150</mn><mn>33</mn></mfrac><mo>]</mo><mo>&#160;</mo><mo>=</mo><mn>4</mn></math></p>",
                    solution_hi: "<p>23.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>-</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mfrac><mn>31</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>10</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>6</mn></mfrac><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mfrac><mn>13</mn><mn>3</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>150</mn><mn>33</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23.<span style=\"font-family: Times New Roman;\"> The value of 18</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>-</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo></math>is:</span></p>",
                    question_hi: "<p>23.<span style=\"font-family: Times New Roman;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>-</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo></math></span><span style=\"font-family: Baloo;\">का मान है:</span></p>",
                    options_en: ["<p>4</p>", "<p>5</p>", 
                                "<p>2</p>", "<p>8</p>"],
                    options_hi: ["<p>4</p>", "<p>5</p>",
                                "<p>2</p>", "<p>8</p>"],
                    solution_en: "<p>23.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>-</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mfrac><mn>31</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>10</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>6</mn></mfrac><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mfrac><mn>13</mn><mn>3</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>150</mn><mn>33</mn></mfrac><mo>]</mo><mo>&#160;</mo><mo>=</mo><mn>4</mn></math></p>",
                    solution_hi: "<p>23.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>-</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mo>(</mo><mfrac><mn>31</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>10</mn><mn>3</mn></mfrac><mo>)</mo><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mo>{</mo><mfrac><mn>29</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>11</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>6</mn></mfrac><mo>}</mo><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>7</mn><mn>33</mn></mfrac><mo>+</mo><mfrac><mn>13</mn><mn>3</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mn>200</mn><mn>11</mn></mfrac><mo>&#247;</mo><mo>[</mo><mfrac><mn>150</mn><mn>33</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24.<span style=\"font-family: Times New Roman;\"> An article is listed at ₹5,000 and two successive discounts of 12% and 12% are given on it. How much will the seller gain or lose if he gives a single discount of 24%?</span></p>",
                    question_hi: "<p>24.<span style=\"font-family: Baloo;\"> किसी वस्तु का अंकित मूल्य ₹5,000 में सूचीबद्ध किया गया है और उस पर 12% और 12% की दो क्रमागत छूट दी जाती है। यदि विक्रेता 24% की एकल छूट देता है तो उसे होने वाला लाभ या हानि ज्ञात कीजिये?</span></p>",
                    options_en: ["<p>Loss ₹72</p>", "<p>Loss ₹64</p>", 
                                "<p>Profit ₹64</p>", "<p>Profit ₹72</p>"],
                    options_hi: ["<p>हानि ₹72</p>", "<p>हानि ₹64</p>",
                                "<p>लाभ ₹64</p>", "<p>लाभ ₹72</p>"],
                    solution_en: "<p>24 (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the cost price = 100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Two successive discount of 12% = -12-12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 22.56%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Selling price = 100 - 22.56 = 77.44</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Single discount = 24%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Loss = 24% - 22.56% = 1.44%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1.44% of 5000 = Rs. 72 loss</span></p>",
                    solution_hi: "<p>24 (a)</p>\r\n<p><span style=\"font-family: Baloo;\">माना लागत मूल्य = 100</span></p>\r\n<p><span style=\"font-family: Baloo;\">12% की लगातार दो छूट = -12-12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 22.56%</span></p>\r\n<p><span style=\"font-family: Baloo;\">विक्रय मूल्य = 100 - 22.56 = 77.44</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Single discount = 24%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Loss = 24% - 22.56% = 1.44%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1.44% of 5000 = Rs. 72 loss</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24.<span style=\"font-family: Times New Roman;\"> An article is listed at ₹5,000 and two successive discounts of 12% and 12% are given on it. How much will the seller gain or lose if he gives a single discount of 24%?</span></p>",
                    question_hi: "<p>24.<span style=\"font-family: Baloo;\"> किसी वस्तु का अंकित मूल्य ₹5,000 में सूचीबद्ध किया गया है और उस पर 12% और 12% की दो क्रमागत छूट दी जाती है। यदि विक्रेता 24% की एकल छूट देता है तो उसे होने वाला लाभ या हानि ज्ञात कीजिये?</span></p>",
                    options_en: ["<p>Loss ₹72</p>", "<p>Loss ₹64</p>", 
                                "<p>Profit ₹64</p>", "<p>Profit ₹72</p>"],
                    options_hi: ["<p>हानि ₹72</p>", "<p>हानि ₹64</p>",
                                "<p>लाभ ₹64</p>", "<p>लाभ ₹72</p>"],
                    solution_en: "<p>24 (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the cost price = 100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Two successive discount of 12% = -12-12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 22.56%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Selling price = 100 - 22.56 = 77.44</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Single discount = 24%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Loss = 24% - 22.56% = 1.44%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1.44% of 5000 = Rs. 72 loss</span></p>",
                    solution_hi: "<p>24 (a)</p>\r\n<p><span style=\"font-family: Baloo;\">माना लागत मूल्य = 100</span></p>\r\n<p><span style=\"font-family: Baloo;\">12% की लगातार दो छूट = -12-12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 22.56%</span></p>\r\n<p><span style=\"font-family: Baloo;\">विक्रय मूल्य = 100 - 22.56 = 77.44</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Single discount = 24%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Loss = 24% - 22.56% = 1.44%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1.44% of 5000 = Rs. 72 loss</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> The given pie charts show the distribution of students of graduate and postgraduate levels in five different institutes, P, Q, R, S and T, in a town. Study the pie charts and answer the question that follows.</span></p>\r\n<p>(I) Total number of students of graduate level in five different institutes= 12400</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_83901873911648275853214.jpg\" /></p>\r\n<p>(ii) Total number of students of postgraduate level in five different institutes = 8000&nbsp;</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_22294356621648275875751.jpg\" /></p>",
                    question_hi: "<p>25.<span style=\"font-family: Baloo;\"> दिए गए पाई चार्ट किसी शहर के पांच विभिन्न संस्थानों, P, Q, R, S और T में स्नातक और स्नातकोत्तर स्तर के छात्रों का बंटन दर्शाते हैं। पाई चार्ट का अध्ययन करें और उसके बाद दिए गए प्रश्न का उत्तर दें।</span></p>\r\n<p>(I) पांच विभिन्न संस्थानों में&nbsp; स्नातक स्तर के छात्रों की कुल संख्या = 12400</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_29376782611648544411733.jpg\" /></p>\r\n<p>&nbsp;</p>\r\n<p>(ii) पांच विभिन्न संस्थानों में स्नातकोत्तर स्तर के छात्रों की कुल संख्या = 8000</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_3225298721648544428585.jpg\" /></p>",
                    options_en: ["<p>5216</p>", "<p>5126</p>", 
                                "<p>5116</p>", "<p>4116</p>"],
                    options_hi: ["<p>5216</p>", "<p>5126</p>",
                                "<p>5116</p>", "<p>4116</p>"],
                    solution_en: "<p>25.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total number of graduate level in Q = 29% of 12400 = 3596</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total number of post graduate in Q = 19% of 8000 = 1520</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total = 3596+1520 = 5116</span></p>",
                    solution_hi: "<p>25.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">Q में स्नातक स्तर की कुल संख्या = 12400 का 29% = 3596</span></p>\r\n<p><span style=\"font-family: Baloo;\">Q में स्नातकोत्तर की कुल संख्या = 8000 का 19% = 1520</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल = 3596 1520 = 5116</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> The given pie charts show the distribution of students of graduate and postgraduate levels in five different institutes, P, Q, R, S and T, in a town. Study the pie charts and answer the question that follows.</span></p>\r\n<p>(I) Total number of students of graduate level in five different institutes= 12400</p>\r\n<p><img src=\"images/mceu_83901873911648275853214.jpg\" /></p>\r\n<p>(ii) Total number of students of postgraduate level in five different institutes = 8000&nbsp;</p>\r\n<p><img src=\"images/mceu_22294356621648275875751.jpg\" /></p>",
                    question_hi: "<p>25.<span style=\"font-family: Baloo;\"> दिए गए पाई चार्ट किसी शहर के पांच विभिन्न संस्थानों, P, Q, R, S और T में स्नातक और स्नातकोत्तर स्तर के छात्रों का बंटन दर्शाते हैं। पाई चार्ट का अध्ययन करें और उसके बाद दिए गए प्रश्न का उत्तर दें।</span></p>\r\n<p>(I) पांच विभिन्न संस्थानों में&nbsp; स्नातक स्तर के छात्रों की कुल संख्या = 12400</p>\r\n<p><img src=\"images/mceu_29376782611648544411733.jpg\" /></p>\r\n<p>&nbsp;</p>\r\n<p>(ii) पांच विभिन्न संस्थानों में स्नातकोत्तर स्तर के छात्रों की कुल संख्या = 8000</p>\r\n<p><img src=\"images/mceu_3225298721648544428585.jpg\" /></p>",
                    options_en: ["<p>5216</p>", "<p>5126</p>", 
                                "<p>5116</p>", "<p>4116</p>"],
                    options_hi: ["<p>5216</p>", "<p>5126</p>",
                                "<p>5116</p>", "<p>4116</p>"],
                    solution_en: "<p>25.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total number of graduate level in Q = 29% of 12400 = 3596</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total number of post graduate in Q = 19% of 8000 = 1520</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total = 3596+1520 = 5116</span></p>",
                    solution_hi: "<p>25.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">Q में स्नातक स्तर की कुल संख्या = 12400 का 29% = 3596</span></p>\r\n<p><span style=\"font-family: Baloo;\">Q में स्नातकोत्तर की कुल संख्या = 8000 का 19% = 1520</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल = 3596 1520 = 5116</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>