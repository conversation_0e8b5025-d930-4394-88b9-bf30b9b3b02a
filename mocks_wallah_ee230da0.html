<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 19</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">19</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 17
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 18,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> When X is subtracted from each of 19, 28, 55, and 91, the numbers obtained in this order are in proportion. What is the value of X?</span></p>",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">जब</span><span style=\"font-family: Cambria Math;\"> 19, 28, 55 </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> 91 </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">प्रत्येक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">से</span><span style=\"font-family: Cambria Math;\"> X </span><span style=\"font-family: Nirmala UI;\">घटाया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">इस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्रम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">प्राप्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">संख्याएँ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">समानुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">होती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">हैं।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">एक्स</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मूल्य</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>8</p>", "<p>7</p>", 
                                "<p>9</p>", "<p>5</p>"],
                    options_hi: ["<p>8</p>", "<p>7</p>",
                                "<p>9</p>", "<p>5</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">Let the number to be subtracted is x </span><span style=\"font-family: Cambria Math;\">so,</span><br><span style=\"font-family: Cambria Math;\">The required ratio is </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>19</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>28</mn><mo>-</mo><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>55</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>91</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math></span><br><span style=\"font-family: Cambria Math;\">On cross multiply</span><br><span style=\"font-family: Cambria Math;\">1729 -110x = 1540-83x</span><br><span style=\"font-family: Cambria Math;\">189 = 27x &rArr; So, x = 7</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">माना कि घटाई जाने वाली संख्या x है,</span><br>आवश्यक अनुपात है =<strong id=\"docs-internal-guid-c7ff8281-7fff-475b-1dbe-e8c58ac655b0\">&nbsp;</strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>19</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>28</mn><mo>-</mo><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>55</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>91</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math><br>1729 -110x = 1540-83x<br>189 = 27x इसलिए , x = 7</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> The ratio of two numbers A and B is </span><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 8. If 5 is added to each of A and B, then the ratio of A and B becomes </span><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3. The sum of A and B is:</span></p>",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">दो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">संख्याओं</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 8</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">प्रत्येक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">जोड़</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">दिया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">जाए</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 2 : 3 </span><span style=\"font-family: Nirmala UI;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है।</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">योग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">:</span></p>",
                    options_en: ["<p>42</p>", "<p>78</p>", 
                                "<p>65</p>", "<p>91</p>"],
                    options_hi: ["<p>42</p>", "<p>78</p>",
                                "<p>65</p>", "<p>91</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">According to the question</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>5</mn></mrow><mrow><mn>8</mn><mi>x</mi><mo>+</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br><span style=\"font-family: Cambria Math;\">15x + 15 = 16x + 10 &rArr; x = 5</span><br><span style=\"font-family: Cambria Math;\">Sum of the numbers = 13x = 65</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Nirmala UI;\">प्रश्न</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुसार</span><span style=\"font-family: Cambria Math;\">,</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>5</mn></mrow><mrow><mn>8</mn><mi>x</mi><mo>+</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math>= </span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">15x + 15 = 16x + 10 , x = 5</span><br><span style=\"font-family: Nirmala UI;\">संख्याओं</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">योग</span><span style=\"font-family: Cambria Math;\"> = 13x = 65</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> A, B and C divide a certain sum of money among themselves. The average of the amounts with them is 4520. Share of A is 10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">% more than share of B and 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">% less than share of C. What is the share of B (in rupee)?</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">A, B </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">निश्चित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">राशि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">आपस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">बांट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">लेते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">हैं।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">उनके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">पास</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">राशियों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">औसत</span><span style=\"font-family: Cambria Math;\"> 4520 </span><span style=\"font-family: Nirmala UI;\">है।</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">हिस्सा</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">हिस्से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">से</span><span style=\"font-family: Cambria Math;\"> 10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Nirmala UI;\">अधिक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">हिस्से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">से</span><span style=\"font-family: Cambria Math;\"> 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Nirmala UI;\">कम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है।</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">हिस्सा</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">रुपये</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>3500<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p>5976<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>3600<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>3984</p>"],
                    options_hi: ["<p>3500</p>", "<p>5976</p>",
                                "<p>3600<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>3984</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">Ratio of A and B = 110<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> : 100</span><br><span style=\"font-family: Cambria Math;\">&nbsp;&rArr; 83 : 75</span><br>Ratio of A and C = 66<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>: 100&nbsp; <span style=\"font-family: Cambria Math;\">&rArr; </span>2 : 3<br>Balancing the ratio : 166 : 150 : 249<br>Average of A, B and C<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>166</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>249</mn></mrow><mn>3</mn></mfrac></math> <span style=\"font-family: Cambria Math;\">&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>565</mn><mn>3</mn></mfrac></math> </span>unit<br>So, 1 unit =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4520</mn><mstyle displaystyle=\"true\"><mfrac><mn>565</mn><mn>3</mn></mfrac></mstyle></mfrac></math>=24<br>Share of B = 24&times;150 = 3600</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">A और B का अनुपात <strong id=\"docs-internal-guid-ae0f00f6-7fff-e9f1-da92-b26d824067b0\">= </strong>110<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> : 100</span><br><span style=\"font-family: Cambria Math;\">&nbsp;&rArr; 83 : 75</span><br>A और C का अनुपात = 66<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>: 100&nbsp; <span style=\"font-family: Cambria Math;\">&rArr; </span>2 : 3<br>अनुपात को संतुलित करने पर :<strong id=\"docs-internal-guid-aafc638e-7fff-9071-f5b3-0ff6dfdac1f0\"> </strong>166 : 150 : 249<br>A, B और C का औसत&nbsp; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>166</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>249</mn></mrow><mn>3</mn></mfrac></math> <span style=\"font-family: Cambria Math;\">&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>565</mn><mn>3</mn></mfrac></math> </span>unit<br>इसलिए, 1 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4520</mn><mstyle displaystyle=\"true\"><mfrac><mn>565</mn><mn>3</mn></mfrac></mstyle></mfrac></math>=24<br>&nbsp;B का हिस्सा = 24&times;150 = 3600</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Hridaya</span><span style=\"font-family: Cambria Math;\"> opened her piggy bank and found coins of denomination Rs.1, Rs.2, Rs.5, and Rs.10 in the ratio </span><span style=\"font-family: Cambria Math;\">10 :</span><span style=\"font-family: Cambria Math;\"> 5 : 2 : 1. If there are 72 coins in all, then how much money (in rupee) was there in the piggy bank in the f</span><span style=\"font-family: Cambria Math;\">orm of coins?</span></p>",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">हृदय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">ने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अपना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">गुल्लक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">खोला</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> 10</span><span style=\"font-family: Cambria Math;\"> : 5 : 2 : 1 </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> 1</span><span style=\"font-family: Nirmala UI;\">रुपये</span><span style=\"font-family: Cambria Math;\">, 2</span><span style=\"font-family: Nirmala UI;\">रुपये</span><span style=\"font-family: Cambria Math;\">, 5 </span><span style=\"font-family: Nirmala UI;\">रुपये</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Nirmala UI;\">रुपये</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मूल्य</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">सिक्के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">पाए।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कुल</span><span style=\"font-family: Cambria Math;\"> 72 </span><span style=\"font-family: Nirmala UI;\">सिक्के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">हैं</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कितना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">पैसा</span><span style=\"font-family: Cambria Math;\"> ( </span><span style=\"font-family: Nirmala UI;\">रुपये</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">सिक्कों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">रूप</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">गुल्लक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">था</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>160<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>90<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>72<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>100</p>"],
                    options_hi: ["<p>160<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>90<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>72<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>100</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">Ratio of coins = </span><span style=\"font-family: Cambria Math;\">10 :</span><span style=\"font-family: Cambria Math;\"> 5 : 2 : 1 = 18 units</span><br><span style=\"font-family: Cambria Math;\">Total coins = 72 = 18 units, so</span><br><span style=\"font-family: Cambria Math;\">1 unit = 4 coins</span><br><span style=\"font-family: Cambria Math;\">Number of each coins are </span><span style=\"font-family: Cambria Math;\">40 :</span><span style=\"font-family: Cambria Math;\"> 20 : 8 : 4</span><br><span style=\"font-family: Cambria Math;\">Total denomination = 40 + 40 + 40 + 40 = 160 rupees </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Nirmala UI;\">सिक्कों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> = 10 : 5 : 2 : 1 = 18 units</span><br><span style=\"font-family: Nirmala UI;\">कुल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">सिक्के</span><span style=\"font-family: Cambria Math;\"> = 72 = 18 units,</span><br><span style=\"font-family: Nirmala UI;\">इसलिए</span><span style=\"font-family: Cambria Math;\"> 1 unit = 4 </span><span style=\"font-family: Nirmala UI;\">सिक्के</span><br><span style=\"font-family: Nirmala UI;\">प्रत्येक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">सिक्के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">संख्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\"> = 40 : 20 : 8 : 4</span><br><span style=\"font-family: Nirmala UI;\">कुल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मूल्यवर्ग</span><span style=\"font-family: Cambria Math;\"> = 40 + 40 + 40 + 40</span><br><span style=\"font-family: Cambria Math;\">= 160 rupees </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Fourth proportion to 12, 18 and 6 is the same as the third proportion to k and 6. What is the value of K?</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> 12, 18 </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">चौथा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> k </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">तीसरे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">समान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है।</span><span style=\"font-family: Cambria Math;\"> K </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?&nbsp;</span></p>",
                    options_en: ["<p>3&radic;6</p>", "<p>13.5</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>3&radic;6</p>", "<p>13.5</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">Fourth proportion =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>12</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&nbsp;= 9</span><br><span style=\"font-family: Cambria Math;\">Third proportion = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>&#215;</mo><mn>9</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 4</span></p>",
                    solution_hi: "<p>.5<span style=\"font-family: Cambria Math;\">.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Nirmala UI;\">चौथा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>12</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 9</span><br><span style=\"font-family: Nirmala UI;\">तीसरा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>&#215;</mo><mn>9</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 4</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> What is the difference in the mean proportional between 1.8 and 3.2 and the third proportional to 5 and 3?</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">1.8 </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> 3.2 </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">बीच</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मध्यानुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">त</span><span style=\"font-family: Nirmala UI;\">थ</span><span style=\"font-family: Nirmala UI;\">ा</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">तृतीयानुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अंतर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>0.6</p>", "<p>0.4</p>", 
                                "<p>0.5</p>", "<p>0.7</p>"],
                    options_hi: ["<p>0.6</p>", "<p>0.4</p>",
                                "<p>0.5</p>", "<p>0.7</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a) Mean proportional</span><br><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><msqrt><mn>1</mn><mo>.</mo><mn>8</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>2</mn></msqrt><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>.</mo><mn>4</mn><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\">Third proportional =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> = 1.8</span><br>Difference = 2.4 - 1.8 = 0.6</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">माध्य आनुपातिक<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><msqrt><mn>1</mn><mo>.</mo><mn>8</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>2</mn></msqrt><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>.</mo><mn>4</mn><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\">तीसरा आनुपातिक&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> = 1.8</span><br>अंतर = 2.4 - 1.8 = 0.6</p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">The ratio of present ages of A and B is </span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 8. After 6 years from now, the ratio of their ages will be </span><span style=\"font-family: Cambria Math;\">8 :</span><span style=\"font-family: Cambria Math;\"> 9. If C&rsquo;s present age is 10 years more than the present age of </span><span style=\"font-family: Cambria Math;\">A</span><span style=\"font-family: Cambria Math;\">, then the present age (in years) of C is:</span></p>",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">वर्तमान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">आयु</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 7:8 </span><span style=\"font-family: Nirmala UI;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अब</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">से</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Nirmala UI;\">वर्ष</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">बाद</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">उनकी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">आयु</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 8:9 </span><span style=\"font-family: Nirmala UI;\">होगा।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">वर्तमान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">आयु</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">वर्तमान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">आयु</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">से</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Nirmala UI;\">वर्ष</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अधिक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">वर्तमान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">आयु</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">वर्षों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\">) C </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">:</span></p>",
                    options_en: ["<p>56</p>", "<p>52</p>", 
                                "<p>59</p>", "<p>45</p>"],
                    options_hi: ["<p>56</p>", "<p>52</p>",
                                "<p>59</p>", "<p>45</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi><mo>+</mo><mn>6</mn></mrow><mrow><mn>8</mn><mi>x</mi><mo>+</mo><mn>6</mn></mrow></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>9</mn></mfrac></math><br>63x + 54 = 64x + 48&nbsp; &rArr; x = 6<br>Present Age of A = 7x = 42<br>Present Age of C = 42 + 10 = 52</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi><mo>+</mo><mn>6</mn></mrow><mrow><mn>8</mn><mi>x</mi><mo>+</mo><mn>6</mn></mrow></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>9</mn></mfrac></math><br>63x + 54 = 64x + 48&nbsp; &rArr; x = 6<br>A की वर्तमान आयु = 7x = 42<br>C की वर्तमान आयु = 42 + 10 = 52</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Find the ratio between the fourth proportional of 12, 16, 6 and the third proportional of 4, 6.</span></p>",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> 12, 16, 6 </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">चौथे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">समानुपाती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> 4, 6 </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">तीसरे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">समानुपाती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">बीच</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कीजिए।</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">11 :</span><span style=\"font-family: Cambria Math;\"> 5</span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 2</span><span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><span style=\"font-family: Cambria Math;\">8 :</span><span style=\"font-family: Cambria Math;\"> 9</span></p>"],
                    options_hi: ["<p>11 : 5<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>3 : 2</p>",
                                "<p>4 : 3<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>8 : 9</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\">Fourth proportional =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>a</mi><mi>b</mi></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>c</mi><mi>d</mi></mfrac></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>16</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mi>x</mi></mfrac></math>&rArr; x = 8</span><br><span style=\"font-family: Cambria Math;\">Third proportional =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>4</mn></mfrac></math> = 9</span><br><span style=\"font-family: Cambria Math;\">Ratio = 8 : 9</span></p>\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Nirmala UI;\">चौथा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">आनुपातिक</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>a</mi><mi>b</mi></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>c</mi><mi>d</mi></mfrac></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>16</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mi>x</mi></mfrac></math>&rArr; x = 8</span><br><span style=\"font-family: Nirmala UI;\">तीसरा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">आनुपातिक</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>4</mn></mfrac></math> = 9</span><br><span style=\"font-family: Cambria Math;\">Ratio = </span><span style=\"font-family: Cambria Math;\">8 :</span><span style=\"font-family: Cambria Math;\"> 9</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> A sum of ₹6342 is divided amongst A, B, C and D is the ratio </span><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 4 : 8 : 6. What is the difference between the shares of B and D?</span></p>",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> ₹6342 </span><span style=\"font-family: Cambria Math;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">राशि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">को</span><span style=\"font-family: Cambria Math;\"> A, B, C </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Cambria Math;\">म</span><span style=\"font-family: Cambria Math;\">ें</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">विभाजित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जिसका</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 3:4:8:6 </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हिस्सों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अंतर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>₹302<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>₹306<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>₹604<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>₹1510</p>"],
                    options_hi: ["<p>₹302</p>", "<p>₹306</p>",
                                "<p>₹604<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>₹1510</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">Sum of ratio = 3 + 4 + 8 + 6 = 21</span><br><span style=\"font-family: Cambria Math;\">Difference of Share of B and D = 2</span><br><span style=\"font-family: Cambria Math;\">Required difference = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>21</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 6342 = 604</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">योग</span><span style=\"font-family: Cambria Math;\"> = 3 + 4 + 8 + 6 = 21</span><br><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हिस्से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अंतर</span><span style=\"font-family: Cambria Math;\"> = 2</span><br><span style=\"font-family: Cambria Math;\">आवश्यक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अंतर</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>21</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 6342 = 604</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Keshav</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">Surjeet</span><span style=\"font-family: Cambria Math;\"> and Thomas started a business with investments in the ratio </span><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3 : 4. The ratio of their period of investments is </span><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 6 : 9. Twenty percent of the profit was spent on rent and maintenance of the office. Remaining profit was distributed am</span><span style=\"font-family: Cambria Math;\">ong themselves. If the difference in the share of profit of </span><span style=\"font-family: Cambria Math;\">Keshav</span><span style=\"font-family: Cambria Math;\"> and </span><span style=\"font-family: Cambria Math;\">Surjeet</span><span style=\"font-family: Cambria Math;\"> is Rs.7264 then how much is the total profit (in </span><span style=\"font-family: Cambria Math;\">Rs</span><span style=\"font-family: Cambria Math;\">.)?</span></p>\n<p>&nbsp;</p>",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">केशव</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">सुरजीत</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">थॉमस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ने</span><span style=\"font-family: Cambria Math;\"> 2:3:4 </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">निवेश</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">साथ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">व्यवसाय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">शुरू</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">किया।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">उनके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">निवेश</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अवधि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 5:6:9 </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">लाभ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Cambria Math;\">कार्यालय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">किराए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और </span><span style=\"font-family: Cambria Math;\">रखरखाव</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">खर्च</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">गया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">था।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">शेष</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">लाभ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">आपस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बांट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">लिया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">था।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">केशव</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">सुरजीत</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">लाभ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हिस्से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अं</span><span style=\"font-family: Cambria Math;\">तर</span><span style=\"font-family: Cambria Math;\"> 7264 </span><span style=\"font-family: Cambria Math;\">रुपये</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कुल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">लाभ</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">रु</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">कितना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>51060<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>58112<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>46490<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>72640</p>"],
                    options_hi: ["<p>51060<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>58112<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>46490<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>72640</p>"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(d)</span><br>Ratio of investments = 2&nbsp; :&nbsp; 3&nbsp; :&nbsp; 4<br>Ratio of period = 5&nbsp; :&nbsp; 6&nbsp; :&nbsp; 9<br>Ratio of profit =<br>&nbsp;Keshav : Surjeet&nbsp; : &nbsp; Thomas<br>&nbsp;&nbsp;2 &times; 5&nbsp; &nbsp; :&nbsp; &nbsp;3 &times;6&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;4 &times; 9<br>&nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 18&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 36<br>&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp;: &nbsp; &nbsp; 9 &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 18<br>Difference of profit of Keshav and Surjeet = 9 - 5 = 4 unit<br>Total share of profit = 5 + 9 + 18 = 32 unit, A/Q<br>4 &nbsp;&rarr; &nbsp;7264 and 32 &rarr; 58112<br>Let total profit = 100%<br>Profit shared = 80%<br>80% &nbsp;&rarr;58112 &rArr; 100% &nbsp;&rarr; 72640</p>",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(d)</span><br>निवेश का अनुपात = 2&nbsp; :&nbsp; 3&nbsp; :&nbsp; 4<br>अवधि का अनुपात = 5&nbsp; :&nbsp; 6&nbsp; :&nbsp; 9<br>लाभ का अनुपात =<br>&nbsp; &nbsp;केशव&nbsp; &nbsp;: सुरजीत&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; थॉमस<br>&nbsp; 2 &times; 5&nbsp; &nbsp; :&nbsp; &nbsp;3 &times; 6&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;4 &times; 9<br>&nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;18&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 36<br>&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 18<br>केशव और सुरजीत के लाभ का अंतर <br>= 9 - 5 = 4 unit<br>लाभ का कुल हिस्सा = 5 + 9 + 18 = 32 unit<br>A/Q<br>4 &nbsp;&rarr; 7264<br>32 &rarr; 58112<br>माना कुल लाभ = 100%<br>वितरित लाभ = 80%<br>80% &rarr; 58112<br>100% &rarr; 72640</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Alloy A contains metal x and y in the ratio </span><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 2 and alloy B contains these metals in the ratio 3 : 4. Alloy C is prepared by mixing A and B in the ratio </span><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 5. The percentage of y in alloy C is:</span></p>",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मिश्र</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">धातु</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">धातु</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 5:2 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मिश्र</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">धातु</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ये</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">धातुएँ</span><span style=\"font-family: Cambria Math;\"> 3:4 </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हैं।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मिश्र</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">धातु</span><span style=\"font-family: Cambria Math;\"> C, A </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">को</span><span style=\"font-family: Cambria Math;\"> 4:5 </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मिलाकर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">तैयार</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मिश्र</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">धातु</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">प्रतिशत</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>44</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>33</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>66</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>55</mn><mfrac><mn>5</mn><mn>9</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>44</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>33</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>66</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>55</mn><mfrac><mn>5</mn><mn>9</mn></mfrac></math></p>"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(a)</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; X &nbsp; : &nbsp; Y<br>A &nbsp; &nbsp;&rarr; &nbsp;(5 &nbsp; : &nbsp; 2) &times; 4<br>B &nbsp; &nbsp;&rarr; &nbsp;(3 &nbsp; : &nbsp; 4) &times; 5<br>A&nbsp; &nbsp; &nbsp; 20&nbsp; : &nbsp; 8<br>B&nbsp; &nbsp; &nbsp; <span style=\"text-decoration: underline;\">15&nbsp; : &nbsp; 20<br></span>C&nbsp; &nbsp; &nbsp; 35&nbsp; : &nbsp; 28<br>Percentage of Y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>63</mn></mfrac></math> &times; 100 = 44<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math>%</p>",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(a)</span><br><span style=\"font-family: Cambria Math;\">&nbsp; </span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;X &nbsp; : &nbsp; Y<br>A &nbsp; &nbsp;&rarr; &nbsp;(5 &nbsp; : &nbsp; 2) &times; 4<br>B &nbsp; &nbsp;&rarr; &nbsp;(3 &nbsp; : &nbsp; 4) &times; 5<br>A&nbsp; &nbsp; &nbsp; 20&nbsp; : &nbsp; 8<br>B&nbsp; &nbsp; &nbsp; <span style=\"text-decoration: underline;\">15&nbsp; : &nbsp; 20<br></span>C&nbsp; &nbsp; &nbsp; 35&nbsp; : &nbsp; 28<br>Y का प्रतिशत&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>63</mn></mfrac></math> &times; 100 = 44<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> The ratio of monthly incomes of A and B is </span><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 5 and that of their monthly expenditure is 3 : 8. If the income of A is equal to the expenditure of B, then what is the ratio of savings of A and B?</span></p>",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मासिक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">आय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">उनके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मासिक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">व्यय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 3 : 8 </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">आय</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">व्यय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बराबर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बचत</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">8 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>", "<p><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 5</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 2</span></p>", "<p><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 8</span></p>"],
                    options_hi: ["<p>8 : 3</p>", "<p>2 : 5</p>",
                                "<p>5 : 2</p>", "<p>3 : 8</p>"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(c) </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">Ratio of incomes = </span><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 5 = 8 : 10<br></span><span style=\"font-family: Cambria Math;\">Ratio of expenditure = </span><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 8<br></span><span style=\"font-family: Cambria Math;\">Ratio of saving = </span><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 2</span></p>",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><span style=\"font-family: Cambria Math;\">आय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> = 4 : 5 = 8 : 10<br></span><span style=\"font-family: Cambria Math;\">व्यय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> = 3 : 8<br></span><span style=\"font-family: Cambria Math;\">बचत</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> = 5 : 2</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Two numbers are in </span><span style=\"font-family: Cambria Math;\">the ratio </span><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3. If 5 is subtracted from the first number and six is added to the second number, then the ratio becomes </span><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 12. What would the ratio become when </span><span style=\"font-family: Cambria Math;\">eight </span><span style=\"font-family: Cambria Math;\">is</span><span style=\"font-family: Cambria Math;\"> added to each number?</span></p>",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">दो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">संख्याएँ</span><span style=\"font-family: Cambria Math;\"> 2:3 </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हैं।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">पहली</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">संख्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Cambria Math;\">घटाया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">दूसरी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">संख्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">छह</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जोड़ा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 5:12 </span><span style=\"font-family: Cambria Math;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">प्रत्येक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">संख्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">आठ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जोड़ने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होगा</span><span style=\"font-family: Cambria Math;\">?&nbsp;</span></p>",
                    options_en: ["<p>14 : 11<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>14 : 19&nbsp;<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>11 : 14&nbsp;<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>19 : 14</p>"],
                    options_hi: ["<p>14 <span style=\"font-family: Cambria Math;\">: 11</span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p>14 : 19</p>",
                                "<p>11 : 14</p>", "<p>19 : 14</p>"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(b)</span><br>1st number = 2x<br>2nd number = 3x<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi><mo>-</mo><mn>5</mn></mrow><mrow><mn>3</mn><mi>x</mi><mo>+</mo><mn>6</mn></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>22</mn></mfrac></math><br>24x - 60 = 15x + 30<br>9x = 90&nbsp;<br>x = 10<br>1st number = 20 &rArr; 2nd number = 30<br>New ratio = 20 + 8&nbsp; &nbsp; : &nbsp; 30 + 8<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; 28&nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 38&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; 14&nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 19</p>",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(b)</span><br>पहला नंबर = 2x<br>दूसरा नंबर = 3x<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi><mo>-</mo><mn>5</mn></mrow><mrow><mn>3</mn><mi>x</mi><mo>+</mo><mn>6</mn></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>22</mn></mfrac></math><br>24x - 60 = 15x + 30<br>9x = 90<br>x = 10<br>पहला नंबर = 20<br>दूसरा नंबर = 30<br>नया अनुपात = 20 + 8&nbsp; &nbsp; : &nbsp; 30 + 8<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; 28&nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 38<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; 14&nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 19</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Fourth proportion to 12, 18, </span><span style=\"font-family: Cambria Math;\">6</span><span style=\"font-family: Cambria Math;\"> is equal to the third proportion to 4, k. What is the value of k?</span></p>",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> 12, 18, 6 </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">चौथा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">k </span><span style=\"font-family: Cambria Math;\">क</span><span style=\"font-family: Cambria Math;\">े</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">तीसरे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बराबर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> k </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>6<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>6.5<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>4</p>"],
                    options_hi: ["<p>6</p>", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p>6.5</p>", "<p>4</p>"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>12</mn><mn>18</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mi>x</mi></mfrac><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mo>&#160;</mo><mn>9</mn><mo>&#8658;</mo><mfrac><mn>4</mn><mi>k</mi></mfrac><mo>=</mo><mfrac><mi>k</mi><mn>9</mn></mfrac><mo>&#8658;</mo><mi>k</mi><mo>=</mo><mn>6</mn></math></p>",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>18</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mn>9</mn><mo>&#8658;</mo><mfrac><mn>4</mn><mi>k</mi></mfrac><mo>=</mo><mfrac><mi>k</mi><mn>9</mn></mfrac><mo>&#8658;</mo><mi>k</mi><mo>=</mo><mn>6</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If x is subtracted from each of 24, 40, 33 and 57, the numbers, so obtained are in proportion. The ratio of (</span><span style=\"font-family: Cambria Math;\">5x</span><span style=\"font-family: Cambria Math;\"> + 12) to (4x + 15) is:</span></p>",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> 24, 40, 33 </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> 57 </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">प्रत्येक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Cambria Math;\">घटाया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाए</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">प्राप्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">संख्याएँ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">समानुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हैं।</span><span style=\"font-family: Cambria Math;\"> (5x + 12) </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> (4x + 15) </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>",
                    options_en: ["<p>4:3</p>", "<p>14:13<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>7:4<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>7:5</p>"],
                    options_hi: ["<p>4 : 3</p>", "<p>14 : 13<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>7 : 4<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>7 : 5</p>"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>40</mn><mo>-</mo><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>33</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>57</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math><br>1368 + x<sup>2</sup> - 81x = 1320+ x<sup>2</sup> - 73x<br>8x = 48 &rArr; x = 6<br>Ratio: (5x + 12)&nbsp; &nbsp; :&nbsp; (4x + 15)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 42&nbsp; &nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 39&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;=&nbsp; 14&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 13</p>",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>40</mn><mo>-</mo><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>33</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>57</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math><br>1368 + x<sup>2</sup> - 81x = 1320+ x<sup>2</sup> - 73x<br>8x = 48 &rArr; x = 6<br>अनुपात : (5x + 12)&nbsp; &nbsp; :&nbsp; (4x + 15)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = 42&nbsp; &nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 39&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; 14&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 13</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> When x is subtracted from each of the numbers 54, 49, 22 and 21, the numbers so obtained are in proportion. The ratio of (8x - 25) to (7x - 26) is:</span></p>",
                    question_hi: "<p>16<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जब</span><span style=\"font-family: Cambria Math;\"> 54, 49, 22 </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> 21 </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">प्रत्येक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">संख्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Cambria Math;\">घटाया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">प्राप्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">संख्याएँ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">समानुपाती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हैं।</span><span style=\"font-family: Cambria Math;\"> (8x - 25) </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> (7x - 26) </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> SSC CGL 23/8/2021 (Evening)</span></p>",
                    options_en: ["<p>29:24<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>15:13<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>27:26<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>5:4</p>"],
                    options_hi: ["<p>29 : 24<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>15 <span style=\"font-family: Cambria Math;\">: 13</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>27 : 26<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>5 : 4</p>"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(a)</span><br>Using componendo &amp; dividendo<br>(22 - 21) &times; (54 - x) = (54 - 49) &times; (22 - x)<br>54 - x = 110 - 5x &rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>4</mn></mfrac></math>= 14<br>Ratio - (8&times;14 -25)&nbsp; &nbsp; :&nbsp; (7&times;14 - 26)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; 87&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 72 &nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; 29&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 24</p>",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(a)</span><br>(22-21) &times; (54 - x) = (54 - 49) &times; (22 -x )<br>54 - x = 110 - 5x<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>4</mn></mfrac></math>= 14<br>अनुपात - (8 &times; 14 - 25)&nbsp; :&nbsp; (7 &times; 14 - 26)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;=&nbsp; &nbsp;87&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;: &nbsp; &nbsp; &nbsp; &nbsp; 72&nbsp;&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; 29&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;24</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If p is the third proportional to 3, 9, then what is the fourth proportional to 6, p, 4?</span></p>",
                    question_hi: "<p>17<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> p, 3, 9 </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">तीसरा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">समानुपाती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> 6, p, 4 </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">चौथा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">समानुपाती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>", "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>10<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>18</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>10<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>18</p>"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(d)</span><br>Third proportional = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>9</mn></mrow><mn>3</mn></mfrac></math> = 27<br>Fourth proportional = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>27</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>x</mi></mfrac></math> &rArr; x = 18</p>",
                    solution_hi: "<p>17<span style=\"font-family: Cambria Math;\">. (d)</span><br>तीसरा आनुपातिक = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>9</mn></mrow><mn>3</mn></mfrac></math>=27<br>चौथा आनुपातिक&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>27</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>x</mi></mfrac></math><br>x = 18</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Monthly salaries of Anil and </span><span style=\"font-family: Cambria Math;\">Kumud</span><span style=\"font-family: Cambria Math;\"> are in the ratio 19 : 17, If Anil and </span><span style=\"font-family: Cambria Math;\">Kumud</span><span style=\"font-family: Cambria Math;\"> get salary hike of </span><span style=\"font-family: Cambria Math;\">Rs</span><span style=\"font-family: Cambria Math;\">. 2000 and </span><span style=\"font-family: Cambria Math;\">Rs</span><span style=\"font-family: Cambria Math;\">. 1000 respectively, then the ratio in their salaries becomes 8 : 7. What is the present salary of </span><span style=\"font-family: Cambria Math;\">Kumud</span><span style=\"font-family: Cambria Math;\"> (in </span><span style=\"font-family: Cambria Math;\">Rs</span><span style=\"font-family: Cambria Math;\">) ?</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>",
                    question_hi: "<p>18<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">अनिल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कुमुद</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मासिक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">वेतन</span><span style=\"font-family: Cambria Math;\"> 19:17 </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनिल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कुमुद</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">वेतन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्रमशः</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">रु</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">2000 </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">रु</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> 1000 </span><span style=\"font-family: Cambria Math;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">वृद्धि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">उनके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">वेतन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">8 :</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Cambria Math;\">ह</span><span style=\"font-family: Cambria Math;\">ो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कुमुद</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">वर्तमान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">वेतन</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">रु</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">कितना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>18000</p>", "<p>38000</p>", 
                                "<p>34000</p>", "<p>35000</p>"],
                    options_hi: ["<p>18000</p>", "<p>38000</p>",
                                "<p>34000</p>", "<p>35000</p>"],
                    solution_en: "<p>18<span style=\"font-family: Cambria Math;\">.(c)</span><br>According to the question&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>19</mn><mi>x</mi><mo>+</mo><mn>2000</mn></mrow><mrow><mn>17</mn><mi>x</mi><mo>+</mo><mn>1000</mn></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>7</mn></mfrac></math><br>133x + 14000 = 136x + 8000<br>3x = 6000 &rArr; x&nbsp; = 2000<br>Present salary = 17 &times; 2000 = 34000</p>",
                    solution_hi: "<p>18<span style=\"font-family: Cambria Math;\">.(c)</span><br>प्रश्न के अनुसार<strong id=\"docs-internal-guid-04359ee5-7fff-fcbc-536d-eb95c77644ee\"> </strong>&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>19</mn><mi>x</mi><mo>+</mo><mn>2000</mn></mrow><mrow><mn>17</mn><mi>x</mi><mo>+</mo><mn>1000</mn></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>7</mn></mfrac></math><br>133x + 14000 = 136x + 8000<br>3x = 6000 &rArr; x&nbsp; = 2000<br>वर्तमान वेतन = 17 &times; 2000 = 34000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
                questions.push({
                    id: "19",
                    section: "misc",
                    question_en: "<p>19<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> A certain sum is divided among A, B, C and D such that the ratio of the shares is </span><span style=\"font-family: Cambria Math;\">A :</span><span style=\"font-family: Cambria Math;\"> B : C : D = 4 : 12 : 30 : 45. If the difference between the shares of A and D is ₹5,535, then the total sum (in ₹) </span><span style=\"font-family: Cambria Math;\">is :</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>",
                    question_hi: "<p>19<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">निश्चित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">राशि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">को</span><span style=\"font-family: Cambria Math;\"> A, B, C </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बीच</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">इस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">तरह</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">विभाजित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कि</span><span style=\"font-family: Cambria Math;\"> A: B: C: D </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हिस्सों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुपात</span><span style=\"font-family: Cambria Math;\"> = 4: 12: 30: 45 </span><span style=\"font-family: Cambria Math;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हिस्सों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बीच</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अंतर</span><span style=\"font-family: Cambria Math;\"> ₹ 5,535 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कुल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">योग</span><span style=\"font-family: Cambria Math;\"> (₹ </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">:</span></p>",
                    options_en: ["<p>12285</p>", "<p><span style=\"font-family: Cambria Math;\"> 11000</span></p>", 
                                "<p>12785</p>", "<p>13550</p>"],
                    options_hi: ["<p>12285</p>", "<p>11000</p>",
                                "<p>12785</p>", "<p>13550</p>"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(a)</span><br><span style=\"font-family: Cambria Math;\">According to question difference between A and D is 41 </span><span style=\"font-family: Cambria Math;\">unit</span><br><span style=\"font-family: Cambria Math;\">41 unit = 5535</span><br><span style=\"font-family: Cambria Math;\">Required sum = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5535</mn><mn>41</mn></mfrac><mo>&#215;</mo></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">91 = 12,285</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19 .(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">प्रश्न</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अनुसार</span><span style=\"font-family: Cambria Math;\">, A </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बीच</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">अंतर</span><span style=\"font-family: Cambria Math;\"> 41 </span><span style=\"font-family: Cambria Math;\">इकाई</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><br><span style=\"font-family: Cambria Math;\">41 unit = 5535</span><br><span style=\"font-family: Cambria Math;\">आवश्यक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">राशि</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5535</mn><mn>41</mn></mfrac><mo>&#215;</mo></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">91 = 12,285</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: -0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>