<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Which is a nutritional disorder caused by vitamin B3 deficiency, leading to clinical manifestations from the skin, gastrointestinal tract and nervous system?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2335;&#2366;&#2350;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> B3 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2357;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2336;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2340;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2306;&#2340;&#2381;&#2352;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2306;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2376;&#2342;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> &#8203;&#8203;&#8203;&#8203;</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2367;&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Anemia</p>\n", "<p>Pellagra</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Beri Beri </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Scurvy </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2325;&#2381;&#2340;&#2354;&#2366;&#2346;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2354;&#2366;&#2327;&#2381;&#2352;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2352;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2352;&#2381;&#2357;&#2368;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><strong>&nbsp;Pellagra </strong><span style=\"font-weight: 400;\">(B3 - Niacin)</span><strong>. Anemia </strong><span style=\"font-weight: 400;\">- A problem of not having enough healthy red blood cells or hemoglobin to carry oxygen to the body\'s tissues. </span><strong>Beri Beri </strong><span style=\"font-weight: 400;\">- Disease in which the body does not have enough thiamine (vitamin B1). </span><strong>Scurvy </strong><span style=\"font-weight: 400;\">- A disease that occurs when you have a severe lack of vitamin C (ascorbic acid) in your diet.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b) </span><strong>&#2346;&#2375;&#2354;&#2366;&#2327;&#2381;&#2352;&#2366; </strong><span style=\"font-weight: 400;\">(B3-&#2344;&#2367;&#2351;&#2366;&#2360;&#2367;&#2344;)</span><strong>&#2404; &#2319;&#2344;&#2368;&#2350;&#2367;&#2351;&#2366; - </strong><span style=\"font-weight: 400;\">&nbsp;&#2358;&#2352;&#2368;&#2352; &#2325;&#2375; &#2314;&#2340;&#2325;&#2379;&#2306; &#2340;&#2325; &#2346;&#2352;&#2381;&#2351;&#2366;&#2346;&#2381;&#2340; &#2321;&#2325;&#2381;&#2360;&#2368;&#2332;&#2344; &#2354;&#2375; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2354;&#2366;&#2354; &#2352;&#2325;&#2381;&#2340; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2323;&#2306; &#2351;&#2366; &#2361;&#2368;&#2350;&#2379;&#2327;&#2381;&#2354;&#2379;&#2348;&#2367;&#2344; &#2325;&#2368; &#2325;&#2350;&#2368; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2340;&#2368;&nbsp; &#2361;&#2376;&#2404; </span><strong>&#2348;&#2375;&#2352;&#2368; &#2348;&#2375;&#2352;&#2368; </strong><span style=\"font-weight: 400;\">- &#2320;&#2360;&#2366; &#2352;&#2379;&#2327; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2358;&#2352;&#2368;&#2352; &#2350;&#2375;&#2306; &#2346;&#2352;&#2381;&#2351;&#2366;&#2346;&#2381;&#2340; &#2341;&#2366;&#2351;&#2350;&#2367;&#2344; (&#2357;&#2367;&#2335;&#2366;&#2350;&#2367;&#2344; B1) &#2344;&#2361;&#2368;&#2306; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2381;&#2325;&#2352;&#2381;&#2357;&#2368; </strong><span style=\"font-weight: 400;\">-&nbsp; &#2351;&#2375; &#2348;&#2368;&#2350;&#2366;&#2352;&#2368; &#2310;&#2361;&#2366;&#2352; &#2350;&#2375;&#2306; &#2357;&#2367;&#2335;&#2366;&#2350;&#2367;&#2344; C (&#2319;&#2360;&#2381;&#2325;&#2377;&#2352;&#2381;&#2348;&#2367;&#2325; &#2309;&#2350;&#2381;&#2354;) &#2325;&#2368; &#2325;&#2350;&#2368; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Which of the following ministries of the Government of India has under it the Council of Scientific &amp; Industrial Research (CSIR)?</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2357;&#2376;&#2332;&#2381;&#2334;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2359;&#2342;</span><span style=\"font-family: Cambria Math;\"> (CSIR) </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Ministry of Commerce and Industry</p>\n", "<p>Ministry of Earth Sciences</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Ministry of Science and Technology </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Ministry of Education </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2339;&#2367;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2381;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2341;&#2381;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2367;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>Ministry of Science and Technology. </strong><span style=\"font-weight: 400;\">Council of Scientific &amp; Industrial Research</span><strong> (CSIR): </strong><span style=\"font-weight: 400;\">The largest research and development (R&amp;D) organization in India. Established - 1942, Headquarters - New Delhi. Founder(s) - Arcot Ramasamy Mudaliar, Shanti Swaroop Bhatnagar. President: Prime Minister of India. Motto - &ldquo;CSIR-The Innovation Engine of India&rdquo;. Parent institution - Ministry of Science and Technology, Government of India.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344; &#2324;&#2352; &#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2368; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;&#2404; </strong><span style=\"font-weight: 400;\">&#2357;&#2376;&#2332;&#2381;&#2334;&#2366;&#2344;&#2367;&#2325; &#2319;&#2357;&#2306; &#2324;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2346;&#2352;&#2367;&#2359;&#2342; (CSIR): &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2324;&#2352; &#2357;&#2367;&#2325;&#2366;&#2360; (R&amp;D) &#2360;&#2306;&#2327;&#2336;&#2344;&#2404; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; - 1942, &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; - &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;&#2404; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2346;&#2325; - &#2309;&#2352;&#2381;&#2325;&#2379;&#2335; &#2352;&#2366;&#2350;&#2366;&#2360;&#2366;&#2350;&#2368; &#2350;&#2369;&#2342;&#2354;&#2367;&#2351;&#2366;&#2352;, &#2358;&#2366;&#2306;&#2340;&#2367; &#2360;&#2381;&#2357;&#2352;&#2370;&#2346; &#2349;&#2335;&#2344;&#2366;&#2327;&#2352;&#2404; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; : &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368;, &#2310;&#2342;&#2352;&#2381;&#2358; &#2357;&#2366;&#2325;&#2381;&#2351; - \"CSIR- &#2342; &#2311;&#2344;&#2379;&#2357;&#2375;&#2358;&#2344; &#2311;&#2306;&#2332;&#2344; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;\"&#2404; &#2350;&#2370;&#2354; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366; - &#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344; &#2319;&#2357;&#2306; &#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2368; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;, &#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Lactitol is derived through the hydrogenatio</span><span style=\"font-family: Cambria Math;\">n of:</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2325;&#2381;&#2335;&#2367;&#2335;&#2379;</span><span style=\"font-family: Nirmala UI;\">&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Lactitol ) </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2332;&#2344;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>maltose</p>\n", "<p>lactic acid</p>\n", 
                                "<p>lactose</p>\n", "<p>glucose</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;&#2381;&#2335;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2325;&#2381;&#2335;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2350;&#2381;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2325;&#2381;&#2335;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Lactose</strong></span><span style=\"font-family: Cambria Math;\"> (Milk sugar). It is a disaccharide which is made by the condensation of glucose and galactose. </span><span style=\"font-family: Cambria Math;\">Examples :</span><span style=\"font-family: Cambria Math;\"> Butter, Cheese, Cream, Dried milk, Milk solids, Powdered milk, and Whey. </span><span style=\"font-family: Cambria Math;\"><strong>Maltose</strong>: </span><span style=\"font-family: Cambria Math;\">(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>C</mi><mn>12</mn></msub><msub><mi>H</mi><mn>22</mn></msub><msub><mi>O</mi><mn>11</mn></msub></math><span style=\"font-family: Cambria Math;\">), known as maltobiose or malt sugar. </span><span style=\"font-family: Cambria Math;\"><strong>Lactic</strong> </span><span style=\"font-family: Cambria Math;\"><strong>acid </strong>:</span><span style=\"font-family: Cambria Math;\"> Form</span><span style=\"font-family: Cambria Math;\">ed by anaerobic respiration due to lack of oxygen in muscles. </span><span style=\"font-family: Cambria Math;\">Glucose:</span><span style=\"font-family: Cambria Math;\"> Sugar with the molecular formula </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>C</mi><mn>6</mn></msub><msub><mi>H</mi><mn>12</mn></msub><msub><mi>O</mi><mn>6</mn></msub></math><span style=\"font-family: Cambria Math;\">. </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp; </span><strong>&#2354;&#2376;&#2325;&#2381;&#2335;&#2379;&#2332;</strong><span style=\"font-weight: 400;\"> (&#2350;&#2367;&#2354;&#2381;&#2325; &#2358;&#2369;&#2327;&#2352;)&#2404; &#2351;&#2361; &#2319;&#2325; &#2337;&#2366;&#2312;&#2360;&#2376;&#2325;&#2375;&#2352;&#2366;&#2311;&#2337; &#2361;&#2376; &#2332;&#2379; &#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332; &#2324;&#2352; &#2327;&#2376;&#2354;&#2375;&#2325;&#2381;&#2335;&#2379;&#2332; &#2325;&#2375; &#2360;&#2306;&#2328;&#2344;&#2344; &#2360;&#2375; &#2348;&#2344;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; : &#2350;&#2325;&#2381;&#2326;&#2344;, &#2346;&#2344;&#2368;&#2352;, &#2325;&#2381;&#2352;&#2368;&#2350;, &#2337;&#2381;&#2352;&#2366;&#2312; &#2350;&#2367;&#2354;&#2381;&#2325;, &#2350;&#2367;&#2354;&#2381;&#2325; &#2360;&#2377;&#2354;&#2367;&#2337;, &#2346;&#2366;&#2313;&#2337;&#2352; &#2350;&#2367;&#2354;&#2381;&#2325; &#2324;&#2352; &#2354;&#2360;&#2381;&#2360;&#2368; &#2404; </span><strong>&#2350;&#2366;&#2354;&#2381;&#2335;&#2379;&#2332;&#2364; :</strong><span style=\"font-weight: 400;\"> (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>C</mi><mn>12</mn></msub><msub><mi>H</mi><mn>22</mn></msub><msub><mi>O</mi><mn>11</mn></msub></math><span style=\"font-weight: 400;\">), &#2332;&#2367;&#2360;&#2375; &#2350;&#2366;&#2354;&#2381;&#2335;&#2379;&#2348;&#2366;&#2351;&#2379;&#2332;&#2364; &#2351;&#2366; &#2350;&#2366;&#2354;&#2381;&#2335; &#2358;&#2352;&#2381;&#2325;&#2352;&#2366; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2354;&#2376;&#2325;&#2381;&#2335;&#2367;&#2325; &#2309;&#2350;&#2381;&#2354; :</strong><span style=\"font-weight: 400;\"> &#2350;&#2366;&#2306;&#2360;&#2346;&#2375;&#2358;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2321;&#2325;&#2381;&#2360;&#2368;&#2332;&#2344; &#2325;&#2368; &#2325;&#2350;&#2368; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2309;&#2357;&#2366;&#2351;&#2357;&#2368;&#2351; &#2358;&#2381;&#2357;&#2360;&#2344; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340;&#2404; </span><strong>&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332; : </strong><span style=\"font-weight: 400;\">&#2358;&#2352;&#2381;&#2325;&#2352;&#2366; &#2332;&#2367;&#2360;&#2325;&#2366; &#2310;&#2339;&#2357;&#2367;&#2325; &#2360;&#2370;&#2340;&#2381;&#2352; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>C</mi><mn>6</mn></msub><msub><mi>H</mi><mn>12</mn></msub><msub><mi>O</mi><mn>6</mn></msub></math><span style=\"font-weight: 400;\">&#2361;&#2376;&#2404; </span><strong>&nbsp;</strong><span style=\"font-weight: 400;\">&nbsp;</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Which of the following tests is used for starch detection?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Nirmala UI;\">&#2350;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2337;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\"> starch ) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Iodine test</p>\n", "<p>Glucose test</p>\n", 
                                "<p>Salt test</p>\n", "<p>Sodium test</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2337;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2339;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2339;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2357;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2339;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2379;&#2337;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2339;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Iodine test. </strong><span style=\"font-weight: 400;\">The light orange-brown colour Iodine solution turns blue-black in colour when it reacts with starch. This indicates the presence of carbohydrates in the food item to which iodine solution was added. </span><strong>Example :</strong><span style=\"font-weight: 400;\"> If Iodine is added to a peeled potato then it will turn black.</span><strong>&nbsp;</strong></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>&#2310;&#2351;&#2379;&#2337;&#2368;&#2344; &#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2339;&#2404; </strong><span style=\"font-weight: 400;\">&#2361;&#2354;&#2381;&#2325;&#2375; &#2344;&#2366;&#2352;&#2306;&#2327;&#2368;-&#2349;&#2370;&#2352;&#2375; &#2352;&#2306;&#2327; &#2325;&#2366; &#2310;&#2351;&#2379;&#2337;&#2368;&#2344; &#2357;&#2367;&#2354;&#2351;&#2344;, &#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2330; &#2325;&#2375; &#2360;&#2366;&#2341; &#2309;&#2349;&#2367;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352; &#2344;&#2368;&#2354;&#2375;-&#2325;&#2366;&#2354;&#2375; &#2352;&#2306;&#2327; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2313;&#2360; &#2326;&#2366;&#2342;&#2381;&#2351; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2350;&#2375;&#2306; &#2325;&#2366;&#2352;&#2381;&#2348;&#2379;&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2375;&#2335; &#2325;&#2368; &#2313;&#2346;&#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2325;&#2379; &#2311;&#2306;&#2327;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2310;&#2351;&#2379;&#2337;&#2368;&#2344; &#2357;&#2367;&#2354;&#2351;&#2344; &#2350;&#2367;&#2354;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; : </strong><span style=\"font-weight: 400;\">&#2351;&#2342;&#2367; &#2331;&#2367;&#2354;&#2375; &#2361;&#2369;&#2319;&nbsp; &#2310;&#2354;&#2370; &#2350;&#2375;&#2306; &#2310;&#2351;&#2379;&#2337;&#2368;&#2344; &#2350;&#2367;&#2354;&#2366; &#2342;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319; &#2340;&#2379; &#2357;&#2361; &#2325;&#2366;&#2354;&#2366; &#2361;&#2379; &#2332;&#2366;&#2319;&#2327;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Which of the following diseases is transmitted by the bite of an infected female phlebotomine sandfly, characterized by irregular fever, weight loss and enlargement of the spleen and liver?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2381;&#2354;&#2375;&#2348;&#2379;&#2335;&#2379;&#2350;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2325;&#2381;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> (phlebotomine sandfly) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2335;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2376;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2367;&#2351;&#2350;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2357;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2354;&#2368;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2306;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Japanese encephalitis</p>\n", "<p>Visce<span style=\"font-family: Cambria Math;\">ral leishmaniasis</span></p>\n", 
                                "<p>Diarrhea</p>\n", "<p>Anaplasmosis</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2332;&#2366;&#2346;&#2366;&#2344;&#2368; &#2350;&#2360;&#2381;&#2340;&#2367;&#2359;&#2381;&#2325; &#2332;&#2381;&#2357;&#2352; (Japanese encephalitis)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2310;&#2306;&#2340; &#2325;&#2366; &#2354;&#2368;&#2358;&#2350;&#2376;&#2344;&#2367;&#2351;&#2366;&#2360;&#2367;&#2360; / &#2325;&#2366;&#2354;&#2366;&#2332;&#2364;&#2366;&#2352; &#2313;&#2344;&#2381;&#2350;&#2370;&#2354;&#2344; (Visceral leishmaniasis)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2309;&#2340;&#2367;&#2360;&#2366;&#2352; (Diarrhea)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2344;&#2366;&#2346;&#2381;&#2354;&#2366;&#2360;&#2381;&#2350;&#2379;&#2360;&#2367;&#2360; (Anaplasmosis)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>Visceral leishmaniasis </strong><span style=\"font-weight: 400;\">(kala-azar).</span><strong> </strong><span style=\"font-weight: 400;\">It is caused by protozoan parasites which are transmitted by the bite of infected female phlebotomine sandflies. </span><strong>Japanese encephalitis</strong><span style=\"font-weight: 400;\"> virus is spread to people through the bite of an infected mosquito.</span><strong> Anaplasmosis</strong><span style=\"font-weight: 400;\"> is a disease caused by the bacterium Anaplasma phagocytophilum. </span><strong>Other protozoan Diseases: </strong><span style=\"font-weight: 400;\">Amoebiasis (Entamoeba Histolytica Protozoa); Malaria (Plasmodium). </span><strong>Other bacteria diseases: </strong><span style=\"font-weight: 400;\">Salmonella Tuberculosis, Whooping cough (pertussis), Chlamydia, gonorrhoea, Cholera, etc.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2310;&#2306;&#2340; &#2325;&#2366; &#2354;&#2368;&#2358;&#2350;&#2376;&#2344;&#2367;&#2351;&#2366;&#2360;&#2367;&#2360;</strong><span style=\"font-weight: 400;\"> (&#2325;&#2366;&#2354;&#2366;&#2332;&#2364;&#2366;&#2352; &#2352;&#2379;&#2327;)&#2404; &#2351;&#2361; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2332;&#2379;&#2310; &#2346;&#2352;&#2332;&#2368;&#2357;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; &#2332;&#2379; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2367;&#2340; &#2350;&#2366;&#2342;&#2366; &#2347;&#2381;&#2354;&#2375;&#2348;&#2379;&#2335;&#2379;&#2350;&#2366;&#2311;&#2344; &#2360;&#2376;&#2306;&#2337;&#2347;&#2381;&#2354;&#2366;&#2311;&#2332;&#2364; &#2325;&#2375; &#2325;&#2366;&#2335;&#2344;&#2375; &#2360;&#2375; &#2347;&#2376;&#2354;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2332;&#2366;&#2346;&#2366;&#2344;&#2368; &#2319;&#2344;&#2381;&#2360;&#2375;&#2347;&#2354;&#2366;&#2311;&#2335;&#2367;&#2360; </strong><span style=\"font-weight: 400;\">&#2357;&#2366;&#2351;&#2352;&#2360; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2367;&#2340; &#2350;&#2330;&#2381;&#2331;&#2352; &#2325;&#2375; &#2325;&#2366;&#2335;&#2344;&#2375; &#2360;&#2375; &#2354;&#2379;&#2327;&#2379;&#2306; &#2350;&#2375;&#2306; &#2347;&#2376;&#2354;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2319;&#2344;&#2366;&#2346;&#2381;&#2354;&#2366;&#2332;&#2381;&#2350;&#2379;&#2360;&#2367;&#2360; </strong><span style=\"font-weight: 400;\">&#2332;&#2368;&#2357;&#2366;&#2339;&#2369; &#2319;&#2344;&#2366;&#2346;&#2381;&#2354;&#2366;&#2332;&#2381;&#2350;&#2366; &#2347;&#2366;&#2327;&#2379;&#2360;&#2366;&#2311;&#2335;&#2379;&#2347;&#2367;&#2354;&#2350; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2348;&#2368;&#2350;&#2366;&#2352;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2309;&#2344;&#2381;&#2351; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2332;&#2379;&#2310; &#2352;&#2379;&#2327;:</strong><span style=\"font-weight: 400;\"> &#2309;&#2350;&#2368;&#2348;&#2367;&#2351;&#2366;&#2360;&#2367;&#2360; (&#2319;&#2306;&#2335;&#2366;&#2350;&#2379;&#2311;&#2348;&#2366; &#2361;&#2367;&#2360;&#2381;&#2335;&#2379;&#2354;&#2367;&#2335;&#2367;&#2325;&#2366; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2332;&#2379;&#2310;); &#2350;&#2354;&#2375;&#2352;&#2367;&#2351;&#2366; (&#2346;&#2381;&#2354;&#2366;&#2332;&#2381;&#2350;&#2379;&#2337;&#2367;&#2351;&#2350;)&#2404; </span><strong>&#2309;&#2344;&#2381;&#2351; &#2332;&#2368;&#2357;&#2366;&#2339;&#2369; &#2352;&#2379;&#2327;: </strong><span style=\"font-weight: 400;\">&#2360;&#2366;&#2354;&#2381;&#2350;&#2379;&#2344;&#2375;&#2354;&#2366; &#2340;&#2346;&#2375;&#2342;&#2367;&#2325;, &#2325;&#2366;&#2354;&#2368; &#2326;&#2366;&#2306;&#2360;&#2368; (&#2346;&#2352;&#2381;&#2335;&#2369;&#2360;&#2367;&#2360;), &#2325;&#2381;&#2354;&#2376;&#2350;&#2366;&#2311;&#2337;&#2367;&#2351;&#2366;, &#2327;&#2379;&#2344;&#2379;&#2352;&#2367;&#2351;&#2366;, &#2361;&#2376;&#2332;&#2366;, &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Which part of a cell converts nutrients into </span><span style=\"font-family: Cambria Math;\">energy ?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2359;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2340;&#2381;&#2357;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2352;&#2381;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;</span><span style=\"font-family: Nirmala UI;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Cell membrane</p>\n", "<p>Lysosomes</p>\n", 
                                "<p>Mitochondria</p>\n", "<p>Chromosome</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2333;&#2367;&#2354;&#2381;&#2354;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2351;&#2344;&#2325;&#2366;&#2351;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;&#2352;&#2325;&#2339;&#2367;&#2325;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2339;&#2360;&#2370;&#2340;&#2381;&#2352;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>Mitochondria - </strong><span style=\"font-weight: 400;\">Double membrane-bound cell organelles present in most eukaryotic organisms (such as animals, plants and fungi). </span><strong>Cell membrane</strong><span style=\"font-weight: 400;\"> (Plasma membrane) - The outermost covering of animal cells. It is a semi - permeable membrane composed of lipids and proteins. </span><strong>Lysosomes - </strong><span style=\"font-weight: 400;\">Small spherical vesicles of approximately 0.2 - 0.5 micron in diameter, which are bound by a single membrane containing hydrolytic enzymes capable of breaking down macromolecules. </span><strong>Chromosome - </strong><span style=\"font-weight: 400;\">A thread - like microscopic structure formed by coiling of DNA packaged with protein containing all genetic material of an organism.</span></p>\n",
                    solution_hi: "<p>6.(c)&nbsp;<strong>&#2360;&#2370;&#2340;&#2381;&#2352;&#2325;&#2339;&#2367;&#2325;&#2366; -</strong><span style=\"font-weight: 400;\"> &#2309;&#2343;&#2367;&#2325;&#2366;&#2306;&#2358; &#2351;&#2370;&#2325;&#2375;&#2352;&#2367;&#2351;&#2379;&#2335;&#2367;&#2325; &#2332;&#2368;&#2357;&#2379;&#2306; (&#2332;&#2376;&#2360;&#2375; &#2332;&#2344;&#2381;&#2340;&#2369;, &#2346;&#2380;&#2343;&#2375; &#2324;&#2352; &#2325;&#2357;&#2325;) &#2350;&#2375;&#2306; &#2342;&#2381;&#2357;&#2367;-&#2333;&#2367;&#2354;&#2381;&#2354;&#2368;-&#2348;&#2342;&#2381;&#2343; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366; &#2309;&#2306;&#2327; &#2350;&#2380;&#2332;&#2370;&#2342; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376; &#2404; </span><strong>&#2325;&#2379;&#2358;&#2367;&#2325;&#2366; &#2333;&#2367;&#2354;&#2381;&#2354;&#2368;</strong><span style=\"font-weight: 400;\"> (&#2346;&#2381;&#2354;&#2366;&#2332;&#2381;&#2350;&#2366; &#2333;&#2367;&#2354;&#2381;&#2354;&#2368;) - &#2332;&#2306;&#2340;&#2369; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2323;&#2306; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2366;&#2361;&#2381;&#2351; &#2310;&#2357;&#2352;&#2339; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2354;&#2367;&#2346;&#2367;&#2337; &#2324;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2368;&#2344; &#2360;&#2375; &#2348;&#2344;&#2368; &#2319;&#2325; &#2309;&#2352;&#2381;&#2343;-&#2346;&#2366;&#2352;&#2327;&#2350;&#2381;&#2351; &#2333;&#2367;&#2354;&#2381;&#2354;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2354;&#2366;&#2311;&#2360;&#2379;&#2360;&#2379;&#2350; (&#2354;&#2351;&#2344;&#2325;&#2366;&#2351;) </strong><span style=\"font-weight: 400;\">&#2354;&#2327;&#2349;&#2327; 0.2 - 0.5 &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2344; &#2357;&#2381;&#2351;&#2366;&#2360; &#2325;&#2375; &#2331;&#2379;&#2335;&#2375; &#2327;&#2379;&#2354;&#2366;&#2325;&#2366;&#2352; &#2357;&#2376;&#2360;&#2367;&#2325;&#2354;&#2381;&#2360;, &#2332;&#2379; &#2350;&#2376;&#2325;&#2381;&#2352;&#2379;&#2350;&#2379;&#2354;&#2375;&#2325;&#2381;&#2351;&#2370;&#2354;&#2381;&#2360; &#2325;&#2379; &#2340;&#2379;&#2337;&#2364;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2325;&#2381;&#2359;&#2350; &#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2354;&#2366;&#2311;&#2335;&#2367;&#2325; &#2319;&#2306;&#2332;&#2366;&#2311;&#2350; &#2351;&#2369;&#2325;&#2381;&#2340; &#2319;&#2325; &#2333;&#2367;&#2354;&#2381;&#2354;&#2368; &#2360;&#2375; &#2348;&#2306;&#2343;&#2375; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2327;&#2369;&#2339;&#2360;&#2370;&#2340;&#2381;&#2352; </strong><span style=\"font-weight: 400;\">- &#2319;&#2325; &#2343;&#2366;&#2327;&#2375; &#2332;&#2376;&#2360;&#2368; &#2360;&#2370;&#2325;&#2381;&#2359;&#2381;&#2350; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2332;&#2379; &#2325;&#2367;&#2360;&#2368; &#2332;&#2368;&#2357; &#2325;&#2368; &#2360;&#2349;&#2368; &#2310;&#2344;&#2369;&#2357;&#2306;&#2358;&#2367;&#2325; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2357;&#2366;&#2354;&#2375; &#2346;&#2381;&#2352;&#2379;&#2335;&#2368;&#2344; &#2360;&#2375; &#2346;&#2370;&#2352;&#2381;&#2339;&nbsp; DNA &#2325;&#2375; &#2325;&#2369;&#2306;&#2337;&#2354;&#2367;&#2340; &#2361;&#2379;&#2344;&#2375; &#2360;&#2375; &#2348;&#2344;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">What is the monomer unit of glycogen?</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2366;&#2311;&#2325;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;</span><span style=\"font-family: Nirmala UI;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Fructose</p>\n", "<p>Glucose</p>\n", 
                                "<p>Galactose</p>\n", "<p>Mannose</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2347;&#2381;&#2352;&#2369;&#2325;&#2381;&#2335;&#2379;&#2332;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2376;&#2354;&#2375;&#2325;&#2381;&#2335;&#2379;&#2332;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2344;&#2379;&#2360;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>Glucose (</strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>C</mi><mn>6</mn></msub><msub><mi>H</mi><mn>12</mn></msub><msub><mi>O</mi><mn>6</mn></msub></math><strong>). Glycogen:</strong><span style=\"font-weight: 400;\"> The carbohydrates are stored in the animal body as glycogen</span><strong>. </strong><span style=\"font-weight: 400;\">Also known as animal starch because its structure is similar to amylopectin and is rather more highly branched. It is present in the liver, muscles and brain. When the body needs glucose, enzymes break the glycogen down to glucose. Found in yeast and fungi. </span><strong>Fructose </strong><span style=\"font-weight: 400;\">- It is a natural monosaccharide found in fruits, honey and vegetables.&nbsp;</span></p>\n",
                    solution_hi: "<p>7.(b) <strong><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332;</span></strong><span style=\"font-family: Cambria Math;\"> (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold-italic\">C</mi><mn mathvariant=\"bold\">6</mn></msub><msub><mi mathvariant=\"bold-italic\">H</mi><mn mathvariant=\"bold\">12</mn></msub><msub><mi mathvariant=\"bold-italic\">O</mi><mn mathvariant=\"bold\">6</mn></msub></math><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2366;&#2311;&#2325;&#2379;&#2332;&#2344;</span></strong><span style=\"font-family: Cambria Math;\"><strong>:</strong> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2348;&#2379;&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2306;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2352;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2366;&#2311;&#2325;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2327;&#2381;&#2352;&#2361;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2352;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2350;&#2366;&#2311;&#2354;&#2379;&#2346;&#2375;&#2325;&#2381;&#2335;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2330;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2326;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;&#2360;&#2346;&#2375;&#2358;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2360;&#2381;&#2340;&#2367;&#2359;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2380;&#2332;&#2370;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2306;&#2332;&#2366;&#2311;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2366;&#2311;&#2325;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2368;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2347;&#2381;&#2352;&#2369;&#2325;&#2381;&#2335;&#2379;&#2332;</span></strong><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2361;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2381;&#2332;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2379;&#2344;&#2379;&#2360;&#2376;&#2325;&#2375;&#2352;&#2366;&#2311;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Which of the following diseases is a potential contender for worldwide eradication?</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2350;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2357;&#2381;&#2351;&#2366;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2350;&#2370;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2357;&#2375;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Polio</p>\n", "<p>Tuberculosis</p>\n", 
                                "<p>Measles</p>\n", "<p>Malaria</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2354;&#2367;&#2351;&#2379;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2346;&#2375;&#2342;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2326;&#2360;&#2352;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2354;&#2375;&#2352;&#2367;&#2351;&#2366;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Polio </strong><span style=\"font-weight: 400;\">(poliomyelitis)</span><strong> </strong><span style=\"font-weight: 400;\">{caused by a virus (poliovirus)} - This virus infects your throat and intestines, causing flu-like symptoms. It can then spread to your brain and spine, causing paralysis. It mainly affects children under </span><strong>5 years</strong><span style=\"font-weight: 400;\"> of age. Pulse Polio Immunization programme: It was launched in India in </span><strong>1995</strong><span style=\"font-weight: 400;\">, India was declared as polio-free country In </span><strong>2014</strong><span style=\"font-weight: 400;\">.&nbsp;</span></p>\n",
                    solution_hi: "<p>8.(a) <strong>&#2346;&#2379;&#2354;&#2367;&#2351;&#2379;</strong><span style=\"font-weight: 400;\"> (&#2346;&#2379;&#2354;&#2367;&#2351;&#2379;&#2350;&#2366;&#2311;&#2354;&#2366;&#2311;&#2335;&#2367;&#2360;) {&#2319;&#2325; &#2357;&#2366;&#2351;&#2352;&#2360; (&#2346;&#2379;&#2354;&#2367;&#2351;&#2379;&#2357;&#2366;&#2351;&#2352;&#2360;) &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;} - &#2351;&#2361; &#2357;&#2366;&#2351;&#2352;&#2360; &#2310;&#2346;&#2325;&#2375; &#2327;&#2354;&#2375; &#2324;&#2352; &#2310;&#2306;&#2340;&#2379;&#2306; &#2325;&#2379; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2360;&#2375; &#2347;&#2381;&#2354;&#2370; &#2332;&#2376;&#2360;&#2375; &#2354;&#2325;&#2381;&#2359;&#2339; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2351;&#2361; &#2310;&#2346;&#2325;&#2375; &#2350;&#2360;&#2381;&#2340;&#2367;&#2359;&#2381;&#2325; &#2324;&#2352; &#2352;&#2368;&#2338;&#2364; &#2340;&#2325; &#2347;&#2376;&#2354; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2360;&#2375; &#2358;&#2352;&#2368;&#2352; &#2325;&#2379; &#2354;&#2325;&#2357;&#2366; (paralysis) &#2361;&#2379; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2350;&#2369;&#2326;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; </span><strong>5 &#2357;&#2352;&#2381;&#2359;</strong><span style=\"font-weight: 400;\"> &#2360;&#2375; &#2325;&#2350; &#2313;&#2350;&#2381;&#2352; &#2325;&#2375; &#2348;&#2330;&#2381;&#2330;&#2379;&#2306; &#2325;&#2379; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2346;&#2354;&#2381;&#2360; &#2346;&#2379;&#2354;&#2367;&#2351;&#2379; &#2346;&#2381;&#2352;&#2340;&#2367;&#2352;&#2325;&#2381;&#2359;&#2339; &#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350;: &#2311;&#2360;&#2375; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; </span><strong>1995 </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306; &#2358;&#2369;&#2352;&#2370; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;, &#2349;&#2366;&#2352;&#2340; &#2325;&#2379; </span><strong>2014 </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306; &#2346;&#2379;&#2354;&#2367;&#2351;&#2379; &#2350;&#2369;&#2325;&#2381;&#2340; &#2342;&#2375;&#2358; &#2328;&#2379;&#2359;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Which cell analysis technique is used to rapidly analyse single cells or partic</span><span style=\"font-family: Cambria Math;\">les as they flow through single or multiple lasers while suspended in a buffered salt-based solution?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;</span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2354;&#2375;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;&#2344;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2349;&#2351;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2352;&#2379;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2357;&#2339;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2354;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2354;&#2306;&#2348;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2360;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2357;&#2366;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Mass spectroscopy</p>\n", "<p>Flow cytometry</p>\n", 
                                "<p>Electrochemical analysis</p>\n", "<p>Multiple displacement amplification</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2339;&#2325;&#2381;&#2352;&#2350;&#2350;&#2366;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Mass spectroscopy) </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2357;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2350;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> (Flow</span><span style=\"font-family: Cambria Math;\"> cytometry) </span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2357;&#2367;&#2342;&#2381;&#2351;&#2369;&#2340; &#2352;&#2366;&#2360;&#2366;&#2351;&#2344;&#2367;&#2325; &#2357;&#2367;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339; (Electrochemical analysis)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2325;&#2366;&#2343;&#2367;&#2325; &#2357;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2344; &#2346;&#2381;&#2352;&#2357;&#2352;&#2381;&#2343;&#2344; (Multiple displacement amplification)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span><strong>&nbsp;Flow cytometry - </strong><span style=\"font-weight: 400;\">It is a laser-based technique used to detect and analyze the chemical and physical characteristics of cells or particles, It is to evaluate bone marrow, peripheral blood and other fluids in your body</span><strong>. Mass spectroscopy - </strong><span style=\"font-weight: 400;\">Used to identify and quantify molecules by measuring their mass-to-charge ratio.</span><strong> Electrochemical analysis - </strong><span style=\"font-weight: 400;\">Used to measure the electrical properties of cells or particles. </span><strong>Multiple displacement amplification</strong><span style=\"font-weight: 400;\"> - Used to amplify DNA.</span></p>\n",
                    solution_hi: "<p>9.(b) <strong>&#2347;&#2381;&#2354;&#2379; &#2360;&#2366;&#2311;&#2335;&#2379;&#2350;&#2375;&#2335;&#2381;&#2352;&#2368; -</strong><span style=\"font-weight: 400;\"> &#2351;&#2361; &#2319;&#2325; &#2354;&#2375;&#2332;&#2352;-&#2310;&#2343;&#2366;&#2352;&#2367;&#2340; &#2340;&#2325;&#2344;&#2368;&#2325; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2323;&#2306; &#2351;&#2366; &#2325;&#2339;&#2379;&#2306; &#2325;&#2368; &#2352;&#2366;&#2360;&#2366;&#2351;&#2344;&#2367;&#2325; &#2324;&#2352; &#2349;&#2380;&#2340;&#2367;&#2325; &#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;&#2323;&#2306; &#2325;&#2366; &#2346;&#2340;&#2366; &#2354;&#2327;&#2366;&#2344;&#2375; &#2324;&#2352; &#2313;&#2344;&#2325;&#2366; &#2357;&#2367;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2351;&#2361; &#2310;&#2346;&#2325;&#2375; &#2358;&#2352;&#2368;&#2352; &#2350;&#2375;&#2306; &#2309;&#2360;&#2381;&#2341;&#2367; &#2350;&#2332;&#2381;&#2332;&#2366;, &#2346;&#2352;&#2367;&#2343;&#2368;&#2351; &#2352;&#2325;&#2381;&#2340; &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2342;&#2381;&#2352;&#2357; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341;&#2379;&#2306; &#2325;&#2366; &#2350;&#2370;&#2354;&#2381;&#2351;&#2366;&#2306;&#2325;&#2344; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2366;&#2360; &#2360;&#2381;&#2346;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2379;&#2360;&#2381;&#2325;&#2379;&#2346;&#2368; -</strong><span style=\"font-weight: 400;\"> &#2313;&#2344;&#2325;&#2375; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344;-&#2360;&#2375;-&#2310;&#2357;&#2375;&#2358; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2325;&#2379; &#2350;&#2366;&#2346;&#2325;&#2352; &#2309;&#2339;&#2369;&#2323;&#2306; &#2325;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2324;&#2352; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2343;&#2366;&#2352;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2379;&#2325;&#2375;&#2350;&#2367;&#2325;&#2354; &#2357;&#2367;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339; - </strong><span style=\"font-weight: 400;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2323;&#2306; &#2351;&#2366; &#2325;&#2339;&#2379;&#2306; &#2325;&#2375; &#2357;&#2367;&#2342;&#2381;&#2351;&#2369;&#2340; &#2327;&#2369;&#2339;&#2379;&#2306; &#2325;&#2379; &#2350;&#2366;&#2346;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2319;&#2325;&#2366;&#2343;&#2367;&#2325; &#2357;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2344; &#2346;&#2381;&#2352;&#2357;&#2352;&#2381;&#2343;&#2344; -</strong><span style=\"font-weight: 400;\"> DNA &#2325;&#2379; &#2348;&#2397;&#2366;&#2344;&#2375; &#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> An excessive amount of cadmium in the human body causes:</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2352;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2381;&#2351;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2337;&#2350;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>minamata</p>\n", "<p>anaemia</p>\n", 
                                "<p>alzima</p>\n", "<p>itai-itai</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2366;&#2350;&#2366;&#2340;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2325;&#2381;&#2340;&#2366;&#2354;&#2381;&#2346;&#2340;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2354;&#2381;&#2332;&#2364;&#2367;&#2350;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2335;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2311;&#2335;&#2366;&#2312;</span></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(d)&nbsp; </span><strong>itai itai.</strong><span style=\"font-weight: 400;\"> It is the severe form of chronic cadmium intoxication, which is characterized by osteomalacia with severe bone pain and is associated with renal tubular dysfunction. </span><strong>Minamata</strong><span style=\"font-weight: 400;\"> - A neurological disease caused by severe mercury poisoning. </span><strong>Anemia</strong><span style=\"font-weight: 400;\"> - A problem of not having enough healthy red blood cells or hemoglobin. </span><strong>Alzima (Alzheimer)</strong><span style=\"font-weight: 400;\"> - A brain disorder that slowly destroys memory and thinking skills and, eventually, the ability to carry out the simplest tasks.</span></p>\n",
                    solution_hi: "<p>10.(d) <strong>&#2311;&#2335;&#2366;&#2312;-&#2311;&#2335;&#2366;&#2312;- </strong><span style=\"font-weight: 400;\">&nbsp;&#2351;&#2361; &#2325;&#2381;&#2352;&#2379;&#2344;&#2367;&#2325; &#2325;&#2376;&#2337;&#2350;&#2367;&#2351;&#2350; &#2344;&#2358;&#2375;&nbsp; &#2325;&#2366; &#2327;&#2306;&#2349;&#2368;&#2352; &#2352;&#2370;&#2346; &#2361;&#2376;, &#2332;&#2379; &#2309;&#2360;&#2381;&#2341;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2327;&#2306;&#2349;&#2368;&#2352; &#2342;&#2352;&#2381;&#2342; &#2325;&#2375; &#2360;&#2366;&#2341; &#2321;&#2360;&#2381;&#2335;&#2367;&#2351;&#2379;&#2350;&#2354;&#2375;&#2358;&#2367;&#2351;&#2366; &#2325;&#2368; &#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2327;&#2369;&#2352;&#2381;&#2342;&#2375; &#2325;&#2368; &#2335;&#2381;&#2351;&#2370;&#2348;&#2354;&#2352; &#2358;&#2367;&#2341;&#2367;&#2354;&#2340;&#2366; &#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2367;&#2344;&#2366;&#2350;&#2366;&#2335;&#2366; </strong><span style=\"font-weight: 400;\">- &#2327;&#2306;&#2349;&#2368;&#2352; &#2346;&#2366;&#2352;&#2366; &#2357;&#2367;&#2359;&#2366;&#2325;&#2381;&#2340;&#2340;&#2366; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; &#2340;&#2306;&#2340;&#2381;&#2352;&#2367;&#2325;&#2366; &#2360;&#2306;&#2348;&#2306;&#2343;&#2368; &#2352;&#2379;&#2327;&#2404; </span><strong>&#2319;&#2344;&#2368;&#2350;&#2367;&#2351;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2346;&#2352;&#2381;&#2351;&#2366;&#2346;&#2381;&#2340; &#2360;&#2381;&#2357;&#2360;&#2381;&#2341; &#2354;&#2366;&#2354; &#2352;&#2325;&#2381;&#2340; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2323;&#2306; &#2351;&#2366; &#2361;&#2368;&#2350;&#2379;&#2327;&#2381;&#2354;&#2379;&#2348;&#2367;&#2344; &#2344; &#2361;&#2379;&#2344;&#2375; &#2325;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366;&#2404; </span><strong>&#2309;&#2354;&#2381;&#2332;&#2367;&#2350;&#2366; (&#2309;&#2354;&#2381;&#2332;&#2366;&#2311;&#2350;&#2352;)</strong><span style=\"font-weight: 400;\"> - &#2319;&#2325; &#2350;&#2360;&#2381;&#2340;&#2367;&#2359;&#2381;&#2325; &#2357;&#2367;&#2325;&#2366;&#2352; &#2332;&#2379; &#2343;&#2368;&#2352;&#2375;-&#2343;&#2368;&#2352;&#2375; &#2360;&#2381;&#2350;&#2371;&#2340;&#2367; &#2324;&#2352; &#2360;&#2379;&#2330;&#2344;&#2375; &#2325;&#2375; &#2325;&#2380;&#2358;&#2354; &#2325;&#2379; &#2344;&#2359;&#2381;&#2335; &#2325;&#2352; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2309;&#2306;&#2340;&#2340;&#2307;, &#2360;&#2348;&#2360;&#2375; &#2360;&#2352;&#2354; &#2325;&#2366;&#2352;&#2381;&#2351;&#2379;&#2306; &#2325;&#2379; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366; &#2325;&#2379; &#2344;&#2359;&#2381;&#2335; &#2325;&#2352; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\">Which</span><span style=\"font-family: Cambria Math;\"> of the following chemical compounds, that is used as a food preservative in some sauces and beverages, retains their natural colour and protects against bacteria?</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2377;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2352;&#2348;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2366;&#2342;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2352;&#2325;&#2381;&#2359;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2360;&#2366;&#2351;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2380;&#2327;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2325;&#2352;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2325;&#2381;&#2335;&#2368;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Dihydroxy benzene</p>\n", "<p>Hydrazine hydrate</p>\n", 
                                "<p>Potassium metabisulphite</p>\n", "<p>Monosodium glutamate</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2337;&#2366;&#2351;&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2377;&#2325;&#2381;&#2360;&#2368; &#2348;&#2375;&#2306;&#2332;&#2368;&#2344; (Dihydroxy benzene)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2375;&#2332;&#2366;&#2311;&#2344; &#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2375;&#2335; (Hydrazine hydrate)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2346;&#2379;&#2335;&#2375;&#2358;&#2367;&#2351;&#2350; &#2350;&#2375;&#2335;&#2366; &#2348;&#2366;&#2311;&#2360;&#2354;&#2381;&#2347;&#2366;&#2311;&#2335; (Potassium metabisulphite)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2379;&#2344;&#2379;&#2360;&#2379;&#2337;&#2367;&#2351;&#2350; &#2327;&#2381;&#2354;&#2370;&#2335;&#2366;&#2350;&#2375;&#2335; (Monosodium glutamate)</span></p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>Potassium metabisulphite. Other Food Preservatives -</strong><span style=\"font-weight: 400;\"> Sodium benzoate, calcium propionate, and potassium sorbate.</span><strong> m-Dihydroxybenzene</strong><span style=\"font-weight: 400;\"> (Resorcinol) : Uses - Manufacture of resins, plastics, dyes, medicine and other organic chemical compounds. </span><strong>Hydrazine hydrate</strong><span style=\"font-weight: 400;\"> is used as a reducing agent, blowing agent, corrosion inhibitor, oxygen scavenger or intermediate of synthesis. </span><strong>Monosodium</strong><span style=\"font-weight: 400;\"> </span><strong>glutamate</strong><span style=\"font-weight: 400;\"> is a flavor enhancer in foods.</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>&#2346;&#2379;&#2335;&#2375;&#2358;&#2367;&#2351;&#2350; &#2350;&#2375;&#2335;&#2366; &#2348;&#2366;&#2311;&#2360;&#2354;&#2381;&#2347;&#2366;&#2311;&#2335;</strong><strong>(Potassium metabisulphite)</strong><strong>&#2404; &#2309;&#2344;&#2381;&#2351; &#2326;&#2366;&#2342;&#2381;&#2351; &#2360;&#2306;&#2352;&#2325;&#2381;&#2359;&#2325; -</strong><span style=\"font-weight: 400;\"> &#2360;&#2379;&#2337;&#2367;&#2351;&#2350; &#2348;&#2375;&#2306;&#2332;&#2379;&#2319;&#2335;, &#2325;&#2376;&#2354;&#2381;&#2358;&#2367;&#2351;&#2350; &#2346;&#2381;&#2352;&#2379;&#2346;&#2367;&#2351;&#2379;&#2344;&#2375;&#2335;, &#2324;&#2352; &#2346;&#2379;&#2335;&#2375;&#2358;&#2367;&#2351;&#2350; &#2360;&#2379;&#2352;&#2381;&#2348;&#2375;&#2335;&#2404; </span><strong>&#2319;&#2350;-&#2337;&#2366;&#2351;&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2377;&#2325;&#2381;&#2360;&#2368;&#2348;&#2375;&#2306;&#2332;&#2368;&#2344; </strong><span style=\"font-weight: 400;\">(&#2352;&#2367;&#2360;&#2379;&#2352;&#2381;&#2360;&#2367;&#2344;&#2377;&#2354;) : &#2313;&#2346;&#2351;&#2379;&#2327; - &#2352;&#2375;&#2332;&#2367;&#2344;, &#2346;&#2381;&#2354;&#2366;&#2360;&#2381;&#2335;&#2367;&#2325;, &#2352;&#2306;&#2327;, &#2342;&#2357;&#2366; &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2325;&#2366;&#2352;&#2381;&#2348;&#2344;&#2367;&#2325; &#2352;&#2366;&#2360;&#2366;&#2351;&#2344;&#2367;&#2325; &#2351;&#2380;&#2327;&#2367;&#2325;&#2379;&#2306; &#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;&#2404; </span><strong>&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2366;&#2332;&#2364;&#2368;&#2344; &#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2375;&#2335;</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2319;&#2332;&#2375;&#2306;&#2335; (&#2328;&#2335;&#2325;) &#2325;&#2379; &#2325;&#2350; &#2325;&#2352;&#2344;&#2366; , &#2348;&#2381;&#2354;&#2379;&#2311;&#2306;&#2327; &#2319;&#2332;&#2375;&#2306;&#2335;, &#2360;&#2306;&#2325;&#2381;&#2359;&#2366;&#2352;&#2339; &#2309;&#2357;&#2352;&#2379;&#2343;&#2325;, &#2321;&#2325;&#2381;&#2360;&#2368;&#2332;&#2344; &#2360;&#2381;&#2325;&#2375;&#2357;&#2375;&#2306;&#2332;&#2352; &#2351;&#2366; &#2360;&#2306;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339; &#2325;&#2375; &#2350;&#2343;&#2381;&#2351;&#2357;&#2352;&#2381;&#2340;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2379;&#2344;&#2379;&#2360;&#2379;&#2337;&#2367;&#2351;&#2350; &#2327;&#2381;&#2354;&#2370;&#2335;&#2366;&#2350;&#2375;&#2335;</strong><span style=\"font-weight: 400;\"> &#2326;&#2366;&#2342;&#2381;&#2351; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2381;&#2357;&#2366;&#2342; &#2348;&#2397;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">At what temperature (in degree celsius) is milk heated for 15 to 30 seconds to kill microbes in the pasteurization method?</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2358;&#2381;&#2330;&#2369;&#2352;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2325;&#2381;&#2359;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2346;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2354;&#2381;&#2360;&#2367;&#2351;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>45</p>\n", "<p>100</p>\n", 
                                "<p>70</p>\n", "<p>20</p>\n"],
                    options_hi: ["<p>45</p>\n", "<p>100</p>\n",
                                "<p>70</p>\n", "<p>20</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(c) <strong>70&#8451;. Pasteurization</strong></span><span style=\"font-family: Cambria Math;\"><strong>:</strong> Process by which milk is heated to a specific temperature for a set period of time to kill harmful bacteria that can lead to diseases like Listeriosis, Typhoid fever, Tuberculosis, Diphtheria and Brucellosis. This process was discovered by Louis Pasteur.</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(c) <strong>70&#8451; </strong></span><strong><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2358;&#2381;&#2330;&#2369;&#2352;&#2368;&#2325;&#2352;&#2339;</span></strong><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2346;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;&#2325;&#2366;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2325;&#2381;&#2335;&#2368;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2360;&#2381;&#2335;&#2375;&#2352;&#2367;&#2351;&#2379;&#2360;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2366;&#2311;&#2347;&#2366;&#2311;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2369;&#2326;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2346;&#2375;&#2342;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2346;&#2381;&#2341;&#2368;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2352;&#2369;&#2360;&#2375;&#2354;&#2379;&#2360;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2350;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2369;&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2358;&#2381;&#2330;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which of the following states has the National Academy of Sciences?</span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2366;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;</span><span style=\"font-family: Nirmala UI;\">&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Uttarakhand</p>\n", "<p>Telangana</p>\n", 
                                "<p>Uttar Pradesh</p>\n", "<p>Karnataka</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;</span></p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>Uttar Pradesh. The National Academy of Sciences (</strong><span style=\"font-weight: 400;\">Established in 1930) is the oldest science academy in India. </span><strong>Organizations and their Headquarters: </strong><span style=\"font-weight: 400;\">National Council for Educational Research and Training (NCERT) - New Delhi, Inland Waterways Authority of India (IWAI) - Noida, Central Statistical Organization (CSO) - New Delhi, Press Trust of India (PTI) - New Delhi, etc. </span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp;&nbsp;</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(c) <strong>&nbsp;&#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;&#2404; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344; &#2309;&#2325;&#2366;&#2342;&#2350;&#2368; (</strong><span style=\"font-weight: 400;\">1930 &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;) &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2369;&#2352;&#2366;&#2344;&#2368; &#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344; &#2309;&#2325;&#2366;&#2342;&#2350;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2306;&#2327;&#2336;&#2344; &#2324;&#2352; &#2313;&#2344;&#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351;: </strong><span style=\"font-weight: 400;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2358;&#2376;&#2325;&#2381;&#2359;&#2367;&#2325; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2319;&#2357;&#2306; &#2346;&#2381;&#2352;&#2358;&#2367;&#2325;&#2381;&#2359;&#2339; &#2346;&#2352;&#2367;&#2359;&#2342; (NCERT) - &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;, &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2309;&#2306;&#2340;&#2352;&#2381;&#2342;&#2375;&#2358;&#2368;&#2351; &#2332;&#2354;&#2350;&#2366;&#2352;&#2381;&#2327; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; (IWAI) - &#2344;&#2379;&#2319;&#2337;&#2366;, &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2360;&#2366;&#2306;&#2326;&#2381;&#2351;&#2367;&#2325;&#2368; &#2360;&#2306;&#2327;&#2336;&#2344; (CSO) - &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;, &#2346;&#2381;&#2352;&#2375;&#2360; &#2335;&#2381;&#2352;&#2360;&#2381;&#2335; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; (PTI) - &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; , &#2310;&#2342;&#2367;&#2404;</span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> ______ are the amphibians of the plant kingdom.</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">______</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2342;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2360;&#2381;&#2341;&#2354;&#2330;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Gymnosperm</p>\n", "<p>Pteridophyta</p>\n", 
                                "<p>Thallophyta</p>\n", "<p>Bryophyta</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2350;&#2381;&#2344;&#2379;&#2360;&#2381;&#2346;&#2352;&#2381;&#2350;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2341;&#2376;&#2354;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2352;&#2366;&#2351;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;</span></p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(d) </span><strong>Bryophyta </strong><span style=\"font-weight: 400;\">- They include Mosses, liverworts and hornworts. </span><strong>Thallophyta</strong><span style=\"font-weight: 400;\"> are mostly non-motile primitive plant organisms which show simple body forms. These forms mainly include Algae, fungi. </span><strong>Pteridophytes</strong><span style=\"font-weight: 400;\"> are types of plants without seeds or flowers. So it also goes by the name of cryptogams. </span><strong>Gymnosperm</strong><span style=\"font-weight: 400;\"> plants have naked seeds, which means that their seeds are not covered within a fruit. Example - pines, cycads.</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(d)</span><strong>&nbsp;&#2348;&#2381;&#2352;&#2366;&#2351;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2311;&#2344;&#2350;&#2375;&#2306; &#2350;&#2377;&#2360;&#2375;&#2360;, &#2354;&#2367;&#2357;&#2352;&#2357;&#2377;&#2352;&#2381;&#2335;&#2381;&#2360; &#2324;&#2352; &#2361;&#2377;&#2352;&#2381;&#2344;&#2357;&#2377;&#2352;&#2381;&#2335;&#2381;&#2360; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2341;&#2376;&#2354;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366; </strong><span style=\"font-weight: 400;\">&#2309;&#2343;&#2367;&#2325;&#2366;&#2306;&#2358;&#2340;&#2307; &#2327;&#2376;&#2352;-&#2327;&#2340;&#2367;&#2358;&#2368;&#2354; &#2360;&#2366;&#2343;&#2366;&#2352;&#2339; &#2346;&#2366;&#2342;&#2346; &#2332;&#2368;&#2357; &#2361;&#2376;&#2306; &#2332;&#2379; &#2360;&#2352;&#2354; &#2358;&#2366;&#2352;&#2368;&#2352;&#2367;&#2325; &#2352;&#2370;&#2346; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2311;&#2344; &#2352;&#2370;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2350;&#2369;&#2326;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; &#2358;&#2376;&#2357;&#2366;&#2354;, &#2325;&#2357;&#2325; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2381;&#2360; </strong><span style=\"font-weight: 400;\">&#2348;&#2367;&#2344;&#2366; &#2348;&#2368;&#2332; &#2351;&#2366; &#2347;&#2370;&#2354; &#2357;&#2366;&#2354;&#2375; &#2346;&#2380;&#2343;&#2379;&#2306; &#2325;&#2375; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319; &#2311;&#2360;&#2375; &#2325;&#2381;&#2352;&#2367;&#2346;&#2381;&#2335;&#2379;&#2327;&#2376;&#2350;&#2381;&#2360; &#2325;&#2375; &#2344;&#2366;&#2350; &#2360;&#2375; &#2349;&#2368; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2332;&#2367;&#2350;&#2381;&#2344;&#2379;&#2360;&#2381;&#2346;&#2352;&#2381;&#2350; </strong><span style=\"font-weight: 400;\">&#2346;&#2380;&#2343;&#2379;&#2306; &#2350;&#2375;&#2306; &#2344;&#2327;&#2381;&#2344; &#2348;&#2368;&#2332; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2332;&#2367;&#2360;&#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2325;&#2367; &#2313;&#2344;&#2325;&#2375; &#2348;&#2368;&#2332; &#2347;&#2354; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2338;&#2325;&#2375; &#2344;&#2361;&#2368;&#2306; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2346;&#2366;&#2311;&#2306;&#2360;, &#2360;&#2366;&#2311;&#2325;&#2376;&#2337;&#2381;&#2360;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which of the following are cryptogam?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2369;&#2359;&#2381;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Gymnosperm</p>\n", "<p>Dicots</p>\n", 
                                "<p>Angiosperm</p>\n", "<p>Pteridophyta</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2310;&#2357;&#2371;&#2340;&#2348;&#2368;&#2332;&#2368; (&#2332;&#2367;&#2350;&#2381;&#2344;&#2379;&#2360;&#2381;&#2346;&#2352;&#2381;&#2350;)</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2367;&#2348;&#2368;&#2332;&#2346;&#2340;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2350;&#2381;&#2344;&#2379;&#2360;&#2381;&#2346;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(d) <strong>Pteridophyta.</strong><span style=\"font-weight: 400;\"> It belongs to the vascular plant family. They reproduce through spores rather than seeds. Examples -&nbsp; ferns, horsetails and lycophytes. Four classes of pteridophyta - Psilophyta, Lycophyta, Arthrophyta, and Filicophyta. </span><strong>Cryptogams</strong><span style=\"font-weight: 400;\"> - A flowerless and seedless group of plants having hidden sex organs.&nbsp; Three main divisions&nbsp; - Thallophyta, Bryophyta and Pteridophyte</span></span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(d) </span><strong>&#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;</strong><span style=\"font-weight: 400;\">&#2404; &#2351;&#2361; &#2360;&#2306;&#2357;&#2361;&#2344;&#2368; &#2346;&#2366;&#2342;&#2346; &#2346;&#2352;&#2367;&#2357;&#2366;&#2352; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2357;&#2375; &#2348;&#2368;&#2332; &#2325;&#2375; &#2348;&#2332;&#2366;&#2351; &#2348;&#2368;&#2332;&#2366;&#2339;&#2369;&#2323;&#2306; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2346;&#2381;&#2352;&#2332;&#2344;&#2344; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2347;&#2352;&#2381;&#2344;, &#2361;&#2377;&#2352;&#2381;&#2360;&#2335;&#2375;&#2354; &#2324;&#2352; &#2354;&#2366;&#2311;&#2325;&#2379;&#2347;&#2366;&#2311;&#2335;&#2381;&#2360;&#2404; &#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366; &#2325;&#2375; &#2330;&#2366;&#2352; &#2357;&#2352;&#2381;&#2327; - &#2360;&#2366;&#2311;&#2354;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;, &#2354;&#2366;&#2311;&#2325;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;, &#2310;&#2352;&#2381;&#2341;&#2381;&#2352;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366; &#2324;&#2352; &#2347;&#2367;&#2354;&#2367;&#2325;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;&#2404; </span><strong>&#2325;&#2381;&#2352;&#2367;&#2346;&#2381;&#2335;&#2379;&#2327;&#2376;&#2350;&#2381;&#2360; </strong><span style=\"font-weight: 400;\">- &#2346;&#2369;&#2359;&#2381;&#2346;&#2361;&#2368;&#2344; &#2324;&#2352; &#2348;&#2368;&#2332;&#2352;&#2361;&#2367;&#2340; &#2346;&#2380;&#2343;&#2379;&#2306; &#2325;&#2366; &#2360;&#2350;&#2370;&#2361; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2331;&#2367;&#2346;&#2375; &#2361;&#2369;&#2319; &#2351;&#2380;&#2344; &#2309;&#2306;&#2327; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2340;&#2368;&#2344; &#2350;&#2369;&#2326;&#2381;&#2351; &#2357;&#2367;&#2349;&#2366;&#2327; - &#2341;&#2376;&#2354;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;, &#2348;&#2381;&#2352;&#2366;&#2351;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366; &#2324;&#2352; &#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>