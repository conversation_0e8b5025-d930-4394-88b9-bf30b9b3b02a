<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">A pen was sold for &#8377;28.75 at a profit of 15%. If it was sold for &#8377;25.75, then what would have been the percentage of profit?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Morning)</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2354;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;28.75 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;25.75 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Morning)</span></p>\n",
                    options_en: ["<p>2%</p>\n", "<p>3%</p>\n", 
                                "<p>4%</p>\n", "<p>1%</p>\n"],
                    options_hi: ["<p>2%</p>\n", "<p>3%</p>\n",
                                "<p>4%</p>\n", "<p>1%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">when</span><span style=\"font-family: Cambria Math;\"> pen is sold for &#8377;28.75 , There is a profit of 15% </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\">, percentage</span><span style=\"font-family: Cambria Math;\"> of p</span><span style=\"font-family: Cambria Math;\">rofit when pen is sold at &#8377;25.75 = 15 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>.</mo><mn>75</mn></mrow><mrow><mn>28</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>103</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 15 = 103% of CP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the required profit % = 3%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2354;&#2350;</span><span style=\"font-family: Cambria Math;\"> &#8377;28.75 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2354;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;25.75 </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> = 15 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>.</mo><mn>75</mn></mrow><mrow><mn>28</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>103</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\">15 =&nbsp; </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 103%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2309;&#2349;&#2368;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 3%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">By selling a fridge for &#8377;18,200, Anu loses 15%. Find the cost price of the fridge. (Consider integral part only)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2381;&#2352;&#2367;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;18,200 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2344;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2381;&#2352;&#2367;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Mangal;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>&#8377;21,820</p>\n", "<p>&#8377;21,411</p>\n", 
                                "<p>&#8377;19,680</p>\n", "<p>&#8377;19,411</p>\n"],
                    options_hi: ["<p>&#8377;21,820</p>\n", "<p>&#8377;21,411</p>\n",
                                "<p>&#8377;19,680</p>\n", "<p>&#8377;19,411</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of a fridge = &#8377;18,200</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CP of the fridge = 18200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>100</mn></mrow><mn>85</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 214.11 &times; 100 = &#8377; 21411</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2381;&#2352;&#2367;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;18,200</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2347;&#2381;&#2352;&#2367;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 18200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>100</mn></mrow><mn>85</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 214.11 &times; 100 = &#8377; 21411</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">A fruit seller purchased 300 bananas at the rate of &#8377;18 p</span><span style=\"font-family: Cambria Math;\">er dozen and sold 200 bananas at the rate of &#8377;24 per dozen and the remaining bananas at the rate of &#8377;21 per dozen. What is his net profit percentage?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Evening)</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 300 </span><span style=\"font-family: Mangal;\">&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;18 </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;&#2381;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 200 </span><span style=\"font-family: Mangal;\">&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;24 </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;&#2381;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;21 </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;&#2381;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;</span><span style=\"font-family: Mangal;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Evening)</span></p>\n",
                    options_en: ["<p>28%</p>\n", "<p>26%</p>\n", 
                                "<p>27%</p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27</mn><mfrac><mn>7</mn><mn>9</mn></mfrac></math>%</span></p>\n"],
                    options_hi: ["<p>28%</p>\n", "<p>26%</p>\n",
                                "<p>27%</p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27</mn><mfrac><mn>7</mn><mn>9</mn></mfrac></math>%</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CP of 1 banana = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 300 = &#8377;450</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of 1 banana = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 200 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>21</mn></mrow><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 400 + 175 = &#8377;575</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>575</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>450</mn></mrow><mn>450</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>450</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 27<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Mangal;\">&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 300 = &#8377;450</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Mangal;\">&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 200 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>21</mn></mrow><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100</span><span style=\"font-family: Cambria Math;\"> = 400 + 175 = &#8377;575</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>575</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>450</mn></mrow><mn>450</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>450</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 27<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">The profit triples if the selling price is doubled. The profit percentage is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Morning)</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Morning)</span></p>\n",
                    options_en: ["<p>110</p>\n", "<p>50</p>\n", 
                                "<p>100</p>\n", "<p>28</p>\n"],
                    options_hi: ["<p>110</p>\n", "<p>50</p>\n",
                                "<p>100</p>\n", "<p>28</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let CP and SP be &#8377;x&nbsp; and&nbsp; &#8377;y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;CP&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SP&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Profit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Original&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; y - x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2y - x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2y - x = 3 (y - x) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2y - x = 3y - 3x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mi>y</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 100%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;&nbsp;</span><span style=\"font-family: Cambria Math;\"> &#8377;x </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\">&nbsp; &#8377;y </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &#2325;&#2381;&#2352;</span><span style=\"font-family: Mangal;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; y - x</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2y - x</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2366;&#2344;&#2369;&#2360;&#2366;</span><span style=\"font-family: Mangal;\">&#2352;</span><span style=\"font-family: Cambria Math;\"> ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2y - x = 3 (y - x) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2y - x = 3y - 3x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mi>y</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 100%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">The marked price of 42 items was equal to the cost price of 70 items. The selling price of 25 items was equal to the marked price of 21 items. Calculate the percentage profit or loss from the sale of each item.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> 42 </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 70 </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> 25 </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 21 </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;</span><span style=\"font-family: Mangal;\">&#2352;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>42% profit</p>\n", "<p>29% profit</p>\n", 
                                "<p>29% loss</p>\n", "<p>40% profit</p>\n"],
                    options_hi: ["<p>42% <span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span></p>\n", "<p>29% <span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span></p>\n",
                                "<p>29% <span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n", "<p>40% <span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">42 &times; MP = 70 &times; CP</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>70</mn><mn>42</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>3</mn></mfrac></math>) &times; 5 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>15</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Again ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">25 &times; SP = 21 &times; MP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>21</mn></mrow><mn>25</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, the ratio </span><span style=\"font-family: Cambria Math;\">of CP</span><span style=\"font-family: Cambria Math;\"> ,SP and MP = 15 : 21 : </span><span style=\"font-family: Cambria Math;\"> 25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the</span><span style=\"font-family: Cambria Math;\"> required profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>15</mn></mrow><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 40%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">42 &times; </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 70 &times; </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>70</mn><mn>42</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">) &times; 5 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>15</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2369;&#2344;&#2307;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">25 &times; </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 21 &times; </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>25</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">15 :</span><span style=\"font-family: Cambria Math;\"> 21 : 25</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>15</mn></mrow><mn>15</mn></mfrac></math>&times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> = 40%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Giri bought an old machine for &#8377;2,000 and spent &#8377;500 on its repair. He sold it for &#8377;4,000. His profit percentage is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/20</span><span style=\"font-family: Cambria Math;\">22(Evening)</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2367;&#2352;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2369;&#2352;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> &#8377;2,000 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;500 </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2352;&#2350;&#2381;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;4,000 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2327;</span><span style=\"font-family: Mangal;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022(Evening)</span></p>\n",
                    options_en: ["<p>30%</p>\n", "<p>60%</p>\n", 
                                "<p>40%</p>\n", "<p>20%</p>\n"],
                    options_hi: ["<p>30%</p>\n", "<p>60%</p>\n",
                                "<p>40%</p>\n", "<p>20%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total CP </span><span style=\"font-family: Cambria Math;\">of machine</span><span style=\"font-family: Cambria Math;\"> = 2000 + 500 = &#8377;2500</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP </span><span style=\"font-family: Cambria Math;\">of machine</span><span style=\"font-family: Cambria Math;\"> = &#8377;4000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2500</mn></mrow><mn>2500</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>25</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 60%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 2000 + 500 = &#8377;2500</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;4000</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2500</mn></mrow><mn>2500</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>25</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 60%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">A sells an article to B at 10% profit. B sells it to C at 25% profit. If C pays &#8377;6,875 for it, then </span><span style=\"font-family: Cambria Math;\">the</span><span style=\"font-family: Cambria Math;\"> price at which A bought it is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Morning)</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 1</span><span style=\"font-family: Cambria Math;\">0% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> &#8377;6,875 </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Morning)</span></p>\n",
                    options_en: ["<p>&#8377;4,665</p>\n", "<p>&#8377;4,850</p>\n", 
                                "<p>&#8377;5,000</p>\n", "<p>&#8377;5,500</p>\n"],
                    options_hi: ["<p>&#8377;4,665</p>\n", "<p>&#8377;4,850</p>\n",
                                "<p>&#8377;5,000</p>\n", "<p>&#8377;5,500</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the CP of an article for A </span><span style=\"font-family: Cambria Math;\">= 100 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of an article for A = CP of the article for B = 110</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of the article for B = CP of the article for C = 110 &times; 125% = 137.5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">137.5 </span><span style=\"font-family: Cambria Math;\">unit ----------</span><span style=\"font-family: Cambria Math;\"> &#8377;6875</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100 unit ---------- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6875</mn><mrow><mn>137</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = &#8377;5,000</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, A </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 100 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;&#2312;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = B </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 110</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = C </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 110 &times; 125% = 137.5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">137.5 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;</span><span style=\"font-family: Mangal;\">&#2312;</span><span style=\"font-family: Cambria Math;\"> ----------</span><span style=\"font-family: Cambria Math;\"> &#8377;6875</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> ---------- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6875</mn><mrow><mn>137</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &times; 100 = &#8377;5,000</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">A man sold a radio set and gained one-eighth of its cost price. What is the profit percent?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Afternoon)</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">1,800 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2375;&#2337;&#2367;&#2323;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 1/8 </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2367;&#2332;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Afternoon)</span></p>\n",
                    options_en: ["<p>20<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>12.5%</p>\n", 
                                "<p>15.5%</p>\n", "<p>18%</p>\n"],
                    options_hi: ["<p>20%</p>\n", "<p>12.5%</p>\n",
                                "<p>15.5%</p>\n", "<p>18%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let CP of radio = &#8377;8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit =</span><span style=\"font-family: Cambria Math;\"> 8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 12.5% </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2352;&#2375;&#2337;&#2367;&#2351;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;8</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> = 8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;1</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Mangal;\">&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 12.5% </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">Raghuvir purchased some perishable items for sale but 36% of those items could not be sold and went bad. However, Raghuvir managed to sell the rest of the items at </span><span style=\"font-family: Cambria Math;\">a price that helped him earn an overall profit of 28%. At what percentage above the cost price of each item did Raghuvir sell each of the items that did not go bad?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Evening)</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Mangal;\">&#2352;&#2328;&#2369;&#2357;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2366;&#2358;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 36% </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2325;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2312;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2354;&#2366;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2352;&#2328;&#2369;&#2357;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;&#2350;&#2351;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2367;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 28% </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2367;&#2354;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2328;&#2369;&#2357;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;</span><span style=\"font-family: Mangal;\">&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Evening)</span></p>\n",
                    options_en: ["<p>100%</p>\n", "<p>92%</p>\n", 
                                "<p>63%</p>\n", "<p>120%</p>\n"],
                    options_hi: ["<p>100%</p>\n", "<p>92%</p>\n",
                                "<p>63%</p>\n", "<p>120%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>128</mn></mrow><mrow><mn>64</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>1</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 100%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>128</mn></mrow><mrow><mn>64</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>1</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 100%</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> A woman sold her earphone for Rs. 2,000 and got a percentage profit equal to the numerical value of cost price. The cost price of the earphone is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Morning)</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2312;&#2351;&#2352;&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> 2,000 </span><span style=\"font-family: Mangal;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2340;&#2381;&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2312;&#2351;&#2352;&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Morning)</span></p>\n",
                    options_en: ["<p>Rs. 500</p>\n", "<p>Rs. 200</p>\n", 
                                "<p>Rs. 600</p>\n", "<p>Rs. 400</p>\n"],
                    options_hi: ["<p>Rs. 500</p>\n", "<p>Rs. 200</p>\n",
                                "<p>Rs. 600</p>\n", "<p>Rs. 400</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the CP of earphone = &#8377;x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then profit % = x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>x</mi></mrow><mi>x</mi></mfrac><mo>&nbsp;</mo></math>&times; 100 = x</span></p>\r\n<p><span style=\"font-weight: 400;\">(2000 - x) </span>&times; 100 = <span style=\"font-weight: 400;\">x&sup2;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, putting the different value of x in the given options to satisfy the result we get x = 400</span></p>\r\n<p><span style=\"font-weight: 400;\">(2000 - 400) </span>&times; 100 = <span style=\"font-weight: 400;\">&nbsp;400&sup2;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">160000 = 160000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS = RHS</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So,</span><span style=\"font-family: Cambria Math;\"> CP of earphone = &#8377;400</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2312;&#2351;&#2352;&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= &#8377;x</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">% = x</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2366;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>x</mi></mrow><mi>x</mi></mfrac></math>&times; 100 = x</span></p>\r\n<p><span style=\"font-weight: 400;\">(2000 - x) </span>&times; 100 = <span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">x&sup2;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Mangal;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> x = 400 </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Mangal;\">&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">(2000 - 400) </span>&times; 100 = 400<span style=\"font-weight: 400;\">&sup2;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">160000 = 160000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS = RHS</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Mangal;\">&#2312;&#2351;&#2352;&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;400</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A seller combines 26 kg of rice priced at &#8377;20 per kg with 30 kg of rice priced at &#8377;36 per kg and sells the mixture for &#8377;30 per kg. What is the percentage of profit he mak</span><span style=\"font-family: Cambria Math;\">es?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Afternoon)</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> &#8377;20 </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> kg </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 26 kg </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;36 </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> kg </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 30 kg </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2367;&#2354;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2367;&#2358;&#2381;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;30 </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> kg </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2350;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03</span><span style=\"font-family: Cambria Math;\">/06/2022(Afternoon)</span></p>\n",
                    options_en: ["<p>8</p>\n", "<p>5</p>\n", 
                                "<p>7</p>\n", "<p>3</p>\n"],
                    options_hi: ["<p>8</p>\n", "<p>5</p>\n",
                                "<p>7</p>\n", "<p>3</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CP of 1st type of Rice = 26 &times; 20 = &#8377;520</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CP of 2nd type of Rice = 30 &times; 36 = &#8377;1080</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total CP = &#8377;1600</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of mixture = (26 + 30) &times; 30 = 56 &times; 30 = &#8377;1680</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1680</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1600</mn></mrow><mn>1600</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>1600</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 5%</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 26 &times; 20 = &#8377;520</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 30 &times; 36 = &#8377;1080</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;1600</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2367;&#2358;&#2381;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = (26 + 30) &times; 30 = 56 &times; 30 = &#8377;1680</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1680</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1600</mn></mrow><mn>1600</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>1600</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 </span><span style=\"font-family: Cambria Math;\">= 5%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A dealer had 120 kg of wheat. A part of it was sold by him at 10% gain and the rest at 25% gain. </span><span style=\"font-family: Cambria Math;\">overall</span><span style=\"font-family: Cambria Math;\">, he had a gain of 15%. How much of the wheat was sold at 10% gain?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Afternoon)</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2337;&#2368;&#2354;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> 120 </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2354;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2375;&#2361;&#2370;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2367;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2369;&#2310;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Afternoon)</span></p>\n",
                    options_en: ["<p>80 kg</p>\n", "<p>60 kg</p>\n", 
                                "<p>40 kg</p>\n", "<p>50<span style=\"font-family: Cambria Math;\"> kg</span></p>\n"],
                    options_hi: ["<p>80 kg</p>\n", "<p>60 kg</p>\n",
                                "<p>40 kg</p>\n", "<p>50 kg</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1672658099/word/media/image1.png\" width=\"151\" height=\"140\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total weight of Wheat = 120 kg</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 unit = 120 kg </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 2 = 80 kg</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the quantity of the wheat sold at 10% gain = 80 kg</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1672658099/word/media/image1.png\" width=\"144\" height=\"133\"></p>\r\n<p><span style=\"font-family: Mangal;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 120kg</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 120 kg</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 2 = 80 kg</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: 10% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 80 kg</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">An authorised rice dealer purchases rice at Rs. 32 per kg and sells it at Rs. 40 per kg. But while selling, he is giving only 900 grams instead of 1 kg. Find the actual profit percentage of the dealer.</span><span style=\"font-family: Cambria Math;\"> (Correct to one decimal place)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Evening)</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2343;&#2367;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> 32 </span><span style=\"font-family: Mangal;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> kg </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Mangal;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> kg </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 1 kg </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2327;&#2361;</span><span style=\"font-family: Cambria Math;\"> 900 gm </span><span style=\"font-family: Mangal;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Mangal;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2332;&#2367;</span><span style=\"font-family: Mangal;\">&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Mangal;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Evening)</span></p>\n",
                    options_en: ["<p>37.8%</p>\n", "<p>39.7%</p>\n", 
                                "<p>39.8%</p>\n", "<p>38.9%</p>\n"],
                    options_hi: ["<p>37.8%</p>\n", "<p>39.7%</p>\n",
                                "<p>39.8%</p>\n", "<p>38.9%</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;CP&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; SP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; _______________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 36&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">50&nbsp; &nbsp;or&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 18&nbsp; &nbsp;:&nbsp; &nbsp;25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>18</mn></mrow><mn>18</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>18</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 38.88 &asymp;</span><span style=\"font-family: Cambria Math;\"> 38.9%</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&nbsp; &nbsp; &nbsp;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Mangal;\">&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;:</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ____________________</span><span style=\"font-family: Cambria Math;\">___</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;36&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">50&nbsp; &nbsp; &nbsp; or&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> 18&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;25</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>18</mn></mrow><mn>18</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>18</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 38.88 &asymp;</span><span style=\"font-family: Cambria Math;\"> 38.9%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Vishnu sold two items X and Y for &#8377;600 each, thereby gaining 20% on item X and losing 20% on item Y. Find his overall loss or gain percentage.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 0</span><span style=\"font-family: Cambria Math;\">6/06/2022(Morning)</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2359;&#2381;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> X </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Y </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> &#8377;600 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> X </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> Y </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2369;&#2312;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;</span><span style=\"font-family: Mangal;\">&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Morning)</span></p>\n",
                    options_en: ["<p>Loss of 4%</p>\n", "<p>Gain of 4%</p>\n", 
                                "<p>Loss of 41.6%</p>\n", "<p>Loss or gain of 0%</p>\n"],
                    options_hi: ["<p>4% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n", "<p>4% <span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span></p>\n",
                                "<p>41.6% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n", "<p>0% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span></p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;CP&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;SP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">X&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">6 )</span><span style=\"font-family: Cambria Math;\"> &times; 4 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">4 )</span><span style=\"font-family: Cambria Math;\"> &times; 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total CP = 20 + 30 = &#8377;50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss = (+1 &times; 4) - 1 &times; 6 = 4 - 6 = -2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>50</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 4% </span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&nbsp; &nbsp; &nbsp; &nbsp;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;X&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">6 )</span><span style=\"font-family: Cambria Math;\"> &times; 4 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;Y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">4 )</span><span style=\"font-family: Cambria Math;\"> &times; 6</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 20 + 30 = &#8377;50</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> = (+1 &times; 4) - 1 &times; 6 = 4 - 6 = -2</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\">% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>50</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 4% </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> A dishonest dealer is selling sugar at Rs. 40 per kg and pretending that he is selling it at a loss of Rs. 5 per kg but he actually gives 800g instead of 1 kg, using a faulty weight. What is his actual profit or loss percent</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Afternoon)</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2312;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2368;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Mangal;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> kg </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2367;&#2326;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Mangal;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2354;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2369;&#2325;&#2360;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2360;&#2381;&#2340;</span><span style=\"font-family: Mangal;\">&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 1 kg </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2332;&#2366;&#2351;</span><span style=\"font-family: Cambria Math;\"> 800 gm </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Afternoon)</span></p>\n",
                    options_en: ["<p>Gain, 11%</p>\n", "<p>Gain, 11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Loss.</span><span style=\"font-family: Cambria Math;\"> 10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>Loss, 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%</span></p>\n"],
                    options_hi: ["<p>11% <span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span></p>\n", "<p>11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span></p>\n",
                                "<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n", "<p>9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CP&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;SP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 40&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 45</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 800&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 1000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 36&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 40</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>36</mn></mrow><mn>36</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>36</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&nbsp; &nbsp; &#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Mangal;\">&#2351;&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 40&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;45</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;800&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 1000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _</span><span style=\"font-family: Cambria Math;\">________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 36&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;40</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>36</mn></mrow><mn>36</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>36</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Sudhir claimed to sell his items at only 8% above the cost of production, but used a weight that had 750 grams written on it, though it actually wei</span><span style=\"font-family: Cambria Math;\">ghed 720 grams. What was the actual profit percentage earned by Sudhir?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Evening)</span></p>\n",
                    question_hi: "<p>16<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2360;&#2369;&#2343;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Mangal;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2320;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2332;</span><span style=\"font-family: Mangal;\">&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 750 g </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2354;&#2366;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> 720 g </span><span style=\"font-family: Mangal;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2369;&#2343;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Eve</span><span style=\"font-family: Cambria Math;\">ning)</span></p>\n",
                    options_en: ["<p>12.6%</p>\n", "<p>12.25%</p>\n", 
                                "<p>12.5%</p>\n", "<p>12.75%</p>\n"],
                    options_hi: ["<p>12.6%</p>\n", "<p>12.25%</p>\n",
                                "<p>12.5%</p>\n", "<p>12.75%</p>\n"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CP&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; SP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;108</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;720&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;750</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;______________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 600&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 675 </span><span style=\"font-family: Cambria Math;\">or&nbsp; &nbsp; &nbsp; 24</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;27</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Actual Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>27</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>24</mn></mrow><mn>24</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>24</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 12.5% </span></p>\n",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&nbsp; &nbsp; &nbsp;&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Mangal;\">&#2351;&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> :&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;108</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;720&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 750</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;______________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;600&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;675&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">or&nbsp; &nbsp; 24&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> :&nbsp; &nbsp; &nbsp; 27</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>27</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>24</mn></mrow><mn>24</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\">100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>24</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 12.5% </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> A merchant gives an additional discount of 10% on the product after an initial discount of 20% on the listed price. He managed to make a 10% profit by selling the product for &#8377;54,000. What was the listed price of the produ</span><span style=\"font-family: Cambria Math;\">ct?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Evening)</span></p>\n",
                    question_hi: "<p>17<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2340;&#2367;&#2352;&#2367;&#2325;&#2381;</span><span style=\"font-family: Mangal;\">&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;</span><span style=\"font-family: Mangal;\">&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;54,000 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Evening)</span></p>\n",
                    options_en: ["<p>&#8377;77,500</p>\n", "<p>&#8377;77,000</p>\n", 
                                "<p>&#8377;75,000</p>\n", "<p>&#8377;72,500</p>\n"],
                    options_hi: ["<p>&#8377;77,500</p>\n", "<p>&#8377;77,0<span style=\"font-family: Cambria Math;\">00</span></p>\n",
                                "<p>&#8377;75,000</p>\n", "<p>&#8377;72,500</p>\n"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SPof the product = &#8377;54,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CP of the product =&#8377; 54000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>11</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>540000</mn><mrow><mn>11</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective discount of 10% and 20% = 10 + 20 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 30 - 2 = 28%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;CP&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;MP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;<span style=\"font-weight: 400;\">(100 - 28)</span>%&nbsp; &nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; (100 + 10)%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 72&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">110&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;or</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; 36&nbsp; &nbsp; :&nbsp; &nbsp; 55</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">36 </span><span style=\"font-family: Cambria Math;\">unit ----------</span><span style=\"font-family: Cambria Math;\"> &#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>540000</mn><mn>11</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">55 </span><span style=\"font-family: Cambria Math;\">unit ----------</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>540000</mn><mrow><mn>11</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>36</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; 55 = &#8377;75000</span></p>\n",
                    solution_hi: "<p>17<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;54,000</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 54000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>11</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>540000</mn><mrow><mn>11</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 10 + 20 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 30 - 2 = 28%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Mangal;\">&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style=\"font-weight: 400;\">(100 - 28)</span>%&nbsp; &nbsp; &nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(100 + 10)%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;72&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">110&nbsp; &nbsp; &nbsp; &nbsp;or</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; 36&nbsp; &nbsp;:&nbsp; &nbsp;55</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">36 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;</span><span style=\"font-family: Mangal;\">&#2312;</span><span style=\"font-family: Cambria Math;\"> ----------</span><span style=\"font-family: Cambria Math;\"> &#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>540000</mn><mn>11</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">55 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;</span><span style=\"font-family: Mangal;\">&#2312;</span><span style=\"font-family: Cambria Math;\"> ----------</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>540000</mn><mrow><mn>11</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>36</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; 55 = &#8377;75000</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If a man were to sell his home theater system for &#8377; 14,400, he would lose 25%. But if he </span><span style=\"font-family: Cambria Math;\">wants</span><span style=\"font-family: Cambria Math;\"> to gain 20%, the item should be sold for:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 07</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>18<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2367;&#2319;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\"> 14,400 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 07</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>&#8377; 23,040</p>\n", "<p>&#8377; 23,000</p>\n", 
                                "<p>&#8377; 22,960</p>\n", "<p>&#8377; 23,300</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"> &#8377; 23,040</span></p>\n", "<p>&#8377; 23,000</p>\n",
                                "<p>&#8377; 22,960</p>\n", "<p>&#8377; 23,300</p>\n"],
                    solution_en: "<p>18<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the CP of theater system be 100%</span></p>\r\n<p><span style=\"font-weight: 400;\">(100 - 25)</span>% = 75% corresponds to &#8377;14,400</p>\r\n<p><span style=\"font-weight: 400;\">(100 + 20)</span>% = 120% corresponds to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14400</mn><mn>75</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> &times; 120 = &#8377;23,040 </span></p>\n",
                    solution_hi: "<p>18<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2341;&#2367;&#2319;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 100% </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-weight: 400;\">(100 - 25)</span>% = 75% &#8377;14,400 <span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2344;&#2369;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">(100 + 20)</span>% = 120% <span style=\"font-family: Mangal;\">&#2309;&#2344;&#2369;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14400</mn><mn>75</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 120 = &#8377;23,040</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">While selling an item, a businessman allows a 40% discount on the marked price and incurs a loss of 30%. If the item is sold at the marked price, profit will be _______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 07</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 40% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 07</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>10%</p>\n", "<p>16%</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></math> %</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</span></p>\n"],
                    options_hi: ["<p>10%</p>\n", "<p>16%</p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</span></p>\n"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let MP of an item = &#8377;100</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of an item = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;60</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CP of an item = 60 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>70</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>7</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since the item is sold at MP. So</span><span style=\"font-family: Cambria Math;\">,.</span><span style=\"font-family: Cambria Math;\"> New SP = &#8377;100 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CP :</span><span style=\"font-family: Cambria Math;\"> SP = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mrow><mn>7</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> : 100 = 600 : 700 or 6 : 7</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>-</mo><mn>6</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 16</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</span></p>\n",
                    solution_hi: "<p>19<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;100</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;60</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 60 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>70</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>7</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2330;&#2370;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;100</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Mangal;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mrow><mn>7</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> : 100 = 600 : 700 </span><span style=\"font-family: Mangal;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 6 : 7</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>-</mo><mn>6</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 16</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A shopkeeper sells rice at cost price but uses a faulty weighing machine and thus gains a profit of 25%. Find how many grams rice he is giving in 1 </span><span style=\"font-family: Cambria Math;\">kg ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 07</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>20<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2380;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2354;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 07</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>800gm</p>\n", "<p>700gm</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"> 900gm</span></p>\n", "<p>750 gm</p>\n"],
                    options_hi: ["<p>8<span style=\"font-family: Cambria Math;\">00gm</span></p>\n", "<p>700gm</p>\n",
                                "<p>900gm</p>\n", "<p>750 gm</p>\n"],
                    solution_en: "<p>20<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the CP of rice for the shopkeeper </span><span style=\"font-family: Cambria Math;\">be</span><span style=\"font-family: Cambria Math;\"> 4 units. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since he uses faulty weight, </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\">, the SP of rice for him be 5 units.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, it is represented as </span><span style=\"font-family: Cambria Math;\">CP :</span><span style=\"font-family: Cambria Math;\"> SP = 4 : 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the quantity of rice he is gi</span><span style=\"font-family: Cambria Math;\">ving to customer = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 4 = 800 gm</span></p>\n",
                    solution_hi: "<p>20<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2330;&#2370;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2380;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Mangal;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Mangal;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 4 : 5 </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 4 = 800 </span><span style=\"font-family: Mangal;\">&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>