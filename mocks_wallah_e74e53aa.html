<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following diseases is caused by a virus ?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन-सा रोग विषाणु से होता है?</p>",
                    options_en: ["<p>Cholera</p>", "<p>Chicken Pox</p>", 
                                "<p>Typhoid</p>", "<p>Tuberculosis</p>"],
                    options_hi: ["<p>हैजा</p>", "<p>चिकन पॉक्स</p>",
                                "<p>टाइफाइड</p>", "<p>क्षय रोग</p>"],
                    solution_en: "<p>1.(b) <strong>Chicken Pox.</strong> It is a highly contagious illness caused by Varicella Zoster Virus (VZV). <strong>Takahashi</strong> developed the first vaccine against chickenpox.<strong> Major diseases and their cause: Virus</strong> - Rabies, Measles, Herpes, Meningitis, Hepatitis, Trachoma, Polio, AIDS (Acquired Immune Deficiency Syndrome), Smallpox, Dengue fever, Influenza. <strong>Bacteria</strong> - plague, diphtheria, typhoid, whooping cough, tetanus, leprosy, syphilis, pneumonia,cholera. <strong>Protozoa-</strong> pyorrhea, kala-azar, malaria, dysentery, sleeping sickness. <strong>Fungi</strong> - Aspergillus infection, Athlete\'s foot, Jock itch, Ringworm, Coccidioidomycosis, Sporotrichosis, histoplasmosis.</p>",
                    solution_hi: "<p>1.(b) <strong>चिकन पॉक्स</strong> । यह वैरीसेला ज़ोस्टर वायरस (VZV) के कारण होने वाली एक अत्यधिक संक्रामक बीमारी है। ताकाहाशी ने चिकन पॉक्स के खिलाफ पहला टीका विकसित किया। <strong>प्रमुख रोग एवं उनके कारण</strong>: विषाणु - रेबीज, खसरा, हर्पीस, मेनिनजाइटिस, हेपेटाइटिस, ट्रेकोमा, पोलियो, AIDS (एक्वायर्ड इम्यून डेफिसिएंसी सिंड्रोम), स्मालपॉक्स , डेंगू बुखार, इन्फ्लुएंजा। <strong>जीवाणु</strong> - प्लेग, डिप्थीरिया, टाइफाइड, काली खांसी, टिटनस, कुष्ठ रोग, सिफ़लिस, निमोनिया, हैजा। <strong>प्रोटोजोआ-</strong> पायरिया, कालाजार, मलेरिया, पेचिश, नींद की बीमारी। <strong>कवक</strong> - एस्परगिलस संक्रमण, एथलीट फुट, जॉक खुजली, दाद, कोक्सीडायोडोमाइकोसिस, स्पोरोट्रीकोसिस, हिस्टोप्लास्मोसिस।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. The disease related with apple is known as</p>",
                    question_hi: "<p>2. सेब से संबंधित रोग को किस नाम से जाना जाता है?</p>",
                    options_en: ["<p>green ear disease</p>", "<p>red rust disease</p>", 
                                "<p>fire blight</p>", "<p>tikka disease</p>"],
                    options_hi: ["<p>हरे कान की बीमारी</p>", "<p>लाल जंग रोग</p>",
                                "<p>फायर ब्लाइट</p>", "<p>टिक्का रोग</p>"],
                    solution_en: "<p>2.(c)<strong> Fire blight:- </strong>It is a highly contagious bacterial infection affecting fruit trees, such as apple and pear, causing wilting, blackening, and \"burned\" appearance of branches and fruit. <strong>Tikka disease</strong> - Crop- Groundnut, Caused by - Fungus. <strong>Green ear disease</strong> - Crop - Millet, Caused by - Fungus. Red rust disease - Crop - Tea, Caused by - Algae of the genus Cephaluros.</p>",
                    solution_hi: "<p>2.(c) <strong>फायर ब्लाइट</strong>:- यह एक अत्यधिक संक्रामक जीवाणु संक्रमण है जो सेब और नाशपाती जैसे पेड़ों और फलों को प्रभावित करता है, जिसके कारण शाखाएं और फल मुरझा जाते हैं, काले पड़ जाते हैं और \"जले हुए\" दिखाई देते हैं। <strong>टिक्का रोग </strong>- फसल - मूंगफली, कारण - कवक।<strong> ग्रीन ईयर रोग</strong> - फसल - बाजरा, कारण - कवक। <strong>रेड-रस्ट रोग </strong>- फसल - चाय, कारण - जीनस सेफ़ेल्युरोस ( genus Cephaluros) के शैवाल।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "3. Beriberi is a disease caused due to deficiency of________.",
                    question_hi: "3. बेरीबेरी एक रोग है जो _________ की कमी के कारण होता है।",
                    options_en: [" Vitamin A ", " Vitamin B1 ", 
                                " Vitamin C  ", " Vitamin D "],
                    options_hi: [" विटामिन A", " विटामिन B1",
                                " विटामिन C", " विटामिन D"],
                    solution_en: "<p>3.(b) <strong>Vitamin B1</strong> (Thiamin): <strong>Sources</strong> - pork, whole grains, lentils, spinach, sunflower seeds, milk, red meat, cauliflower, and nuts.<strong> Vitamin A</strong> (Retinol) - Deficiency diseases - Night blindness, Xerophthalmia.<strong> Vitamin C</strong> (Ascorbic acid) - <strong>Deficiency diseases </strong>- Anemia, Infections, Bleeding Gums, Scurvy, Poor Wound Healing. <strong>Vitamin D (</strong>Calciferol) - Deficiency diseases - Osteoporosis, Rickets.</p>",
                    solution_hi: "<p>3.(b) <strong>विटामिन B1 </strong>(थायमिन): <strong>स्रोत </strong>- सूअर का मांस, साबुत अनाज, दाल, पालक, सूरजमुखी के बीज, दूध, लाल मांस, फूलगोभी, और मेवे। <strong>विटामिन A</strong> (रेटिनॉल) - <strong>कमी से होने वाले रोग - </strong>रतौंधी, ज़ेरोफथाल्मिया। <strong>विटामिन C </strong>(एस्कॉर्बिक अम्ल) - <strong>कमी से होने वाले रोग </strong>- एनीमिया, संक्रमण, मसूड़ों से खून आना, स्कर्वी, घाव ठीक से न भरना।<strong> विटामिन </strong>D(कैल्सीफेरोल) - कमी से होने वाले रोग - ऑस्टियोपोरोसिस, रिकेट्स।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following human diseases is caused by bacteria?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन सा मानव रोग जीवाणु के कारण होता है?</p>",
                    options_en: ["<p>Typhoid</p>", "<p>Polio</p>", 
                                "<p>Measles</p>", "<p>Hepatitis A</p>"],
                    options_hi: ["<p>टाइफाइड</p>", "<p>पोलियो</p>",
                                "<p>खसरा</p>", "<p>हेपेटाइटिस A</p>"],
                    solution_en: "<p>4.(a) <strong>Typhoid</strong> - It caused by Salmonella Typhi (Bacteria). <strong>Common infectious diseases caused by bacteria</strong> - Leprosy (Mycobacterium leprae), Pertussis (Bordetella pertussis), Tetanus (Clostridium tetani), Plague (Yersinia pestis). Polio, Measles and Hepatitis-A are caused by viruses.</p>",
                    solution_hi: "<p>4.(a) <strong>टाइफाइड</strong> - यह साल्मोनेला टाइफी (जीवाणु) के कारण होता है।<strong> बैक्टीरिया से होने वाले सामान्य संक्रामक रोग</strong> - कुष्ठ (माइकोबैक्टीरियम लेप्री), पर्टुसिस (बोर्डेटेला पर्टुसिस), टिटनस (क्लोस्ट्रीडियम टेटानी), प्लेग (येर्सिनिया पेस्टिस)। पोलियो, खसरा और हेपेटाइटिस-A वायरस के कारण होते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. The cause of Hepatitis A is a</p>",
                    question_hi: "<p>5. हेपेटाइटिस A का कारण क्या है ?</p>",
                    options_en: ["<p>virus</p>", "<p>bacteria</p>", 
                                "<p>protozoa</p>", "<p>mosquito bite</p>"],
                    options_hi: ["<p>विषाणु</p>", "<p>जीवाणु</p>",
                                "<p>प्रोटोजोआ</p>", "<p>मच्छर काटना</p>"],
                    solution_en: "<p>5.(a)<strong> Virus. Hepatitis A</strong> - It is a viral infection that affects the liver and is caused by the hepatitis A virus (HAV). The virus is transmitted through contaminated food or water, or by close contact with an infected person. <strong>Bacteria</strong> - They are single-celled microorganisms that can be found in various environments and play essential roles in ecosystems, some being beneficial while others can cause diseases. <strong>Protozoa</strong> - They are a group of single-celled eukaryotes, either free-living or parasitic, that feed on organic matter such as other microorganisms or organic tissues and debris.</p>",
                    solution_hi: "<p>5.(a) <strong>विषाणु</strong> । <strong>हेपेटाइटिस A </strong>- यह एक वायरल संक्रमण है जो यकृत को प्रभावित करता है और हेपेटाइटिस A विषाणु (HAV) के कारण होता है। यह विषाणु दूषित भोजन या पानी से या किसी संक्रमित व्यक्ति के निकट संपर्क से फैलता है। <strong>जीवाणु</strong> - ये एकल-कोशिका वाले सूक्ष्मजीव हैं जो विभिन्न वातावरण में पाए जा सकते हैं और पारिस्थितिक तंत्र में आवश्यक भूमिका निभाते हैं, कुछ फायदेमंद होते हैं जबकि अन्य बीमारियों का कारण बन सकते हैं। <strong>प्रोटोजोआ </strong>- ये एकल-कोशिका वाले यूकेरियोट्स का एक समूह हैं, जो या तो मुक्त-जीवित या परजीवी हैं, जो अन्य सूक्ष्मजीवों या कार्बनिक ऊतकों और मलबे जैसे कार्बनिक पदार्थों का इस्तेमाल भोजन के रूप में करते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following is an example of non-infectious disease?</p>",
                    question_hi: "<p>6. निम्न में से कौन असंक्रामक रोग का उदाहरण है?</p>",
                    options_en: ["<p>High Blood Pressure</p>", "<p>Typhoid</p>", 
                                "<p>Influenza</p>", "<p>Pneumonia</p>"],
                    options_hi: ["<p>उच्च रक्त चाप</p>", "<p>टाइफाइड</p>",
                                "<p>इंफ्लुएंजा</p>", "<p>न्यूमोनिया</p>"],
                    solution_en: "<p>6.(a)<strong> High Blood Pressure</strong> - Caused by unhealthy lifestyle choices, such as not getting enough regular physical activity, diabetes and obesity. Other non infectious diseases (non communicable disease) - Cardiovascular diseases, Diabetes, Preventable cancers, Chronic respiratory diseases, Mental health. <strong>Infectious disease</strong> - Typhoid (bacterial disease), <strong>Influenza</strong> (Viral disease), Viruses, bacteria and fungi can all cause <strong>Pneumonia.</strong></p>",
                    solution_hi: "<p>6.(a) <strong>उच्च रक्तचाप </strong>- यह अस्वास्थ्यकर जीवनशैली अपनाने जैसे- पर्याप्त नियमित शारीरिक कार्य न करना, मधुमेह और मोटापे के कारण होता है। अन्य असंक्रामक रोग (असंचारी रोग) - हृदय रोग, मधुमेह, रोकथाम योग्य कैंसर, पुरानी श्वसन रोग, मानसिक स्वास्थ्य।<strong> संक्रामक रोग</strong> - <strong>टाइफाइड</strong> (जीवाणु रोग), <strong>इन्फ्लूएंजा</strong> (वायरल रोग), वायरस, जीवाणु और कवक सभी <strong>निमोनिया</strong> का कारण बन सकते हैं</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The female Anopheles mosquito is a transmitter of</p>",
                    question_hi: "<p>7. मादा एनोफिलीज मच्छर _______का संचारी है।</p>",
                    options_en: ["<p>cholera</p>", "<p>typhoid</p>", 
                                "<p>malaria</p>", "<p>dengue</p>"],
                    options_hi: ["<p>हैज़ा</p>", "<p>टाइफाइड</p>",
                                "<p>मलेरिया</p>", "<p>डेंगू</p>"],
                    solution_en: "<p>7.(c) <strong>Malaria. Cholera</strong> is an acute, diarrheal illness disease caused by the bacterium \'Vibrio Cholerae\'. <strong>Typhoid</strong> fever is a life-threatening illness caused by Salmonella Typhi bacteria. <strong>Dengue viruses </strong>are spread to people through the bite of an infected Aedes aegypti.</p>",
                    solution_hi: "<p>7.(c) <strong>मलेरिया।</strong> हैजा एक तीव्र, दस्त संबंधी बीमारी है जो \'विब्रियो कॉलरा\' जीवाणु के कारण होती है। <strong>टाइफाइड</strong> बुखार साल्मोनेला टाइफी जीवाणु के कारण होने वाली एक जानलेवा बीमारी है<strong>। डेंगू वायरस,</strong> संक्रमित एडीज एजिप्टी के काटने से लोगों में फैलता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Aedes mosquito is a carrier of:</p>",
                    question_hi: "<p>8. एडीज मच्छर__________ का वाहक है :</p>",
                    options_en: ["<p>malaria</p>", "<p>typhoid</p>", 
                                "<p>dengue</p>", "<p>cholera</p>"],
                    options_hi: ["<p>मलेरिया</p>", "<p>टाइफाइड</p>",
                                "<p>डेंगू</p>", "<p>हैज़ा</p>"],
                    solution_en: "<p>8.(c) <strong>Dengue. Malaria </strong>- Caused by protozoa of the genus Plasmodium, Carrier - Female Anopheles mosquito. <strong>Typhoid</strong> - Caused by Salmonella Typhi bacteria. <strong>Cholera</strong> - Acute diarrheal illness caused by infection of the intestine with Vibrio cholerae bacteria.</p>",
                    solution_hi: "<p>8.(c) <strong>डेंगू। मलेरिया(Malaria)</strong> - जीनस प्लास्मोडियम के प्रोटोजोआ के कारण होता है, वाहक - मादा एनोफिलीज मच्छर। <strong>टाइफाइड</strong> - साल्मोनेला टाइफी जीवाणु के कारण होता है। <strong>हैज़ा(Cholera</strong>)- विब्रियो कॉलेरी बैक्टीरिया से आंत के संक्रमण के कारण होने वाली तीव्र डायरिया संबंधी बीमारी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which Vitamin activates proteins and calcium essential for blood clotting?</p>",
                    question_hi: "<p>9. कौन सा विटामिन रक्त के थक्के के लिए आवश्यक प्रोटीन और कैल्शियम को सक्रिय करता है?</p>",
                    options_en: ["<p>Vitamin K</p>", "<p>Vitamin B1</p>", 
                                "<p>Vitamin D</p>", "<p>Vitamin C</p>"],
                    options_hi: ["<p>विटामिन K</p>", "<p>विटामिन B1</p>",
                                "<p>विटामिन D</p>", "<p>विटामिन C</p>"],
                    solution_en: "<p>9.(a)<strong> Vitamin K (Phylloquinone),</strong> Source - Green leafy vegetables such as kale, collard greens, broccoli, spinach, cabbage, and lettuce. <strong>Deficiency disease </strong>- Poor bone development, Osteoporosis, and increased risk of cardiovascular disease. <strong>Vitamin B1</strong> (Thiamine) Source - Meats, fish and whole grains. Deficiency disease - Beri Beri, <strong>Vitamin D</strong> (Calciferol) Source - Mushrooms, Cheese, Fish, Egg. Deficiency Disease - Rickets. <strong>Vitamin C</strong> (Ascorbic Acid) Source - Oranges, kiwi, lemon, grapefruit. Deficiency Disease - Scurvy.</p>",
                    solution_hi: "<p>9.(a) <strong>विटामिन K (फ़ाइलोक्विनोन),</strong> स्रोत - हरी पत्तेदार सब्जियाँ जैसे केल, कोलार्ड ग्रीन्स, ब्रोकोली, पालक, पत्तागोभी और सलाद।<strong>&nbsp;कमी से होने वाला रोग- </strong>हड्डियों का कमजोर होना, ऑस्टियोपोरोसिस और हृदय रोग का खतरा बढ़ जाता है। <strong>विटामिन B1</strong> (थायमिन), स्रोत - मांस, मछली और साबुत अनाज। कमी से होने वाले रोग - बेरी बेरी। <strong>विटामिन D </strong>(कैल्सीफेरोल), स्रोत - मशरूम, पनीर, मछली, अंडा। कमी से होने वाला रोग - रिकेट्स। <strong>विटामिन C</strong> (एस्कॉर्बिक अम्ल ), स्रोत - संतरा, कीवी, नींबू, अंगूर। कमी से होने वाला रोग - स्कर्वी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following unicellular organisms causes kala-azar?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सा एककोशिकीय जीव कालाजार का कारण बनता है?</p>",
                    options_en: ["<p>Leishmania</p>", "<p>Ascaris</p>", 
                                "<p>Liver fluke</p>", "<p>Tapeworm</p>"],
                    options_hi: ["<p>लीशमैनिया</p>", "<p>एस्केरिस</p>",
                                "<p>यकृतकृमि</p>", "<p>फ़ीता कृमि</p>"],
                    solution_en: "<p>10.(a) <strong>Leishmania:</strong> Caused by protozoan parasites, transmitted by the bite of infected female phlebotomine sandflies. <strong>Symptoms:</strong> Fever, weight loss, enlargement (swelling) of the spleen and liver, and abnormal blood tests. <strong>Liver fluke </strong>- phylum-Platyhelminthes. <strong>Tapeworm</strong> - Phylum-Platyhelminthes of Kingdom-Animalia. <strong>Ascaris</strong> - Genus of parasitic nematode worms known as the \"small intestinal roundworms,\" a type of parasitic worm.</p>",
                    solution_hi: "<p><strong>10.(a)लीशमैनिया:</strong> प्रोटोजोआ परजीवी के कारण होता है, जो संक्रमित मादा फ़्लेबोटोमाइन सैंडफ्लाइज़ के काटने से फैलता है। <strong>लक्षण</strong>: बुखार, वजन घटना, प्लीहा और यकृत का सूजन, और असामान्य रक्त परीक्षण। <strong>लीवर फ्लूक </strong>- फाइलम-प्लैटिहेल्मिन्थेस। <strong>फीताकृमि</strong> - किंगडम-एनिमलिया का फाइलम-प्लैटिहेल्मिन्थेस। <strong>एस्केरिस</strong> - परजीवी नेमाटोड कृमियों की प्रजाति जिसे \"छोटी आंत के राउंडवॉर्म\" के रूप में जाना जाता है, यह एक प्रकार का परजीवी कृमि।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which disease is caused by the deficiency of protein in our body?</p>",
                    question_hi: "<p>11. हमारे शरीर में प्रोटीन की कमी से कौन सा रोग होता है ?</p>",
                    options_en: ["<p>Kwashiorkor</p>", "<p>Scurvy</p>", 
                                "<p>Rickets</p>", "<p>Beri Beri</p>"],
                    options_hi: ["<p>क्वाशियोरकोर</p>", "<p>स्कर्वी</p>",
                                "<p>रिकेट्स</p>", "<p>बेरी-बेरी</p>"],
                    solution_en: "<p>11.(a)<strong> Kwashiorkor. Disease and deficiency:</strong> Scurvy (vitamin C). The most common cause of <strong>rickets</strong> ( vitamin D). Beriberi (vitamin B1). <strong>Disease caused by bacteria</strong> :- tuberculosis, pneumonia, typhoid, tetanus, etc. Diseases caused by virus:- AIDS, Common cold , Ebola, Genital herpes, Influenza, Measles, Chickenpox and shingles, Coronavirus disease 2019 (COVID-19).</p>",
                    solution_hi: "<p>11.(a) <strong>क्वाशियोरकोर। रोग और कमी: स्कर्वी (विटामिन C )। रिकेट्स</strong> का सबसे सामान्य कारण (विटामिन D )। बेरीबेरी (विटामिन B1)। <strong>बैक्टीरिया से होने वाले रोग:</strong>- तपेदिक, निमोनिया, टाइफाइड, टिटेनस , आदि। <strong>वायरस से होने वाले रोग:-</strong> एड्स, जुकाम , इबोला, जननांग दाद, इन्फ्लूएंजा, खसरा, चिकनपॉक्स और दाद, कोरोनावायरस रोग 2019 (कोविड-19)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following causes Kala-azar?</p>",
                    question_hi: "<p>12. निम्नलिखित में से किसके कारण काला-जार होता है</p>",
                    options_en: ["<p>Protozoa</p>", "<p>Virus</p>", 
                                "<p>Bacteria</p>", "<p>Fungi</p>"],
                    options_hi: ["<p>प्रोटोजोआ</p>", "<p>वायरस</p>",
                                "<p>बैक्टीरिया</p>", "<p>कवक</p>"],
                    solution_en: "<p>12.(a) <strong>Protozoa.</strong> Kala-Azar is a slow progressing indigenous disease caused by a protozoan parasite of the genus Leishmania. Diseases caused by: Protozoa - Malaria, Amoebiasis, giardiasis. Bacteria - Typhoid, Diarrhoea, Tuberculosis, Plague. Virus - Chickenpox, Smallpox, Common Cold.</p>",
                    solution_hi: "<p>12.(a) <strong>प्रोटोज़ोआ।</strong> काला-ज़ार एक धीमी गति से बढ़ने वाली एक देशी बीमारी है जो लीशमैनिया जीनस के प्रोटोजोआ परजीवी के कारण होती है। इनसे होने वाले रोग: प्रोटोजोआ - मलेरिया, अमीबियासिस, जिआर्डियासिस। जीवाणु - टाइफाइड, डायरिया, तपेदिक, प्लेग। विषाणु - चेचक, जुकाम ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which one of the following diseases is NOT caused by a virus?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सा रोग वायरस से नहीं होता है?</p>",
                    options_en: ["<p>Chicken Pox</p>", "<p>Measles</p>", 
                                "<p>Influenza</p>", "<p>Tuberculosis</p>"],
                    options_hi: ["<p>चिकन पॉक्स</p>", "<p>खसरा</p>",
                                "<p>इंफ्लुएंजा</p>", "<p>क्षयरोग</p>"],
                    solution_en: "<p>13.(d) <strong>Tuberculosis (TB)</strong> - It is a bacterium infectious disease that affects the lungs and is caused by Mycobacterium tuberculosis. <strong>Chickenpox </strong>- A highly contagious disease caused by the varicella-zoster virus. <strong>Measles </strong>- An airborne disease caused by Measles morbillivirus. <strong>Flu</strong> - A contagious respiratory illness caused by influenza virus that infect the nose, throat, and sometimes the lungs.</p>",
                    solution_hi: "<p>13.(d) <strong>क्षय रोग (TB) -</strong> यह एक जीवाणु संक्रामक रोग है जो फेफड़ों को प्रभावित करता है और माइकोबैक्टीरियम ट्यूबरकुलोसिस जीवाणु के कारण होता है। <strong>चिकनपॉक्स -</strong> वैरिसेला-ज़ोस्टर वायरस के कारण होने वाला एक अत्यधिक संक्रामक रोग है । <strong>खसरा (Measles )</strong> - यह मोर्बिलीवायरस के कारण होने वाला एक वायुजनित रोग है। <strong>फ़्लू </strong>- इन्फ्लूएंजा वायरस के कारण होने वाली एक संक्रामक श्वसन बीमारी जो नाक, गले और कभी-कभी फेफड़ों को संक्रमित करती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Non-clotting of blood occurs because of the deficiency of:</p>",
                    question_hi: "<p>14. रक्त का थक्का न जमना किसकी कमी के कारण होता है?</p>",
                    options_en: ["<p>Vitamin D</p>", "<p>Vitamin A</p>", 
                                "<p>Vitamin K</p>", "<p>Vitamin B</p>"],
                    options_hi: ["<p>विटामिन D</p>", "<p>विटामिन A</p>",
                                "<p>विटामिन K</p>", "<p>विटामिन B</p>"],
                    solution_en: "<p>14.(c)<strong> Vitamin K. Deficiency</strong> - Bleeding, Poor bone growth, Osteoporosis and Increased risk of heart disease. <strong>List of Deficiency Diseases -</strong> Vitamin A (Retinol - Night blindness), Vitamin B1 (Thiamine - Beri - beri), Vitamin C (Ascorbic acid - Scurvy), Vitamin D (Calciferol - Rickets).</p>",
                    solution_hi: "<p>14.(c) <strong>विटामिन K। </strong>कमी - रक्तस्राव, हड्डियों का कमजोर विकास, ऑस्टियोपोरोसिस और हृदय रोग का खतरा बढ़ जाना। <strong>कमी से होने वाले रोग</strong> - विटामिन A (रेटिनॉल - रतौंधी), विटामिन B1 (थायमिन - बेरी - बेरी), विटामिन C (एस्कॉर्बिक एसिड - स्कर्वी), विटामिन D (कैल्सीफेरोल - रिकेट्स)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Ringworm is a __________ type of disease.</p>",
                    question_hi: "<p>15. दाद एक __________ प्रकार की बीमारी है</p>",
                    options_en: ["<p>viral</p>", "<p>fungal</p>", 
                                "<p>bacterial</p>", "<p>prion</p>"],
                    options_hi: ["<p>विषाणु</p>", "<p>कवक</p>",
                                "<p>जीवाण्विक</p>", "<p>प्रिओन</p>"],
                    solution_en: "<p>15.(b)<strong> fungal. List diseases in humans </strong>- Bacterial disease (Cholera, Leprosy, Plague, Diphtheria). Fungal diseases (Aspergillus infection, Athlete\'s foot, Jock itch, Coccidioidomycosis, Sporotrichosis). Viruses disease (AIDS, Measles, COVID-19, Influenza)</p>",
                    solution_hi: "<p>15.(b) <strong>कवक। मनुष्यों में होने वाले रोगों की सूची</strong> - जीवाणुजनित रोग (हैजा, कुष्ठ रोग, प्लेग, डिप्थीरिया)। कवक रोग (एस्परगिलस संक्रमण, एथलीट फुट, जॉक खुजली, वैली फीवर (Coccidioidomycosis), स्पोरोट्रीकोसिस (Sporotrichosis))। वायरस रोग (एड्स, खसरा, कोविड-19, इन्फ्लुएंजा)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>