<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Times New Roman;\"> The circumference of the base of a right circular cone is 44cm and its height is 24cm. The curved surface area <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>i</mi><mi>n</mi><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></math></span><span style=\"font-family: Times New Roman;\">) of the cone is :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020</span></p>",
                    question_hi: "<p>1.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक गोलाकार </span><span style=\"font-family: Baloo;\">शंकु</span><span style=\"font-family: Baloo;\"> के आधार की परिधि 44 cm है और इसकी ऊंचाई 24 cm है। </span><span style=\"font-family: Baloo;\">शंकु</span><span style=\"font-family: Baloo;\"> की घुमावदार सतह क्षेत्र (</span><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math>में) है</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020</span></p>",
                    options_en: ["<p>572</p>", "<p>550</p>", 
                                "<p>528</p>", "<p>440</p>"],
                    options_hi: ["<p>572</p>", "<p>550</p>",
                                "<p>528</p>", "<p>440</p>"],
                    solution_en: "<p>1. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> Circumference = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;</mi><mi>r</mi></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>44</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mi>&#960;</mi><mi>r</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">r =7</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Curved surface area =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mi>r</mi><mi>l</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">l&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><msqrt><mo>(</mo><msup><mn>24</mn><mn>2</mn></msup><mo>+</mo><msup><mn>7</mn><mn>2</mn></msup><mo>)</mo></msqrt></math> </span><span style=\"font-family: Times New Roman;\">=25</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Curved surface area =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>25</mn><mo>&#160;</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 550</span></p>",
                    solution_hi: "<p>1. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">परिधि</span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;</mi><mi>r</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">44 =2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></span><span style=\"font-family: Times New Roman;\">r</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">r =7</span></p>\r\n<p><span style=\"font-family: Baloo;\">वक्र सतह का क्षेत्र</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mi>r</mi><mi>l</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>l</mi><mo>=</mo><mo>&#8730;</mo><mo>(</mo><msup><mn>24</mn><mn>2</mn></msup><mo>+</mo><msup><mn>7</mn><mn>2</mn></msup><mo>)</mo><mo>=</mo><mn>25</mn></math> </span></p>\r\n<p><span style=\"font-family: Baloo;\">वक्र सतह का क्षेत्र</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>25</mn><mo>&#160;</mo></math> </span><span style=\"font-family: Times New Roman;\">= 550</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Times New Roman;\"> A solid metallic cuboid of dimensions 18 cm <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> 36cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\"> 72cm is melted and recast into 8 cubes of the same volume. What is the ratio of the total surface area of the cuboid to the sum of the lateral surface area of all 8 cubes?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020</span></p>",
                    question_hi: "<p>2.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">18 सेमी </span><span style=\"font-family: Baloo;\">36 सेमी</span><span style=\"font-family: Baloo;\">72 सेमी आयाम वाले एक ठोस धात्विक घनाभ को </span><span style=\"font-family: Baloo;\">पिघला</span><span style=\"font-family: Baloo;\">कर समान </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> वाले 8 घन में पुनर्गठित किया जाता है। घनाभ के कुल सतह </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> से सभी 8 </span><span style=\"font-family: Baloo;\">घन</span><span style=\"font-family: Baloo;\"> के पार्श्व सतह </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> का अनुपात क्या है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020</span></p>",
                    options_en: ["<p>2 : 3</p>", "<p>7 : 8</p>", 
                                "<p>4 : 7</p>", "<p>7 : 12</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>7 : 8</p>",
                                "<p>4 : 7</p>", "<p>7 : 12</p>"],
                    solution_en: "<p>2. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of cuboid = 18 cm <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> 36cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\"> 72cm =46656</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of 1 cube =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>46656</mn><mrow><mo>(</mo><mn>8</mn><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>=</mo><mn>5832</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Side of cube </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>i</mi><mi>d</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>c</mi><mi>u</mi><mi>b</mi><mi>e</mi><mo>&#160;</mo><mo>=</mo><mo>&#8731;</mo><mn>5832</mn><mo>=</mo><mn>18</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Lateral surface area of 8 cubes = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>18</mn><mo>&#215;</mo><mn>18</mn><mo>=</mo><mn>10368</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total surface area of the cuboid = 2((18<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">36) +(36<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">72) +(72<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">18)) = 9072</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Required ratio = 10368 : 9072 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 7 : 8</span></p>",
                    solution_hi: "<p>2. (b)</p>\r\n<p><span style=\"font-family: Baloo;\"> घनाभ का आयतन </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 18 cm <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> 36cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\">72cm =46656</span></p>\r\n<p><span style=\"font-family: Baloo;\">घन का आयतन</span><span style=\"font-family: Times New Roman;\"> =</span><span style=\"font-family: Times New Roman;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>46656</mn><mrow><mo>(</mo><mn>8</mn><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>=</mo><mn>5832</mn></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">घन की भुजा</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#8731;</mo><mn>5832</mn><mo>=</mo><mn>18</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8 </span><span style=\"font-family: Baloo;\">घनों का पार्श्व पृष्ठीय क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>u</mi><mi>b</mi><mi>e</mi><mi>s</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>18</mn><mo>&#215;</mo><mn>18</mn><mo>=</mo><mn>10368</mn></math></p>\r\n<p><span style=\"font-family: Baloo;\">घनाभ का कुल पृष्ठीय क्षेत्रफल </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>(</mo><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>36</mn><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>(</mo><mn>36</mn><mo>&#215;</mo><mn>72</mn><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>(</mo><mn>72</mn><mo>&#215;</mo><mn>18</mn><mo>)</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>9072</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">आवश्यक अनुपात </span><span style=\"font-family: Times New Roman;\">= 10368 : 9072 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 7 : 8</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Times New Roman;\"> The base of a pyramid is an equilateral triangle of side is 10m. If the height of the pyramid is 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\"> m, then the volume of the pyramid is :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2319;&#2325; &#2346;&#2367;&#2352;&#2366;&#2350;&#2367;&#2337; &#2325;&#2366; &#2310;&#2343;&#2366;&#2352; 10 m &#2349;&#2369;&#2332;&#2366; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; &#2360;&#2350;&#2348;&#2366;&#2361;&#2369; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2346;&#2367;&#2352;&#2366;&#2350;&#2367;&#2337; &#2325;&#2368; &#2314;&#2306;&#2330;&#2366;&#2312; 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Baloo;\"> &#2350;&#2368;&#2335;&#2352; &#2361;&#2376;, &#2340;&#2379; &#2346;&#2367;&#2352;&#2366;&#2350;&#2367;&#2337; &#2325;&#2366; </span><span style=\"font-family: Baloo;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Baloo;\"> &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>\n",
                    options_en: ["<p>800</p>\n", "<p>900</p>\n", 
                                "<p>1000<span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>1200</p>\n"],
                    options_hi: ["<p>800</p>\n", "<p>900</p>\n",
                                "<p>1000<span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>1200</p>\n"],
                    solution_en: "<p>3. (c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Area of base = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&radic;</mo><mn>3</mn><msup><mi>a</mi><mn>2</mn></msup><mo>)</mo><mo>/</mo><mn>4</mn><mo>=</mo><mn>25</mn><mo>&radic;</mo><mn>3</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of pyramid </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math></span><span style=\"font-family: Times New Roman;\">Height</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math>Area of base</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of pyramid = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo></math></span><span style=\"font-family: Times New Roman;\">40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math>25<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> </span><span style=\"font-family: Times New Roman;\">=1000<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\n",
                    solution_hi: "<p>3. (c)</p>\r\n<p><span style=\"font-family: Baloo;\">&#2310;&#2343;&#2366;&#2352; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Times New Roman;\">=&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>&radic;</mo><mn>3</mn><msup><mi>a</mi><mn>2</mn></msup><mo>)</mo></mrow><mn>4</mn></mfrac><mo>=</mo><mn>25</mn><mo>&radic;</mo><mn>3</mn></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2346;&#2367;&#2352;&#2366;&#2350;&#2367;&#2337; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; </span><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math></span><span style=\"font-family: Baloo;\">&nbsp;</span><span style=\"font-family: Baloo;\">&#2310;&#2343;&#2366;&#2352; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math>&#2314;&#2306;&#2330;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2346;&#2367;&#2352;&#2366;&#2350;&#2367;&#2337; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; </span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math> </span><span style=\"font-family: Times New Roman;\">40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math>25<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\">=1000<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Times New Roman;\"> The curved surface area of a cylinder is five times the area of a base. Find the ratio of radius and height of the cylinder. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>4.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक बेलन का घुमावदार सतह </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> उसके आधार के </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> का पांच गुना है। बेलन के त्रिज्या और ऊंचाई के अनुपात का पता </span><span style=\"font-family: Baloo;\">लगाएं</span><span style=\"font-family: Baloo;\">।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>2 : 5</p>", "<p>2 : 3</p>", 
                                "<p>3 : 4</p>", "<p>3 : 5</p>"],
                    options_hi: ["<p>2 : 5</p>", "<p>2 : 3</p>",
                                "<p>3 : 4</p>", "<p>3 : 5</p>"],
                    solution_en: "<p>4. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;</mi><mi>r</mi><mi>h</mi><mo>=</mo><mn>5</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mi>r</mi><mo>/</mo><mi>h</mi><mo>=</mo><mn>2</mn><mo>/</mo><mn>5</mn></math></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>4. (a)</p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;</mi><mi>r</mi><mi>h</mi><mo>=</mo><mn>5</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mspace linebreak=\"newline\"/><mi>r</mi><mo>/</mo><mi>h</mi><mo>=</mo><mn>2</mn><mo>/</mo><mn>5</mn></math></p>\r\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Times New Roman;\"> The volume of a hemisphere is 2425<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">. Find its radius. (take <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi>&#960;</mi><mo>=</mo><mn>22</mn><mo>/</mo><mn>7</mn><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक गोलार्ध का </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Times New Roman;\"> 2425<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span><span style=\"font-family: Baloo;\"> है तो उसकी त्रिज्या ज्ञात करे ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>t</mi><mi>a</mi><mi>k</mi><mi>e</mi><mo>&#160;</mo><mi>&#960;</mi><mo>=</mo><mn>22</mn><mo>/</mo><mn>7</mn><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>10 cm</p>", "<p>9.5 cm</p>", 
                                "<p>12 cm</p>", "<p>10.5cm</p>"],
                    options_hi: ["<p>10 cm</p>", "<p>9.5 cm</p>",
                                "<p>12 cm</p>", "<p>10.5cm</p>"],
                    solution_en: "<p>5. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of hemisphere&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>2</mn><mo>/</mo><mn>3</mn><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>/</mo><mn>3</mn><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup><mo>=</mo><mn>24251</mn><mo>/</mo><mn>2</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">r =10.5 cm</span></p>",
                    solution_hi: "<p>5. (d)</p>\r\n<p><span style=\"font-family: Baloo;\">गोलार्द्ध का आयतन</span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>2</mn><mo>/</mo><mn>3</mn><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>/</mo><mn>3</mn><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup><mo>=</mo><mn>24251</mn><mo>/</mo><mn>2</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">r=10.5 cm</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Times New Roman;\"> If the radius of a cylinder is decreased by 20% and the height is increased by 20% to form a new cylinder then the volume will be decreased by:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>6.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि किसी बेलन की त्रिज्या 20% तक कम हो जाती है और उसकी ऊँचाई 20% तक बढ़ जाती है तो नए सिलिंडर की </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> में कितनी कमी होगी</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>23.2%</p>", "<p>32.2%</p>", 
                                "<p>22.3%</p>", "<p>20.5%</p>"],
                    options_hi: ["<p>23.2%</p>", "<p>32.2%</p>",
                                "<p>22.3%</p>", "<p>20.5%</p>"],
                    solution_en: "<p>6. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Initial radius = 5r Initial height =5h</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Final radius = 4r Final height = 6h</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Initial volume = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>125</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Final volume = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>96</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Decrease in volume = 29</span><span style=\"font-family: Times New Roman;\">h</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Percentage decrease = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>29</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>125</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mo>)</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math></span><span style=\"font-family: Times New Roman;\">= 23.2%</span></p>",
                    solution_hi: "<p>6. (a)</p>\r\n<p><span style=\"font-family: Baloo;\">प्रारंभिक त्रिज्या </span><span style=\"font-family: Times New Roman;\">= 5r, </span><span style=\"font-family: Baloo;\">प्रारंभिक ऊंचाई </span><span style=\"font-family: Times New Roman;\">=5h</span></p>\r\n<p><span style=\"font-family: Baloo;\">अंतिम त्रिज्या</span><span style=\"font-family: Times New Roman;\"> = 4r, </span><span style=\"font-family: Baloo;\">अंतिम ऊँचाई </span><span style=\"font-family: Times New Roman;\">= 6h</span></p>\r\n<p><span style=\"font-family: Baloo;\">प्रारंभिक आयतन </span><span style=\"font-family: Times New Roman;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>125</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></p>\r\n<p><span style=\"font-family: Baloo;\">अंतिम आयतन </span><span style=\"font-family: Times New Roman;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>96</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></p>\r\n<p><span style=\"font-family: Baloo;\">आयतन में कमी </span><span style=\"font-family: Times New Roman;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>29</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></p>\r\n<p><span style=\"font-family: Baloo;\">प्रतिशत में कमी </span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>29</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>125</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mo>)</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 23.2%</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Times New Roman;\"> The ratio of the height and the diameter of a right circular cone is 6:5 and its volume is&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2200</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math> </span><span style=\"font-family: Times New Roman;\">.What is its slant height? (Take </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>(</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&pi;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>22</mn><mo>&nbsp;</mo><mo>/</mo><mo>&nbsp;</mo><mn>7</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2319;&#2325; &#2327;&#2379;&#2354;&#2366;&#2325;&#2366;&#2352; </span><span style=\"font-family: Baloo;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Baloo;\"> &#2325;&#2366; &#2314;&#2306;&#2330;&#2366;&#2312; &#2324;&#2352; </span><span style=\"font-family: Baloo;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Baloo;\"> &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; 6: 5 &#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2325;&#2368; </span><span style=\"font-family: Baloo;\">&#2310;&#2351;&#2340;&#2344;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2200</mn><mn>7</mn></mfrac><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2361;&#2376;&#2404; &#2340;&#2379; &#2311;&#2360;&#2325;&#2368; &#2340;&#2367;&#2352;&#2381;&#2351;&#2325; &#2314;&#2306;&#2330;&#2366;&#2312; &#2325;&#2367;&#2340;&#2344;&#2368; &#2361;&#2376;?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>\n",
                    options_en: ["<p>25cm<span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>26cm</p>\n", 
                                "<p>13cm</p>\n", "<p>5cm</p>\n"],
                    options_hi: ["<p>25cm<span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>26cm</p>\n",
                                "<p>13cm</p>\n", "<p>5cm</p>\n"],
                    solution_en: "<p>7. (c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">h : d = 6:5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h : 2r = 6:5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h : r = 12:5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h =12k, r = 5k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>1</mn><mo>/</mo><mn>3</mn><mo>)</mo><mo>&times;</mo><mi>&pi;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>1</mn><mo>/</mo><mn>3</mn><mo>)</mo><mo>&times;</mo><mi>&pi;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mo>=</mo><mn>2200</mn><mo>/</mo><mn>7</mn></math></p>\r\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&times;</mo><mn>25</mn><mo>&nbsp;</mo><msup><mi>k</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>&times;</mo><mn>12</mn><mo>&nbsp;</mo><mi>k</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>2200</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi>k</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>h</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>r</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi>s</mi><mi>l</mi><mi>a</mi><mi>n</mi><mi>t</mi><mo>&nbsp;</mo><mi>h</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mo>&nbsp;</mo><mo>(</mo><mi>l</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>13</mn><mi>c</mi><mi>m</mi><mspace linebreak=\"newline\"></mspace></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>7. (c)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> h:d=6:5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h:2r=6:5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h:r=12:5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h=12k ,r=5k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>1</mn><mo>/</mo><mn>3</mn><mo>)</mo><mo>&times;</mo><mi>&pi;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>1</mn><mo>/</mo><mn>3</mn><mo>)</mo><mo>&times;</mo><mi>&pi;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi><mo>=</mo><mn>2200</mn><mo>/</mo><mn>7</mn></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mn>25</mn><mo>&nbsp;</mo><msup><mi>k</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mi>k</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>2200</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi>k</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi>h</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>r</mi><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi>l</mi><mo>&nbsp;</mo><mo>=</mo><mn>13</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Times New Roman;\"> The radii of two cylinders are in the ratio 3:4 and their heights are in the ratio 8:5. The ratio of their volumes is .</span><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>8.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">दो बेलनों की त्रिज्या 3: 4 के अनुपात में है और </span><span style=\"font-family: Baloo;\">उनकी</span><span style=\"font-family: Baloo;\"> ऊँचाई 8: 5 के अनुपात में है। उनके </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> का अनुपात है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>8:9</p>", "<p>9:10</p>", 
                                "<p>7:10</p>", "<p>9:11</p>"],
                    options_hi: ["<p>8:9</p>", "<p>9:10</p>",
                                "<p>7:10</p>", "<p>9:11</p>"],
                    solution_en: "<p>8. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>1</mn></msub><mo>=</mo></math>3k,&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>2</mn></msub><mo>=</mo></math></span><span style=\"font-family: Times New Roman;\">4k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>1</mn></msub><mo>=</mo><mn>8</mn><mi>a</mi></math>,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>2</mn></msub></math></span><span style=\"font-family: Times New Roman;\">=5k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of 1st cylinder=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>&#215;</mo><msubsup><mi>r</mi><mn>1</mn><mn>2</mn></msubsup><msub><mi>h</mi><mn>1</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>72</mn><mi>&#960;</mi><msup><mi>k</mi><mn>3</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of 2st cylinder=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>&#215;</mo><msubsup><mi>r</mi><mn>2</mn><mn>2</mn></msubsup><msub><mi>h</mi><mn>2</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>80</mn><mi>&#960;</mi><msup><mi>k</mi><mn>3</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Ratio = 9:10</span></p>",
                    solution_hi: "<p>8. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>1</mn></msub></math>=3k,&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>2</mn></msub><mo>=</mo></math></span><span style=\"font-family: Times New Roman;\">4k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>1</mn></msub><mo>=</mo></math>8a&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>2</mn></msub><mo>=</mo><mn>5</mn><mi>k</mi></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">पहले बेलन का आयतन</span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>&#215;</mo><msubsup><mi>r</mi><mn>1</mn><mn>2</mn></msubsup><msub><mi>h</mi><mn>1</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">पहले बेलन का आयतन</span><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>72</mn><mi>&#960;</mi><msup><mi>k</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>V</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>2</mn><mi>s</mi><mi>t</mi><mo>&#160;</mo><mi>c</mi><mi>y</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>d</mi><mi>e</mi><mi>r</mi><mo>=</mo><mo>&#160;</mo><mi>&#960;</mi><mo>&#215;</mo><msubsup><mi>r</mi><mn>2</mn><mn>2</mn></msubsup><msub><mi>h</mi><mn>2</mn></msub><mo>=</mo><mn>80</mn><mi>&#960;</mi><msup><mi>k</mi><mn>3</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Baloo;\">अनुपात </span><span style=\"font-family: Times New Roman;\"> = 9:10</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\"> The number of lead balls, each 3cm in diameter, that can be made from a solid lead sphere of diameter 42 cm is </span><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>9.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">प्रत्येक 3 cm व्यास वाले लेड गेंदों की संख्या क्या होगी जिसे 42 cm व्यास वाले ठोस लेड गोले से बनाया जा सकता है ?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>2742</p>", "<p>2744</p>", 
                                "<p>4722</p>", "<p>7244</p>"],
                    options_hi: ["<p>2742</p>", "<p>2744</p>",
                                "<p>4722</p>", "<p>7244</p>"],
                    solution_en: "<p>9. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of solid lead sphere <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><msup><mn>21</mn><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of lead balls=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup><mo>=</mo><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><mn>1</mn><mo>.</mo><msup><mn>5</mn><mn>3</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of lead balls =Volume of solid lead sphere/Volume of lead balls</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of lead balls =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><msup><mn>21</mn><mn>3</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><mn>1</mn><mo>.</mo><msup><mn>5</mn><mn>3</mn></msup><mo>)</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 2744</span></p>",
                    solution_hi: "<p>9. (b)</p>\r\n<p><span style=\"font-family: Baloo;\">सीसे के ठोस गोले का आयतन</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><msup><mn>21</mn><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">सीसे की गेंदों का आयतन</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup><mo>=</mo><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><mn>1</mn><mo>.</mo><msup><mn>5</mn><mn>3</mn></msup></math></p>\r\n<p><span style=\"font-family: Baloo;\">सीसे की गेंदों की संख्या = ठोस सीसे के गोले का आयतन/सीसा गेंदों का आयतन</span></p>\r\n<p><span style=\"font-family: Baloo;\">सीसे गेंदों की संख्या </span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><msup><mn>21</mn><mn>3</mn></msup><mo>)</mo><mo>/</mo><mo>(</mo><mn>4</mn><mo>/</mo><mn>3</mn><mo>)</mo><mi>&#960;</mi><mn>1</mn><mo>.</mo><msup><mn>5</mn><mn>3</mn></msup><mo>)</mo></math> </span><span style=\"font-family: Times New Roman;\">=2744</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Times New Roman;\"> The base of a right prism is a square having side of 15 cm. If its height is 8 cm, then find the total surface area. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>10.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक प्रिज्म का आधार एक वर्ग होता है, जो 15 सेमी की </span><span style=\"font-family: Baloo;\">भुजा</span><span style=\"font-family: Baloo;\"> का है। यदि इसकी ऊंचाई 8 सेमी है, तो कुल सतह के क्षेत्र को ज्ञात करे </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>920</p>", "<p>930&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", 
                                "<p>900&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", "<p>940<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>"],
                    options_hi: ["<p>920<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", "<p>930<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>",
                                "<p>900<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", "<p>940<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>"],
                    solution_en: "<p>10. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total surface area of prism = </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">Area of base + Curved surface area</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area of base=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>15</mn><mn>2</mn></msup></math> </span><span style=\"font-family: Times New Roman;\">=225 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Curved surface area= Perimeter of base<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\"> height= 4 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\">8=480</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total surface area of prism </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">225 + 480 = 930<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> </span></p>",
                    solution_hi: "<p>10. (b)</p>\r\n<p><span style=\"font-family: Baloo;\">प्रिज्म का कुल पृष्ठीय क्षेत्रफल = 2 &times;आधार का क्षेत्रफल +वक्र पृष्ठीय क्षेत्रफल</span></p>\r\n<p><span style=\"font-family: Baloo;\">आधार का क्षेत्रफल </span><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>15</mn><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">=225 </span></p>\r\n<p><span style=\"font-family: Baloo;\">वक्र पृष्ठीय क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Baloo;\">आधार का परिमाप &times;ऊँचाई</span><span style=\"font-family: Times New Roman;\"> = 4 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\">8=480</span></p>\r\n<p><span style=\"font-family: Baloo;\">प्रिज्म का कुल पृष्ठीय क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>2</mn><mo>&#215;</mo><mn>225</mn><mo>+</mo><mn>480</mn><mo>=</mo><mn>930</mn><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Times New Roman;\"> If the perimeter of an isosceles right triangle is </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>8</mn><mo>(</mo><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mi>c</mi><mi>m</mi><mo>,</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\">&nbsp;then the length of the hypotenuse of the triangle is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>11.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि समद्विबाहु समकोण त्रिभुज की परिधि </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>(</mo><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mi>c</mi><mi>m</mi></math><span style=\"font-family: Baloo;\">सेमी है, तो त्रिभुज के </span><span style=\"font-family: Baloo;\">विकर्ण</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">की लंबाई है</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>10cm</p>", "<p>8cm</p>", 
                                "<p>24cm</p>", "<p>12cm</p>"],
                    options_hi: ["<p>10cm</p>", "<p>8cm</p>",
                                "<p>24cm</p>", "<p>12cm</p>"],
                    solution_en: "<p>11. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the length of equal sides (Perpendicular and Base)=x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Hypotenuse=</span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8730;</mo><mn>2</mn><mo>&#160;</mo><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Perimeter = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mi>x</mi><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>(</mo><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mi>x</mi><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">On solving x <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#8730;</mo><mn>2</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Hypotenuse =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8730;</mo><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn><mo>&#8730;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math></p>",
                    solution_hi: "<p>11. (b)</p>\r\n<p><span style=\"font-family: Baloo;\">माना समान भुजाओं की लंबाई (लंब और आधार)</span><span style=\"font-family: Times New Roman;\">=x</span></p>\r\n<p><span style=\"font-family: Baloo;\">कर्ण</span><span style=\"font-family: Times New Roman;\">=</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8730;</mo><mn>2</mn><mo>&#160;</mo><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">परिमाप </span><span style=\"font-family: Times New Roman;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mi>x</mi><mo>&#160;</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>(</mo><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mi>x</mi><mo>&#160;</mo></math></p>\r\n<p><span style=\"font-family: Baloo;\">हल करने पर,</span><span style=\"font-family: Times New Roman;\"> x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>&#8730;</mo><mn>2</mn></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">कर्ण</span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>&#8730;</mo><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn><mo>&#8730;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Times New Roman;\"> The sum of length, breadth and height of a cuboid is 20 cm. If the length of the diagonal is 12cm, then find the total surface area of the cuboid.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>12.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक घनाभ की लंबाई, चौड़ाई और ऊंचाई का </span><span style=\"font-family: Baloo;\">योग</span><span style=\"font-family: Baloo;\"> 20 सेमी है। यदि </span><span style=\"font-family: Baloo;\">विकर्ण</span><span style=\"font-family: Baloo;\"> की लंबाई 12 सेमी है, तो घनाभ का कुल सतह </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> ज्ञात करें | </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>264&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", "<p>364<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", 
                                "<p>356<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>256<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>"],
                    options_hi: ["<p>264<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", "<p>364<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>",
                                "<p>356<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>256<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>"],
                    solution_en: "<p>12. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>l</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>h</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>20</mn></math></span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8730;</mo><mo>(</mo><msup><mi>l</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup><mo>)</mo><mo>=</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><msup><mi>l</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup><mo>=</mo><mn>144</mn><mspace linebreak=\"newline\"></mspace><mo>(</mo><mi>l</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>h</mi><msup><mo>)</mo><mn>2</mn></msup><mo>=</mo><msup><mi>l</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>h</mi><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mn>400</mn><mo>-</mo><mn>144</mn><mo>=</mo><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>h</mi><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mn>256</mn><mo>=</mo><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>h</mi><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo></math></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>12. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>l</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>h</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>20</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8730;</mo><mo>(</mo><msup><mi>l</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup><mo>)</mo><mo>=</mo><mn>12</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>l</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup><mo>=</mo><mn>144</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>l</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>h</mi><msup><mo>)</mo><mn>2</mn></msup><mo>=</mo><msup><mi>l</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>h</mi><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">400- 144 = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mi>h</mi><mo>&#160;</mo><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">256 =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mi>h</mi><mo>&#160;</mo><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> If the surface area of a sphere is 1386<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> </span><span style=\"font-family: Times New Roman;\">, then its volume is: </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>(</mo><mi>&#960;</mi><mo>=</mo><mn>22</mn><mo>/</mo><mn>7</mn><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>13.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि एक गोले का सतह क्षेत्र 1386<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> </span><span style=\"font-family: Baloo;\"> है, तो इसका </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> है:</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>&#960;</mi><mo>=</mo><mn>22</mn><mo>/</mo><mn>7</mn><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>8451<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>", "<p>5418<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>", 
                                "<p>4581<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>", "<p>4851<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>"],
                    options_hi: ["<p>8451<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>", "<p>5418<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>",
                                "<p>4581<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>", "<p>4851<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>"],
                    solution_en: "<p>13. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> Surface area = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>=</mo><mn>1386</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R = 10.5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>/</mo><mn>3</mn><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></math> </span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>&#160;</mo></math></span><span style=\"font-family: Times New Roman;\"> =&nbsp;4851 &nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>",
                    solution_hi: "<p>13. (d)</p>\r\n<p><span style=\"font-family: Baloo;\">सतह का क्षेत्रफल </span><span style=\"font-family: Times New Roman;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>=</mo><mn>1386</mn></math><span style=\"font-family: Times New Roman;\"> &nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R = 10.5</span></p>\r\n<p><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup><mo>=</mo></math></span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>=</mo><mn>4851</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Times New Roman;\"> In a triangle ABC, AB = AC and the perimeter of </span><span style=\"font-family: Times New Roman;\">ABC is </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\">cm . If the length of BC is<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8730;</mo><mn>2</mn><mo>&#160;</mo></math> </span><span style=\"font-family: Times New Roman;\">times the length of AB, then find the area of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;</mi></math></span><span style=\"font-family: Times New Roman;\">ABC.</span></p>\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    question_hi: "<p>14.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक त्रिभुज ABC में AB = AC और </span><span style=\"font-family: Baloo;\">ABC की परिधि</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo></math><span style=\"font-family: Baloo;\"> सेमी है। यदि BC की लंबाई AB की लंबाई से </span><span style=\"font-family: Baloo;\"> गुना है, तो </span><span style=\"font-family: Baloo;\">ABC का क्षेत्रफल ज्ञात कीजिए|</span></p>\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020</span></p>",
                    options_en: ["<p>28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", "<p>36<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", 
                                "<p>32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>"],
                    options_hi: ["<p>28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>", "<p>36<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>",
                                "<p>32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>"],
                    solution_en: "<p>14. (c)</p>\n<p><img src=\"data:image/png;base64,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\" width=\"218\" height=\"162\"></p>\n<p><span style=\"font-family: Times New Roman;\">Perimeter = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mi>x</mi><mo>=</mo><mn>8</mn><mo>(</mo><mn>2</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mi>x</mi><mo>=</mo><mn>8</mn><mspace linebreak=\"newline\"></mspace><mi>a</mi><mi>l</mi><mi>s</mi><mi>o</mi><mo>&#160;</mo><mi>s</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>s</mi><mi>f</mi><mi>y</mi><mo>&#160;</mo><mi>p</mi><mi>y</mi><mi>t</mi><mi>h</mi><mi>a</mi><mi>g</mi><mi>o</mi><mi>r</mi><mi>a</mi><mi>s</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mi>o</mi><mi>r</mi><mi>e</mi><mi>m</mi><mo>&#160;</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mo>=</mo><mn>1</mn><mo>/</mo><mn>2</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>8</mn><mo>=</mo><mn>32</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n<p><span style=\"font-family: Times New Roman;\">&nbsp;</span></p>",
                    solution_hi: "<p>14. (c)</p>\n<p><img src=\"data:image/png;base64,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\" width=\"218\" height=\"162\"></p>\n<p><span style=\"font-family: Baloo;\">परिमाप</span><span style=\"font-family: Times New Roman;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mi>x</mi><mo>=</mo><mn>8</mn><mo>(</mo><mn>2</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>)</mo><mspace linebreak=\"newline\"/><mi>x</mi><mo>=</mo><mn>8</mn><mo>&#160;</mo><mi>a</mi><mi>l</mi><mi>s</mi><mi>o</mi><mo>&#160;</mo><mi>s</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>s</mi><mi>f</mi><mi>y</mi><mo>&#160;</mo><mi>p</mi><mi>y</mi><mi>t</mi><mi>h</mi><mi>a</mi><mi>g</mi><mi>o</mi><mi>r</mi><mi>a</mi><mi>s</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mi>o</mi><mi>r</mi><mi>e</mi><mi>m</mi><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mo>=</mo><mn>1</mn><mo>/</mo><mn>2</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>8</mn><mo>=</mo><mn>32</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n<p><span style=\"font-family: Times New Roman;\">&nbsp;</span></p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> A right circular cylinder of maximum volume is cut out from a solid wooden cube. The material left is what percent of the volume (nearest to an integer) of the original cube ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक ठोस लकड़ी के </span><span style=\"font-family: Baloo;\">घन</span><span style=\"font-family: Baloo;\"> से अधिकतम </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> वाला एक </span><span style=\"font-family: Baloo;\">लम्ब</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">वृत्तीय</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">बेलन</span><span style=\"font-family: Baloo;\"> काटा जाता है | बची हुई सामग्री आरंभिक </span><span style=\"font-family: Baloo;\">घन</span><span style=\"font-family: Baloo;\"> के </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> ( एक पूर्णांक के निकटतम ) का कितना प्रतिशत है ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>19</p>", "<p>28</p>", 
                                "<p>23</p>", "<p>21</p>"],
                    options_hi: ["<p>19</p>", "<p>28</p>",
                                "<p>23</p>", "<p>21</p>"],
                    solution_en: "<p>15. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the side of cube = a </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of cube =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup></math>&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Now, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Radius of the cylinder =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>a</mi></mrow><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Height of the cylinder = a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the cylinder =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&#960;</mi><msup><mrow><mo>(</mo><mi>a</mi><mo>/</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mi>a</mi><mo>=</mo><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>4</mn></mrow></mfrac><msup><mi>a</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required %age =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><mi>&#960;</mi><mo>/</mo><mn>4</mn><msup><mi>a</mi><mn>3</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>)</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>(</mo><mn>1</mn><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>22</mn><mrow><mo>(</mo><mn>7</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo></mrow></mfrac></mstyle><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>)</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>6</mn><mn>28</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>&#8776;</mo><mn>21</mn></math></span></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>15. (d)</p>\r\n<p><span style=\"font-family: Baloo;\">माना घन की भुजा</span><span style=\"font-family: Times New Roman;\"> = a </span></p>\r\n<p><span style=\"font-family: Baloo;\">घन का आयतन</span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">बेलन की त्रिज्या</span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>/</mo><mn>2</mn></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\">बेलन की ऊंचाई</span><span style=\"font-family: Times New Roman;\"> = a</span></p>\r\n<p><span style=\"font-family: Baloo;\">बेलन का आयतन</span><span style=\"font-family: Times New Roman;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&#960;</mi><msup><mrow><mo>(</mo><mfrac><mi>a</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mi>a</mi><mo>=</mo><mfrac><mi>&#960;</mi><mn>4</mn></mfrac><msup><mi>a</mi><mn>3</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक प्रतिशत</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><mi>&#960;</mi><mo>/</mo><mn>4</mn><msup><mi>a</mi><mn>3</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>)</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>(</mo><mn>1</mn><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>22</mn><mrow><mo>(</mo><mn>7</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo></mrow></mfrac></mstyle><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>)</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>6</mn><mn>28</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>&#8776;</mo><mn>21</mn></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.<span style=\"font-family: Times New Roman;\"> The base of right prism is a trapezium whose parallel sides are 11cm and 15cm and the distance between them is 9 cm. If the volume of the prism is 1731.6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math> </span><span style=\"font-family: Times New Roman;\">, then the height (in cm) of the prism will be : </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>16.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक </span><span style=\"font-family: Baloo;\">लम्ब</span><span style=\"font-family: Baloo;\"> प्रिज्म का आधार समलंब है जिसकी </span><span style=\"font-family: Baloo;\">समानांतर</span><span style=\"font-family: Baloo;\"> भुजाएं 11 सेमी और 15 सेमी हैं तथा उनके बीच की दूरी 9 सेमी है | यदि प्रिज्म का </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Times New Roman;\"> 1731.6 </span><span style=\"font-family: Baloo;\">घन</span><span style=\"font-family: Baloo;\"> सेमी है, तो प्रिज्म की ऊंचाई होगी : </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>15.6</p>", "<p>15.2</p>", 
                                "<p>14.8</p>", "<p>14.2</p>"],
                    options_hi: ["<p>15.6</p>", "<p>15.2</p>",
                                "<p>14.8</p>", "<p>14.2</p>"],
                    solution_en: "<p>16. (c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of prism = base area <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">height</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1731.6 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\">(15+11)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">9 </span><span style=\"font-family: Times New Roman;\">height</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">height = 14.8</span></p>",
                    solution_hi: "<p>16. (c)</p>\r\n<p><span style=\"font-family: Baloo;\">प्रिज्म का आयतन = आधार क्षेत्रफल &times;ऊँचाई</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1731.6 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mo>(</mo></math></span><span style=\"font-family: Times New Roman;\">(15+11)</span><span style=\"font-family: Times New Roman;\">9 </span><span style=\"font-family: Baloo;\">&times;ऊँचाई</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">ऊँचाई</span><span style=\"font-family: Times New Roman;\"> = 14.8</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17.<span style=\"font-family: Times New Roman;\"> If the radius of a sphere is increased by 4 cm, its surface area is increased by <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>464</mn><mi>&#960;</mi><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup><mo>.</mo></math></span><span style=\"font-family: Times New Roman;\">What is the volume (in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>i</mi><mi>n</mi><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup><mo>)</mo></math></span><span style=\"font-family: Times New Roman;\">) of the original sphere ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>17.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि किसी गोले की त्रिज्या 4 सेमी से बढ़ा दी जाए, तो इसका पृष्ठ </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> 464<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup><mo>.</mo></math></span><span style=\"font-family: Baloo;\"> वर्ग सेमी से बढ़ जाता है। आरंभिक गोले का </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Times New Roman;\"> ( </span><span style=\"font-family: Baloo;\">घन</span><span style=\"font-family: Baloo;\"> सेमी में ) क्या था ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15625</mn><mn>6</mn></mfrac><mo>&#160;</mo><mi>&#960;</mi><mo> </mo></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35937</mn><mn>8</mn></mfrac><mo>&#160;</mo><mi>&#960;</mi><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11979</mn><mrow><mn>2</mn><mo>&#160;</mo></mrow></mfrac><mi>&#960;</mi><mo> </mo></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15625</mn><mn>8</mn></mfrac><mo>&#160;</mo><mi>&#960;</mi></math><span style=\"font-family: Times New Roman;\"> </span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15625</mn><mrow><mn>6</mn><mo>&#160;</mo></mrow></mfrac><mi>&#960;</mi><mo> </mo></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35937</mn><mn>8</mn></mfrac><mo>&#160;</mo><mi>&#960;</mi><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11979</mn><mn>2</mn></mfrac><mo>&#160;</mo><mi>&#960;</mi><mo> </mo></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15625</mn><mrow><mn>8</mn><mo>&#160;</mo></mrow></mfrac><mi>&#960;</mi></math><span style=\"font-family: Times New Roman;\"> </span></p>"],
                    solution_en: "<p>17. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> Let the original radius be r.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">According to the question</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi>&#960;</mi><msup><mrow><mo>(</mo><mi>r</mi><mo>+</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><mn>464</mn><mi>&#960;</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><mn>16</mn><mo>+</mo><mn>8</mn><mi>r</mi><mo>=</mo><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><mn>116</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">r = 12.5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the sphere </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mi>&#960;</mi><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>=</mo><mfrac><mn>15625</mn><mn>6</mn></mfrac><mi>&#960;</mi><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>17. (a)</p>\r\n<p><span style=\"font-family: Baloo;\">माना त्रिज्या = r </span></p>\r\n<p><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi>&#960;</mi><msup><mrow><mo>(</mo><mi>r</mi><mo>+</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><mn>464</mn><mi>&#960;</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><mn>16</mn><mo>+</mo><mn>8</mn><mi>r</mi><mo>=</mo><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><mn>116</mn><mspace linebreak=\"newline\"></mspace><mi>r</mi><mo>=</mo><mn>12</mn><mo>.</mo><mn>5</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Baloo;\">गोले का आयतन</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>V</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mi>o</mi><mi>f</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mo>=</mo><mn>4</mn><mo>/</mo><mn>3</mn><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mi>&#960;</mi><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>=</mo><mfrac><mn>15625</mn><mrow><mn>6</mn><mi>&#960;</mi></mrow></mfrac><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18.<span style=\"font-family: Times New Roman;\"> The ratio of the volumes of two cylinders is x:y and the ratio of their diameters is a:b. What is the ratio of their heights?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2342;&#2379; &#2348;&#2375;&#2354;&#2344;&#2379;&#2306; &#2325;&#2375; </span><span style=\"font-family: Baloo;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Baloo;\"> &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; x: y &#2361;&#2376; &#2324;&#2352; &#2313;&#2344;&#2325;&#2375; &#2357;&#2381;&#2351;&#2366;&#2360; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; a: b &#2361;&#2376; | &#2313;&#2344;&#2325;&#2368; &#2314;&#2306;&#2330;&#2366;&#2312; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2352;&#2375;&#2306; | </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>\n",
                    options_en: ["<p>xb:ya</p>\n", "<p>xa:yb</p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><msup><mi>b</mi><mn>2</mn></msup><mo>:</mo><mi>y</mi><msup><mi>a</mi><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><msup><mi>a</mi><mn>2</mn></msup><mo>:</mo><mi>y</mi><msup><mi>b</mi><mn>2</mn></msup></math></p>\n"],
                    options_hi: ["<p>xb:ya</p>\n", "<p>xa:yb</p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><msup><mi>b</mi><mn>2</mn></msup><mo>:</mo><mi>y</mi><msup><mi>a</mi><mn>2</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>x<span style=\"font-family: Times New Roman;\">:y</span></p>\n"],
                    solution_en: "<p>18. (c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the heights of the cylinders are </span><span style=\"font-family: Times New Roman;\">.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>1</mn></msub><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><msub><mi>h</mi><mn>2</mn></msub><mo>.</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the first cylinder&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&pi;</mi><msup><mi>r</mi><mn>2</mn></msup><msub><mi>h</mi><mn>1</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&#120587;</mo><msup><mrow><mo>(</mo><mfrac><mo>&#119886;</mo><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><msub><mi>h</mi><mn>1</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>1</mn></msub><mo>=</mo><mfrac><mrow><mn>4</mn><mi>x</mi></mrow><mrow><mi>&pi;</mi><msup><mi>a</mi><mn>2</mn></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Similarly</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>2</mn></msub><mo>=</mo><mfrac><mrow><mn>4</mn><mi>y</mi></mrow><mrow><mi>&pi;</mi><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required ratio =</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">:<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>x</mi></mrow><mrow><mi>&pi;</mi><msup><mi>a</mi><mn>2</mn></msup></mrow></mfrac><mo>:</mo><mfrac><mrow><mn>4</mn><mi>y</mi></mrow><mrow><mi>&pi;</mi><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>x</mi><msup><mi>b</mi><mn>2</mn></msup><mo>:</mo><mi>y</mi><msup><mi>a</mi><mn>2</mn></msup></math></p>\n",
                    solution_hi: "<p>18. (c)</p>\r\n<p><span style=\"font-family: Baloo;\">&#2350;&#2366;&#2344;&#2366; &#2348;&#2375;&#2354;&#2344;&#2379;&#2306; &#2325;&#2368; &#2314;&#2305;&#2330;&#2366;</span><span style=\"font-family: Baloo;\">&#2312;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>1</mn></msub><mo>&nbsp;</mo><msub><mi>h</mi><mn>2</mn></msub><mi>&#2361;&#2376;&#2404;</mi></math></p>\r\n<p><span style=\"font-family: Baloo;\">&#2346;&#2361;&#2354;&#2375; &#2348;&#2375;&#2354;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&pi;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mi>x</mi><mo>=</mo><mi>&pi;</mi><msup><mrow><mo>(</mo><mi>a</mi><mo>/</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><msub><mi>h</mi><mrow><mn>1</mn></mrow></msub></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>1</mn></msub><mo>=</mo><mn>4</mn><mi>x</mi><mo>/</mo><mi>&pi;</mi><msup><mi>a</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>2</mn></msub><mo>=</mo><mn>4</mn><mi>y</mi><mo>/</mo><mi>&pi;</mi><msup><mi>b</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Times New Roman;\"> =</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">:&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>x</mi><mo>:</mo></mrow><mrow><mi>&pi;</mi><msup><mi>a</mi><mn>2</mn></msup></mrow></mfrac><mfrac><mrow><mn>4</mn><mi>y</mi></mrow><mrow><mi>&pi;</mi><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>x</mi><msup><mi>b</mi><mn>2</mn></msup><mo>:</mo><mi>y</mi><msup><mi>a</mi><mn>2</mn></msup></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19.<span style=\"font-family: Times New Roman;\"> If the radius of a right circular cylinder is decreased by 20% while its height is increased by 40%, then the percentage change in its volume will be :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>19.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि किसी </span><span style=\"font-family: Baloo;\">लम्ब</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">वृत्तीय</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">बेलन</span><span style=\"font-family: Baloo;\"> की त्रिज्या 20% कम कर दी जाए और इसकी ऊंचाई 40% बढ़ा दी जाए, तो इसके </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> में आने वाला प्रतिशत परिवर्तन होगा : </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>1.04% increase</p>", "<p>10.4%decrease</p>", 
                                "<p>No increase <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>10.4% increase</p>"],
                    options_hi: ["<p>1.04% increase</p>", "<p>10.4%decrease</p>",
                                "<p>No increase <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>10.4% increase</p>"],
                    solution_en: "<p>19. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">20% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">And 40% <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We know that volume is directly proportional to the product of the square of the radius and the height</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Old : New</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Radius</span><span style=\"font-family: Times New Roman;\"> 5 : 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Height </span><span style=\"font-family: Times New Roman;\"> 5 : 7</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> ____________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume 125 : 112</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Decrease in volume = 125-112 = 13</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required %age = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>13</mn></mrow><mn>125</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mo>&#160;</mo><mn>10</mn><mo>.</mo><mn>4</mn><mo>%</mo></math></span></p>",
                    solution_hi: "<p>19. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">20% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">और 40% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">हम जानते हैं कि आयतन त्रिज्या के वर्ग और ऊँचाई के गुणनफल के सीधे समानुपाती होता है|</span></p>\r\n<p><span style=\"font-family: Baloo;\">पुरानी : नई </span></p>\r\n<p><span style=\"font-family: Baloo;\">त्रिज्या </span><span style=\"font-family: Times New Roman;\"> 5 : 4</span></p>\r\n<p><span style=\"font-family: Baloo;\">ऊँचाई </span><span style=\"font-family: Times New Roman;\"> 5 : 7</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> _______________</span></p>\r\n<p><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Times New Roman;\"> 125 : 112</span></p>\r\n<p><span style=\"font-family: Baloo;\">आयतन में कमी</span><span style=\"font-family: Times New Roman;\"> = 125 - 112 = 13</span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक प्रतिशत </span><span style=\"font-family: Times New Roman;\">=&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>125</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mo>&#160;</mo><mn>10</mn><mo>.</mo><mn>4</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\">= 10.4%</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20.<span style=\"font-family: Times New Roman;\"> If the radius of the base of a cone is doubled, and the volume of the new cone is three times the volume of the original cone, then what will be the ratio of the height of the original cone to that of the new cone ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>20.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि किसी शंकु के आधार की त्रिज्या दोगुनी कर दी गयी है , और नए शंकु का </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> आरंभिक शंकु के </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Baloo;\"> का तिगुना है, तो आरंभिक शंकु और नए शंकु की ऊँचाइयों के बीच क्या अनुपात होगा ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>1:3</p>", "<p>4:3</p>", 
                                "<p>2:9</p>", "<p>9:4</p>"],
                    options_hi: ["<p>1:3</p>", "<p>4:3</p>",
                                "<p>2:9</p>", "<p>9:4</p>"],
                    solution_en: "<p>20. (b)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> Let original volume = v , original radius = r and original height = h and new height = H</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">v =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>h</mi><mo>=</mo><mfrac><mrow><mn>3</mn><mo>&#119907;</mo></mrow><mrow><mo>&#120587;</mo><msup><mo>&#119903;</mo><mn>2</mn></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">According to the question</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3v&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mrow><mo>(</mo><mn>2</mn><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup><mi>H</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">H =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mi>v</mi></mrow><mrow><mo>(</mo><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required ratio =</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">:&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>v</mi></mrow><mrow><mo>(</mo><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac><mo>:</mo><mfrac><mrow><mn>9</mn><mi>v</mi></mrow><mrow><mo>(</mo><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 4 : 3</span></p>",
                    solution_hi: "<p>20. (b)</p>\r\n<p><span style=\"font-family: Baloo;\">माना मूल आयतन</span><span style=\"font-family: Times New Roman;\"> = v,</span><span style=\"font-family: Baloo;\"> मूल त्रिज्या</span><span style=\"font-family: Times New Roman;\"> = r </span><span style=\"font-family: Baloo;\">और मूल ऊँचाई</span><span style=\"font-family: Times New Roman;\"> = h </span><span style=\"font-family: Baloo;\">और नई ऊँचाई </span><span style=\"font-family: Times New Roman;\">= H</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">v =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mn>3</mn><mi>v</mi></mrow><mrow><mo>(</mo><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3v =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mrow><mo>(</mo><mn>2</mn><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup><mi>H</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">H =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mi>v</mi></mrow><mrow><mo>(</mo><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक अनुपात</span><span style=\"font-family: Times New Roman;\"> =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>v</mi></mrow><mrow><mo>(</mo><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac><mo>:</mo><mfrac><mrow><mn>9</mn><mi>v</mi></mrow><mrow><mo>(</mo><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math><span style=\"font-family: Times New Roman;\"> = 4 : 3</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21.<span style=\"font-family: Times New Roman;\"> If the diameter of the base of a cone is 42 cm and its curved surface area is 2310<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup><mo>,</mo></math> </span><span style=\"font-family: Times New Roman;\">, then what will be its volume (in<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup><mo>)</mo><mo>?</mo></math> </span><span style=\"font-family: Times New Roman;\">) ?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>21.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि किसी </span><span style=\"font-family: Baloo;\">शंकु</span><span style=\"font-family: Baloo;\"> के आधार का व्यास 42 सेमी है और इसका </span><span style=\"font-family: Baloo;\">वक्र</span><span style=\"font-family: Baloo;\"> पृष्ठ </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> 2310 वर्ग सेमी है, तो इसका </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Times New Roman;\"> ( </span><span style=\"font-family: Baloo;\">घन</span><span style=\"font-family: Baloo;\"> सेमी में ) क्या होगा ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>25872</p>", "<p>19404</p>", 
                                "<p>12936</p>", "<p>38808</p>"],
                    options_hi: ["<p>25872</p>", "<p>19404</p>",
                                "<p>12936</p>", "<p>38808</p>"],
                    solution_en: "<p>21. (c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Curved surface area of a cone&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mi>&#960;</mi><mi>r</mi><mi>l</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2310</mn><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>22</mn></mrow><mn>7</mn></mfrac><mo>&#215;</mo><mn>21</mn><mo>&#215;</mo><mo>&#119897;</mo></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">l =35 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Height of the cone =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8730;</mo><mo>(</mo><msup><mi>l</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#8730;</mo><mo>(</mo><msup><mn>35</mn><mn>2</mn></msup><mo>-</mo><msup><mn>21</mn><mn>2</mn></msup><mo>)</mo><mo>=</mo><mn>28</mn><mi>c</mi><mi>m</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Now,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the cone&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>21</mn><mo>&#215;</mo><mn>21</mn><mo>&#215;</mo><mn>28</mn><mo>=</mo><mn>12936</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math>&nbsp;</span></p>",
                    solution_hi: "<p>21. (c)</p>\r\n<p><span style=\"font-family: Baloo;\">एक शंकु का वक्र पृष्ठीय क्षेत्रफल </span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#960;</mi><mi>r</mi><mi>l</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2310 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>21</mn><mo>&#215;</mo><mi>l</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">l =35 cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">शंकु की ऊंचाई </span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8730;</mo><mo>(</mo><msup><mi>l</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#8730;</mo><mo>(</mo><msup><mn>35</mn><mn>2</mn></msup><mo>-</mo><msup><mn>21</mn><mn>2</mn></msup><mo>)</mo><mo>=</mo><mn>28</mn><mi>c</mi><mi>m</mi></math></span><span style=\"font-family: Times New Roman;\"> =28 cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">अब, </span></p>\r\n<p><span style=\"font-family: Baloo;\">शंकु का आयतन</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>21</mn><mo>&#215;</mo><mn>21</mn><mo>&#215;</mo><mn>28</mn><mo>=</mo><mn>12936</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\r\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22.<span style=\"font-family: Times New Roman;\"> If a cuboid of dimensions 32cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">12cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">9cm is cut into two cubes of same size, what will be the ratio of the surface area of the cuboid to the total surface area of the two cubes ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>22. <span style=\"font-family: Baloo;\">यदि 32 सेमी &times;12 सेमी &times;9 सेमी विमाओं वाले एक घनाभ को बराबर आकार के दो घनों में काटा जाए, तो घनाभ के पृष्ठ </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> और दोनों घनों के कुल पृष्ठ </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> में क्या अनुपात होगा ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>65:72</p>", "<p>37:48</p>", 
                                "<p>24:35</p>", "<p>32:39</p>"],
                    options_hi: ["<p>65:72</p>", "<p>37:48</p>",
                                "<p>24:35</p>", "<p>32:39</p>"],
                    solution_en: "<p>22. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the cuboid = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">Volume of the cube</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the side of cube = a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the cuboid =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>32</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>9</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the cube =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>32</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>9</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><msup><mo>&#119886;</mo><mn>3</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">=</span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>=</mo><mroot><mrow><mo>&#160;</mo><mo>&#160;</mo><mn>16</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>9</mn><mo>&#160;</mo></mrow><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total surface area of the cuboid = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>h</mi><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 2(32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">12+12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">9+32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">9 )</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 1560&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total surface area of the cube = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><msup><mi>a</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mo>&#215;</mo><msup><mn>12</mn><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">= 864<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required ratio = 1560 : 2</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>864</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 65 : 72</span></p>",
                    solution_hi: "<p>22. (a)</p>\r\n<p><span style=\"font-family: Baloo;\">घनाभ का आयतन</span><span style=\"font-family: Times New Roman;\"> = 2 &times;</span><span style=\"font-family: Baloo;\">घन का आयतन</span></p>\r\n<p><span style=\"font-family: Baloo;\">माना घन की भुजा</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">घनाभ का आयतन</span><span style=\"font-family: Times New Roman;\"> = 32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">घन का आयतन</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">9 = 2&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><msup><mi>a</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a=</span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8731;</mo><mo>(</mo><mn>16</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>9</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn></math>&nbsp;</span></p>\r\n<p><span style=\"font-family: Baloo;\">घनाभ के कुल पृष्ठीय क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>h</mi><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 2(32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">12+12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">9+32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">9 )</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 1560&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">घन का कुल पृष्ठीय क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><msup><mi>a</mi><mn>2</mn></msup><mo>=</mo><mn>6</mn><mo>&#215;</mo><msup><mn>12</mn><mn>2</mn></msup><mo>=</mo><mn>864</mn><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक अनुपात </span><span style=\"font-family: Times New Roman;\"> = 1560 : 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> 864</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 65 : 72</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23.<span style=\"font-family: Times New Roman;\"> The lateral surface area of a cylinder is 352 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup><mo>.</mo></math></span><span style=\"font-family: Times New Roman;\">. If its height is 7 cm, then its volume </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>i</mi><mi>n</mi><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup><mo>)</mo></math><span style=\"font-family: Times New Roman;\">is : (Take </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>=</mo><mn>22</mn><mo>/</mo><mn>7</mn><mo>)</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>23.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक </span><span style=\"font-family: Baloo;\">बेलन</span><span style=\"font-family: Baloo;\"> का पार्श्व पृष्ठ </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> 352 वर्ग सेमी है | यदि इसकी ऊंचाई 7 सेमी है, तो इसका </span><span style=\"font-family: Baloo;\">आयतन</span><span style=\"font-family: Times New Roman;\"> ( </span><span style=\"font-family: Baloo;\">घन</span><span style=\"font-family: Baloo;\"> सेमी में ) ज्ञात करें |</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>=</mo><mn>22</mn><mo>/</mo><mn>7</mn><mo>)</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>1408<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>1078</p>", 
                                "<p>1243</p>", "<p>891</p>"],
                    options_hi: ["<p>1408<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>1078</p>",
                                "<p>1243</p>", "<p>891</p>"],
                    solution_en: "<p>23. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Lateral surface area of the cylinder </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><mi>&#960;</mi><mi>r</mi><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>352</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mn>7</mn></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">r=8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the cylinder&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> =</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1408</mn></math></span></p>",
                    solution_hi: "<p>23. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">बेलन का पार्श्व सतह क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><mi>&#960;</mi><mi>r</mi><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>352</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mn>7</mn></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">r=8</span></p>\r\n<p><span style=\"font-family: Baloo;\">बेलन की आयतन</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo></math>=<span style=\"font-family: Times New Roman;\">1408</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24.<span style=\"font-family: Times New Roman;\"> The internal and external radii of a hollow hemispherical vessel are 6 cm and 7 cm respectively. What is the total surface are </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>i</mi><mi>n</mi><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></math><span style=\"font-family: Times New Roman;\">of the vessel ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>24.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक खोखले अर्धगोलीय बर्तन की आतंरिक और </span><span style=\"font-family: Baloo;\">बाह्य</span><span style=\"font-family: Baloo;\"> त्रिज्या क्रमश 6 सेमी और 7 सेमी है | इस बर्तन का कुल पृष्ठ </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> ( वर्ग सेमी में ) ज्ञात करें | </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>183</p>", "<p>189</p>", 
                                "<p>177</p>", "<p>174</p>"],
                    options_hi: ["<p>183<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo> </mo></math></p>", "<p>189<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo> </mo></math></p>",
                                "<p>177<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo> </mo></math></p>", "<p>174<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo> </mo></math></p>"],
                    solution_en: "<p>24. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> Total surface area of a hollow hemisphere= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;</mi><mo>(</mo><msup><mi>R</mi><mn>2</mn></msup><mo>+</mo><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo><mo>+</mo><mi>&#960;</mi><mo>(</mo><msup><mi>R</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Here R =7 cm and r = 6 cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total surface area of the vessel </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&#215;</mo><mi>&#960;</mi><mo>&#215;</mo><mo>(</mo><msup><mn>7</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup><mo>)</mo><mo>+</mo><mi>&#960;</mi><mo>&#215;</mo><mo>(</mo><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><msup><mn>6</mn><mn>2</mn></msup><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>85</mn><mo>+</mo><mn>13</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>183</mn><mi>&#960;</mi></math></p>",
                    solution_hi: "<p>24. (a)</p>\r\n<p><span style=\"font-family: Baloo;\">एक खोखले अर्धगोले का कुल पृष्ठीय क्षेत्रफल </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>2</mn><mi>&#960;</mi><mo>(</mo><msup><mi>R</mi><mn>2</mn></msup><mo>+</mo><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo><mo>+</mo><mi>&#960;</mi><mo>(</mo><msup><mi>R</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Baloo;\">यहाँ</span><span style=\"font-family: Times New Roman;\"> R =7 </span><span style=\"font-family: Baloo;\">सेमी</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> r = 6 </span><span style=\"font-family: Baloo;\">सेमी</span></p>\r\n<p><span style=\"font-family: Baloo;\">कुल सतह का क्षेत्रफल </span><span style=\"font-family: Times New Roman;\"> =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&#215;</mo><mi>&#960;</mi><mo>&#215;</mo><mo>(</mo><msup><mn>7</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup><mo>)</mo><mo>+</mo><mi>&#960;</mi><mo>&#215;</mo><mo>(</mo><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><msup><mn>6</mn><mn>2</mn></msup><mo>)</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mi>&#960;</mi><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>85</mn><mo>+</mo><mn>13</mn><mo>)</mo><mo>&#160;</mo></math>=<span style=\"font-family: Times New Roman;\">183<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> Three solid metallic spheres whose radii are 1cm, x cm and 8 cm, are melted and recast into a single solid sphere of diameter 18 cm. The surface area (in<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>i</mi><mi>n</mi><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup><mo>)</mo></math> </span><span style=\"font-family: Times New Roman;\">) of the sphere with radius x cm is :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>25.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">तीन ठोस </span><span style=\"font-family: Baloo;\">धात्विक</span><span style=\"font-family: Baloo;\"> गोले जिनकी त्रिज्याएँ 1 सेमी, xसेमी और 8 सेमी हैं, उन्हें पिघलाया जाता है और फिर 18 सेमी व्यास वाला एक ठोस </span><span style=\"font-family: Baloo;\">गोला</span><span style=\"font-family: Baloo;\"> बनाया जाता है | उस गोले का पृष्ठ </span><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Baloo;\"> ( वर्ग सेमी में ) ज्ञात करें जिसकी त्रिज्या xसेमी थी | </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>144<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></p>", "<p>72<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></p>", 
                                "<p>64<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></p>", "<p>100<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></p>"],
                    options_hi: ["<p>144<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi><mo>	</mo></math></p>", "<p>72<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></p>",
                                "<p>64<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></p>", "<p>100<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></p>"],
                    solution_en: "<p>25. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of the sphere =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Here,&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mn>9</mn><mn>3</mn></msup><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mn>1</mn><mn>3</mn></msup><mo>+</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mn>8</mn><mn>3</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mn>9</mn><mn>3</mn></msup><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><mo>(</mo><msup><mn>1</mn><mn>3</mn></msup><mo>+</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><msup><mn>8</mn><mn>3</mn></msup><mo>)</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>729</mn><mo>=</mo><mn>1</mn><mo>+</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mn>512</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">x=6</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Surface area of the sphere =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup></math></span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>4</mn><mo>&#215;</mo><mi>&#960;</mi><mo>&#215;</mo><msup><mn>6</mn><mn>2</mn></msup><mo>=</mo><mn>144</mn><mi>&#960;</mi></math></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>25. (a)</p>\r\n<p><span style=\"font-family: Baloo;\">गोले का आयतन</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यहां, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mn>9</mn><mn>3</mn></msup><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mn>1</mn><mn>3</mn></msup><mo>+</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mn>8</mn><mn>3</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mn>9</mn><mn>3</mn></msup><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><mo>(</mo><msup><mn>1</mn><mn>3</mn></msup><mo>+</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><msup><mn>8</mn><mn>3</mn></msup><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">729 =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>1</mn><mo>+</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mn>512</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">x =6</span></p>\r\n<p><span style=\"font-family: Baloo;\">गोले का पृष्ठीय क्षेत्रफल </span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>4</mn><mo>&#215;</mo><mi>&#960;</mi><mo>&#215;</mo><msup><mn>6</mn><mn>2</mn></msup><mo>=</mo><mn>144</mn><mi>&#960;</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 144<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;</mi></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>