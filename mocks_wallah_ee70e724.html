<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.A hundred rupee note measures 15 cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math> 8 cm and a bundle of 125 such notes is 2 cm thick. Find the value of the hundred rupees note that can be contained in a box of size 48 cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math> 36 cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math>30 cm , if the bundles are tightly packed in it without any empty space.</p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Palanquin Dark;\"> &#2360;&#2380; &#2352;&#2369;&#2346;&#2319; &#2325;&#2375; &#2319;&#2325; &#2344;&#2379;&#2335; &#2325;&#2368; &#2350;&#2366;&#2346; 15 &#2360;&#2375;&#2350;&#2368;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> 8 &#2360;&#2375;&#2350;&#2368; &#2324;&#2352; 125 &#2320;&#2360;&#2375; &#2344;&#2379;&#2335;&#2379;&#2306; &#2325;&#2366; &#2319;&#2325; &#2348;&#2306;&#2337;&#2354; 2 &#2360;&#2375;&#2350;&#2368; &#2350;&#2379;&#2335;&#2366; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; 48 &#2360;&#2375;&#2350;&#2368;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math></span><span style=\"font-family: Palanquin Dark;\">36 &#2360;&#2375;&#2350;&#2368;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math></span><span style=\"font-family: Palanquin Dark;\">30&#2360;&#2375;&#2350;&#2368; &#2310;&#2325;&#2366;&#2352; &#2325;&#2375; &#2319;&#2325; &#2348;&#2377;&#2325;&#2381;&#2360; &#2350;&#2375;&#2306; &#2352;&#2326;&#2375; &#2332;&#2366; &#2360;&#2325;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2360;&#2380; &#2352;&#2369;&#2346;&#2351;&#2375; &#2325;&#2375; &#2344;&#2379;&#2335; &#2325;&#2366; &#2350;&#2370;&#2354;&#2381;&#2351; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2352;&#2375;&#2306;, &#2351;&#2342;&#2367; &#2348;&#2306;&#2337;&#2354;&#2379;&#2306; &#2325;&#2379; &#2348;&#2367;&#2344;&#2366; &#2325;&#2367;&#2360;&#2368; &#2326;&#2366;&#2354;&#2368; &#2332;&#2327;&#2361; &#2325;&#2375; &#2325;&#2360;&#2325;&#2352; &#2346;&#2376;&#2325; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>36 Lakhs</p>\n", "<p>33 Lakhs</p>\n", 
                                "<p>30 Lakhs</p>\n", "<p>27 Lakhs</p>\n"],
                    options_hi: ["<p>36 &#2354;&#2366;&#2326;</p>\n", "<p>33 &#2354;&#2366;&#2326;</p>\n",
                                "<p>30 &#2354;&#2366;&#2326;</p>\n", "<p>27 &#2354;&#2366;&#2326;</p>\n"],
                    solution_en: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">As per question,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">48 &times; 36 &times; 30 = 15 &times; 8 &times; 2 &times; n</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">n = 216</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total value of hundred rupee note = 216 &times; 125 &times; 100 = 27,00,000</span></p>\n",
                    solution_hi: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">48 &times; 36 &times; 30 = 15 &times; 8 &times; 2 &times; n</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">n = 216</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2380; &#2352;&#2369;&#2346;&#2351;&#2375; &#2325;&#2375; &#2344;&#2379;&#2335; &#2325;&#2366; &#2325;&#2369;&#2354; &#2350;&#2370;&#2354;&#2381;&#2351; = 216 &times; 125 &times; 100 = 27,00,000</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Palanquin Dark;\">A cylindrical bucket of height 150 cm and radius 50 cm is full of water. A person wants to fill spherical balloons of radius 5 cm each with the water in the bucket. How many balloons can he fill completely with the water present in the bucket?</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Palanquin Dark;\">150 &#2360;&#2375;&#2350;&#2368; &#2314;&#2305;&#2330;&#2366;&#2312; &#2324;&#2352; 50 &#2360;&#2375;&#2350;&#2368; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; &#2325;&#2368; &#2319;&#2325; &#2348;&#2375;&#2354;&#2344;&#2366;&#2325;&#2366;&#2352; &#2348;&#2366;&#2354;&#2381;&#2335;&#2368; &#2346;&#2366;&#2344;&#2368; &#2360;&#2375; &#2349;&#2352;&#2368; &#2361;&#2369;&#2312; &#2361;&#2376;&#2404; &#2319;&#2325; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2348;&#2366;&#2354;&#2381;&#2335;&#2368; &#2325;&#2375; &#2346;&#2366;&#2344;&#2368; &#2360;&#2375; 5 &#2360;&#2375;&#2350;&#2368; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; &#2325;&#2375; &#2327;&#2379;&#2354;&#2366;&#2325;&#2366;&#2352; &#2327;&#2369;&#2348;&#2381;&#2348;&#2366;&#2352;&#2375; &#2349;&#2352;&#2344;&#2366; &#2330;&#2366;&#2361;&#2340;&#2366; &#2361;&#2376;&#2404; &#2348;&#2366;&#2354;&#2381;&#2335;&#2368; &#2350;&#2375;&#2306; &#2350;&#2380;&#2332;&#2370;&#2342; &#2346;&#2366;&#2344;&#2368; &#2360;&#2375; &#2357;&#2361; &#2325;&#2367;&#2340;&#2344;&#2375; &#2327;&#2369;&#2348;&#2381;&#2348;&#2366;&#2352;&#2375; &#2346;&#2370;&#2352;&#2368; &#2340;&#2352;&#2361; &#2349;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>2250 </p>\n", "<p>1753</p>\n", 
                                "<p>1456</p>\n", "<p>1832</p>\n"],
                    options_hi: ["<p>2250</p>\n", "<p>1753</p>\n",
                                "<p>1456</p>\n", "<p>1832</p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Radius = 50</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Height = 150</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the number of balloons can be filled = n</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">As per question,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&pi; &times; 50 &times; 50 &times; 150 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; &pi; &times; 5 &times; 5 &times; 5 &times; n</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">n = 2250</span></p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; = 50</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2314;&#2306;&#2330;&#2366;&#2312; = 150</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2327;&#2369;&#2348;&#2381;&#2348;&#2366;&#2352;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2349;&#2352;&#2368; &#2332;&#2366; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376; = n</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&pi; &times; 50 &times; 50 &times; 150 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; &pi; &times; 5 &times; 5 &times; 5 &times; n</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">n = 2250</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Palanquin Dark;\"> The area of a rectangular field, whose sides are in the ratio 13 : 5, is 26<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">. What is the perimeter of the rectangular field?</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Palanquin Dark;\"> &#2319;&#2325; &#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352; &#2350;&#2376;&#2342;&#2366;&#2344; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;, &#2332;&#2367;&#2360;&#2325;&#2368; &#2349;&#2369;&#2332;&#2366;&#2319;&#2305; 13 : 5 &#2325;&#2375; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2306;, 260 &#2357;&#2352;&#2381;&#2327; &#2350;&#2368;&#2335;&#2352; &#2361;&#2376;&#2404; &#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2366; &#2346;&#2352;&#2367;&#2350;&#2366;&#2346; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>70 m</p>\n", "<p>72 m</p>\n", 
                                "<p>66 m</p>\n", "<p>68 m</p>\n"],
                    options_hi: ["<p>70 &#2350;&#2368;&#2335;&#2352;</p>\n", "<p>72 &#2350;&#2368;&#2335;&#2352;</p>\n",
                                "<p>66 &#2350;&#2368;&#2335;&#2352;</p>\n", "<p>68 &#2350;&#2368;&#2335;&#2352;</p>\n"],
                    solution_en: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Area of rectangular field = 260</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Length : Breadth = 13 : 5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">As per question,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">13x &times; 5x = 260</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> = 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Length = 13x = 13 &times; 2 = 26 and Breadth = 5x = 5 &times; 2 = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Perimeter = 2(26 + 10) = 72 m</span></p>\n",
                    solution_hi: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352; &#2326;&#2375;&#2340; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = 260</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2354;&#2306;&#2348;&#2366;&#2312; : &#2330;&#2380;&#2337;&#2364;&#2366;&#2312; = 13 : 5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">13x &times; 5x = 260</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> = 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2354;&#2306;&#2348;&#2366;&#2312; = 13x = 13 &times; 2 = 26 &#2324;&#2352; &#2330;&#2380;&#2337;&#2364;&#2366;&#2312; = 5x = 5 &times; 2 = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346; = 2(26 + 10) = 72 &#2350;&#2368;&#2335;&#2352;</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The height of the frustum of a cone is 8 cm. The radius on the top of the frustum is 6 cm and the bottom of the frustum is 12 cm. What is the curved surface area of the frustum?</p>\n",
                    question_hi: "<p>4. &#2319;&#2325; &#2358;&#2306;&#2325;&#2369; &#2325;&#2375; &#2331;&#2367;&#2344;&#2381;&#2344;&#2325; &#2325;&#2368; &#2314;&#2305;&#2330;&#2366;&#2312; 8 &#2360;&#2375;&#2350;&#2368; &#2361;&#2376;&#2404; &#2331;&#2367;&#2344;&#2381;&#2344;&#2325; &#2325;&#2375; &#2358;&#2368;&#2352;&#2381;&#2359; &#2346;&#2352; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; 6 &#2360;&#2375;&#2350;&#2368; &#2361;&#2376; &#2324;&#2352; &#2331;&#2367;&#2344;&#2381;&#2344;&#2325; &#2325;&#2375; &#2340;&#2354; &#2325;&#2368; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; 12 &#2360;&#2375;&#2350;&#2368; &#2361;&#2376;&#2404; &#2331;&#2367;&#2344;&#2381;&#2344;&#2325; &#2325;&#2366; &#2357;&#2325;&#2381;&#2352; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</p>\n",
                    options_en: ["<p>240<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math><span style=\"font-family: Palanquin Dark;\"> cm&sup2;</span></p>\n", "<p>180<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math><span style=\"font-family: Palanquin Dark;\"> cm&sup2;</span></p>\n", 
                                "<p>360<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math><span style=\"font-family: Palanquin Dark;\"> cm&sup2;</span></p>\n", "<p>36<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math><span style=\"font-family: Palanquin Dark;\"> cm&sup2;</span></p>\n"],
                    options_hi: ["<p>240<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math><span style=\"font-family: Palanquin Dark;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup></math></span></p>\n", "<p>180<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math><span style=\"font-family: Palanquin Dark;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup></math>&nbsp;</span></p>\n",
                                "<p>360<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math><span style=\"font-family: Palanquin Dark;\">&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup></math></span></p>\n", "<p>36<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup></math><span style=\"font-family: Palanquin Dark;\">&nbsp;</span></p>\n"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Slant height =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>-</mo><mn>6</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> </span><span style=\"font-family: Palanquin Dark;\"> = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Curved surface area of frustum = </span><span style=\"font-family: Palanquin Dark;\">&pi;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>1</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>2</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">) &times; l = &pi;(6 + 12) &times; 10 = 180&pi;</span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span></p>\n",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2367;&#2352;&#2331;&#2368; &#2314;&#2306;&#2330;&#2366;&#2312; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mfenced><mn>8</mn></mfenced><mn>2</mn></msup><mo>+</mo><msup><mfenced><mrow><mn>12</mn><mo>-</mo><mn>6</mn></mrow></mfenced><mn>2</mn></msup></msqrt></math> </span><span style=\"font-family: Palanquin Dark;\"> = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2331;&#2367;&#2344;&#2381;&#2344;&#2325; &#2325;&#2366; &#2357;&#2325;&#2381;&#2352; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = </span><span style=\"font-family: Palanquin Dark;\">&pi;(</span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>1</mn></msub><mo>+</mo><mo>&nbsp;</mo><msub><mi>r</mi><mn>2</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">) &times; l = &pi;(6 + 12) &times; 10 = 180&pi; </span><span style=\"font-family: Palanquin Dark;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup></math></span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Find the volume of a cylinder whose base radius is 12.5 cm and whose height is<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> <span style=\"font-family: Palanquin Dark;\"> of the base radius.</span></p>\n",
                    question_hi: "<p>5. &#2319;&#2325; &#2348;&#2375;&#2354;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2332;&#2367;&#2360;&#2325;&#2368; &#2310;&#2343;&#2366;&#2352; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; 12.5 &#2360;&#2375;&#2350;&#2368; &#2361;&#2376; &#2324;&#2352; &#2332;&#2367;&#2360;&#2325;&#2368; &#2314;&#2305;&#2330;&#2366;&#2312;, &#2310;&#2343;&#2366;&#2352; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; &#2325;&#2368;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> <span style=\"font-family: Palanquin Dark;\"> &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>1652.5<span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>cm</mi><mn>3</mn></msup></math></span></p>\n", "<p>1562.5<span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>cm</mi><mn>3</mn></msup></math></span></p>\n", 
                                "<p>1250<span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>cm</mi><mn>3</mn></msup></math></span></p>\n", "<p>2441.4<span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>cm</mi><mn>3</mn></msup></math></span></p>\n"],
                    options_hi: ["<p>1652.5<span style=\"font-family: Palanquin Dark;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>3</mn></msup></math></span></p>\n", "<p>1562.5<span style=\"font-family: Palanquin Dark;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>3</mn></msup></math></span></p>\n",
                                "<p>1250<span style=\"font-family: Palanquin Dark;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>3</mn></msup></math></span></p>\n", "<p>2441.4<span style=\"font-family: Palanquin Dark;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>3</mn></msup></math></span></p>\n"],
                    solution_en: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Radius = 12.5 cm</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Height = 12.5 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Volume of the cylinder = &pi; &times; 12.5 &times; 12.5 &times; 10 = 1562.5&pi;</span></p>\n",
                    solution_hi: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; = 12.5 cm</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2314;&#2306;&#2330;&#2366;&#2312; = 12.5 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 10</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&#2360;&#2367;&#2354;&#2375;&#2306;&#2337;&#2352; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; = &pi; &times; 12.5 &times; 12.5 &times; 10 = 1562.5&pi;</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A field is in the shape of a rhombus, whose perimeter is 292 m and one of its diagonals is 96 m. What is the area of the field?</p>\n",
                    question_hi: "<p>6. &#2319;&#2325; &#2326;&#2375;&#2340; &#2319;&#2325; &#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2375; &#2310;&#2325;&#2366;&#2352; &#2325;&#2366; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2325;&#2366; &#2346;&#2352;&#2367;&#2350;&#2366;&#2346; 292 &#2350;&#2368;&#2335;&#2352; &#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2325;&#2366; &#2319;&#2325; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; 96 &#2350;&#2368;&#2335;&#2352; &#2361;&#2376;&#2404; &#2350;&#2376;&#2342;&#2366;&#2344; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</p>\n",
                    options_en: ["<p>4800&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></p>\n", "<p>7008&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></p>\n", 
                                "<p>5280&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></p>\n", "<p>5040&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></p>\n"],
                    options_hi: ["<p>4800&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></p>\n", "<p>7008&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></p>\n",
                                "<p>5280&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></p>\n", "<p>5040&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></p>\n"],
                    solution_en: "<p>6.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680672355/word/media/image1.png\" width=\"158\" height=\"135\"></p>\r\n<p><span style=\"font-family: Basic;\">Side =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>292</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Basic;\"> = 73</span></p>\r\n<p><span style=\"font-family: Basic;\">In triangle ODC,</span></p>\r\n<p><span style=\"font-family: Basic;\">O<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>C</mi><mn>2</mn></msup></math></span><span style=\"font-family: Basic;\"> = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>2</mn></msup></math></span><span style=\"font-family: Basic;\"> - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Basic;\">OC = 55</span></p>\r\n<p><span style=\"font-family: Basic;\">AC = 2 &times; 55 = 110 m</span></p>\r\n<p><span style=\"font-family: Basic;\">Area of rhombus =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><msub><mi>d</mi><mn>1</mn></msub><mo>&times;</mo><msub><mi>d</mi><mn>2</mn></msub></math></span><span style=\"font-family: Basic;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Basic;\"> &times; 96 &times; 110 = 5280 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></span></p>\n",
                    solution_hi: "<p>6.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680672355/word/media/image1.png\" width=\"189\" height=\"163\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2349;&#2369;&#2332;&#2366; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>292</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 73</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; ODC &#2350;&#2375;&#2306;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">O<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>C</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">OC = 55</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">AC = 2 &times; 55 = 110</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><msub><mi>d</mi><mn>1</mn></msub><mo>&times;</mo><msub><mi>d</mi><mn>2</mn></msub><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&times; 96 &times; 110 = 5280&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math></span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. A 3200 m long cylindrical copper wire of radius 0.3 mm is melted and recast into a sphere( without wasting any material). What is the surface area (in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math><span style=\"font-family: Palanquin Dark;\">) of the sphere?</span></p>\n",
                    question_hi: "<p>7. 0.3 &#2350;&#2367;&#2350;&#2368; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; &#2325;&#2375; &#2319;&#2325; 3200 &#2350;&#2368;&#2335;&#2352; &#2354;&#2306;&#2348;&#2375; &#2348;&#2375;&#2354;&#2344;&#2366;&#2325;&#2366;&#2352; &#2340;&#2366;&#2306;&#2348;&#2375; &#2325;&#2375; &#2340;&#2366;&#2352; &#2325;&#2379; &#2346;&#2367;&#2328;&#2354;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2319;&#2325; &#2327;&#2379;&#2354;&#2375; &#2350;&#2375;&#2306; (&#2348;&#2367;&#2344;&#2366; &#2325;&#2367;&#2360;&#2368; &#2360;&#2366;&#2350;&#2327;&#2381;&#2352;&#2368; &#2325;&#2379; &#2348;&#2352;&#2381;&#2348;&#2366;&#2342; &#2325;&#2367;&#2319;) &#2346;&#2369;&#2344;: &#2338;&#2366;&#2354;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2327;&#2379;&#2354;&#2375; &#2325;&#2366; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> <span style=\"font-family: Palanquin Dark;\">&#2350;&#2375;&#2306;) &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>140&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></p>\n", "<p>144&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></p>\n", 
                                "<p>198&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></p>\n", "<p>256&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></p>\n"],
                    options_hi: ["<p>140&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></p>\n", "<p>144&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></p>\n",
                                "<p>198&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></p>\n", "<p>256&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></p>\n"],
                    solution_en: "<p>7.(b)</p>\r\n<p><span style=\"font-family: Basic;\">Radius = 0.3 mm or 0.03 cm</span></p>\r\n<p><span style=\"font-family: Basic;\">Volume of wire = Volume of sphere</span></p>\r\n<p><span style=\"font-family: Basic;\">&pi; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>3</mn><mn>100</mn></mfrac><mo>&times;</mo></math></span><span style=\"font-family: Basic;\">3200 &times; 100 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Basic;\"> &times; &pi; &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>r</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Basic;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>r</mi><mn>3</mn></msup></math></span><span style=\"font-family: Basic;\"> = 216</span></p>\r\n<p><span style=\"font-family: Basic;\">r = 6</span></p>\r\n<p><span style=\"font-family: Basic;\">Surface area of the sphere = 4 &times; &pi; &times; 6 &times; 6 = 144&pi;</span></p>\n",
                    solution_hi: "<p>7.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; = 0.3 &#2350;&#2367;&#2350;&#2368; &#2351;&#2366; 0.03 &#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2366;&#2352; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; = &#2327;&#2379;&#2354;&#2375; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&pi; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>3</mn><mn>100</mn></mfrac><mo>&times;</mo></math></span><span style=\"font-family: Palanquin Dark;\">3200 &times; 100 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; &pi; &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>r</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>r</mi><mn>3</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> = 216</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">r = 6</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&#2327;&#2379;&#2354;&#2375; &#2325;&#2366; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = 4 &times; &pi; &times; 6 &times; 6 = 144&pi;</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The area of a rectangle is 2430 <span style=\"font-family: Arial Unicode MS;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Arial Unicode MS;\">&nbsp;and its width is 9 cm less than its length. Find the perimeter of the rectangle.</span></p>\n",
                    question_hi: "<p>8. &#2319;&#2325; &#2310;&#2351;&#2340; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; 2430 &#2357;&#2352;&#2381;&#2327; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup><mo>&nbsp;</mo></math><span style=\"font-family: Palanquin Dark;\">&#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2325;&#2368; &#2330;&#2380;&#2337;&#2364;&#2366;&#2312; &#2311;&#2360;&#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; &#2360;&#2375; 9 &#2360;&#2375;&#2350;&#2368; &#2325;&#2350; &#2361;&#2376;&#2404; &#2310;&#2351;&#2340; &#2325;&#2366; &#2346;&#2352;&#2367;&#2350;&#2366;&#2346; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>192 cm</p>\n", "<p>180 cm</p>\n", 
                                "<p>216 cm</p>\n", "<p>198 cm</p>\n"],
                    options_hi: ["<p>192 &#2360;&#2375;&#2350;&#2368;</p>\n", "<p>180 &#2360;&#2375;&#2350;&#2368;</p>\n",
                                "<p>216 &#2360;&#2375;&#2350;&#2368;</p>\n", "<p>198 &#2360;&#2375;&#2350;&#2368;</p>\n"],
                    solution_en: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let Length = x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Width = x - 9</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Area of rectangle = x(x - 9)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>2430 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;- 9x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;- 9x - 2430 = 0</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;- 54x + 45x - 2430 = 0</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>x(x - 54) + 45(x - 54) = 0</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>(x - 54)(x + 45) = 0</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 54 (x can&rsquo;t be negative)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, length = 54</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Width = 54 - 9 = 45</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Perimeter = 2(54 + 45) = 198</span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2354;&#2306;&#2348;&#2366;&#2312; = x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312; = x - 9</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2310;&#2351;&#2340; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = x(x - 9)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2430 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;- 9x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> - 9x - 2430 = 0</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> - 54x + 45x - 2430 = 0</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&rArr; x(x - 54) + 45(x - 54) = 0</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&rArr; (x - 54) </span>(x + 45) = 0</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 54 (x &#2315;&#2339;&#2366;&#2340;&#2381;&#2350;&#2325; &#2344;&#2361;&#2368;&#2306; &#2361;&#2379; &#2360;&#2325;&#2340;&#2366;)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;: &#2354;&#2350;&#2381;&#2348;&#2366;&#2312; = 54</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312; = 54 - 9 = 45</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346; = 2(54 + 45) = 198</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Palanquin Dark;\"> The difference between the external and internal curved surface area of a 7 cm long cylindrical metallic pipe is 110 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> </span><span style=\"font-family: Palanquin Dark;\">. If the pipe is made of 302.5&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math> </span><span style=\"font-family: Palanquin Dark;\">metal, then find the inner radius of the pipe.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">(use <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Palanquin Dark;\"> 7 cm &#2354;&#2306;&#2348;&#2375; &#2343;&#2366;&#2340;&#2369; &#2325;&#2375; &#2319;&#2325; &#2348;&#2375;&#2354;&#2344;&#2366;&#2325;&#2366;&#2352; &#2346;&#2366;&#2311;&#2346; &#2325;&#2375; &#2348;&#2366;&#2361;&#2381;&#2351; &#2324;&#2352; &#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2357;&#2325;&#2381;&#2352; &#2346;&#2371;&#2359;&#2381;&#2336;&#2379;&#2306; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;&#2379;&#2306; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; 110<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> </span><span style=\"font-family: Palanquin Dark;\"> &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2346;&#2366;&#2311;&#2346; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340; &#2343;&#2366;&#2340;&#2369; 302.5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math> </span><span style=\"font-family: Palanquin Dark;\"> &#2361;&#2376;, &#2340;&#2379; &#2346;&#2366;&#2311;&#2346; &#2325;&#2368; &#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2367;&#2319;) </span></p>\n",
                    options_en: ["<p>2.2 cm</p>\n", "<p>1.8 cm</p>\n", 
                                "<p>1.5 cm</p>\n", "<p>1.6 cm</p>\n"],
                    options_hi: ["<p>2.2 cm</p>\n", "<p>1.8 cm</p>\n",
                                "<p>1.5 cm</p>\n", "<p>1.6 cm</p>\n"],
                    solution_en: "<p>9.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Curved surface area of cylinder = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></span><span style=\"font-family: Palanquin Dark;\">(R-r)&times;h = 110</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times;(R-r)&times;7 = 110</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">(R - r)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>110</mn><mn>44</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo></math>(i)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Volume of cylinder =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>R</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">) &times; h = 302.5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times;(R+r)&times;2.5&times;7 = 302.5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>.</mo><mn>5</mn><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">From (i) and (ii)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">r = 1.5cm </span></p>\n",
                    solution_hi: "<p>9.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2348;&#2375;&#2354;&#2344; &#2325;&#2366; &#2357;&#2325;&#2381;&#2352; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></span><span style=\"font-family: Palanquin Dark;\">(R-r)&times;h = 110</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times;(R-r)&times;7 = 110</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">(R - r)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>110</mn><mrow><mn>44</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&mdash;</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo></math>(i)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2348;&#2375;&#2354;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344;=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>R</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">) &times; h = 302.5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times;(R+r)&times;2.5&times;7 = 302.5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>.</mo><mn>5</mn><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>i</mi><mo>)</mo><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mo>&nbsp;</mo><mi>&#2360;&#2375;</mi><mo>,</mo></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>r</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mi>c</mi><mi>m</mi><mo>&nbsp;</mo></math></span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Palanquin Dark;\"> The perimeters of two squares differ by 48 cm. If the side of one square is four times the side of the other square, then find the area of the larger square (in&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> </span><span style=\"font-family: Palanquin Dark;\">).</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Palanquin Dark;\"> &#2342;&#2379; &#2357;&#2352;&#2381;&#2327;&#2379;&#2306; &#2325;&#2375; &#2346;&#2352;&#2367;&#2350;&#2366;&#2346;&#2379;&#2306; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; 48 cm &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2319;&#2325; &#2357;&#2352;&#2381;&#2327; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; &#2342;&#2370;&#2360;&#2352;&#2375; &#2357;&#2352;&#2381;&#2327; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; &#2325;&#2368; &#2330;&#2366;&#2352; &#2327;&#2369;&#2344;&#2368; &#2361;&#2376;, &#2340;&#2379; &#2348;&#2337;&#2364;&#2375; &#2357;&#2352;&#2381;&#2327; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> &#2350;&#2375;&#2306;) &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>256</p>\n", "<p>289</p>\n", 
                                "<p>196</p>\n", "<p>225</p>\n"],
                    options_hi: ["<p>256</p>\n", "<p>289</p>\n",
                                "<p>196</p>\n", "<p>225</p>\n"],
                    solution_en: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the side of first square = x, Perimeter = 4x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Side of the another square = 4x, perimeter = 4 &times; 4x = 16x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">As per question,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">16x - 4x = 48</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Side of the larger square = 4x = 4 &times; 4 = 16</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Area of the larger square = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">= (16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mo>)</mo><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 256</span></p>\n",
                    solution_hi: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2346;&#2361;&#2354;&#2375; &#2357;&#2352;&#2381;&#2327; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; = x, &#2346;&#2352;&#2367;&#2350;&#2366;&#2346; = 4x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2370;&#2360;&#2352;&#2375; &#2357;&#2352;&#2381;&#2327; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; = 4x, &#2346;&#2352;&#2367;&#2350;&#2366;&#2346; = 4 &times; 4x = 16x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">16x - 4x = 48</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2348;&#2337;&#2364;&#2375; &#2357;&#2352;&#2381;&#2327; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; = 4x = 4 &times; 4 = 16</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2348;&#2337;&#2364;&#2375; &#2357;&#2352;&#2381;&#2327; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> = (16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mo>)</mo><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 256</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Palanquin Dark;\"> The area of trapezium is 140&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> and the perpendicular distance between its parallel sides is 7 cm. If the length of one of the parallel sides is 28 cm, what is the length of the other parallel side?</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2360;&#2350;&#2354;&#2306;&#2348; &#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; 140<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math> </span><span style=\"font-family: Palanquin Dark;\"> &#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2325;&#2368; &#2360;&#2350;&#2366;&#2306;&#2340;&#2352; &#2349;&#2369;&#2332;&#2366;&#2323;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2368; &#2354;&#2306;&#2348;&#2357;&#2340; &#2342;&#2370;&#2352;&#2368; 7 cm &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2311;&#2360;&#2325;&#2368; &#2319;&#2325; &#2360;&#2350;&#2366;&#2306;&#2340;&#2352; &#2349;&#2369;&#2332;&#2366; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; 28 cm &#2361;&#2376;, &#2340;&#2379; &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2350;&#2366;&#2306;&#2340;&#2352; &#2349;&#2369;&#2332;&#2366; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2368;?</span></p>\n",
                    options_en: ["<p>12 cm</p>\n", "<p>16 cm</p>\n", 
                                "<p>14 cm</p>\n", "<p>10 cm</p>\n"],
                    options_hi: ["<p>12 cm</p>\n", "<p>16 cm</p>\n",
                                "<p>14 cm</p>\n", "<p>10 cm</p>\n"],
                    solution_en: "<p>11.(a)</p>\r\n<p><span style=\"font-family: Basic;\">Area of trapezium =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Basic;\"> &times; (sum of parallel sides) &times; h</span></p>\r\n<p><span style=\"font-family: Basic;\">140 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Basic;\"> &times; (28 + x) &times; 7</span></p>\r\n<p><span style=\"font-family: Basic;\">40 = 28 + x</span></p>\r\n<p><span style=\"font-family: Basic;\">x = 12</span></p>\n",
                    solution_hi: "<p>11.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2350;&#2354;&#2350;&#2381;&#2348; &#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; (&#2360;&#2350;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352; &#2349;&#2369;&#2332;&#2366;&#2323;&#2306; &#2325;&#2366; &#2351;&#2379;&#2327;) &times; h</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">140 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; (28 + x) &times; 7</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">40 = 28 + x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 12</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Palanquin Dark;\"> Rohan had a cuboidal box having dimensions of 36cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> 25cm<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> 20cm. He packed into it as many cubes as possible, each of which has edges 4cm long. How much space will be still left in this box?</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Palanquin Dark;\">&#2352;&#2379;&#2361;&#2344; &#2325;&#2375; &#2346;&#2366;&#2360; 36 cm x 25cm x 20 cm &#2357;&#2367;&#2350;&#2366;&#2323;&#2306; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; &#2328;&#2344;&#2366;&#2325;&#2366;&#2352; &#2348;&#2377;&#2325;&#2381;&#2360; &#2341;&#2366;&#2404; &#2313;&#2360;&#2344;&#2375; &#2311;&#2360;&#2350;&#2375;&#2306; 4 cm &#2354;&#2306;&#2348;&#2368; &#2349;&#2369;&#2332;&#2366; &#2357;&#2366;&#2354;&#2375; &#2328;&#2344;&#2379;&#2306; &#2325;&#2368; &#2309;&#2343;&#2367;&#2325;&#2340;&#2350; &#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2346;&#2376;&#2325; &#2325;&#2368;&#2404; &#2348;&#2377;&#2325;&#2381;&#2360; &#2350;&#2375;&#2306; &#2347;&#2367;&#2352; &#2349;&#2368; &#2325;&#2367;&#2340;&#2344;&#2368; &#2332;&#2327;&#2361; &#2348;&#2330;&#2375;&#2327;&#2368; ?</span></p>\n",
                    options_en: ["<p>680 <span style=\"font-family: Basic;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\n", "<p>780&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", 
                                "<p>820&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", "<p>720&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n"],
                    options_hi: ["<p>680&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", "<p>780&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n",
                                "<p>820&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", "<p>720&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n"],
                    solution_en: "<p>12.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Volume of cuboid = 36&times;25&times;20 = 18000&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Volume of one cube = 4&times;4&times;4 = 64&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total no of possible cubes = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 9&times;6&times;5= 270</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total volume of all cubes = 64 &times;270 = 17280</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So space left in this box = 18000 - 17280 = 720&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\n",
                    solution_hi: "<p>12.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2328;&#2344;&#2366;&#2349; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; = 36&times;25&times;20 = 18000&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2328;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; = 4&times;4&times;4 = 64&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340; &#2328;&#2344;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 9&times;6&times;5= 270</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2349;&#2368; &#2328;&#2344;&#2379;&#2306; &#2325;&#2366; &#2325;&#2369;&#2354; &#2310;&#2351;&#2340;&#2344; = 64 &times;270 = 17280</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;&#2307; &#2311;&#2360; &#2337;&#2367;&#2348;&#2381;&#2348;&#2375; &#2350;&#2375;&#2306; &#2348;&#2330;&#2368; &#2332;&#2327;&#2361; = 18000 - 17280 = 720&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>3</mn></msup></math></span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Palanquin Dark;\"> Two cylinders have the same volume, but the radius of the base of the second cylinder is 20% less than the radius of the base of the first. How much greater should the height of the second cylinder be in comparison to the height of the first?</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Palanquin Dark;\">&#2342;&#2379; &#2348;&#2375;&#2354;&#2344;&#2379;&#2306; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2376;, &#2354;&#2375;&#2325;&#2367;&#2344; &#2342;&#2370;&#2360;&#2352;&#2375; &#2348;&#2375;&#2354;&#2344; &#2325;&#2368; &#2310;&#2343;&#2366;&#2352; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;, &#2346;&#2361;&#2354;&#2375; &#2348;&#2375;&#2354;&#2344; &#2325;&#2368; &#2310;&#2343;&#2366;&#2352; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; &#2360;&#2375; 20% &#2325;&#2350; &#2361;&#2376;&#2404; &#2346;&#2361;&#2354;&#2375; &#2348;&#2375;&#2354;&#2344; &#2325;&#2368; &#2314;&#2306;&#2330;&#2366;&#2312; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2342;&#2370;&#2360;&#2352;&#2375; &#2348;&#2375;&#2354;&#2344; &#2325;&#2368; &#2314;&#2306;&#2330;&#2366;&#2312; &#2325;&#2367;&#2340;&#2344;&#2368; &#2309;&#2343;&#2367;&#2325; &#2361;&#2379;&#2344;&#2368; &#2330;&#2366;&#2361;&#2367;&#2319;?</span></p>\n",
                    options_en: ["<p>55.25%</p>\n", "<p>56.25%</p>\n", 
                                "<p>56.75%</p>\n", "<p>55.75%</p>\n"],
                    options_hi: ["<p>55.25%</p>\n", "<p>56.25%</p>\n",
                                "<p>56.75%</p>\n", "<p>55.75%</p>\n"],
                    solution_en: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the height of the first cylinder and second cylinder be &lsquo;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>1</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">&rsquo; and &lsquo;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>2</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">&rsquo; respectively.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Given that, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>1</mn></msub><mo>:</mo><msub><mi>r</mi><mn>2</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 5 : 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">ATQ, Volume of first cylinder = Volume of second cylinder</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mi>&pi;</mi><msup><msub><mi>r</mi><mn>1</mn></msub><mn>2</mn></msup><msub><mi>h</mi><mn>1</mn></msub><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>&pi;</mi><msup><msub><mi>r</mi><mn>2</mn></msub><mn>2</mn></msup><msub><mi>h</mi><mn>2</mn></msub></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><msub><mi>h</mi><mn>1</mn></msub><msub><mi>h</mi><mn>2</mn></msub></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>16</mn><mn>25</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, The required percentage =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>16</mn></mrow><mn>16</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 100% = 56.25% </span></p>\n",
                    solution_hi: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2346;&#2361;&#2354;&#2375; &#2348;&#2375;&#2354;&#2344; &#2324;&#2352; &#2342;&#2370;&#2360;&#2352;&#2375; &#2348;&#2375;&#2354;&#2344; &#2325;&#2368; &#2314;&#2305;&#2330;&#2366;&#2312; &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; \'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>1</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">\' &#2324;&#2352; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>h</mi><mn>2</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">\' &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2351;&#2366; &#2361;&#2376;, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>r</mi><mn>1</mn></msub><mo>:</mo><msub><mi>r</mi><mn>2</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 5: 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, &#2346;&#2361;&#2354;&#2375; &#2348;&#2375;&#2354;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; = &#2342;&#2370;&#2360;&#2352;&#2375; &#2348;&#2375;&#2354;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mi>&pi;</mi><msup><msub><mi>r</mi><mn>1</mn></msub><mn>2</mn></msup><msub><mi>h</mi><mn>1</mn></msub><mo>=</mo><mi>&pi;</mi><msup><msub><mi>r</mi><mn>2</mn></msub><mn>2</mn></msup><msub><mi>h</mi><mn>2</mn></msub></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><msub><mi>h</mi><mn>1</mn></msub><msub><mi>h</mi><mn>2</mn></msub></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>16</mn><mn>25</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2379;, &#2309;&#2349;&#2368;&#2359;&#2381;&#2335; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>16</mn></mrow><mn>16</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 100% = 56.25% </span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Palanquin Dark;\"> Find the radius of a sphere having a surface area of 900<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>cm</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">.</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Palanquin Dark;\">900<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>cm</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2357;&#2366;&#2354;&#2375; &#2327;&#2379;&#2354;&#2375; &#2325;&#2368; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>18 cm</p>\n", "<p>20 cm</p>\n", 
                                "<p>15 cm</p>\n", "<p>12 cm</p>\n"],
                    options_hi: ["<p>18 cm</p>\n", "<p>20 cm</p>\n",
                                "<p>15 cm</p>\n", "<p>12 cm</p>\n"],
                    solution_en: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Surface area of the sphere = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&pi;r</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> = 900<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&pi;</mi><mo>&nbsp;</mo><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>r = 15 cm </span></p>\n",
                    solution_hi: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2327;&#2379;&#2354;&#2375; &#2325;&#2366; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&pi;r</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\"> = 900<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>r = 15 &#2360;&#2375;&#2350;&#2368;</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15.<span style=\"font-family: Palanquin Dark;\"> The ratio of the length, width and height of a cuboid is 60 : 24 : 7 and the length of each of its four space diagonals is 130 cm. What is the volume (in </span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">) of the cuboid?</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Palanquin Dark;\">&#2325;&#2367;&#2360;&#2368; &#2328;&#2344;&#2366;&#2349; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312;, &#2330;&#2380;&#2337;&#2364;&#2366;&#2312; &#2324;&#2352; &#2314;&#2306;&#2330;&#2366;&#2312; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; 60: 24: 7 &#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2325;&#2375; &#2330;&#2366;&#2352; &#2310;&#2349;&#2381;&#2351;&#2366;&#2306;&#2340;&#2352; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339;&#2380; (space diagonals) &#2350;&#2375;&#2306; &#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; 130 cm &#2361;&#2376;&#2404; &#2328;&#2344;&#2366;&#2349; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; ( </span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span><span style=\"font-family: Palanquin Dark;\">&#2350;&#2375;&#2306;) &#2325;&#2367;&#2340;&#2344;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>86400</p>\n", "<p>80640</p>\n", 
                                "<p>80460</p>\n", "<p>84600</p>\n"],
                    options_hi: ["<p>86400</p>\n", "<p>80640</p>\n",
                                "<p>80460</p>\n", "<p>84600</p>\n"],
                    solution_en: "<p>15.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the length, width and height of the cuboid be 60x , 24x and 7x.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>60</mn><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>24</mn><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>7</mn><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><mn>130</mn></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3600</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>576</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>49</mn><msup><mi>x</mi><mn>2</mn></msup></msqrt><mo>=</mo><mn>130</mn></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4225</mn><msup><mi>x</mi><mn>2</mn></msup><mo>=</mo><mn>16900</mn><mo>&rArr;</mo><mi>x</mi><mo>=</mo><msqrt><mfrac><mn>16900</mn><mn>4225</mn></mfrac></msqrt><mo>=</mo><mfrac><mn>130</mn><mn>65</mn></mfrac><mo>=</mo><mn>2</mn><mo>&nbsp;</mo><mi>c</mi><mi>m</mi></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Volume of cuboid=l &times; b &times; h =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>60</mn><mi>x</mi><mo>)</mo><mo>&times;</mo><mo>(</mo><mn>24</mn><mi>x</mi><mo>)</mo><mo>&times;</mo><mo>(</mo><mn>7</mn><mi>x</mi><mo>)</mo><mo>=</mo><mn>10080</mn><mo>&times;</mo><msup><mn>2</mn><mn>3</mn></msup><mo>=</mo><mn>80640</mn></math> </span></p>\n",
                    solution_hi: "<p>15.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2328;&#2344;&#2366;&#2349; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312;, &#2330;&#2380;&#2337;&#2364;&#2366;&#2312; &#2324;&#2352; &#2314;&#2306;&#2330;&#2366;&#2312; 60x, 24x &#2324;&#2352; 7x &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>60</mn><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>24</mn><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>7</mn><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><mn>130</mn></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3600</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>576</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>49</mn><msup><mi>x</mi><mn>2</mn></msup></msqrt><mo>=</mo><mn>130</mn></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4225</mn><msup><mi>x</mi><mn>2</mn></msup><mo>=</mo><mn>16900</mn><mo>&rArr;</mo><mi>x</mi><mo>=</mo><msqrt><mfrac><mn>16900</mn><mn>4225</mn></mfrac></msqrt><mo>=</mo><mfrac><mn>130</mn><mn>65</mn></mfrac><mo>=</mo><mn>2</mn><mo>&nbsp;</mo><mi>c</mi><mi>m</mi></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2328;&#2344;&#2366;&#2349; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344;= l &times; b &times; h =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>60</mn><mi>x</mi><mo>)</mo><mo>&times;</mo><mo>(</mo><mn>24</mn><mi>x</mi><mo>)</mo><mo>&times;</mo><mo>(</mo><mn>7</mn><mi>x</mi><mo>)</mo><mo>=</mo><mn>10080</mn><mo>&times;</mo><msup><mn>2</mn><mn>3</mn></msup><mo>=</mo><mn>80640</mn></math> </span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>