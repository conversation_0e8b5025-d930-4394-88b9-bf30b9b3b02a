<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">22:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 22 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-weight: 400;\">&nbsp;If x+y+z=19, </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">=133</span><span style=\"font-weight: 400;\"> and xz=</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">, then the difference between z and x is :</span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">1. <span style=\"font-weight: 400;\">यदि x+y+z=19, </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">=133</span><span style=\"font-weight: 400;\"> तथा xz=</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\"> है, तो z और x के बीच क्या अंतर है ?</span></span></p>",
                    options_en: ["<p>5</p>", "<p>3</p>", 
                                "<p>6</p>", "<p>4</p>"],
                    options_hi: ["<p>5</p>", "<p>3</p>",
                                "<p>6</p>", "<p>4</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-weight: 400;\">We know that&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">=</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><msup><mi>z</mi><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">+2(xy+yz+zx)</span></p>\r\n<p><span style=\"font-weight: 400;\">Put the given values</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>19</mn><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">= 133 + 2(xy + yz + </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">)</span></p>\r\n<p><span style=\"font-weight: 400;\">114= y(x+y+z)</span></p>\r\n<p><span style=\"font-weight: 400;\">y = 6</span></p>\r\n<p><span style=\"font-weight: 400;\">x+z = 19-6 = 13</span></p>\r\n<p><span style=\"font-weight: 400;\">xz = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">= 36</span></p>\r\n<p><span style=\"font-weight: 400;\">Only 9 and 4 are such pairs whose sum is 13 and multiplication is 36</span></p>\r\n<p><span style=\"font-weight: 400;\">Required difference = 9-4 = 5</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) </span></p>\r\n<p>व्याख्या:</p>\r\n<p>हम जानते हैं कि</p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">=</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><msup><mi>z</mi><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">+2(xy+yz+zx)</span></p>\r\n<p>दिए गए मान रखने पर ,</p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>19</mn><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">= 133 + 2(xy + yz + </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">)</span></p>\r\n<p><span style=\"font-weight: 400;\">114= y(x+y+z)</span></p>\r\n<p><span style=\"font-weight: 400;\">y = 6</span></p>\r\n<p><span style=\"font-weight: 400;\">x+z = 19-6 = 13</span></p>\r\n<p><span style=\"font-weight: 400;\">xz = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">= 36</span></p>\r\n<p>केवल 9 और 4 ऐसे जोड़े हैं जिनका योग 13 और गुणनफल 36 है</p>\r\n<p><span style=\"font-weight: 400;\">आवश्यक अंतर= 9-4 = 5</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The length of the metallic pipe is 14.70 m. Its external and internal radii are 3.5 cm and 2.5 cm respectively. If 1 cubic cm of the metal weigh 6.55 gm, then the weight of the pipe is : ( Take<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#120587;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">2. एक धात्विक पाइप की लंबाई 14.70 मीटर है | इसकी बाहरी एवं आतंरिक त्रिज्याएँ क्रमशः 3.5 सेमी और 2.5 सेमी की हैं | यदि एक घन सेमी धातु का वज़न 6.55 ग्राम है, तो इस पाइप का वज़न ज्ञात करें |&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&#160;</mo><mo>&#120587;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>)</mo></math></span></p>",
                    options_en: ["<p>181.566 kg</p>", "<p>181.166 kg</p>", 
                                "<p>181.966 kg</p>", "<p>181.466 kg</p>"],
                    options_hi: ["<p>181.566 kg</p>", "<p>181.166 kg</p>",
                                "<p>181.966 kg</p>", "<p>181.466 kg</p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Volume of a cylinder=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&#960;</mi><mo>(</mo><msup><mi>R</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo><mi>h</mi></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Where R = external radius</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">r = internal radius</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h = height or length of the cylinder</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Here, volume = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac><msup><mrow><mo>&#215;</mo><mo>(</mo><msup><mn>3.5</mn><mn>2</mn></msup><mo>-</mo><mn>2.5</mn></mrow><mn>2</mn></msup><mo>)</mo><mo>&#215;</mo><mn>1470</mn></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>22</mn><mo>&#215;</mo><mo>(</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo><mo>(</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo><mo>&#215;</mo><mn>210</mn><mo>=</mo><mn>22</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>1</mn><mo>&#215;</mo><mn>210</mn><mo>=</mo><mn>27720</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total Weight of pipe=Volume of pipe &times; </span><span style=\"font-family: Times New Roman;\">weight of metal=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27720</mn><mo>&#215;</mo><mn>6</mn><mo>.</mo><mn>55</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 181566 g or 181.566 kg</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>व्याख्या:</p>\r\n<p><span style=\"font-family: Times New Roman;\">पाइप का आयतन&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&#960;</mi><mo>(</mo><msup><mi>R</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup><mo>)</mo><mi>h</mi></math> </span></p>\r\n<p>जहां R = बाहरी त्रिज्या</p>\r\n<p>r = आंतरिक त्रिज्या</p>\r\n<p>h = बेलन की ऊँचाई या लंबाई</p>\r\n<p><span style=\"font-family: Times New Roman;\">यहाँ, आयतन&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac><msup><mrow><mo>&#215;</mo><mo>(</mo><msup><mn>3.5</mn><mn>2</mn></msup><mo>-</mo><mn>2.5</mn></mrow><mn>2</mn></msup><mo>)</mo><mo>&#215;</mo><mn>1470</mn></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>22</mn><mo>&#215;</mo><mo>(</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo><mo>(</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo><mo>&#215;</mo><mn>210</mn><mo>=</mo><mn>22</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>1</mn><mo>&#215;</mo><mn>210</mn><mo>=</mo><mn>27720</mn><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">पाइप का कुल वजन = पाइप का आयतन&nbsp; &times; धातु का वजन =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>27720</mn><mo>&#215;</mo><mn>6</mn><mo>.</mo><mn>55</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 181566 g or 181.566 kg</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A, B and C started walking from a point. Their steps measure 64 cm, 88 cm, and 112 cm respectively. What is the minimum distance they should walk so that each takes an exact number of steps?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">3. A, B और C ने एक बिंदु से चलना शुरू किया | उनके क़दमों की माप क्रमशः 64 सेमी, 88 सेमी और 112 सेमी है | उन्हें कम से कम कितनी दूरी तय करनी चाहिए ताकि प्रत्येक के क़दमों की संख्या बराबर हो ? </span></p>",
                    options_en: ["<p>24.24 m</p>", "<p>48.28 m</p>", 
                                "<p>24.94 m</p>", "<p>49.28 m</p>"],
                    options_hi: ["<p>24.24 m</p>", "<p>48.28 m</p>",
                                "<p>24.94 m</p>", "<p>49.28 m</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>112</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>88</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>11</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>64</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>R</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&#160;</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>tan</mi><mi>c</mi><mi>e</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi>L</mi><mi>C</mi><mi>M</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>112</mn><mo>,</mo><mn>88</mn><mo>&#160;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mo>&#215;</mo><mn>211</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mn>4928</mn><mo>&#160;</mo><mi>c</mi><mi>m</mi><mo>&#160;</mo><mi>o</mi><mi>r</mi><mo>&#160;</mo><mn>49</mn><mo>.</mo><mn>28</mn><mi>m</mi><mo>.</mo></math></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p>व्याख्या:</p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>112</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>88</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>11</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>64</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi>L</mi><mi>C</mi><mi>M</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>112</mn><mo>,</mo><mn>88</mn><mo>&#160;</mo><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mo>&#215;</mo><mn>211</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mn>4928</mn><mo>&#160;</mo><mi>c</mi><mi>m</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2351;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>49</mn><mo>.</mo><mn>28</mn><mi>m</mi><mo>.</mo></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The price of sugar has increased by 25%. A person wants to increase its expenditure by 40% only. By what percent should he increase his consumption?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">4. चीनी की कीमत 25% बढ़ गयी है | एक व्यक्ति अपने व्यय में केवल 40% की वृद्धि करना चाहता है | उसे अपनी खपत में कितने प्रतिशत की वृद्धि करनी चाहिए ? </span></p>",
                    options_en: ["<p>10</p>", "<p>11</p>", 
                                "<p>12</p>", "<p>18</p>"],
                    options_hi: ["<p>10</p>", "<p>11</p>",
                                "<p>12</p>", "<p>18</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Price&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> Consumption = Expenditure</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">25% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">and 40% =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Initial : New</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Price&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Times New Roman;\"> 4 : 5 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Expenditure&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 : 7 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Consumption&nbsp; &nbsp; 5/4 : 7/5 = 25 : 28</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">%age increase in consumption =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>28</mn><mo>-</mo><mn>25</mn></mrow><mn>25</mn></mfrac><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> 100 = 12%&nbsp;</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p>व्याख्या:</p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;व्यय = खपत &times; मूल्य</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">25% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>और </span><span style=\"font-family: Times New Roman;\">&nbsp;40% =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;प्रारंभिक : नया</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">मूल्य&nbsp; &nbsp; &nbsp; &nbsp;=&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4 : 5 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">व्यय&nbsp; &nbsp; &nbsp; &nbsp;=&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 : 7 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">खपत&nbsp; &nbsp; &nbsp; =&nbsp; &nbsp;5/4 : 7/5 = 25 : 28</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">खपत में % आयु वृद्धि =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>28</mn><mo>-</mo><mn>25</mn></mrow><mn>25</mn></mfrac><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> 100 = 12%&nbsp;</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Times New Roman;\">In △ ABC</span><span style=\"font-family: Gungsuh;\"> , D is a point on AC such that AB = BD = DC. If &ang;BAD = 50&deg;, then the measure of &ang;B is : </span></p>",
                    question_hi: "<p><span style=\"font-family: Arial Unicode MS;\">5. △ ABC में, D, AC पर स्थित एक ऐसा बिंदु है कि AB = BD = DC है | यदि &ang;BAD = 50&deg; है, तो कोण B का मान है : </span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>115</mn><mo>&#176;</mo></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>100</mn><mo>&#176;</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>112</mn><mo>&#176;</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>105</mn><mo>&#176;</mo></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>115</mn><mo>&#176;</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>100</mn><mo>&#176;</mo></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>112</mn><mo>&#176;</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>105</mn><mo>&#176;</mo></math></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image1.png\" /></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>I</mi><mi>n</mi><mo>&#160;</mo><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>D</mi><mo>,</mo><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>D</mi><mo>=</mo><mo>&#8736;</mo><mi>B</mi><mi>D</mi><mi>A</mi><mo>=</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mi>A</mi><mi>B</mi><mo>=</mo><mi>B</mi><mi>D</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&#8736;</mo><mi>A</mi><mi>B</mi><mi>D</mi><mo>=</mo><msup><mn>180</mn><mo>&#8728;</mo></msup><mo>-</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mo>-</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mo>=</mo><msup><mn>80</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&#160;</mo><mi>&#916;</mi><mi>B</mi><mi>D</mi><mi>C</mi><mo>,</mo><mo>&#8736;</mo><mi>B</mi><mi>D</mi><mi>C</mi><mo>=</mo><msup><mn>180</mn><mo>&#8728;</mo></msup><mo>-</mo><mo>&#8736;</mo><mi>B</mi><mi>D</mi><mi>A</mi><mo>=</mo><msup><mn>180</mn><mo>&#8728;</mo></msup><mo>-</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mo>=</mo><msup><mn>130</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"></mspace><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>=</mo><mo>&#8736;</mo><mi>D</mi><mi>C</mi><mi>B</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mi>B</mi><mi>D</mi><mo>=</mo><mi>D</mi><mi>C</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>+</mo><mo>&#8736;</mo><mi>D</mi><mi>C</mi><mi>B</mi><mo>=</mo><msup><mn>180</mn><mo>&#8728;</mo></msup><mo>-</mo><msup><mn>130</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"></mspace><mn>2</mn><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>=</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"></mspace><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>=</mo><msup><mn>25</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"></mspace><mo>&#8736;</mo><mi>A</mi><mi>B</mi><mi>C</mi><mo>=</mo><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>+</mo><mo>&#8736;</mo><mi>A</mi><mi>B</mi><mi>D</mi><mo>=</mo><msup><mn>25</mn><mo>&#8728;</mo></msup><mo>+</mo><msup><mn>80</mn><mo>&#8728;</mo></msup><mo>=</mo><msup><mn>105</mn><mo>&#8728;</mo></msup></math></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p>व्याख्या</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image1.png\" /></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>D</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>&#160;</mo><mo>,</mo><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>D</mi><mo>=</mo><mo>&#8736;</mo><mi>B</mi><mi>D</mi><mi>A</mi><mo>=</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mi>A</mi><mi>B</mi><mo>=</mo><mi>B</mi><mi>D</mi><mo>)</mo><mspace linebreak=\"newline\"/><mo>&#8736;</mo><mi>A</mi><mi>B</mi><mi>D</mi><mo>=</mo><msup><mn>180</mn><mo>&#8728;</mo></msup><mo>-</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mo>-</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mo>=</mo><msup><mn>80</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"/><mi>&#916;</mi><mi>B</mi><mi>D</mi><mi>C</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>&#160;</mo><mo>,</mo><mo>&#8736;</mo><mi>B</mi><mi>D</mi><mi>C</mi><mo>=</mo><msup><mn>180</mn><mo>&#8728;</mo></msup><mo>-</mo><mo>&#8736;</mo><mi>B</mi><mi>D</mi><mi>A</mi><mo>=</mo><msup><mn>180</mn><mo>&#8728;</mo></msup><mo>-</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mo>=</mo><msup><mn>130</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"/><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>=</mo><mo>&#8736;</mo><mi>D</mi><mi>C</mi><mi>B</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mi>B</mi><mi>D</mi><mo>=</mo><mi>D</mi><mi>C</mi><mo>)</mo><mspace linebreak=\"newline\"/><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>+</mo><mo>&#8736;</mo><mi>D</mi><mi>C</mi><mi>B</mi><mo>=</mo><msup><mn>180</mn><mo>&#8728;</mo></msup><mo>-</mo><msup><mn>130</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"/><mn>2</mn><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>=</mo><msup><mn>50</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"/><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>=</mo><msup><mn>25</mn><mo>&#8728;</mo></msup><mspace linebreak=\"newline\"/><mo>&#8736;</mo><mi>A</mi><mi>B</mi><mi>C</mi><mo>=</mo><mo>&#8736;</mo><mi>D</mi><mi>B</mi><mi>C</mi><mo>+</mo><mo>&#8736;</mo><mi>A</mi><mi>B</mi><mi>D</mi><mo>=</mo><msup><mn>25</mn><mo>&#8728;</mo></msup><mo>+</mo><msup><mn>80</mn><mo>&#8728;</mo></msup><mo>=</mo><msup><mn>105</mn><mo>&#8728;</mo></msup></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Rs. 12500 is distributed among A, B and C such that they receive notes of Rs. 500, Rs. 2000 and Rs. 100 respectively. The amounts received by them are in the ratio of 10:5:15. What was the ratio of the numbers of notes of Rs. 500, Rs 2000 and Rs 100 ?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">6. 12500 रुपये की राशि का वितरण A, B और C में इस प्रकार किया जाता है कि उन्हें क्रमशः 500 रुपये, 2000 रुपये और 100 रुपये के नोट प्राप्त होते हैं | उनके द्वारा प्राप्त की गयी 10 : 5 : 15 के अनुपात में है | 500 रुपये, 2000 रुपये और 100 रुपये के नोटों की संख्या का अनुपात क्या है ? </span></p>",
                    options_en: ["<p>8:1:60</p>", "<p>8:5:60</p>", 
                                "<p>4:1:60</p>", "<p>10:1:60</p>"],
                    options_hi: ["<p>8:1:60</p>", "<p>8:5:60</p>",
                                "<p>4:1:60</p>", "<p>10:1:60</p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Ratio of amount received = 10:5:15</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Ratio of note&rsquo;s worth = 500 : 2000: 100 = 5 : 20 : 1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Number of notes =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>a</mi><mi>m</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi></mrow><mrow><mi>n</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>w</mi><mi>o</mi><mi>r</mi><mi>t</mi><mi>h</mi></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Ratio of number of notes =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac><mo>:</mo><mfrac><mn>5</mn><mn>20</mn></mfrac><mo>:</mo><mfrac><mn>15</mn><mn>1</mn></mfrac><mo>=</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>40</mn><mi>&#160;</mi><mo>:</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>:</mo><mn>300</mn></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>60</mn></math></span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>व्याख्या:-</p>\r\n<p><span style=\"font-family: Times New Roman;\">प्राप्त राशि का अनुपात = 10:5:15</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">नोट के मूल्य का अनुपात= 500 : 2000: 100 = 5 : 20 : 1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">नोटों की संख्या =<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2352;&#2366;&#2358;&#2367;</mi></mrow><mrow><mi>&#2344;&#2379;&#2335;&#2379;&#2306;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2368;&#2350;&#2340;</mi><mo>&#160;</mo></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">नोटों की संख्या का अनुपात =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac><mo>:</mo><mfrac><mn>5</mn><mn>20</mn></mfrac><mo>:</mo><mfrac><mn>15</mn><mn>1</mn></mfrac><mo>=</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>40</mn><mi>&#160;</mi><mo>:</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>:</mo><mn>300</mn></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>60</mn></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: " <p>7. A circle is inscribed in a quadrilateral touching sides AB , BC , CD and AD at the points P , Q , R and S respectively. If BP = 9cm, SD = 13cm and BC = 17cm, then the length of DC is: ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">7. एक वृत्त किसी चतुर्भुज के भीतर है जो </span><span style=\"font-family: Baloo;\">इसकी</span><span style=\"font-family: Baloo;\"> भुजाओं AB, BC, CD और AD को क्रमशः बिंदु P, Q, R और S पर स्पर्श करता है | यदि BP = 9 सेमी, SD = 13 सेमी और BC = 17 सेमी है, तो DC की लंबाई है : </span></p>",
                    options_en: [" <p> 15</span></p>", " <p> 20</span></p>", 
                                " <p> 12</span></p>", " <p> 21</span></p>"],
                    options_hi: ["<p>15</p>", "<p>20</p>",
                                "<p>12</p>", "<p>21</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d)</span></p> <p><span style=\"font-family:Times New Roman\">Explanation:</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image2.png\"/></p> <p><span style=\"font-family:Times New Roman\">Given, SD = 13 cm, BP = 9 cm </span></p> <p><span style=\"font-family:Times New Roman\">SD = DR = 13 cm</span><span style=\"font-family:Times New Roman\">(Tangent of same circle)</span></p> <p><span style=\"font-family:Times New Roman\">BP = BQ = 9 cm</span><span style=\"font-family:Times New Roman\">(Tangent of same circle)</span></p> <p><span style=\"font-family:Times New Roman\">QC = BC - BQ = 17-9 = 8 cm</span></p> <p><span style=\"font-family:Times New Roman\">QC = RC = 8 cm</span><span style=\"font-family:Times New Roman\">(Tangent of same circle)</span></p> <p><span style=\"font-family:Times New Roman\">DC = DR+RC = 13+8 = 21 cm</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p>व्याख्या:-</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image2.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">दिया है , SD = 13 cm, BP = 9 cm </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SD = DR = 13 cm</span><span style=\"font-family: Times New Roman;\">(समान वृत्त की स्पर्श रेखा)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">BP = BQ = 9 cm</span><span style=\"font-family: Times New Roman;\">(समान वृत्त की स्पर्श रेखा)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">QC = RC = 8 cm</span><span style=\"font-family: Times New Roman;\">(समान वृत्त की स्पर्श रेखा)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">DC = DR+RC = 13+8 = 21 cm</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. AB is a diameter of a circle with centre O. CB is a tangent to the circle at B. AC intersects the circle at G. If the radius of the circle is 10 cm and AG = 16 cm, then the length of BC is :</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">8. AB एक वृत्त का व्यास है जिसका केंद्र O है | CB बिंदु B पर इस वृत्त की एक स्पर्श रेखा है | AC वृत्त को G पर काटता है | यदि वृत्त की त्रिज्या 10 सेमी है तथा AG = 16 सेमी है, तो BC की लंबाई ज्ञात करें | </span></p>",
                    options_en: ["<p>10</p>", "<p>12</p>", 
                                "<p>15</p>", "<p>8</p>"],
                    options_hi: ["<p>10</p>", "<p>12</p>",
                                "<p>15</p>", "<p>8</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image3.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">AGB</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AG = 16 cm and AB = 20 cm </span><span style=\"font-family: Times New Roman;\">(given)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8736;</mo><mi>A</mi><mi>G</mi><mi>B</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>90</mn><mo>&#176;</mo></math> = </span><span style=\"font-family: Times New Roman;\">(Angle made by Diagonal)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mrow><mi>B</mi><mi>G</mi></mrow><mn>2</mn></msup><mo>=</mo><msqrt><msup><mrow><mi>A</mi><mi>B</mi></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mi>A</mi><mi>G</mi></mrow><mn>2</mn></msup></msqrt></math></span><span style=\"font-family: Times New Roman;\">&hellip;.(Pythagoras Theorem)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>20</mn><mn>2</mn></msup><mo>-</mo><msup><mn>16</mn><mn>2</mn></msup></msqrt></math>=&nbsp;12</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">ABG and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">ABC</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>G</mi><mo>=</mo><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>C</mi><mo>&#160;</mo><mo>(</mo><mi>s</mi><mi>a</mi><mi>m</mi><mi>e</mi><mo>&#160;</mo><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&#8736;</mo><mi>B</mi><mi>G</mi><mi>A</mi><mo>=</mo><mo>&#8736;</mo><mi>A</mi><mi>B</mi><mi>C</mi><mo>&#160;</mo><mo>(</mo><mi>B</mi><mi>o</mi><mi>t</mi><mi>h</mi><mo>&#160;</mo><msup><mn>90</mn><mo>&#8728;</mo></msup><mo>)</mo></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>G</mi><mo>&#160;</mo><mo>~</mo><mo>&#160;</mo><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mfrac><mrow><mi>B</mi><mi>G</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mi>A</mi><mi>G</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mfrac><mn>12</mn><mrow><mi>B</mi><mi>C</mi></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>16</mn><mn>20</mn></mfrac><mo>&#160;</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>B</mi><mi>C</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>15</mn><mo>&#160;</mo><mi>c</mi><mi>m</mi></math></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p>व्याख्या:</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image3.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">AGB</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AG = 16 cm और&nbsp; AB = 20 cm </span><span style=\"font-family: Times New Roman;\">(दिया है )</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8736;</mo><mi>A</mi><mi>G</mi><mi>B</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>90</mn><mo>&#176;</mo></math> = </span><span style=\"font-family: Times New Roman;\">(विकर्ण द्वारा बनाया गया कोण)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mrow><mi>B</mi><mi>G</mi></mrow><mn>2</mn></msup><mo>=</mo><msqrt><msup><mrow><mi>A</mi><mi>B</mi></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mi>A</mi><mi>G</mi></mrow><mn>2</mn></msup></msqrt></math></span><span style=\"font-family: Times New Roman;\">&hellip;.(पाइथागोरस प्रमेय)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>20</mn><mn>2</mn></msup><mo>-</mo><msup><mn>16</mn><mn>2</mn></msup></msqrt></math>=&nbsp;12</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">ABG और&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">ABC में&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>G</mi><mo>=</mo><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>C</mi><mo>&#160;</mo><mo>(</mo><mi>&#2360;&#2350;&#2366;&#2344;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2379;&#2339;</mi><mo>)</mo><mspace linebreak=\"newline\"/><mo>&#8736;</mo><mi>B</mi><mi>G</mi><mi>A</mi><mo>=</mo><mo>&#8736;</mo><mi>A</mi><mi>B</mi><mi>C</mi><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2342;&#2379;&#2344;&#2379;&#2306;</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mn>90</mn><mo>&#8728;</mo></msup><mo>)</mo></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>G</mi><mo>&#160;</mo><mo>~</mo><mo>&#160;</mo><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mfrac><mrow><mi>B</mi><mi>G</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mi>A</mi><mi>G</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mfrac><mn>12</mn><mrow><mi>B</mi><mi>C</mi></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>16</mn><mn>20</mn></mfrac><mo>&#160;</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>B</mi><mi>C</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>15</mn><mo>&#160;</mo><mi>c</mi><mi>m</mi></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>A</mi><mo>&#8201;</mo><mo>+</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mo>+</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>A</mi></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>3</mn></msup><mi>A</mi><mo>+</mo><mn>3</mn></math><span style=\"font-family: Times New Roman;\">,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#60;</mo><mo>&#160;</mo><mi>A</mi><mo>&#160;</mo><mo>&#60;</mo><mo>&#160;</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo></math> , </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">then the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>tan</mi><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos</mi><mi>A</mi><mo>)</mo><mo>&#160;</mo></math>is : </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">9. यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>A</mi><mo>&#8201;</mo><mo>+</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mo>+</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>A</mi><mo>=</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>3</mn></msup><mi>A</mi><mo>+</mo><mn>3</mn><mo>,</mo><mo>&#160;</mo><mn>0</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#60;</mo><mo>&#160;</mo><mi>A</mi><mo>&#160;</mo><mo>&#60;</mo><mo>&#160;</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&nbsp;है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>tan</mi><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos</mi><mi>A</mi><mo>)</mo><mo>&#160;</mo></math> का मान ज्ञात करें | </span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>3</mn></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>2</mn></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>3</mn></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>2</mn></math></p>"],
                    solution_en: "<p>(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>A</mi><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mo>+</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>A</mi><mo>=</mo><mo>&#160;</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>3</mn></msup><mi>A</mi><mo>+</mo><mn>3</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore A = 45&deg; will satisfy the above equation.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><mfrac><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mrow><mn>1</mn><mo>-</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow></mfrac><mo>+</mo><mfrac><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow></mfrac><mo>+</mo><mn>2</mn><mo>=</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>3</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac><mo>+</mo><mn>2</mn></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>3</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>1</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>-</mo><mn>1</mn><mo>+</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>=</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>3</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mfrac><mrow><mo>+</mo><mn>1</mn></mrow><mrow><mo>&#8730;</mo><mn>2</mn></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><mo>&#8730;</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>1</mn></math></p>",
                    solution_hi: "<p>(a)</p>\r\n<p>दोनों ,</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>A</mi><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mo>+</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>A</mi><mo>=</mo><mo>&#160;</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>3</mn></msup><mi>A</mi><mo>+</mo><mn>3</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">अतः A = 45&deg; उपरोक्त समीकरण को संतुष्ट करेगा।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><mfrac><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mrow><mn>1</mn><mo>-</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow></mfrac><mo>+</mo><mfrac><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow></mfrac><mo>+</mo><mn>2</mn><mo>=</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>3</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac><mo>+</mo><mn>2</mn></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>3</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>1</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>-</mo><mn>1</mn><mo>+</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>=</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>3</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>+</mo><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mfrac><mrow><mo>+</mo><mn>1</mn></mrow><mrow><mo>&#8730;</mo><mn>2</mn></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><mo>&#8730;</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn><mo>+</mo><mn>1</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>(</mo><mn>6</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>8</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>9</mn><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mo>)</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">then x = ? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">10. यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>(</mo><mn>6</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>8</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>9</mn><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mo>)</mo><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">&nbsp;है, तो </span><span style=\"font-family: \'Times New Roman\';\">x = ?</span></p>",
                    options_en: ["<p>2/23</p>", "<p>9/23</p>", 
                                "<p>5/23</p>", "<p>3/23</p>"],
                    options_hi: ["<p>2/23</p>", "<p>9/23</p>",
                                "<p>5/23</p>", "<p>3/23</p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>G</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mo>,</mo><msup><mrow><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>=</mo><mn>3</mn><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><msup><mrow><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>W</mi><mi>e</mi><mo>&#160;</mo><mi>k</mi><mi>n</mi><mi>o</mi><mi>w</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>a</mi><mi>t</mi><mo>&#160;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi><mi>c</mi><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"></mspace><mi>O</mi><mi>n</mi><mi>l</mi><mi>y</mi><mo>&#160;</mo><mi>i</mi><mi>f</mi><mo>&#160;</mo><mi>e</mi><mi>i</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>=</mo><mn>0</mn><mo>&#160;</mo><mi>o</mi><mi>r</mi><mo>&#160;</mo><mi>a</mi><mo>=</mo><mi>b</mi><mo>=</mo><mi>c</mi><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>+</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>+</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mn>23</mn><mi>x</mi><mo>=</mo><mn>2</mn><mo>,</mo><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mfrac><mn>2</mn><mn>23</mn></mfrac></math></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>व्याख्या:</p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>&#160;</mo><mo>,</mo><msup><mrow><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>=</mo><mn>3</mn><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><msup><mrow><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>&#2361;&#2350;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2332;&#2366;&#2344;&#2340;&#2375;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2361;&#2376;&#2306;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2367;</mi><mo>&#160;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi><mi>c</mi><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"/><mi>&#2351;&#2342;&#2367;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>=</mo><mn>0</mn><mo>&#160;</mo><mi>o</mi><mi>r</mi><mo>&#160;</mo><mi>a</mi><mo>=</mo><mi>b</mi><mo>=</mo><mi>c</mi><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>)</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>3</mn><mo>+</mo><mn>8</mn><mi>x</mi><mo>-</mo><mn>2</mn><mo>+</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>23</mn><mi>x</mi><mo>=</mo><mn>2</mn><mo>,</mo><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mfrac><mn>2</mn><mn>23</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>cos</mi><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn><mi>sin</mi><mi>A</mi></math>, then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin</mi><mi>A</mi><mo>.</mo><mi>cos</mi><mi>A</mi><mo>+</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>.</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></math></span><span style=\"font-family: Times New Roman;\">? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">11. यदि&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn><mi>sin</mi><mi>A</mi></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin</mi><mi>A</mi><mo>.</mo><mi>cos</mi><mi>A</mi><mo>+</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>.</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></math></span><span style=\"font-family: Baloo;\">&nbsp;का मान क्या होगा ? </span></p>",
                    options_en: ["<p>4.5</p>", "<p>4.9</p>", 
                                "<p>5.1</p>", "<p>5.4</p>"],
                    options_hi: ["<p>4.5</p>", "<p>4.9</p>",
                                "<p>5.1</p>", "<p>5.4</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Given , cosA = 5sinA </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi><mo>=</mo><mfrac><mrow><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>1</mn></mfrac><mo>,</mo><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mi>n</mi><mo>&#160;</mo><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi><mo>=</mo><mo>&#8730;</mo><mo>(</mo><msup><mn>5</mn><mn>2</mn></msup><mo>+</mo><msup><mn>1</mn><mn>2</mn></msup><mo>)</mo><mo>=</mo><mo>&#8730;</mo><mn>26</mn><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mi>sin</mi><mi>A</mi><mo>.</mo><mi>cos</mi><mi>A</mi><mo>+</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>.</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>26</mn></msqrt></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><msqrt><mn>26</mn></msqrt></mfrac><mo>+</mo><msqrt><mn>26</mn></msqrt><mo>&#215;</mo><mfrac><msqrt><mn>26</mn></msqrt><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>26</mn></mfrac><mo>+</mo><mfrac><mn>26</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mrow><mn>25</mn><mo>+</mo><mn>676</mn></mrow><mn>130</mn></mfrac><mo>=</mo><mfrac><mn>701</mn><mn>130</mn></mfrac><mo>=</mo><mn>5</mn><mo>.</mo><mn>39</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>&#160;</mo><mi>a</mi><mi>p</mi><mi>p</mi><mi>r</mi><mi>o</mi><mi>x</mi></math></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p>व्याख्या:-</p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;दिया है , cosA = 5sinA </span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi><mo>=</mo><mfrac><mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>&#160;</mo></mrow><mi>&#2354;&#2350;&#2381;&#2348;</mi></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>1</mn></mfrac><mo>,</mo><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo><mo>=</mo><mo>&#8730;</mo><mo>(</mo><msup><mn>5</mn><mn>2</mn></msup><mo>+</mo><msup><mn>1</mn><mn>2</mn></msup><mo>)</mo><mo>=</mo><mo>&#8730;</mo><mn>26</mn><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mi>sin</mi><mi>A</mi><mo>.</mo><mi>cos</mi><mi>A</mi><mo>+</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>.</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>26</mn></msqrt></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><msqrt><mn>26</mn></msqrt></mfrac><mo>+</mo><msqrt><mn>26</mn></msqrt><mo>&#215;</mo><mfrac><msqrt><mn>26</mn></msqrt><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>26</mn></mfrac><mo>+</mo><mfrac><mn>26</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mrow><mn>25</mn><mo>+</mo><mn>676</mn></mrow><mn>130</mn></mfrac><mo>=</mo><mfrac><mn>701</mn><mn>130</mn></mfrac><mo>=</mo><mn>5</mn><mo>.</mo><mn>39</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>&#160;</mo><mi>&#2354;&#2327;&#2349;&#2327;</mi><mo>&#160;</mo></math></p>\r\n<p>&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>Directions(12-15): The pie-chart below shows the expenses of a person on different items and its savings during the year 2019. Study the chart carefully and answer the following questions:</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image4.png\" width=\"211\" height=\"330\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">Q12. If the total income of a person was 955879, then what is the difference between expenses for education and transport? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">दिशा निर्देश(12-15): यह वृत्त-आरेख एक व्यक्ति के द्वारा वर्ष 2019 में विभिन्न मदों पर किये गए व्यय को एवं उसकी बचत को दर्शाता है | इस आरेख का ध्यानपूर्वक अध्ययन करें और प्रश्नों का उत्तर दें | </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image4.png\" width=\"210\" height=\"328\" /></p>\r\n<p><span style=\"font-family: Baloo;\">12. यदि व्यक्ति की कुल आय 955879 रुपये है, तो शिक्षा एवं परिवहन पर किये गए व्यय में क्या अंतर है ? </span></p>",
                    options_en: ["<p>66911.53</p>", "<p>66911.83</p>", 
                                "<p>66911.13</p>", "<p>66911.93</p>"],
                    options_hi: ["<p>66911.53</p>", "<p>66911.83</p>",
                                "<p>66911.13</p>", "<p>66911.93</p>"],
                    solution_en: "<p>(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">requires difference = (12-5)% of 955879 = Rs. 66911.53</span></p>",
                    solution_hi: "<p>(a)</p>\r\n<p>व्याख्या</p>\r\n<p><span style=\"font-family: Times New Roman;\">आवश्यक&nbsp; अंतर= (12-5)% of 955879 = Rs. 66911.53</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>Directions(12-15): The pie-chart below shows the expenses of a person on different items and its savings during the year 2019. Study the chart carefully and answer the following questions:</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image4.png\" width=\"206\" height=\"322\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">Q13. If the total income of a person is 999999 . Then the savings is what percent of the expenses of clothing, food and house rent together? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">दिशा निर्देश(62-65): यह वृत्त-आरेख एक व्यक्ति के द्वारा वर्ष 2019 में विभिन्न मदों पर किये गए व्यय को एवं उसकी बचत को दर्शाता है | इस आरेख का ध्यानपूर्वक अध्ययन करें और प्रश्नों का उत्तर दें | </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image4.png\" width=\"211\" height=\"330\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">1</span><span style=\"font-family: Baloo;\">3. यदि व्यक्ति की कुल आय 999999 है। </span><span style=\"font-family: Baloo;\">तो उसकी कुल बचत, भोजन, वस्त्र, एवं किराये पर होने वाले कुल व्यय का कितना प्रतिशत है ?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>37</mn><mo>.</mo><mn>13</mn><mo>%</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>37.93</mn><mi>%</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>37.33</mn><mi>%</mi></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>37.73</mn><mi>%</mi></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>37.13</mn><mi>%</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>37.93</mn><mi>%</mi></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>37.33</mn><mi>%</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>37.73</mn><mi>%</mi></math></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Directly take percentage</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>20</mn><mrow><mn>28</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>15</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mn>20</mn><mn>53</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>37</mn><mo>.</mo><mn>73</mn><mo>%</mo></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p>व्याख्या:-</p>\r\n<p>&nbsp;प्रतिशत :</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>20</mn><mrow><mn>28</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>15</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mn>20</mn><mn>53</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>37</mn><mo>.</mo><mn>73</mn><mo>%</mo></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>Directions(12-15): The pie-chart below shows the expenses of a person on different items and its savings during the year 2019. Study the chart carefully and answer the following questions:</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image4.png\" width=\"212\" height=\"332\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">Q14. If total income is 1200001 then find the ratio of sum of three lowest expenditures to sum of two highest expenditures as shown in chart? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">&#2342;&#2367;&#2358;&#2366; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;(12-15): &#2351;&#2361; &#2357;&#2371;&#2340;&#2381;&#2340;-&#2310;&#2352;&#2375;&#2326; &#2319;&#2325; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2325;&#2375; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2357;&#2352;&#2381;&#2359; 2019 &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2350;&#2342;&#2379;&#2306; &#2346;&#2352; &#2325;&#2367;&#2351;&#2375; &#2327;&#2319; &#2357;&#2381;&#2351;&#2351; &#2325;&#2379; &#2319;&#2357;&#2306; &#2313;&#2360;&#2325;&#2368; &#2348;&#2330;&#2340; &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366; &#2361;&#2376; | &#2311;&#2360; &#2310;&#2352;&#2375;&#2326; &#2325;&#2366; &#2343;&#2381;&#2351;&#2366;&#2344;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2379;&#2306; &#2325;&#2366; &#2313;&#2340;&#2381;&#2340;&#2352; &#2342;&#2375;&#2306; | </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image4.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">1</span><span style=\"font-family: Baloo;\">4. &#2351;&#2342;&#2367; &#2325;&#2369;&#2354; &#2310;&#2351; 1200001 &#2361;&#2376;, &#2340;&#2379; &#2340;&#2368;&#2344; &#2360;&#2348;&#2360;&#2375; &#2331;&#2379;&#2335;&#2375; &#2357;&#2381;&#2351;&#2351;&#2379;&#2306; &#2325;&#2375; &#2332;&#2379;&#2396; &#2340;&#2341;&#2366; &#2342;&#2379; &#2360;&#2348;&#2360;&#2375; &#2348;&#2396;&#2375; &#2357;&#2381;&#2351;&#2351;&#2379;&#2306; &#2325;&#2375; &#2332;&#2379;&#2396; &#2350;&#2375;&#2306; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2352;&#2375;&#2306; | </span></p>\n",
                    options_en: ["<p>25:43</p>", "<p>43:25</p>", 
                                "<p>41:25</p>", "<p>25:41</p>"],
                    options_hi: ["<p>25:43</p>\n", "<p>43:25</p>\n",
                                "<p>41:25</p>\n", "<p>25:41</p>\n"],
                    solution_en: "<p>(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>r</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&#160;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mi>c</mi><mi>l</mi><mi>o</mi><mi>t</mi><mi>h</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>+</mo><mi>t</mi><mi>r</mi><mi>a</mi><mi>n</mi><mi>s</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi><mo>+</mo><mi>o</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>f</mi><mi>o</mi><mi>o</mi><mi>d</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>h</mi><mi>o</mi><mi>u</mi><mi>s</mi><mi>e</mi><mo>&#160;</mo><mi>r</mi><mi>e</mi><mi>n</mi><mi>t</mi><mo>)</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>5</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>10</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>28</mn><mo>+</mo><mn>15</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>25</mn><mo>:</mo><mn>43</mn></math></p>",
                    solution_hi: "<p>(a)</p>\r\n<p>&#2357;&#2381;&#2351;&#2366;&#2326;&#2381;&#2351;&#2366;:-</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><mi>c</mi><mi>l</mi><mi>o</mi><mi>t</mi><mi>h</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>+</mo><mi>t</mi><mi>r</mi><mi>a</mi><mi>n</mi><mi>s</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi><mo>+</mo><mi>o</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>f</mi><mi>o</mi><mi>o</mi><mi>d</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>h</mi><mi>o</mi><mi>u</mi><mi>s</mi><mi>e</mi><mo>&nbsp;</mo><mi>r</mi><mi>e</mi><mi>n</mi><mi>t</mi><mo>)</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><mn>5</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>10</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>28</mn><mo>+</mo><mn>15</mn><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mn>25</mn><mo>:</mo><mn>43</mn></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>Directions(12-15): The pie-chart below shows the expenses of a person on different items and its savings during the year 2019. Study the chart carefully and answer the following questions:</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image4.png\" width=\"223\" height=\"349\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">Q15. If total income is 150500 and if the expenditure on house rent is 5000 more than the previous expenditure and this 5000 was spent less on food then find the new percentage of house rent and food? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">दिशा निर्देश(12-15): यह वृत्त-आरेख एक व्यक्ति के द्वारा वर्ष 2019 में विभिन्न मदों पर किये गए व्यय को एवं उसकी बचत को दर्शाता है | इस आरेख का ध्यानपूर्वक अध्ययन करें और प्रश्नों का उत्तर दें | </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503532/word/media/image4.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">1</span><span style=\"font-family: Baloo;\">5. यदि कुल आय 150500 है तथा यदि घर के किराए पर किया गया व्यय पूर्व के व्यय की तुलना में 5000 अधिक है और इस 5000 को भोजन पर कम खर्च किया गया है, तो घर के किराए एवं भोजन का नया प्रतिशत ज्ञात करें | </span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18.67</mn><mi>%</mi><mo>,</mo><mo>&#160;</mo><mn>24.17</mn><mi>%</mi></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18.53</mn><mi>%</mi><mo>,</mo><mo>&#160;</mo><mn>24.07</mn><mi>%</mi></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18.13</mn><mi>%</mi><mo>,</mo><mo>&#160;</mo><mn>24.37</mn><mi>%</mi></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18.32</mn><mi>%</mi><mo>,</mo><mo>&#160;</mo><mn>24.67</mn><mi>%</mi></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18.67</mn><mi>%</mi><mo>,</mo><mo>&#160;</mo><mn>24.17</mn><mi>%</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18.53</mn><mi>%</mi><mo>,</mo><mo>&#160;</mo><mn>24.07</mn><mi>%</mi></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18.13</mn><mi>%</mi><mo>,</mo><mo>&#160;</mo><mn>24.37</mn><mi>%</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18.32</mn><mi>%</mi><mo>,</mo><mo>&#160;</mo><mn>24.67</mn><mi>%</mi></math></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Previous expenditure on house rent = 15% of 150500 = 22575</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">new expenditure on house rent = 22575 + 5000 = 27575</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">new percentage of house rent =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>27575</mn></mrow><mn>150500</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>18</mn><mo>.</mo><mn>32</mn></math></span><span style=\"font-family: Times New Roman;\">%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Previous expenditure on food = 28% of 150500 = 42140</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">new expenditure on food = 42140-5000 = 37140</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">new percentage of food =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37140</mn><mn>150500</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>24.67</mn><mi>%</mi></math> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p>व्याख्या</p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;पिछला मकान किराए का खर्च = 15% of 150500 = 22575</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">मकान किराए पर नया खर्च = 22575 + 5000 = 27575</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">मकान किराए का नया प्रतिशत =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>27575</mn></mrow><mn>150500</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>18</mn><mo>.</mo><mn>32</mn></math></span><span style=\"font-family: Times New Roman;\">%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">भोजन पर पिछला खर्च= 28% of 150500 = 42140</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">भोजन पर नया खर्च= 42140-5000 = 37140</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">भोजन का नया प्रतिशत =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37140</mn><mn>150500</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>24.67</mn><mi>%</mi></math> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. If sin 2A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math><span style=\"font-family: Times New Roman;\">and cos 2B = -1 , then find the value of cot(A+B) as&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>&#160;</mo><mo>&#8804;</mo><mi>A</mi><mo>&#8804;</mo><mn>45</mn><mo>&#176;</mo></math> </span><span style=\"font-family: Times New Roman;\">and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>180</mn><mo>&#176;</mo><mo>&#8805;</mo><mi>B</mi><mo>&#8805;</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo></math></span><span style=\"font-family: Times New Roman;\">? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">16. यदि sin 2A =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> </span><span style=\"font-family: Baloo;\"> तथा cos 2B = -1 है, तो cot(A+B) का मान ज्ञात करें, जहाँ&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>&#160;</mo><mo>&#8804;</mo><mi>A</mi><mo>&#8804;</mo><mn>45</mn><mo>&#176;</mo></math> </span><span style=\"font-family: Baloo;\">और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>180</mn><mo>&#176;</mo><mo>&#8805;</mo><mi>B</mi><mo>&#8805;</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">है | </span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>6</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></m", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>6</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">Given , sin 2A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">a</span><span style=\"font-family: Times New Roman;\">nd cos 2B = -1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin 2A =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mroot><mn>3</mn><mrow/></mroot><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= sin 60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">2A=60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">A=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>30</mn><mo>&#176;</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cos 2B = -1 = cos 180&deg; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\">B = 90&deg;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cot(A +B) = cot (90&deg; + 30&deg;) = cot(120&deg;) = - tan30&deg; =</span><span style=\"font-family: Times New Roman;\"> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">व्याख्या:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">दिया है&nbsp; , sin 2A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">और </span><span style=\"font-family: Times New Roman;\">&nbsp;cos 2B = -1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin 2A =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mroot><mn>3</mn><mrow></mrow></mroot><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= sin 60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">2A=60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">A=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>30</mn><mo>&#176;</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cos 2B = -1 = cos 180&deg; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\">B = 90&deg;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cot(A +B) = cot (90&deg; + 30&deg;) = cot(120&deg;) = - tan30&deg; =</span><span style=\"font-family: Times New Roman;\"> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mo>.</mo><mn>1</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>11</mn><mo>+</mo><mn>9</mn><mo>.</mo><mn>7</mn><mo>+</mo><mn>4</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>4</mn><mo>.</mo><mn>9</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>7</mn><mo>-</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>?</mo></math></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">17. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mo>.</mo><mn>1</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>11</mn><mo>+</mo><mn>9</mn><mo>.</mo><mn>7</mn><mo>+</mo><mn>4</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>4</mn><mo>.</mo><mn>9</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>7</mn><mo>-</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>?</mo></math></span><span style=\"font-family: Baloo;\">&nbsp;का मान ज्ञात करें | </span></p>",
                    options_en: ["<p>122.1<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>122.9</p>", 
                                "<p>122.3<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>122.7</p>"],
                    options_hi: ["<p>122.1</p>", "<p>122.9</p>",
                                "<p>122.3</p>", "<p>122.7</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mo>.</mo><mn>1</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>11</mn><mo>+</mo><mn>9</mn><mo>.</mo><mn>7</mn><mo>+</mo><mn>4</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>4</mn><mo>.</mo><mn>9</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>7</mn><mo>-</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>7</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi>A</mi><mi>p</mi><mi>p</mi><mi>l</mi><mi>y</mi><mo>&#160;</mo><mi>B</mi><mi>O</mi><mi>D</mi><mi>M</mi><mi>A</mi><mi>S</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>.</mo><mn>1</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac><mo>+</mo><mn>9</mn><mo>.</mo><mn>7</mn><mo>+</mo><mn>4</mn><mo>.</mo><mn>5</mn><mo>-</mo><mfrac><mrow><mn>4</mn><mo>.</mo><mn>9</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>7</mn></mrow></mfrac><mo>-</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>110</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>14</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mo>.</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>122</mn><mo>.</mo><mn>7</mn></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p>व्याख्या:-</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mo>.</mo><mn>1</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>11</mn><mo>+</mo><mn>9</mn><mo>.</mo><mn>7</mn><mo>+</mo><mn>4</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>4</mn><mo>.</mo><mn>9</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>7</mn><mo>-</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>7</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi>B</mi><mi>O</mi><mi>D</mi><mi>M</mi><mi>A</mi><mi>S</mi><mo>&#160;</mo><mi>&#2354;&#2327;&#2366;&#2344;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mo>&#160;</mo></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>.</mo><mn>1</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac><mo>+</mo><mn>9</mn><mo>.</mo><mn>7</mn><mo>+</mo><mn>4</mn><mo>.</mo><mn>5</mn><mo>-</mo><mfrac><mrow><mn>4</mn><mo>.</mo><mn>9</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>7</mn></mrow></mfrac><mo>-</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>110</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>14</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mo>.</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>122</mn><mo>.</mo><mn>7</mn></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. If A : B = 2 : 3 and B =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac><mi>C</mi></math> <span style=\"font-family: Times New Roman;\">. The difference between A and C is 2800. Then find the value of D, If D is 61.9% more than B? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">18. यदि A : B = 2 : 3 तथा B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac><mi>C</mi></math></span><span style=\"font-family: Baloo;\"> है और A तथा C के बीच 2800 का अंतर है, तो D का मान ज्ञात करें, यदि D, B से 61.9% अधिक है | </span></p>",
                    options_en: ["<p>4857</p>", "<p>4855</p>", 
                                "<p>4850</p>", "<p>4851</p>"],
                    options_hi: ["<p>4857</p>", "<p>4855</p>",
                                "<p>4850</p>", "<p>4851</p>"],
                    solution_en: "<p>(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Given, A : B and B : C is 2 : 3 and 5 : 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A : B : C</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2 : 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp;5 : 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">_________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10 : 15 : 24 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">24x - 10x = 2800 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\">x = 200</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B = 200(15) = 3000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">D = 161.9% of 3000 = 4857</span></p>",
                    solution_hi: "<p>(a)</p>\r\n<p>व्याख्या:-</p>\r\n<p><span style=\"font-family: Times New Roman;\">दिया है , A : B &nbsp;और&nbsp; B : C &nbsp;क्रमश: 2 : 3 &nbsp;और 5 : 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A : B : C</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2 : 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp;5 : 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">_________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10 : 15 : 24 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">24x - 10x = 2800 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\">x = 200</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B = 200(15) = 3000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">D = 161.9% of 3000 = 4857</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. Efficiency ratio for P:Q is 3:4 and Q:R is 5:4. Working together they can complete the work in 45 days. P and R together will complete 40% of the total work in ?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">19. P:Q का दक्षता अनुपात 3:4 है और Q:R का 5:4 है। एक साथ कार्य करते हुए, वे इस कार्य को 45 दिनों में पूरा कर सकते हैं | P और R एक साथ इस कार्य का 40% भाग कितने दिनों में पूरा करेंगे ? </span></p>",
                    options_en: ["<p>29<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>31</mn></mfrac></math><span style=\"font-family: Times New Roman;\">days </span></p>", "<p>29<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>31</mn></mfrac></math><span style=\"font-family: Times New Roman;\">days </span></p>", 
                                "<p>29<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>30</mn></mfrac></math><span style=\"font-family: Times New Roman;\">days </span></p>", "<p>29<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>32</mn></mfrac></math><span style=\"font-family: Times New Roman;\">days </span></p>"],
                    options_hi: ["<p>29<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>31</mn></mfrac></math><span style=\"font-family: Times New Roman;\">days</span></p>", "<p>29<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>31</mn></mfrac></math><span style=\"font-family: Times New Roman;\">days</span></p>",
                                "<p>29<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>30</mn></mfrac></math><span style=\"font-family: Times New Roman;\">days</span></p>", "<p>29<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>32</mn></mfrac></math><span style=\"font-family: Times New Roman;\">days</span></p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P : Q : R</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 : 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; 5 : 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> ________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">15 : 20 : 16 = efficiency ratio</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">total work = (15+20+16)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mn>45</mn><mo>=</mo></math></span><span style=\"font-family: Times New Roman;\">2295</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> P and R together will complete 40% of the total work </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>2295</mn><mrow><mn>15</mn><mo>+</mo><mn>16</mn></mrow></mfrac><mo>=</mo><mfrac><mn>918</mn><mn>31</mn></mfrac><mo>=</mo></math><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>29</mn><mfrac><mn>19</mn><mn>31</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">days</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>व्याख्या:-</p>\r\n<p><span style=\"font-family: Times New Roman;\">P : Q : R</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 : 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; 5 : 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> ________</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">15 : 20 : 16 = दक्षता: अनुपात</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">कुल काम = (15+20+16)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mn>45</mn><mo>=</mo></math></span><span style=\"font-family: Times New Roman;\">2295</span></p>\r\n<p>P और R मिलकर कुल कार्य का 40% पूरा करेंगे</p>\r\n<p><span style=\"font-family: Times New Roman;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>2295</mn><mrow><mn>15</mn><mo>+</mo><mn>16</mn></mrow></mfrac><mo>=</mo><mfrac><mn>918</mn><mn>31</mn></mfrac><mo>=</mo></math><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>29</mn><mfrac><mn>19</mn><mn>31</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">days</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. Total number of boys and girls in a dance competition is 220. The number of boys is 20% more than the number of girls. Average age of boys is 25% more than that of girls. If the average age of all competitors is 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>11</mn></mfrac></math><span style=\"font-family: Times New Roman;\">years. What is the average age of girls? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">20. किसी नृत्य प्रतियोगिता में लड़कों एवं लड़कियों की कुल संख्या 220 है | लड़कों की संख्या लड़कियों की संख्या से 20% अधिक है | लड़कों की औसत उम्र लड़कियों की औसत उम्र से 25% अधिक है | यदि सभी प्रतियोगियों की औसत उम्र 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>11</mn></mfrac></math></span><span style=\"font-family: Baloo;\"> वर्ष है, तो लड़कियों की औसत उम्र ज्ञात करें | </span></p>",
                    options_en: ["<p>2.13<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>4.01 <span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>4.16</p>", "<p>2.15</p>"],
                    options_hi: ["<p>2.13</p>", "<p>4.01</p>",
                                "<p>4.16</p>", "<p>2.15</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the number of girls = X</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore number of boys = 120% of X = 1.2X</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>X+1.2X= 220<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\">X=100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>girls = 100 and boys = 120</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sum of ages of group = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>220</mn><mo>&#215;</mo><mn>4</mn><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>=</mo><mn>220</mn><mo>&#215;</mo><mfrac><mn>52</mn><mn>11</mn></mfrac><mo>=</mo><mn>1040</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the average age of girls = A</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average age of boys in a group = 125% of A = 1.25A</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>100</mn><mi>A</mi><mo>+</mo><mo>(</mo><mn>120</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>25</mn><mi>A</mi><mo>)</mo><mo>=</mo><mo>&#160;</mo><mn>1040</mn><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mn>250</mn><mi>A</mi><mo>=</mo><mn>1040</mn><mo>&#160;</mo><mo>&#8658;</mo><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>.</mo><mn>16</mn><mo>&#160;</mo><mo>=</mo><mi>a</mi><mi>v</mi><mi>e</mi><mi>r</mi><mi>a</mi><mi>g</mi><mi>e</mi><mo>&#160;</mo><mi>a</mi><mi>g</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>g</mi><mi>i</mi><mi>r</mi><mi>l</mi><mi>s</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p>व्याख्या;-</p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;माना लड़कियों की संख्या = X</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">इसलिए लड़कों की संख्या = 120% of X = 1.2X</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>X+1.2X= 220<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span><span style=\"font-family: Times New Roman;\">X=100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></span></p>\r\n<p>लड़कियां = 100 और लड़के = 120</p>\r\n<p>समूह की आयु का योग <span style=\"font-family: Times New Roman;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>220</mn><mo>&#215;</mo><mn>4</mn><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>=</mo><mn>220</mn><mo>&#215;</mo><mfrac><mn>52</mn><mn>11</mn></mfrac><mo>=</mo><mn>1040</mn></math></p>\r\n<p>माना लड़कियों की औसत आयु = A</p>\r\n<p>एक समूह में लड़कों की औसत आयु = A का 125% = 1.25A</p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>100</mn><mi>A</mi><mo>+</mo><mo>(</mo><mn>120</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>25</mn><mi>A</mi><mo>)</mo><mo>=</mo><mo>&#160;</mo><mn>1040</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>250</mn><mi>A</mi><mo>=</mo><mn>1040</mn><mo>&#160;</mo><mo>&#8658;</mo><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>.</mo><mn>16</mn><mo>&#160;</mo><mo>=</mo><mi>&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2324;&#2360;&#2340;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2369;</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>1442</mn></math><span style=\"font-family: Times New Roman;\">, then find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mo>(</mo><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>]</mo><mo>-</mo><mo>(</mo><mi>x</mi><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo></math>?</span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">21. यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>1442</mn></math></span><span style=\"font-family: Baloo;\"> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mo>(</mo><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>]</mo><mo>-</mo><mo>(</mo><mi>x</mi><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo></math> </span><span style=\"font-family: Baloo;\">का मान ज्ञात करें |</span></p>",
                    options_en: ["<p>54799</p>", "<p>54790</p>", 
                                "<p>54791</p>", "<p>54795</p>"],
                    options_hi: ["<p>54799</p>", "<p>54790</p>",
                                "<p>54791</p>", "<p>54795</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>1442</mn><mspace linebreak=\"newline\"/><mo>&#8658;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mo>&#160;</mo><msqrt><mn>1442</mn><mo>+</mo><mn>2</mn></msqrt><mo>=</mo><msqrt><mn>1444</mn></msqrt><mo>=</mo><mn>38</mn><mo>&#160;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mspace linebreak=\"newline\"/><mi>x</mi><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><msqrt><mn>38</mn><mo>-</mo><mn>2</mn></msqrt><mo>=</mo><mn>6</mn><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>-</mo><mo>(</mo><mi>x</mi><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>1442</mn><mo>&#215;</mo><mn>38</mn><mo>)</mo><mo>-</mo><mn>6</mn><mo>=</mo><mo>&#160;</mo><mn>54790</mn></math></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p>व्याख्या:-</p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>1442</mn><mspace linebreak=\"newline\"/><mo>&#8658;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mo>&#160;</mo><msqrt><mn>1442</mn><mo>+</mo><mn>2</mn></msqrt><mo>=</mo><msqrt><mn>1444</mn></msqrt><mo>=</mo><mn>38</mn><mo>&#160;</mo><mo>&#160;</mo><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>x</mi><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><msqrt><mn>38</mn><mo>-</mo><mn>2</mn></msqrt><mo>=</mo><mn>6</mn><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>-</mo><mo>(</mo><mi>x</mi><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>1442</mn><mo>&#215;</mo><mn>38</mn><mo>)</mo><mo>-</mo><mn>6</mn><mo>=</mo><mo>&#160;</mo><mn>54790</mn></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. Arun bought a toy for 205 which was marked at 250 with two successive discounts. The first discount was 15%, find the second discount?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">22. अरुण ने दो क्रमिक छूट पर एक खिलौना 205 में ख़रीदा जिसका अंकित मूल्य 250 था| पहली छूट 15% की थी, तो दूसरी छूट कितनी थी ? </span></p>",
                    options_en: ["<p>3.13%</p>", "<p>3.93%</p>", 
                                "<p>3.23%<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3.53%</p>"],
                    options_hi: ["<p>3.13%</p>", "<p>3.93%</p>",
                                "<p>3.23%</p>", "<p>3.53%</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">Let second discount is x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>250</mn><mo>&#215;</mo><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi>x</mi><mo>)</mo></mrow><mn>100</mn></mfrac><mo>=</mo><mn>205</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>100</mn><mo>-</mo><mi>x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>205</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>250</mn><mo>&#215;</mo><mn>85</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mn>96</mn><mo>.</mo><mn>47</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = second discount = 3.53%</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p>व्याख्या:-</p>\r\n<p>माना दूसरी छूट x &nbsp;है</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>250</mn><mo>&#215;</mo><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi>x</mi><mo>)</mo></mrow><mn>100</mn></mfrac><mo>=</mo><mn>205</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>100</mn><mo>-</mo><mi>x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>205</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>250</mn><mo>&#215;</mo><mn>85</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mn>96</mn><mo>.</mo><mn>47</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x =दूसरी छूट = 3.53%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. A fruit seller marks his fruits at 72.3% above the cost price. He sells 55% at marked price and rest he sold by allowing 27% discount on the marked price. Find his profit and loss percentage?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">23. एक फल विक्रेता अपने फलों की कीमत क्रय मूल्य से 72.3% अधिक तय करता है | वह 55% फलों को अंकित मूल्य पर तथा शेष फलों को अंकित मूल्य से 27% की छूट पर बेचता है | उसके लाभ या हानि का प्रतिशत ज्ञात करें | </span></p>",
                    options_en: ["<p>50.365%</p>", "<p>51.365%</p>", 
                                "<p>51.192%</p>", "<p>50.192%</p>"],
                    options_hi: ["<p>50.365 %</p>", "<p>51.365%</p>",
                                "<p>51.192%</p>", "<p>50.192%</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let there are 100 fruits with Rs 100 CP</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>SP of 55% of fruits at marked price =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>55</mn><mo>%</mo><mo>&#215;</mo><mn>172</mn><mo>.</mo><mn>3</mn><mo>%</mo><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>94</mn><mo>.</mo><mn>765</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SP of remaining 45% at 27% discount = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>45</mn><mo>%</mo><mo>&#215;</mo><mn>73</mn><mo>%</mo><mo>&#215;</mo><mn>172</mn><mo>.</mo><mn>3</mn><mo>%</mo><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>56</mn><mo>.</mo><mn>60</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">total SP = 94.765+56.6=151.365</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">required profit percentage </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>151.365</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>100</mn><mo>=</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>51</mn><mo>.</mo><mn>365</mn><mo>%</mo></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p>व्याख्या:-</p>\r\n<p>मान लीजिए 100 फल हैं जिनका मूल्य 100 रु. है</p>\r\n<p><span style=\"font-family: Times New Roman;\">अंकित मूल्य पर 55% फलों का विक्रय मूल्य == <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>55</mn><mo>%</mo><mo>&#215;</mo><mn>172</mn><mo>.</mo><mn>3</mn><mo>%</mo><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>94</mn><mo>.</mo><mn>765</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">27% छूट पर शेष 45% का विक्रय मूल्य = = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>45</mn><mo>%</mo><mo>&#215;</mo><mn>73</mn><mo>%</mo><mo>&#215;</mo><mn>172</mn><mo>.</mo><mn>3</mn><mo>%</mo><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>56</mn><mo>.</mo><mn>60</mn></math></p>\r\n<p>&nbsp;</p>\r\n<p><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>55</mn><mo>%</mo><mo>&#215;</mo><mn>172</mn><mo>.</mo><mn>3</mn><mo>%</mo><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>94</mn><mo>.</mo><mn>765</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>45</mn><mo>%</mo><mo>&#215;</mo><mn>73</mn><mo>%</mo><mo>&#215;</mo><mn>172</mn><mo>.</mo><mn>3</mn><mo>%</mo><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>56</mn><mo>.</mo><mn>60</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">कुल विक्रय मूल्य= 94.765+56.6=151.365</span></p>\r\n<p>आवश्यक लाभ प्रतिशत</p>\r\n<p><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>151.365</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>100</mn><mo>=</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>51</mn><mo>.</mo><mn>365</mn><mo>%</mo></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. 3 circles of radius 5 cm cut from an equilateral triangular sheet of side 32cm. Find the percentage area of the rest of the sheet?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">24. 32 सेमी भुजा वाले एक समबाहु त्रिभुजाकार कागज़ से 5 सेमी त्रिज्या वाले 3 वृत्त काटे जाते हैं| शेष कागज़ का प्रतिशत क्षेत्रफल ज्ञात करें | </span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>46</mn><mo>.</mo><mn>84</mn><mo>%</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>51</mn><mo>.</mo><mn>36</mn><mo>%</mo></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>51</mn><mo>.</mo><mn>44</mn><mo>%</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>56</mn><mo>.</mo><mn>04</mn><mo>%</mo></math></p>"],
                    options_hi: ["<p>46.84%</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>51</mn><mo>.</mo><mn>36</mn><mo>%</mo></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>51</mn><mo>.</mo><mn>44</mn><mo>%</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>56</mn><mo>.</mo><mn>04</mn><mo>%</mo></math></p>"],
                    solution_en: "<p>(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area of equilateral triangle </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&#8730;</mo><mn>3</mn></mrow><mn>4</mn></mfrac><mo>&#215;</mo><mn>32</mn><mo>&#215;</mo><mn>32</mn><mo>=</mo><mn>443</mn><mo>.</mo><mn>40</mn><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area of three circles =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>&#215;</mo><mi>&#960;</mi><mo>&#215;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>=</mo><mn>235</mn><mo>.</mo><mn>7</mn><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">required percentage</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>443.40</mn><mo>-</mo><mn>235.7</mn></mrow><mn>443.40</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mn>207.7</mn><mn>443.40</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>46.84</mn><mi>%</mi></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>(a)</p>\r\n<p>व्याख्या:</p>\r\n<p>समबाहु त्रिभुज का क्षेत्रफल</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&#8730;</mo><mn>3</mn></mrow><mn>4</mn></mfrac><mo>&#215;</mo><mn>32</mn><mo>&#215;</mo><mn>32</mn><mo>=</mo><mn>443</mn><mo>.</mo><mn>40</mn><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">तीन वृत्तों का क्षेत्रफल&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>&#215;</mo><mi>&#960;</mi><mo>&#215;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>=</mo><mn>235</mn><mo>.</mo><mn>7</mn><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">आवश्यक प्रतिशत =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>443.40</mn><mo>-</mo><mn>235.7</mn></mrow><mn>443.40</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mn>207.7</mn><mn>443.40</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>46.84</mn><mi>%</mi></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Rahul invested 40% of his money at 17% per annum and the rest at 21% per annum. What would be the annual rate of interest, if the interest is calculated on the whole sum?</p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">25. राहुल ने अपनी 40% धनराशि 17% प्रति वर्ष की दर से तथा शेष राशि 21% प्रति वर्ष की दर से निवेश की | यदि ब्याज की गणना पूरी राशि पर की जाए, तो ब्याज की वार्षिक दर कितनी होगी ? </span></p>",
                    options_en: ["<p>19.9%</p>", "<p>19.2%<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>19.4%<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>19.5%</p>"],
                    options_hi: ["<p>19.9%</p>", "<p>19.2%</p>",
                                "<p>19.4%</p>", "<p>19.5%</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Explanation:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">According to question,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let total amount = Rs 100</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">amount invested ate 17% = 40% of 100 = 40 and remaining amount = 100-40=60</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">rate</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mi>%</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi><mo>+</mo><mn>21</mn><mi>%</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>60</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6.8</mn><mo>+</mo><mn>12.6</mn></mrow><mn>100</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>19</mn><mo>.</mo><mn>4</mn><mo>%</mo></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p>व्याख्या:</p>\r\n<p>प्रश्न के अनुसार,</p>\r\n<p>माना कुल राशि = 100</p>\r\n<p>निवेश की गई राशि 17% = 100 का 40% = 40 और शेष राशि = 100-40 = 60</p>\r\n<p><span style=\"font-family: Times New Roman;\">दर&nbsp; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mi>%</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi><mo>+</mo><mn>21</mn><mi>%</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>60</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6.8</mn><mo>+</mo><mn>12.6</mn></mrow><mn>100</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>19</mn><mo>.</mo><mn>4</mn><mo>%</mo></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>