<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If the cost price of 5 shirts is the same as the selling price of 3 shirts, then find the profit percentage.</p>",
                    question_hi: "<p>1. यदि 5 शर्ट का क्रय मूल्य, 3 शर्ट के विक्रय मूल्य के समान है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>55.33% profit</p>", "<p>63.33% profit</p>", 
                                "<p>66.66% profit</p>", "<p>33.66% profit</p>"],
                    options_hi: ["<p>55.33% लाभ</p>", "<p>63.33% लाभ</p>",
                                "<p>66.66% लाभ</p>", "<p>33.66% लाभ</p>"],
                    solution_en: "<p>1.(c) According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> CP of 5 shirt = SP of 3 shirt <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math>.<br><math display=\"inline\"><mo>&#8658;</mo></math> Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>3</mn></mrow><mn>3</mn></mfrac></math> &times; 100 = 66.66 %</p>",
                    solution_hi: "<p>1.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 5 शर्ट का क्रय मूल्य = 3 शर्ट का विक्रय मूल्य <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> लाभ % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>3</mn></mrow><mn>3</mn></mfrac></math> &times; 100 = 66.66 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A book is sold at <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> of its marked price and there is a loss of 50 percent. What will be the ratio of marked price and cost price of the book ?</p>",
                    question_hi: "<p>2. एक पुस्तक को उसके अंकित मूल्य के <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> पर बेचने पर 50 प्रतिशत की हानि होती है। पुस्तक के अंकित मूल्य और क्रय मूल्य का अनुपात कितना होगा ?</p>",
                    options_en: ["<p>2 : 5</p>", "<p>3 : 8</p>", 
                                "<p>5 : 6</p>", "<p>6 : 5</p>"],
                    options_hi: ["<p>2 : 5</p>", "<p>3 : 8</p>",
                                "<p>5 : 6</p>", "<p>6 : 5</p>"],
                    solution_en: "<p>2.(d) Let the marked price be <math display=\"inline\"><mi>x</mi></math><br>According to question,<br>S.P. of book = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math><br>CP of book = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>50</mn></mfrac><mo>=</mo><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>6</mn></mfrac></math><br>Required Ratio <math display=\"inline\"><mo>&#8658;</mo></math> x : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>6</mn></mfrac></math> &rArr; 6 : 5</p>",
                    solution_hi: "<p>2.(d) माना अंकित मूल्य <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार,<br>पुस्तक का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math><br>पुस्तक का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>50</mn></mfrac><mo>=</mo><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>6</mn></mfrac></math>.<br>आवश्यक अनुपात <math display=\"inline\"><mo>&#8658;</mo></math>x : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>6</mn></mfrac></math> &rArr; 6 : 5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. The marked price of a chair is 10 percent more than its cost price. If the chair is sold for Rs. 132 after a discount of Rs. 11, then what will be the profit percentage?",
                    question_hi: "3. एक कुर्सी का अंकित मूल्य, उसके क्रय मूल्य से 10 प्रतिशत अधिक है। यदि 11 रुपए की छूट देने के बाद कुर्सी 132 रुपए में बेची जाती है, तो लाभ प्रतिशत कितना होगा? ",
                    options_en: [" 2 percent", " 1.53 percent", 
                                " 1.78 percent ", " 1.21 percent "],
                    options_hi: [" 2 प्रतिशत", " 1.53 प्रतिशत",
                                " 1.78 प्रतिशत", " 1.21 प्रतिशत"],
                    solution_en: "<p>3.(b)<br>CP : MP = 10 : 11<br>MP = 132 + 11 = ₹143&nbsp;<br>11 unit ------------- ₹143 <br>10 unit ------------- <math display=\"inline\"><mfrac><mrow><mn>143</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 10 = ₹130<br>Now, CP : SP = 130 : 132 = 65 : 66<br>Profit% = <math display=\"inline\"><mfrac><mrow><mn>66</mn><mo>-</mo><mn>65</mn></mrow><mrow><mn>65</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>65</mn></mfrac></math>= 1.53%</p>",
                    solution_hi: "<p>3.(b)<br>क्रय मूल्य : अंकित मूल्य = 10 : 11<br>अंकित मूल्य = 132 + 11 = ₹143&nbsp;<br>11 इकाई ------------- ₹143 <br>10 इकाई ------------- <math display=\"inline\"><mfrac><mrow><mn>143</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 10 = ₹130<br>अब , CP : SP = 130 : 132 = 65 : 66<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>66</mn><mo>-</mo><mn>65</mn></mrow><mrow><mn>65</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>65</mn></mfrac></math>= 1.53%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The difference between cost price and selling price of an article is Rs. 120. If profit percentage is 10 percent, then what is the selling price?</p>",
                    question_hi: "<p>4. एक वस्तु के क्रय मूल्य और विक्रय मूल्य के बीच 120 रुपए का अंतर है। यदि लाभ प्रतिशत 10 प्रतिशत है, तो विक्रय मूल्य क्या है?</p>",
                    options_en: ["<p>Rs. 1320</p>", "<p>Rs. 1000</p>", 
                                "<p>Rs. 1200</p>", "<p>Rs. 1440</p>"],
                    options_hi: ["<p>1320 रुपए</p>", "<p>1000 रुपए</p>",
                                "<p>1200 रुपए</p>", "<p>1440 रुपए</p>"],
                    solution_en: "<p>4.(a)<br>(SP - CP) or profit = ₹120, Profit% = 10%<br>10% ------------ ₹120<br>100% ------------ <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = ₹1200<br>Then, SP of the article = 1200 + 120 = ₹1320</p>",
                    solution_hi: "<p>4.(a)<br>(विक्रय मूल्य - क्रय मूल्य ) और लाभ = ₹120, लाभ % = 10%<br>10% ------------ ₹120<br>100% ------------ <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = ₹1200<br>तो, वस्तु का विक्रय मूल्य = 1200 + 120 = ₹1320</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If the selling price of a fan is Rs. 306 and loss percentage is 32 percent, then what is the cost price of the fan?</p>",
                    question_hi: "<p>5. यदि एक पंखे का विक्रय मूल्य 306 रुपए है और हानि प्रतिशत 32 प्रतिशत है, तो पंखे का क्रय मूल्य कितना है?</p>",
                    options_en: ["<p>Rs. 400</p>", "<p>Rs. 450</p>", 
                                "<p>Rs. 350</p>", "<p>Rs. 380</p>"],
                    options_hi: ["<p>400 रुपए</p>", "<p>450 रुपए</p>",
                                "<p>350 रुपए</p>", "<p>380 रुपए</p>"],
                    solution_en: "<p>5.(b)<br>CP of fan = 306 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>68</mn></mrow></mfrac></math> = ₹450</p>",
                    solution_hi: "<p>5.(b)<br>पंखे का क्रय मूल्य = 306 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>68</mn></mrow></mfrac></math> = ₹450</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Suresh sells a car at the loss of 36 percent. What will be the ratio of cost price to selling price?</p>",
                    question_hi: "<p>6. सुरेश एक कार को 36 प्रतिशत की हानि पर बेचता है। क्रय मूल्य और विक्रय मूल्य का अनुपात कितना होगा?</p>",
                    options_en: ["<p>16 : 25</p>", "<p>15 : 16</p>", 
                                "<p>25 : 16</p>", "<p>4 : 5</p>"],
                    options_hi: ["<p>16 : 25</p>", "<p>15 : 16</p>",
                                "<p>25 : 16</p>", "<p>4 : 5</p>"],
                    solution_en: "<p>6.(c)<br>36% = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>CP : SP = 25 : (25 - 9) = 25 : 16</p>",
                    solution_hi: "<p>6.(c)<br>36% = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>क्रय मूल्य : विक्रय मूल्य = 25 : (25 - 9) = 25 : 16</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.The difference between cost price and selling price is Rs. 132. If profit percentage is 20 percent , then what is selling price ?</p>",
                    question_hi: "<p>7. क्रय मूल्य और विक्रय मूल्य के बीच का अंतर 132 रुपए है। यदि लाभ प्रतिशत 20 प्रतिशत है, तो विक्रय मूल्य कितना है?</p>",
                    options_en: ["<p>Rs. 760</p>", "<p>Rs. 822</p>", 
                                "<p>Rs. 802</p>", "<p>Rs. 792</p>"],
                    options_hi: ["<p>760 रुपए</p>", "<p>822 रुपए</p>",
                                "<p>802 रुपए</p>", "<p>792 रुपए</p>"],
                    solution_en: "<p>7.(d)<br>Let CP be 100%<br>SP - CP = profit = ₹132<br>Profit% = 20%<br><math display=\"inline\"><mo>&#8658;</mo></math> 20% = ₹132<br><math display=\"inline\"><mo>&#8658;</mo></math> 120% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>132</mn><mn>20</mn></mfrac></math> &times; 120 = ₹792</p>",
                    solution_hi: "<p>7.(d)<br>माना क्रय मूल्य 100%<br>विक्रय मूल्य - क्रय मूल्य = लाभ = ₹132<br>लाभ% = 20%<br><math display=\"inline\"><mo>&#8658;</mo></math> 20% = ₹132<br><math display=\"inline\"><mo>&#8658;</mo></math> 120% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>132</mn><mn>20</mn></mfrac></math> &times; 120 = ₹792</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A watch is purchased for Rs. 6000 and sold for Rs. 5500. What is the loss percentage?</p>",
                    question_hi: "<p>8. एक घड़ी 6000 रुपए में खरीदी जाती है और 5500 रुपए में बेची जाती है। हानि प्रतिशत कितना है?</p>",
                    options_en: ["<p>8.08 percent</p>", "<p>11.11 percent</p>", 
                                "<p>8.33 percent</p>", "<p>7.28 percent</p>"],
                    options_hi: ["<p>8.08 प्रतिशत</p>", "<p>11.11 प्रतिशत</p>",
                                "<p>8.33 प्रतिशत</p>", "<p>7.28 प्रतिशत</p>"],
                    solution_en: "<p>8.(c)<br>CP : SP = 6000 : 5500 = 12 : 11<br>loss% = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>-</mo><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>12</mn></mfrac></math> = 8.33%</p>",
                    solution_hi: "<p>8.(c)<br>क्रय मूल्य : विक्रय मूल्य = 6000 : 5500 = 12 : 11<br>हानि% = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>-</mo><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>12</mn></mfrac></math> = 8.33%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Anurag sold a refrigerator for ₹28000, at a loss of 30%. If he wanted to earn a profit of 25%, at what price should he have sold the refrigerator?</p>",
                    question_hi: "<p>9. ₹28000 में एक रेफ्रिजरेटर बेचने पर अनुराग को 30% की हानि होती है। यदि वह 25% का लाभ अर्जित करना चाहता है, तो उसे रेफ्रिजरेटर किस मूल्य पर बेचना चाहिए था?</p>",
                    options_en: ["<p>Rs. 52,700</p>", "<p>Rs. 52,000</p>", 
                                "<p>Rs. 50,000</p>", "<p>Rs. 51,500</p>"],
                    options_hi: ["<p>52,700 रुपए</p>", "<p>52,000 रुपए</p>",
                                "<p>50,000 रुपए</p>", "<p>51,500 रुपए</p>"],
                    solution_en: "<p>24.(c)<br>Let CP be 100%<br>70% ------------- ₹28,000<br>125% ------------- <math display=\"inline\"><mfrac><mrow><mn>28</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 125 = ₹50,000</p>",
                    solution_hi: "<p>9.(c)<br>माना क्रय मूल्य 100% है<br>70% ------------- ₹28,000<br>125% ------------- <math display=\"inline\"><mfrac><mrow><mn>28</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 125 = ₹50,000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A person bought some pens at the rate of 20 for a rupee and sold them at the rate of 15 for a rupee. What is the profit percentage?</p>",
                    question_hi: "<p>10. एक व्यक्ति ने एक रुपए में 20 पेन की दर से कुछ पेन खरीदे और उन्हें एक रुपए में 15 पेन की दर से बेच दिया। लाभ प्रतिशत कितना है?</p>",
                    options_en: ["<p>35 percent</p>", "<p>15 percent</p>", 
                                "<p>33.33 percent</p>", "<p>40 percent</p>"],
                    options_hi: ["<p>35 प्रतिशत</p>", "<p>15 प्रतिशत</p>",
                                "<p>33.33 प्रतिशत</p>", "<p>40 प्रतिशत</p>"],
                    solution_en: "<p>10.(c)<br>CP : SP = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>15</mn></mfrac></math>= 15 : 20 = 3 : 4<br>Required profit% = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 100 = 33.33%</p>",
                    solution_hi: "<p>10.(c)<br>क्रयमूल्य : विक्रय मूल्य =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>15</mn></mfrac></math>= 15 : 20 = 3 : 4<br>आवश्यक लाभ% = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 100 = 33.33%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A person bought an article and sold it at a loss of 20 percent. If he had bought it for 30 percent less price and sold it for Rs.125 more, then he would earn a profit of 50 percent. What is the cost price of the article</p>",
                    question_hi: "<p>11. एक व्यक्ति एक वस्तु खरीदता है और उसे 20 प्रतिशत की हानि पर बेच देता है। यदि वह उस वस्तु को 30 प्रतिशत कम मूल्य पर खरीदता और उसे 125 रुपए अधिक मूल्य पर बेचता, तो वह 50 प्रतिशत का लाभ अर्जित करता है। वस्तु का क्रय मूल्य कितना है?</p>",
                    options_en: ["<p>Rs. 450</p>", "<p>Rs. 620</p>", 
                                "<p>Rs. 500</p>", "<p>Rs.600</p>"],
                    options_hi: ["<p>450 रुपए</p>", "<p>620 रुपए</p>",
                                "<p>500 रुपए</p>", "<p>600 रुपए</p>"],
                    solution_en: "<p>11.(c)<br>Let original CP of an article be 100 इकाई <br>Then, original SP of the article = 100 &times; 80% = 80 इकाई&nbsp;<br>New CP = 100 &times; 70% = 70 इकाई&nbsp;<br>New SP = 70 &times; 150% = 105 इकाई&nbsp;<br>ATQ, 105 - 80 = 25 unit ------------ ₹125<br>Then, 100 unit ------------ <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = ₹500</p>",
                    solution_hi: "<p>11.(c)<br>माना कि एक वस्तु का वास्तविक क्रयमूल्य 100 इकाई है<br>तब, वस्तु का वास्तविक विक्रयमूल्य = 100 &times; 80% = 80 इकाई<br>नया क्रयमूल्य = 100 &times; 70% = 70 इकाई <br>नया विक्रयमूल्य = 70 &times; 150% = 105 इकाई&nbsp;<br>प्रश्न के अनुसार, 105 - 80 = 25 इकाई ------- ₹125<br>अतः, 100 इकाई ----------- <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = ₹500</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. While selling a bag for Rs. 5688, a person suffered a loss of 10 percent. At what price he should have sold the bag to earn a profit of 20 percent?</p>",
                    question_hi: "<p>12. एक बैग को 5688 रुपए में बेचने पर, एक व्यक्ति को 10 प्रतिशत की हानि हुई। 20 प्रतिशत का लाभ कमाने के लिए उसे बैग को किस मूल्य पर बेचना चाहिए था?</p>",
                    options_en: ["<p>Rs. 7854</p>", "<p>Rs. 7216</p>", 
                                "<p>Rs. 7584</p>", "<p>Rs. 6816</p>"],
                    options_hi: ["<p>7854 रुपए</p>", "<p>7216 रुपए</p>",
                                "<p>7584 रुपए</p>", "<p>6816 रुपए</p>"],
                    solution_en: "<p>12.(c)<br>Let CP be 100%<br>90% = ₹5688<br>120% = <math display=\"inline\"><mfrac><mrow><mn>5688</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> &times; 120 = ₹7584</p>",
                    solution_hi: "<p>12.(c)<br>माना क्रय मूल्य 100% है<br>90% = ₹5688<br>120% = <math display=\"inline\"><mfrac><mrow><mn>5688</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> &times; 120 = ₹7584</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A merchant marks the price of his article 50 percent above the cost price. If he allows a 20 percent discount, then what is the profit or loss percentage?</p>",
                    question_hi: "<p>13. एक व्यापारी अपनी वस्तु का मूल्य क्रय मूल्य से 50 प्रतिशत अधिक अंकित करता है। यदि वह 20 प्रतिशत की छूट देता है, तो लाभ या हानि प्रतिशत कितना है?</p>",
                    options_en: ["<p>20 percent profit</p>", "<p>40 percent loss</p>", 
                                "<p>30 percent profit</p>", "<p>10 percent loss</p>"],
                    options_hi: ["<p>20 प्रतिशत लाभ</p>", "<p>40 प्रतिशत हानि</p>",
                                "<p>30 प्रतिशत लाभ</p>", "<p>10 प्रतिशत हानि</p>"],
                    solution_en: "<p>13.(a)<br>Net profit/loss% = 50 - 20 + <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>20</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 50 - 30 = 20% profit</p>",
                    solution_hi: "<p>13.(a)<br>शुद्ध लाभ/हानि% = 50 - 20 + <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>20</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 50 - 30 = 20% लाभ</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. If an article is sold at a loss of 20 percent, then what is the respective ratio of cost price and selling price of the article?</p>",
                    question_hi: "<p>14. यदि कोई वस्तु 20 प्रतिशत की हानि पर बेची जाती है, तो वस्तु के क्रय मूल्य और विक्रय मूल्य का क्रमशः अनुपात कितना है?</p>",
                    options_en: ["<p>7 : 4</p>", "<p>4 : 5</p>", 
                                "<p>5 : 4</p>", "<p>7 : 8</p>"],
                    options_hi: ["<p>7 : 4</p>", "<p>4 : 5</p>",
                                "<p>5 : 4</p>", "<p>7 : 8</p>"],
                    solution_en: "<p>14.(c)<br>20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>&#8594;</mo><mi>l</mi><mi>o</mi><mi>s</mi><mi>s</mi></mrow><mrow><mn>5</mn><mo>&#8594;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> Cost price : Selling price = 5 : 5 - 1 = 5 : 4</p>",
                    solution_hi: "<p>14.(c)<br>20% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#8594;</mo><mo>&#160;</mo><mi>&#2361;&#2366;&#2344;&#2367;</mi></mrow><mrow><mn>5</mn><mo>&#8594;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> क्रय मूल्य : विक्रय मूल्य = 5 : 5 - 1 = 5 : 4</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Cost price of an article is Rs. 360. If the profit percentage is 8 percent, then what is the value of profit</p>",
                    question_hi: "<p>15. एक वस्तु का क्रय मूल्य 360 रुपए है। यदि लाभ प्रतिशत 8 प्रतिशत है, तो लाभ का मूल्य कितना है?</p>",
                    options_en: ["<p>Rs. 30</p>", "<p>Rs 25.6</p>", 
                                "<p>Rs. 28.8</p>", "<p>Rs. 24.5</p>"],
                    options_hi: ["<p>30 रुपए</p>", "<p>25.6 रुपए</p>",
                                "<p>28.8 रुपए</p>", "<p>24.5 रुपए</p>"],
                    solution_en: "<p>15.(c)<br>8% = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#8594;</mo><mi>P</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi></mrow><mrow><mn>25</mn><mo>&#8594;</mo><mi>C</mi><mi>P</mi></mrow></mfrac></math><br>25 unit = ₹360<br>2 unit = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 2 = ₹28.8</p>",
                    solution_hi: "<p>15.(c)<br>8% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mo>&#8594;</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi></mrow><mrow><mn>25</mn><mo>&#8594;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math><br>25 इकाई = ₹360<br>2 इकाई = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 2 = ₹28.8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>