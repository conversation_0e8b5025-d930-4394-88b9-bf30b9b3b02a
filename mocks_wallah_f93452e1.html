<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">If tanx </span><span style=\"font-family: Cambria Math;\">= - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> where x</span><span style=\"font-family: Cambria Math;\"> lies in the second quadrant. </span><span style=\"font-family: Cambria Math;\">what</span><span style=\"font-family: Cambria Math;\"> is the value of sinx </span><span style=\"font-family: Cambria Math;\">- cotx </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> tanx = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">जहाँ</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Nirmala UI;\">दूसरे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">चतुर्थांश</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">स्थित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sinx</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">cotx</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कितना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>209</mn><mn>156</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>169</mn><mn>156</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>156</mn><mn>209</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>169</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>209</mn><mn>156</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>169</mn><mn>156</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>156</mn><mn>209</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>169</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\n<p><span style=\"font-family: Cambria Math;\">Pythagoras triplets :- (</span><span style=\"font-family: Cambria Math;\">5 ,</span><span style=\"font-family: Cambria Math;\"> 12 , 13)</span></p>\n<p><span style=\"font-family: Cambria Math;\">tanx</span><span style=\"font-family: Cambria Math;\"> = - </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mi>Perpendicular</mi><mi>Base</mi></mfrac></math> </span></p>\n<p><span style=\"font-family: Cambria Math;\">Hypotenuse = 13</span></p>\n<p><span style=\"font-family: Cambria Math;\">In II </span><span style=\"font-family: Cambria Math;\">quadrant ,</span><span style=\"font-family: Cambria Math;\"> only value of sin x</span><span style=\"font-family: Cambria Math;\"> and cosec x</span><span style=\"font-family: Cambria Math;\"> is +ve.</span></p>\n<p><span style=\"font-family: Cambria Math;\">Now ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sinx - </span><span style=\"font-family: Cambria Math;\">cotx =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac><mo>-</mo><mo>(</mo><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>12</mn></mfrac><mo>)</mo></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mn>144</mn><mo>+</mo><mn>65</mn></mrow><mrow><mn>156</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>209</mn><mn>156</mn></mfrac></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">पाइथागोरस त्रिक</span> :- (</span><span style=\"font-family: Cambria Math;\">5 ,</span><span style=\"font-family: Cambria Math;\"> 12 , 13)</span></p>\n<p><span style=\"font-family: Cambria Math;\">tanx</span><span style=\"font-family: Cambria Math;\"> = - </span><span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math> </span></p>\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">कर्ण </span>&nbsp;= 13</span></p>\n<p><span style=\"font-weight: 400;\">द्वितीय चतुर्थांश में, केवल sin </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> और cosec </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> का मान +ve&nbsp; होता है।</span></p>\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">अब</span> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sinx - </span><span style=\"font-family: Cambria Math;\">cotx = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac><mo>-</mo><mo>(</mo><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>12</mn></mfrac><mo>)</mo></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mn>144</mn><mo>+</mo><mn>65</mn></mrow><mrow><mn>156</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>209</mn><mn>156</mn></mfrac></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> The value of tan5&deg;tan25&deg;tan45&deg;tan65&deg;tan85&deg; is equal to</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">tan5&deg;</span><span style=\"font-family: Cambria Math;\">tan25&deg;tan45&deg;tan65&deg;tan85&deg; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> _______ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>4<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>3</p>\n", 
                                "<p>1</p>\n", "<p>2</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>3</p>\n",
                                "<p>1</p>\n", "<p>2</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">If A+B = 90</span><span style=\"font-family: Cambria Math;\">&deg; ,</span><span style=\"font-family: Cambria Math;\"> then </span><span style=\"font-family: Cambria Math;\">TanA</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\">TanB</span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tan5&deg;tan25&deg;tan45</span><span style=\"font-family: Cambria Math;\">&deg;tan65&deg;tan85&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= (tan5&deg; &times; tan85&deg;) &times; (tan25&deg; &times; tan65&deg;) &times; tan45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 1 &times; 1 &times; 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> A</span><span style=\"font-family: Cambria Math;\">+B = 90&deg; , </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">TanA</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\">TanB</span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tan5&deg;tan25&deg;tan45&deg;tan65&deg;tan85&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= (tan5&deg; &times; tan85&deg;) &times; (tan25&deg; &times; tan65&deg;) &times; tan45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 1 &times; 1 &times; 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 1</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> Which of the follow</span><span style=\"font-family: Cambria Math;\">ing options gives an expression equivalent to </span><span style=\"font-family: Cambria Math;\">sin(</span><span style=\"font-family: Cambria Math;\">A + B)?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sin(</span><span style=\"font-family: Cambria Math;\">A + B) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2340;&#2369;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2369;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">cosAcosB</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">sinAsinB</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">sinAcosB</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cosAsinB</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">cosAcosB</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">sinAsinB</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">sinAcosB</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">cosAsinB</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">cosAcosB</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">sinAsinB</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">sinAcosB</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cosAsinB</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">cosAcosB</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">sinAsinB</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">sinAcosB</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">cosAsinB</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin(A+B) = </span><span style=\"font-family: Cambria Math;\">sinA</span><span style=\"font-family: Cambria Math;\">cosB</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cosA</span><span style=\"font-family: Cambria Math;\">sinB</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin(A+B) = </span><span style=\"font-family: Cambria Math;\">sinA</span><span style=\"font-family: Cambria Math;\">cosB</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cosA</span><span style=\"font-family: Cambria Math;\">sinB</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> If </span><span style=\"font-family: Cambria Math;\">tanx</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mi>sinx</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>42</mn><mn>5</mn></mfrac></mstyle><mi>cosx</mi></mrow><mrow><mn>15</mn><mi>sinx</mi><mo>+</mo><mn>21</mn><mi>cosx</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">tanx</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mi>sinx</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>42</mn><mn>5</mn></mfrac></mstyle><mi>cosx</mi></mrow><mrow><mn>15</mn><mi>sinx</mi><mo>+</mo><mn>21</mn><mi>cosx</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कीजिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">।</span></p>",
                    options_en: ["<p>0</p>", "<p>1</p>", 
                                "<p>0.1</p>", "<p>0.5</p>"],
                    options_hi: ["<p>0</p>", "<p>1</p>",
                                "<p>0.1</p>", "<p>0.5</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">tanx = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mi>sinx</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>42</mn><mn>5</mn></mfrac></mstyle><mi>cosx</mi></mrow><mrow><mn>15</mn><mo>&#160;</mo><mi>sinx</mi><mo>+</mo><mn>21</mn><mo>&#160;</mo><mi>cosx</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>9</mn><mi>tanx</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>42</mn><mn>5</mn></mfrac></mstyle><mo>&#160;</mo></mrow><mrow><mn>15</mn><mi>tanx</mi><mo>+</mo><mn>21</mn><mo>&#160;</mo></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>5</mn></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>42</mn><mn>5</mn></mfrac></mstyle></mrow><mrow><mn>15</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>5</mn></mfrac></mstyle><mo>+</mo><mn>21</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>63</mn><mo>-</mo><mn>42</mn></mrow><mrow><mn>105</mn><mo>+</mo><mn>105</mn></mrow></mfrac><mo>=</mo><mfrac><mn>21</mn><mn>210</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>1</mn></math></p>\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">tanx = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mi>sinx</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>42</mn><mn>5</mn></mfrac></mstyle><mi>cosx</mi></mrow><mrow><mn>15</mn><mo>&#160;</mo><mi>sinx</mi><mo>+</mo><mn>21</mn><mo>&#160;</mo><mi>cosx</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>9</mn><mi>tanx</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>42</mn><mn>5</mn></mfrac></mstyle><mo>&#160;</mo></mrow><mrow><mn>15</mn><mi>tanx</mi><mo>+</mo><mn>21</mn><mo>&#160;</mo></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>5</mn></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>42</mn><mn>5</mn></mfrac></mstyle></mrow><mrow><mn>15</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>5</mn></mfrac></mstyle><mo>+</mo><mn>21</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>63</mn><mo>-</mo><mn>42</mn></mrow><mrow><mn>105</mn><mo>+</mo><mn>105</mn></mrow></mfrac><mo>=</mo><mfrac><mn>21</mn><mn>210</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>1</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">sin&theta;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\">, then the value of <span style=\"font-weight: 400;\">sin&sup3;</span><span style=\"font-weight: 400;\">&theta;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">cosec&sup3;&theta;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= __________.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sin&theta;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">sin&sup3;</span><span style=\"font-weight: 400;\">&theta;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">cosec&sup3;&theta;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> _______ </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>0</p>\n", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>5</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n"],
                    options_hi: ["<p>0</p>\n", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>5</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-weight: 400;\">sin&theta;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">cosec&theta;</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span><span style=\"font-weight: 400;\">&nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">sin&theta;</span><span style=\"font-weight: 400;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>sin&theta;</mi></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;=&nbsp; </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span></p>\r\n<p><span style=\"font-weight: 400;\">Then ,&nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">sin&sup3;</span><span style=\"font-weight: 400;\">&theta;</span><span style=\"font-weight: 400;\"> + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&theta;</mi></mrow></mfrac></math><span style=\"font-weight: 400;\"> = (</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>)&sup3; </span><span style=\"font-weight: 400;\">- 3 &times; </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span><span style=\"font-weight: 400;\">&nbsp; = 2</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-weight: 400;\">sin&theta;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">cosec&theta;</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span><span style=\"font-weight: 400;\">&nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">sin&theta;</span><span style=\"font-weight: 400;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>sin&theta;</mi></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;=&nbsp; </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span></p>\r\n<p><span style=\"font-weight: 400;\">&#2340;&#2348;,&nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">sin&sup3;</span><span style=\"font-weight: 400;\">&theta;</span><span style=\"font-weight: 400;\"> + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&theta;</mi></mrow></mfrac></math><span style=\"font-weight: 400;\"> = (</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>)&sup3; </span><span style=\"font-weight: 400;\">- 3 &times; </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span><span style=\"font-weight: 400;\">&nbsp; = 2</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> , then what is the value of cosec&theta; ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>12</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>13</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>12</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>13</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &hellip;..</span><span style=\"font-family: Cambria Math;\">e.q .(</span><span style=\"font-family: Cambria Math;\">1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">cot&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &hellip;..</span><span style=\"font-family: Cambria Math;\">e.q .(</span><span style=\"font-family: Cambria Math;\">2)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Adding </span><span style=\"font-family: Cambria Math;\">e.q</span><span style=\"font-family: Cambria Math;\"> . (1) and </span><span style=\"font-family: Cambria Math;\">e.q</span><span style=\"font-family: Cambria Math;\"> .</span><span style=\"font-family: Cambria Math;\"> (2</span><span style=\"font-family: Cambria Math;\">) ,</span><span style=\"font-family: Cambria Math;\"> we get </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2cosec&theta; = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>13</mn><mn>6</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mrow><mn>6</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>13</mn><mn>12</mn></mfrac></math> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2351;&#2342;&#2367;,</span> </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &hellip;..</span><span style=\"font-weight: 400;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2340;&#2348; ,</span> </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">cot&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &hellip;..<span style=\"font-weight: 400;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;&nbsp; (2)</span></span></p>\r\n<p><span style=\"font-weight: 400;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;&nbsp; (1) &#2324;&#2352; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339;&nbsp; (2) &#2325;&#2379; &#2332;&#2379;&#2396;&#2344;&#2375; &#2346;&#2352;,&nbsp; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2cosec&theta; = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>13</mn><mn>6</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2340;&#2307;</span>, </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mrow><mn>6</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>13</mn><mn>12</mn></mfrac></math> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">acot</span><span style=\"font-family: Cambria Math;\"> = b, then what will be the value </span><span style=\"font-family: Cambria Math;\">of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>bcos&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>asin&#952;</mi></mrow><mrow><mi>bcos&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>asin&#952;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Nirmala UI;\">यद</span><span style=\"font-family: Nirmala UI;\">ि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">acot</span><span style=\"font-family: Cambria Math;\"> = b </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>bcos&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>asin&#952;</mi></mrow><mrow><mi>bcos&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>asin&#952;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">होगा</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><span style=\"font-weight: 400;\">b&sup2;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">a&sup2;</span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p>0</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><span style=\"font-weight: 400;\">b&sup2;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">a&sup2;</span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p>0</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\n<p><span style=\"font-family: Cambria Math;\">Given, cot&theta;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mi>Base</mi><mi>Perpendicular</mi></mfrac></math> </span></p>\n<p><span style=\"font-family: Cambria Math;\">By using </span><span style=\"font-family: Cambria Math;\">pythagoras</span><span style=\"font-family: Cambria Math;\"> theorem,</span></p>\n<p><span style=\"font-family: Cambria Math;\">Hypotenuse =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></msqrt></math></span></p>\n<p><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>,</mo><mo>&#160;</mo><mfrac><mrow><mi>bcos&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>asin&#952;</mi></mrow><mrow><mi>bcos&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>asin&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mi mathvariant=\"normal\">b</mi><mo>&#215;</mo><mi mathvariant=\"normal\">b</mi></mrow><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></msqrt></mfrac><mo>-</mo><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>&#215;</mo><mi mathvariant=\"normal\">a</mi></mrow><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></msqrt></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mi mathvariant=\"normal\">b</mi><mo>&#215;</mo><mi mathvariant=\"normal\">b</mi></mrow><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></msqrt></mfrac><mo>+</mo><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>&#215;</mo><mi mathvariant=\"normal\">a</mi></mrow><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo></msqrt></mfrac></mstyle></mfrac><mspace linebreak=\"newline\"/><mo>=</mo><mo>&#160;</mo><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">दिया गया है,</span> cot&theta;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2354;&#2306;&#2348;</mi></mfrac></math> </span></p>\n<p><span style=\"font-weight: 400;\">पाइथागोरस प्रमेय का उपयोग करके,</span></p>\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">कर्ण</span> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></msqrt></math></span></p>\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>,</mo><mo>&#160;</mo><mfrac><mrow><mi>bcos&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>asin&#952;</mi></mrow><mrow><mi>bcos&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>asin&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mi mathvariant=\"normal\">b</mi><mo>&#215;</mo><mi mathvariant=\"normal\">b</mi></mrow><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></msqrt></mfrac><mo>-</mo><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>&#215;</mo><mi mathvariant=\"normal\">a</mi></mrow><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></msqrt></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mi mathvariant=\"normal\">b</mi><mo>&#215;</mo><mi mathvariant=\"normal\">b</mi></mrow><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></msqrt></mfrac><mo>+</mo><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>&#215;</mo><mi mathvariant=\"normal\">a</mi></mrow><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo></msqrt></mfrac></mstyle></mfrac><mspace linebreak=\"newline\"/><mo>=</mo><mo>&#160;</mo><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">Simplify the given expression.</span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math></span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">दिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">गए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">व्यंजक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">सरलीकरण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कीजिए</span><span style=\"font-family: Nirmala UI;\">।</span></p>\n<p><span style=\"font-family: Nirmala UI;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math></span></p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">Let &theta;</span><span style=\"font-family: Cambria Math;\"> = 45&deg; </span></p>\n<p><span style=\"font-family: Cambria Math;\">According to question,</span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mi>sin</mi><mn>4</mn></msup><mn>45</mn><mo>&#176;</mo><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mn>45</mn><mo>&#176;</mo></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo><mo>+</mo><msup><mi>sin</mi><mn>4</mn></msup><mn>45</mn><mo>&#176;</mo></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mo>&#160;</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup><mo>&#160;</mo><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><mn>4</mn></mfrac><mo>&#160;</mo></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></mfrac><mo>=</mo><mo>&#160;</mo><mn>2</mn></math></p>\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">माना</span> &theta;</span><span style=\"font-family: Cambria Math;\"> = 45&deg;</span></p>\n<p><span style=\"font-weight: 400;\">प्रश्न के अनुसार,</span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mi>sin</mi><mn>4</mn></msup><mn>45</mn><mo>&#176;</mo><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mn>45</mn><mo>&#176;</mo></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo><mo>+</mo><msup><mi>sin</mi><mn>4</mn></msup><mn>45</mn><mo>&#176;</mo></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mo>&#160;</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup><mo>&#160;</mo><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><mn>4</mn></mfrac><mo>&#160;</mo></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></mfrac><mo>=</mo><mo>&#160;</mo><mn>2</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> Using cosec(&alpha;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">+ &beta;</span><span style=\"font-family: Cambria Math;\">) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec&alpha;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>sec&beta;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>cosec&alpha;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>cosec&beta;</mi></mrow><mrow><mi>sec&alpha;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>cosec&beta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cosec&alpha;</mi><mo>&nbsp;</mo><mo>&times;</mo><mi>sec&beta;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> , find the value of cosec75&deg;.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\">cosec(&alpha;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">+ &beta;</span><span style=\"font-family: Cambria Math;\">) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec&alpha;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>sec&beta;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>cosec&alpha;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>cosec&beta;</mi></mrow><mrow><mi>sec&alpha;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>cosec&beta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cosec&alpha;</mi><mo>&nbsp;</mo><mo>&times;</mo><mi>sec&beta;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> , <span style=\"font-weight: 400;\">&#2325;&#2375; &#2313;&#2346;&#2351;&#2379;&#2327; &#2360;&#2375;,</span> cosec75&deg; <span style=\"font-weight: 400;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>6</mn></msqrt><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>6</mn></msqrt><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></math> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></math> </span></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>6</mn></msqrt><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>6</mn></msqrt><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></math> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></math> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cosec</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">&#945;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">&#946;</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mi>sec&#945;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>sec&#946;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec&#945;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec&#946;</mi></mrow><mrow><mi>sec&#945;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec&#946;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosec&#945;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>sec&#946;</mi></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cosec</mi><mo>&#160;</mo><mo>(</mo><mn>45</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>30</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mi>sec</mi><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>sec</mi><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec</mi><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec</mi><mn>30</mn><mo>&#176;</mo></mrow><mrow><mi>sec</mi><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec</mi><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosec</mi><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>sec</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac><mo>&#160;</mo></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cosec</mi><mn>75</mn><mo>&#176;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle><mo>&#215;</mo><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mn>2</mn></mrow><mrow><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle><mo>&#160;</mo></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>&#215;</mo><mfrac><msqrt><mo>&#160;</mo><mn>3</mn></msqrt><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow></mfrac><mo>&#160;</mo></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><msqrt><mn>2</mn></msqrt><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><msqrt><mo>&#160;</mo><mn>6</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msqrt><mn>2</mn></msqrt><mo>&#160;</mo></math></p>\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cosec</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">&#945;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">&#946;</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mi>sec&#945;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>sec&#946;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec&#945;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec&#946;</mi></mrow><mrow><mi>sec&#945;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec&#946;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosec&#945;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>sec&#946;</mi></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cosec</mi><mo>&#160;</mo><mo>(</mo><mn>45</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>30</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mi>sec</mi><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>sec</mi><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec</mi><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec</mi><mn>30</mn><mo>&#176;</mo></mrow><mrow><mi>sec</mi><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>cosec</mi><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosec</mi><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>sec</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac><mo>&#160;</mo></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cosec</mi><mn>75</mn><mo>&#176;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle><mo>&#215;</mo><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mn>2</mn></mrow><mrow><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle><mo>&#160;</mo></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>&#215;</mo><mfrac><msqrt><mo>&#160;</mo><mn>3</mn></msqrt><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow></mfrac><mo>&#160;</mo></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><msqrt><mn>2</mn></msqrt><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><msqrt><mo>&#160;</mo><mn>6</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msqrt><mn>2</mn></msqrt><mo>&#160;</mo></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If tan&alpha;&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 6 then sec&alpha;&nbsp;</span><span style=\"font-family: Cambria Math;\"> ________equals to:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> tan&alpha;&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 6 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> sec&alpha;&nbsp;</span><span style=\"font-family: Cambria Math;\"> ________</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>37</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>35</mn></msqrt></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>37</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>35</mn></msqrt></math></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan&alpha;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>sec&alpha;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&alpha;</mi></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>36</mn></msqrt><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>37</mn></msqrt><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan&alpha;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>sec&alpha;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&alpha;</mi></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>36</mn></msqrt><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>37</mn></msqrt><mo>&nbsp;</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If cotA = 1, sinB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></span><span style=\"font-family: Cambria Math;\"> then find the value of </span><span style=\"font-family: Cambria Math;\">sin(</span><span style=\"font-family: Cambria Math;\">A + B) - cot(A + B).</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> cot</span><span style=\"font-family: Cambria Math;\">A = 1, sinB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> sin(A + B) - cot(A + B) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>1 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", 
                                "<p>0</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>1 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n",
                                "<p>0</p>\n", "<p>1</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cot</span><span style=\"font-family: Cambria Math;\">A = 1 &rArr;</span><span style=\"font-family: Cambria Math;\"> A = 45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sin</span><span style=\"font-family: Cambria Math;\">B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &rArr;</span><span style=\"font-family: Cambria Math;\"> B = 45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now ,</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sin</span><span style=\"font-family: Cambria Math;\">(A + B) - </span><span style=\"font-family: Cambria Math;\">cot(A + B)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 1 - </span><span style=\"font-family: Cambria Math;\">0 = 1 </span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cot</span><span style=\"font-family: Cambria Math;\">A = 1 &rArr;</span><span style=\"font-family: Cambria Math;\"> A = 45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sin</span><span style=\"font-family: Cambria Math;\">B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &rArr;</span><span style=\"font-family: Cambria Math;\"> B = 45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2348;</span> ,</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sin</span><span style=\"font-family: Cambria Math;\">(A + B) - </span><span style=\"font-family: Cambria Math;\">cot(A + B)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 1 - </span><span style=\"font-family: Cambria Math;\">0 = 1 </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Find the value of tan (-1125&deg;).</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">tan</span><span style=\"font-family: Cambria Math;\"> (-1125&deg;)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>1</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>-1</p>\n", "<p>0</p>\n"],
                    options_hi: ["<p>1</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>-1</p>\n", "<p>0</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">tan</span><span style=\"font-family: Cambria Math;\"> (-1125&deg;)</span><span style=\"font-family: Cambria Math;\"> = - </span><span style=\"font-family: Cambria Math;\">tan(3 &times; 360 + 45&deg; ) = - </span><span style=\"font-family: Cambria Math;\">tan(45&deg;) = - </span><span style=\"font-family: Cambria Math;\">1 </span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">tan</span><span style=\"font-family: Cambria Math;\"> (-1125&deg;)</span><span style=\"font-family: Cambria Math;\"> = - </span><span style=\"font-family: Cambria Math;\">tan(3 &times; 360 + 45&deg; ) = - </span><span style=\"font-family: Cambria Math;\">tan(45&deg;) = - </span><span style=\"font-family: Cambria Math;\">1 </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">sec&theta;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">sec&theta;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = ?</span></p>\n",
                    options_en: ["<p>-1</p>\n", "<p>1</p>\n", 
                                "<p>&infin;<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>0</p>\n"],
                    options_hi: ["<p>-1</p>\n", "<p>1</p>\n",
                                "<p>&infin;<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>0</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sec&theta;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= sec&theta;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo></msqrt></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">sec&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> &times; cos&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sec&theta;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= sec&theta;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo></msqrt></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">sec&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> &times; cos&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> The value of which of the following is different from the other options?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>sin90&deg;</p>\n", "<p>sec60&deg;</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">cos</span><span style=\"font-family: Cambria Math;\">0&deg; </span></p>\n", "<p>tan45&deg;</p>\n"],
                    options_hi: ["<p>sin90&deg;</p>\n", "<p>sec60&deg;</p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">cos</span><span style=\"font-family: Cambria Math;\">0&deg; </span></p>\n", "<p>tan45&deg;</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(b) </span><span style=\"font-family: Cambria Math;\">As per option,</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) sin 90&deg;&nbsp; = 1</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) sec 60&deg;&nbsp; = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>\r\n<p><span style=\"font-weight: 400;\">(c) cos 0&deg; = 1&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) tan 45&deg;&nbsp; = 1&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">so ,</span><span style=\"font-family: Cambria Math;\"> option &lsquo;b&rsquo; different from the other options.</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(b) <span style=\"font-weight: 400;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></span></p>\r\n<p><span style=\"font-weight: 400;\">(a) sin 90&deg;&nbsp; = 1</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) sec 60&deg;&nbsp; = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>\r\n<p><span style=\"font-weight: 400;\">(c) cos 0&deg; = 1&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) tan 45&deg;&nbsp; = 1&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; \'b\' &#2309;&#2344;&#2381;&#2351; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2360;&#2375; &#2309;&#2354;&#2327; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow></mfrac></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is :</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow></mfrac></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">होगा</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>sec&theta;<span style=\"font-family: Cambria Math;\"> + tan&theta;</span></p>", "<p>cosec&theta;<span style=\"font-family: Cambria Math;\"> - cot&theta;</span></p>", 
                                "<p>cosec&theta;<span style=\"font-family: Cambria Math;\"> + cot&theta;</span></p>", "<p>sec&theta;<span style=\"font-family: Cambria Math;\"> - tan&theta;</span></p>"],
                    options_hi: ["<p>sec&theta;<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> + tan&theta; </span></p>", "<p>cosec&theta;<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> - cot&theta; </span></p>",
                                "<p>cosec&theta;<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> + cot&theta; </span></p>", "<p>sec&theta;<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> - tan&theta; </span></p>"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(c)</span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow></mfrac></msqrt><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><msqrt><mfrac><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo></mrow></mfrac></msqrt><mo>&#160;</mo></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><msqrt><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></msqrt><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><msqrt><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo></mrow></mfrac></msqrt></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow><mi>sin&#952;</mi></mfrac><mo>=</mo><mi>cosec&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cot&#952;</mi></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(c)</span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow></mfrac></msqrt><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><msqrt><mfrac><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo></mrow></mfrac></msqrt><mo>&#160;</mo></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><msqrt><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></msqrt><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><msqrt><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo></mrow></mfrac></msqrt></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow><mi>sin&#952;</mi></mfrac><mo>=</mo><mi>cosec&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cot&#952;</mi></math></span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>