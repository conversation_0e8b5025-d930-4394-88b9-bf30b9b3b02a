<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The salary of a worker is first increased by 30% and later reduced by 28.5%. What is the net change in his salary ?</p>",
                    question_hi: "<p>1. एक कर्मचारी का वेतन पहले 30% बढ़ाया गया और बाद में 28.5% कम कर दिया गया। उसके वेतन में निवल परिवर्तन कितना हुआ ?</p>",
                    options_en: ["<p>9.05% decrease</p>", "<p>7.05% decrease</p>", 
                                "<p>6.05% increase</p>", "<p>8.05% increase</p>"],
                    options_hi: ["<p>9.05% की कमी</p>", "<p>7.05% की कमी</p>",
                                "<p>6.05% की वृद्धि</p>", "<p>8.05% की वृद्धि</p>"],
                    solution_en: "<p>1.(b) Net change in the salary = +30 - 28.5 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mo>-</mo><mn>28</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> <br>= 1.5 - 8.55 = - 7.05 %<br>Hence, salary of worker decrease will be 7.05 %</p>",
                    solution_hi: "<p>1.(b) वेतन में शुद्ध परिवर्तन = +30 - 28.5 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mo>-</mo><mn>28</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math><br>= 1.5 - 8.55 = - 7.05 %<br>अत: कर्मचारी के वेतन में 7.05% की कमी होगी</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If the price of a commodity is increased by 10% and 20% successively, what is the net percentage increase/decrease in its price, if it is further decreased by 25% ?</p>",
                    question_hi: "<p>2. यदि किसी वस्तु की कीमत में क्रमश: 10% और 20% की वृद्धि होती है, तो इसकी कीमत में निवल प्रतिशत वृद्धि/कमी क्या है, यदि इसमें 25% की कमी और की जाए ?</p>",
                    options_en: ["<p>1% Decrease</p>", "<p>5% Decrease</p>", 
                                "<p>5% Increase</p>", "<p>1% Increase</p>"],
                    options_hi: ["<p>1% की कमी</p>", "<p>5% की कमी</p>",
                                "<p>5% की वृद्धि</p>", "<p>1% की वृद्धि</p>"],
                    solution_en: "<p>2.(a) used formula = <math display=\"inline\"><mi>x</mi></math> + y + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mi>y</mi></mrow><mn>100</mn></mfrac></math><br>Two successively discount = 10 + 20 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math> <br>= 30 + 2 = 32<br>Then, discount = 32 - 25 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mo>&#215;</mo><mo>-</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> <br>= 7 - 8 = -1<br>Hence, the net percentage decrease by 1 %</p>",
                    solution_hi: "<p>2.(a) प्रयुक्त सूत्र = <math display=\"inline\"><mi>x</mi></math> + y + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mi>y</mi></mrow><mn>100</mn></mfrac></math><br>दो क्रमिक छूट = 10 + 20 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math> <br>= 30 + 2 = 32<br>फिर, छूट = 32 - 25 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mo>&#215;</mo><mo>-</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> <br>= 7 - 8 = -1<br>इसलिए, शुद्ध प्रतिशत में 1% की कमी हुई</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A shopkeeper claims that he sells rock salt at ₹23 per kg, which costs him ₹25 per kg. But he gives 800 g instead of 1,000 g. What is his percentage profit or loss ?</p>",
                    question_hi: "<p>3. एक दुकानदार दावा करता है कि वह सेंधा नमक ₹23 प्रति kg की दर से बेचता है, जिसकी कीमत उसे ₹25 प्रति kg पड़ती है। लेकिन वह 1,000 g की जगह 800 g ही देता है। उसका लाभ या हानि प्रतिशत क्या है ?</p>",
                    options_en: ["<p>Profit of 8%</p>", "<p>Loss of 3%</p>", 
                                "<p>Loss of 5%</p>", "<p>Profit of 15%</p>"],
                    options_hi: ["<p>8% का लाभ</p>", "<p>3% की हानि</p>",
                                "<p>5% की हानि</p>", "<p>15% का लाभ</p>"],
                    solution_en: "<p>3.(d) <br>Ratio&nbsp; &nbsp; -&nbsp; CP&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;SP<br>Cost&nbsp; &nbsp; &nbsp;-&nbsp; 25&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;23<br>Weight -&nbsp; 800g&nbsp; :&nbsp; &nbsp;1000g<br>&mdash;-------------------------------<br>Final&nbsp; &nbsp; -&nbsp; &nbsp;200&nbsp; &nbsp;:&nbsp; &nbsp; 230<br>So,<br>Required profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>230</mn><mo>-</mo><mn>200</mn></mrow><mn>200</mn></mfrac></math> &times; 100 = 15 %</p>",
                    solution_hi: "<p>3.(d) <br>अनुपात - क्रय मूल्य&nbsp; :&nbsp; विक्रय मूल्य<br>लागत&nbsp; &nbsp;-&nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;23<br>भार&nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; 800g&nbsp; &nbsp; :&nbsp; &nbsp; 1000g<br>&mdash;------------------------------------<br>अंतिम&nbsp; &nbsp;-&nbsp; &nbsp; &nbsp;200&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 230<br>इसलिए,<br>अभीष्ट लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>230</mn><mo>-</mo><mn>200</mn></mrow><mn>200</mn></mfrac></math> &times; 100 = 15 %</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A car&rsquo;s radiator should contain a solution of 52% antifreeze. A motorist has 8 litres of 40% antifreeze. How many litres (rounded off to 2 decimal places) of 95% antifreeze liquid should he add to his solution to produce a 52% antifreeze ?</p>",
                    question_hi: "<p>4. एक कार के रेडिएटर में 52% एंटीफ्रीज का घोल होना चाहिए। एक मोटर चालक के पास 40% एंटीफ्रीज वाला 8 लीटर घोल है। 52% एंटीफ्रीज बनाने लिए उसे अपने घोल में 95% एंटीफ्रीज तरल की कितनी लीटर मात्रा मिलानी होगी (दो दशमलव स्थान तक पूर्णांकित) ?</p>",
                    options_en: ["<p>1.92</p>", "<p>3.45</p>", 
                                "<p>4.26</p>", "<p>2.23</p>"],
                    options_hi: ["<p>1.92</p>", "<p>3.45</p>",
                                "<p>4.26</p>", "<p>2.23</p>"],
                    solution_en: "<p>4.(d) By using allegation method <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738993200472.png\" alt=\"rId4\" width=\"215\" height=\"94\"><br>&rArr; 43 Unit = 8 liters<br>&rArr; 12 Unit =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>43</mn></mfrac></math> &times; 12 = 2.23</p>",
                    solution_hi: "<p>4.(d) एलिगेशन विधि का उपयोग करके <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738993200591.png\" alt=\"rId5\" width=\"193\" height=\"95\"><br>&rArr; 43 इकाई = 8 लीटर&nbsp;<br>&rArr; 12 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>43</mn></mfrac></math> &times; 12 = 2.23</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. During the first year, the population of a village increased by 5%, and during the second year, it reduced by 6%. At the end of the second year, its population was 35,532. What was the population at the beginning of the first year ?</p>",
                    question_hi: "<p>5. पहले वर्ष के दौरान एक गाँव की जनसंख्या में 5% की वृद्धि हुई और दूसरे वर्ष के दौरान इसमें 6% की कमी आई। दूसरे वर्ष के अंत में इसकी जनसंख्या 35,532 थी। पहले वर्ष की शुरुआत में जनसंख्या कितनी थी ?</p>",
                    options_en: ["<p>38,000</p>", "<p>36,000</p>", 
                                "<p>37,568</p>", "<p>34,567</p>"],
                    options_hi: ["<p>38,000</p>", "<p>36,000</p>",
                                "<p>37,568</p>", "<p>34,567</p>"],
                    solution_en: "<p>5.(b) Let the population at the beginning of the first year = x<br>x &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>50</mn></mfrac></math> = 35,532<br>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35532</mn><mo>&#215;</mo><mn>50</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>21</mn><mo>&#215;</mo><mn>47</mn></mrow></mfrac></math> = 36000</p>",
                    solution_hi: "<p>5.(b) माना पहले वर्ष की शुरुआत में जनसंख्या = x<br>x &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>50</mn></mfrac></math> = 35,532<br>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35532</mn><mo>&#215;</mo><mn>50</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>21</mn><mo>&#215;</mo><mn>47</mn></mrow></mfrac></math> = 36000</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If Mohan&rsquo;s income is 20% less than Arvind&rsquo;s income, then how much per cent is Arvind&rsquo;s income more than Mohan&rsquo;s income ?</p>",
                    question_hi: "<p>6. यदि मोहन की आय अरविंद की आय से 20% कम है, तो अरविंद की आय मोहन की आय से कितने प्रतिशत अधिक है ?</p>",
                    options_en: ["<p>30%</p>", "<p>20%</p>", 
                                "<p>15%</p>", "<p>25%</p>"],
                    options_hi: ["<p>30%</p>", "<p>20%</p>",
                                "<p>15%</p>", "<p>25%</p>"],
                    solution_en: "<p>6.(d) Income &rarr;&nbsp;Mohan : Arvind<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;80&nbsp; &nbsp; &nbsp;:&nbsp; 100 or 4 : 5<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>6.(d) आय &rarr;&nbsp; मोहन : अरविंद<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;80&nbsp; &nbsp;:&nbsp; &nbsp;100 या 4 : 5<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. When the price of an article was reduced by 10% its sale increased by 60%. What was the net effect on the revenue ?</p>",
                    question_hi: "<p>7. जब एक वस्तु की कीमत 10% कम कर दी जाती है, तो उसकी बिक्री 60% बढ़ जाती है। आमदनी पर निवल प्रभाव क्या होगा ?</p>",
                    options_en: ["<p>Decrease of 24%</p>", "<p>Increase of 44%</p>", 
                                "<p>Decrease of 44%</p>", "<p>Increase of 24%</p>"],
                    options_hi: ["<p>24% की कमी</p>", "<p>44% की वृद्धि</p>",
                                "<p>44% की कमी</p>", "<p>24% की वृद्धि</p>"],
                    solution_en: "<p>7.(b) Net effect on the revenue = -10 + 60 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>100</mn></mfrac></math> = 44% (increase)</p>",
                    solution_hi: "<p>7.(b) आमदनी पर निवल प्रभाव = -10 + 60 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>100</mn></mfrac></math> = 44% (वृद्धि)</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A number is increased by 24% and then decreased by 25%. What is the net increase or decrease percentage ?</p>",
                    question_hi: "<p>8. एक संख्या में 24% की वृद्धि की जाती है और फिर 25% की कमी की जाती है। निवल वृद्धि या कमी प्रतिशत क्या है ?</p>",
                    options_en: ["<p>Decrease 8%</p>", "<p>Increase 7%</p>", 
                                "<p>Increase 8%</p>", "<p>Decrease 7%</p>"],
                    options_hi: ["<p>8% की कमी</p>", "<p>7% की वृद्धि</p>",
                                "<p>8% की वृद्धि</p>", "<p>7% की कमी</p>"],
                    solution_en: "<p>8.(d) Net decrease/ increase percentage = 24 - 25 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> = -7% (Decrease)</p>",
                    solution_hi: "<p>8.(d) शुद्ध कमी/वृद्धि प्रतिशत = 24 - 25 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> = -7% (कमी)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The rate of oil is increased by 12% every year. The rate in the next year would be ₹125.44 per litre. What is the difference between the prices (in ₹) per litre of the oil in the last year and this year ?</p>",
                    question_hi: "<p>9. हर वर्ष तेल की दर में 12% की बढ़ोतरी होती है। अगले वर्ष की दर ₹125.44 प्रति लीटर होगी। पिछले वर्ष और इस वर्ष प्रति लीटर तेल की कीमतों के बीच कितना अंतर (₹ में) है ?</p>",
                    options_en: ["<p>14.5</p>", "<p>17.5</p>", 
                                "<p>15.5</p>", "<p>12</p>"],
                    options_hi: ["<p>14.5</p>", "<p>17.5</p>",
                                "<p>15.5</p>", "<p>12</p>"],
                    solution_en: "<p>9.(d) 12% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>Let rate for last year = ₹ x<br>According to question,<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> = 125.44<br>x = ₹ 100<br>Present year rate = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math>= ₹ 112<br>Difference between last year and this year = 112 - 100 = ₹ 12</p>",
                    solution_hi: "<p>9.(d) 12% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>माना पिछले वर्ष की दर = ₹ x<br>प्रश्न के अनुसार,<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> = 125.44<br>x = ₹ 100<br>वर्तमान वर्ष की दर = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math>&nbsp;= ₹ 112<br>पिछले वर्ष और इस वर्ष के बीच अंतर = 112 - 100 = ₹ 12</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Out of his monthly income, Mr. Sharma spends 24% on house rent and 70% on household expenses and saves the balance. If every month, he saves ₹1,200, then what is his monthly income ?</p>",
                    question_hi: "<p>10. श्री शर्मा अपनी मासिक आय में से 24% घर के किराए पर और 70% घर के खर्च पर खर्च करते हैं और शेष बचत करते हैं। यदि वह हर महीने ₹1,200 बचाते हैं, तो उनकी मासिक आय कितनी है ?</p>",
                    options_en: ["<p>₹20,000</p>", "<p>₹32,000</p>", 
                                "<p>₹27,000</p>", "<p>₹25,000</p>"],
                    options_hi: ["<p>₹20,000</p>", "<p>₹32,000</p>",
                                "<p>₹27,000</p>", "<p>₹25,000</p>"],
                    solution_en: "<p>10.(a)<br>Savings of Mr. Sharma = 100 - (24 + 70) = 6%<br>According to the question,<br>6% = 1200 &rArr;&nbsp;1% = ₹200<br>Hence, monthly income (100%) = ₹20,000</p>",
                    solution_hi: "<p>10.(a)<br>श्री शर्मा की बचत = 100 - (24 + 70) = 6%<br>प्रश्न के अनुसार,<br>6% = 1200 &rArr; 1% = ₹200<br>अतः, मासिक आय (100%) = ₹20,000</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Manoj buys 1 kg tomato for ₹80 and sells it for ₹90. He also uses a weight of 800 gm in place of 1 kg. What is Manoj&rsquo;s actual profit percentage on the sale of 1 kg tomato ?</p>",
                    question_hi: "<p>11. मनोज 1 kg टमाटर ₹80 में खरीदता है और उसे ₹90 में बेचता है। वह 1 kg के स्थान पर 800 gm वजन का भी इस्तेमाल करता है। 1 kg टमाटर की बिक्री पर मनोज का वास्तविक लाभ प्रतिशत कितना है ?</p>",
                    options_en: ["<p>40.625%</p>", "<p>12.5%</p>", 
                                "<p>37.5%</p>", "<p>25%</p>"],
                    options_hi: ["<p>40.625%</p>", "<p>12.5%</p>",
                                "<p>37.5%</p>", "<p>25%</p>"],
                    solution_en: "<p>11.(a)<br><strong>&nbsp; &nbsp; &nbsp;Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;-&nbsp; CP&nbsp; &nbsp; :&nbsp; &nbsp;SP</strong><br>Due to price&nbsp; &nbsp; -&nbsp; &nbsp;80&nbsp; &nbsp; :&nbsp; &nbsp;90<br>Due to weight -&nbsp; &nbsp;800&nbsp; :&nbsp; &nbsp;1000<br>-----------------------------------------<br>Final ratio&nbsp; &nbsp; &nbsp; &nbsp;-&nbsp; &nbsp; 32&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;45<br>Required profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>-</mo><mn>32</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 40.625%</p>",
                    solution_hi: "<p>11.(a)<br><strong>अनुपात&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; -&nbsp; &nbsp;क्र.मू.&nbsp; :&nbsp; वि.मू.</strong> <br>कीमत के कारण -&nbsp; &nbsp; &nbsp; 80&nbsp; &nbsp;:&nbsp; &nbsp;90<br>वजन के कारण&nbsp; &nbsp;-&nbsp; &nbsp; 800&nbsp; &nbsp;:&nbsp; 1000<br>-------------------------------------------<br>अंतिम अनुपात&nbsp; &nbsp;-&nbsp; &nbsp; &nbsp; 32&nbsp; &nbsp;:&nbsp; &nbsp; 45<br>आवश्यक लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>-</mo><mn>32</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 40.625%</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A dishonest shopkeeper promises to sell his goods at cost price. However, he uses a weight that actually weighs 33% less than what is written on it. Find his profit percentage.</p>",
                    question_hi: "<p>12. एक बेईमान दुकानदार अपने सामान को क्रय मूल्य पर बेचने का दावा करता है। हालाँकि, वह एक ऐसे बाट का उपयोग करता है जिसका वजन वास्तव में उस पर लिखे बाट से 33% कम है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>48<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math>%</p>", "<p>50<math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math>%</p>", 
                                "<p>49<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math>%</p>", "<p>51<math display=\"inline\"><mfrac><mrow><mn>34</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>48<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math>%</p>", "<p>50<math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math>%</p>",
                                "<p>49<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math>%</p>", "<p>51<math display=\"inline\"><mfrac><mrow><mn>34</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>12.(c)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; Initial&nbsp; :&nbsp; Final<br>By price&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>By weight &rarr;&nbsp; &nbsp; &nbsp;67&nbsp; &nbsp;:&nbsp; &nbsp;100<br>------------------------------------<br>Final price &rarr;&nbsp; &nbsp;67&nbsp; &nbsp; :&nbsp; &nbsp;100<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>67</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>3300</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math> = 49<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>67</mn></mfrac></math> %</p>",
                    solution_hi: "<p>12.(c)<br>अनुपात&nbsp; &rarr;&nbsp; &nbsp; प्रारंभिक : अंतिम<br>कीमत&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 1<br>वजन&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp;67&nbsp; &nbsp; &nbsp;:&nbsp; 100<br>-----------------------------------<br>अंतिम कीमत &rarr; 67&nbsp; &nbsp; :&nbsp; 100<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>67</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>3300</mn></mrow><mrow><mn>67</mn></mrow></mfrac></math> = 49<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>67</mn></mfrac></math> %</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The salary of a person was reduced by 20%. By what percentage should his reduced salary be raised so as to bring it at par with his original salary ?</p>",
                    question_hi: "<p>13. एक व्यक्ति के वेतन में 20% की कमी होती है। उसके कम किए गए वेतन (reduced salary) में कितने प्रतिशत की वृद्धि करनी चाहिए ताकि उसे उसके मूल वेतन के बराबर लाया जा सके ?</p>",
                    options_en: ["<p>22%</p>", "<p>20%</p>", 
                                "<p>25%</p>", "<p>30%</p>"],
                    options_hi: ["<p>22%</p>", "<p>20%</p>",
                                "<p>25%</p>", "<p>30%</p>"],
                    solution_en: "<p>13.(c)<br>Ratio&nbsp; &rarr; initial : final<br>Salary &rarr;&nbsp; &nbsp; 5&nbsp; &nbsp;:&nbsp; &nbsp;4<br>Required raised salary to bring to original = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> &times; 100 = 25 %</p>",
                    solution_hi: "<p>13.(c)<br>अनुपात &rarr; आरंभिक : अंतिम<br>वेतन&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 4<br>वेतन को मूल स्तर पर लाने के लिए आवश्यक बढ़ाया गया % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> &times; 100 = 25 %</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. When a number is first increased by 18% and then decreased by 18%, the change in the number is ________.</p>",
                    question_hi: "<p>14. जब किसी संख्या में पहले 18% की वृद्धि की जाती है और फिर 18% की कमी की जाती है, तो संख्या में _____होती है।</p>",
                    options_en: ["<p>3.24 % decrease</p>", "<p>0%</p>", 
                                "<p>3.24% increase</p>", "<p>32.4% increase</p>"],
                    options_hi: ["<p>3.24 % कमी</p>", "<p>0%</p>",
                                "<p>3.24% वृद्धि</p>", "<p>32.4% वृद्धि</p>"],
                    solution_en: "<p>14.(a)<br>Net change % = - <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>18</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>100</mn></mrow></mfrac></math> = - 3.24%&nbsp; (-ve sign denotes decrement)</p>",
                    solution_hi: "<p>14.(a)<br>शुद्ध परिवर्तन % = - <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>18</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>100</mn></mrow></mfrac></math> = - 3.24%&nbsp; (-ve चिह्न कमी को दर्शाता है)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If a reduction of 10% in the price of rice enables a person to obtain 22 kg more for ₹250, then the original price of the rice per kg is: <br>(correct to two decimal place)</p>",
                    question_hi: "<p>15. यदि चावल के मूल्य में 10% की कमी की वजह से एक व्यक्ति ₹250 में 22 kg अधिक चावल खरीद पाता है, तो प्रति kg चावल का वास्तविक मूल्य _____है। (दशमलव के दो स्थान तक सन्निकटित)</p>",
                    options_en: ["<p>₹1.20</p>", "<p>₹1.22</p>", 
                                "<p>₹1.26</p>", "<p>₹1.24</p>"],
                    options_hi: ["<p>₹1.20</p>", "<p>₹1.22</p>",
                                "<p>₹1.26</p>", "<p>₹1.24</p>"],
                    solution_en: "<p>15.(c)<br>Price &prop; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>c</mi><mi>o</mi><mi>n</mi><mi>s</mi><mi>u</mi><mi>m</mi><mi>p</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi></mrow></mfrac></math> , total expense = ₹ 250<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; Initial : Final<br>Price&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; 10&nbsp; &nbsp;:&nbsp; &nbsp; 9<br>Consumption &rarr;&nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; :&nbsp; &nbsp;10<br>-----------------------------------------<br>1 unit = 22 kg<br>Consumption before price increase (9 unit) = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> &times; 9 = 198 kg<br>Price of 1 kg = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>e</mi><mi>x</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>n</mi><mi>s</mi><mi>u</mi><mi>m</mi><mi>p</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>250</mn></mrow><mrow><mn>198</mn></mrow></mfrac></math> = ₹ 1.26</p>",
                    solution_hi: "<p>15.(c)<br>कीमत &prop;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>&#2326;&#2346;&#2340;</mi></mfrac></math> , कुल व्यय = ₹ 250<br>अनुपात &rarr; आरंभिक : अंतिम<br>कीमत&nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;9<br>खपत&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 10<br>---------------------------------<br>1 इकाई = 22 kg<br>मूल्य वृद्धि से पहले खपत (9 इकाई) =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> &times; 9 = 198 kg<br>1 किलो की कीमत = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2357;&#2381;&#2351;&#2351;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2326;&#2346;&#2340;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>250</mn></mrow><mrow><mn>198</mn></mrow></mfrac></math> = ₹ 1.26</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>