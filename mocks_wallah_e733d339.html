<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 1.</p>",
                    question_hi: "<p>1. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 1.</p>",
                    options_en: ["<p>prone</p>", "<p>delectable</p>", 
                                "<p>quaint</p>", "<p>habitual</p>"],
                    options_hi: ["<p>prone</p>", "<p>delectable</p>",
                                "<p>quaint</p>", "<p>habitual</p>"],
                    solution_en: "<p>1.(c) quaint<br>&lsquo;Quaint&rsquo; means strange and unusual in an old-fashioned and charming way. The given passage states that the beginning of the story is quaint in its presentation. Hence, \'quaint\' is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(c) quaint<br>&lsquo;Quaint&rsquo; का अर्थ है पुराने ढंग का और आकर्षक तरीके से अजीब एवं असामान्य। दिए गए passage में कहा गया है कि story की शुरुआत इसकी presentation में विचित्र है। अतः, \'quaint\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 2.</p>",
                    question_hi: "<p>2.<strong> Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 2.</p>",
                    options_en: ["<p>hardly</p>", "<p>recently</p>", 
                                "<p>initially</p>", "<p>gradually</p>"],
                    options_hi: ["<p>hardly</p>", "<p>recently</p>",
                                "<p>initially</p>", "<p>gradually</p>"],
                    solution_en: "<p>2.(d) gradually<br>&lsquo;Gradually&rsquo; means slowly over a period of time. The given passage states that the beginning of the story is quaint in its presentation but gradually the style eases out and becomes haunting in its presentation. Hence, &lsquo;gradually&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(d) gradually<br>&lsquo;Gradually&rsquo; का अर्थ है समय के साथ धीरे-धीरे। दिए गए passage में कहा गया है कि story की शुरुआत इसकी presentation में विचित्र है लेकिन धीरे-धीरे यह style सहज हो जाती है और अपनी प्रस्तुति (presentation) में भयावह हो जाती है। अतः, &lsquo;gradually&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 3.</p>",
                    question_hi: "<p>3. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 3.</p>",
                    options_en: ["<p>supervision</p>", "<p>condition</p>", 
                                "<p>dimension</p>", "<p>infection</p>"],
                    options_hi: ["<p>supervision</p>", "<p>condition</p>",
                                "<p>dimension</p>", "<p>infection</p>"],
                    solution_en: "<p>3.(c) dimension<br>&lsquo;Dimension&rsquo; means an aspect or feature of a situation. The given passage states that this common element of sympathy and sacrifice is given a new dimension by the old artist. Hence, &lsquo;dimension&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(c) dimension<br>&lsquo;Dimension&rsquo; का अर्थ है किसी स्थिति का एक पहलू या विशेषता। दिए गए passage में कहा गया है कि सहानुभूति (sympathy) और बलिदान (sacrifice) के इस common element को पुराने कलाकार द्वारा एक नया आयाम(dimension) दिया गया है। अतः, &lsquo;dimension&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 4.</p>",
                    question_hi: "<p>4. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 4.</p>",
                    options_en: ["<p>through</p>", "<p>though</p>", 
                                "<p>tough</p>", "<p>enough</p>"],
                    options_hi: ["<p>through</p>", "<p>though</p>",
                                "<p>tough</p>", "<p>enough</p>"],
                    solution_en: "<p>4.(d) enough<br>The given passage states that this common element of sympathy and sacrifice is given a new dimension by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares enough. Hence, &lsquo;enough&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(d) enough<br>दिए गए passage में बताया गया है कि सहानुभूति और बलिदान के इस common element को पुराने कलाकार ने एक नया आयाम दिया है, जो अपने अंतिम हताशापूर्ण कार्य (desperate act) से दिखाता है कि अगर कोई पर्याप्त चिंता करे तो वह एक दिन के लिए hero बन सकता है। अतः, &lsquo;enough&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5.<strong> Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 5.</p>",
                    question_hi: "<p>5. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The beginning of the story is (1)______ in its presentation but (2)______ the style eases out and becomes haunting in its simplicity. The author brings out the human elements in his story. This common element of sympathy and sacrifice is given a new (3)_____ by the old artist who shows by his last desperate act that one can be a hero for one day if only one cares (4)______. O Henry&rsquo;s treatment of his material is balanced and controlled, a fine (5)______ of humour and pathos.<br>Select the most appropriate option to fill in blank number 5.</p>",
                    options_en: ["<p>legend</p>", "<p>ascend</p>", 
                                "<p>blend</p>", "<p>amend</p>"],
                    options_hi: ["<p>legend</p>", "<p>ascend</p>",
                                "<p>blend</p>", "<p>amend</p>"],
                    solution_en: "<p>5.(c) blend<br>&lsquo;Blend&rsquo; means a mixture of different things or styles. The given passage states that O Henry&rsquo;s treatment of his material is balanced and controlled, a fine blend of humour and pathos. Hence, \'blend\' is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(c) blend<br>&lsquo;Blend&rsquo; का अर्थ है विभिन्न वस्तुओं या शैलियों का मिश्रण। दिए गए passage में कहा गया है कि O Henry का अपनी material के प्रति व्यवहार balanced और controlled है, जो हास्य (humour) और करुणा (patho) का एक बढ़िया मिश्रण है। अतः , \'blend\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 6.</p>",
                    question_hi: "<p>6. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 6.</p>",
                    options_en: ["<p>power</p>", "<p>incidents</p>", 
                                "<p>case</p>", "<p>actions</p>"],
                    options_hi: ["<p>power</p>", "<p>incidents</p>",
                                "<p>case</p>", "<p>actions</p>"],
                    solution_en: "<p>6.(d) actions<br>The given passage states that people forget the fact that success or failure is the result of one&rsquo;s own actions. Hence, &lsquo;actions&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(d) actions<br>दिए गए Passage में कहा गया है कि लोग यह भूल जाते हैं कि सफलता या असफलता व्यक्ति के अपने actions का परिणाम है। अतः, &lsquo;actions&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 7.</p>",
                    question_hi: "<p>7.<strong> Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 7.</p>",
                    options_en: ["<p>does</p>", "<p>have</p>", 
                                "<p>create</p>", "<p>put</p>"],
                    options_hi: ["<p>does</p>", "<p>have</p>",
                                "<p>create</p>", "<p>put</p>"],
                    solution_en: "<p>7.(a) does<br>The given passage states that the result or consequence of an action depends on what one does and to the extent he does it. Hence, &lsquo;does&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(a) does<br>दिए गए Passage में कहा गया है कि किसी action का परिणाम (result) या नतीजा (consequence) इस बात पर निर्भर करता है कि व्यक्ति क्या करता है और वह इसे किस हद(extent) तक करता है। अतः, &lsquo;does&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 8.</p>",
                    question_hi: "<p>8. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 8.</p>",
                    options_en: ["<p>supreme</p>", "<p>greatest</p>", 
                                "<p>more</p>", "<p>most</p>"],
                    options_hi: ["<p>supreme</p>", "<p>greatest</p>",
                                "<p>more</p>", "<p>most</p>"],
                    solution_en: "<p>8.(a) supreme<br>&lsquo;Supreme&rsquo; means highest in authority. The given passage states an opinion about the people who think that the law of karma is supreme. Hence, &lsquo;supreme&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(a) supreme<br>&lsquo;Supreme&rsquo; का अर्थ है सर्वोच्च अधिकार। दिए गए passage में उन लोगों के बारे में राय दी गई है जो सोचते हैं कि कर्म का नियम (law of karma) सर्वोच्च(supreme) है। अतः, &lsquo;supreme&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 9.</p>",
                    options_en: ["<p>horribly</p>", "<p>badly</p>", 
                                "<p>dishonesty</p>", "<p>fairness</p>"],
                    options_hi: ["<p>horribly</p>", "<p>badly</p>",
                                "<p>dishonesty</p>", "<p>fairness</p>"],
                    solution_en: "<p>9.(c) dishonesty<br>&lsquo;Dishonesty&rsquo; means lack of honesty. The given passage states that dishonesty and evil deeds never go unpunished. Hence, \'dishonesty\' is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(c) dishonesty<br>&lsquo;Dishonesty&rsquo; का अर्थ है बेईमानी। दिए गए passage में कहा गया है कि बेईमानी (dishonesty) और बुरे काम (evil deeds) कभी भी दंड से मुक्त नहीं होते। अतः, \'dishonesty\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <strong>Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 10.</p>",
                    question_hi: "<p>10.<strong> Cloze Test :</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>People often say that the stars govern their fate but they forget the fact that success or failure is the result of one&rsquo;s own (6)________. If we sow chaff, we can&rsquo;t reap grain; and the result or consequence of an action depends on what one does and to the extent he (7)________ it. If we work hard, we succeed only to the extent that we work. People who think that the law of karma is (8)________ may feel that if one is destined to get something he will always get it whether he strives for it or not. But experience tells us that reward and punishments are the direct result of our action. Honesty and hard work never go unrewarded and (9)________ and evil deeds never go (10)________. In fact even the law of karma, in its proper sense, means that the fruit we get is decided by whatever actions we did in the past.<br>Select the most appropriate option to fill in blank number 10.</p>",
                    options_en: ["<p>unpunished</p>", "<p>vile</p>", 
                                "<p>punished</p>", "<p>waste</p>"],
                    options_hi: ["<p>unpunished</p>", "<p>vile</p>",
                                "<p>punished</p>", "<p>waste</p>"],
                    solution_en: "<p>10.(a) unpunished<br>&lsquo;Unpunished&rsquo; means not receiving any punishment or penalty. The given passage states that dishonesty and evil deeds never go unpunished. Hence, \'unpunished\' is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(a) unpunished<br>&lsquo;Unpunished&rsquo; का अर्थ है कोई दंड या जुर्माना (penalty) न मिलना। दिए गए passage में कहा गया है कि बेईमानी और बुरे काम कभी भी दंड से मुक्त नहीं होते। अतः, \'unpunished\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 11.</p>",
                    question_hi: "<p>11. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 11.</p>",
                    options_en: ["<p>an</p>", "<p>for</p>", 
                                "<p>the</p>", "<p>a</p>"],
                    options_hi: ["<p>an</p>", "<p>for</p>",
                                "<p>the</p>", "<p>a</p>"],
                    solution_en: "<p>11.(c) the<br>The definite article &lsquo;the&rsquo; is used before titles given to great personalities. Gandhi has been given the title &lsquo;The father of the nation&rsquo;. Hence, &lsquo;the&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(c) the<br>महान व्यक्तित्वों को दी जाने वाली उपाधियों (titles) से पहले definite article &lsquo;the&rsquo; का use किया जाता है। गांधी को &lsquo;राष्ट्रपिता(father of the nation)&rsquo; की उपाधि दी गई है। अतः, &lsquo;the&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12.<strong> Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 12.</p>",
                    question_hi: "<p>12. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 12.</p>",
                    options_en: ["<p>forest-living</p>", "<p>life</p>", 
                                "<p>forest-lived</p>", "<p>alive</p>"],
                    options_hi: ["<p>forest-living</p>", "<p>life</p>",
                                "<p>forest-lived</p>", "<p>alive</p>"],
                    solution_en: "<p>12.(a) forest-living<br>&lsquo;Forest-living&rsquo; means living in the forest. The given passage states that if violence is the law of the forest-living-beasts, non-violence is the law of the civilised human species. Hence, &lsquo;forest-living&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(a) forest-living<br>&lsquo;Forest-living&rsquo; का अर्थ है जंगल में रहने वाला। दिए गए passage में कहा गया है कि यदि violence वन-जीवों का नियम है, तो non-violence सभ्य मानव प्रजाति का नियम है। अतः, &lsquo;forest-living&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 13.</p>",
                    question_hi: "<p>13. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 13.</p>",
                    options_en: ["<p>her</p>", "<p>its</p>", 
                                "<p>one&rsquo;s</p>", "<p>one</p>"],
                    options_hi: ["<p>her</p>", "<p>its</p>",
                                "<p>one&rsquo;s</p>", "<p>one</p>"],
                    solution_en: "<p>13.(c) one&rsquo;s<br>The possessive of &lsquo;one&rsquo; is one&rsquo;s. Hence, one&rsquo;s is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(c) one&rsquo;s<br>&lsquo;One&rsquo; का possessive, one&rsquo;s है। अतः, one&rsquo;s सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 14.</p>",
                    question_hi: "<p>14. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 14.</p>",
                    options_en: ["<p>prove</p>", "<p>approved</p>", 
                                "<p>proven</p>", "<p>proving</p>"],
                    options_hi: ["<p>prove</p>", "<p>approved</p>",
                                "<p>proven</p>", "<p>proving</p>"],
                    solution_en: "<p>14.(c) proven<br>&lsquo;Proven&rsquo; means tried and tested. The given passage states that it is a practically proven philosophy. Hence, &lsquo;proven&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>14.(c) proven<br>&lsquo;Proven&rsquo; का अर्थ है आजमाया और परीक्षण किया हुआ। दिए गए passage में कहा गया है कि यह व्यावहारिक रूप से सिद्ध दर्शन (philosophy) है। अतः, &lsquo;proven&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 15.</p>",
                    question_hi: "<p>15. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>Gandhi, (11)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (12)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (13)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (14)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (15)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 15.</p>",
                    options_en: ["<p>relevant</p>", "<p>ill-gotten</p>", 
                                "<p>ill-will</p>", "<p>moral</p>"],
                    options_hi: ["<p>relevant</p>", "<p>ill-gotten</p>",
                                "<p>ill-will</p>", "<p>moral</p>"],
                    solution_en: "<p>15.(b) ill-gotten<br>&lsquo;Ill-gotten&rsquo; means acquired by illegal or unfair means. The given passage states that it won&rsquo;t work in the defence of ill-gotten gains and immoral acts. Hence, &lsquo;ill-gotten&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(b) ill-gotten<br>&lsquo;Ill-gotten&rsquo; का अर्थ है अवैध या अनुचित तरीकों से अर्जित किया गया। दिए गए passage में कहा गया है कि यह गलत तरीके से अर्जित लाभ और अनैतिक कार्यों (immoral acts) के बचाव में काम नहीं करेगा। अतः, &lsquo;ill-gotten&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>