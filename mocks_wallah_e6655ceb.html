<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The areas of two similar triangles &Delta;PQR and &Delta;XYZ are 12.96 cm<sup>2</sup> and 635.04 cm<sup>2</sup>, respectively. If QR = 2.9 cm, then the length (in cm) of YZ equals:</p>",
                    question_hi: "<p>1. दो समरूप त्रिभुजों &Delta;PQR और &Delta;XYZ का क्षेत्रफल क्रमशः 12.96 cm<sup>2</sup> और 635.04 cm<sup>2</sup> है। यदि QR = 2.9 cm है, तो YZ की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>23.2</p>", "<p>25.2</p>", 
                                "<p>30.4</p>", "<p>20.3</p>"],
                    options_hi: ["<p>23.2</p>", "<p>25.2</p>",
                                "<p>30.4</p>", "<p>20.3</p>"],
                    solution_en: "<p>1.(d) <br>&Delta;PQR ~ &Delta;XYZ<br>The ratio of the area of two similar triangles is equal to the square of the ratio of any pair of the corresponding sides of the similar triangles.<br>Then, <math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>&#916;</mi><mi>P</mi><mi>Q</mi><mi>R</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>&#916;</mi><mi>X</mi><mi>Y</mi><mi>Z</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>QR</mi><mi>YZ</mi></mfrac></math>)<sup>2</sup><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>.</mo><mn>96</mn></mrow><mrow><mn>635</mn><mo>.</mo><mn>04</mn></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math>)<sup>2</sup><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>44</mn></mrow><mrow><mn>70</mn><mo>.</mo><mn>56</mn></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math>)<sup>2</sup><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>.</mo><mn>44</mn></mrow><mrow><mn>70</mn><mo>.</mo><mn>56</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>8</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math><br>&rArr; YZ = 20.3</p>",
                    solution_hi: "<p>1.d) <br>&Delta;PQR ~ &Delta;XYZ<br>दो समरूप त्रिभुजों के क्षेत्रफल का अनुपात समरूप त्रिभुजों की संगत भुजाओं के किसी युग्म के अनुपात के वर्ग के बराबर होता है।<br>फिर, <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;PQR</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#916;XYZ</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>QR</mi><mi>YZ</mi></mfrac></math>)<sup>2</sup><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>.</mo><mn>96</mn></mrow><mrow><mn>635</mn><mo>.</mo><mn>04</mn></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math>)<sup>2</sup><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>44</mn></mrow><mrow><mn>70</mn><mo>.</mo><mn>56</mn></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math>)<sup>2</sup><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>.</mo><mn>44</mn></mrow><mrow><mn>70</mn><mo>.</mo><mn>56</mn></mrow></mfrac></msqrt></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>8</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>9</mn></mrow><mi>YZ</mi></mfrac></math><br>&rArr; YZ = 20.3</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. In &Delta;ABC,XY is drawn parallel to BC, cutting sides at X and Y, where AB = 5.4 cm ,BC = 7.2 cm and BX = 3 cm. What is the length of XY (in cm)?</p>",
                    question_hi: "<p>2. &Delta;ABC में,XY को, भुजाओं को X और Y पर काटते हुए, BC के समांतर खींचा जाता है, जहाँ AB = 5.4cm ,BC = 7.2 cm और BX = 3 cm है। XY की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>3.0</p>", "<p>2.8</p>", 
                                "<p>4.3</p>", "<p>3.2</p>"],
                    options_hi: ["<p>3.0</p>", "<p>2.8</p>",
                                "<p>4.3</p>", "<p>3.2</p>"],
                    solution_en: "<p>2.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753687961.png\" alt=\"rId4\" width=\"183\" height=\"133\"><br>By the thales theorem<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>X</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>XY</mi><mi>BC</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>XY</mi><mrow><mn>7</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math><br>XY = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>4</mn><mo>&#215;</mo><mn>7</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>5</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = 3.2 cm</p>",
                    solution_hi: "<p>2.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753688146.png\" alt=\"rId5\" width=\"171\" height=\"120\"><br>थेल्स प्रमेय द्वारा<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>X</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>XY</mi><mi>BC</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>XY</mi><mrow><mn>7</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math><br>XY = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>4</mn><mo>&#215;</mo><mn>7</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>5</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = 3.2 सेमी</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Two circles touch each other externally as shown in the figure above. The radius of the circle with centre O is 49 cm. The radius of the circle with centre A is 16 cm. Find the length (in cm) of their common tangent BC.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753688297.png\" alt=\"rId6\" width=\"166\" height=\"128\"></p>",
                    question_hi: "<p>3. उपर्युक्त चित्र में दर्शाए अनुसार दो वृत्त एक-दूसरे को बाह्य रूप से स्पर्श करते हैं। केंद्र O वाले वृत्त की त्रिज्या 49 cm है। केंद्र A वाले वृत्त की त्रिज्या 16 cm है। उनकी उभयनिष्ठ स्पर्श रेखा BC की लंबाई (cm में) ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753688475.png\" alt=\"rId7\" width=\"138\" height=\"110\"></p>",
                    options_en: ["<p>56</p>", "<p>58</p>", 
                                "<p>57</p>", "<p>55</p>"],
                    options_hi: ["<p>56</p>", "<p>58</p>",
                                "<p>57</p>", "<p>55</p>"],
                    solution_en: "<p>3.(a)<br>Length of common tangent = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi>r</mi><mn>1</mn></msub><msub><mi>r</mi><mn>2</mn></msub></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>&#215;</mo><mn>16</mn></msqrt></math> <br>= 2 &times; 7 &times; 4 <br>= 56 cm</p>",
                    solution_hi: "<p>3.(a)<br>उभयनिष्ठ स्पर्शरेखा की लंबाई = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi>r</mi><mn>1</mn></msub><msub><mi>r</mi><mn>2</mn></msub></msqrt></math>&nbsp;= 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>&#215;</mo><mn>16</mn></msqrt></math> <br>= 2 &times; 7 &times; 4 <br>= 56 सेमी</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. O is the centre and the arc PQR subtends an angle of 240&deg; at O. PQ is extended to A. Then &ang;AQR is:</p>",
                    question_hi: "<p>4. O केंद्र है और चाप PQR, O पर 240&deg; का कोण अंतरित करता है। PQ को A तक बढ़ाया जाता है। तब &ang;AQR की माप कितनी होगी?</p>",
                    options_en: ["<p>240&deg;</p>", "<p>60&deg;</p>", 
                                "<p>120&deg;</p>", "<p>180&deg;</p>"],
                    options_hi: ["<p>240&deg;</p>", "<p>60&deg;</p>",
                                "<p>120&deg;</p>", "<p>180&deg;</p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753688746.png\" alt=\"rId8\" width=\"133\" height=\"86\"><br>The angle subtended by an arc at the center of a circle is double the size of the angle subtended by the same arc at the circle circumference <br>Take any point M on the circumference and join PM and MR <br>∵ &ang; PMR = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; &ang; POR <br>&rArr; &ang; PMR = 120&deg;<br>Now , &ang; PMR = &ang;AQR [ Exterior angle of cyclic quadrilateral ]<br>&ang;AQR = 120&deg;</p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753688746.png\" alt=\"rId8\" width=\"133\" height=\"86\"><br>एक वृत्त के केंद्र पर एक चाप द्वारा अंतरित कोण, वृत्त की परिधि पर उसी चाप द्वारा अंतरित कोण का दोगुना होता है <br>परिधि पर कोई भी बिंदु M लें और PM और MR को मिलाएँ&nbsp;<br>∵ &ang; PMR = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; &ang; POR <br>&rArr; &ang; PMR = 120&deg;<br>अब, &ang; PMR = &ang;AQR [ चक्रीय चतुर्भुज का बाहरी कोण ]<br>&ang;AQR = 120&deg;</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The distance between the centres of two circles of radii 4 cm and 2 cm is 10 cm. The length (in cm) of a transverse common tangent is:</p>",
                    question_hi: "<p>5. 4 cm और 2 cm त्रिज्या वाले दो वृत्तों के केंद्रों के बीच की दूरी 10 cm है। अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा (transverse common tangent) की लंबाई (cm में) कितनी है?</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>11</p>", "<p>13</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>11</p>", "<p>13</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689122.png\" alt=\"rId9\" width=\"254\" height=\"133\"><br>Length of transverse common tangent (PQ) = <math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><msub><mrow><mi>r</mi></mrow><mrow><mn>1</mn></mrow></msub><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>10</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn><mo>-</mo><mn>36</mn></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> <br>= 8 cm</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689122.png\" alt=\"rId9\" width=\"240\" height=\"126\"><br>अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा की लंबाई (PQ) =&nbsp;<math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><msub><mrow><mi>r</mi></mrow><mrow><mn>1</mn></mrow></msub><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>10</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn><mo>-</mo><mn>36</mn></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> <br>= 8 सेमी</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Consider two concentric circles having radii 17 cm and 15 cm. What is the length (in cm) of the chord, of the bigger circle, which is a tangent to the smaller circle?</p>",
                    question_hi: "<p>6. 17 cm और 15 cm त्रिज्या वाले दो संकेंद्रित वृत्तों पर विचार कीजिए। बड़े वृत्त की जीवा की लंबाई (cm में) क्या है, जो छोटे वृत्त की स्पर्श रेखा है?</p>",
                    options_en: ["<p>12</p>", "<p>10</p>", 
                                "<p>8</p>", "<p>16</p>"],
                    options_hi: ["<p>12</p>", "<p>10</p>",
                                "<p>8</p>", "<p>16</p>"],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689242.png\" alt=\"rId10\" width=\"136\" height=\"126\"><br>In the right angles &Delta;OAC<br>AC = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>17</mn><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>15</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>289</mn><mo>-</mo><mn>225</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> = 8 cm<br>Length of the cord of bigger circle (AB) = 2AC = 16 cm</p>",
                    solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689242.png\" alt=\"rId10\" width=\"136\" height=\"126\"><br>समकोण &Delta;OAC में <br>AC = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>17</mn><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>15</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>289</mn><mo>-</mo><mn>225</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> = 8 सेमी<br>बड़े वृत्त की जीवा की लंबाई (AB) = 2AC = 16 सेमी</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. If &Delta;ABC and &Delta;PQR are similar. AB = 8 cm, PQ = 12 cm, QR = 18 cm and RP = 24 cm, then the perimeter of &Delta;ABC is _______ cm.</p>",
                    question_hi: "<p>7. यदि ∆ABC और ∆PQR समरूप हैं। AB = 8 cm, PQ = 12 cm, QR = 18 cm और RP = 24 cm है, तो △ABC का परिमाप ________ cm है।</p>",
                    options_en: ["<p>36</p>", "<p>42</p>", 
                                "<p>54</p>", "<p>27</p>"],
                    options_hi: ["<p>36</p>", "<p>42</p>",
                                "<p>54</p>", "<p>27</p>"],
                    solution_en: "<p>7.(a) <br>&Delta;ABC and &Delta;PQR are similar triangles<br>Then, <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></math><br>Now, according to the question,<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>12</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mn>18</mn></mfrac></math> <br>BC = 12 cm<br>Now, <br><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>18</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mn>24</mn></mfrac></math><br>AC = 16 cm<br>So, perimeter of &Delta;ABC = (AB + BC + AC) <br>= (8 + 12 + 16) <br>= 36 cm</p>",
                    solution_hi: "<p>7.(a) <br>&Delta;ABC और &Delta;PQR समरूप त्रिभुज हैं<br>फिर, <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></math><br>अब, प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>12</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mn>18</mn></mfrac></math> <br>BC = 12 सेमी<br>अब, <br><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>18</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mn>24</mn></mfrac></math><br>AC = 16 सेमी<br>तो, &Delta;ABC का परिमाप = (AB + BC + AC) <br>= (8 + 12 + 16) <br>= 36 सेमी I</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Two circles of diameters 16 cm and 20 cm are such that the distance between their centers is 13 cm. What is the length (in cm) of a common tangent to these circles that does not intersect the line joining the centers ?</p>",
                    question_hi: "<p>8. 16 cm और 20 cm व्यास वाले दो वृत्त इस प्रकार हैं कि उनके केंद्रों के बीच की दूरी 13 cm है। इन वृत्तों की उस उभयनिष्ठ स्पर्शरेखा की लंबाई (cm में) कितनी है जो केंद्रों को मिलाने वाली रेखा को प्रतिच्छेद नहीं करती है?</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>167</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>165</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\"><msqrt><mn>163</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>1</mn><mn>73</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>167</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>165</mn></msqrt></math></p>",
                                "<p><math display=\"inline\"><msqrt><mn>163</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>173</mn></msqrt></math></p>"],
                    solution_en: "<p>8.(b)<br>Length of the common tangent = <math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mi>R</mi><mo>-</mo><mi>r</mi></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>13</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>10</mn><mo>-</mo><mn>8</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>169</mn><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>165</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>8.(b)<br>उभयनिष्ठ स्पर्शरेखा की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mi>R</mi><mo>-</mo><mi>r</mi></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>13</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>10</mn><mo>-</mo><mn>8</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>169</mn><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>165</mn></msqrt></math> cm</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. If &Delta; ABC ~ &Delta; QRP; area of &Delta; ABC : area of &Delta; PQR = 9 : 4; AB = 18 cm and BC = 15 cm, then find the length of PR.</p>",
                    question_hi: "<p>9. यदि &Delta; ABC ~ &Delta; QRP; &Delta; ABC का क्षेत्रफल : &Delta; PQR का क्षेत्रफल = 9 : 4; AB = 18 cm और BC = 15 cm हैं, तो PR की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>15 cm</p>", "<p>12 cm</p>", 
                                "<p>10 cm</p>", "<p>14 cm</p>"],
                    options_hi: ["<p>15 cm</p>", "<p>12 cm</p>",
                                "<p>10 cm</p>", "<p>14 cm</p>"],
                    solution_en: "<p>9.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>area</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#916;ABC</mi></mrow><mrow><mi>area</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#916;QRP</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>RP</mi></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><msqrt><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>RP</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mi mathvariant=\"bold-italic\">R</mi><mi mathvariant=\"bold-italic\">P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> <br>PR = 10 cm</p>",
                    solution_hi: "<p>9.(c)<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;ABC</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;QRP</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>RP</mi></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><msqrt><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>RP</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mi mathvariant=\"bold-italic\">R</mi><mi mathvariant=\"bold-italic\">P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> <br>PR = 10 cm</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Let C be a circle with Centre O and P be an external point to C. Let PQ and PR be the tangents to C such that Q and R be the points of tangency, respectively. If &ang;ROQ = 110&deg;, find &ang;RPQ .</p>",
                    question_hi: "<p>10. माना C केंद्र O वाला एक वृत्त है और P, C का एक बाहरी बिंदु है। माना PQ और PR, C की स्पर्श रेखाएं इस प्रकार हैं कि Q और R क्रमशः स्पर्श रेखा (tangency) के बिंदु हैं। यदि &ang;ROQ = 110&deg; है, तो &ang;RPQ का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>80&deg;</p>", "<p>90&deg;</p>", 
                                "<p>70&deg;</p>", "<p>110&deg;</p>"],
                    options_hi: ["<p>80&deg;</p>", "<p>90&deg;</p>",
                                "<p>70&deg;</p>", "<p>110&deg;</p>"],
                    solution_en: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689405.png\" alt=\"rId11\" width=\"185\" height=\"136\"><br>We know that,<br>&ang;RPQ = 180&deg; - 110&deg; <br>= 70&deg;</p>",
                    solution_hi: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689405.png\" alt=\"rId11\" width=\"181\" height=\"133\"><br>हम जानते है कि, <br>&ang;RPQ = 180&deg; - 110&deg; <br>= 70&deg;</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. In ∆PQR, PR = 10 cm. Find the length of PT, where ST∥QR. Given that PS = 6 cm and QS = 14 cm.</p>",
                    question_hi: "<p>11. ∆PQR में, PR = 10 cm है। PT की लंबाई ज्ञात कीजिए, जहाँ ST|| QR है। दिया गया है कि PS = 6 cm और QS = 14 cm है।</p>",
                    options_en: ["<p>2 cm</p>", "<p>4 cm</p>", 
                                "<p>1.5 cm</p>", "<p>3 cm</p>"],
                    options_hi: ["<p>2 cm</p>", "<p>4 cm</p>",
                                "<p>1.5 cm</p>", "<p>3 cm</p>"],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689659.png\" alt=\"rId12\" width=\"114\" height=\"94\"><br>Thales theorem : - <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>S</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>T</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></math><br>Now, the length of PT&nbsp;<br>&rarr; <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>T</mi></mrow><mn>10</mn></mfrac></math>, PT = 3 cm</p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689659.png\" alt=\"rId12\" width=\"134\" height=\"110\"><br>थेल्स प्रमेय: - <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>S</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>T</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></math><br>अब, PT की लंबाई&nbsp;<br>&rarr; <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>T</mi></mrow><mn>10</mn></mfrac></math>, PT = 3 cm</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. In ∆EFG, XY ∥ FG, area of the quadrilateral XFGY = 44 m<sup>2</sup> . If EX : XF = 2 : 3, then find the area of ∆EXY (in m<sup>2</sup>).</p>",
                    question_hi: "<p>12. △EFG में, XY || FG है, चतुर्भुज XFGY का क्षेत्रफल = 44 m&sup2; है। यदि EX : XF = 2: 3 है, तो △EXY का क्षेत्रफल (m&sup2; में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>7.28</p>", "<p>8.10</p>", 
                                "<p>8.38</p>", "<p>9.46</p>"],
                    options_hi: ["<p>7.28</p>", "<p>8.10</p>",
                                "<p>8.38</p>", "<p>9.46</p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689860.png\" alt=\"rId13\" width=\"176\" height=\"146\"><br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>E</mi><mi>X</mi><mi>Y</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>E</mi><mi>F</mi><mi>G</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>25</mn></mfrac></math><br>Area of quadrilateral XFGY = 25 - 4 = 21 units<br>21 units = 44 m<sup>2</sup><br>(area of &Delta;EXY) 2 units = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>21</mn></mfrac></math> &times; 4 = 8.38 m<sup>2</sup></p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753689860.png\" alt=\"rId13\" width=\"166\" height=\"137\"><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;EXY</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;EFG</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>25</mn></mfrac></math><br>चतुर्भुज XFGY का क्षेत्रफल = 25 - 4 = 21 इकाई <br>21 इकाई = 44 m<sup>2</sup><br>(&Delta;EXY का क्षेत्रफल) 2 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>21</mn></mfrac></math> &times; 4 = 8.38 m<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. L and M are the mid points of sides AB and AC of a triangle ABC, respectively, and BC = 18 cm. If LM || BC, then find the length of LM (in cm).</p>",
                    question_hi: "<p>13. L और M क्रमशः त्रिभुज ABC की भुजाओं AB और AC के मध्य बिंदु हैं, और BC = 18 cm है। यदि LM || BC है, तो LM की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>6</p>", "<p>9</p>", 
                                "<p>3</p>", "<p>12</p>"],
                    options_hi: ["<p>6</p>", "<p>9</p>",
                                "<p>3</p>", "<p>12</p>"],
                    solution_en: "<p>13.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753690103.png\" alt=\"rId14\" width=\"144\" height=\"129\"><br>Thales theorem : - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AL</mi><mi>AB</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>LM</mi><mi>BC</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mi>x</mi><mo>+</mo><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>LM</mi><mn>18</mn></mfrac></math><br>LM = 9 cm</p>",
                    solution_hi: "<p>13.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753690103.png\" alt=\"rId14\" width=\"126\" height=\"113\"><br>थेल्स प्रमेय :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AL</mi><mi>AB</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>LM</mi><mi>BC</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mi>x</mi><mo>+</mo><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>LM</mi><mn>18</mn></mfrac></math><br>LM = 9 cm</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. In ∆ABC the straight line parallel to the side BC meets AB and AC at the points P and Q, respectively. If AP = QC, the length of AB is 12 cm and the length of AQ is 2 cm, then the length (in cm) of CQ is:</p>",
                    question_hi: "<p>14. △ABC में भुजा BC के समांतर सीधी रेखा, AB और AC से क्रमशः बिंदु P और Q पर मिलती है। यदि AP = QC है, AB की लंबाई 12 cm है और AQ की लंबाई 2 cm है, तो CQ की लंबाई (cm में) कितनी होगी?</p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>14.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753690358.png\" alt=\"rId15\" width=\"119\" height=\"101\"><br>Thales theorem : - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AP</mi><mi>PB</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AQ</mi><mi>QC</mi></mfrac></math> <br>AP = QC = x<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>12</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>x</mi></mfrac></math><br>x<sup>2</sup> = 24 - 2x<br>x<sup>2</sup> + 2x - 24 = 0<br>x<sup>2</sup> + 6x - 4x - 24 = 0<br>x(x + 6) - 4(x + 6) = 0<br>(x - 4)(x + 6) = 0<br>x = 4, - 6 (can&rsquo;t take -ve value of x)</p>",
                    solution_hi: "<p>14.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736753690358.png\" alt=\"rId15\" width=\"146\" height=\"125\"><br>थेल्स प्रमेय :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AP</mi><mi>PB</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AQ</mi><mi>QC</mi></mfrac></math> <br>AP = QC = x<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>12</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>x</mi></mfrac></math><br>x<sup>2</sup> = 24 - 2x<br>x<sup>2</sup> + 2x - 24 = 0<br>x<sup>2</sup> + 6x - 4x - 24 = 0<br>x(x + 6) - 4(x + 6) = 0<br>(x - 4)(x + 6) = 0<br>x = 4, - 6 ( x का मान नकारात्मक नहीं ले सकते)</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In &Delta;PQR, S and T are points on PQ and PR, respectively, such that ST ∥ QR and ST divides the &Delta;PQR into two parts of equal areas. Then the ratio of PS and QS is:</p>",
                    question_hi: "<p>15. &Delta;PQR में, S और T क्रमशः PQ और PR पर स्थित बिंदु इस प्रकार हैं कि ST ∥ QR है और ST, &Delta;PQR को समान क्षेत्रफल वाले दो भागों में विभाजित करती है। तो PS और QS का अनुपात कितना है?</p>",
                    options_en: ["<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>1 : 1</p>", 
                                "<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1</p>", "<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> + 1</p>"],
                    options_hi: ["<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>1 : 1</p>",
                                "<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1</p>", "<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> + 1</p>"],
                    solution_en: "<p>15.(c)<br>&Delta;PST &sim; &Delta;PQR by BPT<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>P</mi><mi>S</mi><mi>T</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>P</mi><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = (<math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>S</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>S</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>QS = PQ - PS = <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1 unit<br>Hence, the ratio of PS and QS = 1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1</p>",
                    solution_hi: "<p>15.(c)<br>मूल समानुपातिक प्रमेय (BPT) द्वारा &Delta;PST &sim; &Delta;PQR&nbsp;<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mi>&#916;PST</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;PQR</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = (<math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>S</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>S</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>QS = PQ - PS = <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1 इकाई<br>इसलिए, PS और QS का अनुपात = 1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>