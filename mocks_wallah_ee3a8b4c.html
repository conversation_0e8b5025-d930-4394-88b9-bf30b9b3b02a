<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["6"] = {
                name: "Reasoning",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Roboto;\">Find the part of the given sentence that has an error in it. If there is no error, choose </span><span style=\"font-family: Roboto;\">\'No error\'.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> Our hands have been crucial tools for finding and </span><span style=\"font-family: Roboto;\">eat</span><span style=\"font-family: Roboto;\"> food, and helping us navigate the world.</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Roboto;\">Find the part of the given sentence that has an error in it. If there is no error, choose </span><span style=\"font-family: Roboto;\">\'No error\'.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> Our hands have been crucial tools for finding and eat food, and helping us navigate the world.</span></p>\n",
                    options_en: ["<p>tools for finding and eat food,</p>\n", "<p>Our hands have been crucial</p>\n", 
                                "<p>No error</p>\n", "<p>and helping us navigate the world.</p>\n"],
                    options_hi: ["<p>tools for finding and eat food,</p>\n", "<p>Our hands have been crucial</p>\n",
                                "<p>No error</p>\n", "<p>and helping us navigate the world.<span style=\"font-family: Roboto;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">1.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">We generally use the same form of the verb before and after the conjunction &lsquo;and&rsquo;. Hence, &lsquo;eat&rsquo; must be replaced with &lsquo;eating&rsquo; &amp; &lsquo;crucial tools for finding and eating food&rsquo; becomes the most appropriate answer.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">1.(a)</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2361;&#2350; &#2310;&#2350; &#2340;&#2380;&#2352; &#2346;&#2352; conjunction &lsquo;and&rsquo; &#2325;&#2375; &#2346;&#2361;&#2354;&#2375; &#2324;&#2352; &#2348;&#2366;&#2342; &#2350;&#2375;&#2306; verb &#2325;&#2375; &#2360;&#2350;&#2366;&#2344; form &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;eat&rsquo; &#2325;&#2379; &lsquo;eating&rsquo; &#2360;&#2375; &#2348;&#2342;&#2354;&#2366; &#2332;&#2366;&#2344;&#2366; &#2330;&#2366;&#2361;&#2367;&#2319; &#2324;&#2352; &lsquo;crucial tools for finding and eating food&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2348;&#2344; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.<span style=\"font-family: Roboto;\"> Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.</span></p>\r\n<p><span style=\"font-family: Roboto;\">I can hear the band which is playing </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">patriotic tunes</span></span><span style=\"font-family: Roboto;\">,</span><span style=\"font-family: Roboto;\"> in the playground.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Roboto;\">Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.</span></p>\r\n<p><span style=\"font-family: Roboto;\">I can hear the band which is playing </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">patriotic tunes</span></span><span style=\"font-family: Roboto;\">,</span><span style=\"font-family: Roboto;\"> in the playground.</span></p>\n",
                    options_en: ["<p>No improvement</p>\n", "<p>patriot tunes</p>\n", 
                                "<p>patriotic tuning</p>\n", "<p>patriotically tuned</p>\n"],
                    options_hi: ["<p>No improvement</p>\n", "<p>patriot tunes</p>\n",
                                "<p>patriotic tuning</p>\n", "<p>patriotically tuned</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">2.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">No improvement. The given sentence is grammatically correct.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">2.(a)</span></p>\r\n<p><span style=\"font-family: Palanquin;\">No improvement. &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; sentence grammatically &#2360;&#2361;&#2368;&#2404; </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <span style=\"font-family: Roboto;\">Select the correct passive form of the given sentence. </span></p>\r\n<p><span style=\"font-family: Roboto;\">The manager lost the letter.</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Roboto;\">Select the correct passive form of the given sentence. </span></p>\r\n<p><span style=\"font-family: Roboto;\">The manager lost the letter.</span></p>\n",
                    options_en: ["<p>The letter was loss by the manager.</p>\n", "<p>The letter was loose by the manager.</p>\n", 
                                "<p>The letter was lost by the manager.</p>\n", "<p>The letter was lose by the manager.</p>\n"],
                    options_hi: ["<p>The letter was loss by the manager.</p>\n", "<p>The letter was loose by the manager.</p>\n",
                                "<p>The letter was lost by the manager.</p>\n", "<p>The letter was lose by the manager.</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">3.(c) </span><span style=\"font-family: Roboto;\">The letter was lost by the manager.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) The letter was </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">loss</span></span><span style=\"font-weight: 400;\"> by the manager.(Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) The letter was </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">loose</span></span><span style=\"font-weight: 400;\"> by the manager.(Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) The letter was </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">lose</span></span><span style=\"font-weight: 400;\"> by the manager.(Incorrect Verb)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">3.(c) </span><span style=\"font-family: Roboto;\">The letter was lost by the manager.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) The letter was </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">loss</span></span><span style=\"font-weight: 400;\"> by the manager.(&#2327;&#2354;&#2340; Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) The letter was </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">loose</span></span><span style=\"font-weight: 400;\"> by the manager.(&#2327;&#2354;&#2340; Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) The letter was </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">lose</span></span><span style=\"font-weight: 400;\"> by the manager.(&#2327;&#2354;&#2340; Verb)</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: " <p>4. </span><span style=\"font-family:Roboto\">Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Roboto\">P. The earliest years of a child\'s life are critical. </span></p> <p><span style=\"font-family:Roboto\">Q. This is why an establishment has been created for the protection of children in these crucial years, known as ECCE.</span></p> <p><span style=\"font-family:Roboto\">R. It is during the early years that children develop the cognitive, physical, social and emotional skills that they need to succeed in life.</span></p> <p><span style=\"font-family:Roboto\">S. These years determine child\'s survival and thriving in life, and lay the foundations for her/his learning and holistic development.</span></p>",
                    question_hi: " <p>4. </span><span style=\"font-family:Roboto\">Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Roboto\">P. The earliest years of a child\'s life are critical. </span></p> <p><span style=\"font-family:Roboto\">Q. This is why an establishment has been created for the protection of children in these crucial years, known as ECCE.</span></p> <p><span style=\"font-family:Roboto\">R. It is during the early years that children develop the cognitive, physical, social and emotional skills that they need to succeed in life.</span></p> <p><span style=\"font-family:Roboto\">S. These years determine child\'s survival and thriving in life, and lay the foundations for her/his learning and holistic development.</span></p>",
                    options_en: [" <p> RSPQ</span></p>", " <p> PSRQ</span></p>", 
                                " <p> PSQR</span></p>", " <p> QPSR</span></p> <p><span style=\"font-family:Roboto\"> </span></p>"],
                    options_hi: [" <p> RSPQ</span></p>", " <p> PSRQ</span></p>",
                                " <p> PSQR</span></p>", " <p> QPSR</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">4.(b) </span><span style=\"font-family:Roboto\">PSRQ</span></p> <p><span style=\"font-family:Roboto\">Sentence P will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Roboto\">The earliest years of a child\'s life are critical. </span><span style=\"font-family:Roboto\">However, Sentence S states that </span><span style=\"font-family:Roboto\">these years determine child\'s survival and thriving in life</span><span style=\"font-family:Roboto\">. So, S will follow P. Further, Sentence R states that </span><span style=\"font-family:Roboto\">it is during the early years that children develop the cognitive, physical, social and emotional skills </span><span style=\"font-family:Roboto\">and Sentence Q states that </span><span style=\"font-family:Roboto\">this is why an establishment has been created for the protection of children in these crucial years, known as ECCE</span><span style=\"font-family:Roboto\">. So, Q will follow R. Going through the options, option b has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">4.(b) </span><span style=\"font-family:Roboto\">PSRQ</span></p> <p><span style=\"font-family:Palanquin\">Sentence P प्रारंभिक line होगी क्योंकि इसमें parajumble का  मुख्य विचार ‘</span><span style=\"font-family:Roboto\">The earliest years of a child\'s life are critical’ </span><span style=\"font-family:Palanquin\">हैं। हालांकि, Sentence S बताता है कि ये वर्ष बच्चे के जीवन में जीवित रहने और संपन्न होने का निर्धारण करते हैं। तो,  P, के बाद S आएगा। आगे, Sentence R कहता है कि यह प्रारंभिक वर्षों के दौरान है कि बच्चे संज्ञानात्मक, शारीरिक, सामाजिक और भावनात्मक कौशल विकसित करते हैं और Sentence Q कहता है कि यही कारण है कि बच्चों की सुरक्षा के लिए एक प्रतिष्ठान बनाया गया है इन महत्वपूर्ण वर्षों को </span><span style=\"font-family:Roboto\">ECCE</span><span style=\"font-family:Palanquin\"> के नाम से जाना जाता है। तो, R, के बाद </span><span style=\"font-family:Palanquin\">Q आएगा</span><span style=\"font-family:Palanquin\">। options के माध्यम से जाने पर , option b में सही sequence है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: " <p>5. </span><span style=\"font-family:Roboto\">Identify the segment in the sentence which contains a grammatical error. If there is no error, then select the option \"No error\".</span></p> <p><span style=\"font-family:Roboto\">A witness had seen a youth, / perhaps aged young as/ ten or eleven, acting suspiciously.</span></p>",
                    question_hi: " <p>5. </span><span style=\"font-family:Roboto\">Identify the segment in the sentence which contains a grammatical error. If there is no error, then select the option \"No error\".</span></p> <p><span style=\"font-family:Roboto\">A witness had seen a youth, / perhaps aged young as/ ten or eleven, acting suspiciously.</span></p>",
                    options_en: [" <p> No error</span></p>", " <p> A witness had seen a youth,</span></p>", 
                                " <p> perhaps aged young as</span></p>", " <p> ten or eleven, acting suspiciously.</span></p>"],
                    options_hi: [" <p> No error</span></p>", " <p> A witness had seen a youth,</span></p>",
                                " <p> perhaps aged young as</span></p>", " <p> ten or eleven, acting suspiciously.</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">5.(c)</span></p> <p><span style=\"font-family:Roboto\">The grammatically correct phrase for the given sentence is ‘as + adjective + as’. Hence, ‘aged as young(adjective) as ten or eleven’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">5.(c)</span></p> <p><span style=\"font-family:Palanquin\">दिए गए sentence के लिए grammatically सही phrase \'as + adjective + as\' है। इसलिए,  ‘aged as young(adjective) as ten or eleven’ सबसे उपयुक्त उत्तर है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6.<span style=\"font-family: Roboto;\"> Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.</span></p>\r\n<p><span style=\"font-family: Roboto;\">I won\'t bore you with the details, but I</span><span style=\"font-family: Roboto;\"> <span style=\"text-decoration: underline;\">finally finish</span> </span><span style=\"font-family: Roboto;\">my taxes.</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Roboto;\"> Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.</span></p>\r\n<p><span style=\"font-family: Roboto;\">I won\'t bore you with the details, but I</span><span style=\"font-family: Roboto;\"> <span style=\"text-decoration: underline;\">finally finish</span> </span><span style=\"font-family: Roboto;\">my taxes.</span></p>\n",
                    options_en: ["<p>No improvement</p>\n", "<p>finally finished</p>\n", 
                                "<p>final finishing</p>\n", "<p>finally finishing</p>\n"],
                    options_hi: ["<p>No improvement</p>\n", "<p>finally finished</p>\n",
                                "<p>final finishing</p>\n", "<p>finally finishing</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">6.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\">We will use the past form of the verb &lsquo;finish&rsquo; in the latter part of the given sentence as it talks about an activity of the past. Hence, &lsquo;finally finished&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">6.(b)</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2361;&#2350; &#2342;&#2367;&#2319; &#2327;&#2319; sentence &#2325;&#2375; &#2348;&#2366;&#2342; &#2325;&#2375; &#2349;&#2366;&#2327; &#2350;&#2375;&#2306; verb &lsquo;finish&rsquo; &#2325;&#2375; past form &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2351;&#2361; past &#2325;&#2368; &#2319;&#2325; &#2327;&#2340;&#2367;&#2357;&#2367;&#2343;&#2367; &#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2348;&#2366;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;finally finished&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7.<span style=\"font-family: Roboto;\"> Choose the word that is opposite in meaning to the given word.</span></p>\r\n<p><strong><span style=\"font-family: Roboto;\"> Inchoate</span></strong></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Roboto;\">Choose the word that is opposite in meaning to the given word.</span></p>\r\n<p><strong><span style=\"font-family: Roboto;\"> Inchoate</span></strong></p>\n",
                    options_en: ["<p>Artless</p>\n", "<p>Refined</p>\n", 
                                "<p>Articulate</p>\n", "<p>Formless</p>\n"],
                    options_hi: ["<p>Artless</p>\n", "<p>Refined</p>\n",
                                "<p>Articulate</p>\n", "<p>Formless</p>\n"],
                    solution_en: "<p>7.(b)</p>\r\n<p><span style=\"font-family: Roboto;\"> Inchoate - </span><span style=\"font-family: Roboto;\">being only partly in existence or operation</span></p>\r\n<p><span style=\"font-family: Roboto;\">Artless - free from deceit or honest</span></p>\r\n<p><span style=\"font-family: Roboto;\">Articulate - </span><span style=\"font-family: Roboto;\">to express things easily and clearly</span></p>\r\n<p><span style=\"font-family: Roboto;\">Formless - </span><span style=\"font-family: Roboto;\">having no regular form or shape</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">7.(b)</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Inchoate(&#2310;&#2306;&#2358;&#2367;&#2325;) - </span><span style=\"font-family: Palanquin;\">being only partly in existence or operation</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Artless(&#2349;&#2379;&#2354;&#2366;) - free from deceit or honest</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Articulate(&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;) - </span><span style=\"font-family: Palanquin;\">to express things easily and clearly</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Formless(&#2344;&#2367;&#2352;&#2366;&#2325;&#2366;&#2352;) - </span><span style=\"font-family: Palanquin;\">having no regular form or shape</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Roboto;\">8. </span><span style=\"font-family: Roboto;\">Select the most appropriate one-word substitution for the given group of words.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><strong><span style=\"font-family: Roboto;\">The</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">violation or profanation of anything sacred or held sacred</span></strong></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Roboto;\">Select the most appropriate one-word substitution for the given group of words.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><strong><span style=\"font-family: Roboto;\">The</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">violation or profanation of anything sacred or held sacred</span></strong></p>\n",
                    options_en: ["<p>Pious</p>\n", "<p>Sacramental</p>\n", 
                                "<p>Holy</p>\n", "<p>Sacrilege</p>\n"],
                    options_hi: ["<p>Pious</p>\n", "<p>Sacramental</p>\n",
                                "<p>Holy</p>\n", "<p>Sacrilege</p>\n"],
                    solution_en: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Roboto;\">Sacrilege - </span><span style=\"font-family: Roboto;\">The</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">violation or profanation of anything sacred or held sacred</span></p>\r\n<p><span style=\"font-family: Roboto;\">Pious - strict in religious beliefs and practices</span></p>\r\n<p><span style=\"font-family: Roboto;\">Sacramental - something regarded as possessing a sacred character or mysterious significance</span></p>\r\n<p><span style=\"font-family: Roboto;\">Holy - worthy of complete devotion and trust</span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Palanquin;\">Sacrilege(&#2309;&#2346;&#2357;&#2367;&#2340;&#2381;&#2352;&#2368;&#2325;&#2352;&#2339;) - </span><span style=\"font-family: Roboto;\">The</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">violation or profanation of anything sacred or held sacred</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Pious(&#2343;&#2366;&#2352;&#2381;&#2350;&#2367;&#2325;) - strict in religious beliefs and practices</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Sacramental(&#2346;&#2357;&#2367;&#2340;&#2381;&#2352;)- something regarded as possessing a sacred character or mysterious significance</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Holy(&#2346;&#2357;&#2367;&#2340;&#2381;&#2352;) - worthy of complete devotion and trust</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9. </span><span style=\"font-family:Roboto\">Select the option that will improve the (bracketed) part of the sentence. In case of no improvement select the option \'No improvement\'.</span></p> <p><span style=\"font-family:Roboto\">Check all radiators for (meagre leaks, special) round pipework connections.</span></p>",
                    question_hi: " <p>9. </span><span style=\"font-family:Roboto\">Select the option that will improve the (bracketed) part of the sentence. In case of no improvement select the option \'No improvement\'.</span></p> <p><span style=\"font-family:Roboto\">Check all radiators for (meagre leaks, special) round pipework connections.</span></p>",
                    options_en: [" <p> small leaks, especially</span></p>", " <p> No improvement</span></p>", 
                                " <p> modest leaks, specially</span></p>", " <p> cramped leaks, specially</span></p>"],
                    options_hi: [" <p> small leaks, especially</span></p>", " <p> No improvement</span></p>",
                                " <p> modest leaks, specially</span></p>", " <p> cramped leaks, specially</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">9.(a)</span></p> <p><span style=\"font-family:Roboto\">The given sentence needs an adverb(</span><span style=\"font-family:Roboto\">especially</span><span style=\"font-family:Roboto\">) and not an adjective(</span><span style=\"font-family:Roboto\">special</span><span style=\"font-family:Roboto\">). </span><span style=\"font-family:Roboto\">Hence, ‘small leaks, especially’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">9.(a)</span></p> <p><span style=\"font-family:Palanquin Dark\">दिए गए sentence में adverb(</span><span style=\"font-family:Roboto\">especially</span><span style=\"font-family:Palanquin Dark\">) की आवश्यकता है न कि  adjective(</span><span style=\"font-family:Roboto\">special</span><span style=\"font-family:Palanquin Dark\">) की। इसलिए, </span><span style=\"font-family:Roboto\"> ‘small leaks, especially’ </span><span style=\"font-family:Palanquin Dark\"> सबसे उपयुक्त उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p>10.</span><span style=\"font-family:Roboto\"> Fill in the blank with an appropriate option.</span></p> <p><span style=\"font-family:Roboto\"> David always kept his ________ open for anything that might put them at risk.</span></p>",
                    question_hi: " <p>10.</span><span style=\"font-family:Roboto\"> Fill in the blank with an appropriate option.</span></p> <p><span style=\"font-family:Roboto\"> David always kept his ________ open for anything that might put them at risk.</span></p>",
                    options_en: [" <p> black eye</span></p>", " <p> starry eyes</span></p>", 
                                " <p> eagle eyes</span></p>", " <p> sheep eyes</span></p>"],
                    options_hi: [" <p> black eye</span></p>", " <p> starry eyes</span></p>",
                                " <p> eagle eyes</span></p>", " <p> sheep eyes</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">10.(c)</span></p> <p><span style=\"font-family:Roboto\">‘Eagle eyes’ means </span><span style=\"font-family:Roboto\">a careful or close watch</span><span style=\"font-family:Roboto\">. The given sentence states that David always kept his eagle eyes open for anything that might put them at risk. Hence, ‘eagle eyes’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">10.(c)</span></p> <p><span style=\"font-family:Palanquin\">‘Eagle eyes’ का अर्थ है सावधान या निकट से देखना। दिए गए sentence में कहा गया है कि डेविड हमेशा किसी भी चीज के लिए अपनी नजर को बहुत तेज  रखता था जो उन्हें जोखिम में डाल सकती थी। इसलिए, ‘eagle eyes’ सबसे उपयुक्त उत्तर है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Roboto;\">Select the word that is closest in meaning (SYNONYM) to the word given below.</span><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\"> Attune</span></strong></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Roboto;\">Select the word that is closest in meaning (SYNONYM) to the word given below.</span><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\"> Attune</span></strong></p>\n",
                    options_en: ["<p>Assimilate</p>\n", "<p>Skew</p>\n", 
                                "<p>Estrange</p>\n", "<p>Disrupt</p>\n"],
                    options_hi: ["<p>Assimilate</p>\n", "<p>Skew</p>\n",
                                "<p>Estrange</p>\n", "<p>Disrupt</p>\n"],
                    solution_en: "<p>11.(a)</p>\r\n<p><span style=\"font-family: Roboto;\"> Attune - </span><span style=\"font-family: Roboto;\">able to understand or recognize something</span></p>\r\n<p><span style=\"font-family: Roboto;\">Skew - to affect facts or information so that they are not accurate</span></p>\r\n<p><span style=\"font-family: Roboto;\">Estrange - to cause someone to be no longer friendly </span></p>\r\n<p><span style=\"font-family: Roboto;\">Disrupt - to interrupt or throw something into disorder</span></p>\n",
                    solution_hi: "<p>11.(a)</p>\r\n<p><span style=\"font-family: Palanquin;\"> Attune(&#2309;&#2344;&#2369;&#2325;&#2370;&#2354; &#2325;&#2352;&#2344;&#2366;) - </span><span style=\"font-family: Roboto;\">able to understand or recognize something</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Skew(&#2340;&#2367;&#2352;&#2331;&#2366; &#2351;&#2366; &#2335;&#2375;&#2397;&#2366; ) - to affect facts or information so that they are not accurate</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Estrange(&#2342;&#2370;&#2352; &#2325;&#2352;&#2344;&#2366; ) - to cause someone to be no longer friendly </span></p>\r\n<p><span style=\"font-family: Palanquin;\">Disrupt(&#2327;&#2337;&#2364;&#2348;&#2337;&#2364; &#2325;&#2352;&#2344;&#2366;) - to interrupt or throw something into disorder</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12. </span><span style=\"font-family:Roboto\">Select the wrongly spelt word</span></p>",
                    question_hi: " <p>12. </span><span style=\"font-family:Roboto\">Select the wrongly spelt word</span></p>",
                    options_en: [" <p>   Innocuous</span></p>", " <p>   Ingenous</span></p>", 
                                " <p>   Inimical</span></p>", " <p>  Invulnerable</span></p>"],
                    options_hi: [" <p>   Innocuous</span></p>", " <p>   Ingenous</span></p>",
                                " <p>   Inimical</span></p>", " <p>  Invulnerable</span></p>"],
                    solution_en: " <p>12.(b) </span><span style=\"font-family:Roboto\">Ingenous</span></p> <p><span style=\"font-family:Roboto\">“</span><span style=\"font-family:Roboto\">Ingenious</span><span style=\"font-family:Roboto\">” is the correct spelling.</span></p>",
                    solution_hi: " <p>12.(b) </span><span style=\"font-family:Roboto\">Ingenous</span></p> <p><span style=\"font-family:Roboto\">“</span><span style=\"font-family:Roboto\">Ingenious</span><span style=\"font-family:Palanquin\">” सही spelling है। </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <span style=\"font-family: Roboto;\">Select the most appropriate antonym of the given word.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><strong><span style=\"font-family: Roboto;\">BABBLE</span></strong></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Roboto;\">Select the most appropriate antonym of the given word.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><strong><span style=\"font-family: Roboto;\">BABBLE</span></strong></p>\n",
                    options_en: ["<p>DRIVEL</p>\n", "<p>SENSE</p>\n", 
                                "<p>JARGON</p>\n", "<p>GIBBERISH</p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n"],
                    options_hi: ["<p>DRIVEL</p>\n", "<p>SENSE</p>\n",
                                "<p>JARGON</p>\n", "<p>GIBBERISH</p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n"],
                    solution_en: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Roboto;\">BABBLE - talk rapidly and continuously in a foolish and excited manner.</span></p>\r\n<p><span style=\"font-family: Roboto;\">DRIVEL - To write or say completely worthless or nonsense</span></p>\r\n<p><span style=\"font-family: Roboto;\">JARGON - special words and phrases that are used by particular groups of people</span></p>\r\n<p><span style=\"font-family: Roboto;\">GIBBERISH - words that have no meaning or are impossible to understand</span></p>\n",
                    solution_hi: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Palanquin;\">BABBLE(&#2327;&#2346;&#2358;&#2346; &#2325;&#2352;&#2344;&#2366;) - talk rapidly and continuously in a foolish and excited manner.</span></p>\r\n<p><span style=\"font-family: Palanquin;\">DRIVEL(&#2344;&#2367;&#2352;&#2352;&#2381;&#2341;&#2325; &#2348;&#2366;&#2340;&#2375;&#2306; ) - To write or say completely worthless or nonsense</span></p>\r\n<p><span style=\"font-family: Palanquin;\">JARGON(&#2348;&#2375;&#2350;&#2340;&#2354;&#2348;) - special words and phrases that are used by particular groups of people</span></p>\r\n<p><span style=\"font-family: Palanquin;\">GIBBERISH(&#2309;&#2360;&#2381;&#2346;&#2359;&#2381;&#2335; &#2313;&#2330;&#2381;&#2330;&#2366;&#2352;&#2339;) - words that have no meaning or are impossible to understand</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14.</span><span style=\"font-family:Roboto\"> Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Roboto\">It could be</span></p> <p><span style=\"font-family:Roboto\">P. became a byword for superhuman intellectual ability</span></p> <p><span style=\"font-family:Roboto\">Q. argued that 1919 was the year when Einstein\'s name </span></p> <p><span style=\"font-family:Roboto\">R. making possible the small industry of</span></p> <p><span style=\"font-family:Roboto\">S. Einstein-themed merchandise that still exists today</span></p>",
                    question_hi: " <p>14.</span><span style=\"font-family:Roboto\"> Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Roboto\">It could be</span></p> <p><span style=\"font-family:Roboto\">P. became a byword for superhuman intellectual ability</span></p> <p><span style=\"font-family:Roboto\">Q. argued that 1919 was the year when Einstein\'s name </span></p> <p><span style=\"font-family:Roboto\">R. making possible the small industry of</span></p> <p><span style=\"font-family:Roboto\">S. Einstein-themed merchandise that still exists today</span></p>",
                    options_en: [" <p> QPRS</span></p>", " <p>  PSQR</span></p>", 
                                " <p>  RSPQ</span></p>", " <p>  PQRS</span></p>"],
                    options_hi: [" <p> QPRS</span></p>", " <p>  PSQR</span></p>",
                                " <p>  RSPQ</span></p>", " <p>  PQRS</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">14.(a)</span><span style=\"font-family:Roboto\"> QPRS</span></p> <p><span style=\"font-family:Roboto\">Sentence Q will be the starting line as it introduces the main idea of the parajumble i.e. argument on the year 1919. However, Sentence P states that it is the year </span><span style=\"font-family:Roboto\">when Einstein\'s name became a byword for superhuman intellectual ability</span><span style=\"font-family:Roboto\">. So, P will follow Q. Further, Sentence R talks about the small industry and Sentence S talks about </span><span style=\"font-family:Roboto\">Einstein-themed merchandise that still exists today</span><span style=\"font-family:Roboto\">. So, S will follow R. Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">14.(a) </span><span style=\"font-family:Roboto\"> QPRS</span></p> <p><span style=\"font-family:Palanquin\">Sentence Q प्रारम्भिक line होगी क्योंकि इसमें parajumble का मुख्य विचार ‘argument on the year 1919’ शामिल  है। हालांकि, Sentence P बताता है कि यह वह वर्ष है जब आइंस्टीन का नाम अलौकिक बौद्धिक क्षमता के लिए एक संकेत बन गया। तो, Q, के बाद P आएगा। इसके अलावा, Sentence R छोटे उद्योग के बारे में बात करता है और Sentence S आइंस्टीन-थीम वाले माल के बारे में बात करता है जो आज भी मौजूद है। तो, R, के बाद </span><span style=\"font-family:Palanquin\">S आएगा</span><span style=\"font-family:Palanquin\">। options के माध्यम से जाने पर , option a में सही sequence है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Roboto;\">Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\">All bark and no bite</span></strong></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Roboto;\">Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\">All bark and no bite</span></strong></p>\n",
                    options_en: ["<p>Someone who sticks to his words</p>\n", "<p>Actions are stronger than mere words</p>\n", 
                                "<p>Not being able to understand anything</p>\n", "<p>Full of talk, but low on action</p>\n"],
                    options_hi: ["<p>Someone who sticks to his words</p>\n", "<p>Actions are stronger than mere words</p>\n",
                                "<p>Not being able to understand anything</p>\n", "<p>Full of talk, but low on action</p>\n"],
                    solution_en: "<p>15.(d) All bark and no bite - <span style=\"font-family: Roboto;\">Full of talk, but low on action</span></p>\r\n<p><span style=\"font-family: Roboto;\">E.g. John is all bark and no bite about the new project.</span></p>\n",
                    solution_hi: "<p>15.(d) All bark and no bite - <span style=\"font-family: Palanquin;\">Full of talk, but low on action/&#2348;&#2366;&#2340;&#2379;&#2306; &#2360;&#2375; &#2349;&#2352;&#2366; &#2361;&#2376;, &#2354;&#2375;&#2325;&#2367;&#2344; &#2325;&#2366;&#2350; &#2325;&#2350; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - John is all bark and no bite about the new project./&#2332;&#2377;&#2344; &#2344;&#2319; &#2346;&#2381;&#2352;&#2379;&#2332;&#2375;&#2325;&#2381;&#2335; &#2350;&#2375;&#2306; &#2325;&#2369;&#2331; &#2325;&#2366;&#2350; &#2344;&#2361;&#2368;&#2306; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2360;&#2367;&#2352;&#2381;&#2347; &#2309;&#2344;&#2366;&#2357;&#2358;&#2381;&#2351;&#2325; &#2348;&#2366;&#2340;&#2375; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Roboto;\">Choose the word that is opposite in meaning to the given word.</span></p>\r\n<p><strong><span style=\"font-family: Roboto;\"> Leisure</span></strong></p>\n",
                    question_hi: "<p>16. <span style=\"font-family: Roboto;\">Choose the word that is opposite in meaning to the given word.</span></p>\r\n<p><strong><span style=\"font-family: Roboto;\"> Leisure</span></strong></p>\n",
                    options_en: ["<p>Retire</p>\n", "<p>Worship</p>\n", 
                                "<p>Work</p>\n", "<p>Play</p>\n"],
                    options_hi: ["<p>Retire</p>\n", "<p>Worship</p>\n",
                                "<p>Work</p>\n", "<p>Play</p>\n"],
                    solution_en: "<p>16.(c)</p>\r\n<p><span style=\"font-family: Roboto;\">Leisure - </span><span style=\"font-family: Roboto;\">free time or unoccupied</span></p>\r\n<p><span style=\"font-family: Roboto;\">Retire - to withdraw from one\'s occupation</span></p>\r\n<p><span style=\"font-family: Roboto;\">Worship - to show a strong feeling of respect and admiration for God </span></p>\r\n<p><span style=\"font-family: Roboto;\">Play - to do activities for fun or enjoyment </span></p>\n",
                    solution_hi: "<p>16.(c)</p>\r\n<p><span style=\"font-family: Palanquin;\">Leisure(&#2393;&#2366;&#2354;&#2368; &#2360;&#2350;&#2351;) - </span><span style=\"font-family: Roboto;\">free time or unoccupied</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Retire(&#2360;&#2375;&#2357;&#2366;&#2344;&#2367;&#2357;&#2371;&#2340;&#2381;&#2340; &#2361;&#2379;&#2344;&#2366;) - to withdraw from one\'s occupation</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Worship(&#2346;&#2370;&#2332;&#2366; &#2325;&#2352;&#2344;&#2366;) - to show a strong feeling of respect and admiration for God </span></p>\r\n<p><span style=\"font-family: Palanquin;\">Play(&#2326;&#2375;&#2354;&#2344;&#2366; ) - to do activities for fun or enjoyment</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Roboto;\">17. </span><span style=\"font-family: Roboto;\">In the following question, out of the given four alternatives, select the alternative which best expresses the meaning of the Idiom/Phrase. </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\">Appeal to Caesar</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">17. </span><span style=\"font-family: Roboto;\">In the following question, out of the given four alternatives, select the alternative which best expresses the meaning of the Idiom/Phrase. </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\">Appeal to Caesar</span></strong></p>\n",
                    options_en: ["<p><span style=\"font-family: Roboto;\"> An authority given to a foolish person</span></p>\n", "<p><span style=\"font-family: Roboto;\">To expect good from a wrong person</span></p>\n", 
                                "<p><span style=\"font-family: Roboto;\"> To live in a false reality</span></p>\n", "<p><span style=\"font-family: Roboto;\">Appeal to the highest possible authority</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Roboto;\"> An authority given to a foolish person</span></p>\n", "<p><span style=\"font-family: Roboto;\">To expect good from a wrong person</span></p>\n",
                                "<p><span style=\"font-family: Roboto;\"> To live in a false reality</span></p>\n", "<p><span style=\"font-family: Roboto;\">Appeal to the highest possible authority</span></p>\n"],
                    solution_en: "<p>17.(d) <span style=\"font-family: Roboto;\">Appeal to Caesar - </span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">Appeal to the highest possible authority</span></p>\r\n<p><span style=\"font-family: Roboto;\">E.g. Paul appealed to Caesar for justice.</span></p>\n",
                    solution_hi: "<p>17.(d) <span style=\"font-family: Roboto;\">Appeal to Caesar - </span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin;\">Appeal to the highest possible authority/&#2313;&#2330;&#2381;&#2330;&#2340;&#2350; &#2360;&#2306;&#2349;&#2357; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2360;&#2375; &#2309;&#2346;&#2368;&#2354; &#2325;&#2352;&#2375;&#2306;</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - Paul appealed to Caesar for justice./ &#2346;&#2377;&#2354; &#2344;&#2375; &#2313;&#2330;&#2381;&#2330;&#2340;&#2350; &#2360;&#2306;&#2349;&#2357; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2360;&#2375; &#2344;&#2381;&#2351;&#2366;&#2351; &#2325;&#2368; &#2351;&#2366;&#2330;&#2344;&#2366; &#2325;&#2368;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Roboto;\">18. </span><span style=\"font-family: Roboto;\">Select the most appropriate one-word substitution for the given group of words. </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\">One who is a centre of attraction</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">18. </span><span style=\"font-family: Roboto;\">Select the most appropriate one-word substitution for the given group of words. </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\">One who is a centre of attraction</span></strong></p>\n",
                    options_en: ["<p><span style=\"font-family: Roboto;\">Exposure</span></p>\n", "<p><span style=\"font-family: Roboto;\">Cynosure</span></p>\n", 
                                "<p><span style=\"font-family: Roboto;\">Commissure</span></p>\n", "<p><span style=\"font-family: Roboto;\"> Censure</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Roboto;\">Exposure</span></p>\n", "<p><span style=\"font-family: Roboto;\">Cynosure</span></p>\n",
                                "<p><span style=\"font-family: Roboto;\">Commissure</span></p>\n", "<p><span style=\"font-family: Roboto;\"> Censure</span></p>\n"],
                    solution_en: "<p>18.(b)</p>\r\n<p><span style=\"font-family: Roboto;\">Cynosure - </span><span style=\"font-family: Roboto;\">One who is a centre of attraction</span></p>\r\n<p><span style=\"font-family: Roboto;\">Exposure - an act of revealing or unmasking</span></p>\r\n<p><span style=\"font-family: Roboto;\">Commissure -surface where two parts meet or a Joint</span></p>\r\n<p><span style=\"font-family: Roboto;\">Censure - strong criticism</span></p>\n",
                    solution_hi: "<p>18.(b)</p>\r\n<p><span style=\"font-family: Palanquin;\">Cynosure(&#2310;&#2325;&#2352;&#2381;&#2359;&#2339;-&#2348;&#2367;&#2344;&#2381;&#2342;&#2369;) - </span><span style=\"font-family: Roboto;\">One who is a centre of attraction</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Exposure(&#2326;&#2369;&#2354;&#2366;&#2360;&#2366;) - an act of revealing or unmasking</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Commissure(&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2325;&#2366;) -surface where two parts meet or a Joint</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Censure(&#2344;&#2367;&#2306;&#2342;&#2366;) - strong criticism</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Roboto;\">19.</span><span style=\"font-family: Roboto;\"> Choose the option that is the correct indirect form of the sentence.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> Rahul asked me, \"Are we going to New York next year?\"</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">19.</span><span style=\"font-family: Roboto;\"> Choose the option that is the correct indirect form of the sentence.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> Rahul asked me, \"Are we going to New York next year?\"</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Roboto;\">Rahul asked that are we going to New York next year.</span></p>\n", "<p><span style=\"font-family: Roboto;\">Rahul asked me that were we going to New York next year.</span></p>\n", 
                                "<p><span style=\"font-family: Roboto;\">Rahul asked me that would we go to New York next year?</span></p>\n", "<p><span style=\"font-family: Roboto;\">Rahul asked me if we were going to New York next year.</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Roboto;\">Rahul asked that are we going to New York next year.</span></p>\n", "<p><span style=\"font-family: Roboto;\">Rahul asked me that were we going to New York next year.</span></p>\n",
                                "<p><span style=\"font-family: Roboto;\">Rahul asked me that would we go to New York next year?</span></p>\n", "<p><span style=\"font-family: Roboto;\">Rahul asked me if we were going to New York next year.</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">19.(d) </span><span style=\"font-family: Roboto;\">Rahul asked me if we were going to New York next year.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) </span><span style=\"font-weight: 400;\">Rahul asked that are we going to New York next year.(&lsquo;asked&rsquo; &amp; &lsquo;that&rsquo; can&rsquo;t be used together)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) </span><span style=\"font-weight: 400;\">Rahul asked me that were we going to New York next year.(&lsquo;asked&rsquo; &amp; &lsquo;that&rsquo; can&rsquo;t be used together)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><span style=\"font-weight: 400;\">Rahul asked me that would we go to New York next year?(&lsquo;asked&rsquo; &amp; &lsquo;that&rsquo; can&rsquo;t be used together)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">19.(d) </span><span style=\"font-family: Roboto;\">Rahul asked me if we were going to New York next year.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) </span><span style=\"font-weight: 400;\">Rahul asked that are we going to New York next year.(&lsquo;asked&rsquo; &#2324;&#2352; &lsquo;that&rsquo; &#2319;&#2325; &#2360;&#2366;&#2341; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2344;&#2361;&#2368;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) </span><span style=\"font-weight: 400;\">Rahul asked me that were we going to New York next year.(&lsquo;asked&rsquo; &#2324;&#2352; &lsquo;that&rsquo; &#2319;&#2325; &#2360;&#2366;&#2341; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2344;&#2361;&#2368;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><span style=\"font-weight: 400;\">Rahul asked me that would we go to New York next year?(&lsquo;asked&rsquo; &#2324;&#2352; &lsquo;that&rsquo; &#2319;&#2325; &#2360;&#2366;&#2341; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2344;&#2361;&#2368;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376; )</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Roboto\">20. </span><span style=\"font-family:Roboto\">Select the wrongly spelt word</span></p>",
                    question_hi: " <p><span style=\"font-family:Roboto\">20. </span><span style=\"font-family:Roboto\">Select the wrongly spelt word</span></p>",
                    options_en: [" <p> </span><span style=\"font-family:Roboto\">gauche</span></p>", " <p> </span><span style=\"font-family:Roboto\">fallacious</span></p>", 
                                " <p> </span><span style=\"font-family:Roboto\"> erudite</span></p>", " <p> </span><span style=\"font-family:Roboto\">harange</span></p> <p><span style=\"font-family:Roboto\"> </span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Roboto\">gauche</span></p>", " <p> </span><span style=\"font-family:Roboto\">fallacious</span></p>",
                                " <p> </span><span style=\"font-family:Roboto\"> erudite</span></p>", " <p> </span><span style=\"font-family:Roboto\">harange</span></p>"],
                    solution_en: " <p>20.(d) </span><span style=\"font-family:Roboto\">harange</span></p> <p><span style=\"font-family:Roboto\">“Harangue” is the correct spelling.</span></p>",
                    solution_hi: " <p>20.(d) </span><span style=\"font-family:Roboto\">harange</span></p> <p><span style=\"font-family:Palanquin\">“Harangue” सही spelling है। </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Roboto;\">21. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high </span><span style=\"font-family: Roboto;\">surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused </span><span style=\"font-family: Roboto;\">mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; </span><span style=\"font-family: Roboto;\">and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________ </span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.21.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">21. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.21.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Roboto;\">on</span></p>\n", "<p><span style=\"font-family: Roboto;\">across</span></p>\n", 
                                "<p><span style=\"font-family: Roboto;\"> from</span></p>\n", "<p><span style=\"font-family: Roboto;\">through</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Roboto;\">on</span></p>\n", "<p><span style=\"font-family: Roboto;\">across</span></p>\n",
                                "<p><span style=\"font-family: Roboto;\"> from</span></p>\n", "<p><span style=\"font-family: Roboto;\">through</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">21.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\">The given passage talks about the </span><span style=\"font-family: Roboto;\">Data from the Pioneer spacecraft of NASA</span><span style=\"font-family: Roboto;\">. Hence, &lsquo;from&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">21.(c)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; passage </span><span style=\"font-family: Roboto;\">NASA </span><span style=\"font-family: Palanquin Dark;\">&#2325;&#2375; </span><span style=\"font-family: Roboto;\">Pioneer spacecraft </span><span style=\"font-family: Palanquin Dark;\">&#2325;&#2375; </span><span style=\"font-family: Roboto;\">Data </span><span style=\"font-family: Palanquin Dark;\">&#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2348;&#2366;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;from&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Roboto;\">22. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.22.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">22. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.22.</span></p>\n",
                    options_en: ["<p>due</p>\n", "<p>understood</p>\n", 
                                "<p>because</p>\n", "<p>cause</p>\n"],
                    options_hi: ["<p>due</p>\n", "<p>understood</p>\n",
                                "<p>because</p>\n", "<p>cause</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">22.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">&lsquo;</span><span style=\"font-family: Roboto;\">Due to</span><span style=\"font-family: Roboto;\">&rsquo; means because of. The given passage states that </span><span style=\"font-family: Roboto;\">the high surface temperature of Venus is due to an atmospheric greenhouse effect</span><span style=\"font-family: Roboto;\">. Hence, &lsquo;due&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">22.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">&lsquo;</span><span style=\"font-family: Roboto;\">Due to</span><span style=\"font-family: Palanquin Dark;\">&rsquo; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; passage &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2358;&#2369;&#2325;&#2381;&#2352; &#2325;&#2368; &#2360;&#2340;&#2361; &#2325;&#2366; &#2313;&#2330;&#2381;&#2330; &#2340;&#2366;&#2346;&#2350;&#2366;&#2344; &#2357;&#2366;&#2351;&#2369;&#2350;&#2306;&#2337;&#2354;&#2368;&#2351; &#2327;&#2381;&#2352;&#2368;&#2344;&#2361;&#2366;&#2313;&#2360; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;due&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Roboto;\">23. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.23.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">23. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.23.</span></p>\n",
                    options_en: ["<p>affect</p>\n", "<p>displacement</p>\n", 
                                "<p>contour</p>\n", "<p>effect</p>\n"],
                    options_hi: ["<p>affect</p>\n", "<p>displacement</p>\n",
                                "<p>contour</p>\n", "<p>effect</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">23.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\">&lsquo;</span><span style=\"font-family: Roboto;\">Effect</span><span style=\"font-family: Roboto;\">&rsquo; means </span><span style=\"font-family: Roboto;\">a change that is caused by something</span><span style=\"font-family: Roboto;\">. The given passage talks about the greenhouse</span><span style=\"font-family: Roboto;\"> effect</span><span style=\"font-family: Roboto;\">. Hence, &lsquo;</span><span style=\"font-family: Roboto;\">effect</span><span style=\"font-family: Roboto;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">23.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\">&lsquo;</span><span style=\"font-family: Roboto;\">Effect</span><span style=\"font-family: Palanquin Dark;\">&rsquo; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2320;&#2360;&#2366; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2332;&#2379; &#2325;&#2367;&#2360;&#2368; &#2330;&#2368;&#2332; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; passage &#2327;&#2381;&#2352;&#2368;&#2344;&#2361;&#2366;&#2313;&#2360; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357; &#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2348;&#2366;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;</span><span style=\"font-family: Roboto;\">effect</span><span style=\"font-family: Palanquin Dark;\">&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Roboto;\">24. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.24.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">24. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.24.</span></p>\n",
                    options_en: ["<p>surface</p>\n", "<p>walls</p>\n", 
                                "<p>atmosphere</p>\n", "<p>core</p>\n"],
                    options_hi: ["<p>surface</p>\n", "<p>walls</p>\n",
                                "<p>atmosphere</p>\n", "<p>core</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">24.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">&lsquo;Surface&rsquo; means </span><span style=\"font-family: Roboto;\">the outside part of something</span><span style=\"font-family: Roboto;\">. The given passage states that the greenhouse effect </span><span style=\"font-family: Roboto;\">warms the planet\'s </span><span style=\"font-family: Roboto;\">surface</span><span style=\"font-family: Roboto;\">. Hence, &lsquo;</span><span style=\"font-family: Roboto;\">surface</span><span style=\"font-family: Roboto;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">24.(a)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&lsquo;Surface&rsquo; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2348;&#2366;&#2361;&#2352;&#2368; &#2349;&#2366;&#2327;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; passage &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2327;&#2381;&#2352;&#2368;&#2344;&#2361;&#2366;&#2313;&#2360; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357; &#2325;&#2368; &#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2325;&#2368; &#2360;&#2340;&#2361; &#2325;&#2379; &#2327;&#2352;&#2381;&#2350; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;</span><span style=\"font-family: Roboto;\">surface</span><span style=\"font-family: Palanquin Dark;\">&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Roboto;\">25. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.25.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">25. Cloze test:</span></p>\r\n<p><span style=\"font-family: Roboto;\">Data (21) ________ the Pioneer spacecraft of NASA apparently prove the theory that the high surface temperature of Venus is (22) ________ to an atmospheric greenhouse effect caused mainly by a blanket of carbon dioxide. Such a greenhouse (23) ________ is created when energy in the form of sunlight easily passes through a planet\'s atmosphere, warms its (24) ________&nbsp; and is converted to heat radiation that is then held in by the atmosphere from to bottom. Venus has a relatively thin atmosphere like the Earth\'s, but Venus\' atmosphere consists of more than ninety percent carbon dioxide, compared to less than four percent in that of the (25) ________</span></p>\r\n<p><span style=\"font-family: Roboto;\">Select the most appropriate option to fill in the blank No.25.</span></p>\n",
                    options_en: ["<p>Pluto</p>\n", "<p>Mars</p>\n", 
                                "<p>Earth</p>\n", "<p>Mercury</p>\n"],
                    options_hi: ["<p>Pluto</p>\n", "<p>Mars</p>\n",
                                "<p>Earth</p>\n", "<p>Mercury</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">25.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\">The given passage talks about Earth on which we all live. Hence, &lsquo;earth&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">25.(c)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; passage &#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2348;&#2366;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360; &#2346;&#2352; &#2361;&#2350; &#2360;&#2349;&#2368; &#2352;&#2361;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;earth&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Roboto;\">26. </span><span style=\"font-family: Roboto;\">Select the option in which the numbers share the same relationship in set as that shared by the numbers in the given sets. (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g.13 &mdash; Operations on 13 such as adding /subtracting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(15, 13, 56)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(17, 11, 168)&nbsp;</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">26. </span><span style=\"font-family: Palanquin;\">&#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2357;&#2361;&#2368; &#2360;&#2306;&#2348;&#2306;&#2343; &#2360;&#2366;&#2333;&#2366; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2306; &#2332;&#2379; &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351; &#2350;&#2375;&#2306; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2366;&#2333;&#2366; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; (&#2343;&#2381;&#2351;&#2366;&#2344; &#2342;&#2375;&#2306;: &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2379; &#2313;&#2344;&#2325;&#2375; &#2328;&#2335;&#2325; &#2309;&#2306;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2354;&#2327; &#2309;&#2354;&#2327; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366; &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2368; &#2332;&#2366;&#2344;&#2368; &#2330;&#2366;&#2361;&#2367;&#2319; &#2313;&#2342;&#2366;. 13 - 13 &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2332;&#2376;&#2360;&#2375; 13 &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2366; /&#2328;&#2335;&#2366;&#2344;&#2366;/&#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2344;&#2366; &#2310;&#2342;&#2367; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; 13 &#2325;&#2379; 1 &#2319;&#2357;&#2306; 3 &#2350;&#2375;&#2306; &#2309;&#2354;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2324;&#2352; &#2347;&#2367;&#2352; 1 &#2319;&#2357;&#2306; 3 &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(15, 13, 56)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(17, 11, 168)</span></p>\n",
                    options_en: ["<p>(15, 11,114)</p>\n", "<p>(23, 21,78)</p>\n", 
                                "<p>(20, 14, 204)</p>\n", "<p>(22, 16, 238)</p>\n"],
                    options_hi: ["<p>(15, 11,114)</p>\n", "<p>(23, 21,78)</p>\n",
                                "<p>(20, 14, 204)</p>\n", "<p>(22, 16, 238)</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">26.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Logic : - (First number + Second number) &times; (First number - Second number) = Third number</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (15 , 13, 56) &rarr; (15 + 13) <span style=\"font-family: Roboto;\">&times;</span> (15 - 13) &rarr; (28) <span style=\"font-family: Roboto;\">&times;</span> (2) = 56</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (17 ,11, 168) &rarr; (17 + 11) <span style=\"font-family: Roboto;\">&times;</span> (17- 11) &rarr; (28) <span style=\"font-family: Roboto;\">&times;</span> (6) = 168</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Similarly,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (20 , 14, 204) &rarr; (20 + 14) <span style=\"font-family: Roboto;\">&times;</span> (20 - 14) &rarr; (34) x (6) &rarr; 204 </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">26.(c)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2352;&#2381;&#2325; : - (&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; + &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;) <span style=\"font-family: Arial Unicode MS;\"><span style=\"font-family: Roboto;\">&times;</span></span> (&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; - &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;) = &#2340;&#2368;&#2360;&#2352;&#2366; &#2344;&#2306;&#2348;&#2352;</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (15 , 13, 56) &rarr; (15 + 13) <span style=\"font-family: Roboto;\">&times;</span> (15 - 13) &rarr; (28) <span style=\"font-family: Roboto;\">&times;</span> (2) = 56</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (17 ,11, 168) &rarr; (17 + 11) <span style=\"font-family: Roboto;\">&times;</span> (17- 11) &rarr; (28) <span style=\"font-family: Roboto;\">&times;</span> (6) = 168</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (20 , 14, 204) &rarr; (20 + 14) <span style=\"font-family: Roboto;\">&times;</span> (20 - 14) &rarr; (34) x (6) &rarr; 204</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Roboto\">27.</span><span style=\"font-family:Roboto\"> If 132 F 624 = 657 and 84 F 321 = 504, then 168 F 27 = ? </span></p>",
                    question_hi: " <p><span style=\"font-family:Roboto\">27.</span><span style=\"font-family:Palanquin\"> यदि 132 F 624 = 657 और 84 F 321 = 504, तो 168 F 27 = ?</span></p>",
                    options_en: [" <p>  591  </span></p>", " <p>  203</span></p>", 
                                " <p>  195</span></p>", " <p> 793</span></p>"],
                    options_hi: [" <p>  591  </span></p>", " <p>  203</span></p>",
                                " <p>  195</span></p>", " <p> 793</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">27.(a)</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ (132 F 624) = 657 → (624 + 132) = 756 </span></p> <p><span style=\"font-family:Arial Unicode MS\">On reversing the 756 we get = 657</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ (84 F 321) = 504 →  (321 + 84) = 405</span></p> <p><span style=\"font-family:Arial Unicode MS\">On reversing the 405 we get = 504</span></p> <p><span style=\"font-family:Arial Unicode MS\">Similarly,</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ (168 F 27) = ? → (168 + 27) = 195</span></p> <p><span style=\"font-family:Arial Unicode MS\">On reversing the 195 we get = 591</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">27.(a)</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ (132 F 624) = 657 → (624 + 132) = 756 </span></p> <p><span style=\"font-family:Palanquin Dark\">756 के क्रम को उलटने पर हमें प्राप्त होता है = 657</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ (84 F 321) = 504 →  (321 + 84) = 405</span></p> <p><span style=\"font-family:Palanquin Dark\">405 के क्रम को उलटने पर हमें  प्राप्त होता है = 504</span></p> <p><span style=\"font-family:Palanquin Dark\">इसी प्रकार,</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ (168 F 27) = ? → (168 + 27) = 195</span></p> <p><span style=\"font-family:Palanquin Dark\">195 के क्रम को उलटने पर हमें मिलता है = 591</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Roboto;\">28. </span><span style=\"font-family: Roboto;\"> In the following Question, select the related letters from the given alternatives. </span></p>\r\n<p><span style=\"font-family: Roboto;\">OQ : PT :: WZ : ? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">28. </span><span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">OQ : PT : : WZ : ? </span></p>\n",
                    options_en: ["<p>ZW</p>\n", "<p>ID</p>\n", 
                                "<p>XC</p>\n", "<p>WC</p>\n"],
                    options_hi: ["<p>ZW</p>\n", "<p>ID</p>\n",
                                "<p>XC</p>\n", "<p>WC</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">28.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image41.png\" width=\"87\" height=\"53\"><span style=\"font-family: Roboto;\">Similarly, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image12.png\" width=\"91\" height=\"56\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">28.(c)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image41.png\" width=\"87\" height=\"53\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, <img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image12.png\" width=\"91\" height=\"56\"></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Roboto;\">29.</span><span style=\"font-family: Roboto;\"> In a certain code language, &lsquo;</span><span style=\"font-family: Roboto;\">RATHER&rsquo;</span><span style=\"font-family: Roboto;\"> is coded as </span><span style=\"font-family: Roboto;\">&lsquo;TGJUBS&rsquo;</span><span style=\"font-family: Roboto;\">. What is the code for </span><span style=\"font-family: Roboto;\">&lsquo;SAFETY&rsquo; </span><span style=\"font-family: Roboto;\">in that code language? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">29.</span><span style=\"font-family: Palanquin;\"> &#2319;&#2325; &#2326;&#2366;&#2360; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306;,</span><span style=\"font-family: Roboto;\"> \'RATHER\' </span><span style=\"font-family: Palanquin;\">&#2325;&#2379; \'</span><span style=\"font-family: Roboto;\">TGJUBS\' </span><span style=\"font-family: Palanquin;\">&#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; </span><span style=\"font-family: Roboto;\">\'SAFETY\'</span><span style=\"font-family: Palanquin;\"> &#2325;&#2379; &#2325;&#2376;&#2360;&#2375; &#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>AVGHBT</p>\n", "<p>AVGBGT</p>\n", 
                                "<p>AVHGBT</p>\n", "<p>AVGGBT</p>\n"],
                    options_hi: ["<p>AVGHBT</p>\n", "<p>AVGBGT</p>\n",
                                "<p>AVHGBT</p>\n", "<p>AVGGBT</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">29.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image5.png\" width=\"121\" height=\"81\"></p>\r\n<p><span style=\"font-family: Roboto;\">Similarly,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image2.png\" width=\"119\" height=\"78\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">29.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image5.png\" width=\"121\" height=\"81\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image2.png\" width=\"119\" height=\"78\"></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "6",
                    question_en: "<p>30. <span style=\"font-family: Roboto;\">By Interchanging the given two numbers (Not digits) which of the following equation will be correct?</span></p>\r\n<p><span style=\"font-family: Roboto;\"> 5 and 9 </span></p>\r\n<p><span style=\"font-family: Roboto;\">I. 7 &times;</span><span style=\"font-family: Roboto;\"> 9 + 5 - </span><span style=\"font-family: Roboto;\">8 &divide; </span><span style=\"font-family: Roboto;\">4 = 42 </span></p>\r\n<p><span style=\"font-family: Roboto;\">II. 9 &times;</span><span style=\"font-family: Roboto;\"> 3 + 5 &divide;</span><span style=\"font-family: Roboto;\"> 1 &ndash; 4 = 20 </span></p>\n",
                    question_hi: "<p>30. <span style=\"font-family: Palanquin;\">&#2342;&#2368; &#2327;&#2312; &#2342;&#2379; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; (&#2309;&#2306;&#2325;&#2379; &#2325;&#2379; &#2344;&#2361;&#2368;&#2306;) &#2325;&#2379; &#2310;&#2346;&#2360; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2344;&#2375; &#2346;&#2352; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; - &#2360;&#2366; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2360;&#2361;&#2368; &#2361;&#2379;&#2327;&#2366;?</span></p>\r\n<p><span style=\"font-family: Roboto;\"> 5</span><span style=\"font-family: Palanquin;\"> &#2324;&#2352; </span><span style=\"font-family: Roboto;\"> 9 </span></p>\r\n<p><span style=\"font-family: Roboto;\">I. 7 &times;</span><span style=\"font-family: Roboto;\"> 9 + 5 - </span><span style=\"font-family: Roboto;\">8 &divide; </span><span style=\"font-family: Roboto;\">4 = 42 </span></p>\r\n<p><span style=\"font-family: Roboto;\">II. 9 &times;</span><span style=\"font-family: Roboto;\"> 3 + 5 &divide;</span><span style=\"font-family: Roboto;\"> 1 &ndash; 4 = 20</span></p>\n",
                    options_en: ["<p>Both l and ll</p>\n", "<p>Neither I nor II</p>\n", 
                                "<p>Only ll</p>\n", "<p>Only l</p>\n"],
                    options_hi: ["<p>&#2404; &#2324;&#2352; &#2404;&#2404; &#2342;&#2379;&#2344;&#2379;&#2306;</p>\n", "<p>&#2344; &#2340;&#2379; &#2404; &#2324;&#2352; &#2344; &#2361;&#2368;&#2404;&#2404;</p>\n",
                                "<p>&#2325;&#2375;&#2357;&#2354; &#2404;&#2404;</p>\n", "<p>&#2325;&#2375;&#2357;&#2354; &#2404;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">30.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">1. Given = 7 <span style=\"font-family: Palanquin Dark;\">&times;</span> 9 + 5 - 8 &divide;</span><span style=\"font-family: Roboto;\"> 4 = 42</span></p>\r\n<p><span style=\"font-family: Roboto;\">After interchanging 5 and 9 we get,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 7 <span style=\"font-family: Palanquin Dark;\">&times;</span> 5 + 9 - 8 <span style=\"font-family: Roboto;\">&divide;</span></span><span style=\"font-family: Arial Unicode MS;\"> 4</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 35 + 7 = 42</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">2. Given = 9 <span style=\"font-family: Palanquin Dark;\">&times;</span> 3 + 5 <span style=\"font-family: Roboto;\">&divide;</span></span><span style=\"font-family: Arial Unicode MS;\"> 1 - 4 = 20</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">After interchanging 5 and 9 we get,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 5 <span style=\"font-family: Palanquin Dark;\">&times;</span> 3 + 9 <span style=\"font-family: Roboto;\">&divide;</span></span><span style=\"font-family: Arial Unicode MS;\"> 1 - 4</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 15 + 9 - 4</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 24 - 4 = 20</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Both 1 and 2 are correct</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">30.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">1. <span style=\"font-weight: 400;\">&#2342;&#2367;&#2351;&#2366; &#2361;&#2376;</span> = 7 <span style=\"font-family: Palanquin Dark;\">&times;</span> 9 + 5 - 8 &divide;</span><span style=\"font-family: Roboto;\"> 4 = 42</span></p>\r\n<p><span style=\"font-weight: 400;\">5 &#2324;&#2352; 9 &#2325;&#2379; &#2346;&#2352;&#2360;&#2381;&#2346;&#2352; &#2348;&#2342;&#2354;&#2344;&#2375; &#2346;&#2352; &#2361;&#2350; &#2346;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 7 <span style=\"font-family: Palanquin Dark;\">&times;</span> 5 + 9 - 8 <span style=\"font-family: Roboto;\">&divide;</span></span><span style=\"font-family: Arial Unicode MS;\"> 4</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 35 + 7 = 42</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">2. <span style=\"font-weight: 400;\">&#2342;&#2367;&#2351;&#2366; &#2361;&#2376;</span> = 9 <span style=\"font-family: Palanquin Dark;\">&times;</span> 3 + 5 <span style=\"font-family: Roboto;\">&divide;</span></span><span style=\"font-family: Arial Unicode MS;\"> 1 - 4 = 20</span></p>\r\n<p><span style=\"font-weight: 400;\">5 &#2324;&#2352; 9 &#2325;&#2379; &#2346;&#2352;&#2360;&#2381;&#2346;&#2352; &#2348;&#2342;&#2354;&#2344;&#2375; &#2346;&#2352; &#2361;&#2350; &#2346;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 5 <span style=\"font-family: Palanquin Dark;\">&times;</span> 3 + 9 <span style=\"font-family: Roboto;\">&divide;</span></span><span style=\"font-family: Arial Unicode MS;\"> 1 - 4</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 15 + 9 - 4</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; 24 - 4 = 20</span></p>\r\n<p><span style=\"font-weight: 400;\">1 &#2324;&#2352; 2 &#2342;&#2379;&#2344;&#2379;&#2306; &#2360;&#2361;&#2368; &#2361;&#2376;&#2306;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "6",
                    question_en: "<p>31.<span style=\"font-family: Roboto;\"> In the following question, four number pairs are given. In each pair the number on left side of (-) is related to the number of the right side of (-) with some Logic/Rule / Relation. Three pairs are similar on basis of same Logic/Rule/Relation. Select the odd one out from the given alternatives. (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g.13 &mdash; Operations on 13 such as adding /subtracting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed) </span></p>\n",
                    question_hi: "<p>31. <span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2330;&#2366;&#2352; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2351;&#2369;&#2327;&#2381;&#2350; &#2342;&#2367;&#2319; &#2327;&#2319; &#2361;&#2376;&#2306;&#2404; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2351;&#2369;&#2327;&#2381;&#2350; &#2350;&#2375;&#2306; (-) &#2325;&#2375; &#2348;&#2366;&#2351;&#2368;&#2306; &#2323;&#2352; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; (-) &#2325;&#2375; &#2342;&#2366;&#2351;&#2368; &#2323;&#2352; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2360;&#2375; &#2325;&#2367;&#2360;&#2368; &#2340;&#2352;&#2381;&#2325; / &#2344;&#2367;&#2351;&#2350; / &#2360;&#2306;&#2348;&#2306;&#2343; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; / &#2344;&#2367;&#2351;&#2350; / &#2360;&#2306;&#2348;&#2306;&#2343; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; &#2340;&#2368;&#2344; &#2351;&#2369;&#2327;&#2381;&#2350; &#2360;&#2350;&#2366;&#2344; &#2361;&#2376;&#2306;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2309;&#2354;&#2327; &#2351;&#2369;&#2327;&#2381;&#2350; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319;&#2404; (&#2343;&#2381;&#2351;&#2366;&#2344; &#2342;&#2375;&#2306;: &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2379; &#2313;&#2344;&#2325;&#2375; &#2328;&#2335;&#2325; &#2309;&#2306;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2354;&#2327; &#2309;&#2354;&#2327; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366;, &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2368; &#2332;&#2366;&#2344;&#2368; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404; &#2313;&#2342;&#2366;. 13 - 13 &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2332;&#2376;&#2360;&#2375; 13 &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2366; / &#2328;&#2335;&#2366;&#2344;&#2366; / &#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2344;&#2366; &#2310;&#2342;&#2367; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; 13 &#2325;&#2379; 1 &#2319;&#2357;&#2306; 3 &#2350;&#2375;&#2306; &#2309;&#2354;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2324;&#2352; &#2347;&#2367;&#2352; 1 &#2319;&#2357;&#2306; 3 &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;)</span></p>\n",
                    options_en: ["<p>2278 &mdash; 6834</p>\n", "<p>3072 &mdash; 9216</p>\n", 
                                "<p>1008 &mdash; 3024</p>\n", "<p>3744 &mdash; 11322</p>\n"],
                    options_hi: ["<p>2278 &mdash; 6834</p>\n", "<p>3072 &mdash; 9216</p>\n",
                                "<p>1008 &mdash; 3024</p>\n", "<p>3744 &mdash; 11322</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">31.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Logic : - (First number) &times; 3 = Second number</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (2278 - 6834) &rarr; (2278) x 3 = 6834</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (3072 - 9216) &rarr; (3072) x 3 = 9216</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (1008 - 3024) &rarr; (1008) x 3 = 3024</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">But,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (3744 - 11322) &rarr; (3744) x 3 = 11232 (Not 11322)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">31.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2352;&#2381;&#2325; : - (&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;) <span style=\"font-family: Roboto;\">&times;</span> 3 = &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (2278 - 6834) &rarr; (2278) x 3 = 6834</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (3072 - 9216) &rarr; (3072) x 3 = 9216</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (1008 - 3024) &rarr; (1008) x 3 = 3024</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2354;&#2375;&#2325;&#2367;&#2344;,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (3744 - 11322) &rarr; (3744) x 3 = 11232 (11322 &#2344;&#2361;&#2368;&#2306;)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "6",
                    question_en: " <p>32</span><span style=\"font-family:Roboto\">. In the following question, select the odd word pair from the given alternatives. </span></p>",
                    question_hi: " <p>32</span><span style=\"font-family:Palanquin\">. निम्नलिखित प्रश्न में दिए गए विकल्पों में से अलग शब्द युग्म को चुनिए ।</span></p>",
                    options_en: [" <p> Court— Tennis </span></p>", " <p> Gymnasium — Exercise </span></p>", 
                                " <p> Wrestling — Arena </span></p>", " <p> Pitch — Cricket </span></p>"],
                    options_hi: [" <p> कोर्ट- टेनिस  </span></p>", " <p> व्यायामशाला - व्यायाम</span></p>",
                                " <p> कुश्ती - अखाड़ा</span></p>", " <p>  पिच - क्रिकेट</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">32.(c)</span></p> <p><span style=\"font-family:Roboto\">The names of the playing area and sports are on the left and right side respectively except option 3.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">32.(c)</span></p> <p><span style=\"font-family:Palanquin Dark\">खेल क्षेत्र और खेल के नाम विकल्प 3 को छोड़कर क्रमशः बायीं और दायीं ओर हैं।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "6",
                    question_en: "<p>33. <span style=\"font-family: Roboto;\">A piece of paper is folded and punched as shown below in the question figures. From the given answer figures, indicate how it will appear when opened? </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image55.png\" width=\"180\" height=\"54\"></p>\n",
                    question_hi: "<p>33.<span style=\"font-family: Palanquin;\"> &#2325;&#2366;&#2327;&#2332; &#2325;&#2375; &#2319;&#2325; &#2335;&#2369;&#2325;&#2337;&#2364;&#2375; &#2325;&#2379; &#2350;&#2379;&#2337;&#2364;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2313;&#2360;&#2350;&#2375;&#2306; &#2331;&#2375;&#2342; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2376;&#2360;&#2366; &#2325;&#2367; &#2344;&#2368;&#2330;&#2375; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2310;&#2325;&#2371;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2342;&#2367;&#2326;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2342;&#2368; &#2327;&#2312; &#2313;&#2340;&#2381;&#2340;&#2352; &#2310;&#2325;&#2371;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2348;&#2340;&#2366;&#2311;&#2319; &#2325;&#2367; &#2326;&#2379;&#2354;&#2375; &#2332;&#2366;&#2344;&#2375; &#2325;&#2375; &#2348;&#2366;&#2342; &#2357;&#2361; &#2325;&#2367;&#2360; &#2313;&#2340;&#2381;&#2340;&#2352; &#2310;&#2325;&#2371;&#2340;&#2367; &#2325;&#2375; &#2360;&#2350;&#2366;&#2344; &#2342;&#2367;&#2326;&#2366;&#2312; &#2342;&#2375;&#2327;&#2368;? </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image55.png\" width=\"180\" height=\"54\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image46.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image40.png\" width=\"70\" height=\"70\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image26.png\" width=\"71\" height=\"71\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image36.png\" width=\"71\" height=\"71\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image46.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image40.png\" width=\"70\" height=\"70\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image26.png\" width=\"71\" height=\"71\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image36.png\" width=\"71\" height=\"71\"></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">33.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image46.png\" width=\"78\" height=\"78\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">33.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image46.png\" width=\"78\" height=\"78\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "6",
                    question_en: "<p>34.<span style=\"font-family: Roboto;\"> In the following question some statements are given, followed by some conclusions based on those statements. Taking the given statements to be true even if they seem to be at variance from commonly known facts. Read all the conclusions and then decide which of the given conclusion logically follows the given statements. </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\">Statements:</span></strong></p>\r\n<p><span style=\"font-family: Roboto;\"> I. All S are R.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> II. No R is T. </span></p>\r\n<p><strong><span style=\"font-family: Roboto;\">Conclusions:</span></strong></p>\r\n<p><span style=\"font-family: Roboto;\"> I. No T is R. </span></p>\r\n<p><span style=\"font-family: Roboto;\"> II. No S is T .</span></p>\r\n<p><span style=\"font-family: Roboto;\"> III. No R is S. </span></p>\n",
                    question_hi: "<p>34. <span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2325;&#2369;&#2331; &#2325;&#2341;&#2344; &#2342;&#2367;&#2319; &#2327;&#2319; &#2361;&#2376;&#2306; &#2324;&#2352; &#2313;&#2344; &#2325;&#2341;&#2344;&#2379;&#2306; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; &#2325;&#2369;&#2331; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; &#2342;&#2367;&#2319; &#2327;&#2319; &#2361;&#2376;&#2306;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; &#2325;&#2341;&#2344;&#2379;&#2306; &#2325;&#2379; &#2360;&#2340;&#2381;&#2351; &#2350;&#2366;&#2344;&#2344;&#2366; &#8203;&#8203;&#2349;&#2354;&#2375; &#2361;&#2368; &#2357;&#2375; &#2360;&#2352;&#2381;&#2357;&#2332;&#2381;&#2334;&#2366;&#2340; &#2340;&#2341;&#2381;&#2351;&#2379;&#2306; &#2360;&#2375; &#2349;&#2367;&#2344;&#2381;&#2344; &#2346;&#2381;&#2352;&#2340;&#2368;&#2340; &#2361;&#2379;&#2340;&#2375; &#2361;&#2379;&#2306;&#2404; &#2360;&#2349;&#2368; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2379; &#2346;&#2338;&#2364;&#2375;&#2306; &#2324;&#2352; &#2347;&#2367;&#2352; &#2340;&#2351; &#2325;&#2352;&#2375;&#2306; &#2325;&#2367; &#2342;&#2367;&#2319; &#2327;&#2319; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; &#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325; &#2352;&#2370;&#2346; &#2360;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2325;&#2341;&#2344;&#2379;&#2306; &#2325;&#2366; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Palanquin;\">&#2325;&#2341;&#2344;:</span></strong></p>\r\n<p><span style=\"font-family: Palanquin;\"> I. &#2360;&#2349;&#2368; S, R &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin;\"> II. &#2325;&#2379;&#2312; R, T &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;:</span></strong></p>\r\n<p><span style=\"font-family: Palanquin;\"> I. &#2325;&#2379;&#2312; T, R &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin;\"> II. &#2325;&#2379;&#2312; S, T &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin;\"> III. &#2325;&#2379;&#2312; R, S &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>All conclusion follows</p>\n", "<p>Both conclusions l and ll follows</p>\n", 
                                "<p>Both conclusions ll and lll follows</p>\n", "<p>Both conclusions l and lll follows</p>\n"],
                    options_hi: ["<p>&#2360;&#2349;&#2368; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</p>\n", "<p>&#2342;&#2379;&#2344;&#2379;&#2306; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; l &#2324;&#2352; ll &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</p>\n",
                                "<p>&#2342;&#2379;&#2344;&#2379;&#2306; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; ll &#2324;&#2352; llI &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</p>\n", "<p>&#2342;&#2379;&#2344;&#2379;&#2306; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; I &#2324;&#2352; III &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">34.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image7.png\" width=\"161\" height=\"61\"></p>\r\n<p><span style=\"font-family: Roboto;\">Both the conclusion 1 and 2 follows.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">34.(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image7.png\" width=\"161\" height=\"61\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; 1 &#2324;&#2352; 2 &#2342;&#2379;&#2344;&#2379;&#2306; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "6",
                    question_en: "<p>35. <span style=\"font-family: Roboto;\">Select the option figure in which the given figure is embedded. (rotation is NOT allowed) </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image24.png\"></p>\n",
                    question_hi: "<p>35. <span style=\"font-family: Palanquin;\">&#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2310;&#2325;&#2371;&#2340;&#2367; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2342;&#2368; &#2327;&#2312; &#2310;&#2325;&#2371;&#2340;&#2367; &#2344;&#2367;&#2361;&#2367;&#2340; &#2361;&#2376;&#2404; (&#2328;&#2370;&#2352;&#2381;&#2339;&#2344; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image24.png\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image31.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image14.png\" width=\"69\" height=\"69\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image53.png\" width=\"71\" height=\"71\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image37.png\" width=\"69\" height=\"69\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image31.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image14.png\" width=\"69\" height=\"69\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image53.png\" width=\"71\" height=\"71\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image37.png\" width=\"69\" height=\"69\"></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">35.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image1.png\" width=\"90\" height=\"90\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">35.(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image1.png\" width=\"90\" height=\"90\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "6",
                    question_en: "<p>36. <span style=\"font-family: Roboto;\">A piece of paper is folded and punched as shown below in the question figures, From the given answer figures, indicate how it will appear when opened?</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image30.png\" width=\"130\" height=\"61\"></p>\n",
                    question_hi: "<p>36. <span style=\"font-family: Palanquin;\">&#2325;&#2366;&#2327;&#2332; &#2325;&#2375; &#2319;&#2325; &#2335;&#2369;&#2325;&#2337;&#2364;&#2375; &#2325;&#2379; &#2350;&#2379;&#2337;&#2364;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2313;&#2360;&#2350;&#2375;&#2306; &#2331;&#2375;&#2342; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2376;&#2360;&#2366; &#2325;&#2367; &#2344;&#2368;&#2330;&#2375; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2310;&#2325;&#2371;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2342;&#2367;&#2326;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2342;&#2368; &#2327;&#2312; &#2313;&#2340;&#2381;&#2340;&#2352; &#2310;&#2325;&#2371;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2348;&#2340;&#2366;&#2311;&#2319; &#2325;&#2367; &#2326;&#2379;&#2354;&#2375; &#2332;&#2366;&#2344;&#2375; &#2325;&#2375; &#2348;&#2366;&#2342; &#2357;&#2361; &#2325;&#2367;&#2360; &#2313;&#2340;&#2381;&#2340;&#2352; &#2310;&#2325;&#2371;&#2340;&#2367; &#2325;&#2375; &#2360;&#2350;&#2366;&#2344; &#2342;&#2367;&#2326;&#2366;&#2312; &#2342;&#2375;&#2327;&#2368;?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image30.png\" width=\"130\" height=\"61\"></p>\n",
                    options_en: ["<p>&nbsp;</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image17.png\" width=\"70\" height=\"70\"></p>\n", "<p>&nbsp;</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image11.png\" width=\"71\" height=\"71\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image9.png\" width=\"71\" height=\"71\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image48.png\" width=\"70\" height=\"70\"></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image17.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image11.png\" width=\"71\" height=\"71\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image9.png\" width=\"71\" height=\"71\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image48.png\" width=\"70\" height=\"70\"></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">36.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image9.png\" width=\"95\" height=\"95\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">36.(c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image9.png\" width=\"95\" height=\"95\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "6",
                    question_en: "<p>37. <span style=\"font-family: Roboto;\">How many quadrilaterals are there in the given figure? </span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image23.png\" width=\"82\" height=\"130\"></p>\n",
                    question_hi: "<p>37.<span style=\"font-family: Palanquin;\"> &#2342;&#2368; &#2327;&#2312; &#2310;&#2325;&#2371;&#2340;&#2367; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2375; &#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2361;&#2376;&#2306;?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image23.png\" width=\"82\" height=\"130\"></p>\n",
                    options_en: ["<p>25</p>\n", "<p>24</p>\n", 
                                "<p>23</p>\n", "<p>22</p>\n"],
                    options_hi: ["<p>25</p>\n", "<p>24</p>\n",
                                "<p>23</p>\n", "<p>22</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">37.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image52.png\" width=\"111\" height=\"155\"></p>\r\n<p><span style=\"font-family: Roboto;\">Total number of quadrilaterals : - ABQD, BCEQ , ADEC , DQGF, QEHG , DFHE, </span><span style=\"font-family: Roboto;\">FRJI, RJKH, FIKH, IJML , JMNP ,PNOK, ILNP, </span><span style=\"font-family: Roboto;\">JMOK , ILOK , AFGB , BGHC, FLMR, RMOH, </span><span style=\"font-family: Roboto;\">AFHC, AIKC, ALOC, DIKE , DLOE , FLOH</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">37.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image52.png\" width=\"111\" height=\"155\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; :- ABQD, BCEQ , ADEC , DQGF, QEHG , DFHE, </span><span style=\"font-family: Palanquin Dark;\">FRJI, RJKH, FIKH, IJML, JMNP ,PNOK, ILNP, </span><span style=\"font-family: Palanquin Dark;\">JMOK , ILOK , AFGB , BGHC, FLMR, RMOH, </span><span style=\"font-family: Palanquin Dark;\">AFHC, AIKC, ALOC, DIKE , DLOE , FLOH</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "6",
                    question_en: "<p>38. <span style=\"font-family: Roboto;\">Select the option in which the numbers shares the same relationship in set as that shared by the numbers in the given set. (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g.13 &mdash; Operations on 13 such as adding /subtracting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</span></p>\r\n<p><span style=\"font-weight: 400;\">(6, 36, 108)</span></p>\r\n<p><span style=\"font-weight: 400;\">(7, 49, 147)</span></p>\n",
                    question_hi: "<p>38. <span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2330;&#2366;&#2352; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2351;&#2369;&#2327;&#2381;&#2350; &#2342;&#2367;&#2319; &#2327;&#2319; &#2361;&#2376;&#2306;&#2404; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2351;&#2369;&#2327;&#2381;&#2350; &#2350;&#2375;&#2306; (-) &#2325;&#2375; &#2348;&#2366;&#2351;&#2368;&#2306; &#2323;&#2352; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; (-) &#2325;&#2375; &#2342;&#2366;&#2351;&#2368; &#2323;&#2352; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2360;&#2375; &#2325;&#2367;&#2360;&#2368; &#2340;&#2352;&#2381;&#2325; / &#2344;&#2367;&#2351;&#2350; / &#2360;&#2306;&#2348;&#2306;&#2343; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; / &#2344;&#2367;&#2351;&#2350; / &#2360;&#2306;&#2348;&#2306;&#2343; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; &#2340;&#2368;&#2344; &#2351;&#2369;&#2327;&#2381;&#2350; &#2360;&#2350;&#2366;&#2344; &#2361;&#2376;&#2306;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2309;&#2354;&#2327; &#2351;&#2369;&#2327;&#2381;&#2350; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319;&#2404; (&#2343;&#2381;&#2351;&#2366;&#2344; &#2342;&#2375;&#2306;: &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2379; &#2313;&#2344;&#2325;&#2375; &#2328;&#2335;&#2325; &#2309;&#2306;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2354;&#2327; &#2309;&#2354;&#2327; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366;, &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2368; &#2332;&#2366;&#2344;&#2368; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404; &#2313;&#2342;&#2366;. 13 - 13 &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2332;&#2376;&#2360;&#2375; 13 &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2366; / &#2328;&#2335;&#2366;&#2344;&#2366; / &#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2344;&#2366; &#2310;&#2342;&#2367; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; 13 &#2325;&#2379; 1 &#2319;&#2357;&#2306; 3 &#2350;&#2375;&#2306; &#2309;&#2354;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2324;&#2352; &#2347;&#2367;&#2352; 1 &#2319;&#2357;&#2306; 3 &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;)</span></p>\r\n<p><span style=\"font-weight: 400;\">(6, 36, 108)</span></p>\r\n<p><span style=\"font-weight: 400;\">(7, 49, 147)</span></p>\n",
                    options_en: ["<p>(8, 64, 96)</p>\n", "<p>(8, 32, 64)</p>\n", 
                                "<p>(4, 16, 48)</p>\n", "<p>(4, 16, 64)</p>\n"],
                    options_hi: ["<p>(8, 64, 96)</p>\n", "<p>(8, 32, 64)</p>\n",
                                "<p>(4, 16, 48)</p>\n", "<p>(4, 16, 64)</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">38.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Logic : - (First number)</span><span style=\"font-family: Roboto;\">&sup2;</span><span style=\"font-family: Roboto;\"> = Second number , (Second number) &times; 3 = Third number</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (6 ,36, 108) &rarr; (6)</span><span style=\"font-family: Arial Unicode MS;\"><span style=\"font-family: Roboto;\">&sup2;</span></span><span style=\"font-family: Arial Unicode MS;\"> = 36 , (36) x 3 = 108 </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (7 ,49, 147) &rarr; (7)</span><span style=\"font-family: Arial Unicode MS;\"><span style=\"font-family: Roboto;\">&sup2;</span></span><span style=\"font-family: Arial Unicode MS;\"> = 49 , (49) x 3 = 147</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Similarly,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (4 , 16, 48) &rarr; (4)</span><span style=\"font-family: Arial Unicode MS;\"><span style=\"font-family: Roboto;\">&sup2;</span></span><span style=\"font-family: Arial Unicode MS;\"> = 16 , (16) x 3 = 48</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">38.(c)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2352;&#2381;&#2325;: - (&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;)</span><span style=\"font-family: Palanquin Dark;\"><span style=\"font-family: Arial Unicode MS;\"><span style=\"font-family: Roboto;\">&sup2;</span></span></span><span style=\"font-family: Palanquin Dark;\"> = &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;, (&#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;) <span style=\"font-family: Roboto;\">&times;</span> 3 = &#2340;&#2368;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (6 ,36, 108) &rarr; (6)</span><span style=\"font-family: Arial Unicode MS;\"><span style=\"font-family: Roboto;\">&sup2;</span></span><span style=\"font-family: Arial Unicode MS;\"> = 36 , (36) x 3 = 108 </span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (7 ,49, 147) &rarr; (7)</span><span style=\"font-family: Arial Unicode MS;\"><span style=\"font-family: Roboto;\">&sup2;</span></span><span style=\"font-family: Arial Unicode MS;\"> = 49 , (49) x 3 = 147</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr; (4 , 16, 48) &rarr; (4)</span><span style=\"font-family: Arial Unicode MS;\"><span style=\"font-family: Roboto;\">&sup2;</span></span><span style=\"font-family: Arial Unicode MS;\"> = 16 , (16) x 3 = 48</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "6",
                    question_en: " <p>39.</span><span style=\"font-family:Roboto\"> Words given on the left side of (::) are related with each other by some Logic/Rule Relation. Select the missing word/word pair on the right side of (::) from the given alternatives based on the same Logic/Rule/Relation </span></p> <p><span style=\"font-family:Roboto\">Arena : Wrestling :: ?  </span></p>",
                    question_hi: " <p>39. </span><span style=\"font-family:Palanquin\">(::) के बायीं ओर दिये गये शब्द आपस में किसी तर्क/नियम सम्बन्ध द्वारा सम्बन्धित होते हैं। समान तर्क/नियम/संबंध के आधार पर दिए गए विकल्पों में से (::) के दायीं ओर लुप्त शब्द/शब्द युग्म को चुनिए।</span></p> <p><span style=\"font-family:Palanquin\">अखाड़ा : कुश्ती :: ?</span></p>",
                    options_en: [" <p>  Pitch : Exercise </span><span style=\"font-family:Roboto\">  </span></p>", " <p>  Athletics : Stadium  </span></p>", 
                                " <p>  Tennis : Court </span></p>", " <p>  Rink : Skating </span></p>"],
                    options_hi: [" <p> पिच : व्यायाम</span></p>", " <p>  एथलेटिक्स : स्टेडियम</span></p>",
                                " <p>  टेनिस : कोर्ट </span></p>", " <p>  रिंक : स्केटिंग</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">39.(d)</span></p> <p><span style=\"font-family:Roboto\">As wrestling takes place in the arena similarly, skating takes place in the Rink.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">39.(d)</span></p> <p><span style=\"font-family:Palanquin Dark\">जैसे कुश्ती अखाड़े में होती है, वैसे ही स्केटिंग रिंक में होती है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "6",
                    question_en: "<p>40. <span style=\"font-family: Roboto;\"> In the following question, select the missing number from the given series. </span></p>\r\n<p><span style=\"font-family: Roboto;\">729, 512, 343, 216, ? </span></p>\n",
                    question_hi: "<p>40. <span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306;, &#2342;&#2368; &#2327;&#2312; &#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366; &#2360;&#2375; &#2354;&#2369;&#2346;&#2381;&#2340; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319;</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">729, 512, 343, 216, ? </span></p>\n",
                    options_en: ["<p>36</p>\n", "<p>125</p>\n", 
                                "<p>25</p>\n", "<p>64</p>\n"],
                    options_hi: ["<p>36</p>\n", "<p>125</p>\n",
                                "<p>25</p>\n", "<p>64</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">40.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image18.png\" width=\"245\" height=\"58\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">40.(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image18.png\" width=\"245\" height=\"58\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "6",
                    question_en: "<p>41. <span style=\"font-family: Roboto;\">In the following question, select the odd letter/letters from the given alternatives. </span></p>\n",
                    question_hi: "<p>41. <span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2309;&#2354;&#2327; &#2309;&#2325;&#2381;&#2359;&#2352; / &#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2404; </span></p>\n",
                    options_en: ["<p>IHEW</p>\n", "<p>ONKP</p>\n", 
                                "<p>EDAZ</p>\n", "<p>YXUF</p>\n"],
                    options_hi: ["<p>IHEW</p>\n", "<p>ONKP</p>\n",
                                "<p>EDAZ</p>\n", "<p>YXUF</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">41.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image39.png\" width=\"99\" height=\"50\">&nbsp; &nbsp; &nbsp;<span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image35.png\" width=\"101\" height=\"53\">&nbsp; &nbsp; &nbsp;<span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image38.png\" width=\"101\" height=\"52\"></p>\r\n<p><span style=\"font-family: Roboto;\">But,</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image42.png\" width=\"98\" height=\"51\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">41.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><span style=\"font-family: Roboto;\"><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image39.png\" width=\"99\" height=\"50\">&nbsp; &nbsp; &nbsp; <img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image35.png\" width=\"101\" height=\"53\"></span><span style=\"font-family: Roboto;\">&nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image38.png\" width=\"101\" height=\"52\"></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2354;&#2375;&#2325;&#2367;&#2344;,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image42.png\" width=\"98\" height=\"51\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "6",
                    question_en: "<p>42.<span style=\"font-family: Roboto;\"> In a certain code language, </span><span style=\"font-family: Roboto;\">&lsquo;TALE&rsquo;</span><span style=\"font-family: Roboto;\"> is coded as </span><span style=\"font-family: Roboto;\">&lsquo;7234&rsquo;,</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">&lsquo;TOLK&rsquo; </span><span style=\"font-family: Roboto;\">is coded as </span><span style=\"font-family: Roboto;\">&lsquo;9172&rsquo;</span><span style=\"font-family: Roboto;\">, </span><span style=\"font-family: Roboto;\">&lsquo;KITE&rsquo;</span><span style=\"font-family: Roboto;\"> is coded as </span><span style=\"font-family: Roboto;\">&lsquo;1283&rsquo;</span><span style=\"font-family: Roboto;\">, </span><span style=\"font-family: Roboto;\">&lsquo;SEAN&rsquo;</span><span style=\"font-family: Roboto;\"> is coded as </span><span style=\"font-family: Roboto;\">&lsquo;3064&rsquo;</span><span style=\"font-family: Roboto;\">. What is the code for </span><span style=\"font-family: Roboto;\">&lsquo;SON&rsquo;</span><span style=\"font-family: Roboto;\"> in that code language? </span></p>\n",
                    question_hi: "<p>42.<span style=\"font-family: Palanquin;\"> &#2319;&#2325; &#2326;&#2366;&#2360; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306;, \'</span><span style=\"font-family: Roboto;\">TALE\'</span><span style=\"font-family: Palanquin;\"> &#2325;&#2379; \'</span><span style=\"font-family: Roboto;\">7234</span><span style=\"font-family: Palanquin;\">\' &#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, </span><span style=\"font-family: Roboto;\">TOLK\'</span><span style=\"font-family: Palanquin;\"> &#2325;&#2379;</span><span style=\"font-family: Roboto;\"> \'9172</span><span style=\"font-family: Palanquin;\">\' &#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;,</span><span style=\"font-family: Roboto;\"> \'KITE</span><span style=\"font-family: Palanquin;\">\' &#2325;&#2379; </span><span style=\"font-family: Roboto;\">\'1283\' </span><span style=\"font-family: Palanquin;\">&#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, </span><span style=\"font-family: Roboto;\">\'SEAN </span><span style=\"font-family: Palanquin;\">&#2325;&#2379; </span><span style=\"font-family: Roboto;\">3064\' </span><span style=\"font-family: Palanquin;\">&#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; </span><span style=\"font-family: Roboto;\">\'SON</span><span style=\"font-family: Palanquin;\">\' &#2325;&#2379; &#2325;&#2376;&#2360;&#2375; &#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>906</p>\n", "<p>916</p>\n", 
                                "<p>896</p>\n", "<p>914</p>\n"],
                    options_hi: ["<p>906</p>\n", "<p>916</p>\n",
                                "<p>896</p>\n", "<p>914</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">42.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image15.png\" width=\"214\" height=\"137\"></p>\r\n<p><span style=\"font-family: Roboto;\">From the second line we get the code of &lsquo;O&rsquo; = 9</span></p>\r\n<p><span style=\"font-family: Roboto;\">From the last line we get the code of S and N = 0/ 6</span></p>\r\n<p><span style=\"font-family: Roboto;\">By going through the option , only option (a) satisfies the condition .</span></p>\r\n<p><span style=\"font-family: Roboto;\"> The code of SON = 906.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">42.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image15.png\" width=\"214\" height=\"137\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2370;&#2360;&#2352;&#2368; &#2354;&#2366;&#2311;&#2344; &#2360;&#2375; &#2361;&#2350;&#2375;&#2306; \'O\' = 9 &#2325;&#2366; &#2325;&#2379;&#2337; &#2350;&#2367;&#2354;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2306;&#2340;&#2367;&#2350; &#2346;&#2306;&#2325;&#2381;&#2340;&#2367; &#2360;&#2375; &#2361;&#2350;&#2375;&#2306; S &#2324;&#2352; N = 0/6 &#2325;&#2366; &#2325;&#2379;&#2337; &#2350;&#2367;&#2354;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2332;&#2366;&#2344;&#2375; &#2346;&#2352;, &#2325;&#2375;&#2357;&#2354; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (a) &#2358;&#2352;&#2381;&#2340; &#2325;&#2379; &#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SON &#2325;&#2366; &#2325;&#2379;&#2337; = 906</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "6",
                    question_en: "<p>43.<span style=\"font-family: Roboto;\"> select the figure from the given options that can replace the question mark (?) </span><span style=\"font-family: Roboto;\">in the following series.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image3.png\" width=\"303\" height=\"62\"></p>\n",
                    question_hi: "<p>43. <span style=\"font-family: Palanquin;\">&#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2313;&#2360; &#2310;&#2325;&#2371;&#2340;&#2367; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2379; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2330;&#2367;&#2361;&#2381;&#2344; (?) &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2310; &#2360;&#2325;&#2340;&#2368; </span><span style=\"font-family: Palanquin;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image3.png\" width=\"303\" height=\"62\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image4.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image33.png\" width=\"71\" height=\"71\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image50.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image34.png\" width=\"68\" height=\"68\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image4.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image33.png\" width=\"71\" height=\"71\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image50.png\" width=\"70\" height=\"70\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image34.png\" width=\"68\" height=\"68\"></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">43.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image34.png\" width=\"71\" height=\"71\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">43.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image34.png\" width=\"71\" height=\"71\"></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "6",
                    question_en: " <p>44. </span><span style=\"font-family:Roboto\">Select the option that is related to the third number in the same way as the first number is related to the second number and the fifth number is related to the sixth number.</span></p> <p><span style=\"font-family:Roboto\"> 391 : 626 : : 426 : ? : : 173 : 408 </span></p>",
                    question_hi: " <p>44.</span><span style=\"font-family:Palanquin\"> उस विकल्प को चुनिए जो तीसरी संख्या से ठीक उसी तरह संबंधित है जिस तरह पहली संख्या दूसरी संख्या से संबंधित है और पाँचवीं संख्या छठी संख्या से संबंधित है।</span></p> <p><span style=\"font-family:Roboto\"> 391 : 626 : : 426 : ? : : 173 : 408 </span></p>",
                    options_en: [" <p> 711</span></p>", " <p> 661</span></p>", 
                                " <p> 701</span></p>", " <p> 681</span></p>"],
                    options_hi: [" <p> 711</span></p>", " <p> 661</span></p>",
                                " <p> 701</span></p>", " <p> 681</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">44.(b)</span></p> <p><span style=\"font-family:Roboto\">Logic : - a : a + 235</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ 391: 391 + 235→ 391 : 626</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ 173 : 173 + 235 = 173 : 408</span></p> <p><span style=\"font-family:Arial Unicode MS\">Similarly,</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ 426 : 426 + 235 = 426 : 661</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">44.(b)</span></p> <p><span style=\"font-family:Palanquin Dark\">तर्क : - a : a + 235</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ 391: 391 + 235→ 391 : 626</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ 173 : 173 + 235 = 173 : 408</span></p> <p><span style=\"font-family:Palanquin Dark\">इसी प्रकार,</span></p> <p><span style=\"font-family:Arial Unicode MS\">⇒ 426 : 426 + 235 = 426 : 661</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "6",
                    question_en: "<p>45. <span style=\"font-family: Roboto;\">Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> p _ nj _ c _ r _ jlcp _ njl _ pr_ _ lc </span></p>\n",
                    question_hi: "<p>45. <span style=\"font-family: Palanquin;\">&#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2375; &#2313;&#2360; &#2360;&#2306;&#2351;&#2379;&#2332;&#2344; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2367;&#2360;&#2375; &#2342;&#2368; &#2327;&#2312; &#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366; &#2325;&#2375; &#2352;&#2367;&#2325;&#2381;&#2340; &#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2325;&#2381;&#2352;&#2350;&#2367;&#2325; &#2352;&#2370;&#2346; &#2360;&#2375; &#2352;&#2326;&#2344;&#2375; &#2346;&#2352; &#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366; &#2346;&#2370;&#2352;&#2368; &#2361;&#2379; &#2332;&#2366;&#2319;&#2327;&#2368;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\"> p _ nj _ c _ r _ jlcp _ njl _ pr_ _ lc </span></p>\n",
                    options_en: ["<p>rlpnrcnj</p>\n", "<p>rppnrcnj</p>\n", 
                                "<p>rllnrcnj</p>\n", "<p>rlprncnj</p>\n"],
                    options_hi: ["<p>rlpnrcnj</p>\n", "<p>rppnrcnj</p>\n",
                                "<p>rllnrcnj</p>\n", "<p>rlprncnj</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">45.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">p </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">r</span></span><span style=\"font-family: Roboto;\"> n j </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">l</span></span><span style=\"font-family: Roboto;\"> c / </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">p</span></span><span style=\"font-family: Roboto;\"> r </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">n</span></span><span style=\"font-family: Roboto;\"> j l c / p</span><span style=\"font-family: Roboto;\"> <span style=\"text-decoration: underline;\">r</span></span><span style=\"font-family: Roboto;\"> n j l </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">c</span></span><span style=\"font-family: Roboto;\"> / p r </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">n j</span></span><span style=\"font-family: Roboto;\"> l c</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">45.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">p </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">r</span></span><span style=\"font-family: Roboto;\"> n j </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">l</span></span><span style=\"font-family: Roboto;\"> c / </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">p</span></span><span style=\"font-family: Roboto;\"> r </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">n</span></span><span style=\"font-family: Roboto;\"> j l c / p</span><span style=\"font-family: Roboto;\"> <span style=\"text-decoration: underline;\">r</span></span><span style=\"font-family: Roboto;\"> n j l </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">c</span></span><span style=\"font-family: Roboto;\"> / p r </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Roboto;\">n j</span></span><span style=\"font-family: Roboto;\"> l c</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "6",
                    question_en: "<p>46.<span style=\"font-family: Roboto;\"> select the option figure in which the given figure is embedded. (rotation is NOT allowed) </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image19.png\" width=\"80\" height=\"73\"></p>\n",
                    question_hi: "<p>46. <span style=\"font-family: Palanquin;\">&#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2310;&#2325;&#2371;&#2340;&#2367; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2342;&#2368; &#2327;&#2312; &#2310;&#2325;&#2371;&#2340;&#2367; &#2344;&#2367;&#2361;&#2367;&#2340; &#2361;&#2376;&#2404; (&#2328;&#2370;&#2352;&#2381;&#2339;&#2344; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image19.png\" width=\"80\" height=\"73\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image25.png\" width=\"75\" height=\"69\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image28.png\" width=\"76\" height=\"71\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image21.png\" width=\"75\" height=\"69\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image43.png\" width=\"75\" height=\"68\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image25.png\" width=\"75\" height=\"69\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image28.png\" width=\"76\" height=\"71\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image21.png\" width=\"75\" height=\"69\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image43.png\" width=\"75\" height=\"68\"></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">46.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image32.png\" width=\"100\" height=\"91\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">46.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image32.png\" width=\"100\" height=\"91\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "6",
                    question_en: "<p>47. <span style=\"font-family: Roboto;\">Pointing towards a man, Parkhi said, &ldquo;He is the brother of my son&rsquo;s mother&rdquo;. How that man is related to Parkhi&rsquo;s husband? </span></p>\n",
                    question_hi: "<p>47. <span style=\"font-family: Palanquin;\">&#2319;&#2325; &#2310;&#2342;&#2350;&#2368; &#2325;&#2368; &#2323;&#2352; &#2311;&#2358;&#2366;&#2352;&#2366; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319;, &#2346;&#2366;&#2352;&#2326;&#2368; &#2344;&#2375; &#2325;&#2361;&#2366;, \"&#2357;&#2361; &#2350;&#2375;&#2352;&#2375; &#2348;&#2375;&#2335;&#2375; &#2325;&#2368; &#2350;&#2366;&#2305; &#2325;&#2366; &#2349;&#2366;&#2312; &#2361;&#2376;\"&#2404; &#2357;&#2361; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2346;&#2366;&#2352;&#2326;&#2368; &#2325;&#2375; &#2346;&#2340;&#2367; &#2360;&#2375; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Son</p>\n", "<p>Maternal Uncle</p>\n", 
                                "<p>Nephew</p>\n", "<p>Brother in law</p>\n"],
                    options_hi: ["<p>&#2348;&#2375;&#2335;&#2366;</p>\n", "<p>&#2350;&#2366;&#2350;&#2366;</p>\n",
                                "<p>&#2349;&#2366;&#2306;&#2332;&#2366;</p>\n", "<p>&#2360;&#2366;&#2354;&#2366;</p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">47.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image20.png\" width=\"197\" height=\"105\"></p>\r\n<p><span style=\"font-family: Roboto;\">The man will be the brother-in-law of Parkhi&rsquo;s husband.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">47.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image51.png\" width=\"195\" height=\"104\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2357;&#2361; &#2358;&#2326;&#2381;&#2360; &#2346;&#2366;&#2352;&#2326;&#2368; &#2325;&#2375; &#2346;&#2340;&#2367; &#2325;&#2366; &#2360;&#2366;&#2354;&#2366; &#2361;&#2379;&#2327;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "6",
                    question_en: "<p>48.<span style=\"font-family: Roboto;\"> After arranging the given words according to dictionary order, which word will come at &lsquo;Fifth&rsquo; position? </span></p>\r\n<p><span style=\"font-family: Roboto;\">1. Superstition&nbsp; &nbsp;</span><span style=\"font-family: Roboto;\">2. Superintend&nbsp; &nbsp; </span><span style=\"font-family: Roboto;\">3. Supervene&nbsp; &nbsp; </span><span style=\"font-family: Roboto;\">4. Supervise&nbsp; &nbsp; </span><span style=\"font-family: Roboto;\">5. Superpose</span></p>\n",
                    question_hi: "<p>48.<span style=\"font-family: Palanquin;\"> &#2342;&#2367;&#2319; &#2327;&#2319; &#2358;&#2348;&#2381;&#2342;&#2379;&#2306; &#2325;&#2379; &#2358;&#2348;&#2381;&#2342;&#2325;&#2379;&#2358; &#2325;&#2381;&#2352;&#2350; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;, &#2325;&#2380;&#2344; &#2360;&#2366; &#2358;&#2348;&#2381;&#2342; \'&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;\' &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2310;&#2319;&#2327;&#2366;?</span></p>\r\n<p><span style=\"font-family: Roboto;\">1. Superstition&nbsp; &nbsp;</span><span style=\"font-family: Roboto;\">2. Superintend&nbsp; &nbsp; </span><span style=\"font-family: Roboto;\">3. Supervene&nbsp; &nbsp; </span><span style=\"font-family: Roboto;\">4. Supervise&nbsp; &nbsp; </span><span style=\"font-family: Roboto;\">5. Superpose </span></p>\n",
                    options_en: ["<p>Superstition</p>\n", "<p>Supervene</p>\n", 
                                "<p>Supervise</p>\n", "<p>Superintend</p>\n"],
                    options_hi: ["<p>Superstition</p>\n", "<p>Supervene</p>\n",
                                "<p>Supervise</p>\n", "<p>Superintend</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">48.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\"><span style=\"font-weight: 400;\">(2) Superintend &rarr; (5) Superpose &rarr; (1) Superstition &rarr; (3) Supervene &rarr; (4) Supervise</span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">48.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\"><span style=\"font-weight: 400;\">(2) Superintend &rarr; (5) Superpose &rarr; (1) Superstition &rarr; (3) Supervene &rarr; (4) Supervise</span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "6",
                    question_en: " <p>49</span><span style=\"font-family:Roboto\">. Select the option that is related to the third word on the same basis as the second word is related to the first word. </span></p> <p><span style=\"font-family:Roboto\">Tokyo : Japan :: Paris : ? </span></p>",
                    question_hi: " <p>49</span><span style=\"font-family:Palanquin\">. उस विकल्प का चयन करें जो तीसरे शब्द से उसी आधार पर संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है।</span></p> <p><span style=\"font-family:Palanquin\">टोक्यो : जापान :: पेरिस : ?</span></p>",
                    options_en: [" <p>  Ireland </span></p>", " <p>  France </span></p>", 
                                " <p>  italy </span></p>", " <p> Switzerland </span></p>"],
                    options_hi: [" <p>  आयरलैंड</span></p>", " <p>  फ्रांस</span></p>",
                                " <p>  इटली</span></p>", " <p>  स्विट्जरलैंड</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">49.(b)</span></p> <p><span style=\"font-family:Roboto\">As Tokyo is the capital of Japan, similarly Paris is the capital of France.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">49.(b)</span></p> <p><span style=\"font-family:Palanquin Dark\">जैसे टोक्यो, जापान की राजधानी है, वैसे ही पेरिस, फ्रांस की राजधानी है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: " <p>50.</span><span style=\"font-family:Roboto\"> How many tablet are only car? </span></p> <p><span style=\"font-family:Roboto\">  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image29.png\"/></p>",
                    question_hi: " <p>50. कितने  टैबलेट  केवल कार हैं?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image27.png\"/><span style=\"font-family:Roboto\">    </span></p>",
                    options_en: [" <p>  4</span></p>", " <p>  12</span></p>", 
                                " <p>  8</span></p>", " <p> 17</span></p>"],
                    options_hi: [" <p>  4</span></p>", " <p> 12 </span></p>",
                                " <p>  8</span></p>", " <p> 17</span></p>"],
                    solution_en: " <p><span style=\"font-family:Roboto\">50.(c)</span></p> <p><span style=\"font-family:Roboto\">The correct answer is 8.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">50.(c)</span></p> <p><span style=\"font-family:Palanquin Dark\">सही उत्तर 8 है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. <span style=\"font-family: Roboto;\">Rajiv sells a pen at a profit of 5 percent. If he sells the pen for Rs. 36 more, then he earns a profit of 14 percent. What is the cost price of the pen? </span></p>\n",
                    question_hi: "<p>51. <span style=\"font-family: Palanquin;\">&#2352;&#2366;&#2332;&#2368;&#2348; &#2319;&#2325; &#2346;&#2375;&#2344; &#2325;&#2379; 5 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2375; &#2354;&#2366;&#2349; &#2346;&#2352; &#2348;&#2375;&#2330;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2357;&#2361; &#2346;&#2375;&#2344; &#2325;&#2379; Rs. 36 &#2309;&#2343;&#2367;&#2325; &#2350;&#2375;&#2306; &#2348;&#2375;&#2330;&#2340;&#2366; &#2361;&#2376;, &#2340;&#2379; &#2313;&#2360;&#2375; 14 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2366; &#2354;&#2366;&#2349; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; &#2346;&#2375;&#2344; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Rs. 600</p>\n", "<p>Rs. 300</p>\n", 
                                "<p>Rs. 500</p>\n", "<p>Rs. 400</p>\n"],
                    options_hi: ["<p>Rs. 600</p>\n", "<p>Rs. 300</p>\n",
                                "<p>Rs. 500</p>\n", "<p>Rs. 400</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">51.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span><br><span style=\"font-family: Cambria Math;\">Profit on the pen (14% - </span><span style=\"font-family: Cambria Math;\">5%) = 36&rArr;</span><span style=\"font-family: Cambria Math;\">1% = 4</span><br><span style=\"font-family: Cambria Math;\">Therefore, cost price (100%) = 400 Rs.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">51.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2354;&#2350; &#2346;&#2352; &#2354;&#2366;&#2349; (14% - </span><span style=\"font-family: Palanquin Dark;\">5%) = 36 <span style=\"font-family: Cambria Math;\">&rArr;</span></span><span style=\"font-family: Palanquin Dark;\"> 1% = 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; (100%) = 400 &#2352;&#2369;&#2346;&#2351;&#2375;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52.<span style=\"font-family: Roboto;\"> What is the value of tan 10&deg;.tan 25&deg; .tan 45&deg;.tan 65&deg;.tan 80&deg; </span></p>\n",
                    question_hi: "<p>52. <span style=\"font-family: Roboto;\">tan 10&deg;.tan 25&deg; .tan 45&deg;.tan 65&deg;.tan 80&deg;</span><span style=\"font-family: Palanquin;\"> &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>0</p>\n", "<p>2</p>\n", 
                                "<p>1</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Roboto;\"> </span></p>\n"],
                    options_hi: ["<p>0</p>\n", "<p>2</p>\n",
                                "<p>1</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">52.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan 10&deg;.tan 25&deg; .tan 45&deg;.tan 65&deg;.tan 80&deg; &rArr;</span><span style=\"font-family: Cambria Math;\"> tan (90&deg; - </span><span style=\"font-family: Cambria Math;\">80&deg;).tan (90&deg; -</span><span style=\"font-family: Cambria Math;\"> 65&deg;) .tan 45&deg;.tan 65&deg;.tan 80&deg;</span><br><span style=\"font-family: Cambria Math;\">&rArr;tan 45&deg; = 1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">52.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan 10&deg;.tan 25&deg; .tan 45&deg;.tan 65&deg;.tan 80&deg; &rArr;</span><span style=\"font-family: Cambria Math;\"> tan (90&deg; - </span><span style=\"font-family: Cambria Math;\">80&deg;).tan (90&deg; -</span><span style=\"font-family: Cambria Math;\"> 65&deg;) .tan 45&deg;.tan 65&deg;.tan 80&deg;</span><br><span style=\"font-family: Cambria Math;\">&rArr;tan 45&deg; = 1</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. If <span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup></mfrac><mo>=</mo><mn>16</mn></math><span style=\"font-family: Roboto;\">, then what is the value of </span><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo></math> </span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">? </span></p>\n",
                    question_hi: "<p>53.<span style=\"font-family: Palanquin;\"> &#2351;&#2342;&#2367;</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup></mfrac><mo>=</mo><mn>16</mn></math><span style=\"font-family: Palanquin;\">, &#2340;&#2379; </span><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo></math><span style=\"font-family: Palanquin;\"> &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Roboto;\"> </span></p>\n", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", 
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n"],
                    options_hi: ["<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Roboto;\"> </span></p>\n", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n",
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">53.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup></mfrac><mo>=</mo><mn>16</mn><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mi>n</mi><mo>,</mo><mo>&nbsp;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><msqrt><mn>16</mn><mo>+</mo><mn>2</mn><mo>&nbsp;</mo></msqrt><mo>=</mo><mn>3</mn><msqrt><mn>2</mn></msqrt></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">53.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup></mfrac><mo>=</mo><mn>16</mn><mo>&nbsp;</mo><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><msqrt><mn>16</mn><mo>+</mo><mn>2</mn><mo>&nbsp;</mo></msqrt><mo>=</mo><mn>3</mn><msqrt><mn>2</mn></msqrt></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54.<span style=\"font-family: Roboto;\"> The table given below shows the cost price and value of profit of 6 articles. </span><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/04%202023-05-06%20111323.png\" alt=\"\" width=\"276\" height=\"187\"></p>\r\n<p><span style=\"font-family: Roboto;\">J1 = The value of average cost price of A, B, C and D.</span></p>\r\n<p><span style=\"font-family: Roboto;\">J2 = The total profit of all articles.</span></p>\r\n<p><span style=\"font-family: Roboto;\">What is the value of (J1 : J2)? </span></p>\n",
                    question_hi: "<p>54.<span style=\"font-family: Palanquin;\"> &#2344;&#2368;&#2330;&#2375; &#2342;&#2368; &#2327;&#2312; &#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; 6 &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2324;&#2352; &#2354;&#2366;&#2349; &#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image1.png\" width=\"285\" height=\"178\"></p>\r\n<p><span style=\"font-family: Palanquin;\">J1 = A, B, C &#2324;&#2352; D &#2325;&#2375; &#2324;&#2360;&#2340; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2366; &#2350;&#2366;&#2344;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin;\">J2 = &#2360;&#2349;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2366; &#2325;&#2369;&#2354; &#2354;&#2366;&#2349; &#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\"><span style=\"font-weight: 400;\">(J1 :&nbsp; J2)&nbsp; </span></span>&#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</p>\n",
                    options_en: ["<p>9 : 7</p>\n", "<p>7 : 3</p>\n", 
                                "<p>3 : 11</p>\n", "<p>3 : 7</p>\n"],
                    options_hi: ["<p>9 : 7</p>\n", "<p>7 : 3</p>\n",
                                "<p>3 : 11</p>\n", "<p>3 : 7</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">54.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The value of average cost price of A, B, C and D(J1) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>900</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 225</span><br><span style=\"font-family: Cambria Math;\">The total profit of all articles (J2) = 525</span><br><span style=\"font-family: Cambria Math;\">Then, the value of (J1 : J2) = 225 : 525&rArr;</span><span style=\"font-family: Cambria Math;\">3 : 7</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">54.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin;\"> A, B, C &#2324;&#2352; D &#2325;&#2366; &#2324;&#2360;&#2340; &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; (J1) =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>900</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Roboto;\"> = 225</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2360;&#2349;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2366; &#2325;&#2369;&#2354; &#2354;&#2366;&#2349; (J2) = 525</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2340;&#2348;, (J1 : J2) &#2325;&#2366; &#2350;&#2366;&#2344; = 225 : 525 <span style=\"font-family: Cambria Math;\">&rArr;</span></span><span style=\"font-family: Roboto;\"> 3 : 7</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55.<span style=\"font-family: Roboto;\"> Piyush marks the price of his article 30 percent more than its cost price. If he sells the article for Rs. 5720 after allowing a discount of 20 percent, then what will be the cost price of the article? </span></p>\n",
                    question_hi: "<p>55.<span style=\"font-family: Palanquin;\"> &#2346;&#2368;&#2351;&#2370;&#2359; &#2309;&#2346;&#2344;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2350;&#2370;&#2354;&#2381;&#2351; &#2313;&#2360;&#2325;&#2375; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2360;&#2375; 30 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2309;&#2343;&#2367;&#2325; &#2309;&#2306;&#2325;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2357;&#2361; 20 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2375;&#2325;&#2352; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2379; Rs. 5720 &#2350;&#2375;&#2306; &#2348;&#2375;&#2330;&#2340;&#2366; &#2361;&#2376;, &#2340;&#2379; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>Rs. 5000</p>\n", "<p>Rs. 5350</p>\n", 
                                "<p>Rs. 5200</p>\n", "<p>Rs. 5500</p>\n"],
                    options_hi: ["<p>Rs. 5000</p>\n", "<p>Rs. 5350</p>\n",
                                "<p>Rs. 5200</p>\n", "<p>Rs. 5500</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">55.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>10</mn><mn>13</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Roboto;\">After allowing the discount of 20% </span></p>\r\n<p><span style=\"font-family: Roboto;\">SP = </span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mo>&times;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mo>&nbsp;</mo><mn>5</mn></mrow></mfrac><mo>&rArr;</mo><mn>10</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi></math></span></p>\r\n<p><span style=\"font-family: Roboto;\">Now, SP(10.4) &rarr; unit </span><span style=\"font-family: Roboto;\"> 5720</span></p>\r\n<p><span style=\"font-family: Roboto;\">So, CP(10 unit) = 5720<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>10</mn><mrow><mn>10</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> </span><span style=\"font-family: Roboto;\"> =Rs. 5500 </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">55.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>=</mo><mfrac><mn>10</mn><mn>13</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">20% &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2375;&#2344;&#2375; &#2325;&#2375; &#2348;&#2366;&#2342;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SP = </span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mo>&times;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mo>&nbsp;</mo><mn>5</mn></mrow></mfrac><mo>&rArr;</mo><mn>10</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mi>&#2311;&#2325;&#2366;&#2312;</mi></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2348;, &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; (10.4) &#2311;&#2325;&#2366;&#2312; &rarr;&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> 5720</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319; , </span><span style=\"font-family: Palanquin Dark;\"> (10 unit) = 5720 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>10</mn><mrow><mn>10</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 5500 &#2352;&#2369;&#2346;&#2351;&#2375;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56.<span style=\"font-family: Roboto;\"> Parallel sides of a trapezium are 21 cm and 14 cm and its area is 875 cm&sup2;. What is the distance between parallel sides?</span></p>\n",
                    question_hi: "<p>56.<span style=\"font-family: Palanquin;\"> &#2319;&#2325; &#2360;&#2350;&#2354;&#2306;&#2348; &#2325;&#2368; &#2360;&#2350;&#2366;&#2344;&#2381;&#2340;&#2352; &#2349;&#2369;&#2332;&#2366;&#2319;&#2305; 21 cm &#2324;&#2352; 14 cm &#2361;&#2376;&#2306; &#2324;&#2352; &#2311;&#2360;&#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; 875 cm&sup2; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344;&#2381;&#2340;&#2352; &#2349;&#2369;&#2332;&#2366;&#2323;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2368; &#2342;&#2370;&#2352;&#2368; &#2325;&#2367;&#2340;&#2344;&#2368; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>45 cm</p>\n", "<p>50 cm</p>\n", 
                                "<p>60 cm</p>\n", "<p>65 cm</p>\n"],
                    options_hi: ["<p>45 cm</p>\n", "<p>50 cm</p>\n",
                                "<p>60 cm</p>\n", "<p>65 cm</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">56.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> Formula : - Area of the trapezium = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo></math></span><span style=\"font-family: Roboto;\"> sum of parallel sides &times;</span><span style=\"font-family: Roboto;\">(distance between parallel sides)</span></p>\r\n<p><span style=\"font-family: Roboto;\">then , distance between parallel sides =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>875</mn></mrow><mrow><mn>21</mn><mo>+</mo><mn>14</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&rArr;</mo></math> </span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> 50 cm</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">56.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\"><strong><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"><strong>: -</strong> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2354;&#2306;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo></math></span><span style=\"font-family: Roboto;\"> <span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> &times; (</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span></span></p>\r\n<p><span style=\"font-family: Roboto;\"><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>875</mn></mrow><mrow><mn>21</mn><mo>+</mo><mn>14</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&rArr;</mo></math> <span style=\"font-family: Cambria Math;\">50 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57.<span style=\"font-family: Roboto;\"> The line chart given below shows the number of male teacher and female teachers in 5 schools, </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image2.png\" width=\"327\" height=\"228\"></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><span style=\"font-family: Roboto;\">J1 = Total number of male and female teachers in school P.</span></p>\r\n<p><span style=\"font-family: Roboto;\">J2 = The difference between the total number of male and the total number of female teachers in all the 5 schools. </span></p>\r\n<p><span style=\"font-family: Roboto;\">What is the value of (J1: J2)?</span></p>\n",
                    question_hi: "<p>57. <span style=\"font-family: Palanquin;\">&#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2352;&#2375;&#2326;&#2366; &#2310;&#2354;&#2375;&#2326; &#2350;&#2375;&#2306; 5 &#2360;&#2381;&#2325;&#2370;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2369;&#2352;&#2369;&#2359; &#2358;&#2367;&#2325;&#2381;&#2359;&#2325;&#2379;&#2306; (male teacher) &#2324;&#2352; &#2350;&#2361;&#2367;&#2354;&#2366; &#2358;&#2367;&#2325;&#2381;&#2359;&#2325;&#2379;&#2306; (female teachers) &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image2.png\" width=\"327\" height=\"228\"></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin;\">J1 = &#2360;&#2381;&#2325;&#2370;&#2354; P &#2350;&#2375;&#2306; &#2346;&#2369;&#2352;&#2369;&#2359; &#2324;&#2352; &#2350;&#2361;&#2367;&#2354;&#2366; &#2358;&#2367;&#2325;&#2381;&#2359;&#2325;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin;\"> J2 = &#2360;&#2349;&#2368; 5 &#2360;&#2381;&#2325;&#2370;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2369;&#2352;&#2369;&#2359; &#2324;&#2352; &#2350;&#2361;&#2367;&#2354;&#2366; &#2358;&#2367;&#2325;&#2381;&#2359;&#2325;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; &#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">(J1 : J2)</span>&#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</p>\n",
                    options_en: ["<p>4 : 7</p>\n", "<p>9 : 7</p>\n", 
                                "<p>5 : 7</p>\n", "<p>7 : 4</p>\n"],
                    options_hi: ["<p>4 : 7</p>\n", "<p>9 : 7</p>\n",
                                "<p>5 : 7</p>\n", "<p>7 : 4</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">57.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Total number of male and female teachers in school P(J1) = 140</span></p>\r\n<p><span style=\"font-family: Roboto;\">The difference between the total no. of male and the total no. of female teachers (J2)</span></p>\r\n<p><span style=\"font-family: Roboto;\">In all 5 school = 290 - </span><span style=\"font-family: Roboto;\">210 &rArr;</span><span style=\"font-family: Roboto;\"> 80</span></p>\r\n<p><span style=\"font-family: Roboto;\">then, the value of (J1: J2) = 140 : 80 &rArr;</span><span style=\"font-family: Roboto;\"> 7 : 4 </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">57.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2360;&#2381;&#2325;&#2370;&#2354; P &#2350;&#2375;&#2306; &#2346;&#2369;&#2352;&#2369;&#2359; &#2324;&#2352; &#2350;&#2361;&#2367;&#2354;&#2366; &#2358;&#2367;&#2325;&#2381;&#2359;&#2325;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; (J1) = 140</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2360;&#2349;&#2368; 5 &#2360;&#2381;&#2325;&#2370;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2369;&#2352;&#2369;&#2359; &#2324;&#2352; &#2350;&#2361;&#2367;&#2354;&#2366; &#2358;&#2367;&#2325;&#2381;&#2359;&#2325;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352;(J2) = 290 - </span><span style=\"font-family: Roboto;\">210 &rArr;</span><span style=\"font-family: Roboto;\"> 80</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2340;&#2379;, (J1: J2) &#2325;&#2366; &#2350;&#2366;&#2344; = 140 : 80 <span style=\"font-family: Roboto;\">&rArr;</span></span><span style=\"font-family: Roboto;\"> 7 : 4 </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58.<span style=\"font-family: Roboto;\"> What is the value of </span><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>o</mi><mi>f</mi><mn>14</mn><mo>&divide;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&times;</mo><mn>4</mn><mo>&divide;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>6</mn><mo>&divide;</mo><mn>12</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>5</mn><mo>&divide;</mo><mn>25</mn><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>15</mn><mo>&divide;</mo><mn>5</mn><mo>&times;</mo><mn>6</mn></mrow><mrow><mo>&nbsp;</mo><mn>5</mn><mo>&times;</mo><mn>15</mn><mo>&nbsp;</mo><mo>&divide;</mo><mn>6</mn><mo>&nbsp;</mo></mrow></mfrac></math><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> ?</span></p>\n",
                    question_hi: "<p>58.<span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>o</mi><mi>f</mi><mn>14</mn><mo>&divide;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&times;</mo><mn>4</mn><mo>&divide;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>6</mn><mo>&divide;</mo><mn>12</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>5</mn><mo>&divide;</mo><mn>25</mn><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>15</mn><mo>&divide;</mo><mn>5</mn><mo>&times;</mo><mn>6</mn></mrow><mrow><mo>&nbsp;</mo><mn>5</mn><mo>&times;</mo><mn>15</mn><mo>&nbsp;</mo><mo>&divide;</mo><mn>6</mn><mo>&nbsp;</mo></mrow></mfrac></math><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin;\"> &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>7.64</p>\n", "<p>7.44</p>\n", 
                                "<p>7.62</p>\n", "<p>7.22</p>\n"],
                    options_hi: ["<p>7.64</p>\n", "<p>7.44</p>\n",
                                "<p>7.62</p>\n", "<p>7.22</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">58.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>o</mi><mi>f</mi><mn>14</mn><mo>&divide;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&times;</mo><mn>4</mn><mo>&divide;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>6</mn><mo>&divide;</mo><mn>12</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>5</mn><mo>&divide;</mo><mn>25</mn><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>15</mn><mo>&divide;</mo><mn>5</mn><mo>&times;</mo><mn>6</mn></mrow><mrow><mo>&nbsp;</mo><mn>5</mn><mo>&times;</mo><mn>15</mn><mo>&nbsp;</mo><mo>&divide;</mo><mn>6</mn><mo>&nbsp;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>49</mn><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac></mstyle><mrow><mn>10</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac></mstyle></mfrac><mo>+</mo><mfrac><mn>18</mn><mstyle displaystyle=\"true\"><mfrac><mn>25</mn><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac></mstyle></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>49</mn><mo>&nbsp;</mo></mrow><mrow><mn>20</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mn>15</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>36</mn><mn>25</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>245</mn><mo>+</mo><mn>375</mn><mo>+</mo><mn>144</mn></mrow><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>764</mn><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mn>7</mn><mo>.</mo><mn>64</mn><mo>%</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">58.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>o</mi><mi>f</mi><mn>14</mn><mo>&divide;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&times;</mo><mn>4</mn><mo>&divide;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>6</mn><mo>&divide;</mo><mn>12</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>5</mn><mo>&divide;</mo><mn>25</mn><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>15</mn><mo>&divide;</mo><mn>5</mn><mo>&times;</mo><mn>6</mn></mrow><mrow><mo>&nbsp;</mo><mn>5</mn><mo>&times;</mo><mn>15</mn><mo>&nbsp;</mo><mo>&divide;</mo><mn>6</mn><mo>&nbsp;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>49</mn><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac></mstyle><mrow><mn>10</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac></mstyle></mfrac><mo>+</mo><mfrac><mn>18</mn><mstyle displaystyle=\"true\"><mfrac><mn>25</mn><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac></mstyle></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>49</mn><mo>&nbsp;</mo></mrow><mrow><mn>20</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mn>15</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>36</mn><mn>25</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>245</mn><mo>+</mo><mn>375</mn><mo>+</mo><mn>144</mn></mrow><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>764</mn><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mn>7</mn><mo>.</mo><mn>64</mn><mo>%</mo></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. <span style=\"font-family: Roboto;\">The ratio of the present age of two girls is 3 : 4. After two year the ratio will be 7 : 9. Which of the following statement(s) is/are correct?</span></p>\r\n<p><span style=\"font-family: Roboto;\">I. Ratio of their ages after 10 year is 6 : 7.</span></p>\r\n<p><span style=\"font-family: Roboto;\">II. Ratio of their ages after 12 years is 6 : 7.</span></p>\n",
                    question_hi: "<p>59. <span style=\"font-family: Palanquin;\">&#2342;&#2379; &#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2348;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2310;&#2351;&#2369; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; 3 : 4 &#2361;&#2376;&#2404; &#2342;&#2379; &#2357;&#2352;&#2381;&#2359; &#2348;&#2366;&#2342; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; 7 : 9 &#2361;&#2379; &#2332;&#2366;&#2319;&#2327;&#2366;&#2404; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366;/&#2360;&#2375; &#2325;&#2341;&#2344; &#2360;&#2361;&#2368; &#2361;&#2376;/ &#2361;&#2376;&#2306;?</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2404;. 10 &#2357;&#2352;&#2381;&#2359; &#2348;&#2366;&#2342; &#2313;&#2344;&#2325;&#2368; &#2310;&#2351;&#2369; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; 6 : 7 &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin;\"> &#2405;. 12 &#2357;&#2352;&#2381;&#2359; &#2348;&#2366;&#2342; &#2313;&#2344;&#2325;&#2368; &#2310;&#2351;&#2369; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; 6 : 7 &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Only II</p>\n", "<p>Neither I nor II</p>\n", 
                                "<p>Both I and II</p>\n", "<p>Only I</p>\n"],
                    options_hi: ["<p>&#2325;&#2375;&#2357;&#2354; &#2405;</p>\n", "<p>&#2344; &#2340;&#2379; I &#2324;&#2352; &#2344; &#2361;&#2368; &#2405;</p>\n",
                                "<p>I &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306;</p>\n", "<p>&#2325;&#2375;&#2357;&#2354; &#2404;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">59.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Let the present age of two girls be 3x and 4x</span></p>\r\n<p><span style=\"font-family: Roboto;\">Then, according to the question,</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mn>4</mn><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mo>&rArr;</mo></math>x = 4</span></p>\r\n<p><span style=\"font-family: Roboto;\">So , Present age of the girls 12 years and 16 years</span></p>\r\n<p><span style=\"font-family: Roboto;\">Therefore , the ratio of ages after 10 years = 11 : 13(statement 1st wrong)</span></p>\r\n<p><span style=\"font-family: Roboto;\">And the ratio of their ages after 12 years = 6 : 7(statement 2nd right)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">59.(a)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2342;&#2379; &#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2310;&#2351;&#2369; 3x &#2324;&#2352; 4x &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2347;&#2367;&#2352;, &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mn>4</mn><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mo>&rArr;</mo></math>x = 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;&#2307;, &#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2310;&#2351;&#2369; 12 &#2357;&#2352;&#2381;&#2359; &#2324;&#2352; 16 &#2357;&#2352;&#2381;&#2359; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;, 10 &#2357;&#2352;&#2381;&#2359; &#2348;&#2366;&#2342; &#2325;&#2368; &#2310;&#2351;&#2369; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; = 11 : 13 (&#2346;&#2361;&#2354;&#2366; &#2325;&#2341;&#2344; &#2327;&#2354;&#2340;)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2324;&#2352; 12 &#2357;&#2352;&#2381;&#2359; &#2348;&#2366;&#2342; &#2313;&#2344;&#2325;&#2368; &#2310;&#2351;&#2369; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; = 6 : 7(&#2342;&#2370;&#2360;&#2352;&#2366; &#2325;&#2341;&#2344; &#2360;&#2361;&#2368;)</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60.<span style=\"font-family: Roboto;\"> ABCD is a cyclic quadrilateral in which angle B is opposite to angle D. If &ang;</span><span style=\"font-family: Roboto;\">B = x + 10 degree and &ang;</span><span style=\"font-family: Roboto;\">D = 2x + 35 degree, then what is the value of x?</span></p>\n",
                    question_hi: "<p>60.<span style=\"font-family: Palanquin;\"> ABCD &#2319;&#2325; &#2330;&#2325;&#2381;&#2352;&#2368;&#2351; &#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2325;&#2379;&#2339; B &#2325;&#2379;&#2339; D &#2325;&#2375; &#2360;&#2350;&#2381;&#2350;&#2369;&#2326; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; <span style=\"font-family: Roboto;\">&ang;</span></span><span style=\"font-family: Palanquin;\">B = x + 10 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368; &#2324;&#2352;&nbsp; </span><span style=\"font-family: Palanquin;\"><span style=\"font-family: Roboto;\">&ang;</span>D = 2x + 35 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368; &#2361;&#2376; &#2340;&#2379; x &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>35 degree</p>\n", "<p>40 degree</p>\n", 
                                "<p>50 degree</p>\n", "<p>45 degree</p>\n"],
                    options_hi: ["<p>35 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</p>\n", "<p>40 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</p>\n",
                                "<p>50 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</p>\n", "<p>45 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">60.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image22.png\" width=\"118\" height=\"108\"></p>\r\n<p><span style=\"font-family: Roboto;\">In the cyclic quadrilateral sum of the opposite angle is always be 180&deg;</span></p>\r\n<p><span style=\"font-family: Roboto;\">Therefore, &ang;</span><span style=\"font-family: Roboto;\">B + &ang;</span><span style=\"font-family: Roboto;\">D = 180&deg; &rArr;</span><span style=\"font-family: Roboto;\"> 3x + 45 = 180 &rArr;</span><span style=\"font-family: Roboto;\"> x = 45&deg;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">60.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image22.png\" width=\"118\" height=\"108\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2330;&#2325;&#2381;&#2352;&#2368;&#2351; &#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2350;&#2375;&#2306; &#2360;&#2350;&#2381;&#2350;&#2369;&#2326; &#2325;&#2379;&#2339;&#2379;&#2306; &#2325;&#2366; &#2351;&#2379;&#2327; &#2360;&#2342;&#2376;&#2357; 180&deg; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;, <span style=\"font-family: Roboto;\">&ang;</span></span><span style=\"font-family: Roboto;\">B + &ang;</span><span style=\"font-family: Roboto;\">D = 180&deg; &rArr;</span><span style=\"font-family: Roboto;\"> 3x + 45 = 180 &rArr;</span><span style=\"font-family: Roboto;\"> x = 45&deg;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. <span style=\"font-family: Roboto;\">P\'s income is 250 percent of Q\'s income and P\'s expenditure is 180 percent of Q\'s expenditure. If P\'s income is 400 percent of Q\'s expenditure, then what is the respective ratio of P\'s savings and Q\'s savings?</span></p>\n",
                    question_hi: "<p>61.<span style=\"font-family: Palanquin;\"> P &#2325;&#2368; &#2310;&#2351; Q &#2325;&#2368; &#2310;&#2351; &#2325;&#2366; 250 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2361;&#2376; &#2324;&#2352; P &#2325;&#2366; &#2357;&#2381;&#2351;&#2351; Q &#2325;&#2375; &#2357;&#2381;&#2351;&#2351; &#2325;&#2366; 180 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; P &#2325;&#2368; &#2310;&#2351; Q &#2325;&#2375; &#2357;&#2381;&#2351;&#2351; &#2325;&#2366; 400 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2361;&#2376;, &#2340;&#2379; P &#2325;&#2368; &#2348;&#2330;&#2340; &#2324;&#2352; Q &#2325;&#2368; &#2348;&#2330;&#2340; &#2325;&#2366; &#2325;&#2381;&#2352;&#2350;&#2358;: &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>8 : 3</p>\n", "<p>11 : 3</p>\n", 
                                "<p>11 : 5</p>\n", "<p>5 : 2</p>\n"],
                    options_hi: ["<p>8 : 3</p>\n", "<p>11 : 3</p>\n",
                                "<p>11 : 5</p>\n", "<p>5 : 2</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">61.(b) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">P&nbsp; &nbsp;&nbsp; :&nbsp; &nbsp; &nbsp;Q</span><br><span style=\"font-family: Cambria Math;\">Income&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">40&nbsp; &nbsp;:&nbsp; &nbsp;16</span><br><span style=\"font-family: Cambria Math;\">Expenditure&nbsp; &nbsp;&rarr;&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">18&nbsp; &nbsp;:&nbsp; &nbsp;10</span><br><strong><span style=\"font-family: Cambria Math;\">------------------------------------------------</span></strong><br><span style=\"font-family: Cambria Math;\">Savings &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp;&nbsp; </span><span style=\"font-family: Cambria Math;\">11&nbsp;&nbsp; :&nbsp; &nbsp;3</span><br><span style=\"font-family: Cambria Math;\">Therefore, the ratio of the savings of P And Q = 11 : 3</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">61.(b) </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> &nbsp; &nbsp; &rarr;&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">P&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Q</span><br><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">40&nbsp; &nbsp; :&nbsp; &nbsp; 16</span><br><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">18&nbsp; &nbsp; :&nbsp; &nbsp; 10</span><br><span style=\"font-family: Cambria Math;\">-<strong>-----------------------------------------------</strong></span><br><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">11&nbsp; &nbsp; :&nbsp; &nbsp; 3</span><br><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, P </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 11 : 3</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62.<span style=\"font-family: Roboto;\"> If sin&theta;&nbsp;</span><span style=\"font-family: Roboto;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> , then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mn>2</mn><mi>cos</mi><mi>&theta;</mi><mo>.</mo><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Roboto;\"> ?</span></p>\n",
                    question_hi: "<p>62. <span style=\"font-family: Palanquin;\"> &#2351;&#2342;&#2367; sin<span style=\"font-family: Roboto;\">&theta;</span>&nbsp;</span><span style=\"font-family: Roboto;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math></span><span style=\"font-family: Palanquin;\"> , &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mn>2</mn><mi>cos</mi><mi>&theta;</mi><mo>.</mo><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Palanquin;\"> &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>126</mn></mfrac></math><span style=\"font-family: Roboto;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>113</mn><mn>120</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>117</mn><mn>136</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>119</mn><mn>120</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>126</mn></mfrac></math><span style=\"font-family: Roboto;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>113</mn><mn>120</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>117</mn><mn>136</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>119</mn><mn>120</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">62.(d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow></mfrac><mo>=</mo><mfrac><mn>5</mn><mrow><mn>13</mn><mo>&nbsp;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>h</mi><mi>e</mi><mi>n</mi><mo>,</mo><mo>&nbsp;</mo><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi><mo>=</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mn>2</mn><mi>cos</mi><mi>&theta;</mi><mo>.</mo><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>144</mn><mrow><mn>169</mn><mo>&nbsp;</mo></mrow></mfrac><mo>-</mo><mfrac><mn>25</mn><mn>169</mn></mfrac></mstyle><mrow><mn>2</mn><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>12</mn><mn>13</mn></mfrac></mstyle><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mrow><mn>13</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></mstyle></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>119</mn><mn>120</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">62.(d)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac><mo>=</mo><mfrac><mn>5</mn><mrow><mn>13</mn><mo>&nbsp;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2347;&#2367;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>=</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mn>2</mn><mi>cos</mi><mi>&theta;</mi><mo>.</mo><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>144</mn><mrow><mn>169</mn><mo>&nbsp;</mo></mrow></mfrac><mo>-</mo><mfrac><mn>25</mn><mn>169</mn></mfrac></mstyle><mrow><mn>2</mn><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>12</mn><mn>13</mn></mfrac></mstyle><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mrow><mn>13</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></mstyle></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>119</mn><mn>120</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63.<span style=\"font-family: Roboto;\"> The perimeter of an isosceles triangle is 91 cm. If one of the equal sides measures 28 cm, then what is the valve of the other non-equal side? </span></p>\n",
                    question_hi: "<p>63. <span style=\"font-family: Palanquin;\">&#2319;&#2325; &#2360;&#2350;&#2342;&#2381;&#2357;&#2367;&#2348;&#2366;&#2361;&#2369; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2325;&#2366; &#2346;&#2352;&#2367;&#2350;&#2366;&#2346; 91 cm &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2360;&#2350;&#2366;&#2344; &#2349;&#2369;&#2332;&#2366;&#2323;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2325;&#2366; &#2350;&#2366;&#2346; 28 cm &#2361;&#2376;, &#2340;&#2379; &#2309;&#2360;&#2350;&#2366;&#2344; &#2349;&#2369;&#2332;&#2366; &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>56 cm</p>\n", "<p>42 cm</p>\n", 
                                "<p>14 cm</p>\n", "<p>35 cm</p>\n"],
                    options_hi: ["<p>56 cm</p>\n", "<p>42 cm</p>\n",
                                "<p>14 cm</p>\n", "<p>35 cm</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">63.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image45.png\" width=\"122\" height=\"94\"></p>\r\n<p><span style=\"font-family: Roboto;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Roboto;\">Value of the non - equal side = 91 - </span><span style=\"font-family: Roboto;\">(28 + 28 ) = 35 cm</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">63.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683871144/word/media/image45.png\" width=\"122\" height=\"94\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2360;&#2350;&#2366;&#2344; &#2349;&#2369;&#2332;&#2366; &#2325;&#2366; &#2350;&#2366;&#2344; = 91 - (28 + 28 ) = 35 &#2360;&#2375;&#2350;&#2368;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. <span style=\"font-family: Roboto;\">What is the average of all prime numbers between 30 and 50 ? </span></p>\n",
                    question_hi: "<p>64. <span style=\"font-family: Palanquin;\">30 &#2324;&#2352; 50 &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2349;&#2368; &#2309;&#2349;&#2366;&#2332;&#2381;&#2351; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>38.5</p>\n", "<p>39.8</p>\n", 
                                "<p>34.2</p>\n", "<p>36.5</p>\n"],
                    options_hi: ["<p>38.5</p>\n", "<p>39.8</p>\n",
                                "<p>34.2</p>\n", "<p>36.5</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">64.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\">prime numbers between 30 and 50 = 31 , 37 , 41 , 43 , 47</span></p>\r\n<p><span style=\"font-family: Roboto;\">Therefore, their average =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>199</mn><mn>5</mn></mfrac></math>&rArr;&nbsp;</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> 39.8</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">64.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\"><span style=\"font-family: Cambria Math;\">30 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 50 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span> = 31 , 37 , 41 , 43 , 47</span></p>\r\n<p><span style=\"font-family: Roboto;\"><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>199</mn><mn>5</mn></mfrac></math>&rArr;&nbsp;</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> 39.8</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65.<span style=\"font-family: Roboto;\"> In &Delta;</span><span style=\"font-family: Roboto;\">XYZ, I is the incentre of the &Delta;</span><span style=\"font-family: Roboto;\">XYZ. If &ang;</span><span style=\"font-family: Roboto;\">XYZ = 40 degree. then what is the value of&nbsp; </span><span style=\"font-family: Roboto;\">&ang;XIZ? </span></p>\n",
                    question_hi: "<p>65. <span style=\"font-family: Roboto;\">&Delta;</span><span style=\"font-family: Palanquin;\">XYZ &#2350;&#2375;&#2306;, I, <span style=\"font-family: Roboto;\">&Delta;</span></span><span style=\"font-family: Palanquin;\">XYZ &#2325;&#2366; &#2309;&#2306;&#2340;: &#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; <span style=\"font-family: Roboto;\">&ang;</span></span><span style=\"font-family: Palanquin;\">XYZ = 40 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368; &#2361;&#2376;, &#2340;&#2379; <span style=\"font-family: Roboto;\">&ang;</span></span><span style=\"font-family: Palanquin;\">XIZ &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;? </span></p>\n",
                    options_en: ["<p>115 degree</p>\n", "<p>110 degree</p>\n", 
                                "<p>120 degree</p>\n", "<p>130 degree</p>\n"],
                    options_hi: ["<p>115 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</p>\n", "<p>110 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</p>\n",
                                "<p>120 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</p>\n", "<p>130 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">65.(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image5.png\" width=\"124\" height=\"91\"></p>\r\n<p><span style=\"font-family: Roboto;\">I is the incentre of the &Delta;</span><span style=\"font-family: Roboto;\">XYZ</span></p>\r\n<p><span style=\"font-family: Roboto;\">therefore , &ang;</span><span style=\"font-family: Roboto;\">XIZ = 90 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> = 110&deg;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">65.(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image5.png\" width=\"124\" height=\"91\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&nbsp;<span style=\"font-family: Roboto;\">&Delta;</span>XYZ &#2325;&#2366; &#2309;&#2344;&#2381;&#2340;&#2307;&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; I &#2361;&#2376; </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Roboto;\"> , &ang;</span><span style=\"font-family: Roboto;\">XIZ = 90 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> = 110&deg;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66.<span style=\"font-family: Roboto;\"> Sides of a triangle are 9 cm, 6 cm and 5 cm. What is the value of circumradius of this triangle? </span></p>\n",
                    question_hi: "<p>66.<span style=\"font-family: Palanquin;\"> &#2319;&#2325; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366;&#2319;&#2306; 9 cm, 6 cm &#2324;&#2352; 5 cm &#2361;&#2376;&#2306;&#2404; &#2311;&#2360; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2325;&#2368; &#2346;&#2352;&#2367;&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>3</mn></msqrt></mrow><mn>4</mn></mfrac></math> cm</span></p>\n", "<p><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>3</mn></msqrt></mrow><mn>5</mn></mfrac></math>cm</span></p>\n", 
                                "<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><msqrt><mn>2</mn></msqrt></mrow><mn>8</mn></mfrac></math>cm</span></p>\n", "<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math>cm</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>3</mn></msqrt></mrow><mn>4</mn></mfrac></math>cm</span></p>\n", "<p><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>3</mn></msqrt></mrow><mn>5</mn></mfrac></math>cm</span></p>\n",
                                "<p><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><msqrt><mn>2</mn></msqrt></mrow><mn>8</mn></mfrac></math>cm</span></p>\n", "<p><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math>cm</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">66.(c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>e</mi><mi>m</mi><mi>i</mi><mo>&nbsp;</mo><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>t</mi><mi>r</mi><mi>i</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi><mo>&nbsp;</mo><mo>(</mo><mi>s</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>9</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>10</mn><mo>&nbsp;</mo><mi>c</mi><mi>m</mi><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>i</mi><mi>r</mi><mi>c</mi><mi>u</mi><mi>m</mi><mi>r</mi><mi>a</mi><mi>d</mi><mi>i</mi><mi>u</mi><mi>s</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>s</mi><mi>c</mi><mi>a</mi><mi>l</mi><mi>e</mi><mi>n</mi><mi>e</mi><mo>&nbsp;</mo><mi>t</mi><mi>r</mi><mi>i</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mi>a</mi><mi>b</mi><mi>c</mi></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mi>&Delta;</mi></mrow></mfrac><mo>=</mo><mfrac><mn>270</mn><mrow><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><msqrt><mn>10</mn><mo>(</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>)</mo><mo>&nbsp;</mo></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>270</mn><mo>&times;</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>5</mn><mo>&times;</mo><mn>2</mn><msqrt><mn>2</mn><mo>&nbsp;</mo></msqrt><mo>&times;</mo><msqrt><mn>2</mn><mo>&nbsp;</mo></msqrt></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mrow><mn>27</mn><msqrt><mn>2</mn></msqrt></mrow><mn>8</mn></mfrac><mo>&nbsp;</mo><mi>c</mi><mi>m</mi></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">66.(c)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2309;&#2352;&#2381;&#2343;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mo>&nbsp;</mo><mo>(</mo><mi>s</mi><mo>)</mo><mo>=</mo><mfrac><mrow><mn>9</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>10</mn><mo>&nbsp;</mo><mi>&#2360;&#2375;&#2350;&#2368;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2357;&#2367;&#2359;&#2350;&#2348;&#2366;&#2361;&#2369;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2346;&#2352;&#2367;&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mi>a</mi><mi>b</mi><mi>c</mi></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mi>&Delta;</mi></mrow></mfrac><mo>=</mo><mfrac><mn>270</mn><mrow><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><msqrt><mn>10</mn><mo>(</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>)</mo><mo>&nbsp;</mo></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>270</mn><mo>&times;</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>5</mn><mo>&times;</mo><mn>2</mn><msqrt><mn>2</mn><mo>&nbsp;</mo></msqrt><mo>&times;</mo><msqrt><mn>2</mn><mo>&nbsp;</mo></msqrt></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mrow><mn>27</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>8</mn><mo>&nbsp;</mo></mrow></mfrac><mi>&#2360;&#2375;&#2350;&#2368;</mi></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67.<span style=\"font-family: Roboto;\"> S does half as much work as T in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> of the time taken by T. If they together complete a work in 60 days, then how many days shall S alone take to complete that work? </span></p>\n",
                    question_hi: "<p>67. <span style=\"font-family: Palanquin;\">T &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2354;&#2367;&#2319; &#2327;&#2319; &#2360;&#2350;&#2351; &#2325;&#2375; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Palanquin;\"> &#2349;&#2366;&#2327; &#2350;&#2375;&#2306; S, T &#2360;&#2375; &#2310;&#2343;&#2366; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2357;&#2375; &#2350;&#2367;&#2354;&#2325;&#2352; &#2325;&#2367;&#2360;&#2368; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; 60 &#2342;&#2367;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2340;&#2379; S &#2309;&#2325;&#2375;&#2354;&#2375; &#2313;&#2360;&#2368; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; &#2325;&#2367;&#2340;&#2344;&#2375; &#2342;&#2367;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2375;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>80<span style=\"font-family: Roboto;\"> days</span></p>\n", "<p>90 days</p>\n", 
                                "<p>75 days</p>\n", "<p>100 days</p>\n"],
                    options_hi: ["<p>80 &#2342;&#2367;&#2344;</p>\n", "<p>90 &#2342;&#2367;&#2344;</p>\n",
                                "<p>75 &#2342;&#2367;&#2344;</p>\n", "<p>100 &#2342;&#2367;&#2344;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">67.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Roboto;\">Time taken by T to complete the work = 8 days</span></p>\r\n<p><span style=\"font-family: Roboto;\">And time taken by S to complete the work = 2 days</span></p>\r\n<p><span style=\"font-family: Roboto;\">Then, the efficiency of S and T = 8 unit and 2 unit</span></p>\r\n<p><span style=\"font-family: Roboto;\"> As we know Total work = total time &times; total efficiency </span></p>\r\n<p><span style=\"font-family: Roboto;\">Then, total work = 60&times;10 = 600 unit </span></p>\r\n<p><span style=\"font-family: Roboto;\">Therefore, total time taken by S to complete the work = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> = 75 days </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">67.(c)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; T &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2354;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2360;&#2350;&#2351; = 8 &#2342;&#2367;&#2344; </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2341;&#2366; S &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2354;&#2327;&#2366; &#2360;&#2350;&#2351; = 2 &#2342;&#2367;&#2344; </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2348;, S &#2324;&#2352; T &#2325;&#2368; &#2342;&#2325;&#2381;&#2359;&#2340;&#2366; = 8 &#2311;&#2325;&#2366;&#2312; &#2324;&#2352; 2 &#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> &#2332;&#2376;&#2360;&#2366; &#2325;&#2367; &#2361;&#2350; &#2332;&#2366;&#2344;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2369;&#2354; &#2325;&#2366;&#2352;&#2381;&#2351; = &#2325;&#2369;&#2354; &#2360;&#2350;&#2351; &times; &#2325;&#2369;&#2354; &#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2348;, &#2325;&#2369;&#2354; &#2325;&#2366;&#2352;&#2381;&#2351; = 60 &times; 10 = 600 &#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; S &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2354;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2325;&#2369;&#2354; &#2360;&#2350;&#2351; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>8</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 75 &#2342;&#2367;&#2344;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68.<span style=\"font-family: Roboto;\"> If sin&alpha;&nbsp;</span><span style=\"font-family: Roboto;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> and sin&beta; </span><span style=\"font-family: Roboto;\">=</span><span style=\"font-family: Roboto;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> then what is the value of cos (</span><span style=\"font-family: Roboto;\"> &alpha; + &beta;</span><span style=\"font-family: Roboto;\">)? (0 degree &lt; &alpha;</span><span style=\"font-family: Roboto;\">, <span style=\"font-family: Palanquin;\">&beta;</span></span><span style=\"font-family: Roboto;\"> &lt; 90 degree) </span></p>\n",
                    question_hi: "<p>68. <span style=\"font-family: Palanquin;\">&#2351;&#2342;&#2367; sin<span style=\"font-family: Roboto;\">&alpha;</span> </span><span style=\"font-family: Roboto;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Palanquin;\"> &#2324;&#2352; sin&beta; </span><span style=\"font-family: Roboto;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Palanquin;\"> &#2340;&#2379; cos(</span><span style=\"font-family: Roboto;\"> &alpha; +<span style=\"font-family: Palanquin;\">&beta;</span> </span><span style=\"font-family: Palanquin;\">) &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ? (0 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;&lt; <span style=\"font-family: Roboto;\">&alpha; </span></span><span style=\"font-family: Roboto;\">,<span style=\"font-family: Palanquin;\">&beta;</span>&nbsp;</span><span style=\"font-family: Palanquin;\"> &lt; 90 &#2337;&#2367;&#2327;&#2381;&#2352;&#2368;) </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\n", "<p>1</p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\n", "<p>1</p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">68.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> sin&alpha;&nbsp;</span><span style=\"font-family: Roboto;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> and sin&beta; </span><span style=\"font-family: Roboto;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> &rArr;</span><span style=\"font-family: Roboto;\"> (</span><span style=\"font-family: Roboto;\"> &alpha; + &beta;= 60&deg;)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Then, cos(</span><span style=\"font-family: Roboto;\"> &alpha; + &beta;</span><span style=\"font-family: Roboto;\">) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">68.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\">sin&alpha;&nbsp;</span><span style=\"font-family: Roboto;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> <span style=\"font-weight: 400;\">&#2324;&#2352; </span>sin&beta; </span><span style=\"font-family: Roboto;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> &rArr;</span><span style=\"font-family: Roboto;\"> (</span><span style=\"font-family: Roboto;\"> &alpha; + &beta;= 60&deg;)</span></p>\r\n<p><span style=\"font-family: Roboto;\"><span style=\"font-weight: 400;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span>, cos(</span><span style=\"font-family: Roboto;\"> &alpha; + &beta;</span><span style=\"font-family: Roboto;\">) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. <span style=\"font-family: Roboto;\">If a + b + c = 1, ab + bc + ca = -1 and abc = -1, then what is the value <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>&nbsp;</mo></math></span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">? </span></p>\n",
                    question_hi: "<p>69. <span style=\"font-family: Palanquin;\">&#2351;&#2342;&#2367; a + b + c = 1, ab + bc + ca = -1 &#2324;&#2352; abc = -1, &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>&nbsp;</mo></math></span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>3</p>\n", "<p>5</p>\n", 
                                "<p>2</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>3</p>\n", "<p>5</p>\n",
                                "<p>2</p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">69.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo mathvariant=\"bold\">&nbsp;</mo><mi mathvariant=\"bold-italic\">F</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">m</mi><mi mathvariant=\"bold-italic\">u</mi><mi mathvariant=\"bold-italic\">l</mi><mi mathvariant=\"bold-italic\">a</mi><mo mathvariant=\"bold\">&nbsp;</mo><mo mathvariant=\"bold\">:</mo><mo mathvariant=\"bold\">-</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><mi>a</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>c</mi><mo>+</mo><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mo>&rArr;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>c</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>N</mi><mi>o</mi><mi>w</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>=</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mi>c</mi><mo>)</mo><mo>[</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>(</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>]</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mn>3</mn><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>[</mo><mn>3</mn><mo>-</mo><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>=</mo><mn>1</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">69.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo mathvariant=\"bold\">&nbsp;</mo><mi mathvariant=\"bold-italic\">F</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">m</mi><mi mathvariant=\"bold-italic\">u</mi><mi mathvariant=\"bold-italic\">l</mi><mi mathvariant=\"bold-italic\">a</mi><mo mathvariant=\"bold\">&nbsp;</mo><mo mathvariant=\"bold\">:</mo><mo mathvariant=\"bold\">-</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><mi>a</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>c</mi><mo>+</mo><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mfenced><mn>1</mn></mfenced><mn>2</mn></msup><mo>=</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mo>&rArr;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>c</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2348;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>=</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mi>c</mi><mo>)</mo><mo>[</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>(</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>]</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mn>3</mn><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>[</mo><mn>3</mn><mo>-</mo><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>=</mo><mn>1</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. <span style=\"font-family: Roboto;\">Initial value of a car is Rs. 20000. The value of car depreciated by 20 percent of its initial value each year, What will be its value after 3 years? </span></p>\n",
                    question_hi: "<p>70. <span style=\"font-family: Palanquin;\">&#2319;&#2325; &#2325;&#2366;&#2352; &#2325;&#2366; &#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351; Rs. 20000 &#2361;&#2376;&#2404; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2357;&#2352;&#2381;&#2359; &#2325;&#2366;&#2352; &#2325;&#2366; &#2350;&#2370;&#2354;&#2381;&#2351; &#2311;&#2360;&#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351; &#2360;&#2375; 20 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2328;&#2335; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, 3 &#2357;&#2352;&#2381;&#2359; &#2348;&#2366;&#2342; &#2325;&#2366;&#2352; &#2325;&#2366; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>Rs. 10560</p>\n", "<p>Rs. 11980</p>\n", 
                                "<p>Rs. 9840</p>\n", "<p>Rs. 10240</p>\n"],
                    options_hi: ["<p>Rs. 10560</p>\n", "<p>Rs. 11980</p>\n",
                                "<p>Rs. 9840</p>\n", "<p>Rs. 10240</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">70.(d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>P</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>c</mi><mi>a</mi><mi>r</mi><mo>&nbsp;</mo><mi>a</mi><mi>f</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mi>y</mi><mi>e</mi><mi>a</mi><mi>r</mi><mi>s</mi><mo>=</mo><mn>20000</mn><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>=</mo><mn>10240</mn><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">70.(d)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2360;&#2366;&#2354;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2348;&#2366;&#2342;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2325;&#2366;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2325;&#2368;&#2350;&#2340;</mi><mo>&nbsp;</mo><mo>=</mo><mn>20000</mn><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>=</mo><mn>10240</mn><mo>&nbsp;</mo><mi>&#2352;&#2369;</mi><mo>.</mo></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. <span style=\"font-family: Roboto;\">What is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>&nbsp;</mo><mo>)</mo></mrow><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><msup><mi>b</mi><mn>3</mn></msup></math></span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> ? </span></p>\n",
                    question_hi: "<p>71. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>&nbsp;</mo><mo>)</mo></mrow><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><msup><mi>b</mi><mn>3</mn></msup></math> <span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; </span><span style=\"font-family: Roboto;\"> ? </span></p>\n",
                    options_en: ["<p>3ab(a &mdash; b)</p>\n", "<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">3ab(a &mdash; b) </span></p>\n", 
                                "<p>3ab(a + b)</p>\n", "<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">3ab(a + b) </span></p>\n"],
                    options_hi: ["<p>3ab(a &mdash; b)</p>\n", "<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">3ab(a &mdash; b) </span></p>\n",
                                "<p>3ab(a + b)</p>\n", "<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">3ab(a + b) </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">71.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mn>3</mn><mo>(</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>(</mo><mi>a</mi><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo></math><span style=\"font-family: Roboto;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">71.(c)</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mn>3</mn><mo>(</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>(</mo><mi>a</mi><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo></math><span style=\"font-family: Roboto;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. <span style=\"font-family: Roboto;\">The pie chart given below show the runs scored by 5 batsman in a cricket match, Total runs scored by these batsman are 400. Runs scored by a batsman is shown as a percentage of runs scored by all these five batsman:</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image6.png\" width=\"252\" height=\"196\"></p>\r\n<p><span style=\"font-family: Roboto;\">What is the ratio of runs scored by D to the runs scored by H? </span></p>\n",
                    question_hi: "<p>72.<span style=\"font-family: Palanquin;\"> &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2371;&#2340;&#2381;&#2340; &#2310;&#2354;&#2375;&#2326; &#2350;&#2375;&#2306; &#2319;&#2325; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; 5 &#2348;&#2354;&#2381;&#2354;&#2375;&#2348;&#2366;&#2332;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344;&#2379;&#2306; &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2311;&#2344; &#2348;&#2354;&#2381;&#2354;&#2375;&#2348;&#2366;&#2332; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2325;&#2369;&#2354; &#2352;&#2344; 400 &#2361;&#2376;&#2306;&#2404; &#2319;&#2325; &#2348;&#2354;&#2381;&#2354;&#2375;&#2348;&#2366;&#2332; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344;&#2379;&#2306; &#2325;&#2379; &#2311;&#2344; &#2360;&#2349;&#2368; &#2346;&#2366;&#2305;&#2330; &#2348;&#2354;&#2381;&#2354;&#2375;&#2348;&#2366;&#2332; <span style=\"font-weight: 400;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366; </span>&#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344;&#2379;&#2306; &#2325;&#2375; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image6.png\" width=\"252\" height=\"196\"></p>\r\n<p><span style=\"font-family: Palanquin;\"> D &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344;&#2379;&#2306; &#2324;&#2352; H &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344;&#2379;&#2306; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2325;&#2367;&#2340;&#2344;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>1 : 5</p>\n", "<p>4 : 1</p>\n", 
                                "<p>1 : 3</p>\n", "<p>3 : 1</p>\n"],
                    options_hi: ["<p>1 : 5</p>\n", "<p>4 : 1</p>\n",
                                "<p>1 : 3</p>\n", "<p>3 : 1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">72.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Runs scored by D = 400<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>15</mn><mn>100</mn></mfrac><mo>=</mo><mn>60</mn></math> </span></p>\r\n<p><span style=\"font-family: Roboto;\">Runs scored by H = 400<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>5</mn><mn>100</mn></mfrac><mo>=</mo><mn>20</mn></math> </span></p>\r\n<p><span style=\"font-family: Roboto;\">Then, </span><span style=\"font-family: Roboto;\">the ratio of runs scored by D to the runs scored by H = 3 : 1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">72.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">D &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344; = 400<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>15</mn><mn>100</mn></mfrac><mo>=</mo><mn>60</mn></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">H &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344; = 400<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>5</mn><mn>100</mn></mfrac><mo>=</mo><mn>20</mn></math> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2347;&#2367;&#2352;, D &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344;&#2379;&#2306; &#2325;&#2366; H &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2319; &#2327;&#2319; &#2352;&#2344;&#2379;&#2306; &#2360;&#2375; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; = 3 : 1</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. <span style=\"font-family: Roboto;\">The following bar graph shows the amount (in lakh Rs.) investment by a company in purchasing raw materials over the years 2015 to 2019. </span><span style=\"font-family: Roboto;\">By how much percent the company increase its investment on raw materials in 2019, as compared to 2015 ? </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image7.png\" width=\"288\" height=\"201\"></p>\n",
                    question_hi: "<p>73.<span style=\"font-family: Palanquin;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2342;&#2306;&#2337; &#2310;&#2354;&#2375;&#2326; &#2357;&#2352;&#2381;&#2359; 2015 &#2360;&#2375; 2019 &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2325;&#2330;&#2381;&#2330;&#2375; &#2350;&#2366;&#2354; &#2325;&#2368; &#2326;&#2352;&#2368;&#2342; &#2350;&#2375;&#2306; &#2319;&#2325; &#2325;&#2306;&#2346;&#2344;&#2368; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2357;&#2375;&#2358; &#2325;&#2368; &#2327;&#2312; &#2352;&#2366;&#2358;&#2367; (&#2354;&#2366;&#2326; &#2352;&#2369;&#2346;&#2351;&#2375; &#2350;&#2375;&#2306;) &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2357;&#2352;&#2381;&#2359; 2015 &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2359; 2019 &#2350;&#2375;&#2306; &#2325;&#2306;&#2346;&#2344;&#2368; &#2344;&#2375; &#2325;&#2330;&#2381;&#2330;&#2375; &#2350;&#2366;&#2354; &#2346;&#2352; &#2309;&#2346;&#2344;&#2375; &#2344;&#2367;&#2357;&#2375;&#2358; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2375; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2368; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2325;&#2368; ?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683349468/word/media/image8.png\" width=\"294\" height=\"205\"></p>\n",
                    options_en: ["<p>33.33%</p>\n", "<p>66.66%</p>\n", 
                                "<p>11.66%</p>\n", "<p>20%</p>\n"],
                    options_hi: ["<p>33.33%</p>\n", "<p>66.66%</p>\n",
                                "<p>11.66%</p>\n", "<p>20%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">73.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\">company increase its investment on raw materials in 2019, as compared to 2015<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>220</mn></mrow><mn>330</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&rArr;</mo><mn>66</mn><mo>.</mo><mn>66</mn><mo>%</mo></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">73.(b)</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2325;&#2306;&#2346;&#2344;&#2368; &#2344;&#2375; 2015 &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; 2019 &#2350;&#2375;&#2306; &#2325;&#2330;&#2381;&#2330;&#2375; &#2350;&#2366;&#2354; &#2346;&#2352; &#2309;&#2346;&#2344;&#2366; &#2344;&#2367;&#2357;&#2375;&#2358; &#2348;&#2338;&#2364;&#2366;&#2351;&#2366;</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>220</mn></mrow><mn>330</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&rArr;</mo><mn>66</mn><mo>.</mo><mn>66</mn><mo>%</mo></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74.<span style=\"font-family: Roboto;\"> Average of 8 values is 36. If 4 is subtracted from each of the first three values and 5 is added in the each of the next five values, then what will be the new average?</span></p>\n",
                    question_hi: "<p>74.<span style=\"font-family: Palanquin;\"> 8 &#2350;&#2370;&#2354;&#2381;&#2351;&#2379;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; 36 &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2346;&#2361;&#2354;&#2375; &#2340;&#2368;&#2344; &#2350;&#2370;&#2354;&#2381;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2350;&#2375;&#2306; &#2360;&#2375; 4 &#2328;&#2335;&#2366; &#2342;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319; &#2324;&#2352; &#2309;&#2327;&#2354;&#2375; &#2346;&#2366;&#2306;&#2330; &#2350;&#2370;&#2354;&#2381;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2350;&#2375;&#2306; 5 &#2332;&#2379;&#2337;&#2364; &#2342;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319;, &#2340;&#2379; &#2344;&#2351;&#2366; &#2324;&#2360;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>34.325</p>\n", "<p>36.350</p>\n", 
                                "<p>38.325</p>\n", "<p>37.625</p>\n"],
                    options_hi: ["<p>34.325</p>\n", "<p>36.350</p>\n",
                                "<p>38.325</p>\n", "<p>37.625</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">74.(d)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Sum of the 8 values = 288</span></p>\r\n<p><span style=\"font-family: Roboto;\">After subtracting 4 from each of the first three number and adding 5 in each of next five numbers</span></p>\r\n<p><span style=\"font-family: Roboto;\">Then, the new average = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>288</mn><mo>+</mo><mo>(</mo><mo>-</mo><mn>12</mn><mo>+</mo><mn>25</mn><mo>)</mo></mrow><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>301</mn><mn>8</mn></mfrac><mo>&rArr;</mo></math></span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">37.625</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">74.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">8 &#2350;&#2366;&#2344;&#2379;&#2306; &#2325;&#2366; &#2351;&#2379;&#2327; = 288</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2361;&#2354;&#2368; &#2340;&#2368;&#2344; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2350;&#2375;&#2306; &#2360;&#2375; 4 &#2328;&#2335;&#2366;&#2325;&#2352; &#2324;&#2352; &#2309;&#2327;&#2354;&#2368; &#2346;&#2366;&#2305;&#2330; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2350;&#2375;&#2306; 5 &#2332;&#2379;&#2337;&#2364; &#2325;&#2352;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2344;&#2351;&#2366; &#2324;&#2360;&#2340; = </span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>288</mn><mo>+</mo><mo>(</mo><mo>-</mo><mn>12</mn><mo>+</mo><mn>25</mn><mo>)</mo></mrow><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>301</mn><mn>8</mn></mfrac><mo>&rArr;</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> 37.625</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "18",
                    question_en: "<p>75.<span style=\"font-family: Roboto;\"> Diameter of wheel of a cycle is 7 cm . A cyclist takes 75 minutes to reach a destination at the speed of 36.3 km/hr. How many revolutions will the wheel make during the journey? </span></p>\n",
                    question_hi: "<p>75.<span style=\"font-family: Palanquin;\"> &#2319;&#2325; &#2360;&#2366;&#2311;&#2325;&#2367;&#2354; &#2325;&#2375; &#2346;&#2361;&#2367;&#2319; &#2325;&#2366; &#2357;&#2381;&#2351;&#2366;&#2360; 7 cm &#2361;&#2376;&#2404; &#2319;&#2325; &#2360;&#2366;&#2311;&#2325;&#2367;&#2354; &#2360;&#2357;&#2366;&#2352; &#2325;&#2379; &#2319;&#2325; &#2327;&#2306;&#2340;&#2357;&#2381;&#2351; &#2340;&#2325; &#2346;&#2361;&#2369;&#2305;&#2330;&#2344;&#2375; &#2350;&#2375;&#2306; 36.3 km/hr &#2325;&#2368; &#2330;&#2366;&#2354; &#2360;&#2375; 75 &#2350;&#2367;&#2344;&#2335; &#2354;&#2327;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2351;&#2366;&#2340;&#2381;&#2352;&#2366; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2346;&#2361;&#2367;&#2351;&#2366; &#2325;&#2367;&#2340;&#2344;&#2375; &#2330;&#2325;&#2381;&#2325;&#2352; &#2354;&#2327;&#2366;&#2319;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>196280</p>\n", "<p>206250</p>\n", 
                                "<p>216580</p>\n", "<p>212520</p>\n"],
                    options_hi: ["<p>196280</p>\n", "<p>206250</p>\n",
                                "<p>216580</p>\n", "<p>212520</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">75.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\">1 revolution of wheel </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">&pi;</mi><mi>r</mi><mo>)</mo><mo>=</mo><mn>2</mn><mo>&times;</mo><mfrac><mn>22</mn><mrow><mn>7</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>22</mn><mo>&nbsp;</mo><mi>c</mi><mi>m</mi><mo>&rArr;</mo><mn>0</mn><mo>.</mo><mn>22</mn><mo>&nbsp;</mo><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi></math></p>\r\n<p><span style=\"font-family: Roboto;\">Total distance covered by cycle =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>36</mn><mo>.</mo><mn>3</mn><mo>&times;</mo><mfrac><mn>75</mn><mn>60</mn></mfrac><mo>=</mo><mn>45375</mn><mo>&nbsp;</mo><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi></math></p>\r\n<p><span style=\"font-family: Roboto;\">therefore , total revolution by the wheel = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45375</mn><mrow><mn>0</mn><mo>.</mo><mn>22</mn></mrow></mfrac><mo>&rArr;</mo></math></span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> 2,06,250</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">75.(b)</span></p>\r\n<p><span style=\"font-family: Roboto;\">&#2346;&#2361;&#2367;&#2319; &#2325;&#2366; &#2319;&#2325; &#2330;&#2325;&#2381;&#2325;&#2352; &nbsp;</span><span style=\"font-family: Roboto;\">= </span><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">&pi;</mi><mi>r</mi><mo>)</mo><mo>=</mo><mn>2</mn><mo>&times;</mo><mfrac><mn>22</mn><mrow><mn>7</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>22</mn><mo>&nbsp;</mo><mi>c</mi><mi>m</mi><mo>&rArr;</mo><mn>0</mn><mo>.</mo><mn>22</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2350;&#2368;&#2335;&#2352;</mi></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2366;&#2311;&#2325;&#2367;&#2354; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2340;&#2351; &#2325;&#2368; &#2327;&#2312; &#2325;&#2369;&#2354; &#2342;&#2370;&#2352;&#2368; = </span><span style=\"font-family: Palanquin Dark;\"> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>36</mn><mo>.</mo><mn>3</mn><mo>&times;</mo><mfrac><mn>75</mn><mn>60</mn></mfrac><mo>=</mo><mn>45375</mn><mo>&nbsp;</mo><mi>&#2350;&#2368;&#2335;&#2352;</mi></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2346;&#2361;&#2367;&#2351;&#2375; &#2325;&#2366; &#2325;&#2369;&#2354; &#2330;&#2325;&#2381;&#2325;&#2352; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45375</mn><mrow><mn>0</mn><mo>.</mo><mn>22</mn></mrow></mfrac><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> 2,06,250</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "18",
                    question_en: "<p>76.<span style=\"font-family: Roboto;\"> Which of the following statements is NOT correct regarding the rabi crops? </span></p>\n",
                    question_hi: "<p>76.<span style=\"font-family: Palanquin;\">&#2352;&#2348;&#2368; &#2325;&#2368; &#2347;&#2360;&#2354; &#2325;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343; &#2350;&#2375;&#2306; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344;-&#2360;&#2366; &#2325;&#2341;&#2344; &#2360;&#2361;&#2368; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Southern and southeastern states are important for the production of wheat and other rabi crops.</p>\n", "<p>These crops are harvested in summer from April to June.</p>\n", 
                                "<p>Some of the important rabi crops are wheat, barley, peas, gram and mustard.</p>\n", "<p>Rabi crops are sown in winter from October to December.</p>\n"],
                    options_hi: ["<p>&#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2324;&#2352; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2346;&#2370;&#2352;&#2381;&#2357;&#2368; &#2352;&#2366;&#2332;&#2381;&#2351; &#2327;&#2375;&#2361;&#2370;&#2305; &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2352;&#2348;&#2368; &#2347;&#2360;&#2354;&#2379;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2350;&#2361;&#2340;&#2381;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2352;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;&#2306;&#2404;</p>\n", "<p>&#2311;&#2344; &#2347;&#2360;&#2354;&#2379;&#2306; &#2325;&#2379; &#2327;&#2381;&#2352;&#2368;&#2359;&#2381;&#2350; &#2315;&#2340;&#2369; &#2350;&#2375;&#2306; &#2309;&#2346;&#2381;&#2352;&#2376;&#2354; &#2360;&#2375; &#2332;&#2370;&#2344; &#2325;&#2375; &#2350;&#2343;&#2381;&#2351; &#2325;&#2366;&#2335;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                                "<p>&#2327;&#2375;&#2361;&#2370;&#2305;, &#2332;&#2380;, &#2350;&#2335;&#2352;, &#2330;&#2344;&#2366; &#2324;&#2352; &#2360;&#2352;&#2360;&#2379;&#2306; &#2325;&#2369;&#2331; &#2350;&#2369;&#2326;&#2381;&#2351; &#2352;&#2348;&#2368; &#2347;&#2360;&#2354;&#2375;&#2306; &#2361;&#2376;&#2306;&#2404;.</p>\n", "<p>&#2352;&#2348;&#2368; &#2347;&#2360;&#2354;&#2379;&#2306; &#2325;&#2379; &#2358;&#2368;&#2340; &#2315;&#2340;&#2369; &#2350;&#2375;&#2306; &#2309;&#2325;&#2381;&#2340;&#2370;&#2348;&#2352; &#2360;&#2375; &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; &#2325;&#2375; &#2350;&#2343;&#2381;&#2351; &#2348;&#2379;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">76.(a)</span><span style=\"font-family: Roboto;\">&nbsp;</span><strong>Option (a) is correct.</strong><span style=\"font-weight: 400;\"> </span><strong>Rabi crops</strong><span style=\"font-weight: 400;\"> - Sown in October to December and harvested in April to June. Example - wheat, barley, peas, gram and mustard. </span><strong>Kharif crops </strong><span style=\"font-weight: 400;\">- Sown in June to July and harvested in September to October. Example - Paddy, maize, bajra, jowar. </span><strong>Zaid crops </strong><span style=\"font-weight: 400;\">- Sown in March and harvested by June end. Example - melon, pepper, tomato.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Palanquin Dark;\">76.(a)&nbsp;</span><strong>&#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (a) &#2360;&#2361;&#2368; &#2361;&#2376;&#2404; &#2352;&#2348;&#2368; &#2347;&#2360;&#2354;&#2375;&#2306; - </strong><span style=\"font-weight: 400;\">&#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352; &#2360;&#2375; &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; &#2350;&#2375;&#2306; &#2348;&#2379;&#2312; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2309;&#2346;&#2381;&#2352;&#2376;&#2354; &#2360;&#2375; &#2332;&#2370;&#2344; &#2350;&#2375;&#2306; &#2325;&#2366;&#2335;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2327;&#2375;&#2361;&#2370;&#2305;, &#2332;&#2380;, &#2350;&#2335;&#2352;, &#2330;&#2344;&#2366; &#2324;&#2352; &#2360;&#2352;&#2360;&#2379;&#2306;&#2404; </span><strong>&#2326;&#2352;&#2368;&#2347; &#2347;&#2360;&#2354;&#2375;&#2306;</strong><span style=\"font-weight: 400;\"> - &#2332;&#2370;&#2344; &#2360;&#2375; &#2332;&#2369;&#2354;&#2366;&#2312; &#2350;&#2375;&#2306; &#2348;&#2379;&#2312; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2360;&#2367;&#2340;&#2306;&#2348;&#2352; &#2360;&#2375; &#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352; &#2350;&#2375;&#2306; &#2325;&#2366;&#2335;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2343;&#2366;&#2344;, &#2350;&#2325;&#2381;&#2325;&#2366;, &#2348;&#2366;&#2332;&#2352;&#2366;, &#2332;&#2381;&#2357;&#2366;&#2352;&#2404; </span><strong>&#2332;&#2366;&#2351;&#2342; &#2325;&#2368; &#2347;&#2360;&#2354;&#2375;&#2306; </strong><span style=\"font-weight: 400;\">- &#2350;&#2366;&#2352;&#2381;&#2330; &#2350;&#2375;&#2306; &#2348;&#2379;&#2312; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2332;&#2370;&#2344; &#2325;&#2375; &#2309;&#2306;&#2340; &#2340;&#2325; &#2325;&#2366;&#2335;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2326;&#2352;&#2348;&#2370;&#2332;&#2366;, &#2325;&#2366;&#2354;&#2368; &#2350;&#2367;&#2352;&#2381;&#2330;, &#2335;&#2350;&#2366;&#2335;&#2352;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "18",
                    question_en: "<p>77.<span style=\"font-family: Roboto;\"> Which of the following pairs is correct regarding the East India Company army?</span></p>\r\n<p><span style=\"font-family: Roboto;\"> I. Sawar &mdash; Men on horses</span></p>\r\n<p><span style=\"font-family: Roboto;\"> Il. Musket &mdash; A heavy gun used by infantry soldiers </span></p>\n",
                    question_hi: "<p>77.<span style=\"font-family: Palanquin;\"> &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2368; &#2360;&#2375;&#2344;&#2366; &#2325;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343; &#2350;&#2375;&#2306; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2351;&#2369;&#2327;&#2381;&#2350; &#2360;&#2361;&#2368; &#2361;&#2376;?</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2404;. &#2360;&#2357;&#2366;&#2352; &mdash; &#2328;&#2369;&#2337;&#2364;&#2360;&#2357;&#2366;&#2352;</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2405;. &#2350;&#2360;&#2381;&#2325;&#2335; &ndash; &#2346;&#2376;&#2342;&#2354; &#2360;&#2375;&#2344;&#2366; &#2325;&#2375; &#2360;&#2376;&#2344;&#2367;&#2325;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354; &#2325;&#2368; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2349;&#2366;&#2352;&#2368; &#2348;&#2306;&#2342;&#2370;&#2325;</span></p>\n",
                    options_en: ["<p>Only l</p>\n", "<p>Only ll</p>\n", 
                                "<p>Both l and lI</p>\n", "<p>Neither I nor Il</p>\n"],
                    options_hi: ["<p>&#2325;&#2375;&#2357;&#2354; &#2404;</p>\n", "<p>&#2325;&#2375;&#2357;&#2354; &#2405;</p>\n",
                                "<p>&#2404; &#2324;&#2352; &#2405; &#2342;&#2379;&#2344;&#2379;&#2306;</p>\n", "<p>&#2344; &#2340;&#2379; &#2324;&#2352; &#2344; &#2361;&#2368; &#2405;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">77.(c)&nbsp;</span><strong>Both l and II.&nbsp; </strong><span style=\"font-weight: 400;\">East India Company was established on 31 December 1600 AD. </span><span style=\"font-weight: 400;\">The massive British corporation was founded under Queen Elizabeth I and rose to exploit overseas trade and become a dominating global player. The EIC was the means by which Britain conducted its imperialistic policies in Asia, and it made millions through its global trade in spices, tea, textiles, and opium. The first English factory of India - Masulipatnam (1611). The first English factory of Bengal: Hugli (1651).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Palanquin Dark;\">77.(c)&nbsp;</span><strong>&nbsp;l &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306;</strong><span style=\"font-weight: 400;\">&#2404; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; 31 &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; 1600 &#2312;. &#2325;&#2379; &#2361;&#2369;&#2312; &#2341;&#2368;&#2404; &#2348;&#2337;&#2364;&#2375; &#2346;&#2376;&#2350;&#2366;&#2344;&#2375; &#2346;&#2352; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2344;&#2367;&#2327;&#2350; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2352;&#2366;&#2344;&#2368; &#2319;&#2354;&#2367;&#2332;&#2366;&#2348;&#2375;&#2341; I &#2325;&#2375; &#2340;&#2361;&#2340; &#2325;&#2368; &#2327;&#2312; &#2341;&#2368; &#2324;&#2352; &#2357;&#2367;&#2342;&#2375;&#2358;&#2368; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2325;&#2366; &#2347;&#2366;&#2351;&#2342;&#2366; &#2313;&#2336;&#2366;&#2344;&#2375; &#2324;&#2352; &#2319;&#2325; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368; &#2348;&#2344;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2349;&#2352;&#2366;&#2404; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2381;&#2352;&#2367;&#2335;&#2375;&#2344; &#2344;&#2375; &#2319;&#2358;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2309;&#2346;&#2344;&#2368; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351;&#2357;&#2366;&#2342;&#2368; &#2344;&#2368;&#2340;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2360;&#2306;&#2330;&#2366;&#2354;&#2344; &#2325;&#2367;&#2351;&#2366; &#2324;&#2352; &#2311;&#2360;&#2344;&#2375; &#2350;&#2360;&#2366;&#2354;&#2379;&#2306;, &#2330;&#2366;&#2351;, &#2325;&#2346;&#2337;&#2364;&#2366; &#2324;&#2352; &#2309;&#2347;&#2368;&#2350; &#2350;&#2375;&#2306; &#2309;&#2346;&#2344;&#2375; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2354;&#2366;&#2326;&#2379;&#2306; &#2325;&#2350;&#2366;&#2319;&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2347;&#2376;&#2325;&#2381;&#2335;&#2381;&#2352;&#2368; - &#2350;&#2360;&#2370;&#2354;&#2368;&#2346;&#2335;&#2381;&#2335;&#2344;&#2350; (1611)&#2404; &#2348;&#2306;&#2327;&#2366;&#2354; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2347;&#2376;&#2325;&#2381;&#2335;&#2381;&#2352;&#2368;: &#2361;&#2369;&#2327;&#2354;&#2368; (1651)&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "18",
                    question_en: "<p>78. <span style=\"font-family: Roboto;\">Which of the following is related to the concurrent list of the seventh schedule of the Indian constitution? </span></p>\n",
                    question_hi: "<p>78.<span style=\"font-family: Palanquin;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2368; &#2360;&#2366;&#2340;&#2357;&#2368;&#2306; &#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2368; &#2325;&#2368; &#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368; &#2360;&#2370;&#2330;&#2368; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Taxes on agricultural income</p>\n", "<p>Tolls</p>\n", 
                                "<p>Factories</p>\n", "<p>Taxes on income other than agricultural income.</p>\n"],
                    options_hi: ["<p>&#2325;&#2371;&#2359;&#2367; &#2310;&#2351; &#2346;&#2352; &#2325;&#2352;</p>\n", "<p>&#2335;&#2379;&#2354;</p>\n",
                                "<p>&#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2379;&#2306;</p>\n", "<p>&#2325;&#2371;&#2359;&#2367; &#2310;&#2351; &#2325;&#2375; &#2309;&#2354;&#2366;&#2357;&#2366; &#2309;&#2344;&#2381;&#2351; &#2310;&#2351; &#2346;&#2352; &#2325;&#2352;&#2404;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">78.</span><span style=\"font-family: Roboto;\">(c)</span><span style=\"font-family: Roboto;\">&nbsp;</span><strong>Factories. 7th Schedule of Indian Constitution - Union List Subjects</strong><span style=\"font-weight: 400;\"> - Defence, Army, International, Ports, Railways, Highways, Communication etc. </span><strong>State List Subjects</strong><span style=\"font-weight: 400;\"> - Public order, Police, Public health and sanitation, Hospitals and dispensaries etc. </span><strong>Concurrent List Subjects</strong><span style=\"font-weight: 400;\"> - Education, Forest, Trade, unions Marriage, Adoption, Succession etc. The Union list has 100 subjects, the State list has 61 subjects and the Concurrent list has 52 subjects.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">78.</span><span style=\"font-family: Roboto;\">(c)</span><span style=\"font-family: Roboto;\">&nbsp;</span><strong>&#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2379;&#2306;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2368; 7&#2357;&#2368;&#2306; &#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2368;</strong><span style=\"font-weight: 400;\"> - </span><strong>&#2360;&#2306;&#2328; &#2360;&#2370;&#2330;&#2368; &#2357;&#2367;&#2359;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2352;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2375;&#2344;&#2366;, &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;, &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361;, &#2352;&#2375;&#2354;&#2357;&#2375;, &#2352;&#2366;&#2332;&#2350;&#2366;&#2352;&#2381;&#2327;, &#2360;&#2306;&#2330;&#2366;&#2352; &#2310;&#2342;&#2367;&#2404; </span><strong>&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2370;&#2330;&#2368; &#2357;&#2367;&#2359;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;, &#2346;&#2369;&#2354;&#2367;&#2360;, &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2324;&#2352; &#2360;&#2381;&#2357;&#2330;&#2381;&#2331;&#2340;&#2366;, &#2309;&#2360;&#2381;&#2346;&#2340;&#2366;&#2354; &#2324;&#2352; &#2324;&#2359;&#2343;&#2366;&#2354;&#2351; &#2310;&#2342;&#2367;&#2404; </span><strong>&#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368; &#2360;&#2370;&#2330;&#2368; &#2357;&#2367;&#2359;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2358;&#2367;&#2325;&#2381;&#2359;&#2366;, &#2357;&#2344;, &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;, &#2360;&#2306;&#2328; &#2357;&#2367;&#2357;&#2366;&#2361;, &#2342;&#2340;&#2381;&#2340;&#2325; &#2327;&#2381;&#2352;&#2361;&#2339;, &#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2343;&#2367;&#2325;&#2366;&#2352; &#2310;&#2342;&#2367;&#2404; &#2360;&#2306;&#2328; &#2360;&#2370;&#2330;&#2368; &#2350;&#2375;&#2306; 100 &#2357;&#2367;&#2359;&#2351; &#2361;&#2376;&#2306;, &#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2370;&#2330;&#2368; &#2350;&#2375;&#2306; 61 &#2357;&#2367;&#2359;&#2351; &#2361;&#2376;&#2306; &#2324;&#2352; &#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368; &#2360;&#2370;&#2330;&#2368; &#2350;&#2375;&#2306; 52 &#2357;&#2367;&#2359;&#2351; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "18",
                    question_en: "<p>79. <span style=\"font-family: Roboto;\"> As on 1st November 2022, who is the chairperson of SEBI&rsquo;s Market Data Advisory Committee (MDAC)?</span><span style=\"font-family: Roboto;\"> </span></p>\n",
                    question_hi: "<p>79.<span style=\"font-family: Palanquin;\">1 &#2344;&#2357;&#2306;&#2348;&#2352; 2022 &#2340;&#2325;, &#2360;&#2375;&#2348;&#2368; (SEBI) &#2325;&#2375; &#2348;&#2366;&#2332;&#2364;&#2366;&#2352; &#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306; &#2325;&#2368; &#2360;&#2354;&#2366;&#2361;&#2325;&#2366;&#2352; &#2360;&#2350;&#2367;&#2340;&#2367; (Market Data Advisory Committee (MDAC)) &#2325;&#2375; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; &#2325;&#2380;&#2344; &#2361;&#2376;&#2306;?</span></p>\n",
                    options_en: ["<p>Suresh N Patel</p>\n", "<p>M.S. Sahoo</p>\n", 
                                "<p>K.V. Kamath</p>\n", "<p>Ajay Tyagi</p>\n"],
                    options_hi: ["<p>&#2360;&#2369;&#2352;&#2375;&#2358; &#2319;&#2344; &#2346;&#2335;&#2375;&#2354;</p>\n", "<p>&#2319;&#2350;.&#2319;&#2360;. &#2360;&#2366;&#2361;&#2370;</p>\n",
                                "<p>&#2325;&#2375;.&#2357;&#2368;. &#2325;&#2366;&#2350;&#2341;</p>\n", "<p>&#2309;&#2332;&#2351; &#2340;&#2381;&#2351;&#2366;&#2327;&#2368;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">79.(b)&nbsp;</span><strong>M.S. Sahoo. </strong><span style=\"font-weight: 400;\">The Securities and Exchange Board of India was constituted as a non-statutory body on April 12, 1988 through a resolution of the Government of India. The Securities and Exchange Board of India was established as a statutory body in the year 1992 and the provisions of the Securities and Exchange Board of India Act, 1992 came into force on January 30, 1992. </span><strong>Functions : </strong><span style=\"font-weight: 400;\">To protect the interests of investors in securities and to promote the development of, and to regulate the securities market and for matters connected therewith or incidental thereto.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">79.(b)&nbsp;</span><strong>&#2319;&#2350; &#2319;&#2360; &#2360;&#2366;&#2361;&#2370;&#2404; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2319;&#2325; &#2360;&#2306;&#2325;&#2354;&#2381;&#2346; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; 12 &#2309;&#2346;&#2381;&#2352;&#2376;&#2354;, 1988 &#2325;&#2379; &#2319;&#2325; &#2327;&#2376;&#2352;-&#2360;&#2366;&#2306;&#2357;&#2367;&#2343;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2348;&#2379;&#2352;&#2381;&#2337; &#2325;&#2366; &#2327;&#2336;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2348;&#2379;&#2352;&#2381;&#2337; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2357;&#2352;&#2381;&#2359; 1992 &#2350;&#2375;&#2306; &#2319;&#2325; &#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2361;&#2369;&#2312; &#2341;&#2368; &#2324;&#2352; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2348;&#2379;&#2352;&#2381;&#2337; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 1992 &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344; 30 &#2332;&#2344;&#2357;&#2352;&#2368;, 1992 &#2325;&#2379; &#2354;&#2366;&#2327;&#2370; &#2361;&#2369;&#2319;&#2404; </span><strong>&#2325;&#2366;&#2352;&#2381;&#2351;</strong><span style=\"font-weight: 400;\">: &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2344;&#2367;&#2357;&#2375;&#2358;&#2325;&#2379;&#2306; &#2325;&#2375; &#2361;&#2367;&#2340;&#2379;&#2306; &#2325;&#2368; &#2352;&#2325;&#2381;&#2359;&#2366; &#2325;&#2352;&#2344;&#2366; &#2324;&#2352; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2348;&#2366;&#2332;&#2366;&#2352; &#2325;&#2375; &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2379; &#2348;&#2338;&#2364;&#2366;&#2357;&#2366; &#2342;&#2375;&#2344;&#2375; &#2324;&#2352; &#2313;&#2360;&#2375; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2324;&#2352; &#2313;&#2360;&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2351;&#2366; &#2346;&#2381;&#2352;&#2366;&#2360;&#2306;&#2327;&#2367;&#2325; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "18",
                    question_en: "<p>80. <span style=\"font-family: Roboto;\">An isotope of which of the following is used in the treatment of cancer?</span><span style=\"font-family: Roboto;\"> </span></p>\n",
                    question_hi: "<p>80. <span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2325;&#2375; &#2360;&#2350;&#2360;&#2381;&#2341;&#2366;&#2344;&#2367;&#2325; ( isotope ) &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2376;&#2306;&#2360;&#2352; &#2325;&#2375; &#2313;&#2346;&#2330;&#2366;&#2352; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Cobalt</p>\n", "<p>Aluminum</p>\n", 
                                "<p>Nickel</p>\n", "<p>Iron</p>\n"],
                    options_hi: ["<p>&#2325;&#2379;&#2348;&#2366;&#2354;&#2381;&#2335;</p>\n", "<p>&#2319;&#2354;&#2381;&#2351;&#2370;&#2350;&#2368;&#2344;&#2367;&#2351;&#2350;</p>\n",
                                "<p><span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2325;&#2375;&#2354;</span></p>\n", "<p>&#2354;&#2379;&#2361;&#2366;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">80.</span><span style=\"font-family: Roboto;\">(a) </span><strong>Cobalt. </strong><span style=\"font-weight: 400;\">Cobalt (27) is used in the treatment of cancer. </span><strong>&nbsp;Isotopes -</strong><span style=\"font-weight: 400;\"> Same atomic number but different mass numbers. </span><strong>Example </strong><span style=\"font-weight: 400;\">- Isotopes of hydrogen - protium <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>1</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts></math></span><span style=\"font-weight: 400;\">, deuterium </span><span style=\"font-weight: 400;\"> or&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>2</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts><mo>&nbsp;</mo></math> and&nbsp; tritium <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>3</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts></math></span><span style=\"font-weight: 400;\">&nbsp;. </span><strong>Isobars </strong><span style=\"font-weight: 400;\">are atoms of different elements with different atomic numbers but have the same mass number. </span><strong>Example </strong><span style=\"font-weight: 400;\">of a pair of isobar i.e.,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi>Ca</mi><mn>20</mn><none></none><mprescripts></mprescripts><none></none><mn>40</mn></mmultiscripts></math></span><span style=\"font-weight: 400;\"> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi>Ar</mi><mn>18</mn><none></none><mprescripts></mprescripts><none></none><mn>40</mn></mmultiscripts></math></span><span style=\"font-weight: 400;\">.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">80.</span><span style=\"font-family: Palanquin;\">(a)&nbsp;</span><strong>&#2325;&#2379;&#2348;&#2366;&#2354;&#2381;&#2335;</strong><span style=\"font-weight: 400;\">&#2404; &#2325;&#2379;&#2348;&#2366;&#2354;&#2381;&#2335; 27 &#2325;&#2366; &#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354; &#2325;&#2376;&#2306;&#2360;&#2352; &#2325;&#2375; &#2311;&#2354;&#2366;&#2332; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2350;&#2360;&#2381;&#2341;&#2366;&#2344;&#2367;&#2325; </strong><span style=\"font-weight: 400;\">- &#2360;&#2350;&#2366;&#2344; &#2346;&#2352;&#2350;&#2366;&#2339;&#2369; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2354;&#2375;&#2325;&#2367;&#2344; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;&#2404; </span><strong>&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; </strong><span style=\"font-weight: 400;\">- &#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2332;&#2344; &#2325;&#2375; &#2360;&#2350;&#2360;&#2381;&#2341;&#2366;&#2344;&#2367;&#2325; - &#2346;&#2381;&#2352;&#2379;&#2335;&#2367;&#2351;&#2350; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>1</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts></math></span><span style=\"font-weight: 400;\">, &#2337;&#2381;&#2351;&#2370;&#2335;&#2375;&#2352;&#2367;&#2351;&#2350; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>2</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts><mo>&nbsp;</mo></math></span><span style=\"font-weight: 400;\">&#2351;&#2366; &#2335;&#2381;&#2352;&#2367;&#2335;&#2367;&#2351;&#2350; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>3</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts></math></span><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2360;&#2350;&#2349;&#2366;&#2352;&#2367;&#2325; </strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2340;&#2340;&#2381;&#2357;&#2379;&#2306; &#2325;&#2375; &#2346;&#2352;&#2350;&#2366;&#2339;&#2369; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306; &#2332;&#2367;&#2344;&#2325;&#2368; &#2346;&#2352;&#2350;&#2366;&#2339;&#2369; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2349;&#2367;&#2344;&#2381;&#2344; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376; &#2354;&#2375;&#2325;&#2367;&#2344; &#2313;&#2344;&#2325;&#2368; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2360;&#2350;&#2366;&#2344; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; &#2360;&#2350;&#2349;&#2366;&#2352;&#2367;&#2325; &#2325;&#2375; &#2351;&#2369;&#2327;&#2381;&#2350; &#2325;&#2366; </span><strong>&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; </strong><span style=\"font-weight: 400;\">&#2309;&#2352;&#2381;&#2341;&#2366;&#2340;&#2381; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mmultiscripts><mi>Ca</mi><mn>20</mn><none></none><mprescripts></mprescripts><none></none><mn>40</mn></mmultiscripts></math></span><span style=\"font-weight: 400;\">&#2324;&#2352;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi>Ar</mi><mn>18</mn><none></none><mprescripts></mprescripts><none></none><mn>40</mn></mmultiscripts></math> </span><span style=\"font-weight: 400;\">&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "18",
                    question_en: "<p>81.<span style=\"font-family: Roboto;\"> How many non-major (minor) ports are there in India as on 31st October 2022?</span><span style=\"font-family: Roboto;\"> </span></p>\n",
                    question_hi: "<p>81.<span style=\"font-family: Palanquin;\"> 31 &#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352; 2022 &#2325;&#2368; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2375; &#2327;&#2376;&#2352;-&#2346;&#2381;&#2352;&#2350;&#2369;&#2326; (&#2354;&#2328;&#2369;) &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2361;&#2376;&#2306;?</span></p>\n",
                    options_en: ["<p>154</p>\n", "<p>168</p>\n", 
                                "<p>200</p>\n", "<p>246</p>\n"],
                    options_hi: ["<p>154</p>\n", "<p>168</p>\n",
                                "<p>200</p>\n", "<p>246</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">81.</span><span style=\"font-family: Roboto;\">(c) <strong>200.</strong>&nbsp;</span><span style=\"font-weight: 400;\">India has 13 </span><strong>major ports</strong><span style=\"font-weight: 400;\"> and more than 200 </span><strong>notified minor </strong><span style=\"font-weight: 400;\">and intermediate ports. </span><strong>Kolkata Port </strong><span style=\"font-weight: 400;\">is the only riverine port in India. </span><strong>Mumbai Port</strong><span style=\"font-weight: 400;\"> is the biggest port in India in terms of size and shipping traffic.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">81.</span><span style=\"font-family: Palanquin;\">(c)&nbsp;</span><strong>200 </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; </span><strong>13 &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; </strong><span style=\"font-weight: 400;\">&#2324;&#2352;</span><strong> 200</strong><span style=\"font-weight: 400;\"> &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2309;&#2343;&#2367;&#2360;&#2370;&#2330;&#2367;&#2340; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2357;&#2352;&#2381;&#2340;&#2368; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2361;&#2376;&#2306;&#2404; &#2325;&#2379;&#2354;&#2325;&#2366;&#2340;&#2366; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; </span><strong>&#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; </strong><span style=\"font-weight: 400;\">&#2344;&#2342;&#2368; &#2325;&#2366; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2361;&#2376;&#2404; &#2350;&#2369;&#2306;&#2348;&#2312; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; </span><strong>&#2310;&#2325;&#2366;&#2352; </strong><span style=\"font-weight: 400;\">&#2324;&#2352; </span><strong>&#2358;&#2367;&#2346;&#2367;&#2306;&#2327; </strong><span style=\"font-weight: 400;\">&#2351;&#2366;&#2340;&#2366;&#2351;&#2366;&#2340; &#2325;&#2375; &#2350;&#2366;&#2350;&#2354;&#2375; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "18",
                    question_en: "<p>82. <span style=\"font-family: Roboto;\">Who won the best actress award in the NEXA International Indian Film Academy Awards 2022? </span></p>\n",
                    question_hi: "<p>82. <span style=\"font-family: Palanquin;\">NEXA &#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2358;&#2344;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2344; &#2347;&#2367;&#2354;&#2381;&#2350; &#2319;&#2325;&#2375;&#2337;&#2350;&#2368; &#2309;&#2357;&#2366;&#2352;&#2381;&#2337;&#2381;&#2360; 2022 &#2350;&#2375;&#2306; &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2381;&#2352;&#2368; &#2325;&#2366; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2325;&#2367;&#2360;&#2344;&#2375; &#2332;&#2368;&#2340;&#2366;?</span></p>\n",
                    options_en: ["<p>Kriti Sanon</p>\n", "<p>Jhanvi Kapoor</p>\n", 
                                "<p>Alia Bhatt</p>\n", "<p>Deepika Padukone</p>\n"],
                    options_hi: ["<p>&#2325;&#2371;&#2340;&#2367; &#2360;&#2375;&#2344;&#2344;</p>\n", "<p>&#2332;&#2366;&#2361;&#2381;&#2344;&#2357;&#2368; &#2325;&#2346;&#2370;&#2352;</p>\n",
                                "<p>&#2310;&#2354;&#2367;&#2351;&#2366; &#2349;&#2335;&#2381;&#2335;</p>\n", "<p>&#2342;&#2368;&#2346;&#2367;&#2325;&#2366; &#2346;&#2366;&#2342;&#2369;&#2325;&#2379;&#2339;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">82.</span><span style=\"font-family: Roboto;\">(a)&nbsp;</span><strong>Kriti Sanon. Nexa iifa awards 2022&nbsp; : -&nbsp; Best Picture -</strong><span style=\"font-weight: 400;\"> Shershah, </span><strong>&nbsp;Best Direction -</strong><span style=\"font-weight: 400;\"> Vishnuvardhan (Shershaah),</span><strong>&nbsp; Best Actor - </strong><span style=\"font-weight: 400;\">Vicky Kaushal (Sardar Udham),</span><strong>&nbsp; Best Actress - </strong><span style=\"font-weight: 400;\">Kriti Sanon (Mimi), </span><strong>&nbsp;Best Male Playback Singer -</strong><span style=\"font-weight: 400;\"> Jubin Nautiyal ( \"Rattan Lamiyan\"), &nbsp; </span><strong>Best Female Playback Singer -</strong><span style=\"font-weight: 400;\"> Asees Kaur.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">82.</span><span style=\"font-family: Palanquin;\">(a)&nbsp;</span><strong>&#2325;&#2371;&#2340;&#2367; &#2360;&#2375;&#2344;&#2344;&#2404; </strong><span style=\"font-weight: 400;\">&#2344;&#2375;&#2325;&#2381;&#2360;&#2366;(Nexa ) IIFA&#2309;&#2357;&#2366;&#2352;&#2381;&#2337;&#2381;&#2360; 2022: - </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2346;&#2367;&#2325;&#2381;&#2330;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2358;&#2375;&#2352;&#2358;&#2366;&#2361;, </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2337;&#2366;&#2351;&#2352;&#2375;&#2325;&#2381;&#2358;&#2344;</strong><span style=\"font-weight: 400;\"> - &#2357;&#2367;&#2359;&#2381;&#2339;&#2369;&#2357;&#2352;&#2381;&#2343;&#2344; (&#2358;&#2375;&#2352;&#2358;&#2366;&#2361;), </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2357;&#2367;&#2325;&#2381;&#2325;&#2368; &#2325;&#2380;&#2358;&#2354; (&#2360;&#2352;&#2342;&#2366;&#2352; &#2313;&#2343;&#2350;), </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2381;&#2352;&#2368;</strong><span style=\"font-weight: 400;\"> - &#2325;&#2371;&#2340;&#2367; &#2360;&#2375;&#2344;&#2344; (&#2350;&#2367;&#2350;&#2368;), </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2350;&#2375;&#2354; &#2346;&#2381;&#2354;&#2375;&#2348;&#2376;&#2325; &#2360;&#2367;&#2306;&#2327;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2332;&#2369;&#2348;&#2367;&#2344; &#2344;&#2380;&#2335;&#2367;&#2351;&#2366;&#2354; (\"&#2352;&#2340;&#2344; &#2354;&#2366;&#2350;&#2367;&#2351;&#2366;&#2344;\" ), </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2347;&#2368;&#2350;&#2375;&#2354; &#2346;&#2381;&#2354;&#2375;&#2348;&#2376;&#2325; &#2360;&#2367;&#2306;&#2327;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2309;&#2360;&#2368;&#2360; &#2325;&#2380;&#2352;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "18",
                    question_en: "<p>83.<span style=\"font-family: Roboto;\"> Indian Army&rsquo;s 1st women combat aviator is ___________ . </span></p>\n",
                    question_hi: "<p>83. <span style=\"font-family: Palanquin;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2375;&#2344;&#2366; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2350;&#2361;&#2367;&#2354;&#2366; &#2354;&#2337;&#2364;&#2366;&#2325;&#2370; &#2357;&#2367;&#2350;&#2366;&#2344;&#2357;&#2366;&#2361;&#2325; (combat aviator) _________ &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Punita Arora</p>\n", "<p>Divya Ajit Kumar</p>\n", 
                                "<p>Abhilasha Barak</p>\n", "<p>Priya Semwal</p>\n"],
                    options_hi: ["<p>&#2346;&#2369;&#2344;&#2368;&#2340;&#2366; &#2309;&#2352;&#2379;&#2337;&#2364;&#2366;</p>\n", "<p>&#2342;&#2367;&#2357;&#2381;&#2351;&#2366; &#2309;&#2332;&#2367;&#2340; &#2325;&#2369;&#2350;&#2366;&#2352;</p>\n",
                                "<p>&#2309;&#2349;&#2367;&#2354;&#2366;&#2359;&#2366; &#2348;&#2352;&#2366;&#2325;</p>\n", "<p>&#2346;&#2381;&#2352;&#2367;&#2351;&#2366; &#2360;&#2375;&#2350;&#2357;&#2366;&#2354;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">83.</span><span style=\"font-family: Roboto;\">(c)&nbsp;</span><strong>Abhilasha Barak.&nbsp; </strong><span style=\"font-weight: 400;\">The Army Aviation Corps is a form of the Army that was formed in November 1986.&nbsp; </span><strong>Punita Arora</strong><span style=\"font-weight: 400;\"> was the first woman Lieutenant General of the Indian Army. She held the ranks of Lieutenant General in the Indian Army and Surgeon Vice Admiral in the Navy.</span><strong> Divya Ajit Kumar</strong><span style=\"font-weight: 400;\"> is the first woman to be conferred by the Army with the Sword of Honour.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">83.</span><span style=\"font-family: Palanquin;\">(c)&nbsp;</span><strong>&#2309;&#2349;&#2367;&#2354;&#2366;&#2359;&#2366; &#2348;&#2352;&#2366;&#2325;&#2404; </strong><span style=\"font-weight: 400;\">&#2310;&#2352;&#2381;&#2350;&#2368; &#2319;&#2357;&#2367;&#2319;&#2358;&#2344; &#2325;&#2377;&#2352;&#2381;&#2346;&#2381;&#2360; &#2360;&#2375;&#2344;&#2366; &#2325;&#2366; &#2319;&#2325; &#2352;&#2370;&#2346; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2327;&#2336;&#2344; &#2344;&#2357;&#2306;&#2348;&#2352; 1986 &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404;&nbsp; </span><strong>&#2346;&#2369;&#2344;&#2368;&#2340;&#2366; &#2309;&#2352;&#2379;&#2337;&#2364;&#2366; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2375;&#2344;&#2366; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2350;&#2361;&#2367;&#2354;&#2366; &#2354;&#2375;&#2347;&#2381;&#2335;&#2367;&#2344;&#2375;&#2306;&#2335; &#2332;&#2344;&#2352;&#2354; &#2341;&#2368;&#2306;&#2404; &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2375;&#2344;&#2366; &#2350;&#2375;&#2306; &#2354;&#2375;&#2347;&#2381;&#2335;&#2367;&#2344;&#2375;&#2306;&#2335; &#2332;&#2344;&#2352;&#2354; &#2324;&#2352; &#2344;&#2380;&#2360;&#2375;&#2344;&#2366; &#2350;&#2375;&#2306; &#2360;&#2352;&#2381;&#2332;&#2344; &#2357;&#2366;&#2311;&#2360; &#2319;&#2337;&#2350;&#2367;&#2352;&#2354; &#2325;&#2366; &#2346;&#2342; &#2360;&#2306;&#2349;&#2366;&#2354;&#2366;&#2404; </span><strong>&#2342;&#2367;&#2357;&#2381;&#2351;&#2366; &#2309;&#2332;&#2368;&#2340; &#2325;&#2369;&#2350;&#2366;&#2352; </strong><span style=\"font-weight: 400;\">&#2360;&#2375;&#2344;&#2366; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2381;&#2357;&#2377;&#2352;&#2381;&#2337; &#2321;&#2347; &#2321;&#2344;&#2352; &#2360;&#2375; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2346;&#2361;&#2354;&#2368; &#2350;&#2361;&#2367;&#2354;&#2366; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "18",
                    question_en: "<p>84. <span style=\"font-family: Roboto;\">Raja Parba, also known as Mithuna Sankranti, is a ____________-day-long festival of womanhood celebrated in Odisha, India. </span></p>\n",
                    question_hi: "<p>84. <span style=\"font-family: Palanquin;\">&#2352;&#2366;&#2332;&#2366; &#2346;&#2352;&#2348;&#2366; (Raja Parba), &#2332;&#2367;&#2360;&#2375; &#2350;&#2367;&#2341;&#2369;&#2344; &#2360;&#2306;&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2344;&#2366;&#2350; &#2360;&#2375; &#2349;&#2368; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2313;&#2337;&#2364;&#2368;&#2360;&#2366; &#2350;&#2375;&#2306; &#2350;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; ______________ &#2342;&#2367;&#2344; &#2330;&#2354;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2344;&#2366;&#2352;&#2368;&#2340;&#2381;&#2357; &#2325;&#2366; &#2340;&#2381;&#2351;&#2379;&#2361;&#2366;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>five</p>\n", "<p>six</p>\n", 
                                "<p>four</p>\n", "<p>three</p>\n"],
                    options_hi: ["<p>&#2346;&#2366;&#2305;&#2330;</p>\n", "<p>&#2331;&#2361;</p>\n",
                                "<p>&#2330;&#2366;&#2352;</p>\n", "<p>&#2340;&#2368;&#2344;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">84.</span><span style=\"font-family: Roboto;\">(d)&nbsp;</span><strong>three.</strong><span style=\"font-weight: 400;\"> </span><strong>&nbsp;Raja Parba</strong><strong> </strong><strong>festival </strong><strong>(Odisha)</strong><strong> - </strong><span style=\"font-weight: 400;\">The first day is called Pahili Raja, the second day is Mithun Sankranti, the third day is called Bhoodah or Basi Raja.&nbsp; </span><strong>Other festivals of Odisha - </strong><span style=\"font-weight: 400;\">Ratha Jatra, Magha Saptami, Makara Mela, Chhau festival, Puri Beach festival, Naukhai, Chatar Jatra, Durga Puja, Kalinga Mahotsav, Chandan Yatra,Konark Dance festival, Mahabisuva Sankranti &#2404;&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">84.</span><span style=\"font-family: Roboto;\">(d)&nbsp;</span><strong>&#2340;&#2368;&#2344;&#2404; &#2352;&#2366;&#2332;&#2366; &#2346;&#2352;&#2348;&#2366; &#2351;&#2366;&nbsp; &#2352;&#2332; &#2346;&#2352;&#2381;&#2357; &#2313;&#2340;&#2381;&#2360;&#2357;</strong><span style=\"font-weight: 400;\"> </span><strong>(&#2323;&#2337;&#2367;&#2358;&#2366;)</strong><span style=\"font-weight: 400;\"> - &#2346;&#2361;&#2354;&#2375; &#2342;&#2367;&#2344; &#2325;&#2379; &#2346;&#2361;&#2367;&#2354;&#2368; &#2352;&#2332; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2342;&#2370;&#2360;&#2352;&#2375; &#2342;&#2367;&#2344; &#2325;&#2379; &#2350;&#2367;&#2341;&#2369;&#2344; &#2360;&#2306;&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;, &#2340;&#2368;&#2360;&#2352;&#2375; &#2342;&#2367;&#2344; &#2325;&#2379; &#2349;&#2370;&#2342;&#2366;&#2361; &#2351;&#2366; &#2348;&#2360;&#2368; &#2352;&#2366;&#2332;&#2366; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2323;&#2337;&#2367;&#2358;&#2366; &#2325;&#2375; &#2309;&#2344;&#2381;&#2351; &#2340;&#2381;&#2351;&#2380;&#2361;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2352;&#2341; &#2332;&#2366;&#2340;&#2381;&#2352;&#2366;, &#2350;&#2366;&#2328; &#2360;&#2346;&#2381;&#2340;&#2350;&#2368;, &#2350;&#2325;&#2352; &#2350;&#2375;&#2354;&#2366;, &#2331;&#2314; &#2313;&#2340;&#2381;&#2360;&#2357;, &#2346;&#2369;&#2352;&#2368; &#2348;&#2368;&#2330; &#2313;&#2340;&#2381;&#2360;&#2357;, &#2344;&#2380;&#2326;&#2366;&#2312;, &#2330;&#2340;&#2352; &#2332;&#2366;&#2340;&#2381;&#2352;&#2366;, &#2342;&#2369;&#2352;&#2381;&#2327;&#2366; &#2346;&#2370;&#2332;&#2366;, &#2325;&#2354;&#2367;&#2306;&#2327; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2330;&#2306;&#2342;&#2344; &#2351;&#2366;&#2340;&#2381;&#2352;&#2366;, &#2325;&#2379;&#2339;&#2366;&#2352;&#2381;&#2325; &#2344;&#2371;&#2340;&#2381;&#2351; &#2313;&#2340;&#2381;&#2360;&#2357;, &#2350;&#2361;&#2366;&#2348;&#2367;&#2360;&#2369;&#2357;&#2366; &#2360;&#2306;&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2404;&nbsp;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "18",
                    question_en: "<p>85.<span style=\"font-family: Roboto;\"> Which of the following statements is correct regarding inertia? </span></p>\r\n<p><span style=\"font-family: Roboto;\">I. Inertia is the natural tendency of an object to resist a change in its state of motion or of rest. </span></p>\r\n<p><span style=\"font-family: Roboto;\">II. The mass of an object is a measure of its inertia. </span></p>\n",
                    question_hi: "<p>85.<span style=\"font-family: Palanquin;\"> &#2332;&#2337;&#2364;&#2340;&#2381;&#2357; &#2325;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343; &#2350;&#2375;&#2306; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366;/&#2360;&#2375; &#2325;&#2341;&#2344; &#2360;&#2361;&#2368; &#2361;&#2376;/&#2361;&#2376;&#2306;?</span></p>\r\n<p><span style=\"font-family: Palanquin;\"> l .&#2325;&#2367;&#2360;&#2368; &#2349;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2332;&#2337;&#2364;&#2340;&#2381;&#2357; &#2313;&#2360;&#2325;&#2366; &#2357;&#2361; &#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325; &#2327;&#2369;&#2339; &#2361;&#2376;, &#2332;&#2379; &#2313;&#2360;&#2325;&#2368; &#2357;&#2367;&#2352;&#2366;&#2350; &#2351;&#2366; &#2327;&#2340;&#2367; &#2325;&#2368; &#2309;&#2357;&#2360;&#2381;&#2341;&#2366; &#2350;&#2375;&#2306; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2325;&#2366; &#2357;&#2367;&#2352;&#2379;&#2343; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2405;. &#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; &#2313;&#2360;&#2325;&#2375; &#2332;&#2337;&#2364;&#2340;&#2381;&#2357; &#2325;&#2368; &#2350;&#2366;&#2346; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Only l</p>\n", "<p>Neither I nor Il</p>\n", 
                                "<p>Onlyll</p>\n", "<p>Both l and Il</p>\n"],
                    options_hi: ["<p>&#2325;&#2375;&#2357;&#2354; &#2404;</p>\n", "<p>&#2344; &#2340;&#2379; &#2324;&#2352; &#2344; &#2361;&#2368; &#2405;</p>\n",
                                "<p>&#2325;&#2375;&#2357;&#2354; &#2405;</p>\n", "<p>l &#2324;&#2352; &#2405; &#2342;&#2379;&#2344;&#2379;&#2306;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">85.</span><span style=\"font-family: Roboto;\">(d)&nbsp;</span><strong>Both l and Il. Newton\'s first law (Law of Inertia) - </strong><span style=\"font-weight: 400;\">Inertia is the natural tendency of an object to resist a change in its state of motion or of rest. The mass of an object is a measure of its inertia. </span><strong>Second law of motion -</strong><span style=\"font-weight: 400;\"> Force is equal to the rate of change of momentum. When the mass is constant, the force (F) on the body equals mass (m) times acceleration (a) i.e, F = ma. </span><strong>Third law of motion -</strong><span style=\"font-weight: 400;\"> When two objects interact, they apply forces to each other of equal magnitude and opposite direction.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">85.</span><span style=\"font-family: Palanquin;\">(d) </span><strong>l&nbsp; &#2324;&#2352; &#2405; &#2342;&#2379;&#2344;&#2379;&#2306; </strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2344;&#2381;&#2351;&#2370;&#2335;&#2344; &#2325;&#2366; &#2346;&#2361;&#2354;&#2366; &#2344;&#2367;&#2351;&#2350; (&#2332;&#2337;&#2364;&#2340;&#2381;&#2357; &#2325;&#2366; &#2344;&#2367;&#2351;&#2350;) - </strong><span style=\"font-weight: 400;\">&#2332;&#2337;&#2364;&#2340;&#2366; &#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2368; &#2309;&#2346;&#2344;&#2368; &#2327;&#2340;&#2367; &#2351;&#2366; &#2310;&#2352;&#2366;&#2350; &#2325;&#2368; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2350;&#2375;&#2306; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2325;&#2366; &#2357;&#2367;&#2352;&#2379;&#2343; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325; &#2346;&#2381;&#2352;&#2357;&#2371;&#2340;&#2381;&#2340;&#2367; &#2361;&#2376;&#2404; &#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; &#2313;&#2360;&#2325;&#2375; &#2332;&#2337;&#2364;&#2340;&#2381;&#2357; &#2325;&#2366; &#2350;&#2366;&#2346; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2327;&#2340;&#2367; &#2325;&#2366; &#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351; &#2344;&#2367;&#2351;&#2350; </strong><span style=\"font-weight: 400;\">- &#2348;&#2354; &#2360;&#2306;&#2357;&#2375;&#2327; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2325;&#2368; &#2342;&#2352; &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2332;&#2348; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; &#2360;&#2381;&#2341;&#2367;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;, &#2340;&#2379; &#2357;&#2360;&#2381;&#2340;&#2369; &#2346;&#2352; &#2348;&#2354; (F) ,&#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; (m) &#2340;&#2341;&#2366;&nbsp; &#2340;&#2381;&#2357;&#2352;&#2339; (a)&#2325;&#2375; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354;&nbsp; &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; &#2309;&#2352;&#2381;&#2341;&#2366;&#2340;,</span><strong> F = ma</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2327;&#2340;&#2367; &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2344;&#2367;&#2351;&#2350;</strong><span style=\"font-weight: 400;\"> - &#2332;&#2348; &#2342;&#2379; &#2357;&#2360;&#2381;&#2340;&#2369;&#2319;&#2305; &#2346;&#2352;&#2360;&#2381;&#2346;&#2352; &#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2306;, &#2340;&#2379; &#2357;&#2375; &#2319;&#2325; &#2342;&#2370;&#2360;&#2352;&#2375; &#2346;&#2352; &#2360;&#2350;&#2366;&#2344; &#2346;&#2352;&#2367;&#2350;&#2366;&#2339; &#2324;&#2352; &#2357;&#2367;&#2346;&#2352;&#2368;&#2340; &#2342;&#2367;&#2358;&#2366; &#2350;&#2375;&#2306; &#2348;&#2354; &#2354;&#2327;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "18",
                    question_en: "<p>86. <span style=\"font-family: Roboto;\">How many gold medals did Maharashtra win in the fourth edition of Khelo India Youth Games? </span></p>\n",
                    question_hi: "<p>86. <span style=\"font-family: Palanquin;\">&#2326;&#2375;&#2354;&#2379; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2351;&#2370;&#2341; &#2327;&#2375;&#2350;&#2381;&#2360; &#2325;&#2375; &#2330;&#2380;&#2341;&#2375; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; &#2350;&#2375;&#2306; &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2344;&#2375; &#2325;&#2367;&#2340;&#2344;&#2375; &#2360;&#2381;&#2357;&#2352;&#2381;&#2339; &#2346;&#2342;&#2325; &#2332;&#2368;&#2340;&#2375; &#2341;&#2375; ?</span></p>\n",
                    options_en: ["<p>41</p>\n", "<p>38</p>\n", 
                                "<p>54</p>\n", "<p>45</p>\n"],
                    options_hi: ["<p>41</p>\n", "<p>38</p>\n",
                                "<p>54</p>\n", "<p>45</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">86.</span><span style=\"font-family: Roboto;\">(d) <strong>45. </strong><span style=\"font-weight: 400;\">The fifth edition of Khelo India Youth Games 2023 was held in Madhya Pradesh from January 30 to February 11, 2023. Maharashtra continues to top the KIYG 2023 medal tally with 56 gold medals, 55 silver and 50 bronze. </span><strong>Khelo India</strong><span style=\"font-weight: 400;\">, which translates to &lsquo;Let&rsquo;s play India&rsquo;, was proposed by the government of India in </span><strong>2017 </strong><span style=\"font-weight: 400;\">to revive India&rsquo;s sporting culture by engaging with children at the grassroots level. The initiative also focused on building better sporting infrastructure and academies across the country for various sports.</span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">86.</span><span style=\"font-family: Roboto;\">(d)&nbsp; </span><strong>45 </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; </strong><span style=\"font-weight: 400;\">&#2326;&#2375;&#2354;&#2379; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2351;&#2370;&#2341; &#2327;&#2375;&#2350;&#2381;&#2360; 2023 &#2325;&#2366; &#2346;&#2366;&#2306;&#2330;&#2357;&#2366;&#2306; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2350;&#2375;&#2306; 30 &#2332;&#2344;&#2357;&#2352;&#2368; &#2360;&#2375; 11 &#2347;&#2352;&#2357;&#2352;&#2368;, 2023 &#2340;&#2325; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; KIYG 2023 &#2346;&#2342;&#2325; &#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; 56 &#2360;&#2381;&#2357;&#2352;&#2381;&#2339; &#2346;&#2342;&#2325;, 55 &#2352;&#2332;&#2340; &#2324;&#2352; 50 &#2325;&#2366;&#2306;&#2360;&#2381;&#2351; &#2325;&#2375; &#2360;&#2366;&#2341; &#2358;&#2368;&#2352;&#2381;&#2359; &#2346;&#2352; &#2348;&#2344;&#2366; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; </span><strong>&#2326;&#2375;&#2354;&#2379; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</strong><span style=\"font-weight: 400;\">, &#2332;&#2367;&#2360;&#2325;&#2366; &#2309;&#2344;&#2369;&#2357;&#2366;&#2342; \'&#2354;&#2375;&#2335;&#2381;&#2360; &#2346;&#2381;&#2354;&#2375; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;\' &#2361;&#2376;, &#2325;&#2379; </span><strong>2017</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2332;&#2350;&#2368;&#2344;&#2368; &#2360;&#2381;&#2340;&#2352; &#2346;&#2352; &#2348;&#2330;&#2381;&#2330;&#2379;&#2306; &#2325;&#2375; &#2360;&#2366;&#2341; &#2332;&#2369;&#2337;&#2364;&#2325;&#2352; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2326;&#2375;&#2354; &#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340;&#2367; &#2325;&#2379; &#2346;&#2369;&#2344;&#2352;&#2381;&#2332;&#2368;&#2357;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2311;&#2360; &#2346;&#2361;&#2354; &#2344;&#2375; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2326;&#2375;&#2354;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2342;&#2375;&#2358; &#2349;&#2352; &#2350;&#2375;&#2306; &#2348;&#2375;&#2361;&#2340;&#2352; &#2326;&#2375;&#2354; &#2310;&#2343;&#2366;&#2352;&#2349;&#2370;&#2340; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2324;&#2352; &#2309;&#2325;&#2366;&#2342;&#2350;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2346;&#2352; &#2349;&#2368; &#2343;&#2381;&#2351;&#2366;&#2344; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "18",
                    question_en: "<p>87. <span style=\"font-family: Roboto;\">Which of the following is NOT a part of western coastal plains? </span></p>\n",
                    question_hi: "<p>87. <span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2340;&#2335;&#2368;&#2351; &#2350;&#2376;&#2342;&#2366;&#2344; &#2325;&#2366; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Konkan</p>\n", "<p>Coromandel Coast</p>\n", 
                                "<p>Kannad Plain</p>\n", "<p>Malabar coast</p>\n"],
                    options_hi: ["<p>&#2325;&#2379;&#2306;&#2325;&#2339;</p>\n", "<p>&#2325;&#2379;&#2352;&#2379;&#2350;&#2306;&#2337;&#2354; &#2340;&#2335;</p>\n",
                                "<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin;\">&#2325;&#2344;&#2381;&#2344;&#2337;&#2364; &#2325;&#2375; &#2340;&#2335;&#2368;&#2351; &#2350;&#2376;&#2342;&#2366;&#2344;</span></p>\n", "<p><span style=\"font-family: Palanquin;\">&#2350;&#2366;&#2354;&#2366;&#2348;&#2366;&#2352; &#2340;&#2335;</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">87.</span><span style=\"font-family: Roboto;\">(b)&nbsp;</span><strong>Coromandel Coast. Part of western coastal plains :-</strong><span style=\"font-weight: 400;\"> It includes the states of Gujarat, Maharashtra, Goa, Karnataka and Kerala .The Northern part of the coast is called the </span><strong>Konkan </strong><span style=\"font-weight: 400;\">(Mumbai to Goa), the central stretch is called the </span><strong>Kanara </strong><span style=\"font-weight: 400;\">or the \"</span><strong>Karavali</strong><span style=\"font-weight: 400;\">\", while the southern stretch is referred to as the </span><strong>Malabar </strong><span style=\"font-weight: 400;\">Coast.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">87.</span><span style=\"font-family: Palanquin;\">(b)&nbsp;</span><strong>&#2325;&#2379;&#2352;&#2379;&#2350;&#2306;&#2337;&#2354; &#2340;&#2335;&#2404; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2340;&#2335;&#2368;&#2351; &#2350;&#2376;&#2342;&#2366;&#2344;&#2379;&#2306; &#2325;&#2366; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; :-</strong><span style=\"font-weight: 400;\"> &#2311;&#2360;&#2350;&#2375;&#2306; &#2327;&#2369;&#2332;&#2352;&#2366;&#2340;, &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2327;&#2379;&#2357;&#2366;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2324;&#2352; &#2325;&#2375;&#2352;&#2354; &#2352;&#2366;&#2332;&#2381;&#2351; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404; &#2340;&#2335; &#2325;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2349;&#2366;&#2327; &#2325;&#2379; </span><strong>&#2325;&#2379;&#2306;&#2325;&#2339; </strong><span style=\"font-weight: 400;\">(&#2350;&#2369;&#2306;&#2348;&#2312; &#2360;&#2375; &#2327;&#2379;&#2357;&#2366;) &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2326;&#2306;&#2337; &#2325;&#2379; </span><strong>&#2325;&#2375;&#2344;&#2352;&#2366; </strong><span style=\"font-weight: 400;\">&#2351;&#2366; \"</span><strong>&#2325;&#2352;&#2366;&#2357;&#2354;&#2368;</strong><span style=\"font-weight: 400;\">\" &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; \", &#2332;&#2348;&#2325;&#2367; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339;&#2368; &#2326;&#2306;&#2337; &#2325;&#2379; </span><strong>&#2350;&#2366;&#2354;&#2366;&#2348;&#2366;&#2352; </strong><span style=\"font-weight: 400;\">&#2340;&#2335; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "18",
                    question_en: "<p>88. <span style=\"font-family: Roboto;\">Pick the odd one out in the Central Processing Unit (CPU) of a computer. </span></p>\n",
                    question_hi: "<p>88. <span style=\"font-family: Palanquin;\">&#2325;&#2350;&#2381;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2375;&#2306;&#2335;&#2381;&#2352;&#2354; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; &#2351;&#2370;&#2344;&#2367;&#2335; (CPU) &#2325;&#2375; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349; &#2350;&#2375;&#2306; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2309;&#2354;&#2327; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>ALU and Control Unit both</p>\n", "<p>Control Unit</p>\n", 
                                "<p>ALU (Arithmetic Logic Unit)</p>\n", "<p>Output Unit</p>\n"],
                    options_hi: ["<p>ALU &#2324;&#2352; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2351;&#2370;&#2344;&#2367;&#2335; &#2342;&#2379;&#2344;&#2379;&#2306;</p>\n", "<p>&#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2351;&#2370;&#2344;&#2367;&#2335;</p>\n",
                                "<p><span style=\"font-weight: 400;\">ALU (&#2309;&#2352;&#2367;&#2341;&#2350;&#2376;&#2335;&#2367;&#2325; &#2354;&#2377;&#2332;&#2367;&#2325; &#2351;&#2370;&#2344;&#2367;&#2335;)</span></p>\n", "<p>&#2310;&#2313;&#2335;&#2346;&#2369;&#2335; &#2351;&#2370;&#2344;&#2367;&#2335;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">88.</span><span style=\"font-family: Roboto;\">(d)&nbsp;</span><strong>Output Unit. The Central Processing Unit (CPU) </strong><span style=\"font-weight: 400;\">is considered as the brain of the computer.</span><strong> </strong><span style=\"font-weight: 400;\">The CPU has two units:-</span><strong> Arithmetic Logic Unit (ALU)</strong><span style=\"font-weight: 400;\"> performs all the tasks of logical and arithmetic operations. CU stands for </span><strong>Control Unit </strong><span style=\"font-weight: 400;\">which works to control all the parts of the computer and give proper instructions.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">88.</span><span style=\"font-family: Roboto;\">(d)</span><span style=\"font-family: Roboto;\">&nbsp;</span><strong>&#2310;&#2313;&#2335;&#2346;&#2369;&#2335; &#2351;&#2370;&#2344;&#2367;&#2335;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2360;&#2375;&#2306;&#2335;&#2381;&#2352;&#2354; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; &#2351;&#2370;&#2344;&#2367;&#2335;(CPU) </strong><span style=\"font-weight: 400;\">&#2325;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2366; &#2350;&#2360;&#2381;&#2340;&#2367;&#2359;&#2381;&#2325; &#2350;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; CPU &#2325;&#2368; &#2342;&#2379; Units &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306;:- </span><strong>Arithmetic Logic Unit (ALU) </strong><span style=\"font-weight: 400;\">&#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325; &#2324;&#2352; &#2309;&#2306;&#2325;&#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2360;&#2349;&#2368; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; CU &#2325;&#2366; &#2350;&#2340;&#2354;&#2348; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2351;&#2370;&#2344;&#2367;&#2335; &#2361;&#2376; &#2332;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2349;&#2368; &#2349;&#2366;&#2327;&#2379;&#2306; &#2325;&#2379; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2313;&#2330;&#2367;&#2340; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358; &#2342;&#2375;&#2344;&#2375; &#2325;&#2366; &#2325;&#2366;&#2350; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "18",
                    question_en: "<p>89<span style=\"font-family: Roboto;\">. ___________ diseases last for only shorter periods of time. </span></p>\n",
                    question_hi: "<p>89. ________ <span style=\"font-family: Palanquin;\">&#2352;&#2379;&#2327; &#2325;&#2375;&#2357;&#2354; &#2325;&#2350; &#2309;&#2357;&#2343;&#2367; &#2340;&#2325; &#2352;&#2361;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Hereditary</p>\n", "<p>Chronic</p>\n", 
                                "<p>Acute</p>\n", "<p>Genetic</p>\n"],
                    options_hi: ["<p>&#2357;&#2306;&#2358;&#2366;&#2344;&#2369;&#2327;&#2340;</p>\n", "<p>&#2342;&#2368;&#2352;&#2381;&#2328;&#2325;&#2366;&#2354;&#2367;&#2325;</p>\n",
                                "<p>&#2340;&#2368;&#2357;&#2381;&#2352;</p>\n", "<p>&#2310;&#2344;&#2369;&#2357;&#2306;&#2358;&#2367;&#2325;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">89.</span><span style=\"font-family: Roboto;\">(c)&nbsp; </span><strong>Acute. </strong><span style=\"font-weight: 400;\">Acute conditions are often caused by a virus or an infection.&nbsp; </span><strong>Example -</strong><span style=\"font-weight: 400;\"> Common cold, typhoid, jaundice, cholera etc. </span><strong>Genetic disorder : </strong><span style=\"font-weight: 400;\">An inherited medical condition caused by a DNA abnormality. </span><strong>Eg; </strong><span style=\"font-weight: 400;\">Congenital deafness, Cystic fibrosis, Beta thalassemia and Spinal muscular atrophy (SMA). A </span><strong>chronic </strong><span style=\"font-weight: 400;\">condition is a health condition or disease that is persistent or otherwise long-lasting in its effects or a disease that comes with time. </span><strong>Eg; </strong><span style=\"font-weight: 400;\">Arthritis, Asthma, Cancer etc.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">89.</span><span style=\"font-family: Palanquin;\">(c)&nbsp; </span><strong>&#2340;&#2368;&#2357;&#2381;&#2352;&#2404; </strong><span style=\"font-weight: 400;\">&#2340;&#2368;&#2357;&#2381;&#2352; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2351;&#2366;&#2306; &#2309;&#2325;&#2381;&#2360;&#2352; &#2357;&#2366;&#2351;&#2352;&#2360; &#2351;&#2366; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2339; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; </strong><span style=\"font-weight: 400;\">- &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2360;&#2352;&#2381;&#2342;&#2368;, &#2335;&#2366;&#2311;&#2347;&#2366;&#2311;&#2337;, &#2346;&#2368;&#2354;&#2367;&#2351;&#2366;, &#2361;&#2376;&#2332;&#2366; &#2310;&#2342;&#2367;&#2404;</span><strong> &#2310;&#2344;&#2369;&#2357;&#2306;&#2358;&#2367;&#2325; &#2357;&#2367;&#2325;&#2366;&#2352;</strong><span style=\"font-weight: 400;\">:&nbsp; DNA &#2309;&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2366; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2357;&#2306;&#2358;&#2366;&#2327;&#2340; &#2350;&#2375;&#2306; &#2350;&#2367;&#2354;&#2368; &#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2404; </span><strong>&#2332;&#2376;&#2360;&#2375;</strong><span style=\"font-weight: 400;\">; &#2332;&#2344;&#2381;&#2350;&#2332;&#2366;&#2340; &#2348;&#2361;&#2352;&#2366;&#2346;&#2344;, &#2360;&#2367;&#2360;&#2381;&#2335;&#2367;&#2325; &#2347;&#2366;&#2311;&#2348;&#2381;&#2352;&#2379;&#2360;&#2367;&#2360;, &#2348;&#2368;&#2335;&#2366; &#2341;&#2376;&#2354;&#2375;&#2360;&#2368;&#2350;&#2367;&#2351;&#2366; &#2324;&#2352; &#2360;&#2381;&#2346;&#2366;&#2311;&#2344;&#2354; &#2350;&#2360;&#2381;&#2325;&#2369;&#2354;&#2352; &#2319;&#2335;&#2381;&#2352;&#2379;&#2347;&#2368; (SMA)&#2404; &#2319;&#2325; </span><strong>&#2342;&#2368;&#2352;&#2381;&#2328;&#2325;&#2366;&#2354;&#2367;&#2325; </strong><span style=\"font-weight: 400;\">&#2360;&#2381;&#2341;&#2367;&#2340;&#2367; (chronic condition) &#2319;&#2325; &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2351;&#2366; &#2348;&#2368;&#2350;&#2366;&#2352;&#2368; &#2361;&#2376; &#2332;&#2379; &#2311;&#2360;&#2325;&#2375; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2379;&#2306; &#2351;&#2366; &#2360;&#2350;&#2351; &#2325;&#2375; &#2360;&#2366;&#2341; &#2310;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2348;&#2368;&#2350;&#2366;&#2352;&#2368; &#2350;&#2375;&#2306; &#2354;&#2327;&#2366;&#2340;&#2366;&#2352; &#2351;&#2366; &#2309;&#2344;&#2381;&#2351;&#2341;&#2366; &#2354;&#2306;&#2348;&#2375; &#2360;&#2350;&#2351; &#2340;&#2325; &#2330;&#2354;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2332;&#2376;&#2360;&#2375;</strong><span style=\"font-weight: 400;\">; &#2327;&#2336;&#2367;&#2351;&#2366;, &#2342;&#2350;&#2366;, &#2325;&#2376;&#2306;&#2360;&#2352; &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "18",
                    question_en: "<p>90. <span style=\"font-family: Roboto;\"> In which year was the Insurance Regulatory and Development Authority of India set up? </span></p>\n",
                    question_hi: "<p>90. <span style=\"font-family: Palanquin;\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2325;&#2367;&#2360; &#2357;&#2352;&#2381;&#2359; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2341;&#2366;?</span></p>\n",
                    options_en: ["<p>1999</p>\n", "<p>2011</p>\n", 
                                "<p>1995</p>\n", "<p>2002</p>\n"],
                    options_hi: ["<p>1999</p>\n", "<p>2011</p>\n",
                                "<p>1995</p>\n", "<p>2002</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">90.</span><span style=\"font-family: Roboto;\">(a) <strong>1999.</strong></span><strong>The Insurance Regulatory and Development Authority (IRDA) </strong><span style=\"font-weight: 400;\">of India is headquartered in Hyderabad, Telangana, where it moved from Delhi in 2001. At present (January, 2023), the authority is chaired by </span><strong>Mr. Debasish Panda. </strong><span style=\"font-weight: 400;\">The Insurance Regulatory and Development Authority of India is a statutory body under the jurisdiction of the Ministry of Finance, Government of India and is tasked with regulating and licensing the insurance and re-insurance industries in India.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">90.</span><span style=\"font-family: Palanquin;\">(a)&nbsp;</span><strong>1999 </strong><span style=\"font-weight: 400;\">&#2404;</span><strong> &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2348;&#2368;&#2350;&#2366; &#2357;&#2367;&#2344;&#2367;&#2351;&#2366;&#2350;&#2325; &#2324;&#2352; &#2357;&#2367;&#2325;&#2366;&#2360; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; (IRDA) </strong><span style=\"font-weight: 400;\">&#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; &#2361;&#2376;&#2342;&#2352;&#2366;&#2348;&#2366;&#2342;, &#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366; &#2350;&#2375;&#2306; &#2361;&#2376;, &#2332;&#2361;&#2366;&#2305; &#2351;&#2361; 2001 &#2350;&#2375;&#2306; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2360;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2341;&#2366;&#2404; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306; (&#2332;&#2344;&#2357;&#2352;&#2368;, 2023), &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2325;&#2375; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; </span><strong>&#2358;&#2381;&#2352;&#2368; &#2342;&#2375;&#2348;&#2366;&#2358;&#2368;&#2359; &#2346;&#2366;&#2306;&#2337;&#2366; </strong><span style=\"font-weight: 400;\">&#2361;&#2376;&#2306;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2348;&#2368;&#2350;&#2366; &#2357;&#2367;&#2344;&#2367;&#2351;&#2366;&#2350;&#2325; &#2324;&#2352; &#2357;&#2367;&#2325;&#2366;&#2360; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2357;&#2367;&#2340;&#2381;&#2340; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351; &#2325;&#2375; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2319;&#2325; &#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2375; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2348;&#2368;&#2350;&#2366; &#2324;&#2352; &#2346;&#2369;&#2344;: &#2348;&#2368;&#2350;&#2366; &#2313;&#2342;&#2381;&#2351;&#2379;&#2327;&#2379;&#2306; &#2325;&#2379; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2367;&#2340; &#2324;&#2352; &#2354;&#2366;&#2311;&#2360;&#2375;&#2306;&#2360; &#2342;&#2375;&#2344;&#2375; &#2325;&#2366; &#2325;&#2366;&#2350; &#2360;&#2380;&#2306;&#2346;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "18",
                    question_en: "<p>91.<span style=\"font-family: Roboto;\"> Which of the following is the largest freshwater lake in India? </span></p>\n",
                    question_hi: "<p>91.<span style=\"font-family: Palanquin;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2350;&#2368;&#2336;&#2375; &#2346;&#2366;&#2344;&#2368; &#2325;&#2368; &#2333;&#2368;&#2354; &#2325;&#2380;&#2344;-&#2360;&#2368; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>Barapani</p>\n", "<p>Wular</p>\n", 
                                "<p>Bhimtal</p>\n", "<p>Loktak</p>\n"],
                    options_hi: ["<p>&#2348;&#2366;&#2352;&#2366;&#2346;&#2366;&#2344;&#2368;</p>\n", "<p>&#2357;&#2370;&#2354;&#2352;</p>\n",
                                "<p>&#2349;&#2368;&#2350;&#2340;&#2366;&#2354;</p>\n", "<p>&#2354;&#2379;&#2325;&#2340;&#2366;&#2325;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">91.</span><span style=\"font-family: Roboto;\">(b)&nbsp;</span><strong>Wular.</strong><span style=\"font-weight: 400;\"> </span><strong>Chilika Lake</strong><span style=\"font-weight: 400;\"> in Odisha is the largest&nbsp; brackish water lake in&nbsp; india.</span><strong> Umiam Lake</strong><span style=\"font-weight: 400;\"> is a reservoir located in&nbsp; the North of Shillong in the state of Meghalaya, India. </span><strong>Keibul Lamjao National Park</strong><span style=\"font-weight: 400;\">, the only floating national park in the world, is located on</span><strong> Loktak Lake</strong><span style=\"font-weight: 400;\"> in Manipur. </span><strong>Bhimtal </strong><span style=\"font-weight: 400;\">is the largest lake of Nainital district, in uttarakhand.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">91.</span><span style=\"font-family: Palanquin;\">(b)&nbsp;</span><strong>&#2357;&#2369;&#2354;&#2352;&#2404; </strong><span style=\"font-weight: 400;\">&#2313;&#2337;&#2364;&#2368;&#2360;&#2366; &#2350;&#2375;&#2306;</span><strong> &#2330;&#2367;&#2354;&#2381;&#2325;&#2366; &#2333;&#2368;&#2354;</strong><span style=\"font-weight: 400;\"> &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2326;&#2366;&#2352;&#2375; &#2346;&#2366;&#2344;&#2368; &#2325;&#2368; &#2333;&#2368;&#2354; &#2361;&#2376;&#2404; </span><strong>&#2313;&#2350;&#2367;&#2351;&#2350; &#2333;&#2368;&#2354;</strong><span style=\"font-weight: 400;\"> &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2350;&#2375;&#2328;&#2366;&#2354;&#2351; &#2352;&#2366;&#2332;&#2381;&#2351; &#2350;&#2375;&#2306; &#2358;&#2367;&#2354;&#2366;&#2306;&#2327; &#2325;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2367;&#2340; &#2319;&#2325; &#2332;&#2354;&#2366;&#2358;&#2351; &#2361;&#2376;&#2404; </span><strong>&#2325;&#2375;&#2348;&#2369;&#2354; &#2354;&#2366;&#2350;&#2332;&#2366;&#2323; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2313;&#2342;&#2381;&#2351;&#2366;&#2344;</strong><span style=\"font-weight: 400;\">, &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; &#2340;&#2376;&#2352;&#2340;&#2366; &#2361;&#2369;&#2310; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2313;&#2342;&#2381;&#2351;&#2366;&#2344;, &#2350;&#2339;&#2367;&#2346;&#2369;&#2352; &#2350;&#2375;&#2306; </span><strong>&#2354;&#2379;&#2325;&#2335;&#2325; &#2333;&#2368;&#2354;</strong><span style=\"font-weight: 400;\"> &#2346;&#2352; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2376;&#2404; </span><strong>&#2349;&#2368;&#2350;&#2340;&#2366;&#2354;</strong><span style=\"font-weight: 400;\"> &#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337; &#2325;&#2375; &#2344;&#2376;&#2344;&#2368;&#2340;&#2366;&#2354; &#2332;&#2367;&#2354;&#2375; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2333;&#2368;&#2354; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "18",
                    question_en: "<p>92.<span style=\"font-family: Roboto;\"> LTE technology is used in which of the following generations of wireless technologies? </span></p>\n",
                    question_hi: "<p>92. <span style=\"font-family: Palanquin;\">LTE &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360; &#2357;&#2366;&#2351;&#2352;&#2354;&#2375;&#2360; &#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>3G</p>\n", "<p>2G</p>\n", 
                                "<p>4G</p>\n", "<p>1G</p>\n"],
                    options_hi: ["<p>3G</p>\n", "<p>2G</p>\n",
                                "<p>4G</p>\n", "<p>1G</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">92.</span><span style=\"font-family: Roboto;\">(c)&nbsp;</span><strong>4G. LTE (Long-Term Evolution)</strong><span style=\"font-weight: 400;\"> is a fourth-generation </span><strong>(4G)</strong><span style=\"font-weight: 400;\"> wireless standard that provides increased network capacity and speed for cellphones and other cellular devices compared with third-generation (3G) technology. </span><strong>1G </strong><span style=\"font-weight: 400;\">was the first generation of cell phone technology. 1G is an analog technology and the phones generally had poor battery life&nbsp; and would sometimes experience dropped calls.&nbsp; </span><strong>2G </strong><span style=\"font-weight: 400;\">networks are digital. Main motive of this generation was to provide secure and reliable communication.&nbsp; The </span><strong>3G </strong><span style=\"font-weight: 400;\">standard utilizes a new technology called UMTS (Universal Mobile Telecommunications System) as its core network architecture.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">92.</span><span style=\"font-family: Roboto;\">(c)&nbsp;</span><strong>4G </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; LTE (&#2354;&#2377;&#2344;&#2381;&#2327;-&#2335;&#2352;&#2381;&#2350; &#2311;&#2357;&#2379;&#2354;&#2381;&#2351;&#2370;&#2358;&#2344;) </strong><span style=\"font-weight: 400;\">&#2330;&#2380;&#2341;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368;</span><strong> (4G)</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2357;&#2366;&#2351;&#2352;&#2354;&#2375;&#2360; &#2350;&#2366;&#2344;&#2325; &#2361;&#2376; &#2332;&#2379; &#2340;&#2368;&#2360;&#2352;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; (3G) &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2360;&#2375;&#2354;&#2347;&#2379;&#2344; &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2360;&#2375;&#2354;&#2369;&#2354;&#2352; &#2313;&#2346;&#2325;&#2352;&#2339;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2338;&#2364;&#2368; &#2361;&#2369;&#2312; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366; &#2324;&#2352; &#2327;&#2340;&#2367; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>1G </strong><span style=\"font-weight: 400;\">&#2360;&#2375;&#2354; &#2347;&#2379;&#2344; &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; &#2341;&#2368;&#2404; </span><strong>1G</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2319;&#2344;&#2366;&#2354;&#2377;&#2327; &#2340;&#2325;&#2344;&#2368;&#2325; &#2361;&#2376; &#2324;&#2352; &#2347;&#2379;&#2344; &#2350;&#2375;&#2306; &#2310;&#2350; &#2340;&#2380;&#2352; &#2346;&#2352; &#2326;&#2352;&#2366;&#2348; &#2348;&#2376;&#2335;&#2352;&#2368; &#2354;&#2366;&#2311;&#2347; &#2361;&#2379;&#2340;&#2368; &#2341;&#2368; &#2324;&#2352; &#2325;&#2349;&#2368;-&#2325;&#2349;&#2368; &#2325;&#2377;&#2354; &#2337;&#2381;&#2352;&#2377;&#2346; &#2361;&#2379;&#2344;&#2375; &#2325;&#2366; &#2309;&#2344;&#2369;&#2349;&#2357; &#2361;&#2379;&#2340;&#2366; &#2341;&#2366;&#2404; </span><strong>2G</strong><span style=\"font-weight: 400;\"> &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360; &#2346;&#2368;&#2338;&#2364;&#2368; &#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2367;&#2340; &#2324;&#2352; &#2357;&#2367;&#2358;&#2381;&#2357;&#2360;&#2344;&#2368;&#2351; &#2360;&#2306;&#2330;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2344;&#2366; &#2341;&#2366;&#2404; </span><strong>3G</strong><span style=\"font-weight: 400;\"> &#2350;&#2366;&#2344;&#2325; &#2309;&#2346;&#2344;&#2375; &#2325;&#2379;&#2352; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2310;&#2352;&#2381;&#2325;&#2367;&#2335;&#2375;&#2325;&#2381;&#2330;&#2352; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; UMTS (&#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360;&#2354; &#2350;&#2379;&#2348;&#2366;&#2311;&#2354; &#2335;&#2375;&#2354;&#2368;&#2325;&#2377;&#2350; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;) &#2344;&#2366;&#2350;&#2325; &#2319;&#2325; &#2344;&#2312; &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">93.</span><span style=\"font-family: Roboto;\"> Macroeconomics deals with which of the following studies?</span></p>\r\n<p><span style=\"font-family: Roboto;\"> I. Reasons behind the unemployment of resources</span></p>\r\n<p><span style=\"font-family: Roboto;\"> Il. Determination of total output </span></p>\n",
                    question_hi: "<p>93. <span style=\"font-family: Palanquin;\">&#2360;&#2350;&#2359;&#2381;&#2335;&#2367; &#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352; (Macroeconomics ) &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2404;. &#2360;&#2306;&#2360;&#2366;&#2343;&#2344;&#2379;&#2306; &#2325;&#2366; &#2346;&#2370;&#2352;&#2381;&#2339; &#2352;&#2370;&#2346; &#2360;&#2375; &#2313;&#2346;&#2351;&#2379;&#2327; &#2344; &#2361;&#2379;&#2344;&#2375; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339;&#2379;&#2306;</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2405;. &#2325;&#2369;&#2354; &#2344;&#2367;&#2352;&#2381;&#2327;&#2340; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2343;&#2366;&#2352;&#2339;</span></p>\n",
                    options_en: ["<p>Only l</p>\n", "<p>Only ll</p>\n", 
                                "<p>Neither I nor II</p>\n", "<p>Both l and Il</p>\n"],
                    options_hi: ["<p>&#2325;&#2375;&#2357;&#2354; &#2404;</p>\n", "<p>&#2325;&#2375;&#2357;&#2354; &#2405;</p>\n",
                                "<p>&#2344; &#2340;&#2379; &#2404; &#2324;&#2352; &#2344; &#2361;&#2368; &#2405;</p>\n", "<p>&#2404; &#2324;&#2352; &#2405; &#2342;&#2379;&#2344;&#2379;&#2306;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">93.</span><span style=\"font-family: Roboto;\">(d) </span><strong>Both I and II. </strong><span style=\"font-weight: 400;\">&nbsp;</span><strong>Macroeconomics</strong><span style=\"font-weight: 400;\"> is that part of economics which deals with the individual units of the economy. It takes into account the demand and supply of individual units, which deals with the economic issues that take place on a large scale. It takes into account aggregate demand and aggregate supply.&nbsp;&nbsp;&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">93.</span><span style=\"font-family: Palanquin;\">(d)&nbsp; </span><strong>I &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306; </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; </strong><span style=\"font-weight: 400;\">&#2360;&#2350;&#2359;&#2381;&#2335;&#2367; &#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352; (Macroeconomics )</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352; </span><span style=\"font-weight: 400;\">&#2357;&#2361; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; &#2361;&#2376; &#2332;&#2379; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2311;&#2325;&#2366;&#2311;&#2351;&#2379;&#2306; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2351;&#2361; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340; &#2311;&#2325;&#2366;&#2311;&#2351;&#2379;&#2306; &#2325;&#2368; &#2350;&#2366;&#2306;&#2327; &#2324;&#2352; &#2310;&#2346;&#2370;&#2352;&#2381;&#2340;&#2367; &#2325;&#2379; &#2343;&#2381;&#2351;&#2366;&#2344; &#2350;&#2375;&#2306; &#2352;&#2326;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2379; &#2348;&#2337;&#2364;&#2375; &#2346;&#2376;&#2350;&#2366;&#2344;&#2375; &#2346;&#2352; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2350;&#2369;&#2342;&#2381;&#2342;&#2379;&#2306; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2351;&#2361; &#2325;&#2369;&#2354; &#2350;&#2366;&#2306;&#2327; &#2324;&#2352; &#2360;&#2350;&#2327;&#2381;&#2352; &#2310;&#2346;&#2370;&#2352;&#2381;&#2340;&#2367; &#2325;&#2379; &#2343;&#2381;&#2351;&#2366;&#2344; &#2350;&#2375;&#2306; &#2352;&#2326;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "18",
                    question_en: "<p>94.<span style=\"font-family: Roboto;\"> Indian cricket team lost the 2021 ICC World Test Championship Final to which country? </span></p>\n",
                    question_hi: "<p>94. <span style=\"font-family: Palanquin;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; 2021 &#2310;&#2312;&#2360;&#2368;&#2360;&#2368; &#2357;&#2367;&#2358;&#2381;&#2357; &#2335;&#2375;&#2360;&#2381;&#2335; &#2330;&#2376;&#2350;&#2381;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346; &#2347;&#2366;&#2311;&#2344;&#2354; &#2325;&#2367;&#2360; &#2342;&#2375;&#2358; &#2360;&#2375; &#2361;&#2366;&#2352;&#2368; &#2341;&#2368;?</span></p>\n",
                    options_en: ["<p>New Zealand</p>\n", "<p>Australia</p>\n", 
                                "<p>England</p>\n", "<p>Pakistan</p>\n"],
                    options_hi: ["<p>&#2344;&#2381;&#2351;&#2370;&#2332;&#2368;&#2354;&#2376;&#2306;&#2337;</p>\n", "<p>&#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2366;</p>\n",
                                "<p>&#2311;&#2306;&#2327;&#2381;&#2354;&#2376;&#2306;&#2337;</p>\n", "<p>&#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">94.</span><span style=\"font-family: Roboto;\">(a)&nbsp;</span><strong>New Zealand. </strong><span style=\"font-weight: 400;\">The final of the </span><strong>2019&ndash;2021 (ICC) World Test Championship, </strong><span style=\"font-weight: 400;\">&nbsp;was played from 18 to 23 June 2021 between India and New Zealand at the Rose Bowl, Southampton, England. New Zealand won the match by eight wickets.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">94.</span><span style=\"font-family: Palanquin;\">(a)&nbsp;</span><strong>&#2344;&#2381;&#2351;&#2370;&#2332;&#2364;&#2368;&#2354;&#2376;&#2306;&#2337;&#2404; 2019-2021 (ICC) &#2357;&#2367;&#2358;&#2381;&#2357; &#2335;&#2375;&#2360;&#2381;&#2335; &#2330;&#2376;&#2350;&#2381;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346;</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2347;&#2366;&#2311;&#2344;&#2354;, 18 &#2360;&#2375; 23 &#2332;&#2370;&#2344; 2021 &#2340;&#2325; &#2349;&#2366;&#2352;&#2340; &#2324;&#2352; &#2344;&#2381;&#2351;&#2370;&#2332;&#2368;&#2354;&#2376;&#2306;&#2337; &#2325;&#2375; &#2348;&#2368;&#2330; &#2352;&#2379;&#2332;&#2364; &#2348;&#2366;&#2313;&#2354;, &#2360;&#2366;&#2313;&#2341;&#2375;&#2350;&#2381;&#2346;&#2381;&#2335;&#2344;, &#2311;&#2306;&#2327;&#2381;&#2354;&#2376;&#2306;&#2337; &#2350;&#2375;&#2306; &#2326;&#2375;&#2354;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2344;&#2381;&#2351;&#2370;&#2332;&#2368;&#2354;&#2376;&#2306;&#2337; &#2344;&#2375; &#2310;&#2336; &#2357;&#2367;&#2325;&#2375;&#2335; &#2360;&#2375; &#2350;&#2376;&#2330; &#2332;&#2368;&#2340; &#2354;&#2367;&#2351;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "18",
                    question_en: "<p>95. <span style=\"font-family: Roboto;\">&lsquo;281 and beyond&rsquo; is the autobiography of which veteran cricketer? </span></p>\n",
                    question_hi: "<p>95. \'<span style=\"font-family: Palanquin;\">281 &#2319;&#2306;&#2337; &#2348;&#2367;&#2351;&#2379;&#2306;&#2337;\' &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2340;&#2367;&#2359;&#2381;&#2336;&#2367;&#2340; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335;&#2352; &#2325;&#2368; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Kapil Dev</p>\n", "<p>VVS Laxman</p>\n", 
                                "<p>Sourav Ganguly</p>\n", "<p>Ajinkya Rahane</p>\n"],
                    options_hi: ["<p>&#2325;&#2346;&#2367;&#2354; &#2342;&#2375;&#2357;</p>\n", "<p>&#2357;&#2368;&#2357;&#2368;&#2319;&#2360; &#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2339;</p>\n",
                                "<p>&#2360;&#2380;&#2352;&#2357; &#2327;&#2366;&#2306;&#2327;&#2369;&#2354;&#2368;</p>\n", "<p>&#2309;&#2332;&#2367;&#2306;&#2325;&#2381;&#2351; &#2352;&#2361;&#2366;&#2339;&#2375;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">95.</span><span style=\"font-family: Roboto;\">(b)&nbsp;</span><strong>VVS Laxman. &lsquo;281 and beyond&rsquo;</strong><span style=\"font-weight: 400;\"> is the autobiography of VVS Laxman.</span><strong> Kapil Dev\'s autobiography -</strong><span style=\"font-weight: 400;\"> He has written four books &ndash; three autobiographical and one book on Sikhism. Autobiographical works include &mdash; By God\'s Decree which came out in 1985, Cricket My Style in 1987, and Straight from the Heart in 2004. His latest book titled We, The Sikhs was released in 2019.&nbsp; </span><strong>A Century is Not Enough</strong><span style=\"font-weight: 400;\"> -&nbsp; Sourav Ganguly,&nbsp; </span><strong>Playing It My Way</strong><span style=\"font-weight: 400;\"> -&nbsp; Sachin Tendulkar.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">95.</span><span style=\"font-family: Palanquin;\">(b)&nbsp;</span><strong>&#2357;&#2368;&#2357;&#2368;&#2319;&#2360; &#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2339;&#2404; \'281 &#2319;&#2306;&#2337; &#2348;&#2367;&#2351;&#2377;&#2344;&#2381;&#2337;\'</strong><span style=\"font-weight: 400;\"> &#2357;&#2368;&#2357;&#2368;&#2319;&#2360; &#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2339; &#2325;&#2368; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2325;&#2346;&#2367;&#2354; &#2342;&#2375;&#2357; &#2325;&#2368; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2330;&#2366;&#2352; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2375;&#2306; &#2354;&#2367;&#2326;&#2368; &#2361;&#2376;&#2306; - &#2340;&#2368;&#2344; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366;&#2340;&#2381;&#2350;&#2325; &#2324;&#2352; &#2319;&#2325; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2360;&#2367;&#2326; &#2343;&#2352;&#2381;&#2350; &#2346;&#2352;&#2404; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366;&#2340;&#2381;&#2350;&#2325; &#2352;&#2330;&#2344;&#2366;&#2323;&#2306; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306; - &#2348;&#2366;&#2351; &#2327;&#2377;&#2337;&#2381;&#2360; &#2337;&#2367;&#2325;&#2381;&#2352;&#2368; &#2332;&#2379; 1985 &#2350;&#2375;&#2306; &#2360;&#2366;&#2350;&#2344;&#2375; &#2310;&#2312; &#2404; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2350;&#2366;&#2312; &#2360;&#2381;&#2335;&#2366;&#2311;&#2354; 1987 &#2350;&#2375;&#2306;, &#2324;&#2352; &#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2335; &#2347;&#2381;&#2352;&#2377;&#2350; &#2342; &#2361;&#2366;&#2352;&#2381;&#2335; 2004 &#2350;&#2375;&#2306;&#2404; &#2313;&#2344;&#2325;&#2368; &#2344;&#2357;&#2368;&#2344;&#2340;&#2350; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2332;&#2367;&#2360;&#2325;&#2366; &#2358;&#2368;&#2352;&#2381;&#2359;&#2325; &#2357;&#2368;, &#2342; &#2360;&#2367;&#2326; 2019 &#2350;&#2375;&#2306; &#2332;&#2366;&#2352;&#2368; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2319; &#2360;&#2375;&#2306;&#2330;&#2369;&#2352;&#2368; &#2311;&#2332; &#2344;&#2377;&#2335; &#2311;&#2344;&#2347;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2380;&#2352;&#2357; &#2327;&#2366;&#2306;&#2327;&#2369;&#2354;&#2368;, </span><strong>&#2346;&#2381;&#2354;&#2375;&#2311;&#2306;&#2327; &#2311;&#2335; &#2350;&#2366;&#2351; &#2357;&#2375;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2330;&#2367;&#2344; &#2340;&#2375;&#2306;&#2342;&#2369;&#2354;&#2325;&#2352;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "18",
                    question_en: "<p>96. <span style=\"font-family: Roboto;\">In which year was the Citizenship Act passed in India? </span></p>\n",
                    question_hi: "<p>96. <span style=\"font-family: Palanquin;\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2325;&#2367;&#2360; &#2357;&#2352;&#2381;&#2359; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2341;&#2366;?</span></p>\n",
                    options_en: ["<p>1951</p>\n", "<p>1955</p>\n", 
                                "<p>1959</p>\n", "<p>1964</p>\n"],
                    options_hi: ["<p>1951</p>\n", "<p>1955</p>\n",
                                "<p>1959</p>\n", "<p>1964</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">96.</span><span style=\"font-family: Roboto;\">(b) <strong>1955.</strong> </span><span style=\"font-weight: 400;\">The Constitution of India along with the Indian Citizenship Act 1955 governs the citizenship status of a person. Commenced on 30 December 1955. </span><strong>Citizenship </strong><span style=\"font-weight: 400;\">can be defined as a relationship between a nation and an individual of that specific nation. The concept of single citizenship was adopted from </span><strong>England</strong><span style=\"font-weight: 400;\">.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">96.</span><span style=\"font-family: Palanquin;\">(b) </span><strong>1955 </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1955 &#2325;&#2375; &#2360;&#2366;&#2341; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2367;&#2360;&#2368; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2325;&#2368; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; &#2325;&#2368; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2325;&#2379; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; 30 &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; 1955 &#2325;&#2379; &#2358;&#2369;&#2352;&#2370; &#2361;&#2369;&#2310;&#2404; </span><strong>&#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; </strong><span style=\"font-weight: 400;\">&#2325;&#2379; &#2319;&#2325; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2324;&#2352; &#2313;&#2360; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2325;&#2375; &#2319;&#2325; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2306;&#2348;&#2306;&#2343; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2346;&#2352;&#2367;&#2349;&#2366;&#2359;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; &#2319;&#2325;&#2354; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; &#2325;&#2368; &#2309;&#2357;&#2343;&#2366;&#2352;&#2339;&#2366; </span><strong>&#2311;&#2306;&#2327;&#2381;&#2354;&#2376;&#2306;&#2337; </strong><span style=\"font-weight: 400;\">&#2360;&#2375; &#2309;&#2346;&#2344;&#2366;&#2312; &#2327;&#2312; &#2341;&#2368;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "18",
                    question_en: "<p>97. _________<span style=\"font-family: Roboto;\"> is an Act of the Parliament of the United Kingdom that partitioned British India into two new independent dominions of India and Pakistan in 1947. </span></p>\n",
                    question_hi: "<p>97. <span style=\"font-family: Palanquin;\">&#2351;&#2370;&#2344;&#2366;&#2311;&#2335;&#2375;&#2337; &#2325;&#2367;&#2306;&#2327;&#2337;&#2350; &#2325;&#2368; &#2360;&#2306;&#2360;&#2342; &#2325;&#2366; &#2319;&#2325; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2361;&#2376; &#2332;&#2367;&#2360;&#2344;&#2375; 1947 &#2350;&#2375;&#2306; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2349;&#2366;&#2352;&#2340; &#2325;&#2379; &#2349;&#2366;&#2352;&#2340; &#2324;&#2352; &#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344; &#2325;&#2375; &#2342;&#2379; &#2344;&#2319; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352; &#2313;&#2346;&#2344;&#2367;&#2357;&#2375;&#2358;&#2379;&#2306; (dominions) &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2341;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>Rowlatt Act</p>\n", "<p>Indian Independence Act</p>\n", 
                                "<p>Government of India Act</p>\n", "<p>Pitt\'s India Act</p>\n"],
                    options_hi: ["<p>&#2352;&#2377;&#2354;&#2375;&#2335; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</p>\n", "<p>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</p>\n",
                                "<p>&#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</p>\n", "<p>&#2346;&#2367;&#2335;&#2381;&#2360; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">97.</span><span style=\"font-family: Roboto;\">(b)&nbsp;</span><strong>Indian Independence Act. </strong><span style=\"font-weight: 400;\">The Indian Independence Act 1947 is an Act of the Parliament of the United Kingdom that partitioned British India into the two new independent dominions of India and Pakistan. </span><strong>Rowlatt Act : </strong><span style=\"font-weight: 400;\">The act allowed certain political cases to be tried without juries and permitted internment of suspects without trial. </span><strong>Government of India Act 1858</strong><span style=\"font-weight: 400;\">, established India as a nation consisting of British India and princely states.&nbsp; </span><strong>Pitt\'s India Act (EIC Act 1784), </strong><span style=\"font-weight: 400;\">intended to address the shortcomings of the Regulating Act of 1773 by bringing the East India Company\'s rule in India under the control of the British Government.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">97.</span><span style=\"font-family: Palanquin;\">(b)&nbsp;</span><strong>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</strong><span style=\"font-weight: 400;\">&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1947 &#2351;&#2370;&#2344;&#2366;&#2311;&#2335;&#2375;&#2337; &#2325;&#2367;&#2306;&#2327;&#2337;&#2350; &#2325;&#2368; &#2360;&#2306;&#2360;&#2342; &#2325;&#2366; &#2319;&#2325; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2361;&#2376; &#2332;&#2367;&#2360;&#2344;&#2375; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2349;&#2366;&#2352;&#2340; &#2325;&#2379; &#2349;&#2366;&#2352;&#2340; &#2324;&#2352; &#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344; &#2325;&#2375; &#2342;&#2379; &#2344;&#2319; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352; &#2346;&#2381;&#2352;&#2349;&#2369;&#2340;&#2381;&#2357;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404; </span><strong>&#2352;&#2379;&#2354;&#2375;&#2335; &#2319;&#2325;&#2381;&#2335;</strong><span style=\"font-weight: 400;\">: &#2311;&#2360; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2344;&#2375; &#2325;&#2369;&#2331; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2325;&#2379; &#2344;&#2367;&#2352;&#2381;&#2339;&#2366;&#2351;&#2325; &#2350;&#2306;&#2337;&#2354; &#2325;&#2375; &#2348;&#2367;&#2344;&#2366; &#2330;&#2354;&#2366;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2368; &#2324;&#2352; &#2348;&#2367;&#2344;&#2366; &#2350;&#2369;&#2325;&#2342;&#2350;&#2375; &#2325;&#2375; &#2360;&#2306;&#2342;&#2367;&#2327;&#2381;&#2343;&#2379;&#2306; &#2325;&#2379; &#2344;&#2332;&#2364;&#2352;&#2348;&#2306;&#2342; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2368;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1858</strong><span style=\"font-weight: 400;\">, &#2344;&#2375; &#2349;&#2366;&#2352;&#2340; &#2325;&#2379; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2349;&#2366;&#2352;&#2340; &#2324;&#2352; &#2352;&#2367;&#2351;&#2366;&#2360;&#2340;&#2379;&#2306; &#2360;&#2375; &#2350;&#2367;&#2354;&#2325;&#2352; &#2319;&#2325; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404;</span><strong> &#2346;&#2367;&#2335;&#2381;&#2360; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2319;&#2325;&#2381;&#2335; (EIC Act 1784</strong><span style=\"font-weight: 400;\">), &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2339; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2375; &#2358;&#2366;&#2360;&#2344; &#2325;&#2379; &#2354;&#2366;&#2325;&#2352; 1773 &#2325;&#2375; &#2352;&#2375;&#2327;&#2369;&#2354;&#2375;&#2335;&#2367;&#2306;&#2327; &#2319;&#2325;&#2381;&#2335; &#2325;&#2368; &#2325;&#2350;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2342;&#2370;&#2352; &#2325;&#2352;&#2344;&#2366; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "18",
                    question_en: "<p>98.<span style=\"font-family: Roboto;\"> Who won the women\'s singles title in Australian Open 2022?</span></p>\n",
                    question_hi: "<p>98. <span style=\"font-family: Palanquin;\">&#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344; &#2323;&#2346;&#2344; 2022 &#2350;&#2375;&#2306; &#2350;&#2361;&#2367;&#2354;&#2366; &#2319;&#2325;&#2354; &#2325;&#2366; &#2326;&#2367;&#2340;&#2366;&#2348; &#2325;&#2367;&#2360;&#2344;&#2375; &#2332;&#2368;&#2340;&#2366; &#2341;&#2366;?</span></p>\n",
                    options_en: ["<p>Ashley barty</p>\n", "<p><span style=\"font-family: Roboto;\">Elena Rybakina</span></p>\n", 
                                "<p><span style=\"font-family: Roboto;\"> Ons jebure</span></p>\n", "<p>Serena Williams</p>\n"],
                    options_hi: ["<p>&#2319;&#2358;&#2354;&#2375; &#2348;&#2366;&#2352;&#2381;&#2335;&#2368;</p>\n", "<p>&#2320;&#2354;&#2375;&#2344;&#2366; &#2352;&#2367;&#2348;&#2366;&#2325;&#2367;&#2344;&#2366;</p>\n",
                                "<p>&#2323;&#2344;&#2381;&#2360; &#2332;&#2375;&#2348;&#2381;&#2351;&#2369;&#2352;</p>\n", "<p>&#2360;&#2375;&#2352;&#2375;&#2344;&#2366; &#2357;&#2367;&#2354;&#2367;&#2351;&#2350;&#2381;&#2360;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">98.</span><span style=\"font-family: Roboto;\">(a)&nbsp;</span><strong>Ashley Barty.&nbsp; Ashleigh Barty</strong><span style=\"font-weight: 400;\"> defeated </span><strong>Danielle Collins</strong><span style=\"font-weight: 400;\"> in the final to win the women\'s singles tennis title at the 2022 Australian Open.&nbsp; The 2022 Australian Open was a Grand Slam tennis tournament that took place at Melbourne Park, Australia from 17 to 30 January 2022.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">98.</span><span style=\"font-family: Roboto;\">(a)&nbsp; </span><strong>&#2319;&#2358;&#2354;&#2375; &#2348;&#2366;&#2352;&#2381;&#2335;&#2368;&#2404;</strong><span style=\"font-weight: 400;\"> </span><strong>&#2319;&#2358;&#2354;&#2375; &#2348;&#2366;&#2352;&#2381;&#2335;&#2368; </strong><span style=\"font-weight: 400;\">&#2344;&#2375; 2022 &#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344; &#2323;&#2346;&#2344; &#2350;&#2375;&#2306; &#2350;&#2361;&#2367;&#2354;&#2366; &#2319;&#2325;&#2354; &#2335;&#2375;&#2344;&#2367;&#2360; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; </span><strong>&#2337;&#2375;&#2344;&#2367;&#2351;&#2354; &#2325;&#2379;&#2354;&#2367;&#2344;&#2381;&#2360;</strong><span style=\"font-weight: 400;\"> &#2325;&#2379; &#2361;&#2352;&#2366;&#2351;&#2366;&#2404; 2022 &#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344; &#2323;&#2346;&#2344; &#2319;&#2325; &#2327;&#2381;&#2352;&#2376;&#2306;&#2337; &#2360;&#2381;&#2354;&#2376;&#2350; &#2335;&#2375;&#2344;&#2367;&#2360; &#2335;&#2370;&#2352;&#2381;&#2344;&#2366;&#2350;&#2375;&#2306;&#2335; &#2341;&#2366; &#2332;&#2379; 17 &#2360;&#2375; 30 &#2332;&#2344;&#2357;&#2352;&#2368; 2022 &#2340;&#2325; &#2350;&#2375;&#2354;&#2348;&#2352;&#2381;&#2344; &#2346;&#2366;&#2352;&#2381;&#2325;, &#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2361;&#2369;&#2310; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "18",
                    question_en: "<p>99.<span style=\"font-family: Roboto;\"> Birju Maharaj was a noted dancer of ________ .</span></p>\n",
                    question_hi: "<p>99.<span style=\"font-family: Palanquin;\"> &#2348;&#2367;&#2352;&#2332;&#2370; &#2350;&#2361;&#2366;&#2352;&#2366;&#2332; _________ &#2325;&#2375; &#2319;&#2325; &#2332;&#2366;&#2344;&#2375;-&#2350;&#2366;&#2344;&#2375; &#2344;&#2352;&#2381;&#2340;&#2325; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Bharatnatyam</p>\n", "<p>Manipuri</p>\n", 
                                "<p>Kathak</p>\n", "<p>Sattriya</p>\n"],
                    options_hi: ["<p>&#2349;&#2352;&#2340;&#2344;&#2366;&#2335;&#2381;&#2351;&#2350;</p>\n", "<p>&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368;</p>\n",
                                "<p>&#2325;&#2341;&#2325;</p>\n", "<p>&#2360;&#2340;&#2381;&#2352;&#2367;&#2351;&#2366;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">99.(c)&nbsp;</span><strong>Kathak.</strong><span style=\"font-weight: 400;\"> He was from&nbsp; Lucknow Gharana and recipient of the Padma Vibhushan (1986). </span><strong>Manipuri dancers -</strong><span style=\"font-weight: 400;\"> Guru Amubi Singh, Elam Endira Devi. </span><strong>Bharatnatyam dancers -</strong><span style=\"font-weight: 400;\"> Padma Subrahmanyam, R Muthukannamal; </span><strong>Sattriya dancer - </strong><span style=\"font-weight: 400;\">Indira Bora.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Palanquin Dark;\">99.(c)&nbsp;</span><strong>&#2325;&#2341;&#2325;</strong><span style=\"font-weight: 400;\">&#2404; &#2357;&#2375; &#2354;&#2326;&#2344;&#2314; &#2328;&#2352;&#2366;&#2344;&#2375; &#2360;&#2375; &#2341;&#2375; &#2324;&#2352; &#2346;&#2342;&#2381;&#2350; &#2357;&#2367;&#2349;&#2370;&#2359;&#2339; (1986) &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366; &#2341;&#2375;&#2404; </span><strong>&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368; &#2344;&#2352;&#2381;&#2340;&#2325;</strong><span style=\"font-weight: 400;\"> - &#2327;&#2369;&#2352;&#2369; &#2309;&#2350;&#2369;&#2348;&#2368; &#2360;&#2367;&#2306;&#2361;, &#2319;&#2354;&#2350; &#2319;&#2306;&#2337;&#2367;&#2352;&#2366; &#2342;&#2375;&#2357;&#2368;; </span><strong>&#2349;&#2352;&#2340;&#2344;&#2366;&#2335;&#2381;&#2351;&#2350; &#2344;&#2352;&#2381;&#2340;&#2325;</strong><span style=\"font-weight: 400;\"> - &#2346;&#2342;&#2381;&#2350; &#2360;&#2369;&#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2339;&#2381;&#2351;&#2350;, &#2310;&#2352; &#2350;&#2369;&#2341;&#2369;&#2325;&#2344;&#2381;&#2344;&#2350;&#2354;; </span><strong>&#2360;&#2340;&#2381;&#2352;&#2367;&#2351;&#2366; &#2344;&#2352;&#2381;&#2340;&#2325;&#2368;</strong><span style=\"font-weight: 400;\"> - &#2311;&#2306;&#2342;&#2367;&#2352;&#2366; &#2348;&#2379;&#2352;&#2366;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<span style=\"font-family: Roboto;\"> Who among the following was famously known as the \"The parrot of India\"? </span></p>\n",
                    question_hi: "<p>100.<span style=\"font-family: Palanquin;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2375; \"&#2346;&#2376;&#2352;&#2375;&#2335; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; \" &#2325;&#2375; &#2344;&#2366;&#2350; &#2360;&#2375; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Amir Khusro</p>\n", "<p>Lata Mangeshkar</p>\n", 
                                "<p>Pandit Ravishankar</p>\n", "<p>Kalidas</p>\n"],
                    options_hi: ["<p>&#2309;&#2350;&#2368;&#2352; &#2326;&#2369;&#2360;&#2352;&#2379;</p>\n", "<p>&#2354;&#2340;&#2366; &#2350;&#2306;&#2327;&#2375;&#2358;&#2325;&#2352;</p>\n",
                                "<p>&#2346;&#2306;&#2337;&#2367;&#2340; &#2352;&#2357;&#2367; &#2358;&#2306;&#2325;&#2352;</p>\n", "<p>&#2325;&#2366;&#2354;&#2367;&#2342;&#2366;&#2360;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">100.(a)&nbsp;</span><strong>Amir Khusro : </strong><span style=\"font-weight: 400;\">He is a poet in Delhi Sultanate.</span><strong> </strong><strong>Lata Mangeshkar</strong><strong> -</strong><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">Queen of Melody\", \"Nightingale of India\", and \"Voice of the Millennium\". </span><strong>Pandit Ravi Shankar</strong><span style=\"font-weight: 400;\"> was an Indian musician and composer, best known for popularizing the Indian classical instrument Sitar all over the world. </span><strong>Kalidasa </strong><span style=\"font-weight: 400;\">was a Classical Sanskrit author who is often considered ancient India\'s greatest poet and playwright.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">100.(a)&nbsp;</span><strong>&#2309;&#2350;&#2368;&#2352; &#2326;&#2369;&#2360;&#2352;&#2379; : </strong><span style=\"font-weight: 400;\">&#2357;&#2361; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2360;&#2354;&#2381;&#2340;&#2344;&#2340; &#2350;&#2375;&#2306; &#2319;&#2325; &#2325;&#2357;&#2367; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2354;&#2340;&#2366; &#2350;&#2306;&#2327;&#2375;&#2358;&#2325;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2350;&#2375;&#2354;&#2379;&#2337;&#2368; &#2325;&#2368; &#2352;&#2366;&#2344;&#2368;\", \"&#2344;&#2366;&#2311;&#2335;&#2367;&#2306;&#2327;&#2375;&#2354; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;\", &#2324;&#2352; \"&#2357;&#2377;&#2351;&#2360; &#2321;&#2347; &#2342; &#2350;&#2367;&#2354;&#2375;&#2344;&#2367;&#2351;&#2350;\"&#2404; </span><strong>&#2346;&#2306;&#2337;&#2367;&#2340; &#2352;&#2357;&#2367;&#2358;&#2306;&#2325;&#2352; </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2327;&#2368;&#2340;&#2325;&#2366;&#2352; &#2324;&#2352; &#2352;&#2330;&#2351;&#2367;&#2340;&#2366; &#2341;&#2375;, &#2332;&#2367;&#2344;&#2381;&#2361;&#2375;&#2306; &#2346;&#2370;&#2352;&#2368; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2357;&#2366;&#2342;&#2381;&#2351; &#2351;&#2306;&#2340;&#2381;&#2352; &#2360;&#2367;&#2340;&#2366;&#2352; &#2325;&#2379; &#2354;&#2379;&#2325;&#2346;&#2381;&#2352;&#2367;&#2351; &#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2325;&#2366;&#2354;&#2367;&#2342;&#2366;&#2360; </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340; &#2354;&#2375;&#2326;&#2325; &#2341;&#2375; &#2332;&#2367;&#2344;&#2381;&#2361;&#2375;&#2306; &#2309;&#2325;&#2381;&#2360;&#2352; &#2346;&#2381;&#2352;&#2366;&#2330;&#2368;&#2344; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2350;&#2361;&#2366;&#2344; &#2325;&#2357;&#2367; &#2324;&#2352; &#2344;&#2366;&#2335;&#2325;&#2325;&#2366;&#2352; &#2350;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>