<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Who among the following is the world renowned exponent of the bamboo flute?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन बाँस की बाँसुरी के विश्व विख्यात प्रतिवादक हैं?</p>",
                    options_en: ["<p>MS Subbulakshmi</p>", "<p>Ravi Shankar</p>", 
                                "<p>Hariprasad Chaurasia</p>", "<p>Bismillah Khan</p>"],
                    options_hi: ["<p>एम एस सुब्बुलक्ष्मी</p>", "<p>रवि शंकर</p>",
                                "<p>हरिप्रसाद चौरसिया</p>", "<p>बिस्मिल्लाह खाँ</p>"],
                    solution_en: "<p>1.(c) <strong>Hariprasad Chaurasia.</strong> His Awards : Sangeet Natak Academy award (1984), Padma Bhushan (1992), Padma Vibhushan (2000). Musical Instruments and exponents: Flute - Pannalal Ghosh, T.R. Mahalingam, N. Ramani. Sitar - Pt Ravi Shankar, Shahid Parvez Khan, Budhaditya Mukherjee. Shehnai - Bismillah Khan, Krishna Ram Chaudhary, Ali Ahmad Hussain.</p>",
                    solution_hi: "<p>1.(c) <strong>हरिप्रसाद चौरसिया। </strong>उनके पुरस्कार: संगीत नाटक अकादमी पुरस्कार (1984), पद्म भूषण (1992), पद्म विभूषण (2000)। संगीत वाद्ययंत्र और वादक: बांसुरी - पन्नालाल घोष, टी.आर. महालिंगम, एन. रमानी। सितार - पं. रविशंकर, शाहिद परवेज़ खान, बुधादित्य मुखर्जी। शहनाई - बिस्मिल्लाह खान, कृष्णा राम चौधरी, अली अहमद हुसैन।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. What is net investment?</p>",
                    question_hi: "<p>2. शुद्ध निवेश क्या है?</p>",
                    options_en: ["<p>Sum of all the investments in a country</p>", "<p>Gross investment + depreciation</p>", 
                                "<p>Gross capital investment - indirect taxes</p>", "<p>Gross investment - depreciation</p>"],
                    options_hi: ["<p>किसी देश में सभी निवेशों का योग</p>", "<p>सकल निवेश + मूल्यह्रास</p>",
                                "<p>सकल पूंजी निवेश - अप्रत्यक्ष कर</p>", "<p>सकल निवेश - मूल्यह्रास</p>"],
                    solution_en: "<p>2.(d) <strong>Gross investment - depreciation.</strong> Net Investment: It refers to the actual addition to the capital stock of a country or business, accounting for the depreciation of existing assets. Gross Investment: This is the total expenditure on new assets, including new buildings, machinery, equipment, and inventory. Depreciation: This refers to the reduction in value of existing assets due to wear and tear, obsolescence, and damage.</p>",
                    solution_hi: "<p>2.(d) <strong>सकल निवेश - मूल्यह्रास। </strong>शुद्ध निवेश: यह किसी देश या व्यवसाय के पूंजी स्टॉक में वास्तविक वृद्धि को संदर्भित करता है, जिसमें मौजूदा परिसंपत्तियों के मूल्यह्रास को शामिल किया जाता है। सकल निवेश: यह नई इमारतों, मशीनरी, उपकरण और इन्वेंट्री सहित नई परिसंपत्तियों पर कुल व्यय है। मूल्यह्रास: यह टूट-फूट, अप्रचलन और क्षति के कारण मौजूदा परिसंपत्तियों के मूल्य में कमी को संदर्भित करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. India won the ICC Men Cricket World Cup for the first time in which of the following years?</p>",
                    question_hi: "<p>3. निम्नलिखित में से किस वर्ष में भारत ने पहली बार आई.सी.सी. (ICC) पुरुष क्रिकेट विश्व कप जीता?</p>",
                    options_en: ["<p>1996</p>", "<p>1992</p>", 
                                "<p>1987</p>", "<p>1983</p>"],
                    options_hi: ["<p>1996 में</p>", "<p>1992 में</p>",
                                "<p>1987 में</p>", "<p>1983 में</p>"],
                    solution_en: "<p>3.(d) <strong>1983 </strong>Cricket World Cup was held in England and Wales, India defeated the West Indies in the final under the captaincy of Kapil Dev. Roger Binny was the highest wicket-taker with 18 wickets. Mohinder Amarnath was \'Man of the Match\' in both the semi-final and final. India won their second World Cup in 2011, after defeating Sri Lanka in the final at Wankhede Stadium in Mumbai.</p>",
                    solution_hi: "<p>3.(d) <strong>1983 </strong>क्रिकेट विश्व कप इंग्लैंड और वेल्स में आयोजित किया गया था, भारत ने कपिल देव की कप्तानी में फाइनल में वेस्टइंडीज को हराया था। रोजर बिन्नी 18 विकेट लेकर सर्वाधिक विकेट लेने वाले गेंदबाज थे। मोहिंदर अमरनाथ सेमीफाइनल और फाइनल दोनों में \'मैन ऑफ द मैच\' रहे। भारत ने 2011 में मुंबई के वानखेड़े स्टेडियम में श्रीलंका को फाइनल में हराकर अपना दूसरा विश्व कप जीता था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Match the following institutes with their respective founders of British India.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730956857364.png\" alt=\"rId4\" width=\"426\" height=\"110\"></p>",
                    question_hi: "<p>4. ब्रिटिश भारत के, निम्नलिखित संस्थानों का उनसे संबंधित संस्थापकों के साथ मिलान कीजिए।</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730956857684.png\" alt=\"rId5\" width=\"472\" height=\"138\"></p>",
                    options_en: ["<p>a-ii, b-i, c-iv, d-iii</p>", "<p>a-iii, b-iv, c-i, d-ii</p>", 
                                "<p>a-iv, b-iii, c-ii, d-i</p>", "<p>a-i, b-ii, c-iii, d-iv</p>"],
                    options_hi: ["<p>a-ii, b-i, c-iv, d-iii</p>", "<p>a-iii, b-iv, c-i, d-ii</p>",
                                "<p>a-iv, b-iii, c-ii, d-i</p>", "<p>a-i, b-ii, c-iii, d-iv <br><br><br></p>"],
                    solution_en: "<p>4.(c) <strong>A-iv, B-iii, C-ii, D-i.</strong></p>",
                    solution_hi: "<p>4.(c) <strong>A-iv, B-iii, C-ii, D-i.</strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Kathakali, one of the classical dances of India, is predominantly performed in which of the following states of India?</p>",
                    question_hi: "<p>5. कथकली जो भारत के शास्त्रीय नृत्यों में से एक नृत्&zwj;य है, मुख्य रूप से भारत के निम्नलिखित में से किस राज्य में किया जाता है?</p>",
                    options_en: ["<p>Assam</p>", "<p>Manipur</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>असम</p>", "<p>मणिपुर</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>केरल</p>"],
                    solution_en: "<p>5.(d) <strong>Kerala</strong>. Kathakali, a classical Indian dance form, blends storytelling with expressive costumes.The characters in a Kathakali performance are broadly divided into satvika, rajasika and tamasika types. The performance is accompanied by traditional drums such as Maddalam, Chenda, and Idakka.</p>",
                    solution_hi: "<p>5.(d) <strong>केरल।</strong> कथकली, एक शास्त्रीय भारतीय नृत्य शैली है, जिसमें कहानी कहने के साथ-साथ भावपूर्ण वेशभूषा का भी मिश्रण होता है। कथकली प्रदर्शन में पात्रों को सामान्य तौर पर सात्विक, राजसिक और तामसिक प्रकारों में विभाजित किया जाता है। प्रदर्शन के साथ मद्दलम, चेंडा और इडक्का जैसे पारंपरिक ढोल बजाए जाते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Identify the oldest iron and steel company of India from the following options.</p>",
                    question_hi: "<p>6. निम्नलिखित विकल्पों में से भारत की सबसे पुरानी लौह-इस्पात कंपनी की पहचान कीजिए।</p>",
                    options_en: ["<p>Tata Iron &amp; Steel Company (TISCO)</p>", "<p>Visvesvaraiya Iron &amp; Steel Works</p>", 
                                "<p>Indian Iron &amp; Steel Company (IISCO)</p>", "<p>Mysore Iron &amp; Steel Works</p>"],
                    options_hi: ["<p>टाटा आयरन एंड स्टील कंपनी (TISCO)</p>", "<p>विश्वेश्वरैया आयरन एंड स्टील वर्क्स</p>",
                                "<p>इंडियन आयरन एंड स्टील कंपनी (IISCO)</p>", "<p>मैसूर आयरन एंड स्टील वर्क्स</p>"],
                    solution_en: "<p>6.(a)<strong> Tata Iron and Steel Company (TISCO): </strong>Founded by Jamsetji Tata and Dorabji Tata in 1907, It is located in Jamshedpur (Jharkhand). Visvesvaraya Iron and Steel Plant: Founded in 1923, this plant is situated in Bhadravathi (Karnataka), and specializes in the production of pig iron and steel. Indian Iron and Steel Company (IISCO): Established in 1918, IISCO is an integrated steel plant located in Burnpur (West Bengal) and is part of Steel Authority of India Limited (SAIL).</p>",
                    solution_hi: "<p>6.(a)<strong> टाटा आयरन एंड स्टील कंपनी (TISCO): </strong>इसकी स्थापना 1907 में जमशेदजी टाटा और दोराबजी टाटा द्वारा की गई थी । यह जमशेदपुर (झारखंड) में स्थित है। विश्वेश्वरैया आयरन एंड स्टील प्लांट: 1923 में स्थापित, यह प्लांट भद्रावती (कर्नाटक) में स्थित है, और पिग आयरन और स्टील के उत्पादन में अग्रणी है। इंडियन आयरन एंड स्टील कंपनी (IISCO): 1918 में स्थापित, IISCO एक एकीकृत स्टील प्लांट है जो बर्नपुर (पश्चिम बंगाल) में स्थित है और स्टील अथॉरिटी ऑफ इंडिया लिमिटेड (SAIL) का हिस्सा है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The magnificent Kailasa temple at Ellora was built during the reign of which Rashtrakuta king?</p>",
                    question_hi: "<p>7. एलोरा का भव्य कैलास मंदिर किस राष्ट्रकूट राजा के शासनकाल के दौरान बनाया गया था?</p>",
                    options_en: ["<p>Krishna I</p>", "<p>Indra III</p>", 
                                "<p>Govinda III</p>", "<p>Amoghavarsha</p>"],
                    options_hi: ["<p>कृष्ण प्रथम (Krishna I)</p>", "<p>इंद्र तृतीय (Indra III)</p>",
                                "<p>गोविंद तृतीय (Govinda III)</p>", "<p>अमोघवर्ष (Amoghavarsha)</p>"],
                    solution_en: "<p>7.(a) <strong>Krishna I.</strong> The Kailashnatha Temple in Ellora, Maharashtra, dedicated to Lord Shiva, is renowned for its Dravidian architecture and intricate sculptures, showcasing the engineering and artistic brilliance of ancient India. Famous Heritage Sites in India: Elephanta Caves (Rashtrakuta Kings), Khajurao Temples (Chandela Dynasty), Rani ki Vav (Queen Udayamati).</p>",
                    solution_hi: "<p>7.(a)<strong> कृष्ण प्रथम।</strong> महाराष्ट्र के एलोरा में भगवान शिव को समर्पित कैलाशनाथ मंदिर अपनी द्रविड़ वास्तुकला और जटिल मूर्तियों के लिए प्रसिद्ध है, जो प्राचीन भारत की इंजीनियरिंग और कलात्मक प्रतिभा को दर्शाता है। भारत में प्रसिद्ध विरासत स्थल: एलीफेंटा गुफाएँ (राष्ट्रकूट राजा), खजुराहो मंदिर (चंदेल राजवंश), रानी की वाव (रानी उदयमती)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. In which state/UT is the Hemis festival celebrated?</p>",
                    question_hi: "<p>8. हेमिस उत्सव किस राज्य/केंद्र शासित प्रदेश में मनाया जाता है?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Sikkim</p>", 
                                "<p>Lakshadweep</p>", "<p>Ladakh</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>सिक्किम</p>",
                                "<p>लक्षद्वीप</p>", "<p>लद्दाख</p>"],
                    solution_en: "<p>8.(d) <strong>Ladakh</strong>. Hemis festival is celebrated to mark the birth anniversary of Guru Padmasambhava. Festivals in India: Ladakh: Sindhu Darshan Festival, Losar Festival, Tak-Thok, Matho Nagrang. Punjab: Lohri, Baisakhi, Hola Mohalla, Gurupurab. Sikkim: Loosong/Namsoong, Sonam Lochhar, Losar (Tibetan New Year), Bumchu.</p>",
                    solution_hi: "<p>8.(d) <strong>लद्दाख</strong>। हेमिस उत्सव गुरु पद्मसंभव की जयंती के उपलक्ष्य में मनाया जाता है। भारत में त्योहार: लद्दाख: सिंधु दर्शन महोत्सव, लोसर महोत्सव, ताक-थोक, माथो नागरंग। पंजाब: लोहड़ी, बैसाखी, होला मोहल्ला, गुरुपर्व। सिक्किम: लूसोंग/नामसूंग, सोनम लोचर, लोसार (तिब्बती नव वर्ष), बुमचू।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following is the largest artificial lake of Asia?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सी एशिया की सबसे बड़ी कृत्रिम झील है?</p>",
                    options_en: ["<p>Naini Lake</p>", "<p>Bhopal Lake</p>", 
                                "<p>Dal Lake</p>", "<p>Chilika Lake</p>"],
                    options_hi: ["<p>नैनी झील</p>", "<p>भोपाल झील</p>",
                                "<p>डल झील</p>", "<p>चिल्का झील</p>"],
                    solution_en: "<p>9.(b) <strong>Bhopal Lake, </strong>also known as Bhojtal, was created in the 11th century by King Bhoja of the Paramara dynasty. It is situated in Bhopal, Madhya Pradesh, and is constructed on Kolans river. Lakes in Madhya Pradesh: Sangram Sagar, Shahpura lake. Chilika Lake: It is the largest brackish water lake in Asia, situated in Odisha. Dal Lake - It is often referred to as Srinagar\'s Jewel, is a freshwater lake located in Srinagar, Jammu and Kashmir.</p>",
                    solution_hi: "<p>9.(b) <strong>भोपाल झील, </strong>जिसे भोजताल के नाम से भी जाना जाता है, 11वीं शताब्दी में परमार वंश के राजा भोज द्वारा बनवाई गई थी। यह मध्य प्रदेश के भोपाल में स्थित है और कोलांस नदी पर निर्मित है। मध्य प्रदेश में झीलें: संग्राम सागर, रानी झील, शाहपुरा झील। चिल्का झील: यह ओडिशा में स्थित एशिया की सबसे बड़ी खारे पानी की झील है। डल झील - इसे श्रीनगर का गहना भी कहा जाता है, यह जम्मू और कश्मीर के श्रीनगर में स्थित एक मीठे पानी की झील है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. In 2002, Zakir Hussain became the youngest percussionist to be honoured with which award?</p>",
                    question_hi: "<p>10. वर्ष 2002 में, ज़ाकिर हुसैन किस पुरस्कार से सम्मानित होने वाले सबसे कम उम्र के तालवादक बने?</p>",
                    options_en: ["<p>Sangeet Natak Akademi Award</p>", "<p>Grammy Award</p>", 
                                "<p>Padma Bhushan</p>", "<p>National Heritage Fellowship</p>"],
                    options_hi: ["<p>संगीत नाटक अकादमी पुरस्कार</p>", "<p>ग्रैमी पुरस्कार</p>",
                                "<p>पद्म भूषण</p>", "<p>नेशनल हेरिटेज फैलोशिप</p>"],
                    solution_en: "<p>10.(c) <strong>Padma Bhushan.</strong> Ustad Zakir Hussain is the eldest son of tabla player Alla Rakha. His Awards: Sangeet Natak Akademi Award (1991), Padma Shri (1988), Padma Vibhushan (2023). He won three Grammys at the 66th Annual Grammy Awards in 2024. Indian tabla players: Pandit Shankar Ghosh, Pandit Udhai Mazumdar, Nandan Mehta, Pandit Swapan Chaudhuri, Pandit Vijay Ghate.</p>",
                    solution_hi: "<p>10.(c) <strong>पद्म भूषण। </strong>उस्ताद ज़ाकिर हुसैन तबला वादक अल्ला रक्खा के सबसे बड़े पुत्र हैं। उनके पुरस्कार: संगीत नाटक अकादमी पुरस्कार (1991), पद्म श्री (1988), पद्म विभूषण (2023)। उन्होंने 2024 में 66वें वार्षिक ग्रैमी अवार्ड्स में तीन ग्रैमी पुरस्कार जीते थे। भारतीय तबला वादक: पंडित शंकर घोष, पंडित उदय मजूमदार, नंदन मेहता, पंडित स्वपन चौधरी, पंडित विजय घाटे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Microbes like Rhizobium, Nitrosomonas and Nitrobacter are used for:</p>",
                    question_hi: "<p>11. राइजोबियम, नाइट्रोसोमोनास और नाइट्रोबैक्टर जैसे सूक्ष्मजीवों का उपयोग कहाँ किया जाता है?</p>",
                    options_en: ["<p>nitrogen cycling</p>", "<p>carbon cycling</p>", 
                                "<p>water cycling</p>", "<p>sulphur cycling</p>"],
                    options_hi: ["<p>नाइट्रोजन चक्र</p>", "<p>कार्बन चक्र</p>",
                                "<p>जल चक्र</p>", "<p>सल्फर चक्र</p>"],
                    solution_en: "<p>11.(a) <strong>nitrogen cycling.</strong> Nitrogen cycle is the biogeochemical cycle that describes the transformation of nitrogen and nitrogen-containing compounds in nature. Atmospheric nitrogen is the biggest source of nitrogen. Green plants absorb nitrogen in the form of nitrates and nitrites from the soil and water. Animals get nitrogen when they feed upon plants. Carbon cycle - In which carbon is exchanged between soil, water and atmosphere (air) of the earth. Sulfur cycle - The movement of sulfur through the atmosphere, mineral forms, and through living things. Water cycle - The continuous movement of water from the earth to the atmosphere.</p>",
                    solution_hi: "<p>11.(a)<strong> नाइट्रोजन चक्र।</strong> नाइट्रोजन चक्र जैव-रासायनिक चक्र है जो प्रकृति में नाइट्रोजन और नाइट्रोजन युक्त यौगिकों के परिवर्तन का वर्णन करता है। वायुमंडलीय नाइट्रोजन, नाइट्रोजन का सबसे बड़ा स्रोत है। हरे पौधे मिट्टी और जल से नाइट्रेट और नाइट्राइट के रूप में नाइट्रोजन को अवशोषित करते हैं। जानवर पौधों को खा करके नाइट्रोजन प्राप्त करते है। कार्बन चक्र - जिसमें मृदा, जल और पृथ्वी के वायुमंडल (वायु) के बीच कार्बन का आदान-प्रदान होता है। सल्फर चक्र - वायुमंडल, खनिज रूपों और जीवित चीजों के माध्यम से सल्फर की संचलन। जल चक्र - पृथ्वी से वायुमंडल में जल का निरंतर संचलन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. With reference to Sepoy Mutiny of 1857, on which of the following dates did the soldiers at Meerut start their journey to Delhi?</p>",
                    question_hi: "<p>12. 1857 के सिपाही विद्रोह के संदर्भ में, निम्नलिखित में से किस तारीख को मेरठ के सैनिकों ने दिल्ली के लिए यात्रा शुरू की थी?</p>",
                    options_en: ["<p>10 May</p>", "<p>19 April</p>", 
                                "<p>2 June</p>", "<p>29 March</p>"],
                    options_hi: ["<p>10 मई</p>", "<p>19 अप्रैल</p>",
                                "<p>2 जून</p>", "<p>29 मार्च</p>"],
                    solution_en: "<p>12.(a) <strong>10 May. </strong>The Revolt of 1857, the first major rebellion against the British East India Company, began in Meerut. The immediate cause of this revolt was the introduction of the Enfield rifle and its greased cartridges. In March 1857, Mangal Pandey, a sepoy in Barrackpore, refused to use the new cartridges because of the rumor that they were greased with pork and beef fat, which violated their religious beliefs, and he attacked his officers, which led to his execution on April 8. On 9th May, 85 soldiers in Meerut refused the new rifles and were imprisoned. Lord Canning was the British viceroy during the revolt.</p>",
                    solution_hi: "<p>12.(a) <strong>10 मई। </strong>1857 का विद्रोह, ब्रिटिश ईस्ट इंडिया कंपनी के विरुद्ध प्रथम बड़ा विद्रोह, मेरठ में शुरू हुआ था। इस विद्रोह का तात्कालिक कारण एनफील्ड राइफल और उसके चर्बी युक्त कारतूसों का प्रचलन था। मार्च 1857 में, बैरकपुर के एक सिपाही मंगल पांडे ने नए कारतूसों का इस्तेमाल करने से इनकार कर दिया था क्योंकि यह अफवाह थी कि उनमें सूअर और गाय की चर्बी लगी हुई थी, जो उनकी धार्मिक मान्यताओं का उल्लंघन था, और उसने अपने अधिकारियों पर हमला कर दिया, जिसके परिणामस्वरूप 8 अप्रैल को उन्हें फांसी दे दी गई थी। 9 मई को, मेरठ में 85 सैनिकों ने नई राइफलों को लेने से इनकार कर दिया और उन्हें जेल में डाल दिया गया। विद्रोह के दौरान लॉर्ड कैनिंग ब्रिटिश वायसराय थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who among the following was selected as the Sherpa for India&rsquo;s G20 hosted in 2022-23 ?</p>",
                    question_hi: "<p>13. निम्नलिखित में से किसे 2022-23 में आयोजित हुए जी-20 सम्मेलन में शेरपा के रूप में चुना गया?</p>",
                    options_en: ["<p>Piyush Goyal</p>", "<p>Ashwini Vaishnav</p>", 
                                "<p>Shaktikanta Das</p>", "<p>Amitabh Kant</p>"],
                    options_hi: ["<p>पियूष गोयल</p>", "<p>अश्विनी वैष्णव</p>",
                                "<p>शक्तिकांत दास</p>", "<p>अमिताभ कांत</p>"],
                    solution_en: "<p>13.(d) <strong>Amitabh Kant</strong> was the CEO of NITI Aayog from 2016 to 2022. The theme of India\'s G20 Presidency is \"Vasudhaiva Kutumbakam,\" which translates to \"One Earth, One Family, One Future\". The G20 was founded in 1999 after the Asian financial crisis as a forum for the Finance Ministers and Central Bank Governors to discuss global economic and financial issues.</p>",
                    solution_hi: "<p>13.(d)<strong> अमिताभ कांत </strong>2016 से 2022 तक नीति आयोग के CEO थे। भारत की जी20 प्रेसीडेंसी का विषय \"वसुधैव कुटुम्बकम\" है, जिसका अर्थ है \"एक पृथ्वी, एक परिवार, एक भविष्य\"। जी20 की स्थापना 1999 में एशियाई वित्तीय संकट के बाद वित्त मंत्रियों और केंद्रीय बैंक गवर्नरों के लिए वैश्विक आर्थिक और वित्तीय मुद्दों पर चर्चा करने के लिए एक मंच के रूप में की गई थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Purvanchal Himalayas does NOT comprise of :</p>",
                    question_hi: "<p>14. पूर्वांचल हिमालय में निम्नलिखित में से कौन-सा शामिल नहीं है?</p>",
                    options_en: ["<p>Naga hills</p>", "<p>Pir Panjal range</p>", 
                                "<p>Manipur hills</p>", "<p>Mizo hills</p>"],
                    options_hi: ["<p>नागा पहाड़ियाँ</p>", "<p>पीर पंजाल पर्वत श्रृंखला</p>",
                                "<p>मणिपुर की पहाड़ियाँ</p>", "<p>मिज़ो पहाड़ियाँ</p>"],
                    solution_en: "<p>14.(b)<strong> Pir Panjal Range</strong> in the Himalayas is a part of the Lesser Himalayas. The Pir Panjal Range starts near PatniTop in Jammu and Kashmir and runs through Himachal Pradesh to Garhwal. The Purvanchal Himalayas, also known as the Eastern Hills, are a mountain range in northeastern India. This range includes the Patkai Hills, the Naga Hills in Nagaland, the Manipur Hills in Manipur, and the Mizo Hills in Mizoram.</p>",
                    solution_hi: "<p>14.(b) हिमालय में<strong> पीर पंजाल पर्वत</strong> <strong>श्रृंखला </strong>लघु हिमालय का एक हिस्सा है। पीर पंजाल पर्वतमाला जम्मू-कश्मीर में पटनी टॉप के पास से शुरू होती है और हिमाचल प्रदेश से होते हुए गढ़वाल तक जाती है। पूर्वांचल हिमालय, जिसे पूर्वी पहाड़ियों के नाम से भी जाना जाता है, पूर्वोत्तर भारत में एक पर्वत श्रृंखला है। इस श्रृंखला में पटकाई पहाड़ियाँ, नागालैंड में नागा पहाड़ियाँ, मणिपुर में मणिपुर पहाड़ियाँ और मिज़ोरम में मिज़ो पहाड़ियाँ शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. In which year did India make its Olympic debut in hockey?</p>",
                    question_hi: "<p>15. भारत ने हॉकी में ओलंपिक में अपनी शुरूआत किस वर्ष की थी?</p>",
                    options_en: ["<p>1936</p>", "<p>1932</p>", 
                                "<p>1924</p>", "<p>1928</p>"],
                    options_hi: ["<p>1936</p>", "<p>1932</p>",
                                "<p>1924</p>", "<p>1928</p>"],
                    solution_en: "<p>15.(d) <strong>1928</strong>. India made its Olympic debut in hockey in 1928 at the Amsterdam Olympics. The Indian men\'s hockey team won their first Olympic gold medal in this tournament, beating the Netherlands. The team was led by captain Jaipal Singh and Dhyan Chand, who scored the most goals with 14. India\'s hockey team is the most successful in Olympic history, winning eight gold medals in 1928, 1932, 1936, 1948, 1952, 1956, 1964, and 1980.</p>",
                    solution_hi: "<p>15.(d) <strong>1928</strong>. भारत ने हॉकी में अपना ओलंपिक पदार्पण 1928 में एम्स्टर्डम ओलंपिक में किया था। भारतीय पुरुष हॉकी टीम ने इस टूर्नामेंट में नीदरलैंड को हराकर अपना पहला ओलंपिक स्वर्ण पदक जीता था। टीम का नेतृत्व कप्तान जयपाल सिंह और ध्यानचंद ने किया था, जिन्होंने सबसे अधिक 14 गोल किए थे। भारत की हॉकी टीम ओलंपिक इतिहास में सबसे सफल है, जिसने 1928, 1932, 1936, 1948, 1952, 1956, 1964 और 1980 में आठ स्वर्ण पदक जीते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. A student, on his school assignment, is taking a session on how to make compost at home for using it at a park. Which fundamental duty is he performing?",
                    question_hi: "16. एक विद्यार्थी अपने स्कूल असाइनमेंट में, पार्क में उपयोग करने के लिए घर पर खाद बनाने का तरीका सीख रहा है। वह निम्नलिखित में से कौन-सा मौलिक कर्तव्य निभा रहा है?",
                    options_en: [" To strive towards excellence in all spheres of individual and collective activity", " To safeguard public property and to abjure violence", 
                                " To develop the scientific temper, humanism and the spirit of inquiry", " To protect and improve the natural environment"],
                    options_hi: [" व्यक्तिगत और सामूहिक गतिविधि के सभी क्षेत्रों में उत्कृष्टता की दिशा में सतत प्रयास करना", " सार्वजनिक संपत्ति की सुरक्षा करना और हिंसा से दूर रहना",
                                " वैज्ञानिक दृष्टिकोण, मानवतावाद और अन्वेषण की भावना विकसित करना", " प्राकृतिक पर्यावरण की रक्षा और संवर्द्धन करना"],
                    solution_en: "16.(d) Fundamental Duty (Part IVA - Article 51A). Related Article: Article 51A(g) - To protect and improve the natural environment. Article 51A (h):  To develop the scientific temper, humanism and the spirit of inquiry and reform; Article 51A(i) : To safeguard public property and to abjure violence; Article 51A(j): To strive towards excellence in all spheres of individual and collective activity so that the nation constantly rises to higher levels of endeavour and achievement.",
                    solution_hi: "16.(d) मौलिक कर्तव्य (भाग IVA - अनुच्छेद 51A)। संबंधित अनुच्छेद : अनुच्छेद 51A(g) - प्राकृतिक पर्यावरण की रक्षा और सुधार करना। अनुच्छेद 51A (h): वैज्ञानिक दृष्टिकोण, मानवतावाद तथा जिज्ञासा और सुधार की भावना का विकास करना। अनुच्छेद 51A (i): सार्वजनिक संपत्ति की सुरक्षा करना और हिंसा से दूर रहना। अनुच्छेद 51A(j): व्यक्तिगत और सामूहिक गतिविधि के सभी क्षेत्रों में उत्कृष्टता की ओर बढ़ने का प्रयास करना ताकि राष्ट्र निरंतर प्रयास और उपलब्धि के उच्च स्तर तक पहुंच सके।<br /> ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. In August 2022, the Ministry of Social Justice and Empowerment launched the _____ scheme, with an aim to provide comprehensive rehabilitation services to people engaged in begging in 75 municipalities.</p>",
                    question_hi: "<p>17. अगस्त 2022 में सामाजिक न्याय और अधिकारिता मंत्रालय ने 75 नगर पालिकाओं में भीख माँगने वाले लोगों को व्यापक पुनर्वास सेवाएँ प्रदान करने के उद्देश्य से ______ योजना शुरू की।</p>",
                    options_en: ["<p>TWINKLE-75</p>", "<p>BEAM-75</p>", 
                                "<p>SMILE-75</p>", "<p>RISE-75</p>"],
                    options_hi: ["<p>ट्विंकल-75</p>", "<p>बीम-75</p>",
                                "<p>स्माइल-75</p>", "<p>राइज-75</p>"],
                    solution_en: "<p>17.(c) <strong>SMILE-75. </strong>The objective of SMILE- 75 is to make our cities/town and municipal areas begging-free. Other Schemes of the Ministry of Social Justice and Empowerment: PM-SURAJ (2024), PM-AJAY (2021-2022), PM-DAKSH (2020-2021).</p>",
                    solution_hi: "<p>17.(c) <strong>स्माइल-75. </strong>SMILE-75 का उद्देश्य हमारे शहरों/कस्बों और नगरपालिका क्षेत्रों को भिक्षावृत्ति से मुक्त बनाना है। सामाजिक न्याय और अधिकारिता मंत्रालय की अन्य योजनाएँ: PM-SURAJ (2024), PM-AJAY (2020-2021), PM-DAKSH (2020-2021)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which is the National Mission for Financial Inclusion to ensure access to financial services, namely, a basic savings and deposits accounts, remittance, credit, insurance, pension in an affordable manner?</p>",
                    question_hi: "<p>18. वित्तीय सेवाओं, अर्थात् मूल बचत और जमा खातों, विप्रेषण, ऋण, बीमा, पेंशन तक किफायती तरीके से पहुंच सुनिश्&zwj;चित करने के लिए वित्तीय समावेशन के लिए राष्ट्रीय मिशन कौन सा है?</p>",
                    options_en: ["<p>Deendayal Antyodaya Yojana</p>", "<p>Deen Dayal Upadhyaya Grameen Kaushalya Yojana</p>", 
                                "<p>Swarnjayanti Gram Swarozgar Yojana</p>", "<p>Pradhan Mantri Jan Dhan Yojana</p>"],
                    options_hi: ["<p>दीनदयाल अंत्योदय योजना</p>", "<p>दीन दयाल उपाध्याय ग्रामीण कौशल्&zwj;य योजना</p>",
                                "<p>स्वर्णजयंती ग्राम स्वरोज़गार योजना</p>", "<p>प्रधानमंत्री जन धन योजना</p>"],
                    solution_en: "<p>18.(d) <strong>Pradhan Mantri Jan Dhan Yojana (PMJDY) </strong>is a National Mission for Financial Inclusion launched on August 28, 2014. Deendayal Antyodaya Yojana - National Rural Livelihoods Mission (DAY-NRLM): Aim to promote poverty reduction through building strong institutions for the poor, particularly women, and enabling these institutions to access a range of financial services and livelihoods. Deen Dayal Upadhyaya Grameen Kaushalya Yojana (2014): Aims at skill development and placement for rural youth. Swarnjayanti Gram Swarozgar Yojana (1999): A self-employment program for rural poor.</p>",
                    solution_hi: "<p>18.(d) <strong>प्रधानमंत्री जन धन योजना (PMJDY)</strong> 28 अगस्त, 2014 को शुरू किया गया वित्तीय समावेशन के लिए एक राष्ट्रीय मिशन है। दीनदयाल अंत्योदय योजना - राष्ट्रीय ग्रामीण आजीविका मिशन (DAY-NRLM): इसका उद्देश्य गरीबों, विशेषकर महिलाओं के लिए मजबूत संस्थाओं का निर्माण करके गरीबी में कमी लाना तथा इन संस्थाओं को वित्तीय सेवाओं और आजीविका तक पहुँच प्रदान करना है। दीनदयाल उपाध्याय ग्रामीण कौशल योजना (2014): इसका उद्देश्य ग्रामीण युवाओं के लिए कौशल विकास और रोजगार उपलब्ध कराना है। स्वर्ण जयंती ग्राम स्वरोजगार योजना (1999): ग्रामीण गरीबों के लिए एक स्वरोजगार कार्यक्रम।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Which of the following plays was NOT written by Harshavardhana ?</p>",
                    question_hi: "<p>19. निम्नलिखित में से कौन-सा नाटक हर्षवर्द्धन द्वारा नहीं लिखा गया था?</p>",
                    options_en: ["<p>Vikramorvasiyam</p>", "<p>Ratnavali</p>", 
                                "<p>Nagananda</p>", "<p>Priyadarshika</p>"],
                    options_hi: ["<p>विक्रमोर्वशीयम्</p>", "<p>रत्नावली</p>",
                                "<p>नागानंद</p>", "<p>प्रियदर्शिका</p>"],
                    solution_en: "<p>19.(a) <strong>Vikramorvasiyam </strong>is a romantic play written by Kalidasa, based on the legend of King Pururava and an Apsara named Urvashi. Kalidasa\'s works include: Abhijnanasakuntalam, Malavikagnimitram, Kumarasambhavam. Harshavardhana belonged to the Pushyabhuti dynasty, also known as the Vardhana Dynasty, founded by Naravardhana. Harsha\'s reign is documented in Harshacharitra, written by his court poet Banabhatta.</p>",
                    solution_hi: "<p>19.(a) <strong>विक्रमोर्वशीयम् </strong>कालिदास द्वारा लिखित एक प्रेमपूर्ण नाटक है, जो राजा पुरुरवा और उर्वशी नामक एक अप्सरा की कथा पर आधारित है। कालिदास की कृतियों में शामिल हैं: अभिज्ञानशाकुंतलम, मालविकाग्निमित्रम, कुमारसंभवम। हर्षवर्धन पुष्यभूति वंश से संबंधित थे, जिसे वर्धन वंश के नाम से भी जाना जाता है, जिसकी स्थापना नरवर्धन ने की थी। हर्ष के शासनकाल का विवरण उनके दरबारी कवि बाणभट्ट द्वारा लिखित हर्षचरित्र में मिलता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. The organisms that do not have a defined nucleus or organelles are classified into _______ Kingdom.</p>",
                    question_hi: "<p>20. वे जीव जिनमें निश्चित केन्द्रक या अंगक नहीं होते, उन्हें _______ जगत में वर्गीकृत किया जाता है।</p>",
                    options_en: ["<p>Fungi</p>", "<p>Protista</p>", 
                                "<p>Monera</p>", "<p>Plantae</p>"],
                    options_hi: ["<p>फंजाई (Fungi)</p>", "<p>प्रोटिस्टा (Protista)</p>",
                                "<p>मोनेरा (Monera)</p>", "<p>प्लांटी (Plantae)</p>"],
                    solution_en: "<p>20.(c) <strong>Monera </strong>- It comprises unicellular, prokaryotic organisms, having no well-defined nucleus and no organelles. Bacteria are the sole members of the Kingdom Monera. Kingdom Monera is classified into three sub-kingdoms- Archaebacteria, Eubacteria, and Cyanobacteria. Protista - The protistan cell body contains a well defined nucleus and other membrane-bound organelles. Plantae - It includes all eukaryotic chlorophyll-containing organisms commonly called plants. Fungi - They are eukaryotic organisms that include microorganisms such as yeasts, moulds and mushrooms.</p>",
                    solution_hi: "<p>20.(c) <strong>मोनेरा</strong> <strong>(Monera) </strong>- इसमें एककोशिकीय, प्रोकैरियोटिक जीव शामिल हैं, जिनमें कोई स्पष्ट केन्द्रक और कोई कोशिकांग नहीं होता है। बैक्टीरिया मोनेरा जगत के एकमात्र सदस्य हैं। मोनेरा जगत को तीन उप-जगतों में वर्गीकृत किया गया है - आर्कीबैक्टीरिया, यूबैक्टीरिया और साइनोबैक्टीरिया। प्रोटिस्टा - प्रोटिस्टा कोशिका शरीर में एक स्पष्ट केन्द्रक और अन्य झिल्ली-बद्ध कोशिकांग होते हैं। प्लांटी - इसमें सभी यूकेरियोटिक क्लोरोफिल युक्त जीव शामिल हैं जिन्हें सामान्यतः पौधे कहा जाता है। कवक - वे यूकेरियोटिक जीव हैं जिनमें यीस्ट, मोल्ड और मशरूम जैसे सूक्ष्मजीव शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which Article of the Constitution of India provides that &lsquo;there shall be a Vice President of India&rsquo; ?</p>",
                    question_hi: "<p>21. भारत के संविधान के किस अनुच्छेद में यह प्रावधान है कि \'भारत का एक उपराष्ट्रपति होगा\' ?</p>",
                    options_en: ["<p>Article 61</p>", "<p>Article 63</p>", 
                                "<p>Article 65</p>", "<p>Article 62</p>"],
                    options_hi: ["<p>अनुच्छेद 61</p>", "<p>अनुच्छेद 63</p>",
                                "<p>अनुच्छेद 65</p>", "<p>अनुच्छेद 62</p>"],
                    solution_en: "<p>21.(b) <strong>Article 63. </strong>The Vice President of India is the second-highest constitutional office in India, after the President. Important Articles related to Vice President: Article 64 - The Vice-President to be ex officio Chairman of the Council of States. Article 65 - The Vice-President to act as President or to discharge his functions during casual vacancies in the office, or during the absence, of President.</p>",
                    solution_hi: "<p>21.(b) <strong>अनुच्छेद 63.</strong> भारत का उपराष्ट्रपति, राष्ट्रपति के बाद भारत का दूसरा सर्वोच्च संवैधानिक पद है। उपराष्ट्रपति से संबंधित महत्त्वपूर्ण अनुच्छेद: अनुच्छेद 64 - उपराष्ट्रपति का राज्य सभा का पदेन अध्यक्ष होता है। अनुच्छेद 65 - उपराष्ट्रपति का राष्ट्रपति के रूप में कार्य करना या राष्ट्रपति के पद में आकस्मिक रिक्तियों के दौरान या राष्ट्रपति की अनुपस्थिति के दौरान उसके कार्यों का निर्वहन करना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. A javelin thrown by an athlete is in ______ motion.</p>",
                    question_hi: "<p>22. एक एथलीट द्वारा फेंका गया भाला _____ गति में होता है।</p>",
                    options_en: ["<p>oscillatory</p>", "<p>periodic</p>", 
                                "<p>rectilinear</p>", "<p>curvilinear</p>"],
                    options_hi: ["<p>दोलनी</p>", "<p>आवर्ती</p>",
                                "<p>ऋजु रेखीय</p>", "<p>वक्रीय</p>"],
                    solution_en: "<p>22.(d) <strong>curvilinear</strong>. This is because the javelin follows a curved trajectory due to the influence of gravity and the initial angle of release. Curvilinear motion refers to the motion of a particle along a curved path. Oscillatory motion: Repeated back-and-forth motion (Example - A swinging pendulum). Periodic motion: Motion repeating in regular intervals (Example - Rotation of the earth around the sun). Rectilinear motion: Motion in a straight line (Example - An object falling straight down).</p>",
                    solution_hi: "<p>22.(d)<strong> वक्रीय।</strong> ऐसा इसलिए है क्योंकि भाला गुरुत्वाकर्षण के प्रभाव और छोड़ने के प्रारंभिक कोण के कारण एक वक्र प्रक्षेप पथ का अनुसरण करता है। वक्रीय गति से तात्पर्य एक कण की एक घुमावदार पथ पर गति करने से है। दोलन गति: बार-बार आगे-पीछे होने वाली गति (उदाहरण - एक झूलता हुआ पेंडुलम)। आवर्ती गति: नियमित अंतराल में दोहराई जाने वाली गति (उदाहरण - सूर्य के चारों ओर पृथ्वी का घूमना)। ऋजु रैखिक गति: एक सीधी रेखा में गति (उदाहरण - एक वस्तु का सीधे नीचे गिरना)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. The green revolution technology resulted in an increase in production of cereal production from 72.4 million tons in 1965-66 to _____ million tons in 1978-79.</p>",
                    question_hi: "<p>23. हरित क्रांति प्रौद्योगिकी के परिणामस्वरूप अनाज उत्पादन में वृद्धि हुई जिससे उत्पादन 1965-66 में 72.4 मिलियन टन से 1978-79 में ______ मिलियन टन हो गया।</p>",
                    options_en: ["<p>150.8</p>", "<p>165.9</p>", 
                                "<p>131.9</p>", "<p>141.2</p>"],
                    options_hi: ["<p>150.8</p>", "<p>165.9</p>",
                                "<p>131.9</p>", "<p>141.2</p>"],
                    solution_en: "<p>23.(c) <strong>131.9.</strong> The cereal production was increased due to three factors: (i) Increase in net area under cultivation; (ii) Growing two or more crops in a year on the same piece of land; and (iii) Use of HYV seeds. The Green Revolution in India was spearheaded by M.S. Swaminathan.</p>",
                    solution_hi: "<p>23.(c) <strong>131.9. </strong>अनाज उत्पादन में वृद्धि तीन कारकों के कारण हुई थी: (i) खेती के तहत शुद्ध क्षेत्र में वृद्धि; (ii) एक ही भूमि पर एक वर्ष में दो या अधिक फसलें उगाना; और (iii) HYV बीजों का उपयोग। भारत में हरित क्रांति का नेतृत्व एम.एस. स्वामीनाथन ने किया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Calculate the oxidation number of &lsquo;S&rsquo; in H<sub>2</sub>S<sub>2</sub>O<sub>7</sub></p>",
                    question_hi: "<p>24. H<sub>2</sub>S<sub>2</sub>O<sub>7</sub> में \'S\' की ऑक्सीकरण संख्या की गणना कीजिए।</p>",
                    options_en: ["<p>3</p>", "<p>7</p>", 
                                "<p>6</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>7</p>",
                                "<p>6</p>", "<p>2</p>"],
                    solution_en: "<p>24.(c) <strong>6</strong>. To calculate the oxidation number of S in H<sub>2</sub>S<sub>2</sub>O<sub>7</sub>:<br>Assign oxidation numbers to known elements : H = + 1 and O = - 2<br>Write the equation: 2(+ 1) + 2x + 7(- 2) = 0<br>&rArr; 2 + 2x - 14 = 0<br>&rArr; 2x = 12<br>&rArr; x = 6.</p>",
                    solution_hi: "<p>24.(c) <strong>6</strong>. H<sub>2</sub>S<sub>2</sub>O<sub>7</sub> में S की ऑक्सीकरण संख्या की गणना करने के लिए:<br>ज्ञात तत्वों को ऑक्सीकरण संख्याएँ निर्दिष्ट करें : H = + 1 और O = - 2<br>समीकरण लिखें: 2(+ 1) + 2x + 7(- 2) = 0<br>&rArr; 2 + 2x - 14 = 0<br>&rArr; 2x = 12<br>&rArr; x = 6.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Which of the following is NOT an amendment made to the Airport Economic Regulatory Authority (AERA) Amendment Act, 2021 ?</p>",
                    question_hi: "<p>25. विमानपत्तन आर्थिक नियामक प्राधिकरण (AERA) संशोधन अधिनियम, 2021 में निम्नलिखित में से कौन-सा संशोधन नहीं किया गया है?</p>",
                    options_en: ["<p>The government can call any airport a major airport just by notification.</p>", "<p>The government will club together profit-making and loss-making airports.</p>", 
                                "<p>The government can offer airports as a package in PPP mode to the prospective bidders.</p>", "<p>A major airport is one which has an annual traffic of minimum 35 lakh passengers.</p>"],
                    options_hi: ["<p>सरकार किसी भी हवाई अड्डे को सिर्फ अधिसूचना के जरिए प्रमुख हवाई अड्डा मान सकती है।</p>", "<p>सरकार लाभ कमाने वाले और घाटे में चल रहे हवाई अड्डों को एक साथ मिला देगी।</p>",
                                "<p>सरकार संभावित बोलीकर्ताओं को PPP मोड में पैकेज के रूप में हवाई अड्डों की पेशकश कर सकती है।</p>", "<p>एक प्रमुख हवाई अड्डा वह है जिसमें न्यूनतम 35 लाख यात्रियों का वार्षिक आवागमन होता है।</p>"],
                    solution_en: "<p>25.(d) Airport Economic Regulatory Authority (AERA) Amendment Act, 2021 amended the Airports Economic Regulatory Authority of India Act, 2008. The Act allows tariff determination of a \'group of airports\' by way of amending the definition of \'major airport.\' The act amends the provisions of the law in relation to tariffs for single airports.</p>",
                    solution_hi: "<p>25.(d) विमानपत्तन आर्थिक विनियामक प्राधिकरण (AERA) संशोधन अधिनियम, 2021 ने भारतीय हवाई अड्डा आर्थिक विनियामक प्राधिकरण अधिनियम, 2008 में संशोधन किया। अधिनियम \'प्रमुख हवाई अड्डे\' की परिभाषा में संशोधन करके \'हवाई अड्डों के समूह\' के टैरिफ निर्धारण की अनुमति देता है। अधिनियम एकल हवाई अड्डों के लिए टैरिफ के संबंध में कानून के प्रावधानों में संशोधन करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>