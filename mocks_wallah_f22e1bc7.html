<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. TMBF is related to ATIM in a certain way based on the English alphabetical order. In the same way, CLGP is related to JSNW. To which of the following is QEAD related, following the same logic?</p>",
                    question_hi: "<p>1. अंग्रेजी वर्णमाला क्रम के आधार पर TMBF, ATIM से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, CLGP, JSNW से संबंधित है। समान तर्क का अनुसरण करते हुए QEAD निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>XKHL</p>", "<p>XLKH</p>", 
                                "<p>XHKL</p>", "<p>XLHK</p>"],
                    options_hi: ["<p>XKHL</p>", "<p>XLKH</p>",
                                "<p>XHKL</p>", "<p>XLHK</p>"],
                    solution_en: "<p>1.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc8Am5eC3ilOyUP0jrTt2JhPdwGC_8a9OpIVqIstR71N_WpIKCu0iX_6VBjM5-_eK79oF7Gkr_gC17P-3GqFV7rI-uV44kvv9e9tOhtfdw-pWwAQenZO-g0jbsU1KcxMUvKTy_n?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"95\" height=\"71\">&nbsp; &nbsp; &nbsp; <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdRi7cxhBswi5QeT0SwxLbrwmyHI4iNYsgaEbNcxn8UArjO06998aww65JTtBNs-LsfUIBhqe1PKkRNSk6nTwrLAogjw22JgrKRVW_1C6enOzLosnaAK03HgiJhtVu-xE3PQI2f?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"100\" height=\"73\">&nbsp;<br><br>&nbsp;Similarly, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdkjOpQ542-wN4M2EbvtqANF4fYJgjxfGI6neN_y64gWcqkSZvVlVOrjQlsd9fYhGOh9uRbyhMkoVNAoysEla5mgzYReZht31fdC0IIEI8uMOX-LIAT9ARO01cOheKEphBSAJixmg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"93\" height=\"70\"></p>\n<p><strong id=\"docs-internal-guid-ba475994-7fff-4f43-5d0b-7c9e2b9fcc9d\"></strong><br><br></p>",
                    solution_hi: "<p>1.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc8Am5eC3ilOyUP0jrTt2JhPdwGC_8a9OpIVqIstR71N_WpIKCu0iX_6VBjM5-_eK79oF7Gkr_gC17P-3GqFV7rI-uV44kvv9e9tOhtfdw-pWwAQenZO-g0jbsU1KcxMUvKTy_n?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"95\" height=\"71\">&nbsp; &nbsp; &nbsp; <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdRi7cxhBswi5QeT0SwxLbrwmyHI4iNYsgaEbNcxn8UArjO06998aww65JTtBNs-LsfUIBhqe1PKkRNSk6nTwrLAogjw22JgrKRVW_1C6enOzLosnaAK03HgiJhtVu-xE3PQI2f?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"100\" height=\"73\">&nbsp;<br><br>&nbsp;इसी प्रकार, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdkjOpQ542-wN4M2EbvtqANF4fYJgjxfGI6neN_y64gWcqkSZvVlVOrjQlsd9fYhGOh9uRbyhMkoVNAoysEla5mgzYReZht31fdC0IIEI8uMOX-LIAT9ARO01cOheKEphBSAJixmg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"93\" height=\"70\"><br><br><br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements</strong> : <br>All jams are butter. <br>Some nibs are ink. <br>Some nibs are butter. <br><strong>Conclusion (I) :</strong> Some ink is butter. <br><strong>Conclusion (II) :</strong> All jams are nibs.</p>",
                    question_hi: "<p>2. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों सेअलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं। <br><strong>कथन :</strong> <br>सभी जैम, मक्खन हैं। <br>कुछ निब, स्याही हैं। <br>कुछ निब, मक्खन हैं। <br><strong>निष्कर्ष (I) : </strong>कुछ स्याही, मक्खन है। <br><strong>निष्कर्ष (II) : </strong>सभी जैम, निब हैं</p>",
                    options_en: ["<p>Only conclusion (II) follows.</p>", "<p>Only conclusion (I) follows.</p>", 
                                "<p>Neither conclusion (I) nor (II) follows.</p>", "<p>Both conclusions (I) and (II) follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>", "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                                "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है</p>", "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>"],
                    solution_en: "<p>2.(c)<br><strong id=\"docs-internal-guid-8ab1c859-7fff-84d0-43c8-0f4d5cdcca59\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeTKkME6xonMFSGc4a1G-FcLeif9n3jqoPjEYpdvMsL_mea9V6hSgqon875ZNFjlH9bVuGI-WmQO1tV5z6ouMpafEtIhcaPDhJ1NUdqANZB5voSV4vA3QecMdzbT_sPJu_ddCNgKA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"211\" height=\"65\"></strong><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>2.(c)<br><strong id=\"docs-internal-guid-f0ce66d8-7fff-d2be-0fa9-09fa8040a731\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcSoQJzWN4Vw4QasJlB2FjFUIDtaeBejBo9PXuUbNKfaObmVG57eejuM2n1AHJr-t59ovjqbSkzHX9dzXBhZ5Ne_qhntjgNzjXRz83okwIOVvwttP7gmQE2tu_E01l-Prvpbjqj?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"207\" height=\"64\"></strong><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the option in which the given figure is embedded. (Rotation is NOT allowed.)<br><strong id=\"docs-internal-guid-9108489c-7fff-8ba0-c883-8b40fca42a4e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeFoae2Of-pqTLF1s3FxxPHaiFSTH4z00yyWXt2BjPHyNFjsS3K46GAlbvyqw8r1UZVUNWDhUEOlFRSrZEYSIR_StRroNbpPjB3sDEeZ2DBoq-ImWmvohRspq_dZJBn5lVdlfJA8A?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"104\" height=\"57\"></strong></p>",
                    question_hi: "<p>3. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है। (घूर्णन की अनुमति नहीं है)।<br><strong id=\"docs-internal-guid-e738775a-7fff-59bb-d35c-b2eb1f4849ae\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeFoae2Of-pqTLF1s3FxxPHaiFSTH4z00yyWXt2BjPHyNFjsS3K46GAlbvyqw8r1UZVUNWDhUEOlFRSrZEYSIR_StRroNbpPjB3sDEeZ2DBoq-ImWmvohRspq_dZJBn5lVdlfJA8A?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"108\" height=\"59\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-b55c80ed-7fff-5a23-ebd3-bec84ac4347d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd5yC7eQsDfoRKEPxU1aHR2hGC8yioW1wWByWQFrKVs__fJ19Ahstkb8LfWkD5M78riu4qRA-fU_wHzHt-jhpFnV9WG-UO0vDptrUbqmGY04s9SzcfiuljAzEWiX8GNauL6fFLiug?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"93\" height=\"80\"></strong></p>", "<p><strong id=\"docs-internal-guid-304f144e-7fff-c533-435e-0d34f552c428\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeq_TL-AXPrjt3TUdIqcKvaG-LKCwjfFxdXYphgOxjaw-EzOfYgjn_f5T5QmlLeCOjEmvrMQY34vSvMURxrX1jrZ5k8Ndgq-rIhEDJU7eP4ggvN9VHcL2NwE4WRVI88sBBQ6PZ1Yg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"95\" height=\"56\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-3ccc1b7f-7fff-10eb-891e-51a43eb2bf37\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeUmJiIscz6ez6ZApJRquxTcVDg4pQazqg4JMord192NpGKjKsRCBod0zxotmNckZVU__b9cX_5GJj0HtGCJ8Kd647Zwm7rLwQiw_BfTJSpC88Ql54S8hErEvRyKpmPKPX-uBh_sg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"88\" height=\"70\"></strong></p>", "<p><strong id=\"docs-internal-guid-b58fdf1b-7fff-26a8-5436-1cac1169642e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4RRZEqm_JvPNFCLWu6lKKsyAczh9wYdDJEpeUBeC02cEoEnftiTDWGd9TLzSqpREqFQIzAdKLAMv0LNXQV8EW3Y1bVFUD912SIwL2kqIb_FHSVpoDI_iJ69jEYf4UebCMCBPlvg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"81\" height=\"98\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-e74ee9a9-7fff-8a33-a32c-6bc850849b49\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd5yC7eQsDfoRKEPxU1aHR2hGC8yioW1wWByWQFrKVs__fJ19Ahstkb8LfWkD5M78riu4qRA-fU_wHzHt-jhpFnV9WG-UO0vDptrUbqmGY04s9SzcfiuljAzEWiX8GNauL6fFLiug?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"93\" height=\"80\"></strong></p>", "<p><strong id=\"docs-internal-guid-bff45251-7fff-a034-297b-0bcd4bca22b8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeq_TL-AXPrjt3TUdIqcKvaG-LKCwjfFxdXYphgOxjaw-EzOfYgjn_f5T5QmlLeCOjEmvrMQY34vSvMURxrX1jrZ5k8Ndgq-rIhEDJU7eP4ggvN9VHcL2NwE4WRVI88sBBQ6PZ1Yg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"95\" height=\"56\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-869d44ae-7fff-bf1e-61d5-2933c2d2aa95\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeUmJiIscz6ez6ZApJRquxTcVDg4pQazqg4JMord192NpGKjKsRCBod0zxotmNckZVU__b9cX_5GJj0HtGCJ8Kd647Zwm7rLwQiw_BfTJSpC88Ql54S8hErEvRyKpmPKPX-uBh_sg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"88\" height=\"70\"></strong></p>", "<p><strong id=\"docs-internal-guid-3307dd7d-7fff-df09-30ca-334dc0071db4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4RRZEqm_JvPNFCLWu6lKKsyAczh9wYdDJEpeUBeC02cEoEnftiTDWGd9TLzSqpREqFQIzAdKLAMv0LNXQV8EW3Y1bVFUD912SIwL2kqIb_FHSVpoDI_iJ69jEYf4UebCMCBPlvg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"81\" height=\"98\"></strong></p>"],
                    solution_en: "<p>3.(b)<br><strong id=\"docs-internal-guid-664421ef-7fff-0cef-adfa-730e43c3a2b2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdqkzESLnqC0MT3HuYAYaa4bHpD2qtnXPBtNt3IZmcHnz2Yw8rQYMNxGRynWnftTfJoDlH7qfsRj7IybLWR_bEEV7FTVawo7nSuRPu4Ga4CAWl-DcDy7EeWXBEICCP3AEWCyCPlDw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"89\" height=\"68\"></strong></p>",
                    solution_hi: "<p>3.(b)<br><strong id=\"docs-internal-guid-664421ef-7fff-0cef-adfa-730e43c3a2b2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdqkzESLnqC0MT3HuYAYaa4bHpD2qtnXPBtNt3IZmcHnz2Yw8rQYMNxGRynWnftTfJoDlH7qfsRj7IybLWR_bEEV7FTVawo7nSuRPu4Ga4CAWl-DcDy7EeWXBEICCP3AEWCyCPlDw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"89\" height=\"68\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. If 19 July 2012 is Thursday, then what will be the day of the week on 11 January 2018?</p>",
                    question_hi: "<p>4. यदि 19 जुलाई 2012 को गुरुवार है, तो 11 जनवरी 2018 को सप्ताह का कौन-सा दिन होगा?</p>",
                    options_en: ["<p>Wednesday</p>", "<p>Saturday</p>", 
                                "<p>Monday</p>", "<p>Thursday</p>"],
                    options_hi: ["<p>बुधवार</p>", "<p>शनिवार</p>",
                                "<p>सोमवार</p>", "<p>गुरुवार</p>"],
                    solution_en: "<p>4.(d) 19 July 2012 is thursday.<br>On moving to 2018 the number of odd days = +1 + 1 + 1 + 2 + 1 + 1 = 7. <br>On dividing 7 by 7 the remainder = 0. <br>Thursday + 0 = Thursday . <br>We have reached till 19 July, but we have to go back to 11 January,<br>the number of days between = 19 + 30 + 31 + 30 + 31 + 28 + 20 = 189. <br>On dividing 189 by 7 the remainder is 0. <br>Thursday - 0 = Thursday.</p>",
                    solution_hi: "<p>4.(d) 19 जुलाई 2012 को गुरुवार है। <br>2018 में जाने पर विषम दिनों की संख्या = +1 + 1 + 1 + 2 + 1 + 1 = 7. <br>7 को 7 से विभाजित करने पर शेषफल = 0. <br>गुरुवार + 0 = गुरुवार। <br>हम 19 जुलाई तक पहुंच गए हैं, लेकिन हमें 11 जनवरी तक वापस जाना है, <br>बीच में दिनों की संख्या = 19 + 30 + 31 + 30 + 31 + 28 + 20 = 189. <br>189 को 7 से विभाजित करने पर शेषफल 0 आता है. <br>गुरुवार - 0 = गुरूवार.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. If \'A\' stands for &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; \'B\' stands for, &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and \'D\' stands for &lsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation?<br>17 C 69 A 3 D 22 B 1= ?</p>",
                    question_hi: "<p>5. यदि \'A\' का अर्थ &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo;, \'B\' का अर्थ\' &lsquo;&times;&rsquo;, \'C\' का अर्थ &lsquo;+&rsquo;, और D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>17 C 69 A 3 D 22 B 1 = ?</p>",
                    options_en: ["<p>14</p>", "<p>16</p>", 
                                "<p>12</p>", "<p>18</p>"],
                    options_hi: ["<p>14</p>", "<p>16</p>",
                                "<p>12</p>", "<p>18</p>"],
                    solution_en: "<p>5.(d) <strong>Given</strong> :- 17 C 69 A 3 D 22 B 1 <br>As per given instruction after interchanging letter with sign we get<br>17 + 69 <math display=\"inline\"><mo>&#247;</mo></math> 3 - 22 &times; 1<br>17 + 23 - 22 = 18</p>",
                    solution_hi: "<p>5.(d) <strong>दिया गया :- </strong>17 C 69 A 3 D 22 B 1 <br>दिए गए निर्देश के अनुसार अक्षर को चिह्न से बदलने पर हमें प्राप्त होता है<br>17 + 69 <math display=\"inline\"><mo>&#247;</mo></math> 3 - 22 &times; 1<br>17 + 23 - 22 = 18</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What would be the number on the opposite side of \'8\' if the given sheet is folded to form a cube?<br><strong id=\"docs-internal-guid-1cf89aae-7fff-9398-5a31-3380ff009e75\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdiKqoSzURWc4tGPkNcCraB89OT91uc7ByyuPtLwGRmSe1rvhOopFClQUFStHvVFDdHrKttr8Jx98s4Pap2ucxWalwiHX2kdrSH7OSLiakkQfCKb3AXVYPprA3pOuTasee7rTANjw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"114\" height=\"134\"></strong></p>",
                    question_hi: "<p>6. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो 8\' के विपरीत फ़्लक पर कौन-सी संख्या होगी?<br><strong id=\"docs-internal-guid-1cf89aae-7fff-9398-5a31-3380ff009e75\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdiKqoSzURWc4tGPkNcCraB89OT91uc7ByyuPtLwGRmSe1rvhOopFClQUFStHvVFDdHrKttr8Jx98s4Pap2ucxWalwiHX2kdrSH7OSLiakkQfCKb3AXVYPprA3pOuTasee7rTANjw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"114\" height=\"134\"></strong></p>",
                    options_en: ["<p>10</p>", "<p>6</p>", 
                                "<p>7</p>", "<p>5</p>"],
                    options_hi: ["<p>10</p>", "<p>6</p>",
                                "<p>7</p>", "<p>5</p>"],
                    solution_en: "<p>6.(c)<br><strong id=\"docs-internal-guid-8cdd4c88-7fff-e789-e439-768d40650994\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdVzH6ddisX1eSXoI2KxjPrY8PpuPR6I79TDBg24H_VjbMq2t2k_Hvn1LTfDvu56VnZkAI52YlQHlYS3GM2Bu6biUz9l1coktmEvVZeFF9guCrPRjQLdyniALpOOtDwl217T5QuFg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"123\" height=\"128\"></strong><br>The opposite face are :- 6 &harr; 5 , 8 &harr; 7 , 9 &harr; 10</p>",
                    solution_hi: "<p>6.(c)<br><strong id=\"docs-internal-guid-8cdd4c88-7fff-e789-e439-768d40650994\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdVzH6ddisX1eSXoI2KxjPrY8PpuPR6I79TDBg24H_VjbMq2t2k_Hvn1LTfDvu56VnZkAI52YlQHlYS3GM2Bu6biUz9l1coktmEvVZeFF9guCrPRjQLdyniALpOOtDwl217T5QuFg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"123\" height=\"128\"></strong><br>विपरीत फलक हैं:- 6 &harr; 5 , 8 &harr; 7 , 9 &harr; 10</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(13, 234, 9) <br>(15, 180, 6)<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>7. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं।<br>(13, 234, 9) <br>(15, 180, 6)<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(12, 108, 9)</p>", "<p>(14, 196, 7)</p>", 
                                "<p>(16, 240, 5)</p>", "<p>(17, 340, 5)</p>"],
                    options_hi: ["<p>(12, 108, 9)</p>", "<p>(14, 196, 7)</p>",
                                "<p>(16, 240, 5)</p>", "<p>(17, 340, 5)</p>"],
                    solution_en: "<p>7.(b) <strong>Logic</strong> :- (1st number &times; 3rd number) &times; 2 = 2nd number<br>(13, 234, 9) :- (13 &times; 9) &times; 2 &rArr; (117) &times; 2 = 234<br>(15, 180, 6) :- (15 &times; 6) &times; 2 &rArr; (90) &times; 2 = 180<br>Similarly,<br>(14, 196, 7):- (14 &times; 7) &times; 2 &rArr; (98) &times; 2 = 196</p>",
                    solution_hi: "<p>7.(b) <strong>तर्क:-</strong> (पहली संख्या &times; तीसरी संख्या) &times; 2 = दूसरी संख्या<br>(13, 234, 9) :- (13 &times; 9) &times; 2 &rArr; (117) &times; 2 = 234<br>(15, 180, 6) :- (15 &times; 6) &times; 2 &rArr; (90) &times; 2 = 180<br>इसी प्रकार,<br>(14, 196, 7):- (14 &times; 7) &times; 2 &rArr; (98) &times; 2 = 196</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What should come in place of the question mark (?) in the given series?<br>63, 64, 66, 69, 73, ?</p>",
                    question_hi: "<p>8. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>63, 64, 66, 69, 73, ?</p>",
                    options_en: ["<p>80</p>", "<p>78</p>", 
                                "<p>77</p>", "<p>79</p>"],
                    options_hi: ["<p>80</p>", "<p>78</p>",
                                "<p>77</p>", "<p>79</p>"],
                    solution_en: "<p>8.(b)<br><strong id=\"docs-internal-guid-37dec137-7fff-6ca2-c49a-8c269f8feb5a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfhG28XcS0xgEvkmhlxjB2YZqSisBAuQ0eBE7k_Zhjb57aC2x11MZJe8Oq9TlwaQWpnmT3m3kNOzF70jHsH5aESh6U0tKPsR_xQAzhbxc9ZcP_Dyw8Ad1At-M4RdJUFzTUKEkxT-w?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"251\" height=\"49\"></strong></p>",
                    solution_hi: "<p>8.(b)<br><strong id=\"docs-internal-guid-37dec137-7fff-6ca2-c49a-8c269f8feb5a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfhG28XcS0xgEvkmhlxjB2YZqSisBAuQ0eBE7k_Zhjb57aC2x11MZJe8Oq9TlwaQWpnmT3m3kNOzF70jHsH5aESh6U0tKPsR_xQAzhbxc9ZcP_Dyw8Ad1At-M4RdJUFzTUKEkxT-w?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"251\" height=\"49\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. The position of how many letters will remain unchanged if all the letters in the word DIALECT are arranged in alphabetical order ?</p>",
                    question_hi: "<p>9. यदि शब्द DIALECT के सभी अक्षरों को वर्णानुक्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा ?</p>",
                    options_en: ["<p>Three</p>", "<p>One</p>", 
                                "<p>Four</p>", "<p>Two</p>"],
                    options_hi: ["<p>तीन</p>", "<p>एक</p>",
                                "<p>चार</p>", "<p>दो</p>"],
                    solution_en: "<p>9.(b)<br><strong id=\"docs-internal-guid-f7b470f2-7fff-2dd7-75c5-0917c756e72e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXflE2shU9do37wcNLu8NqxmhC8dZh9lLW12rKdMdO2A0t8dsp-5ncDzCU_coXGRp_w2taNTDVHvaKea2nTni8Du2bXC-zCbIOfDeCQKPj-5Q6FSJCYIWgCEeXIRYZEqvch1vRaWtA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"137\" height=\"70\"></strong><br>The position of only one letter will remain unchanged.</p>",
                    solution_hi: "<p>9.(b)<br><strong id=\"docs-internal-guid-f7b470f2-7fff-2dd7-75c5-0917c756e72e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXflE2shU9do37wcNLu8NqxmhC8dZh9lLW12rKdMdO2A0t8dsp-5ncDzCU_coXGRp_w2taNTDVHvaKea2nTni8Du2bXC-zCbIOfDeCQKPj-5Q6FSJCYIWgCEeXIRYZEqvch1vRaWtA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"137\" height=\"70\"></strong><br>केवल एक अक्षर की स्थिति अपरिवर्तित रहेगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What will come in the place of &lsquo;?&rsquo; in the following equation if &lsquo;+&rsquo; and &lsquo;&times;&rsquo; are interchanged and &lsquo;&ndash;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ? <br>21 &ndash; 7 &times; 26 + 46 &divide; 36 = ?</p>",
                    question_hi: "<p>10. यदि \'+\' और \'&times;\' को आपस में बदल दिया जाए तथा \'&ndash;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ? <br>21 &ndash; 7 &times; 26 + 46 &divide; 36 = ?</p>",
                    options_en: ["<p>908</p>", "<p>1163</p>", 
                                "<p>760</p>", "<p>8667</p>"],
                    options_hi: ["<p>908</p>", "<p>1163</p>",
                                "<p>760</p>", "<p>8667</p>"],
                    solution_en: "<p>10.(b) <strong>Given</strong> :- 21 - 7 &times; 26 + 46 <math display=\"inline\"><mo>&#247;</mo></math> 36<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>21 <math display=\"inline\"><mo>&#247;</mo></math> 7 + 26 &times; 46 - 36<br>3 + 1196 - 36 = 1163</p>",
                    solution_hi: "<p>10.(b) <strong>दिया गया :- 2</strong>1 - 7 &times; 26 + 46 <math display=\"inline\"><mo>&#247;</mo></math> 36<br>दिए गए निर्देश के अनुसार \'+\' और \'&times;\' तथा \'-\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>21 <math display=\"inline\"><mo>&#247;</mo></math> 7 + 26 &times; 46 - 36<br>3 + 1196 - 36 = 1163</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Which of the following numbers will replace the question mark (?) in the given series?<br>11, 4, 22, 20, 44, 100, 88, 500, ?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>11, 4, 22, 20, 44, 100, 88, 500, ?</p>",
                    options_en: ["<p>154</p>", "<p>182</p>", 
                                "<p>176</p>", "<p>160</p>"],
                    options_hi: ["<p>154</p>", "<p>182</p>",
                                "<p>176</p>", "<p>160</p>"],
                    solution_en: "<p>11.(c)<br><strong id=\"docs-internal-guid-d8640eed-7fff-f19e-1c68-514f61b3a552\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0ie1TUxoBehT9o_wr1BRX2CowPrjKKdNwBHGn8gqLwna8_hwe0Uk9sebpvLMoKMkh3qf8QtblObOCSwG0n7fWixBdmnrCRixucrmRjDNlcqfIAY09wRpWIdonDZ-vCmIvoj8qJQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"309\" height=\"59\"></strong></p>",
                    solution_hi: "<p>11.(c)<br><strong id=\"docs-internal-guid-d8640eed-7fff-f19e-1c68-514f61b3a552\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0ie1TUxoBehT9o_wr1BRX2CowPrjKKdNwBHGn8gqLwna8_hwe0Uk9sebpvLMoKMkh3qf8QtblObOCSwG0n7fWixBdmnrCRixucrmRjDNlcqfIAY09wRpWIdonDZ-vCmIvoj8qJQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"309\" height=\"59\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. What should come in place of? in the given series based on the English alphabetical order?<br>TUV&nbsp; &nbsp; RXR&nbsp; &nbsp; ?&nbsp; &nbsp; NDJ&nbsp; &nbsp; LGF</p>",
                    question_hi: "<p>12. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?\' के स्थान पर क्या आना चाहिए?<br>TUV&nbsp; &nbsp; RXR&nbsp; &nbsp; ?&nbsp; &nbsp; NDJ&nbsp; &nbsp; LGF</p>",
                    options_en: ["<p>NAP</p>", "<p>VOI</p>", 
                                "<p>PAN</p>", "<p>CAL</p>"],
                    options_hi: ["<p>NAP</p>", "<p>VOI</p>",
                                "<p>PAN</p>", "<p>CAL</p>"],
                    solution_en: "<p>12.(c)<br><strong id=\"docs-internal-guid-cca85abe-7fff-6d71-93ed-a85dcc10edd0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdhCxyPVAo5Kv4VxiFYLdy_C5fa8OX-StTxpRMKqCji9ZqhoUs4Dgst4zV3WSjTz91U4-DO49LE1L_UQNoB5sH5CH1e7qKOfrKiRc4u90u5hUAztgjHjeqTtMejHtQHsfvTB6USJw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"266\" height=\"88\"></strong></p>",
                    solution_hi: "<p>12.(c)<br><strong id=\"docs-internal-guid-cca85abe-7fff-6d71-93ed-a85dcc10edd0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdhCxyPVAo5Kv4VxiFYLdy_C5fa8OX-StTxpRMKqCji9ZqhoUs4Dgst4zV3WSjTz91U4-DO49LE1L_UQNoB5sH5CH1e7qKOfrKiRc4u90u5hUAztgjHjeqTtMejHtQHsfvTB6USJw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"266\" height=\"88\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language. \"SOAP\' is coded as \'4629\' and \'SPOT\" is coded as \'9824\'. How is \'A\' coded in the given language?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में, \'SOAP\' को 4629\' लिखा जाता है और \'SPOT\' को 9824\' लिखा जाता है। उसी कूट भाषा में \'A&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>6</p>", "<p>4</p>", 
                                "<p>2</p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p>4</p>",
                                "<p>2</p>", "<p>8</p>"],
                    solution_en: "<p>13.(a) SOAP &rarr; 4629&hellip;&hellip;(i)<br>SPOT &rarr; 9824&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;S&rsquo; , &lsquo;P&rsquo; , &lsquo;O&rsquo; and &lsquo;4&rsquo; , &lsquo;2&rsquo; , &lsquo;9&rsquo; are common. The code of &lsquo;A&rsquo; = &lsquo;6&rsquo;.</p>",
                    solution_hi: "<p>13.(a) SOAP &rarr; 4629&hellip;&hellip;(i)<br>SPOT &rarr; 9824&hellip;&hellip;(ii)<br>(i) और (ii) से \'S\', \'P\', \'O\' और \'4\', \'2\', \'9\' उभयनिष्ठ हैं। \'A\' का कोड = \'6\'.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><strong id=\"docs-internal-guid-c0861e47-7fff-2cff-02eb-b0cff15915da\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfD0Fpy_4VRE6IM69UBbK4F3CiNHALm6yO2b4ohPqqMe1gi4Ep29TQcEjD2USGJcjVL3yoGsf-NkBZkeXeT8IjK3gCE-tVJr3RoJQ6pc2WQI24A72RvgEWtErcBbvkobXKTtdDmNA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"102\" height=\"105\"></strong></p>",
                    question_hi: "<p>14. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><strong id=\"docs-internal-guid-c0861e47-7fff-2cff-02eb-b0cff15915da\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfD0Fpy_4VRE6IM69UBbK4F3CiNHALm6yO2b4ohPqqMe1gi4Ep29TQcEjD2USGJcjVL3yoGsf-NkBZkeXeT8IjK3gCE-tVJr3RoJQ6pc2WQI24A72RvgEWtErcBbvkobXKTtdDmNA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"102\" height=\"105\"></strong></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081876801.png\" alt=\"rId24\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081876919.png\" alt=\"rId25\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877046.png\" alt=\"rId26\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877172.png\" alt=\"rId27\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081876801.png\" alt=\"rId24\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081876919.png\" alt=\"rId25\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877046.png\" alt=\"rId26\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877172.png\" alt=\"rId27\"></p>"],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081876801.png\" alt=\"rId24\"></p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081876801.png\" alt=\"rId24\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. In a certain code language,<br>A + B means \'A is the daughter of B\'<br>A - B means \'A is the wife of B\'<br>A <math display=\"inline\"><mo>&#215;</mo></math> B means \'A is the brother of B\'<br>A <math display=\"inline\"><mo>&#247;</mo></math> B means \'A is the father of B\'.<br>Based on the above, if P + Q <math display=\"inline\"><mo>&#215;</mo></math> R - S &divide; T\' which of the following is true?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में,<br>A + B का अर्थ \'A, B की बेटी है\' <br>A - B का अर्थ \'A, B की पत्नी है\' <br>A &times; B का अर्थ \'A, B का भाई है\' <br>A &divide; B का अर्थ \'A, B का पिता है\'।<br>उपरोक्त के आधार पर, यदि \'P + Q <math display=\"inline\"><mo>&#215;</mo></math> R - S &divide; T\' तो निम्न में से कौन-सा सत्य है?</p>",
                    options_en: ["<p>T is S\'s Son,</p>", "<p>P is the daughter of T\'s Mother\'s Brother.</p>", 
                                "<p>P is the sister of T.</p>", "<p>Q is the brother of S.</p>"],
                    options_hi: ["<p>T, S का बेटा है।</p>", "<p>P, T की मां के भाई की बेटी है।</p>",
                                "<p>P, T की बहन है।</p>", "<p>Q, S का भाई है।</p>"],
                    solution_en: "<p>15.(b)<br><strong id=\"docs-internal-guid-18e3feff-7fff-643c-bd38-75588e89173b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfL744i85I3DfDBZRSNt6-9JYka7Z4fDfikkXGgQz1JBelFkkvdxkcwCUbCFrhEe_rcjRNybYVbVu611aZVE9UzG01fidt1owHLFYg6Wkix1FED061wGba9vwMnstwClsLdSsSS?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"159\" height=\"90\"></strong><br>P is the daughter of T&rsquo;s Mother&rsquo;s Brother</p>",
                    solution_hi: "<p>15.(b) <br><strong id=\"docs-internal-guid-18e3feff-7fff-643c-bd38-75588e89173b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfL744i85I3DfDBZRSNt6-9JYka7Z4fDfikkXGgQz1JBelFkkvdxkcwCUbCFrhEe_rcjRNybYVbVu611aZVE9UzG01fidt1owHLFYg6Wkix1FED061wGba9vwMnstwClsLdSsSS?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"159\" height=\"90\"></strong><br>P, T की माँ के भाई की बेटी है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Which figure should replace the question mark (?) if the following figure series were to be continued ?<br><strong id=\"docs-internal-guid-7e15d11d-7fff-c880-d14a-2e8e158b227b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXecZPue9CECc9-3IboOigldFCOaV__nSxl5PWuntiLDmyeiFVxXVWdCLkA2wA-LTeDHJVQQkEwroIMYnCuAIEsNfRjw6jtXDoL9USLstcKHE7nrbjtf68BssbqJ8Dtps_9b4NRsNA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"323\" height=\"77\"></strong></p>",
                    question_hi: "<p>16. यदि निम्नलिखित आकृति श्रृंखला को जारी रखना हो तो कौन-सी आकृति प्रश्न चिन्ह (?) के स्थान पर आनी चाहिए?<br><strong id=\"docs-internal-guid-7e15d11d-7fff-c880-d14a-2e8e158b227b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXecZPue9CECc9-3IboOigldFCOaV__nSxl5PWuntiLDmyeiFVxXVWdCLkA2wA-LTeDHJVQQkEwroIMYnCuAIEsNfRjw6jtXDoL9USLstcKHE7nrbjtf68BssbqJ8Dtps_9b4NRsNA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"323\" height=\"77\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-ab29faf4-7fff-72b0-3e24-683f7a1c29e7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc8HhqM7s_iY19Wo8fxTaPToStGokIuS_KGsx5IJy_SUccbexNHbJ_6tI0nOZgSa4m-HXpEOI2yX75RV_GUP7pZ26--c7lPm9v9UyHeOAHTdbgFC8VQu-Hhd9wEaTAz8E9ex1sysQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"93\" height=\"95\"></strong></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877677.png\" alt=\"rId31\" width=\"92\" height=\"89\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877808.png\" alt=\"rId32\" width=\"92\" height=\"88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877939.png\" alt=\"rId33\" width=\"93\" height=\"94\"></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-ab29faf4-7fff-72b0-3e24-683f7a1c29e7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc8HhqM7s_iY19Wo8fxTaPToStGokIuS_KGsx5IJy_SUccbexNHbJ_6tI0nOZgSa4m-HXpEOI2yX75RV_GUP7pZ26--c7lPm9v9UyHeOAHTdbgFC8VQu-Hhd9wEaTAz8E9ex1sysQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"93\" height=\"95\"></strong></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877677.png\" alt=\"rId31\" width=\"92\" height=\"89\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877808.png\" alt=\"rId32\" width=\"92\" height=\"88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081877939.png\" alt=\"rId33\" width=\"93\" height=\"94\"></p>"],
                    solution_en: "<p>16.(a)<br><strong id=\"docs-internal-guid-58ba98fc-7fff-9bc5-1d95-7ebfe244ab54\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdgaXrrm7Y3zaHzC855iN61Xt0pFGvOONOg52zIubyW-ERpWBbC4hI5jB8TAY19VAX66aylpt8oWx_CpkQyf5IMfp-ek4UoLPj5EeWS_HrMh6cOY_7jiybOxqxkie_rw2lDOXp-PQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"92\" height=\"94\"></strong></p>",
                    solution_hi: "<p>16.(a)<br><strong id=\"docs-internal-guid-58ba98fc-7fff-9bc5-1d95-7ebfe244ab54\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdgaXrrm7Y3zaHzC855iN61Xt0pFGvOONOg52zIubyW-ERpWBbC4hI5jB8TAY19VAX66aylpt8oWx_CpkQyf5IMfp-ek4UoLPj5EeWS_HrMh6cOY_7jiybOxqxkie_rw2lDOXp-PQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"92\" height=\"94\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. RTVX is related to SUWY in a certain way based on the English alphabetical order. In the same way, KMOQ is related to LNPR. To which of the following is FHJL related, following the same logic?</p>",
                    question_hi: "<p>17. अंग्रेजी वर्णमाला क्रम के आधार पर RTVX, SUWY से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, KMOQ, LNPR से संबंधित है। समान तर्क का अनुसरण करते हुए, FHJL निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>GIJK</p>", "<p>GIKN</p>", 
                                "<p>GIKM</p>", "<p>GHJK</p>"],
                    options_hi: ["<p>GIJK</p>", "<p>GIKN</p>",
                                "<p>GIKM</p>", "<p>GHJK</p>"],
                    solution_en: "<p>17.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrvKG0hsh74ykiNEN7HbUrkzCbIz8TxTXjuFX4a1rZdFCjHqF-zK0e2brgUcpLJXI0w0rOklAlhAYWqnywksh8y2T35bcRGEjyax3ctmXxr_qcJylBwcXSWno0Tsf7AT8Oqykurw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"112\" height=\"84\">, &nbsp; <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPAtfrilqH-zEscH1xNWV-LpxwZMVJJyLc4M6tj0VfDKMj-zu7mQdjSsk8U2A2grWhpnjTBjso_qmNX29ESh1t9LF7J_zvIudLKsBB7j9W8kLnAe4G_qBOXO11ryReGgXiD-CuKQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"105\" height=\"82\"><br><br>Similarly<strong id=\"docs-internal-guid-eaf2057c-7fff-b943-5534-f675cf2ca1c3\">, </strong><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdoXReRCN6pbFYcx7xee8DRg8YVEdWOPXVBIsONZMinmTziCw_i7R-6oZ76ed6PK61XIw4SAKOH7UD2HkEsqqkkkWheTV8tgXbSX9Sisluc8tNJDxzOwgIfkOdYS348rK4iNkIVgQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"112\" height=\"86\"></p>\n<p><strong id=\"docs-internal-guid-8adddb8f-7fff-4b63-9ce7-ba35db47b3b7\"></strong></p>",
                    solution_hi: "<p>17.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrvKG0hsh74ykiNEN7HbUrkzCbIz8TxTXjuFX4a1rZdFCjHqF-zK0e2brgUcpLJXI0w0rOklAlhAYWqnywksh8y2T35bcRGEjyax3ctmXxr_qcJylBwcXSWno0Tsf7AT8Oqykurw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"112\" height=\"84\">, &nbsp; <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPAtfrilqH-zEscH1xNWV-LpxwZMVJJyLc4M6tj0VfDKMj-zu7mQdjSsk8U2A2grWhpnjTBjso_qmNX29ESh1t9LF7J_zvIudLKsBB7j9W8kLnAe4G_qBOXO11ryReGgXiD-CuKQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"105\" height=\"82\"><br><strong id=\"docs-internal-guid-eaf2057c-7fff-b943-5534-f675cf2ca1c3\"><br></strong>इसी प्रकार,<strong id=\"docs-internal-guid-eaf2057c-7fff-b943-5534-f675cf2ca1c3\">&nbsp;</strong><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdoXReRCN6pbFYcx7xee8DRg8YVEdWOPXVBIsONZMinmTziCw_i7R-6oZ76ed6PK61XIw4SAKOH7UD2HkEsqqkkkWheTV8tgXbSX9Sisluc8tNJDxzOwgIfkOdYS348rK4iNkIVgQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"112\" height=\"86\"><br><br><br></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><strong id=\"docs-internal-guid-8974b65a-7fff-36ad-76b7-b6a1fb201edc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdueIEBOBSkF0SEKfodHtKmCZFJkJaoMMGm80_hlmFuE8WYWccX1THnB3Tsh_94sHsxh3GF6VCuHZiMrZOy9MzsJTkDqcMZc2WGJnFrC1nbzccx0XXlyhhxz5JkkHIXqVVWOLv-qg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"115\" height=\"87\"></strong></p>",
                    question_hi: "<p>18. दर्पण को नीचे दर्शाए गए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए। <br><strong id=\"docs-internal-guid-8974b65a-7fff-36ad-76b7-b6a1fb201edc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdueIEBOBSkF0SEKfodHtKmCZFJkJaoMMGm80_hlmFuE8WYWccX1THnB3Tsh_94sHsxh3GF6VCuHZiMrZOy9MzsJTkDqcMZc2WGJnFrC1nbzccx0XXlyhhxz5JkkHIXqVVWOLv-qg?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"115\" height=\"87\"></strong></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081878895.png\" alt=\"rId38\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081879014.png\" alt=\"rId39\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081879172.png\" alt=\"rId40\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081879363.png\" alt=\"rId41\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081878895.png\" alt=\"rId38\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081879014.png\" alt=\"rId39\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081879172.png\" alt=\"rId40\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081879363.png\" alt=\"rId41\"></p>"],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081879363.png\" alt=\"rId41\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734081879363.png\" alt=\"rId41\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the correct option that when filled in the blanks in the same sequence will make the series logically complete. <br>GH_ST_HO_RGHOSPG_OS_</p>",
                    question_hi: "<p>19. उस सही विकल्प का चयन कीजिए जिसे दी गई श्रृंखला के रिक्त स्थानों में उसी क्रम में रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br>GH_ST_HO_RGHOSPG_OS_</p>",
                    options_en: ["<p>ORHSN</p>", "<p>GOHNP</p>", 
                                "<p>OGSHN</p>", "<p>GORNH</p>"],
                    options_hi: ["<p>ORHSN</p>", "<p>GOHNP</p>",
                                "<p>OGSHN</p>", "<p>GORNH</p>"],
                    solution_en: "<p>19.(c) GH<span style=\"text-decoration: underline;\"><strong>O</strong></span>ST / <span style=\"text-decoration: underline;\"><strong>G</strong></span>HO<span style=\"text-decoration: underline;\"><strong>S</strong></span>R / GHOSP / G<strong>H</strong>OS<strong>N</strong><br><strong>Logic</strong> :- Fourth letter decreasing by 2.</p>",
                    solution_hi: "<p>19.(c) GH<span style=\"text-decoration: underline;\"><strong>O</strong></span>ST / <span style=\"text-decoration: underline;\"><strong>G</strong></span>HO<span style=\"text-decoration: underline;\"><strong>S</strong></span>R / GHOSP / G<strong>H</strong>OS<strong>N</strong><br><strong>तर्क</strong> :- चौथा अक्षर 2 से घट रहा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. What will come in place of \'?\' in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>15 + 2 - 13 <math display=\"inline\"><mo>&#215;</mo></math> 13 &divide; 3 = ?</p>",
                    question_hi: "<p>20. यदि &lsquo;+&rsquo; और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में &lsquo;?&rsquo; के स्थान पर क्या आएगा?<br>15 + 2 - 13 <math display=\"inline\"><mo>&#215;</mo></math> 13 &divide; 3 = ?</p>",
                    options_en: ["<p>24</p>", "<p>23</p>", 
                                "<p>18</p>", "<p>16</p>"],
                    options_hi: ["<p>24</p>", "<p>23</p>",
                                "<p>18</p>", "<p>16</p>"],
                    solution_en: "<p>20.(d) <strong>Given</strong> :- 15 + 2 - 13 &times; 13 <math display=\"inline\"><mo>&#247;</mo></math> 3<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo;<br>15 - 2 + 13 <math display=\"inline\"><mo>&#247;</mo></math> 13 &times; 3<br>13 + 1 &times; 3 = 16</p>",
                    solution_hi: "<p>20.(d) <strong>दिया गया :- </strong>15 + 2 - 13 &times; 13 <math display=\"inline\"><mo>&#247;</mo></math> 3<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' और \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने के बाद<br>15 - 2 + 13 <math display=\"inline\"><mo>&#247;</mo></math> 13 &times; 3<br>13 + 1 &times; 3 = 16</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different. <br><strong>(Note</strong> : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>21. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है? <br>(<strong>ध्यान दें :</strong> असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>OMJ</p>", "<p>PNK</p>", 
                                "<p>BZW</p>", "<p>RTQ</p>"],
                    options_hi: ["<p>OMJ</p>", "<p>PNK</p>",
                                "<p>BZW</p>", "<p>RTQ</p>"],
                    solution_en: "<p>21.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcWeekOD5_rotlDhPSgo8MWBYSk1YApcYM6dWz-y2hLekofIZM9yZo_UGy8ICjCfbMWCPQRS3NHwlOuzc6QhLH8jxnGUhzQTxfzCG0mNB17Vl0xJq9Lo6_9ob3ra-EEEHI393HQIw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"99\" height=\"58\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4CZ-9i0KZtlqIef8zSqB70x4f5FWcvylFw4eXZToKMPK9vdijh-U4lr-o8qKepr35e_5D3ed_w56d2MqXKAQ5Ha99F0NRIeIrWDzDzOM_7cMFFqj7XriNRvj1oYQk0d9ORPUOig?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"93\" height=\"56\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf_AqONZnDaTQ5yXQ9e8y7VUyIUGF1tizzBNB7NmQcU6a2KEl9vMLvhQwLDZUNmsbmlkPLuhXFysanRCARsPZMkDe_5_72a1vEx2RixQERH2fghC9B2rYYqbZVFRJ_Tg2zn-cx_QQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"105\" height=\"60\"><br><br>But, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe4tpwwZfMJwT2MAcQ7x_e30R1jRBPksCucB0jkm1rOg39HFaLEIUAfNW22gJNa3xa0hhWGAR0JEcIEQFH0mtIVyyAk55wswD-P-zva0ZfoGv2MyUjcDEHWqookAEa7M3vu9olxWA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"103\" height=\"59\"></p>\n<p><strong id=\"docs-internal-guid-180abc58-7fff-5480-b57b-8ecf7abda10f\"></strong><br><br><br><br></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcWeekOD5_rotlDhPSgo8MWBYSk1YApcYM6dWz-y2hLekofIZM9yZo_UGy8ICjCfbMWCPQRS3NHwlOuzc6QhLH8jxnGUhzQTxfzCG0mNB17Vl0xJq9Lo6_9ob3ra-EEEHI393HQIw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"99\" height=\"58\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4CZ-9i0KZtlqIef8zSqB70x4f5FWcvylFw4eXZToKMPK9vdijh-U4lr-o8qKepr35e_5D3ed_w56d2MqXKAQ5Ha99F0NRIeIrWDzDzOM_7cMFFqj7XriNRvj1oYQk0d9ORPUOig?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"93\" height=\"56\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf_AqONZnDaTQ5yXQ9e8y7VUyIUGF1tizzBNB7NmQcU6a2KEl9vMLvhQwLDZUNmsbmlkPLuhXFysanRCARsPZMkDe_5_72a1vEx2RixQERH2fghC9B2rYYqbZVFRJ_Tg2zn-cx_QQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"105\" height=\"60\"><br><br>लेकिन,&nbsp;<img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe4tpwwZfMJwT2MAcQ7x_e30R1jRBPksCucB0jkm1rOg39HFaLEIUAfNW22gJNa3xa0hhWGAR0JEcIEQFH0mtIVyyAk55wswD-P-zva0ZfoGv2MyUjcDEHWqookAEa7M3vu9olxWA?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"103\" height=\"59\"><br><br><br><br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In a certain code language, &lsquo;pleased to meet&rsquo; is written as &lsquo;bj sc vw&rsquo; and &lsquo;meet and greet&rsquo; is written as &lsquo;ih bj tf&rsquo;. How is &lsquo;meet&rsquo; written in the given language?</p>",
                    question_hi: "<p>22. एक निश्चित कूट भाषा में, &lsquo;pleased to meet&rsquo; को &lsquo;bj sc vw&rsquo; लिखा जाता हैऔर &lsquo;meet and greet&rsquo; को &lsquo;ih bj tf&rsquo; लिखा जाता है। उसी कूट भाषा में &lsquo;meet&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>bj</p>", "<p>ih</p>", 
                                "<p>tf</p>", "<p>vw</p>"],
                    options_hi: ["<p>bj</p>", "<p>ih</p>",
                                "<p>tf</p>", "<p>vw</p>"],
                    solution_en: "<p>22.(a) pleased to meet &rarr; bj sc vw&hellip;&hellip;(i)<br>meet and greet &rarr; ih bj tf&hellip;&hellip;..(ii)<br>From (i) and (ii) &lsquo;meet&rsquo; and &lsquo;bj&rsquo; are common. The code of &lsquo;meet&rsquo; = &lsquo;bj&rsquo;.</p>",
                    solution_hi: "<p>22.(a) pleased to meet &rarr; bj sc vw&hellip;&hellip;(i)<br>meet and greet &rarr; ih bj tf&hellip;&hellip;..(ii)<br>(i) और (ii) से \'meet\' और \'bj\' उभयनिष्ठ हैं। \'meet\' का कोड = \'bj\'।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. 12 is related to 72 following a certain logic. Following the same logic, 19 is related to 114. To which of the following is 29 related following the same logic? <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>23. एक निश्चित तर्क का अनुसरण करते हुए 12, 72 से संबंधित है। उसी तर्क का अनुसरण करते हुए 19, 114 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 29 निम्नलिखित में से किस से संबंधित है? (ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>171</p>", "<p>174</p>", 
                                "<p>173</p>", "<p>170</p>"],
                    options_hi: ["<p>171</p>", "<p>174</p>",
                                "<p>173</p>", "<p>170</p>"],
                    solution_en: "<p>23.(b) <strong>Logic</strong> :- (1st number &times; 6) = 2nd number<br>(12, 72) :- (12 &times; 6) = 72<br>(19, 114) :- (19 &times; 6) = 114<br>Similarly,<br>(29, ?) :- (29 &times; 6) = 174</p>",
                    solution_hi: "<p>23.(b) <strong>तर्क</strong> :- (पहली संख्या &times; 6) = दूसरी संख्या<br>(12, 72) :- (12 &times; 6) = 72<br>(19, 114) :- (19 &times; 6) = 114<br>इसी प्रकार,<br>(29, ?) :- (29 &times; 6) = 174</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. How many triangles are there in the given figure?<br><strong id=\"docs-internal-guid-5f7c617b-7fff-4c08-8c69-f35c08ef7aeb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfEU8S2TY1dUhThlpe_KhS3edeXXsrD5LIkl_r8FRojuxJoaGERiaCS5R2dXYrHSZNZgZjpgdowijAWZ6YO2WMpy2H6DQe0dirzCiWTRpXvT5BNxAzf6Jy7nR5fB94IfDtHOwVT?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"213\" height=\"138\"></strong></p>",
                    question_hi: "<p>24. दी गई आकृति में कितने त्रिभुज हैं?<br><strong id=\"docs-internal-guid-5f7c617b-7fff-4c08-8c69-f35c08ef7aeb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfEU8S2TY1dUhThlpe_KhS3edeXXsrD5LIkl_r8FRojuxJoaGERiaCS5R2dXYrHSZNZgZjpgdowijAWZ6YO2WMpy2H6DQe0dirzCiWTRpXvT5BNxAzf6Jy7nR5fB94IfDtHOwVT?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"213\" height=\"138\"></strong></p>",
                    options_en: ["<p>12</p>", "<p>14</p>", 
                                "<p>15</p>", "<p>13</p>"],
                    options_hi: ["<p>12</p>", "<p>14</p>",
                                "<p>15</p>", "<p>13</p>"],
                    solution_en: "<p>24.(b)<br><strong id=\"docs-internal-guid-e434cf6f-7fff-afaf-9689-a280e0cdcb82\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfR-vKliGyi7TLOTrwcI8pkh8KXo5YkLaAhz4dLLZ-dx4Frx25z6g49f2r8R6lvoz7xDQrraggXGZ-BZYLILm4E66YyMOw0c-ENj9SLzCC0RHFhJXAaROmjVR1OyDiT1iIGgBz2?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"207\" height=\"132\"></strong><br>There are 14 triangle.</p>",
                    solution_hi: "<p>24.(b)<br><strong id=\"docs-internal-guid-e434cf6f-7fff-afaf-9689-a280e0cdcb82\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfR-vKliGyi7TLOTrwcI8pkh8KXo5YkLaAhz4dLLZ-dx4Frx25z6g49f2r8R6lvoz7xDQrraggXGZ-BZYLILm4E66YyMOw0c-ENj9SLzCC0RHFhJXAaROmjVR1OyDiT1iIGgBz2?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"207\" height=\"132\"></strong><br>14 त्रिभुज हैं.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded?<br><strong id=\"docs-internal-guid-24421ab4-7fff-44d4-df52-e1c17e52e5ed\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe-uaCU9KZHwcXKiS5F5tKIXg3dENaqlrjmJsJkQTjNRrGXMQVtdgFoGLFJllle6oHlSWu-0gqtmmHIgAaT3Yx4wvLxkI-0d4waTSudF3ObUjH9CO-4Op1Eoh-k5vR4hUwBm8RHDQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"202\" height=\"69\"></strong></p>",
                    question_hi: "<p>25. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छेद किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा ?<br><strong id=\"docs-internal-guid-3d34ae52-7fff-4ecf-db04-838bf3545bf0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe-uaCU9KZHwcXKiS5F5tKIXg3dENaqlrjmJsJkQTjNRrGXMQVtdgFoGLFJllle6oHlSWu-0gqtmmHIgAaT3Yx4wvLxkI-0d4waTSudF3ObUjH9CO-4Op1Eoh-k5vR4hUwBm8RHDQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"202\" height=\"69\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-72be4c5c-7fff-6976-58b4-f63ffacfb03d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcIae_QDK-kTkM1hOVVD0bl6ZEPo-WszIhPbQ_08OwAxQPG3NUp4aM1eDOilybEDQcdHXIxUArnWPUO10tVdhqtzPhn2xYUb0XoTtrtVRuM2mAJSiTENI4FDu5WEIhrevdnwuu9hQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"83\" height=\"76\"></strong></p>", "<p><strong id=\"docs-internal-guid-e5ee4d10-7fff-9c37-3c1c-e6ac24b5bcf9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcphaZutEHIk5rM-O35iq7Y02PtvaPuNHzkTxvvHIFHAo4YQ3YOJzzGvw7hPZZ_BX80m_Rr8ESVOO0-A7mJrRcvIIvgKshMCa2hpKzv1ilVtfiZecOV5WJA7d6tThyeyJKSWZDMgw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"85\" height=\"82\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-65a99ae4-7fff-cd14-39fa-384aef295288\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQS4KpkiifBAvhSQIobGR6fxMNYb-1cq18BoFFe59TbTPfYeqHp0ZXREcDIeEClpEkZL7cRUh5ImPnX8V_ijjMoJvul5utQT9tVnaGeHZkhLO87YlVNhaFY3xvhkk2W_-w-kXX4Q?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"88\" height=\"85\"></strong></p>", "<p><strong id=\"docs-internal-guid-f7df2bf1-7fff-fa6d-ded9-9b9a25c72ac9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcEgCtafEe2Qk5EIYgqMzDGLYuMWQaSsEQMdif4Ubu_iat_CmNgLUEbOSrUaZeA3aSR0_oAI-dWRX5rKDr3Qm5UUJIT2yz4RZrsfqj1rjHbv3S9Ma78tKZt-AJM691qUQKrpoH94g?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"85\" height=\"83\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-ba2abc2e-7fff-66b2-0ae6-39700376cb31\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcIae_QDK-kTkM1hOVVD0bl6ZEPo-WszIhPbQ_08OwAxQPG3NUp4aM1eDOilybEDQcdHXIxUArnWPUO10tVdhqtzPhn2xYUb0XoTtrtVRuM2mAJSiTENI4FDu5WEIhrevdnwuu9hQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"83\" height=\"76\"></strong></p>", "<p><strong id=\"docs-internal-guid-8c613bab-7fff-7e19-cb69-************\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcphaZutEHIk5rM-O35iq7Y02PtvaPuNHzkTxvvHIFHAo4YQ3YOJzzGvw7hPZZ_BX80m_Rr8ESVOO0-A7mJrRcvIIvgKshMCa2hpKzv1ilVtfiZecOV5WJA7d6tThyeyJKSWZDMgw?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"85\" height=\"82\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-219cbdb8-7fff-a346-1c31-25521c46dac5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQS4KpkiifBAvhSQIobGR6fxMNYb-1cq18BoFFe59TbTPfYeqHp0ZXREcDIeEClpEkZL7cRUh5ImPnX8V_ijjMoJvul5utQT9tVnaGeHZkhLO87YlVNhaFY3xvhkk2W_-w-kXX4Q?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"88\" height=\"85\"></strong></p>", "<p><strong id=\"docs-internal-guid-45b61e81-7fff-ce55-064a-095f61d938ac\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcEgCtafEe2Qk5EIYgqMzDGLYuMWQaSsEQMdif4Ubu_iat_CmNgLUEbOSrUaZeA3aSR0_oAI-dWRX5rKDr3Qm5UUJIT2yz4RZrsfqj1rjHbv3S9Ma78tKZt-AJM691qUQKrpoH94g?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"85\" height=\"83\"></strong></p>"],
                    solution_en: "<p>25.(d)<br><strong id=\"docs-internal-guid-92207802-7fff-1c22-329b-7f14fac99087\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfu3kYcVdaZO_6d21c06C-RDDo_PYtFHI-JyVXzYGzSURyr93oZBuWAdjVHsuYvTQ3WzIC59ucmeYllhVLsYyHOCucO9UgrYamwxaQspnzVFOGMxeazffJ7VUhMv4T59nAIuE7MwQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"90\" height=\"87\"></strong></p>",
                    solution_hi: "<p>25.(d)<br><strong id=\"docs-internal-guid-92207802-7fff-1c22-329b-7f14fac99087\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfu3kYcVdaZO_6d21c06C-RDDo_PYtFHI-JyVXzYGzSURyr93oZBuWAdjVHsuYvTQ3WzIC59ucmeYllhVLsYyHOCucO9UgrYamwxaQspnzVFOGMxeazffJ7VUhMv4T59nAIuE7MwQ?key=DJFAriQ_I8VOIOkirGEe___7\" width=\"90\" height=\"87\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>