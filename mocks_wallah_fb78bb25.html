<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Given that the perimeters of two similar triangles, &Delta;ABC and &Delta;DEF, are 24 cm and 9 cm, respectively, and DE = 3 cm, what is the ratio between the area of &Delta;ABC and that of &Delta;DEF ?</p>",
                    question_hi: "<p>1. यदि दो समरूप त्रिभुजों , &Delta;ABC और &Delta;DEF के परिमाप क्रमशः 24 cm और 9 cm हैं, और DE = 3 cm है, तो &Delta;ABC और &Delta;DEF के क्षेत्रफल के बीच का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>54 : 9</p>", "<p>54 : 7</p>", 
                                "<p>64 : 9</p>", "<p>64 : 7</p>"],
                    options_hi: ["<p>54 : 9</p>", "<p>54 : 7</p>",
                                "<p>64 : 9</p>", "<p>64 : 7</p>"],
                    solution_en: "<p>1.(c)<br><math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>9</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>D</mi><mi>E</mi></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>E</mi><mi>F</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mn>3</mn></mfrac></math>, AB = 8 cm<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>D</mi><mi>E</mi></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math><br>Required ratio = 64 : 9</p>",
                    solution_hi: "<p>1.(c)<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow><mrow><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>9</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>D</mi><mi>E</mi></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>E</mi><mi>F</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mn>3</mn></mfrac></math>, AB = 8 cm<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>D</mi><mi>E</mi></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math><br>अभीष्ट अनुपात = 64 : 9</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Two parallel chords on the same side of the centre of a circle are 12 cm and 20 cm long and the radius of the circle is 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm.What is the distance between the chords?</p>",
                    question_hi: "<p>2. एक वृत्त के केंद्र के एक ही ओर 12 cm और 20 cm लंबाई की दो समान्तर जीवाएँ हैं और वृत्त की त्रिज्या 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm है। इन जीवाओं के बीच की दूरी कितनी होगी?</p>",
                    options_en: ["<p>2 cm</p>", "<p>3 cm</p>", 
                                "<p>4 cm</p>", "<p>5 cm</p>"],
                    options_hi: ["<p>2 cm</p>", "<p>3 cm</p>",
                                "<p>4 cm</p>", "<p>5 cm</p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828377.png\" alt=\"rId5\" width=\"177\" height=\"157\"><br>Radius of the circle(OB) = 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm<br>In triangle OAB,<br>AO = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mi>O</mi><mi>B</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>A</mi><mi>B</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>AO = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>6</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>AO = <math display=\"inline\"><msqrt><mn>325</mn><mo>-</mo><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>289</mn></msqrt></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>17</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>AO = 17 cm<br>In triangle OCD,<br>CO= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>CO = <math display=\"inline\"><msqrt><mn>325</mn><mo>-</mo><mn>100</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>15</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>CO = 15 cm<br>AC = AO - CO = 17 - 15 = 2 cm</p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828377.png\" alt=\"rId5\" width=\"173\" height=\"153\"><br>वृत्त की त्रिज्या (OB) = 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm<br>त्रिभुज OAB में,<br>AO = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mi>O</mi><mi>B</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>A</mi><mi>B</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>AO = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>6</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>AO = <math display=\"inline\"><msqrt><mn>325</mn><mo>-</mo><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>289</mn></msqrt></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>17</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>AO = 17 cm<br>त्रिभुज OCD में,<br>CO= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>CO = <math display=\"inline\"><msqrt><mn>325</mn><mo>-</mo><mn>100</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>15</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>CO = 15 cm<br>AC = AO - CO = 17 - 15 = 2 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If PQ and PR are the two tangents to a circle with center O, and <math display=\"inline\"><mi>&#8736;</mi></math>QOR=150&deg;, then &ang;QPR is equal to :</p>",
                    question_hi: "<p>3. यदि PQ और PR केद्र O वाले वृत्त की दो स्पर्श रेखाएँ हैं, और &ang;QOR = 150&deg; है, तो &ang;QPR किसके बराबर है?</p>",
                    options_en: ["<p>60&deg;</p>", "<p>90&deg;</p>", 
                                "<p>45&deg;</p>", "<p>30&deg;</p>"],
                    options_hi: ["<p>60&deg;</p>", "<p>90&deg;</p>",
                                "<p>45&deg;</p>", "<p>30&deg;</p>"],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828494.png\" alt=\"rId6\" width=\"214\" height=\"137\"><br>the angle between the radius and the tangent always be exactly 90&deg;.<br>So, &ang;OQP = &ang;ORP = 90&deg;<br>&ang;QPR = 360&deg; - (150&deg; + 90&deg; + 90&deg;) = 30&deg;</p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828494.png\" alt=\"rId6\" width=\"214\" height=\"137\"><br>त्रिज्या और स्पर्शरेखा के बीच का कोण हमेशा 90&deg; होता है।<br>तो, &ang;OQP = &ang;ORP = 90&deg;<br>&ang;QPR = 360&deg; - (150&deg; + 90&deg; + 90&deg;) = 30&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Which of the following is a correct statement?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: ["<p>If two circles touch each other, the point of contact lies on the line joining the two centres.</p>", "<p>Equal chords are equidistant from the centre of the circle but not always subtend equal angle at centre of the circle</p>", 
                                "<p>The sum of the angles of a cyclic quadrilateral is always 180&deg;.</p>", "<p>Angles subtended by the arc in the same segment of the circle are in ratio of 2 : 1.</p>"],
                    options_hi: ["<p>यदि दो वृत्त एक-दूसरे को स्पर्श करते हैं, तो संपर्क बिंदु दोनों केंद्रों को जोड़ने वाली रेखा पर स्थित होता है।</p>", "<p>बराबर लंबाई वाली जीवाएं वृत्त के केंद्र से समान दूरी पर होती हैं लेकिन सदैव वृत्त के केंद्र पर समान कोण अंतरित नहीं करती हैं।</p>",
                                "<p>चक्रीय चतुर्भुज के कोणों का योग सदैव 180&deg; होता है।</p>", "<p>वृत्त के एक ही खंड में चाप द्वारा अंतरित कोणों का अनुपात 2 : 1 होता है।</p>"],
                    solution_en: "<p>4.(a)<br>Only option (a) is the correct option.<br>If two circles touch each other, the point of contact lies on the line joining the two&nbsp;centres.</p>",
                    solution_hi: "<p>4.(a)<br>केवल विकल्प (a) सही विकल्प है।<br>यदि दो वृत्त एक-दूसरे को स्पर्श करते हैं, तो संपर्क बिंदु दोनों केंद्रों को जोड़ने वाली रेखा पर स्थित होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. PQ is a diameter of a circle with centre O. The tangents at R meets PQ produced at A. If &ang;RPQ = 27&deg;, then measure of &ang;RQP is:</p>",
                    question_hi: "<p>5. केंद्र O वाले एक वृत्त का व्यास PQ है। PQ रेखा को बढ़ाया जाता है, जो R की स्पर्श रेखा के साथ A पर मिलती है। यदि &ang;RPQ = 27&deg; है, तो &ang;RQP का माप क्या होगा?</p>",
                    options_en: ["<p>63&deg;</p>", "<p>54&deg;</p>", 
                                "<p>27&deg;</p>", "<p>153&deg;</p>"],
                    options_hi: ["<p>63&deg;</p>", "<p>54&deg;</p>",
                                "<p>27&deg;</p>", "<p>153&deg;</p>"],
                    solution_en: "<p>5.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828630.png\" alt=\"rId7\" width=\"273\" height=\"121\"><br>In ∆ PQR<br>&ang;PRQ = 90&deg; [angle made by diameter at the circumference of the circle]<br>&ang;RPQ = 27&deg; [given]<br>90&deg; + 27&deg; + &ang;RQP = 180&deg;<br>So, &ang;RQP = 180&deg; - 117&deg; = 63&deg;</p>",
                    solution_hi: "<p>5.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828630.png\" alt=\"rId7\" width=\"273\" height=\"121\"><br>∆ PQR में<br>&ang;PRQ = 90&deg; [वृत्त की परिधि पर व्यास द्वारा बनाया गया कोण]<br>&ang;RPQ = 27&deg; [दिया गया है]<br>90&deg; + 27&deg; + &ang;RQP = 180&deg;<br>तो, &ang;RQP = 180&deg; - 117&deg; = 63&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Two circles touch each other at a point O. AB is a simple common tangent to both the circles touching at point A and point B. If radii of the circles are 9 cm and 4 cm, then find AB.</p>",
                    question_hi: "<p>6. दो वृत्त एक दूसरे को बिंदु O पर स्पर्श करते हैं। AB दोनों वृत्तों की एक सरल उभयनिष्ठ स्पर्श रेखा है जो बिंदु A और बिंदु B पर स्पर्श करती है। यदि वृत्तों की त्रिज्याएँ 9 cm और 4 cm हैं, तो AB ज्ञात कीजिए।</p>",
                    options_en: ["<p>72 cm</p>", "<p>12 cm</p>", 
                                "<p>24 cm</p>", "<p>144 cm</p>"],
                    options_hi: ["<p>72 cm</p>", "<p>12 cm</p>",
                                "<p>24 cm</p>", "<p>144 cm</p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828806.png\" alt=\"rId8\" width=\"223\" height=\"137\"><br>Length of tangent AB = 2<math display=\"inline\"><msqrt><msub><mrow><mi>r</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mo>&#215;</mo><mn>9</mn></msqrt></math>= 12 cm</p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828806.png\" alt=\"rId8\" width=\"223\" height=\"137\"><br>स्पर्शरेखा AB की लंबाई = 2<math display=\"inline\"><msqrt><msub><mrow><mi>r</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mo>&#215;</mo><mn>9</mn></msqrt></math> = 12 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. In a ∆ABC, two medians AD and BE intersect at G at right angles. If AD = 18 cm and BE = 12 cm, then the length of BD is equal to:</p>",
                    question_hi: "<p>7. ∆ABC में, दो माध्यिकाएँ AD और BE, G पर समकोण पर प्रतिच्छेद करती हैं। यदि AD = 18 cm और BE = 12 cm है, तो BD की लंबाई किसके बराबर है?</p>",
                    options_en: ["<p>15 cm</p>", "<p>10 cm</p>", 
                                "<p>20 cm</p>", "<p>8 cm</p>"],
                    options_hi: ["<p>15 cm</p>", "<p>10 cm</p>",
                                "<p>20 cm</p>", "<p>8 cm</p>"],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828997.png\" alt=\"rId9\" width=\"200\" height=\"165\"><br>Centroid intersect the medians into 2 : 1<br>AD = 18 cm<br>AD (3 unit) = 18 cm <math display=\"inline\"><mo>&#8658;</mo></math> 1 unit = 6 cm<br>Then, AG (2 unit) = 12 cm , GD (1 cm) = 6 cm<br>Now, BE = 12 cm<br>BE (3 unit) = 12 cm <math display=\"inline\"><mo>&#8658;</mo></math> 1 unit = 4 cm<br>BG (2 unit) = 8 cm , GE (1 unit) = 4 cm<br>Now, in right angle <math display=\"inline\"><mi>&#916;</mi></math> BGD<br><math display=\"inline\"><mi>B</mi><msup><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>B</mi><msup><mi>G</mi><mn>2</mn></msup></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>G</mi><msup><mi>D</mi><mn>2</mn></msup></math><br><math display=\"inline\"><mi>B</mi><msup><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>2</mn></msup></math>= 100<br><math display=\"inline\"><mi>B</mi><mi>D</mi></math> = 10 cm</p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750828997.png\" alt=\"rId9\" width=\"200\" height=\"165\"><br>केन्द्रक माध्यिकाओं को 2 : 1 में काटता है<br>AD = 18 सेमी<br>AD (3 इकाई) = 18 सेमी<br>फिर, AG (2 इकाई) = 12 सेमी, GD (1 सेमी) = 6 सेमी<br>अब, BE = 12 सेमी<br>BE (3 इकाई) = 12 सेमी<br>BG (2 इकाई) = 8 सेमी, GE (1 इकाई) = 4 सेमी<br>अब, समकोण <math display=\"inline\"><mi>&#916;</mi></math> BGD में<br><math display=\"inline\"><mi>B</mi><msup><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>B</mi><msup><mi>G</mi><mn>2</mn></msup></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>G</mi><msup><mi>D</mi><mn>2</mn></msup></math><br><math display=\"inline\"><mi>B</mi><msup><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>2</mn></msup></math>= 100<br><math display=\"inline\"><mi>B</mi><mi>D</mi></math> = 10 सेमी</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In a circle, ABCD is a cyclic quadrilateral in which AE is drawn parallel to CD, and BA&nbsp;is produced to F. If &ang;ABC = 85&deg; and &ang;FAE = 24&deg;, find the value of &ang;BCD<em>.</em></p>",
                    question_hi: "<p>8. एक वृत में, ABCD एक चक्रीय चतुर्भुज है जिसमें AE को CD के समानांतर खींचा गया है, और BA को F तक बढ़ाया गया है। यदि &ang;ABC = 85&deg; और &ang;FAE = 24&deg; हो, तो &ang;BCD का मान ज्ञात करें।</p>",
                    options_en: ["<p>119&deg;</p>", "<p>124&deg;</p>", 
                                "<p>115&deg;</p>", "<p>125&deg;</p>"],
                    options_hi: ["<p>119&deg;</p>", "<p>124&deg;</p>",
                                "<p>115&deg;</p>", "<p>125&deg;</p>"],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750829117.png\" alt=\"rId10\" width=\"249\" height=\"165\"><br>In cyclic quadrilateral ABCD<br>&ang;B + &ang;D = 180&deg;<br>&ang;D = 180&deg; - 85&deg; = 95&deg;<br>Now EA <math display=\"inline\"><mo>&#8741;</mo></math> DC<br>So, <br>&ang;DAH + &ang;D = 180&deg; &hellip; (co-interior angle)<br>&ang;DAB + &ang;BAH+ &ang;D = 180&deg;<br>&ang;DAB = 180&deg; - 95&deg; - 24&deg; = 61&deg;<br>Now <br>&ang;C + &ang;DAB = 180&deg;<br>&ang;C = 180&deg; - 61&deg; = 119&deg;</p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750829117.png\" alt=\"rId10\" width=\"249\" height=\"165\"><br>चक्रीय चतुर्भुज ABCD में<br>&ang;B + &ang;D = 180&deg;<br>&ang;D = 180&deg; - 85&deg; = 95&deg;<br>अब, EA <math display=\"inline\"><mo>&#8741;</mo></math> DC<br>इसलिए, <br>&ang;DAH + &ang;D = 180&deg; &hellip; (सह-आंतरिक कोण)<br>&ang;DAB + &ang;BAH+ &ang;D = 180&deg;<br>&ang;DAB = 180&deg; - 95&deg; - 24&deg; = 61&deg;<br>अब,<br>&ang;C + &ang;DAB = 180&deg;<br>&ang;C = 180&deg; - 61&deg; = 119&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. CD is a tangent to a circle of circumference of 88 cm with its centre at O. OC cuts the circle at P while OD cuts circle at V such that &ang;COD = 90&deg;. Length of PC is 6 cm. What is half of CD (in cm) if OD exceeds OC by 1 cm?</p>",
                    question_hi: "<p>9. CD, 88 सेमी परिधि वाले एक वृत्त की स्पर्शरेखा है जिसका केंद्र O है। OC वृत्त को P पर काटता है जबकि OD वृत्त को V पर इस प्रकार काटता है कि &ang;COD = 90&deg; है। PC की लंबाई 6 सेमी है। यदि OD, OC से 1 सेमी अधिक है तो CD का आधा (सेमी में) क्या है?</p>",
                    options_en: ["<p>14.5</p>", "<p>15.5</p>", 
                                "<p>16.5</p>", "<p>31</p>"],
                    options_hi: ["<p>14.5</p>", "<p>15.5</p>",
                                "<p>16.5</p>", "<p>31</p>"],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750829280.png\" alt=\"rId11\" width=\"220\" height=\"144\"><br>Circumference of the circle = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi></math><br>88 = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; r<br>r = 14 cm<br>Length of PO and OV = 14 cm<br>So the length of CO = 6 + 14 = 20 cm<br>And the length of OD = 20 + 1 = 21<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>C</mi><mi>D</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>C</mi><mi>O</mi><mo>)</mo></mrow><mn>2</mn></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>O</mi><mi>D</mi><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>C</mi><mi>D</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mn>2</mn></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>21</mn><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>C</mi><mi>D</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 400 + 441 = 841<br>CD = 29 cm<br>Half of the CD = <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 14.5 cm</p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750829280.png\" alt=\"rId11\" width=\"220\" height=\"144\"><br>वृत्त की परिधि = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi></math><br>88 = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; r<br>r = 14 cm<br>PO और OV की लंबाई = 14 सेमी<br>अतः CO की लंबाई = 6 + 14 = 20 cm<br>और OD की लंबाई = 20 + 1 = 21<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>C</mi><mi>D</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>C</mi><mi>O</mi><mo>)</mo></mrow><mn>2</mn></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>O</mi><mi>D</mi><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>C</mi><mi>D</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mn>2</mn></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>21</mn><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>C</mi><mi>D</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 400 + 441 = 841<br>CD = 29 cm<br>CD का आधा भाग = <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 14.5 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. In a triangle ABC, AB = 6 units, BC = 8 units and AC = 10 units. Let M be a point on AC such that BM = 5 units. With a point D, a triangle BMD is formed and the triangle BMD is similar to the triangle ABC with <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>M</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>D</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>What is the length of BD in units?</p>",
                    question_hi: "<p>10. त्रिभुज ABC में, AB = 6 इकाई, BC = 8 इकाई तथा AC = 10 इकाई है। माना M, AC पर एक बिंदु इस प्रकार है कि BM = 5 इकाई है। एक बिंदु D के साथ, एक त्रिभुज BMD बनाया जाता है तथा त्रिभुज BMD त्रिभुज ABC के समरूप है, जिसमें <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>M</mi><mi>&#160;</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>D</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math> है। BD की लंबाई इकाइयों में कितनी है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750829511.png\" alt=\"rId13\" width=\"201\" height=\"216\"><br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>M</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>D</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>D</mi></mrow><mn>10</mn></mfrac></math><br>BD = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> units</p>",
                    solution_hi: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750829762.png\" alt=\"rId14\" width=\"181\" height=\"198\"><br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>M</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>D</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>D</mi></mrow><mn>10</mn></mfrac></math><br>BD = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> इकाई</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If the radii of two circles are 6 cm and 3 cm and the length of the transverse common tangent is 8 cm, then the distance between the two circles is:</p>",
                    question_hi: "<p>11. यदि दो वृत्तों की त्रिज्याएं 6 cm और 3 cm हैं तथा अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा (transverse common tangent) की लंबाई 8 cm है, तो दोनों वृत्तों के बीच की दूरी ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>145</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>141</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\"><msqrt><mn>147</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>143</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>145</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>141</mn></msqrt></math></p>",
                                "<p><math display=\"inline\"><msqrt><mn>147</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>143</mn></msqrt></math></p>"],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750829966.png\" alt=\"rId15\" width=\"289\" height=\"147\"><br>Transverse common tangent (<math display=\"inline\"><mi>l</mi></math>) = 8 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 8<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>6</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 8<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><mn>81</mn></msqrt></math> = 8<br><math display=\"inline\"><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>81</mn></math> = 64 <br><math display=\"inline\"><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 64 + 81<br>= 145<br><math display=\"inline\"><mi>d</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>145</mn></msqrt></math></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750829966.png\" alt=\"rId15\" width=\"289\" height=\"147\"><br>अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा = <math display=\"inline\"><mn>8</mn></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 8<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>6</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 8<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><mn>81</mn></msqrt></math> = 8<br><math display=\"inline\"><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>81</mn></math> = 64 <br><math display=\"inline\"><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 64 + 81<br>= 145<br><math display=\"inline\"><mi>d</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>145</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. In an isosceles triangle, the angle between equal sides is 40&deg;. The bisectors of the other two angles will intersect each other at an angle of:</p>",
                    question_hi: "<p>12. एक समद्विबाहु त्रिभुज की बराबर भुजाओं के बीच का कोण 40&deg; है। अन्य दो कोणों के समद्विभाजक एक-दूसरे को _______ के कोण पर प्रतिच्छेद करेंगे।</p>",
                    options_en: ["<p>100&deg;</p>", "<p>105&deg;</p>", 
                                "<p>120&deg;</p>", "<p>110&deg;</p>"],
                    options_hi: ["<p>100&deg;</p>", "<p>105&deg;</p>",
                                "<p>120&deg;</p>", "<p>110&deg;</p>"],
                    solution_en: "<p>12.(d)<br>Required angle = 90&deg; + <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>&#176;</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 110&deg;</p>",
                    solution_hi: "<p>12.(d)<br>अभीष्ट कोण = 90&deg; + <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>&#176;</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 110&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Given is a circle with centre at C. A, B and D are the points on the circumference. Find&nbsp;&ang;ABC.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750830088.png\" alt=\"rId16\" width=\"181\" height=\"170\"></p>",
                    question_hi: "<p>13. केंद्र C पर एक वृत्त दिया गया है। A, B और D परिधि पर बिंदु हैं। &ang;ABC ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750830088.png\" alt=\"rId16\" width=\"181\" height=\"170\"></p>",
                    options_en: ["<p>37&deg;</p>", "<p>33&deg;</p>", 
                                "<p>35&deg;</p>", "<p>30&deg;</p>"],
                    options_hi: ["<p>37&deg;</p>", "<p>33&deg;</p>",
                                "<p>35&deg;</p>", "<p>30&deg;</p>"],
                    solution_en: "<p>13.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750830192.png\" alt=\"rId17\" width=\"165\" height=\"153\"><br>Construction : join A and C<br>(∵ The angle subtended by an arc at the center is twice the angle subtended at the circumference of the circle)<br>So, &ang;BCD = 2&ang;BAD <br>126&deg; = 2&ang;BAD <math display=\"inline\"><mo>&#8658;</mo></math> &ang;BAD = 63&deg;<br>in <math display=\"inline\"><mi>&#916;</mi></math> ACD <br>&ang;CAD = &ang;CDA = 33&deg; &hellip; {angle opposite to equal side}<br>Now, &ang;BAC = &ang;BAD - &ang;CAD <br>&ang;BAC = 63&deg; - 33&deg; = 30&deg;<br>in <math display=\"inline\"><mi>&#916;</mi></math> ACB<br>&ang;CAB = &ang;ABC = 30&deg; &hellip; {angle opposite to equal side}<br>Hence, &ang;ABC = 30&deg;</p>",
                    solution_hi: "<p>13.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750830192.png\" alt=\"rId17\" width=\"165\" height=\"153\"><br>(∵ एक चाप द्वारा केंद्र पर बनाया गया कोण वृत्त की परिधि पर बनाए गए कोण का दोगुना होता है।)<br>तो, &ang;BCD = 2&ang;BAD <br>126&deg; = 2&ang;BAD <math display=\"inline\"><mo>&#8658;</mo></math> &ang;BAD = 63&deg;<br><math display=\"inline\"><mi>&#916;</mi></math> ACD में<br>&ang;CAD = &ang;CDA = 33&deg; {सामान भुजाओं के विपरीत कोण}<br>अब , &ang;BAC = &ang;BAD - &ang;CAD <br>&ang;BAC = 63&deg; - 33&deg; = 30&deg;<br><math display=\"inline\"><mi>&#916;</mi></math> ACB में<br>&ang;CAB = &ang;ABC = 30&deg; {सामान भुजाओं के विपरीत कोण}<br>अत: , &ang;ABC = 30&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Diagonals of a trapezium ABCD with AB∥DC, intersect each other at the point &lsquo;O&rsquo;. If AB = 2.5CD, find the ratio of the area of triangle AOB to the area of triangle COD.</p>",
                    question_hi: "<p>14. AB || DC वाले एक समलंब चतुर्भुज ABCD के विकर्ण, एक दूसरे को बिंदु \'O\' पर प्रतिच्छेदित करते हैं। यदि AB = 2.5CD है, तो त्रिभुज AOB के क्षेत्रफल और त्रिभुज COD के क्षेत्रफल का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>16 : 1</p>", "<p>25 : 4</p>", 
                                "<p>9 : 2</p>", "<p>5 : 2</p>"],
                    options_hi: ["<p>16 : 1</p>", "<p>25 : 4</p>",
                                "<p>9 : 2</p>", "<p>5 : 2</p>"],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750830319.png\" alt=\"rId18\" width=\"206\" height=\"124\"><br>In the trapezium ABCD <br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>&#160;</mi><mi>&#916;</mi><mi>&#160;</mi><mi>A</mi><mi>O</mi><mi>B</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>&#160;</mi><mi>&#916;</mi><mi>&#160;</mi><mi>D</mi><mi>O</mi><mi>C</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><msup><mi>B</mi><mn>2</mn></msup></mrow><mrow><mi>D</mi><msup><mi>C</mi><mn>2</mn></msup></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>&#160;</mi><mi>&#916;</mi><mi>&#160;</mi><mi>A</mi><mi>O</mi><mi>B</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>&#160;</mi><mi>&#916;</mi><mi>&#160;</mi><mi>D</mi><mi>O</mi><mi>C</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math><br>Hence, required ratio = 25 : 4</p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750830319.png\" alt=\"rId18\" width=\"206\" height=\"124\"><br>समलंब ABCD में <br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;</mi><mi>&#160;</mi><mi>A</mi><mi>O</mi><mi>B</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;</mi><mi>&#160;</mi><mi>D</mi><mi>O</mi><mi>C</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><msup><mi>B</mi><mn>2</mn></msup></mrow><mrow><mi>D</mi><msup><mi>C</mi><mn>2</mn></msup></mrow></mfrac></math><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;</mi><mi>&#160;</mi><mi>A</mi><mi>O</mi><mi>B</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#160;</mi><mi>&#916;</mi><mi>&#160;</mi><mi>D</mi><mi>O</mi><mi>C</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math><br>अतः, आवश्यक अनुपात = 25 : 4</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Two circles of radius 7 units each, intersect in such a way that the common chord is of length 7 units. What is the common area in square units of the intersection ?</p>",
                    question_hi: "<p>15. प्रत्येक 7 इकाई त्रिज्या वाले दो वृत्त इस प्रकार प्रतिच्छेद करते हैं कि उभयनिष्ठ जीवा की लंबाई 7 इकाई हो। प्रतिच्छेदन का वर्ग इकाइयों में उभयनिष्ठ क्षेत्रफल क्या होगा ?</p>",
                    options_en: ["<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>3</mn></mfrac></math>)</p>", "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>", 
                                "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>6</mn></mfrac></math>)</p>", "<p>98(<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>"],
                    options_hi: ["<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>3</mn></mfrac></math>)</p>", "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>",
                                "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>6</mn></mfrac></math>)</p>", "<p>98(<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>"],
                    solution_en: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750830475.png\" alt=\"rId19\" width=\"245\" height=\"159\"><br>Common area = 2 &times; (area of sector OABO - Area of equilateral <math display=\"inline\"><mi>&#916;</mi></math> OAB)<br>= 2 &times; (<math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>360</mn></mrow></mfrac><mi>&#960;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mo>(</mo><mn>7</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>&times; <math display=\"inline\"><mi>&#160;</mi><mo>(</mo><mn>7</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math>)<br>= 2 &times;<math display=\"inline\"><mi>&#160;</mi><mo>(</mo><mn>7</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&pi; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)<br>= 98 (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac><mi>&#960;</mi><mi>&#160;</mi></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)<br>Hence, required area = 98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736750830475.png\" alt=\"rId19\" width=\"245\" height=\"159\"><br>उभयनिष्ठ क्षेत्रफल = 2 &times; (त्रिज्यखंड OABO का क्षेत्रफल - समबाहु <math display=\"inline\"><mi>&#916;</mi></math>OAB का क्षेत्रफल)<br>= 2 &times; (<math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>360</mn></mrow></mfrac><mi>&#960;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mo>(</mo><mn>7</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>&times; <math display=\"inline\"><mi>&#160;</mi><mo>(</mo><mn>7</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math>)<br>= 2 &times;<math display=\"inline\"><mi>&#160;</mi><mo>(</mo><mn>7</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&pi; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)<br>= 98 (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac><mi>&#960;</mi><mi>&#160;</mi></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)<br>अत: अभीष्ट क्षेत्रफल = 98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>