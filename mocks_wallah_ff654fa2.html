<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. Article 350A of the Indian constitution is related with______.</p>",
                    question_hi: "<p>1. भारतीय संविधान का अनुच्छेद 350A ______से संबंधित है।</p>",
                    options_en: ["<p>Special officers for linguistic minorities</p>", "<p>Facilities for instruction in mother-tongue at primary stage</p>", 
                                "<p>Direction for development of the Hindi language</p>", "<p>Language to` be used in Supreme Court and High Courts of India</p>"],
                    options_hi: ["<p>भाषाई अल्पसंख्यकों के लिए विशेष अधिकारी</p>", "<p>प्राथमिक स्तर पर मातृभाषा में शिक्षा की सुविधाएं</p>",
                                "<p>हिंदी भाषा के विकास के लिए निदेशन</p>", "<p>भारत के उच्चतम न्यायालय और उच्च न्यायालयों में प्रयोग होने वाली भाषा</p>"],
                    solution_en: "<p>1.(b) Article - 350 : Language to be used in representations for redress of grievances. Article - 350B: Special Officer for linguistic minorities. Article- 351: Directive for development of the Hindi language. Article 348 (1)(a): All proceedings in the Supreme Court and in every High Court, shall be in English language.</p>",
                    solution_hi: "<p>1.(b) अनुच्छेद - 350: शिकायतों के निवारण के लिए अभ्यावेदन में प्रयोग की जाने वाली भाषा। अनुच्छेद - 350B : भाषाई अल्पसंख्यकों के लिए विशेष अधिकारी। अनुच्छेद- 351 : हिन्दी भाषा के विकास के लिए निर्देश। अनुच्छेद 348 (1)(a): सर्वोच्च न्यायालय और प्रत्येक उच्च न्यायालय में सभी कार्यवाही अंग्रेजी भाषा में होगी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. Who among the following is a famous Santoor instrumentalist ?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन प्रसिद्ध संतूर वादक हैं ?</p>",
                    options_en: ["<p>Shiv Kumar Sharma</p>", "<p>Ustad Binda Khan</p>", 
                                "<p>Sajjad Hussain</p>", "<p>Nikhil Banerjee</p>"],
                    options_hi: ["<p>शिव कुमार शर्मा</p>", "<p>उस्ताद बिंदा खान</p>",
                                "<p>सज्जाद हुसैन</p>", "<p>निखिल बनर्जी</p>"],
                    solution_en: "<p>2.(a) <strong>Pandit Shiv Kumar Sharma:- </strong>He belongs to the state of Jammu and Kashmir. <strong>Ustad Binda Khan - </strong>He was a famous Sarangi player. <strong>Sajjad Hussain -</strong> He was a famous music composer of Indian Hindi cinema. <strong>Nikhil Banerjee -</strong> He was an Indian classical sitarist of the Maihar Gharana. <strong>Other Santoor Players -</strong> Ulhas Bapat, Tarun Bhattacharya, Rahul Sharma, Shivkumar Sharma, etc.</p>",
                    solution_hi: "<p>2.(a)<strong> पंडित शिव कुमार शर्मा:- </strong>ये जम्मू-कश्मीर राज्य से हैं। <strong>उस्ताद बिंदा खान -</strong> वह एक प्रसिद्ध सारंगी वादक थे। <strong>सज्जाद हुसैन - </strong>वह भारतीय हिंदी सिनेमा के प्रसिद्ध संगीत रचयिता ( music composer) थे। <strong>निखिल बनर्जी - </strong>वह मैहर घराने के एक भारतीय शास्त्रीय सितारवादक थे। <strong>अन्य संतूर वादक - </strong>उल्हास बापट, तरूण भट्टाचार्य, राहुल शर्मा, शिवकुमार शर्मा आदि।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. How many such pairs of letters are there in the word &lsquo;VERSION&rsquo;(in both the forward and backward directions) which have as many letters between them in the word as there are in the English alphabetical order ?</p>",
                    question_hi: "<p>3. शब्द \'VERSION\' (आगे और पीछे दोनों दिशाओं में) में अक्षरों के ऐसे कितने जोड़े हैं जिनके बीच शब्द में उतने ही अक्षर हैं जितने कि अंग्रेजी वर्णानुक्रम में हैं ?</p>",
                    options_en: ["<p>5</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>5</p>", "<p>6</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>3.(a) <br>VERSION = 22 5 18 19 9 15 14<br>Pairs are: (R and S), (R and N), (O and N), (R and O), (V and S)<br>So, there are total 5 pairs possible.</p>",
                    solution_hi: "<p>3.(a)<br>VERSION = 22 5 18 19 9 15 14<br>जोड़े हैं: (R और S), (R और N), (O और N), (R और O), (V और S)<br>तो, कुल 5 जोड़े संभव हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. Rengma is primarily a folk dance of ______.</p>",
                    question_hi: "<p>4. रेंगमा मुख्य रूप से _____ का लोक नृत्य है।</p>",
                    options_en: ["<p>Bihar</p>", "<p>West Bengal</p>", 
                                "<p>Nagaland</p>", "<p>Rajasthan</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>पश्चिम बंगाल</p>",
                                "<p>नागालैंड</p>", "<p>राजस्थान</p>"],
                    solution_en: "<p>4.(c) <br>Rengma is primarily a folk dance of Nagaland. Nagaland has multiple tribes each having their own uniqueness, dance forms, cultures, language and more. Rengma is one among them. Rengma Tribes celebrate a lot of seasonal festivals that are related to their agriculture. &lsquo;Ngada&rsquo; is the most important festival of the Rengma tribes. It is celebrated after harvesting the crop or at the end of November or in the beginning of December. It is a \"Thanks giving\" festival of the Rengma tribes.</p>",
                    solution_hi: "<p>4.(c)<br>रेंगमा मुख्य रूप से <strong>नागालैंड </strong>का लोक नृत्य है। नागालैंड में कई जनजातियाँ हैं जिनमें से प्रत्येक की अपनी विशिष्टता, नृत्य रूप, संस्कृति, भाषा और बहुत कुछ है। रेंगमा उनमें से एक हैं। रेंगमा जनजाति बहुत सारे मौसमी त्योहार मनाती है जो उनकी कृषि से संबंधित हैं। \'नगाड़ा\' रेंगमा जनजातियों का सबसे महत्वपूर्ण त्योहार है। यह फसल की कटाई के बाद या नवंबर के अंत में या दिसंबर की शुरुआत में मनाया जाता है। यह रेंगमा जनजातियों का \"धन्यवाद देने\" का त्योहार है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. Select the option that is related to the third term in the same way as the second term is related to the ﬁrst term.<br>BSTN : AQUP :: DNUC : ?</p>",
                    question_hi: "<p>5. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>BSTN : AQUP :: DNUC : ?</p>",
                    options_en: ["<p>CLVE</p>", "<p>BSTO</p>", 
                                "<p>TSTB</p>", "<p>TOUS</p>"],
                    options_hi: ["<p>CLVE</p>", "<p>BSTO</p>",
                                "<p>TSTBE</p>", "<p>TOUS</p>"],
                    solution_en: "<p>5.(a)<br>B-1 = A, S-2 = Q, T+1 = U, N+2 = P<br>D-1=C, N-2 = L, U+1 = V, C+2 = E<br>CLVE, option (A) is the correct answer.</p>",
                    solution_hi: "<p>5.(a) <br>B-1 = A, S-2 = Q, T+1 = U, N+2 = P<br>D-1=C, N-2 = L, U+1 = V, C+2 = E<br>विकल्प (A), CLVE सही उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. The Annual Financial Statement, which is presented as a part of budget, is covered under which Article of the Constitution of india ?</p>",
                    question_hi: "<p>6. वार्षिक वित्तीय विवरण, जिसे बजट के भाग के रूप में प्रस्तुत किया जाता है, भारतीय संविधान के किस अनुच्छेद के अंतर्गत आता है ?</p>",
                    options_en: ["<p>Article 115<strong id=\"docs-internal-guid-8f13aeea-7fff-dafe-1115-4e049cd6ae47\"><br></strong></p>", "<p>&nbsp;Article 114</p>", 
                                "<p>&nbsp;Article 112</p>", "<p>&nbsp;Article 113</p>"],
                    options_hi: ["<p>अनुच्छेद 115</p>", "<p>अनुच्छेद 114</p>",
                                "<p>अनुच्छेद 112</p>", "<p>अनुच्छेद 113</p>"],
                    solution_en: "<p>6.(c) <br>The Annual Financial Statement is a document presented to Parliament every financial year as part of the Budget process, as required under Article 112. Article 112 - Annual financial statement.. Article 114 - Appropriation Bills. Article 115 - Supplementary, additional or excess grants.</p>",
                    solution_hi: "<p>6.(c)<br>वार्षिक वित्तीय विवरण बजट प्रक्रिया के भाग के रूप में प्रत्येक वित्तीय वर्ष में संसद में प्रस्तुत किया जाने वाला एक दस्तावेज है, जैसा कि <strong>अनुच्छेद 112 </strong>के तहत आवश्यक है। <strong>अनुच्छेद 112 -</strong> वार्षिक वित्तीय विवरण। लेख । <strong>अनुच्छेद 114 - </strong>विनियोग विधेयक। <strong>अनुच्छेद 115 -</strong> अनुपूरक, अतिरिक्त या अधिक अनुदान।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. A drum of water is <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>&nbsp;full. When 9 litres of water is drawn from it, it is <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&nbsp;full. What is the capacity of the drum ?</p>",
                    question_hi: "<p>7. पानी का एक ड्रम <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> भरा हुआ है। जब इसमें से 9 लीटर पानी निकाला जाता है, तो यह <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> भरा होता है। ड्रम की क्षमता क्या है ?</p>",
                    options_en: ["<p>28 litres</p>", "<p>36 litres</p>", 
                                "<p>27 litres</p>", "<p>20 litres</p>"],
                    options_hi: ["<p>28 लीटर</p>", "<p>36 लीटर</p>",
                                "<p>27 लीटर</p>", "<p>20 लीटर</p>"],
                    solution_en: "<p>7.(b) <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>This is equal to 9 litre of water which is drawn.<br>Capacity of drum = 9<math display=\"inline\"><mo>&#215;</mo></math>4 = 36 litres.</p>",
                    solution_hi: "<p>7.(b) <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math> <br>यह निकला गया 9 लीटर पानी के बराबर है।<br>ड्रम की क्षमता = 9<math display=\"inline\"><mo>&#215;</mo></math>4 = 36 लीटर।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. The Theory of Relativity is associated with</p>",
                    question_hi: "<p>8. सापेक्षता का सिद्धांत संबंधित है ?</p>",
                    options_en: ["<p>Newton</p>", "<p>Kelvin</p>", 
                                "<p>WC Roentgen</p>", "<p>Albert Einstein</p>"],
                    options_hi: ["<p>न्यूटन</p>", "<p>केल्विन</p>",
                                "<p>डब्ल्यूसी रोंटजेन</p>", "<p>अल्बर्ट आइंस्टीन</p>"],
                    solution_en: "<p>8.(d) <strong>Albert Einstein. Theory of Relativity -</strong> It describes how time, space, and gravity interact.<strong> Newton -</strong> Explained the laws of universal gravitation and motion. <strong>Kelvin </strong>- It is the unit of measurement of temperature. The Kelvin scale is named after the British physicist Baron Kelvin. <strong>Roentgen -</strong> He discovered X-rays in the year 1895.</p>",
                    solution_hi: "<p>8.(d) <strong>अल्बर्ट आइंस्टीन। सापेक्षता का सिद्धांत - </strong>यह बताता है कि समय, स्थान और गुरुत्वाकर्षण कैसे परस्पर कार्य करते हैं। <strong>न्यूटन - </strong>सार्वत्रिक गुरुत्वाकर्षण और गति के नियमों की व्याख्या। <strong>केल्विन - </strong>यह तापमान मापने की इकाई है। केल्विन स्केल का नाम ब्रिटिश भौतिक विज्ञानी बैरन केल्विन के नाम पर रखा गया है। <strong>रॉन्टगन- </strong>इन्होंने वर्ष 1895 में एक्स-रे की खोज की।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. Which famous book was written by &lsquo;Haripala&rsquo; ?</p>",
                    question_hi: "<p>9. कौन-सी प्रसिद्ध पुस्तक, \'हरिपाल\' द्वारा लिखी गई थी ?</p>",
                    options_en: ["<p>Gita Govinda</p>", "<p>Sangeet Darpan</p>", 
                                "<p>Sangeet Ratnakara</p>", "<p>Sangeeta Sudhakara</p>"],
                    options_hi: ["<p>गीत गोविंदा (Gita Govinda)</p>", "<p>संगीत दर्पण (Sangeet Darpan)</p>",
                                "<p>संगीत रत्नाकर (Sangeet Ratnakara)</p>", "<p>संगीत सुधाकर (Sangeeta Sudhakara)</p>"],
                    solution_en: "<p>9.(d) <strong>Sangeeta Sudhakara. </strong>The term &lsquo;Carnatic&rsquo; and &lsquo;Hindustani&rsquo; to classify musical styles first appeared in it. The Gita Govinda is a work composed by the 12th-century Hindu poet, Jayadeva. Sangeet Darpan is a musical treatise on Indian classical music written by Damodar Pandit. The Sangita-Ratnakara (Ocean of Music and Dance) is one of the most important musicological texts from India.</p>",
                    solution_hi: "<p>9.(d) <strong>संगीत सुधाकर।</strong> संगीत शैलियों को वर्गीकृत करने के लिए &lsquo;कर्नाटक&rsquo; और &lsquo;हिंदुस्तानी&rsquo; शब्द सबसे पहले इसमें दिखाई दिए। गीत गोविंद 12वीं सदी के हिंदू कवि जयदेव द्वारा रचित एक रचना है। संगीत दर्पण, दामोदर पंडित द्वारा लिखित भारतीय शास्त्रीय संगीत पर एक संगीत ग्रंथ है। संगीत-रत्नाकर (संगीत और नृत्य का सागर), भारत के सबसे महत्वपूर्ण संगीत संबंधी ग्रंथों में से एक है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. Which Article of the Indian Constitution deals with the provision of protection of monuments and places and objects of national importance ?</p>",
                    question_hi: "<p>10. भारतीय संविधान का कौन सा अनुच्छेद राष्ट्रीय महत्व के संस्मारकों, स्थानों और वस्तुओं के संरक्षण के प्रावधान से संबंधित है ?</p>",
                    options_en: ["<p>Article 49<strong id=\"docs-internal-guid-f95e162b-7fff-1460-e8e6-da9d62c8e0f4\"><br></strong></p>", "<p>Article 48</p>", 
                                "<p>Article 48A</p>", "<p>Article 43</p>"],
                    options_hi: ["<p>अनुच्छेद 49</p>", "<p>अनुच्छेद 48</p>",
                                "<p>अनुच्छेद 48 A</p>", "<p>अनुच्छेद 43</p>"],
                    solution_en: "<p>10.(a)<br><strong>Article 49 -</strong> Protection of monuments and places and objects of national importance. <strong>Article 48 - </strong>The organization of agriculture and animal husbandry. <strong>Article 48A -</strong> Protection and improvement of environment and safeguarding of forests and wildlife. <strong>Article 43 - </strong>Living wage, etc., for workers. <strong>Articles 36 to 51</strong> forms the Directive Principles of State Policy under Part IV of the Indian Constitution.</p>",
                    solution_hi: "<p>10.(a)<br><strong>अनुच्छेद 49 -</strong> राष्ट्रीय महत्व के स्मारकों और स्थानों और वस्तुओं का संरक्षण। <strong>अनुच्छेद 48 -</strong> कृषि और पशुपालन का संगठन। <strong>अनुच्छेद 48 A -</strong> पर्यावरण की सुरक्षा और सुधार तथा वनों और वन्यजीवों की सुरक्षा। <strong>अनुच्छेद 43 - </strong>श्रमिकों के लिए निर्वाह मजदूरी आदि। <strong>अनुच्छेद 36 से 51 </strong>भारतीय संविधान के भाग IV के तहत राज्य के नीति निर्देशक सिद्धांतों का निर्माण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. The difference between the fraction 5 minutes of an hour and 20 seconds of an hour is</p>",
                    question_hi: "<p>11. एक घंटे के 5 मिनट और एक घंटे के 20 सेकंड के अंश के बीच का अंतर कितना है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>7</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>270</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>7</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>270</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(a) 5 minutes of an hour = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>12</mn></mfrac></math><br>20 seconds of an hour = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3600</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>180</mn></mfrac></math><br>Difference = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>180</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>180</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mrow><mn>90</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>7</mn></mrow><mrow><mn>9</mn><mo>&#160;</mo></mrow></mfrac></math></p>",
                    solution_hi: "<p>10.(a) एक घंटे के 5 मिनट = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>12</mn></mfrac></math><br>एक घंटे के 20 सेकंड = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3600</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>180</mn></mfrac></math><br>एक घंटे के 20 सेकंड = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>180</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>180</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mrow><mn>90</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>7</mn></mrow><mrow><mn>9</mn><mo>&#160;</mo></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. Who is the only Indian woman who has been ranked world&rsquo;s no.1 for badminton ?</p>",
                    question_hi: "<p>12. वह एकमात्र भारतीय महिला कौन है जिसे बैडमिंटन में विश्व में नंबर 1 स्थान दिया गया है ?</p>",
                    options_en: ["<p>Saina Nehwal</p>", "<p>Rituparna Das</p>", 
                                "<p>Jwala Gutta</p>", "<p>P V Sindhu</p>"],
                    options_hi: ["<p>साइना नेहवाल</p>", "<p>ऋतुपर्णा दास</p>",
                                "<p>ज्वाला गुट्टा</p>", "<p>पी. वी. सिंधु</p>"],
                    solution_en: "<p>12.(a) <strong>Saina Nehwal. </strong>Other Indian Badminton Players: Kidambi Srikanth, Prakash Padukone, Sai Praneeth, Pullela Gopichand, Parupalli Kashyap, Ashwini Ponnappa, Satwiksairaj Rankireddy, Chirag Shetty. Major tournaments: BWF World Championships, Thomas Cup, Uber Cup, Sudirman Cup</p>",
                    solution_hi: "<p>12.(a) <strong>साइना नेहवाल। </strong>अन्य भारतीय बैडमिंटन खिलाड़ी: किदाम्बी श्रीकांत, प्रकाश पादुकोण, साई प्रणीत, पुलेला गोपीचंद, पारुपल्ली कश्यप, अश्विनी पोनप्पा, सात्विकसाईराज रंकीरेड्डी, चिराग शेट्टी। प्रमुख टूर्नामेंट: BWF विश्व चैंपियनशिप, थॉमस कप, उबेर कप, सुदीरमन कप।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. A sum of money becomes Rs. 10648 after 3 years and Rs. 9680 after 2 years of compound interest computed yearly. What is the rate of interest ?</p>",
                    question_hi: "<p>13. एक राशि वार्षिक चक्रवृद्धि ब्याज पर 3 वर्ष में 10648 रूपये तथा 2 वर्ष में 9680 रूपये हो जाती है। ब्याज दर क्या है ?</p>",
                    options_en: ["<p>12%</p>", "<p>8%</p>", 
                                "<p>9%</p>", "<p>10%</p>"],
                    options_hi: ["<p>12%</p>", "<p>8%</p>",
                                "<p>9%</p>", "<p>10%</p>"],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672479814.png\" alt=\"rId4\" width=\"335\" height=\"105\"><br>After 2 year, Principal for 3rd year = 9680 <br>Ratio of Principal to Amount = 9680 : 10648 = 10 : 11<br>Rate of interest = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac><mo>&#215;</mo></math>100 = 10%</p>",
                    solution_hi: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672480410.png\" alt=\"rId5\" width=\"324\" height=\"101\"><br>2 वर्ष बाद तीसरे वर्ष के लिए मूलधन = 9680<br>मूलधन से राशि का अनुपात = 9680 : 10648 = 10 : 11<br>ब्याज की दर= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac><mo>&#215;</mo></math>100 = 10%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. The Moplah Rebellion took place between the years</p>",
                    question_hi: "<p>14. मोपला विद्रोह किन वर्षों के बीच हुआ था ?</p>",
                    options_en: ["<p>1914 - 1915</p>", "<p>1923 - 1924</p>", 
                                "<p>1921 - 1919</p>", "<p>1921 - 1922</p>"],
                    options_hi: ["<p>1914 - 1915</p>", "<p>1923 - 1924</p>",
                                "<p>1921 - 1919</p>", "<p>1921 - 1922</p>"],
                    solution_en: "<p>14.(d) The Moplah Rebellion took place between the years of 1921 - 1922. The Malabar rebellion happened in the Malabar region of Kerala, India, against the British colonial rule in that region and it was also against the prevailing feudal system controlled by elite Hindus.</p>",
                    solution_hi: "<p>14.(d) मोपला विद्रोह 1921 - 1922 के वर्षों के बीच हुआ था। मालाबार विद्रोह भारत के केरल के मालाबार क्षेत्र में उस क्षेत्र में ब्रिटिश औपनिवेशिक शासन के खिलाफ हुआ था और यह कुलीन हिंदुओं द्वारा नियंत्रित प्रचलित सामंती व्यवस्था के खिलाफ भी था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. In an election, 2% persons enrolled in the voter list did not participate and 500 votes were invalid. Two candidates A and B fought the election, and A defeated B by 200 votes. If 43% of the persons enrolled in the voter list casted their votes in favour of A, then what is the number of the total casted votes ?</p>",
                    question_hi: "<p>15. एक चुनाव में मतदाता सूची में नामांकित 2% व्यक्तियों ने वोटिंग में भाग नहीं लिया और 500 वोट अवैध थे। दो उम्मीदवार A और B चुनाव लड़े और A ने B को 200 वोट से हरा दिया। यदि मतदाता सूची में नामांकित व्यक्तियों में से 43% ने A के पक्ष में अपना वोट डाला, तो डाले गए कुल वोटों की संख्या कितनी है ?</p>",
                    options_en: ["<p>2450</p>", "<p>2800</p>", 
                                "<p>3000</p>", "<p>3250</p>"],
                    options_hi: ["<p>2450</p>", "<p>2800</p>",
                                "<p>3000</p>", "<p>3250</p>"],
                    solution_en: "<p>15.(a)<br>Let total number of voters enrolled = 100x<br>Voters participated in elections = 98x<br>Valid votes = 98x&nbsp;- 500<br>Votes in favour of A = 43% of enrolled voters = 43x<br>Votes in favour of B = (98x&nbsp;- 500) - 43x<br>According to question,<br>43<math display=\"inline\"><mi>x</mi></math> - [(98x - 500) - 43x] = 200<br>43<math display=\"inline\"><mi>x</mi></math> - (98x - 500) + 43x = 200<br>86<math display=\"inline\"><mi>x</mi></math> - 98x + 500 = 200<br><math display=\"inline\"><mo>-</mo></math>12x = - 300 &rArr; x = 25<br>Total number of voters participated = 98<math display=\"inline\"><mi>x</mi></math> <br>= 98<math display=\"inline\"><mo>&#215;</mo></math>25 = 2450</p>",
                    solution_hi: "<p>15.(a)<br>मान लीजिए नामांकित मतदाताओं की कुल संख्या = 100x<br>मतदाताओं ने चुनाव में भाग लिया = 98x<br>वैध वोट = 98x&nbsp;- 500<br>A के पक्ष में वोट = नामांकित मतदाताओं का 43% = 43x<br>B के पक्ष में वोट = (98x&nbsp;- 500) - 43x<br>प्रश्न के अनुसार,<br>43<math display=\"inline\"><mi>x</mi></math> - [(98x - 500) - 43x] = 200<br>43<math display=\"inline\"><mi>x</mi></math> - (98x - 500) + 43x = 200<br>86<math display=\"inline\"><mi>x</mi></math> - 98x + 500 = 200<br><math display=\"inline\"><mo>-</mo></math>12x = - 300 &rArr; x = 25<br>भाग लेने वाले मतदाताओं की कुल संख्या = 98<math display=\"inline\"><mi>x</mi></math> <br>= 98<math display=\"inline\"><mo>&#215;</mo></math>25 = 2450</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. \'Chhau\' is a popular dance of which of the following state ?</p>",
                    question_hi: "<p>16. \'छऊ (Chhau)\' निम्नलिखित में से किस राज्य का एक लोकप्रिय नृत्य है ?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Haryana</p>", 
                                "<p>Jharkhand</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>हरियाणा</p>",
                                "<p>झारखंड</p>", "<p>केरल</p>"],
                    solution_en: "<p>16.(c) <strong>Jharkhand. </strong>Chhau is a semi-classical Indian dance with martial and folk traditions. It is found in three styles named after the location where they are performed, i.e., the Purulia Chhau of West Bengal, the Seraikella Chhau of Jharkhand and the Mayurbhanj Chhau of Odisha. The Cultural Ministry of India has included Chhau in the list of classical dances making a total of 9 classical dance forms.</p>",
                    solution_hi: "<p>16.(c) <strong>झारखंड। </strong>छऊ एक अर्ध-शास्त्रीय भारतीय नृत्य है जिसमें मार्शल और लोक परंपराएं हैं। यह तीन शैलियों में होता है, जिनका नाम उस स्थान के नाम पर रखा गया है जहाँ इसे प्रदर्शित किया जाता है, अर्थात पश्चिम बंगाल का पुरुलिया छऊ, झारखंड का सरायकेला छऊ और ओडिशा का मयूरभंज छऊ। भारत के सांस्कृतिक मंत्रालय ने छऊ को शास्त्रीय नृत्यों की सूची में शामिल किया है, जिससे कुल 9 शास्त्रीय नृत्य रूप बन गए हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. &lsquo;Garden&rsquo; is related to &lsquo;Gardener&rsquo; in the same way as &lsquo;Museum&rsquo; is related to &lsquo;______&rsquo;</p>",
                    question_hi: "<p>17. जिस प्रकार \'उद्यान\' का संबंध \'माली\' से है, उसी प्रकार \'संग्रहालय\' का संबंध \'____\' से है।</p>",
                    options_en: ["<p>Guide</p>", "<p>Curator</p>", 
                                "<p>Museology</p>", "<p>Artist</p>"],
                    options_hi: ["<p>मार्गदर्शक</p>", "<p>संग्रहाध्यक्ष</p>",
                                "<p>संग्रहालय विज्ञान</p>", "<p>कलाकार</p>"],
                    solution_en: "<p>17.(b) Garden is related to Gardener in the same way as Museum is related to Curator.<br>It means like the gardener who tends and cultivates a garden, a curator is a keeper or custodian of a museum.</p>",
                    solution_hi: "<p>17.(b) जिस प्रकार \'उद्यान\' का संबंध \'माली\' से है उसी प्रकार \'संग्रहालय\' का संबंध \'संग्रहाध्यक्ष\' से है।<br>इसका अर्थ है कि जैसे माली जो बगीचे की देखभाल और खेती करता है, संग्रहाध्यक्ष संग्रहालय का रखवाला या संरक्षक होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. Evaluate the following. <br>sin 25&deg; sin 65&deg; -&nbsp;cos 25&deg; cos 65&deg;.</p>",
                    question_hi: "<p>18. निम्नलिखित का मान निकालिए। <br>sin 25&deg; sin 65&deg; -&nbsp;cos 25&deg; cos 65&deg;.</p>",
                    options_en: ["<p>40</p>", "<p>4</p>", 
                                "<p>0</p>", "<p>1</p>"],
                    options_hi: ["<p>40</p>", "<p>4</p>",
                                "<p>0</p>", "<p>1</p>"],
                    solution_en: "<p>18.(c)<br>Sin 25&deg; Sin 65&deg; -&nbsp;Cos 25&deg; Cos 65&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math>Sin 25&deg; Sin 65&deg; - Cos (90&deg;-65&deg;) Cos(90&deg;- 25&deg;)<br><math display=\"inline\"><mo>&#8658;</mo></math>Sin 25&deg; Sin 65&deg; - Sin65&deg; Sin 25&deg; &rArr; 0</p>",
                    solution_hi: "<p>18.(c)<br>Sin 25&deg; Sin 65&deg; -&nbsp;Cos 25&deg; Cos 65&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math>Sin 25&deg; Sin 65&deg; - Cos (90&deg;-65&deg;) Cos(90&deg;- 25&deg;)<br><math display=\"inline\"><mo>&#8658;</mo></math>Sin 25&deg; Sin 65&deg; - Sin65&deg; Sin 25&deg; &rArr; 0</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. Select the option that is closest to the given shapes ?<br>Square, Rhombus, Rectangle, Parallelogram</p>",
                    question_hi: "<p>19. उस विकल्प का चयन करें जो दी गई आकृतियों के सबसे निकट है ?<br>वर्ग, समचतुर्भुज, आयत, समांतर चतुर्भुज</p>",
                    options_en: ["<p>Equilateral</p>", "<p>Scalene</p>", 
                                "<p>Quadrilateral</p>", "<p>Equiangular</p>"],
                    options_hi: ["<p>समबाहु</p>", "<p>विषमबाहु</p>",
                                "<p>चतुर्भुज</p>", "<p>समानकोणिक</p>"],
                    solution_en: "<p>19.(c) Square, Rhombus, Rectangle, Parallelogram all are Quadrilaterals.</p>",
                    solution_hi: "<p>19.(c) वर्ग, समचतुर्भुज, आयत, समांतर चतुर्भुज सभी चतुर्भुज हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. In the acronym SARAS mela, which are organized frequently, what does R stand for ?</p>",
                    question_hi: "<p>20. SARAS मेला में, जो अक्सर आयोजित होते हैं, R का क्या अर्थ है ?<strong id=\"docs-internal-guid-1e3f85ac-7fff-4f12-953d-23a7790cfc67\"><br></strong></p>",
                    options_en: ["<p>Remote</p>", "<p>Rural</p>", 
                                "<p>Regressive</p>", "<p>Right</p>"],
                    options_hi: ["<p>Remote</p>", "<p>Rural</p>",
                                "<p>Regressive</p>", "<p>Right</p>"],
                    solution_en: "<p>20.(b) Rural</p>\n<p dir=\"ltr\">SARAS stands for Sale of Articles of Rural Artisans Society, aimed at promoting rural artisans across India. The recent SARAS Mela 2023, launched by Tripura Chief Minister Dr. Manik Saha, featured the theme &lsquo;Didi Lakhpati Tripura Agragati&rsquo; (Progressive Tripura through Women Entrepreneurs).</p>",
                    solution_hi: "<p>20.(b) Rural</p>\n<p>SARAS का अर्थ है \"सेल ऑफ आर्टिकल्स ऑफ रूरल आर्टिज़ंस सोसाइटी\", जिसका उद्देश्य भारत भर में ग्रामीण कारीगरों को बढ़ावा देना है। हाल ही में, त्रिपुरा के मुख्यमंत्री डॉ. माणिक साहा द्वारा लॉन्च किए गए <strong>SARAS Mela 2023 </strong>का थीम &lsquo;दिदी लक्ष्मीपति त्रिपुरा अग्रगति&rsquo; (महिला उद्यमियों के माध्यम से प्रगतिशील त्रिपुरा) था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. Two angles of a triangle are in the ratio 1 : 2 and the sum of these angles is equal to the third angle. What is the measure of the smallest angle ?</p>",
                    question_hi: "<p>21. एक त्रिभुज के दो कोणों का अनुपात 1 : 2 है और इन कोणों का योग तीसरे कोण के बराबर है। सबसे छोटे कोण का माप क्या है?</p>",
                    options_en: ["<p>30&deg;</p>", "<p>40&deg;</p>", 
                                "<p>25&deg;</p>", "<p>20&deg;</p>"],
                    options_hi: ["<p>30&deg;</p>", "<p>40&deg;</p>",
                                "<p>25&deg;</p>", "<p>20&deg;</p>"],
                    solution_en: "<p>21.(a) Let the two angles be x and 2x.<br>ATQ, x + 2x = 3x (third angle)<br>Sum of angles of a triangle = 180<math display=\"inline\"><mo>&#176;</mo></math><br>x + 2x + 3x = 180<math display=\"inline\"><mo>&#176;</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math>6x = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math>x = 30&deg; (smallest angle)</p>",
                    solution_hi: "<p>21.(a) माना कि दोनों कोण x और 2x हैं।<br>प्रश्न के अनुसार, x + 2x = 3x (तीसरा कोण) <br>त्रिभुज के कोणों का योग = 180&deg;<br>x + 2x + 3x = 180<math display=\"inline\"><mo>&#176;</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math>6x = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math>x = 30&deg; (सबसे छोटा कोण)</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. Which of the following Awards is associated with only Music ?</p>",
                    question_hi: "<p>22. निम्नलिखित में से कौन सा पुरस्कार केवल संगीत से संबंधित है ?</p>",
                    options_en: ["<p>Grammy</p>", "<p>Tagore</p>", 
                                "<p>Oscar</p>", "<p>Cannes</p>"],
                    options_hi: ["<p>ग्रैमी</p>", "<p>टैगोर</p>",
                                "<p>ऑस्कर</p>", "<p>कान्न</p>"],
                    solution_en: "<p>22.(a) The Grammy Award is associated with only Music. It is presented by the Recording Academy to recognize achievement in the music industry where the trophy depicts a gilded gramophone.</p>",
                    solution_hi: "<p>22.(a) ग्रैमी अवार्ड केवल संगीत से जुड़ा है। यह रिकॉर्डिंग अकादमी द्वारा संगीत उद्योग में उपलब्धि को पहचानने के लिए प्रस्तुत किया जाता है जहां ट्रॉफी में एक सोने का पानी चढ़ा हुआ ग्रामोफोन दिया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. In the following number-pairs, the second number is obtained by applying certain mathematical operations to the first number. In three of the four pairs, the same pattern is applied and hence they form a group. Select the number-pair that does NOT belong to this group.</p>",
                    question_hi: "<p>23. दिए गए संख्या-युग्मों में से प्रत्येक में, दूसरी संख्या, पहली संख्या पर निश्चित गणितीय संक्रियाएं करके प्राप्त की जाती है। दिए गए चार संख्या युग्मों में से तीन एक समान पैटर्न का पालन करते हैं, और इस प्रकार एक समूह बनाते हैं। उस संख्या -युग्म का चयन करें, जो उस समूह से संबंधित नहीं है।</p>",
                    options_en: ["<p>18, 52</p>", "<p>24,70</p>", 
                                "<p>43,86</p>", "<p>39,115</p>"],
                    options_hi: ["<p>18, 52</p>", "<p>24,70</p>",
                                "<p>43,86</p>", "<p>39,115</p>"],
                    solution_en: "<p>23.(c) <br>In (18, 52), &rArr; 18 &times; 3 - 2 = 52<br>In (24, 70), &rArr; 24 &times; 3 - 2 = 70<br>In (43, 86), &rArr; 43 &times; 3 - 2<math display=\"inline\"><mo>&#8800;</mo></math>86<br>In (39, 115), &rArr; 39 &times; 3 - 2 = 115<br>We can clearly see in the above expression that (43, 86) is an odd one.</p>",
                    solution_hi: "<p>23.(c) <br>(18, 52) में, &rArr; 18 &times; 3 - 2 = 52<br>(24, 70) में, &rArr; 24 &times; 3 - 2 = 70<br>(43, 86) में, &rArr; 43 &times; 3 - 2<math display=\"inline\"><mo>&#8800;</mo></math>86<br>(39, 115) में, &rArr; 39 &times; 3 - 2 = 115<br>उपरोक्त व्यंजक में हम स्पष्ट रूप से देख सकते हैं कि (43, 86) विषम है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. How many sex Chromosomes are there in a normal human being ?</p>",
                    question_hi: "<p>24. एक सामान्य मनुष्य में कितने लिंग गुणसूत्र होते हैं ?</p>",
                    options_en: ["<p>Two</p>", "<p>Eight</p>", 
                                "<p>One</p>", "<p>Four</p>"],
                    options_hi: ["<p>दो</p>", "<p>आठ</p>",
                                "<p>एक</p>", "<p>चार</p>"],
                    solution_en: "<p>24.(a) <br>Humans have 46 chromosomes i.e. 23 pairs of chromosomes. 22 of these pairs are called autosomes and look the same in both males and females. The 23rd pair called sex chromosomes differ in males and females. Females have two copies of the X chromosome (XX), while males have one X and one Y chromosome (XY).</p>",
                    solution_hi: "<p>24.(a)<br>मनुष्य में 46 गुणसूत्र अर्थात 23 जोड़े गुणसूत्र होते हैं। इनमें से 22 जोड़ियों को समजात गुणसूत्र (ऑटोसोम्स ) कहा जाता है और ये नर और मादा दोनों में एक जैसे दिखते हैं। 23वां जोड़ा जिसे सेक्स क्रोमोसोम ( गुणसूत्र) कहा जाता है, पुरुषों और महिलाओं में भिन्न होता है। महिलाओं में X गुणसूत्र (XX) की दो प्रतियां होती हैं, जबकि पुरुषों में एक X और एक Y गुणसूत्र (XY) होता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. Which organization recently launched &ldquo;Vulture Count 2024&rdquo; initiative ?</p>",
                    question_hi: "<p>25. हाल ही में &ldquo;Vulture Count 2024&rdquo; पहल किस संगठन ने शुरू की ?</p>",
                    options_en: ["<p>United Nations Environment Programme</p>", "<p>Global Footprint Network</p>", 
                                "<p>World Wide Fund for Nature-India</p>", "<p>Greenpeace</p>"],
                    options_hi: ["<p>संयुक्त राष्ट्र पर्यावरण कार्यक्रम</p>", "<p>ग्लोबल फुटप्रिंट नेटवर्क</p>",
                                "<p>वर्ल्ड वाइड फंड फॉर नेचर-इंडिया</p>", "<p>ग्रीनपीस</p>"],
                    solution_en: "<p>25.(c) World Wide Fund for Nature-India<br>WWF-India launched the &ldquo;Vulture Count 2024&rdquo; project from September 7 to October 6, 2024. This initiative aligns with International Vulture Awareness Day. The project aims to count and assess the vulture population in India. It focuses particularly on critically endangered vulture species.</p>",
                    solution_hi: "<p>25.(c) वर्ल्ड वाइड फंड फॉर नेचर-इंडिया<br>WWF-इंडिया ने &ldquo;Vulture Count 2024&rdquo; परियोजना 7 सितंबर से 6 अक्टूबर 2024 तक शुरू की। यह पहल अंतर्राष्ट्रीय गिद्ध जागरूकता दिवस के साथ मेल खाती है और भारत में गिद्ध जनसंख्या की गणना और मूल्यांकन करने का लक्ष्य रखती है, विशेष रूप से संकटग्रस्त गिद्ध प्रजातियों पर ध्यान केंद्रित करते हुए।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. Study the given pie chart and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672480720.png\" alt=\"rId6\" width=\"290\" height=\"209\"> <br>The population of village D in 2020 was 10,500. What was the population of village A in 2020? Total population of these six villages is 100%.</p>",
                    question_hi: "<p>26. दिए गए पाई पार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672480874.png\" alt=\"rId7\" width=\"314\" height=\"217\"> <br>2020 में गाँव की जनसंख्या 10,500 थी। 2020 में गाँव A की जनसंख्या कितनी थी ? इन छह गांवों की कुल जनसंख्या 100% है।</p>",
                    options_en: ["<p>15,570</p>", "<p>15,750</p>", 
                                "<p>17,550</p>", "<p>7,875</p>"],
                    options_hi: ["<p>15,570</p>", "<p>15,750</p>",
                                "<p>17,550</p>", "<p>7,875</p>"],
                    solution_en: "<p>26.(b)<br>According to question,<br>8% = 10500<br>population of village A =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>10500</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 15750</p>",
                    solution_hi: "<p>26.(b)<br>प्रश्न के अनुसार,<br>8% = 10500<br>गाँव A की जनसंख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10500</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>8</mn></mfrac></math> = 15750</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. In this question, a set of numbers/symbols is coded using the letters as per the table given below followed by the conditions. The correct combination of codes according to the conditions will be your answer.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672481151.png\" alt=\"rId8\" width=\"327\" height=\"80\"> <br><strong>Terms:</strong><br>1. If the first and last digit are odd digits, both will be coded as L.<br>2.If the first and the last digit are even digits, both will be coded as Z. <br>What will be the code for 564783 ?</p>",
                    question_hi: "<p>27. इस प्रश्न में, अंकों / प्रतीकों के एक समूह को नीचे दी गई तालिका और उसके बाद दी गई शर्तों के अनुसार अक्षरों का उपयोग करके कूटबद्ध किया गया है। शर्तों के अनुसार कूटों का सही संयोजन आपका उत्तर होगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672481329.png\" alt=\"rId9\" width=\"353\" height=\"62\"> <br><strong>शर्तें:</strong><br>1. यदि पहले और अंतिम अंक विषम अंक हैं, तो दोनों को L के रूप में कूटबद्ध किया जाएगा। <br>2. यदि पहले और अंतिम अंक सम अंक हैं, तो दोनों को Z के रूप में कूटबद्ध किया जाएगा। <br>564783 का कूट क्या होगा ?</p>",
                    options_en: ["<p>SLHJDK</p>", "<p>LLHJDL</p>", 
                                "<p>LLHJKL</p>", "<p>SLHJDL</p>"],
                    options_hi: ["<p>SLHJDK</p>", "<p>LLHJDL</p>",
                                "<p>LLHJKL</p>", "<p>SLHJDL</p>"],
                    solution_en: "<p>27.(b)<br>564783<math display=\"inline\"><mo>&#8594;</mo></math><strong>S</strong>LHJD<strong>K</strong><br>As per given two conditions, Only condition <strong>(i) </strong>follows<br>After changing codes of First and last element with Code <strong>L</strong> we get,<br><strong>LLHJDL</strong></p>",
                    solution_hi: "<p>27.(b)<br>564783<math display=\"inline\"><mo>&#8594;</mo></math><strong>S</strong>LHJD<strong>K</strong><br>दी गई दो शर्तों के अनुसार, केवल शर्त (i) अनुसरण करती है,<br>कोड <strong>L</strong> के साथ पहले और आखिरी अक्षर के कोड बदलने के बाद, हम प्राप्त करते हैं,<br><strong>LLHJDL</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.<br>M N L_M N L L_ _L L M_L L_N L L_N</p>",
                    question_hi: "<p>28. अक्षरों के उस संयोजन का चयन करें जो दी गई श्रृंखला के रिक्त स्थान में क्रम से रखने पर श्रृंखला को पूरा करेगा।<br>M N L_M N L L_ _L L M_L L_N L L_N</p>",
                    options_en: ["<p>N M N M M L</p>", "<p>L N M N M M</p>", 
                                "<p>L M N N M M</p>", "<p>L N N M M M</p>"],
                    options_hi: ["<p>N M N M M L</p>", "<p>L N M N M M</p>",
                                "<p>L M N N M M</p>", "<p>L N N M M M</p>"],
                    solution_en: "<p>28.(c) <br>MNL<strong>L</strong> / MNLL / <strong>MN</strong>LL/ M<strong>N</strong>LL / <strong>M</strong>NLL /MN</p>",
                    solution_hi: "<p>228.(c) <br>MNL<strong>L</strong> / MNLL / <strong>MN</strong>LL/ M<strong>N</strong>LL / <strong>M</strong>NLL /MN</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. In an examination, 41% of students failed in Economics, 35% of students failed in Geography and 39% of students failed in History, 5% of the students failed in all the three subjects, 14% of students failed in Economics and Geography. 21% of the students failed in Geography and History and 18% of students failed in History and Economics. Find the percentage of students who failed in only Economics.</p>",
                    question_hi: "<p>29. एक परीक्षा में, 41% छात्र अर्थशास्त्र में अनुत्तीर्ण हुए, 35% छात्र भूगोल में अनुत्तीर्ण हुए और 39% छात्र इतिहास में अनुत्तीर्ण हुए, 5% छात्र तीनों विषयों में अनुत्तीर्ण हुए, 14% छात्र अर्थशास्त्र और भूगोल में अनुत्तीर्ण हुए। 21% छात्र भूगोल और इतिहास में और 18% छात्र इतिहास और अर्थशास्त्र में अनुत्तीर्ण हुए। केवल अर्थशास्त्र में अनुत्तीर्ण होने वाले छात्रों का प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>10%</p>", "<p>14%</p>", 
                                "<p>16%</p>", "<p>12%</p>"],
                    options_hi: ["<p>10%</p>", "<p>14%</p>",
                                "<p>16%</p>", "<p>12%</p>"],
                    solution_en: "<p>29.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672481792.png\" alt=\"rId10\" width=\"213\" height=\"95\"><br>Students who failed in only economics = 41 - (18 + 14 - 5) = 14%</p>",
                    solution_hi: "<p>29.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672482021.png\" alt=\"rId11\" width=\"185\" height=\"100\"><br>सिर्फ अर्थशास्त्र में फेल हुए छात्र = 41 - (18 + 14 - 5) = 14%</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. The famous Gol Gumbaz is located in:</p>",
                    question_hi: "<p>30. प्रसिद्ध गोल गुम्बद कहाँ स्थित है ?</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Rajasthan</p>", 
                                "<p>Gujarat</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>राजस्थान</p>",
                                "<p>गुजरात</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>30.(a) <strong>Karnataka </strong>(Vijayapura District). <strong>Gol Gumbaz -</strong> It is the tomb of Mohammed Adil Shah (ruled 1627&ndash;1657). It is the second largest dome ever built, next in size only to St Peter&rsquo;s Basilica in Rome. Its complex includes a mosque, a Naqqar Khana (a hall for the trumpeters) and the ruins of guest houses. <strong>Other Monuments in Karnataka - </strong>Mysore Palace, Hampi, Badami Cave Temples, Lad Khan Temple, Gomateshwara Statue etc.</p>",
                    solution_hi: "<p>30.(a) <strong>कर्नाटक (विजयपुरा जिला)। गोल गुम्बज -</strong> यह मोहम्मद आदिल शाह (शासनकाल 1627-1657) का मकबरा है। यह अब तक का बनाया गया दूसरा सबसे बड़ा गुंबद है जो आकार में रोम में सेंट पीटर बेसिलिका के बाद दूसरा है। इसके परिसर में एक मस्जिद, एक नक्कार खाना (तुरही बजानेवालों के लिए एक हॉल) और अतिथि गृहों के खंडहर शामिल हैं। <strong>कर्नाटक में अन्य स्मारक -</strong> मैसूर पैलेस, हम्पी, बादामी गुफा मंदिर, लाड खान मंदिर, गोमतेश्वर प्रतिमा आदि।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. What is the value of 64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup>, when x = 3 and y = - 4 ?</p>",
                    question_hi: "<p>31. x = 3 और y = - 4 होने पर 64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>1236</p>", "<p>488</p>", 
                                "<p>536</p>", "<p>1256</p>"],
                    options_hi: ["<p>1236</p>", "<p>488</p>",
                                "<p>536</p>", "<p>1256</p>"],
                    solution_en: "<p>31.(d)<br>(4x + y)<sup>3</sup> = 64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup><br>According to question,<br>64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3 </sup>&nbsp;= (4x + y)<sup>3</sup> - 10x<sup>2</sup>y + 8xy<sup>2</sup><br>= (4 &times; 3 - 4)<sup>3</sup> - 10 &times; 3<sup>2 </sup>&times; (-4) + 8 &times; 3 &times; 16<br>= (8)<sup>3</sup>&nbsp;+ 360 + 384<br>= 512 + 744 = 1256</p>",
                    solution_hi: "<p>31.(d)<br>(4x + y)<sup>3</sup> = 64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup><br>प्रश्न के अनुसार ,<br>64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3 </sup>&nbsp;= (4x + y)<sup>3</sup> - 10x<sup>2</sup>y + 8xy<sup>2</sup><br>= (4 &times; 3 - 4)<sup>3</sup>&nbsp;- 10 &times; 3<sup>2 </sup>&times; (-4) + 8 &times; 3 &times; 16<br>= (8)<sup>3</sup>&nbsp;+ 360 + 384<br>= 512 + 744 = 1256</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. The Ministry of Electronics &amp; Information Technology has recently launched the &lsquo;Graphene-Aurora program&rsquo; in which state ?</p>",
                    question_hi: "<p>32. इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी मंत्रालय ने हाल ही में \'ग्रेफीन-ऑरोरा कार्यक्रम\' किस राज्य में शुरू किया ?</p>",
                    options_en: ["<p>Andhra Pradesh</p>", "<p>Kerala</p>", 
                                "<p>Tamil Nadu</p>", "<p>Assam</p>"],
                    options_hi: ["<p>आंध्र प्रदेश</p>", "<p>केरल</p>",
                                "<p>तमिल नाडु</p>", "<p>असम</p>"],
                    solution_en: "<p>32.(b) <br>The Ministry of Electronics and Information Technology (MeitY) launched the India Graphene Engineering and Innovation Centre (I-GEIC) as part of the Graphene Aurora program (GAP) in Kerala. The GAP is managed by Digital University Kerala.<br>The IGEIC is a non-profit organization based in Trivandrum, Kerala. Its goal is to become a leading center for the commercialization of graphene technology.</p>",
                    solution_hi: "<p>32.(b) <br>इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी मंत्रालय (MeitY) ने ग्रेफीन ऑरोरा कार्यक्रम (GAP) के तहत भारत ग्रेफीन इंजीनियरिंग और नवाचार केंद्र (I-GEIC) की स्थापना केरल में की है। GAP का प्रबंधन डिजिटल यूनिवर्सिटी केरल द्वारा किया जाता है। IGEIC त्रिवेन्द्रम, केरल में स्थित एक गैर-लाभकारी संगठन है। इसका लक्ष्य ग्रेफीन तकनीक के वाणिज्यीकरण के लिए एक प्रमुख केंद्र बनना है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Select the number from among the given options that can replace the question mark(?) in the following series ?&nbsp;<br>8, 18, 32, 50, ?</p>",
                    question_hi: "<p>33. दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है ?<br>8, 18, 32, 50, ?</p>",
                    options_en: ["<p>72</p>", "<p>70</p>", 
                                "<p>62</p>", "<p>68</p>"],
                    options_hi: ["<p>72</p>", "<p>70</p>",
                                "<p>62</p>", "<p>68</p>"],
                    solution_en: "<p>33.(a) 8, 18, 32, 50, ?<br>Here, the difference between the consecutive numbers are in AP<br>10, 14, 18, 22, &hellip; <br>So, the fifth term = (50 + 22) = 72</p>",
                    solution_hi: "<p>33.(a) 8, 18, 32, 50, ?<br>यहाँ उपरोक्त श्रंखला में क्रमागत संख्याओं का अंतर समान्तर श्रेणी में है।<br>10, 14, 18, 22, &hellip; <br>अत: पाँचवाँ पद = (50 + 22) = 72</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. Which of the following is NOT a poverty alleviation programme in India ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन-सा भारत में एक गरीबी उन्मूलन कार्यक्रम नहीं है ?</p>",
                    options_en: ["<p>Rashtriya Ucchatar Shiksha Abhiyan</p>", "<p>Swarna Jayanti Shahari Rozgar Yojana</p>", 
                                "<p>Prime Minister Rozgar Yojana</p>", "<p>Integrated Rural Development Program</p>"],
                    options_hi: ["<p>राष्ट्रीय उच्चतर शिक्षा अभियान</p>", "<p>स्वर्ण जयंती शहरी रोजगार योजना</p>",
                                "<p>प्रधान मंत्री रोजगार योजना</p>", "<p>एकीकृत ग्रामीण विकास कार्यक्रम</p>"],
                    solution_en: "<p>34.(a) <strong>Rashtriya Ucchatar Shiksha Abhiyan. </strong>It is a holistic scheme of development for higher education in India initiated in 2013 by the Ministry of Education. Swarna Jayanti Shahari Rozgar Yojana in India is a Centrally Sponsored Scheme which came into effect on 1 December 1997. The Prime Minister Rozgar Yojna (PMRY) scheme is to provide assistance to the educated unemployed persons.</p>",
                    solution_hi: "<p>34.(a) <strong>राष्ट्रीय उच्चतर शिक्षा अभियान। </strong>यह भारत में उच्च शिक्षा के विकास के लिए एक समग्र योजना है जिसे शिक्षा मंत्रालय द्वारा 2013 में शुरू किया गया था। भारत में स्वर्ण जयंती शहरी रोजगार योजना एक केंद्र प्रायोजित योजना है जो 1 दिसंबर 1997 को लागू हुई थी। प्रधानमंत्री रोजगार योजना (PMRY) शिक्षित बेरोजगार व्यक्तियों को सहायता प्रदान करने के लिए है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. On which day is Gudi Padwa celebrated in the month of Chaitra as per Hindu calendar ?</p>",
                    question_hi: "<p>35. हिंदू पंचांग के अनुसार गुड़ी पड़वा, चैत्र मास की किस तिथि को मनाया जाता है ?</p>",
                    options_en: ["<p>Second</p>", "<p>First</p>", 
                                "<p>Fifth</p>", "<p>Fourth</p>"],
                    options_hi: ["<p>द्वितीय</p>", "<p>प्रथम</p>",
                                "<p>पंचम</p>", "<p>चतुर्थ</p>"],
                    solution_en: "<p>35.(b)<br>Gudi Padwa is celebrated on the first day of the month of Chaitra as per the Hindu lunisolar calendar, which usually falls between March and April as per the Gregorian calendar. Gudhi Padwa is a spring-time festival that marks the traditional new year for Marathi and Konkani Hindus, but is also celebrated by other Hindus as well.</p>",
                    solution_hi: "<p>35.(b)<br>गुड़ी पड़वा हिंदू लूनिसोलर कैलेंडर के अनुसार चैत्र महीने के प्रथम दिन मनाया जाता है, जो आमतौर पर ग्रेगोरियन कैलेंडर के अनुसार मार्च और अप्रैल के बीच आता है। गुड़ी पड़वा एक वसंत-समय का त्योहार है जो मराठी और कोंकणी हिंदुओं के लिए पारंपरिक नए वर्ष का प्रतीक है, लेकिन अन्य हिंदुओं द्वारा भी मनाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. Read the given statements and conclusions carefully. Assuming that the information given in the statement is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>All stools are round<br>Some mountains are round<br>Some ponds are mountains<br>All ponds are square<br><strong>Conclusions:</strong><br>I. Some ponds are round<br>II. Some mountains are square<br>III. Some ponds are both mountains and round<br>IV. Some stools are square</p>",
                    question_hi: "<p>36. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। कथनों में दी गई जानकारी को सत्य मानते हुए विचार करें, भले ही वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो, और तय करें कि दिए गए निष्कर्षों में से कौन से कथनों का तार्किक रूप से पालन करते हैं ?<br><strong>कथन:</strong><br>सभी स्टूल गोल हैं।<br>कुछ पर्वत गोल हैं।<br>कुछ तालाब पर्वत हैं।<br>सभी तालाब वर्गाकार हैं।<br><strong>निष्कर्ष:</strong><br>I. कुछ तालाब गोल हैं।<br>II. कुछ पर्वत वर्गाकार हैं।<br>III. कुछ तालाब पर्वत और गोल दोनों हैं।<br>IV. कुछ स्टूल वर्गाकार हैं।</p>",
                    options_en: ["<p>Both conclusions I and II follow</p>", "<p>Only conclusion II follows</p>", 
                                "<p>Both conclusions I and III follow</p>", "<p>Both Conclusions III and IV follow</p>"],
                    options_hi: ["<p>निष्कर्ष I और II दोनों पालन करते हैं।</p>", "<p>केवल निष्कर्ष ॥ पालन करता है।</p>",
                                "<p>निष्कर्ष I और III दोनों पालन करते हैं।</p>", "<p>निष्कर्ष III और IV दोनों पालन करते हैं।</p>"],
                    solution_en: "<p>36.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672482277.png\" alt=\"rId12\" width=\"268\" height=\"104\"><br>So, only conclusion II follows.</p>",
                    solution_hi: "<p>36.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672483193.png\" alt=\"rId13\" width=\"317\" height=\"120\"><br>अत: केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. A shopkeeper uses 940 gm weight in place of one kg weight. He sells it at 4% profit. What will be the actual profit percentage? (rounded off to two decimal places)</p>",
                    question_hi: "<p>37. एक दुकानदार एक kg बाट के स्थान पर 940 gm बाट का उपयोग करता है। इस वजन का उपयोग करके वह माल 4% लाभ पर बेचता है। वास्तविक लाभ प्रतिशत क्या होगा ? (दो दशमलव स्थान तक पूर्णाकित)</p>",
                    options_en: ["<p>9.25%</p>", "<p>10.32%</p>", 
                                "<p>10.64%</p>", "<p>10.96%</p>"],
                    options_hi: ["<p>9.25%</p>", "<p>10.32%</p>",
                                "<p>10.64%</p>", "<p>10.96%</p>"],
                    solution_en: "<p>37.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;C.P.&nbsp; :&nbsp; S.P.<br>Profit&nbsp; &nbsp; =&nbsp; 25&nbsp; &nbsp;:&nbsp; &nbsp;26<br>Weight = 940&nbsp; &nbsp;:&nbsp; 1000<br>------------------------------<br>Total = 47 : 52<br>Required profit = <math display=\"inline\"><mfrac><mrow><mn>52</mn><mo>-</mo><mn>47</mn></mrow><mrow><mn>47</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 10.64 %</p>",
                    solution_hi: "<p>37.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;क्रय&nbsp; &nbsp; :&nbsp; &nbsp;विक्रय मूल्य <br>लाभ&nbsp; &nbsp;=&nbsp; &nbsp;25&nbsp; &nbsp; :&nbsp; &nbsp; 26<br>वजन&nbsp; =&nbsp; 940&nbsp; &nbsp;:&nbsp; &nbsp;1000<br>-----------------------------------<br>कुल = 47 : 52<br>आवश्यक लाभ = <math display=\"inline\"><mfrac><mrow><mn>52</mn><mo>-</mo><mn>47</mn></mrow><mrow><mn>47</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 10.64 %</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. Given below is a statement followed by two possible underlying assumptions I and II. Read the information carefully and select the correct option.<br><strong>Statement:</strong><br>As per reports by International recruiters, around 25% of the candidates are rejected just on the basis of insufficient data in their CVs or due to the format of the CVs auto-created by some websites. <br>Which of the following can be assumed from the given statement ?<br>I. CVs manually created by job seekers have more chances of being viewed / selected than auto created CVs. <br>II. No criteria other than insufficient information and formatting results in rejection of CVs.</p>",
                    question_hi: "<p>38. नीचे एक कथन दिया गया है और उसकी दो संभावित अंतर्निहित धारणाएँ I और II दी गई हैं। जानकारी को ध्यान से पढ़ें और सही विकल्प का चयन करें।<br><strong>कथन:</strong><br>अंतर्राष्ट्रीय भर्तीकर्ताओं की रिपोर्ट के अनुसार, लगभग 25% उम्मीदवारों को उनके CV में अपर्याप्त डेटा के आधार पर या कुछ वेबसाइटों द्वारा स्वत: निर्मित किए गए CV के फॉर्मेट के कारण खारिज कर दिया जाता है।<br>दिए गए कथन से निम्नलिखित में से कौन सा माना जा सकता है ?<br>I. नौकरी चाहने वालों द्वारा मैन्युअल रूप से बनाए गए सीवी में स्वत: निर्मित किये गए CV की तुलना में देखे जाने/चयन किए जाने की संभावना अधिक होती है।<br>II. CV की अस्वीकृति में अपर्याप्त जानकारी और फॉर्मेटिंग गड़बड़ी के अलावा कोई मानदंड नहीं है।</p>",
                    options_en: ["<p>Only II can be assumed</p>", "<p>Neither I nor II can be assumed</p>", 
                                "<p>Only I can be assumed</p>", "<p>Both I and II can be assumed</p>"],
                    options_hi: ["<p>केवल II माना जा सकता है</p>", "<p>न तो I और न ही II माना जा सकता है</p>",
                                "<p>केवल I माना जा सकता है</p>", "<p>I और II दोनों को माना जा सकता है</p>"],
                    solution_en: "<p>38.(c)<br><strong>According to the statement,</strong> the assumption (i) is appropriately explaining the given statement as stated in the statement that manually generated CVs are viewed/selected by job seekers as compared to automatically generated CVs more likely to be done.</p>",
                    solution_hi: "<p>38.(c) <strong>कथन के अनुसार </strong>, धारणा (i) दिए गए कथन को उचित रूप से स्पष्ट कर रही है जैसा कि कथन में कहा है कि नौकरी चाहने वालों द्वारा मैन्युअल रूप से बनाए गए सीवी में स्वत: निर्मित किये गए CV की तुलना में देखे जाने/चयन किए जाने की संभावना अधिक होती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. The area of triangle ABC is 39 cm<sup>2</sup>. D and E are two points on BC such that BD = DE = EC, then what is the area of triangle ADC ?</p>",
                    question_hi: "<p>39. त्रिभुज ABC का क्षेत्रफल 39 cm<sup>2</sup> है। BC पर D और E ऐसे दो बिंदु हैं इस प्रकार कि BD = DE = EC है, तो त्रिभुज ADC का क्षेत्रफल क्या है ?</p>",
                    options_en: ["<p>26 cm<sup>2</sup></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> cm<sup>2</sup></p>", 
                                "<p>13 cm<sup>2</sup></p>", "<p>52 cm<sup>2</sup></p>"],
                    options_hi: ["<p>26 cm<sup>2</sup></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> cm<sup>2</sup></p>",
                                "<p>13 cm<sup>2</sup></p>", "<p>52 cm<sup>2</sup></p>"],
                    solution_en: "<p>39.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672483560.png\" alt=\"rId14\" width=\"122\" height=\"118\"><br>BD = DE = EC and height for triangle ABC and ADC will be the same so the area of the triangle depends on its base.<br>DC = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> BC<br>Area of &Delta;ADC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; area of &Delta;ABC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; 39 = 26 cm<sup>2</sup></p>",
                    solution_hi: "<p>39.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672483560.png\" alt=\"rId14\" width=\"122\" height=\"118\"><br>BD = DE = EC और त्रिभुज ABC और ADC की ऊँचाई समान होगी, इसलिए त्रिभुज का क्षेत्रफल उसके आधार पर निर्भर करता है।<br>DC = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> BC<br>ADC का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; ABC का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; 39 = 26 cm<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. If the number 356yx is divisible by 90, then&nbsp;(y - x) is</p>",
                    question_hi: "<p>40. यदि संख्या 356yx, 90 से विभाज्य है, तो (y - x) का मान क्या है ?</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>40.(c) 356yx is divisible by 90, so it must be divisible by 9 and 10 both.<br>&there4; x = 0 <br>And the number should be divisible by 9, <br>3 + 5 + 6 + y + 0 = 14 + y<br><math display=\"inline\"><mo>&#8756;</mo></math> y = 4<br>y - x = 4 - 0 = 4</p>",
                    solution_hi: "<p>40.(c) 356yx 90 से विभाज्य है, इसलिए इसे 9 और 10 दोनों से विभाज्य होना चाहिए,<br>&there4; x = 0<br>और संख्या 9 से विभाज्य होनी चाहिए,<br>3 + 5 + 6 + y + 0 = 14 + y<br><math display=\"inline\"><mo>&#8756;</mo></math> y = 4<br>y - x = 4 - 0 = 4</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. If D is the midpoint of BC in &Delta;ABC and &ang;A = 90&deg;, then AD =______.</p>",
                    question_hi: "<p>41. यदि D, &Delta;ABC में BC का मध्यबिन्दु है और &ang;A = 90&deg; है, तो AD = _____होता है</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>2BC</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>BC</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>2BC</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>BC</p>"],
                    solution_en: "<p>41.(c)<br><strong>Theorem :</strong> The mid point of hypotenuse of a right angled triangle is equidistant from all the vertices of the triangle (AD = BD = DC).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672484878.png\" alt=\"rId15\" width=\"139\" height=\"150\"><br>Therefore, AD = <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>41.(c)<br><strong>प्रमेय:</strong> समकोण त्रिभुज के कर्ण का मध्य बिंदु त्रिभुज के सभी शीर्षों से समान दूरी पर होता है। (AD = BD = DC)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672484878.png\" alt=\"rId15\" width=\"139\" height=\"150\"><br>इसलिए, AD = <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. Choose the conclusion(s) which logically follow from the given statements.<br><strong>Statements:</strong><br>1. MS Dhoni is a popular cricketer <br>2. All cricketers are ﬁt and healthy <br>3. MS Dhoni earns a handsome amount every year through advertisements of various products.<br><strong>Conclusions:</strong><br>A. All popular cricketers earn a handsome amount through advertisement.<br>B. MS Dhoni is fit and healthy <br>C. MS Dhoni, being famous. advertises only famous products</p>",
                    question_hi: "<p>42. उस निष्कर्ष को चुनिए जो दिए गए कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>1. एम.एस. धोनी एक लोकप्रिय क्रिकेटर हैं।<br>2. सभी क्रिकेटर फिट और स्वस्थ हैं।<br>3. एम.एस. धोनी हर साल विभिन्न उत्पादों के विज्ञापनों के जरिए अच्छी खासी कमाई करते हैं।<br><strong>निष्कर्ष:</strong><br>A. सभी लोकप्रिय क्रिकेटर विज्ञापन के माध्यम से अच्छी खासी कमाई करते हैं।<br>B. एम.एस. धोनी फिट और स्वस्थ हैं।<br>C. एम एस धोनी मशहूर होने के कारण केवल मशहूर उत्पादों का ही विज्ञापन करते हैं।</p>",
                    options_en: ["<p>Conclusions A and C follow</p>", "<p>Only conclusion B follows</p>", 
                                "<p>Only conclusion C follows</p>", "<p>Conclusions A and B follow</p>"],
                    options_hi: ["<p>निष्कर्ष A और C अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष B अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष C अनुसरण करता है</p>", "<p>निष्कर्ष A और B अनुसरण करते हैं</p>"],
                    solution_en: "<p>42.(b) From the given statements, It is definitely correct that MS Dhoni is fit and healthy. <br>But just because MS Dhoni earns a handsome amount, does not mean that all the cricketers earn a handsome amount. Also MS Dhoni advertises various products but it is not mentioned that he advertises only famous products. So, only conclusion B follows.</p>",
                    solution_hi: "<p>42.(b) दिए गए कथनों से, यह निश्चित रूप से सही है कि एमएस धोनी फिट और स्वस्थ हैं।<br>लेकिन सिर्फ इसलिए कि एमएस धोनी अच्छी कमाई करते हैं, इसका मतलब यह नहीं है कि सभी क्रिकेटर्स अच्छी कमाई करते हैं। साथ ही एमएस धोनी विभिन्न उत्पादों का विज्ञापन करते हैं लेकिन यह उल्लेख नहीं है कि वह केवल प्रसिद्ध उत्पादों का विज्ञापन करते हैं। अत: केवल निष्कर्ष B अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. Pointing at a picture, Yuvika said that the boy in the picture is the son of her father&rsquo;s mother&rsquo;s daughter. How is that boy related to Yuvika ?</p>",
                    question_hi: "<p>43. एक तस्वीर की ओर इंगित करते हुए युबिका ने कहा कि तस्वीर में मौजूद लड़का, उसके पिता की मां की पुत्री का पुत्र है। उस लड़के का युविका से क्या संबंध है ?</p>",
                    options_en: ["<p>Brother</p>", "<p>Mother&rsquo;s brother&rsquo;s son</p>", 
                                "<p>Father&rsquo;s sister&rsquo;s son</p>", "<p>Father&rsquo;s brother</p>"],
                    options_hi: ["<p>भाई</p>", "<p>ममेरा भाई / मां के भाई का पुत्र</p>",
                                "<p>फुफेरा भाई /पिता की बहन का पुत्र</p>", "<p>चाचा / पिता का भाई</p>"],
                    solution_en: "<p>43.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672485147.png\" alt=\"rId16\" width=\"123\" height=\"182\"><br>According to the question, the person in the picture is the son of Yuvika\'s father\'s mother\'s daughter i.e. the son of grandmother\'s daughter is cousin. Hence the person in the picture is the son of sister of Yuvika\'s father.</p>",
                    solution_hi: "<p>43.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672485432.png\" alt=\"rId17\" width=\"133\" height=\"200\"><br>सवाल के मुताबिक तस्वीर में दिख रहा शख्स युविका के पिता की मां की बेटी का बेटा है यानी दादी की बेटी का बेटा चचेरा भाई है, इसलिए तस्वीर में दिख रहा व्यक्ति युविका के पिता की बहन का पुत्र है</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. A guard observes an enemy boat, from an observation tower at a height of 180 meters above the sea level, to be at an angle of depression of 60&deg;. What is the distance of the boat from the foot of the tower ?</p>",
                    question_hi: "<p>44. एक गार्ड समुद्र तल से 180 मीटर की ऊंचाई पर एक अवलोकन टावर से दुश्मन की नाव को 60&deg; के अवनमन कोण पर देखता है। मीनार के पाद से नाव की दूरी कितनी है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> m</p>", "<p>30 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> m</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> m</p>", "<p>60<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> m</p>", "<p>30 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> m</p>", "<p>60<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>"],
                    solution_en: "<p>44.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672485588.png\" alt=\"rId18\" width=\"153\" height=\"131\"><br>tan 60&deg; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>=</mo><mfrac><mn>180</mn><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>BC = 60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                    solution_hi: "<p>44.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672485588.png\" alt=\"rId18\" width=\"153\" height=\"131\"><br>tan 60&deg; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>=</mo><mfrac><mn>180</mn><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>BC = 60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp;मीटर</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. In what ratio must water be mixed with milk, costing ₹32 per litre, in order to get a mixture costing ₹28 per litre ?</p>",
                    question_hi: "<p>45. ₹32 प्रति लीटर की कीमत वाले दूध में किस अनुपात में पानी मिलाया जाए, ताकि ₹28 प्रति लीटर वाला मिश्रण प्राप्त हो सके ?</p>",
                    options_en: ["<p>8 : 1</p>", "<p>1 : 7</p>", 
                                "<p>1 : 8</p>", "<p>7 : 1</p>"],
                    options_hi: ["<p>8 : 1</p>", "<p>1 : 7</p>",
                                "<p>1 : 8</p>", "<p>7 : 1</p>"],
                    solution_en: "<p>45.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672485891.png\" alt=\"rId19\" width=\"135\" height=\"140\"><br>Required ratio = 4 : 28 = 1 : 7</p>",
                    solution_hi: "<p>45.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486015.png\" alt=\"rId20\" width=\"133\" height=\"136\"><br>आवश्यक अनुपात = 4 : 28 = 1 : 7</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. If &lsquo;A + B&rsquo; means &lsquo;A is daughter of B&rsquo;, \'A - B&rsquo; means &lsquo;A is wife of B&rsquo;, &lsquo;A &times; B&rsquo; means \'A is the son of B&rsquo;. If P &times; Q - S, then which of the following is true ?</p>",
                    question_hi: "<p>46. यदि \'A + B\' का अर्थ है \'A, B की पुत्री है&rsquo;, \'A - B\' का अर्थ है A, B की पत्नी है\', \'A &times; B&rsquo; का अर्थ है &lsquo;A, B का बेटा है\'। यदि P &times; Q - S है, तो निम्नलिखित में से कौन सा सत्य है ?</p>",
                    options_en: ["<p>Q is the father of P</p>", "<p>S is the father of P</p>", 
                                "<p>P is a daughter of Q</p>", "<p>S is the wife Q</p>"],
                    options_hi: ["<p>Q, P का पिता है</p>", "<p>S, P का पिता है</p>",
                                "<p>P, Q की पुत्री है</p>", "<p>S, Q की पत्नी है</p>"],
                    solution_en: "<p>46.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486116.png\" alt=\"rId21\" width=\"123\" height=\"81\"><br>From the above diagram, we can see that S is the father of P.</p>",
                    solution_hi: "<p>46.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486116.png\" alt=\"rId21\" width=\"123\" height=\"81\"><br>उपरोक्त आरेख से हम देख सकते हैं कि S, P का पिता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. Select the venn diagram that best represents the relationship between the given set of classes.<br>Bronze, Brass, Alloys<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486274.png\" alt=\"rId22\" width=\"425\" height=\"95\"> </p>",
                    question_hi: "<p>47. वेन आरेख का चयन कीजिये जो दिए गए वर्गों के बीच संबंध को सबसे अच्छा दर्शाता है।<br>कांस्य, पीतल, मिश्र धातु<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486274.png\" alt=\"rId22\" width=\"425\" height=\"95\"></p>",
                    options_en: ["<p>d</p>", "<p>c</p>", 
                                "<p>b</p>", "<p>a</p>"],
                    options_hi: ["<p>d</p>", "<p>c</p>",
                                "<p>b</p>", "<p>a</p>"],
                    solution_en: "<p>47.(b) Both bronze &amp; brass are alloys.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486425.png\" alt=\"rId23\" width=\"146\" height=\"90\"></p>",
                    solution_hi: "<p>47.(b) दोनों कांस्य और पीतल मिश्र धातु हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486558.png\" alt=\"rId24\" width=\"166\" height=\"102\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. 5 women and 9 girls earn a total of ₹18,720 in 9 days, while 9 women and 16 girls earn a total of ₹52,080 in 14 days. How much will 12 women and 7 girls together earn (in ₹) in 13 days?</p>",
                    question_hi: "<p>48. 5 महिलाएं और 9 लड़कियां 9 दिन में कुल ₹18,720 कमाती हैं, जबकि 9 महिलाएं और 16 लड़कियां 14 दिन में कुल ₹ 52,080 कमाती हैं। 12 महिलाएं और 7 लड़कियां मिलकर 13 दिन में कितना (₹ में) कमाएंगी ?</p>",
                    options_en: ["<p>42510</p>", "<p>41990</p>", 
                                "<p>42380</p>", "<p>42120</p>"],
                    options_hi: ["<p>42510</p>", "<p>41990</p>",
                                "<p>42380</p>", "<p>42120</p>"],
                    solution_en: "<p>48.(d)<br>Let the share of a woman and a girl be &lsquo;a&rsquo; and &lsquo;b&rsquo;.<br>According to question,<br>(5a + 9b) &times; 9 = 18720 --------(1)<br>(9a + 16b) &times; 14 = 52080 --------(2)<br>From the above equations, we get ;<br>a = 200 and b = 120<br>Required amount = {(12 &times; 200) + (7 &times; 120)} &times; 13 = 42,120 Rs.</p>",
                    solution_hi: "<p>48.(d)<br>माना कि एक महिला और एक लड़की का हिस्सा a और b है।<br>प्रश्न के अनुसार,<br>(5a + 9b) &times; 9 = 18720 --------(1)<br>(9a + 16b) &times; 14 = 52080 --------(2)<br>उपरोक्त समीकरणों से, हमें मिलता है;<br>a = 200 और b = 120<br>आवश्यक राशि {(12 &times; 200) + (7 &times; 120)} &times; 13 = 42,120 रुपये।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. The following graph shows the number (in hundreds) of bats manufactured by a factory in Meerut over the period of 2010 to 2014.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486732.png\" alt=\"rId25\" width=\"402\" height=\"242\"> <br>What is the average number of bats manufactured during 2010 to 2014 ?</p>",
                    question_hi: "<p>49. निम्नलिखित ग्राफ 2010 से 2014 की अवधि में मेरठ में एक कारखाने द्वारा निर्मित बैट की संख्या (सैकड़ों<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486862.png\" alt=\"rId26\" width=\"521\" height=\"313\"> <br>2010 से 2014 के दौरान निर्मित बैट की औसत संख्या क्या है ?</p>",
                    options_en: ["<p>3600</p>", "<p>3655</p>", 
                                "<p>3400</p>", "<p>3450</p>"],
                    options_hi: ["<p>3600</p>", "<p>3655</p>",
                                "<p>3400</p>", "<p>3450</p>"],
                    solution_en: "<p>49.(c) Average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>+</mo><mn>42</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>35</mn><mo>+</mo><mn>27</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>35</mn></mrow><mn>5</mn></mfrac></math> &times; 100 = 3400</p>",
                    solution_hi: "<p>49.(c) औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>+</mo><mn>42</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>35</mn><mo>+</mo><mn>27</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>35</mn></mrow><mn>5</mn></mfrac></math> &times; 100 = 3400</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. Four different positions of the same dice are shown. Select the letter that will be on the face opposite to the one having the letter F. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486962.png\" alt=\"rId27\" width=\"282\" height=\"59\"></p>",
                    question_hi: "<p>50. एक ही पासे की चार अलग-अलग स्थितियों को दिखाया गया है। उस अक्षर का चयन करें जो F अक्षर वाले फलक के विपरीत फलक पर होगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672486962.png\" alt=\"rId27\" width=\"282\" height=\"59\"></p>",
                    options_en: ["<p>C</p>", "<p>A</p>", 
                                "<p>E</p>", "<p>D</p>"],
                    options_hi: ["<p>C</p>", "<p>A</p>",
                                "<p>E</p>", "<p>D</p>"],
                    solution_en: "<p>50.(a)<br>Opposite of A = D (from dice 1 and 2)<br>Opposite of B = E (from dice 3 and 4)<br>Opposite of C = F</p>",
                    solution_hi: "<p>50.(a)<br>A के विपरीत = D पासा 1 और 2 से<br>B के विपरीत = E पासा 3 और 4 से<br>C के विपरीत = F</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. An electronic store owner allows two successive discounts of 20% and 25% on each item. The store has a reward points scheme which enables a customer to get free shopping worth ₹0.10 on every 1 reward point credited to the customer\'s account on previous purchases from the store. A customer decides to buy a laptop that is marked at ₹72,000. What will be its net selling price if he has 2850 reward points to his credit ?</p>",
                    question_hi: "<p>51. एक इलेक्ट्रॉनिक स्टोर मालिक प्रत्येक वस्तु पर 20% और 25% की दो क्रमिक छूटें देता है। स्टोर में एक रिवॉर्ड पॉइंट स्कीम है, जो ग्राहक को स्टोर से पिछली खरीदारी पर ग्राहक के खाते में जमा किए गए प्रत्येक 1 रिवार्ड पॉइंट पर ₹0.10 की मुफ्त खरीदारी करने में सक्षम बनाती है। एक ग्राहक एक लैपटॉप खरीदने का फैसला करता है जिसका अंकित मूल्य ₹72,000 है। यदि उसके खाते में 2850 रिवार्ड पॉइंट हैं, तो लैपटॉप का निवल विक्रय मूल्य क्या होगा ?</p>",
                    options_en: ["<p>₹43,200</p>", "<p>₹42,915</p>", 
                                "<p>₹42,215</p>", "<p>₹42,942</p>"],
                    options_hi: ["<p>₹43,200</p>", "<p>₹42,915</p>",
                                "<p>₹42,215</p>", "<p>₹42,942</p>"],
                    solution_en: "<p>51.(b)<br>Marked price of laptop = 72000 Rs.<br>According to question,<br>Net discount % = 20 + 25 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>25</mn></mrow><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac></math> = 40%<br>Price of laptop after discount = 72000 &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 43200 Rs.<br>Required price = 43200 -&nbsp;(2850 &times; 0.10) = 42915 Rs.</p>",
                    solution_hi: "<p>51.(b)<br>लैपटॉप का अंकित मूल्य = 72000 रु.<br>प्रश्न के अनुसार,<br>शुद्ध छूट % = 20 + 25 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>25</mn></mrow><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac></math> = 40%<br>छूट के बाद लैपटॉप की कीमत = 72000 &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 43200 रु.<br>आवश्यक मूल्य = 43200 -&nbsp;(2850 &times; 0.10) = 42915 रु.</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. The first attempt to calculate national income in India was made by</p>",
                    question_hi: "<p>52. भारत में राष्ट्रीय आय की गणना करने का पहला प्रयास किसके द्वारा किया गया था ?</p>",
                    options_en: ["<p>V. K. R. V.&nbsp; Rao</p>", "<p>S. D. Tendulkar</p>", 
                                "<p>PC Mahalanobis</p>", "<p>Dadabhai Naoroji</p>"],
                    options_hi: ["<p>वी.के.आर.वी. राव</p>", "<p>एस.डी. तेंदुलकर</p>",
                                "<p>PC महालनोबिस</p>", "<p>दादाभाई नौरोजी</p>"],
                    solution_en: "<p>52.(d) <strong>Dadabhai Naoroji. V. K. R. V. Rao -</strong> He used a scientific method to calculate the national income of India for the first time in 1931-32.<strong> S. D. Tendulkar</strong> - He was an Indian economist and former chief of the National Statistical Commission. <strong>PC Mahalanobis - </strong>He is also known as the father of Indian Statistics. <strong>National income -</strong> It is the total value of all goods and services produced within a country\'s borders in a specific time period.</p>",
                    solution_hi: "<p>52.(d) <strong>दादाभाई नौरोजी। वी. के. आर. वी. राव - </strong>उन्होंने 1931-32 में पहली बार भारत की राष्ट्रीय आय की गणना के लिए वैज्ञानिक पद्धति का प्रयोग किया। <strong>एस. डी. तेंदुलकर - </strong>वे एक भारतीय अर्थशास्त्री और राष्ट्रीय सांख्यिकी आयोग के पूर्व प्रमुख थे।<strong> PC महालनोबिस - </strong>इन्हें भारतीय सांख्यिकी के जनक के रूप में भी जाना जाता है। <strong>राष्ट्रीय आय -</strong> यह एक निश्चित समय अवधि में देश की सीमाओं के भीतर उत्पादित सभी वस्तुओं और सेवाओं का कुल मूल्य है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. A certain number of people are sitting in a row, facing the north. R sits third to the left of P. Q sits fourth to the right of P. Only two persons sit between Q and T. If no other person is sitting in the row, what is the total number of persons seated ?</p>",
                    question_hi: "<p>53. कुछ व्यक्ति उत्तर की ओर मुख करके एक पंक्ति में बैठे हैं। R, P के बाईं ओर तीसरे स्थान पर बैठा है। Q, P के दाईं ओर चौथे स्थान पर बैठा है। Q और T के बीच केवल दो व्यक्ति बैठे हैं। यदि कोई अन्य व्यक्ति पंक्ति में नहीं बैठा है, तो पंक्ति में बैठे हुए व्यक्तियों की कुल संख्या कितनी है ?</p>",
                    options_en: ["<p>12</p>", "<p>11</p>", 
                                "<p>15</p>", "<p>13</p>"],
                    options_hi: ["<p>12</p>", "<p>11</p>",
                                "<p>15</p>", "<p>13</p>"],
                    solution_en: "<p>53.(b)<br><strong>As per the instructions given in the question</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487125.png\" alt=\"rId28\" width=\"230\" height=\"71\"><br><strong>It is clear after arrangement that total 11 persons are sitting in the row.</strong></p>",
                    solution_hi: "<p>53.(b)<br><strong>प्रश्न में दिए गए निर्देशों के अनुसार</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487125.png\" alt=\"rId28\" width=\"230\" height=\"71\"><br><strong>व्यवस्था के बाद स्पष्ट है कि पंक्ति में कुल 11 व्यक्ति बैठे हैं।</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. Evaluate the following:<br>cos(36&deg; + A).cos(36&deg; - A) + cos(54&deg; + A).cos(54&deg; - A)</p>",
                    question_hi: "<p>54. निम्नलिखित का मान ज्ञात कीजिए। <br>cos(36&deg; + A).cos(36&deg; - A) + cos(54&deg; + A).cos(54&deg; - A)</p>",
                    options_en: ["<p>sin 2A</p>", "<p>cos A</p>", 
                                "<p>sin A</p>", "<p>cos 2A</p>"],
                    options_hi: ["<p>sin 2A</p>", "<p>cos A</p>",
                                "<p>sin A</p>", "<p>cos 2A</p>"],
                    solution_en: "<p>54.(d)<br><strong>Formula used: </strong>cos (A - B) = cosA .cosB + sinA.sin B<br>cos(36&deg;+A).cos(36&deg;<math display=\"inline\"><mo>-</mo></math>A) + cos(54&deg; + A).cos(54&deg; - A)<br><math display=\"inline\"><mo>&#8658;</mo></math>cos(36&deg; + A).cos(36&deg; - A) + cos[90 - (36&deg; + A)].cos[90 - (36&deg; - A)]<br><math display=\"inline\"><mo>&#8658;</mo></math>cos(36&deg; + A).cos(36&deg; - A) + sin (36&deg; + A) sin (36&deg;- A)<br><math display=\"inline\"><mo>&#8658;</mo></math>cos[36&deg; + A - (36&deg; - A)] &rArr; cos(2A)</p>",
                    solution_hi: "<p>54.(d)<br><strong>प्रयुक्त सूत्र : </strong>cos (A - B) = cosA .cosB + sinA.sin B<br>cos(36&deg;+A).cos(36&deg;<math display=\"inline\"><mo>-</mo></math>A) + cos(54&deg; + A).cos(54&deg; - A)<br><math display=\"inline\"><mo>&#8658;</mo></math>cos(36&deg; + A).cos(36&deg; - A) + cos[90 - (36&deg; + A)].cos[90 - (36&deg; - A)]<br><math display=\"inline\"><mo>&#8658;</mo></math>cos(36&deg; + A).cos(36&deg; - A) + sin (36&deg; + A) sin (36&deg;- A)<br><math display=\"inline\"><mo>&#8658;</mo></math>cos[36&deg; + A - (36&deg; - A)] &rArr; cos(2A)</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. Six friends are sitting around a round table. Mohan is sitting opposite to Rohit who is sitting between Ram and Mohit. Mohit is sitting opposite Shyam. Who is sitting opposite Rohan ?</p>",
                    question_hi: "<p>55. छह मित्र एक गोल मेज के चारों ओर बैठे हैं। मोहन रोहित के सामने बैठा है जो राम और मोहित के बीच बैठा है। मोहित श्याम के सामने बैठा है। रोहन के सामने कौन बैठा है ?</p>",
                    options_en: ["<p>Ram</p>", "<p>Mohit</p>", 
                                "<p>Shyam</p>", "<p>Rohit</p>"],
                    options_hi: ["<p>राम</p>", "<p>मोहित</p>",
                                "<p>श्याम</p>", "<p>रोहित</p>"],
                    solution_en: "<p>55.(a) From the given information we can draw the following diagram:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487244.png\" alt=\"rId29\" width=\"182\" height=\"131\"><br>So, Ram is sitting opposite Rohan.</p>",
                    solution_hi: "<p>55.(a) दी गई जानकारी से हम निम्नलिखित आरेख बना सकते हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487351.png\" alt=\"rId30\" width=\"155\" height=\"146\"><br>तो, राम रोहन के सामने बैठा है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. Find the simplified value of the given expression.<br>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of 5 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>10</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    question_hi: "<p>56. दिए गए व्यंजक का सरलीकृत मान ज्ञात कीजिए।<br>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of 5 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>10</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    options_en: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>5</p>", 
                                "<p>1<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p>6</p>"],
                    options_hi: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>5</p>",
                                "<p>1<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p>6</p>"],
                    solution_en: "<p>56.(c)<br>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of 5 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>10</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>5</mn></mfrac></math> &divide; 3 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>50</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mn>2</mn><mn>50</mn></mfrac></math>&nbsp;&rArr; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math></p>",
                    solution_hi: "<p>56.(c)<br>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of 5 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>10</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>5</mn></mfrac></math> &divide; 3 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>50</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mn>2</mn><mn>50</mn></mfrac></math> &rArr; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57. After starting from a point, Naveen walks 3 km towards the east. Then turning to his left he moves 3 km. After this he again turns his left and moves 4 km. In what direction is he standing from his starting point ?</p>",
                    question_hi: "<p>57. एक बिंदु से शुरू करने के बाद, नवीन पूर्व की ओर 3 km चलता है। फिर अपने बायीं ओर मुड़कर वह 3 km चलता है। इसके बाद वह फिर से अपने बाएं मुड़ता है और 4 km चलता है। वह अपने आरंभिक बिंदु से किस दिशा में खड़ा है ?</p>",
                    options_en: ["<p>South</p>", "<p>South-east</p>", 
                                "<p>North-west</p>", "<p>North</p>"],
                    options_hi: ["<p>दक्षिण</p>", "<p>दक्षिण पूर्व</p>",
                                "<p>उत्तर पश्चिम</p>", "<p>उत्तर</p>"],
                    solution_en: "<p>57.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487446.png\" alt=\"rId31\" width=\"238\" height=\"117\"></p>",
                    solution_hi: "<p>57.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487581.png\" alt=\"rId32\" width=\"232\" height=\"130\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. Which of the following are metalloids ?</p>",
                    question_hi: "<p>58. निम्नलिखित में से कौन-से उपधातु हैं ?</p>",
                    options_en: ["<p>Boron, oxygen, aluminium</p>", "<p>Boron, mercury, iron</p>", 
                                "<p>Boron, silicon, antimony</p>", "<p>Aluminium, mercury, copper</p>"],
                    options_hi: ["<p>बोरॉन, ऑक्सीजन, ऐलुमीनियम</p>", "<p>बोरॉन, मर्करी, आयरन</p>",
                                "<p>बोरान, सिलिकॉन, ऐन्टिमनी</p>", "<p>ऐलुमिनियम, मर्करी, कॉपर</p>"],
                    solution_en: "<p>58.(c) <strong>Boron, silicon, antimony.</strong> <strong>Metalloids - </strong>Chemical elements whose physical and chemical properties fall in between the metal and non-metal categories. Seven most widely recognized metalloids - Boron, germanium, silicon, antimony, arsenic, tellurium and polonium.</p>",
                    solution_hi: "<p>58.(c) <strong>बोरान, सिलिकॉन, ऐन्टिमनी। उपधातु - </strong>रासायनिक तत्व जिनके भौतिक और रासायनिक गुण धातु और अधातु श्रेणियों के बीच होते हैं। सात सबसे व्यापक रूप से मान्यता प्राप्त उपधातुएँ - बोरॉन, जर्मेनियम, सिलिकॉन, एंटीमनी, आर्सेनिक, टेल्यूरियम और पोलोनियम।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. If &lsquo;&lt;&rsquo; means &lsquo;<math display=\"inline\"><mo>-</mo></math>&rsquo;, &lsquo;&gt;&rsquo; means &lsquo;+&rsquo;, &lsquo;&amp;&rsquo; means &lsquo;&times;&rsquo;, and &lsquo;@&rsquo; means &lsquo;&divide;&rsquo;, then what would be the value of the following expression ?<br>119&lt;56@4&gt;23&amp;2</p>",
                    question_hi: "<p>59. If &lsquo;&lt;\' का अर्थ है, &lsquo;<math display=\"inline\"><mo>-</mo></math>&rsquo;, &lsquo;&gt;&rsquo; का अर्थ \'+\' है, \'&amp;\' का अर्थ \'&times;\' है, और \'@\' का अर्थ \'&divide;\' है, तो दिए गए व्यंजक का मान ज्ञात कीजिए।<br>119&lt;56@4&gt;23&amp;2</p>",
                    options_en: ["<p>231</p>", "<p>151</p>", 
                                "<p>191</p>", "<p>202</p>"],
                    options_hi: ["<p>231</p>", "<p>151</p>",
                                "<p>191</p>", "<p>202</p>"],
                    solution_en: "<p>59.(b) <br>119 &lt; 56 @ 4 &gt; 23 &amp; 2<br>Now, Interchange the signs according to the question.<br><math display=\"inline\"><mo>&#8658;</mo></math>119 - 56 &divide; 4 + 23 &times; 2<br><math display=\"inline\"><mo>&#8658;</mo></math>119 - 14 + 46<br><math display=\"inline\"><mo>&#8658;</mo></math>165 - 14<br><math display=\"inline\"><mo>&#8658;</mo></math>151</p>",
                    solution_hi: "<p>59.(b) <br>119 &lt; 56 @ 4 &gt; 23 &amp; 2<br>अब प्रश्न के अनुसार चिन्हों को आपस में बदल लें।<br><math display=\"inline\"><mo>&#8658;</mo></math>119 - 56 &divide; 4 + 23 &times; 2<br><math display=\"inline\"><mo>&#8658;</mo></math>119 - 14 + 46<br><math display=\"inline\"><mo>&#8658;</mo></math>165 - 14<br><math display=\"inline\"><mo>&#8658;</mo></math>151</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. 4 women or 6 boys can finish a work in the same number of days. A Boy can finish it in 60 days. In how many days can 5 women finish the work, working together every day ?</p>",
                    question_hi: "<p>60. 4 महिलाएं या 6 लड़के एक कार्य को समान दिनों में पूरा कर सकते हैं। एक लड़का इसे 60 दिनों में पूरा कर सकता है। प्रतिदिन एक साथ मिलकर कार्य करते हुए 5 महिलाएं उस कार्य को कितने दिनों में पूरा कर सकती हैं ?</p>",
                    options_en: ["<p>4</p>", "<p>10</p>", 
                                "<p>8</p>", "<p>6</p>"],
                    options_hi: ["<p>4</p>", "<p>10</p>",
                                "<p>8</p>", "<p>6</p>"],
                    solution_en: "<p>60.(c)<br>According to question,<br>4W = 6B<math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>W</mi><mi>B</mi></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math><br>Total work (60B) = 60 <math display=\"inline\"><mo>&#215;</mo></math> 2<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>60</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 8 days</p>",
                    solution_hi: "<p>60.(c)<br>प्रश्न के अनुसार,<br>4W = 6B<math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>W</mi><mi>B</mi></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math><br>कुल कार्य (60B) = 60 <math display=\"inline\"><mo>&#215;</mo></math> 2<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 8 दिन</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Which of the following grains contain oryzenin ?</p>",
                    question_hi: "<p>61. निम्नलिखित में से किस अनाज में ओरीजेनिन होता है ?</p>",
                    options_en: ["<p>Barley</p>", "<p>Wheat</p>", 
                                "<p>Soyabean</p>", "<p>Rice</p>"],
                    options_hi: ["<p>जौ</p>", "<p>गेहूं</p>",
                                "<p>सोयाबीन</p>", "<p>चावल</p>"],
                    solution_en: "<p>61.(d) <strong>Rice </strong>- A source of protein and contains various vitamins, such as thiamin and niacin, and minerals, such as zinc and phosphorus. <strong>Oryzenin </strong>- A rice glutelin, is an important constituent of rice protein. Glutelin comprises the major protein fraction of the rice grain and constitutes up to 80% of the total protein. <strong>Wheat </strong>contains protein, B vitamins, dietary fiber, and phytochemicals. <strong>Soyabean </strong>is high in protein and contains vitamin K1, folate, copper, manganese, phosphorus, and thiamine.</p>",
                    solution_hi: "<p>61.(d) <strong>चावल -</strong> प्रोटीन का एक स्रोत और इसमें विभिन्न विटामिन, जैसे थायमिन और नियासिन, और खनिज, जैसे जस्ता और फास्फोरस शामिल हैं। <strong>ओरिजेनिन </strong>- चावल का ग्लूटेलिन, चावल प्रोटीन का एक महत्वपूर्ण घटक है। ग्लूटेलिन में चावल के दाने का प्रमुख प्रोटीन अंश होता है और यह कुल प्रोटीन का 80% तक होता है। <strong>गेहूं </strong>में प्रोटीन, विटामिन B, आहार फाइबर और फाइटोकेमिकल्स होते हैं। <strong>सोयाबीन </strong>में उच्च मात्रा में प्रोटीन होता है और इसमें विटामिन K1, फ़ोलेट, तांबा, मैंगनीज, फॉस्फोरस और थायमिन&nbsp;होता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. The price of sugar increased by 10%. A family of 5 members did not want to increase their expenditure. What is the percentage reduction in their consumption of sugar ?</p>",
                    question_hi: "<p>62. चीनी की कीमत में 10% की वृद्धि हुई। 5 सदस्यों का एक परिवार अपना खर्च नहीं बढ़ाना चाहता था। उनकी चीनी की खपत में कितने प्रतिशत की कमी आई है ?</p>",
                    options_en: ["<p>8</p>", "<p><math display=\"inline\"><mn>9</mn><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>", 
                                "<p>10</p>", "<p>12</p>"],
                    options_hi: ["<p>8</p>", "<p><math display=\"inline\"><mn>9</mn><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                                "<p>10</p>", "<p>12</p>"],
                    solution_en: "<p>62.(b) 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>Expenditure depends on price and quantity, Exp. = Price &times; Quantity<br>Price increases by 10% means, price = 10<math display=\"inline\"><mo>&#8594;</mo></math>11<br>Expenditure remains constant it do not change, so quantity must be decreased.<br>Quantity = 11&rarr;10<br>Change in quantity = 1<br>% change in quantity = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 100 = 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>11</mn></mfrac></math></p>",
                    solution_hi: "<p>62.(b) 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>व्यय कीमत और मात्रा पर निर्भर करता है, व्यय = मूल्य &times; मात्रा<br>कीमत 10% बढ़ जाती है मतलब, कीमत = 10<math display=\"inline\"><mo>&#8594;</mo></math>11<br>व्यय स्थिर रहता है यह नहीं बदलता है, इसलिए मात्रा कम होनी चाहिए<br>मात्रा = 11&rarr;10<br>मात्रा में परिवर्तन = 1 <br>मात्रा में % परिवर्तन = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 100 = 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>11</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. Assuming 8<sup>th&nbsp;</sup>March 2013 was a Wednesday, what day of the week was 8<sup>th</sup> March 2014 ?</p>",
                    question_hi: "<p>63. यह मानते हुए कि 8 मार्च 2013 को बुधवार था, 8 मार्च 2014 को सप्ताह का कौन सा दिन था ?</p>",
                    options_en: ["<p>Tuesday</p>", "<p>Monday</p>", 
                                "<p>Thursday</p>", "<p>Wednesday</p>"],
                    options_hi: ["<p>मंगलवार</p>", "<p>सोमवार</p>",
                                "<p>गुरुवार</p>", "<p>बुधवार</p>"],
                    solution_en: "<p>63.(c)<br>8th March 2013 = Wednesday<br>8th March 2014 = Wednesday + 1 = Thursday</p>",
                    solution_hi: "<p>63.(c)<br>8 मार्च 2013 = बुधवार<br>8 मार्च 2014 = बुधवार + 1 = गुरुवार</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. In a class of 21 students, each scored differently. P&rsquo;s rank from the bottom is <math display=\"inline\"><msup><mrow><mn>9</mn></mrow><mrow><mi>t</mi><mi>h</mi></mrow></msup></math>, while Q&rsquo;s&nbsp;rank from the top is also <math display=\"inline\"><msup><mrow><mn>9</mn></mrow><mrow><mi>t</mi><mi>h</mi></mrow></msup></math>. How many students are ranked between Q and P ?</p>",
                    question_hi: "<p>64. 21 विद्यार्थियों की एक कक्षा में, प्रत्येक विद्यार्थी ने अलग-अलग अंक प्राप्त किए। P का स्थान नीचे से 9वां है, जबकि Q का स्थान ऊपर से 9वां है। कितने विद्यार्थियों के स्थान Q और P के बीच हैं ?</p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>5</p>", "<p>3</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>5</p>", "<p>3</p>"],
                    solution_en: "<p>64.(d)<br>Let x be the number of students ranked between Q and P.<br>P&rsquo;s rank from bottom = 9th<br>Q&rsquo;s rank from top = 9th<br>Total students = 21<br>Therefore,<br>21 = 9 + 9 + x<br><strong>x = 3</strong></p>",
                    solution_hi: "<p>64.(d)<br>मान लीजिए x, Q और P के बीच रैंक किए गए छात्रों की संख्या है।<br>P की नीचे से रैंक = 9<br>शीर्ष से Q की रैंक = 9<br>कुल विद्यार्थी = 21<br>इसलिए,<br>21 = 9 + 9 + x<br><strong>x = 3</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. Raman wants to use a rear-view mirror in his vehicle. Which type of mirror should he pick for it ?</p>",
                    question_hi: "<p>65. रमन अपने वाहन में पश्च-हृश्य दर्पण का प्रयोग करना चाहता है। इसके लिए उसे किस प्रकार का दर्पण चुनना चाहिए ?</p>",
                    options_en: ["<p>Convex Mirrors</p>", "<p>Plane Mirrors</p>", 
                                "<p>Cylindrical Mirrors</p>", "<p>Concave Mirrors</p>"],
                    options_hi: ["<p>उत्तल दर्पण</p>", "<p>समतल दर्पण</p>",
                                "<p>बेलनाकार दर्पण</p>", "<p>अवतल दर्पण</p>"],
                    solution_en: "<p>65.(a) <strong>Convex Mirrors.</strong> This is because they give an erect, virtual, full size diminished image of distant objects with a wider field of view.<strong> Uses -</strong> Sunglasses, street lights.</p>",
                    solution_hi: "<p>65.(a) <strong>उत्तल दर्पण।</strong> ऐसा इसलिए है क्योंकि ये दर्पण दूर की वस्तुओं की व्यापक दृश्य क्षेत्र के साथ एक सीधी, आभासी, पूर्ण आकार की छोटी प्रतिबिंब प्रदान करती हैं। <strong>उपयोग -</strong> धूप का चश्मा, स्ट्रीट लाइट।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. Find the value of <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>270</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>180</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo><mo>+</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mfrac><mi mathvariant=\"bold-italic\">&#960;</mi><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>270</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo></math>.</p>",
                    question_hi: "<p>66. निम्न का मान ज्ञात कीजिये ?<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>270</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>180</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo><mo>+</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mfrac><mi mathvariant=\"bold-italic\">&#960;</mi><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>270</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo></math>.</p>",
                    options_en: ["<p><math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mi>&#934;</mi></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mfrac><mi mathvariant=\"bold-italic\">&#960;</mi><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi></math></p>", 
                                "<p><math display=\"inline\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>(</mo><mi>&#934;</mi><mo>)</mo></math> - 1</p>", "<p><math display=\"inline\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>(</mo><mi>&#934;</mi><mo>)</mo></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mi>&#934;</mi></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mfrac><mi mathvariant=\"bold-italic\">&#960;</mi><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi></math></p>",
                                "<p><math display=\"inline\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>(</mo><mi>&#934;</mi><mo>)</mo></math> - 1</p>", "<p><math display=\"inline\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>(</mo><mi>&#934;</mi><mo>)</mo></math></p>"],
                    solution_en: "<p>66.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>270</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>180</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo><mo>+</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mfrac><mi mathvariant=\"bold-italic\">&#960;</mi><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>270</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#981;</mi><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#981;</mi><mo>+</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#981;</mi></math> = cos<sup>2</sup>ϕ</p>",
                    solution_hi: "<p>66.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>270</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>180</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo><mo>+</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mfrac><mi mathvariant=\"bold-italic\">&#960;</mi><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>(</mo><mn>270</mn><mo>-</mo><mi>&#934;</mi><mo>)</mo></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#981;</mi><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#981;</mi><mo>+</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#981;</mi></math> = cos<sup>2</sup>ϕ</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. Answer the question based on the letter-number-symbol series given below.<br>(left)) S $ 3 &amp; H P W 8 # J * M 2 F A 2 $ Y 1 V( right)<br>How many such symbols are there in the above series which are immediately preceded by a letter and also immediately followed by a number ?</p>",
                    question_hi: "<p>67. नीचे दी गई अक्षर, संख्या, प्रतीक शृंखला के आधार पर पूछे गए प्रश्न का उत्तर दें।<br>(बाएं) S $ 3 &amp; H P W 8 # J * M 2 F A 2 $ Y 1 V (दाएं)<br>उपरोक्त शृंखला में ऐसे कितने प्रतीक हैं, जिनके ठीक पहले एक अक्षर और ठीक बाद एक संख्या भी है?</p>",
                    options_en: ["<p>0</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>3</p>"],
                    options_hi: ["<p>0</p>", "<p>2</p>",
                                "<p>1</p>", "<p>3</p>"],
                    solution_en: "<p>67.(c)<br><strong>Given series: </strong>(Left)S $ 3 &amp; H P W 8 # J * M 2 F A 2 $ Y 1 V (Right)<br>According to the question, there is only symbol (S <strong>$</strong> 3) that is immediately preceded by a letter and immediately followed by a number.</p>",
                    solution_hi: "<p>67.(c)<br>दी गई शृंखला : <strong>(बाएं) </strong>S $ 3 &amp; H P W 8 # J * M 2 F A 2 $ Y 1 V (दाएं)<br>प्रश्न के अनुसार केवल एक प्रतीक (S $ 3) है जिसके ठीक पहले एक अक्षर और ठीक बाद में एक संख्या है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. Identify the figure given in the options which when put in place of ? will logically complete the series. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487705.png\" alt=\"rId33\" width=\"331\" height=\"80\"></p>",
                    question_hi: "<p>68. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिन्ह ? के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487705.png\" alt=\"rId33\" width=\"331\" height=\"80\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487829.png\" alt=\"rId34\" width=\"86\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487964.png\" alt=\"rId35\" width=\"86\" height=\"89\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488068.png\" alt=\"rId36\" width=\"86\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488190.png\" alt=\"rId37\" width=\"86\" height=\"84\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487829.png\" alt=\"rId34\" width=\"85\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487964.png\" alt=\"rId35\" width=\"86\" height=\"89\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488068.png\" alt=\"rId36\" width=\"86\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488190.png\" alt=\"rId37\" width=\"86\" height=\"84\"></p>"],
                    solution_en: "<p>68.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487829.png\" alt=\"rId34\" width=\"90\" height=\"94\"></p>",
                    solution_hi: "<p>68.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672487829.png\" alt=\"rId34\" width=\"90\" height=\"94\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Article 20 of the Constitution of India is related to _____.</p>",
                    question_hi: "<p>69. भारतीय संविधान का अनुच्छेद 20 ______से संबंधित है।</p>",
                    options_en: ["<p>equality of opportunities in matters of public employment</p>", "<p>protection in respect of conviction for offenses</p>", 
                                "<p>freedom to manage religious affairs</p>", "<p>abolition of titles</p>"],
                    options_hi: ["<p>सार्वजनिक रोजगार के मामलों में अवसरों की समानता</p>", "<p>अपराधों के लिए दोष सिद्धि के संबंध में संरक्षण</p>",
                                "<p>धार्मिक मामलों के प्रबंधन की स्वतंत्रता</p>", "<p>उपाधियों के उन्मूलन</p>"],
                    solution_en: "<p>69.(b) Part three of the Constitution deals with Fundamental Rights. The Constitution guarantees six fundamental rights to Indian citizens as follows (i) right to equality, (ii) right to freedom (iii) right against exploitation (iv) right to freedom of religion (v) cultural and educational rights (vi) right to constitutional remedies. Article 16 - Equality of opportunities in matters of public employment. Article 26 - Freedom to manage religious affairs. Article 18 - Abolition of titles.</p>",
                    solution_hi: "<p>69.(b) संविधान का भाग तीन, मौलिक अधिकारों से संबंधित है। संविधान भारतीय नागरिकों को निम्नलिखित छह मौलिक अधिकारों की गारंटी देता है: (i) समानता का अधिकार, (ii) स्वतंत्रता का अधिकार (iii) शोषण के विरुद्ध अधिकार (iv) धार्मिक स्वतंत्रता का अधिकार (v) सांस्कृतिक और शैक्षिक अधिकार (vi) संवैधानिक उपचारों का अधिकार। अनुच्छेद 16 - सार्वजनिक रोजगार के मामलों में अवसरों की समानता। अनुच्छेद 26 - धार्मिक मामलों के प्रबंधन की स्वतंत्रता। अनुच्छेद 18 - उपाधियों का उन्मूलन।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. If the simple interest at the same interest rate on ₹500 for 4 years and ₹700 for 2 years, combined together, is ₹280, then what is the rate of interest ?</p>",
                    question_hi: "<p>70. यदि समान ब्याज दर पर 500 रूपये का 4 वर्ष और 700 रूपये का 2 वर्ष का साधारण ब्याज मिलाकर 280 रूपये है, तो व्याज की दर कितनी होगी ?</p>",
                    options_en: ["<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", 
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>",
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>70.(c)<br>Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math><br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>500</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>700</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 280<br>Rate of interest = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>17</mn></mfrac></math> %</p>",
                    solution_hi: "<p>70.(c)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>500</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>700</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 280<br>ब्याज दर = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>17</mn></mfrac></math> %</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. Find the compound interest on ₹32000 for 6 months at 12% per annum compounded quarterly ?</p>",
                    question_hi: "<p>71. त्रैमासिक रूप से संयोजित 12% प्रति वर्ष की दर से 6 महीने के लिए ₹32000 पर चक्रवृद्धि ब्याज ज्ञात कीजिए ?</p>",
                    options_en: ["<p>₹ 1947</p>", "<p>₹ 1948</p>", 
                                "<p>₹ 1947.80</p>", "<p>₹ 1948.80</p>"],
                    options_hi: ["<p>₹ 1947</p>", "<p>₹ 1948</p>",
                                "<p>₹ 1947.80</p>", "<p>₹ 1948.80</p>"],
                    solution_en: "<p>71.(d) <br>Interest is compounded quarterly,<br>Interest = 3%, Time = 2 units (compounding cycles=6/3=2)<br>In 1st year =&nbsp; 100 : 103<br>In 2nd year = 100 : 103<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _____________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10,000 : 10609<br>Interest = 10,609 - 10,000 = 609<br>As per question,<br>10,000 = 32,000<br>609 = 1948.80<br>So, Compound interest = 1948.80</p>",
                    solution_hi: "<p>71.(d)<br>ब्याज त्रैमासिक चक्रवृद्धि है,<br>ब्याज = 3%, समय = 2 इकाई<br>पहला साल = 100 : 103<br>दूसरा वर्ष&nbsp; &nbsp;= 100 : 103<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _____________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10,000 : 10609<br>ब्याज = 10,609 - 10,000 = 609<br>प्रश्न के अनुसार,<br>10,000 = 32,000<br>609 = 1948.80<br>अत: चक्रवृद्धि ब्याज = 1948.80</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. The Jawaharlal Nehru Port is situated in which of the Following cities ?</p>",
                    question_hi: "<p>72. जवाहरलाल नेहरू बंदरगाह निम्नलिखित में से किस शहर में स्थित है ?</p>",
                    options_en: ["<p>Mormugao</p>", "<p>Kochi</p>", 
                                "<p>Navi Mumbai<strong id=\"docs-internal-guid-0d8bb48e-7fff-f0f6-c53b-85adb3a3f6d4\"><br></strong></p>", "<p>Kandla</p>"],
                    options_hi: ["<p>मोरमुगांव</p>", "<p>कोच्चि</p>",
                                "<p>नवी मुंबई</p>", "<p>कांडला</p>"],
                    solution_en: "<p>72.(c)<br>Jawaharlal Nehru Port Trust (JNPT) or JLN Port, also known as Nhava Sheva Port, is the second largest container port in India after Mundra Port. Located in Navi Mumbai\'s Raigad district, this port on the Arabian Sea is accessed via Thane Creek. This port is also the terminal of Western Dedicated Freight Corridor.</p>",
                    solution_hi: "<p>72.(c)<br>जवाहरलाल नेहरू बंदरगाह ट्रस्ट (JNPT) या JLN बंदरगाह, जिसे न्हावा शेवा बंदरगाह (Nhava Sheva Port,) के नाम से भी जाना जाता है, मुंद्रा (Mundra) बंदरगाह के बाद भारत का दूसरा सबसे बड़ा कंटेनर बंदरगाह है। नवी मुंबई के रायगढ़ जिले में स्थित, अरब सागर पर स्थित इस बंदरगाह तक ठाणे क्रीक के माध्यम से पहुँचा जा सकता है। यह बंदरगाह वेस्टर्न डेडिकेटेड फ्रेट कॉरिडोर का टर्मिनल भी है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. A thief steals a van at 3:00 a.m. and drives it at a speed of 57 km/h. The thief is discovered at 4:00 am. and the owner starts the chase with another van at a speed of 76 km/h. At what time will he catch the thief ?</p>",
                    question_hi: "<p>73. एक चोर 3:00 am पर एक वैन चुराता है और उसे 57 km/h की चाल से चलाता है। 4:00 am पर चोर का पता चलता है और मालिक 76 km/h की चाल से एक अन्य वैन से उसका पीछा करना शुरू करता है। वह चोर को कितने बजे पकड़ पाएगा ?</p>",
                    options_en: ["<p>7 : 30 a.m</p>", "<p>7 : 00 p.m</p>", 
                                "<p>7 : 00 a.m</p>", "<p>6 : 00 a.m</p>"],
                    options_hi: ["<p>7 : 30 a.m</p>", "<p>7 : 00 p.m</p>",
                                "<p>7 : 00 a.m</p>", "<p>6 : 00 a.m</p>"],
                    solution_en: "<p>73.(c)<br>According to question,<br>Distance covered by thief in 1 hour = 57 km<br>Relative Speed = (76 -&nbsp;57) = 19 km/hr<br>Time required to catch the thief = <math display=\"inline\"><mfrac><mrow><mn>57</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math> = 3 hours<br>Therefore, Required time = 4 : 00 am + 3hrs. = 7 : 00 a.m.</p>",
                    solution_hi: "<p>73.(c)<br>प्रश्न के अनुसार,<br>चोर द्वारा 1 घंटे में तय की गई दूरी = 57 किमी<br>सापेक्ष गति = (76 -&nbsp;57) = 19 किमी/घंटा<br>चोर को पकड़ने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>57</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math> = 3 घंटे<br>इसलिए, आवश्यक समय = 4 : 00 a.m. + 3घंटे = 7 : 00 a.m.</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. The statement that the upward buoyant force that is exerted on a body immersed in a fluid, whether fully&nbsp;or partially, is equal to the weight of the fluid that the body displaces is related to:</p>",
                    question_hi: "<p>74. द्रव में पूर्णतः या आंशिक रूप से डूबे हुए किसी पिंड पर ऊपर की ओर लगने वाला उत्प्लावन बल, पिंड द्वारा विस्थापित द्रव के भार के बराबर होता है। यह कथन किस से संबंधित है।</p>",
                    options_en: ["<p>Beer Lambert Law</p>", "<p>Pascal&rsquo;s Law<strong id=\"docs-internal-guid-a3bc5e69-7fff-5d4a-52f6-18088ced2855\"><br></strong></p>", 
                                "<p>Archimedes&rsquo; Principle<strong id=\"docs-internal-guid-1d7fd2e7-7fff-76f1-9d48-bf9ec8182e30\"><br></strong></p>", "<p>Bernoulli&rsquo;s Principle</p>"],
                    options_hi: ["<p>बीयर लैम्बर्ट नियम</p>", "<p>पास्कल का नियम</p>",
                                "<p>आर्किमिडीज का सिद्धांत</p>", "<p>बर्नौली का सिद्धांत</p>"],
                    solution_en: "<p>74.(c) <br>Archimedes\' principle (or, more properly, Archimedes\'s principle) states that the upward buoyant force that is exerted on a body immersed in a fluid, whether fully or partially, is equal to the weight of the fluid that the body displaces. Archimedes\' principle is a law of physics fundamental to fluid mechanics. It was formulated by Archimedes of Syracuse.</p>",
                    solution_hi: "<p>74.(c)<br><strong>आर्किमिडीज के सिद्धांत </strong>में कहा गया है कि किसी तरल पदार्थ में डूबे हुए पिण्ड पर ऊपर की ओर उत्प्लावक बल लगाया जाता है, चाहे वह पूरी तरह से या आंशिक रूप से तरल पदार्थ के भार के बराबर होता है जो पिण्ड को विस्थापित करता है। आर्किमिडीज का सिद्धांत द्रव यांत्रिकी के लिए मौलिक भौतिकी का नियम है। यह सिरैक्यूज़ के आर्किमिडीज़ द्वारा तैयार किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the end. How would the paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488326.png\" alt=\"rId38\" width=\"260\" height=\"81\"></p>",
                    question_hi: "<p>75. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओं में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छेद किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488326.png\" alt=\"rId38\" width=\"260\" height=\"81\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488432.png\" alt=\"rId39\" width=\"88\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488532.png\" alt=\"rId40\" width=\"88\" height=\"83\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488666.png\" alt=\"rId41\" width=\"88\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488789.png\" alt=\"rId42\" width=\"90\" height=\"86\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488432.png\" alt=\"rId39\" width=\"88\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488532.png\" alt=\"rId40\" width=\"88\" height=\"84\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488666.png\" alt=\"rId41\" width=\"88\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488789.png\" alt=\"rId42\" width=\"89\" height=\"85\"></p>"],
                    solution_en: "<p>75.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488432.png\" alt=\"rId39\" width=\"88\" height=\"84\"></p>",
                    solution_hi: "<p>75.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488432.png\" alt=\"rId39\" width=\"88\" height=\"84\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. Orographic rainfall occurs when _______.</p>",
                    question_hi: "<p>76. पर्वतीय वर्षा (Orographic rainfall) तब होती है जब ______।</p>",
                    options_en: ["<p>a cyclone makes a landfall</p>", "<p>saturated air mass comes across a mountain</p>", 
                                "<p>hot air rises up in conventional currents</p>", "<p>the air masses collide at Intertropical Conversion Zone</p>"],
                    options_hi: ["<p>चक्रवात एक लैंडफॉल (भूस्खलन) बनाता है</p>", "<p>संतृप्त वायु द्रव्यमान पर्वत के पूर्णत: संपर्क में आता है</p>",
                                "<p>पारंपरिक धाराओं में गर्म हवा ऊपर उठती है</p>", "<p>अंतर-उष्ण कटिबंधीय रूपांतरण क्षेत्र में वायु द्रव्यमान टकराते हैं</p>"],
                    solution_en: "<p>76.(b) <strong>Saturated air mass comes across a mountain.</strong> Orographic rainfall occurs when the rain-bearing clouds from the sea, reach the land and move upwards along the mountain slopes. The upward ascension of clouds along steep mountain slopes leads to lower temperatures and further condensation in clouds.</p>",
                    solution_hi: "<p>76.(b) <strong>संतृप्त वायु द्रव्यमान पर्वत के पूर्णत: संपर्क में आता है।</strong> पर्वतीय वर्षा तब होती है जब समुद्र से वर्षा लाने वाले बादल भूमि पर पहुँचते हैं और पहाड़ी ढलानों के साथ ऊपर की ओर बढ़ते हैं। खड़ी पहाड़ी ढलानों के साथ बादलों के ऊपर की ओर बढ़ने से तापमान कम हो जाता है और बादलों में संघनन बढ़ जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. If the 7 digit number 504x5y3 is divisible by 11, then one of the values of the sum of x and y is:-</p>",
                    question_hi: "<p>77. यदि 7 अंकों की संख्या 504x5y3, 11 से विभाज्य है, तो x और y के योग का एक मान है:-</p>",
                    options_en: ["<p>7</p>", "<p>11</p>", 
                                "<p>5</p>", "<p>17</p>"],
                    options_hi: ["<p>7</p>", "<p>11</p>",
                                "<p>5</p>", "<p>17</p>"],
                    solution_en: "<p>77.(d) <br>Number = 504x5y3<br>For divisibility of 11,<br>(5 + 4 + 5 + 3) - (0 + x + y) = 0 or multiple of 11<br>17 - x - y = 0<br>x + y = 17<br>Sum of x and y = 17</p>",
                    solution_hi: "<p>77.(d)<br>संख्या = 504x5y3<br>11 की विभाज्यता के लिए,<br>(5 + 4 + 5 + 3) - (0 +x + y) = 0 या 11 का गुणज<br>17 - x - y = 0<br>x + y = 17<br>x और y का योग = 17</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. Who is called the father of taxonomy ?</p>",
                    question_hi: "<p>78. वर्गिकी का जनक किसे कहा जाता है ?</p>",
                    options_en: ["<p>Engler</p>", "<p>Eicher</p>", 
                                "<p>Bentham and Hooker</p>", "<p>Carl Linnaeus</p>"],
                    options_hi: ["<p>एंगलर</p>", "<p>आइशर</p>",
                                "<p>बेंथम और हूकर</p>", "<p>कार्ल लिनिअस</p>"],
                    solution_en: "<p>78.(d) Carl Linnaeus is called the father of taxonomy. Taxonomy is the science of naming, describing, classifying organisms and it includes all plants, animals and microorganisms.</p>",
                    solution_hi: "<p>78.(d) कार्ल लिनिअस को वर्गिकी का जनक कहा जाता है। टैक्सोनॉमी जीवों के नामकरण, वर्णन, वर्गीकरण का विज्ञान है और इसमें सभी पौधे, जानवर और सूक्ष्मजीव शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. Three friends arranged a party. Tanveer paid <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> as much as Yusuf paid. Yusuf paid <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> as much as Sachin paid. The fraction of the total expenditure by Yusuf was</p>",
                    question_hi: "<p>79. तीन दोस्तों ने एक पार्टी का आयोजन किया। तनवीर ने यूसुफ के भुगतान के <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> गुना अधिक भुगतान किया। युसुफ ने सचिन के जितना भुगतान किया उतना <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> भुगतान किया। यूसुफ द्वारा कुल व्यय का अंश कितना था ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>79.(b) Ratio of expenditure &ndash; Tanveer : Yusuf : Sachin = 2 : 3 : 6<br>The fraction of the total expenditure by Yusuf = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>79.(b) व्यय का अनुपात &ndash; तनवीर : यूसुफ : सचिन = 2 : 3 : 6<br>यूसुफ द्वारा कुल व्यय का अंश = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. Which of the following is NOT a coniferous tree ?</p>",
                    question_hi: "<p>80. निम्न में से कौन सा एक शंकुधारी वृक्ष नहीं है ?</p>",
                    options_en: ["<p>Spruce</p>", "<p>Cedar</p>", 
                                "<p>Jatropha</p>", "<p>pine</p>"],
                    options_hi: ["<p>स्प्रूस</p>", "<p>देवदार</p>",
                                "<p>जेट्रोफा</p>", "<p>पाइन</p>"],
                    solution_en: "<p>80.(c) <br>Jatropha is NOT a coniferous tree. Jatropha is a plant that occurs in the form of a small tree or shrub and belongs to the family of Euphorbia. Evergreen cone-shaped trees, growing needle- or scale-like leaves are called conifers. There are several categories of coniferous trees - Spruces, Firs, Pines, Cedars, Junipers, Hemlocks, Larches, Yews, Cypress.</p>",
                    solution_hi: "<p>80.(c)<br>जेट्रोफा एक शंकुधारी वृक्ष नहीं है। जेट्रोफा एक पौधा है जो एक छोटे पेड़ या झाड़ी के रूप में होता है और यूफोरबिया के परिवार से संबंधित होता है। सदाबहार शंकु के आकार के पेड़, बढ़ती सुई- या शल्क जैसी पत्तियों को शंकुधारी कहा जाता है। शंकुधारी पेड़ों की कई श्रेणियां हैं - स्प्रूस, फ़िर, पाइंस, देवदार, जुनिपर्स, हेमलॉक, लार्चेस, यस, सरू।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. Consider the given consumption function of country X and answer the following question.<br>C = 100 + 0.8Y<br>What is the value of autonomous consumption (A) and marginal propensity to consume (MPC) in the given equation ?</p>",
                    question_hi: "<p>81. देश X के दिए गए उपभोग फलन पर विचार करें और निम्नलिखित प्रश्न का उत्तर दें। <br>C = 100 + 0.8Y<br>दिए गए समीकरण में स्वायत्त उपभोग (A) और सीमांत उपभोग प्रवृत्ति (MPC) का मूल्य क्या है ?</p>",
                    options_en: ["<p>A = 0.8, MPC = 100</p>", "<p>A = 100, MPC = 80</p>", 
                                "<p>A = 80, MPC = 0.10</p>", "<p>A = 100, MPC = 0.8</p>"],
                    options_hi: ["<p>A = 0.8, MPC = 100</p>", "<p>A = 100, MPC = 80</p>",
                                "<p>A = 80, MPC = 0.10</p>", "<p>A = 100, MPC = 0.8</p>"],
                    solution_en: "<p>81.(d) <strong>A = 100, MPC = 0.8.</strong> The correct limit of marginal propensity to consume is 0 &lt; MPC &lt; 1. Marginal propensity to consume (MPC) refers to the proportion of extra income that a person spends instead of saving. The average propensity to consume is the percentage of income spent. Marginal propensity to save (MPS): An economic measure of how savings change, given a change in income.</p>",
                    solution_hi: "<p>81.(d) <strong>A = 100, MPC = 0.8 । </strong>सीमांत उपभोग प्रवृत्ति की सही सीमा 0 &lt; MPC &lt;1 है। उपभोग करने के लिए सीमांत प्रवृत्ति (MPC) अतिरिक्त आय के अनुपात को दर्शाता है जो एक व्यक्ति बचत के बजाय खर्च करता है। उपभोग करने की औसत प्रवृत्ति व्यय की गई आय का प्रतिशत है, जबकि बचत करने की औसत प्रवृत्ति बचत की गई आय का प्रतिशत है। सीमांत बचत प्रवृत्ति (MPS) : यह एक आर्थिक माप है कि आय में परिवर्तन होने पर बचत कैसे बदलती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. Select the correct mirror image of the given figure when the mirror is placed at MN as shown. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488906.png\" alt=\"rId43\" width=\"114\" height=\"149\"></p>",
                    question_hi: "<p>82. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए जो निचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672488906.png\" alt=\"rId43\" width=\"114\" height=\"149\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489034.png\" alt=\"rId44\" width=\"79\" height=\"112\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489156.png\" alt=\"rId45\" width=\"79\" height=\"106\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489316.png\" alt=\"rId46\" width=\"79\" height=\"114\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489419.png\" alt=\"rId47\" width=\"76\" height=\"108\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489034.png\" alt=\"rId44\" width=\"79\" height=\"112\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489156.png\" alt=\"rId45\" width=\"79\" height=\"106\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489316.png\" alt=\"rId46\" width=\"79\" height=\"114\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489419.png\" alt=\"rId47\" width=\"79\" height=\"113\"></p>"],
                    solution_en: "<p>82.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489316.png\" alt=\"rId46\" width=\"78\" height=\"112\"></p>",
                    solution_hi: "<p>82.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489316.png\" alt=\"rId46\" width=\"78\" height=\"112\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. What is the main objective of Fiscal Responsibility and Budget Management Act (FRBMA), 2003 ?</p>",
                    question_hi: "<p>83. राजकोषीय उत्तरदायित्व और बजट प्रबंधन अधिनियम (FRBMA), 2003 का मुख्य उद्देश्य क्या है ?</p>",
                    options_en: ["<p>To increase exports</p>", "<p>To increase excise duty</p>", 
                                "<p>To reduce fiscal deficit</p>", "<p>To reduce subsidies</p>"],
                    options_hi: ["<p>निर्यात बढ़ाना</p>", "<p>उत्पाद शुल्क बढ़ाना</p>",
                                "<p>राजकोषीय घाटे को कम करना</p>", "<p>अनुदान कम करना</p>"],
                    solution_en: "<p>83.(c) <strong>To reduce fiscal deficit.</strong> The Fiscal Responsibility and Budget Management (FRBM) Act is a law enacted by the Government of India in 2003 to ensure fiscal discipline &ndash; by setting targets including reduction of fiscal deficits and elimination of revenue deficit. Enacted - 26 August 2003. Introduced by - Mr.Yashwant Sinha.</p>",
                    solution_hi: "<p>83.(c) <strong>राजकोषीय घाटे को कम करना। </strong>राजकोषीय उत्तरदायित्व और बजट प्रबंधन (FRBM) अधिनियम, भारत सरकार द्वारा 2003 में राजकोषीय अनुशासन सुनिश्चित करने के लिए बनाया गया एक कानून है जिसका उद्देश्य राजकोषीय घाटे में कमी और राजस्व घाटे को समाप्त करने सहित लक्ष्य निर्धारित करके राजकोषीय अनुशासन सुनिश्चित करना है। अधिनियमित - 26 अगस्त 2003। श्री यशवंत सिन्हा द्वारा प्रस्तुत किया गया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. Which of the following is NOT a part of the circulatory system ?</p>",
                    question_hi: "<p>84. निम्नलिखित में से कौन संचार प्रणाली का हिस्सा नहीं है ?</p>",
                    options_en: ["<p>Blood</p>", "<p>Heart</p>", 
                                "<p>Large intestine</p>", "<p>Arteries</p>"],
                    options_hi: ["<p>रक्त</p>", "<p>हृदय</p>",
                                "<p>बड़ी आंत</p>", "<p>धमनी</p>"],
                    solution_en: "<p>84.(c) The large intestine is not a part of the circulatory system. The long tube - like organ that is connected to the small intestine at one end and the anus at the other. It consists of four parts: cecum, colon, rectum, and anal canal.</p>",
                    solution_hi: "<p>84.(c) बड़ी आंत संचार प्रणाली का हिस्सा नहीं है। लंबी नली जैसा अंग जो एक सिरे पर छोटी आंत से जुड़ा होता है और दूसरे सिरे पर गुदा द्वारा जुड़ जाता है इसे बड़ी आंत कहते हैं । इसमें चार भाग होते हैं: सीकुम, कोलन, रेक्टम और एनल कैनाल।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. The present age of Rahim is five times the present age of his daughter, Savita. Seven years from now, Rahim will be three times as old as Savita. What is the present age (in years) of Rahim ?</p>",
                    question_hi: "<p>85. रहीम की वर्तमान आयु, उसकी पुत्री सविता की वर्तमान आयु की पाँच गुना है। अब से सात वर्ष बाद रहीम की आयु, सविता की आयु की तीन गुनी होगी। रहीम की वर्तमान आयु (वर्ष में) क्या है ?</p>",
                    options_en: ["<p>45</p>", "<p>35</p>", 
                                "<p>40</p>", "<p>30</p>"],
                    options_hi: ["<p>45</p>", "<p>35</p>",
                                "<p>40</p>", "<p>30</p>"],
                    solution_en: "<p>85.(b)<br>Let the present age of Savita and Rahim be a year and b year.<br>According to question,<br>b = 5a --------(1)<br>(b + 7) = 3(a + 7)<br>(5a + 7) = 3a + 21<br>a = 7<br>Therefore, Present age of Rahim (b) = 5a = 35 years<br><strong>Short trick :</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489547.png\" alt=\"rId48\" width=\"172\" height=\"161\"><br>(5 <math display=\"inline\"><mo>&#215;</mo></math> 1) - (3 &times; 1) = (7 &times; 3) - (7 &times; 1)<br>2 units = 14 years <br>So the present age of Rahim(5 units) = 5<math display=\"inline\"><mo>&#215;</mo></math>7 = 35 years.</p>",
                    solution_hi: "<p>85.(b)<br>माना कि सविता और रहीम की वर्तमान आयु = a और b <br>प्रश्न के अनुसार,<br>b = 5a --------(1)<br>(b + 7) = 3(a + 7)<br>(5a + 7) = 3a + 21<br>a = 7<br>इसलिए, रहीम (b) की वर्तमान आयु = 5a = 35 वर्ष<br><strong>Short trick :</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489653.png\" alt=\"rId49\" width=\"172\" height=\"167\"><br>(5 <math display=\"inline\"><mo>&#215;</mo></math> 1) - (3 &times; 1) = (7 &times; 3) - (7 &times; 1)<br>2 units = 14 वर्ष <br>तो रहीम की वर्तमान आयु (5 units) = 5<math display=\"inline\"><mo>&#215;</mo></math>7 = 35 वर्ष</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. A major population of Israel is:</p>",
                    question_hi: "<p>86. इज़राइल की एक बड़ी आबादी है:</p>",
                    options_en: ["<p>Jewish</p>", "<p>Toda</p>", 
                                "<p>Lurish</p>", "<p>Beja</p>"],
                    options_hi: ["<p>यहूदी</p>", "<p>टोडा</p>",
                                "<p>लुरीश</p>", "<p>बेजा</p>"],
                    solution_en: "<p>86.(a) <br>A major population of Israel is jewish. Among the global Jewish population, the number of Jews in Israel is 7,080,000, while about 8.25 million live outside Israel. Jews originated as an ethnic and religious group in the Middle East during the second millennium BCE.</p>",
                    solution_hi: "<p>86.(a)<br>इज़राइल की एक बड़ी आबादी <strong>यहूदी </strong>है। वैश्विक यहूदी आबादी में, इज़राइल में यहूदियों की संख्या 7,080,000 है, जबकि लगभग 8.25 मिलियन इज़राइल के बाहर रहते हैं। यहूदियों की उत्पत्ति दूसरी सहस्राब्दी(millennium) ईसा पूर्व के दौरान मध्य पूर्व में एक जातीय और धार्मिक समूह के रूप में हुई थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. Select the option in which the given figure is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489769.png\" alt=\"rId50\" width=\"90\" height=\"97\"></p>",
                    question_hi: "<p>87. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति सन्&zwj;निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489769.png\" alt=\"rId50\" width=\"90\" height=\"97\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489874.png\" alt=\"rId51\" width=\"95\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489979.png\" alt=\"rId52\" width=\"95\" height=\"84\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490149.png\" alt=\"rId53\" width=\"95\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490265.png\" alt=\"rId54\" width=\"95\" height=\"87\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489874.png\" alt=\"rId51\" width=\"95\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672489979.png\" alt=\"rId52\" width=\"96\" height=\"85\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490149.png\" alt=\"rId53\" width=\"96\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490265.png\" alt=\"rId54\" width=\"95\" height=\"87\"></p>"],
                    solution_en: "<p>87.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490394.png\" alt=\"rId55\" width=\"102\" height=\"89\"></p>",
                    solution_hi: "<p>87.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490394.png\" alt=\"rId55\" width=\"102\" height=\"89\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. What will happen when you select the cell contents of a particular row in MS-Excel and then click the Delete button ?</p>",
                    question_hi: "<p>88. जब आप एमएस-एक्सेल (MS-Excel) में किसी विशेष पंक्ति (row) के सेल कंटेंट का चयन करते हैं और फिर डिलीट ( Delete) बटन पर क्लिक करते हैं तो क्या होगा ?</p>",
                    options_en: ["<p>First five cell entries will be deleted</p>", "<p>Only first three cells will be deleted</p>", 
                                "<p>Entire row will be deleted</p>", "<p>Entries in all the selected cells will be deleted</p>"],
                    options_hi: ["<p>पहली पाँच सेल प्रविष्टियाँ डिलीट हो जाएंगी</p>", "<p>केवल प्रथम तीन सेल डिलीट होंगे</p>",
                                "<p>संपूर्ण पंक्ति (row) डिलीट हो जाएगी</p>", "<p>सभी चयनित सेलों की प्रविष्टियाँ डिलीट हो जाएंगी</p>"],
                    solution_en: "<p>88.(d) <strong>Entries in all the selected cells will be deleted.</strong> Microsoft Excel is a spreadsheet editor developed by Microsoft for Windows, macOS, Android, iOS and iPadOS. It features calculation or computation capabilities, graphing tools, pivot tables, and a macro programming language called Visual Basic for Applications (VBA). Excel forms part of the Microsoft 365 suite of software.</p>",
                    solution_hi: "<p>88.(d) <strong>सभी चयनित सेलों की प्रविष्टियाँ डिलीट हो जाएंगी। </strong>Microsoft Excel एक स्प्रेडशीट एडिटर है जिसे Microsoft ने Windows, macOS, Android, iOS और iPadOS के लिए विकसित किया है। इसमें गणना या संगणना क्षमताएँ, ग्राफ़िंग टूल, पिवट टेबल और विजुअल बेसिक फॉर एप्लीकेशन (VBA) नामक मैक्रो प्रोग्रामिंग भाषा शामिल है। Excel, माइक्रोसॉफ्ट 365 सॉफ़्टवेयर के समूह का हिस्सा है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. A certain number of people are sitting in a row, facing north. B sits fourth to the right of E. C sits third to the right of B. A sits sixth to the left of E. There are only two persons between C and D. If no other person is sitting in the row, what is the total number of persons seated ?</p>",
                    question_hi: "<p>89. किसी निश्चित संख्या में व्यक्ति, एक पंक्ति में उत्तर की ओर मुख करके बैठे हैं। B, E के दाईं ओर चौथे स्थान पर बैठा है। C, B के दाईं ओर तीसरे स्थान पर बैठा है। A, E के बाईं ओर छठे स्थान पर बैठा है। C और D के बीच केवल दो व्यक्ति हैं। यदि पंक्ति में कोई अन्य व्यक्ति नहीं बैठता है, तो बैठे हुए व्यक्तियों की कुल संख्या कितनी है ?</p>",
                    options_en: ["<p>20</p>", "<p>12</p>", 
                                "<p>9</p>", "<p>17</p>"],
                    options_hi: ["<p>20</p>", "<p>12</p>",
                                "<p>9</p>", "<p>17</p>"],
                    solution_en: "<p>89.(d)<br>By arranging people as per given condition we get , <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490497.png\" alt=\"rId56\" width=\"304\" height=\"63\"><br>From the above diagram we can say that the total number of persons seated in a row = 5 + 3 + 2 + 2 + 5(ABCDE) = 17.</p>",
                    solution_hi: "<p>89.(d)<br>दी गई स्थिति के अनुसार लोगों को व्यवस्थित करने पर हमें प्राप्त होता है,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490617.png\" alt=\"rId57\" width=\"304\" height=\"63\"><br>उपरोक्त आरेख से हम कह सकते हैं कि एक पंक्ति में बैठे व्यक्तियों की कुल संख्या = 5 + 3 + 2 + 2 + 5(ABCDE) = 17.</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. Where is the headquarters of the United Nations Security Council located ?</p>",
                    question_hi: "<p>90. संयुक्त राष्ट्र सुरक्षा परिषद का मुख्यालय कहाँ स्थित है ?</p>",
                    options_en: ["<p>New Jersey</p>", "<p>Chicago</p>", 
                                "<p>New York</p>", "<p>Boston</p>"],
                    options_hi: ["<p>न्यू जर्सी</p>", "<p>शिकागो</p>",
                                "<p>न्यूयॉर्क</p>", "<p>बोस्टन</p>"],
                    solution_en: "<p>90.(c) <strong>New York. United Nations Security Council (UNSC) - </strong>It was established on 24 October 1945, charged with ensuring international peace and security. <strong>Composition -</strong> 15 members (5 permanent). <strong>Permanent members -</strong> China, France, Russia, the United Kingdom, and the United States. <strong>Ten non-permanent members -</strong>Elected for two-year terms.</p>",
                    solution_hi: "<p>90.(c) <strong>न्यूयॉर्क। संयुक्त राष्ट्र सुरक्षा परिषद (UNSC) - </strong>इसकी स्थापना 24 अक्टूबर 1945 को, अंतर्राष्ट्रीय शांति और सुरक्षा सुनिश्चित करने के लिए की गई थी। <strong>संरचना - </strong>15 सदस्य (5 स्थायी)। <strong>स्थायी सदस्य -</strong> चीन, फ्रांस, रूस, यूनाइटेड किंगडम और संयुक्त राज्य अमेरिका। <strong>दस गैर-स्थायी सदस्य - </strong>दो साल के कार्यकाल के लिए चुने जाते है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. Study the given diagram carefully and answer the question. The numbers in different sections indicate the number of persons. \"Managers\" are represented by the circle, B. Tech degree holders\" are represented by the square, and \"MBA degree holders\' are represented by the triangle. How many managers are there who are B. Tech degree holders but not MBA degree holders ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490732.png\" alt=\"rId58\" width=\"130\" height=\"125\"></p>",
                    question_hi: "<p>91. दिए गए आरेख का ध्यानपूर्वक अध्ययन करें और प्रश्न का उत्तर दें। विभिन्न वर्गों में संख्या व्यक्तियों की संख्या दर्शाती है। \"प्रबंधकों\" को सर्कल द्वारा दर्शाया गया है, B. Tech डिग्री धारकों को वर्ग द्वारा दर्शाया गया है, और \"MBA डिग्री धारकों\" को त्रिकोण द्वारा दर्शाया गया है। ऐसे कितने प्रबंधक हैं जो बी.टेक डिग्री धारक हैं लेकिन एमबीए डिग्री धारक नहीं हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490732.png\" alt=\"rId58\" width=\"130\" height=\"125\"></p>",
                    options_en: ["<p>2</p>", "<p>1</p>", 
                                "<p>5</p>", "<p>6</p>"],
                    options_hi: ["<p>2</p>", "<p>1</p>",
                                "<p>5</p>", "<p>6</p>"],
                    solution_en: "<p>91.(b)<br>Total number of person who are Manager and B.Tech degree holder but not MBA degree holder = 1</p>",
                    solution_hi: "<p>91.(b)<br>प्रबंधक और B. Tech डिग्री धारक लेकिन MBA डिग्री धारक नहीं होने वाले व्यक्तियों की कुल संख्या = 1</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. Mahabalipuram temple was built under the reign of which of the following dynasties ?</p>",
                    question_hi: "<p>92. महाबलीपुरम मंदिर का निर्माण निम्नलिखित में से किस राजवंश के शासनकाल में हुआ था ?</p>",
                    options_en: ["<p>Rashtrakuta</p>", "<p>Pallava</p>", 
                                "<p>Pratihara</p>", "<p>Chola</p>"],
                    options_hi: ["<p>राष्ट्रकूट</p>", "<p>पल्लव</p>",
                                "<p>प्रतिहार</p>", "<p>चोल</p>"],
                    solution_en: "<p>92.(b) <strong>Pallava</strong>. This dynasty existed from 275 CE to 897 CE, ruling a significant portion of the Deccan, also known as Tondaimandalam. Capital - Kanchipuram. Common languages - Tamil (Official), Prakrit (Rarely), Sanskrit. Mahabalipuram Temple is located near Chennai in Tamil Nadu. The whole structure is constructed using granite.</p>",
                    solution_hi: "<p>92.(b) <strong>पल्लव। </strong>यह राजवंश 275 ई. से 897 ई. तक अस्तित्व में रहा, जिसने दक्कन के एक महत्वपूर्ण हिस्से पर शासन किया, जिसे तोंडईमंडलम के नाम से भी जाना जाता है। राजधानी - कांचीपुरम। सामान्य भाषाएँ - तमिल (आधिकारिक), प्राकृत (कभी-कभार), संस्कृत। महाबलीपुरम मंदिर तमिलनाडु में चेन्नई के पास स्थित है। संपूर्ण संरचना का निर्माण ग्रेनाइट का उपयोग करके किया गया है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. A college offers two papers to a student such that if a student studies Philosophy, he or she must study French. One who studies History cannot choose Psychology. French students cannot study History. French students cannot opt for Spanish. Only History students can study Political Science. No Psychology student has opted for Sociology. If so, what other discipline will a Psychology student have to study ?</p>",
                    question_hi: "<p>93. एक कॉलेज एक छात्र को दो विषय इस प्रकार प्रदान करता है कि यदि कोई छात्र दर्शनशास्त्र का अध्ययन करता है। उसे फ्रेंच का अध्ययन करना होगा । इतिहास का अध्ययन करने वाला मनोविज्ञान का चयन नहीं कर सकता। फ्रेंच छात्र इतिहास का अध्ययन नहीं कर सकते। फ्रेंच छात्र ,स्पेनिश का विकल्प नहीं चुन सकते। केवल इतिहास के छात्र ही राजनीति विज्ञान का अध्ययन कर सकते हैं। मनोविज्ञान के किसी भी छात्र ने समाजशास्त्र का विकल्प नहीं चुना है। यदि ऐसा है, तो मनोविज्ञान के छात्र को और कौन-से विषय का अध्ययन करना होगा ?</p>",
                    options_en: ["<p>French</p>", "<p>Sociology</p>", 
                                "<p>Political Science</p>", "<p>Spanish</p>"],
                    options_hi: ["<p>फ़्रेंच</p>", "<p>समाज शास्त्र</p>",
                                "<p>राजनीति विज्ञान</p>", "<p>स्पेनिश</p>"],
                    solution_en: "<p>93.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490849.png\" alt=\"rId59\" width=\"186\" height=\"83\"></p>",
                    solution_hi: "<p>93.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672490958.png\" alt=\"rId60\" width=\"204\" height=\"89\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. The process of taking out stored results out of physical memory of Computers is known as</p>",
                    question_hi: "<p>94. कंप्यूटर की भौतिक मेमोरी से संग्रहित परिणाम निकालने की प्रक्रिया कहलाती है ?</p>",
                    options_en: ["<p>input process</p>", "<p>processing</p>", 
                                "<p>programming</p>", "<p>output process</p>"],
                    options_hi: ["<p>इनपुट प्रक्रिया</p>", "<p>प्रोसेसिंग</p>",
                                "<p>प्रोग्रामिंग</p>", "<p>आउटपुट प्रक्रिया</p>"],
                    solution_en: "<p>94.(d)<strong> Output processes. Examples of Output units - </strong>Microphones, Monitors, Printers, headphones, etc.<strong> Input devices -</strong> They are used to provide data and instructions to a computer system. Example: Keyboard, Mouse, Touchpad, Scanner, Digital Camera etc. <strong>Processing Unit -</strong> It handles all the instructions received by the computer and processes them to produce the desired results. <strong>Programming - </strong>It is the process of writing, testing, and maintaining a set of instructions (code) that a computer can execute to perform specific tasks or functions.</p>",
                    solution_hi: "<p>94.(d) <strong>आउटपुट प्रक्रिया। आउटपुट यूनिट के उदाहरण - </strong>माइक्रोफोन, मॉनिटर, प्रिंटर, हेडफ़ोन आदि।<strong> इनपुट डिवाइस - </strong>इनका उपयोग कंप्यूटर सिस्टम को डेटा और निर्देश प्रदान करने के लिए किया जाता है। उदाहरण: कीबोर्ड, माउस, टचपैड, स्कैनर, डिजिटल कैमरा आदि। <strong>प्रोसेसिंग यूनिट - </strong>यह कंप्युटर द्वारा निर्देशों को प्राप्त करता है और वांछित परिणाम के लिए संसाधित करता है।<strong> प्रोग्रामिंग - </strong>यह निर्देशों (कोड) के एक सेट को लिखने, टेस्टिंग करने और मैनटैनिंग रखने की प्रक्रिया है जिसे कंप्यूटर स्पेसिफिक टास्कस (specific task) या फंक्शन्स (functions) के प्रदर्शन (perform) करने लिए एक्सेक्यूट कर सकता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. The speed of a boat in still water is 15 km/h. The speed of the current is 3 km/h. The difference between the time taken for upstream and downstream to complete two trips (i.e from one end to the other coming back and repeating the same again) is 10 minutes. What is the distance between the two ends ?</p>",
                    question_hi: "<p>95. शांत जल में एक नाव की गति 15 km/h है। धारा की गति 3 km/h है। धारा के प्रतिकूल और धारा के अनुकूल को दो बार यात्रा को पूरा करने में लगने वाले समय के बीच का अंतर (अर्थात एक छोर से दूसरे छोर तक वापस आने और फिर से वही दोहराने में) 10 मिनट है। दोनों छोर के बीच की दूरी कितनी है ?</p>",
                    options_en: ["<p>2 km</p>", "<p>3.5 km</p>", 
                                "<p>2.5 km</p>", "<p>3 km</p>"],
                    options_hi: ["<p>2 km</p>", "<p>3.5 km</p>",
                                "<p>2.5 km</p>", "<p>3 km</p>"],
                    solution_en: "<p>95.(d) Downstream speed = 15 + 3 = 18 km/h<br>Upstream speed = 15 - 3 = 12 km/h<br>Distance travelled in both cases are equal, <math display=\"inline\"><msub><mrow><mi>D</mi></mrow><mrow><mi>d</mi></mrow></msub></math> = D<sub>u</sub><br><math display=\"inline\"><mo>&#8658;</mo></math>18 &times; T<sub>d </sub>= 12 &times; T<sub>u</sub><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>T</mi><mi>d</mi></msub><msub><mi>T</mi><mi>u</mi></msub></mfrac><mo>=</mo><mfrac><mn>12</mn><mn>18</mn></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>Difference of time in ratio = 1, which is equal to 5 minutes (per round)<br><math display=\"inline\"><mo>&#8658;</mo></math> Time (upstream) = 3(in ratio) = 3 &times; 5 = 15 minutes = 0.25 hour<br>Distance = 12<math display=\"inline\"><mo>&#215;</mo></math>0.25 = 3 km</p>",
                    solution_hi: "<p>95.(d) धारा के अनुप्रवाह गति = 15 + 3 = 18 km/h<br>धारा के प्रतिकूल गति = 15 - 3 = 12 km/h<br>दोनों मामलों में तय की गई दूरी बराबर है, <math display=\"inline\"><msub><mrow><mi>D</mi></mrow><mrow><mi>d</mi></mrow></msub></math> = D<sub>u</sub><br><math display=\"inline\"><mo>&#8658;</mo></math>18 &times; T<sub>d </sub>= 12 &times; T<sub>u</sub><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>T</mi><mi>d</mi></msub><msub><mi>T</mi><mi>u</mi></msub></mfrac><mo>=</mo><mfrac><mn>12</mn><mn>18</mn></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>अनुपात में समय का अंतर = 1, जो 5 मिनट के बराबर है,<br>समय (धारा के प्रतिकूल) = 3 (अनुपात में) = 3<math display=\"inline\"><mo>&#215;</mo></math>5 = 15 मिनट = 0.25 घंटा<br>दूरी = 12<math display=\"inline\"><mo>&#215;</mo></math>0.25 = 3 km</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. Which of the following places is known for copper mines in India ?</p>",
                    question_hi: "<p>96. निम्नलिखित में से कौन सा स्थान भारत में तांबे की खानों के लिए जाना जाता है ?</p>",
                    options_en: ["<p>Khetri</p>", "<p>Kudremukh</p>", 
                                "<p>Kolar</p>", "<p>Ratnagiri</p>"],
                    options_hi: ["<p>खेतड़ी</p>", "<p>कुद्रेमुख</p>",
                                "<p>कोलार</p>", "<p>रत्नागिरी</p>"],
                    solution_en: "<p>96.(a)<br><strong>Khetri</strong> is a town in the Jhunjhunu district of Rajasthan. Khetri is known for copper mines in India. Kudremukh is a mountain range and name of a peak located in Chikkamagaluru district, in Karnataka. It is also the name of a small hill station iron ore mining town situated near the mountain. Kolar district is a district in the state of Karnataka. Due to the discovery of the Kolar Gold Fields, the district has become known as the \"Golden Land\" of India. Ratnagiri is a port town in Maharashtra.</p>",
                    solution_hi: "<p>96.(a)<br><strong>खेतड़ी </strong>राजस्थान के झुंझुनू जिले का एक कस्बा है। खेतड़ी भारत में तांबे की खानों के लिए जाना जाता है। कुद्रेमुख एक पर्वत श्रृंखला है और कर्नाटक में चिक्कमगलुरु जिले में स्थित एक चोटी का नाम है। यह पहाड़ के पास स्थित एक छोटे हिल स्टेशन लौह अयस्क खनन शहर का नाम भी है। कोलार जिला कर्नाटक राज्य का एक जिला है। कोलार सोने का क्षेत्र (गोल्ड फील्ड्स) की खोज के कारण, जिला भारत की \"स्वर्ण भूमि\" के रूप में जाना जाता है। रत्नागिरी महाराष्ट्र का एक बंदरगाह शहर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. Which of the following is the festival of Kerala that heralds the harvest season, lasts for 10 days, and has snake boat races etc ?</p>",
                    question_hi: "<p>97. निम्नलिखित में से केरल का वह त्योहार कौन-सा है जो फसल के मौसम की शुरुआत करता है, 10 दिनों तक चलता है और जिसके दौरान सर्पाकार नौका दौड़ आदि होती है ?</p>",
                    options_en: ["<p>Vishu</p>", "<p>Thiruvathira</p>", 
                                "<p>Thrissur Pooram</p>", "<p>Onam</p>"],
                    options_hi: ["<p>विशु (Vishu)</p>", "<p>थिरुवातीर (Thiruvathira)</p>",
                                "<p>त्रिशूर पूरम (Thrissur Pooram)</p>", "<p>ओणम (Onam)</p>"],
                    solution_en: "<p>97.(d) <strong>Onam. </strong>Onam is the biggest festival in the Indian state of Kerala. Onam Festival falls during the Malayali month of Chingam (Aug - Sep) and marks the homecoming of legendary King Mahabali. Other Festivals of Kerala : Thrissur Pooram Festival, Attukal Pongala Festival, Navarathri Festival, Makaravilakku Festival, Kerala Village Fair, Aluva Sivarathri Festival, Vishu Festival.</p>",
                    solution_hi: "<p>97.(d) <strong>ओणम।</strong> ओणम भारत के केरल राज्य का सबसे बड़ा त्योहार है। ओणम महोत्सव मलयाली महीने चिंगम (अगस्त-सितंबर) के दौरान आता है और यह प्रसिद्ध राजा महाबली की घर वापसी का प्रतीक है। केरल के अन्य त्योहार : त्रिशूर पूरम महोत्सव, अट्टुकल पोंगल महोत्सव, नवरात्रि महोत्सव, मकरविलक्कू महोत्सव, केरल ग्राम मेला, अलुवा शिवरात्रि महोत्सव, विशु महोत्सव।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. In September 2022 the book &lsquo;Ambedkar and Modi: Reformer&rsquo;s Ideas Performer&rsquo;s Implementation&rsquo; has been written by which of the following personalities ?</p>",
                    question_hi: "<p>98. सितंबर 2022 में, \'अंबेडकर एंड मोदी: रिफॉर्मर्स आइडियाज़ परफॉर्मर्स इम्प्लीमेंटेशन (Ambedkar and Modi: Reformer&rsquo;s Ideas Performer&rsquo;s Implementation)\' पुस्तक निम्नलिखित में से किस शख़्सियत द्वारा लिखी गई है ?</p>",
                    options_en: ["<p>Sarvepalli Radhakrishnan</p>", "<p>Pratibha Patil</p>", 
                                "<p>APJ Abdul Kalam</p>", "<p>Ram Nath Kovind</p>"],
                    options_hi: ["<p>सर्वपल्ली राधाकृष्णन</p>", "<p>प्रतिभा पाटिल</p>",
                                "<p>ए.पी.जे. अब्दुल कलाम</p>", "<p>राम नाथ कोविंद</p>"],
                    solution_en: "<p>98.(d) <strong>Ram Nath Kovind.</strong> He is an Indian politician and lawyer who served as the 14th President of India from 2017 to 2022. Pratibha Devisingh Patil is an Indian politician and lawyer who served as the 12th President of India from 2007 to 2012. Dr.A.P.J.Abdul Kalam was an Indian aerospace scientist and statesman who served as the 11th president of India from 2002 to 2007. Sarvepalli Radhakrishnan (The first vice president of India) served as the second president of India from 1962 to 1967.</p>",
                    solution_hi: "<p>98.(d) <strong>राम नाथ कोविंद </strong>एक भारतीय राजनीतिज्ञ और वकील हैं, जिन्होंने 2017 से 2022 तक भारत के 14वें राष्ट्रपति के रूप में कार्य किया। प्रतिभा देवीसिंह पाटिल एक भारतीय राजनीतिज्ञ और वकील हैं, जिन्होंने 2007 से 2012 तक भारत के 12वें राष्ट्रपति के रूप में कार्य किया। डॉ. ए.पी.जे.अब्दुल कलाम एक भारतीय एयरोस्पेस वैज्ञानिक और राजनेता थे, जिन्होंने 2002 से 2007 तक भारत के 11वें राष्ट्रपति के रूप में कार्य किया। सर्वपल्ली राधाकृष्णन (भारत के प्रथम उपराष्ट्रपति) ने 1962 से 1967 तक भारत के दूसरे राष्ट्रपति के रूप में कार्य किया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672491080.png\" alt=\"rId61\" width=\"146\" height=\"106\"></p>",
                    question_hi: "<p>99. नीचे दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672491080.png\" alt=\"rId61\" width=\"146\" height=\"106\"></p>",
                    options_en: ["<p>8</p>", "<p>5</p>", 
                                "<p>7</p>", "<p>6</p>"],
                    options_hi: ["<p>8</p>", "<p>5</p>",
                                "<p>7</p>", "<p>6</p>"],
                    solution_en: "<p>99.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672491220.png\" alt=\"rId62\" width=\"148\" height=\"122\"><br>There are 6 triangles in the given figure :- ABC, CEF, HIF, KFG, JKL, DEG</p>",
                    solution_hi: "<p>99.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727672491220.png\" alt=\"rId62\" width=\"148\" height=\"122\"><br>दी गई आकृति में 6 त्रिभुज हैं:- ABC, CEF, HIF, KFG, JKL, DEG</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Which folk music does NOT belong to Nagaland ?</p>",
                    question_hi: "<p>100. इनमें से कौन-सा लोक संगीत नागालैंड से संबंधित नहीं है ?</p>",
                    options_en: ["<p>Nyioga</p>", "<p>Heliamleu</p>", 
                                "<p>Hereileu</p>", "<p>Neuleu</p>"],
                    options_hi: ["<p>निओगा (Nyioga)</p>", "<p>हेलियमलियू (Heliamleu)</p>",
                                "<p>हेरेइलियू (Hereileu)</p>", "<p>नेउलियू (Neuleu)</p>"],
                    solution_en: "<p>100.(a) <strong>Nyioga. </strong>It is sung when a marriage ceremony is concluded and the bride\'s family returns leaving the bride in her home in Arunachal Pradesh. Nagaland\'s folk music is known for its enchanting melodies and lyrics that tell stories of love, nature, bravery, and cultural identity. Nagaland famous folk dance: Hereileu, Chang Lo, Tetseo Sisters, Tati, Theku, Asem, Kuki dance.</p>",
                    solution_hi: "<p>100.(a) <strong>निओगा।</strong> यह अरुणाचल प्रदेश में तब गाया जाता है जब विवाह समारोह संपन्न हो जाता है और दुल्हन का परिवार दुल्हन को उसके घर छोड़कर वापस लौट आता है। नागालैंड का लोक संगीत अपनी मनमोहक धुनों और गीतों के लिए जाना जाता है जो प्रेम, प्रकृति, बहादुरी और सांस्कृतिक पहचान की कहानियाँ बताते हैं। नागालैंड के प्रसिद्ध लोक नृत्य: हेरेइलु, चांग लो, टेट्सियो सिस्टर्स, ताती, थेकू, असेम, कुकी नृत्य।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>