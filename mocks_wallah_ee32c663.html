<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>You must avoid riding in a crowded bus / or travelling in a metro / during rush hour / as both are quiet unpleasant experiences.</p>",
                    question_hi: "<p>1. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>You must avoid riding in a crowded bus / or travelling in a metro / during rush hour / as both are quiet unpleasant experiences.</p>",
                    options_en: ["<p>You must avoid riding in a crowded bus</p>", "<p>during rush hour</p>", 
                                "<p>as both are quiet unpleasant experiences</p>", "<p>or travelling in a metro</p>"],
                    options_hi: ["<p>You must avoid riding in a crowded bus</p>", "<p>during rush hour</p>",
                                "<p>as both are quiet unpleasant experiences</p>", "<p>or travelling in a metro</p>"],
                    solution_en: "<p>1.(c) as both are quiet unpleasant experiences.<br>Quiet is an adjective that means with very little or no noise &amp; &lsquo;Quite&rsquo; is an adverb that is used for emphasizing something. However, the given sentence does not talk about any sound or noise. Hence, &lsquo;both are <strong>quite </strong>unpleasant experiences&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(c) as both are quiet unpleasant experiences.<br>&lsquo;Quiet&rsquo; एक adjective है जिसका अर्थ है &lsquo;बहुत कम&rsquo; या &lsquo;बिल्कुल शोर न होना&rsquo; तथा &lsquo;Quite&rsquo; एक adverb है जिसका प्रयोग किसी बात पर जोर (emphasizing) देने के लिए किया जाता है। हालाँकि, दिया गया sentence किसी भी sound या noise के बारे में बात नहीं करता है। अतः, both are <strong>quite </strong>unpleasant experiences&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>Had you /not reached in time, / we will have / lost our lives.</p>",
                    question_hi: "<p>2. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>Had you /not reached in time, / we will have / lost our lives.</p>",
                    options_en: ["<p>lost our lives</p>", "<p>not reached in time</p>", 
                                "<p>Had you</p>", "<p>we will have</p>"],
                    options_hi: ["<p>lost our lives</p>", "<p>not reached in time</p>",
                                "<p>Had you</p>", "<p>we will have</p>"],
                    solution_en: "<p>2.(d) we will have.<br>&ldquo;Had + V<sub>3</sub> _________ would have + V<sub>3</sub>&rdquo; is grammatically the correct structure for the given sentence. Hence, &lsquo;we would have&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(d) we will have.<br>&ldquo;Had + V<sub>3</sub> _________ would have + V<sub>3</sub>&rdquo; दिए गए sentence के लिए सही grammatically structure है। अतः, &lsquo;we would have&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. The following sentence has been split into four segments. Identify the segment that  contains a grammatical error. <br />The woodcutter felled / some trees / with hardly / many effort at all. ",
                    question_hi: "3. The following sentence has been split into four segments. Identify the segment that <br />contains a grammatical error. <br />The woodcutter felled / some trees / with hardly / many effort at all. ",
                    options_en: [" some trees ", " with hardly ", 
                                " many effort at all ", " The woodcutter felled "],
                    options_hi: [" some trees ", " with hardly ",
                                " many effort at all ", " The woodcutter felled "],
                    solution_en: "3.(c) many effort at all. <br />The phrase ‘hardly any’ means almost none. Similarly, the given sentence states that the woodcutter felled some trees with almost no effort at all. However, ‘many’ will never come with words like ‘hardly, scarcely, barely’ because these words are of opposite sense. Hence, ‘any effort at all’ is the most appropriate answer.",
                    solution_hi: "3.(c) many effort at all. <br />Phrase ‘hardly any’ का अर्थ है ‘लगभग न के बराबर’। इसी तरह, दिए गए sentence में कहा गया है कि लकड़हारे (woodcutter) ने कुछ वृक्ष लगभग बिना किसी प्रयास के काट दिए। हालाँकि, ‘many’ word कभी भी ‘hardly, scarcely, barely’ जैसे words के साथ नहीं आएगा क्योंकि इन words का अर्थ विपरीत होता है। अतः, ‘any effort at all’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. The following sentence has been divided into parts. One of them may contain an error.  Select the part that contains the error from the given options. If you don’t find any error, mark ‘No error’ in your answer. <br />The river appears to have / got its name / from the town nearby. ",
                    question_hi: "4. The following sentence has been divided into parts. One of them may contain an error. <br />Select the part that contains the error from the given options. If you don’t find any error, mark ‘No error’ in your answer. <br />The river appears to have / got its name / from the town nearby. ",
                    options_en: [" The river appears to have ", " No error ", 
                                " got its name ", " from the town nearby "],
                    options_hi: [" The river appears to have ", " No error ",
                                " got its name ", " from the town nearby "],
                    solution_en: "4.(b) No error. <br />The sentence is grammatically correct.",
                    solution_hi: "4.(b) No error. <br />दिया गया sentence, grammatically सही है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "5. The following sentence has been divided into parts. One of them contains an error.  Select the part that contains the error from the given options. <br />Lack of required / vitamins and minerals / lead against / several complications / in the human body. ",
                    question_hi: "5. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />Lack of required / vitamins and minerals / lead against / several complications / in the human body. ",
                    options_en: [" in the human body ", " Lack of required ", 
                                " lead against ", " several complications<br /> "],
                    options_hi: [" in the human body ", " lack of required ",
                                " lead against ", " several complications "],
                    solution_en: "5.(c) lead against. <br />‘Lead’ will take the preposition ‘to’ with it & the subject ‘lack of’ is singular so it will take a singular form of lead which is ‘leads’. Hence, ‘leads to’ is the most appropriate answer.",
                    solution_hi: "5.(c)  lead against. <br />‘Lead’ के साथ preposition ‘to’ का प्रयोग होगा तथा subject ‘lack of’ singular है, इसलिए इसके साथ ‘lead’ के singular form ‘leads’ का प्रयोग किया जाएगा। अतः, ‘leads to’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "6. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The Dussehra celebrations / in Mysore / this year / are grandest than / in any other part / of the state. ",
                    question_hi: "6. The following sentence has been divided into parts. One of them contains an error.  Select the part that contains the error from the given options. <br />The Dussehra celebrations / in Mysore / this year / are grandest than / in any other part / of the state. ",
                    options_en: [" in Mysore ", " The Dussehra celebrations ", 
                                " are grandest than ", " any other part "],
                    options_hi: [" in Mysore ", " The Dussehra celebrations ",
                                " are grandest than ", " any other part "],
                    solution_en: "6.(c) are grandest than. <br />We always use ‘than’ after a comparative degree when we do a comparison between two things. Similarly in the given sentence, the Dussehra celebrations in Mysore are compared with the other parts of Kerala. However, ‘grander’ is the comparative degree of ‘grand’. Hence, ‘are grander than’ is the most appropriate answer.",
                    solution_hi: "6.(c) are grandest than. <br />जब हम दो चीज़ों के बीच comparison करते हैं तो हम हमेशा comparative degree के बाद ‘than’ का प्रयोग करते हैं। इसी तरह दिए गए sentence में, Mysore में Dussehra उत्सव की तुलना Kerala के अन्य हिस्सों से की गई है। हालाँकि, ‘grand’ की comparative degree, ‘grander’ है। अतः, ‘are grander than’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>It have been only / through writing / that men have been able / to spread their ideas to mankind.</p>",
                    question_hi: "<p>7. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>It have been only / through writing / that men have been able / to spread their ideas to mankind.</p>",
                    options_en: ["<p>It have been only</p>", "<p>to spread their ideas to mankind</p>", 
                                "<p>through writing</p>", "<p>that men have been able</p>"],
                    options_hi: ["<p>It have been only</p>", "<p>to spread their ideas to mankind</p>",
                                "<p>through writing</p>", "<p>that men have been able</p>"],
                    solution_en: "<p>7.(a) It have been only. <br>&lsquo;It&rsquo; is a <strong>3<sup>rd</sup> person singular</strong> pronoun that always takes &lsquo;<strong>has</strong>&rsquo;(singular) in <strong>present perfect tense</strong>. However, <span style=\"text-decoration: underline;\">have</span> is a plural &amp; <span style=\"text-decoration: underline;\">has</span> is a singular verb. Hence, &lsquo;have&rsquo; will be replaced by &lsquo;<strong>has</strong>&rsquo; to make the given sentence grammatically correct.</p>",
                    solution_hi: "<p>7.(a) It have been only. <br>&lsquo;It&rsquo; एक <strong>3<sup>rd</sup> person singular</strong> pronoun है जिसके साथ <strong>present perfect tense</strong> में हमेशा &lsquo;<strong>has</strong>&rsquo;(singular) का प्रयोग होता है। हालाँकि, <span style=\"text-decoration: underline;\">have</span> एक plural तथा <span style=\"text-decoration: underline;\">has</span>, singular verb है। अतः, दिए गए sentence को grammatically सही बनाने के लिए &lsquo;have&rsquo; के स्थान पर &lsquo;<strong>has</strong>&rsquo; का प्रयोग करना होगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The Principal requested / the teacher&rsquo;s / to monitor / and take care of the small children.</p>",
                    question_hi: "<p>8. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The Principal requested / the teacher&rsquo;s / to monitor / and take care of the small children.</p>",
                    options_en: ["<p>The Principal requested</p>", "<p>the teacher&rsquo;s</p>", 
                                "<p>to monitor</p>", "<p>and take care of the small children</p>"],
                    options_hi: ["<p>The Principal requested</p>", "<p>the teacher&rsquo;s</p>",
                                "<p>to monitor</p>", "<p>and take care of the small children</p>"],
                    solution_en: "<p>8.(b) the teacher&rsquo;s. <br>We use <span style=\"text-decoration: underline;\">apostrophes</span>(&lsquo;s) when we want to show <span style=\"text-decoration: underline;\">possession</span>, for example that is Dheeraj&rsquo;s book. But according to the given sentence, the Principal requested the teachers(plural) to monitor and take care of the small children. So, there is no case of possession. Hence, &lsquo;the teachers&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(b) the teacher&rsquo;s. <br>हम <span style=\"text-decoration: underline;\">apostrophes</span>(&lsquo;s) का प्रयोग तब करते हैं जब हम स्वामित्व (<span style=\"text-decoration: underline;\">possession</span>) दिखाना चाहते हैं, उदाहरण के लिए, that is Dheeraj&rsquo;s book. लेकिन दिए गए sentence के अनुसार, Principal ने teachers(plural) से छोटे बच्चों को monitor करने तथा उनकी देखभाल करने का अनुरोध किया। इसलिए, यहाँ possession का कोई case नही है। अतः, &lsquo;the teachers&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>&ldquo;Would you liked / to have / some fries / along with your coffee, / sir?&rdquo; / asked the waiter.</p>",
                    question_hi: "<p>9. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>&ldquo;Would you liked / to have / some fries / along with your coffee, / sir?&rdquo; / asked the waiter.</p>",
                    options_en: ["<p>asked the waiter</p>", "<p>along with your coffee</p>", 
                                "<p>Would you liked</p>", "<p>some fries</p>"],
                    options_hi: ["<p>asked the waiter</p>", "<p>along with your coffee</p>",
                                "<p>Would you liked</p>", "<p>some fries</p>"],
                    solution_en: "<p>9.(c) Would you liked.<br>The interrogative statement in the given sentence is in the present tense so the verb must be used in its present/V<sub>1</sub> form (like) and not in the past form(liked). Hence, &lsquo;<strong>liked</strong>&rsquo; will be replaced by &lsquo;<strong>like</strong>&rsquo; to make the given sentence grammatically correct.</p>",
                    solution_hi: "<p>9.(c) Would you liked.<br>दिए गए sentence में interrogative statement, present tense में है, इसलिए verb का प्रयोग उसके present/V<sub>1</sub> form (like) में होना चाहिए, न कि past form (liked) में। अतः, दिए गए sentence को grammatically सही बनाने के लिए \'<strong>liked</strong>\' के स्थान पर \'<strong>like</strong>\' का प्रयोग करना होगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>The supervisor / wanted to known / the pros and cons / of the issue.</p>",
                    question_hi: "<p>10. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>The supervisor / wanted to known / the pros and cons / of the issue.</p>",
                    options_en: ["<p>the pros and cons</p>", "<p>wanted to known</p>", 
                                "<p>of the issue</p>", "<p>The supervisor</p>"],
                    options_hi: ["<p>the pros and cons</p>", "<p>wanted to known</p>",
                                "<p>of the issue</p>", "<p>The supervisor</p>"],
                    solution_en: "<p>10.(b) wanted to known.<br>The preposition &lsquo;to&rsquo; always takes the first form of the verb(V<sub>1</sub>) with it. However, &lsquo;know&rsquo; is the base/first form of &lsquo;known&rsquo;(V<sub>3</sub>). Hence, &lsquo;<strong>known</strong>&rsquo; will be replaced by &lsquo;<strong>know</strong>&rsquo; to make the given sentence grammatically correct.</p>",
                    solution_hi: "<p>10.(b) wanted to known.<br>Preposition &lsquo;to&rsquo; के साथ हमेशा verb की first form (V<sub>1</sub>) का प्रयोग होता है। हालाँकि, &lsquo;known&rsquo;(V<sub>3</sub>) की base/first form, &lsquo;know&rsquo; है। अतः, दिए गए sentence को grammatically सही बनाने के लिए &lsquo;<strong>known</strong>&rsquo; के स्थान पर &lsquo;<strong>know</strong>&rsquo; का प्रयोग किया जाएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Raman putted the vegetables / in the frying pan / and after adding some water, / closed the lid.</p>",
                    question_hi: "<p>11. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Raman putted the vegetables / in the frying pan / and after adding some water, / closed the lid.</p>",
                    options_en: ["<p>and after adding some water</p>", "<p>closed the lid</p>", 
                                "<p>in the frying pan</p>", "<p>Raman putted the vegetables</p>"],
                    options_hi: ["<p>and after adding some water</p>", "<p>closed the lid</p>",
                                "<p>in the frying pan</p>", "<p>Raman putted the vegetables</p>"],
                    solution_en: "<p>11.(d) Raman putted the vegetables. <br>The given sentence is in the simple past tense so the verb must be used in its past form. However, the <strong>past form</strong> of &lsquo;put&rsquo; is &lsquo;put&rsquo;. Hence, &lsquo;putted&rsquo; will be replaced by &lsquo;put&rsquo; to make the given sentence grammatically correct.</p>",
                    solution_hi: "<p>11.(d) Raman putted the vegetables. <br>दिया गया sentence, simple past tense में है इसलिए verb भी past form में होनी चाहिए। हालाँकि, &lsquo;put&rsquo; का <strong>past form</strong> &lsquo;put&rsquo; ही है। अतः, दिए गए sentence को grammatically सही बनाने के लिए &lsquo;putted&rsquo; के स्थान पर &lsquo;put&rsquo; का प्रयोग किया जाएगा।<br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The recruiters / were pleased / to John&rsquo;s / domain knowledge and personality.</p>",
                    question_hi: "<p>12. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The recruiters / were pleased / to John&rsquo;s / domain knowledge and personality.</p>",
                    options_en: ["<p>The recruiters</p>", "<p>to John&rsquo;s</p>", 
                                "<p>domain knowledge and personality</p>", "<p>were pleased</p>"],
                    options_hi: ["<p>The recruiters</p>", "<p>to John&rsquo;s</p>",
                                "<p>domain knowledge and personality</p>", "<p>were pleased</p>"],
                    solution_en: "<p>12.(b) to John&rsquo;s. <br>The phrase &lsquo;someone is <strong>pleased with</strong> something(Object)&rsquo; is grammatically correct, for example, I am pleased with his kind behavior. Similarly in the given sentence, the recruiters were <strong>pleased with</strong> John&rsquo;s domain knowledge and personality. Hence, &lsquo;<strong>with John&rsquo;s</strong>&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(b) to John&rsquo;s. <br>Phrase &lsquo;someone is <strong>pleased with</strong> something(Object)&rsquo;, grammatically सही है, उदाहरण के लिए, I am pleased with his kind behavior. इसी प्रकार दिए गए sentence में, the recruiters were <strong>pleased with</strong> John&rsquo;s domain knowledge and personality. अतः, &lsquo;<strong>with John&rsquo;s</strong>&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>Won&rsquo;t you / please come / to help me / along this heavy box?</p>",
                    question_hi: "<p>13. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>Won&rsquo;t you / please come / to help me / along this heavy box?</p>",
                    options_en: ["<p>Won&rsquo;t you</p>", "<p>to help me</p>", 
                                "<p>along this heavy box</p>", "<p>please come</p>"],
                    options_hi: ["<p>Won&rsquo;t you</p>", "<p>to help me</p>",
                                "<p>along this heavy box</p>", "<p>please come</p>"],
                    solution_en: "<p>13.(c) along this heavy box. <br>The phrase &ldquo;Would/Will/Can you help me <span style=\"text-decoration: underline;\"><strong>with</strong></span> this______&rdquo; is grammatically the correct structure that we use when we ask someone for help in doing something. Similarly, in the given sentence, the narrator is asking someone to help him/her lift a heavy bag. Hence, &lsquo;<span style=\"text-decoration: underline;\"><strong>with</strong> this heavy box</span>&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(c) along this heavy box. <br>Phrase &ldquo;Would/Will/Can you help me <span style=\"text-decoration: underline;\"><strong>with</strong></span> this______&rdquo; grammatically सही structure है जिसका प्रयोग हम तब करते हैं जब हम किसी से कुछ करने में मदद मांगते हैं। इसी प्रकार, दिए गए sentence में, वर्णनकर्ता (narrator) किसी से इस भारी बक्से (heavy box) को उठाने में मदद करने के लिए कह रहा है। अतः, &lsquo;<span style=\"text-decoration: underline;\"><strong>with</strong> this heavy box</span>&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The sea has become / an cheap dumping ground / for all kinds / of waste products.</p>",
                    question_hi: "<p>14. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The sea has become / an cheap dumping ground / for all kinds / of waste products.</p>",
                    options_en: ["<p>of waste products</p>", "<p>an cheap dumping ground</p>", 
                                "<p>for all kinds</p>", "<p>The sea has become</p>"],
                    options_hi: ["<p>of waste products</p>", "<p>an cheap dumping ground</p>",
                                "<p>for all kinds</p>", "<p>The sea has become</p>"],
                    solution_en: "<p>14.(b) an cheap dumping ground. <br>We use the article &lsquo;a&rsquo; with words starting with a <span style=\"text-decoration: underline;\">consonant</span>(B,D,F,N,etc.) for example, a dirty shirt, a neglected department, etc. However, the article &lsquo;an&rsquo; is used with words starting with a <span style=\"text-decoration: underline;\">vowel</span>(A,E,I,O,U) for example, an educated person, an imminent danger, etc. Hence, &lsquo;<span style=\"text-decoration: underline;\">a cheap</span>(consonant) dumping ground&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>14.(b) an cheap dumping ground. <br><span style=\"text-decoration: underline;\">Consonant sound</span> से प्रारंभ हम words के साथ हम article &lsquo;a&rsquo; का प्रयोग करते हैं (जैसे B, D, F, N, आदि)। उदाहरण के लिए, a dirty shirt, a neglected department आदि। जबकि, <span style=\"text-decoration: underline;\">vowel sound</span> से प्रारंभ होने वाले words के साथ article &lsquo;an&rsquo; का प्रयोग करते हैं, (जैसे A, E, I, O, U)। उदाहरण के लिए, an educated person, an imminent danger आदि। अतः, &lsquo;<span style=\"text-decoration: underline;\">a cheap</span>(consonant) dumping ground&rsquo; सबसे उपयुक्त उतर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "15. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The construction of / the new business school / is led to / a sudden rise of population in our area. ",
                    question_hi: "15. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The construction of / the new business school / is led to / a sudden rise of population in our area. ",
                    options_en: [" a sudden rise of population in our area ", " The construction of ", 
                                " the new business school ", " is led to  "],
                    options_hi: [" a sudden rise of population in our area ", " The construction of ",
                                " the new business school ", " is led to  "],
                    solution_en: "15.(d) is led to.<br />The given sentence is in the present perfect tense so the verb must be used in its present perfect form(has) and not in the present/base form(is). Hence, ‘has led to’ is the most appropriate answer.",
                    solution_hi: "15.(d) is led to.<br />दिया गया Sentence, Present Perfect tense ​​में है, इसलिए verb भी present perfect form(has) में होनी चाहिए, न कि present/base form(is) में। अतः, ‘has led to’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If there is no error, select &lsquo;No error&rsquo;. <br>They have lived / in this apartment / since ten years.</p>",
                    question_hi: "<p>16. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If there is no error, select &lsquo;No error&rsquo;. <br>They have lived / in this apartment / since ten years.</p>",
                    options_en: ["<p>They have lived</p>", "<p>No error</p>", 
                                "<p>in this apartment</p>", "<p>since ten years</p>"],
                    options_hi: ["<p>They have lived</p>", "<p>No error</p>",
                                "<p>in this apartment</p>", "<p>since ten years</p>"],
                    solution_en: "<p>16.(d) since ten years.<br>&lsquo;<strong>For</strong>&rsquo; refers to a specific period of time or numerical value of time, for example for 2 years, 4 weeks, 3 days, etc. However, in the given question, the period of time is 10 years. Hence, &lsquo;<strong>for </strong>ten years&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>16.(d) since ten years.<br>&lsquo;<strong>For</strong>&rsquo; एक &lsquo;specific period of time&rsquo; या &lsquo;numerical value of time&rsquo; को संदर्भित करता है, उदाहरण के लिए, for 2 years, 4 weeks, 3 days आदि। हालाँकि, दिए गए question में, समय की अवधि (period of time) 10 वर्ष है। अतः, &lsquo;<strong>for </strong>ten years&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>He win / several accolades / for his / music compositions.</p>",
                    question_hi: "<p>17. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>He win / several accolades / for his / music compositions.</p>",
                    options_en: ["<p>He win</p>", "<p>music compositions</p>", 
                                "<p>for his</p>", "<p>several accolades</p>"],
                    options_hi: ["<p>He win</p>", "<p>music compositions</p>",
                                "<p>for his</p>", "<p>several accolades</p>"],
                    solution_en: "<p>17.(a) He win. <br>According to the &ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;he&rsquo; is a singular subject that will take &lsquo;wins&rsquo; as a singular verb. Hence, &lsquo;he wins&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>17.(a) He win. <br>&ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo; के अनुसार, singular subject के साथ हमेशा singular verb का तथा plural subject के साथ हमेशा plural verb का प्रयोग होता है। दिए गए sentence में, &lsquo;he&rsquo; एक singular subject है जिसके साथ &lsquo;wins&rsquo; singular verb का प्रयोग होगा । अतः, &lsquo;he wins&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>When she went for a walk / Maya couldn\'t take off her scarf / because it was / too much windy.</p>",
                    question_hi: "<p>18. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>When she went for a walk / Maya couldn\'t take off her scarf / because it was / too much windy.</p>",
                    options_en: ["<p>too much windy</p>", "<p>Maya couldn\'t take off her scarf</p>", 
                                "<p>because it was</p>", "<p>When she went for a walk</p>"],
                    options_hi: ["<p>too much windy</p>", "<p>Maya couldn\'t take off her scarf</p>",
                                "<p>because it was</p>", "<p>When she went for a walk</p>"],
                    solution_en: "<p>18.(a) too much windy. <br>We use \"too much\" before <span style=\"text-decoration: underline;\">uncountable nouns </span>like too much money, too much milk, etc. However, \"too many\" is used before <span style=\"text-decoration: underline;\">countable nouns</span> like too many shoes(countable), too many books, etc. But, in the given sentence, &lsquo;windy&rsquo; is <span style=\"text-decoration: underline;\">not a noun</span> it is an adjective. So, we will remove &lsquo;much&rsquo; before &lsquo;windy&rsquo;. Hence, either &lsquo;too windy&rsquo; or &lsquo;too much wind(noun)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(a) too much windy. <br>हम \"too much\" का प्रयोग <span style=\"text-decoration: underline;\">uncountable nouns</span> से पहले करते हैं, जैसे too much money, too much milk आदि। हालाँकि, \"too many\" का प्रयोग <span style=\"text-decoration: underline;\">countable nouns</span> से पहले किया जाता है, जैसे too many shoes, too many books आदि। लेकिन, दिए गए sentence में, &lsquo;windy&rsquo; कोई <span style=\"text-decoration: underline;\">noun</span> नहीं है, यह एक adjective है। इसलिए, हम &lsquo;windy&rsquo; से पहले &lsquo;much&rsquo; हटा देंगे। अतः, &lsquo;too windy&rsquo; या &lsquo;too much wind (noun)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>We don\'t really / want a large house; / we are looking for some comfort / and some convenience on a moderate price.</p>",
                    question_hi: "<p>19. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>We don\'t really / want a large house; / we are looking for some comfort / and some convenience on a moderate price.</p>",
                    options_en: ["<p>and some convenience on a moderate price</p>", "<p>want a large house</p>", 
                                "<p>We don\'t really</p>", "<p>we are looking for some comfort</p>"],
                    options_hi: ["<p>and some convenience on a moderate price</p>", "<p>want a large house</p>",
                                "<p>We don\'t really</p>", "<p>we are looking for some comfort</p>"],
                    solution_en: "<p>19.(a) and some convenience on a moderate price.<br>There is a prepositional error in the given sentence. The preposition &lsquo;<strong>on</strong>&rsquo; will be replaced with &lsquo;<strong>at</strong>&rsquo; because we generally <strong>buy something <span style=\"text-decoration: underline;\">at</span></strong> a low/high price, <strong>not <span style=\"text-decoration: underline;\">on</span></strong> a low/high price. Hence, &lsquo;some convenience at a moderate price&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>19.(a) and some convenience on a moderate price.<br>दिए गए sentence में एक prepositional error है। preposition &lsquo;<strong>on</strong>&rsquo; के स्थान पर &lsquo;<strong>at</strong>&rsquo; का प्रयोग होगा, क्योंकि हम आमतौर पर कोई चीज़ कम/ऊंची कीमत पर खरीदते हैं तो हम \'<strong>at </strong>a low/high price\' का प्रयोग करते हैं, न कि \'<strong>on </strong>a low/high price\' का। अतः, &lsquo;some convenience at a moderate price&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Lalitha will go / to sister&rsquo;s house / in Mumbai / this summer.</p>",
                    question_hi: "<p>20. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Lalitha will go / to sister&rsquo;s house / in Mumbai / this summer.</p>",
                    options_en: ["<p>to sister&rsquo;s house</p>", "<p>in Mumbai</p>", 
                                "<p>Lalitha will go</p>", "<p>this summer</p>"],
                    options_hi: ["<p>to sister&rsquo;s house</p>", "<p>in Mumbai</p>",
                                "<p>Lalitha will go</p>", "<p>this summer</p>"],
                    solution_en: "<p>20.(a) to sister&rsquo;s house.<br>There is a pronoun error in the given sentence. The possessive pronoun &lsquo;<strong>her</strong>&rsquo; that is generally used for a female subject(Lalitha in the given sentence) is <strong>missing</strong>. Hence, &lsquo;to her sister&rsquo;s house&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>20.(a) to sister&rsquo;s house.<br>दिए गए Sentence में Pronoun संबंधी error है। Possessive pronoun &lsquo;<strong>her</strong>&rsquo;, जिसका प्रयोग आमतौर पर female subject (दिए गए sentence में Lalitha) के लिए किया जाता है, अनुपस्थित <strong id=\"docs-internal-guid-fd8e87d4-7fff-ddd9-97ae-f8676d8cacde\">(</strong><strong id=\"docs-internal-guid-96cad903-7fff-80ec-0688-4841b2b79e24\">missing)</strong> है। अतः, &lsquo;to her sister&rsquo;s house&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>