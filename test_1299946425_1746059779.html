<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. What should come in the place of ? in the given series based on the English alphabetical order?<br>YQI&nbsp; &nbsp;CZQ&nbsp; &nbsp;GIY&nbsp; &nbsp;KRG&nbsp; &nbsp;?</p>",
                    question_hi: "<p>1. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?\' के स्थान पर क्या आना चाहिए ?<br>YQI&nbsp; &nbsp;CZQ&nbsp; &nbsp;GIY&nbsp; &nbsp;KRG&nbsp; &nbsp;?</p>",
                    options_en: [
                        "<p>OAO</p>",
                        "<p>DMX</p>",
                        "<p>CNY</p>",
                        "<p>EOZ</p>"
                    ],
                    options_hi: [
                        "<p>OAO</p>",
                        "<p>DMX</p>",
                        "<p>CNY</p>",
                        "<p>EOZ</p>"
                    ],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200011.png\" alt=\"rId4\" width=\"519\" height=\"149\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200011.png\" alt=\"rId4\" width=\"519\" height=\"149\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent<br>digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(144, 12, 24)<br>(216, 18, 36)</p>",
                    question_hi: "<p>2. उस समुच्चय का चयन करें जिसकी संख्याएं उसी तरह से संबंधित हैं जिस तरह निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br>(नोट : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)<br>(144, 12, 24)<br>(216, 18, 36)</p>",
                    options_en: [
                        "<p>(324, 27, 54)</p>",
                        "<p>(324, 29, 54)</p>",
                        "<p>(324, 27, 50)</p>",
                        "<p>(320, 27, 54)</p>"
                    ],
                    options_hi: [
                        "<p>(324, 27, 54)</p>",
                        "<p>(324, 29, 54)</p>",
                        "<p>(324, 27, 50)</p>",
                        "<p>(320, 27, 54)</p>"
                    ],
                    solution_en: "<p>2.(a) <strong>Logic</strong> :- (2nd number + 3rd number) &times; 4 = 1st number<br>(144, 12, 24) :- (12 + 24) &times; 4 &rArr; (36) &times; 4 = 144<br>(216, 18, 36) :- (18 + 36) &times; 4 &rArr; (54) &times; 4 = 216<br>Similarly,<br>(324, 27, 54) :- (27 + 54) &times; 4 &rArr; (81) &times; 4 = 324</p>",
                    solution_hi: "<p>2.(a) <strong>तर्क:-</strong> (दूसरी संख्या + तीसरी संख्या) &times; 4 = पहली संख्या<br>(144, 12, 24) :- (12 + 24) &times; 4 &rArr; (36) &times; 4 = 144<br>(216, 18, 36) :- (18 + 36) &times; 4 &rArr; (54) &times; 4 = 216<br>इसी प्रकार,<br>(324, 27, 54) :- (27 + 54) &times; 4 &rArr; (81) &times; 4 = 324</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200118.png\" alt=\"rId5\" width=\"134\" height=\"142\"></p>",
                    question_hi: "<p>3. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200118.png\" alt=\"rId5\" width=\"134\" height=\"142\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200215.png\" alt=\"rId6\" width=\"124\" height=\"36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200309.png\" alt=\"rId7\" width=\"124\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200398.png\" alt=\"rId8\" width=\"124\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200490.png\" alt=\"rId9\" width=\"124\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200215.png\" alt=\"rId6\" width=\"124\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200309.png\" alt=\"rId7\" width=\"124\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200398.png\" alt=\"rId8\" width=\"124\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200490.png\" alt=\"rId9\" width=\"124\"></p>"
                    ],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200309.png\" alt=\"rId7\" width=\"124\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200309.png\" alt=\"rId7\" width=\"124\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/consonants/vowels in the word.)<br>Problem : Solution</p>",
                    question_hi: "<p>4. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>समस्या : समाधान</p>",
                    options_en: [
                        "<p>Lost : Found</p>",
                        "<p>Time : Money</p>",
                        "<p>Talk : Speak</p>",
                        "<p>Success : Fame</p>"
                    ],
                    options_hi: [
                        "<p>खोया : पाया</p>",
                        "<p>समय : पैसा</p>",
                        "<p>बातचीत : बोलना</p>",
                        "<p>सफलता : प्रसिद्धि</p>"
                    ],
                    solution_en: "<p>4.(a) As problem and solution are opposite of each other similarly lost and found are opposite of each other.</p>",
                    solution_hi: "<p>4.(a) जिस प्रकार समस्या और समाधान एक दूसरे के विपरीत हैं उसी प्रकार खोया और पाया एक दूसरे के विपरीत हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does NOT belong to that group?<br>(Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>5. निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं । वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>(ध्यान देंः असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>OPSN</p>",
                        "<p>VWZU</p>",
                        "<p>EFHD</p>",
                        "<p>MNQL</p>"
                    ],
                    options_hi: [
                        "<p>OPSN</p>",
                        "<p>VWZU</p>",
                        "<p>EFHD</p>",
                        "<p>MNQL</p>"
                    ],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200591.png\" alt=\"rId10\" width=\"369\" height=\"120\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200688.png\" alt=\"rId11\" width=\"112\" height=\"114\"></p>",
                    solution_hi: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200591.png\" alt=\"rId10\" width=\"369\" height=\"120\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200688.png\" alt=\"rId11\" width=\"112\" height=\"114\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the option figure in which the given figure (X) is embedded as its part (rotation<br>is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200798.png\" alt=\"rId12\" width=\"112\" height=\"115\"></p>",
                    question_hi: "<p>6. उस विकल्प आकृति का चयन कीजिए, जिसमें दी गई आकृति (X) उसके एक भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200798.png\" alt=\"rId12\" width=\"112\" height=\"115\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200898.png\" alt=\"rId13\" width=\"100\" height=\"112\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200997.png\" alt=\"rId14\" width=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201093.png\" alt=\"rId15\" width=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201196.png\" alt=\"rId16\" width=\"100\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200898.png\" alt=\"rId13\" width=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246200997.png\" alt=\"rId14\" width=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201093.png\" alt=\"rId15\" width=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201196.png\" alt=\"rId16\" width=\"100\"></p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201319.png\" alt=\"rId17\" width=\"100\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201319.png\" alt=\"rId17\" width=\"100\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into&nbsp;1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>7. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है?<br>(ध्यान दें : संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>81 &ndash; 118 &ndash; 91</p>",
                        "<p>52 &ndash; 68 &ndash; 51</p>",
                        "<p>99 &ndash; 104 &ndash; 71</p>",
                        "<p>21 &ndash; 38 &ndash; 31</p>"
                    ],
                    options_hi: [
                        "<p>81 &ndash; 118 &ndash; 91</p>",
                        "<p>52 &ndash; 68 &ndash; 51</p>",
                        "<p>99 &ndash; 104 &ndash; 71</p>",
                        "<p>21 &ndash; 38 &ndash; 31</p>"
                    ],
                    solution_en: "<p>7.(b) <strong>Logic</strong> : sum of all number in all option is even except option (b)<br>81 &ndash; 118 &ndash; 91 :-&nbsp; 81 + 118 + 91 = 290<br>99 &ndash; 104 &ndash; 71 :-&nbsp; 99 + 104 +71 = 274<br>21 &ndash; 38 &ndash; 31 :-&nbsp; 21 + 38 + 31 = 90<br>But <br>52 &ndash; 68 &ndash; 51 :-&nbsp; 52 + 68 + 51 = 171(odd)</p>",
                    solution_hi: "<p>7.(b) <strong>तर्क</strong> : विकल्प (b) को छोड़कर सभी विकल्पों में सभी संख्याओं का योग सम है<br>81 &ndash; 118 &ndash; 91:- 81 + 118 + 91 = 290<br>99 &ndash; 104 &ndash; 71 :- 99 + 104 +71 = 274<br>21 &ndash; 38 &ndash; 31 :- 21 + 38 + 31 = 90<br>लेकिन <br>52 &ndash; 68 &ndash; 51 :- 52 + 68 + 51 = 171(विषम)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, &lsquo;time and again&rsquo; is coded as &lsquo;zx df mo&rsquo; and &lsquo;value your time&rsquo; is coded as &lsquo;df pn tv&rsquo;. How is &lsquo;time&rsquo; coded in the given language ?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में, &lsquo;time and again&rsquo; को &lsquo;zx df mo&rsquo; लिखा जाता है और &lsquo;value your time&rsquo;&nbsp;को &lsquo;df pn tv&rsquo; लिखा जाता है। उसी कूट भाषा में &lsquo;time&rsquo; को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>zx</p>",
                        "<p>tv</p>",
                        "<p>mo</p>",
                        "<p>df</p>"
                    ],
                    options_hi: [
                        "<p>zx</p>",
                        "<p>tv</p>",
                        "<p>mo</p>",
                        "<p>df</p>"
                    ],
                    solution_en: "<p>8.(d) time and again &rarr; zx df mo&hellip;&hellip;.(i)<br>value your time &rarr; df pn tv&hellip;&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;time&rsquo; and &lsquo;df&rsquo; are common. The code of &lsquo;time&rsquo; = &lsquo;df&rsquo;.</p>",
                    solution_hi: "<p>8.(d) time and again &rarr; zx df mo&hellip;&hellip;.(i)<br>value your time &rarr; df pn tv&hellip;&hellip;.(ii)<br>(i) और (ii) से \'time\' और \'df\' उभयनिष्ठ हैं। \'time\' का कूट = \'df\'।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201439.png\" alt=\"rId18\" width=\"248\" height=\"77\"></p>",
                    question_hi: "<p>9. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201439.png\" alt=\"rId18\" width=\"248\" height=\"77\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201536.png\" alt=\"rId19\" width=\"80\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201631.png\" alt=\"rId20\" width=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201723.png\" alt=\"rId21\" width=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201816.png\" alt=\"rId22\" width=\"80\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201536.png\" alt=\"rId19\" width=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201631.png\" alt=\"rId20\" width=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201723.png\" alt=\"rId21\" width=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201816.png\" alt=\"rId22\" width=\"80\"></p>"
                    ],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201723.png\" alt=\"rId21\" width=\"80\"></p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201723.png\" alt=\"rId21\" width=\"80\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language, \'BACK\' is coded as \'4162\' and \'CAB\' is coded as \'214. What is the code for \'K\' in that language ?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में, \'BACK\' को \'4162\' लिखा जाता है और \'CAB\' को \'214\' लिखा जाता है। तो उस कूट भाषा में \'K\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>1</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>1</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>10.(d) BACK &rarr; 4162&hellip;..(i)<br>CAB &rarr; 214&hellip;..(ii) <br>From (i) and (ii) &lsquo;BAC&rsquo; and &lsquo;214&rsquo; are common. The code of &lsquo;K&rsquo; = &lsquo;6&rsquo;</p>",
                    solution_hi: "<p>10.(d) BACK &rarr; 4162&hellip;..(i)<br>CAB &rarr; 214&hellip;..(ii) <br>(i) और (ii) से \'BAC\' और \'214\' उभयनिष्ठ हैं। \'K\' का कोड = \'6\'</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201917.png\" alt=\"rId23\" width=\"132\" height=\"115\"></p>",
                    question_hi: "<p>11. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246201917.png\" alt=\"rId23\" width=\"132\" height=\"115\"></p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>15</p>",
                        "<p>11</p>",
                        "<p>13</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>15</p>",
                        "<p>11</p>",
                        "<p>13</p>"
                    ],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202026.png\" alt=\"rId24\" width=\"245\" height=\"241\"><br>Triangle are :- ABD, BCH, HGF, DEF, IJL, JLK, ILK, IVL, KML, MNT, TUN, NOP, TSR, PQR</p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202026.png\" alt=\"rId24\" width=\"245\" height=\"241\"><br>त्रिभुज :- ABD, BCH, HGF, DEF, IJL, JLK, ILK, IVL, KML, MNT, TUN, NOP, TSR, PQR</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In a code language, &lsquo;ROCK&rsquo; is written as &lsquo;PKAG&rsquo;, and &lsquo;CLUB&rsquo; is written as &lsquo;AHSX&rsquo;. How will &lsquo;SHOP&rsquo; be written in that language ?</p>",
                    question_hi: "<p>12. एक कूट भाषा में \'ROCK\' को \'PKAG\' के रूप में लिखा जाता है और &lsquo;CLUB\' को &lsquo;AHSX\' के रूप में लिखा जाता है। इसी कूट भाषा में &lsquo;SHOP\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>QDML</p>",
                        "<p>QMLD</p>",
                        "<p>MQDL</p>",
                        "<p>QMDL</p>"
                    ],
                    options_hi: [
                        "<p>QDML</p>",
                        "<p>QMLD</p>",
                        "<p>MQDL</p>",
                        "<p>QMDL</p>"
                    ],
                    solution_en: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202147.png\" alt=\"rId25\" width=\"171\" height=\"112\">,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202243.png\" alt=\"rId26\" width=\"171\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202349.png\" alt=\"rId27\" width=\"171\"></p>",
                    solution_hi: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202147.png\" alt=\"rId25\" width=\"171\" height=\"112\">,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202243.png\" alt=\"rId26\" width=\"171\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202349.png\" alt=\"rId27\" width=\"171\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the figure from among the given options that can replace the question mark (?) and logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202450.png\" alt=\"rId28\" width=\"359\" height=\"84\"></p>",
                    question_hi: "<p>13. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है और शृंखला को तार्किक रूप से पूरा कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202450.png\" alt=\"rId28\" width=\"359\" height=\"84\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202563.png\" alt=\"rId29\" width=\"90\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202656.png\" alt=\"rId30\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202752.png\" alt=\"rId31\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202850.png\" alt=\"rId32\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202563.png\" alt=\"rId29\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202656.png\" alt=\"rId30\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202752.png\" alt=\"rId31\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202850.png\" alt=\"rId32\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202656.png\" alt=\"rId30\" width=\"90\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202656.png\" alt=\"rId30\" width=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Which of the following numbers will replace the question mark (?) in the given series?<br>549&nbsp; &nbsp; 482&nbsp; &nbsp; 439&nbsp; &nbsp; 372&nbsp; &nbsp; 329&nbsp; &nbsp; 262&nbsp; &nbsp; 219&nbsp; &nbsp; ?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी ?<br>549&nbsp; &nbsp; 482&nbsp; &nbsp; 439&nbsp; &nbsp; 372&nbsp; &nbsp; 329&nbsp; &nbsp; 262&nbsp; &nbsp; 219&nbsp; &nbsp; ?</p>",
                    options_en: [
                        "<p>152</p>",
                        "<p>132</p>",
                        "<p>149</p>",
                        "<p>162</p>"
                    ],
                    options_hi: [
                        "<p>152</p>",
                        "<p>132</p>",
                        "<p>149</p>",
                        "<p>162</p>"
                    ],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202999.png\" alt=\"rId33\" width=\"384\" height=\"55\"></p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246202999.png\" alt=\"rId33\" width=\"384\" height=\"55\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements</strong> :<br>Some keys are chains.<br>All chains are locks.<br>No lock is a rock.<br><strong>Conclusions</strong> :<br>(I) No rock is a key.<br>(II) At least some keys are locks.</p>",
                    question_hi: "<p>15. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होता हो, निर्णय लें कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते है।<br><strong>कथन</strong> :<br>कुछ चाबियां, जंजीरें हैं।<br>सभी जंजीरें, ताले हैं।<br>कोई ताला, चट्टान नहीं है।<br><strong>निष्कर्ष</strong> :<br>(I) कोई चट्टान चाबी नहीं है.<br>(II) कम से कम कुछ चाबियां, ताले हैं।</p>",
                    options_en: [
                        "<p>Only conclusion I follows</p>",
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Only conclusion II follows</p>",
                        "<p>None of the conclusion follow</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                        "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203151.png\" alt=\"rId34\" width=\"452\" height=\"98\"><br>Only conclusion II follow.</p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203252.png\" alt=\"rId35\" width=\"451\" height=\"93\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is B&rsquo;s son&rsquo;.<br>&lsquo;A &times; B&rsquo; means &lsquo;A is B&rsquo;s sister&rsquo;.<br>&lsquo;A + B&rsquo; means &lsquo;A is B&rsquo;s brother&rsquo;.<br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is B&rsquo;s mother&rsquo;.<br>Using the mathematical operators meaning the same as given above, which of the following means that &lsquo;S is T&rsquo;s husband&rsquo; ?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में<br>A &divide; B\' का अर्थ है \'A, B का पुत्र है\'।<br>\'A &times; B\' का अर्थ है \'A, B की बहन है\'।<br>\'A + B\' का अर्थ है \'A, B का भाई है\'।<br>\'A - B\' का अर्थ है \'A, B की माँ है\'।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि निम्नलिखित में से किसका अर्थ हैकि \'S, T का पति है\'?</p>",
                    options_en: [
                        "<p>T &ndash; R + V &divide; S</p>",
                        "<p>T &divide; R &times; V + S</p>",
                        "<p>T &ndash; R &divide; V &times; S</p>",
                        "<p>T &times; R &ndash; V + S</p>"
                    ],
                    options_hi: [
                        "<p>T &ndash; R + V &divide; S</p>",
                        "<p>T &divide; R &times; V + S</p>",
                        "<p>T &ndash; R &divide; V &times; S</p>",
                        "<p>T &times; R &ndash; V + S</p>"
                    ],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203347.png\" alt=\"rId36\" width=\"134\" height=\"133\"><br>S is T&rsquo;s husband.</p>",
                    solution_hi: "<p>16.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203347.png\" alt=\"rId36\" width=\"134\" height=\"133\"><br>S, T का पति है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Six letters P, J, U, W, L and K are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to P.<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203482.png\" alt=\"rId37\" width=\"151\" height=\"74\"></p>",
                    question_hi: "<p>17. एक पासे के विभिन्न फलकों पर छह अक्षर P, J, U, W, L और K लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। P के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203482.png\" alt=\"rId37\" width=\"151\" height=\"74\"></p>",
                    options_en: [
                        "<p>U</p>",
                        "<p>K</p>",
                        "<p>W</p>",
                        "<p>J</p>"
                    ],
                    options_hi: [
                        "<p>U</p>",
                        "<p>K</p>",
                        "<p>W</p>",
                        "<p>J</p>"
                    ],
                    solution_en: "<p>17.(c)<br>From both the dice the opposite faces are <br>J &harr; K , P &harr; W, U &harr; L</p>",
                    solution_hi: "<p>17.(c)<br>दोनों पासों के विपरीत फलक हैं <br>J &harr; K , P &harr; W, U &harr; L</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Study the given Venn diagram and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203664.png\" alt=\"rId38\" width=\"205\" height=\"222\"> <br>How many smart people are polite ?</p>",
                    question_hi: "<p>18. दिए गए वेन आरेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203767.png\" alt=\"rId39\" width=\"197\" height=\"221\"> <br>कितने चतुर लोग विनम्र हैं?</p>",
                    options_en: [
                        "<p>19</p>",
                        "<p>63</p>",
                        "<p>12</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>19</p>",
                        "<p>63</p>",
                        "<p>12</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>18.(a) Number of smart people who are polite = 12 + 7 = 19</p>",
                    solution_hi: "<p>18.(a) चतुर और विनम्र लोगों की संख्या = 12 + 7 = 19</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. J, K, L, M, O and P are sitting around a circular table facing the centre. Both L and M are immediate neighbours of J. Only one person sits between M and P. O sits second to the right of J. O is not an immediate neighbour of L. Who is sitting to the immediate right of K?</p>",
                    question_hi: "<p>19. J, K, L, M, O और P एक वृताकार मेज के चारो ओर केंद्र की ओर मुख करके बैठे हैं। L और M दोनों J के निकटतम पड़ोसी हैं। M और P के बीच केवल एक व्यक्ति बैठा है। O, J के दाएं से दूसरे स्थान पर बैठा है। O, L का निकटतम पड़ोसी नहीं है। K के ठीक दाएं कौन बैठा है?</p>",
                    options_en: [
                        "<p>J</p>",
                        "<p>L</p>",
                        "<p>P</p>",
                        "<p>O</p>"
                    ],
                    options_hi: [
                        "<p>J</p>",
                        "<p>L</p>",
                        "<p>P</p>",
                        "<p>O</p>"
                    ],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203869.png\" alt=\"rId40\" width=\"207\" height=\"138\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203869.png\" alt=\"rId40\" width=\"207\" height=\"138\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Harini starts from a cafe and walks 4 km towards the south. She then turns right and walks 2 km, again turns right and walks 4 km, and then turns left and walks 4 km. How many kilometres will Harini need to walk to reach the caf&eacute; straight from this point ?</p>",
                    question_hi: "<p>20. हरिनी एक कैफे से चलना शुरू करती है और दक्षिण दिशा की ओर 4 km चलती है। वह फिर दाएं मुड़ती है और 2 km चलती है, फिर से दाएं मुड़ती है और 4 km चलती है, और फिर बाएं मुड़ती है और 4 km चलती है। इस बिंदु से सीधे कैफे तक पहुंचने के लिए हरिनी को कितने किलोमीटर पैदल चलना पड़ेगा ?</p>",
                    options_en: [
                        "<p>8 km</p>",
                        "<p>6 km</p>",
                        "<p>4 km</p>",
                        "<p>2 km</p>"
                    ],
                    options_hi: [
                        "<p>8 km</p>",
                        "<p>6 km</p>",
                        "<p>4 km</p>",
                        "<p>2 km</p>"
                    ],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246203968.png\" alt=\"rId41\" width=\"255\" height=\"163\"><br>Harini needs to walk 6km.</p>",
                    solution_hi: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204086.png\" alt=\"rId42\" width=\"222\" height=\"145\"><br>हरिनी को 6km पैदल चलना पड़ेगा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Which two numbers should be interchanged to make the given equation correct?<br>(24 &divide; 6) + (18 &times; 4) &ndash; 54 + 42 = 99<br>(Note: Interchange should be done of entire number and not individual digits of a given number)</p>",
                    question_hi: "<p>21. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए ?<br>(24 &divide; 6) + (18 &times; 4) &ndash; 54 + 42 = 99<br>(नोट: अदला-बदली पूरी संख्या की होनी चाहिए न कि दी गई संख्या के अलग-अलग अंकों की)</p>",
                    options_en: [
                        "<p>24 and 54</p>",
                        "<p>54 and 42</p>",
                        "<p>4 and 6</p>",
                        "<p>18 and 24</p>"
                    ],
                    options_hi: [
                        "<p>24 और 54</p>",
                        "<p>54 और 42</p>",
                        "<p>4 और 6</p>",
                        "<p>18 और 24</p>"
                    ],
                    solution_en: "<p>21.(a) <strong>Given</strong> :- (24 &divide;&nbsp;6) + (18 &times; 4) - 54 + 42 = 99<br>After going through all the options, option (a) satisfies. After interchanging 24 and 54 we get<br>(54 &divide; 6) + (72) - 24 + 42 <br>9 + 114 - 24 = 99<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>21.(a) <strong>दिया गया :-</strong> (24 &divide;&nbsp;6) + (18 &times; 4) - 54 + 42 = 99<br>सभी विकल्पों को देखने के बाद, विकल्प (a) संतुष्ट करता है। 24 और 54 को आपस में बदलने पर हमें प्राप्त होता है<br>(54 &divide; 6) + (72) - 24 + 42 <br>9 + 114 - 24 = 99<br>L.H.S. = R.H.S.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. The position of how many letters will remain unchanged if each of the letters in the word COMPLEXITY is arranged from left to right in alphabetical order?</p>",
                    question_hi: "<p>22. यदि COMPLEXITY शब्द के प्रत्येक अक्षर को बाएं से दाएं वर्णमाला क्रम में व्यवस्थित किया जाता है, तो कितने अक्षरों का स्थान परिवर्तित नहीं होगा?</p>",
                    options_en: [
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>None</p>",
                        "<p>Three</p>"
                    ],
                    options_hi: [
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>किसी का भी नहीं</p>",
                        "<p>तीन</p>"
                    ],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204230.png\" alt=\"rId43\" width=\"309\" height=\"90\"><br>The position of two letters remain unchanged.</p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204230.png\" alt=\"rId43\" width=\"309\" height=\"90\"><br>दो अक्षरों का स्थान अपरिवर्तित रहेगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the correct option that when filled in the blanks in the same sequence will make&nbsp;the series logically complete.<br>QUAI_Q_YIL_U_ILQUU_L</p>",
                    question_hi: "<p>23. उस सही विकल्प का चयन कीजिए जिसे दी गई शृंखला के रिक्त स्थानों में उसी क्रम में रखने पर श्रृंखला&nbsp;तार्किक रूप से पूरी हो जाएगी।<br>QUAI_Q_YIL_U_ILQUU_L</p>",
                    options_en: [
                        "<p>LUQWI</p>",
                        "<p>LIQAU</p>",
                        "<p>LXIUQ</p>",
                        "<p>LWQUI</p>"
                    ],
                    options_hi: [
                        "<p>LUQWI</p>",
                        "<p>LIQAU</p>",
                        "<p>LXIUQ</p>",
                        "<p>LWQUI</p>"
                    ],
                    solution_en: "<p>23.(a) <br><strong>Logic:-</strong> third letter from right is decreased by 2.<br>QUAI<span style=\"text-decoration: underline;\"><strong>L</strong></span> / Q<span style=\"text-decoration: underline;\"><strong>U</strong></span>YIL / <span style=\"text-decoration: underline;\"><strong>Q</strong></span>U<span style=\"text-decoration: underline;\"><strong>W</strong></span>IL / QUU<span style=\"text-decoration: underline;\"><strong>I</strong></span>L</p>",
                    solution_hi: "<p>23.(a) <strong>तर्क:-</strong> दाएं से तीसरा अक्षर 2 कम हो जाता है।<br>QUAI<span style=\"text-decoration: underline;\"><strong>L</strong></span> / Q<span style=\"text-decoration: underline;\"><strong>U</strong></span>YIL / <span style=\"text-decoration: underline;\"><strong>Q</strong></span>U<span style=\"text-decoration: underline;\"><strong>W</strong></span>IL / QUU<span style=\"text-decoration: underline;\"><strong>I</strong></span>L</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. If 15 December 2006 is a Friday, then what will the day of the week on 13 October 2009?</p>",
                    question_hi: "<p>24. यदि 15 दिसम्बर 2006 को शुक्रवार है, तो 13 अक्टूबर 2009 को कौन-सा दिन होगा?</p>",
                    options_en: [
                        "<p>Friday</p>",
                        "<p>Monday</p>",
                        "<p>Saturday</p>",
                        "<p>Tuesday</p>"
                    ],
                    options_hi: [
                        "<p>शुक्रवार</p>",
                        "<p>सोमवार</p>",
                        "<p>शनिवार</p>",
                        "<p>मंगलवार</p>"
                    ],
                    solution_en: "<p>24.(d) 15 December 2006 is Friday. On moving from 2006 to 2009 the odd days are <br>+ 1 + 2 + 1 = 4. Friday + 4 = Tuesday. We have reached till 15 Dec 2009, now we have to go back to 13 october . So the number of days are 15 + 30 + 18 = 63. On dividing 63 by 7 the remainder = 0. Tuesday - 0 = Tuesday.</p>",
                    solution_hi: "<p>24.(d) 15 दिसंबर 2006 को शुक्रवार है. 2006 से 2009 तक जाने पर विषम दिन हैं <br>+ 1 + 2 + 1 = 4. शुक्रवार + 4 = मंगलवार. हम 15 दिसंबर 2009 तक पहुंच चुके हैं, अब हमें 13 अक्टूबर तक जाना है। अतः दिनों की संख्या 15 + 30 + 18 = 63 है। 63 को 7 से विभाजित करने पर शेषफल = 0। मंगलवार - 0 = मंगलवार।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo;are interchanged?<br>315 &times; 15 &minus; 93 + 16 &divide; 7 = ?</p>",
                    question_hi: "<p>25. यदि &lsquo;+&rsquo; और &lsquo;&minus;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न-चिन्ह (?) के स्थान पर क्या आएगा?<br>315 &times; 15 &minus; 93 + 16 &divide; 7 = ?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>8</p>",
                        "<p>2</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>8</p>",
                        "<p>2</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>25.(c) <strong>Given:-</strong> 315 &times; 15 - 93 + 16 &divide;&nbsp;7<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>315 &divide; 15 + 93 - 16 &times; 7<br>21 + 93 - 112<br>114 - 112 = 2</p>",
                    solution_hi: "<p>25.(c)<strong> दिया गया है:</strong>- 315 &times; 15 - 93 + 16 &divide;&nbsp;7<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>315 &divide; 15 + 93 - 16 &times; 7<br>21 + 93 - 112<br>114 - 112 = 2</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Who among the following has written the book \'The Philosophy of the Bomb\'?</p>",
                    question_hi: "<p>26. \'द फिलॉसफी ऑफ द बम (The philosophy of the bomb)\' पुस्तक निम्नलिखित में से किसने लिखी है?</p>",
                    options_en: [
                        "<p>Alpana Dutt</p>",
                        "<p>Bhagat Singh</p>",
                        "<p>Bhagwati Charan Vohra</p>",
                        "<p>Surya Sen</p>"
                    ],
                    options_hi: [
                        "<p>अल्पना दत्त</p>",
                        "<p>भगत सिंह</p>",
                        "<p>भगवती चरण वोहरा</p>",
                        "<p>सूर्य सेन</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>Bhagwati Charan Vohra</strong> was born in July 1904 in Lahore, which is now in Pakistan. He was associated with the Naujawan Bharat Sabha. Books and Authors: <AUTHORS>
                    solution_hi: "<p>26.(c) <strong>भगवती चरण वोहरा </strong>का जन्म जुलाई 1904 में लाहौर में हुआ था, जो अब पाकिस्तान में है। वे नौजवान भारत सभा से जुड़े थे। पुस्तकें एवं उनके लेखक: &lsquo;व्हाई आई एम एन एथिस्ट&rsquo;- भगत सिंह, &lsquo;कॉन्क्वेस्ट ऑफ सेल्फ&rsquo; - महात्मा गांधी, &lsquo;स्केप्टर्ड फ्लूट&rsquo; - सरोजिनी नायडू, सावित्री- अरबिंदो घोष।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Rahul decided to take a walk through a forest early in the morning. While he was inside the dense forest, he could see the beautiful sunrays through the small gaps within trees and clearly see the path taken by those rays. Of which effect was this a result ?</p>",
                    question_hi: "<p>27.राहुल, सुबह-सुबह एक वन में टहलने का फैसला करता है। जब वह सघन वन के अंदर था, तो वह वृक्षों के बीच छोटे अंतराल के माध्यम से सुंदर सूर्य किरणों को देख सकता था और उन किरणों के पथ को स्पष्ट रूप से देख सकता था। यह किस प्रभाव का परिणाम था?</p>",
                    options_en: [
                        "<p>Reflection</p>",
                        "<p>Diffraction</p>",
                        "<p>Dispersion</p>",
                        "<p>Tyndall effect</p>"
                    ],
                    options_hi: [
                        "<p>परावर्तन</p>",
                        "<p>विवर्तन</p>",
                        "<p>विक्षेपण</p>",
                        "<p>टिंडल प्रभाव</p>"
                    ],
                    solution_en: "<p>27.(d) <strong>Tyndall effect.</strong> It happens due to the scattering of light by the particles of dust and smoke in the air. Example - When a beam of light is directed at a glass of milk, the light is scattered; When a torch is switched on in a foggy environment, the path of the light becomes visible. According to the Law of reflection angle of incidence is always equal to angle of reflection. Dispersion - Splitting of white light into its constituent colors.</p>",
                    solution_hi: "<p>27.(d) <strong>टिन्डल प्रभाव।</strong> ऐसा वायु में धूल और धुएं के कणों द्वारा प्रकाश के प्रकीर्णन के कारण होता है। उदाहरण - जब प्रकाश की किरणे दूध के गिलास की ओर निर्देशित की जाती है, तो प्रकाश फ़ैल जाता है; जब कोहरे के वातावरण में टॉर्च को जलाया जाता है तो प्रकाश का मार्ग दिखाई देने लगता है। परावर्तन के नियम के अनुसार आपतन कोण सदैव परावर्तन कोण के बराबर होता है। प्रकीर्णन - श्वेत प्रकाश का उसके विभिन्न संघटक रंगों में पृथक किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Where among the following is the headquarters of Indian Bureau of Mines located ?</p>",
                    question_hi: "<p>28. भारतीय खान ब्यूरो का मुख्यालय निम्नलिखित में से कहां स्थित है ?</p>",
                    options_en: [
                        "<p>Jabalpur</p>",
                        "<p>Kanpur</p>",
                        "<p>Ranchi</p>",
                        "<p>Nagpur</p>"
                    ],
                    options_hi: [
                        "<p>जबलपुर</p>",
                        "<p>कानपुर</p>",
                        "<p>रांची</p>",
                        "<p>नागपुर</p>"
                    ],
                    solution_en: "<p>28.(d) <strong>Nagpur.</strong> The Indian Bureau of Mines (IBM) is a government agency responsible for promoting the development and conservation of mineral resources in India. The IBM was established on 1st March,1948 and is under the Ministry of Mines, Government of India.</p>",
                    solution_hi: "<p>28.(d) <strong>नागपुर।</strong> भारतीय खान ब्यूरो (IBM) एक सरकारी एजेंसी है जो भारत में खनिज संसाधनों के विकास और संरक्षण को बढ़ावा देने के लिए उत्तरदायी है। IBM की स्थापना 1 मार्च, 1948 को हुई थी और यह भारत सरकार के खान मंत्रालय के अधीन है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. What is India&rsquo;s rank in the Global Peace Index (GPI) 2024?</p>",
                    question_hi: "<p>29. वैश्विक शांति सूचकांक (GPI) 2024 में भारत का स्थान क्या है ?</p>",
                    options_en: [
                        "<p>112th</p>",
                        "<p>116th</p>",
                        "<p>120th</p>",
                        "<p>110th</p>"
                    ],
                    options_hi: [
                        "<p>112वाँ</p>",
                        "<p>116वाँ</p>",
                        "<p>120वाँ</p>",
                        "<p>110वाँ</p>"
                    ],
                    solution_en: "<p>29.(b) <strong>116th.</strong> The Global Peace Index (GPI) 2024, published by the Institute for Economics &amp; Peace (IEP), provides a comprehensive measure of global peacefulness, evaluating 163 independent states and territories. Most Peaceful Country: Iceland.</p>",
                    solution_hi: "<p>29.(b) <strong>116वाँ।</strong> इंस्टीट्यूट फॉर इकोनॉमिक्स एंड पीस (IEP) द्वारा प्रकाशित वैश्विक शांति सूचकांक (GPI) 2024, 163 स्वतंत्र राज्यों और क्षेत्रों का मूल्यांकन करते हुए वैश्विक शांति का एक व्यापक माप प्रदान करता है। सबसे शांतिपूर्ण देश: आइसलैंड है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. When electricity is passed through water, what kind of chemical reaction occurs?</p>",
                    question_hi: "<p>30. जब जल में विद्युत धारा प्रवाहित की जाती है, तो किस प्रकार की रासायनिक अभिक्रिया होती है?</p>",
                    options_en: [
                        "<p>Decomposition</p>",
                        "<p>Displacement</p>",
                        "<p>Double displacement</p>",
                        "<p>Combination</p>"
                    ],
                    options_hi: [
                        "<p>अपघटन</p>",
                        "<p>विस्थापन</p>",
                        "<p>दोहरा विस्थापन</p>",
                        "<p>संयोजन</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>Decomposition.</strong> This process is known as electrolysis which breaks down water (H<sub>2</sub>O) into its constituent gases, hydrogen (H<sub>2</sub>) and oxygen (O<sub>2</sub>). Reactions in which a compound splits up into two or more simpler substances are known as decomposition reactions. Displacement: Reactions in which a more reactive element displaces a less reactive element from its compound. Example - When iron (Fe) reacts with copper sulfate (CuSO<sub>4</sub>). Double displacement: Reactions in which two compounds react by an exchange of ions to form two new compounds. Example - Silver nitrate (AgNO<sub>3</sub>) reacts with magnesium chloride (MgCl<sub>2</sub>). Combination Reaction: Two or more substances combine to form a single compound. Example - Formation of water 2H<sub>2</sub>(g) + O<sub>2</sub>(g) &rarr; 2H<sub>2</sub>O(l).</p>",
                    solution_hi: "<p>30.(a) <strong>अपघटन।</strong> इस प्रक्रिया को इलेक्ट्रोलिसिस के रूप में जाना जाता है जो जल (H<sub>2</sub>O) को उसके घटक गैसों, हाइड्रोजन (H<sub>2</sub>) और ऑक्सीजन (O<sub>2</sub>) में तोड़ देता है। ऐसी अभिक्रियाएं जिसमें एक यौगिक दो या दो से अधिक सरल पदार्थों में विभाजित हो जाता है, अपघटन अभिक्रियाएं कहलाती हैं। विस्थापन: ऐसी अभिक्रियाएं जिसमें एक अधिक अभिक्रियाशील तत्व अपने यौगिक से एक कम अभिक्रियाशील तत्व को विस्थापित करता है। उदाहरण - जब आयरन (Fe) कॉपर सल्फेट (CuSO<sub>4</sub>) के साथ अभिक्रिया करता है। दोहरा विस्थापन: ऐसी अभिक्रियाएं जिसमें दो यौगिक आयनों के आदान-प्रदान द्वारा दो नए यौगिक बनाते हैं। उदाहरण - सिल्वर नाइट्रेट (AgNO<sub>3</sub>) मैग्नीशियम क्लोराइड (MgCl<sub>2</sub>) के साथ अभिक्रिया करता है। संयोजन अभिक्रिया: दो या दो से अधिक पदार्थ मिलकर एकल यौगिक बनाते हैं। उदाहरण - 2H<sub>2</sub>(g) + O<sub>2</sub>(g) &rarr; 2H<sub>2</sub>O(l). जल का निर्माण।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. According to the Indian Constitution, how many organs are there in the government of India ?</p>",
                    question_hi: "<p>31. भारतीय संविधान के अनुसार, भारत सरकार के कितने अंग हैं ?</p>",
                    options_en: [
                        "<p>Two</p>",
                        "<p>Three</p>",
                        "<p>Four</p>",
                        "<p>Five</p>"
                    ],
                    options_hi: [
                        "<p>दो</p>",
                        "<p>तीन</p>",
                        "<p>चार</p>",
                        "<p>पांच</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>Three.</strong> According to the Constitution, the three organs of government are the Legislature, Executive, and Judiciary. Together, they perform the functions of governance, maintain law and order, and ensure the welfare of the people.</p>",
                    solution_hi: "<p>31.(b) <strong>तीन।</strong> संविधान के अनुसार सरकार के तीन अंग हैं - विधायिका, कार्यपालिका और न्यायपालिका। ये तीनों मिलकर शासन का काम करते हैं, कानून और व्यवस्था बनाए रखते हैं और लोगों का कल्याण सुनिश्चित करते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which statement gives the details of the estimated revenue and expenditure of the government ?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन-सा विवरण सरकार के अनुमानित राजस्व और व्यय का विवरण देता है?</p>",
                    options_en: [
                        "<p>Income statement</p>",
                        "<p>Balance sheet</p>",
                        "<p>Annual financial statement</p>",
                        "<p>Balance of payments</p>"
                    ],
                    options_hi: [
                        "<p>आय विवरण</p>",
                        "<p>तुलनपत्र</p>",
                        "<p>वार्षिक वित्तीय विवरण</p>",
                        "<p>भुगतान संतुलन</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>Annual financial statement</strong>. Under Article 112 of the Constitution, a statement of estimated receipts and expenditure of the Government of India has to be laid before Parliament in respect of every financial year which runs from 1st April to 31st March. The Annual Financial Statement shows the receipts and payments of Government under the three parts in which Government accounts are kept: (i) Consolidated Fund, (ii) Contingency Fund and (iii) Public Account.</p>",
                    solution_hi: "<p>32.(c) <strong>वार्षिक वित्तीय विवरण।</strong> संविधान के अनुच्छेद 112 के तहत, 1 अप्रैल से 31 मार्च तक चलने वाले प्रत्येक वित्तीय वर्ष के संबंध में भारत सरकार की अनुमानित प्राप्तियों और व्यय का विवरण संसद के समक्ष रखा जाता है । वार्षिक वित्तीय विवरण सरकार की प्राप्तियों और भुगतानों को तीन भागों के अंतर्गत दर्शाता है जिनमें सरकारी खाते रखे जाते हैं: (i) समेकित निधि, (ii) आकस्मिकता निधि और (iii) सार्वजनिक खाता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following bacteria grow in milk and convert it to curd ?</p>",
                    question_hi: "<p>33. निम्नलिखित में से कौन-सा बैक्टीरिया दूध में पनप कर उसे दही में बदल देता है ?</p>",
                    options_en: [
                        "<p>E. curdae</p>",
                        "<p>Salmonella typhi</p>",
                        "<p>Lactic acid bacteria</p>",
                        "<p>Salmonella lactae</p>"
                    ],
                    options_hi: [
                        "<p>E. कर्डी</p>",
                        "<p>साल्मोनेला टाइफी</p>",
                        "<p>लैक्टिक एसिड बैक्टीरिया</p>",
                        "<p>साल्मोनेला लैक्टे</p>"
                    ],
                    solution_en: "<p>33.(c) <strong>Lactic acid bacteria.</strong> This group of bacteria, which includes species like Lactobacillus, is responsible for fermenting lactose in milk to produce lactic acid, thereby converting milk into curd. Salmonella typhi - causes typhoid fever.</p>",
                    solution_hi: "<p>33.(c) <strong>लैक्टिक एसिड बैक्टीरिया।</strong> बैक्टीरिया का यह समूह, जिसमें लैक्टोबैसिलस जैसी प्रजातियाँ शामिल हैं, दूध में लैक्टोज को किण्वित करके लैक्टिक एसिड बनाने के लिए उत्तरदायी है, जिससे दूध दही में बदल जाता है। साल्मोनेला टाइफी - टाइफाइड बुखार का कारण बनता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who was the founder of Agra Gharana?</p>",
                    question_hi: "<p>34. आगरा घराने के संस्थापक कौन थे?</p>",
                    options_en: [
                        "<p>Nathan Peerbaksh</p>",
                        "<p>Hassu Khan</p>",
                        "<p>Haddu Khan</p>",
                        "<p>Khuda Baksh</p>"
                    ],
                    options_hi: [
                        "<p>नाथन पीरबख्श (Nathan Peerbaksh)</p>",
                        "<p>हस्सु खां (Hassu Khan)</p>",
                        "<p>हद्दू खां (Haddu Khan)</p>",
                        "<p>खुदाबख्श (Khuda Baksh)</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Khuda Baksh</strong> (1790&ndash;1880 AD). He introduced the \"Khayal\" style of the Gwalior Gharana into the Agra Gharana, which he learned from Natthan Paribaksh of Gwalior. Gharanas of Indian Music and Founder: Benaras Gharana - Pandit Ram Sahai, Indore Gharana - Ustad Amir Khan, Ramdasi Gharana - Baba Ramdas Bairagi, Rampur-Sahaswan Gharana - Ustad Inayat Hussain Khan.</p>",
                    solution_hi: "<p>34.(d) <strong>खुदाबख्श</strong> (1790-1880 ई.)। उन्होंने ग्वालियर घराने की \"ख्याल\" शैली को आगरा घराने में पेश किया, जिसे उन्होंने ग्वालियर के नत्थन परिबक्श से सीखा था। भारतीय संगीत के घराने और संस्थापक: बनारस घराना - पंडित राम सहाय, इंदौर घराना - उस्ताद अमीर खान, रामदासी घराना - बाबा रामदास बैरागी, रामपुर-सहसवान घराना - उस्ताद इनायत हुसैन ख़ाँ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Dussehra is celebrated in ___________ month of the Hindu calendar.</p>",
                    question_hi: "<p>35. दशहरा, हिंदू पंचांग के ____________ मास में मनाया जाता है।</p>",
                    options_en: [
                        "<p>Asada</p>",
                        "<p>Ashvina</p>",
                        "<p>Kartika</p>",
                        "<p>Chaitra</p>"
                    ],
                    options_hi: [
                        "<p>आषाढ़</p>",
                        "<p>अश्विन</p>",
                        "<p>कार्तिक</p>",
                        "<p>चैत्र</p>"
                    ],
                    solution_en: "<p>35.(b) <strong>Ashvina.</strong> The tenth day after Sharad Navratri is celebrated as Dussehra or Vijayadashami. Navratri, meaning \"nine nights,\" is also known as Durga Puja and is an important Hindu festival that honors the divine feminine. It lasts for 9 days during the month of Ashvin, which usually falls in September-October in the Gregorian calendar. Fairs &amp; Festivals of India along with months: January - Makar Sankranti, Pongal. February - Surajkund Crafts, Desert Festival Jaisalmer. March - Holi, Gudi Padwa.</p>",
                    solution_hi: "<p>35.(b) <strong>अश्विन।</strong> शरद नवरात्रि के बाद दसवें दिन दशहरा या विजयादशमी के रूप में मनाया जाता है। नवरात्रि, जिसका अर्थ है \"नौ रातें\", को दुर्गा पूजा के रूप में भी जाना जाता है और यह एक महत्वपूर्ण हिंदू त्योहार है जो दिव्य स्त्री का सम्मान करता है। यह अश्विन के महीने के दौरान 9 दिनों तक चलता है, जो आमतौर पर ग्रेगोरियन कैलेंडर में सितंबर-अक्टूबर में पड़ता है। महीनों सहित भारत के मेले और त्यौहार : जनवरी - मकर संक्रांति, पोंगल। फरवरी - सूरजकुंड शिल्प, रेगिस्तान महोत्सव जैसलमेर। मार्च - होली, गुड़ी पड़वा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The oldest Veda is the Rigveda, composed about ________ years ago.</p>",
                    question_hi: "<p>36. वेदों में सबसे पुराना वेद ऋग्वेद है, जिसकी रचना लगभग ________ वर्ष पहले हुई</p>",
                    options_en: [
                        "<p>2500</p>",
                        "<p>5000</p>",
                        "<p>3500</p>",
                        "<p>2000</p>"
                    ],
                    options_hi: [
                        "<p>2500</p>",
                        "<p>5000</p>",
                        "<p>3500</p>",
                        "<p>2000</p>"
                    ],
                    solution_en: "<p>36.(c) <strong>3500.</strong> The Rig Veda is also the oldest book in Sanskrit or any Indo-European language. It has 1028 hymns called &lsquo;Suktas&rsquo;. It is a collection of 10 books called &lsquo;Mandalas.&rsquo; Four Vedas: The Rig Veda, The Sama Veda, The Yajur Veda, and The Atharva Veda.</p>",
                    solution_hi: "<p>36.(c) <strong>3500</strong> । ऋग्वेद संस्कृत या किसी भी इंडो-यूरोपीय भाषा में सबसे पुरानी ग्रंथ है। इसमें 1028 भजन हैं जिन्हें \'सूक्त\' कहा जाता है। यह 10 पुस्तकों का संग्रह है जिन्हें \'मंडल\' कहा जाता है। चार वेद: ऋग्वेद, सामवेद, यजुर्वेद और अथर्ववेद।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. Which of the following terms is NOT related with cricket ?<br />I. Eagle<br />II. Basket",
                    question_hi: "37. निम्नलिखित में से किस शब्द का संबंध क्रिकेट से नहीं है ?<br />I. ईगल (Eagle)<br />II. बास्केट (Basket)",
                    options_en: [
                        " Neither I nor II",
                        " Only I",
                        " Only II",
                        " Both I and II"
                    ],
                    options_hi: [
                        " न तो । और न ही II",
                        " केवल ।",
                        " केवल II",
                        " । और II दोनों"
                    ],
                    solution_en: "<p>37.(d) <strong>Both I and II.</strong> The term Eagle is related to Golf sports. The term \"basket\" is related to Basketball. Terminologies related to Cricket: Wicket, Bouncer, Batsman, Bowler, Century, All Out, All-rounder, Bye, Catch, etc.</p>",
                    solution_hi: "<p>37.(d) <strong>। और II</strong> दोनों। ईगल शब्द गोल्फ़ खेल से संबंधित है। \"बास्केट\" शब्द बास्केटबॉल से संबंधित है। क्रिकेट से संबंधित शब्दावली : विकेट, बाउंसर, बैट्समैन, बॉलर, सेंचुरी, ऑल आउट, ऑलराउंडर, बाई, कैच, आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Find the acceleration (in m/s&sup2;) of a body which accelerates from 5 m/s to 10 m/s in 2 seconds.</p>",
                    question_hi: "<p>38. उस पिंड का त्वरण (m/s&sup2; में ) ज्ञात कीजिए जो 2 सेकंड में 5 m/s से 10 m/s तक त्वरित हो जाती है।</p>",
                    options_en: [
                        "<p>5 m/sec&sup2;</p>",
                        "<p>7.5 m/sec&sup2;</p>",
                        "<p>10 m/sec&sup2;</p>",
                        "<p>2.5 m/sec&sup2;</p>"
                    ],
                    options_hi: [
                        "<p>5 m/sec&sup2;</p>",
                        "<p>7.5 m/sec&sup2;</p>",
                        "<p>10 m/sec&sup2;</p>",
                        "<p>2.5 m/sec&sup2;</p>"
                    ],
                    solution_en: "<p>38.(d)<strong> 2.5 m/sec&sup2;.</strong> The formula for acceleration is: a = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">v</mi><mo>-</mo><mi mathvariant=\"normal\">u</mi><mo>)</mo></mrow><mi mathvariant=\"normal\">t</mi></mfrac></math>, <br>where: a = acceleration<br>v = final velocity<br>u = initial velocity<br>t = time taken<br>Given values:<br>Initial velocity (u) = 5 m/s<br>Final velocity (v) = 10 m/s<br>Time (t) = 2 seconds<br>Putting these into the formula: <br>a = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>a = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>a = 2.5 m/s&sup2;.</p>",
                    solution_hi: "<p>38.(d)<strong> 2.5 m/sec&sup2;</strong>. त्वरण का सूत्र है:a = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">v</mi><mo>-</mo><mi mathvariant=\"normal\">u</mi><mo>)</mo></mrow><mi mathvariant=\"normal\">t</mi></mfrac></math>,<br>जहाँ: a = त्वरण<br>v = अंतिम वेग<br>u = प्रारंभिक वेग<br>t = लिया गया समय<br>दिए गए मान :<br>प्रारंभिक वेग (u) = 5 m/s<br>अंतिम वेग (v) = 10 m/s<br>समय (t) = 2 सेकंड<br>इन्हें सूत्र में रखने पर:<br>a = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>a = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>a = 2.5 m/s&sup2;.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In which year, Alexander invaded India?</p>",
                    question_hi: "<p>39. सिकंदर ने किस वर्ष भारत पर आक्रमण किया ?</p>",
                    options_en: [
                        "<p>400 BC</p>",
                        "<p>126 BC</p>",
                        "<p>326 BC</p>",
                        "<p>550 BC</p>"
                    ],
                    options_hi: [
                        "<p>400 ई. पू</p>",
                        "<p>126 ई. पू</p>",
                        "<p>326 ई. पू</p>",
                        "<p>550 ई. पू</p>"
                    ],
                    solution_en: "<p>39.(c) <strong>326 BC.</strong> The invasion of Alexander took place during his campaign to expand his empire into the Indian subcontinent. He crossed the Indus River and fought the famous Battle of the Hydaspes against King Porus, which took place near the river Jhelum in present-day Punjab, Pakistan. At the time of Alexander\'s invasion, Ghana Nanda was the king of Magadha.</p>",
                    solution_hi: "<p>39.(c) <strong>326</strong> <strong>ई. पू।</strong> सिकंदर का आक्रमण भारतीय उपमहाद्वीप में अपने साम्राज्य का विस्तार करने के अभियान के दौरान हुआ था। उसने सिंधु नदी पार की और राजा पोरस के खिलाफ प्रसिद्ध हाइडस्पेस की लड़ाई लड़ी, जो वर्तमान पंजाब, पाकिस्तान में झेलम नदी के पास हुई थी। सिकंदर के आक्रमण के समय, घनानंद मगध का राजा था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Where was the third edition of World Food India 2024 held?</p>",
                    question_hi: "<p>40. वर्ल्ड फूड इंडिया 2024 का तीसरा संस्करण कहाँ आयोजित किया गया था?</p>",
                    options_en: [
                        "<p>Mumbai</p>",
                        "<p>Bengaluru</p>",
                        "<p>New Delhi</p>",
                        "<p>Hyderabad</p>"
                    ],
                    options_hi: [
                        "<p>मुंबई</p>",
                        "<p>बेंगलुरु</p>",
                        "<p>नई दिल्ली</p>",
                        "<p>हैदराबाद</p>"
                    ],
                    solution_en: "<p>40.(c) <strong>New Delhi</strong>. <br>The third edition of&nbsp;World Food India 2024 took place from September 19 to 22 at Bharat Mandapam, New Delhi, aiming to boost the food processing and allied sectors in India.</p>",
                    solution_hi: "<p>40.(c) <strong>नई दिल्ली।</strong> <br>वर्ल्ड फूड इंडिया 2024 का तीसरा संस्करण 19 से 22 सितंबर तक भारत मंडपम, नई दिल्ली में हुआ, जिसका उद्देश्य भारत में खाद्य प्रसंस्करण और संबद्ध क्षेत्रों को बढ़ावा देना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The famous musician Aban Mistry is associated with which instrument?</p>",
                    question_hi: "<p>41. प्रसिद्ध संगीतकार अबन मिस्त्री किस वाद्य-यंत्र से संबंधित हैं?</p>",
                    options_en: [
                        "<p>Flute</p>",
                        "<p>Tabla</p>",
                        "<p>Shehnai</p>",
                        "<p>Drums</p>"
                    ],
                    options_hi: [
                        "<p>बाँसुरी</p>",
                        "<p>तबला</p>",
                        "<p>शहनाई</p>",
                        "<p>ड्रम</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>Tabla</strong> - Percussion instrument. Aban Mistry known as the first woman solo tabla player in India. Instruments and their exponents - Tabla: Zakir Hussain, Allah Rakha, Pt. Kishan Maharaj. Flute: Hari Prasad Chaurasia, Pannalal Ghosh. Shehnai: Bismillah Khan, Krishna Ram Chaudhary. Guitar: Braj Bhushan Kabra.</p>",
                    solution_hi: "<p>41.(b) <strong>तबला</strong> - ताल वाद्य। अबन मिस्त्री को भारत की प्रथम महिला एकल तबला वादक के रूप में जाना जाता है। वाद्ययंत्र और उनके प्रतिपादक तबला &ldquo; जाकिर हुसैन, अल्लाह रक्खा, पं. किशन महाराज. बांसुरी: हरि प्रसाद चौरसिया, पन्नालाल घोष। शहनाई: बिस्मिल्लाह खान, कृष्णा राम चौधरी। गिटार: ब्रज भूषण काबरा आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following options is INCORRECT?<br>In order to become a member of Vidhan Sabha, a person must:</p>",
                    question_hi: "<p>42 निम्नलिखित में से कौन-सा विकल्प गलत है ?<br>विधानसभा का सदस्य बनने के लिए, एक व्यक्ति को चाहिए :</p>",
                    options_en: [
                        "<p>have his/her name in the voters&rsquo; list</p>",
                        "<p>have attained the age of 21 years</p>",
                        "<p>be a citizen of India</p>",
                        "<p>not hold any office of profit</p>"
                    ],
                    options_hi: [
                        "<p>मतदाता सूची में उसका/उसकी नाम हो</p>",
                        "<p>21 वर्ष की आयु प्राप्त कर चुके हो</p>",
                        "<p>भारत का नागरिक हो</p>",
                        "<p>लाभ के किसी पद पर न हो</p>"
                    ],
                    solution_en: "<p>42.(b) <strong>have attained the age of 21 years. </strong>The minimum age to be a member of the Legislative Assembly ( Vidhan Sabha) is 25 years. Legislative Assembly of a state consists of not more than 500 and not less than 60 members (Legislative Assembly of Sikkim has 32 members vide Article 371F of the Constitution) chosen by direct election from territorial constituencies in the state.</p>",
                    solution_hi: "<p>42.(b) <strong>21 वर्ष की आयु प्राप्त कर चुके हो।</strong> विधान सभा (Legislative Assembly) का सदस्य बनने के लिए न्यूनतम आयु 25 वर्ष होनी चाहिए। किसी राज्य की विधान सभा में 500 से अधिक और 60 से कम सदस्य नहीं होना चाहिए (संविधान के अनुच्छेद 371F के अनुसार सिक्किम की विधान सभा में 32 सदस्य हैं) जो राज्य के प्रादेशिक निर्वाचन क्षेत्रों से प्रत्यक्ष चुनाव द्वारा चुने जाते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Ebony and Mahogany trees are found in which type of forests?</p>",
                    question_hi: "<p>43. आबनूस और महोगनी के पेड़ किस प्रकार के वनों में पाए जाते हैं?</p>",
                    options_en: [
                        "<p>Mangrove forests</p>",
                        "<p>Tropical deciduous forests</p>",
                        "<p>Tropical evergreen forests</p>",
                        "<p>Montane forests</p>"
                    ],
                    options_hi: [
                        "<p>मैंग्रोव वन</p>",
                        "<p>उष्णकटिबंधीय पर्णपाती वन</p>",
                        "<p>उष्णकटिबंधीय सदाबहार वन</p>",
                        "<p>पर्वतीय वन</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>Tropical evergreen forests.</strong> In India, these forests are primarily found in the Western Ghats, upper Assam, and the islands of Lakshadweep, Andaman, and Nicobar. Important tree species : Tropical Evergreen Forest - Rosewood, Rubber, Jack wood, and Bamboo. Mangrove Forest - Uppu ponna, Boddu ponna, Urada, Mada, Telli Mada, Gundu mada, Kadili, and Bella. Tropical deciduous forest - Teak, Sandalwood, Neem, Peepal, Sal, Bamboo, Kusum, Arjun, Khair, Mulberry. Montane forest - Oak, Chestnut, Pine, Deodar, Silver fir, Spruce.</p>",
                    solution_hi: "<p>43.(c) <strong>उष्णकटिबंधीय सदाबहार वन।</strong> भारत में, ये वन मुख्य रूप से पश्चिमी घाट, ऊपरी असम और लक्षद्वीप, अंडमान और निकोबार के द्वीपों में पाए जाते हैं। महत्वपूर्ण वृक्ष प्रजातियाँ: उष्णकटिबंधीय सदाबहार वन - शीशम, रबड़, कटहल और बांस। मैंग्रोव वन - उप्पू पोन्ना, बोड्डू पोन्ना, उरद, माडा, तेली माडा, गुंडू माडा, कदिली और बेला। उष्णकटिबंधीय पर्णपाती वन - सागौन, चंदन, नीम, पीपल, साल, बांस, कुसुम, अर्जुन, खैर, शहतूत। पर्वतीय वन - ओक, चेस्टनट, पाइन, देवदार, सिल्वर फर, स्प्रूस।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In which year did Johannes Nicolaus Bronsted and Thomas Martin Lowry propose the fundamental concept of acids and bases?</p>",
                    question_hi: "<p>44. जोहान्स निकोलस ब्रोंस्टेड (Johannes Nicolaus Bronsted) और थॉमस मार्टिन लौरी (Thomas Martin Lowry) ने अम्ल और क्षार की मूल अवधारणा का प्रस्ताव किस वर्ष में दिया था?</p>",
                    options_en: [
                        "<p>1928</p>",
                        "<p>1921</p>",
                        "<p>1925</p>",
                        "<p>1923</p>"
                    ],
                    options_hi: [
                        "<p>1928</p>",
                        "<p>1921</p>",
                        "<p>1925</p>",
                        "<p>1923</p>"
                    ],
                    solution_en: "<p>44.(d) <strong>1923.</strong> According to Bronsted-Lowry theory, acid is a substance which donates an H<sup>+</sup> ion or a proton and forms its conjugate base and the base is a substance which accepts an H<sup>+</sup> ion or a proton and forms its conjugate acid.</p>",
                    solution_hi: "<p>44.(d) <strong>1923.</strong> ब्रोंस्टेड-लोरी सिद्धांत के अनुसार, अम्ल एक ऐसा पदार्थ है जो H<sup>+</sup> आयन या प्रोटॉन का त्याग करता है और अपना संयुग्मी क्षार बनाता है तथा क्षार एक ऐसा पदार्थ है जो H<sup>+</sup> आयन या प्रोटॉन ग्रहण करता है और अपना संयुग्मी अम्ल बनाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Cricket became an international game with the formation of the ___________ in 1909.</p>",
                    question_hi: "<p>45. 1909 में _________ के गठन के साथ क्रिकेट एक अंतर्राष्ट्रीय खेल बन गया।</p>",
                    options_en: [
                        "<p>ICC</p>",
                        "<p>IYC</p>",
                        "<p>IPL</p>",
                        "<p>BCC</p>"
                    ],
                    options_hi: [
                        "<p>आईसीसी (ICC)</p>",
                        "<p>आईवाईसी (IYC)</p>",
                        "<p>आईपीएल (IPL)</p>",
                        "<p>बीसीसी (BCC)</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>ICC</strong> (International Cricket Council) is the global governing body of cricket. It was founded as the Imperial Cricket Conference by representatives from Australia, England, and South Africa. Formation - 15 June 1909. Headquarters - Dubai (United Arab Emirates). Official language - English. CEO - Geoff Allardice.</p>",
                    solution_hi: "<p>45.(a) <strong>ICC</strong> (अंतर्राष्ट्रीय क्रिकेट परिषद) क्रिकेट की वैश्विक नियामक संस्था है। इसकी स्थापना ऑस्ट्रेलिया, इंग्लैंड और दक्षिण अफ्रीका के प्रतिनिधियों द्वारा इंपीरियल क्रिकेट कॉन्फ्रेंस के रूप में की गई थी। गठन - 15 जून 1909। मुख्यालय - दुबई (संयुक्त अरब अमीरात)। आधिकारिक भाषा - अंग्रेजी। CEO - ज्योफ एलार्डिस।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which glands help in digestion in the stomach?</p>",
                    question_hi: "<p>46. आमाशय में कौन सी ग्रंथियां पाचन में मदद करती हैं ?</p>",
                    options_en: [
                        "<p>Gastric glands</p>",
                        "<p>Pituitary</p>",
                        "<p>Thyroid</p>",
                        "<p>Pineal</p>"
                    ],
                    options_hi: [
                        "<p>गैस्ट्रिक ग्रंथियां</p>",
                        "<p>पिट्यूटरी</p>",
                        "<p>थायरॉयड</p>",
                        "<p>पीनियल</p>"
                    ],
                    solution_en: "<p>46.(a) <strong>Gastric glands</strong> in the stomach lining secrete digestive juices, including hydrochloric acid (HCl) and pepsinogen, which help break down proteins and activate digestive enzymes. Pituitary gland : Regulates hormone secretion, growth, and development. Thyroid gland : Regulates metabolism, growth, and development. Pineal gland : Regulates sleep-wake cycles and melatonin production.</p>",
                    solution_hi: "<p>46.(a)<strong> गैस्ट्रिक ग्रंथियाँ </strong>आमाशय की परत में हाइड्रोक्लोरिक एसिड (HCl) और पेप्सिनोजेन सहित पाचन रसों का स्राव करती हैं, जो प्रोटीन को तोड़ने और पाचन एंजाइमों को सक्रिय करने में मदद करते हैं। पिट्यूटरी ग्रंथि: हार्मोन स्राव, वृद्धि और विकास को नियंत्रित करती है। थायरॉयड ग्रंथि: उपापचय, वृद्धि और विकास को नियंत्रित करती है। पीनियल ग्रंथि: सोने-जागने के चक्र (स्लीप-वेक साइकल) और मेलाटोनिन स्राव को नियंत्रित करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following is in geographical proximity to Sri Lanka?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन-सा स्थान भौगोलिक रूप से श्रीलंका के समीप है?</p>",
                    options_en: [
                        "<p>Only Karaikal</p>",
                        "<p>Karaikal and Yanam</p>",
                        "<p>Only Mahe</p>",
                        "<p>Only Yanam</p>"
                    ],
                    options_hi: [
                        "<p>केवल कराईकल</p>",
                        "<p>कराईकल और यानम</p>",
                        "<p>केवल माहे</p>",
                        "<p>केवल यानम</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>Only Karaikal.</strong> Karaikal is a town in the Union Territory of Puducherry. Sri Lanka, formerly known as Ceylon, is an island nation in South Asia. Located in the Indian Ocean, southwest of the Bay of Bengal, it is separated from the Indian peninsula by the Gulf of Mannar and the Palk Strait. Sri Lanka shares a maritime border with the Maldives to the southwest and India to the northwest.</p>",
                    solution_hi: "<p>47.(a) <strong>केवल कराईकल। </strong>कराईकल पुडुचेरी के केंद्र शासित प्रदेश का एक शहर है। श्रीलंका, जिसे पहले सीलोन के नाम से जाना जाता था, दक्षिण एशिया में एक द्वीप राष्ट्र है। बंगाल की खाड़ी के दक्षिण-पश्चिम में हिंद महासागर में स्थित, यह मन्नार की खाड़ी और पाक जलडमरूमध्य द्वारा भारतीय प्रायद्वीप से अलग होता है। श्रीलंका दक्षिण-पश्चिम में मालदीव और उत्तर-पश्चिम में भारत के साथ समुद्री सीमा साझा करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. ______ unemployment refers to the time lag between jobs when an individual is searching for a new job or is switching between jobs.</p>",
                    question_hi: "<p>48. ______ बेरोजगारी से तात्पर्य नौकरियों के बीच के समय अंतराल से है, जब कोई व्यक्ति नई नौकरी की तलाश कर रहा होता है या नौकरियों के बीच स्विच कर रहा होता है।</p>",
                    options_en: [
                        "<p>Seasonal</p>",
                        "<p>Technological</p>",
                        "<p>Frictional</p>",
                        "<p>Structural</p>"
                    ],
                    options_hi: [
                        "<p>मौसमी</p>",
                        "<p>तकनीकी</p>",
                        "<p>घर्षणात्मक</p>",
                        "<p>संरचनात्मक</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>Frictional.</strong> Seasonal Unemployment : Unemployment due to seasonal fluctuations in industries (e.g., winter tourism). Technological Unemployment : Unemployment caused by technological advancements replacing jobs. Structural Unemployment : Long-term unemployment due to fundamental changes in the economy (e.g., industry decline).</p>",
                    solution_hi: "<p>48.(c) <strong>घर्षणात्मक।</strong> मौसमी बेरोज़गारी: उद्योगों में मौसमी उतार-चढ़ाव के कारण बेरोज़गारी (जैसे, शीतकालीन पर्यटन)। तकनीकी बेरोज़गारी: तकनीकी प्रगति के कारण नौकरियों में कमी के कारण बेरोजगारी। संरचनात्मक बेरोज़गारी: अर्थव्यवस्था में मूलभूत परिवर्तनों (जैसे, उद्योग में गिरावट) के कारण दीर्घकालिक बेरोज़गारी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. On which date is World Environmental Health Day celebrated?</p>",
                    question_hi: "<p>49. विश्व पर्यावरण स्वास्थ्य दिवस किस तिथि को मनाया जाता है?</p>",
                    options_en: [
                        "<p>September 22</p>",
                        "<p>September 25</p>",
                        "<p>September 30</p>",
                        "<p>October 5</p>"
                    ],
                    options_hi: [
                        "<p>22 सितंबर</p>",
                        "<p>25 सितंबर</p>",
                        "<p>30 सितंबर</p>",
                        "<p>5 अक्टूबर</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>September 25</strong>. The day focuses on raising awareness about environmental health issues. The theme for 2024 is \"Environmental Health: Creating Resilient Communities through Disaster Risk Reduction and Climate Change Mitigation and Adaptation\"</p>",
                    solution_hi: "<p>49. (b) <strong>25 सितंबर। </strong>यह दिन पर्यावरण स्वास्थ्य मुद्दों के बारे में जागरूकता बढ़ाने पर केंद्रित है। 2024 का थीम \"पर्यावरण स्वास्थ्य: आपदा जोखिम न्यूनीकरण और जलवायु परिवर्तन शमन और अनुकूलन के माध्यम से लचीला समुदाय बनाना\" है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Free throw is the sport term of_______.</p>",
                    question_hi: "<p>50. फ़्री थ्रो (Free throw) ____________का खेल शब्द है।</p>",
                    options_en: [
                        "<p>Football</p>",
                        "<p>Cricket</p>",
                        "<p>Badminton</p>",
                        "<p>Basketball</p>"
                    ],
                    options_hi: [
                        "<p>फुटबॉल</p>",
                        "<p>क्रिकेट</p>",
                        "<p>बैडमिंटन</p>",
                        "<p>बास्केटबॉल</p>"
                    ],
                    solution_en: "<p>50.(d) <strong>Basketball.</strong> Sports and Terminologies: Basketball - Slam Dunk, Alley-oop, Bank Shot, Double Dribble, Hook Shot. Football - Corner Kick, Goalkeeper, Offside, Penalty Kick, Counterattack. Cricket - LBW, Hit Wicket, Googly, Dead Ball, Bouncer. Badminton - Alley, Baseline, Centerline, Drive, Drop Shot, Net Shot, Wood Shot, Push Shot, etc.</p>",
                    solution_hi: "<p>50.(d) <strong>बास्केटबॉल।</strong> खेल और शब्दावली: बास्केटबॉल - स्लैम डंक, एली-ओप, बैंक शॉट, डबल ड्रिबल, हुक शॉट। फुटबॉल - कॉर्नर किक, गोलकीपर, ऑफसाइड, पेनाल्टी किक, काउंटरअटैक। क्रिकेट - LBW, हिट विकेट, गुगली, डेड बॉल, बाउंसर। बैडमिंटन - एली, बेसलाइन, सेंटरलाइन, ड्राइव, ड्रॉप शॉट, नेट शॉट, वुड शॉट, पुश शॉट, आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Which digits should come in the place of x and y, respectively, if the number 62684xy is divisible by both 8 and 5?</p>",
                    question_hi: "<p>51. यदि संख्या 62684xy, 8 और 5 दोनों से विभाज्य है, तो क्रमशः x और y के स्थान पर कौन से अंक आने चाहिए?</p>",
                    options_en: [
                        "<p>5 and 0</p>",
                        "<p>4 and 0</p>",
                        "<p>2 and 0</p>",
                        "<p>0 and 5</p>"
                    ],
                    options_hi: [
                        "<p>5 और 0</p>",
                        "<p>4 और 0</p>",
                        "<p>2 और 0</p>",
                        "<p>0 और 5</p>"
                    ],
                    solution_en: "<p>51.(b)<br>Given number = 62684xy<br>For divisibility of 5 :- last digit of the number must be 0 or 5.<br>Hence, <math display=\"inline\"><mi>y</mi></math> = 0 or 5<br>For divisibility of 8 :- last 3 digit must be divisible by 8.<br>therefore,<br>When, <math display=\"inline\"><mi>y</mi></math> = 0 then x = 0 or 4 or 8<br>From the option we can see that only option (b) satisfies the condition.</p>",
                    solution_hi: "<p>51.(b)<br>संख्या = 62684xy<br>5 की विभाज्यता के लिए:- संख्या का अंतिम अंक 0 या 5 होना चाहिए।<br>अतः, y&nbsp;= 0 या 5<br>8 से विभाज्य होने के लिए :- अंतिम 3 अंक 8 से विभाज्य होने चाहिए।<br>इसलिए,<br>जब, y&nbsp;= 0 तब x = 0 या 4 या 8<br>विकल्प से हम देख सकते हैं कि केवल विकल्प (b) ही शर्त को पूरा करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. In a triangle ABC, P and Q are two points on AB and AC, respectively, such that PQ parallel to BC. If AC = 5QC, then the ratio PQ : BC is equal to:</p>",
                    question_hi: "<p>52. एक त्रिभुज ABC में, P और Q क्रमशः AB और AC पर दो बिंदु इस प्रकार हैं कि PQ, BC के समानांतर है। यदि AC = 5QC है, तो अनुपात PQ : BC किसके बराबर है?</p>",
                    options_en: [
                        "<p>4 : 3</p>",
                        "<p>4 : 5</p>",
                        "<p>3 : 4</p>",
                        "<p>5 : 4</p>"
                    ],
                    options_hi: [
                        "<p>4 : 3</p>",
                        "<p>4 : 5</p>",
                        "<p>3 : 4</p>",
                        "<p>5 : 4</p>"
                    ],
                    solution_en: "<p>52.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204383.png\" alt=\"rId44\" width=\"159\" height=\"145\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mi>QC</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>1</mn></mfrac></math><br>AQ = 5 - 1 = 4 units<br>By Thales theorem : - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AQ</mi><mi>PQ</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mi>BC</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>PQ</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi>BC</mi></mfrac></math><br>PQ : BC = 4 : 5</p>",
                    solution_hi: "<p>52.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204383.png\" alt=\"rId44\" width=\"159\" height=\"145\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mi>QC</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>1</mn></mfrac></math><br>AQ = 5 - 1 = 4 इकाई <br>थेल्स प्रमेय के अनुसार:- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AQ</mi><mi>PQ</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mi>BC</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>PQ</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi>BC</mi></mfrac></math><br>PQ : BC = 4 : 5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. There are five baskets- A, B, C, D and E. Each basket has rings in it and the average number of rings in all five baskets is 42.<br>If basket D is removed, the average number of rings in the remaining baskets is 38. How many rings are there in basket D?</p>",
                    question_hi: "<p>53. पाँच टोकरियाँ- A, B, C, D और E. हैं। प्रत्येक टोकरी में छल्ले हैं और सभी पाँच टोकरियों में छल्लों की औसत संख्या 42 है। यदि टोकरी D को हटा दिया जाए, तो शेष टोकरियों में छल्लों की औसत संख्या 38 हो जाती है। टोकरी D में कितने छल्ले हैं?</p>",
                    options_en: [
                        "<p>42</p>",
                        "<p>58</p>",
                        "<p>38</p>",
                        "<p>64</p>"
                    ],
                    options_hi: [
                        "<p>42</p>",
                        "<p>58</p>",
                        "<p>38</p>",
                        "<p>64</p>"
                    ],
                    solution_en: "<p>53.(b)<br>No. of rings in basket D = new average + (42 - 38) &times;&nbsp;5<br>= 38 + 20 = 58</p>",
                    solution_hi: "<p>53.(b)<br>टोकरी D में छल्लों की संख्या = नया औसत + (42 - 38) &times;&nbsp;5<br>= 38 + 20 = 58</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If the number 6p5157q is divisible by 88, then p &times; q = ______ where p and q are single digit numbers.</p>",
                    question_hi: "<p>54. यदि संख्या 6p5157q, 88 से विभाज्य है जहां p और q एक अंक वाली संख्याएँ हैं तो p &times; q = ______ है।</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>18</p>",
                        "<p>20</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>18</p>",
                        "<p>20</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>54.(b) <br>88 = 11 &times; 8<br>For 6p5157q to be divisible by 8, its last 3 digits i.e. 57q must be divisible by 8.<br>For this q must be 6<br>Now, For 6p5157<strong>6</strong> to be divisible by 11, the difference of the sum of its alternate digits i.e. ( 6 + 5 + 5 + 6) - (7 + 1 + p) = 14 - p, must be divisible by 11.&nbsp;<br>&nbsp;For this, P must be 3.<br>So, p &times; q = 3 &times; 6 = 18</p>",
                    solution_hi: "<p>54.(b) <br>88 = 11 &times; 8<br>6p5157q को 8 से विभाज्य होने के लिए, इसके अंतिम 3 अंक यानी 57q को 8 से विभाज्य होना चाहिए।<br>इसके लिए q, 6 होना चाहिए<br>अब, 6p5157<strong>6</strong> को 11 से विभाज्य होने के लिए, इसके वैकल्पिक अंकों के योग का अंतर<br>यानी (6 + 5 + 5 + 6) - (7 + 1 + p) = 14 - p ,11 से विभाज्य होना चाहिए।<br>इसके लिए P, 3 होना चाहिए.<br>तो, p &times; q = 3 &times; 6 = 18</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. One company is offering a 25% discount on a particular product. Anurag visited a store. He was trying to find a deal that would allow him to save at least ₹390. How many minimum items should he purchase if each item costs ₹340 ?</p>",
                    question_hi: "<p>55. एक कंपनी एक किसी खास उत्पाद पर 25% की छूट दे रही है। अनुराग स्टोर पर गया। वह एक ऐसा सौदा खोजने की कोशिश कर रहा था जिससे वह कम से कम ₹390 की बचत कर सके। यदि प्रत्येक वस्तु का मूल्य ₹340 है तो उसे न्यूनतम कितनी वस्तुएँ खरीदनी चाहिए ?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>7</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>7</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>55.(d)<br>Discount at each item = 340 &times; 25% = 85<br>Let minimum item purchased by Anurag = x<br>Then, according to the question,<br>85x&nbsp;= 390 &rArr; x = 4.58<br>Hence, Anurag should purchase at least 5 items.</p>",
                    solution_hi: "<p>55.(d)<br>प्रत्येक वस्तु पर छूट = 340 &times; 25% = 85<br>माना अनुराग द्वारा खरीदी गई न्यूनतम वस्तु = x<br>फिर, प्रश्न के अनुसार,<br>85x&nbsp;= 390 &rArr; x = 4.58<br>अतः, अनुराग ने कम से कम 5 वस्तुएँ खरीदनी चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. There are 44 mangoes, 121 bananas and 11 apples, they have to be arranged in several rows in such a way that every row contains equal number of fruits and each row contains fruit of one type. What is the minimum number of rows?</p>",
                    question_hi: "<p>56. 44 आम, 121 केले और 11 सेब हैं, उन्हें कई पंक्तियों में इस प्रकार व्यवस्थित करना है कि प्रत्येक पंक्ति में समान संख्या में फल हों और प्रत्येक पंक्ति में एक प्रकार का फल हो। पंक्तियों की न्यूनतम संख्या कितनी है?</p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>15</p>",
                        "<p>14</p>",
                        "<p>13</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>15</p>",
                        "<p>14</p>",
                        "<p>13</p>"
                    ],
                    solution_en: "<p>56.(a)<br>Maximum number of fruits = HCF of (44,121,11) = 11<br>Minimum no of rows to arrange fruits = <math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>11</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>11</mn></mfrac></math> = 4 + 11 + 1 = 16</p>",
                    solution_hi: "<p>56.(a)<br>फलों की अधिकतम संख्या = (44,121,11) का महत्तम समापवर्तक= 11<br>फलों को व्यवस्थित करने के लिए पंक्तियों की न्यूनतम संख्या = <math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>11</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>11</mn></mfrac></math> = 4 + 11 + 1 = 16</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Express the following as decimals, respectively. <br>20%, 0.5%, 0.03%</p>",
                    question_hi: "<p>57. निम्नलिखित को क्रमशः दशमलव रूप में व्यक्त कीजिए।<br>20%, 0.5%, 0.03%</p>",
                    options_en: [
                        "<p>0.02, 0.005, 0.0003</p>",
                        "<p>0.02, 0.005, 0.003</p>",
                        "<p>0.2, 0.0005, 0.0003</p>",
                        "<p>0.2, 0.005, 0.0003</p>"
                    ],
                    options_hi: [
                        "<p>0.02, 0.005, 0.0003</p>",
                        "<p>0.02, 0.005, 0.003</p>",
                        "<p>0.2, 0.0005, 0.0003</p>",
                        "<p>0.2, 0.005, 0.0003</p>"
                    ],
                    solution_en: "<p>57.(d)<br>20% = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.2 <br>0.5% = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.005<br>0.03% = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>03</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.0003</p>",
                    solution_hi: "<p>57.(d)<br>20% = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.2 <br>0.5% = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.005<br>0.03% = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>03</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.0003</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Find the value of 3sin15&deg; - 4sin<sup>3</sup>15&deg;</p>",
                    question_hi: "<p>58. 3sin15&deg; - 4sin<sup>3</sup>15&deg; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>58.(c)<br>3 sin15&deg;- 4sin<sup>3</sup>15&deg;&nbsp;<br>= sin3A<br>= sin3 &times; 15&deg; <br>= sin 45&deg;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                    solution_hi: "<p>58.(c)<br>3 sin15&deg;- 4sin<sup>3</sup>15&deg;&nbsp;<br>= sin3A<br>= sin3 &times; 15&deg; <br>= sin 45&deg;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A cylinder has a radius <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> cm and its volume is 448 cm&sup3;. What is the height of the cylinder (taken &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>) ?</p>",
                    question_hi: "<p>59. एक बेलन की त्रिज्या <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> सेमी है और इसका आयतन 448 cm&sup3; है। बेलन की ऊंचाई कितनी है (मान लीजिए &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>) ?</p>",
                    options_en: [
                        "<p>24 cm</p>",
                        "<p>21 cm</p>",
                        "<p>23 cm</p>",
                        "<p>22 cm</p>"
                    ],
                    options_hi: [
                        "<p>24 सेमी</p>",
                        "<p>21 सेमी</p>",
                        "<p>23 सेमी</p>",
                        "<p>22 सेमी</p>"
                    ],
                    solution_en: "<p>59.(d) Radius = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math>&nbsp;, Volume = 448<br>Volume of cylinder ( &pi;r<sup>2</sup>h ) = 448<br><math display=\"inline\"><mo>&#8658;</mo></math> &pi; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math>&nbsp; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> &times; h = 448 <br><math display=\"inline\"><mo>&#8658;</mo></math> h =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>448</mn><mo>&#215;</mo><mn>22</mn></mrow><mrow><mn>64</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> &rArr; h = 22 cm</p>",
                    solution_hi: "<p>59.(d) त्रिज्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math>&nbsp;, आयतन = 448 cm&sup3;<br>बेलन का आयतन ( &pi;r<sup>2</sup>h ) = 448<br><math display=\"inline\"><mo>&#8658;</mo></math> &pi; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math>&nbsp; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> &times; h = 448 <br><math display=\"inline\"><mo>&#8658;</mo></math> h =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>448</mn><mo>&#215;</mo><mn>22</mn></mrow><mrow><mn>64</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> &rArr; h = 22 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math> = 1, a &ne; 0, b &ne; 0 then find the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup></mrow><mrow><mn>3</mn><mi>ab</mi></mrow></mfrac></math></p>",
                    question_hi: "<p>60. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math> = 1, a &ne; 0, b &ne; 0 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup></mrow><mrow><mn>3</mn><mi>ab</mi></mrow></mfrac></math> का मान जात करें।</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>0</p>",
                        "<p>2</p>",
                        "<p>-3</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>0</p>",
                        "<p>2</p>",
                        "<p>-3</p>"
                    ],
                    solution_en: "<p>60.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math> = 1<br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = ab &hellip; (i)<br>Now, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup></mrow><mi>ab</mi></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mi>ab</mi><mo>)</mo></mrow><mrow><mn>3</mn><mi>ab</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><mi>ab</mi><mo>-</mo><mi>ab</mi><mo>)</mo></mrow><mrow><mn>3</mn><mi>ab</mi></mrow></mfrac></math> &hellip; from(i)<br>= 0</p>",
                    solution_hi: "<p>60.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math> = 1<br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = ab &hellip; (i)<br>अब, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup></mrow><mi>ab</mi></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mi>ab</mi><mo>)</mo></mrow><mrow><mn>3</mn><mi>ab</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><mi>ab</mi><mo>-</mo><mi>ab</mi><mo>)</mo></mrow><mrow><mn>3</mn><mi>ab</mi></mrow></mfrac></math>&nbsp;&hellip;(i) से<br>= 0</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Arman leaves ₹2,17,600 to be divided among four sons, three daughters and five nephews. If each daughter receives 3 times as much as each nephew and each son receives 5 times as much as each nephew, how much does each son receive?</p>",
                    question_hi: "<p>61. अरमान ने चार पुत्रों, तीन पुत्रियों और पाँच भतीजों में बांटने के लिए ₹2,17,600 छोड़े हैं। यदि प्रत्येक पुत्री को प्रत्येक भतीजे से 3 गुना अधिक धनराशि मिलती है और प्रत्येक पुत्र को प्रत्येक भतीजे से 5 गुना अधिक धनराशि मिलती है, तो प्रत्येक पुत्र को कितनी धनराशि मिलेगी?</p>",
                    options_en: [
                        "<p>₹18,200</p>",
                        "<p>₹32,000</p>",
                        "<p>₹36,000</p>",
                        "<p>₹25,600</p>"
                    ],
                    options_hi: [
                        "<p>₹18,200</p>",
                        "<p>₹32,000</p>",
                        "<p>₹36,000</p>",
                        "<p>₹25,600</p>"
                    ],
                    solution_en: "<p>61.(b) Let amount of money received by each nephew = x <br>According to question , <br>Amount of money received by each son = 5x<br>Amount of money received by each daughter = 3x<br>Now , <br><math display=\"inline\"><mo>&#8658;</mo></math>4(5x ) + 3 (3x) + 5(x) = 2,17,600<br><math display=\"inline\"><mo>&#8658;</mo></math>20x + 9x + 5x = 2,17,600 &rArr;34x = 2,17,600 <br><math display=\"inline\"><mo>&#8658;</mo></math> x = 6400<br>&there4; Amount of money received by each son = 5 &times; 6400 = ₹32000</p>",
                    solution_hi: "<p>61.(b) माना प्रत्येक भतीजे को प्राप्त धनराशि = x <br>प्रश्न के अनुसार, <br>प्रत्येक पुत्र को प्राप्त धनराशि = 5x<br>प्रत्येक पुत्री को प्राप्त धनराशि = 3x<br>अब , <br><math display=\"inline\"><mo>&#8658;</mo></math>4(5x ) + 3 (3x) + 5(x) = 2,17,600<br><math display=\"inline\"><mo>&#8658;</mo></math>20x + 9x + 5x = 2,17,600 &rArr; 34x = 2,17,600&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 6400<br>&there4; प्रत्येक पुत्र को प्राप्त धनराशि = 5 &times; 6400 = ₹32000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. In a mixture, milk and water are in ratio of 2 : 3. Some milk is added to the mixture because of which ratio of milk and water becomes 10 : 3. How much milk was added as a percentage of initial mixture?</p>",
                    question_hi: "<p>62. एक मिश्रण में दूध और पानी का अनुपात 2 : 3 है। मिश्रण में कुछ मात्रा में दूध मिलाया जाता है जिसके कारण दूध और पानी का अनुपात 10 : 3 हो जाता है। प्रारंभिक मिश्रण के प्रतिशत के रूप में कितना दूध मिलाया गया?</p>",
                    options_en: [
                        "<p>100 percent</p>",
                        "<p>140 percent</p>",
                        "<p>160 percent</p>",
                        "<p>120 percent</p>"
                    ],
                    options_hi: [
                        "<p>100 प्रतिशत</p>",
                        "<p>140 प्रतिशत</p>",
                        "<p>160 प्रतिशत</p>",
                        "<p>120 प्रतिशत</p>"
                    ],
                    solution_en: "<p>62.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Milk&nbsp; &nbsp; Water&nbsp; &nbsp;Total quantity<br>Initial mixture &rArr;&nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;3&nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; 5&nbsp;<br>Final mixture &rArr;&nbsp; 10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;3&nbsp; &nbsp; &nbsp;=&nbsp; &nbsp; 13<br>Quantity of milk added = 10 - 2 = 8 unit<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 160%</p>",
                    solution_hi: "<p>62.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; दूध&nbsp; &nbsp; &nbsp;पानी&nbsp; &nbsp;कुल मात्रा<br>प्रारंभिक मिश्रण &rarr;&nbsp; &nbsp;2&nbsp; &nbsp; :&nbsp; &nbsp;3&nbsp; &nbsp; =&nbsp; 5&nbsp;<br>अंतिम मिश्रण &rarr;&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp;:&nbsp; &nbsp;3&nbsp; &nbsp; = 13<br>मिलाये गये दूध की मात्रा = 10 - 2 = 8 इकाई<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 160%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A and B can do a piece of work in 8 days and B alone can do it in 16 days. In how many days, can A do it alone?</p>",
                    question_hi: "<p>63. A और B एक काम को आठ दिनों में कर सकते हैं और B अकेले इसे 16 दिनों में कर सकता है। A अकेले इसी कार्य को कितने दिनों में कर सकता है?</p>",
                    options_en: [
                        "<p>16 days</p>",
                        "<p>24 days</p>",
                        "<p>10 days</p>",
                        "<p>18 days</p>"
                    ],
                    options_hi: [
                        "<p>16 दिन</p>",
                        "<p>24 दिन</p>",
                        "<p>10 दिन</p>",
                        "<p>18 दिन</p>"
                    ],
                    solution_en: "<p>63.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204494.png\" alt=\"rId45\" width=\"254\" height=\"153\"><br>Efficiency of A = 2 - 1 = 1 unit<br>Hence, required days = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 16 days</p>",
                    solution_hi: "<p>63.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204635.png\" alt=\"rId46\" width=\"252\" height=\"157\"><br>A की क्षमता = 2 - 1 = 1 इकाई<br>अतः, आवश्यक दिन = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 16 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A person drives 110 miles at 55 miles per hour, he drives the next 120 miles at 60 miles per hour, and then he drives the next 60 miles in an hour. What is his average speed for the entire journey in miles per hour ?</p>",
                    question_hi: "<p>64. एक व्यक्ति 55 मील प्रति घंटे की चाल से 110 मील तक गाड़ी चलाता है, वह अगली 120 मील की यात्रा 60 मील प्रति घंटे की चाल से तय करता है और अगली 60 मील की दूरी एक घंटे में तय करता है। मील प्रति घंटे में पूरी यात्रा के लिए उसकी औसत चाल कितनी है?</p>",
                    options_en: [
                        "<p>55</p>",
                        "<p>60</p>",
                        "<p>63</p>",
                        "<p>58</p>"
                    ],
                    options_hi: [
                        "<p>55</p>",
                        "<p>60</p>",
                        "<p>63</p>",
                        "<p>58</p>"
                    ],
                    solution_en: "<p>64.(d)<br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>distance</mi><mi mathvariant=\"normal\">&#160;</mi><mi>covered</mi></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi><mi mathvariant=\"normal\">&#160;</mi><mi>taken</mi></mrow></mfrac></math><br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>110</mn><mo>+</mo><mn>120</mn><mo>+</mo><mn>60</mn></mrow><mrow><mfrac><mn>110</mn><mn>55</mn></mfrac><mo>+</mo><mfrac><mn>120</mn><mn>60</mn></mfrac><mo>+</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>290</mn><mn>5</mn></mfrac></math> = 58 miles/hour</p>",
                    solution_hi: "<p>64.(d)<br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2340;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2327;&#2312;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2354;&#2367;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2327;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>110</mn><mo>+</mo><mn>120</mn><mo>+</mo><mn>60</mn></mrow><mrow><mfrac><mn>110</mn><mn>55</mn></mfrac><mo>+</mo><mfrac><mn>120</mn><mn>60</mn></mfrac><mo>+</mo><mn>1</mn></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>290</mn><mn>5</mn></mfrac></math>= 58 मील/घंटा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In an election, a candidate secured 41% of the votes polled and lost the election by 97,632. What is the number of votes obtained by the winning candidate?</p>",
                    question_hi: "<p>65. एक चुनाव में, एक उम्मीदवार को डाले गए कुल मतों का 41% मत प्राप्त हुआ और वह 97,632 मतों से चुनाव हार गया। विजयी उम्मीदवार को प्राप्त मतों की संख्या ज्ञात करें।</p>",
                    options_en: [
                        "<p>542400</p>",
                        "<p>320016</p>",
                        "<p>360072</p>",
                        "<p>222384</p>"
                    ],
                    options_hi: [
                        "<p>542400</p>",
                        "<p>320016</p>",
                        "<p>360072</p>",
                        "<p>222384</p>"
                    ],
                    solution_en: "<p>65.(b)<br>According to the question,<br>(59 - 41)% = 97632 <br>1% = 5424 <br>So, votes obtained by the winning candidate(59%) = 5424 &times; 59 = 320016</p>",
                    solution_hi: "<p>65.(b)<br>प्रश्न के अनुसार,<br>(59 - 41)% = 97632 <br>1% = 5424 <br>तो, विजयी उम्मीदवार को प्राप्त वोट (59%) = 5424 &times; 59 = 320016</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If 10 sin&sup2;&theta; + 6 cos&sup2;&theta; = 7, 0 &lt; &theta; &lt; 90&deg; then find the value of tan &theta;.</p>",
                    question_hi: "<p>66. यदि 10 sin&sup2;&theta; + 6 cos&sup2;&theta; = 7, 0 &lt; &theta; &lt; 90&deg; है ,तो tan &theta; का मान ज्ञात कीजिए |</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>66.(c)<br>10 sin&sup2;&theta; + 6 cos&sup2;&theta; = 7,&nbsp;<br>4 sin&sup2;&theta; + 6 sin&sup2;&theta; + 6 cos&sup2;&theta; = 7<br>4 sin&sup2;&theta; + 6 (sin&sup2;&theta; + cos&sup2;&theta;) = 7<br>4 sin&sup2;&theta; = 1 <br>sin&sup2;&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>4</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n<p>sin&theta; = sin30&deg; &hellip; (sin30&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)<br>&theta;&nbsp; = 30&deg;<br>Now, tan30&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>",
                    solution_hi: "<p>66.(c)<br>10 sin&sup2;&theta; + 6 cos&sup2;&theta; = 7,&nbsp;<br>4 sin&sup2;&theta; + 6 sin&sup2;&theta; + 6 cos&sup2;&theta; = 7<br>4 sin&sup2;&theta; + 6 (sin&sup2;&theta; + cos&sup2;&theta;) = 7<br>4 sin&sup2;&theta; = 1 <br>sin&sup2;&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>4</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin&theta; = sin30&deg; &hellip; (sin30&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)<br>&theta;&nbsp;= 30&deg;<br>अब , tan30&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The marked price of 55 items was equal to the cost price of 99 items. The selling price of 56 items was equal to the marked price of 35 items. Calculate the profit or loss percentage from the sale of each item.</p>",
                    question_hi: "<p>67. 55 वस्तुओं का अंकित मूल्य 99 वस्तुओं के क्रय मूल्य के बराबर था। 56 वस्तुओं का विक्रय मूल्य 35 वस्तुओं के अंकित मूल्य के बराबर था। प्रत्येक वस्तु की बिक्री से होने वाले लाभ या हानि प्रतिशत की गणना करें।</p>",
                    options_en: [
                        "<p>15% profit</p>",
                        "<p>12.25% profit</p>",
                        "<p>12.5% profit</p>",
                        "<p>12.5% loss</p>"
                    ],
                    options_hi: [
                        "<p>15% लाभ</p>",
                        "<p>12.25% लाभ</p>",
                        "<p>12.5% लाभ</p>",
                        "<p>12.5% हानि</p>"
                    ],
                    solution_en: "<p>67.(c) MP of 55 items = CP of 99 items<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MP</mi><mi>CP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>99</mn><mn>55</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br>SP of 56 items = MP of 35 items<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>SP</mi><mi>MP</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>36</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> CP : MP : SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; :&nbsp; &nbsp;9&nbsp; &nbsp;:&nbsp; &nbsp;9&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; :&nbsp; &nbsp;8&nbsp; &nbsp;:&nbsp; &nbsp;5 <br>______________________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;40&nbsp; :&nbsp; 72 :&nbsp; 45<br>Required profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>-</mo><mn>40</mn></mrow><mn>40</mn></mfrac></math> &times; 100 = 12.5%</p>",
                    solution_hi: "<p>67.(c) 55 वस्तुओं का अंकित मूल्य = 99 वस्तुओं का CP<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MP</mi><mi>CP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>99</mn><mn>55</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br>56 वस्तुओं का विक्रय मूल्य = 35 वस्तुओं का अंकित मूल्य <br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>SP</mi><mi>MP</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>36</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> क्रय मूल्य : अंकित मूल्य : विक्रय मूल्य <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;9<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 8&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;5 <br>-----------------------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;40&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 72&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 45<br>आवश्यक लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>-</mo><mn>40</mn></mrow><mn>40</mn></mfrac></math>&nbsp;&times; 100 = 12.5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A shopkeeper bought 288 oranges for ₹115.20. He sold 50 of them at 70 paise each and the remaining at ₹46.20. Find his profit or loss.</p>",
                    question_hi: "<p>68. एक दुकानदार ने ₹115.20 में 288 संतरे खरीदे। उसने उनमें से 50 को 70 पैसे प्रति संतरे की दर से और शेष ₹46.20 में बेच दिए। उसका लाभ या हानि ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>Loss, ₹36</p>",
                        "<p>Loss, ₹34</p>",
                        "<p>Profit, ₹37</p>",
                        "<p>Profit, ₹25</p>"
                    ],
                    options_hi: [
                        "<p>₹36 की हानि</p>",
                        "<p>₹34 की हानि</p>",
                        "<p>₹37 का लाभ</p>",
                        "<p>₹25 का लाभ</p>"
                    ],
                    solution_en: "<p>68.(b)<br>SP of 50 oranges = 50 &times; 0.7 = ₹ 35<br>SP of remaining oranges = 46.20<br>Total SP = 35 + 46.20 = 81.2<br>Loss = 115.20 - 81.2 = ₹ 34</p>",
                    solution_hi: "<p>68.(b)<br>50 संतरो का विक्रय मूल्य = 50 &times; 0.7 = ₹ 35<br>शेष संतरो का विक्रय मूल्य = 46.20<br>कुल विक्रय मूल्य = 35 + 46.20 = 81.2<br>हानि = 115.20 - 81.2 = ₹ 34</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. An article is of size 9 cm &times; 6 cm &times; 3 cm. The number of such articles that can be packed in a box measuring 63 cm &times; 42 cm &times; 21 cm is:</p>",
                    question_hi: "<p>69. एक वस्तु का आकार 9 cm &times; 6 cm &times; 3 cm है। 63 cm &times; 42 cm &times; 21 cm आकार वाले बॉक्स में ऐसी कितनी वस्तुओं को पैक किया जा सकता है?</p>",
                    options_en: [
                        "<p>196</p>",
                        "<p>243</p>",
                        "<p>343</p>",
                        "<p>49</p>"
                    ],
                    options_hi: [
                        "<p>196</p>",
                        "<p>243</p>",
                        "<p>343</p>",
                        "<p>49</p>"
                    ],
                    solution_en: "<p>69.(c) <br>Let the no. of articles be n<br>According to the question,<br>n &times; 9 &times; 6 &times; 3 = 63 &times; 42 &times; 21<br>n = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>&#215;</mo><mn>42</mn><mo>&#215;</mo><mn>21</mn></mrow><mrow><mn>9</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 343</p>",
                    solution_hi: "<p>69.(c) <br>माना कि वस्तुओं की संख्या n है<br>प्रश्न के अनुसार,<br>n &times; 9 &times; 6 &times; 3 = 63 &times; 42 &times; 21<br>n = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>&#215;</mo><mn>42</mn><mo>&#215;</mo><mn>21</mn></mrow><mrow><mn>9</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 343</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A sum becomes ₹6,600 in 4 years at simple interest at a yearly interest rate of 5% per annum. What is the sum?</p>",
                    question_hi: "<p>70. एक धनराशि 5% प्रति वर्ष की वार्षिक ब्याज दर पर साधारण ब्याज पर 4 वर्षों में ₹6,600 हो जाती है। वह धनराशि क्या है?</p>",
                    options_en: [
                        "<p>₹6,000</p>",
                        "<p>₹3,300</p>",
                        "<p>₹5,500</p>",
                        "<p>₹4,400</p>"
                    ],
                    options_hi: [
                        "<p>₹6,000</p>",
                        "<p>₹3,300</p>",
                        "<p>₹5,500</p>",
                        "<p>₹4,400</p>"
                    ],
                    solution_en: "<p>70.(c)<br>Amount = principal + SI<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>amount = p + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>6600 = p + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math><br>6600 = p + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">p</mi><mo>+</mo><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>p = 5500</p>",
                    solution_hi: "<p>70.(c)<br>राशि = मूलधन + साधारण ब्याज <br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>मिश्रधन (राशि) = मूलधन + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math>&nbsp;<br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi></math>&nbsp;+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi></mrow><mn>5</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>+</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>मूलधन = 5500</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Find the mode for the following data of student ages:<br>16, 17, 15, 17, 16, 15, 14, 14, 13, 17, 13, 12, 12, 16, 10, 14, 17, 10, 11.</p>",
                    question_hi: "<p>71. विद्यार्थी की आयु के निम्नलिखित आँकड़ों के लिए बहुलक ज्ञात कीजिए:<br>16, 17, 15, 17, 16, 15, 14, 14, 13, 17, 13, 12, 12, 16, 10, 14, 17, 10, 11.</p>",
                    options_en: [
                        "<p>15</p>",
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>11</p>"
                    ],
                    options_hi: [
                        "<p>15</p>",
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>11</p>"
                    ],
                    solution_en: "<p>71.(c)<br>16, 17, 15, 17, 16, 15, 14, 14, 13, 17, 13, 12, 12, 16, 10, 14, 17, 10, 11.<br>On arranging the numbers,<br>10, 10, 11, 12, 12, 13, 13, 14, 14, 14, 15, 15, 16, 16, 16, 17, 17, 17, 17, <br>So, mode = 17</p>",
                    solution_hi: "<p>71.(c)<br>16, 17, 15, 17, 16, 15, 14, 14, 13, 17, 13, 12, 12, 16, 10, 14, 17, 10, 11.<br>संख्याओं को व्यवस्थित करने पर,<br>10, 10, 11, 12, 12, 13, 13, 14, 14,14, 15, 15, 16, 16, 16, 17, 17, 17, 17, <br>अतः, बहुलक = 17</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. In June, Rohit&rsquo;s bank account balance is ₹5,000 for 25 days, ₹20,000 for 2 days and ₹1,500 for 3 days. What is the average balance (in ₹) in Rohit\'s bank account in June?</p>",
                    question_hi: "<p>72. जून में, रोहित के बैंक खाते का बैलेंस 25 दिनों के लिए ₹5,000, 2 दिनों के लिए ₹20,000 और 3 दिनों के लिए ₹1,500 है। जून में रोहित के बैंक खाते में औसत बैलेंस (₹ में) कितना है?</p>",
                    options_en: [
                        "<p>5650</p>",
                        "<p>5575</p>",
                        "<p>6000</p>",
                        "<p>5200</p>"
                    ],
                    options_hi: [
                        "<p>5650</p>",
                        "<p>5575</p>",
                        "<p>6000</p>",
                        "<p>5200</p>"
                    ],
                    solution_en: "<p>72.(a)<br>Average balance = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 5650</p>",
                    solution_hi: "<p>72.(a)<br>औसत बैलेंस = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math>&nbsp;= 5650</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A student studying in 6th class scored the following marks in 6 subjects.<br>The maximum marks for each subject are 100.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204735.png\" alt=\"rId47\" width=\"168\" height=\"145\"> <br>What are the average marks scored by the student?</p>",
                    question_hi: "<p>73. छठी कक्षा में पढ़ने वाले एक छात्र ने 6 विभिन्न विषयों में निम्नलिखित अंक प्राप्त किए हैं। <br>प्रत्येक विषय के लिए पूर्णांक 100 हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739246204846.png\" alt=\"rId48\" width=\"151\" height=\"163\"> <br>इस छात्र द्वारा प्राप्त किए गए औसत अंक ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>92.5</p>",
                        "<p>96</p>",
                        "<p>95</p>",
                        "<p>94.5</p>"
                    ],
                    options_hi: [
                        "<p>92.5</p>",
                        "<p>96</p>",
                        "<p>95</p>",
                        "<p>94.5</p>"
                    ],
                    solution_en: "<p>73.(d) Average marks scored by the student = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>98</mn><mo>+</mo><mn>97</mn><mo>+</mo><mn>93</mn><mo>+</mo><mn>94</mn><mo>+</mo><mn>92</mn><mo>+</mo><mn>93</mn><mi>&#160;</mi></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>567</mn></mrow><mn>6</mn></mfrac></math> = 94.5</p>",
                    solution_hi: "<p>73.(d) छात्र द्वारा प्राप्त औसत अंक = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>98</mn><mo>+</mo><mn>97</mn><mo>+</mo><mn>93</mn><mo>+</mo><mn>94</mn><mo>+</mo><mn>92</mn><mo>+</mo><mn>93</mn><mi>&#160;</mi></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>567</mn></mrow><mn>6</mn></mfrac></math> = 94.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. There are two pipes to fill a tank. Together, they can fill the tank in 15 minutes. If one pipe can fill the tank in one and a half times as fast as the other, the faster pipe alone can fill the tank in:</p>",
                    question_hi: "<p>74. एक टंकी को भरने के लिए दो पाइप हैं। वे मिलकर टंकी को 15 मिनट में भर सकते हैं। यदि एक पाइप दूसरे पाइप की तुलना में डेढ़ गुना तेजी से टंकी को भर सकता है, तो तेज गति से भरने वाला पाइप अकेले टंकी को कितने समय में भर सकता है?</p>",
                    options_en: [
                        "<p>10 minutes</p>",
                        "<p>25 minutes</p>",
                        "<p>20 minutes</p>",
                        "<p>32<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> minutes</p>"
                    ],
                    options_hi: [
                        "<p>10 मिनट</p>",
                        "<p>25 मिनट</p>",
                        "<p>20 मिनट</p>",
                        "<p>32<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> मिनट</p>"
                    ],
                    solution_en: "<p>74.(b)<br>Efficiency of pipes = 1 : 1.5<br>Time taken to fill the tank by both the pipes = 15 min.<br>Total capacity of tank = 15 &times; (1 + 1.5) = 15 &times; 2.5 unit<br>Hence, time taken to fill the tank by faster pipe alone = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 25 min.</p>",
                    solution_hi: "<p>74.(b) <br>पाइपों की दक्षता = 1 : 1.5<br>दोनों पाइपों द्वारा टंकी को भरने में लगा समय = 15 मिनट<br>टैंक की कुल क्षमता = 15 &times; (1 + 1.5) = 15 &times; 2.5 इकाई<br>अतः, अकेले तेज़ पाइप द्वारा टैंक को भरने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math>&nbsp;= 25 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Simplify<br>8.16 &times;&nbsp;5.35 + 17.9 - 19.5 of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>15</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    question_hi: "<p>75. निम्नलिखित को सरल कीजिए।<br>8.16 &times;&nbsp;5.35 + 17.9 - 19.5 of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>15</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    options_en: [
                        "<p>57.426</p>",
                        "<p>60.456</p>",
                        "<p>65.234</p>",
                        "<p>70.225</p>"
                    ],
                    options_hi: [
                        "<p>57.426</p>",
                        "<p>60.456</p>",
                        "<p>65.234</p>",
                        "<p>70.225</p>"
                    ],
                    solution_en: "<p>75.(b)<br>8.16 &times;&nbsp;5.35 + 17.9 - 19.5 of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>15</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>= 43.656 + 17.9 - 19.5 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>15</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>= 61.556 - 1.3 + 0.2<br>= 60.456</p>",
                    solution_hi: "<p>75.(b)<br>8.16 &times;&nbsp;5.35 + 17.9 - 19.5 of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>15</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>= 43.656 + 17.9 - 19.5 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>15</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>= 61.556 - 1.3 + 0.2<br>= 60.456</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate synonym of the given word.<br>Seclusion</p>",
                    question_hi: "<p>76. Select the most appropriate synonym of the given word.<br>Seclusion</p>",
                    options_en: [
                        "<p>Readiness</p>",
                        "<p>Solitude</p>",
                        "<p>Tactics</p>",
                        "<p>Involvement</p>"
                    ],
                    options_hi: [
                        "<p>Readiness</p>",
                        "<p>Solitude</p>",
                        "<p>Tactics</p>",
                        "<p>Involvement</p>"
                    ],
                    solution_en: "<p>76.(b) <strong>Solitude-</strong> the state or situation of being alone.<br><strong>Seclusion-</strong> the state of being private and away from other people.<br><strong>Readiness-</strong> the state of being fully prepared for something.<br><strong>Tactics-</strong> an action or strategy carefully planned to achieve a specific end.<br><strong>Involvement-</strong> the fact or condition of being involved with or participating in something.</p>",
                    solution_hi: "<p>76.(b) <strong>Solitude</strong> (एकान्त) - the state or situation of being alone.<br><strong>Seclusion</strong> (एकान्त) - the state of being private and away from other people.<br><strong>Readiness</strong> (तत्परता) - the state of being fully prepared for something.<br><strong>Tactics</strong> (रणनीति) - an action or strategy carefully planned to achieve a specific end.<br><strong>Involvement</strong> (संलिप्तता/सहभागिता) - the fact or condition of being involved with or participating in something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "77. Identify the segment in the sentence which contains grammatical error. Mark ‘no error’ in case the sentence given is correct. <br />The teacher promised that he will explain it if they come before school the following day. ",
                    question_hi: "77. Identify the segment in the sentence which contains grammatical error. Mark ‘no error’ in case the sentence given is correct. <br />The teacher promised that he will explain it if they come before school the following day. ",
                    options_en: [
                        " The teacher promised that",
                        " he will explain it if they come",
                        " before school the following day.",
                        " No error"
                    ],
                    options_hi: [
                        " The teacher promised that",
                        " he will explain it if they come",
                        " before school the following day.",
                        " No error"
                    ],
                    solution_en: "<p>77.(b) he will explain it if they comeThe given sentence is in the past tense so it must have a verb in the past form(V<sub>2</sub>) and not in the future form(will). Hence, &lsquo;would&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(b) he will explain it if they come<br>दिया गया वाक्य past tense में है इसलिए इसमें verb past form (V<sub>2</sub>) में होनी चाहिए न कि future form (will) में। इसलिए, &lsquo;would&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who eats meat but not seafood or fish</p>",
                    question_hi: "<p>78. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who eats meat but not seafood or fish</p>",
                    options_en: [
                        "<p>Herbivorous</p>",
                        "<p>Carnitarian</p>",
                        "<p>Carnivorous</p>",
                        "<p>Omnivorous</p>"
                    ],
                    options_hi: [
                        "<p>Herbivorous</p>",
                        "<p>Carnitarian</p>",
                        "<p>Carnivorous</p>",
                        "<p>Omnivorous</p>"
                    ],
                    solution_en: "<p>78.(b) <strong>Carnitarian-</strong> a person who eats meat but not seafood or fish.<br><strong>Herbivorous-</strong> an animal that eats only plants.<br><strong>Carnivorous-</strong> an animal that eats meat.<br><strong>Omnivorous-</strong> an animal or person feeding on a variety of food of both plant and animal origin.</p>",
                    solution_hi: "<p>78.(b) <strong>Carnitarian</strong> (मांसभक्षी) - a person who eats meat but not seafood or fish.<br><strong>Herbivorous</strong> (शाकाहारी) - an animal that eats only plants.<br><strong>Carnivorous</strong> (माँसाहारी) - an animal that eats meat.<br><strong>Omnivorous</strong> (सर्वाहारी) - an animal or person feeding on a variety of food of both plant and animal origin.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the option that will improve the underlined part of the given sentence. In case no improvement is needed, select \'No improvement required\'. <br>What <span style=\"text-decoration: underline;\">flavour ice cream</span> do you want?</p>",
                    question_hi: "<p>79. Select the option that will improve the underlined part of the given sentence. In case no improvement is needed, select \'No improvement required\'. <br>What <span style=\"text-decoration: underline;\">flavour ice cream</span> do you want?</p>",
                    options_en: [
                        "<p>ice cream flavoured</p>",
                        "<p>flavours ice cream</p>",
                        "<p>No improvement required</p>",
                        "<p>flavour of ice cream</p>"
                    ],
                    options_hi: [
                        "<p>ice cream flavoured</p>",
                        "<p>flavours ice cream</p>",
                        "<p>No improvement required</p>",
                        "<p>flavour of ice cream</p>"
                    ],
                    solution_en: "<p>79.(d) flavour of ice cream<br>&lsquo;Of&rsquo; is used to show one thing related to another. Similarly, flavour is related to ice cream. Hence, &lsquo;flavour of ice cream&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(d) flavour of ice cream<br>&lsquo;Of&rsquo; का प्रयोग एक चीज़ से दूसरी चीज़ से संबंधित दिखाने के लिए किया जाता है। इसी तरह, flavour आइसक्रीम से संबंधित है। इसलिए, &lsquo;flavour of ice cream&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the correctly spelt word :</p>",
                    question_hi: "<p>80. Select the correctly spelt word :</p>",
                    options_en: [
                        "<p>Accommodation</p>",
                        "<p>Accomodation</p>",
                        "<p>Acommodation</p>",
                        "<p>Accomodetion</p>"
                    ],
                    options_hi: [
                        "<p>Accommodation</p>",
                        "<p>Accomodation</p>",
                        "<p>Acommodation</p>",
                        "<p>Accomodetion</p>"
                    ],
                    solution_en: "<p>80.(a) Accommodation</p>",
                    solution_hi: "<p>80.(a) Accommodation/ निवास स्थान</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the given sentence in active voice. <br>I was never supported by my family.</p>",
                    question_hi: "<p>81. Select the option that expresses the given sentence in active voice. <br>I was never supported by my family.</p>",
                    options_en: [
                        "<p>My family have never supported me.</p>",
                        "<p>My family had never supported me.</p>",
                        "<p>My family never supported me.</p>",
                        "<p>My family never supports me.</p>"
                    ],
                    options_hi: [
                        "<p>My family have never supported me.</p>",
                        "<p>My family had never supported me.</p>",
                        "<p>My family never supported me.</p>",
                        "<p>My family never supports me.</p>"
                    ],
                    solution_en: "<p>81.(c) My family never supported me.(Correct)<br>(a) My family <span style=\"text-decoration: underline;\">have never supported</span> me.(Incorrect Tense) <br>(b) My family <span style=\"text-decoration: underline;\">had never supported</span> me.(Incorrect Tense) <br>(d) My family never <span style=\"text-decoration: underline;\">supports</span> me.(Incorrect Tense)</p>",
                    solution_hi: "<p>81.(c) My family never supported me. (Correct)<br>(a) My family <span style=\"text-decoration: underline;\">have never supported</span> me. (गलत Tense) <br>(b) My family <span style=\"text-decoration: underline;\">had never supported</span> me. (गलत Tense) <br>(d) My family never <span style=\"text-decoration: underline;\">supports</span> me. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br>P. having the opportunity of observing people&rsquo;s <br>Q. we respect and appreciate their point of views <br>R. the advantage of travelling different places and <br>S. culture, custom and lifestyle, is that</p>",
                    question_hi: "<p>82. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br>P. having the opportunity of observing people&rsquo;s <br>Q. we respect and appreciate their point of views <br>R. the advantage of travelling different places and <br>S. culture, custom and lifestyle, is that</p>",
                    options_en: [
                        "<p>RQPS</p>",
                        "<p>PSQR</p>",
                        "<p>RPSQ</p>",
                        "<p>SQPR</p>"
                    ],
                    options_hi: [
                        "<p>RQPS</p>",
                        "<p>PSQR</p>",
                        "<p>RPSQ</p>",
                        "<p>SQPR </p>"
                    ],
                    solution_en: "<p>82.(c) <strong>RPSQ</strong><br>The given sentence starts with Part R as it introduces the main idea of the sentence, i.e. the advantages of travelling. Part R will be followed by Part P as they are joined by the conjunction &lsquo;and&rsquo;. Further, Part S completes Part P by providing the nouns for the possessive <span style=\"text-decoration: underline;\">people&rsquo;s</span> &amp; Part Q states the advantage that we respect and appreciate their point of views. So, Q will follow S. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>82.(c) <strong>RPSQ</strong><br>दिया गया sentence, Part R से प्रारंभ होगा क्योंकि इसमे sentence का मुख्य विचार, &lsquo;the advantages of travelling&rsquo; शामिल है। Part R के बाद Part P आएगा क्योंकि ये conjunction &lsquo;and&rsquo; से जुड़े हुए हैं। इसके अलावा, Part S, possessive <span style=\"text-decoration: underline;\">people&rsquo;s</span> के लिए nouns प्रदान करके Part P को पूरा करता है और Part Q में यह advantage बताया गया है कि हम उनके विचारों का सम्मान करते हैं और उनकी सराहना करते हैं। इसलिए, S के बाद Q आएगा। Options के माध्यम से जाने पर, option \'c\' में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Choose the word opposite in meaning to the given word as your answer.<br>Transience</p>",
                    question_hi: "<p>83. Choose the word opposite in meaning to the given word as your answer.<br>Transience</p>",
                    options_en: [
                        "<p>eternity</p>",
                        "<p>shallow</p>",
                        "<p>slow</p>",
                        "<p>rest</p>"
                    ],
                    options_hi: [
                        "<p>eternity</p>",
                        "<p>shallow</p>",
                        "<p>slow</p>",
                        "<p>rest</p>"
                    ],
                    solution_en: "<p>83.(a) <strong>eternity</strong><br>Transience - the state or fact of lasting only for a short time; transitoriness</p>",
                    solution_hi: "<p>83.(a) <strong>eternity</strong><br>Transience - थोड़े समय के लिए ही टिकने की अवस्था/ क्षणभंगुरता</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(a) the use of animal research<br>(b) throughout the past century<br>(c) and it continues to be used to understand many diseases<br>(d) has been of considerable importance<br>(e) in the field of scientific and medical advancements</p>",
                    question_hi: "<p>84. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(a) the use of animal research<br>(b) throughout the past century<br>(c) and it continues to be used to understand many diseases<br>(d) has been of considerable importance<br>(e) in the field of scientific and medical advancements</p>",
                    options_en: [
                        "<p>aecdb</p>",
                        "<p>ecdba</p>",
                        "<p>adebc</p>",
                        "<p>edcba</p>"
                    ],
                    options_hi: [
                        "<p>aecdb</p>",
                        "<p>ecdba</p>",
                        "<p>adebc</p>",
                        "<p>edcba</p>"
                    ],
                    solution_en: "<p>84.(c) <strong>adebc</strong><br>The given sentence starts with Part (a) as it contains the main subject of the sentence, i.e. &lsquo;the use of animal research&rsquo;. Part (d) contains the main verb of the subject, i.e. &lsquo;been&rsquo; and Part (e) states the field in which the use of animal research is of importance. So, (e) will follow (d). Further, Part (b) states the time period during which animal research has been of importance and Part (c) talks about the further use of animal research to understand many diseases. So, (c) will follow (b). Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>84.(c) <strong>adebc</strong><br>दिया गया वाक्य Part (a) से प्रारंभ होगा क्योंकि इसमें sentence का main subject, &lsquo;the use of animal research&rsquo; शामिल है। Part (d) में subject की main verb &lsquo;been&rsquo; शामिल है, और Part (e) में उस क्षेत्र के बारे में कहा गया है जिसमें पशु अनुसंधान(animal research) का उपयोग महत्वपूर्ण है। इसलिए, (d) के बाद (e) आएगा। इसके अलावा, Part (b) में उस समय अवधि के बारे में कहा गया है जिसके दौरान पशु अनुसंधान महत्वपूर्ण रहा है और Part (c) कई बीमारियों को समझने के लिए पशु अनुसंधान के अतिरिक्त उपयोग के बारे में बात करता है। इसलिए, (b) के बाद (c) आएगा। Options के मध्यम से जाने पर option &lsquo;c&rsquo; में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>85. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Equally</p>",
                        "<p>Occured</p>",
                        "<p>Unwilling</p>",
                        "<p>Argument</p>"
                    ],
                    options_hi: [
                        "<p>Equally</p>",
                        "<p>Occured</p>",
                        "<p>Unwilling</p>",
                        "<p>Argument</p>"
                    ],
                    solution_en: "<p>85.(b) Occured<br>&lsquo;Occurred&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(b) Occured<br>&lsquo;Occurred&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>My uncle&rsquo;s business has <span style=\"text-decoration: underline;\"><strong>gone to the dogs</strong></span>.</p>",
                    question_hi: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>My uncle&rsquo;s business has <span style=\"text-decoration: underline;\"><strong>gone to the dogs</strong></span>.</p>",
                    options_en: [
                        "<p>is ruined</p>",
                        "<p>is dead</p>",
                        "<p>is sick</p>",
                        "<p>is angry</p>"
                    ],
                    options_hi: [
                        "<p>is ruined</p>",
                        "<p>is dead</p>",
                        "<p>is sick</p>",
                        "<p>is angry</p>"
                    ],
                    solution_en: "<p>86.(a) is ruined<br>Eg- Many startups have gone to the dogs due to lack of funds.</p>",
                    solution_hi: "<p>86.(a) is ruined/बर्बाद हो गया <br>Eg- Many startups have gone to the dogs due to lack of funds./ फंड की कमी के कारण कई स्टार्टअप बर्बाद हो गए हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Identify the INCORRECT section of the given sentence. <br>Four people / were / witnesses of / that event.</p>",
                    question_hi: "<p>87. Identify the INCORRECT section of the given sentence. <br>Four people / were / witnesses of / that event.</p>",
                    options_en: [
                        "<p>witnesses of</p>",
                        "<p>that event.</p>",
                        "<p>were</p>",
                        "<p>Four people</p>"
                    ],
                    options_hi: [
                        "<p>witnesses of</p>",
                        "<p>that event.</p>",
                        "<p>were</p>",
                        "<p>Four people</p>"
                    ],
                    solution_en: "<p>87.(a) witnesses of<br>The preposition &lsquo;to&rsquo; is used after the nouns &lsquo;witness&rsquo; or &lsquo;testimony&rsquo;. And the correct structure is &lsquo;witness/testimony to an event&rsquo;. Hence, &lsquo;witnesses to&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>87.(a) witnesses of<br>Noun &lsquo;witness&rsquo; या &lsquo;testimony&rsquo; के बाद Preposition &lsquo;to&rsquo; का प्रयोग किया जाता है। और &lsquo;witness/testimony to an event&rsquo; सही structure है। अतः, &lsquo;witnesses to&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate meaning of the following idiom. <br>At sixes and sevens</p>",
                    question_hi: "<p>88. Select the most appropriate meaning of the following idiom. <br>At sixes and sevens</p>",
                    options_en: [
                        "<p>Heavy rains</p>",
                        "<p>In happy mood</p>",
                        "<p>In disorder</p>",
                        "<p>Having dispute</p>"
                    ],
                    options_hi: [
                        "<p>Heavy rains</p>",
                        "<p>In happy mood</p>",
                        "<p>In disorder</p>",
                        "<p>Having dispute</p>"
                    ],
                    solution_en: "<p>88.(c) At sixes and sevens- in disorder.<br>E.g.- After the party, the whole house was at sixes and sevens.</p>",
                    solution_hi: "<p>88.(c) At sixes and sevens - in disorder./किसी परेशानी&nbsp;या अव्यवस्था में।&nbsp;<br>E.g.- After the party, the whole house was at sixes and sevens.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>Before she began, she asked the teacher whether <span style=\"text-decoration: underline;\">she write</span> with a pen or a pencil.</p>",
                    question_hi: "<p>89. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>Before she began, she asked the teacher whether <span style=\"text-decoration: underline;\">she write</span> with a pen or a pencil.</p>",
                    options_en: [
                        "<p>she wrote</p>",
                        "<p>she can write</p>",
                        "<p>she should write</p>",
                        "<p>she did write.</p>"
                    ],
                    options_hi: [
                        "<p>she wrote</p>",
                        "<p>she can write</p>",
                        "<p>she should write</p>",
                        "<p>she did write.</p>"
                    ],
                    solution_en: "<p>89.(c) she should write<br>&lsquo;Should&rsquo; is used to indicate what is probable. Pen and pencil are probable ways to write. Hence, &lsquo;she should write&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(c) she should write<br>&lsquo;Should&rsquo; का प्रयोग यह बताने के लिए किया जाता है कि क्या probable है। Pen और pencil लिखने के संभावित तरीके हैं। इसलिए, &lsquo;she should write&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>A person who wastes his money on luxury.</p>",
                    question_hi: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>A person who wastes his money on luxury.</p>",
                    options_en: [
                        "<p>Luxuriant</p>",
                        "<p>Stingy</p>",
                        "<p>Extravagant</p>",
                        "<p>Luxurious</p>"
                    ],
                    options_hi: [
                        "<p>Luxuriant</p>",
                        "<p>Stingy</p>",
                        "<p>Extravagant</p>",
                        "<p>Luxurious</p>"
                    ],
                    solution_en: "<p>90.(c) <strong>Extravagant</strong><br><strong>Luxuriant</strong> - Rich and profuse<br><strong>Stingy</strong> - very unwilling to spend money or use resources.<br><strong>Extravagant</strong> - Lacking restraint in spending money or using resources<br><strong>Luxurious</strong> - Extremely comfortable or elegant, especially when involving great expense.</p>",
                    solution_hi: "<p>90.(c) <strong>Extravagant</strong><br><strong>Luxuriant</strong> - प्रचुर<br><strong>Stingy</strong> - कंजूस<br><strong>Extravagant</strong> -पैसे खर्च करने या संसाधनों का उपयोग करने में संयम की कमी.<br><strong>Luxurious</strong> - बेहद आरामदायक या उचित तरीके से, खासकर जब बहुत अधिक खर्च शामिल हो।</p>\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the option that expresses the given sentence in active voice. <br>By whom can the problem be solved ?</p>",
                    question_hi: "<p>91. Select the option that expresses the given sentence in active voice. <br>By whom can the problem be solved ?</p>",
                    options_en: [
                        "<p>Whom can solve the problem ?</p>",
                        "<p>Who can solved the problem ?</p>",
                        "<p>Whom can solved the problem ?</p>",
                        "<p>Who can solve the problem?</p>"
                    ],
                    options_hi: [
                        "<p>Whom can solve the problem ?</p>",
                        "<p>Who can solved the problem ?</p>",
                        "<p>Whom can solved the problem ?</p>",
                        "<p>Who can solve the problem?</p>"
                    ],
                    solution_en: "<p>91.(d) Who can solve the problem?(Correct)<br>(a) <span style=\"text-decoration: underline;\">Whom</span> can solve the problem? (Incorrect Pronoun)<br>(b) Who can <span style=\"text-decoration: underline;\">solved</span> the problem? (Incorrect Form of the Verb)<br>(c) <span style=\"text-decoration: underline;\">Whom</span> can solved the problem? (Incorrect Pronoun)</p>",
                    solution_hi: "<p>91.(d) Who can solve the problem? (Correct)<br>(a) <span style=\"text-decoration: underline;\">Whom</span> can solve the problem? (गलत Pronoun)<br>(b) Who can <span style=\"text-decoration: underline;\">solved</span> the problem? (Verb की गलत Form)<br>(c) <span style=\"text-decoration: underline;\">Whom</span> can solved the problem? (गलत Pronoun)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. The synonym for &lsquo;optimise&rsquo; is</p>",
                    question_hi: "<p>92. The synonym for &lsquo;optimise&rsquo; is</p>",
                    options_en: [
                        "<p>to make best use of</p>",
                        "<p>to improve vision</p>",
                        "<p>to see clearly</p>",
                        "<p>to enlarge</p>"
                    ],
                    options_hi: [
                        "<p>to make best use of</p>",
                        "<p>to improve vision</p>",
                        "<p>to see clearly</p>",
                        "<p>to enlarge</p>"
                    ],
                    solution_en: "<p>92.(a) to make best use of</p>",
                    solution_hi: "<p>92.(a) to make best use of./ उत्तम उपयोग करने के लिए</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate option to fill in the blank. <br>She could easily eat the _________ biryani by herself.</p>",
                    question_hi: "<p>93. Select the most appropriate option to fill in the blank. <br>She could easily eat the _________ biryani by herself.</p>",
                    options_en: [
                        "<p>hole</p>",
                        "<p>haul</p>",
                        "<p>whole</p>",
                        "<p>hall</p>"
                    ],
                    options_hi: [
                        "<p>hole</p>",
                        "<p>haul</p>",
                        "<p>whole</p>",
                        "<p>hall</p>"
                    ],
                    solution_en: "<p>93.(c) whole<br>&lsquo;Whole&rsquo; means all of something. The given sentence states that she could easily eat the whole biryani by herself. Hence, &lsquo;whole&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(c) whole<br>&lsquo;Whole&rsquo; का अर्थ है किसी चीज़ का पूरा हिस्सा। दिए गए sentence में कहा गया है कि वह पूरी बिरयानी (biryani) अकेले ही आसानी से खा सकती है। अतः, &lsquo;whole&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Identify the most appropriate antonym of the given word. <br>Summary</p>",
                    question_hi: "<p>94. Identify the most appropriate antonym of the given word. <br>Summary</p>",
                    options_en: [
                        "<p>significant</p>",
                        "<p>Abrupt</p>",
                        "<p>lengthy</p>",
                        "<p>elliptical</p>"
                    ],
                    options_hi: [
                        "<p>significant</p>",
                        "<p>Abrupt</p>",
                        "<p>lengthy</p>",
                        "<p>elliptical</p>"
                    ],
                    solution_en: "<p>94.(c) <strong>Lengthy-</strong> long in duration or extent.<br><strong>Summary-</strong> a brief statement or account of the main points.<br><strong>Significant-</strong> important or meaningful.<br><strong>Abrupt-</strong> sudden or unexpected.<br><strong>Elliptical-</strong> using very few words.</p>",
                    solution_hi: "<p>94.(c) <strong>Lengthy</strong> (लम्बा) - long in duration or extent.<br><strong>Summary</strong> (सारांश) - a brief statement or account of the main points.<br><strong>Significant</strong> (महत्वपूर्ण) - important or meaningful.<br><strong>Abrupt</strong> (एकाएक) - sudden or unexpected.<br><strong>Elliptical</strong> (दीर्घ वृत्ताकार) - using very few words.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>The two families have been engaged in a bitter_________ for the past two decades.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>The two families have been engaged in a bitter_________ for the past two decades.</p>",
                    options_en: [
                        "<p>feud</p>",
                        "<p>argument</p>",
                        "<p>quarrel</p>",
                        "<p>fight</p>"
                    ],
                    options_hi: [
                        "<p>feud</p>",
                        "<p>argument</p>",
                        "<p>quarrel</p>",
                        "<p>fight</p>"
                    ],
                    solution_en: "<p>95.(a) feud<br>Feud - an angry and bitter argument between two people or groups of people that continues, for a long period of time</p>",
                    solution_hi: "<p>95.(a) feud<br>Feud -दो लोगों या लोगों के समूहों के बीच एक क्रोधित और द्वेषपूर्ण बहस जो लंबे समय तक चलती रहती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96)______ called Thanni Paarai. Chennai Thambi was (97)_______ water at a waterbody. The (98)_______ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99)_______ to a rock, lifted it, and drank from a (100)________ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96)_______ called Thanni Paarai. Chennai Thambi was (97)________ water at a waterbody. The (98)_______ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99)_________ to a rock, lifted it, and drank from a (100)________ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: [
                        "<p>place</p>",
                        "<p>shop</p>",
                        "<p>street</p>",
                        "<p>market</p>"
                    ],
                    options_hi: [
                        "<p>place</p>",
                        "<p>shop</p>",
                        "<p>street</p>",
                        "<p>market</p>"
                    ],
                    solution_en: "<p>96.(a) place<br>&ldquo;Thanni paarai&rdquo; is the name of a place.</p>",
                    solution_hi: "<p>96.(a) place<br>&ldquo;Thanni paarai&rdquo; एक जगह का नाम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96)______ called Thanni Paarai. Chennai Thambi was (97)______ water at a waterbody. The (98)______ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99)_______ to a rock, lifted it, and drank from a (100)_______ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96)_______ called Thanni Paarai. Chennai Thambi was (97)______ water at a waterbody. The (98)______ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99)_____ to a rock, lifted it, and drank from a (100)_____ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: [
                        "<p>raining</p>",
                        "<p>wetting</p>",
                        "<p>sprinkling</p>",
                        "<p>sparkling</p>"
                    ],
                    options_hi: [
                        "<p>raining</p>",
                        "<p>wetting</p>",
                        "<p>sprinkling</p>",
                        "<p>sparkling</p>"
                    ],
                    solution_en: "<p>97.(c) <strong>Sprinkling</strong><br><strong>Sprinkling</strong> - a small thinly distributed amount of water(according to the context of the passage).<br><strong>Raining</strong> - rain falls.<br><strong>Wetting</strong> - cover or touch with liquid; moisten.<br><strong>Sparkling</strong> - shining brightly with flashes of light.<br>So, option (c) is the answer</p>",
                    solution_hi: "<p>97.(c) <strong>Sprinkling</strong><br><strong>Sprinkling</strong> - पानी का छोटी मात्रा में वितरित होना (passage के संदर्भ के अनुसार)।<br><strong>Raining</strong> - बारिश <br><strong>Wetting</strong> - भीगा हुआ; नम।<br><strong>Sparkling</strong> - प्रकाश की चमक के साथ चमकना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96)_______ called Thanni Paarai. Chennai Thambi was (97)________ water at a waterbody. The (98)_________ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99)_________ to a rock, lifted it, and drank from a (100)________ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96)________ called Thanni Paarai. Chennai Thambi was (97)_________ water at a waterbody. The (98)________ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99)________ to a rock, lifted it, and drank from a (100)_________ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: [
                        "<p>leaf</p>",
                        "<p>flower</p>",
                        "<p>water</p>",
                        "<p>milk</p>"
                    ],
                    options_hi: [
                        "<p>leaf</p>",
                        "<p>flower</p>",
                        "<p>water</p>",
                        "<p>milk</p>"
                    ],
                    solution_en: "<p>98.(c) Water</p>",
                    solution_hi: "<p>98.(c) Water. / पानी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96)_________ called Thanni Paarai. Chennai Thambi was (97)_________ water at a waterbody. The (98)________ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99)________ to a rock, lifted it, and drank from a (100)________ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96)______ called Thanni Paarai. Chennai Thambi was (97)____ water at a waterbody. The (98)______ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99)______ to a rock, lifted it, and drank from a (100)______ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: [
                        "<p>behind</p>",
                        "<p>over</p>",
                        "<p>above</p>",
                        "<p>near</p>"
                    ],
                    options_hi: [
                        "<p>behind</p>",
                        "<p>over</p>",
                        "<p>above</p>",
                        "<p>near</p>"
                    ],
                    solution_en: "<p>99.(b) over<br>&lsquo;Over&rsquo; is the most appropriate preposition for the blank.</p>",
                    solution_hi: "<p>99.(b) over<br>&lsquo;Over&rsquo; रिक्त स्थान के लिए सबसे उपयुक्त preposition है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96) _______ called Thanni Paarai. Chennai Thambi was (97) ______ water at a waterbody. The (98) ______ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99) _______ to a rock, lifted it, and drank from a (100) ______ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test</strong><br>He remembers the first time he saw the elephant. &ldquo;This was in 2007 at a (96) _______ called Thanni Paarai. Chennai Thambi was (97) ______ water at a waterbody. The (98) ______ was green; not the kind people would drink, &ldquo;says Abraham. &ldquo;After a nice shower, the elephant walked (99) _______ to a rock, lifted it, and drank from a (100) ______ under it.&rdquo;<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: [
                        "<p>tank</p>",
                        "<p>spring</p>",
                        "<p>tap</p>",
                        "<p>pond</p>"
                    ],
                    options_hi: [
                        "<p>tank</p>",
                        "<p>spring</p>",
                        "<p>tap</p>",
                        "<p>pond</p>"
                    ],
                    solution_en: "<p>100.(b) Spring<br><strong>Tank</strong> - A large container or storage chamber, especially for a liquid or gas.<br><strong>Spring</strong> - move or jump suddenly or rapidly upwards or forwards.<br><strong>Tap</strong> - a device by which a flow of liquid or gas from a pipe or container can be controlled.<br><strong>Pond</strong> - a small body of still water formed naturally or by artificial mean.</p>",
                    solution_hi: "<p>100.(b) Spring<br><strong>Tank</strong> - विशेष रूप से तरल या गैस के लिए एक बड़ा पात्र या भंडारण कक्ष।<br><strong>Spring</strong> - अचानक या तेजी से ऊपर या आगे की ओर बढ़ना या कूदना।<br><strong>Tap</strong> - एक उपकरण जिसके द्वारा पाइप या कंटेनर से तरल या गैस के प्रवाह को नियंत्रित किया जा सकता है।<br><strong>Pond</strong> - प्राकृतिक रूप से या कृत्रिम माध्यम से बने पानी का एक छोटा रूप।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>