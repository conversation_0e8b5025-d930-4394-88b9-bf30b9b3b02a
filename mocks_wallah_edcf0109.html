<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Taxus wallichiana Zucc (Himalayan yew) is a medicinal plant found in which of the following states?</p>",
                    question_hi: "<p>1. टैक्सस वालिचियाना (Taxus wallichiana) ज़ुक्क (हिमालयी यू) एक औषधीय पौधा है जो निम्नलिखित में से किस राज्य में पाया जाता है?</p>",
                    options_en: ["<p>Goa</p>", "<p>Jharkhand</p>", 
                                "<p>Himachal Pradesh</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>गोवा</p>", "<p>झारखंड</p>",
                                "<p>हिमाचल प्रदेश</p>", "<p>बिहार</p>"],
                    solution_en: "<p>1.(c) <strong>Himachal Pradesh.</strong> The Himalayan yew grows in the high-altitude forests of the Western Himalayas, especially in the states of Himachal Pradesh, Uttarakhand, and parts of Jammu &amp; Kashmir. The cool and moist environment of these regions provides the ideal conditions for this plant. This plant has traditionally been used to treat epilepsy, respiratory infections, colds, cough, asthma, and liver disorders.</p>",
                    solution_hi: "<p>1.(c) <strong>हिमाचल प्रदेश।</strong> हिमालयी यू पश्चिमी हिमालय के ऊंचे जंगलों में, खासकर हिमाचल प्रदेश, उत्तराखंड तथा जम्मू और कश्मीर के कुछ हिस्सों में उगता है। इन क्षेत्रों का ठंडा और आर्द्र वातावरण इस पौधे के लिए आदर्श परिस्थितियाँ प्रदान करता है। इस पौधे का उपयोग पारंपरिक रूप से मिर्गी, श्वसन संक्रमण, सर्दी, खांसी, अस्थमा एवं यकृत विकारों के इलाज के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. Which of the following pairs is INCORRECTLY matched?",
                    question_hi: "2. निम्नलिखित में से कौन-सा युग्म गलत सुमेलित है?",
                    options_en: [" Nucleus: Lipid metabolism", " Lysosomes: Suicidal bags", 
                                " Mitochondria: Power house of the cell", " Ribosomes: Protein factory"],
                    options_hi: [" नाभिक: लिपिड चयापचय", " लाइसोसोम: आत्मघाती थैली",
                                " माइटोकॉन्ड्रिया: सेल का पावर हाउस", " राइबोसोम: प्रोटीन का कारखाना"],
                    solution_en: "2.(a) The nucleus is primarily involved in storing genetic material and regulating gene expression. Lipids are required for the formation of membranes and contribute to many different processes such as cell signaling, energy supply, and cell death. Various organelles such as the endoplasmic reticulum, mitochondria, peroxisomes, and lipid droplets are involved in lipid metabolism. ",
                    solution_hi: "2.(a) नाभिक मुख्य रूप से आनुवंशिक पदार्थ को संग्रहीत करने और जीन अभिव्यक्ति को विनियमित करने में शामिल है। झिल्ली के निर्माण के लिए लिपिड की आवश्यकता होती है और यह कोशिका संकेतन, ऊर्जा आपूर्ति एवं कोशिका मृत जैसी कई अलग-अलग प्रक्रियाओं में योगदान देता है। एंडोप्लाज्मिक रेटिकुलम, माइटोकॉन्ड्रिया, पेरॉक्सिसोम और लिपिड ड्रॉपलेट्स जैसे विभिन्न अंग लिपिड चयापचय में शामिल होते हैं।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following battles is considered the formal beginning of the British Raj in India?</p>",
                    question_hi: "<p>3. निम्नलिखित में से किस युद्ध को भारत में ब्रिटिश राज की औपचारिक शुरुआत माना जाता है?</p>",
                    options_en: ["<p>First Carnatic War</p>", "<p>Battle of Plassey</p>", 
                                "<p>Battle of Wandiwash</p>", "<p>Battle of Buxar</p>"],
                    options_hi: ["<p>प्रथम कर्नाटक युद्ध</p>", "<p>प्लासी का युद्ध</p>",
                                "<p>वांडिवाश का युद्ध</p>", "<p>बक्सर का युद्ध</p>"],
                    solution_en: "<p>3.(b) <strong>The Battle of Plassey </strong>took place in Bengal on June 23, 1757. British East India Company troops, led by Robert Clive, confronted the forces of Siraj-ud-Daulah, the Nawab of Bengal, along with his French allies. The British East India Company won this battle. The Battle of Buxar was fought in 1764 between the forces of the British East India Company, led by Hector Munro, and the combined armies of Mir Qasim, Shah Alam II, and Shuja-ud-Daulah of Awadh. The Battle of Wandiwash was fought in 1760, between the British and French East India Company.</p>",
                    solution_hi: "<p>3.(b) <strong>प्लासी का युद्ध </strong>23 जून, 1757 को बंगाल में हुआ था। रॉबर्ट क्लाइव के नेतृत्व में ब्रिटिश ईस्ट इंडिया कंपनी की सेना ने बंगाल के नवाब सिराजुद्दौला की सेना एवं उनके फ्रांसीसी सहयोगियों का सामना किया। इस युद्ध में ब्रिटिश ईस्ट इंडिया कंपनी की जीत हुई। बक्सर की लड़ाई 1764 में हेक्टर मुनरो के नेतृत्व में ब्रिटिश ईस्ट इंडिया कंपनी की सेनाओं और अवध के मीर कासिम, शाह आलम द्वितीय एवं शुजाउद्दौला की संयुक्त सेनाओं के बीच हुआ। वांडिवाश का युद्ध 1760 में ब्रिटिश एवं फ्रांसीसी ईस्ट इंडिया कंपनी के बीच हुआ था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following Indian states has the least number of operational airports as of 2022?</p>",
                    question_hi: "<p>4. 2022 तक की स्थिति के अनुसार निम्नलिखित में से किस भारतीय राज्य में सबसे कम परिचालित हवाई अड्डे हैं?</p>",
                    options_en: ["<p>Bihar</p>", "<p>Maharashtra</p>", 
                                "<p>Mizoram</p>", "<p>Karnataka</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>महाराष्ट्र</p>",
                                "<p>मिज़ोरम</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p>4.(c) <strong>Mizoram.</strong> As of September 2024, there are 157 operational airports in India. Uttar Pradesh has the highest number of operational airports in India. India is the third-largest domestic aviation market in the world, after the USA and China.</p>",
                    solution_hi: "<p>4.(c) <strong>मिज़ोरम।</strong> सितंबर 2024 तक, भारत में 157 हवाई अड्डे परिचालन में हैं। भारत में सबसे अधिक परिचालित हवाई अड्डे उत्तर प्रदेश में हैं। संयुक्त राज्य अमेरिका और चीन के बाद भारत विश्व का तीसरा सबसे बड़ा घरेलू विमानन बाजार है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Who among the following, while praising the amending feature of the Indian Constitution said that &lsquo;This variety in the amending process is wise but is rarely found&rsquo;?</p>",
                    question_hi: "<p>5. निम्नलिखित में से किसने भारतीय संविधान की संशोधित विशेषता की प्रशंसा करते हुए कहा था कि \'संशोधन प्रक्रिया में यह विविधता बुद्धिमानी है लेकिन बहुत कम पाई जाती है\'?</p>",
                    options_en: ["<p>Granville Austin</p>", "<p>Ivor Jennings</p>", 
                                "<p>K C Wheare</p>", "<p>HM Seervai</p>"],
                    options_hi: ["<p>ग्रैनविले ऑस्टिन (Granville Austin)</p>", "<p>इवोर जेनिंग्स (Ivor Jennings)</p>",
                                "<p>के. सी. व्हेयर (K C Wheare)</p>", "<p>एचएम सीरवई (HM Seervai)</p>"],
                    solution_en: "<p>5.(c) <strong>K C Wheare</strong> was an Australian academic who was an expert on the constitutions of the British Commonwealth. He also described the Indian Constitution as Quasi federal. Granville Austin, a well-known scholar of the Indian Constitution, said, &ldquo;The amending process has proved itself as one of the most ably conceived aspects of the constitution&rdquo;. Ivor Jennings said that &lsquo;India is a federation with a strong centralising tendency&rsquo;.</p>",
                    solution_hi: "<p>5.(c) <strong>के. सी. व्हेयर</strong> (K C Wheare) एक ऑस्ट्रेलियाई शिक्षाविद थे जो ब्रिटिश राष्ट्रमंडल के संविधानों के विशेषज्ञ थे। उन्होंने भारतीय संविधान को अर्द्ध-संघीय बताया। भारतीय संविधान के सुप्रसिद्ध विद्वान ग्रैनविले ऑस्टिन ने कहा, \"संशोधन प्रक्रिया ने स्वयं को संविधान के सबसे सुविचारित पहलुओं में से एक साबित किया है\"। इवोर (आइवर) जेनिंग्स ने कहा था कि &lsquo;भारत एक मजबूत केंद्रीकरण प्रवृत्ति वाला संघ है।&rsquo;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Tabla player, Ustad Zakir Hussain was awarded which of the following awards by the Government of India in 2023?",
                    question_hi: "6. तबला वादक उस्ताद ज़ाकिर हुसैन को 2023 में भारत सरकार द्वारा निम्नलिखित में से किस पुरस्कार से सम्मानित किया गया?",
                    options_en: [" Bharat Ratna", " Padma Shri", 
                                " Padma Vibhushan", " Padma Bhushan<br /> "],
                    options_hi: [" भारत रत्न", " पद्म श्री",
                                " पद्म विभूषण", " पद्म भूषण"],
                    solution_en: "<p>6.(c) <strong>Padma Vibhushan.</strong> Zakir Hussain is the eldest son of tabla player Alla Rakha. He received the Padma Shri in 1988, the Padma Bhushan in 2002, and the Sangeet Natak Akademi Award in 1990. He has received seven Grammy Award nominations, winning four, including three Grammys at the 66th Grammy Awards in 2024. Notable Tabla exponents: Ustad Ahmed Jan Thirakwa, Pt. Kishan Maharaj, Swapan Chaudhuri.</p>",
                    solution_hi: "<p>6.(c) <strong>पद्म विभूषण।</strong> ज़ाकिर हुसैन, तबला वादक अल्ला रक्खा के सबसे बड़े पुत्र हैं। उन्हें 1988 में पद्म श्री, 2002 में पद्म भूषण और 1990 में संगीत नाटक अकादमी पुरस्कार से सम्मानित किया गया था। उन्हें सात ग्रैमी पुरस्कार नामांकन प्राप्त हुए हैं, जिनमें से चार जीते हैं, जिसमें 2024 में 66वें ग्रैमी पुरस्कार में तीन ग्रैमी शामिल हैं। प्रसिद्ध तबला वादक: उस्ताद अहमद जान थिरकवा, पंडित किशन महाराज, स्वप्न चौधरी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Gangu bai Hangal and Prabha Atre were exponents of the ___________ Gharana.</p>",
                    question_hi: "<p>7. गंगू बाई हंगल और प्रभा अत्रे ___________ घराने की प्रतिपादक थीं।</p>",
                    options_en: ["<p>Rampur</p>", "<p>Jaipur</p>", 
                                "<p>Kirana</p>", "<p>Mewati</p>"],
                    options_hi: ["<p>रामपुर</p>", "<p>जयपुर</p>",
                                "<p>किराना</p>", "<p>मेवाती</p>"],
                    solution_en: "<p>7.(c) <strong>Kirana.</strong> Gangu Bai Hangal was an Indian singer of the khayal genre of Hindustani classical music from Karnataka. She received the Padma Shri in 1962, the Padma Bhushan in 1971, and the Padma Vibhushan in 2002. Prabha Atre is another renowned Indian classical vocalist. She received the Padma Shri in 1990, the Padma Bhushan in 2002, and the Padma Vibhushan in 2022 and Sangeet Natak Academy Award in 1991. Abdul Karim Khan was the founder of the Kirana Gharana. Some exponents: Abdul Wahid Khan, Ashique Ali Khan, Roshan Ara Begum.</p>",
                    solution_hi: "<p>7.(c) <strong>किराना।</strong> गंगू बाई हंगल कर्नाटक की हिंदुस्तानी शास्त्रीय संगीत की ख्याल शैली की एक भारतीय गायिका थीं। उन्हें 1962 में पद्म श्री, 1971 में पद्म भूषण और 2002 में पद्म विभूषण से सम्मानित किया गया। प्रभा अत्रे एक अन्य प्रसिद्ध भारतीय शास्त्रीय गायिका हैं। जिन्हें 1990 में पद्म श्री, 2002 में पद्म भूषण और 2022 में पद्म विभूषण से सम्मानित किया गया तथा 1991 में संगीत नाटक अकादमी पुरस्कार प्राप्त हुआ। अब्दुल करीम खान किराना घराने के संस्थापक थे। कुछ प्रमुख प्रतिपादक (कलाकार): अब्दुल वाहिद खान, आशिक अली खान, रोशन आरा बेगम।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following famous Indian athletes won the Zurich Diamond League final 2022 in September 2022?</p>",
                    question_hi: "<p>8. निम्नलिखित प्रसिद्ध भारतीय एथलीटों में से किसने सितंबर 2022 में ज्यूरिख डायमंड लीग फाइनल 2022 में जीत दर्ज की थी?</p>",
                    options_en: ["<p>Bajrang Punia</p>", "<p>Mirabai Chanu</p>", 
                                "<p>Neeraj Chopra</p>", "<p>PT Usha</p>"],
                    options_hi: ["<p>बजरंग पूनिया</p>", "<p>मीराबाई चानू</p>",
                                "<p>नीरज चोपड़ा</p>", "<p>पी.टी. उषा</p>"],
                    solution_en: "<p>8.(c) <strong>Neeraj Chopra</strong> is an Indian athlete who competes in track and field. He has won two Olympic medals : a gold medal at Tokyo 2020 and a silver medal at Paris 2024 in javelin throw. Bajrang Punia is an Indian freestyle wrestler. At the 2020 Tokyo Olympics, he won a bronze medal. Mirabai Chanu is an Indian weightlifter who won a silver medal at the 2020 Tokyo Olympics. PT Usha, also known as Payyoli Express is a retired Indian track and field athlete.</p>",
                    solution_hi: "<p>8.(c) <strong>नीरज चोपड़ा</strong> एक भारतीय एथलीट हैं जो ट्रैक और फील्ड में प्रतिस्पर्धा करते हैं। उन्होंने दो ओलंपिक पदक जीते हैं: टोक्यो 2020 में स्वर्ण पदक और पेरिस 2024 में भाला फेंक में रजत पदक। बजरंग पुनिया एक भारतीय फ्रीस्टाइल पहलवान हैं। 2020 टोक्यो ओलंपिक में, उन्होंने कांस्य पदक जीता। मीराबाई चानू एक भारतीय भारोत्तोलक हैं जिन्होंने 2020 टोक्यो ओलंपिक में रजत पदक जीता। पी.टी. उषा, जिन्हें पय्योली एक्सप्रेस के नाम से भी जाना जाता है, एक सेवानिवृत्त भारतीय ट्रैक और फील्ड एथलीट हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following Table Tennis World Championship events was held in India?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सी टेबल टेनिस विश्व चैम्पियनशिप प्रतियोगिता भारत में आयोजित की गई थी?</p>",
                    options_en: ["<p>19<sup>th</sup> World Championships, 1952</p>", "<p>17<sup>th</sup> World Championships, 1950</p>", 
                                "<p>20<sup>th</sup> World Championships, 1953</p>", "<p>18<sup>th</sup> World Championships, 1951</p>"],
                    options_hi: ["<p>19वीं विश्व चैम्पियनशिप, 1952</p>", "<p>17वीं विश्व चैम्पियनशिप, 1950</p>",
                                "<p>20वीं विश्व चैम्पियनशिप, 1953</p>", "<p>18वीं विश्व चैम्पियनशिप, 1951</p>"],
                    solution_en: "<p>9.(a) <strong>19th World Championships, 1952</strong> were held in Mumbai. India has also hosted the 1987 World Table Tennis Championships in New Delhi. The World Table Tennis Championships are table tennis competitions sanctioned by the International Table Tennis Federation (ITTF). The first event was held in 1926 in London, England. The 56th edition took place in 2024 in Busan, South Korea, and the 58th edition will be held in 2025 in Doha, Qatar.</p>",
                    solution_hi: "<p>9.(a) <strong>19वीं विश्व चैंपियनशिप, 1952</strong> मुंबई में आयोजित की गई थी। भारत ने नई दिल्ली में 1987 के विश्व टेबल टेनिस चैंपियनशिप की भी मेजबानी की थी। विश्व टेबल टेनिस चैंपियनशिप अंतर्राष्ट्रीय टेबल टेनिस महासंघ (ITTF) द्वारा स्वीकृत टेबल टेनिस प्रतियोगिताएं हैं। पहला आयोजन 1926 में लंदन, इंग्लैंड में हुआ था। 56वां संस्करण 2024 में बुसान, दक्षिण कोरिया में हुआ और 58वां संस्करण 2025 में दोहा, कतर में आयोजित किया जाएगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Match the concepts in column A with their respective descriptions in column B.<br><strong id=\"docs-internal-guid-17804e0c-7fff-c838-fb8f-5c72419c02a2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLHfVKE3qmyc4T88IF3INfUwH5R82vHRYM0O5vvCR2pLunuBwJzJu65WL24ctSaiX-WMVhax9jgiA3VcrsNmK0Xdc4_9j-F320BoN-O42wWMpkeyDiICiPR1FuNnSjSNIrWF-9DA?key=UDL8o0XqWiwwwGhFQ5Q9F_p-\" width=\"523\" height=\"121\"></strong></p>",
                    question_hi: "<p>10. स्तंभ A में दी गई अवधारणाओं को स्तंभ B में दिए गए उनसे संबंधित विवरण के साथ सुमेलित कीजिए।<br><strong id=\"docs-internal-guid-d445d1d6-7fff-eab8-3936-878c677fd744\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeDlHzYjSZL2nRxh2AKdel7WhzGu4awjdCFo0l0dTPY-EfZ-Tco0AjDLT8vRHTboxDh5uisxMDapaQ0IPQ_nlFkjfOTzdhD1-DXZxamsSickD1Gn2ygiYskEfFXoE7H64Tk6yIoHQ?key=UDL8o0XqWiwwwGhFQ5Q9F_p-\" width=\"407\" height=\"138\"></strong></p>",
                    options_en: ["<p>a-5, b-1, c-2, d-3</p>", "<p>a-2, b-3, c-4, d-1</p>", 
                                "<p>a-2, b-1, c-4, d-3</p>", "<p>a-5, b-3, c-2, d-1</p>"],
                    options_hi: ["<p>a-5, b-1, c-2, d-3</p>", "<p>a-2, b-3, c-4, d-1</p>",
                                "<p>a-2, b-1, c-4, d-3</p>", "<p>a-5, b-3, c-2, d-1</p>"],
                    solution_en: "<p>10.(a) <strong>a-5, b-1, c-2, d-3.</strong> Devaluation refers to the intentional downward adjustment of a country\'s currency value relative to another currency or standard. Depreciation, on the other hand, allows a business to allocate the cost of a tangible asset over its useful life for accounting and tax purposes. Deflation occurs when prices decrease over time, contrasting with inflation, which signifies rising prices. Exchange controls are governmental restrictions placed on the buying and selling of currencies.</p>",
                    solution_hi: "<p>10.(a) <strong>a-5, b-1, c-2, d-3. </strong>अवमूल्यन से तात्पर्य किसी देश की मुद्रा मूल्य को किसी अन्य मुद्रा या मानक के सापेक्ष जानबूझकर नीचे की ओर समायोजित करना है। दूसरी ओर, मूल्यह्रास, किसी व्यवसाय को लेखांकन और कर उद्देश्यों के लिए उसके उपयोगी जीवन पर एक मूर्त संपत्ति की लागत आवंटित करने की अनुमति देता है। अपस्फीति तब होती है जब समय के साथ कीमतें घटती हैं, मुद्रास्फीति के विपरीत, जो बढ़ती कीमतों का संकेत देती है। विनिमय नियंत्रण खरीददारी पर लगाए गए सरकारी प्रतिबंध हैं ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. When haloalkanes and aryl and vinyl halides react with magnesium metal they yield which reagent?</p>",
                    question_hi: "<p>11. जब हैलोऐल्केन और एरिल और विनाइल हैलाइड मैग्नीशियम धातु के साथ अभिक्रिया करते हैं तो वे किस अभिकर्मक का उत्पादन करते हैं?</p>",
                    options_en: ["<p>Hinsberg reagent</p>", "<p>Grignard reagent</p>", 
                                "<p>Tollens&rsquo; reagent</p>", "<p>Fehling reagent</p>"],
                    options_hi: ["<p>हिंसबर्ग अभिकर्मक (Hinsberg reagent)</p>", "<p>ग्रिग्नार्ड अभिकर्मक (Grignard reagent)</p>",
                                "<p>टॉलेंस अभिकर्मक (Tollens&rsquo; reagent)</p>", "<p>फेलिंग अभिकर्मक (Fehling reagent)</p>"],
                    solution_en: "<p>11.(b) <strong>Grignard reagent.</strong> The chemical formula for Grignard reagents is R-Mg-X, where R represents an aryl or alkyl group and X represents a halogen. The Tollens reagent is used to test for the presence of aldehydes. Hinsberg Reagent is a chemical compound used in organic chemistry, specifically in the study of amines.</p>",
                    solution_hi: "<p>11.(b) <strong>ग्रिग्नार्ड अभिकर्मक</strong> (Grignard reagent)। ग्रिग्नार्ड अभिकर्मकों का रासायनिक सूत्र R-Mg-X है, जहाँ R एरिल या एल्काइल समूह को दर्शाता है और X हैलोजन को दर्शाता है। टॉलेंस अभिकर्मक (Tollens&rsquo; reagent) का उपयोग एल्डिहाइड की उपस्थिति के परीक्षण के लिए किया जाता है। हिंसबर्ग अभिकर्मक (Hinsberg reagent) एक रासायनिक यौगिक है जिसका उपयोग कार्बनिक रसायन विज्ञान में किया जाता है, विशेष रूप से अमीनों के अध्ययन में।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. How many chambers are there in the heart of fishes?</p>",
                    question_hi: "<p>12. मछलियों के हृदय में कितने प्रकोष्ठ होते हैं?</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>12.(b) <strong>2</strong>. Fish have a single circuit for blood flow and a two-chambered heart that has only a single atrium and a single ventricle. The atrium collects blood that has returned from the body, while the ventricle pumps the blood to the gills where gas exchange occurs and the blood is re-oxygenated; this is called gill circulation. Amphibians and most reptiles have three-chambered hearts, with the exception of crocodiles, which have four-chambered hearts.</p>",
                    solution_hi: "<p>12.(b) <strong>2</strong>. मछलियों में रक्त प्रवाह के लिए एकल परिपथ होता है और दो कक्षीय (प्रकोष्ठ) हृदय होता है जिसमें केवल एक ही आलिंद और एक ही निलय होता है। आलिंद शरीर से वापस आए रक्त को एकत्र करता है, जबकि निलय रक्त को गलफड़ों में पंप करता है जहाँ वायु विनिमय होता है और रक्त को पुनः ऑक्सीजनयुक्त बना देता है; इसे गिल परिसंचरण कहा जाता है। उभयचरों एवं अधिकांश सरीसृपों में तीन कक्षीय हृदय होते हैं, मगरमच्छों को छोड़कर, जिनके हृदय चार कक्षीय होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. As per the report of the NITI Aayog in 2023, which among the following states in India has the lowest percentage of the total population that is multidimensionally poor?</p>",
                    question_hi: "<p>13. 2023 में नीति आयोग की रिपोर्ट के अनुसार, भारत में निम्नलिखित में से किस राज्य में कुल जनसंख्या का सबसे कम प्रतिशत बहुआयामी गरीब का है?</p>",
                    options_en: ["<p>Goa</p>", "<p>Kerala</p>", 
                                "<p>Sikkim</p>", "<p>Uttarakhand</p>"],
                    options_hi: ["<p>गोवा</p>", "<p>केरल</p>",
                                "<p>सिक्किम</p>", "<p>उत्तराखंड</p>"],
                    solution_en: "<p>13.(b) <strong>Kerala.</strong> According to the National Multidimensional Poverty Index (MPI) 2023, Bihar, with 33.76%, followed by Jharkhand with 28.81%, are the states with the highest proportion of population experiencing multidimensional poverty. This report is released by the NITI Aayog.</p>",
                    solution_hi: "<p>13.(b) <strong>केरल।</strong> राष्ट्रीय बहुआयामी गरीबी सूचकांक (MPI) 2023 के अनुसार, बिहार 33.76% के साथ दूसरे और झारखंड 28.81% के साथ तीसरे स्थान पर है, जहाँ बहुआयामी गरीबी का सामना करने वाली आबादी का सबसे अधिक अनुपात है। यह रिपोर्ट नीति आयोग द्वारा जारी की जाती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. What is the unit of measurement for optical power of the lens ?</p>",
                    question_hi: "<p>14. लेंस की ऑप्टिकल पॉवर (प्रकाशीय शक्ति) के लिए माप की इकाई क्या है?</p>",
                    options_en: ["<p>Yotta</p>", "<p>Katal</p>", 
                                "<p>Radian</p>", "<p>Diopter</p>"],
                    options_hi: ["<p>योट्टा</p>", "<p>कटल</p>",
                                "<p>रेडियन</p>", "<p>डायोप्टर</p>"],
                    solution_en: "<p>14.(d) <strong>Diopter.</strong> Base Quantity and SI Units: Length - Metre (m); Mass - Kilogram (kg); Time - Second (s); Electric current - Ampere (A); Thermodynamic temperature - Kelvin (K); Amount of substance - Mole (mol); Luminous intensity - Candela (cd). The prefix \"yotta\" is a metric system unit that represents a factor of 10<sup>24</sup>. The katal is the SI unit of catalytic activity. A radian is a unit of measuring angles.</p>",
                    solution_hi: "<p>14.(d) <strong>डायोप्टर।</strong> मूल मात्रकों का SI मात्रक: लंबाई - मीटर (m); द्रव्यमान - किलोग्राम (kg); समय - सेकंड (s); विद्युत धारा - एम्पीयर (A); ऊष्मागतिक ताप - केल्विन (K); पदार्थ की मात्रा - मोल (mol); ज्योति तीव्रता - कैंडेला (cd)। उपसर्ग (prefix) \"योट्टा\" एक मीट्रिक प्रणाली मात्रक है जो 10<sup>24</sup> के गुणनखंड को दर्शाता है। कैटल उत्प्रेरक क्रियाशीलता की SI मात्रक है। रेडियन कोण मापने की मात्रक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. The Government of India allowed automatic approval of _____% of FDI by Reserve Bank of India in nine categories of industries.</p>",
                    question_hi: "<p>15. भारत सरकार ने उद्योगों की नौ श्रेणियों में भारतीय रिज़र्व बैंक द्वारा ______% तक की एफडीआई (FDI) की स्वचालित स्वीकृति की अनुमति दी।</p>",
                    options_en: ["<p>Up to 51</p>", "<p>Up to 60</p>", 
                                "<p>Up to 41</p>", "<p>Up to 74</p>"],
                    options_hi: ["<p>51</p>", "<p>60</p>",
                                "<p>41</p>", "<p>74</p>"],
                    solution_en: "<p>15.(d) <strong>Up to 74</strong>. Foreign Direct Investment (FDI) refers to an investment made by a company or individual in one country into business operations or assets in another country. The Foreign Investment Facilitation Portal (FIFP) provides single-window clearance for FDI applications under the approval route. It is managed by the Department for Promotion of Industry and Internal Trade (DPIIT), under the Ministry of Commerce and Industry. The two routes for Foreign Direct Investment (FDI) in India are the Automatic Route (Without government approval) and the Government Route (With prior approval of Government).</p>",
                    solution_hi: "<p>15.(d) <strong>74.</strong> प्रत्यक्ष विदेशी निवेश (FDI) से तात्पर्य किसी देश की कंपनी या व्यक्ति द्वारा दूसरे देश में व्यवसाय संचालन या परिसंपत्तियों में किए गए निवेश से है। विदेशी निवेश सुविधा पोर्टल (FIFP) अनुमोदन मार्ग के तहत FDI आवेदनों के लिए एकल-खिड़की मंजूरी प्रदान करता है। इसका प्रबंधन वाणिज्य एवं उद्योग मंत्रालय के तहत उद्योग एवं आंतरिक व्यापार संवर्धन विभाग (DPIIT) द्वारा किया जाता है। भारत में प्रत्यक्ष विदेशी निवेश (FDI) के लिए दो मार्ग हैं स्वचालित मार्ग (सरकारी अनुमोदन के बिना) तथा सरकारी मार्ग (सरकार की पूर्व स्वीकृति के साथ)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Who among the following was the last sultan of Delhi sultanate?</p>",
                    question_hi: "<p>16. निम्नलिखित में से कौन दिल्ली सल्तनत का अंतिम सुल्तान था?</p>",
                    options_en: ["<p>Firoz Shah Tughlaq</p>", "<p>Sikandar Lodi</p>", 
                                "<p>Muhammad Bin Tughlaq</p>", "<p>Ibrahim Lodi</p>"],
                    options_hi: ["<p>फ़िरोज़ शाह तुग़लक</p>", "<p>सिकंदर लोदी</p>",
                                "<p>मुहम्मद बिन तुगलक</p>", "<p>इब्राहिम लोदी</p>"],
                    solution_en: "<p>16.(d) <strong>Ibrahim Lodi</strong> was the last Sultan of the Delhi Sultanate (1206&ndash;1526), he became Sultan in 1517 after the death of his father Sikandar Khan Lodi. The Lodi dynasty was founded by Bahlol lodi, reigning for thirty eight years. In 1526, he was defeated and killed in the Battle of Panipat by Babur, giving way to the emergence of the Mughal Empire in India.</p>",
                    solution_hi: "<p>16.(d) <strong>इब्राहिम लोदी</strong> दिल्ली सल्तनत (1206-1526) का अंतिम सुल्तान था, वह अपने पिता सिकंदर खान लोदी की मृत्यु के बाद 1517 में सुल्तान बना। लोदी वंश की स्थापना बहलोल लोदी ने की थी, जिसने अड़तीस वर्षों तक शासन किया। वह 1526 में, हुए पानीपत के युद्ध में बाबर द्वारा पराजित हुआ और मार दिया गया, जिससे भारत में मुगल साम्राज्य का उदय हुआ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which of the following cups/trophies is NOT related to the Indian domestic cricket?</p>",
                    question_hi: "<p>17. निम्नलिखित में से कौन-सा कप/ट्रॉफी भारतीय घरेलू क्रिकेट से संबंधित नहीं है?</p>",
                    options_en: ["<p>Duleep Trophy</p>", "<p>Durand Cup</p>", 
                                "<p>Deodhar Trophy</p>", "<p>Ranji Trophy</p>"],
                    options_hi: ["<p>दलीप ट्रॉफी</p>", "<p>डूरंड कप</p>",
                                "<p>देवधर ट्रॉफी</p>", "<p>रणजी ट्रॉफी</p>"],
                    solution_en: "<p>17.(b) <strong>Durand Cup</strong> is an annual domestic football competition in India which was first held in 1888 in Shimla, Himachal Pradesh. Domestic cricket competitions in India : The Vijay Hazare Trophy, The Irani Trophy and The NKP Salve Challenger Trophy.</p>",
                    solution_hi: "<p>17.(b) <strong>डूरंड कप</strong> भारत में एक वार्षिक घरेलू फुटबॉल प्रतियोगिता है जो पहली बार 1888 में शिमला, हिमाचल प्रदेश में आयोजित की गई थी। भारत में घरेलू क्रिकेट प्रतियोगिताएँ: विजय हजारे ट्रॉफी, ईरानी ट्रॉफी और एनकेपी साल्वे चैलेंजर ट्रॉफी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. In the context of water bodies, Neap Tides occur :",
                    question_hi: " 18. जल निकायों के संदर्भ में, लघु ज्वार भाटा कब आते हैं?",
                    options_en: [" when the earth, moon and sun line up in a straight line", " when the sun and moon are on the opposite sides", 
                                " when there is so much interference by continents", " when the sun and moon are at right angles to each other"],
                    options_hi: [" जब पृथ्वी, चंद्रमा और सूर्य एक सीधी रेखा में आ जाते हैं", " जब सूर्य और चंद्रमा विपरीत दिशाओं में होते हैं",
                                " जब महाद्वीपों द्वारा बहुत अधिक हस्तक्षेप होता है।", " जब सूर्य और चंद्रमा एक दूसरे के समकोण पर होते हैं"],
                    solution_en: "18.(d) During neap tide, gravitational and centrifugal forces are divided. The high tides of a neap tide are lower than the average high tide and the low tides of a Neap tide are higher than the average low tide. Spring tides - When the sun, the moon and the earth are in a straight line, the height of the tide will be higher. These are called spring tides, and they occur twice a month. ",
                    solution_hi: "18.(d) लघु ज्वार के दौरान, गुरुत्वाकर्षण और अपकेंद्रीय बल विभाजित होते हैं। लघु ज्वार का उच्च ज्वार औसत उच्च ज्वार से कम होता है और लघु ज्वार का निम्न ज्वार औसत निम्न ज्वार से अधिक होता है। वसंत ज्वार - जब सूर्य, चंद्रमा और पृथ्वी एक सीधी रेखा में होते हैं, तो ज्वार की ऊंचाई अधिक होगी। इन्हें वसंत ज्वार कहा जाता है, और ये महीने में दो बार आते हैं। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Chittani Ramachandra Hegde was associated with which of the following dances?</p>",
                    question_hi: "<p>19. चित्तानी रामचंद्र हेगड़े निम्नलिखित में से किस नृत्य से संबंधित थे?</p>",
                    options_en: ["<p>Chhau dance</p>", "<p>Chappeli dance</p>", 
                                "<p>Yakshagana dance</p>", "<p>Huttari dance</p>"],
                    options_hi: ["<p>छऊ नृत्य (Chhau dance)</p>", "<p>छपेली नृत्य (Chappeli dance)</p>",
                                "<p>यक्षगान नृत्य (Yakshagana dance)</p>", "<p>हुत्तारी नृत्य (Huttari dance)</p>"],
                    solution_en: "<p>19.(c) <strong>Yakshagana dance</strong> is a traditional folk dance form popular in Coastal Karnataka districts. Chittani Ramachandra Hegde was awarded the Padma Shri award in 2012. Some exponents of Yakshagana : Kuriya Vithala Shastry, Soorikumeru Govinda Bhat, Narayana Hasyagar.</p>",
                    solution_hi: "<p>19.(c) <strong>यक्षगान नृत्य </strong>कर्नाटक के तटीय जिलों में लोकप्रिय एक पारंपरिक लोक नृत्य है। चित्तानी रामचंद्र हेगड़े को 2012 में पद्म श्री पुरस्कार से सम्मानित किया गया था। यक्षगान के कुछ प्रतिपादक: कुरिया विट्ठल शास्त्री, सूरीकुमेरु गोविंदा भट्ट, नारायण हस्यागर।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. The idols of which three Gods are installed in Jagannath Temple, Puri?</p>",
                    question_hi: "<p>20. जगन्नाथ मंदिर, पुरी में किन तीन भगवानों की मूर्तियाँ स्थापित हैं?</p>",
                    options_en: ["<p>Brahma, Vishnu and Mahesh</p>", "<p>Radha, Krishna and Balram</p>", 
                                "<p>Jagannath, Balabhadra and Subhadra</p>", "<p>Ram, Lakshman and Janaki</p>"],
                    options_hi: ["<p>ब्रह्मा, विष्णु और महेश</p>", "<p>राधा, कृष्ण और बलराम</p>",
                                "<p>जगन्नाथ, बलभद्र और सुभद्रा</p>", "<p>राम, लक्ष्मण और जानकी</p>"],
                    solution_en: "<p>20.(c) <strong>Jagannath, Balabhadra and Subhadra. </strong>These three deities are known as the Trimurti, or trinity. They are carved from sacred neem logs, called daru, and sit on a bejewelled platform called the ratnabedi. The idols are different colors, with Lord Jagannath being black, Balabhadra white, and Subhadra yellow.</p>",
                    solution_hi: "<p>20.(c) <strong>जगन्नाथ, बलभद्र और सुभद्रा।</strong> इन तीनों देवताओं को त्रिमूर्ति या त्रिदेव के नाम से जाना जाता है। वे पवित्र नीम की लकड़ियों, जिन्हें दारू कहा जाता है, से बनाई गई हैं और रत्नबेदी नामक रत्नजड़ित मंच पर विराजमान हैं। मूर्तियाँ अलग-अलग रंगों की हैं, भगवान जगन्नाथ काले, बलभद्र सफेद और सुभद्रा पीले रंग की हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Anna Chandy, the first Indian woman to serve as a judge at a high court was appointed in which High Court?</p>",
                    question_hi: "<p>21. उच्च न्यायालय में न्यायाधीश के रूप में सेवाएं देने वाली पहली भारतीय महिला अन्ना चांडी किस उच्च न्यायालय में नियुक्त थीं?</p>",
                    options_en: ["<p>West Bengal</p>", "<p>Kerala</p>", 
                                "<p>Tamil Nadu</p>", "<p>Andhra Pradesh</p>"],
                    options_hi: ["<p>पश्चिम बंगाल</p>", "<p>केरल</p>",
                                "<p>तमिलनाडु</p>", "<p>आंध्र प्रदेश</p>"],
                    solution_en: "<p>21.(b) <strong>Kerala.</strong> She was the first female judge in India, the first woman to become a judge of a High Court. She became the founder and editor of the journal (Shreemati) in which she supported women\'s rights. Justice Leila Seth was the first woman to be Chief Justice of a High Court. Fathima Beevi is the first female judge of the Supreme Court of India.</p>",
                    solution_hi: "<p>21.(b) <strong>केरल।</strong> वह भारत की पहली महिला न्यायाधीश थीं, तथा उच्च न्यायालय की न्यायाधीश बनने वाली पहली महिला थीं। वह पत्रिका (श्रीमती) की संस्थापक और संपादक बनीं, जिसमें उन्होंने महिलाओं के अधिकारों का समर्थन किया। न्यायमूर्ति लीला सेठ उच्च न्यायालय की मुख्य न्यायाधीश बनने वाली पहली महिला थीं। फातिमा बीवी भारत के सर्वोच्च न्यायालय की पहली महिला न्यायाधीश हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. As in July 2021,The NIPUN Bharat Scheme was launched by which of the following Ministries?</p>",
                    question_hi: "<p>22. जुलाई, 2021 के दौरान, निम्नलिखित में से किस मंत्रालय ने NIPUN भारत योजना का शुभारंभ किया?</p>",
                    options_en: ["<p>Ministry of Health and Family Welfare</p>", "<p>Ministry of Education</p>", 
                                "<p>Ministry of Corporate Affairs</p>", "<p>Ministry of Agriculture and Farmers</p>"],
                    options_hi: ["<p>स्वास्थ्य एवं परिवार कल्याण मंत्रालय</p>", "<p>शिक्षा मंत्रालय</p>",
                                "<p>कॉर्पोरेट कार्य मंत्रालय</p>", "<p>कृषि एवं किसान मंत्रालय</p>"],
                    solution_en: "<p>22.(b) <strong>Ministry of Education.</strong> National Initiative for Proficiency in Reading with Understanding and Numeracy (NIPUN Bharat) was launched to ensure that every child in the country necessarily attains foundational literacy and numeracy by the end of Grade 3, by 2026-27. It aims to address the learning needs of children aged 3 to 9 years.</p>",
                    solution_hi: "<p>22.(b) <strong>शिक्षा मंत्रालय।</strong> नेशनल इनीशिएटिव फॉर प्रोफिसीएंसी इन रीडिंग विद अंडरस्टैंडिंग एंड न्यूमरैसी (NIPUN Bharat) की शुरुआत यह सुनिश्चित करने के लिए की गई थी कि देश का प्रत्येक बच्चा 2026-27 तक कक्षा 3 के अंत तक आवश्यक रूप से बुनियादी साक्षरता और संख्यात्मकता प्राप्त कर ले। इसका उद्देश्य 3 से 9 वर्ष की आयु के बच्चों की सीखने की आवश्यकताओं को पूरा करना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which Article of the Constitution of India expressly provides that the President is eligible for re-election?</p>",
                    question_hi: "<p>23. भारतीय संविधान का कौन-सा अनुच्छेद स्पष्ट रूप से यह व्&zwj;यवस्&zwj;था प्रदान करता है कि राष्ट्रपति पुनर्निर्वाचन के लिए योग्य है?</p>",
                    options_en: ["<p>Article 58</p>", "<p>Article 56</p>", 
                                "<p>Article 59</p>", "<p>Article 57</p>"],
                    options_hi: ["<p>अनुच्छेद 58</p>", "<p>अनुच्छेद 56</p>",
                                "<p>अनुच्छेद 59</p>", "<p>अनुच्छेद 57</p>"],
                    solution_en: "<p>23.(d) <strong>Article 57.</strong> Important articles related to the President of India : Article 52 - The President of India. Article 53 - Executive power of the Union. Article 54 - Election of President. Article 55 - Manner of election of President. Article 56 - Term of office of President. Article 58 - Qualifications for election as President. Article 59 - Conditions of President&rsquo;s office. Article 60 - Oath or affirmation by the President.</p>",
                    solution_hi: "<p>23.(d) <strong>अनुच्छेद 57</strong> । भारत के राष्ट्रपति से संबंधित महत्वपूर्ण अनुच्छेद: अनुच्छेद 52 - भारत का राष्ट्रपति। अनुच्छेद 53 - संघ की कार्यकारी शक्ति। अनुच्छेद 54 - राष्ट्रपति का निर्वाचन। अनुच्छेद 55 - राष्ट्रपति के निर्वाचन की पद्धति। अनुच्छेद 56 - राष्ट्रपति का कार्यकाल। अनुच्छेद 58 - राष्ट्रपति निर्वाचित होने के लिए अर्हताएं। अनुच्छेद 59 - राष्ट्रपति के पद की शर्तें। अनुच्छेद 60 - राष्ट्रपति द्वारा शपथ या प्रतिज्ञान।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. In which of the following cities did Mahatma Gandhi lead the peasant movement against the imposed indigo cultivation by the British planters?</p>",
                    question_hi: "<p>24. निम्नलिखित में से किस शहर में महात्मा गांधी ने ब्रिटिश बागान मालिकों द्वारा थोपी गई नील की खेती के खिलाफ किसान आंदोलन का नेतृत्व किया था?</p>",
                    options_en: ["<p>Kheda</p>", "<p>Gorakhpur</p>", 
                                "<p>Bardoli</p>", "<p>Champaran</p>"],
                    options_hi: ["<p>खेड़ा</p>", "<p>गोरखपुर</p>",
                                "<p>बारदोली</p>", "<p>चंपारण</p>"],
                    solution_en: "<p>24.(d) <strong>The Champaran Satyagraha</strong> of 1917 was the first Satyagraha movement led by Gandhi in India. In 1918, he organised a satyagraha to support the peasants of the Kheda district of Gujarat. In 1918, Mahatma Gandhi went to Ahmedabad to organise a satyagraha movement amongst cotton mill workers. The Bardoli Satyagraha of 1928, in the state of Gujarat was led by Vallabhbhai Patel for the farmers of Bardoli against the unjust raising of taxes.</p>",
                    solution_hi: "<p>24.(d) <strong>चंपारण सत्याग्रह</strong> 1917 में भारत में गांधी जी द्वारा नेतृत्व किया गया पहला सत्याग्रह आंदोलन था। 1918 में, उन्होंने गुजरात के खेड़ा जिले के किसानों का समर्थन करने के लिए सत्याग्रह का आयोजन किया। 1918 में, महात्मा गांधी कपास मिल श्रमिकों के बीच सत्याग्रह आंदोलन का आयोजन करने के लिए अहमदाबाद गए। 1928 में गुजरात राज्य में बारदोली सत्याग्रह का नेतृत्व वल्लभभाई पटेल ने करों की अनुचित वृद्धि के खिलाफ बारदोली के किसानों के लिए किया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "25. According to the Ashrama system of Vedic life, which of the following was the third stage of life?",
                    question_hi: "25. वैदिक जीवन की आश्रम प्रणाली के अनुसार, निम्नलिखित में से जीवन का तीसरा चरण कौन-सा था?",
                    options_en: [" Grihastha", " Sanyasa", 
                                " Brahmacharya", " Vanaprastha"],
                    options_hi: [" गृहस्थ", " सन्यास",
                                " ब्रह्मचर्य", " वानप्रस्थ"],
                    solution_en: "<p>25.(d) <strong>Vanaprastha,</strong> where a person handed over household responsibilities to the next generation, took an advisory role, and gradually withdrew from the world. The four stages of the ashrama system are : Brahmacharya (Student\'s life), Grihastha (household life), Vanaprastha (retired life), Sannyasa (renounced life).</p>",
                    solution_hi: "<p>25.(d) <strong>वानप्रस्थ,</strong> जहाँ व्यक्ति घर की ज़िम्मेदारियाँ अगली पीढ़ी को सौंपता है, सलाहकार की भूमिका निभाता है, और धीरे-धीरे दुनिया से अलग हो जाता है। आश्रम प्रणाली के चार चरण हैं: ब्रह्मचर्य (छात्र जीवन), गृहस्थ (घरेलू जीवन), वानप्रस्थ (सेवानिवृत्त जीवन), संन्यास (त्याग का जीवन)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>