<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, &lsquo;TOUGH&rsquo; is coded as &lsquo;89251&rsquo; and &lsquo;GHOST&rsquo; is coded as &lsquo;93258&rsquo;. What is the code for &lsquo;S&rsquo; in the given code language?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'TOUGH\' को \'89251\' के रूप में कूटबद्ध किया जाता है और \'GHOST\' को \'93258\' के रूप में कूटबद्ध किया जाता है। दी गई कूट भाषा में \'S\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>9</p>", "<p>3</p>", 
                                "<p>8</p>", "<p>1</p>"],
                    options_hi: ["<p>8</p>", "<p>3</p>",
                                "<p>1</p>", "<p>9</p>"],
                    solution_en: "<p>1.(b) &lsquo;TOUGH&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;89251&hellip;&hellip;&hellip; (i)<br>&lsquo;GHOST&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;93258&rsquo; &hellip;&hellip;&hellip; (ii)<br>In equation (i) and (ii) letters (G, H, O , T) are common and codes ( 2, 5, 8, 9) are also common.<br>So, the code of &lsquo;S&rsquo; is 3.</p>",
                    solution_hi: "<p>1.(b) &lsquo;TOUGH&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;89251&hellip;&hellip;&hellip; (i)<br>&lsquo;GHOST&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;93258&rsquo; &hellip;&hellip;&hellip; (ii)<br>समीकरण (i) और (ii) में अक्षर (G, H, O, T) उभयनिष्ठ हैं और कोड (2, 5, 8, 9) भी उभयनिष्ठ हैं।<br>तो, \'S\' का कोड 3 है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, \'SHE\' is written as \'EIT\' and \'SAD\' is written as \'DBT\'. How will \'SUN&rsquo; be written in that language?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में, \'SHE\' को \'EIT\' लिखा जाता है और &lsquo;SAD\' को \'DBT\' लिखा जाता है। इसी कूट भाषा में \'SUN&rsquo; को किस प्रकार लिखाजाएगा?</p>",
                    options_en: ["<p>NTV</p>", "<p>NTY</p>", 
                                "<p>NYT</p>", "<p>NVT</p>"],
                    options_hi: ["<p>NTV</p>", "<p>NTY</p>",
                                "<p>NYT</p>", "<p>NVT</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562186.png\" alt=\"rId4\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562355.png\" alt=\"rId5\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562509.png\" alt=\"rId6\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562186.png\" alt=\"rId4\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562355.png\" alt=\"rId5\"><br>इसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562509.png\" alt=\"rId6\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code,<br>\'it is raining\' is coded as \'dy po ko\', <br>\'is raining now\' is coded as \'po jo ko\' and<br>\'is now it\' is coded as \'po jo dy\'. <br>(All the codes are two-letter codes only.) <br>What is the code for \'raining\'?</p>",
                    question_hi: "<p>3. एक निश्चित कूट में, <br>\'it is raining\' को \'dy po ko\' के रूप में कूटबद्ध किया जाता है, <br>\'is raining now\' को \'po jo ko\' के रूप में कूटबद्ध किया जाता है और<br>\'is now it\' को \'po jo dy\' के रूप में कूटबद्ध किया जाता है। <br>(सभी कूट केवल दो अक्षर वाले कूट हैं।) <br>\'raining\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>ko</p>", "<p>po</p>", 
                                "<p>jo</p>", "<p>dy</p>"],
                    options_hi: ["<p>ko</p>", "<p>po</p>",
                                "<p>jo</p>", "<p>dy</p>"],
                    solution_en: "<p>3.(a)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdEo2UW4TC1uSq35R3x829AM1sP391EtSNh8PVA2jLw-906lU1ldNwIoSw-3NaW5D4ei3x5eLuIZtoqBVCEEZ2303sCHRJHQXsM7t1ZfRJ_JDUEX5OOHymR298WwiErr4C2JCnTKw?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"373\" height=\"128\"><br>The code of &lsquo;raining&rsquo; is &lsquo;ko&rsquo;</p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdEo2UW4TC1uSq35R3x829AM1sP391EtSNh8PVA2jLw-906lU1ldNwIoSw-3NaW5D4ei3x5eLuIZtoqBVCEEZ2303sCHRJHQXsM7t1ZfRJ_JDUEX5OOHymR298WwiErr4C2JCnTKw?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"373\" height=\"128\"><br>\'raining\' का कोड \'ko\' है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. In a certain code language, ‘toys are gadgets’ is coded as ‘dj sq ty’ and ‘gadgets are machines’ is coded as ‘mp ty dj’. What is code for ‘gadgets’ in that language?",
                    question_hi: "4. एक निश्चित कूट भाषा में, \'toys are gadgets\' को \'dj sq ty\' के रूप में कूटबद्ध किया जाता है और \'gadgets are machines\' को \'mp ty dj\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'gadgets\' के लिए संभावित कूट क्या है?",
                    options_en: [" either ‘ty’ or ‘mp’ ", " either ‘ty’ or ‘dj’  ", 
                                " dj  ", " sq"],
                    options_hi: [" either ‘ty’ or ‘mp’ ", " either ‘ty’ or ‘dj’  ",
                                " dj  ", " sq"],
                    solution_en: "4.(b) ‘toys are gadgets’ <math display=\"inline\"><mo>→</mo></math> ‘dj sq ty’ …….. (i)<br /> ‘gadgets are machines’ <math display=\"inline\"><mo>→</mo></math>  ‘mp ty dj’ …….. (ii)<br />In equation (i) and (ii) ‘are gadgets’ are  common and code (dj, ty) are also common.<br />So, the code of ‘gadgets’ is either ‘dj’ or ‘ty’.",
                    solution_hi: "4.(b) ‘toys are gadgets’ <math display=\"inline\"><mo>→</mo></math> ‘dj sq ty’ …….. (i)<br /> ‘gadgets are machines’ <math display=\"inline\"><mo>→</mo></math>  ‘mp ty dj’ …….. (ii)<br />समीकरण (i) और (ii) में ‘are gadgets’ उभयनिष्ठ हैं और कोड (dj, ty) भी उभयनिष्ठ हैं। <br />तो, ‘gadgets’  का कोड या तो ‘ty’ या ‘dj’  \' है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, \'MAIN\' is written as \'OHBL\' and \'MAIL\' is written as \'MHBL\'. How will \'MANY&rsquo; be written in that language?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में, \'MAIN\' को \'OHBL\' लिखा जाता है और &lsquo;MAIL\' को \'MHBL\' लिखा जाता है। इसी कूट भाषा में \'MANY&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>ZLMB</p>", "<p>ZMBL</p>", 
                                "<p>ZBLM</p>", "<p>ZMLB</p>"],
                    options_hi: ["<p>ZLMB</p>", "<p>ZMBL</p>",
                                "<p>ZBLM</p>", "<p>ZMLB</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562829.png\" alt=\"rId8\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562987.png\" alt=\"rId9\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100563089.png\" alt=\"rId10\"></p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562829.png\" alt=\"rId8\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100562987.png\" alt=\"rId9\"><br>इसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100563089.png\" alt=\"rId10\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "6. In a certain code language, ‘PANTHER’ is written as ‘0146283’ and ‘ANOTHER’ is written as ‘2864109’. How will ‘O’ be written in that language?",
                    question_hi: "6. एक निश्चित कूट भाषा में, ‘PANTHER’ को ‘0146283’ के रूप में लिखा जाता है और ‘ANOTHER’ को ‘2864109’ के रूप में लिखा जाता है। उसी भाषा में ‘O’ को किस प्रकार लिखा जाएगा?",
                    options_en: [" 0 ", " 8 ", 
                                " 3  ", " 9"],
                    options_hi: [" 0 ", " 8 ",
                                " 3  ", " 9"],
                    solution_en: "6.(d)<br />P A N T H E R <math display=\"inline\"><mo>→</mo></math> 0 1 4 6 2 8 3<br />A N O T H E R <math display=\"inline\"><mo>→</mo></math> 2 8 6 4 1 0 9<br />From above coding, the code for “O” is 9.",
                    solution_hi: "6.(d)<br />P A N T H E R <math display=\"inline\"><mo>→</mo></math> 0 1 4 6 2 8 3<br />A N O T H E R <math display=\"inline\"><mo>→</mo></math> 2 8 6 4 1 0 9<br />उपरोक्त कोडिंग से, \"O\" का कोड 9 है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, &lsquo;CYCLE&rsquo; is coded as &lsquo;242241522&rsquo; and &lsquo;FINAL&rsquo; is coded as &lsquo;2118132615&rsquo;. How will &lsquo;MUSIC&rsquo; be coded in that language?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में, &lsquo;CYCLE&rsquo; को &lsquo;242241522&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;FINAL&rsquo; को &lsquo;2118132615&rsquo; के रूप में कूटबद्ध किया जाता है। उसी भाषा में &lsquo;MUSIC&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>146192184</p>", "<p>14681824</p>", 
                                "<p>146191824</p>", "<p>1421191824</p>"],
                    options_hi: ["<p>146192184</p>", "<p>14681824</p>",
                                "<p>146191824</p>", "<p>1421191824</p>"],
                    solution_en: "<p>7.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcpjj3Km1C5XJmECjY0GP1rsnKgP1Hb5aKrWZF4ofBPGxIRjias2f1nmy6JVFg8YR5kUVu3P1LbfcuzCFarasGGAD7u168-apMSkW22Ny25kuIuSnKxc7OQOtgqPRw7OwxymoEkqw?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"145\" height=\"124\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdkrCoRIGa_hVHSkFBTXxPHk5es076EDxgaWeyxWF5LrN8XtJ2fCf0svfH4-BFCS3GQb8XkOTDB7i4ZYr9tFHyw60WXu08FwANPHa7Xw0I3SVgTKUrn1T4_bOhUmMkELyg-wW8sYQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"146\" height=\"129\"><br>Similarly<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfDqvolviMFwiKdXQYgTA-zUR8W9EHCeTcEJvEMuCFsoKokE84dBxvMpIY6DrSZBwQaQtK6d-04HEk7fZXXky4rT7XnlRC0c11DAR6n3tbbgA27npJGgFiFSS3uaDnppFKWLtIp?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"157\" height=\"139\"></p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfNa8884yKwyA9KK6wZB5tHhDs0sM4TdX7b1vYRtN_mhESPIrj22t_QY8JSGqh5HQWUUrBcThMTBDTlBlj5WGSVrbdmJRruwy8w6h5oHc-K-posLuY22oenZJRtRiYtzpEzIO7QCg?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"150\" height=\"126\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeS48XUDLIlxlUy0RiMB_aLJ6QHo07tn_7lY8zOGZ9qGI-X1DCvBtAKxOryQoXPUPibihWKMJfs_QPfQYe-ePQ4kORrHLmT2jyik-z90n2HlhjXm36k-pQU8Oo3zJAXWdfZ1mQ6?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"156\" height=\"134\"><br>इसी प्रकार<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFUKJAMEv1cANPFSOrFlTAF8QQL4HNUxZFutMvqecIqEe6unq4FGRF-9Flj20nxFTxCGuOQukJ0Ff9o27Ma9Ym2hobjS03JGbBBK7jQf3TeO8NHN7Cq0av4lFjepnU6uaacnypKQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"165\" height=\"147\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, \'RADIO\' is written as \'SZEHP\' and \'FREQUENCY\' is written as \'GQFPVDOBZ\'. How will \'TELEVISION\' be written in that language?</p>",
                    question_hi: "<p>8. एक निश्चित कोड भाषा में, \'RADIO\' को \'SZEHP\' और \'FREQUENCY\' को \'GQFPVDOBZ\' लिखा जाता है। उसी भाषा में \'TELEVISION\' कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>UDMDWHTHOM</p>", "<p>UDMDWHTHPM</p>", 
                                "<p>UDMDWHTHPN</p>", "<p>UDMDWHHTPM</p>"],
                    options_hi: ["<p>UDMDWHTHOM</p>", "<p>UDMDWHTHPM</p>",
                                "<p>UDMDWHTHPN</p>", "<p>UDMDWHHTPM</p>"],
                    solution_en: "<p>8.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPLJGTDcYUcvRMhP5tnwxNj_OaHwV06q6j9cZ5ro7k1FdgmITXOyGpzRYxERGByhIBQ-1KLc6M93Wnotvmk00u3nFGWV0JjA_HKJ0LOv0F5tKgtKYm08eUF9bb5EuB8FfPK7uDJw?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"164\" height=\"100\"><br><strong>and</strong> <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHXlPuyfXrT46fU9gqX0Xz991ZDiEcgBZMFZ4kAwQRxUIAtIuj80KMRLW9LablQlmLnMpQrsXKQFsrzPF3bOq76bRDxRhQ8oEjN6bywugBuzN1Nwa8duW72M55UL4dU9gS9ajYuA?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"243\" height=\"82\"><br><strong>Similarly</strong><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXci-gC8MF5YdofRUc9g2XwpqBQ7AnqA8DNtd27gQUR0q8hEqklvwWbKW0LiFHJYdoDzE01G55HvzWS38v8C-5bqlEWaWTu2ZtQaIqv8oq4prGaC7ZnKjLwa9Gx-OEIE7V__mOareg?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"250\" height=\"82\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPLJGTDcYUcvRMhP5tnwxNj_OaHwV06q6j9cZ5ro7k1FdgmITXOyGpzRYxERGByhIBQ-1KLc6M93Wnotvmk00u3nFGWV0JjA_HKJ0LOv0F5tKgtKYm08eUF9bb5EuB8FfPK7uDJw?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"164\" height=\"100\"><br>और<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHXlPuyfXrT46fU9gqX0Xz991ZDiEcgBZMFZ4kAwQRxUIAtIuj80KMRLW9LablQlmLnMpQrsXKQFsrzPF3bOq76bRDxRhQ8oEjN6bywugBuzN1Nwa8duW72M55UL4dU9gS9ajYuA?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"245\" height=\"83\"><br>इसी प्रकार<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXci-gC8MF5YdofRUc9g2XwpqBQ7AnqA8DNtd27gQUR0q8hEqklvwWbKW0LiFHJYdoDzE01G55HvzWS38v8C-5bqlEWaWTu2ZtQaIqv8oq4prGaC7ZnKjLwa9Gx-OEIE7V__mOareg?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"250\" height=\"82\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, &lsquo;BOTH&rsquo; is written as &lsquo;DQVJ&rsquo; and &lsquo;DARE&rsquo; is written as &lsquo;FCTG&rsquo; How will &lsquo;WILL&rsquo; be written in that language?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में, \'BOTH\' को \'DQVJ\' के रूप में लिखा जाता है और \'DARE\' को \'FCTG\' के रूप में लिखा जाता है। उसी भाषा में \'WILL\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>XJMM</p>", "<p>YJMN</p>", 
                                "<p>XKNM</p>", "<p>YKNN</p>"],
                    options_hi: ["<p>XJMM</p>", "<p>YJMN</p>",
                                "<p>XKNM</p>", "<p>YKNN</p>"],
                    solution_en: "<p>9.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcIGg7ByAb4H4WI53Hyvz-jp4SgqLddgsHpykLIN_3-Er-bB-HQsfiMVAeZgBhj8VNa1wyG7Z3mG25KaGFpTuk281bmVLU2U2l7ZM_bL6iMCCXyI1FPzds7oRK13AJNKrsqiDE2rQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"165\" height=\"129\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUtL1qeG6GHjIQ0C0wO6t-NWXOqP9W8pIPtIOcIPBYfQIhbyizfjsd5bwOKckkIBqLYiCvyrJDNfR7i_wXtu0b0sXtwFXf7Kx_cXyVxFWLQ4URyD1CY9wFeMm1bcqFTmEheRRL?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"166\" height=\"128\"><br><strong>Similarly</strong> <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXep62jgHgC3PIktG4iaLFvLc7bnMxep01eJw23qQD4XKG3X2m3ivDqFP2f7CShBAQMM-1DG2ur-7XHVNBD8xQomyIp69dKXBtIrna3zFDn0Se9pbsDbEruPsIV-tb39XOES96fUyQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"174\" height=\"137\"></p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcIGg7ByAb4H4WI53Hyvz-jp4SgqLddgsHpykLIN_3-Er-bB-HQsfiMVAeZgBhj8VNa1wyG7Z3mG25KaGFpTuk281bmVLU2U2l7ZM_bL6iMCCXyI1FPzds7oRK13AJNKrsqiDE2rQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"173\" height=\"135\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUtL1qeG6GHjIQ0C0wO6t-NWXOqP9W8pIPtIOcIPBYfQIhbyizfjsd5bwOKckkIBqLYiCvyrJDNfR7i_wXtu0b0sXtwFXf7Kx_cXyVxFWLQ4URyD1CY9wFeMm1bcqFTmEheRRL?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"177\" height=\"136\"><br><strong>इसी प्रकार</strong><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXep62jgHgC3PIktG4iaLFvLc7bnMxep01eJw23qQD4XKG3X2m3ivDqFP2f7CShBAQMM-1DG2ur-7XHVNBD8xQomyIp69dKXBtIrna3zFDn0Se9pbsDbEruPsIV-tb39XOES96fUyQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"182\" height=\"143\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language, &lsquo;BAKE&rsquo; is coded as &lsquo;FDMF&rsquo; and &lsquo;TURN&rsquo; is coded as &lsquo;XXTO&rsquo;. How will &lsquo;MOCK&rsquo; be coded in the given language?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में, &lsquo;BAKE&rsquo; को &lsquo;FDMF&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;TURN&rsquo; को &lsquo;XXTO&rsquo; के रूप में कूटबद्ध किया जाता है। दी गई भाषा में &lsquo;MOCK&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>RRFL</p>", "<p>QREL</p>", 
                                "<p>RQGL</p>", "<p>QSEL</p>"],
                    options_hi: ["<p>RRFL</p>", "<p>QREL</p>",
                                "<p>RQGL</p>", "<p>QSEL</p>"],
                    solution_en: "<p>10.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcMEymigKuDgGSzwSranjy9ayiYFgJByII32CHf-Dh-qly1x9jgB9ke1Tn_jACDuCIXWfecUlbZ96LgdXw_NklkTlXJNJZDZylxxyi7ZZqKRWKDD5ON5zOjkWjop8I6nKhkasDztQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"140\" height=\"104\"></p>\n<p><strong>and</strong><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdnYKFXoHytSMbBuG8lVCcTEOa47zzCeU9FQpdz7TvZZ6K-6_pjWPE2lovLsb6JKHWT2cJ8Kp84JRGXDgahvF7CUvcmSqF-a2rJFztegCyUH5HQgFTA4gQq4xSu43zbkjUEKOQALw?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"135\" height=\"100\"></p>\n<p><strong id=\"docs-internal-guid-59fc2ac8-7fff-858e-53ed-642b6f192098\">Similarly</strong><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcTFvRZgGVjUwVg1AT8K48a7jr2elPZCTNwDZmo5w0ukmL5Fzl6jwz2KkO1VXzXnEO2a_e98FTYFKM_n3uCpxWY_9Yhi2o-SvRHksH3zfEq-msERoCfUKcjrLT_aRi0GjczCLiJqA?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"143\" height=\"110\"></p>",
                    solution_hi: "<p>10.(b)<br><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcMEymigKuDgGSzwSranjy9ayiYFgJByII32CHf-Dh-qly1x9jgB9ke1Tn_jACDuCIXWfecUlbZ96LgdXw_NklkTlXJNJZDZylxxyi7ZZqKRWKDD5ON5zOjkWjop8I6nKhkasDztQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"140\" height=\"104\"></p>\n<p><strong>और</strong><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdnYKFXoHytSMbBuG8lVCcTEOa47zzCeU9FQpdz7TvZZ6K-6_pjWPE2lovLsb6JKHWT2cJ8Kp84JRGXDgahvF7CUvcmSqF-a2rJFztegCyUH5HQgFTA4gQq4xSu43zbkjUEKOQALw?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"135\" height=\"100\"></p>\n<p><strong>इसी प्रकार</strong></p>\n<p><strong id=\"docs-internal-guid-4c1d7ea9-7fff-c58a-b544-284f2688efb4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcTFvRZgGVjUwVg1AT8K48a7jr2elPZCTNwDZmo5w0ukmL5Fzl6jwz2KkO1VXzXnEO2a_e98FTYFKM_n3uCpxWY_9Yhi2o-SvRHksH3zfEq-msERoCfUKcjrLT_aRi0GjczCLiJqA?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"143\" height=\"110\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. In a certain code language, ‘FLAKE’ is written as ‘&2348’ and ‘LAKES’ is written as ‘23@84’. How will ‘S’ be written in that language?",
                    question_hi: "11. एक निश्चित कूट भाषा में, ‘FLAKE’ को ‘&2348’ के रूप में लिखा जाता है और ‘LAKES’ को ‘23@84’ के रूप में लिखा जाता है। उसी भाषा में ‘S’ को किस प्रकार लिखा जाएगा?",
                    options_en: [" 4", " &", 
                                " 3", " @"],
                    options_hi: [" 4", " &",
                                " 3", " @"],
                    solution_en: "11.(d)<br />F L A K E <math display=\"inline\"><mo>→</mo></math> & 2 3 4 8 <br />L A K E S <math display=\"inline\"><mo>→</mo></math> 2 3 @ 8 4 <br />From the above codes the code for ‘S’ is ‘@’.",
                    solution_hi: "11.(d)<br />F L A K E <math display=\"inline\"><mo>→</mo></math> & 2 3 4 8 <br />L A K E S <math display=\"inline\"><mo>→</mo></math> 2 3 @ 8 4 <br />उपरोक्त कोड से ‘S’ का कोड ‘@’ है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In a certain code language, \'WJSL\' is coded as \'25-12-21-14\' and \'DUOH\' is coded as \'6-23-17-10\'. What is the code for \'PFKR\' in the given language?</p>",
                    question_hi: "<p>12. एक निश्चित कूट भाषा में, &lsquo;WJSL&rsquo; को &lsquo;25-12-21-14&rsquo; लिखा जाता है और &lsquo;DUOH&rsquo; को &lsquo;6-23-17-10&rsquo; लिखा जाता है। उसी कूट भाषा में &lsquo;PFKR&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>20-10-14-21</p>", "<p>20-9-12-20</p>", 
                                "<p>19-9-12-19</p>", "<p>18-8-13-20</p>"],
                    options_hi: ["<p>20-10-14-21</p>", "<p>20-9-12-20</p>",
                                "<p>19-9-12-19</p>", "<p>18-8-13-20</p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100566232.png\" alt=\"rId26\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100566349.png\" alt=\"rId27\"></p>\n<p><strong>similarly</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100566463.png\" alt=\"rId28\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100566571.png\" alt=\"rId29\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100566694.png\" alt=\"rId30\"></p>\n<p><strong>इसी प्रकार</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100566820.png\" alt=\"rId31\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. In a certain code language, ‘cat dog monkey’ is coded as ‘55 66 88’; ‘camel cat donkey’ is coded as ‘66 99 33’ and ‘dog goat tiger’ is coded as ‘88 11 22’. How is ‘monkey’ coded in that language?",
                    question_hi: "13.  एक निश्चित कूट भाषा में \'cat dog monkey\' को \'55 66 88\' के रूप में कूटबद्ध किया गया है; ‘camel cat donkey’ को \'66 99 33\' के रूप में कूटबद्ध किया गया है और ‘dog goat tiger’ को \'88 11 22\' के रूप में कूटबद्ध किया गया है। उसी भाषा में \'monkey\' को किस प्रकार कूटबद्ध किया गया है?",
                    options_en: [" 11 ", " 66 ", 
                                " 55 ", " 88"],
                    options_hi: [" 11 ", " 66 ",
                                " 55 ", " 88"],
                    solution_en: "13.(c)<br />cat dog monkey <math display=\"inline\"><mo>→</mo></math> 55 66 88 <br />camel cat donkey <math display=\"inline\"><mo>→</mo></math> 66  99  33 <br />dog goat tiger <math display=\"inline\"><mo>→</mo></math> 88 11 22<br />From above coding the code for ‘monkey’ is 55.",
                    solution_hi: "13.(c)<br />cat dog monkey <math display=\"inline\"><mo>→</mo></math> 55 66 88 <br />camel cat donkey <math display=\"inline\"><mo>→</mo></math> 66  99  33 <br />dog goat tiger <math display=\"inline\"><mo>→</mo></math> 88 11 22<br />उपरोक्त कोड से  ‘monkey’ का कोड 55 है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, \'PUPIL\' is written as \'162116912\' and \'PURGE&rsquo; is written as \'16211875\'. How will \'PURSE\' be written in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'PUPIL\' को \'162116912\' लिखा जाता है और &lsquo;PURGE&rsquo; को \'16211875\' लिखा जाता है। इसी कूट भाषा में \'PURSE\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>126118195</p>", "<p>162118159</p>", 
                                "<p>162111895</p>", "<p>162118195</p>"],
                    options_hi: ["<p>126118195</p>", "<p>162118159</p>",
                                "<p>162111895</p>", "<p>162118195</p>"],
                    solution_en: "<p>14.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGLK-vMtqqYWuIEk1kRQamDIHLzax-iD3vNXWInDdExZy7TioF3DdpZNtZJFA601-wR4HsO6WrjQ-VBJfNh0IUR0uDwrBw0MXIfcO3mj79kGPiQAtbKsNW7jMXGEY_Sbu_1KVJRg?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"162\" height=\"109\"></p>\n<p><strong>and</strong><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_9RfkuEXTYvuqZbWxDpQS6Bf6z_6wvB7NFUukt6-q5ZUL2LIu8IB_JG3wakS5XY-dV7rhZ2aR4Rnzax-Pt007E3_asDsKwYsNrVRM3BwpTKggdxTIs4Y3nMTdvolVqSP2NOW6KQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"172\" height=\"114\"><br><strong>Similarly</strong><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfgKwArpGDB7105FB-queDY35TSxaguDVka3Yt8yvfvcdCqlB-vfNlUe2K-yltnP_TaqRJtO-4qL5HlB6wU93lhBFk8PKq-xMg_B396TVrFkSc7EWINJ2jJmMhkkVreQkcZqdlx?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"167\" height=\"101\"></p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGLK-vMtqqYWuIEk1kRQamDIHLzax-iD3vNXWInDdExZy7TioF3DdpZNtZJFA601-wR4HsO6WrjQ-VBJfNh0IUR0uDwrBw0MXIfcO3mj79kGPiQAtbKsNW7jMXGEY_Sbu_1KVJRg?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"162\" height=\"109\"></p>\n<p><strong id=\"docs-internal-guid-8cb8dde2-7fff-96aa-2ab6-6e5fd438ae03\">और </strong><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_9RfkuEXTYvuqZbWxDpQS6Bf6z_6wvB7NFUukt6-q5ZUL2LIu8IB_JG3wakS5XY-dV7rhZ2aR4Rnzax-Pt007E3_asDsKwYsNrVRM3BwpTKggdxTIs4Y3nMTdvolVqSP2NOW6KQ?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"172\" height=\"114\"><br><strong>इसी प्रकार,</strong> <br><br><strong id=\"docs-internal-guid-2cb16730-7fff-ce08-bff0-13dfd9117247\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfgKwArpGDB7105FB-queDY35TSxaguDVka3Yt8yvfvcdCqlB-vfNlUe2K-yltnP_TaqRJtO-4qL5HlB6wU93lhBFk8PKq-xMg_B396TVrFkSc7EWINJ2jJmMhkkVreQkcZqdlx?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"174\" height=\"106\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In a certain code language, &lsquo;IFF&rsquo; is coded as &lsquo;21&rsquo; and &lsquo;ARB&rsquo; is coded as &lsquo;21&rsquo;. How will &lsquo;CAW&rsquo; be coded in that language?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में, \'IFF\' को \'21\' के रूप में कूटबद्ध किया जाता है और \'ARB\' को \'21\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'CAW\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>25</p>", "<p>27</p>", 
                                "<p>23</p>", "<p>26</p>"],
                    options_hi: ["<p>25</p>", "<p>27</p>",
                                "<p>23</p>", "<p>26</p>"],
                    solution_en: "<p>15.(b)<br><strong>Logic :- </strong>Sum of place value in alphabetical series<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100567520.png\" alt=\"rId35\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100567619.png\" alt=\"rId36\"><br>Similarly <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdOgPqLqLdkDjhUl0x4qDL3L3mZxRwOlrUn4ppwK4N0eMX9KzLR-aas18cX6h9P4hS81MKFGPmQ5b5esY3OWj9QU2vt7zkbQbVZqeAxlQ9nvmKPsVhs_URDmCAKr_aVYR1hYq9i?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"223\" height=\"95\"></p>",
                    solution_hi: "<p>15.(b)<br><strong>तर्क :- </strong>वर्णमाला क्रम में स्थानीय मान का योग<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100567520.png\" alt=\"rId35\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100567619.png\" alt=\"rId36\"><br>उसी प्रकार <br><strong id=\"docs-internal-guid-90c2ed5a-7fff-1f1e-8c9c-f10cea31d0e3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdOgPqLqLdkDjhUl0x4qDL3L3mZxRwOlrUn4ppwK4N0eMX9KzLR-aas18cX6h9P4hS81MKFGPmQ5b5esY3OWj9QU2vt7zkbQbVZqeAxlQ9nvmKPsVhs_URDmCAKr_aVYR1hYq9i?key=B7z1KlRastMFs2AgqPps1E-X\" width=\"223\" height=\"95\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>