<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that represents the letters which, when sequentially placed from left to right in the blanks below, will complete the letter series. <br>f h _ h _ g f _ h g _ h _ f f _ g _ h _ f</p>",
                    question_hi: "<p>1. उस विकल्प का चयन कीजिए, जो उन अक्षरों को दर्शाता है, जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ क्रमिक रूप से रखने पर अक्षर शृंखला पूरी होगी। <br>f h _ h _ g f _ h g _ h _ f f _ g _ h _ f</p>",
                    options_en: ["<p>g h f h g f f g</p>", "<p>g h f h g h f g</p>", 
                                "<p>g h f f g h f g</p>", "<p>g h f h g h h g</p>"],
                    options_hi: ["<p>g h f h g f f g</p>", "<p>g h f h g h f g</p>",
                                "<p>g h f f g h f g</p>", "<p>g h f h g h h g</p>"],
                    solution_en: "<p>1.(d)<br>f h <span style=\"text-decoration: underline;\"><strong>g </strong></span>h <strong><span style=\"text-decoration: underline;\">h </span></strong>g f/ <span style=\"text-decoration: underline;\"><strong>f </strong></span>h g <strong><span style=\"text-decoration: underline;\">h </span></strong>h <span style=\"text-decoration: underline;\"><strong>g </strong></span>f/ f <span style=\"text-decoration: underline;\"><strong>h</strong></span> g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f</p>",
                    solution_hi: "<p>1.(d)<br>f h <span style=\"text-decoration: underline;\"><strong>g </strong></span>h <strong><span style=\"text-decoration: underline;\">h </span></strong>g f/ <span style=\"text-decoration: underline;\"><strong>f </strong></span>h g <strong><span style=\"text-decoration: underline;\">h </span></strong>h <span style=\"text-decoration: underline;\"><strong>g </strong></span>f/ f <span style=\"text-decoration: underline;\"><strong>h</strong></span> g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Which of the following terms will replace the question mark (?) in the given series?<br>RLYV, PJWT, NHUR, ? , JDQN</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?<br>RLYV, PJWT, NHUR, ? , JDQN</p>",
                    options_en: ["<p>LFTP</p>", "<p>LGTP</p>", 
                                "<p>LGSP</p>", "<p>LFSP</p>"],
                    options_hi: ["<p>LFTP</p>", "<p>LGTP</p>",
                                "<p>LGSP</p>", "<p>LFSP</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889332878.png\" alt=\"rId4\" width=\"380\" height=\"88\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889332878.png\" alt=\"rId4\" width=\"380\" height=\"88\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, <br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;, <br>&lsquo;A - B&rsquo; means &lsquo;A is the father of B&rsquo;,<br>&lsquo;A $ B&rsquo; means &lsquo;A is the son of B&rsquo;, <br>&lsquo;A % B&rsquo; means &lsquo;A is the sister of B&rsquo;, <br>&lsquo;A / B&rsquo; means &lsquo;A is the wife of B&rsquo; and <br>&lsquo;A * B&rsquo; means &lsquo;A is the husband of B&rsquo;.<br>Which of the following means P is the Son&rsquo;s wife of T? <br>i. T / B &ndash; K * P + O <br>ii. T + K * P + B % O<br>iii. K * P + B % T + P<br>iv. P + T * K + B % O</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में,<br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माँ है&rsquo;, <br>&lsquo;A &ndash; B&rsquo; का अर्थ है &lsquo;A, B का पिता है&rsquo;, <br>&lsquo;A $ B&rsquo; का अर्थ है &lsquo;A, B का पुत्र है&rsquo;, <br>&lsquo;A % B&rsquo; का अर्थ है &lsquo;A, B की बहन है&rsquo;, <br>&lsquo;A / B&rsquo; का अर्थ है &lsquo;A, B की पत्नी है&rsquo; और <br>&lsquo;A * B&rsquo; का अर्थ है &lsquo;A, B का पति है&rsquo;। <br>निम्नलिखित में से किसका अर्थ है कि P, T की पुत्रवधू है ?<br>i. T / B &ndash; K * P + O <br>ii. T + K * P + B % O<br>iii. K * P + B % T + P<br>iv. P + T * K + B % O</p>",
                    options_en: ["<p>Only i</p>", "<p>Both i and ii</p>", 
                                "<p>iii and iv</p>", "<p>Only ii</p>"],
                    options_hi: ["<p>केवल i</p>", "<p>i और ii दोनों</p>",
                                "<p>iii और iv</p>", "<p>केवल ii</p>"],
                    solution_en: "<p>3.(b)<br>After going through all the options satisfy only (i) and (ii).<br><br>(i)&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889333095.png\" alt=\"rId5\" width=\"113\" height=\"175\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii)&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889333251.png\" alt=\"rId6\" width=\"126\" height=\"179\"></p>",
                    solution_hi: "<p>3.(b)<br>सभी विकल्पों पर विचार करने के बाद केवल (i) और (ii) को संतुष्ट करते है।.<br><br>(i)&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889333095.png\" alt=\"rId5\" width=\"113\" height=\"175\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii)&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889333251.png\" alt=\"rId6\" width=\"126\" height=\"179\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the triad in which the numbers are related to each other in the same way as the numbers in the following triads. <br>16 - 40 - 100<br>8 - 20 - 50<br>(NOTE: Operation should be performed on the whole numbers, Without breaking down the numbers into its constituent digits. E.g. 13- Operation on 13 such as adding/multiplying itc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>4. उस त्रिक का चयन करें जिसमें दी गई संख्याएं एक - दूसरे से उसी प्रकार संबंधित हैं, जिस प्रकार निम्&zwj;नलिखित त्रिकों की संख्याएं एक - दूसरे से संबंधित हैं। <br>16 - 40 - 100<br>8 - 20 - 50<br>नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>12-30-120</p>", "<p>18-50-110</p>", 
                                "<p>22-74-185</p>", "<p>24-60-150</p>"],
                    options_hi: ["<p>12-30-120</p>", "<p>18-50-110</p>",
                                "<p>22-74-185</p>", "<p>24-60-150</p>"],
                    solution_en: "<p>4.(d) <strong>Logic</strong>: 1st number &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">u</mi><mi mathvariant=\"bold-italic\">m</mi><mi mathvariant=\"bold-italic\">b</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">r</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2nd number, 2nd number &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>n</mi><mi>d</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi></mrow><mn>3</mn></mfrac></math> = 3rd number<br>16 - 40 - 100 :- 16 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <strong>= 40, 40 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math>&nbsp;= 100</strong><br>8 - 20 - 50 :- 8 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 20, 20 &times; 2 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>2</mn></mfrac></math> = 50<br>Similarly<br>24-60-150 :- 24 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 60, 60 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>2</mn></mfrac></math>&nbsp;= 150</p>",
                    solution_hi: "<p>4.(d) <strong>तर्क:</strong> पहला नंबर &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi><mo>&#160;</mo></mrow><mn>2</mn></mfrac></math> = दूसरा नंबर, दूसरा नंबर &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi><mo>&#160;</mo></mrow><mn>3</mn></mfrac></math>&nbsp;= तीसरा नंबर<br>16 - 40 - 100 :- 16 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <strong>= 40, 40 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math>&nbsp;= 100</strong><br>8 - 20 - 50 :- 8 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 20, 20 &times; 2 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>2</mn></mfrac></math> = 50<br>उसी प्रकार<br>24-60-150 :- 24 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 60, 60 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>2</mn></mfrac></math> = 150</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Six symbols %, &amp;, ?, +, $ and @ are written on different faces of a dice. Two positions of this dice are sown in the figure below. Find the symbol on the face opposite to the one having @.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889333432.png\" alt=\"rId7\" width=\"182\" height=\"89\"></p>",
                    question_hi: "<p>5. एक पासे के विभिन्न फलकों पर छह प्रतीक %, &amp;, ?, +, $ और @ अंकित हैं। नीचे दी गई आकृति में इस पासे की दो स्थितियाँ दर्शाई गई हैं। @ वाले फलक के विपरीत फलक पर प्रतीक ज्ञात कीजिए । <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889333432.png\" alt=\"rId7\" width=\"182\" height=\"89\"></p>",
                    options_en: ["<p>$</p>", "<p>&amp;</p>", 
                                "<p>%</p>", "<p>?</p>"],
                    options_hi: ["<p>$</p>", "<p>&amp;</p>",
                                "<p>%</p>", "<p>?</p>"],
                    solution_en: "<p>5.(d) From both the dice the opposite face are <br>&amp; <math display=\"inline\"><mo>&#8596;</mo></math> $ , % &harr; + , ? &harr; @</p>",
                    solution_hi: "<p>5.(d) दोनों पासों के विपरीत फलक हैं <br>&amp; <math display=\"inline\"><mo>&#8596;</mo></math> $ , % &harr; + , ? &harr; @</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Read the given statements and conclusions carefully. Assuming that the information given in the information give in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements. <br><strong>Statements:</strong> <br>Some Toys are Plastic.<br>Some Plastic are Metal. <br>No Metal is Chemical. <br><strong>Conclusions:</strong> <br>(I) Some Chemicals are Plastic. <br>(II) No Toy is a Chemical.</p>",
                    question_hi: "<p>6. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन कीजिए। कथनों में दी गई जानकारी को सत्य मानते हुए, भले ही यह सर्वज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्धारित कीजिए कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तर्कसंगत रूप से अनुसरण करता है/करते हैं। <br><strong>कथन :</strong> <br>कुछ खिलौने, प्लास्टिक हैं। <br>कुछ प्लास्टिक, धातु हैं। <br>कोई धातु, रसायन नहीं है। <br><strong>निष्कर्ष :</strong> <br>(I) कुछ रसायन, प्लास्टिक हैं। <br>(II) कोई खिलौना, रसायन नहीं है।</p>",
                    options_en: ["<p>Both conclusions (I) and (II) follow.</p>", "<p>Only conclusion (II) follows.</p>", 
                                "<p>Neither conclusion (I) nor (II) follows.</p>", "<p>Only conclusion (I) follows.</p>"],
                    options_hi: ["<p>निष्कर्ष (I) और (II) दोनों का अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                                "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889333655.png\" alt=\"rId8\" width=\"366\" height=\"59\"><br>Neither conclusion (I) nor (II) follows.</p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334239.png\" alt=\"rId9\" width=\"361\" height=\"60\"><br>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the triad in which the numbers are related to each other in the same way as are the numbers of the given triads. <br>(148, 126, 104), (98, 76, 54) <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>7. उस त्रिक का चयन कीजिए, जिसमें संख्याएँ एक-दूसरे से उसी तरह संबंधित हैं, जैसे दिए गए त्रिकों की संख्याएँ हैं। <br>(148, 126, 104), (98, 76, 54) <br>(ध्यान दीजिए: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रिया जैसे जोड़ना / हटाना / गुणा करना आदि 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>(157, 135, 113)</p>", "<p>(149, 137, 115)</p>", 
                                "<p>(128, 104, 82)</p>", "<p>(134, 102, 90)</p>"],
                    options_hi: ["<p>(157, 135, 113)</p>", "<p>(149, 137, 115)</p>",
                                "<p>(128, 104, 82)</p>", "<p>(134, 102, 90)</p>"],
                    solution_en: "<p>7.(a) <strong>Logic :</strong> 1st number - 2nd number = 2nd number - third number = 22<br>(148, 126, 104) :- 148 - 126 = 126 - 104 = 22<br>(98, 76, 54) :- 98 - 76 = 76 - 54 = 22<br>Similarly<br>(157, 135, 113) :- 157 - 135 = 135 - 113 = 22</p>",
                    solution_hi: "<p>7.(a) <strong>तर्क :</strong> पहली संख्या - दूसरी संख्या = दूसरी संख्या - तीसरी संख्या = 22<br>(148, 126, 104) :- 148 - 126 = 126 - 104 = 22<br>(98, 76, 54) :- 98 - 76 = 76 - 54 = 22<br>इसी प्रकार, <br>(157, 135, 113) :- 157 - 135 = 135 - 113 = 22</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. The sequence of folding a paper and the manner in which the folded paper is cut is shown in the following figures. How would this paper look when unfolded? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334507.png\" alt=\"rId10\" width=\"258\" height=\"134\"></p>",
                    question_hi: "<p>8. निम्नलिखित आकृतियों में एक कागज़ को मोड़ने का क्रम और मुड़े हुए कागज़ को काटने का तरीका दर्शाया गया है। खोले जाने पर यह कागज़ कैसा दिखाई देगा?</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334507.png\" alt=\"rId10\" width=\"258\" height=\"134\"></p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334671.png\" alt=\"rId11\" width=\"88\" height=\"150\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334815.png\" alt=\"rId12\" width=\"96\" height=\"163\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334965.png\" alt=\"rId13\" width=\"105\" height=\"179\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889335108.png\" alt=\"rId14\" width=\"106\" height=\"181\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334671.png\" alt=\"rId11\" width=\"98\" height=\"167\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334815.png\" alt=\"rId12\" width=\"87\" height=\"148\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889334965.png\" alt=\"rId13\" width=\"100\" height=\"171\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889335108.png\" alt=\"rId14\" width=\"89\" height=\"152\"></p>"],
                    solution_en: "<p>8.(b) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889335335.png\" alt=\"rId15\" width=\"98\" height=\"167\"></p>",
                    solution_hi: "<p>8.(b) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889335335.png\" alt=\"rId15\" width=\"94\" height=\"160\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;SIGHTED&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>9. यदि शब्द \'SIGHTED\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा?</p>",
                    options_en: ["<p>None</p>", "<p>One</p>", 
                                "<p>Three</p>", "<p>Two</p>"],
                    options_hi: ["<p>कोई नहीं</p>", "<p>एक</p>",
                                "<p>तीन</p>", "<p>दो</p>"],
                    solution_en: "<p>9.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889335463.png\" alt=\"rId16\" width=\"200\" height=\"101\"><br>The position of two letters will be unchanged.</p>",
                    solution_hi: "<p>9.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889335463.png\" alt=\"rId16\" width=\"200\" height=\"101\"><br>दो अक्षरों की स्थिति अपरिवर्तित रहेगी.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Read the given statements and conclusions carefully. You have to take the given statements to be true if they seem to be at variance from commonly known facts. You have to decide which conclusion(s) logically follow(s) from the given statements. <br><strong>Statements:</strong> <br>All fruit are sour. <br>Some sour are vegetables. <br>Some vegetables are green. <br><strong>Conclusions :</strong><br>(I) All vegetables are sour.<br>(II) Some sour are green.</p>",
                    question_hi: "<p>10. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। आपको दिए गए कथनों को सत्य मानना है, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों। आपको यह तय करना है कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं। <br><strong>कथन :&nbsp;</strong><br>सभी फल, खट्टे हैं। <br>कुछ खट्टे, सब्जियाँ हैं। <br>कुछ सब्जियाँ, हरी हैं। <br><strong>निष्कर्ष :</strong><br>(I) सभी सब्जियाँ, खट्टे हैं। <br>(II) कुछ खट्टे, हरे हैं।</p>",
                    options_en: ["<p>Only conclusion (II) follows</p>", "<p>Neither conclusion (I) nor (II) follows</p>", 
                                "<p>Only conclusion (I) follows</p>", "<p>Both conclusions (I) and (II) follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (II) अनुसरण करता हैं</p>", "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष (I) अनुसरण करता है</p>", "<p>निष्कर्ष (I) और (II) दोनों अनुसरण करते हैं</p>"],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889335663.png\" alt=\"rId17\" width=\"275\" height=\"71\"><br>Neither conclusion (I) nor (II) follows</p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889335989.png\" alt=\"rId18\" width=\"275\" height=\"74\"><br>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What should come in place of the question mark (?) in the given series ?<br>9 17 33 65 129 ?</p>",
                    question_hi: "<p>11. दी गई शृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए?<br>9 17 33 65 129 ?</p>",
                    options_en: ["<p>259</p>", "<p>254</p>", 
                                "<p>257</p>", "<p>258</p>"],
                    options_hi: ["<p>259</p>", "<p>254</p>",
                                "<p>257</p>", "<p>258</p>"],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336310.png\" alt=\"rId19\" width=\"392\" height=\"76\"></p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336310.png\" alt=\"rId19\" width=\"392\" height=\"76\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the figure from among the given options that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336432.png\" alt=\"rId20\" width=\"415\" height=\"79\"></p>",
                    question_hi: "<p>12. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्नचिह्न (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336432.png\" alt=\"rId20\" width=\"415\" height=\"79\"></p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336554.png\" alt=\"rId21\" width=\"108\" height=\"110\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336670.png\" alt=\"rId22\" width=\"105\" height=\"107\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336771.png\" alt=\"rId23\" width=\"107\" height=\"109\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336873.png\" alt=\"rId24\" width=\"106\" height=\"108\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336554.png\" alt=\"rId21\" width=\"108\" height=\"110\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336670.png\" alt=\"rId22\" width=\"105\" height=\"107\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336771.png\" alt=\"rId23\" width=\"107\" height=\"109\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336873.png\" alt=\"rId24\" width=\"106\" height=\"108\"></p>"],
                    solution_en: "<p>12.(b) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336670.png\" alt=\"rId22\" width=\"105\" height=\"107\"></p>",
                    solution_hi: "<p>12.(b) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889336670.png\" alt=\"rId22\" width=\"105\" height=\"107\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337075.png\" alt=\"rId25\" width=\"377\" height=\"100\"></p>",
                    question_hi: "<p>13. एक कागज को नीचे दिए गए चित्र के अनुसार मोड़ा और काटा जाता है। यह कागज खोलने पर कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337075.png\" alt=\"rId25\" width=\"369\" height=\"98\"></p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337232.png\" alt=\"rId26\" width=\"91\" height=\"82\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337394.png\" alt=\"rId27\" width=\"88\" height=\"80\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337574.png\" alt=\"rId28\" width=\"98\" height=\"89\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337695.png\" alt=\"rId29\" width=\"93\" height=\"84\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337232.png\" alt=\"rId26\" width=\"91\" height=\"82\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337394.png\" alt=\"rId27\" width=\"88\" height=\"80\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337574.png\" alt=\"rId28\" width=\"98\" height=\"89\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337695.png\" alt=\"rId29\" width=\"93\" height=\"84\"></p>"],
                    solution_en: "<p>13.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337232.png\" alt=\"rId26\" width=\"100\" height=\"91\"></p>",
                    solution_hi: "<p>13.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889337232.png\" alt=\"rId26\" width=\"100\" height=\"91\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, &lsquo;RETAINED&rsquo; is written as &lsquo;ENIATERD&rsquo; and &lsquo;MATURITY&rsquo; is written as &lsquo;TIRUTAMY&rsquo;. How will &lsquo;FIREWALL&rsquo; be written in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'RETAINED\' को \'ENIATERD\' के रूप में लिखा जाता है और \'MATURITY\' को \'TIRUTAMY\' के रूप में लिखा जाता है। उसी भाषा में &lsquo;FIREWALL&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>LAWEFIFR</p>", "<p>LAWERIFL</p>", 
                                "<p>FLLAWERI</p>", "<p>FLIREWAL</p>"],
                    options_hi: ["<p>LAWEFIFR</p>", "<p>LAWERIFL</p>",
                                "<p>FLLAWERI</p>", "<p>FLIREWAL</p>"],
                    solution_en: "<p>14.(b)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889338045.png\" alt=\"rId30\" width=\"184\" height=\"90\">&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889338232.png\" alt=\"rId31\" width=\"185\" height=\"90\"><br>Similarly,&nbsp;<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889338434.png\" alt=\"rId32\" width=\"188\" height=\"92\"></p>",
                    solution_hi: "<p>14.(b)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889338045.png\" alt=\"rId30\" width=\"184\" height=\"90\">&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889338232.png\" alt=\"rId31\" width=\"185\" height=\"90\"><br>इसी प्रकार,<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889338434.png\" alt=\"rId32\" width=\"188\" height=\"92\"><br><br><br><br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. In a certain code language, \'GLOBAL\' is written as \'221714272817\', \'FOREST\' is written as \'23141124109\', how will \'IMPACT\' be written in that language?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में, \'GLOBAL\' को \'221714272817\' लिखा जाता है, \'FOREST\' को \'23141124109\' लिखा जाता है, उसी कूट भाषा में \'IMPACT\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>20161327259</p>", "<p>20161328269</p>", 
                                "<p>18141126247</p>", "<p>20161226259</p>"],
                    options_hi: ["<p>20161327259</p>", "<p>20161328269</p>",
                                "<p>18141126247</p>", "<p>20161226259</p>"],
                    solution_en: "<p>15.(b)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889338633.png\" alt=\"rId33\" width=\"129\" height=\"148\">&nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889338827.png\" alt=\"rId34\" width=\"129\" height=\"152\"></p>\n<p>Similarly,&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889339034.png\" alt=\"rId35\" width=\"130\" height=\"153\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889339289.png\" alt=\"rId36\" width=\"129\" height=\"152\">&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889339566.png\" alt=\"rId37\" width=\"137\" height=\"164\"></p>\n<p>इसी प्रकार,&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889339996.png\" alt=\"rId38\" width=\"133\" height=\"160\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Three of the following four number-pairs are alike in a certain way and thus form a group. Which number-pair does NOT belong to that group? <br>(NOTE: The relation should be found without breaking down the numbers into its constituent digits)</p>",
                    question_hi: "<p>16. निम्नलिखित चार संख्या-युग्मों में से तीन एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा संख्या-युग्म उस समूह से संबंधित नहीं है? <br>(नोट: संख्याओं को उसके घटक अंकों में विभाजित किए बिना संबंध ज्ञात किया जाना चाहिए</p>",
                    options_en: ["<p>83, 97</p>", "<p>73, 79</p>", 
                                "<p>61,71</p>", "<p>31, 41</p>"],
                    options_hi: ["<p>83, 97</p>", "<p>73, 79</p>",
                                "<p>61,71</p>", "<p>31, 41</p>"],
                    solution_en: "<p>16.(b)<strong> Logic: </strong>There is a prime number between both the prime numbers but in option (b) the prime numbers are consecutive.</p>",
                    solution_hi: "<p>16.(b) <strong>तर्क:</strong> दोनों अभाज्य संख्याओं के बीच एक अभाज्य संख्या है लेकिन विकल्प (b) में अभाज्य संख्याएँ क्रमागत हैं</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged? <br>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?</p>",
                    question_hi: "<p>17. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?</p>",
                    options_en: ["<p>2758</p>", "<p>2268</p>", 
                                "<p>2785</p>", "<p>2578</p>"],
                    options_hi: ["<p>2758</p>", "<p>2268</p>",
                                "<p>2785</p>", "<p>2578</p>"],
                    solution_en: "<p>17.(a) Given: 107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?<br>As per the instructions after interchanging the symbol &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; we get.<br>107 - 2028 &divide; 6 + 427 &times; 7 = ?<br>107 - 338 + 2989<br>3096 - 338 = 2758</p>",
                    solution_hi: "<p>17.(a) Given: दिया गया है: 107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?<br>निर्देशों के अनुसार प्रतीक \'&times;\' और \'&divide;\' तथा \'+\' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है।<br>107 - 2028 &divide; 6 + 427 &times; 7 = ?<br>107 - 338 + 2989<br>3096 - 338 = 2758</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Based on the position in the English alphabetical order, three of the following letter-clusters are alike in some manner and one is different. Select the odd letter-cluster.<br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>18. अंग्रेजी वर्णमाला क्रम में स्थिति के आधार पर, निम्नलिखित में से तीन अक्षर-समूह किसी न किसी रूप में संगत हैं और एक असंगत है। असंगत अक्षर-समूह का चयन करें। <br>(नोट: असंगत, अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।).</p>",
                    options_en: ["<p>SVXZ</p>", "<p>EHJK</p>", 
                                "<p>YBDE</p>", "<p>TWYZ</p>"],
                    options_hi: ["<p>SVXZ</p>", "<p>EHJK</p>",
                                "<p>YBDE</p>", "<p>TWYZ</p>"],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340130.png\" alt=\"rId39\" width=\"144\" height=\"65\"> &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340270.png\" alt=\"rId40\" width=\"136\" height=\"62\"> &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340420.png\" alt=\"rId41\" width=\"153\" height=\"70\"><br><br>but<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340673.png\" alt=\"rId42\" width=\"152\" height=\"70\"></p>",
                    solution_hi: "<p>18.(a)<br>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340130.png\" alt=\"rId39\" width=\"144\" height=\"65\"> &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340270.png\" alt=\"rId40\" width=\"136\" height=\"62\"> &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340420.png\" alt=\"rId41\" width=\"153\" height=\"70\"><br><br>लेकिन<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340673.png\" alt=\"rId42\" width=\"152\" height=\"70\"><br><br><br><br><br></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. &lsquo;Vacant&rsquo; is related to &lsquo;Empty&rsquo; in the same way as &lsquo;Crowd&rsquo; is related to &lsquo;________&rsquo;.</p>",
                    question_hi: "<p>19. \'रिक्त\' का संबंध \'खाली\' से उसी प्रकार है, जैसे \'भीड़\' का संबंध \'______\' से है।</p>",
                    options_en: ["<p>Throes</p>", "<p>Loner</p>", 
                                "<p>Throng</p>", "<p>Disband</p>"],
                    options_hi: ["<p>कष्ट</p>", "<p>अकेला</p>",
                                "<p>जमघट</p>", "<p>उखड़ना</p>"],
                    solution_en: "<p>19.(c) &lsquo;Vacant&rsquo; and &lsquo;Empty&rsquo; are synonyms, Similarly, &lsquo;Crowd&rsquo; and &lsquo;Throng&rsquo; are synonyms.</p>",
                    solution_hi: "<p>19.(c)<br>&lsquo;रिक्त&rsquo; और &lsquo;खाली&rsquo; पर्यायवाची शब्द हैं, इसी प्रकार, &lsquo;भीड़&rsquo; और&rsquo;जमघट&rsquo; पर्यायवाची शब्द हैं.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term. <br>READER : ERZWRE :: SERIAL : ESIRLA :: JINGLE : ?</p>",
                    question_hi: "<p>20. उस विकल्प का चयन करें जो पांचवें पद से उसी प्रकार संबंधित है जिस प्रकार दूसरा पद, पहले पद से संबंधित है और चौथा पद, तीसरे पद से संबंधित है।<br>READER : ERZWRE :: SERIAL : ESIRLA :: JINGLE : ?</p>",
                    options_en: ["<p>IJGNEL</p>", "<p>JIMTEL</p>", 
                                "<p>JIGNEL</p>", "<p>IJMTEL</p>"],
                    options_hi: ["<p>IJGNEL</p>", "<p>JIMTEL</p>",
                                "<p>JIGNEL</p>", "<p>IJMTEL</p>"],
                    solution_en: "<p>20.(d)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340946.png\" alt=\"rId43\" width=\"179\" height=\"99\">&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889341247.png\" alt=\"rId44\" width=\"187\" height=\"105\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889341637.png\" alt=\"rId45\" width=\"197\" height=\"112\"></p>",
                    solution_hi: "<p>20.(d)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889340946.png\" alt=\"rId43\" width=\"179\" height=\"99\">&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889341247.png\" alt=\"rId44\" width=\"187\" height=\"105\"><br>उसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889341637.png\" alt=\"rId45\" width=\"197\" height=\"112\"><br><br><br><br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. What is the number of triangles in the following figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889341921.png\" alt=\"rId46\" width=\"118\" height=\"129\"></p>",
                    question_hi: "<p>21. निम्न आकृति में त्रिभुजों की संख्या कितनी है? <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889341921.png\" alt=\"rId46\" width=\"118\" height=\"129\"></p>",
                    options_en: ["<p>9</p>", "<p>8</p>", 
                                "<p>6</p>", "<p>7</p>"],
                    options_hi: ["<p>9</p>", "<p>8</p>",
                                "<p>6</p>", "<p>7</p>"],
                    solution_en: "<p>21.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342136.png\" alt=\"rId47\" width=\"134\" height=\"129\"><br>ABC, ADE, AHF, AHG, AHK, AKG, FHG, JIK<br>There are 8 triangles.</p>",
                    solution_hi: "<p>21.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342136.png\" alt=\"rId47\" width=\"134\" height=\"129\"><br>ABC, ADE, AHF, AHG, AHK, AKG, FHG, JIK<br>8 त्रिभुज हैं.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342399.png\" alt=\"rId48\" width=\"123\" height=\"105\"></p>",
                    question_hi: "<p>22. उस विकल्प आकृति का चयन कीजिए , जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342399.png\" alt=\"rId48\" width=\"113\" height=\"97\"></p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342517.png\" alt=\"rId49\" width=\"102\" height=\"89\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342700.png\" alt=\"rId50\" width=\"108\" height=\"94\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342840.png\" alt=\"rId51\" width=\"101\" height=\"89\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343025.png\" alt=\"rId52\" width=\"112\" height=\"98\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342517.png\" alt=\"rId49\" width=\"102\" height=\"89\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342700.png\" alt=\"rId50\" width=\"108\" height=\"94\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342840.png\" alt=\"rId51\" width=\"101\" height=\"89\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343025.png\" alt=\"rId52\" width=\"112\" height=\"98\"></p>"],
                    solution_en: "<p>22.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342517.png\" alt=\"rId49\" width=\"116\" height=\"101\"></p>",
                    solution_hi: "<p>22.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889342517.png\" alt=\"rId49\" width=\"116\" height=\"101\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option that is related to the third figure in the same way as the second figure is related to the first figure. Follow the analogy-<br>Figure 1 : Figure 2 :: Figure 3 : ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343261.png\" alt=\"rId53\" width=\"351\" height=\"83\"></p>",
                    question_hi: "<p>23. उस विकल्प का चयन कीजिए जो तीसरी आकृति से उसी प्रकार संबंधित है जिस प्रकार दूसरी आकृति पहली आकृति से संबंधित है। सादृश्य का पालन कीजिए-<br>आकृति 1 : आकृति 2 :: आकृति 3 : ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889344167.png\" alt=\"rId58\" width=\"391\" height=\"94\"></p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343502.png\" alt=\"rId54\" width=\"123\" height=\"88\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343606.png\" alt=\"rId55\" width=\"132\" height=\"94\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343723.png\" alt=\"rId56\" width=\"137\" height=\"98\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343872.png\" alt=\"rId57\" width=\"142\" height=\"101\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343502.png\" alt=\"rId54\" width=\"135\" height=\"96\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343606.png\" alt=\"rId55\" width=\"136\" height=\"97\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343723.png\" alt=\"rId56\" width=\"131\" height=\"93\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343872.png\" alt=\"rId57\" width=\"132\" height=\"94\"></p>"],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343606.png\" alt=\"rId55\" width=\"128\" height=\"91\"></p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889343606.png\" alt=\"rId55\" width=\"140\" height=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. What will come in place of the question mark (?) in the following equation, if &lsquo;&times;&rsquo; is replaced by &lsquo;&divide;&rsquo;, &lsquo;&divide;&rsquo; is replaced by &lsquo;+&rsquo;, &lsquo;+&rsquo; is replaced by &lsquo;&times;&rsquo; and &lsquo;&minus;&rsquo; remains unchanged? <br>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?</p>",
                    question_hi: "<p>24. यदि \'&times;\' को \'&divide;\' से बदल दिया जाए, \'&divide;\' को \'+\' से बदल दिया जाए, \'+\' को \'&times;\' से बदल दिया जाए और \'&minus;\' अपरिवर्तित रहे, तो निम्नलिखित समीकरण में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आएगा? <br>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?</p>",
                    options_en: ["<p>23</p>", "<p>33</p>", 
                                "<p>29</p>", "<p>30</p>"],
                    options_hi: ["<p>23</p>", "<p>33</p>",
                                "<p>29</p>", "<p>30</p>"],
                    solution_en: "<p>24.(b) Given: 42 &times; 3 &divide; 4 + 9 &minus; 17 = ?<br>As per the instructions after interchanging the symbol , we get.<br>42 &divide; 3 + 4 &times; 9 &minus; 17 = ? <br>14 + 4 &times; 9 - 17 <br>14 + 36 - 17<br>50 - 17 = 33</p>",
                    solution_hi: "<p>24.(b) दिया गया है: 42 &times; 3 &divide; 4 + 9 &minus; 17 = ?<br>निर्देशों के अनुसार, चिन्हों को बदलने के बाद हमें प्राप्त होता है।<br>42 &divide; 3 + 4 &times; 9 &minus; 17 = ? <br>14 + 4 &times; 9 - 17 <br>14 + 36 - 17<br>50 - 17 = 33</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. 16 is related to 30 following a certain logic. Following the same logic, 81 is related to 110. To which of the following is 49 related, following the same logic? (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>25. एक निश्चित तर्क के अनुसार 16 का संबंध 30 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 81 का संबंध 110 से है। उसी तर्क का अनुसरण करते हुए, 49 का संबंध निम्नलिखित में से किससे है? <br>नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- - 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>64</p>", "<p>60</p>", 
                                "<p>72</p>", "<p>90</p>"],
                    options_hi: ["<p>64</p>", "<p>60</p>",
                                "<p>72</p>", "<p>90</p>"],
                    solution_en: "<p>25.(c) <strong>Logic</strong>: (<math display=\"inline\"><msqrt><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></msqrt></math> + 1) &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></msqrt></math> + 2) = 2ndno.<br>(16&nbsp;: 30) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> + 2) &rArr; 5 &times; 6 = 30<br>(81 : 110) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> + 2) &rArr; 10 &times; 11 = 110<br>Similarly<br>(49 : <math display=\"inline\"><mi>x</mi></math>) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 2) &rArr; 8 &times; 9 = 72</p>",
                    solution_hi: "<p>25.(c) <strong>तर्क </strong>: (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math> + 1) &times; (पहली संख्या + 2) = दूसरी संख्या&nbsp;<br>(16&nbsp;: 30) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> + 2) &rArr; 5 &times; 6 = 30<br>(81 : 110) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> + 2) &rArr; 10 &times; 11 = 110<br>उसी प्रकार<br>(49 : <math display=\"inline\"><mi>x</mi></math>) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 2) &rArr; 8 &times; 9 = 72<br><br><br></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>