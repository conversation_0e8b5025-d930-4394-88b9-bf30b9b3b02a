<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>RATE : SCWI : : HIGH : IKJL : : OPEN : ?</p>",
                    question_hi: "<p>1. उस विकल्प का चयन करें जो पांचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से और चौथा अक्षर- समूह तीसरे अक्षर-समूह से संबंधित है।<br>RATE : SCWI : : HIGH : IKJL : : OPEN : ?</p>",
                    options_en: ["<p>PRIS</p>", "<p>PQHR</p>", 
                                "<p>PSIR</p>", "<p>PRHR</p>"],
                    options_hi: ["<p>PRIS</p>", "<p>PQHR</p>",
                                "<p>PSIR</p>", "<p>PRHR</p>"],
                    solution_en: "<p>1.(d)<br>,<br><strong id=\"docs-internal-guid-3d33fb92-7fff-df5a-5571-91632ccba37c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf3wi6fOhZI3NNw5YPWSw3lRiou4og0d4MV-zYV1wzxvFkDuWmJUkEVbD6uoQki0p38el8pl56FiCzSDKJtOO6Xc8Tt-loMUndS9Gbv4gDZFzLxIHygGUCXw5G2aPlVNVdxsVwGYA?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"162\" height=\"108\">,<strong id=\"docs-internal-guid-904463dc-7fff-f20d-63f1-cbac9a10d6e1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeb3TUm8VaNrkOQdCW4Z2fvqrINQLOJOfvUYMkOk12WngojWXJnQl7qxrwgr3aSA9G92Hz9VPgfm8aq-CTxhEWWTZLnq8w0VuwLBt4ch72PeKMLUzL9iReQTg9I19i9s5REwqowcw?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"160\" height=\"109\"></strong></strong><br><strong>Similarly</strong><br><strong id=\"docs-internal-guid-03d9a30b-7fff-2048-4053-6f98184dc351\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcZniNqNTrJoyfU2gNGC_ac-DWr7WxlfbMQaSlB7F4I3rHjpQivIx4NDNi-j-QsaJBrd_bW8JlBPhDQNaGP_wyzdJkYFOHnkRY4xGI93nE3k5jrl6QSWFA5KNgdiDx1NidLWF5B?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"162\" height=\"120\"></strong></p>",
                    solution_hi: "<p>1.(d)<br>,<br><strong id=\"docs-internal-guid-9fd7c8d2-7fff-4617-d7ed-923fb433c35d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf3wi6fOhZI3NNw5YPWSw3lRiou4og0d4MV-zYV1wzxvFkDuWmJUkEVbD6uoQki0p38el8pl56FiCzSDKJtOO6Xc8Tt-loMUndS9Gbv4gDZFzLxIHygGUCXw5G2aPlVNVdxsVwGYA?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"162\" height=\"108\">,<strong id=\"docs-internal-guid-d1bc92fd-7fff-5ba9-0380-ecbec0f7ac74\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeb3TUm8VaNrkOQdCW4Z2fvqrINQLOJOfvUYMkOk12WngojWXJnQl7qxrwgr3aSA9G92Hz9VPgfm8aq-CTxhEWWTZLnq8w0VuwLBt4ch72PeKMLUzL9iReQTg9I19i9s5REwqowcw?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"160\" height=\"109\"></strong></strong><br><strong>इसी प्रकार</strong><br><strong id=\"docs-internal-guid-bd52a5fc-7fff-6e32-fed1-6d44089f7858\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcZniNqNTrJoyfU2gNGC_ac-DWr7WxlfbMQaSlB7F4I3rHjpQivIx4NDNi-j-QsaJBrd_bW8JlBPhDQNaGP_wyzdJkYFOHnkRY4xGI93nE3k5jrl6QSWFA5KNgdiDx1NidLWF5B?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"162\" height=\"120\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. 8 is related to 512 following a certain logic. Following the same logic, 12 is related to 1728. To which of the following is 15 related, following the same logic?<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>2. एक निश्चित तर्क का अनुसरण करते हुए 8, 512 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 12, 1728 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 15 निम्नलिखित में से किससे संबंधित है?<br>(<strong>नोट </strong>: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>2275</p>", "<p>2725</p>", 
                                "<p>3375</p>", "<p>3735</p>"],
                    options_hi: ["<p>2275</p>", "<p>2725</p>",
                                "<p>3375</p>", "<p>3735</p>"],
                    solution_en: "<p>2.(c) <strong>Logic</strong>: (1st number)<sup>3</sup> = 2nd number<br>8 : 512 :- (8)<sup>3</sup> = 512<br>12 : 1728 :- (12)<sup>3</sup> = 1728<br>Similarly<br>15 : <math display=\"inline\"><mi>x</mi></math> :- (15)<sup>3</sup> = 3375</p>",
                    solution_hi: "<p>2.(c)&nbsp; <strong>तर्क</strong>: (पहली संख्या)<sup>3</sup> = दूसरी संख्या<br>8 : 512 :- (8)<sup>3</sup> = 512<br>12 : 1728 :- (12)<sup>3</sup> = 1728<br>इसी प्रकार<br>15 : <math display=\"inline\"><mi>x</mi></math> :- (15)<sup>3</sup> = 3375</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "3. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br />Acrimony : Humanity :: Wield : ?",
                    question_hi: "3. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को अर्थपूर्ण अंग्रेजी शब्दों के रूप में माना जाना चाहिए और शब्द आपस में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br />कटुता : मानवता :: संभालना : ?",
                    options_en: [" Exert ", " Forgo ", 
                                " Use", " Apply"],
                    options_hi: [" खींचना", " त्यागना",
                                " निग्रह", " नियुक्त करना "],
                    solution_en: "3.(b)<br />Acrimony\" contrasts with ‘Humanity’ where one is negative and the other is positive.<br />Similarly, ‘Wield’  means to use something, whereas ‘Forgo’ means to do without or abstain from something, forming an opposite relationship.",
                    solution_hi: "3.(b)<br />कटुता\'\' मानवता\' के विपरीत है जहां एक नकारात्मक है और दूसरा सकारात्मक है।<br />इसी तरह, संभालना का अर्थ है किसी चीज़ का उपयोग करना, जबकि ‘त्यागना’ का अर्थ है किसी चीज़ के बिना करना या उसे दूर रहना, जो एकदूसरे के विपरीत संबंध बनाते है.।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the triad in which the numbers are related to each other in the same way as the numbers in the following triads.<br>8-17-35<br>23-47-95<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>4. उस त्रिक का चयन करें जिसमें दी गई संख्याएं एक - दूसरे से उसी प्रकार संबंधित हैं, जिस प्रकार निम्&zwj;नलिखित त्रिकों की संख्याएं एक - दूसरे से संबंधित हैं।<br>8-17-35<br>23-47-95<br><strong>नोट </strong>: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>9-20-41</p>", "<p>12-25-51</p>", 
                                "<p>13-29-55</p>", "<p>16-32-62</p>"],
                    options_hi: ["<p>9-20-41</p>", "<p>12-25-51</p>",
                                "<p>13-29-55</p>", "<p>16-32-62</p>"],
                    solution_en: "<p>4.(b) <strong>Logic </strong>:- (2nd number - 1st number)&times;4 - 1 = 3rd number<br>(8 - 17 - 35) :- (17 - 8)&times;4 - 1 &rArr; (9) &times; 4 - 1 = 35<br>(23 - 47 - 95) :- (47 - 23)&times;4 - 1 &rArr; (24) &times; 4 - 1 = 95<br>Similarly,<br>(12 -25 - 51) :- (25 - 12)&times;4 - 1 &rArr; (13) &times; 4 - 1= 51</p>",
                    solution_hi: "<p>4.(b) <strong>तर्क</strong>:- (दूसरी संख्या - पहली संख्या)&times;4 - 1 = तीसरी संख्या<br>(8 - 17 - 35) :- (17 - 8)&times;4 - 1 &rArr; (9) &times; 4 - 1 = 35<br>(23 - 47 - 95) :- (47 - 23)&times;4 - 1 &rArr; (24) &times; 4 - 1 = 95<br>इसी प्रकार,<br>(12 -25 - 51) :- (25 - 12)&times;4 - 1 &rArr; (13) &times; 4 - 1= 51</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>(34, 15, 64)<br>(29, 17, 63)<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>5. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं।<br>(34, 15, 64)<br>(29, 17, 63)<br>(<strong>नोट </strong>: पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(44, 32, 98)</p>", "<p>(31, 23, 54)</p>", 
                                "<p>(25, 14, 53)</p>", "<p>(24, 28, 76)</p>"],
                    options_hi: ["<p>(44, 32, 98)</p>", "<p>(31, 23, 54)</p>",
                                "<p>(25, 14, 53)</p>", "<p>(24, 28, 76)</p>"],
                    solution_en: "<p>5.(c) <strong>Logic </strong>:- (3rd number - 1st number)<math display=\"inline\"><mo>&#247;</mo></math>2 = 2nd number<br>(34, 15, 64) :- (64 - 34)<math display=\"inline\"><mo>&#247;</mo></math> 2 &rArr; (30) &divide; 2 = 15<br>(29, 17, 63) :- (63 - 29)<math display=\"inline\"><mo>&#247;</mo></math> 2 &rArr; (34)&divide;2 = 17<br>Similarly,<br>(25, 14, 53) :- (53 - 25)<math display=\"inline\"><mo>&#247;</mo></math> 2 &rArr; (28) &divide; 2 = 14</p>",
                    solution_hi: "<p>5.(c) <strong>तर्क</strong>:- (तीसरी संख्या - पहली संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 2 = दूसरी संख्या<br>(34, 15, 64) :- (64 - 34)<math display=\"inline\"><mo>&#247;</mo></math> 2 &rArr; (30) &divide; 2 = 15<br>(29, 17, 63) :- (63 - 29)<math display=\"inline\"><mo>&#247;</mo></math> 2 &rArr; (34)&divide;2 = 17<br>इसी प्रकार,<br>(25, 14, 53) :- (53 - 25)<math display=\"inline\"><mo>&#247;</mo></math> 2 &rArr; (28) &divide; 2 = 14</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/ deleting/ multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>186 : 103<br>156 : 73</p>",
                    question_hi: "<p>6. उस विकल्&zwj;प का चयन कीजिए जिसमें संख्&zwj;याओं के मध्&zwj;य वही संबंध है जो नीचे दिए गए युग्&zwj;म की संख्&zwj;याओं के मध्&zwj;य है।<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/ घटाना/ गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>186 : 103<br>156 : 73</p>",
                    options_en: ["<p>83 : 1</p>", "<p>208 : 127</p>", 
                                "<p>119 : 35</p>", "<p>95 : 12</p>"],
                    options_hi: ["<p>83 : 1</p>", "<p>208 : 127</p>",
                                "<p>119 : 35</p>", "<p>95 : 12</p>"],
                    solution_en: "<p>6.(d) <strong>Logic </strong>:- (1st number - 2nd number ) = 83<br>(186 : 103) :- (186 - 103) = 83<br>(156 : 73) :- (156 - 73) = 83<br>Similarly,<br>(95 : 12) :- (95 - 12) = 83</p>",
                    solution_hi: "<p>6.(d) <strong>तर्क </strong>:- (पहली संख्या - दूसरी संख्या) = 83<br>(186 : 103) :- (186 - 103) = 83<br>(156 : 73) :- (156 - 73) = 83<br>इसी प्रकार,<br>(95 : 12) :- (95 - 12) = 83</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "7. ‘Lavish’ is related to ‘Austere’ in the same way as ‘Prejudiced’ is related to ‘________’.<br />(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)",
                    question_hi: "7. \'अतिव्‍ययी\' (Lavish), \'मिताहारी\' (Austere) से उसी प्रकार संबंधित है, जिस प्रकार \'पक्षपातपूर्ण\' (Prejudiced) \' ________\' से संबंधित है।<br />(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्‍दों को अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)",
                    options_en: [" Impartial", " Compromised", 
                                " Thoughtful", " Careful"],
                    options_hi: [" निष्‍पक्ष (Impartial)", " समझौताकारी (Compromised)",
                                " विचारपूर्ण (Thoughtful)", " सावधान (Careful)"],
                    solution_en: "7.(a) As ‘Lavish’ and ‘Austere’ are antonyms of each other similarly, ‘Prejudiced’ and ‘Impartial’ are antonyms of each other.",
                    solution_hi: "7.(a) जिस प्रकार \'अतिव्‍ययी\' (Lavish)\' और \'मिताहारी\' (Austere) एक दूसरे के विपरीतार्थक हैं उसी प्रकार \'पक्षपातपूर्ण\' (Prejudiced)  और निष्‍पक्ष (Impartial) एक दूसरे के विपरीतार्थक हैं।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option that is related to the fifth term in the same way as the second term is related to the first term and fourth term is related to third term.<br>TRAINER : VTCIQHU :: RADIANT : TCFIDQW : : CUSHION : ?</p>",
                    question_hi: "<p>8. उस विकल्प का चयन कीजिए जो पांचवें पद से उसी प्रकार संबंधित है जिस प्रकार दूसरा पद पहले पद से और चौथा पद तीसरे पद से संबंधित है।<br>TRAINER : VTCIQHU :: RADIANT : TCFIDQW : : CUSHION : ?</p>",
                    options_en: ["<p>EWUHLRQ</p>", "<p>FWUHLRQ</p>", 
                                "<p>FXUHLRQ</p>", "<p>EXUHLRQ</p>"],
                    options_hi: ["<p>EWUHLRQ</p>", "<p>FWUHLRQ</p>",
                                "<p>FXUHLRQ</p>", "<p>EXUHLRQ</p>"],
                    solution_en: "<p>8.(a)<br>, <br><strong id=\"docs-internal-guid-729b6610-7fff-42d5-11b3-1009d9a0a2c7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcpwAnPTgwuRvlk9z9jBL57vRnc41Z571m0n-qQdX90N91a5BHOL0N__cQ5dVPJmwLeNOZKW3DGaQ_nua-oUu7nsjOfstfVyYxyZQzn4m06-jtjrVKUmJmH1ZUxU_ZY0TWIQ-1q0A?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"235\" height=\"102\"></strong><br><strong id=\"docs-internal-guid-1a6d53e8-7fff-6992-2eac-8d3c8f11741b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcuMaD6iqI68U3dSv61Cp35RcUclVwPtzUc7oudtpP-yqYvZs2mmauPJ-ZChqn0x1Cmnkgdt16JOOeEbwI7AibOjpSlCWFL-44VAbseNHJ-bu5N1ui-K0CjrYg7_d5hoaVSmcyZLQ?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"245\" height=\"105\"></strong><br>Similarly,<br><strong id=\"docs-internal-guid-4651577e-7fff-8e47-e981-b3dfb44177b6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdp2Nn5bgrdygOIXPIYlvUD2_ygROI0HgZ0-Wubh-5XQNXK3mROAb1WRKAzC964vghFdILr06s8vV7aklWn52zI7ISqkHCB0waqkBiJauKRmJbq8GG7GH4PCbswJhRrBzPFVDIhIA?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"208\" height=\"90\"></strong></p>",
                    solution_hi: "<p>8.(a)<br>, <br><strong id=\"docs-internal-guid-de3f27d5-7fff-dad6-201a-2c7bfc418401\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcpwAnPTgwuRvlk9z9jBL57vRnc41Z571m0n-qQdX90N91a5BHOL0N__cQ5dVPJmwLeNOZKW3DGaQ_nua-oUu7nsjOfstfVyYxyZQzn4m06-jtjrVKUmJmH1ZUxU_ZY0TWIQ-1q0A?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"231\" height=\"100\"></strong><br><strong id=\"docs-internal-guid-bcef8d83-7fff-f505-1653-44ca30e96a45\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcuMaD6iqI68U3dSv61Cp35RcUclVwPtzUc7oudtpP-yqYvZs2mmauPJ-ZChqn0x1Cmnkgdt16JOOeEbwI7AibOjpSlCWFL-44VAbseNHJ-bu5N1ui-K0CjrYg7_d5hoaVSmcyZLQ?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"245\" height=\"105\"></strong><br>इसी प्रकार,<br><strong id=\"docs-internal-guid-1689d30c-7fff-1762-bcf6-81d31248d01b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdp2Nn5bgrdygOIXPIYlvUD2_ygROI0HgZ0-Wubh-5XQNXK3mROAb1WRKAzC964vghFdILr06s8vV7aklWn52zI7ISqkHCB0waqkBiJauKRmJbq8GG7GH4PCbswJhRrBzPFVDIhIA?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"208\" height=\"90\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>OUR : NPTVQS : : BIG : ACHJFH : : FOR : ?</p>",
                    question_hi: "<p>9. उस विकल्प का चयन करें जो पांचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है। <br>OUR : NPTVQS : : BIG : ACHJFH : : FOR : ?</p>",
                    options_en: ["<p>EGNPQS</p>", "<p>GENPRS</p>", 
                                "<p>EGNOQR</p>", "<p>GEPNSQ</p>"],
                    options_hi: ["<p>EGNPQS</p>", "<p>GENPRS</p>",
                                "<p>EGNOQR</p>", "<p>GEPNSQ</p>"],
                    solution_en: "<p>9.(a)&nbsp;</p>\n<p><strong id=\"docs-internal-guid-3fd87be2-7fff-5aa0-6e16-2730552191d5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeYxdlchB-RZ1XlNebJhxnGt9p82McpL95bdXoTqvSMFe7YE27kAnP3U78XLwWLtBCWnwoGL5K8lzEUo9LhimeF9HY6LNtafIaa4dPky9nqrybz2gvoOXqQh8JjFURwOjmjkvf0ZA?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"150\" height=\"109\"></strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732088129705.png\" alt=\"rId12\" width=\"158\" height=\"132\"><br>Similarly<br><br><strong id=\"docs-internal-guid-9069e78e-7fff-84d8-74c0-014246f1886f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdPfrtVKY6mOcktNxwU5OydA_kDA0n28oAv2l6keEDyfdcqUOGCIOYemy1lITw6WvABQ-KyW25MJKX_ztW7VsmVC5rI13XeCSuAmTH7CZokcHWAeVWZSQwzCMex5PbR6yOdlv4IAQ?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"158\" height=\"130\"></strong></p>",
                    solution_hi: "<p>9.(a)</p>\n<p><strong id=\"docs-internal-guid-56f47da3-7fff-0444-df51-9ec83cebddfb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeYxdlchB-RZ1XlNebJhxnGt9p82McpL95bdXoTqvSMFe7YE27kAnP3U78XLwWLtBCWnwoGL5K8lzEUo9LhimeF9HY6LNtafIaa4dPky9nqrybz2gvoOXqQh8JjFURwOjmjkvf0ZA?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"150\" height=\"109\"></strong><br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732088129705.png\" alt=\"rId12\" width=\"141\" height=\"118\"><br>इसी प्रकार<br><br><strong id=\"docs-internal-guid-cea3b990-7fff-4919-d593-8e7c79c37b05\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdPfrtVKY6mOcktNxwU5OydA_kDA0n28oAv2l6keEDyfdcqUOGCIOYemy1lITw6WvABQ-KyW25MJKX_ztW7VsmVC5rI13XeCSuAmTH7CZokcHWAeVWZSQwzCMex5PbR6yOdlv4IAQ?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"158\" height=\"130\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. Select the word pair that best represents a similar relationship to the one expressed in the pair of words given below. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br />Calcium : Osteomalacia",
                    question_hi: "10. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्द-युग्म में व्यक्त किए गए संबंध के समान संबंध को सबसे अच्छा दर्शाता है। (शब्दों को अर्थपूर्ण हिंदी शब्द माना जाना चाहिए और इन्‍हें शब्द में अक्षरों की संख्या/व्यंजनों/नोंस्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए) कैल्शियम : अस्‍थिमृदुता",
                    options_en: [" Stomach : Blindness ", " Iron : Anaemia", 
                                " Heart : Cardiovascular ", " Lungs : Tuberculosis"],
                    options_hi: [" आमाशय : दृष्टिहीनता", " आयरन : रक्‍ताल्‍पता",
                                "  हृदय : हृदवाहिनी", " फेफड़े : क्षयरोग"],
                    solution_en: "10.(b)<br />Calcium deficiency can lead to osteomalacia, Similarly, Iron deficiency can lead to anaemia.",
                    solution_hi: "10.(b)<br />कैल्शियम की कमी से अस्‍थिमृदुता (osteomalacia) हो सकता है, इसी प्रकार, आयरन की कमी से रक्‍ताल्‍पता (anaemia) हो सकता है.",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1<br>and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>17 : 253<br>19 : 271</p>",
                    question_hi: "<p>11. उस विकल्&zwj;प का चयन कीजिए जिसमें संख्&zwj;याओं के मध्&zwj;य वही संबंध है जो नीचे दिए गए युग्&zwj;म की संख्&zwj;याओं के मध्&zwj;य है। <br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/ घटाना/ गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>17 : 253<br>19 : 271</p>",
                    options_en: ["<p>21 : 289</p>", "<p>23 : 207</p>", 
                                "<p>31 : 400</p>", "<p>32 : 387</p>"],
                    options_hi: ["<p>21 : 289</p>", "<p>23 : 207</p>",
                                "<p>31 : 400</p>", "<p>32 : 387</p>"],
                    solution_en: "<p>11.(a) <strong>Logic</strong>: (1st number &times; 9) + 100 = 2nd number<br>17 : 253 :- (17 &times; 9) + 100 = 253<br>19 : 271 :- (19 &times; 9) + 100 = 271<br>Similarly<br>21 : 289 :- (21 &times; 9) + 100 = 289</p>",
                    solution_hi: "<p>11.(a) <strong>तर्क</strong>: (पहली संख्या &times; 9) + 100 = दूसरी संख्या<br>17 : 253 :- (17 &times; 9) + 100 = 253<br>19 : 271 :- (19 &times; 9) + 100 = 271<br>उसी प्रकार<br>21 : 289 :- (21 &times; 9) + 100 = 289</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>(66, 57, 48)<br>(94, 85, 76)<br>(<strong>NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1<br>and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>12. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं। <br>(66, 57, 48) <br>(94, 85, 76) <br>(<strong>नोट </strong>: पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(85, 77, 64)</p>", "<p>(81, 72, 63)</p>", 
                                "<p>(98, 90, 83)</p>", "<p>(79, 70, 59)</p>"],
                    options_hi: ["<p>(85, 77, 64)</p>", "<p>(81, 72, 63)</p>",
                                "<p>(98, 90, 83)</p>", "<p>(79, 70, 59)</p>"],
                    solution_en: "<p>12.(b) <strong>Logic</strong>; 1st number - 2nd number = 2nd number - 3rd number = 9<br>(66, 57, 48) :- 66 - 57 = 57 - 48 = 9<br>(94, 85, 76) :- 94 - 85 = 85 -76 = 9<br>similarly,<br>(81, 72, 63) :- 81 -72 = 72 - 63 = 9</p>",
                    solution_hi: "<p>12.(b) <strong>तर्क</strong>; पहला नंबर - दूसरा नंबर = दूसरा नंबर - तीसरा नंबर = 9<br>(66, 57, 48) :- 66 - 57 = 57 - 48 = 9<br>(94, 85, 76) :- 94 - 85 = 85 -76 = 9<br>इसी तरह,<br>(81, 72, 63) :- 81 -72 = 72 - 63 = 9</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets?<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>14 &ndash; 196 &ndash; 186 &ndash; 172; 15 &ndash; 225 &ndash; 215 &ndash; 200</p>",
                    question_hi: "<p>13. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है? <br>(<strong>नोट </strong>: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।)<br>14 &ndash; 196 &ndash; 186 &ndash; 172; 15 &ndash; 225 &ndash; 215 &ndash; 200</p>",
                    options_en: ["<p>9 &ndash; 81 &ndash; 71 &ndash; 62</p>", "<p>3 &ndash; 9 &ndash; 19 &ndash; 14</p>", 
                                "<p>8 &ndash; 64 &ndash; 54 &ndash; 40</p>", "<p>6 &ndash; 36 &ndash; 26 &ndash; 22</p>"],
                    options_hi: ["<p>9 &ndash; 81 &ndash; 71 &ndash; 62</p>", "<p>3 &ndash; 9 &ndash; 19 &ndash; 14</p>",
                                "<p>8 &ndash; 64 &ndash; 54 &ndash; 40</p>", "<p>6 &ndash; 36 &ndash; 26 &ndash; 22</p>"],
                    solution_en: "<p>13.(a) <strong>Logic</strong>: (<math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></math>)<sup>2</sup> = 2<sup>nd</sup>no., 2<sup>nd</sup>no. - 10 = 3<sup>rd</sup>no., 3<sup>rd</sup>no. - 1<sup>st</sup>no. = 4<sup>th</sup> no.<br>14 &ndash; 196 &ndash; 186 &ndash; 172:- (14)<sup>2</sup> = 196, 196 - 10 = 186, 186 - 14 = 172<br>15 &ndash; 225 &ndash; 215 &ndash; 200:- (15)<sup>2</sup> = 225, 225 - 10 = 215, 215 - 15 = 200<br>Similarly<br>9 &ndash; 81 &ndash; 71 &ndash; 62 :- (9)<sup>2</sup> = 81, 81 - 10 = 71, 71 - 9 = 62</p>",
                    solution_hi: "<p>13.(a) <strong>तर्क</strong>:(पहली संख्या)<sup>2</sup> = दूसरी संख्या , दूसरी संख्या - 10 = तीसरी संख्या , तीसरी संख्या - पहली संख्या = चौथी संख्या <br>14 &ndash; 196 &ndash; 186 &ndash; 172:- (14)<sup>2</sup> = 196, 196 - 10 = 186, 186 - 14 = 172<br>15 &ndash; 225 &ndash; 215 &ndash; 200:- (15)<sup>2</sup> = 225, 225 - 10 = 215, 215 - 15 = 200<br>उसी प्रकार<br>9 &ndash; 81 &ndash; 71 &ndash; 62 :- (9)<sup>2</sup> = 81, 81 - 10 = 71, 71 - 9 = 62</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. &lsquo;WISK&rsquo; is related to &lsquo;DRHP&rsquo; in a certain way based on the English alphabetical order. In the same way, &lsquo;LENT&rsquo; is related to &lsquo;OVMG&rsquo;. To which of the following is &lsquo;BANG&rsquo; related, following the same logic?</p>",
                    question_hi: "<p>14. अँग्रेजी वर्णमाला-क्रम के आधार पर &lsquo;WISK&rsquo; एक निश्चित तरीके से &lsquo;DRHP&rsquo; से संबंधित है। उसी तरह &lsquo;LENT&rsquo; का संबंध &lsquo;OVMG&rsquo; से है। उसी तर्क के अनुसार &lsquo;BANG&rsquo; का संबंध निम्नलिखित में से किससे है?</p>",
                    options_en: ["<p>ZYLU</p>", "<p>ZYNS</p>", 
                                "<p>YZNU</p>", "<p>YZMT</p>"],
                    options_hi: ["<p>ZYLU</p>", "<p>ZYNS</p>",
                                "<p>YZNU</p>", "<p>YZMT</p>"],
                    solution_en: "<p>14.(d)<br>,<br><strong id=\"docs-internal-guid-2e6ed2d3-7fff-c92f-0673-a9747f9dcede\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc9_NvqH_AlIcFd6Y85pSXdCC5pm_aPRlxT_UmDNKmXbXC-QHGVmzA5S8DikO7fvfKEQdjx-NAXXV3a181fboCcA-gG1-ggbXmZd1o8EcL2cYwN6wn-Dwx_HP8orp58hwSh_cy8eg?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"112\" height=\"140\"></strong></p>\n<p><strong id=\"docs-internal-guid-3952a54d-7fff-c3df-d78f-f1384e011304\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXemE6fzfs5GUj9FoY8O1okGVyuUfLwCwbSB93ghvIXT6QPVSiozLSlyWTJUYnVeu5HMvb6B4PTu89nktoQUCOkEdSHd0r-PfF9jmclR44VetSXSDPt3xugPZu1EUhA5bP6DG2t12KFNYW5ylwWtQH_BpspB?key=0vL3W_HpnpO3OieR-EWAwQ\" width=\"111\" height=\"135\"></strong><br><br>Similarly,<br><strong id=\"docs-internal-guid-4f5bdfc8-7fff-389a-9c22-cd93b8c141c7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdNTDboSDO-fd9D4Dhn2vS5vubr6NPVDN_MuvBGRuoinEK5lrKr3JgM1QF0Y05xp5D7HbpGqKgjCjBGJDzC37FKYe1sV1U3-qw0ayE4GS5rn7P-sZEmmdraDrdBqiRLJjb2IOwEEw?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"113\" height=\"132\"></strong></p>",
                    solution_hi: "<p>14.(d)<br>, <br><strong id=\"docs-internal-guid-e22d53cd-7fff-99b5-deb1-208b183b375c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXciaOfD3EW7veh1aaQHazG8vlwNThWAj3R4hpIX-eDMtzjZYkycb3CLbaTjiFNmxVQ3lKI2Sp72a2mItXu65n89Hsi8MM-x5BRITXOoQBGgQ-f5OKDh-psviLr4pM8UgiVZbuNl?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"118\" height=\"140\"></strong><br><br><strong id=\"docs-internal-guid-5afd5e75-7fff-d320-512d-c53ee801a5d7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfb9GOGK9-wx2UFllEF_cXP9KgWr7ayCppj5hrR5lOhqDF-PMmi7CS6BWnTH3HrbcAJoMOQJ7kyvjy3PYeHltrrqfZZaCH2h2Sm_i1ZTGYFcAmPt6BUWf2E5M3kwLAORr7F_XtbZA?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"111\" height=\"125\"></strong><br>इसी प्रकार,<br><br><strong id=\"docs-internal-guid-df100ebf-7fff-fc29-ae50-309fcf25a19b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfuz7pHf3BdjO3lP8rDoY5uz1UsT9Lo-gARNxjjD4n6RuVk4oe7A13Od0644kG3MQKjemxa3qWOne19C4t4bzmAdWBhdcHUvycgpInnEIdafMsNbLyt6KzxdZEhE9q0KWfNxCks3Q?key=4fjEFQA2CJCeudBlW8AKqJrD\" width=\"115\" height=\"140\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets?<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>18 &ndash; 36 &ndash; 72 &ndash; 107; 15 &ndash; 30 &ndash; 60&ndash; 95</p>",
                    question_hi: "<p>15. संख्याओं के दो सेट नीचे दिए गए हैं। संख्याओं के प्रत्येक सेट में, पहले नंबर पर कुछ गणितीय संक्रिया(ओं) का परिणाम दूसरे नंबर पर होता है। इसी प्रकार, दूसरे नंबर पर कुछ गणितीय संक्रिया(ओं) का परिणाम तीसरे नंबर पर होता है, और इसी प्रकार आगे भी होता है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए सेटों की तरह समान संक्रियाओं वाले सेट का अनुसरण करता है?<br>(<strong>नोट </strong>: संख्याओं को उनके घटक अंकों में तोड़े बिना, ये संक्रियाएँ संपूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए - 13 पर संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना, की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>18 &ndash; 36 &ndash; 72 &ndash; 107; 15 &ndash; 30 &ndash; 60&ndash; 95</p>",
                    options_en: ["<p>22 &ndash; 44 &ndash; 88 &ndash; 123</p>", "<p>25 &ndash; 50 &ndash; 100 &ndash; 125</p>", 
                                "<p>11 &ndash; 22 &ndash; 66 &ndash; 101</p>", "<p>30 &ndash; 60 &ndash; 90 &ndash; 115</p>"],
                    options_hi: ["<p>22 &ndash; 44 &ndash; 88 &ndash; 123</p>", "<p>25 &ndash; 50 &ndash; 100 &ndash; 125</p>",
                                "<p>11 &ndash; 22 &ndash; 66 &ndash; 101</p>", "<p>30 &ndash; 60 &ndash; 90 &ndash; 115</p>"],
                    solution_en: "<p>15.(a) <strong>Logic </strong>:- (2nd number - 1st number)&times;4 = 3rd number, 3rd number + 35 = 4th number.<br>(18 - 36 - 72 - 107) :- (36 - 18)&times;4 = 72 &rArr; (72) + 35 = 107<br>(15 - 30 - 60 - 95) :- (30 - 15)&times;4 = 60 &rArr; (60) + 35 = 95<br>Similarly,<br>(22 - 44 - 88 - 123) :- (44 - 22)&times;4 = 88 &rArr; (88) + 35 = 123</p>",
                    solution_hi: "<p>15.(a) <strong>तर्क </strong>:- (दूसरी संख्या - पहली संख्या)&times;4 = तीसरी संख्या, तीसरी संख्या + 35 = चौथी संख्या।<br>(18 - 36 - 72 - 107) :- (36 - 18)&times;4 = 72 &rArr; (72) + 35 = 107<br>(15 - 30 - 60 - 95) :- (30 - 15)&times;4 = 60 &rArr; (60) + 35 = 95<br>इसी प्रकार,<br>(22 - 44 - 88 - 123) :- (44 - 22)&times;4 = 88 &rArr; (88) + 35 = 123</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>