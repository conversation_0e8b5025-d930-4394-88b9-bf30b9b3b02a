<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Study the given table and select the most appropriate option to fill in the blanks.<br>Consider the data of sales of products, A, B and C, by three companies, X, Y and Z, in the table given below.<br><strong id=\"docs-internal-guid-0cd7515f-7fff-2d7f-3686-12b3e22fbdc4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdhij2AVcwwNdEFdT7-mWF3Z_sLy9E-RMzHcFASFW0QSs-6v7LQxRhsYyj1gVI4oRFkuXZhtPHukw0Zz9D4wseJkK9u-zMYEzpKH1R8YTKMBH3fhSlJ65NApSfSxvN5SSBh-1EVMA?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"256\" height=\"117\"></strong><br>The highest average sale per company is for the product_________ and the lowest average sale per product is for the company______________.</p>",
                    question_hi: "<p>1. दी गई तालिका का अध्ययन कीजिए और रिक्त स्थान को भरने के लिए सबसे उपयुक्त विकल्प का चयन कीजिए।<br>नीचे दी गई तालिका में तीन कंपनियों, X, Y और Z द्वारा उत्पादों, A, B और C की बिक्री के आँकड़ों पर विचार कीजिए।<br><strong id=\"docs-internal-guid-c98771e4-7fff-6e1b-9a3c-530110f0438e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcmwtj8kQqE9np_O_yuZZvFQbCfFX5G6JX7EwUi4uM1HyiJKoiwtdEQ-xmNolNhBCNdvQfu0ulAjM2Rl36QiwrKKiGlt3LcSxnesOd6SkoMNV0TZ9mG9j4BsnGSapiXKdSvpVYr?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"251\" height=\"115\"></strong><br>प्रति कंपनी उच्चतम औसत बिक्री उत्पाद_________ के लिए है और प्रति उत्पाद न्यूनतम औसत बिक्री कंपनी ______________के लिए है।</p>",
                    options_en: ["<p>B; Z</p>", "<p>B; Y</p>", 
                                "<p>A; Y</p>", "<p>A; X</p>"],
                    options_hi: ["<p>B; Z</p>", "<p>B; Y</p>",
                                "<p>A; Y</p>", "<p>A; X</p>"],
                    solution_en: "<p>1.(b)<br>average sale of the product A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2750</mn><mo>+</mo><mn>2500</mn><mo>+</mo><mn>3500</mn></mrow><mn>3</mn></mfrac></math> = 2916.66<br>Average sale of the product B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3250</mn><mo>+</mo><mn>3000</mn><mo>+</mo><mn>3150</mn></mrow><mn>3</mn></mfrac></math> = 3133.33<br>Average sale of the product C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2250</mn><mo>+</mo><mn>1750</mn><mo>+</mo><mn>2400</mn></mrow><mn>3</mn></mfrac></math> = 2133.33<br>We can clearly see that the product B has the highest selling product.<br>average sale of the company X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2750</mn><mo>+</mo><mn>3250</mn><mo>+</mo><mn>2250</mn></mrow><mn>3</mn></mfrac></math> = 2750<br>Average sale of the company Y = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>+</mo><mn>3000</mn><mo>+</mo><mn>1750</mn></mrow><mn>3</mn></mfrac></math> = 2416.66<br>Average sale of the company Z = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3500</mn><mo>+</mo><mn>3150</mn><mo>+</mo><mn>2400</mn></mrow><mn>3</mn></mfrac></math> = 3016.66<br>We can clearly see that company Y has the lowest sales.</p>",
                    solution_hi: "<p>1.(b)<br>उत्पाद A की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2750</mn><mo>+</mo><mn>2500</mn><mo>+</mo><mn>3500</mn></mrow><mn>3</mn></mfrac></math> = 2916.66<br>उत्पाद B की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3250</mn><mo>+</mo><mn>3000</mn><mo>+</mo><mn>3150</mn></mrow><mn>3</mn></mfrac></math> = 3133.33<br>उत्पाद C की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2250</mn><mo>+</mo><mn>1750</mn><mo>+</mo><mn>2400</mn></mrow><mn>3</mn></mfrac></math> = 2133.33<br>हम स्पष्ट रूप से देख सकते हैं कि उत्पाद B सबसे अधिक बिकने वाला उत्पाद है।<br>कंपनी X की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2750</mn><mo>+</mo><mn>3250</mn><mo>+</mo><mn>2250</mn></mrow><mn>3</mn></mfrac></math> = 2750<br>कंपनी Y की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>+</mo><mn>3000</mn><mo>+</mo><mn>1750</mn></mrow><mn>3</mn></mfrac></math> = 2416.66<br>कंपनी Z की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3500</mn><mo>+</mo><mn>3150</mn><mo>+</mo><mn>2400</mn></mrow><mn>3</mn></mfrac></math> = 3016.66<br>हम स्पष्ट रूप से देख सकते हैं कि कंपनी Y की बिक्री सबसे कम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Arun and Bhaskar run a race of 3 km. First, Arun gives Bhaskar a head start of 400 m and beats him by 30 seconds. While coming back, Arun gives Bhaskar a lead of 2.5 minutes and gets beaten by 500 m. What is the difference between the times in minutes in which Arun and Bhaskar can run the race for one side separately?</p>",
                    question_hi: "<p>2. अरुण और भास्कर ने 3 km की दौड़ लगाई। अरुण भास्कर को 400 m की बढ़त देता है और उसे 30 सेकंड से हरा देता है। वापस आते समय, अरुण भास्कर को 2.5 मिनट की बढ़त देता है और 500 m से हार जाता है। मिनट में, उस समय के बीच का अंतर कितना है, जिसमें अरुण और भास्कर एक तरफ की रेस के लिए अलग-अलग दौड़ लगा सकते हैं?</p>",
                    options_en: ["<p>2 min</p>", "<p>1.5 min</p>", 
                                "<p>2.5 min</p>", "<p>3 min</p>"],
                    options_hi: ["<p>2 मिनट</p>", "<p>1.5 मिनट</p>",
                                "<p>2.5 मिनट</p>", "<p>3 मिनट</p>"],
                    solution_en: "<p>2.(b)<br><strong>Case 1:</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Arun&nbsp; &nbsp;:&nbsp; &nbsp;Bhaskar <br>Distance &rarr; 3000&nbsp; &nbsp;:&nbsp; &nbsp;2600 <br>Time &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; t&nbsp; &nbsp;:&nbsp; &nbsp; t + 0.5 <br>Time taken to cover 3000 m = t minutes<br>For 2500 m = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">t</mi><mn>3000</mn></mfrac></math> &times; 2500 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> minutes<br><strong>Case 2:</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Arun&nbsp; &nbsp;:&nbsp; &nbsp;Bhaskar <br>Distance &rarr; 2500&nbsp; &nbsp;:&nbsp; &nbsp;3000 <br>Time &rarr; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math>&nbsp; &nbsp;:&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> + 2.5&nbsp;<br>Now speed of Bhaskar will be same for both side<br>Hence, from case 1 and case 2<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2600</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3000</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></mstyle><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>15</mn></mrow></mfrac></math><br>65t&nbsp;+ 195 = 90t + 45<br>25t&nbsp;= 150<br>t = 6 minute<br>Time taken by arun to travel 3000 m = 6 minute<br>Time taken by Bhaskar to travel 3000 m = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> + 2.5&nbsp;= 7.5 minute<br>Required difference = 7.5 - 6 = 1.5 minutes</p>",
                    solution_hi: "<p>2.(b)<br><strong>Case 1:</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;अरुण&nbsp; &nbsp;:&nbsp; &nbsp;भास्कर <br>दूरी &rarr;&nbsp; &nbsp; &nbsp; &nbsp;3000&nbsp; &nbsp;:&nbsp; &nbsp;2600 <br>समय &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;t&nbsp; &nbsp; :&nbsp; &nbsp; t + 0.5 <br>अरुण द्वारा 3000 मीटर की दूरी तय करने में लिया गया समय = t&nbsp;मिनट<br>2500 मीटर के लिए = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">t</mi><mn>3000</mn></mfrac></math> &times; 2500 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math>&nbsp;मिनट<br><strong>Case 2:</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; अरुण&nbsp; &nbsp; :&nbsp; &nbsp; भास्कर <br>दूरी &rarr;&nbsp; &nbsp; &nbsp;2500&nbsp; &nbsp; :&nbsp; &nbsp; 3000 <br>समय &rarr;&nbsp; &nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math>&nbsp; &nbsp;:&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> + 2.5&nbsp;<br>अब भास्कर की गति दोनों तरफ के लिए समान होगी<br>अतः ,<strong> Case 1 और Case 2 से</strong><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2600</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3000</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></mstyle><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>15</mn></mrow></mfrac></math><br>65t&nbsp;+ 195 = 90t + 45<br>25t = 150<br>t = 6 मिनट<br>अरुण द्वारा 3000 मीटर की दूरी तय करने में लिया गया समय = 6 मिनट<br>भास्कर को 3000 मीटर की दूरी तय करने में लगा समय = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> + 2.5 = 7.5 मिनट<br>आवश्यक अंतर = 7.5 - 6 = 1.5 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. In ∆ABC, P and Q are the middle points of the sides AB and AC, respectively. R is a point on the segment PQ such that PR : RQ = 1 : 5. If PR = 6 cm, then BC = _________.</p>",
                    question_hi: "<p>3. ∆ABC में, P और Q क्रमशः भुजाओं AB और AC के मध्य बिंदु हैं। रेखाखंड PQ पर एक बिंदु R इस प्रकार हैकि PR : RQ = 1 : 5 है। यदि PR = 6 cm है, तो BC = _________;</p>",
                    options_en: ["<p>72 cm</p>", "<p>68 cm</p>", 
                                "<p>66 cm</p>", "<p>70 cm</p>"],
                    options_hi: ["<p>72 cm</p>", "<p>68 cm</p>",
                                "<p>66 cm</p>", "<p>70 cm</p>"],
                    solution_en: "<p>3.(a)<br><strong id=\"docs-internal-guid-0f3f78ea-7fff-5275-865f-cde46d990196\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeABQ3745SfcOckbqkUg-R0KUixUVtlTXvvbGd5tk-OKQgIT2pZ11FiOMaaTq-i81MwdoyohCA1X-qhS2wuO-KUInNyGuowi6znfgamd46n1arMCJUxIcR8or3Ex30Tl1adKOQ5Cg?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"165\" height=\"131\"></strong><br>According to the question,<br>(PR) 1 unit = 6 cm<br>(PQ) 6 units = 36 cm <br>Hence, BC = 36 &times; 2 = 72 cm&nbsp; &nbsp; &nbsp;(∵ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mn>2</mn></mfrac></math> = PQ)</p>",
                    solution_hi: "<p>3.(a)<br><strong id=\"docs-internal-guid-7c4e660e-7fff-7645-7fcb-e6c9e8049943\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeABQ3745SfcOckbqkUg-R0KUixUVtlTXvvbGd5tk-OKQgIT2pZ11FiOMaaTq-i81MwdoyohCA1X-qhS2wuO-KUInNyGuowi6znfgamd46n1arMCJUxIcR8or3Ex30Tl1adKOQ5Cg?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"165\" height=\"131\"></strong><br>प्रश्न के अनुसार,<br>(PR) 1 इकाई= 6 cm<br>(PQ) 6 इकाई = 36 cm <br>अत:, BC = 36 &times; 2 = 72 cm (∵ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mn>2</mn></mfrac></math>&nbsp;= PQ)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <br><strong id=\"docs-internal-guid-b1725605-7fff-3f7f-3b48-52697a60b332\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdwRO7sDQv4OlwfcvOc6pOqA5I9Nmf06PptcB-w5BeKweypi-RwLM5ioDJaHbjDVtj8VxX_kjSPgl3vDpY_ycdbJZPRy3FLO78_Fd-Xq0xiXeKiiGoe0NzjQyrm1VOabhLj9olrGQ?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"377\" height=\"215\"></strong><br>A company starts constructing buildings for a college. The constructions have 4 stages.<br>1.buildings proposals<br>2. proposals approval<br>3.constructions implemented<br>4.buildings completed<br>The above 3D-bar graph indicates the status of constructions for 4 years 2015 to 2018.<br>The X-axis scale 0,2,4,6,8,10 indicates 0, 20%, 40%, 60%, 80%, 100%.<br>Study the bar graph carefully and answer the following question.<br>Find the total percentage of buildings completed during the total period, 2015 to 2018.</p>",
                    question_hi: "<p>4.<br><strong id=\"docs-internal-guid-723c783d-7fff-a22c-4a36-1c1e58cc5453\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcIc8YBSzKAhXpAnCWAdLTXDdEbEJ6f7VBa4TJnXNKvjenPVs-lU1rzEtRfmuee8qV3ULYmI1ufwS0muDsgOtelJdYV6A-TAG645DvnoRJOiF66ctr3D5p32mFWioPuOPU8aBPWdw?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"376\" height=\"230\"></strong><br>एक कंपनी एक कॉलेज के लिए भवनों का निर्माण शुरू करती है। निर्माण के 4 चरण हैं।<br>1. भवनों के प्रस्ताव&nbsp;2. प्रस्तावों की स्वीकृति 3. निर्माण कार्यान्वित 4. भवन पूर्ण<br>उपरोक्त 3D-दंड आलेख 4 वर्ष, 2015 से 2018 के लिए निर्माण की स्थिति को दर्शाता है।<br>X-अक्ष पर पैमाना 0, 2, 4, 6, 8, 10 क्रमशः 0, 20%, 40%, 60%, 80%, 100% को इंगित करता है।<br>दंड आलेख का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>2015 से 2018 तक की कुल अवधि के दौरान भवनों के पूर्ण होने का कुल प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>50%</p>", "<p>30%</p>", 
                                "<p>40%</p>", "<p>60%</p>"],
                    options_hi: ["<p>50%</p>", "<p>30%</p>",
                                "<p>40%</p>", "<p>60%</p>"],
                    solution_en: "<p>4.(a)<br>total percentage of buildings completed during the total period, 2015 to 2018 is 50%</p>",
                    solution_hi: "<p>4.(a)<br>2015 से 2018 की कुल अवधि के दौरान पूर्ण की गई इमारतों का कुल प्रतिशत 50% है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A can complete a piece of work in 20 days and B can complete the same work in 10 days. With the help of C, working together they completed the work in 5 days. The number of days in which C alone can complete the work is:</p>",
                    question_hi: "<p>5. A किसी काम को 20 दिनों में पूरा कर सकता है और B उसी काम को 10 दिनों में पूरा कर सकता है। C की मदद से, एक साथ मिलकर काम करते हुए उन्होंने 5 दिनों में वह काम पूरा किया। C अकेले उस काम को कितने दिनों में पूरा कर सकता है।</p>",
                    options_en: ["<p>20</p>", "<p>25</p>", 
                                "<p>10</p>", "<p>15</p>"],
                    options_hi: ["<p>20</p>", "<p>25</p>",
                                "<p>10</p>", "<p>15</p>"],
                    solution_en: "<p>5.(a)<br><strong id=\"docs-internal-guid-cae70906-7fff-9834-5515-85b469984e91\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc7bCkQCM2Pup9PxCNfkCsNvVPYhLRO5MpTe2pzODlVyLqawwH1SXlJjvlsOJuFUS6DfQNlSZBmizlM45e9eitJk4XS7HyD4uQkHrNk2zutnlFflfv2htySaRSWNX_F83vG3VB3gQ?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"261\" height=\"134\"></strong><br>Efficiency of C = 4 - (2 + 1) = 1 unit<br>Time taken by C = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 20 days</p>",
                    solution_hi: "<p>5.(a)<br><strong id=\"docs-internal-guid-25de88c6-7fff-cfc0-8044-e38c3bf5d855\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeLHOG-2Ra9rEilVmQW2djm0heuR_xRZi8UTTvcOFekjHKe6z3gpW1tswn3LjU1knO8qghz2S-pKBQ57SGUEOGe2BmqPV_AY0QTqRUrY1b5ack0ECSg9MnGiaSW1nrUws_9_lPOvQ?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"220\" height=\"129\"></strong><br>C की दक्षता = 4 - (2 + 1) = 1 इकाई<br>C द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 20 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The measures of the three angles of a triangle are such that the smallest angle measures 42&deg; less than the greatest angle, while the measure of the remaining angle is 24&deg; more than the measure of the smallest angle. Find the measure of the smallest angle of the triangle.</p>",
                    question_hi: "<p>6. एक त्रिभुज के तीनों कोणों के माप इस प्रकार हैं कि सबसे छोटे कोण का माप, सबसे बड़े कोण के माप से 42&deg; कम है, जबकि शेष कोण का माप, सबसे छोटे कोण के माप से 24&deg; अधिक है। त्रिभुज के सबसे छोटे कोण का माप ज्ञात कीजिए।</p>",
                    options_en: ["<p>40&deg;</p>", "<p>42&deg;</p>", 
                                "<p>36&deg;</p>", "<p>38&deg;</p>"],
                    options_hi: ["<p>40&deg;</p>", "<p>42&deg;</p>",
                                "<p>36&deg;</p>", "<p>38&deg;</p>"],
                    solution_en: "<p>6.(d)<br>Let the measure of smallest and largest angle be x&deg;&nbsp;and y&deg;<br>And the 3rd angle be z&deg;<br>According to the question,<br>x&deg; + 42&deg; = y&deg;&hellip;..(i)<br>x&deg; + 24&deg; = z&deg;&hellip;..(ii)<br>On adding the e.q . (i) and (ii) we get,<br>x&deg; + 42&deg; + x&deg; + 24&deg; = y&deg; + z&deg;<br>2x&deg; + 66&deg; = y&deg; + z&deg;<br>Then, sum of all angle<br>x&deg; + 2x&deg; + 66&deg; = 180&deg;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(∵ x&deg; + y&deg; + z&deg; = 180&deg;)<br>3x&deg; = 180&deg; - 66&deg;<br>x&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>114</mn><mn>3</mn></mfrac></math> = 38&deg;</p>",
                    solution_hi: "<p>6.(d)<br>माना सबसे छोटे और सबसे बड़े कोण का माप x&deg; और y&deg; है<br>और तीसरा कोण z&deg; है<br>प्रश्न के अनुसार,<br>x&deg; + 42&deg; = y&deg;&hellip;..(i)<br>x&deg; + 24&deg; = z&deg;&hellip;..(ii)<br>समीकरण (i) और (ii) जोड़ने पर हम पाते हैं,<br>x&deg; + 42&deg; + x&deg; + 24&deg; = y&deg; + z&deg;<br>2x&deg; + 66&deg; = y&deg; + z&deg;<br>फिर, सभी कोणों का योग<br>x&deg; + 2x&deg; + 66&deg; = 180&deg;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(∵ x&deg; + y&deg; + z&deg; = 180&deg;)<br>3x&deg; = 180&deg; - 66&deg;<br>x&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>114</mn><mn>3</mn></mfrac></math> = 38&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Rahul spends 70% of his income. His income increased by 15% and his expenditure increased by 7.5%. What is the percentage increase in his savings?</p>",
                    question_hi: "<p>7. राहुल अपनी आय का 70% खर्च करता है। उसकी आय में 15% की वृद्धि हुई और उसके व्यय में 7.5% की वृद्धि हुई। उसकी बचत में प्रतिशत वृद्धि कितनी है?</p>",
                    options_en: ["<p>32.5%</p>", "<p>25.5%</p>", 
                                "<p>50%</p>", "<p>30.5%</p>"],
                    options_hi: ["<p>32.5%</p>", "<p>25.5%</p>",
                                "<p>50%</p>", "<p>30.5%</p>"],
                    solution_en: "<p>7.(a) <br>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;before&nbsp; &nbsp; :&nbsp; &nbsp; after<br>Income -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; :&nbsp; &nbsp; 115<br>Expenditure -&nbsp; &nbsp; &nbsp; &nbsp;70&nbsp; &nbsp; :&nbsp; &nbsp; 75.25 <br>Saving -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;30&nbsp; &nbsp; :&nbsp; &nbsp; 39.75<br>required% = <math display=\"inline\"><mfrac><mrow><mn>39</mn><mo>.</mo><mn>75</mn><mo>-</mo><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 32.5%</p>",
                    solution_hi: "<p>7.(a) <br>अनुपात -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;पहले&nbsp; &nbsp; :&nbsp; &nbsp; बाद में<br>आय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; :&nbsp; &nbsp; 115<br>व्यय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;70&nbsp; &nbsp; :&nbsp; &nbsp; 75.25 <br>बचत -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 30&nbsp; &nbsp; :&nbsp; &nbsp; 39.75<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>39</mn><mo>.</mo><mn>75</mn><mo>-</mo><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 32.5%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In right triangle ABC with right angle at C, M is the mid - point of hypotenuse AB. C is joined to M and produced to a point D, such that DM = CM. Point D is joined to B. If CD = 10 cm, and BD = 6 cm, find the value of CM.</p>",
                    question_hi: "<p>8. C पर समकोण वाले समकोण त्रिभुज ABC में, M कर्ण AB का मध्य -बिंदु है। C को M से मिलाया जाता है और बिंदु D तक इस प्रकार बढ़ाया जाता है कि DM = CM हो। बिंदु D को B से मिलाया जाता है। यदि CD = 10 cm, और BD = 6 cm है, तो CM का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>11.4 cm</p>", "<p>5 cm</p>", 
                                "<p>9.5 cm</p>", "<p>8 cm</p>"],
                    options_hi: ["<p>11.4 cm</p>", "<p>5 cm</p>",
                                "<p>9.5 cm</p>", "<p>8 cm</p>"],
                    solution_en: "<p>8.(b)<br><strong id=\"docs-internal-guid-020e1f85-7fff-1c01-6783-b2f566370d2a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXebhCqLqjD7TS9YFKIcUMzXg39MOe1OgQpMeKKVDH-dk5IFIooEiYIMW6vWK7JOCiel8R_RFXDVRgktrWCCJKgR2iu9_i4CrkaZzdqTKjIzZYOMvjX92SY9TbePoZV42wSVLBGXrA?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"169\" height=\"163\"></strong><br>CM = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>2</mn></mfrac></math> = 5 cm</p>",
                    solution_hi: "<p>8.(b)<br><strong id=\"docs-internal-guid-020e1f85-7fff-1c01-6783-b2f566370d2a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXebhCqLqjD7TS9YFKIcUMzXg39MOe1OgQpMeKKVDH-dk5IFIooEiYIMW6vWK7JOCiel8R_RFXDVRgktrWCCJKgR2iu9_i4CrkaZzdqTKjIzZYOMvjX92SY9TbePoZV42wSVLBGXrA?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"169\" height=\"163\"></strong><br>CM = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. In a college, the average weight of 40 boys in a section among 72 students is 33 kg and that of the remaining students is 15 kg. What is the average weight of all the students in the section?</p>",
                    question_hi: "<p>9. एक कॉलेज में, 72 छात्रों के एक समूह में से 40 लड़कों का औसत भार 33 kg है और शेष छात्रों का औसत भार 15 kg है। समूह के सभी छात्रों का औसत भार कितना है?</p>",
                    options_en: ["<p>24 kg</p>", "<p>25 kg</p>", 
                                "<p>22 kg</p>", "<p>18 kg</p>"],
                    options_hi: ["<p>24 kg</p>", "<p>25 kg</p>",
                                "<p>22 kg</p>", "<p>18 kg</p>"],
                    solution_en: "<p>9.(b)<br>Sum of 40 students weight = 40 &times; 33 = 1320 kg<br>Sum of 32 students weight = 32 &times; 15 = 480 kg<br>Average of total students = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1320</mn><mo>+</mo><mn>480</mn></mrow><mn>72</mn></mfrac></math> = 25 kg<br><strong>Short tricks:-</strong> 40 : 32 = 5 : 4<br>Ratio - 5&nbsp; &nbsp; :&nbsp; &nbsp; 4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 33&nbsp; &nbsp; :&nbsp; &nbsp; 15<br>----------------------------<br>Final - 165 : 60<br>Required average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>165</mn><mo>+</mo><mn>60</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> = 25 kg</p>",
                    solution_hi: "<p>9.(b)<br>40 विद्यार्थियों का कुल भार = 40 &times; 33 = 1320 किग्रा<br>32 विद्यार्थियों का कुल भार = 32 &times; 15 = 480 किग्रा<br>कुल विद्यार्थियों का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1320</mn><mo>+</mo><mn>480</mn></mrow><mn>72</mn></mfrac></math> = 25 किग्रा<br><strong>शार्ट ट्रिक्स :</strong> - 40 : 32 = 5 : 4<br>अनुपात- 5&nbsp; &nbsp; :&nbsp; &nbsp; 4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;33&nbsp; &nbsp; :&nbsp; &nbsp; 15<br>----------------------------<br>अंतिम - 165&nbsp; :&nbsp; 60<br>आवश्यक औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>165</mn><mo>+</mo><mn>60</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math>&nbsp;= 25 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Find the area of a rhombus whose diagonals are of lengths 10 cm and 8.2 cm.</p>",
                    question_hi: "<p>10. उस समचतुर्भुज का क्षेत्रफल ज्ञात कीजिए जिसके विकर्णों की लंबाई 10 cm और 8.2 cm है।</p>",
                    options_en: ["<p>40 cm<sup>2</sup></p>", "<p>42 cm<sup>2</sup></p>", 
                                "<p>41 cm<sup>2</sup></p>", "<p>43 cm<sup>2</sup></p>"],
                    options_hi: ["<p>40 cm<sup>2</sup></p>", "<p>42 cm<sup>2</sup></p>",
                                "<p>41 cm<sup>2</sup></p>", "<p>43 cm<sup>2</sup></p>"],
                    solution_en: "<p>10.(c)<br>Area of rhombus = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; d<sub>1</sub> &times; d<sub>2</sub><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 10 &times; 8.2<br>= 41 cm<sup>2</sup></p>",
                    solution_hi: "<p>10.(c)<br>समचतुर्भुज का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; d<sub>1</sub> &times; d<sub>2</sub><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 10 &times; 8.2<br>= 41 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If two numbers are each divided by the same divisor, then the remainders are 6 and 7, respectively. If the sum of the two numbers be divided by the same divisor, then the remainder is 5. The divisor is:</p>",
                    question_hi: "<p>11. यदि दो संख्याओं में से प्रत्येक को एक ही भाजक से विभाजित किया जाता है, तो शेषफल क्रमशः 6 और 7 होते हैं। यदि उन दो संख्याओं के योग को उसी भाजक से विभाजित किया जाए, तो शेषफल 5 प्राप्त होता है। भाजक कितना है?</p>",
                    options_en: ["<p>6</p>", "<p>4</p>", 
                                "<p>13</p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p>4</p>",
                                "<p>13</p>", "<p>8</p>"],
                    solution_en: "<p>11.(d)<br>Let the numbers be a and b and the divisor be d. The remainders when a and b are divided by d are 6 and 7 respectively. Thus,<br>a = dq<sub>1</sub>&nbsp;+ 6<br>b = dq<sub>2</sub>&nbsp;+ 7<br>For their sum<br>a + b = d(q<sub>1</sub>&nbsp;+ q<sub>2</sub>) + 13 &hellip; (i)<br>When a + b is divided by d, the remainder is 5,<br>a + b = dq + 5 &hellip; (ii)<br>Equating (i) and (ii) we get,<br>d(q<sub>1</sub> + q<sub>2</sub>) + 13 = dq + 5<br>d(q<sub>1</sub> + q<sub>2 </sub>- q) = - 8<br>Thus, d must divide 8. Testing values, we find that d = 8 satisfies the condition.<br>Therefore, the divisor is 8</p>",
                    solution_hi: "<p>11.(d)<br>माना संख्याएँ a और b हैं और भाजक d है। जब a और b को d से विभाजित किया जाता है तो शेषफल क्रमशः 6 और 7 होते हैं। इस प्रकार,<br>a = dq<sub>1</sub>&nbsp;+ 6<br>b = dq<sub>2</sub> + 7<br>उनके योग के लिए<br>a + b = d(q<sub>1</sub>&nbsp;+ q<sub>2</sub>) + 13 &hellip; (i)<br>जब a + b को d से विभाजित किया जाता है, तो शेषफल 5 होता है,<br>a + b = dq + 5 &hellip; (ii)<br>(i) और (ii) की तुलना करने पर,<br>d(q<sub>1</sub> + q<sub>2</sub>) + 13 = dq + 5<br>d(q<sub>1</sub> + q<sub>2 </sub>- q) = - 8<br>इस प्रकार, 8, d से विभाजित होगा। मानों का परीक्षण करने पर, हम पाते हैं कि d = 8 शर्त को पूरा करता है।<br>अत: भाजक 8 है ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. What is the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>4</mn></msup><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi><mo>)</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi><mo>)</mo><mo>-</mo><mn>1</mn></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>12. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>4</mn></msup><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi><mo>)</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi><mo>)</mo><mo>-</mo><mn>1</mn></mrow></mfrac></math> का मान क्या है ?</p>",
                    options_en: ["<p>sin<sup>2</sup>2&alpha; + tan<sup>2</sup>2&alpha;</p>", "<p>sec<sup>2</sup>2&alpha; + cot<sup>2</sup>2&alpha;</p>", 
                                "<p>cos<sup>2</sup>2&alpha; cot<sup>2</sup>2&alpha;</p>", "<p>sec<sup>2</sup>2&alpha; tan<sup>2</sup>2&alpha;</p>"],
                    options_hi: ["<p>sin<sup>2</sup>2&alpha; + tan<sup>2</sup>2&alpha;</p>", "<p>sec<sup>2</sup>2&alpha; + cot<sup>2</sup>2&alpha;</p>",
                                "<p>cos<sup>2</sup>2&alpha; cot<sup>2</sup>2&alpha;</p>", "<p>sec<sup>2</sup>2&alpha; tan<sup>2</sup>2&alpha;</p>"],
                    solution_en: "<p>12.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>4</mn></msup><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi><mo>)</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi><mo>)</mo><mo>-</mo><mn>1</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>4</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; (∵ sin(90 - &theta;) = cos&theta;)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>4</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math> - 1)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>)<br>sec<sup>2</sup>2&alpha; tan<sup>2</sup>2&alpha;</p>",
                    solution_hi: "<p>12.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>4</mn></msup><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi><mo>)</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi><mo>)</mo><mo>-</mo><mn>1</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>4</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; (∵ sin(90 - &theta;) = cos&theta;)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>4</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math> - 1)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math>)<br>sec<sup>2</sup>2&alpha; tan<sup>2</sup>2&alpha;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Last year, Ranjan&rsquo;s monthly salary was ₹34,500 and this year his monthly salary is ₹38,640. What is the percentage increase in Ranjan&rsquo;s monthly salary this year over his monthly salary last year?</p>",
                    question_hi: "<p>13. पिछले वर्ष, रंजन का मासिक वेतन ₹34,500 था और इस वर्ष उसका मासिक वेतन ₹38,640 है। इस वर्ष रंजन के मासिक वेतन में, पिछले वर्ष के मासिक वेतन की तुलना में कितने प्रतिशत की वृद्धि हुई है?</p>",
                    options_en: ["<p>15%</p>", "<p>13%</p>", 
                                "<p>12%</p>", "<p>20%</p>"],
                    options_hi: ["<p>15%</p>", "<p>13%</p>",
                                "<p>12%</p>", "<p>20%</p>"],
                    solution_en: "<p>13.(c)<br>Increase % = <math display=\"inline\"><mfrac><mrow><mn>38640</mn><mo>-</mo><mn>34500</mn></mrow><mrow><mn>34500</mn></mrow></mfrac></math> &times; 100 = 12%</p>",
                    solution_hi: "<p>13.(c)<br>वृद्धि % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>38640</mn><mo>-</mo><mn>34500</mn></mrow><mn>34500</mn></mfrac></math> &times; 100 = 12%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The given table shows the number of candidates who have appeared, qualified and been selected in an examination from two states A and B over the years 2001 to 2005.<br><strong id=\"docs-internal-guid-1e186477-7fff-242c-2fbd-7d66d5765b06\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcWQDjnjShVcrwMDQjzYmoy4LCfk8DTtoIYxhK4ATN0PwFZm06gkZ0VCkvzr5IcIyDGlmbPa6bSM60LDQHM9l4YJigH__XKMtfS-wxehHLlHB1XK7ajCLAOzitru3IOnMFYm4kKGg?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"439\" height=\"138\"></strong><br>In the year 2001, the percentage of candidates who were selected over the candidates who applied in State A is:</p>",
                    question_hi: "<p>14. दी गई तालिका वर्ष 2001 से 2005 के दौरान दो राज्यों A और B से एक परीक्षा में उपस्थित, उत्तीर्ण और चयनित होने वाले उम्मीदवारों की संख्या को दर्शाती है।<br><strong id=\"docs-internal-guid-ca844b11-7fff-8494-90e5-5ed292d683c6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcYmvd6-cIkCbgCYW0U7wKIZkhj9ZLFWtopBWVsK2wAgrWThzeVNV15yFjXpCJ8mUnDGsOC6sPEmY_tKm9PbX910dgeoOv9G1upiDOAsC1jbbMO14U2WvbLKkkfBEaeKtKLMp-mtQ?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"399\" height=\"142\"></strong><br>वर्ष 2001 में राज्य A में उपस्थित होने वाले उम्मीदवारों की तुलना में चयनित उम्मीदवारों का प्रतिशत है :</p>",
                    options_en: ["<p>1.175%</p>", "<p>1.185%</p>", 
                                "<p>1.155%</p>", "<p>1.165%</p>"],
                    options_hi: ["<p>1.175%</p>", "<p>1.185%</p>",
                                "<p>1.155%</p>", "<p>1.165%</p>"],
                    solution_en: "<p>14.(a)<br>required% = <math display=\"inline\"><mfrac><mrow><mn>94</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 1.175%</p>",
                    solution_hi: "<p>14.(a)<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>94</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 1.175%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. How much time (in years) will it take for an amount of ₹4,50,000 to yield ₹45,000 as simple interest at a 5% per annum rate of interest?</p>",
                    question_hi: "<p>15. ₹4,50,000 की धनराशि पर 5% वार्षिक ब्याज दर पर साधारण ब्याज के रूप में ₹45,000 प्राप्त करने में कितना समय (वर्षों में) लगेगा?</p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>5</p>", "<p>3</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>5</p>", "<p>3</p>"],
                    solution_en: "<p>15.(b)<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math><br>45000 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>450000</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>t = 2 years</p>",
                    solution_hi: "<p>15.(b)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math><br>45000 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>450000</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>(समय ) t = 2 वर्ष</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. The cost of rice is increased by 25% but its consumption is decreased by 30%. Find the percentage increase or decrease in the expenditure of money.</p>",
                    question_hi: "<p>16. चावल के मूल्य में 25% की वृद्धि की जाती है लेकिन इसकी खपत में 30% की कमी हो जाती है। धन के व्यय में प्रतिशत वृद्धि या कमी ज्ञात कीजिए।</p>",
                    options_en: ["<p>Decrease 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>Decrease 13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", 
                                "<p>Increase 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>Increase 13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% कमी</p>", "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% कमी</p>",
                                "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% वृद्धि</p>", "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% वृद्धि</p>"],
                    solution_en: "<p>16.(a) <br>increase/decrease% = 25 - 30 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>30</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = -12.5% (-ve sine denote decrease)<br>Decrease % = 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% Decrease</p>",
                    solution_hi: "<p>16.(a) <br>वृद्धि/कमी% = 25 - 30 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>30</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math>&nbsp;= -12.5% (- चिह्न कमी को दर्शाता है)<br>कमी % = 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. The radius of a sphere is increased by 25%. What is the increase percent in surface area?</p>",
                    question_hi: "<p>17. एक गोले की त्रिज्या में 25% की वृद्धि की जाती है। पृष्ठीय क्षेत्रफल में वृद्धि प्रतिशत क्या है?</p>",
                    options_en: ["<p>15.55%</p>", "<p>36.45%</p>", 
                                "<p>56.25%</p>", "<p>25.25%</p>"],
                    options_hi: ["<p>15.55%</p>", "<p>36.45%</p>",
                                "<p>56.25%</p>", "<p>25.25%</p>"],
                    solution_en: "<p>17.(c) <br>25% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>TSA of the sphere = 4&pi;r<sup>2</sup><br>Radius -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; :&nbsp; &nbsp; 5<br>TSA of the sphere - (4)<sup>2</sup>&nbsp; &nbsp; :&nbsp; &nbsp; (5)<sup>2</sup> = 16 : 25<br>increase % = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>16</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; 100 = 56.25%</p>",
                    solution_hi: "<p>17.(c) <br>25% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>गोले का पृष्ठीय क्षेत्रफल = 4&pi;r<sup>2</sup><br>त्रिज्या -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp; 5<br>गोले का पृष्ठीय क्षेत्रफल - (4)<sup>2</sup>&nbsp; &nbsp; :&nbsp; &nbsp; (5)<sup>2</sup> = 16 : 25<br>वृद्धि % = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>16</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; 100 = 56.25%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. 10 years ago, a man&rsquo;s age was 5 times of his son&rsquo;s age. 2 years hence, twice his age will be equal to 4 times the age of his son. What is the present age (in years) of the son?</p>",
                    question_hi: "<p>18. 10 वर्ष पहले, एक आदमी की आयु उसके पुत्र की आयु की 5 गुना थी। 2 वर्ष बाद, उसकी आयु का दोगुना उसके पुत्र की आयु के 4 गुना के बराबर होगी। पुत्र की वर्तमान आयु (वर्षों में) क्या है?</p>",
                    options_en: ["<p>14</p>", "<p>16</p>", 
                                "<p>20</p>", "<p>18</p>"],
                    options_hi: ["<p>14</p>", "<p>16</p>",
                                "<p>20</p>", "<p>18</p>"],
                    solution_en: "<p>18.(a) <br>Let the present age of the man and his son be 5x&nbsp;+ 10 and x + 10 <br>According to the question<br>2(5x&nbsp;+ 10 + 2) = (x + 10 + 2)4 <br>(5x&nbsp;+ 12) = (x + 12)2<br>5x&nbsp;- 2x = 24 - 12<br>3x&nbsp;= 12, &rArr;<strong id=\"docs-internal-guid-d37c2773-7fff-2be2-ff74-a3034ff63951\"> </strong>x = 4<br>Hence, the present age of the son = 4 + 10 = 14 years</p>",
                    solution_hi: "<p>18.(a) <br>माना कि आदमी और उसके बेटे की वर्तमान आयु 5x + 10 और x + 10 है <br>प्रश्न के अनुसार<br>2(5x&nbsp;+ 10 + 2) = (x + 10 + 2)4 <br>(5x&nbsp;+ 12) = (x + 12)2<br>5x&nbsp;- 2x = 24 - 12<br>3x&nbsp;= 12, &rArr;<strong id=\"docs-internal-guid-d37c2773-7fff-2be2-ff74-a3034ff63951\"> </strong>x = 4<br>अत: पुत्र की वर्तमान आयु = 4 + 10 = 14 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. The centers of two circles of radii 25 cm and 35 cm are 80 cm apart. What is the ratio of the lengths of the transverse common tangent to the direct common tangent to these circles?</p>",
                    question_hi: "<p>19. 25 cm और 35 cm त्रिज्या वाले दो वृत्तों के केंद्र एक-दूसरे से 80 cm दूरी पर हैं। इन वृत्तों की अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा की लंबाई और सीधी उभयनिष्ठ स्पर्श रेखा की लंबाई का अनुपात क्या है?</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 3</p>", "<p>2 : 3</p>", 
                                "<p>2 : <math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>3<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 2</p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 3</p>", "<p>2 : 3</p>",
                                "<p>2 : <math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>3<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 2</p>"],
                    solution_en: "<p>19.(b) <br><strong>Formula:-</strong><br>Lengths of the transverse common tangent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>+</mo><mi mathvariant=\"normal\">r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>Lengths of the direct common tangent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>-</mo><mi mathvariant=\"normal\">r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>Required ratio = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>35</mn><mo>+</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>35</mn><mo>-</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>60</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>140</mn><mo>&#215;</mo><mn>20</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>90</mn><mo>&#215;</mo><mn>70</mn></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>20</mn><mo>&#215;</mo><mn>20</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>100</mn></msqrt></math><br>= 20<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 30<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math><br>= 2 : 3</p>",
                    solution_hi: "<p>19.(b) <br><strong>सूत्र:-</strong><br>अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा की लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>+</mo><mi mathvariant=\"normal\">r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>सीधी उभयनिष्ठ स्पर्शरेखा की लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>-</mo><mi mathvariant=\"normal\">r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>आवश्यक अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>35</mn><mo>+</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>35</mn><mo>-</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>60</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>140</mn><mo>&#215;</mo><mn>20</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>90</mn><mo>&#215;</mo><mn>70</mn></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>20</mn><mo>&#215;</mo><mn>20</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>100</mn></msqrt></math><br>= 20<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 30<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math><br>= 2 : 3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the production of refrigerators (in thousand) by five different companies A, B, C, D and E during 2004 to 2007.<br><strong id=\"docs-internal-guid-6aa933e9-7fff-34ff-f298-211df33521d0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFSlR8so-0PNQElBrgOigHfY4TPOu-4rR9zzHu3JYAUx_h6NMB6qdqflouqnxlD-RwI8wJUQotVSyAl7-Xk4MzA2JBbvCgKVEyY8sRdrUQOrXblIZpM19nfKOqntXj0386fN7zqw?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"385\" height=\"264\"></strong><br>What is the ratio of the average production of the refrigerators by companies A, B and E taken together for the year 2004-2005 to the average production of the refrigerators by companies C and D taken together for the year 2005-2006?</p>",
                    question_hi: "<p>20. दिए गए बार ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>बार ग्राफ में 2004 से 2007 के दौरान पाँच अलग-अलग कंपनियों A, B, C, D और E द्वारा रेफ्रिजरेटर के उत्पादन (हजार में) को दर्शाया गया है।<br><strong id=\"docs-internal-guid-f610b655-7fff-8204-7b9a-b4bade6d8b6f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcsuObFTDypj9GDWDJ21VkY69gVhr3iMKbwH4wy_955BGjf7qFHAHya4S3uuDt1InRl4Fe5zHiGoK7BvRfUy8ipG0BJlyL4xa-ijnDnCQfhtHdHdqhAuQ9MGIyN6wvTQDzFfta3Pw?key=-McMe0kf3XKCETVPK9bXAoAj\" width=\"366\" height=\"248\"></strong><br>कंपनी A, B और E द्वारा मिलाकर वर्ष 2004-2005 में रेफ्रिजरेटर के औसत उत्पादन तथा वर्ष 2005-2006 में कंपनी C और D द्वारा मिलाकर रेफ्रिजरेटर के औसत उत्पादन का अनुपात कितना है?</p>",
                    options_en: ["<p>43 : 19</p>", "<p>41 : 19</p>", 
                                "<p>19 : 41</p>", "<p>19 : 43</p>"],
                    options_hi: ["<p>43 : 19</p>", "<p>41 : 19</p>",
                                "<p>19 : 41</p>", "<p>19 : 43</p>"],
                    solution_en: "<p>20.(d) <br>Average production of the refrigerators by companies A, B and E together for the year <br>2004 - 2005 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>110</mn><mo>+</mo><mn>160</mn><mo>+</mo><mn>300</mn></mrow><mn>3</mn></mfrac></math> = 190<br>Average production of the refrigerators by companies C and D together for the year <br>2005 - 2006 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>370</mn><mo>+</mo><mn>490</mn></mrow><mn>3</mn></mfrac></math> = 430<br>Required ratio = 190 : 430 = 19 : 43</p>",
                    solution_hi: "<p>20.(d) <br>वर्ष 2004 - 2005 के लिए कंपनियों A, B और E द्वारा रेफ्रिजरेटर का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>110</mn><mo>+</mo><mn>160</mn><mo>+</mo><mn>300</mn></mrow><mn>3</mn></mfrac></math>&nbsp;= 190<br>वर्ष 2005 - 2006 के लिए कंपनी C और D द्वारा रेफ्रिजरेटर का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>370</mn><mo>+</mo><mn>490</mn></mrow><mn>3</mn></mfrac></math>&nbsp;= 430<br>आवश्यक अनुपात = 190 : 430 = 19 : 43</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. The value of 10 + 3<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &divide; {7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> - (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>)}] is equal to :</p>",
                    question_hi: "<p>21. 10 + 3<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &divide; {7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> - (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>)}] का मान _____________ के बराबर है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>21.(b) <br>10 + 3<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &divide; {7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> - (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>)}]<br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> &divide; {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>6</mn></mfrac></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>)}] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> &divide; {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>6</mn></mfrac></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>+</mo><mn>4</mn></mrow><mn>6</mn></mfrac></math>)}] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> &divide; {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>43</mn><mo>-</mo><mn>19</mn></mrow><mn>6</mn></mfrac></math>}] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> &times; {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>24</mn></mfrac></math>}] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>21.(b) <br>10 + 3<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &divide; {7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> - (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>)}]<br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> &divide; {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>6</mn></mfrac></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>)}] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> &divide; {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>6</mn></mfrac></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>+</mo><mn>4</mn></mrow><mn>6</mn></mfrac></math>)}] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> &divide; {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>43</mn><mo>-</mo><mn>19</mn></mrow><mn>6</mn></mfrac></math>}] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> &times; {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>24</mn></mfrac></math>}] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>] <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> <br>= 10 + <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. A company sells goods at a markup of 25% above the cost price. During a sale, the company offers a 10% discount on the marked price. If the customer pays ₹540 after the discount, then what was the original cost price of the goods?</p>",
                    question_hi: "<p>22. एक कंपनी क्रय मूल्य से 25% अधिक पर सामान बेचती है। सेल के दौरान, कंपनी अंकित मूल्य पर 10% की छूट देती है। यदि ग्राहक छूट के बाद ₹540 का भुगतान करता है, तो सामान का मूल क्रय मूल्य क्या था?</p>",
                    options_en: ["<p>₹470</p>", "<p>₹460</p>", 
                                "<p>₹480</p>", "<p>₹450</p>"],
                    options_hi: ["<p>₹470</p>", "<p>₹460</p>",
                                "<p>₹480</p>", "<p>₹450</p>"],
                    solution_en: "<p>22.(c)<br>Let the original cost price of the goods = 100 units<br>MP of the goods = 100 &times; 125% = 125 units<br>SP of the goods = 125 &times; <math display=\"inline\"><mn>90</mn><mi>%</mi></math> = 112.5 units<br>112.5 units = ₹ 540<br>(original cost price) 100 units = <math display=\"inline\"><mfrac><mrow><mn>540</mn></mrow><mrow><mn>112</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 = ₹ 480</p>",
                    solution_hi: "<p>22.(c)<br>माना वस्तु का मूल लागत मूल्य = 100 इकाई <br>वस्तु का अंकित मूल्य = 100 &times; 125% = 125 इकाई <br>वस्तु का विक्रय मूल्य = 125 &times; <math display=\"inline\"><mn>90</mn><mi>%</mi></math> = 112.5 इकाई <br>112.5 इकाई = ₹ 540<br>(वस्तु का वास्तविक क्रय मूल्य) 100 इकाई = <math display=\"inline\"><mfrac><mrow><mn>540</mn></mrow><mrow><mn>112</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 = ₹ 480</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. What is the curved surface area of cylinder whose radius is 3cm and height is 14cm? (Take &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>23. उस बेलन का वक्र पृष्ठीय क्षेत्रफल क्या है जिसकी त्रिज्या 3 cm और ऊंचाई 14 cm है ?(&pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    options_en: ["<p>262 cm<sup>2</sup></p>", "<p>264 cm<sup>2</sup></p>", 
                                "<p>263 cm<sup>2</sup></p>", "<p>261 cm<sup>2</sup></p>"],
                    options_hi: ["<p>262 cm<sup>2</sup></p>", "<p>264 cm<sup>2</sup></p>",
                                "<p>263 cm<sup>2</sup></p>", "<p>261 cm<sup>2</sup></p>"],
                    solution_en: "<p>23.(b)<br>CSA of the cylinder = 2&pi;rh<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 3 &times; 14<br>= 264 cm<sup>2</sup></p>",
                    solution_hi: "<p>23.(b)<br>बेलन का वक्र पृष्ठीय क्षेत्रफल = 2&pi;rh<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 3 &times; 14<br>= 264 cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. Kamlesh offers the following two discount schemes to his customers on the sale of an article:<br>(i) Two successive discounts of 25% and 5%<br>(ii) Two successive discounts of 20% and 10%<br>What is the difference between the selling price (in ₹) of the article under these two discount schemes if its marked price is ₹1,000?</p>",
                    question_hi: "<p>24. कमलेश अपने ग्राहकों को एक बस्तु की बिक्री पर निम्नलिखित दो छूट योजनाएं प्रदान करता है।<br>(i) 25% और 5% की दो क्रमिक छूट<br>(ii) 20% और 10% की दो क्रमिक छूट<br>यदि वस्तु का अंकित मूल्य ₹1,000 है तो इन दो छूट योजनाओं के तहत वस्तु के विक्रय मूल्य ( ₹ में) के बीच का अंतर क्या है?</p>",
                    options_en: ["<p>7.0</p>", "<p>7.5</p>", 
                                "<p>8.0</p>", "<p>6.5</p>"],
                    options_hi: ["<p>7.0</p>", "<p>7.5</p>",
                                "<p>8.0</p>", "<p>6.5</p>"],
                    solution_en: "<p>24.(b)<br>(i) SP = 1000 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = 712.5<br>(ii) SP = 1000 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 720<br>Difference between SP = 720 - 712.5 = 7.5</p>",
                    solution_hi: "<p>24.(b)<br>(i) विक्रय मूल्य = 1000 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = 712.5<br>(ii) विक्रय मूल्य = 1000 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 720<br>विक्रय मूल्यो के बीच का अंतर = 720 - 712.5 = 7.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Find the fourth proportional to 2, 6, 8.</p>",
                    question_hi: "<p>25. 2, 6, 8 का चतुर्थ समानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>36</p>", "<p>24</p>", 
                                "<p>12</p>", "<p>48</p>"],
                    options_hi: ["<p>36</p>", "<p>24</p>",
                                "<p>12</p>", "<p>48</p>"],
                    solution_en: "<p>25.(b) <br>Fourth proportional = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>bc</mi><mi mathvariant=\"normal\">a</mi></mfrac></math><br>Fourth proportional of 2, 6, 8 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>2</mn></mfrac></math> = 24</p>",
                    solution_hi: "<p>25.(b) <br>चतुर्थानुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>bc</mi><mi mathvariant=\"normal\">a</mi></mfrac></math><br>2, 6, 8 का चतुर्थानुपात= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>2</mn></mfrac></math>&nbsp;= 24</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>