<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Identify the error in the following sentence and select the option with the correct use of the phrasal verb. <br>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he blacked out.</p>",
                    question_hi: "<p>1. Identify the error in the following sentence and select the option with the correct use of the phrasal verb. <br>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he blacked out.</p>",
                    options_en: ["<p>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he broke in.</p>", "<p>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he chickened out.</p>", 
                                "<p>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he checked out.</p>", "<p>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he turned down.</p>"],
                    options_hi: ["<p>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he broke in.</p>", "<p>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he chickened out.</p>",
                                "<p>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he checked out.</p>", "<p>Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he turned down.</p>"],
                    solution_en: "<p>1.(b) Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he <span style=\"text-decoration: underline;\">chickened out</span>. <br>&lsquo;Blacked out&rsquo; must be replaced with &lsquo;chickened out&rsquo;. The phrasal verb &lsquo;chickened out&rsquo; means to fail to do something due to fear. While &lsquo;blacked out&rsquo; means to lose consciousness. Hence &lsquo;chickened out&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(b) Prateek was planning to ask Shreya for a favour. But he could not muster up the courage. So, he <span style=\"text-decoration: underline;\">chickened out</span>. <br>&lsquo;Blacked out&rsquo; के स्थान पर &lsquo;chickened out&rsquo; का प्रयोग होगा। Phrasal verb &lsquo;chickened out&rsquo; का अर्थ है, डर (fear) के कारण कुछ करने में विफल (fail) होना। जबकि &lsquo;blacked out&rsquo; का अर्थ है होश खोना (lose consciousness)। अतः &lsquo;chickened out&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br>She was singing / beautifully in / the concert hall / last night.</p>",
                    question_hi: "<p>2. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br>She was singing / beautifully in / the concert hall / last night.</p>",
                    options_en: ["<p>She was singing</p>", "<p>the concert hall</p>", 
                                "<p>beautifully in</p>", "<p>last night.</p>"],
                    options_hi: ["<p>She was singing</p>", "<p>the concert hall</p>",
                                "<p>beautifully in</p>", "<p>last night.</p>"],
                    solution_en: "<p>2.(a) She was singing<br>The word &lsquo;last&rsquo; indicates the use of simple past tense(V<sub>2</sub>). Therefore, &lsquo;was singing&rsquo; will be replaced with &lsquo;sang&rsquo;. Hence, &lsquo;She sang(V<sub>2</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(a) She was singing<br>Word &lsquo;last&rsquo;, simple past tense(V<sub>2</sub>) के प्रयोग को indicate करता है। इसलिए, &lsquo;was singing&rsquo; के स्थान पर &lsquo;sang&rsquo; का प्रयोग होगा। अतः, &lsquo;She sang(V<sub>2</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Identify the error in the following sentence and select the option with the correct use of the phrasal verb.<br>The police inspector was trying to give out the contradicting stories in the scene of the accident.</p>",
                    question_hi: "<p>3. Identify the error in the following sentence and select the option with the correct use of the phrasal verb.<br>The police inspector was trying to give out the contradicting stories in the scene of the accident.</p>",
                    options_en: ["<p>The police inspector was trying to lighten up the contradicting stories in the scene of the accident.</p>", "<p>The police inspector was trying to sign up the contradicting stories in the scene of the accident.</p>", 
                                "<p>The police inspector was trying to sort out the contradicting stories in the scene of the accident.</p>", "<p>The police inspector was trying to rush out the contradicting stories in the scene of the accident.</p>"],
                    options_hi: ["<p>The police inspector was trying to lighten up the contradicting stories in the scene of the accident.</p>", "<p>The police inspector was trying to sign up the contradicting stories in the scene of the accident.</p>",
                                "<p>The police inspector was trying to sort out the contradicting stories in the scene of the accident.</p>", "<p>The police inspector was trying to rush out the contradicting stories in the scene of the accident.</p>"],
                    solution_en: "<p>3.(c) The police inspector was trying to <span style=\"text-decoration: underline;\">sort out</span> the contradicting stories in the scene of the accident.<br>The phrasal verb &lsquo;sort out&rsquo; means to understand something such as a reason or solution by thinking. The given sentence talks about understanding the contradictory stories. Hence, the sentence given in option (c) uses the correct phrasal verb.</p>",
                    solution_hi: "<p>3.(c) The police inspector was trying to <span style=\"text-decoration: underline;\">sort out</span> the contradicting stories in the scene of the accident.<br>Phrasal verb &lsquo;sort out&rsquo; का अर्थ है सोच-विचार कर किसी कारण या समाधान जैसी किसी बात को समझना। दिया गया sentence विरोधाभासी कहानियों (contradictory stories) को समझने की बात करता है। अतः, option (c) में दिया गया sentence सही phrasal verb का प्रयोग करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Parts of the following sentence have been given as options. Select the option that contains an error.<br>I will be study around 7 o&rsquo;clock.</p>",
                    question_hi: "<p>4. Parts of the following sentence have been given as options. Select the option that contains an error.<br>I will be study around 7 o&rsquo;clock.</p>",
                    options_en: ["<p>around 7 o&rsquo;clock</p>", "<p>will</p>", 
                                "<p>be study</p>", "<p>I</p>"],
                    options_hi: ["<p>around 7 o&rsquo;clock</p>", "<p>will</p>",
                                "<p>be study</p>", "<p>I</p>"],
                    solution_en: "<p>4.(c) be study<br>&lsquo;Study&rsquo; must be replaced with &lsquo;studying&rsquo; as the given sentence is in the Future continuous Tense and the correct grammatical structure is &ldquo;Subject + will + be + V-ing&rdquo;. Hence, &lsquo;will be studying&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(c) be study<br>&lsquo;Study&rsquo; के स्थान पर &lsquo;studying&rsquo; का प्रयोग होगा क्योंकि दिया गया sentence, future continuous tense में है तथा &ldquo;Subject + will + be + V-ing&rdquo; सही grammatical structure है। अतः, &lsquo;will be studying&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. The given sentence is divided into four segments. Select the option that has the segment with the grammatical error.<br>My daughter participate / in the dance competition / that is organised every year / by the Lion&rsquo;s Club.</p>",
                    question_hi: "<p>5. The given sentence is divided into four segments. Select the option that has the segment with the grammatical error.<br>My daughter participate / in the dance competition / that is organised every year / by the Lion&rsquo;s Club.</p>",
                    options_en: ["<p>My daughter participate</p>", "<p>by the Lion&rsquo;s Club.</p>", 
                                "<p>that is organised every year</p>", "<p>in the dance competition</p>"],
                    options_hi: ["<p>My daughter participate</p>", "<p>by the Lion&rsquo;s Club.</p>",
                                "<p>that is organised every year</p>", "<p>in the dance competition</p>"],
                    solution_en: "<p>5.(a) My daughter participate<br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, a singular subject always takes a singular verb. At the end of a singular verb, s/es is used. Similarly, &lsquo;my daughter&rsquo; is a singular subject that will take &lsquo;participates&rsquo; as a singular verb. Hence, &lsquo;My daughter participates&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(a) My daughter participate<br>&ldquo;Subject-Verb Agreement Rule&rdquo; के अनुसार, एक singular subject के साथ हमेशा singular verb का प्रयोग होता है। Singular verb के अंत में s/es का प्रयोग होता है। इसी तरह, &lsquo;my daughter&rsquo; एक singular subject है जिसके साथ &lsquo;participates&rsquo; singular verb का प्रयोग होगा। अतः, &lsquo;My daughter participates&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. The following sentence has been divided into parts. One of them may contain an error.<br>Select the part that contains the error from the given options. If you don&rsquo;t find any&nbsp;error mark \'No error\' as your answer.<br>The aeroplane took down / without the passengers after / making a sudden announcement.</p>",
                    question_hi: "<p>6. The following sentence has been divided into parts. One of them may contain an error.<br>Select the part that contains the error from the given options. If you don&rsquo;t find any&nbsp;error mark \'No error\' as your answer.<br>The aeroplane took down / without the passengers after / making a sudden announcement.</p>",
                    options_en: ["<p>No error</p>", "<p>making a sudden announcement</p>", 
                                "<p>without the passengers after</p>", "<p>The aeroplane took down</p>"],
                    options_hi: ["<p>No error</p>", "<p>making a sudden announcement</p>",
                                "<p>without the passengers after</p>", "<p>The aeroplane took down</p>"],
                    solution_en: "<p>6.(d) The aeroplane took down<br>&lsquo;Took down&rsquo; must be replaced with &lsquo;took off&rsquo;. &lsquo;Took off&rsquo; means to begin flight. While &lsquo;took down&rsquo; means to defeat someone. The given sentence talks about the flight of the aeroplane. Hence &lsquo;The aeroplane took off&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(d) The aeroplane took down<br>&lsquo;Took down&rsquo; के स्थान पर &lsquo;took off&rsquo; का प्रयोग होगा। &lsquo;Took off&rsquo; का अर्थ है उड़ान प्रारंभ करना। जबकि took down&rsquo; का अर्थ है किसी को हराना (defeat)। दिया गया sentence, aeroplane की उड़ान (flight) के बारे में बात करता है। अतः &lsquo;The aeroplane took off&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Parts of the following sentence have been given as options. Select the option that contains an error.<br>Emily is a American living in Paris for her work.</p>",
                    question_hi: "<p>7. Parts of the following sentence have been given as options. Select the option that contains an error.<br>Emily is a American living in Paris for her work.</p>",
                    options_en: ["<p>for her work</p>", "<p>a American</p>", 
                                "<p>living in Paris</p>", "<p>Emily is</p>"],
                    options_hi: ["<p>for her work</p>", "<p>a American</p>",
                                "<p>living in Paris</p>", "<p>Emily is</p>"],
                    solution_en: "<p>7.(b) a American<br>Article &lsquo;A&rsquo; is used before the words starting with a consonant sound - (a conversation, a story). Whereas, the article &lsquo;An&rsquo; is used before the words starting with a vowel sound(an imminent danger, an apple). Hence, &lsquo;an American&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(b) a American<br>Consonant sound से प्रारंभ होने वाले words से पहले article &lsquo;A&rsquo; का प्रयोग किया जाता है, (a conversation, a story)। जबकि, vowel sound से प्रारंभ होने वाले words से पहले article &lsquo;An&rsquo; का प्रयोग किया जाता है, (an imminent danger, an apple)। अतः, &lsquo;an American&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. The following sentence has been divided into four parts. Identify the part that contains&nbsp;an error.<br>This is / a doll / and there is / a umbrella.</p>",
                    question_hi: "<p>8. The following sentence has been divided into four parts. Identify the part that contains&nbsp;an error.<br>This is / a doll / and there is / a umbrella.</p>",
                    options_en: ["<p>a umbrella.</p>", "<p>This is</p>", 
                                "<p>and there is</p>", "<p>a doll</p>"],
                    options_hi: ["<p>a umbrella.</p>", "<p>This is</p>",
                                "<p>and there is</p>", "<p>a doll</p>"],
                    solution_en: "<p>8.(a) a umbrella<br>Article &lsquo;A&rsquo; is used before the words starting from a consonant sound - (a conversation, a story). Whereas, the article &lsquo;An&rsquo; is used before the words starting from a vowel sound(an imminent danger, an apple). Hence, &lsquo;an umbrella&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(a) a umbrella<br>Consonant sound से प्रारंभ होने वाले words से पहले article &lsquo;A&rsquo; का प्रयोग किया जाता है, (a conversation, a story)। जबकि, vowel sound से प्रारंभ होने वाले words से पहले article &lsquo;An&rsquo; का प्रयोग किया जाता है, (an imminent danger, an apple)। अतः, &lsquo;an umbrella&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. The following sentence has been split into four segments. Identify the segment that&nbsp;contains an error.<br>She shall being / travelling / by this time / tomorrow.</p>",
                    question_hi: "<p>9. The following sentence has been split into four segments. Identify the segment that&nbsp;contains an error.<br>She shall being / travelling / by this time / tomorrow.</p>",
                    options_en: ["<p>She shall being</p>", "<p>tomorrow</p>", 
                                "<p>travelling</p>", "<p>by this time</p>"],
                    options_hi: ["<p>She shall being</p>", "<p>tomorrow</p>",
                                "<p>travelling</p>", "<p>by this time</p>"],
                    solution_en: "<p>9.(a) she shall being<br>Future continuous tense is used to describe an action that will be happening for a particular period of time in the future. &lsquo;Subject + will/shall + be + V-ing&rsquo; is the correct grammatical structure for it. Hence, &lsquo;She shall be travelling&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(a) she shall being<br>Future continuous tense का प्रयोग एक ऐसे action को describe करने के लिए किया जाता है जो future में किसी विशेष समय अवधि के लिए घटित होने वाला है। &lsquo;Subject + will/shall + be + V-ing&rsquo;, इसके लिए सही grammatical structure है। अतः, &lsquo;She shall be travelling&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The hotel room, / which we / booked, / isn&rsquo;t enough big.</p>",
                    question_hi: "<p>10. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The hotel room, / which we / booked, / isn&rsquo;t enough big.</p>",
                    options_en: ["<p>isn&rsquo;t enough big</p>", "<p>The hotel room</p>", 
                                "<p>booked,</p>", "<p>which we</p>"],
                    options_hi: ["<p>isn&rsquo;t enough big</p>", "<p>The hotel room</p>",
                                "<p>booked,</p>", "<p>which we</p>"],
                    solution_en: "<p>10.(a) isn&rsquo;t enough big<br>The adverb &lsquo;enough&rsquo; is placed right after the adjective it modifies. In the given sentence, &lsquo;enough&rsquo; modifies the adjective &lsquo;big&rsquo;. Therefore, &lsquo;enough&rsquo; will be used after &lsquo;big&rsquo;. Hence, &lsquo;isn&rsquo;t big enough&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(a) isn&rsquo;t enough big<br>Adverb &lsquo;enough&rsquo; को उस adjective के ठीक बाद रखा जाता है जिसे वह modify करता है। दिए गए sentence में, adjective &lsquo;enough&rsquo;, &lsquo;big&rsquo; को modify करता है। इसलिए, &lsquo;big&rsquo; के बाद &lsquo;enough&rsquo; का प्रयोग किया जाएगा। अतः, &lsquo;isn&rsquo;t big enough&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>Even in cold weather, the <span style=\"text-decoration: underline;\">reliable</span> car always started on the first try.</p>",
                    question_hi: "<p>11. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>Even in cold weather, the <span style=\"text-decoration: underline;\">reliable</span> car always started on the first try.</p>",
                    options_en: ["<p>dependable</p>", "<p>inaccurate</p>", 
                                "<p>erratic</p>", "<p>flaky</p>"],
                    options_hi: ["<p>dependable</p>", "<p>inaccurate</p>",
                                "<p>erratic</p>", "<p>flaky</p>"],
                    solution_en: "<p>11.(a) <strong>Reliable-</strong> dependable.</p>",
                    solution_hi: "<p>11.(a) <strong>Reliable</strong> (विश्वसनीय) - dependable.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.&nbsp;<br>The soldiers / were selected / on the section / of &lsquo;gallantry of spirits&rsquo;.</p>",
                    question_hi: "<p>12. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.&nbsp;<br>The soldiers / were selected / on the section / of &lsquo;gallantry of spirits&rsquo;.</p>",
                    options_en: ["<p>The soldiers</p>", "<p>were selected</p>", 
                                "<p>of &lsquo;gallantry of spirits&rsquo;.</p>", "<p>on the section</p>"],
                    options_hi: ["<p>The soldiers</p>", "<p>were selected</p>",
                                "<p>of &lsquo;gallantry of spirits&rsquo;.</p>", "<p>on the section</p>"],
                    solution_en: "<p>12.(d) on the section<br>Use of the word &lsquo;section&rsquo; is incorrect here. Rather, we will use the word &lsquo;basis&rsquo; to make the sentence meaningful. &lsquo;Basis&rsquo; means a particular condition that is used as a reference. Hence, &lsquo;on the basis&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(d) on the section<br>यहाँ word &lsquo;section&rsquo; का प्रयोग गलत है। इसके बजाय, हम sentence को meaningful बनाने के लिए word &lsquo;basis&rsquo; का प्रयोग करेंगे। &lsquo;Basis&rsquo; का अर्थ है एक विशेष स्थिति (particular condition) जिसका प्रयोग एक reference के रूप में किया जाता है। अतः, &lsquo;on the basis&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>We has travelled / to many states / of India to get / an essence of its diversity.</p>",
                    question_hi: "<p>13. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.&nbsp;<br>We has travelled / to many states / of India to get / an essence of its diversity.</p>",
                    options_en: ["<p>of India to get</p>", "<p>to many states</p>", 
                                "<p>an essence of its diversity</p>", "<p>We has travelled</p>"],
                    options_hi: ["<p>of India to get</p>", "<p>to many states</p>",
                                "<p>an essence of its diversity</p>", "<p>We has travelled</p>"],
                    solution_en: "<p>13.(d) We has travelled<br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;member&rsquo; is a plural subject that will take &lsquo;have&rsquo; as the helping verb. Hence, &lsquo;We have travelled&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(d) We has travelled<br>&ldquo;Subject-Verb Agreement Rule&rdquo; के अनुसार, एक singular subject के साथ हमेशा singular verb का तथा plural subject के साथ हमेशा plural verb का प्रयोग होता है। दिए गए sentence में, &lsquo;member&rsquo; एक plural subject है जिसके साथ &lsquo;have&rsquo; helping verb का प्रयोग होगा। अतः, We have travelled&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. The following sentence has been split into four segments. Identify the segment that contains a spelling error. <br>The make-up room was filled / with incadescent lights / which added to the misery / of those subjected to make-up.</p>",
                    question_hi: "<p>14. The following sentence has been split into four segments. Identify the segment that contains a spelling error. <br>The make-up room was filled / with incadescent lights / which added to the misery / of those subjected to make-up.</p>",
                    options_en: ["<p>The make-up room was filled</p>", "<p>with incadescent lights</p>", 
                                "<p>which added to the misery</p>", "<p>of those subjected to make-up</p>"],
                    options_hi: ["<p>The make-up room was filled</p>", "<p>with incadescent lights</p>",
                                "<p>which added to the misery</p>", "<p>of those subjected to make-up</p>"],
                    solution_en: "<p>14.(b) with <span style=\"text-decoration: underline;\">incadescent</span> lights<br>&lsquo;Incandescent&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>14.(b) with <span style=\"text-decoration: underline;\">incadescent</span> lights<br>&lsquo;Incandescent&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The following sentence has been split into four segments. Identify the segment that contains an error.<br>If she exercises daily, / she would / remain energetic / and fit forever.</p>",
                    question_hi: "<p>15. The following sentence has been split into four segments. Identify the segment that contains an error.<br>If she exercises daily, / she would / remain energetic / and fit forever.</p>",
                    options_en: ["<p>she would</p>", "<p>If she exercises daily</p>", 
                                "<p>and fit forever</p>", "<p>remain energetic</p>"],
                    options_hi: ["<p>she would</p>", "<p>If she exercises daily</p>",
                                "<p>and fit forever</p>", "<p>remain energetic</p>"],
                    solution_en: "<p>15.(a) she would <br>The given sentence is an example of the first conditional sentence and the correct grammatical structure is &ldquo;if + simple present&hellip;&hellip;.will +V<sub>1</sub>&rdquo;. Hence &lsquo;she will&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(a) she would <br>दिया गया sentence, first conditional sentence का example है तथा &ldquo;if + simple present&hellip;&hellip;.will +V<sub>1</sub>&rdquo; सही grammatical structure&rdquo; है। अतः &lsquo;she will&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>