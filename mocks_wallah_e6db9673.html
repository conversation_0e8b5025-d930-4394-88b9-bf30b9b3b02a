<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">18:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 18 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1<span style=\"font-family: Times New Roman;\">.</span><span style=\"font-family: Times New Roman;\"> </span>What would be the letter on the opposite side of \'Y\' if the given sheet is folded to form a cube?</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQHB_8-M2D5PO007Ei8sgnzjHFQY8fAQXVLJm7bY5au65IH4x0T4P4ha4c_Dorh2N3E0bYsSWRNiQ3IYz76zIOwJoie5rg7Uh1z9jy8Q7u5G1xPJBm0O257ZoLQGOe6kwEgK3G?key=CY1g6-5AIo59CLMBR95EnM89\" width=\"98\" height=\"177\"></p>\n<p>&nbsp;</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">1. </span>&nbsp;यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो \'Y के विपरीत फ़लक पर कोन-सा अक्षर होगा?</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQHB_8-M2D5PO007Ei8sgnzjHFQY8fAQXVLJm7bY5au65IH4x0T4P4ha4c_Dorh2N3E0bYsSWRNiQ3IYz76zIOwJoie5rg7Uh1z9jy8Q7u5G1xPJBm0O257ZoLQGOe6kwEgK3G?key=CY1g6-5AIo59CLMBR95EnM89\" width=\"98\" height=\"177\"></p>\n<p>&nbsp;</p>",
                    options_en: ["<p dir=\"ltr\">&nbsp;</p>\n<p>V</p>", "<p>Z</p>", 
                                "<p>X</p>", "<p>W</p>"],
                    options_hi: ["<p>V</p>", "<p>Z</p>",
                                "<p>X</p>", "<p>W</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\n<p><span style=\"font-family: Times New Roman;\"><strong id=\"docs-internal-guid-32fb143e-7fff-b02f-7a1d-2c593dce08f5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcJiwSEc5njvyjrRItSCcjF27nRD9Dncqrla6PHnDNjZmR5qsAZ-opAkk4mlAUsrqVeaf1BgwMyjcoONUgK0dsi388KZXGZcav0nx2vHQs4wyzNqQlArJjVjmZX_b89C-BF-9arIQ?key=CY1g6-5AIo59CLMBR95EnM89\" width=\"160\" height=\"165\"></strong></span></p>\n<p dir=\"ltr\">The opposite face are&nbsp;</p>\n<p dir=\"ltr\">Z &harr; Y, V &harr; W , U &harr; X</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)&nbsp;</span></p>\n<p><span style=\"font-family: Times New Roman;\"><strong id=\"docs-internal-guid-32fb143e-7fff-b02f-7a1d-2c593dce08f5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcJiwSEc5njvyjrRItSCcjF27nRD9Dncqrla6PHnDNjZmR5qsAZ-opAkk4mlAUsrqVeaf1BgwMyjcoONUgK0dsi388KZXGZcav0nx2vHQs4wyzNqQlArJjVjmZX_b89C-BF-9arIQ?key=CY1g6-5AIo59CLMBR95EnM89\" width=\"160\" height=\"165\"></strong></span></p>\n<p dir=\"ltr\">विपरीत फलक हैं&nbsp;</p>\n<p dir=\"ltr\">Z &harr; Y, V &harr; W , U &harr; X</p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2.Find out the alternative figure which contains figure (X) as its part.</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image31.png\"><span style=\"font-family: Times New Roman;\"> </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">2. विकल्पों में से उस आकृति का चयन करें जिसमें आकृति (X ) अंतर्निहित है | </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image31.png\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image25.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image13.png\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image9.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image16.png\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image25.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image13.png\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image9.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image16.png\"></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image9.png\"></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image9.png\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: " <p>3 </span><span style=\"font-family:Times New Roman\">Select the word-pair in which the two words are related in the same way as are the two words in the given word-pair.</span></p> <p><span style=\"font-family:Times New Roman\">Steel : Iron  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">3. उस शब्द-युग्म का चयन करें जिसमें दो शब्दों के बीच ठीक वही संबंध है जो संबंध दिए गए शब्द-युग्म के शब्दों के बीच है | </span></p>\r\n<p><span style=\"font-family: Baloo;\">इस्पात : लोहा </span></p>",
                    options_en: [" <p> Chromium : Silver </span></p>", " <p> Brass : Copper</span></p>", 
                                " <p> Bronze : Iron</span></p>", " <p> Nickel : Tin</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Baloo;\">क्रोमियम : चांदी</span></p>", "<p><span style=\"font-family: Baloo;\">पीतल : तांबा </span></p>",
                                "<p><span style=\"font-family: Baloo;\">कांसा : लोहा</span></p>", "<p><span style=\"font-family: Baloo;\">निकेल :टिन</span></p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) </span></p> <p><span style=\"font-family:Times New Roman\">Iron is a component of Steel Similarly Copper is a component of Brass.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">लोहा स्टील का एक घटक है इसी प्रकार तांबा पीतल का एक घटक है.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4<span style=\"font-family: Times New Roman;\">. </span><span style=\"font-family: Times New Roman;\">X is the mother of A. Y is the sister of B\'s father. R is the father of B. A and Z are sisters. B is the brother of Z. How is R related to X? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">4. X , A की माँ है | Y , B के पिता की बहन है | R , B का पिता है | A और Z बहने है | B , Z का भाई है | R , X से किस प्रकार सम्बंधित है ?</span></p>",
                    options_en: ["<p>Son</p>", "<p>Husband</p>", 
                                "<p>Father-in-law</p>", "<p>Father</p>"],
                    options_hi: ["<p><span style=\"font-family: Baloo;\">बेटा</span></p>", "<p><span style=\"font-family: Baloo;\">पति</span></p>",
                                "<p><span style=\"font-family: Baloo;\">ससुर</span></p>", "<p><span style=\"font-family: Baloo;\">पिता</span></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image30.png\"></p>\n<p><span style=\"font-family: Times New Roman;\">According to given statements R is the husband of X.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image30.png\"></p>\n<p>दिए गए कथनों के अनुसार R, X का पति है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5 Karan, Hari and Kowshik play cricket. The runs scored by Karan to Hari and Hari to Kowshik are in the ratio of 5:3. They <span style=\"font-family: Times New Roman;\">get altogether</span><span style=\"font-family: Times New Roman;\"> 588 runs. How many runs does </span><span style=\"font-family: Times New Roman;\">Karan</span><span style=\"font-family: Times New Roman;\"> get? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">5. करण, हरी और कौशिक क्रिकेट खेलते हैं | करण और हरी के द्वारा बनाए गए रन तथा हरी और कौशिक के द्वारा बनाए गए रन 5 : 3 के अनुपात में हैं | उन्होंने कुल मिलाकर 588 रन बनाए हैं | करण ने कितने रन बनाए हैं ? </span></p>",
                    options_en: ["<p>150 runs</p>", "<p>300 runs</p>", 
                                "<p>250 runs</p>", "<p>200 runs</p>"],
                    options_hi: ["<p>150 रन</p>", "<p>300 रन</p>",
                                "<p>250 रन</p>", "<p>200 रन</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">According to the given statement</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>Karan : Hari = (5:3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>Hari : Kowshik = (5:3)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>Karan : Hari : Kowshik = (25:15:9)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Therefore, the runs made by Karan,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>49</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\"> 588</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 25 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> 12 = 300 runs</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p>दिए गए कथन के अनुसार,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math></p>\r\n<p>कर्ण : हरि = (5:3)</p>\r\n<p>हरि : कौशिक = (5:3)</p>\r\n<p>कर्ण : हरि : कौशिक = (25:15:9)</p>\r\n<p>इसलिए, करण द्वारा बनाए गए रन,</p>\r\n<p><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>49</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\"> 588</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 25 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> 12 = 300 रन</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6.Ratio of present age of Veer, Sameer, Divyaraj, Ayush and Sumit is 14 : 15 : 13 : 12 : 16 and sum of age of Veer, Divyaraj &amp; Sumit four years later will be 44 years more than sum of present age of Sameer &amp; Ayush. Find the ratio of age of Veer, Sameer, Divyaraj, Ayush and Sumit after 10 years?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">6. वीर, समीर, दिव्यराज, आयुष और सुमित की वर्तमान आयु का अनुपात 14 : 15 : 13 : 12 : 16 है तथा अब से चार वर्ष बाद वीर, दिव्यराज और सुमित की उम्र का जोड़ समीर और आयुष की वर्तमान उम्र के जोड़ से 44 वर्ष अधिक होगा | 10 वर्षों के बाद वीर, समीर, दिव्यराज, आयुष और सुमित की उम्र में क्या अनुपात होगा ? </span></p>",
                    options_en: ["<p>(19 : 20 : 18 : 16 : 21)</p>", "<p><span style=\"font-weight: 400;\">(19 : 20 : 16 : 17 : 21)</span></p>", 
                                "<p>(19 : 20 : 22 : 17 : 21)</p>", "<p><span style=\"font-weight: 400;\">(19 : 20 : 18 : 17 : 21)</span></p>"],
                    options_hi: ["<p>(19 : 20 : 18 : 16 : 21)</p>", "<p><span style=\"font-weight: 400;\">(19 : 20 : 16 : 17 : 21)</span></p>",
                                "<p>(19 : 20 : 22 : 17 : 21)</p>", "<p><span style=\"font-weight: 400;\">(19 : 20 : 18 : 17 : 21)</span></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the present age of Veer, Sameer, Divyaraj, Ayush and Sumit be 14x, 15x, 13x, 12x, 16x respectively.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sum of age of Veer, Divyaraj &amp; Sumit four years hence = 44 years more than sum of present age of Sameer &amp; Ayush</span></p>\r\n<p>= 44 + 15x + 12x</p>\r\n<p><span style=\"font-family: Times New Roman;\">43x + 12 = 27x + 44</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">43x - 27x = 44 - 12</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">16x = 32</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Present age of Veer = 14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2 = 28 yrs</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Present age of Sameer = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2 = 30 yrs</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Present age of Divyaraj = 13<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2 = 26 yrs</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Present age of Ayush = 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2 = 24 yrs</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Present age of Sumit = 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2 = 32 yrs</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Ratio of age of Veer, Sameer, Divyaraj, Ayush and Sumit after 10 yrs = (28 + 10) : (30 + 10) : (26 + 10) : (24 + 10) : (32 + 10)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 38 : 40 : 36 : 34 : 42</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 19 : 20 : 18 : 17 : 21.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)माना वीर, समीर, दिव्यराज, आयुष और सुमित की वर्तमान आयु क्रमशः 14x, 15x, 13x, 12x, 16x है।&nbsp;</span></p>\r\n<pre id=\"tw-target-text\" class=\"tw-data-text tw-text-large tw-ta\" dir=\"ltr\" data-placeholder=\"Translation\"><span class=\"Y2IQFc\" lang=\"hi\">चार वर्ष बाद वीर, दिव्यराज और सुमित की आयु का योग = समीर और आयुष की वर्तमान आयु के योग से 44 वर्ष अधिक &nbsp;है। &nbsp;</span></pre>\r\n<p>= 44 + 15x + 12x</p>\r\n<p><span style=\"font-family: Times New Roman;\">43x + 12 = 27x + 44</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">43x - 27x = 44 - 12</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">16x = 32</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 2</span></p>\r\n<p>वीर की वर्तमान आयु = 14 &times; 2 = 28 वर्ष</p>\r\n<p>समीर की वर्तमान आयु = 15 &times; 2 = 30 वर्ष</p>\r\n<p>दिव्यराज की वर्तमान आयु = 13 &times; 2 = 26 वर्ष</p>\r\n<p>आयुष की वर्तमान आयु = 12 &times; 2 = 24 वर्ष</p>\r\n<p>सुमित की वर्तमान आयु = 16 &times; 2 = 32 वर्ष</p>\r\n<p><span style=\"font-family: Times New Roman;\">10 वर्ष बाद वीर, समीर, दिव्यराज, आयुष और सुमित की आयु का अनुपात = (28 + 10) : (30 + 10) : (26 + 10) : (24 + 10) : (32 + 10)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 38 : 40 : 36 : 34 : 42</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 19 : 20 : 18 : 17 : 21.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: " <p>7.In a certain code, NEWYORK is written as 111, how is </span><span style=\"font-family:Times New Roman\">NEWJERSEY</span><span style=\"font-family:Times New Roman\"> written in that code? ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">7. किसी निश्चित कूट में NEWYORK को 111 लिखा जाता है | इसी कूट में NEWJERSEY को किस प्रकार लिखा जाएगा ? </span></p>",
                    options_en: [" <p>124 </span></p>", " <p> 123</span></p>", 
                                " <p> 121</span><span style=\"font-family:Times New Roman\"> </span></p>", " <p> 120</span></p>"],
                    options_hi: ["<p>124</p>", "<p>123</p>",
                                "<p>121</p>", "<p>120</p>"],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\"> In NEWYORK</span></p> <p><span style=\"font-family:Times New Roman\">N = 14, E = 5, W = 23, Y = 25, O = 15, R = 18, K = 11</span></p> <p><span style=\"font-family:Times New Roman\">Total = 14 + 5 + 23 + 25 + 15 + 18 + 11 = 111</span></p> <p><span style=\"font-family:Times New Roman\">In NEWJERSEY</span></p> <p><span style=\"font-family:Times New Roman\">Total = 14 + 5 + 23 + 10 + 5 + 18 + 19 + 5 + 25</span></p> <p><span style=\"font-family:Times New Roman\">= 124</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\">&nbsp; NEWYORK में,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">N = 14, E = 5, W = 23, Y = 25, O = 15, R = 18, K = 11</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">कुल= 14 + 5 + 23 + 25 + 15 + 18 + 11 = 111</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;NEWJERSEY,में,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">कुल = 14 + 5 + 23 + 10 + 5 + 18 + 19 + 5 + 25</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 124</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">How many triangles are there in the given figure ?&nbsp;</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image19.png\"><span style=\"font-family: Times New Roman;\"> </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">8. दी गयी </span><span style=\"font-family: Baloo;\">आकृति</span><span style=\"font-family: Baloo;\"> में कितने त्रिभुज हैं ? </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image19.png\"></p>",
                    options_en: ["<p>26</p>", "<p>27</p>", 
                                "<p>24</p>", "<p>25</p>"],
                    options_hi: ["<p>26</p>", "<p>27</p>",
                                "<p>24</p>", "<p>25</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)There are a total of 12+9+1(ABC)+1(ABE)+1(ABD)+1(ABF)=25 distinct triangles in the given figure.</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image23.png\"></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)दी गई आकृति में कुल 12 + 9 +1(ABC) + 1(ABE) + 1(ABD) +1(ABF)=25 अलग-अलग त्रिभुज हैं।</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image23.png\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9.Choose the alternative of fig. (x)which most closely resembles the mirror-image placed on the right side of the given figures.</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image17.png\"><span style=\"font-family: Times New Roman;\"> </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">9. विकल्पों में से उस आकृति का चयन करें जो दी गयी आकृति के दाईं ओर रखे दर्पण-प्रतिबिम्ब के सर्वाधिक सदृश दिखती है | </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image17.png\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image3.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image24.png\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image4.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image7.png\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image3.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image24.png\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image4.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image7.png\"></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image7.png\"></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image7.png\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: " <p>10</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Times New Roman\">\'Pencil\' is related to \'Stationary\' in the same way as \'Pilates\' is related </span><span style=\"font-family:Times New Roman\">to\'________</span><span style=\"font-family:Times New Roman\">\'. ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">10. &lsquo;पेंसिल&rsquo; का &lsquo;लेखन सामग्री&rsquo; से ठीक वही संबंध है जो संबंध &lsquo;</span><span style=\"font-family: Baloo;\">पिलेट्स</span><span style=\"font-family: Baloo;\">&rsquo; का _____ से है | </span></p>",
                    options_en: [" <p>Pulses      </span><span style=\"font-family:Times New Roman\">        </span></p>", " <p>Spices</span></p>", 
                                " <p>Dry fruits  </span><span style=\"font-family:Times New Roman\">        </span></p>", " <p>Exercise</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Baloo;\">दाल</span></p>", "<p><span style=\"font-family: Baloo;\">मसाले</span></p>",
                                "<p><span style=\"font-family: Baloo;\">ड्राई फ्रूट्स</span></p>", "<p><span style=\"font-family: Baloo;\">व्यायाम</span></p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d)</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Pencil is an object of the class stationary. Similarly , Pilates is related to exercise.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>पेंसिल कक्षा की स्थिर वस्तु है। इसी तरह, पिलेट्स व्यायाम से संबंधित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: " <p>11.Find the wrong term in the given series</span></p> <p><span style=\"font-family:Times New Roman\">11, 5, 20, 12, 40, 26, 74, 54  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">11. दी गयी श्रृंखला में गलत संख्या कौन सी है ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">11, 5, 20, 12, 40, 26, 74, 54</span></p>",
                    options_en: [" <p> 5</span></p>", " <p> 20</span></p>", 
                                " <p> 40</span></p>", " <p> 26</span></p>"],
                    options_hi: ["<p>5</p>", "<p>20</p>",
                                "<p>40</p>", "<p>26</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)</span></p> <p><span style=\"font-family:Times New Roman\">The given sequence is a combination of two series.</span></p> <p><span style=\"font-family:Times New Roman\">I. 11, 20, 40, 74 and II. 5, 12, 26, 54.</span></p> <p><span style=\"font-family:Times New Roman\">The pattern in I becomes +9, +18, +36... If 40 is replaced by 38.</span></p> <p><span style=\"font-family:Times New Roman\">So, 40 is wrong.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p>या गया अनुक्रम दो श्रृंखलाओं का एक संयोजन है।</p>\r\n<p><span style=\"font-family: Times New Roman;\">I. 11, 20, 40, 74 और&nbsp;&nbsp;II. 5, 12, 26, 54.</span></p>\r\n<p>I में पैटर्न +9, +18, +36 हो जाता है... यदि 40 को 38 से बदल दिया जाता है।</p>\r\n<p>तो, 40 गलत है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: " <p>12. Compare the salary of employees B, R, G, A, and M in relation to each other:</span></p> <p><span style=\"font-family:Times New Roman\">M </span><span style=\"font-family:Times New Roman\">get</span><span style=\"font-family:Times New Roman\"> more salary than A and G. R’s salary is less than three persons. B’s salary is the lowest. Whose salary is the second lowest?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">12. कर्मचारियों B, R, G, A और M के वेतन की एक दूसरे से तुलना करे | M को A और G से अधिक वेतन मिलता है। R का वेतन तीन व्यक्तियों से कम है। B का वेतन सबसे कम है। किसका वेतन दूसरा सबसे कम है?</span></p>",
                    options_en: [" <p> R</span></p>", " <p> A</span><span style=\"font-family:Times New Roman\"> </span></p>", 
                                " <p> G</span></p>", " <p> M</span></p>"],
                    options_hi: ["<p>R</p>", "<p>A</p>",
                                "<p>G</p>", "<p>M</p>"],
                    solution_en: " <p>(a)</span></p> <p><span style=\"font-family:Times New Roman\">In 5 employees R’s salary is less than 3 employees so R’s salary is the second lowest in all.</span></p>",
                    solution_hi: "<p>(a)</p>\r\n<p>5 कर्मचारियों में R का वेतन 3 कर्मचारियों से कम है इसलिए R का वेतन सभी में दूसरा सबसे कम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13.Which of the following diagram correctly represents the Elected house, M.P., M.L.A.</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">13. निम्न में से कौन सा आरेख &lsquo;निर्वाचित सदन, सांसद और विधायक&rsquo; को सही तरह से दर्शाता है ? </span></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image10.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image15.png\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image8.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image21.png\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image10.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image15.png\" width=\"82\" height=\"67\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image8.png\" width=\"90\" height=\"84\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image21.png\" width=\"161\" height=\"57\"></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\n<p><span style=\"font-family: Times New Roman;\">Clearly, no M.P. can be M.L.A.Also, All M.P.s and M.L.A.s belong to the elected house.</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image11.png\"></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\n<p>जाहिर है कोई एम.पी. M.L.A हो सकते हैं।साथ ही, सभी M.P. और M.L.A. निर्वाचित सदन से संबंधित हैं।</p>\n<p><strong id=\"docs-internal-guid-fec1c328-7fff-d168-d224-3c3cd4e9f520\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeoZeN2n75YP8DE9urB_1x0bu65a7bCYLQetAJXnfBtK2JTXUepqZpC0gJjkDgd6NH1HIwYPrX0lCxaSaKhIK0wYTd_8-l4mJk_CvCggiMrWJjLdwmx896ee28dWFXnxCtZhxujcA?key=u3-Gnt1USFjFx8h4QpKY6y93\" width=\"140\" height=\"170\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: " <p>14.Arrange the given words in a logical sequence</span></p> <p><span style=\"font-family:Times New Roman\">1. Country    </span></p> <p><span style=\"font-family:Times New Roman\">2. Furniture   </span></p> <p><span style=\"font-family:Times New Roman\">3. Forest</span></p> <p><span style=\"font-family:Times New Roman\">4. Wood    </span></p> <p><span style=\"font-family:Times New Roman\">5. Trees  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">14. दिए गए शब्दों को एक अर्थपूर्ण क्रम में व्यवस्थित करें | </span></p>\r\n<p><span style=\"font-family: Baloo;\">1. देश</span></p>\r\n<p><span style=\"font-family: Baloo;\">2. फर्नीचर</span></p>\r\n<p><span style=\"font-family: Baloo;\">3. वन</span></p>\r\n<p><span style=\"font-family: Baloo;\">4. लकड़ी</span></p>\r\n<p><span style=\"font-family: Baloo;\">5. वृक्ष</span></p>",
                    options_en: [" <p>1,3,5,4,2</span></p>", " <p>1,4,3,2,5</span></p>", 
                                " <p>2,4,3,1,5</span></p>", " <p>5,2,3,1,4</span></p>"],
                    options_hi: ["<p>1,3,5,4,2</p>", "<p>1,4,3,2,5</p>",
                                "<p>2,4,3,1,5</p>", "<p>5,2,3,1,4</p>"],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">The correct order is :</span></p> <p><span style=\"font-family:Times New Roman\">Country, forest, Trees, Wood, Furniture</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>सही क्रम है :</p>\r\n<p>देश, जंगल, पेड़, लकड़ी, फर्नीचर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: " <p>15.In this question, four words have been given, out of which three are alike in some manner and the fourth one is different. Choose out the odd one? ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">15. इस प्रश्न में, चार शब्द दिए गए हैं | इनमें से तीन शब्द किसी निश्चित प्रकार से एक जैसे हैं जबकि कोई एक अलग है | अलग शब्द का चयन करें | </span></p>",
                    options_en: [" <p>Love </span><span style=\"font-family:Times New Roman\">     </span></p>", " <p>Cuddle</span></p>", 
                                " <p> Clasp </span><span style=\"font-family:Times New Roman\">     </span></p>", " <p> Nestle </span></p>"],
                    options_hi: ["<p>प्यार</p>", "<p>आलिंगन</p>",
                                "<p>गलबहियां</p>", "<p>पनाह देना</p>"],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">All except Love are gestures or endearment</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>प्यार को छोड़कर सभी इशारों या प्रेम हैं</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: " <p>16</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Times New Roman\">In a code language, BLEND is written as ENHPG. How </span><span style=\"font-family:Times New Roman\">will WINNER</span><span style=\"font-family:Times New Roman\"> be written in the same language? ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">16. एक कोड भाषा में, BLEND को ENHPG के रूप में लिखा जाता है। WINNER को उसी भाषा में कैसे लिखा जाएगा?</span></p>",
                    options_en: [" <p><span style=\"font-family:Times New Roman\">ZKQPHT</span><span style=\"font-family:Times New Roman\">     </span></p>", " <p><span style=\"font-family:Times New Roman\">ZKQPH</span><span style=\"font-family:Times New Roman\">U     </span></p>", 
                                " <p><span style=\"font-family:Times New Roman\">YKQPHT</span><span style=\"font-family:Times New Roman\">       </span></p>", " <p>ZKOPHT</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\">ZKQPHT</span></p>", "<p><span style=\"font-family: Times New Roman;\">ZKQPH</span><span style=\"font-family: Times New Roman;\">U</span></p>",
                                "<p><span style=\"font-family: Times New Roman;\">YKQPHT</span></p>", "<p>ZKOPHT</p>"],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Logic is : +3 , +2 , +3 , +2 rule is used. </span></p> <p><span style=\"font-family:Times New Roman\">So, the code for WINNER will be ZKQPHT.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">तर्क है:: +3 , +2 , +3 , +2 नियम का प्रयोग किया जाता है।</span></p>\r\n<p>तो, WINNER के लिए कोड ZKQPHT होगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17.In a certain code language, \'+\' means <span style=\"font-family: Times New Roman;\">\'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math>\' \'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Times New Roman;\">\' means \'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">\', \'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">\' means \'-\', \'-\' means \'+.\' Find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>44</mn><mo>-</mo><mn>11</mn><mo>&#247;</mo><mn>2</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>+</mo><mn>2</mn><mo>-</mo><mn>12</mn><mo>&#160;</mo></math></span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">17. किसी निश्चित कूट भाषा में, \'+\' का अर्थ \'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Times New Roman;\">\', \'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Baloo;\">\' का अर्थ \'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">\', \'<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Baloo;\">\' का अर्थ \'-\', \'-\' का अर्थ \'+\' है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>44</mn><mo>-</mo><mn>11</mn><mo>&#247;</mo><mn>2</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>+</mo><mn>2</mn><mo>-</mo><mn>12</mn><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">&nbsp;का मान क्या होगा ? </span></p>",
                    options_en: ["<p>65</p>", "<p>60</p>", 
                                "<p>104</p>", "<p>192</p>"],
                    options_hi: ["<p>65</p>", "<p>60</p>",
                                "<p>104</p>", "<p>192</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>A</mi><mi>s</mi><mo>&#160;</mo><mi>p</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>g</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mo>&#160;</mo><mi>e</mi><mi>x</mi><mi>p</mi><mi>r</mi><mi>e</mi><mi>s</mi><mi>s</mi><mi>i</mi><mi>o</mi><mi>n</mi><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mn>44</mn><mo>-</mo><mn>11</mn><mo>&#247;</mo><mn>2</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>+</mo><mn>2</mn><mo>-</mo><mn>12</mn><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mi>f</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mi>c</mi><mi>h</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>s</mi><mi>i</mi><mi>g</mi><mi>n</mi><mi>s</mi><mo>&#160;</mo><mi>a</mi><mi>c</mi><mi>c</mi><mi>o</mi><mi>r</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&#160;</mo><mi>t</mi><mi>o</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>i</mi><mi>n</mi><mi>s</mi><mi>t</mi><mi>r</mi><mi>u</mi><mi>c</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mi>s</mi><mo>,</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mn>44</mn><mo>+</mo><mn>11</mn><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>12</mn><mo>.</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>U</mi><mi>sin</mi><mi>g</mi><mo>&#160;</mo><mi>B</mi><mi>O</mi><mi>D</mi><mi>M</mi><mi>A</mi><mi>S</mi><mo>&#160;</mo><mi>r</mi><mi>u</mi><mi>l</mi><mi>e</mi><mo>,</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mn>44</mn><mo>+</mo><mn>11</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>12</mn><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&#160;</mo><mn>44</mn><mo>+</mo><mn>22</mn><mo>-</mo><mn>6</mn><mo>&#215;</mo><mn>3</mn><mo>+</mo><mn>12</mn><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&#160;</mo><mn>66</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>60</mn><mo>.</mo></math></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2327;&#2312;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2357;&#2351;&#2306;&#2332;&#2325;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2375;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>44</mn><mo>-</mo><mn>11</mn><mo>&#247;</mo><mn>2</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>+</mo><mn>2</mn><mo>-</mo><mn>12</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>&#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2379;&#2306;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2375;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2360;&#2306;&#2325;&#2375;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2348;&#2342;&#2354;&#2344;&#2375;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2375;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2348;&#2366;&#2342;</mi><mspace linebreak=\"newline\"/><mn>44</mn><mo>+</mo><mn>11</mn><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>12</mn><mo>.</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mi>B</mi><mi>O</mi><mi>D</mi><mi>M</mi><mi>A</mi><mi>S</mi><mo>&#160;</mo><mi>&#2344;&#2367;&#2351;&#2350;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2346;&#2366;&#2354;&#2344;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mspace linebreak=\"newline\"/><mn>44</mn><mo>+</mo><mn>11</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>12</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>=</mo><mo>&#160;</mo><mn>44</mn><mo>+</mo><mn>22</mn><mo>-</mo><mn>6</mn><mo>&#215;</mo><mn>3</mn><mo>+</mo><mn>12</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>=</mo><mo>&#160;</mo><mn>66</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>12</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>60</mn><mo>.</mo></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18.Find the term that is related to the third ratio in the same manner as the first ratio is related to the second.</p>\n<p><span style=\"font-family: Times New Roman;\"> 6 : 222 :: 9 : __ </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">18. वह शब्द ज्ञात करें जो तीसरे शब्द से ठीक उसी प्रकार संबंधित है जिस प्रकार पहला शब्द दूसरे शब्द से संबंधित है |</span></p>\n<p><span style=\"font-family: Times New Roman;\"> 6 : 222 :: 9 : __</span></p>",
                    options_en: ["<p>738</p>", "<p>767</p>", 
                                "<p>729</p>", "<p>744</p>"],
                    options_hi: ["<p>738</p>", "<p>767</p>",
                                "<p>729</p>", "<p>744</p>\n<p>&nbsp;</p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> Here,the logic: n : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>n</mi><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">+n</span></p>\n<p><span style=\"font-family: Times New Roman;\">6 : 222 :: 9 : __</span></p>\n<p><span style=\"font-family: Times New Roman;\">6 : (6)&sup3;</span><span style=\"font-family: Times New Roman;\"> + 6 :: 9 : (9)&sup3;</span><span style=\"font-family: Times New Roman;\"> + 9</span></p>\n<p><span style=\"font-family: Times New Roman;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>9</mn><mo>)</mo></mrow><mn>3</mn></msup></math></span><span style=\"font-weight: 400;\">&nbsp;+ 9 = 738 is the next number.</span></span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> यहाँ, तर्क है : n : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>n</mi><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">+n</span></p>\n<p><span style=\"font-family: Times New Roman;\">6 : 222 :: 9 : __</span></p>\n<p><span style=\"font-family: Times New Roman;\">6 : (6)&sup3;</span><span style=\"font-family: Times New Roman;\"> + 6 :: 9 : (9)&sup3;</span><span style=\"font-family: Times New Roman;\"> + 9</span></p>\n<p><span style=\"font-family: Times New Roman;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>9</mn><mo>)</mo></mrow><mn>3</mn></msup></math></span><span style=\"font-weight: 400;\"> + 9 = 738 अगली संख्या है।</span></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19.Find the option that follows the same pattern as the numbers in the given set.</p>\r\n<p><span style=\"font-weight: 400;\">(223, 324, 425)</span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">19. वह विकल्प कौन सा है जो उसी प्रारूप का अनुसरण करता है जिसका अनुसरण दिए गए समूह की </span><span style=\"font-family: Baloo;\">संख्याएँ</span><span style=\"font-family: Baloo;\"> करती हैं |</span></p>\r\n<p><span style=\"font-family: Baloo;\"><span style=\"font-weight: 400;\">(223, 324, 425)</span></span></p>",
                    options_en: ["<p>(225, 326, 437)<span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-weight: 400;\">(4511, 552, 626)</span></p>", 
                                "<p>(554, 655, 756) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-weight: 400;\">(623, 726, 823)</span></p>"],
                    options_hi: ["<p>(225, 326, 437)<span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-weight: 400;\">(4511, 552, 626)</span></p>",
                                "<p>(554, 655, 756) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-weight: 400;\">(623, 726, 823)</span></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><span style=\"font-weight: 400;\">The pattern: (1st term +3rd term)/2= 2nd term</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(223, 324, 425)</span></p>\r\n<p><span style=\"font-weight: 400;\">(223+425)/2=648/2=324</span></p>\r\n<p><span style=\"font-weight: 400;\">Similarly,&nbsp; (554, 655, 756)</span></p>\r\n<p><span style=\"font-weight: 400;\">554+756=1310/2=655</span></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p>पैटर्न: (पहला पद + तीसरा पद)/2= दूसरा पद</p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(223, 324, 425)</span></p>\r\n<p><span style=\"font-weight: 400;\">(223+425)/2=648/2=324</span></p>\r\n<p><span style=\"font-weight: 400;\">उसी प्रकार,&nbsp; (554, 655, 756)</span></p>\r\n<p><span style=\"font-weight: 400;\">554+756=1310/2=655</span></p>\r\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. In each of the questions below are given two statements followed by two conclusions numbered I and II. You have to take the given statements to be true even if they seem to be at variance from commonly known facts. Read the conclusions and then decide which of the given conclusions logically follows from the given statements disregarding commonly known facts.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Statements:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">No tree is a flower.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Some trees are fruits.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Conclusions:</span></p>\r\n<p><span style=\"font-weight: 400;\">(I) Fruits that are trees are not flowers.</span></p>\r\n<p><span style=\"font-weight: 400;\">(II) No fruit is a flower.</span></p>",
                    question_hi: "<p>20.&nbsp;<span style=\"font-weight: 400;\">नीचे दिए गए प्रश्न में, दो कथन दिए गए हैं जिनके बाद दो निष्कर्ष I और II हैं | आपको इन कथनों को सही मानना है भले ही ये सामान्य रूप से ज्ञात तथ्यों से अलग प्रतीत होते हैं | निष्कर्षों का अध्ययन करें और तय करें कि दिए गए कथनों से तर्कपूर्ण ढंग से कौन सा निष्कर्ष निकाला जा सकता है ?&nbsp;</span></p>\r\n<p><strong>कथन :</strong></p>\r\n<p><span style=\"font-weight: 400;\">कोई भी पेड़ फूल नहीं है |&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">कुछ वृक्ष फल हैं |&nbsp;</span></p>\r\n<p><strong>निष्कर्ष :</strong></p>\r\n<p><span style=\"font-weight: 400;\">(I)फल जो पेड़ हैं वे फूल नहीं हैं&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(II)कोई भी फल फूल नहीं है |&nbsp;</span></p>",
                    options_en: ["<p>Only conclusion I follows</p>", "<p>Only conclusion II follows</p>", 
                                "<p>Either conclusion I or II follows</p>", "<p>Neither conclusion I nor II follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I सही है |</p>", "<p>केवल निष्कर्ष II सही है |</p>",
                                "<p>या तो निष्कर्ष I या II सही है |</p>", "<p>ना तो निष्कर्ष I ना ही II सही है |</p>"],
                    solution_en: "<p>(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Here, there are two possibilities. </span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image12.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">From given Venn diagrams, we can say that conclusion (I) follows.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Also, the second diagram shows that satisfying conditions in given statements, some fruits can be flowers.</span></p>",
                    solution_hi: "<p>(a)</p>\r\n<p>यहां, दो संभावनाएं हैं।</p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image12.png\" /></p>\r\n<p>दिए गए वेन आरेखों से हम कह सकते हैं कि निष्कर्ष (I) अनुसरण करता है।</p>\r\n<p>साथ ही, दूसरा आरेख दर्शाता है कि दिए गए कथनों में संतोषजनक स्थितियाँ, कुछ फल फूल हो सकते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">In a code language, GLUCOSE is written as DJTCPUH. How will SUCROSE be written as in that language? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">21. एक कूट भाषा में, GLUCOSE को DJTCPUH लिखा जाता है | </span><span style=\"font-family: Baloo;\">इसी</span><span style=\"font-family: Baloo;\"> भाषा में SUCROSE को किस प्रकार लिखा जाएगा ? </span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\">PSBRPUH</span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>PSBSPUH</p>", 
                                "<p><span style=\"font-family: Times New Roman;\">PSBRPVH</span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\">PSBTPUH</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\">PSBRPUH</span></p>", "<p>PSBSPUH</p>",
                                "<p><span style=\"font-family: Times New Roman;\">PSBRPVH</span></p>", "<p><span style=\"font-family: Times New Roman;\">PSBTPUH</span></p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image28.png\"></p>\n<p><span style=\"font-family: Times New Roman;\">Similarly,</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image29.png\"></p>\n<p><span style=\"font-family: Times New Roman;\">Hence, option A is the correct answer.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image28.png\"></p>\n<p>इसी तरह,</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image29.png\"></p>\n<p>अत: विकल्प A सही उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22.Choose the option figure that will complete the given figure series.</p>\n<p><span style=\"font-family: Times New Roman;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image2.png\"></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">22. उस आकृति का चयन करें जो दी गयी आकृति श्रृंखला को पूर्ण कर देगी |</span></p>\n<p><span style=\"font-family: Baloo;\"><img src=\"data:image/png;base64,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\" width=\"440\" height=\"125\"></span></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image20.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image32.png\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image1.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image14.png\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image20.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image32.png\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image1.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image14.png\"></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)The U-shaped arrow is first laterally inverted and then inverted alternately. In the S-shaped arrow, first the arrowhead is inverted and then the whole arrow is inverted alternately.</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image14.png\"></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)U- आकार का तीर पहले पार्श्व में उल्टा होता है और फिर बारी-बारी से उल्टा होता है। S-आकार के तीर में, पहले तीर का सिरा उल्टा होता है और फिर बारी-बारी से पूरा तीर उल्टा होता है।</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626503180/word/media/image14.png\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: " <p>23.The salaries A, B, C are in the ratio 2 : 3 : 5. If the increments of 15%, 10% and 20% are allowed respectively in their salaries, then what will be the new ratio of their salaries?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">23. A, B और C के वेतन 2 : 3 : 5 के अनुपात में हैं | यदि उनके वेतन में क्रमशः 15%, 10% और 20% की वृद्धि हो जाती है, तो उनके वेतन का नया अनुपात क्या होगा ? </span></p>",
                    options_en: [" <p>3 : 3 : 10               </span></p>", " <p>10 : 11 : 20              </span></p>", 
                                " <p> 23 : 33 : 60</span></p>", " <p>Cannot be determined </span></p>"],
                    options_hi: ["<p>3 : 3 : 10</p>", "<p>10 : 11 : 20</p>",
                                "<p>23 : 33 : 60</p>", "<p>निर्धारित नहीं किया जा सकता |</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)</span></p> <p><span style=\"font-family:Times New Roman\">Let A = 2k, B = 3k and C = 5k</span></p> <p><span style=\"font-family:Times New Roman\">A\'s new salary = 115/100 of 2k = 23/10 k</span></p> <p><span style=\"font-family:Times New Roman\">B\'s new salary = 110/100 of 3k = 33/10 k</span></p> <p><span style=\"font-family:Times New Roman\">C\'s new salary = 120/100 of 5k = 6k</span></p> <p><span style=\"font-family:Times New Roman\">New ratio = 23k/10 : 33k/10 : 6k = 23:33:60</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p>माना A = 2k, B = 3k और C = 5k</p>\r\n<p><span style=\"font-family: Times New Roman;\">A का नया वेतन = 115/100 का 2k = 23/10 k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B का नया वेतन = 110/100 का 3k = 33/10 k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C का नया वेतन = 120/100 का5k = 6k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">नया अनुपात = 23k/10 : 33k/10 : 6k = 23:33:60</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: " <p>24.A gardener wants to plant trees in his </span><span style=\"font-family:Times New Roman\">garden in rows in</span><span style=\"font-family:Times New Roman\"> such a way that the number of trees in each row to be the same. If there are 24 rows the number of trees in each row is 42 if there are 12 more rows find the number of trees in each row?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">24. एक माली अपने बगीचे में कतार में इस प्रकार पौधे लगाना चाहता है कि प्रत्येक कतार में पौधों की संख्या समान रहे | कतार 24 हैं तथा प्रत्येक कतार में वृक्षों की संख्या 42 है | यदि 12 अतिरिक्त कतार हो जाते हैं, तो प्रत्येक कतार में वृक्षों की संख्या ज्ञात करें | </span></p>",
                    options_en: [" <p>63</span></p>", " <p>28</span></p>", 
                                " <p>48</span></p>", " <p>32</span></p>"],
                    options_hi: ["<p>63</p>", "<p>28</p>",
                                "<p>48</p>", "<p>32</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\">Required number of trees</span></p> <p><span style=\"font-family:Times New Roman\">24 × 42 = (24+12) × n</span></p> <p><span style=\"font-family:Times New Roman\">n = 28</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p>पेड़ों की आवश्यक संख्या</p>\r\n<p><span style=\"font-family: Times New Roman;\">24 &times; 42 = (24+12) &times; n</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">n = 28</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.&nbsp;What should come in place of the question mark (?) to complete the following letter- cluster series?</p>\n<p dir=\"ltr\">BDC, EGF, HJI, ?</p>\n<p>&nbsp;</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">25.&nbsp; </span>निम्नलिखित अक्षर-समूह श्रृंखला को पूरा करने के लिए प्रश्न चिह्न (?) के स्थान पर क्या आएगा?</p>\n<p dir=\"ltr\">BDC, EGF, HJI, ?</p>\n<p>&nbsp;</p>",
                    options_en: ["<p>KLN</p>", "<p dir=\"ltr\">KLM&nbsp;</p>\n<p>&nbsp;</p>", 
                                "<p dir=\"ltr\">LMN&nbsp;</p>\n<p>&nbsp;</p>", "<p>&nbsp;KML</p>"],
                    options_hi: ["<p dir=\"ltr\">KLN&nbsp;</p>", "<p dir=\"ltr\">KLM&nbsp;</p>\n<p>&nbsp;</p>",
                                "<p dir=\"ltr\">LMN&nbsp;</p>\n<p>&nbsp;</p>", "<p dir=\"ltr\">KML&nbsp;</p>\n<p>&nbsp;</p>"],
                    solution_en: "<p>(d)</p>\n<p><strong id=\"docs-internal-guid-6fffabb0-7fff-085f-3da7-f272d9301ee8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdX7awQgmRzSQdaZackRDcOvTvehEkjStXNuhaYdGGNTqLzOxC5X5Rqg1stKN-TKLTX0if9Pk0W-LQdDX1fKU3guqrhFZTv0f7HMTDmPLm8zYqpZizi1JDmV5qAEWUmuW1LfHRLtQ?key=VvK5dkfFAH4C9ItpJtgGoRrc\" width=\"235\" height=\"96\"></strong></p>",
                    solution_hi: "<p>(d)</p>\n<p><strong id=\"docs-internal-guid-6fffabb0-7fff-085f-3da7-f272d9301ee8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdX7awQgmRzSQdaZackRDcOvTvehEkjStXNuhaYdGGNTqLzOxC5X5Rqg1stKN-TKLTX0if9Pk0W-LQdDX1fKU3guqrhFZTv0f7HMTDmPLm8zYqpZizi1JDmV5qAEWUmuW1LfHRLtQ?key=VvK5dkfFAH4C9ItpJtgGoRrc\" width=\"235\" height=\"96\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>