<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">15:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1.<span style=\"font-family: Times New Roman;\"> Read the given statements and conclusions carefully. Assuming that the information </span><span style=\"font-family: Times New Roman;\">given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.</span><br><span style=\"font-family: Times New Roman;\"><strong>Statements</strong>:</span><br><span style=\"font-family: Times New Roman;\">No guest is trained.</span><br><span style=\"font-family: Times New Roman;\">All </span><span style=\"font-family: Times New Roman;\">trained are employees</span><span style=\"font-family: Times New Roman;\">.</span><br><span style=\"font-family: Times New Roman;\">Some employees are tall.</span><br><strong><span style=\"font-family: Times New Roman;\">Conclusions:</span></strong><br><span style=\"font-family: Times New Roman;\">I. Some </span><span style=\"font-family: Times New Roman;\">tall are</span><span style=\"font-family: Times New Roman;\"> trained.</span><br><span style=\"font-family: Times New Roman;\">II. Some employees are trained.</span><br><span style=\"font-family: Times New Roman;\">III. </span><span style=\"font-family: Times New Roman;\">No tall</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">is a</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">guest</span><span style=\"font-family: Times New Roman;\">. </span></p>",
                    question_hi: "<p>1.<span style=\"font-family: Baloo;\"> दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।</span><br><strong><span style=\"font-family: Baloo;\">कथन:</span></strong><br><span style=\"font-family: Baloo;\">कोई अतिथि प्रशिक्षित नहीं है।</span><br><span style=\"font-family: Baloo;\">सभी प्रशिक्षित कर्मचारी हैं।</span><br><span style=\"font-family: Baloo;\">कुछ कर्मचारी लम्बे हैं।</span><br><strong><span style=\"font-family: Baloo;\">निष्कर्ष:</span></strong><br><span style=\"font-family: Baloo;\">I. कुछ लम्बे प्रशिक्षित हैं।</span><br><span style=\"font-family: Baloo;\">II. कुछ कर्मचारी प्रशिक्षित हैं।</span><br><span style=\"font-family: Baloo;\">III. कोई लंबा अतिथि नहीं है.</span></p>",
                    options_en: ["<p>Only conclusion II follows</p>", "<p>Only conclusion I follows</p>", 
                                "<p>All the conclusions follow</p>", "<p>Only conclusions I and II follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II अनुसरण करता है</p>", "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                                "<p>सभी निष्कर्ष अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष I और II अनुसरण करते हैं</p>"],
                    solution_en: "<p>1.(a)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image26.png\" width=\"269\" height=\"72\"><br><span style=\"font-family: Times New Roman;\">Some </span><span style=\"font-family: Times New Roman;\">tall are trained</span><span style=\"font-family: Times New Roman;\"> can be true but not definitely true. Some employees are trained is definitely true and </span><span style=\"font-family: Times New Roman;\">No tall</span><span style=\"font-family: Times New Roman;\"> is a guest might be true but not definitely. Only Conclusion II is definitely true.</span></p>",
                    solution_hi: "<p>1.(a)<br><strong id=\"docs-internal-guid-3c0f83e3-7fff-c2d3-ba8e-c0dbfdc048d9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEwYz6czgwkYbOmIdqdsYLN77KJngPgXpSTX_ff8OC2iQUV63N4tghV45Um2Zgdpmh0MqAwTfij_Sm9dr-iR4OM_CcUhmYGOZgmNWJG422Lq4ZY4fPbwkIBACmICLdFarbtwBkpQ?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"265\" height=\"72\"></strong><br><span style=\"font-family: Baloo;\">कुछ लम्बे प्रशिक्षित हैं सत्य हो सकते हैं लेकिन निश्चित रूप से सत्य नहीं हैं। कुछ कर्मचारियों को प्रशिक्षित किया जाता है निश्चित रूप से सच है और कोई लंबा अतिथि नहीं है सत्य हो सकता है लेकिन निश्चित रूप से नहीं। केवल निष्कर्ष II निश्चित रूप से सत्य है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">2.</span><span style=\"font-family: Times New Roman;\"> Select the figure from among the given options that can replace the question mark (?) in the following series.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image13.png\" width=\"287\" height=\"72\"></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">2.</span><span style=\"font-family: Baloo;\"> दिए गए विकल्पों में से उस आकृति का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image13.png\" width=\"275\" height=\"69\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image6.png\" width=\"81\" height=\"73\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image29.png\" width=\"81\" height=\"73\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image24.png\" width=\"81\" height=\"73\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image19.png\" width=\"81\" height=\"73\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image6.png\" width=\"81\" height=\"73\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image29.png\" width=\"81\" height=\"74\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image24.png\" width=\"81\" height=\"73\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image19.png\" width=\"81\" height=\"73\"></p>"],
                    solution_en: "<p>2.(c)</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image4.png\"></p>",
                    solution_hi: "<p>2.(c)</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image4.png\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">3.</span><span style=\"font-family: Times New Roman;\"> Select the correct option that indicates the arrangement of the given words in the order in which they appear in an English dictionary.</span><br><span style=\"font-family: Times New Roman;\">1. Hygienic </span><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<br>2. Hydroplane</span><br><span style=\"font-family: Times New Roman;\">3. Hydroelectric&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Times New Roman;\">&nbsp;<br>4. Hydroxide</span><br><span style=\"font-family: Times New Roman;\">5. Hydrology </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">3.</span><span style=\"font-family: Baloo;\"> सही विकल्प का चयन करें जो दिए गए शब्दों की व्यवस्था को उसी क्रम में इंगित करता है जिस क्रम में वे अंग्रेजी शब्दकोश में दिखाई देते हैं।</span><br><span style=\"font-family: Times New Roman;\">1. Hygienic </span><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<br>2. Hydroplane</span><br><span style=\"font-family: Times New Roman;\">3. Hydroelectric </span><span style=\"font-family: Times New Roman;\">&nbsp; &nbsp; &nbsp; &nbsp;<br>4. Hydroxide</span><br><span style=\"font-family: Times New Roman;\">5. Hydrology </span></p>",
                    options_en: ["<p>1, 2, 3, 4, 5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3, 5, 1, 2, 4</p>", 
                                "<p>3, 5, 2, 4, 1 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3, 5, 1, 4, 2</p>"],
                    options_hi: ["<p>1, 2, 3, 4, 5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3, 5, 1, 2, 4</p>",
                                "<p>3, 5, 2, 4, 1 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3, 5, 1, 4, 2</p>"],
                    solution_en: "<p>3.(c)<br><span style=\"font-family: Times New Roman;\">The correct dictionary order is:-</span><br><span style=\"font-family: Cardo;\">Hydroelectric&rarr;Hydrology&rarr;Hydroplane&rarr;Hydroxide&rarr;Hygienic</span></p>",
                    solution_hi: "<p>3.(c)<br><span style=\"font-family: Baloo;\">सही शब्दकोश क्रम है :-</span><br><span style=\"font-family: Cardo;\">Hydroelectric&rarr;Hydrology&rarr;Hydroplane&rarr;Hydroxide&rarr;Hygienic</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">4.</span><span style=\"font-family: Times New Roman;\"> How many rectangles are there in the given figure?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image25.png\"></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">4.</span><span style=\"font-family: Baloo;\"> दी गई आकृति में कितने आयत हैं?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image25.png\"></p>",
                    options_en: ["<p>14</p>", "<p>12</p>", 
                                "<p>8</p>", "<p>10</p>"],
                    options_hi: ["<p>14</p>", "<p>12</p>",
                                "<p>8</p>", "<p>10</p>"],
                    solution_en: "<p>4.(a)<br><strong id=\"docs-internal-guid-057b4ed8-7fff-39e2-610b-0e851177640e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeJZMghbljFjyBAaR4U9QSYbDvDEuZYYU9ZTrDC92PDSVzHcQtxZH3VT6xBmIOaWKx0eZFVRjdlO3MFu9IbdvsAaiuvkLTaEp0FzpQM-5_tEqaUzmSVUY66zpAhykLhj1ez2HEU1A?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"202\" height=\"70\"></strong><br><span style=\"font-family: Times New Roman;\">Rectangles are : a, b, c, d, e, f, ab, bc, cd, ae, df, abc, bcd, and abcd.</span></p>",
                    solution_hi: "<p>4.(a)<br><strong id=\"docs-internal-guid-057b4ed8-7fff-39e2-610b-0e851177640e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeJZMghbljFjyBAaR4U9QSYbDvDEuZYYU9ZTrDC92PDSVzHcQtxZH3VT6xBmIOaWKx0eZFVRjdlO3MFu9IbdvsAaiuvkLTaEp0FzpQM-5_tEqaUzmSVUY66zpAhykLhj1ez2HEU1A?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"202\" height=\"70\"></strong><br><span style=\"font-family: Baloo;\">आयत हैं: a, b, c, d, e, f, ab, bc, cd, ae, df, abc, bcd, &amp; abcd.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">5.</span><span style=\"font-family: Times New Roman;\"> Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different. </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">5.</span><span style=\"font-family: Baloo;\"> चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस अक्षर-समूह का चयन करें जो भिन्न हो।</span></p>",
                    options_en: ["<p>QTV <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>NQT</p>", 
                                "<p>PSV</p>", "<p>MPS</p>"],
                    options_hi: ["<p>QTV</p>", "<p>NQT</p>",
                                "<p>PSV</p>", "<p>MPS</p>"],
                    solution_en: "<p>5. (a)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>+3 pattern is followed</span><br><span style=\"font-family: Times New Roman;\">But in, QTV, T + 3 = W (Not V)</span></p>",
                    solution_hi: "<p>5. (a)<br><span style=\"font-family: Baloo;\"><strong>तर्क:</strong> +3 पैटर्न का पालन किया जाता है</span><br><span style=\"font-family: Baloo;\">लेकिन, QTV, T + 3 = W (V नहीं)</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">6.</span><span style=\"font-family: Times New Roman;\"> Select the correct combination of mathematical signs that can sequentially replace the * signs and make the equation correct.</span><br><span style=\"font-family: Times New Roman;\">48 * 12 * 8 * 15 * 3 * 44 </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">6.</span><span style=\"font-family: Baloo;\"> गणितीय चिन्हों के उस सही संयोजन का चयन करें जो क्रमिक रूप से * चिन्हों को प्रतिस्थापित कर सके और समीकरण को सही बना सके।</span><br><span style=\"font-family: Times New Roman;\">48 * 12 * 8 * 15 * 3 * 44 </span></p>",
                    options_en: ["<p>&times;, &divide;, =, &minus;, + <span style=\"font-family: Gungsuh;\"> </span></p>", "<p>+, &divide;, &minus;, =, &times;</p>", 
                                "<p>=, &times;, +, &divide;, &minus; <span style=\"font-family: Gungsuh;\"> </span></p>", "<p>&divide;, &times;, +, &minus;, =</p>"],
                    options_hi: ["<p>&times;, &divide;, =, &minus;, + <span style=\"font-family: Gungsuh;\"> </span></p>", "<p>+, &divide;, &minus;, =, &times;</p>",
                                "<p>=, &times;, +, &divide;, &minus; <span style=\"font-family: Gungsuh;\"> </span></p>", "<p>&divide;, &times;, +, &minus;, =</p>"],
                    solution_en: "<p>6.(d)<br><span style=\"font-family: Times New Roman;\">Using option (d)</span><br><span style=\"font-family: Times New Roman;\">48 &divide; 12 &times; 8 + 15 - 3 = 44</span><br><span style=\"font-family: Times New Roman;\">4 &times; 8 +15 -3 = 44</span><br><span style=\"font-family: Times New Roman;\">32 + 15 - 3 = 44</span><br><span style=\"font-family: Times New Roman;\">44 = 44 Hence Verified</span></p>",
                    solution_hi: "<p>6.(d)<br><span style=\"font-family: Baloo;\">विकल्प (d) का उपयोग करने पर</span><br><span style=\"font-family: Times New Roman;\">48 &divide; 12 &times; 8 + 15 - 3 = 44</span><br><span style=\"font-family: Times New Roman;\">4 &times; 8 +15 -3 = 44</span><br><span style=\"font-family: Times New Roman;\">32 + 15 - 3 = 44</span><br><span style=\"font-family: Baloo;\">अतः 44 = 44 सत्यापित है </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">7.</span><span style=\"font-family: Times New Roman;\"> Select the number from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Times New Roman;\">215, 231, 256, 292, ? </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">7.</span><span style=\"font-family: Baloo;\"> दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।</span><br><span style=\"font-family: Times New Roman;\"> 215, 231, 256, 292, ? </span></p>",
                    options_en: ["<p>369</p>", "<p>341</p>", 
                                "<p>328</p>", "<p>402</p>"],
                    options_hi: ["<p>369</p>", "<p>341</p>",
                                "<p>328</p>", "<p>402</p>"],
                    solution_en: "<p>7.(b)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>difference is of the square of the consecutive number.</span><br><span style=\"font-family: Times New Roman;\">292 - 256 = 36 next square is 49</span><br><span style=\"font-family: Times New Roman;\">So, 292 + 49 = 341</span></p>",
                    solution_hi: "<p>7.(b)<br><span style=\"font-family: Baloo;\"><strong>तर्क : </strong>अंतर क्रमागत संख्या के वर्ग का होता है।</span><br><span style=\"font-family: Baloo;\">292 - 256 = 36 अगला वर्ग 49 है</span><br><span style=\"font-family: Baloo;\">अतः, 292 + 49 = 341</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">8.</span><span style=\"font-family: Times New Roman;\"> Two different positions of the same dice are shown, the faces of which are marked with the numbers IX, X, XI, XII, XIII and XIV. Select the number that will be on the face opposite to the face having the number &lsquo;XIV&rsquo;.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image12.png\" width=\"191\" height=\"83\"></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">8.</span><span style=\"font-family: Baloo;\"> एक ही पासे के दो अलग-अलग स्थान दिखाए गए हैं, जिनके फलक IX, X, XI, XII, XIII और XIV अंकों से अंकित हैं। उस संख्या का चयन करें जो \'XIV\' संख्या वाले फलक के विपरीत फलक पर होगी।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image12.png\" width=\"177\" height=\"77\"></p>",
                    options_en: ["<p>XI</p>", "<p>X</p>", 
                                "<p>XII</p>", "<p>IX</p>"],
                    options_hi: ["<p>XI</p>", "<p>X</p>",
                                "<p>XII</p>", "<p>IX</p>"],
                    solution_en: "<p>8.(a)<br><span style=\"font-family: Times New Roman;\">In both the dice X is common so moving clockwise from X we get XI and XIV. so they are opposite to each other. </span></p>",
                    solution_hi: "<p>8.(a)<br><span style=\"font-family: Baloo;\">दोनों पासों में X उभयनिष्ठ है इसलिए X से दक्षिणावर्त घूमने पर हमें XI और XIV मिलते हैं। इसलिए वे एक दूसरे के विपरीत हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">9.</span><span style=\"font-family: Times New Roman;\"> The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown in the following figures. How would this paper look when unfolded?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image15.png\" width=\"249\" height=\"76\"></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">9.</span><span style=\"font-family: Baloo;\"> कागज के एक टुकड़े को मोड़ने का क्रम और जिस तरीके से मुड़े हुए कागज को काटा गया है, उसे निम्नलिखित आकृतियों में दिखाया गया है। खोलने पर यह पेपर कैसा दिखेगा?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image15.png\" width=\"246\" height=\"75\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image14.png\" width=\"84\" height=\"73\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image28.png\" width=\"84\" height=\"73\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image22.png\" width=\"84\" height=\"73\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image2.png\" width=\"84\" height=\"73\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image14.png\" width=\"85\" height=\"74\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image28.png\" width=\"84\" height=\"73\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image22.png\" width=\"84\" height=\"73\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image2.png\" width=\"85\" height=\"74\"></p>"],
                    solution_en: "<p>9.(b)</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image18.png\"></p>",
                    solution_hi: "<p>9.(b)</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image18.png\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">10.</span><span style=\"font-family: Times New Roman;\"> Four words have been given, out of which three are alike in some manner and one is different. Select the word that is different. </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">10.</span><span style=\"font-family: Baloo;\"> चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। उस शब्द का चयन करें जो भिन्न है।</span></p>",
                    options_en: ["<p>Guide <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>Instructor</p>", 
                                "<p>Mistress <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>Mentor</p>"],
                    options_hi: ["<p>मार्गदर्शक<span style=\"font-family: Baloo;\"> </span></p>", "<p>प्रशिक्षक</p>",
                                "<p>मालकिन<span style=\"font-family: Baloo;\"> </span></p>", "<p>सलाहकार</p>"],
                    solution_en: "<p>10.(c)<br><span style=\"font-family: Times New Roman;\">Except Mistress</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">all</span><span style=\"font-family: Times New Roman;\"> others perform the same type of job </span><span style=\"font-family: Times New Roman;\">but mistress</span><span style=\"font-family: Times New Roman;\"> is a female school teacher.</span></p>",
                    solution_hi: "<p>10.(c)<br><span style=\"font-family: Baloo;\">मालकिन को छोड़कर अन्य सभी एक ही प्रकार का काम करते हैं लेकिन मालकिन एक महिला स्कूल शिक्षक है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">11.</span><span style=\"font-family: Times New Roman;\"> Select the option in which the numbers are related in the same way as are the numbers of the following set.</span><br>(4,8,16)</p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">11.</span><span style=\"font-family: Baloo;\"> उस विकल्प का चयन करें जिसमें संख्याएँ उसी प्रकार संबंधित हैं जैसे निम्नलिखित सेट की संख्याएँ हैं।</span><br>(4 , 8, 16 )</p>",
                    options_en: ["<p>(20, 24, 16) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(11, 16, 51)</p>", 
                                "<p>(24, 15, 44) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(18, 6, 15)</p>"],
                    options_hi: ["<p>(20, 24, 16) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(11, 16, 51)</p>",
                                "<p>(24, 15, 44) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(18, 6, 15)</p>"],
                    solution_en: "<p>11.(a)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>a, b,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>a</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup></math></span><br><span style=\"font-family: Times New Roman;\">Following the above logic : 20, 24, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>24</mn><mo>-</mo><mn>20</mn><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">= 16</span></p>",
                    solution_hi: "<p>11.(a)<br><span style=\"font-family: Baloo;\"><strong>तर्क:</strong> a, b,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>a</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup></math></span><br><span style=\"font-family: Baloo;\">उपरोक्त तर्क के बाद : 20, 24, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>24</mn><mo>-</mo><mn>20</mn><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">= 16</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">12.</span><span style=\"font-family: Times New Roman;\"> Select the option that is embedded in the given figure (rotation is NOT allowed).</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image23.png\" width=\"132\" height=\"79\"></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">12.</span><span style=\"font-family: Baloo;\"> उस विकल्प का चयन करें जो दी गई आकृति में सन्निहित है (रोटेशन की अनुमति नहीं है)।</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image23.png\" width=\"137\" height=\"82\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image21.png\" width=\"74\" height=\"63\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image20.png\" width=\"67\" height=\"37\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image3.png\" width=\"72\" height=\"56\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image11.png\" width=\"74\" height=\"45\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image21.png\" width=\"62\" height=\"53\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image20.png\" width=\"77\" height=\"43\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image3.png\" width=\"64\" height=\"50\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image11.png\" width=\"72\" height=\"44\"></p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image16.png\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image16.png\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">13. </span><span style=\"font-family: Times New Roman;\">In the following Venn diagram, the triangle stands for &lsquo;Players&rsquo;, the hexagon stands for &lsquo;Indians&rsquo;, and the rectangle stands for &lsquo;Boys&rsquo;. The given numbers represent the number of persons in that particular category.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image5.png\" width=\"133\" height=\"100\"><br><span style=\"font-family: Times New Roman;\">How many Indians are players but are NOT boys?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">13.</span><span style=\"font-family: Baloo;\"> निम्नलिखित वेन आरेख में, त्रिभुज \'खिलाड़ियों\' के लिए है, षट्भुज \'भारतीय\' के लिए है, और आयत \'लड़कों\' के लिए है। दी गई संख्याएं उस विशेष श्रेणी में व्यक्तियों की संख्या दर्शाती हैं।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image5.png\" width=\"131\" height=\"99\"><br><span style=\"font-family: Baloo;\">कितने भारतीय खिलाड़ी हैं लेकिन लड़के नहीं हैं?</span></p>",
                    options_en: ["<p>10</p>", "<p>8</p>", 
                                "<p>9</p>", "<p>11</p>"],
                    options_hi: ["<p>10</p>", "<p>8</p>",
                                "<p>9</p>", "<p>11</p>"],
                    solution_en: "<p>13.(a)<br><span style=\"font-family: Times New Roman;\">Indians players who are not boys are represented by the area common in hexagon and Triangle but not in rectangle. 10 represent that area. </span></p>",
                    solution_hi: "<p>13.(a)<br><span style=\"font-family: Baloo;\">भारतीय खिलाड़ी जो लड़के नहीं हैं, उनका प्रतिनिधित्व षट्भुज और त्रिभुज में उभयनिष्ठ क्षेत्र द्वारा किया जाता है, लेकिन आयत में नहीं। 10 उस क्षेत्र का प्रतिनिधित्व करते हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">14.</span><span style=\"font-family: Times New Roman;\"> Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.</span><br><span style=\"font-family: Times New Roman;\">_ Q _ S P _ R _ _ Q _ _ P _ R _ </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">14.</span><span style=\"font-family: Baloo;\"> अक्षरों के उस संयोजन का चयन करें जिसे दी गई श्रंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रंखला पूरी हो जाएगी।</span><br><span style=\"font-family: Times New Roman;\">_ Q _ S P _ R _ _ Q _ _ P _ R _ </span></p>",
                    options_en: ["<p>P, R, Q, S, P, R, R, P, Q</p>", "<p>P, R, Q, S, P, R, S, Q, S</p>", 
                                "<p>S, P, R, S, P, R, Q, Q, S</p>", "<p>P, R, Q, S, Q, S, P, R, S</p>"],
                    options_hi: ["<p>P, R, Q, S, P, R, R, P, Q</p>", "<p>P, R, Q, S, P, R, S, Q, S</p>",
                                "<p>S, P, R, S, P, R, Q, Q, S</p>", "<p>P, R, Q, S, Q, S, P, R, S</p>"],
                    solution_en: "<p>14.(b)<br><span style=\"font-family: Times New Roman;\">Repetitive unit is &lsquo;PQRS&rsquo;</span><br><span style=\"font-family: Times New Roman;\">Complete series is:-</span><br><span style=\"font-family: Times New Roman;\"> </span><strong><span style=\"font-family: Times New Roman;\">P</span></strong><span style=\"font-family: Times New Roman;\"> Q </span><strong><span style=\"font-family: Times New Roman;\">R</span></strong><span style=\"font-family: Times New Roman;\"> S P </span><strong><span style=\"font-family: Times New Roman;\">Q</span></strong><span style=\"font-family: Times New Roman;\"> R </span><strong><span style=\"font-family: Times New Roman;\">S</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">P</span></strong><span style=\"font-family: Times New Roman;\"> Q </span><strong><span style=\"font-family: Times New Roman;\">R</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">S</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>P </span><strong><span style=\"font-family: Times New Roman;\">Q</span></strong><span style=\"font-family: Times New Roman;\"> R<strong> </strong></span><strong><span style=\"font-family: Times New Roman;\">S</span></strong><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>14.(b)<br><span style=\"font-family: Baloo;\">दोहराई जाने वाली इकाई \'PQRS\' है।</span><br><span style=\"font-family: Baloo;\">पूरी श्रृंखला है:-</span><br><span style=\"font-family: Times New Roman;\"> </span><strong><span style=\"font-family: Times New Roman;\">P</span></strong><span style=\"font-family: Times New Roman;\"> Q </span><strong><span style=\"font-family: Times New Roman;\">R</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>S P </span><strong><span style=\"font-family: Times New Roman;\">Q</span></strong><span style=\"font-family: Times New Roman;\"> R </span><strong><span style=\"font-family: Times New Roman;\">S</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">P</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>Q </span><strong><span style=\"font-family: Times New Roman;\">R</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">S</span></strong><span style=\"font-family: Times New Roman;\"> P </span><strong><span style=\"font-family: Times New Roman;\">Q</span></strong><span style=\"font-family: Times New Roman;\"> R<strong> </strong></span><strong><span style=\"font-family: Times New Roman;\">S</span></strong><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> There are 28 </span><span style=\"font-family: Times New Roman;\">persons</span><span style=\"font-family: Times New Roman;\"> present at a press conference. After the conference they all shake hands with each other. If 7 of them refuse to shake hands at all, then how many </span><span style=\"font-family: Times New Roman;\">handshakes will be there altogether? </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">15.</span><span style=\"font-family: Baloo;\"> एक प्रेस कांफ्रेंस में 28 लोग मौजूद हैं। सम्मेलन के बाद सभी एक दूसरे से हाथ मिलाते हैं। यदि उनमें से 7 हाथ मिलाने से बिल्कुल भी इंकार नहीं करते हैं, तो कुल मिलाकर कितने हाथ मिलाने होंगे?</span></p>",
                    options_en: ["<p>420</p>", "<p>210</p>", 
                                "<p>378</p>", "<p>231</p>"],
                    options_hi: ["<p>420</p>", "<p>210</p>",
                                "<p>378</p>", "<p>231</p>"],
                    solution_en: "<p>15.(b)<br><span style=\"font-family: Times New Roman;\">Total person who agrees to handshake = 21</span><br><span style=\"font-family: Times New Roman;\">Total number of handshake =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><msub><mi>C</mi><mn>2</mn></msub><mprescripts/><none/><mi>n</mi></mmultiscripts></math></span><br><span style=\"font-family: Times New Roman;\">So,&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><msub><mi>C</mi><mn>2</mn></msub><mprescripts/><none/><mn>21</mn></mmultiscripts></math></span><span style=\"font-family: Times New Roman;\">= 210</span></p>",
                    solution_hi: "<p>15.(b)<br><span style=\"font-family: Baloo;\">हाथ मिलाने के लिए सहमत होने वाले कुल व्यक्ति = 21</span><br><span style=\"font-family: Baloo;\">हाथ मिलाने की कुल संख्या =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><msub><mi>C</mi><mn>2</mn></msub><mprescripts/><none/><mi>n</mi></mmultiscripts></math></span><br><span style=\"font-family: Baloo;\">तो, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><msub><mi>C</mi><mn>2</mn></msub><mprescripts/><none/><mn>21</mn></mmultiscripts></math></span><span style=\"font-family: Times New Roman;\">&nbsp;= 210</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">16.</span><span style=\"font-family: Times New Roman;\"> In a certain code language, &lsquo;MANGO&rsquo; is coded as &lsquo;13-2-42-28-75&rsquo;and &lsquo;GRAPE&rsquo; is coded as &lsquo;7-36-3-64-25&rsquo;. How will &lsquo;APPLE&rsquo; be coded in that language? </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">16.</span><span style=\"font-family: Baloo;\"> एक निश्चित कूट भाषा में, \'MANGO\' को \'13-2-42-28-75\' के रूप में कोडित किया जाता है और \'GRAPE\' को \'7-36-3-64-25\' के रूप में कोडित किया जाता है। उसी भाषा में &lsquo;APPLE&rsquo; को किस प्रकार कोडित किया जाएगा?</span></p>",
                    options_en: ["<p>2 - 16 - 48 - 21 - 25</p>", "<p>1 - 32 - 48 - 48 - 25</p>", 
                                "<p>2 - 32 - 48 - 24 - 28</p>", "<p>1 - 32 - 24 - 21 - 30</p>"],
                    options_hi: ["<p>2 - 16 - 48 - 21 - 25</p>", "<p>1 - 32 - 48 - 48 - 25</p>",
                                "<p>2 - 32 - 48 - 24 - 28</p>", "<p>1 - 32 - 24 - 21 - 30</p>"],
                    solution_en: "<p>16.(b)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>Product of alphabetical order and the number at which they lie in the word.</span><br><span style=\"font-family: Times New Roman;\">So, APPLE is coded as 1 &times; 1, 16 &times; 2, 16 &times; 3, 12 &times; 4, 5 &times; 5 is 1 - 32 - 48 - 48 - 25.</span></p>",
                    solution_hi: "<p>16.(b)<br><span style=\"font-family: Baloo;\"><strong>तर्क: </strong>वर्णानुक्रम का गुणनफल और वह संख्या जिस पर वे शब्द स्थित हैं।</span><br><span style=\"font-family: Baloo;\">तो, APPLE को 1 &times; 1, 16 &times; 2, 16 &times; 3, 12 &times; 4, 5 &times; 5 के रूप में कोड किया गया है, 1 - 32 - 48 - 48 - 25 है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">17.</span><span style=\"font-family: Times New Roman;\"> Ram, Rajat and Ramesh are the three sons of Kavita. Mukul is the son of Ram. Mukul&rsquo;s paternal grandfather is Jay. Jay has one daughter, Nikita. Rajat&rsquo;s son, Abhiram is married to Rani and their daughter is Ina. Nirmala is the daughter of Nikita. How is Jay related to Ina? </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">17.</span><span style=\"font-family: Baloo;\"> राम, रजत और रमेश कविता के तीन पुत्र हैं। मुकुल राम का पुत्र है। मुकुल के दादा जय हैं। जय की एक बेटी निकिता है। रजत के बेटे, अभिराम की शादी रानी से हुई है और उनकी बेटी इना है। निर्मला निकिता की पुत्री है। जय इना से कैसे संबंधित है?</span></p>",
                    options_en: ["<p>Great grandfather</p>", "<p>Grandfather</p>", 
                                "<p>Paternal uncle</p>", "<p>Father-in-law</p>"],
                    options_hi: ["<p>परदादा<span style=\"font-family: Baloo;\"> </span></p>", "<p>दादाजी</p>",
                                "<p>पैतृक चाचा<span style=\"font-family: Baloo;\"> </span></p>", "<p>ससुर</p>"],
                    solution_en: "<p>17.(a)<br><img src=\"https://ssccglpinnacle.com/images/imagetools0.png\" width=\"221\" height=\"111\"><br><span style=\"font-family: Times New Roman;\">Family tree of the question shows that Jay is Ina&rsquo;s great grandfather.</span></p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://ssccglpinnacle.com/images/imagetools0.png\" width=\"193\" height=\"97\"><br><span style=\"font-family: Baloo;\">प्रश्न के वंशवृक्ष से पता चलता है कि जय इना के परदादा है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">18.</span><span style=\"font-family: Times New Roman;\"> In a certain code language, &lsquo;PLIGHT&rsquo; is written as &lsquo;JMQUIH&rsquo;. How will &lsquo;CANDLE&rsquo; be written in that language? </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">18.</span><span style=\"font-family: Baloo;\"> एक निश्चित कोड भाषा में, \'PLIGHT\' को \'JMQUIH\' के रूप में लिखा जाता है। उसी भाषा में \'CANDLE\' कैसे लिखा जाएगा?</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\">OBDFEM</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\">BODMFE</span><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\">ODBFME</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>OBDFME</p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\">OBDFEM</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\">BODMFE</span><span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\">ODBFME</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>OBDFME</p>"],
                    solution_en: "<p>18.(d)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>divide the word into two equal halves and then reverse each half and then use +1 pattern.</span><br><span style=\"font-family: Times New Roman;\">CANDLE after breaking CAN-DLE and after reversing NAC-ELD and after +1 it becomes OBD-FME. </span></p>",
                    solution_hi: "<p>18.(d)<br><span style=\"font-family: Baloo;\"><strong>तर्क: </strong>शब्द को दो बराबर हिस्सों में विभाजित करें और फिर प्रत्येक आधे को उलट दें और फिर +1 पैटर्न का उपयोग करें।</span><br><span style=\"font-family: Baloo;\">CAN-DLE को तोड़ने के बाद CANDLE और NAC-ELD को उलटने के बाद और 1 के बाद यह OBD-FME हो जाता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">19.</span><span style=\"font-family: Times New Roman;\"> Select the letter-cluster from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Times New Roman;\">ABD, CEI, EHN, ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">19.</span><span style=\"font-family: Baloo;\"> दिए गए विकल्पों में से उस अक्षर-समूह को चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।</span><br><span style=\"font-family: Times New Roman;\">ABD, CEI, EHN, ?</span></p>",
                    options_en: ["<p>GKS</p>", "<p>GIM</p>", 
                                "<p>FJR</p>", "<p>GHJ</p>"],
                    options_hi: ["<p>GKS</p>", "<p>GIM</p>",
                                "<p>FJR</p>", "<p>GHJ</p>"],
                    solution_en: "<p>19.(a)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>+2, +3, +5 pattern is followed</span><br><span style=\"font-family: Times New Roman;\">So, E +2 = G, H +3 = K and N +5 = S.</span><br><span style=\"font-family: Times New Roman;\">GKS is the correct answer.</span></p>",
                    solution_hi: "<p>19.(a)<br><span style=\"font-family: Baloo;\"><strong>तर्क :</strong> +2, +3, +5 पैटर्न का पालन किया जाता है</span><br><span style=\"font-family: Baloo;\">तो, E +2 = G, H +3 = K और N +5 = S।</span><br><span style=\"font-family: Baloo;\">GKS सही उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">20.</span><span style=\"font-family: Times New Roman;\"> Four number-pairs have been given, out of which three are alike in some manner and one is different. Select the number-pair that is different. </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">20.</span><span style=\"font-family: Baloo;\"> चार संख्या-जोड़े दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस संख्या-युग्म का चयन करें जो भिन्न है।</span></p>",
                    options_en: ["<p>(15, 105) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(18, 128)</p>", 
                                "<p>(17, 119) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(16, 112)</p>"],
                    options_hi: ["<p>(15, 105) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(18, 128)</p>",
                                "<p>(17, 119) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(16, 112)&nbsp;</p>"],
                    solution_en: "<p>20.(b)<br><span style=\"font-family: Times New Roman;\"><strong>Logic</strong> : n : 7n</span><br><span style=\"font-family: Times New Roman;\">But option (b) does not follow the same logic</span></p>",
                    solution_hi: "<p>20.(b)<br><span style=\"font-family: Baloo;\"><strong>तर्क : </strong>n : 7n</span><br><span style=\"font-family: Baloo;\">लेकिन विकल्प (b) समान तर्क का पालन नहीं करता है</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">21.</span><span style=\"font-family: Times New Roman;\"> Select the option in which the numbers are NOT related in the same way as are the </span><span style=\"font-family: Times New Roman;\">numbers of the following set.</span><br>(7,51,22)</p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">21.</span><span style=\"font-family: Baloo;\"> उस विकल्प का चयन करें जिसमें संख्याएँ उसी तरह संबंधित नहीं हैं जैसे निम्नलिखित सेट की संख्याएँ हैं।</span><br>(7,51,22)</p>",
                    options_en: ["<p>(11, 123, 26) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(3, 11, 18)</p>", 
                                "<p>(6, 38, 22) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(4, 18, 19)</p>"],
                    options_hi: ["<p>(11, 123, 26) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(3, 11, 18)</p>",
                                "<p>(6, 38, 22) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-weight: 400;\">(4, 18, 19)</span></p>"],
                    solution_en: "<p>21.(c)<br><span style=\"font-family: Times New Roman;\"><strong>Logic : </strong>(a,&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">+2, a+15)</span><br><span style=\"font-family: Times New Roman;\">But option c does not follow the pattern</span><br><span style=\"font-family: Times New Roman;\">As, 6 + 15 is 21 (not 22)</span></p>",
                    solution_hi: "<p>21.(c)<br><span style=\"font-family: Times New Roman;\"><strong>तर्क </strong>: (a,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">+2, a+15)</span><br>लेकिन विकल्प c पैटर्न का पालन नहीं करता है<br><span style=\"font-family: Times New Roman;\">&nbsp;6 + 15 = 21 (&ne; 22)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">22.</span><span style=\"font-family: Times New Roman;\"> Select the option that is related to the third word in the same way as the second word </span><span style=\"font-family: Times New Roman;\">is related to the first word.</span><br><span style=\"font-family: Times New Roman;\">Sheep : Lamb :: Cockroach : ? </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">22. </span><span style=\"font-family: Baloo;\">उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है।</span><br><span style=\"font-family: Baloo;\">भेड़ : मेमना :: तिलचट्टा : ?</span></p>",
                    options_en: ["<p>Larva <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>Caterpillar</p>", 
                                "<p>Nymph <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>Filly</p>"],
                    options_hi: ["<p>लार्वा</p>", "<p>कैटरपिलर</p>",
                                "<p>निम्फ़</p>", "<p>बछेड़ी</p>"],
                    solution_en: "<p>22.(c)<br><span style=\"font-family: Times New Roman;\">As young</span><span style=\"font-family: Times New Roman;\"> one of sheep is known </span><span style=\"font-family: Times New Roman;\">as lamp</span><span style=\"font-family: Times New Roman;\">, similarly a young</span><span style=\"font-family: Times New Roman;\"> one of</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">cockroach</span><span style=\"font-family: Times New Roman;\"> is known as Nymph.</span></p>",
                    solution_hi: "<p>22.(c)<br><span style=\"font-family: Baloo;\">जैसे भेड़ के बच्चे को मेमना कहा जाता है, उसी तरह तिलचट्टे के बच्चे को निम्फ़ कहा जाता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">23.</span><span style=\"font-family: Times New Roman;\"> Select the option in which the words share the same relationship as that shared by the given pair of words.</span><br><span style=\"font-family: Times New Roman;\">Flower : Floriculture </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">23.</span><span style=\"font-family: Baloo;\"> उस विकल्प का चयन करें जिसमें शब्द वही संबंध साझा करते हैं जो दिए गए शब्दों के जोड़े द्वारा साझा किए गए हैं।</span><br><span style=\"font-family: Baloo;\">फूल: फूलों की खेती</span></p>",
                    options_en: ["<p>Mushroom : Viticulture</p>", "<p>Honey : Horticulture</p>", 
                                "<p>Earthworm : Vermiculture</p>", "<p>Silk : Silviculture</p>"],
                    options_hi: ["<p>मशरूम: अंगूर की खेती</p>", "<p>शहद: बागवानी</p>",
                                "<p>केंचुआ: वर्मीकल्चर</p>", "<p>रेशम: सिल्वीकल्चर</p>"],
                    solution_en: "<p>23.(c)<br><span style=\"font-family: Times New Roman;\">Floriculture is the cultivation of the Flower in the same way Vermiculture is the cultivation of Earthworm.</span></p>",
                    solution_hi: "<p>23.(c)<br><span style=\"font-family: Baloo;\">पुष्पकृषि का संबंध फूलों की खेती से है उसी तरह वर्मीकल्चर का संबंध केंचुए की खेती से है। </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">24.</span><span style=\"font-family: Times New Roman;\"> Select the correct mirror image of the given figure when the mirror is placed to its right side.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image1.png\" /><span style=\"font-family: Times New Roman;\"> </span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">24.</span><span style=\"font-family: Baloo;\"> दी गई आकृति की सही दर्पण छवि का चयन करें जब दर्पण को इसके दाईं ओर रखा जाए।</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image1.png\" /><span style=\"font-family: Times New Roman;\"> </span></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image17.png\" width=\"75\" height=\"81\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image7.png\" width=\"75\" height=\"83\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image30.png\" width=\"75\" height=\"81\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image8.png\" width=\"88\" height=\"94\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image17.png\" width=\"75\" height=\"80\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image7.png\" width=\"75\" height=\"83\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image30.png\" width=\"75\" height=\"81\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image8.png\" width=\"83\" height=\"89\"></p>"],
                    solution_en: "<p>24.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image30.png\" /></p>",
                    solution_hi: "<p>24.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646718908/word/media/image30.png\" /></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> Select the option that is related to the fourth number in the same way as the first number is related to the second number and the fifth number is related to the sixth number.</span><br><span style=\"font-family: Times New Roman;\">396 : 24 : : ? : 28 : : 672 : 30 </span></p>",
                    question_hi: "<p>25<span style=\"font-family: Times New Roman;\">. </span><span style=\"font-family: Baloo;\">उस विकल्प का चयन करें जो चौथी संख्या से उसी प्रकार संबंधित है जैसे पहली संख्या दूसरी संख्या से संबंधित है और पांचवीं संख्या छठी संख्या से संबंधित है।</span><br><span style=\"font-family: Times New Roman;\">396 : 24 : : ? : 28 : : 672 : 30 </span></p>",
                    options_en: ["<p>536</p>", "<p>572</p>", 
                                "<p>588</p>", "<p>504</p>"],
                    options_hi: ["<p>536</p>", "<p>572</p>",
                                "<p>588</p>", "<p>504</p>"],
                    solution_en: "<p>25.(b)<br><span style=\"font-family: Times New Roman;\"><strong>Logic :</strong> first number = second number &times; (second number - 8) +12</span><br><span style=\"font-family: Times New Roman;\">In the same format : 28 &times; (28-8) + 12 = 28&times;20 + 12 = 572</span></p>",
                    solution_hi: "<p>25.(b)<br><span style=\"font-family: Baloo;\"><strong>तर्क :</strong> पहली संख्या = दूसरी संख्या &times; (दूसरी संख्या - 8) 12</span><br><span style=\"font-family: Baloo;\">उसी प्रारूप में : 28 &times; (28-8) + 12 = 28&times;20 + 12 = 572</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>