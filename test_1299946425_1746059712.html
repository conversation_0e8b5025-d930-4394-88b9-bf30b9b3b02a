<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Six numbers 1, 2, 3, 4, 5 and 6 are written on different faces of a dice. Two positions of this dice are shown in the following figure. Find the number on the face opposite to 2.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170343729.png\" alt=\"rId4\" width=\"187\" height=\"109\"></p>",
                    question_hi: "<p>1. एक पासे के अलग-अलग फलकों पर छ: संख्याएँ 1, 2, 3, 4, 5 और 6 लिखी गई हैं। दिए गए चित्र में इस पासे की दो स्थितियाँ दर्शाई गई हैं। 2 दर्शाने वाले फलक के विपरीत वाले फलक पर कौन-सी संख्या होगी ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170343729.png\" alt=\"rId4\" width=\"187\" height=\"109\"></p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>1.(b)<br>From the two dice the opposite face are <br>2<math display=\"inline\"><mo>&#8596;</mo></math> 5 , 3 &harr; 6, and 1 &harr; 4</p>",
                    solution_hi: "<p>1.(b)<br>दो पासों के विपरीत फलक हैं<br>2 <math display=\"inline\"><mo>&#8596;</mo></math> 5 , 3 &harr; 6 और 1 &harr; 4</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(76, 152, 38)<br>(84, 168, 42)</p>",
                    question_hi: "<p>2. उस समुच्चय का चयन करें जिसमें दी गई संख्याएँ आपस में उसी प्रकार संबंधित हैं, जिस प्रकार प्रश्न में दिए गए समुच्चय की संख्याएँ आपस में संबंधित हैं।<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएँ की जानी चाहिए। जैसे, 13 के मामले में - 13 पर की जाने वाली विभिन्न गणितीय संक्रियाएँ जैसे 13 में जोड़ना / घटाना / गुणा करना आदि 13 पर की जा सकती हैं। लेकिन 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)&nbsp;<br>(76, 152, 38) <br>(84, 168, 42)</p>",
                    options_en: [
                        "<p>(84, 178, 42)</p>",
                        "<p>(96, 184, 46)</p>",
                        "<p>(62, 132, 33)</p>",
                        "<p>(68, 136, 34)</p>"
                    ],
                    options_hi: [
                        "<p>(84, 178, 42)</p>",
                        "<p>(96, 184, 46)</p>",
                        "<p>(62, 132, 33)</p>",
                        "<p>(68, 136, 34)</p>"
                    ],
                    solution_en: "<p>2.(d) <strong>Logic :-</strong> (1st number - 3rd number) &times; 4 = 2nd number<br>(76, 152, 38) :- (76 - 38) &times; 4 &rArr; (38) &times; 4 = 152<br>(84, 168, 42) :- (84 - 42) &times; 4 &rArr; (42) &times; 4 = 168<br>Similarly,<br>(68, 136, 34) :- (68 - 34) &times; 4 &rArr; (34) &times; 4 = 136</p>",
                    solution_hi: "<p>2.(d) <strong>तर्क:- </strong>( पहली संख्या - तीसरी संख्या) &times; 4 = दूसरी संख्या<br>(76, 152, 38) :- (76 - 38) &times; 4 &rArr; (38) &times; 4 = 152<br>(84, 168, 42) :- (84 - 42) &times; 4 &rArr; (42) &times; 4 = 168<br>इसी प्रकार,<br>(68, 136, 34) :- (68 - 34) &times; 4 &rArr; (34) &times; 4 = 136</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. 45 is related to 100 following a certain logic. Following the same logic, 65 is related to 140. To which of the following is 105 related, following the same logic ?<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. एक निश्चित तर्क के आधार पर 45 का संबंध 100 से है। उसी तर्क के आधार पर 65 का संबंध 140 से है। समान तर्क के आधार पर निम्नलिखित में से किसका संबंध 105 से है ?&nbsp;<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>210</p>",
                        "<p>215</p>",
                        "<p>220</p>",
                        "<p>230</p>"
                    ],
                    options_hi: [
                        "<p>210</p>",
                        "<p>215</p>",
                        "<p>220</p>",
                        "<p>230</p>"
                    ],
                    solution_en: "<p>3.(c) <strong>Logic :-</strong> (1st number) &times; 2 + 10 = 2nd number<br>(45 , 100) :- (45) &times; 2 + 10 = 100<br>(65, 140) :- (65) &times; 2 + 10 = 140<br>Similarly,<br>(105, ?) :- (105) &times; 2 + 10 = 220</p>",
                    solution_hi: "<p>3.(c) <strong>तर्क :-</strong> (पहली संख्या) &times; 2 + 10 = दूसरी संख्या<br>(45 , 100) :- (45) &times; 2 + 10 = 100<br>(65, 140) :- (65) &times; 2 + 10 = 140<br>इसी प्रकार,<br>(105, ?) :- (105) &times; 2 + 10 = 220</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Three of the following four are alike in a certain way and thus form a group. Which is&nbsp;the one that does NOT belong to that group ?<br>(<strong>Note: </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>4. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें :</strong> असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>OXC</p>",
                        "<p>MVC</p>",
                        "<p>ENU</p>",
                        "<p>AJQ</p>"
                    ],
                    options_hi: [
                        "<p>OXC</p>",
                        "<p>MVC</p>",
                        "<p>ENU</p>",
                        "<p>AJQ</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170343913.png\" alt=\"rId5\" width=\"327\" height=\"71\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344042.png\" alt=\"rId6\" width=\"104\" height=\"68\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170343913.png\" alt=\"rId5\" width=\"327\" height=\"71\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344042.png\" alt=\"rId6\" width=\"104\" height=\"68\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. This question consists of a pair of words which have a certain relationship to each other. Select the pair which does NOT have the same relationship.<br>Smart : Intelligent<br>1) Liable : Accountable<br>2) Modest : Humble<br>3) Nimble : Sluggish<br>4) Outbreak : Eruption</p>",
                    question_hi: "<p>5. इस प्रश्न में शब्द -युग्म है जिनका एक दूसरे से एक निश्चित संबंध है। उस युग्म का चयन कीजिए जिसमें समान संबंध नहीं है।<br>चतुर : बुद्धिमान<br>1) उत्तरदायी : जवाबदेह<br>2) विनम्र : नम्र<br>3) फुर्तीला : सुस्त<br>4) प्रकोप : विस्फोट</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>5.(d) As the meaning of Smart : Intelligent is same, similarly the meaning of pairs of words in all the three options are same except Nimble : Sluggish.</p>",
                    solution_hi: "<p>5.(d) जिस प्रकार चतुर : बुद्धिमान का अर्थ एक ही है, उसी प्रकार फुर्तीला : सुस्त को छोड़कर तीनों विकल्पों में शब्द युग्मों का अर्थ भी एक ही है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the option that is related to the fifth letter cluster in the same way as the second letter cluster is related to the first letter cluster and the fourth letter cluster is related to the third letter cluster. <br>PLEASED : ELPADES : : HOWEVER : WOHEREV : : IMAGINE : ?</p>",
                    question_hi: "<p>6. उस विकल्प का चयन कीजिए, जिसका पाँचवें अक्षर-समूह से ठीक वही संबंध है, जो संबंध दूसरे अक्षर- समूह का पहले अक्षर-समूह से और वही संबंध चौथे अक्षर-समूह का तीसरे अक्षर-समूह से है। <br>PLEASED : ELPADES : : HOWEVER : WOHEREV : : IMAGINE : ?</p>",
                    options_en: [
                        "<p>MAIGEIN</p>",
                        "<p>AMIGENI</p>",
                        "<p>AIMGEIN</p>",
                        "<p>MAIGNEI</p>"
                    ],
                    options_hi: [
                        "<p>MAIGEIN</p>",
                        "<p>AMIGENI</p>",
                        "<p>AIMGEIN</p>",
                        "<p>MAIGNEI</p>"
                    ],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344171.png\" alt=\"rId7\" width=\"165\" height=\"91\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344374.png\" alt=\"rId8\" width=\"169\" height=\"91\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344547.png\" alt=\"rId9\" width=\"165\" height=\"90\"></p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344171.png\" alt=\"rId7\" width=\"165\" height=\"91\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344374.png\" alt=\"rId8\" width=\"169\" height=\"91\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344547.png\" alt=\"rId9\" width=\"165\" height=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, &lsquo;FLAKE&rsquo; is written as &lsquo;&amp;2348&rsquo; and &lsquo;LAKES&rsquo; is written as &lsquo;23@84&rsquo;. How will &lsquo;S&rsquo; be written in that language ?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में, &lsquo;FLAKE&rsquo; को &lsquo;&amp;2348&rsquo; के रूप में लिखा जाता है और &lsquo;LAKES&rsquo; को &lsquo;23@84&rsquo; के रूप में लिखा जाता है। उसी भाषा में &lsquo;S&rsquo; को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>&amp;</p>",
                        "<p>3</p>",
                        "<p>@</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>&amp;</p>",
                        "<p>3</p>",
                        "<p>@</p>"
                    ],
                    solution_en: "<p>7.(d)<br>F L A K E &rarr;&nbsp;&amp; 2 3 4 8 <br>L A K E S &rarr; 2 3 @ 8 4 <br>From the above codes the code for &lsquo;S&rsquo; is &lsquo;@&rsquo;.</p>",
                    solution_hi: "<p>7.(d)<br>F L A K E &rarr; &amp; 2 3 4 8 <br>L A K E S &rarr; 2 3 @ 8 4 <br>उपरोक्त कोड से &lsquo;S&rsquo; का कोड &lsquo;@&rsquo; है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, \'RADIO\' is written as \'SZEHP\' and \'FREQUENCY\' is written as \'GQFPVDOBZ\'. How will \'TELEVISION\' be written in that language ?</p>",
                    question_hi: "<p>8. एक निश्चित कोड भाषा में, \'RADIO\' को \'SZEHP\' और \'FREQUENCY\' को \'GQFPVDOBZ\' लिखा जाता है। उसी भाषा में \'TELEVISION\' कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>UDMDWHTHOM</p>",
                        "<p>UDMDWHTHPM</p>",
                        "<p>UDMDWHTHPN</p>",
                        "<p>UDMDWHHTPM</p>"
                    ],
                    options_hi: [
                        "<p>UDMDWHTHOM</p>",
                        "<p>UDMDWHTHPM</p>",
                        "<p>UDMDWHTHPN</p>",
                        "<p>UDMDWHHTPM</p>"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344794.png\" alt=\"rId11\" width=\"146\" height=\"89\">&nbsp; &nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344929.png\" alt=\"rId12\" width=\"263\" height=\"89\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345063.png\" alt=\"rId13\" width=\"287\" height=\"94\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344794.png\" alt=\"rId11\" width=\"146\" height=\"89\">&nbsp; &nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170344929.png\" alt=\"rId12\" width=\"263\" height=\"89\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345063.png\" alt=\"rId13\" width=\"287\" height=\"94\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What should come in place of ? in the given series based on the English alphabetical order ?<br>SLE CXS ? WVU GHI</p>",
                    question_hi: "<p>9. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?&rsquo; के स्थान पर क्या आना चाहिए ?<br>SLE CXS ? WVU GHI</p>",
                    options_en: [
                        "<p>COR</p>",
                        "<p>HAP</p>",
                        "<p>OWG</p>",
                        "<p>MJG</p>"
                    ],
                    options_hi: [
                        "<p>COR</p>",
                        "<p>HAP</p>",
                        "<p>OWG</p>",
                        "<p>MJG</p>"
                    ],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345341.png\" alt=\"rId15\" width=\"378\" height=\"129\"></p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345341.png\" alt=\"rId15\" width=\"378\" height=\"129\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In this question, three statements are given, followed by three conclusions numbered I, Il and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements.<br><strong>Statements :</strong><br>No pen is a scale.<br>All scales are erasers.<br>All pens are calculators.<br><strong>Conclusions :</strong><br>I. Some erasers are calculators.<br>Il. No pen is an eraser.<br>III. No scale is a calculator.</p>",
                    question_hi: "<p>10. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष ।,॥ और III दिए गए हैं। कथनों को सत्य मानते हुए भले ही वे सामान्य रूप से। तथ्यों से अलग प्रतीत होते हों, निर्धारित करें कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कोई कलम, पैमाना नहीं है।<br>सभी पैमाने, रबड़ हैं।<br>सभी कलमें, कैलकुलेटर हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ रबड़, कैलकुलेटर हैं।<br>II. कोई कलम, रबड़ नहीं है।<br>III. कोई पैमाना, कैलकुलेटर नहीं है।</p>",
                    options_en: [
                        "<p>Neither conclusions I, Il nor III follows.</p>",
                        "<p>All conclusions I, Il and III follow.</p>",
                        "<p>Both conclusions I and II follow.</p>",
                        "<p>Both conclusions II and III follow.</p>"
                    ],
                    options_hi: [
                        "<p>न तो निष्कर्ष।, ॥ न ही निष्कर्ष III अनुसरण करता है।</p>",
                        "<p>I, II और III, सभी निष्कर्ष अनुसरण करते हैं।</p>",
                        "<p>निष्कर्ष। और II, दोनों अनुसरण करते हैं।</p>",
                        "<p>निष्कर्ष॥ और III, दोनों अनुसरण करते हैं।</p>"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345451.png\" alt=\"rId16\" width=\"380\" height=\"113\"><br>Neither conclusion I, II nor III follow.</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345583.png\" alt=\"rId17\" width=\"424\" height=\"121\"><br>न तो निष्कर्ष।, ॥ न ही निष्कर्ष III अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What should come in place of the question mark (?) in the given series ?<br>3, 10, 24, ?, 73, 108, 150, 199</p>",
                    question_hi: "<p>11. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>3, 10, 24, ?, 73, 108, 150, 199</p>",
                    options_en: [
                        "<p>35</p>",
                        "<p>40</p>",
                        "<p>45</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>35</p>",
                        "<p>40</p>",
                        "<p>45</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345767.png\" alt=\"rId18\" width=\"322\" height=\"49\"></p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345767.png\" alt=\"rId18\" width=\"322\" height=\"49\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345895.png\" alt=\"rId19\" width=\"277\" height=\"93\"></p>",
                    question_hi: "<p>12. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170345895.png\" alt=\"rId19\" width=\"277\" height=\"93\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346008.png\" alt=\"rId20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346107.png\" alt=\"rId21\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346227.png\" alt=\"rId22\" width=\"107\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346331.png\" alt=\"rId23\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346008.png\" alt=\"rId20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346107.png\" alt=\"rId21\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346227.png\" alt=\"rId22\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346331.png\" alt=\"rId23\"></p>"
                    ],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346331.png\" alt=\"rId23\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346331.png\" alt=\"rId23\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language, &lsquo;D&rsquo; is coded as &lsquo;16&rsquo;, &lsquo;B&rsquo; is coded as &lsquo;4&rsquo; and &lsquo;T&rsquo; is coded as &lsquo;400&rsquo;. How will &lsquo;H&rsquo; be coded in the same language ?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में, \'D\' को \'16\' के रूप में कूटबद्ध किया जाता है, \'B\' को \'4\' के रूप में कूटबद्ध किया जाता है और \'T\' को \'400\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'H\' को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>64</p>",
                        "<p>25</p>",
                        "<p>36</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>64</p>",
                        "<p>25</p>",
                        "<p>36</p>"
                    ],
                    solution_en: "<p>13.(b)<br><strong>Logic :-</strong> number = (alphabetical place value)<sup>2</sup><br>D &rarr;&nbsp;(4)<sup>2</sup> = 16<br>B &rarr; (2)<sup>2</sup> = 4<br>T &rarr; (20)<sup>2</sup> = 400<br>Similarly<br>H &rarr; (8)<sup>2</sup> = 64</p>",
                    solution_hi: "<p>13.(b)<br><strong>तर्क :- </strong>संख्या = (वर्णमाला में स्थानीय मान)<sup>2</sup><br>D &rarr;&nbsp;(4)<sup>2</sup> = 16<br>B &rarr; (2)<sup>2</sup> = 4<br>T &rarr; (20)<sup>2</sup> = 400<br>इसी प्रकार <br>H &rarr; (8)<sup>2</sup> = 64</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language,<br>A + B means \'A is the daughter of B\'<br>A - B means \'A is the wife of B\'<br>A &times;&nbsp;B means \'A is the brother of B\'<br>A &divide; B means \'A is the father of B\'.<br>Based on the above, if P + Q &times; R - S &divide; T\' which of the following is true ?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में,<br>A + B का अर्थ \'A, B की बेटी है\' <br>A - B का अर्थ \'A, B की पत्नी है\' <br>A &times; B का अर्थ \'A, B का भाई है\' <br>A &divide; B का अर्थ \'A, B का पिता है\'।<br>उपरोक्त के आधार पर, यदि \'P + Q &times; R - S &divide; T\' तो निम्न में से कौन-सा सत्य है ?</p>",
                    options_en: [
                        "<p>T is S\'s Son,</p>",
                        "<p>P is the daughter of T\'s Mother\'s Brother.</p>",
                        "<p>P is the sister of T.</p>",
                        "<p>Q is the brother of S.</p>"
                    ],
                    options_hi: [
                        "<p>T, S का बेटा है।</p>",
                        "<p>P, T की मां के भाई की बेटी है।</p>",
                        "<p>P, T की बहन है।</p>",
                        "<p>Q, S का भाई है।</p>"
                    ],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346478.png\" alt=\"rId24\" width=\"190\" height=\"107\"><br>P is the daughter of T&rsquo;s Mother&rsquo;s Brother</p>",
                    solution_hi: "<p>14.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346478.png\" alt=\"rId24\" width=\"190\" height=\"107\"><br>P, T की माँ के भाई की बेटी है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the correct mirror image of the given combination when the mirror is placed at \'MN\' as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346633.png\" alt=\"rId25\" width=\"132\" height=\"105\"></p>",
                    question_hi: "<p>15. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन (combination) के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346633.png\" alt=\"rId25\" width=\"132\" height=\"105\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346710.png\" alt=\"rId26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346808.png\" alt=\"rId27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346905.png\" alt=\"rId28\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346999.png\" alt=\"rId29\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346710.png\" alt=\"rId26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346808.png\" alt=\"rId27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346905.png\" alt=\"rId28\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346999.png\" alt=\"rId29\"></p>"
                    ],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346710.png\" alt=\"rId26\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170346710.png\" alt=\"rId26\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the combination of letters that when sequentially placed in the blanks of the given series will logically complete the series.<br>AB_ Z&nbsp; &nbsp; DEF_&nbsp; &nbsp; &nbsp;G_ IX&nbsp; &nbsp; &nbsp; JKL_&nbsp; &nbsp; M _ OV</p>",
                    question_hi: "<p>16. अक्षरों के उस संयोजन का चयन कीजिए जिसे दी गई श्रृंखला के रिक्त स्थानों में क्रमिक रूप से रखे जाने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br>AB_ Z&nbsp; &nbsp; DEF_&nbsp; &nbsp; &nbsp;G_ IX&nbsp; &nbsp; &nbsp; JKL_&nbsp; &nbsp; M _ OV</p>",
                    options_en: [
                        "<p>CYHWN</p>",
                        "<p>CYGWN</p>",
                        "<p>YGHVN</p>",
                        "<p>CYHVN</p>"
                    ],
                    options_hi: [
                        "<p>CYHWN</p>",
                        "<p>CYGWN</p>",
                        "<p>YGHVN</p>",
                        "<p>CYHVN</p>"
                    ],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347131.png\" alt=\"rId30\" width=\"391\" height=\"121\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347131.png\" alt=\"rId30\" width=\"391\" height=\"121\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347247.png\" alt=\"rId31\" width=\"144\" height=\"123\"></p>",
                    question_hi: "<p>17. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347247.png\" alt=\"rId31\" width=\"144\" height=\"123\"></p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>14</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>14</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347385.png\" alt=\"rId32\" width=\"192\" height=\"188\"><br>ABO, AOC, ABC, DOB, DOC, DBC, ABD, ACD, FED, FGH, HIJ, AKJ, DLQ, PQR, RXY, AZY<br>There are 16 triangles.</p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347385.png\" alt=\"rId32\" width=\"192\" height=\"188\"><br>ABO, AOC, ABC, DOB, DOC, DBC, ABD, ACD, FED, FGH, HIJ, AKJ, DLQ, PQR, RXY, AZY<br>16 त्रिभुज हैं.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Shivani drives 17 km to the North, then drives 9 km East, and then drives 17 km South. How far is Shivani from the starting point when considered in a straight line ?</p>",
                    question_hi: "<p>18. शिवानी 17 km उत्तर की ओर ड्राइव करती है, फिर 9 km पूर्व की ओर ड्राइव करती है, और फिर 17 km दक्षिण की ओर ड्राइव करती है। जब एक सीधी रेखा में विचार किया जाता है तब शिवानी प्रारंभिक बिंदु से कितनी दूर है ?</p>",
                    options_en: [
                        "<p>26 km</p>",
                        "<p>9 km</p>",
                        "<p>17 km</p>",
                        "<p>12 km</p>"
                    ],
                    options_hi: [
                        "<p>26 km</p>",
                        "<p>9 km</p>",
                        "<p>17 km</p>",
                        "<p>12 km</p>"
                    ],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347630.png\" alt=\"rId33\" width=\"243\" height=\"185\"><br>She is 9 km away from the starting point.</p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347741.png\" alt=\"rId34\" width=\"297\" height=\"226\"><br>वह प्रारंभिक बिंदु से 9 किमी दूर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Sunita, Lokesh, Babita, Isha, Akram, Alok and Kiran are sitting around a circular table facing the centre (but not necessarily in the same order). Sunita is an immediate neighbour of Babita. Lokesh sits 2<sup>nd</sup> to the right of Babita. Isha sits 4<sup>th</sup> to the right of Akram. Alok sits 3<sup>rd</sup> to the right of Lokesh. Isha sits 2<sup>nd</sup> to the left of Alok. Who sits third to the right of Babita ?</p>",
                    question_hi: "<p>19. सुनीता, लोकेश, बबीता, ईशा, अकरम, आलोक और किरण एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। सुनीता, बबीता की निकटतम पड़ोसी है। लोकेश, बबीता के दाएँ से दूसरे स्थान पर बैठा है। ईशा, अकरम के दाएँ से चौथे स्थान पर बैठी है। आलोक, लोकेश के दाएँ से तीसरे स्थान पर बैठा है। ईशा, आलोक के बाएँ से दूसरे स्थान पर बैठी है। बबीता के दाएँ से तीसरे स्थान पर कौन बैठा/बैठी है ?</p>",
                    options_en: [
                        "<p>Alok</p>",
                        "<p>Isha</p>",
                        "<p>Lokesh</p>",
                        "<p>Kiran</p>"
                    ],
                    options_hi: [
                        "<p>आलोक</p>",
                        "<p>ईशा</p>",
                        "<p>लोकेश</p>",
                        "<p>किरण</p>"
                    ],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170347867.png\" alt=\"rId35\" width=\"248\" height=\"199\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348016.png\" alt=\"rId36\" width=\"239\" height=\"205\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. The position of how many letters will remain unchanged if each of the letters in the word DETAIL is arranged in the alphabetical order ?</p>",
                    question_hi: "<p>20. यदि शब्द DETAIL के प्रत्येक अक्षर को वर्णानुक्रम में व्यवस्थित किया जाए तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा ?</p>",
                    options_en: [
                        "<p>One</p>",
                        "<p>Three</p>",
                        "<p>Two</p>",
                        "<p>None</p>"
                    ],
                    options_hi: [
                        "<p>एक</p>",
                        "<p>तीन</p>",
                        "<p>दो</p>",
                        "<p>किसी का भी नहीं</p>"
                    ],
                    solution_en: "<p>20.(d) <br>D E T A I L<br>A D E I L T<br>We can clearly see that the positions of all the letters are changed.</p>",
                    solution_hi: "<p>20.(d)<br>D E T A I L<br>A D E I L T<br>हम स्पष्ट रूप से देख सकते है कि सभी अक्षरो का स्थान परिवर्तित है ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Which two signs should be interchanged to make the given equation correct ?<br>36 + 8 &times; 184 &minus; 23 &divide; 10 = 90</p>",
                    question_hi: "<p>21. दिए गए समीकरण को सही बनाने के लिए कौन से, दो चिह्नों को परस्&zwj;पर बदलना चाहिए ?<br>36 + 8 &times; 184 &minus; 23 &divide; 10 = 90</p>",
                    options_en: [
                        "<p>&divide; and &minus;</p>",
                        "<p>&divide; and &times;</p>",
                        "<p>+ and &times;</p>",
                        "<p>&minus; and +</p>"
                    ],
                    options_hi: [
                        "<p>&divide; और &minus;</p>",
                        "<p>&divide; और &times;</p>",
                        "<p>+ और &times;</p>",
                        "<p>&minus; और +</p>"
                    ],
                    solution_en: "<p>21.(a) <strong>Given :-</strong> 36 + 8 &times; 184 - 23 &divide;&nbsp;10 = 90<br>After checking all the options, only option (a) satisfied. After interchanging &divide; and - we get,<br>36 + 8 &times; 184 &divide; 23 - 10<br>36 + 8 &times; 8 - 10<br>36 + 64 - 10<br>36 + 54 = 90<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>21.(a) <strong>दिया गया :-</strong> 36 + 8 &times; 184 - 23 &divide;10 = 90<br>सभी विकल्पों की जांच करने पर विकल्प (a) संतुष्ट करता है। &divide; तथा - को आपस में बदलने के बाद हमें प्राप्त होता है,<br>36 + 8 &times; 184 &divide; 23 - 10<br>36 + 8 &times; 8 - 10<br>36 + 64 - 10<br>36 + 54 = 90<br>L.H.S. = R.H.S.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Identify the figure from among the given options which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348119.png\" alt=\"rId37\" width=\"377\" height=\"78\"></p>",
                    question_hi: "<p>22. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348119.png\" alt=\"rId37\" width=\"377\" height=\"78\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348217.png\" alt=\"rId38\" width=\"78\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348329.png\" alt=\"rId39\" width=\"77\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348428.png\" alt=\"rId40\" width=\"77\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348531.png\" alt=\"rId41\" width=\"77\" height=\"78\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348217.png\" alt=\"rId38\" width=\"77\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348329.png\" alt=\"rId39\" width=\"77\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348428.png\" alt=\"rId40\" width=\"77\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348531.png\" alt=\"rId41\" width=\"77\" height=\"78\"></p>"
                    ],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348645.png\" alt=\"rId42\" width=\"77\" height=\"78\"></p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348645.png\" alt=\"rId42\" width=\"77\" height=\"78\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. If \'A\' stands for &lsquo;&divide;&rsquo; \'B\' stands for, &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and \'D\' stands for &lsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation ?<br>17 C 69 A 3 D 22 B 1 = ?</p>",
                    question_hi: "<p>23. यदि \'A\' का अर्थ &lsquo;&divide;&rsquo;, \'B\' का अर्थ\' &lsquo;&times;&rsquo;, \'C\' का अर्थ &lsquo;+&rsquo;, और D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>17 C 69 A 3 D 22 B 1 = ?</p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>16</p>",
                        "<p>12</p>",
                        "<p>18</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>16</p>",
                        "<p>12</p>",
                        "<p>18</p>"
                    ],
                    solution_en: "<p>23.(d) <strong>Given :- </strong>17 C 69 A 3 D 22 B 1 <br>As per given instruction after interchanging letter with sign we get<br>17 + 69 &divide; 3 - 22 &times; 1<br>17 + 23 - 22 = 18</p>",
                    solution_hi: "<p>23.(d) <strong>दिया गया :- </strong>17 C 69 A 3 D 22 B 1 <br>दिए गए निर्देश के अनुसार अक्षर को चिह्न से बदलने पर हमें प्राप्त होता है<br>17 + 69 &divide; 3 - 22 &times; 1<br>17 + 23 - 22 = 18</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option in which the given figure X is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348790.png\" alt=\"rId43\" width=\"113\" height=\"132\"></p>",
                    question_hi: "<p>24. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति X निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348790.png\" alt=\"rId43\" width=\"113\" height=\"132\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348886.png\" alt=\"rId44\" width=\"87\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348985.png\" alt=\"rId45\" width=\"91\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349104.png\" alt=\"rId46\" width=\"87\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349202.png\" alt=\"rId47\" width=\"87\" height=\"85\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348886.png\" alt=\"rId44\" width=\"87\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170348985.png\" alt=\"rId45\" width=\"88\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349104.png\" alt=\"rId46\" width=\"88\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349202.png\" alt=\"rId47\" width=\"88\" height=\"86\"></p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349315.png\" alt=\"rId48\" width=\"102\" height=\"102\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349315.png\" alt=\"rId48\" width=\"102\" height=\"102\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. If 16 September 2010 is Thursday, then what will be the day of the week on 17 August 2017 ?</p>",
                    question_hi: "<p>25. यदि 16 सितंबर 2010 को गुरुवार है, तो 17 अगस्त 2017 को सप्ताह का कौन-सा दिन होगा ?</p>",
                    options_en: [
                        "<p>Monday</p>",
                        "<p>Wednesday</p>",
                        "<p>Sunday</p>",
                        "<p>Thursday</p>"
                    ],
                    options_hi: [
                        "<p>सोमवार</p>",
                        "<p>बुधवार</p>",
                        "<p>रविवार</p>",
                        "<p>गुरुवार</p>"
                    ],
                    solution_en: "<p>25.(d) 16 September 2010 is Thursday . We have to go to 2017, the number of odd days =&nbsp;+ 1 + 2 + 1 + 1 + 1 +2 + 1 = 9. On dividing 9 by 7 the remainder = 2. Thursday + 2 = Saturday .We have reached till 16 September, but we have to go back to 17 August, the number of day in between = 16 + 14 = 30. On dividing 30 by 7, the remainder is 2. Saturday - 2 = Thursday.</p>",
                    solution_hi: "<p>25.(d) 16 सितंबर 2010 को गुरुवार है. हमें 2017 में जाना है, विषम दिनों की संख्या =&nbsp;+ 1 + 2 + 1 + 1 + 1 +2 + 1 = 9. 9 को 7 से विभाजित करने पर शेषफल = 2. गुरुवार + 2 = शनिवार। हम 16 सितंबर तक पहुंच गए हैं, लेकिन हमें 17 अगस्त पर वापस जाना है, बीच में दिन की संख्या = 16 + 14 = 30। 30 को 7 से विभाजित करने पर शेषफल 2 आता है। शनिवार - 2 = गुरुवार।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Musician Bhajan Sopori was a Santoor maestro from the ______Gharana.</p>",
                    question_hi: "<p>26. संगीतकार भजन सोपोरी _______घराने के एक संतूर वादक थे।</p>",
                    options_en: [
                        "<p>Maihar</p>",
                        "<p>Benaras</p>",
                        "<p>Punjab</p>",
                        "<p>Sufiana</p>"
                    ],
                    options_hi: [
                        "<p>मैहर</p>",
                        "<p>बनारस</p>",
                        "<p>पंजाब</p>",
                        "<p>सूफियाना</p>"
                    ],
                    solution_en: "<p>26.(d) <strong>Sufiana. </strong>The Classical music of Kashmir is also known as &ldquo;Sufiana Mousiqui&rdquo;. Awards of Bhajan Sopori (Santoor player): Sangeet Natak Akademi Award (1992), Padma Shri (2004), Baba Allauddin Khan Award (2009), M N Mathur Award (2011). Maihar gharana was founded by Ustad Allaudin Khan in Madhya Pradesh. The Punjab gharana was founded by Lal Bhawani Singh. Benares gharana founded by Ram Sahai.</p>",
                    solution_hi: "<p>26.(d) <strong>सूफियाना। </strong>कश्मीर के शास्त्रीय संगीत को \"सूफियाना मौसिकी\" के नाम से भी जाना जाता है। भजन सोपोरी (संतूर वादक) के पुरस्कार: संगीत नाटक अकादमी पुरस्कार (1992), पद्म श्री (2004), बाबा अलाउद्दीन खान पुरस्कार (2009), एम एन माथुर पुरस्कार (2011)। मैहर घराने की स्थापना मध्य प्रदेश में उस्ताद अलाउद्दीन खान ने की थी। पंजाब घराने की स्थापना लाल भवानी सिंह ने की थी। बनारस घराने की स्थापना राम सहाय ने की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following is NOT a feature of a monopoly ?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन-सी एकाधिकार की विशेषता नहीं है ?</p>",
                    options_en: [
                        "<p>One seller and many buyers</p>",
                        "<p>Full control over the price by the seller</p>",
                        "<p>One buyer and many sellers</p>",
                        "<p>No close substitutes</p>"
                    ],
                    options_hi: [
                        "<p>एक विक्रेता और कई क्रेता</p>",
                        "<p>विक्रेता का कीमत पर पूर्ण नियंत्रण होना</p>",
                        "<p>एक क्रेता और कई विक्रेता</p>",
                        "<p>कोई नज़दीकी विकल्प उपलब्धन होना</p>"
                    ],
                    solution_en: "<p>27.(c)<strong> One buyer and many sellers.</strong> In a monopoly, the single entity controls the entire supply of the product or service, allowing it to set prices and control supply without competition. Monopsony is a market structure where there is only one buyer of a particular good or service, while many sellers exist. This is the opposite of a monopoly, where there is only one seller but many buyers.</p>",
                    solution_hi: "<p>27.(c) <strong>एक क्रेता और कई विक्रेता।</strong> एक मोनोपॉली (एकाधिकार) में, एक ही इकाई पूरी तरह से उत्पाद या सेवा की आपूर्ति को नियंत्रित करती है, जिससे वह बिना प्रतिस्पर्धा के कीमतें तय कर सकती है और आपूर्ति को नियंत्रित कर सकती है। मोनोप्सनी एक बाजार संरचना है जहाँ किसी विशेष वस्तु या सेवा का केवल एक खरीददार होता है, जबकि कई विक्रेता उपस्थित होते हैं। यह एकाधिकार के विपरीत है, जहाँ केवल एक विक्रेता होता है लेकिन कई खरीददार होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. When is National Mathematics Day celebrated in India ?</p>",
                    question_hi: "<p>28. भारत में राष्ट्रीय गणित दिवस कब मनाया जाता है ?</p>",
                    options_en: [
                        "<p>December 15</p>",
                        "<p>December 22</p>",
                        "<p>January 26</p>",
                        "<p>November 14</p>"
                    ],
                    options_hi: [
                        "<p>15 दिसंबर</p>",
                        "<p>22 दिसंबर</p>",
                        "<p>26 जनवरी</p>",
                        "<p>14 नवंबर</p>"
                    ],
                    solution_en: "<p>28.(b) <strong>December 22.</strong> National Mathematics Day is celebrated in honor of the birth anniversary of Srinivasa Ramanujan. This day, first declared in 2012 by Dr. Manmohan Singh, acknowledges Ramanujan\'s profound contributions to the field of mathematics.</p>",
                    solution_hi: "<p>28.(b) <strong>22 दिसंबर।</strong> राष्ट्रीय गणित दिवस श्रीनिवास रामानुजन की जयंती के उपलक्ष्य में मनाया जाता है। यह दिवस, जिसे पहली बार 2012 में डॉ. मनमोहन सिंह द्वारा घोषित किया गया था, गणित के क्षेत्र में रामानुजन के गहन योगदान को स्वीकार करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The famous Hindu Temple &lsquo;Koneswaram&rsquo; is located in which country ?</p>",
                    question_hi: "<p>29. प्रसिद्ध हिंदू मंदिर \'कोणेश्वरम (Koneswaram)\' किस देश में स्थित है ?</p>",
                    options_en: [
                        "<p>Pakistan</p>",
                        "<p>Nepal</p>",
                        "<p>Bhutan</p>",
                        "<p>Sri Lanka</p>"
                    ],
                    options_hi: [
                        "<p>पाकिस्तान</p>",
                        "<p>नेपाल</p>",
                        "<p>भूटान</p>",
                        "<p>श्रीलंका</p>"
                    ],
                    solution_en: "<p>29.(d) <strong>Sri Lanka. </strong>Koneswaram: This temple is considered to be one of the Pancha Ishwaram (one of the five abodes of Lord Shiva). Other temples of Sri Lanka - Dambulla Cave Temple, Muthiyangana Temple, Gangaramaya Temple, Naguleswaram Temple.</p>",
                    solution_hi: "<p>29.(d) <strong>श्रीलंका ।</strong> कोणेश्वरम: इस मंदिर को पंच ईश्वरम (भगवान शिव के पांच निवासों में से एक) में से एक माना जाता है। श्रीलंका के अन्य मंदिर - दांबुला गुफा मंदिर, मुथियांगना मंदिर, गंगारामया मंदिर, नागुलेश्वरम मंदिर।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Non-metals, are electronegative. They tend to form bonds by______electrons.</p>",
                    question_hi: "<p>30. अधातुएं विद्युत ऋणात्मक होती हैं। वे______आबंध बनाती है।</p>",
                    options_en: [
                        "<p>substituting</p>",
                        "<p>gaining</p>",
                        "<p>subtracting two</p>",
                        "<p>losing</p>"
                    ],
                    options_hi: [
                        "<p>इलेक्ट्रोनों को प्रतिस्थापित करके</p>",
                        "<p>इलेक्ट्रोनों को ग्रहण करके</p>",
                        "<p>दो इलेक्ट्रोनों को घटाकर</p>",
                        "<p>इलेक्ट्रोनों को त्याग कर</p>"
                    ],
                    solution_en: "<p>30.(b) <strong>gaining.</strong> Non-metals are electronegative, meaning they have a strong tendency to attract and gain electrons to achieve a stable electron configuration, typically forming anions (negatively charged ions) in the process.</p>",
                    solution_hi: "<p>30.(b) <strong>इलेक्ट्रोनों को ग्रहण करके। </strong>अधातुएं विद्युत-ऋणात्मक होती हैं, जिसका अर्थ है कि उनमें इलेक्ट्रॉनों को आकर्षित करने और ग्रहण करने की प्रबल प्रवृत्ति होती है, जिससे एक स्थिर इलेक्ट्रॉन विन्यास प्राप्त होता है, तथा इस प्रक्रिया में आमतौर पर ऋणायन (ऋणावेशित आयन) बनते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. The term \'scoop\' is related to which of the following games ?</p>",
                    question_hi: "<p>31. शब्द \'स्कूप\' निम्नलिखित में से किस खेल से संबंधित है ?</p>",
                    options_en: [
                        "<p>Boxing</p>",
                        "<p>Hockey</p>",
                        "<p>Badminton</p>",
                        "<p>Football</p>"
                    ],
                    options_hi: [
                        "<p>बॉक्सिंग</p>",
                        "<p>हॉकी</p>",
                        "<p>बैडमिंटन</p>",
                        "<p>फुटबॉल</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>Hockey.</strong> Important Sports-Related Terminologies : Hockey - Bully, Free Hits, Penalty Stroke, Penalty Corner, Field goal, Dribbling, Foot Foul, etc. Cricket - Bouncer, Boundary, Maiden, No ball, Powerplay, Six, Yorker etc. Football - Corner kick, Goalkeeper, red card, yellow card, Hat trick, Counterattack, etc. Tennis - Ace, Backhand, Deuce, Tiebreak, etc.</p>",
                    solution_hi: "<p>31.(b)<strong> हॉकी ।</strong> महत्वपूर्ण खेल-संबंधी शब्दावली: हॉकी - बुली, फ्री हिट्स, पेनल्टी स्ट्रोक, पेनल्टी कॉर्नर, फील्ड गोल, ड्रिब्लिंग, फुट फाउल, आदि। क्रिकेट - बाउंसर, बाउंड्री, मेडेन, नो बॉल, पावरप्ले, सिक्स, यॉर्कर आदि। फुटबॉल - कॉर्नर किक, गोलकीपर, लाल कार्ड, पीला कार्ड, हैट ट्रिक, काउंटर अटैक, आदि। टेनिस - ऐस, बैकहैंड, ड्यूस, टाईब्रेक, आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. A plant cell wall is mainly composed of :</p>",
                    question_hi: "<p>32. पादप कोशिका भित्ति मुख्य रूप से______की बनी होती है।</p>",
                    options_en: [
                        "<p>lipid</p>",
                        "<p>starch</p>",
                        "<p>cellulose</p>",
                        "<p>protein</p>"
                    ],
                    options_hi: [
                        "<p>लिपिड</p>",
                        "<p>स्टार्च</p>",
                        "<p>सेल्यूलोज</p>",
                        "<p>प्रोटीन</p>"
                    ],
                    solution_en: "<p>32.(c)<strong> Cellulose.</strong> It plays a structural role and provides rigidity and strength to the cell wall. A plant cell wall is a tough, protective structure that surrounds the plasma membrane of plant cells. Some common Homopolysaccharides : Starch - Storage of energy in plants. Pectin - It has a structural role such as holding cellulose fibrils together in plant cell walls. Chitin - It plays a structural role and provides rigidity to the exoskeleton of insects. Glycogen - Storage of energy in bacteria and animals.</p>",
                    solution_hi: "<p>32.(c) <strong>सेल्यूलोज।</strong> यह एक संरचनात्मक भूमिका निभाता है और कोशिका भित्ति को कठोरता और मजबूती प्रदान करता है। पादप कोशिका भित्ति एक कठोर, सुरक्षात्मक संरचना होती है जो पादप कोशिकाओं की प्लाज्मा झिल्ली को घेरे रहती है। कुछ सामान्य होमोपॉलीसेकेराइड: स्टार्च - पौधों में ऊर्जा का भंडारण। पेक्टिन - इसकी एक संरचनात्मक भूमिका होती है जैसे कि पादप कोशिका भित्ति में सेल्यूलोज तंतुओं को एक साथ रखना। काइटिन - यह एक संरचनात्मक भूमिका निभाता है और कीटों के बाह्यकंकाल को कठोरता प्रदान करता है। ग्लाइकोजन - बैक्टीरिया और जंतुओं में ऊर्जा का भंडारण।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. From the given alternatives, identify an inner planet.</p>",
                    question_hi: "<p>33. दिए गए विकल्पों में से आंतरिक ग्रह की पहचान करें।</p>",
                    options_en: [
                        "<p>Saturn</p>",
                        "<p>Jupiter</p>",
                        "<p>Mercury</p>",
                        "<p>Uranus</p>"
                    ],
                    options_hi: [
                        "<p>शनि</p>",
                        "<p>बृहस्पति</p>",
                        "<p>बुध</p>",
                        "<p>वरुण</p>"
                    ],
                    solution_en: "<p>33.(c)<strong> Mercury.</strong> Mercury, Venus, Earth, and Mars are called Inner Planets as they lie between the Sun and the Asteroid Belt. These first four planets are Terrestrial, meaning earth-like, made of rock and metals with high densities. The outer planets - Jupiter, Saturn, Uranus, and Neptune - are called Jovian or Gas Giants, meaning Jupiter-like, much larger than terrestrial planets, with thick atmospheres mostly of helium and hydrogen.</p>",
                    solution_hi: "<p>33.(c) <strong>बुध। </strong>बुध, शुक्र, पृथ्वी और मंगल को आंतरिक ग्रह कहा जाता है क्योंकि वे सूर्य और क्षुद्रग्रह बेल्ट के बीच स्थित हैं। ये पहले चार ग्रह स्थलीय हैं, अर्थात पृथ्वी जैसे, उच्च घनत्व वाले चट्टान और धातुओं से बने हैं। बाह्य ग्रह बृहस्पति, शनि, यूरेनस और नेपच्यून - को जोवियन या गैस दानव कहा जाता है, जिसका अर्थ है बृहस्पति के समान, स्थलीय ग्रहों की तुलना में बहुत बड़ा, तथा जिसका घना वायुमंडल मुख्यतः हीलियम और हाइड्रोजन से बना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who is the author of \'Half Girlfriend and One Indian Girl\' ?</p>",
                    question_hi: "<p>34. \'हाफ गर्लफ्रेंड एंड वन इंडियन गर्ल\' के लेखक कौन हैं ?</p>",
                    options_en: [
                        "<p>Amit Chaudhuri</p>",
                        "<p>Kiran Desai</p>",
                        "<p>Jeet Thayil</p>",
                        "<p>Chetan Bhagat</p>"
                    ],
                    options_hi: [
                        "<p>अमित चौधरी</p>",
                        "<p>किरण देसाई</p>",
                        "<p>जीत थायिल</p>",
                        "<p>चेतन भगत</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Chetan Bhagat : </strong>Other books written by him - &ldquo;Five point someone&rdquo;, &ldquo;The 3 Mistakes of my life&rdquo;, &ldquo;What Young India Wants&rdquo;, &ldquo;Girl in Room 105&rdquo;, &ldquo;Two States : The story of my marriage&rdquo;. Authors and their books : Amit Chaudhuri - &ldquo;A Strange and sublime address&rdquo;, &ldquo;Afternoon raag&rdquo;. Kiran Desai - &ldquo;The Inheritance of Loss&rdquo;, &ldquo;Hullabaloo in Guava Orchard&rdquo;, &ldquo;Emblems of Transformation&rdquo;. Jeet Thayil - &ldquo;Narcopolis&rdquo;, &ldquo;The Book of Chocolate Saints&rdquo;, &ldquo;These errors are correct&rdquo;.</p>",
                    solution_hi: "<p>34.(d) <strong>चेतन भगत:</strong> इनके द्वारा लिखी गई अन्य पुस्तकें - \"फाइव पॉइंट समवन\", \"द थ्री मिस्टेक्स ऑफ़ माई लाइफ\", \"व्हाट यंग इंडिया वांट्स\", \"गर्ल इन रूम 105\", \"टू स्टेट्स: द स्टोरी ऑफ माय मैरिज&rdquo;। लेखक और उनकी पुस्तकें: अमित चौधरी - \"ए स्ट्रेंज एंड सबलाइम एड्रेस \", \"आफ्टरनून राग\"। किरण देसाई - \"द इन्हेरिटेंस ऑफ लॉस\", \"हुल्लाबालू इन गुआवा ऑर्चार्ड\", \"एम्ब्लेम्स ऑफ ट्रांसफॉर्मेशन\"। जीत थायिल - \"नार्कोपोलिस\", \"द बुक ऑफ़ चॉकलेट सेंट्स\", \"दीज एरर्स आर करेक्ट\"।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. What is the average sea level pressure, according to meteorologists ?</p>",
                    question_hi: "<p>35. मौसम विज्ञानियों के अनुसार समुद्र तल पर औसत दाब कितना होता है ?</p>",
                    options_en: [
                        "<p>1013 mb</p>",
                        "<p>1000 mb</p>",
                        "<p>1030 mb</p>",
                        "<p>870 mb</p>"
                    ],
                    options_hi: [
                        "<p>1013 mb</p>",
                        "<p>1000 mb</p>",
                        "<p>1030 mb</p>",
                        "<p>870 mb</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>1013 mb. </strong>The scientific unit of pressure is the Pascal (Pa) named after Blaise Pascal. One pascal equals 0.01 millibar. Meteorology has used the millibar for air pressure since 1929.</p>",
                    solution_hi: "<p>35.(a) <strong>1013 mb।</strong> दाब की वैज्ञानिक इकाई पास्कल (Pa) है जिसका नाम ब्लेज़ पास्कल के नाम पर रखा गया है। एक पास्कल 0.01 मिलीबार के बराबर होता है। मौसम विज्ञान ने 1929 से वायुदाब के लिए मिलिबार का उपयोग किया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which rocket successfully launched ESA&rsquo;s Proba-3 mission on December 5, 2024 ?</p>",
                    question_hi: "<p>36. 5 दिसंबर 2024 को ESA के प्रोबा-3 मिशन को किस रॉकेट ने सफलतापूर्वक लॉन्च किया ?</p>",
                    options_en: [
                        "<p>PSLV-C57</p>",
                        "<p>PSLV-C59</p>",
                        "<p>GSLV-MkIII</p>",
                        "<p>PSLV-C60</p>"
                    ],
                    options_hi: [
                        "<p>PSLV-C57</p>",
                        "<p>PSLV-C59</p>",
                        "<p>GSLV-MkIII</p>",
                        "<p>PSLV-C60</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>PSLV-C59.</strong> This rocket, launched from ISRO&rsquo;s Satish Dhawan Space Centre in Sriharikota, carried ESA&rsquo;s Proba-3 satellites. Proba-3 mission aims to explore the Sun&rsquo;s outer atmosphere by conducting scientific experiments.</p>",
                    solution_hi: "<p>36.(b) <strong>PSLV-C59। </strong>यह रॉकेट, ISRO के सतीश धवन अंतरिक्ष केंद्र, श्रीहरिकोटा से लॉन्च किया गया, जिसने ESA के प्रोबा-3 उपग्रहों को वहन किया। प्रोबा-3 मिशन का उद्देश्य वैज्ञानिक प्रयोगों के माध्यम से सूर्य के बाहरी वातावरण का अध्ययन करना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who among the following was the successor of Mughal Emperor, Babur ?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन मुगल सम्राट बाबर का उत्तराधिकारी था ?</p>",
                    options_en: [
                        "<p>Shahjahan</p>",
                        "<p>Humayun</p>",
                        "<p>Jahangir</p>",
                        "<p>Aurangzeb</p>"
                    ],
                    options_hi: [
                        "<p>शाहजहाँ</p>",
                        "<p>हुमायूं</p>",
                        "<p>जहांगीर</p>",
                        "<p>औरंगज़ेब</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>Humayun.</strong> Babur (Founder of Mughal emperor) was descended from Timur and Genghis Khan. Key Battles by him : First Battle of Panipat (1526) - Defeated Ibrahim Lodi. Battle of Khanwa (1527) - Defeated Rana Sanga of Mewar and Afghan allies. Battle of Chanderi (1528) - Victory over Medini Rai. Battle of Ghagra (1529) - Defeated Afghan rebels led by Mahmud Lodi. Mughal emperors chronologically : Babur &rarr; Humayun &rarr; Akbar &rarr; Jahangir &rarr; Shah-Jahan &rarr; Aurangzeb etc.</p>",
                    solution_hi: "<p>37.(b) <strong>हुमायूं।</strong> बाबर (मुगल सम्राट का संस्थापक) तैमूर और चंगेज खान का वंशज था। उसके द्वारा लड़े गए प्रमुख युद्ध : पानीपत का पहला युद्ध (1526) - इब्राहिम लोदी को हराया। खानवा का युद्ध (1527) - मेवाड़ के राणा साँगा और अफगान सहयोगियों को हराया। चंदेरी का युद्ध (1528) - मेदिनी राय पर विजय। घाघरा का युद्ध (1529) - महमूद लोदी के नेतृत्व में अफगान विद्रोहियों को हराया। मुगल सम्राट कालानुक्रमिक रूप से : बाबर &rarr; हुमायूँ &rarr; अकबर &rarr; जहाँगीर &rarr; शाहजहाँ &rarr; औरंगज़ेब आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which Article of the Indian Constitution deals with the Governor\'s assent to Bills ?</p>",
                    question_hi: "<p>38. भारतीय संविधान का कौन-सा अनुच्छेद विधेयकों पर राज्यपाल की स्वीकृति से संबंधित है ?</p>",
                    options_en: [
                        "<p>Article 201</p>",
                        "<p>Article 164</p>",
                        "<p>Article 264</p>",
                        "<p>Article 200</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 201</p>",
                        "<p>अनुच्छेद 164</p>",
                        "<p>अनुच्छेद 264</p>",
                        "<p>अनुच्छेद 200</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Article 200 -</strong> It outlines the process for a Bill passed by the Legislative Assembly of a State to be presented to the Governor for assent, who may either give assent, withhold assent, reserve the Bill for consideration of the President, or return the Bill for reconsideration. Other Articles: Article 201 - President shall either give assents or withholds assent to the Bills reserved for his consideration. Article 164: Provisions related to ministers of the state.</p>",
                    solution_hi: "<p>38.(d) <strong>अनुच्छेद 200 -</strong> यह किसी राज्य की विधान सभा द्वारा पारित विधेयक को सहमति के लिए राज्यपाल के समक्ष प्रस्तुत करने की प्रक्रिया की रूपरेखा प्रस्तुत करता है, जो या तो स्वीकृति दे सकता है, स्वीकृति रोक सकता है, विधेयक को राष्ट्रपति के विचार के लिए आरक्षित कर सकता है, या विधेयक को पुनर्विचार के लिए वापस कर सकता है। अन्य अनुच्छेद: अनुच्छेद 201 - राष्ट्रपति अपने विचार के लिए आरक्षित विधेयकों पर या तो स्वीकृति देगा या स्वीकृति रोक देगा। अनुच्छेद 164: राज्य के मंत्रियों से संबंधित प्रावधान।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. &lsquo;Chang dance&rsquo; is popular in the state of:</p>",
                    question_hi: "<p>39. \'चांग नृत्य (Chang dance)\' किस राज्य में लोकप्रिय है ?</p>",
                    options_en: [
                        "<p>Goa</p>",
                        "<p>Bihar</p>",
                        "<p>Rajasthan</p>",
                        "<p>Tripura</p>"
                    ],
                    options_hi: [
                        "<p>गोवा</p>",
                        "<p>बिहार</p>",
                        "<p>राजस्थान</p>",
                        "<p>त्रिपुरा</p>"
                    ],
                    solution_en: "<p>39.(c)<strong> Rajasthan. </strong>The Chang dance, also known as Dhamal or Dhuff dance, is performed during the Hindu festival of Holi to celebrate the triumph of good over evil. It is popularly referred to as the Holi dance due to its association with the festival. Folk Dances in India: Rajasthan - Ghumar, Jhuma, Kalbeliya. Bihar - Jata-Jatin, Bidesia. Tripura- Hojagiri.</p>",
                    solution_hi: "<p>39.(c) <strong>राजस्थान।</strong> चांग नृत्य, जिसे धमाल या ढफ नृत्य के नाम से भी जाना जाता है, जो बुराई पर अच्छाई की जीत का जश्न मनाने के लिए होली के त्योहार के दौरान किया जाता है। होली के त्यौहार से जुड़े होने के कारण इसे लोकप्रिय रूप से होली नृत्य के रूप में जाना जाता है। भारत के लोक नृत्य: राजस्थान - घुमर, झूमा, कालबेलिया। बिहार - जट-जटिन, बिदेसिया। त्रिपुरा- होजागिरी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Who is the founder of modern micro finance ?</p>",
                    question_hi: "<p>40. आधुनिक सूक्ष्म वित्त (modern micro finance) के जनक कौन हैं ?</p>",
                    options_en: [
                        "<p>Muhammad Yunus</p>",
                        "<p>Rangarajan</p>",
                        "<p>YV Reddy</p>",
                        "<p>VKV Rao</p>"
                    ],
                    options_hi: [
                        "<p>मुहम्मद यूनुस</p>",
                        "<p>रंगराजन</p>",
                        "<p>वाई.वी. रेड्डी</p>",
                        "<p>वी.के.वी. राव</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Muhammad Yunus.</strong> Microfinance is a banking service that is provided to low-income individuals who have no other means of gaining financial services. Muhammad Yunus is a Bangladeshi economist, was awarded the Nobel Peace Prize in 2006 for founding the Grameen Bank (Founded - 1976, Dhaka, Bangladesh) and pioneering the concepts of microcredit and microfinance.</p>",
                    solution_hi: "<p>40.(a) <strong>मुहम्मद यूनुस।</strong> सूक्ष्म वित्त एक बैंकिंग सेवा है जो कम आय वाले व्यक्तियों को प्रदान की जाती है जिनके पास वित्तीय सेवाएँ प्राप्त करने का कोई अन्य साधन नहीं है। मुहम्मद यूनुस एक बांग्लादेशी अर्थशास्त्री हैं, जिन्हें ग्रामीण बैंक (स्थापना - 1976, ढाका, बांग्लादेश) की स्थापना और माइक्रो क्रेडिट तथा माइक्रो फाइनेंस की अवधारणाओं को आगे बढ़ाने के लिए 2006 में नोबेल शांति पुरस्कार से सम्मानित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Heptathlon for women includes _______events.</p>",
                    question_hi: "<p>41. महिलाओं के लिए हेप्टाथलॉन (Heptathlon) में _______स्पर्धा शामिल होती हैं।</p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>7. </strong>The &ldquo;combined events&rdquo; includes the decathlon for men and the heptathlon for women. Heptathlon (Women): Consists of 7 events - 100 m hurdles, high jump, shot put, 200 m, long jump, javelin throw, and 800 m. Decathlon (Men): Consists of 10 events - 100 m, shot put, high jump, long jump, 400 m, 110 m hurdles, discus throw, pole vault, javelin throw, and 1500 m.</p>",
                    solution_hi: "<p>41.(b) <strong>7.</strong> \"संयुक्त स्पर्धाओं\" में पुरुषों के लिए डेकाथलॉन और महिलाओं के लिए हेप्टाथलॉन शामिल हैं। हेप्टाथलॉन (महिला): इसमें 7 स्पर्धाएँ शामिल हैं - 100 मीटर बाधा दौड़, ऊंची कूद, शॉट पुट, 200 मीटर, लंबी कूद, भाला फेंक और 800 मीटर। डेकाथलॉन (पुरुष): इसमें 10 स्पर्धाएँ शामिल हैं - 100 मीटर, शॉट पुट, ऊंची कूद, लंबी कूद, 400 मीटर, 110 मीटर बाधा दौड़, डिस्कस थ्रो, पोल वॉल्ट, भाला फेंक और 1500 मीटर।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Compounds like gingerol, paradol, shogaols and zingerone are:</p>",
                    question_hi: "<p>42. जिंजरोल (gingerol), पैराडोल (paradol), शोगोल्स (shogaols) और जिंजरोन (zingerone) जैसे यौगिक किस प्रकार के यौगिक होते हैं ?</p>",
                    options_en: [
                        "<p>antifungal compounds found in ginger</p>",
                        "<p>antimicrobial compounds found in turmeric</p>",
                        "<p>antimicrobial compounds found in ginger</p>",
                        "<p>antimicrobial compounds found in citrus fruits</p>"
                    ],
                    options_hi: [
                        "<p>अदरक में पाए जाने वाले कवकरोधी यौगिक</p>",
                        "<p>हल्दी में पाए जाने वाले प्रतिरोगाणु यौगिक</p>",
                        "<p>अदरक में पाए जाने वाले प्रतिरोगाणु यौगिक</p>",
                        "<p>खट्टे फलों में पाए जाने वाले प्रतिरोगाणु यौगिक</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>Antimicrobial compounds found in ginger. </strong>Gingerol is derived from ginger (Zingiber officinale Roscoe), which also belongs to the Zingiberaceae family. Ginger has multiple bioactive constituents including gingerols, shogaols, zingerone, paradol and gingerdiol. Gingerols possess an alkyl moiety which imparts anti-oxidative potential to it.</p>",
                    solution_hi: "<p>42.(c) <strong>अदरक में पाए जाने वाले प्रतिरोगाणु यौगिक। </strong>जिंजरोल अदरक (ज़िंगिबर ऑफ़िसिनेल रोस्को) से प्राप्त होता है, जो ज़िंगिबरेसी परिवार से भी संबंधित है। अदरक में जिंजरोल, शोगोल, ज़िंगरोन, पैराडोल और जिंजरडिओल सहित कई बायोएक्टिव घटक होते हैं। जिंजरोल में एक एल्काइल मोइटी होती है जो इसे एंटी-ऑक्सीडेटिव क्षमता प्रदान करती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. On 22nd January 2024, Akashvani introduced which new digital channel, now accessible on YouTube and the News on AIR App ?</p>",
                    question_hi: "<p>43. 22 जनवरी 2024 को, आकाशवाणी ने कौन सा नया डिजिटल चैनल लॉन्च किया, जो अब YouTube और News on AIR ऐप पर उपलब्ध है ?</p>",
                    options_en: [
                        "<p>Sangeet Sagar</p>",
                        "<p>Aaradhana</p>",
                        "<p>Swaraj</p>",
                        "<p>Bhakti Dhara</p>"
                    ],
                    options_hi: [
                        "<p>संगीत सागर</p>",
                        "<p>आराधना</p>",
                        "<p>स्वराज</p>",
                        "<p>भक्ति धारा</p>"
                    ],
                    solution_en: "<p>43.(b) <strong>Aaradhana.</strong></p>",
                    solution_hi: "<p>43.(b) <strong>आराधना।</strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which is a colourless, odourless gas of the alkane series of hydrocarbons with a chemical formula of C<sub>3</sub>H<sub>8 </sub>?</p>",
                    question_hi: "<p>44. C<sub>3</sub>H<sub>8</sub> के रासायनिक सूत्र वाली, हाइड्रोकार्बन की एल्केन श्रृंखला की एक रंगहीन, गंधहीन गैस कौन-सी है ?</p>",
                    options_en: [
                        "<p>Ethane</p>",
                        "<p>Butane</p>",
                        "<p>Propane</p>",
                        "<p>Pentane</p>"
                    ],
                    options_hi: [
                        "<p>एथेन</p>",
                        "<p>ब्यूटेन</p>",
                        "<p>प्रोपेन</p>",
                        "<p>पेन्टेन</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Propane.</strong> Its main uses include home and water heating, cooking and refrigerating food, clothes drying, and powering farm and industrial equipment. The chemical formula for ethane is C<sub>2</sub>H<sub>6</sub>, for butane is C<sub>4</sub>H<sub>10</sub>, and for pentane is C<sub>5</sub>H<sub>12</sub>.</p>",
                    solution_hi: "<p>44.(c) <strong>प्रोपेन।</strong> इसके मुख्य उपयोगों में होम और वाटर हीटिंग, कुकिंग और फूड रेफ्रीजेरेटिंग, कपड़े सुखाना (clothes drying), तथा कृषि और औद्योगिक उपकरणों को शक्ति प्रदान करना शामिल है। इथेन का रासायनिक सूत्र C<sub>2</sub>H<sub>6</sub> है, ब्यूटेन का C<sub>4</sub>H<sub>10</sub> है, और पेंटेन का C<sub>5</sub>H<sub>12</sub> है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. &lsquo;Anup Rag Vilas\' is a book written by which of the following musicians ?</p>",
                    question_hi: "<p>45. निम्नलिखित में से किस संगीतकार ने अनूप राग विलास नामक पुस्तक लिखी है ?</p>",
                    options_en: [
                        "<p>Pandit Kumar Gandharva</p>",
                        "<p>Pandit Jasraj</p>",
                        "<p>Pandit Birju Maharaj</p>",
                        "<p>Pandit Dinkar Kaikini</p>"
                    ],
                    options_hi: [
                        "<p>पंडित कुमार गंधर्व</p>",
                        "<p>पंडित जसराज</p>",
                        "<p>पंडित बिरजू महाराज</p>",
                        "<p>पंडित दिनकर कैकिनी</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Pandit Kumar Gandharva.</strong> He was conferred the title &lsquo;Kumar Gandharva&rsquo; by the Shankaracharya. His Awards: Padma Bhushan (1977) and Padma Vibhushan (1990). Other books and authors: Pandit Jasraj - Rasraj. Pandit Birju Maharaj - The Master Through My Eyes, Rasgunjan. Pandit Dinkar Kaikini - Living Music.</p>",
                    solution_hi: "<p>45.(a) <strong>पंडित कुमार गंधर्व।</strong> उन्हें शंकराचार्य द्वारा \'कुमार गंधर्व\' की उपाधि दी गई थी। उनके पुरस्कार: पद्म भूषण (1977) और पद्म विभूषण (1990)। अन्य लेखक एवं पुस्तकें: पंडित जसराज - रसराज। पंडित बिरजू महाराज - द मास्टर थ्रू माई आईज, रसगुंजन। पंडित दिनकर कैकिनी - लिविंग म्यूजिक।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. The first train steamed off from Mumbai to ______.</p>",
                    question_hi: "<p>46. पहली ट्रेन मुंबई से ______के लिए रवाना हुई थी।</p>",
                    options_en: [
                        "<p>Pune</p>",
                        "<p>Malad</p>",
                        "<p>Thane</p>",
                        "<p>Nagpur</p>"
                    ],
                    options_hi: [
                        "<p>पुणे</p>",
                        "<p>मलाड</p>",
                        "<p>ठाणे</p>",
                        "<p>नागपुर</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>Thane. </strong>The first passenger train in India departed from Mumbai to Thane on April 16, 1853, covering a distance of 34 kilometers. It was operated by three locomotives named Sahib, Sultan, and Sindh, under the Great Indian Peninsula (GIP) Railway company. The concept of laying a railway line between Mumbai and Thane, extending to Kalyan and Bhor Ghat, was proposed by George Clark in 1843. Lord Dalhousie is recognized as the father of Indian Railways, while John Mathai was the first railway minister of India.</p>",
                    solution_hi: "<p>46.(c) <strong>ठाणे।</strong> भारत में पहली यात्री रेलगाड़ी 16 अप्रैल, 1853 को मुंबई से ठाणे के लिए रवाना हुई, जिसने 34 किलोमीटर की दूरी तय की थी। इसे ग्रेट इंडियन पेनिनसुला (GIP) रेलवे कंपनी के तहत साहिब, सुल्तान और सिंध नामक तीन इंजनों द्वारा संचालित किया गया था। मुंबई और ठाणे के बीच कल्याण और भोर घाट तक फैली रेलवे लाइन बिछाने की अवधारणा 1843 में जॉर्ज क्लार्क द्वारा प्रस्तावित की गई थी। लॉर्ड डलहौजी को भारतीय रेलवे का जनक माना जाता है, जबकि जॉन मथाई भारत के पहले रेल मंत्री थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. From which country were the ideals of justice of the Indian Constitution borrowed ?</p>",
                    question_hi: "<p>47. भारतीय संविधान के न्याय के आदर्श किस देश से गृहीत किए गए थे ?</p>",
                    options_en: [
                        "<p>The USSR</p>",
                        "<p>The UK</p>",
                        "<p>The US</p>",
                        "<p>Japan</p>"
                    ],
                    options_hi: [
                        "<p>यूएसएसआर (USSR)</p>",
                        "<p>यूके (UK)</p>",
                        "<p>यूएस (US)</p>",
                        "<p>जापान</p>"
                    ],
                    solution_en: "<p>47.(a)<strong> The USSR.</strong> Borrowed features of the Constitution : USSR (Now Russia) - Fundamental duties, and The ideals of justice (social, economic, and political), expressed in the Preamble. Britain - Parliamentary government, Rule of Law, Legislative procedure, Single citizenship, Cabinet system, Prerogative writs, Parliamentary privileges, Bicameralism. Japan - Concept of &ldquo;procedure established by Law&rdquo;. United States of America - Impeachment of the president, Functions of president and vice-president, Removal of Supreme Court and High court judges, Fundamental Rights, Judicial review, Independence of judiciary, The preamble of the constitution.</p>",
                    solution_hi: "<p>47.(a) <strong>यूएसएसआर (USSR)। </strong>संविधान की अपनाई गई विशेषताएँ: USSR (अब रूस) - मौलिक कर्तव्य, और न्याय के आदर्श (सामाजिक, आर्थिक और राजनीतिक), प्रस्तावना में व्यक्त किए गए। ब्रिटेन - संसदीय सरकार, विधि का शासन, विधायी प्रक्रिया, एकल नागरिकता, कैबिनेट प्रणाली, विशेषाधिकार रिट, संसदीय विशेषाधिकार, द्विसदनीयता। जापान - \"विधि द्वारा स्थापित प्रक्रिया\" की अवधारणा। संयुक्त राज्य अमेरिका - राष्ट्रपति का महाभियोग, राष्ट्रपति और उपराष्ट्रपति के कार्य, सर्वोच्च न्यायालय और उच्च न्यायालय के न्यायाधीशों को हटाना, मौलिक अधिकार, न्यायिक समीक्षा, न्यायपालिका की स्वतंत्रता, संविधान की प्रस्तावना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following pacts suspended the Civil Disobedience Movement ?</p>",
                    question_hi: "<p>48. निम्नलिखित में से किस समझौते ने सविनय अवज्ञा आंदोलन (Civil Disobedience Movement) को स्थगित कर दिया था ?</p>",
                    options_en: [
                        "<p>Liaquat&ndash;Nehru Pact</p>",
                        "<p>Sirima&ndash;Gandhi Pact</p>",
                        "<p>Gandhi-Irwin Pact</p>",
                        "<p>Rajah-Moonje Pact</p>"
                    ],
                    options_hi: [
                        "<p>लियाकत-नेहरू समझौता</p>",
                        "<p>सिरीमा-गांधी समझौता</p>",
                        "<p>गांधी-इरविन समझौता</p>",
                        "<p>राजा-मुंजे समझौता</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>Gandhi-Irwin Pact </strong>(March 5, 1931),. This pact aimed to suspend the civil disobedience movement and to ensure Indian National Congress participation in the second Round Table Conference. Civil Disobedience Movement was a non-violent movement which began in 1930 and was led by Mahatma Gandhi. Liaquat&ndash;Nehru Pact (1950) - It was a bilateral agreement signed between India and Pakistan. Raja-Munje Pact In 1932, Raja and Moonje entered into an agreement providing for reserved seats for depressed classes.</p>",
                    solution_hi: "<p>48.(c) <strong>गांधी-इरविन समझौता </strong>(5 मार्च, 1931) । इस समझौते का उद्देश्य सविनय अवज्ञा आंदोलन को स्थगित करना और दूसरे गोलमेज सम्मेलन में भारतीय राष्ट्रीय कांग्रेस की भागीदारी सुनिश्चित करना था। सविनय अवज्ञा आंदोलन एक अहिंसक आंदोलन था जो 1930 में शुरू हुआ और इसका नेतृत्व महात्मा गांधी ने किया था। लियाकत-नेहरू समझौता (1950) - यह भारत और पाकिस्तान के बीच हस्ताक्षरित एक द्विपक्षीय समझौता था । राजा-मुंजे समझौता 1932 में, राजा और मुंजे ने दलित वर्गों के लिए आरक्षित सीटों का प्रावधान करते हुए एक समझौता किया था</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Sri Lanka is separated from India by a narrow channel of sea, formed by the ______ and the Gulf of Mannar.</p>",
                    question_hi: "<p>49. श्रीलंका, भारत से समुद्र के एक संकीर्ण चैनल द्वारा अलग होता है, जो ______और मन्नार की खाड़ी द्वारा निर्मित होता है।</p>",
                    options_en: [
                        "<p>Palk Strait</p>",
                        "<p>Bering Strait</p>",
                        "<p>Strait of Malacca</p>",
                        "<p>Strait of Gibraltar</p>"
                    ],
                    options_hi: [
                        "<p>पाक जलडमरूमध्य</p>",
                        "<p>बेरिंग जलडमरूमध्य</p>",
                        "<p>मलक्का जलडमरूमध्य</p>",
                        "<p>जिब्राल्टर जलडमरूमध्य</p>"
                    ],
                    solution_en: "<p>49.(a) <strong>Palk Strait : </strong>It is bordered by Pamban Island (India), Adam&rsquo;s (Rama&rsquo;s) Bridge, the Gulf of Mannar, and Mannar Island (Sri Lanka). Other Important Straits : Bering Strait - Between Alaska and Russia, connects the Pacific and Arctic oceans; Strait of Malacca - Connects the Andaman Sea (Indian Ocean) and the South China Sea (Pacific Ocean); Strait of Gibraltar - Separates Europe from Africa and connects the Mediterranean Sea to the Atlantic Ocean.</p>",
                    solution_hi: "<p>49.(a) <strong>पाक जलडमरूमध्य:</strong> यह पम्बन द्वीप (भारत), एडम्स (राम) ब्रिज, मन्नार की खाड़ी और मन्नार द्वीप (श्रीलंका) से घिरा है। अन्य महत्वपूर्ण जलडमरूमध्य: बेरिंग जलडमरूमध्य - अलास्का और रूस के बीच, प्रशांत एवं आर्कटिक महासागरों को जोड़ता है; मलक्का जलडमरूमध्य - अंडमान सागर (हिंद महासागर) और दक्षिण चीन सागर (प्रशांत महासागर) को जोड़ता है; जिब्राल्टर जलडमरूमध्य - यूरोप को अफ्रीका से अलग करता है और भूमध्य सागर को अटलांटिक महासागर से जोड़ता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which one of the following statements regarding magnetic field is correct ?</p>",
                    question_hi: "<p>50. चुंबकीय क्षेत्र के संबंध में निम्नलिखित में से कौन-सा कथन सही है ?</p>",
                    options_en: [
                        "<p>Magnetic field lines are open curves</p>",
                        "<p>Magnetic field lines are closed curves</p>",
                        "<p>Magnetic field is a quantity that has no direction and magnitude</p>",
                        "<p>two magnetic field lines are found to cross each other</p>"
                    ],
                    options_hi: [
                        "<p>चुंबकीय क्षेत्र रेखाएं खुले वक्र बनाती हैं</p>",
                        "<p>चुंबकीय क्षेत्र रेखाएं बंद वक्र बनाती हैं</p>",
                        "<p>चुंबकीय क्षेत्र एक ऐसी मात्रा है जिसकी कोई दिशा और परिमाण नहीं होता है</p>",
                        "<p>दो चुंबकीय क्षेत्र रेखाएं एक दूसरे को काटती हुई पाई जाती हैं</p>"
                    ],
                    solution_en: "<p>50.(b) Magnetic field lines are imaginary lines around a magnet that represent the pattern of the magnetic field. They form closed loops, flowing from the North pole to the South pole outside the magnet, and from the South pole to the North pole inside the magnet. The magnetic field is a vector quantity, meaning it has both direction and magnitude.</p>",
                    solution_hi: "<p>50.(b) चुंबकीय क्षेत्र रेखाएँ चुंबक के चारों ओर काल्पनिक रेखाएँ होती हैं जो चुंबकीय क्षेत्र के पैटर्न को दर्शाती हैं। वे बंद लूप बनाते हैं, जो चुंबक के बाहर उत्तरी ध्रुव से दक्षिणी ध्रुव की ओर तथा चुंबक के अंदर दक्षिणी ध्रुव से उत्तरी ध्रुव की ओर प्रवाहित होते हैं। चुंबकीय क्षेत्र एक सदिश राशि है, जिसका अर्थ है कि इसमें दिशा और परिमाण दोनों होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. When 2<sup>256</sup> is divided by 17, the remainder would be:</p>",
                    question_hi: "<p>51. 2<sup>256</sup> को 17 से भाग देने पर कितना शेषफल बचेगा ?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>51.(c) <br>Rem (<math display=\"inline\"><mfrac><mrow><msup><mrow><mn>2</mn></mrow><mrow><mn>256</mn></mrow></msup></mrow><mrow><mn>17</mn></mrow></mfrac></math>) = Rem (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><msup><mn>2</mn><mn>4</mn></msup><mo>)</mo></mrow><mn>64</mn></msup><mn>17</mn></mfrac></math>)<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>64</mn></msup><mn>17</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>17</mn></mfrac></math> = 1</p>",
                    solution_hi: "<p>51.(c) <br>शेषफल (<math display=\"inline\"><mfrac><mrow><msup><mrow><mn>2</mn></mrow><mrow><mn>256</mn></mrow></msup></mrow><mrow><mn>17</mn></mrow></mfrac></math>) = शेषफल (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><msup><mn>2</mn><mn>4</mn></msup><mo>)</mo></mrow><mn>64</mn></msup><mn>17</mn></mfrac></math>)<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>64</mn></msup><mn>17</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>17</mn></mfrac></math> = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The sum of two numbers is 76. Three times the larger number is 46 more than four times the smaller one. Find the two numbers.</p>",
                    question_hi: "<p>52. दो संख्याओं का योग 76 है। बड़ी संख्या का तीन गुना, छोटी संख्या के चार गुने से 46 अधिक है। दोनों संख्याएँ ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>44 and 32</p>",
                        "<p>50 and 26</p>",
                        "<p>40 and 36</p>",
                        "<p>35 and 41</p>"
                    ],
                    options_hi: [
                        "<p>44 और 32</p>",
                        "<p>50 और 26</p>",
                        "<p>40 और 36</p>",
                        "<p>35 और 41</p>"
                    ],
                    solution_en: "<p>52.(b) <br>Let the larger no and smaller no be x and y respectively<br>x + y = 76 ----------- (1)<br>3x - 4y = 46 ----------- (2)<br>Solving eqn (1) &amp; (2) we have ;<br>x = 50, y = 26</p>",
                    solution_hi: "<p>52.(b) <br>माना कि बड़ी संख्या और छोटी संख्या क्रमशः x और y हैं<br>x + y = 76 ----------- (1)<br>3x - 4y = 46 ----------- (2)<br>समीकरण (1) और (2) को हल करने पर हमे प्राप्त होता है<br>x = 50, y = 26</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. 4 bells ring at intervals of 6, 8, 9 and 10 seconds. All the bells ring at the same time. After how many minutes will they ring together again ?</p>",
                    question_hi: "<p>53. 4 घंटियां 6, 8, 9 और 10 सेकंड के अंतराल पर बजती हैं। सभी घंटियां एक ही समय पर बजती हैं। कितने मिनट के बाद वे पुनः एक साथ बजेंगी ?</p>",
                    options_en: [
                        "<p>15 min</p>",
                        "<p>10 min</p>",
                        "<p>8 min</p>",
                        "<p>6 min</p>"
                    ],
                    options_hi: [
                        "<p>15 मिनट</p>",
                        "<p>10 मिनट</p>",
                        "<p>8 मिनट</p>",
                        "<p>6 मिनट</p>"
                    ],
                    solution_en: "<p>53.(d)<br>LCM of (6, 8, 9, 10) = 360 sec. = 6 min.<br>So, after 6 min. bells will ring together again.</p>",
                    solution_hi: "<p>53.(d)<br>(6, 8, 9, 10) का LCM = 360 सेकंड = 6 मिनट<br>तो, 6 मिनट के बाद घंटियाँ फिर से एक साथ बजेगी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Simplify the given expression.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mo>{</mo><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mo>(</mo><mn>7</mn><mo>-</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow><mrow><mn>5</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>5</mn><mi>o</mi><mi>f</mi><mn>5</mn></mrow></mfrac></math></p>",
                    question_hi: "<p>54. दिए गए व्यंजक को सरल कीजिए।<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mo>{</mo><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mo>(</mo><mn>7</mn><mo>-</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow><mrow><mn>5</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>5</mn><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mn>5</mn></mrow></mfrac></math></p>",
                    options_en: [
                        "<p>13</p>",
                        "<p>39</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>13</p>",
                        "<p>39</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>54.(c)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mo>{</mo><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mo>(</mo><mn>7</mn><mo>-</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow><mrow><mn>5</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>5</mn><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>5</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mrow><mo>{</mo><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mo>(</mo><mo>-</mo><mn>2</mn><mo>)</mo><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow></mrow><mrow><mn>5</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>25</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mo>{</mo><mn>6</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow><mrow><mn>5</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mo>{</mo><mn>6</mn><mo>+</mo><mn>24</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow><mn>6</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mn>39</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>18</mn></mfrac></math></p>",
                    solution_hi: "<p>54.(c)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mo>{</mo><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mo>(</mo><mn>7</mn><mo>-</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow><mrow><mn>5</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>5</mn><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mn>5</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mrow><mo>{</mo><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mo>(</mo><mo>-</mo><mn>2</mn><mo>)</mo><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow></mrow><mrow><mn>5</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>25</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mo>{</mo><mn>6</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow><mrow><mn>5</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mo>{</mo><mn>6</mn><mo>+</mo><mn>24</mn><mo>+</mo><mn>9</mn><mo>}</mo></mrow><mn>6</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#247;</mo><mn>39</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>18</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If <math display=\"inline\"><mi>x</mi></math> = a sec&theta;cosϕ, y = b sec&theta;sinϕ and z = c tan&theta;, then the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><msup><mi>a</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><msup><mi>c</mi><mn>2</mn></msup></mfrac></math>) is equal to:</p>",
                    question_hi: "<p>55. यदि&nbsp;x = a sec&theta;cosϕ, y = b sec&theta;sinϕ और z = c tan&theta; है, तो (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><msup><mi>a</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><msup><mi>c</mi><mn>2</mn></msup></mfrac></math>) का मान ______होगा।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>",
                        "<p>0</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>",
                        "<p>0</p>"
                    ],
                    solution_en: "<p>55.(b)<br><math display=\"inline\"><mi>x</mi></math> = a Sec&theta;Cos&Oslash;<br>On squaring both side<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = Sec<sup>2</sup>&theta;Cos<sup>2</sup>&Oslash; &hellip; (i)<br><math display=\"inline\"><mi>y</mi></math> = b Sec&theta;Sin&Oslash;<br>On squaring both side<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = Sec<sup>2</sup>&theta;Sin<sup>2</sup>&Oslash; &hellip;. (ii)<br><math display=\"inline\"><mi>z</mi></math> = c tan&theta;<br>On squaring both side<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>z</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = tan<sup>2</sup>&theta;&hellip; (iii)<br>On adding (i), (ii) and then subtracting (iii)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><msup><mi>a</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><msup><mi>c</mi><mn>2</mn></msup></mfrac></math> = Sec<sup>2</sup>&theta;Cos<sup>2</sup>&Oslash; + Sec<sup>2</sup>&theta;Sin<sup>2</sup>&Oslash; - tan<sup>2</sup>&theta;<br>= Sec<sup>2</sup>&theta;(Cos<sup>2</sup>&Oslash; + Sin<sup>2</sup>&Oslash;) - tan<sup>2</sup>&theta;<br>= Sec<sup>2</sup>&theta; - tan<sup>2</sup>&theta;<br>= 1</p>",
                    solution_hi: "<p>55.(b)<br><math display=\"inline\"><mi>x</mi></math> = a Sec&theta;Cos&Oslash;<br>दोनों तरफ वर्ग करने पर<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = Sec<sup>2</sup>&theta;Cos<sup>2</sup>&Oslash; &hellip; (i)<br><math display=\"inline\"><mi>y</mi></math> = b Sec&theta;Sin&Oslash;<br>दोनों तरफ वर्ग करने पर<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = Sec<sup>2</sup>&theta;Sin<sup>2</sup>&Oslash; &hellip;. (ii)<br><math display=\"inline\"><mi>z</mi></math> = c tan&theta;<br>दोनों तरफ वर्ग करने पर<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>z</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = tan<sup>2</sup>&theta; &hellip; (iii)<br>(i), (ii) जोड़ने और फिर (iii) घटाने पर<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><msup><mi>a</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><msup><mi>c</mi><mn>2</mn></msup></mfrac></math> = Sec<sup>2</sup>&theta;Cos<sup>2</sup>&Oslash; + Sec<sup>2</sup>&theta;Sin<sup>2</sup>&Oslash; - tan<sup>2</sup>&theta;<br>= Sec<sup>2</sup>&theta;(Cos<sup>2</sup>&Oslash; + Sin<sup>2</sup>&Oslash;) - tan<sup>2</sup>&theta;&nbsp;<br>= Sec<sup>2</sup>&theta; - tan<sup>2</sup>&theta;<br>= 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Find the value of (cosec &theta; - sin &theta;)(sec &theta; - cos &theta;)(tan &theta; + cot &theta;).</p>",
                    question_hi: "<p>56. (cosec &theta; - sin &theta;)(sec &theta; - cos &theta;)(tan &theta; + cot &theta;) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>1</p>",
                        "<p>-1</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>1</p>",
                        "<p>-1</p>"
                    ],
                    solution_en: "<p>56.(c) <br>(cosec &theta; - sin &theta;)(sec &theta; - cos &theta;)(tan &theta; + cot &theta;)<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> - sin&theta;) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math> - cos&theta;) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math> + cot&theta;)<br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>)</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math>)<br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>)</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math>)<br>(cos&theta;) (sin&theta;) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mi>cos</mi><mi>&#952;</mi></mrow><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></mstyle></mfrac></mstyle></mfrac></math>)<br>(cos&theta;) (sin&theta;) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi><mo>.</mo><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math>) = 1</p>",
                    solution_hi: "<p>56.(c) <br>(cosec &theta; - sin &theta;)(sec &theta; - cos &theta;)(tan &theta; + cot &theta;)<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> - sin&theta;) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math> - cos&theta;) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math> + cot&theta;)<br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>)</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math>)<br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>)</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math>)<br>(cos&theta;) (sin&theta;) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mi>cos</mi><mi>&#952;</mi></mrow><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></mstyle></mfrac></mstyle></mfrac></math>)<br>(cos&theta;) (sin&theta;) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi><mo>.</mo><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math>) = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A hemispherical bowl is made of silver and has an inner diameter of 4 cm if the thickness of silver is 0.5 cm. Find the volume of silver used in making the bowl correct to two places of decimals (use <math display=\"inline\"><mi>&#960;</mi></math> = 3.14 )</p>",
                    question_hi: "<p>57. चांदी से बना एक अर्धगोलाकार कटोरा, जिसका आंतरिक व्यास 4 cm है यदि चांदी की मोटाई 0.5 cm है। दशमलव के दो स्थानों तक सही करते हुए कटोरे को बनाने में प्रयुक्त चांदी का आयतन ज्ञात कीजिए (<math display=\"inline\"><mi>&#960;</mi></math> = 3.14 का प्रयोग कीजिए)।</p>",
                    options_en: [
                        "<p>15.69 cm<sup>3</sup></p>",
                        "<p>51.96 cm<sup>3</sup></p>",
                        "<p>15.96 cm<sup>3</sup></p>",
                        "<p>51.69 cm<sup>3</sup></p>"
                    ],
                    options_hi: [
                        "<p>15.69 cm<sup>3</sup></p>",
                        "<p>51.96 cm<sup>3</sup></p>",
                        "<p>15.96 cm<sup>3</sup></p>",
                        "<p>51.69 cm<sup>3</sup></p>"
                    ],
                    solution_en: "<p>57.(c)<br>Inner radius of bowl = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2 cm<br>Outer radius of bowl = 2 + 0.5 = 2.5 cm<br>Required volume of silver used in making the bowl = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi></math>(R<sup>3 </sup>- r<sup>3</sup>)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>&times; 3.14 &times; [(2.5)<sup>3 </sup>- (2)<sup>3</sup>]&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 3.14 &times; 7.625&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn><mo>.</mo><mn>885</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>= 15.96 cm<sup>3</sup></p>",
                    solution_hi: "<p>57.(c)<br>कटोरे की भीतरी त्रिज्या = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2 cm<br>कटोरे की बाहरी त्रिज्या = 2 + 0.5 = 2.5 cm<br>कटोरा बनाने में प्रयुक्त चांदी की आवश्यक मात्रा = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi></math>(R<sup>3 </sup>- r<sup>3</sup>)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>&times; 3.14 &times; [(2.5)<sup>3 </sup>- (2)<sup>3</sup>]&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 3.14 &times; 7.625&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn><mo>.</mo><mn>885</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>= 15.96 cm<sup>3</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. There is a large cone with a diameter of 14m and height of 12m. It is being filled with water at a rate of 2 m<sup>3</sup> in every 20 seconds. How long will it take to fill the cone ?</p>",
                    question_hi: "<p>58. एक विशाल शंकु का व्यास 14 m और ऊंचाई 12 m है। इसे प्रति 20 सेकंड में 2 m<sup>3</sup> की दर पर पानी से भरा जाता है। शंकु को भरने में कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>313 sec</p>",
                        "<p>6160 sec</p>",
                        "<p>616 sec</p>",
                        "<p>12320 sec</p>"
                    ],
                    options_hi: [
                        "<p>313 सेकंड</p>",
                        "<p>6160 सेकंड</p>",
                        "<p>616 सेकंड</p>",
                        "<p>12320 सेकंड</p>"
                    ],
                    solution_en: "<p>58.(b)<br>Radius of cone = <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 7 m<br>Volume of cone = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 7<sup>2</sup> &times; 12 = 616 m<sup>3</sup><br>Required time taken to fill the cone = <math display=\"inline\"><mfrac><mrow><mn>616</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 20 = 6160 sec</p>",
                    solution_hi: "<p>58.(b)<br>शंकु की त्रिज्या = <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 7 m<br>शंकु का आयतन = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 7<sup>2</sup> &times; 12 = 616 m<sup>3</sup><br>शंकु को भरने में लगा आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>616</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 20 = 6160 सेकंड</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A tangent is drawn from a point that is at a distance of 37 cm from center of the circle O, and diameter is 24 cm. The length (in cm) of the tangent is:</p>",
                    question_hi: "<p>59. एक स्पर्शरेखा उस बिंदु से खींची जाती है जो वृत्त के केंद्र O से 37 cm की दूरी पर है और व्यास 24 cm है। स्पर्शरेखा की लंबाई (cm में) कितनी है ?</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>35</p>",
                        "<p>28.16</p>",
                        "<p>37</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>35</p>",
                        "<p>28.16</p>",
                        "<p>37</p>"
                    ],
                    solution_en: "<p>59.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349460.png\" alt=\"rId49\" width=\"258\" height=\"183\"><br>Length of tangent (PQ) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>12</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1369</mn><mo>-</mo><mn>144</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1225</mn></msqrt></math><br>= 35 cm</p>",
                    solution_hi: "<p>59.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349460.png\" alt=\"rId49\" width=\"258\" height=\"183\"><br>Length of tangent (PQ) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>12</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1369</mn><mo>-</mo><mn>144</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1225</mn></msqrt></math><br>= 35 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A line from point A is drawn that is tangent to the circle at point B. A secant is also drawn from point A to the circle intersecting it at points C and D. If AB = 42 cm and AC = 21 cm, then what is the ratio between AB and CD ?</p>",
                    question_hi: "<p>60. बिंदु A से एक रेखा खींची गई है जो बिंदु B पर वृत्त की स्पर्शरेखा है। बिंदु A से वृत्त पर एक छेदक रेखा भी खींची गई है जो इसे बिंदु C और D पर काटती है। यदि AB = 42 cm और AC = 21 cm है, तो AB और CD का अनुपात ज्ञात करें।</p>",
                    options_en: [
                        "<p>3 : 2</p>",
                        "<p>2 : 5</p>",
                        "<p>3 : 4</p>",
                        "<p>2 : 3</p>"
                    ],
                    options_hi: [
                        "<p>3 : 2</p>",
                        "<p>2 : 5</p>",
                        "<p>3 : 4</p>",
                        "<p>2 : 3</p>"
                    ],
                    solution_en: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349614.png\" alt=\"rId50\" width=\"244\" height=\"155\"><br>(AB)<sup>2</sup> = AC &times; AD<br>42 &times; 42 = 21 &times; (21 + x)<br>84 = 21 + x &rArr;&nbsp;x = 63<br>Required ratio = 42 : 63 = 2 : 3</p>",
                    solution_hi: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349614.png\" alt=\"rId50\" width=\"244\" height=\"155\"><br>(AB)<sup>2</sup> = AC &times; AD<br>42 &times; 42 = 21 &times; (21 + x)<br>84 = 21 + x &rArr; x = 63<br>अभीष्ट अनुपात = 42 : 63 = 2 : 3</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If 3a + 2b = 27 and 27a<sup>3</sup> + 8b<sup>3</sup> =1458, then find 2ab.</p>",
                    question_hi: "<p>61. यदि 3a + 2b = 27 और 27a<sup>3</sup> + 8b<sup>3</sup> = 1458 है, तो 2ab का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>72</p>",
                        "<p>70</p>",
                        "<p>77</p>",
                        "<p>75</p>"
                    ],
                    options_hi: [
                        "<p>72</p>",
                        "<p>70</p>",
                        "<p>77</p>",
                        "<p>75</p>"
                    ],
                    solution_en: "<p>61.(d) <br>3a + 2b = 27 <br>On cubing both side we get<br>(3a)<sup>3</sup> + (2b)<sup>3</sup> + 3ab(a + b) = 27<sup>3</sup><br>27a<sup>3</sup> + 8b<sup>3</sup> + 3 &times; 3a &times; 2b(3a + 2b) = 27<sup>3</sup><br>1458 + 18ab&nbsp;&times; 27 = 19683<br>18ab&nbsp;&times; 27 = 19683 - 1458 <br>2ab = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18225</mn><mrow><mn>9</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math> = 75</p>",
                    solution_hi: "<p>61.(d) <br>3a + 2b = 27 <br>दोनों ओर घन करने पर हमें प्राप्त होता है<br>(3a)<sup>3</sup> + (2b)<sup>3</sup> + 3ab(a + b) = 27<sup>3</sup><br>27a<sup>3</sup> + 8b<sup>3</sup> + 3 &times; 3a &times; 2b(3a + 2b) = 27<sup>3</sup><br>1458 + 18ab&nbsp;&times; 27 = 19683<br>18ab&nbsp;&times; 27 = 19683 - 1458 <br>2ab = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18225</mn><mrow><mn>9</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math> = 75</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The ratio of the number of boys to that of girls in a school is 7 : 9. If the number of girls in the school is 189, then the number of boys in that school is:</p>",
                    question_hi: "<p>62. एक स्कूल में लड़कों की संख्या का लड़कियों की संख्या से अनुपात 7 : 9 है। यदि स्कूल में लड़कियों की संख्या 189 है, तो उस स्कूल में लड़कों की संख्या कितनी है ?</p>",
                    options_en: [
                        "<p>105</p>",
                        "<p>147</p>",
                        "<p>168</p>",
                        "<p>126 </p>"
                    ],
                    options_hi: [
                        "<p>105</p>",
                        "<p>147</p>",
                        "<p>168</p>",
                        "<p>126</p>"
                    ],
                    solution_en: "<p>62.(b) According to question,<br>&rArr; 9 units = 189 <br>&rArr; 7 units = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mn>9</mn></mfrac></math> &times; 7 = 147<br>Hence, Number of boys is 147</p>",
                    solution_hi: "<p>62.(b) प्रश्न के अनुसार,<br>&rArr; 9 इकाई = 189&nbsp;<br>&rArr; 7 इकाई =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mn>9</mn></mfrac></math> &times; 7 = 147<br>अतः, लड़कों की संख्या 147 है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In a mixture, milk and water are in ratio of 7 : 9. Some milk is added to the mixture because of which ratio of milk and water becomes 11 : 9. How much milk was added as a percentage of initial mixture ?</p>",
                    question_hi: "<p>63. एक मिश्रण में, दूध और पानी का अनुपात 7 : 9 है। मिश्रण में कुछ दूध मिलाया जाता है जिसके कारण दूध और पानी का अनुपात 11 : 9 हो जाता है। प्रारंभिक मिश्रण के प्रतिशत के रूप में, मिश्रण में कितना दूध मिलाया गया ?</p>",
                    options_en: [
                        "<p>10 percent</p>",
                        "<p>60 percent</p>",
                        "<p>25 percent</p>",
                        "<p>30 percent</p>"
                    ],
                    options_hi: [
                        "<p>10 प्रतिशत</p>",
                        "<p>60 प्रतिशत</p>",
                        "<p>25 प्रतिशत</p>",
                        "<p>30 प्रतिशत</p>"
                    ],
                    solution_en: "<p>63.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Milk&nbsp; &nbsp;Water&nbsp; &nbsp;Total<br>Initial&nbsp; &nbsp; &nbsp; &nbsp;7&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp; &nbsp; 16<br>Final&nbsp; &nbsp; &nbsp; &nbsp;11&nbsp; :&nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp; &nbsp; 20<br>Quantity of milk to be added = 11 - 7 = 4 unit<br>Required % of milk = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>63.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; दूध&nbsp; &nbsp; पानी&nbsp; &nbsp;कुल<br>प्रारंभिक&nbsp; &nbsp; 7&nbsp; &nbsp;:&nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp;16<br>अंतिम&nbsp; &nbsp; &nbsp; 11&nbsp; :&nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp;20<br>मिलाये जाने वाले दूध की मात्रा = 11 - 7 = 4 इकाई<br>दूध का आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Riya and Sangeeta can finish a work in 6 days and 8 days, respectively. Riya started the work alone and then after 3 days, Sangeeta joined Riya. They both finish the remaining work. How long did the total work last ?</p>",
                    question_hi: "<p>64. रिया और संगीता एक काम को क्रमशः 6 दिन और 8 दिन में पूरा कर सकती हैं। रिया अकेले काम शुरू करती और फिर 3 दिन बाद संगीता रिया के साथ काम में शामिल हो जाती है। वे दोनों शेष काम पूरा करती हैं। कुल काम को पूरा करने में कितने दिन का समय लगा ?</p>",
                    options_en: [
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>"
                    ],
                    options_hi: [
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>"
                    ],
                    solution_en: "<p>64.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349730.png\" alt=\"rId51\" width=\"197\" height=\"148\"><br>let,<strong> </strong>time taken to complete the work x day<br>According to the question,<br>4x + 3(x - 3) = 24<br>7x = 33 &rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>7</mn></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math>days</p>",
                    solution_hi: "<p>64.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349833.png\" alt=\"rId52\" width=\"186\" height=\"158\"></p>\n<p>माना कार्य पूरा होने में लगा समय x दिन है<br>प्रश्न के अनुसार,<br>4x + 3(x - 3) = 24<br>7x = 33 &rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>7</mn></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math> दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A motorcycle covered the first 60 km of its journey at an average speed of 40 km/h. The speed of the motorcycle for covering the rest of the journey, i.e. 90 km, was 45 km/h. During the whole journey, the overall average speed of the motorcycle was:</p>",
                    question_hi: "<p>65. एक मोटरसाइकिल अपनी यात्रा के पहले 60 km की दूरी को 40 km/h की औसत चाल से तय करती है। शेष यात्रा अर्थात 90 km की दूरी तय करने के लिए मोटरसाइकिल की चाल 45 km/h थी। पूरी यात्रा के दौरान मोटरसाइकिल की कुल औसत चाल कितनी थी ?</p>",
                    options_en: [
                        "<p>42<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>km/h</p>",
                        "<p>42 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>",
                        "<p>42 km/h</p>",
                        "<p>42<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>"
                    ],
                    options_hi: [
                        "<p>42<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>km/h</p>",
                        "<p>42 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>",
                        "<p>42 km/h</p>",
                        "<p>42<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>"
                    ],
                    solution_en: "<p>65.(d)<br>Average speed = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac></math><br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>+</mo><mn>90</mn></mrow><mrow><mfrac><mn>60</mn><mn>40</mn></mfrac><mo>+</mo><mfrac><mn>90</mn><mn>45</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle><mo>+</mo><mn>2</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>7</mn></mfrac></math> = 42<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>",
                    solution_hi: "<p>65.(d)<br>औसत गति = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>+</mo><mn>90</mn></mrow><mrow><mfrac><mn>60</mn><mn>40</mn></mfrac><mo>+</mo><mfrac><mn>90</mn><mn>45</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle><mo>+</mo><mn>2</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>7</mn></mfrac></math> = 42<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. In an election between two candidates, a candidate who gets 78% of the votes is elected by a majority of 448 votes. What is the total number of votes polled ?</p>",
                    question_hi: "<p>66. दो उम्मीदवारों के बीच हुए एक चुनाव में, 78% मत प्राप्त करने वाला उम्मीदवार 448 मतों के बहुमत से निर्वाचित होता है। डाले गए मतों की कुल संख्या ज्ञात करें।</p>",
                    options_en: [
                        "<p>800</p>",
                        "<p>750</p>",
                        "<p>850</p>",
                        "<p>700</p>"
                    ],
                    options_hi: [
                        "<p>800</p>",
                        "<p>750</p>",
                        "<p>850</p>",
                        "<p>700</p>"
                    ],
                    solution_en: "<p>66.(a) <br>Votes received by the candidate who lost the election = 100 - 78 = 22% vote<br>Difference in votes = 78 - 22 = 56%<br>(difference) 56% = 448<br>Total votes (100%) = <math display=\"inline\"><mfrac><mrow><mn>448</mn></mrow><mrow><mn>56</mn></mrow></mfrac></math> &times; 100 = 800</p>",
                    solution_hi: "<p>66.(a) <br>चुनाव हारने वाले उम्मीदवार को मिले वोट = 100 - 78 = 22% vote<br>वोटों में अंतर = 78 - 22 = 56%<br>(अंतर) 56% = 448<br>कुल वोट (100%) = <math display=\"inline\"><mfrac><mrow><mn>448</mn></mrow><mrow><mn>56</mn></mrow></mfrac></math> &times; 100 = 800</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Last year, Ranjan&rsquo;s monthly salary was ₹34,500 and this year his monthly salary is ₹38,640. What is the percentage increase in Ranjan&rsquo;s monthly salary this year over his monthly salary last year ?</p>",
                    question_hi: "<p>67. पिछले वर्ष, रंजन का मासिक वेतन ₹34,500 था और इस वर्ष उसका मासिक वेतन ₹38,640 है। इस वर्ष रंजन के मासिक वेतन में, पिछले वर्ष के मासिक वेतन की तुलना में कितने प्रतिशत की वृद्धि हुई है ?</p>",
                    options_en: [
                        "<p>15%</p>",
                        "<p>13%</p>",
                        "<p>12%</p>",
                        "<p>20%</p>"
                    ],
                    options_hi: [
                        "<p>15%</p>",
                        "<p>13%</p>",
                        "<p>12%</p>",
                        "<p>20%</p>"
                    ],
                    solution_en: "<p>67.(c)<br>Increase % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>38640</mn><mo>-</mo><mn>34500</mn></mrow><mn>34500</mn></mfrac></math> &times; 100 = 12%</p>",
                    solution_hi: "<p>67.(c)<br>वृद्धि % = <math display=\"inline\"><mfrac><mrow><mn>38640</mn><mo>-</mo><mn>34500</mn></mrow><mrow><mn>34500</mn></mrow></mfrac></math> &times; 100 = 12%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Find the gain percentage, given that Anubha sold her scooter for 31524 gaining <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> th of the selling price .</p>",
                    question_hi: "<p>68. अनुभा अपने स्कूटर को ₹31524 में बेचकर, विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> भाग के बराबर लाभ अर्जित करती है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>5%</p>",
                        "<p>20%</p>",
                        "<p>30%</p>",
                        "<p>35%</p>"
                    ],
                    options_hi: [
                        "<p>5%</p>",
                        "<p>20%</p>",
                        "<p>30%</p>",
                        "<p>35%</p>"
                    ],
                    solution_en: "<p>68.(b) Selling price of a scooter = 31524 <br>Gain = 31524 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 5254<br>CP of a scooter = 31524 - 5254 = 26270<br>Gain % = <math display=\"inline\"><mfrac><mrow><mn>5254</mn></mrow><mrow><mn>26270</mn></mrow></mfrac></math> &times; 100 = 20 %</p>",
                    solution_hi: "<p>68.(b) स्कूटर का विक्रय मूल्य = 31524 <br>लाभ = 31524 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 5254<br>एक स्कूटर का CP = 31524 - 5254 = 26270<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>5254</mn></mrow><mrow><mn>26270</mn></mrow></mfrac></math> &times; 100 = 20 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Rohini buys a Bluetooth headphone set for ₹1,700 from a wholesale shop and marks it at ₹2,000. Later on, she allows a discount of 40% on its sale. What is her loss or gain percentage (correct up to two decimal places) ?</p>",
                    question_hi: "<p>69. रोहिणी एक थोक दुकान से ₹1,700 में ब्लूटूथ हेडफ़ोन सेट खरीदती है और उसका अंकित मूल्य ₹2,000 निर्धारित करती है। बाद में, वह इसकी बिक्री पर 40% की छूट देती है। उसका हानि या लाभ प्रतिशत क्या है (दो दशमलव स्थानों तक सही) ?</p>",
                    options_en: [
                        "<p>25.19% loss</p>",
                        "<p>29.41% gain</p>",
                        "<p>29.41% loss</p>",
                        "<p>25.19% gain</p>"
                    ],
                    options_hi: [
                        "<p>25.19% हानि</p>",
                        "<p>29.41% लाभ</p>",
                        "<p>29.41% हानि</p>",
                        "<p>25.19% लाभ</p>"
                    ],
                    solution_en: "<p>69.(c)<br>Selling price of headphone = 2000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 1200<br>Cost price = 1700<br>loss% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1700</mn><mo>-</mo><mn>1200</mn></mrow><mn>1700</mn></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>17</mn></mfrac></math>% = 29.41%</p>",
                    solution_hi: "<p>69.(c)<br>हेडफोन का विक्रय मूल्य = 2000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 1200<br>लागत मूल्य = 1700<br>हानि% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1700</mn><mo>-</mo><mn>1200</mn></mrow><mn>1700</mn></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>17</mn></mfrac></math>% = 29.41%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The simple interest on a sum of money for 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> years at 10% per annum is ₹30 more than the simple interest on the same sum for 1 year at 12% per annum. Find the sum.</p>",
                    question_hi: "<p>70. किसी धनराशि पर 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष के लिए 10% वार्षिक दर से साधारण ब्याज, उसी धनराशि पर 1 वर्ष के लिए 12% वार्षिक दर से साधारण ब्याज से ₹ 30 अधिक है। धनराशि (मूलधन) ज्ञात कीजिये।</p>",
                    options_en: [
                        "<p>₹1,050</p>",
                        "<p>₹1,250</p>",
                        "<p>₹1,000</p>",
                        "<p>₹1,200</p>"
                    ],
                    options_hi: [
                        "<p>₹1,050</p>",
                        "<p>₹1,250</p>",
                        "<p>₹1,000</p>",
                        "<p>₹1,200</p>"
                    ],
                    solution_en: "<p>70.(c)<br>SI = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>R</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>1</mn></mrow><mn>100</mn></mfrac></math> = 30<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>-</mo><mi>P</mi><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>1</mn></mrow><mn>100</mn></mfrac></math> = 30<br>15P&nbsp;- 12P = 3000<br>3P&nbsp;= 3000 &rArr; P = Rs. 1000</p>",
                    solution_hi: "<p>70.(c)<br>SI = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>R</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>1</mn></mrow><mn>100</mn></mfrac></math> = 30<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>-</mo><mi>P</mi><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>1</mn></mrow><mn>100</mn></mfrac></math> = 30<br>15P&nbsp;- 12P = 3000<br>3P&nbsp;= 3000 &rArr; P = Rs. 1000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A loan of ₹2,550 is to be paid back in two equal half-yearly installments. How much is each installment if the interest is compounded half-yearly at 8% p.a.?</p>",
                    question_hi: "<p>71. ₹2,550 का ऋण दो समान अर्धवार्षिक किस्तों में चुकाया जाना है। यदि ब्याज अर्ध-वार्षिक रूप से चक्रवृद्धि किया जाने वाला 8% वार्षिक है, तो प्रत्येक किस्त कितनी होगी ?</p>",
                    options_en: [
                        "<p>₹1,258</p>",
                        "<p>₹1,352</p>",
                        "<p>₹1,745</p>",
                        "<p>₹1,457</p>"
                    ],
                    options_hi: [
                        "<p>₹1,258</p>",
                        "<p>₹1,352</p>",
                        "<p>₹1,745</p>",
                        "<p>₹1,457</p>"
                    ],
                    solution_en: "<p>71.(b) Rate for <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> yrs = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>2</mn></mfrac></math> = 4% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math><br><strong>Principal&nbsp; :&nbsp; Installment</strong> <br>&nbsp; &nbsp; &nbsp;(25&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;26) <strong>&times; 26</strong><br>&nbsp; &nbsp; &nbsp;650&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;676<br>&nbsp; &nbsp; &nbsp;25<sup>2&nbsp; &nbsp; &nbsp; &nbsp;</sup> :&nbsp; &nbsp; &nbsp;26<sup>2</sup><br>___________________<br>&nbsp; &nbsp; 1,275&nbsp; &nbsp; :&nbsp; &nbsp; 1,352<br>&rArr; 1275 unit = ₹2,550<br>&rArr; 676 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2550</mn><mn>1275</mn></mfrac></math> &times; 676 = ₹1,352</p>",
                    solution_hi: "<p>71.(b) <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष के लिए दर = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>2</mn></mfrac></math> = 4% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br><strong>मूलधन&nbsp; &nbsp;:&nbsp; &nbsp;किस्त</strong> <br>&nbsp; &nbsp;(25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;26) <strong>&times; 26</strong> <br>&nbsp; &nbsp;650&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;676<br>&nbsp; &nbsp;25<sup>2&nbsp; </sup>&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;26<sup>2</sup><br>_________________<br>&nbsp;1,275&nbsp; &nbsp;:&nbsp; &nbsp; 1,352<br>&rArr; 1275 इकाई = ₹2,550<br>&rArr; 676 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2550</mn><mn>1275</mn></mfrac></math> &times; 676 = ₹1,352</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If the average of a number and its reciprocal is 2, then the average of its cube and its reciprocal is equal to:</p>",
                    question_hi: "<p>72. यदि किसी संख्या और उसके व्युत्क्रम का औसत 2 है, तो उस संख्या के घन और घन के व्युत्क्रम का औसत _______के बराबर होगा।</p>",
                    options_en: [
                        "<p>36</p>",
                        "<p>48</p>",
                        "<p>28</p>",
                        "<p>26</p>"
                    ],
                    options_hi: [
                        "<p>36</p>",
                        "<p>48</p>",
                        "<p>28</p>",
                        "<p>26</p>"
                    ],
                    solution_en: "<p>72.(d)<br>Let number be x<br>According to the question,<br>x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 4 <br>if x&nbsp;+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = a then, x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = a<sup>3</sup> - 3a<br>x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = 4<sup>3</sup> - 3 &times; 4 = 52<br>Hence, required average = <math display=\"inline\"><mfrac><mrow><mn>52</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 26</p>",
                    solution_hi: "<p>72.(d)<br>माना संख्या x&nbsp;है<br>प्रश्न के अनुसार,<br>x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 4<br>यदि x&nbsp;+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = a तो, x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = a<sup>3</sup> - 3a<br>x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = 4<sup>3</sup> - 3 &times; 4 = 52<br>अतः, अभीष्ट औसत = <math display=\"inline\"><mfrac><mrow><mn>52</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 26</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A stock portfolio consists of four stocks. Stock A represents 20% of the portfolio and has a return of 6%. Stock B represents 30% of the portfolio and has a return of 8%. Stock C represents 20% of the portfolio and has a return of 4%. Stock D represents the remaining 30% of the portfolio and has a negative-return of 5%. What is the average return of the portfolio ?</p>",
                    question_hi: "<p>73. एक स्टॉक पोर्टफोलियो में चार स्टॉक हैं। स्टॉक A, पोर्टफोलियो के 20% को निरूपित करता है और इसका रिटर्न 6% है। स्टॉक B, पोर्टफोलियो के 30% को निरूपित करता है और इसका रिटर्न 8% है। स्टॉक C, पोर्टफोलियो के 20% को निरूपित करता है और इसका रिटर्न 4% है। स्टॉक D, पोर्टफोलियो के शेष 30% को निरूपित करता है और इसका निगेटिव-रिटर्न 5% है। पोर्टफोलियो का औसत रिटर्न कितना है ?</p>",
                    options_en: [
                        "<p>2.9%</p>",
                        "<p>2.6%</p>",
                        "<p>3.2%</p>",
                        "<p>3.4%</p>"
                    ],
                    options_hi: [
                        "<p>2.9%</p>",
                        "<p>2.6%</p>",
                        "<p>3.2%</p>",
                        "<p>3.4%</p>"
                    ],
                    solution_en: "<p>73.(a)<br>Let value of portfolio = 100<br>Average return of the portfolio = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>%</mi><mo>&#215;</mo><mn>20</mn><mo>+</mo><mn>8</mn><mi>%</mi><mo>&#215;</mo><mn>30</mn><mo>+</mo><mn>4</mn><mi>%</mi><mo>&#215;</mo><mn>20</mn><mo>+</mo><mo>(</mo><mo>-</mo><mn>5</mn><mi>%</mi><mo>)</mo><mo>&#215;</mo><mn>30</mn></mrow><mn>100</mn></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>%</mo><mo>+</mo><mn>240</mn><mo>%</mo><mo>+</mo><mn>80</mn><mo>%</mo><mo>-</mo><mn>150</mn><mo>%</mo></mrow><mn>100</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>290</mn><mi>%</mi></mrow><mn>100</mn></mfrac></math> = 2.9%</p>",
                    solution_hi: "<p>73.(a)<br>माना , पोर्टफोलियो का मूल्य= 100<br>पोर्टफोलियो का औसत रिटर्न = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>%</mi><mo>&#215;</mo><mn>20</mn><mo>+</mo><mn>8</mn><mi>%</mi><mo>&#215;</mo><mn>30</mn><mo>+</mo><mn>4</mn><mi>%</mi><mo>&#215;</mo><mn>20</mn><mo>+</mo><mo>(</mo><mo>-</mo><mn>5</mn><mi>%</mi><mo>)</mo><mo>&#215;</mo><mn>30</mn></mrow><mn>100</mn></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>%</mo><mo>+</mo><mn>240</mn><mo>%</mo><mo>+</mo><mn>80</mn><mo>%</mo><mo>-</mo><mn>150</mn><mo>%</mo></mrow><mn>100</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>290</mn><mi>%</mi></mrow><mn>100</mn></mfrac></math> = 2.9%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The table given below shows the marks obtained by four students in 4 different subjects. Maximum marks for each subject is 175<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170349963.png\" alt=\"rId53\" width=\"414\" height=\"143\"> <br>What is the aggregate percentage of marks obtained by Gunja in all the four subjects ?</p>",
                    question_hi: "<p>74. नीचे दी गई तालिका चार अलग-अलग विषयों में चार विद्यार्थियों द्वारा प्राप्त अंकों को दर्शाती है। प्रत्येक विषय के लिए पूर्णांक 175 हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170350140.png\" alt=\"rId54\" width=\"360\" height=\"150\"> <br>गुंजा द्वारा सभी चार विषयों में प्राप्त अंकों का कुल प्रतिशत क्या है ?</p>",
                    options_en: [
                        "<p>67</p>",
                        "<p>61</p>",
                        "<p>58</p>",
                        "<p>71</p>"
                    ],
                    options_hi: [
                        "<p>67</p>",
                        "<p>61</p>",
                        "<p>58</p>",
                        "<p>71</p>"
                    ],
                    solution_en: "<p>74.(b) Total marks obtained by Gunja in all four subject = 112 + 119 + 99 + 97 = 427<br>Total maximum marks in all four subjects = 4 &times; 175 = 700<br>Required percentage = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>427</mn><mn>700</mn></mfrac></math> &times; 100 = 61 %</p>",
                    solution_hi: "<p>74.(b) गुंजा द्वारा सभी चार विषयों में प्राप्त कुल अंक = 112 + 119 + 99 + 97 = 427<br>चारों विषयों में कुल अधिकतम अंक = 4 &times; 175 = 700<br>आवश्यक प्रतिशत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>427</mn><mn>700</mn></mfrac></math> &times; 100 = 61 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The given pie chart shows the amount of money (₹) spent (in degrees) on various sports by a sports academy in a particular year. Study the pie chart and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170350256.png\" alt=\"rId55\" width=\"323\" height=\"316\"> <br>If ₹42,000 were spent on Soccer, then the money spent on Badminton, Tennis and Rugby taken together is what percentage (rounded off to 2 decimal places) more/less than the money spent on Cricket, Swimming and Hockey taken together ?</p>",
                    question_hi: "<p>75. दिया गया पाई चार्ट एक विशेष वर्ष में एक खेल अकादमी द्वारा विभिन्न खेलों पर खर्च की गई धनराशि (₹) को (डिग्री में) दर्शाता है। पाई चार्ट का अध्ययन कीजिए और इसके बाद आगे आने वाले प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739170350367.png\" alt=\"rId56\" width=\"343\" height=\"350\"> <br>यदि फ़ुटबॉल पर ₹42,000 खर्च किए गए थे, तो बैडमिंटन, टेनिस और रग्बी पर कुल मिलाकर खर्च की गई धनराशि, क्रिकेट, तैराकी और हॉकी पर कुल मिलाकर खर्च की गई धनराशि से कितने प्रतिशत (2 दशमलव स्थान तक पूर्णांकित) अधिक/कम है ?</p>",
                    options_en: [
                        "<p>Less, 34.87%</p>",
                        "<p>More, 28.95%</p>",
                        "<p>More, 34.87%</p>",
                        "<p>Less, 28.95%</p>"
                    ],
                    options_hi: [
                        "<p>कम, 34.87%</p>",
                        "<p>अधिक, 28.95%</p>",
                        "<p>अधिक, 34.87%</p>",
                        "<p>कम, 28.95%</p>"
                    ],
                    solution_en: "<p>75.(d) According to the question,<br>Spend money on Badminton, tennis, and Rugby = 45 + 65 + 25 = 135&deg; <br>Spend money on Cricket, Swimming and Hockey = 100 + 60 + 30 = 190&deg;<br>Required less % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>190</mn><mo>-</mo><mn>135</mn></mrow><mn>190</mn></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>190</mn></mfrac></math> &times; 100 &asymp; 28.95%</p>",
                    solution_hi: "<p>75.(d) प्रश्न के अनुसार,<br>बैडमिंटन, टेनिस और रग्बी पर खर्च = 45 + 65 + 25 = 135&deg; <br>क्रिकेट, तैराकी और हॉकी पर खर्च = 100 + 60 + 30 = 190&deg;<br>आवश्यक कम % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>190</mn><mo>-</mo><mn>135</mn></mrow><mn>190</mn></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>190</mn></mfrac></math> &times; 100 &asymp; 28.95%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: The farmer works very hard to make the soil favourable.<br>Q: But these soil bacteria are very necessary and helpful.<br>R: There are millions of bacteria in a cubic inch of fertile soil.<br>S: Some kinds of bacteria are harmful.</p>",
                    question_hi: "<p>76. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: The farmer works very hard to make the soil favourable.<br>Q: But these soil bacteria are very necessary and helpful.<br>R: There are millions of bacteria in a cubic inch of fertile soil.<br>S: Some kinds of bacteria are harmful.</p>",
                    options_en: [
                        "<p>SQPR</p>",
                        "<p>QSPR</p>",
                        "<p>RPSQ</p>",
                        "<p>PRQS</p>"
                    ],
                    options_hi: [
                        "<p>SQPR</p>",
                        "<p>QSPR</p>",
                        "<p>RPSQ</p>",
                        "<p>PRQS</p>"
                    ],
                    solution_en: "<p>76.(c) RPSQ<br>Sentence R will be the starting line as it contains the main idea of the parajumble i.e. There are millions of bacteria in a cubic inch of fertile soil. However, Sentence P states that the farmer works very hard to make the soil favourable. So, P will follow R. Further, Sentence S states that some kinds of bacteria are harmful &amp; Sentence Q states that these soil bacteria are very necessary and helpful. So, Q will follow S. Going through the options, only option c has the correct sequence.</p>",
                    solution_hi: "<p>76.(c) RPSQ<br>वाक्य R शुरूआती पंक्ति (line) होगी क्योंकि इसमें parajumble का मुख्य विचार है -There are millions of bacteria in a cubic inch of fertile soil. हालाँकि, वाक्य P कहता है कि किसान मिट्टी को अनुकूल बनाने के लिए बहुत मेहनत करता है। तो, R के बाद P का प्रयोग होगा । आगे, वाक्य S कहता है कि कुछ प्रकार के बैक्टीरिया हानिकारक होते हैं और वाक्य Q में कहा गया है कि ये मिट्टी के जीवाणु बहुत आवश्यक और सहायक हैं। तो, S के बाद Q आएगा। विकल्पों के माध्यम से जाने पर, केवल विकल्प c में सही क्रम है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "77. Identify the segment in the sentence, which contains the grammatical error.<br />Knowledge and wisdom makes an individual truly complete and self assured.",
                    question_hi: "77. Identify the segment in the sentence, which contains the grammatical error.<br />Knowledge and wisdom makes an individual truly complete and self assured.",
                    options_en: [
                        " Knowledge and wisdom makes",
                        " an individual truly complete",
                        " and self assured",
                        " No error"
                    ],
                    options_hi: [
                        " Knowledge and wisdom makes",
                        " an individual truly complete",
                        " and self assured",
                        " No error"
                    ],
                    solution_en: "77.(d) No error. ",
                    solution_hi: "77.(d) No error/कोई त्रुटि नहीं। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Too much of anything is good for <strong><span style=\"text-decoration: underline;\">everything.</span></strong></p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Too much of anything is good for <strong><span style=\"text-decoration: underline;\">everything.</span></strong></p>",
                    options_en: [
                        "<p>No Improvement</p>",
                        "<p>a thing</p>",
                        "<p>all things</p>",
                        "<p>nothing</p>"
                    ],
                    options_hi: [
                        "<p>No Improvement</p>",
                        "<p>a thing</p>",
                        "<p>all things</p>",
                        "<p>nothing</p>"
                    ],
                    solution_en: "<p>78.(d) nothing<br>Here, nothing should be used. <br>Eg- Too much of anything is good for nothing. Too much of anything is bad for everything.</p>",
                    solution_hi: "<p>78.(d) nothing <br>यहां nothing उपयोग करना होगा।<br>Eg - Too much of anything is good for nothing./ किसी भी चीज की अति अच्छी नहीं होती।<br>Too much of anything is bad for everything./ किसी भी चीज की अति हर चीज के लिए खराब होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79.<strong id=\"docs-internal-guid-2a7d8b21-7fff-eac2-b866-9c0658211e48\">&nbsp;</strong>Select the correct active form of the given sentence.&nbsp;</p>\n<p>The award was presented to her by the director.</p>",
                    question_hi: "<p>79. Select the correct active form of the given sentence.&nbsp;&nbsp;</p>\n<p>The award was presented to her by the director.</p>",
                    options_en: [
                        "<p>She presented the award to the director.</p>",
                        "<p>The award presented to her by the director.</p>",
                        "<p>The director presented the award to her.</p>",
                        "<p>She was presenting the award to the director.</p>"
                    ],
                    options_hi: [
                        "<p>She presented the award to the director.</p>",
                        "<p>The award presented to her by the director.</p>",
                        "<p>The director presented the award to her.</p>",
                        "<p>She was presenting the award to the director.</p>"
                    ],
                    solution_en: "<p>79.(c) The director presented the award to her.(Correct)<br>(a) She presented the award to the director.(Incorrect Sentence Structure)<br>(b) The award presented to her by the director.(Incorrect Sentence Structure)<br>(d) She was presenting the award to the director.(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>79.(c) The director presented the award to her.(Correct)<br>(a) She presented the award to the director.(गलत Sentence Structure)<br>(b) The award presented to her by the director.(गलत Sentence Structure)<br>(d) She was presenting the award to the director.(गलत Sentence Structure)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate meaning of the given underlined idiom. <br>The construction of the office building of the company was taking place i<span style=\"text-decoration: underline;\">n leaps and bounds</span>.</p>",
                    question_hi: "<p>80. Select the most appropriate meaning of the given underlined idiom. <br>The construction of the office building of the company was taking place <span style=\"text-decoration: underline;\">in leaps and bounds</span>.</p>",
                    options_en: [
                        "<p>Progressing very quickly</p>",
                        "<p>Stopped due to some legal issues</p>",
                        "<p>Broken because of negligence</p>",
                        "<p>Moving very slowly</p>"
                    ],
                    options_hi: [
                        "<p>Progressing very quickly</p>",
                        "<p>Stopped due to some legal issues</p>",
                        "<p>Broken because of negligence</p>",
                        "<p>Moving very slowly</p>"
                    ],
                    solution_en: "<p>80.(a)<strong> In leaps and bounds- </strong>progressing very quickly. </p>",
                    solution_hi: "<p>80.(a) <strong>In leaps and bounds-</strong> progressing very quickly./ बहुत तेजी से प्रगति करना। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate antonym of the given word.<br>Melodious</p>",
                    question_hi: "<p>81. Select the most appropriate antonym of the given word.<br>Melodious</p>",
                    options_en: [
                        "<p>Harmonious</p>",
                        "<p>tuneless</p>",
                        "<p>odious</p>",
                        "<p>mellifluous</p>"
                    ],
                    options_hi: [
                        "<p>Harmonious</p>",
                        "<p>tuneless</p>",
                        "<p>odious</p>",
                        "<p>mellifluous</p>"
                    ],
                    solution_en: "<p>81.(b) <strong>tuneless</strong><br><strong>Melodious</strong>- pleasant to listen to<br><strong>Tuneless-</strong> not pleasing to listen to <br><strong>Harmonious-</strong> tuneful, sweet-sounding<br><strong>Odious-</strong> extremely unpleasant; repulsive<br><strong>Mellifluous-</strong> pleasingly smooth and musical to hear.</p>",
                    solution_hi: "<p>81.(b) <strong>tuneless</strong><br><strong>Melodious</strong>- सुनने में सुखद/ मधुर <br><strong>Tuneless</strong>- जो सुनने में अच्छा नहीं लगे / बेसुध<br><strong>Harmonious</strong>- सुरीली<br><strong>Odious-</strong> अत्यंत अप्रिय; प्रतिकारक/ घिनौना<br><strong>Mellifluous-</strong> सुनने के लिए सुखद संगीतमय/ मधुर</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled sentences. Find the correct order: <br>P: In addition, tsunamis move throughout the depth of the ocean and not just its surface. <br>Q: A tsunami wave isn&rsquo;t much different in height compared to other waves in the ocean. <br>R: That is why a tsunami generally goes unnoticed in the open ocean. <br>S: But, due to its wavelength, it \"piles up\" when it approaches land.</p>",
                    question_hi: "<p>82. Given below are four jumbled sentences. Find the correct order: <br>P: In addition, tsunamis move throughout the depth of the ocean and not just its surface. <br>Q: A tsunami wave isn&rsquo;t much different in height compared to other waves in the ocean. <br>R: That is why a tsunami generally goes unnoticed in the open ocean. <br>S: But, due to its wavelength, it \"piles up\" when it approaches land.</p>",
                    options_en: [
                        "<p>&nbsp;RSPQ</p>",
                        "<p>&nbsp;PQRS</p>",
                        "<p>&nbsp;QSRP</p>",
                        "<p>&nbsp;SRPQ</p>"
                    ],
                    options_hi: [
                        "<p>&nbsp;RSPQ</p>",
                        "<p>&nbsp;PQRS</p>",
                        "<p>&nbsp;QSRP</p>",
                        "<p>&nbsp;SRPQ</p>"
                    ],
                    solution_en: "<p>82.(c) QSRP<br>Sentence Q is the starting line of the parajumble because it tells the subject of the parajumble that is about tsunami waves different in height. Then, Sentence S tells the wavelength of tsunami when it approaches land. So, S follows Q . Going through the options, only option (c) shows S follows Q . So Option (c) is the answer.</p>",
                    solution_hi: "<p>82.(c) QSRP<br>वाक्य Q parajumble की शुरुआती लाइन है क्योंकि यह parajumble के subject को बताती है जो अलग-अलग ऊंचाई वाली सुनामी लहरों के बारे में है। फिर, वाक्य S सुनामी की तरंग दैर्ध्य (wavelength) को बताता है जब यह भूमि के करीब आती है। इसलिए, Q के बाद S आएगा। विकल्पो के माध्यम से जाने पर, केवल option(c) से पता चलता है कि Q के बाद S आएगा। अतः option(c) उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the MISSPELT word.</p>",
                    question_hi: "<p>83. Select the MISSPELT word.</p>",
                    options_en: [
                        "<p>Resturant</p>",
                        "<p>Refrain</p>",
                        "<p>Resemblance</p>",
                        "<p>Retain</p>"
                    ],
                    options_hi: [
                        "<p>Resturant</p>",
                        "<p>Refrain</p>",
                        "<p>Resemblance</p>",
                        "<p>Retain</p>"
                    ],
                    solution_en: "<p>83.(a) Resturant<br>&lsquo;Restaurant&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>83.(a) Resturant<br>&lsquo;Restaurant&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>My friends / and their family members / have gone to the beach / two days ago.</p>",
                    question_hi: "<p>84. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>My friends / and their family members / have gone to the beach / two days ago.</p>",
                    options_en: [
                        "<p>have gone to the beach</p>",
                        "<p>My friends</p>",
                        "<p>two days ago</p>",
                        "<p>and their family members</p>"
                    ],
                    options_hi: [
                        "<p>have gone to the beach</p>",
                        "<p>My friends</p>",
                        "<p>two days ago</p>",
                        "<p>and their family members</p>"
                    ],
                    solution_en: "<p>84.(a) have gone to the beach<br>The given sentence is in the past tense, so it must have the verb in the past form (V<sub>2</sub>). &lsquo;Went&rsquo; is the past form. Hence, &lsquo;went to the beach&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>84.(a) have gone to the beach<br>दिया गया Sentence, past tense में है, इसलिए इसमें verb भी past form(V<sub>2</sub>) में होगी। &lsquo;Went&rsquo;, past form है। अतः, &lsquo;went to the beach&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate meaning of the idiom given in the following Question.<br>Sowing wild oats</p>",
                    question_hi: "<p>85. Select the most appropriate meaning of the idiom given in the following Question.<br>Sowing wild oats</p>",
                    options_en: [
                        "<p>inviting troubles as a boy</p>",
                        "<p>warning others as a young man</p>",
                        "<p>irresponsible pleasure seeking in young age</p>",
                        "<p>sowing grains called oats when young</p>"
                    ],
                    options_hi: [
                        "<p>inviting troubles as a boy</p>",
                        "<p>warning others as a young man</p>",
                        "<p>irresponsible pleasure seeking in young age</p>",
                        "<p>sowing grains called oats when young</p>"
                    ],
                    solution_en: "<p>85.(c) irresponsible pleasure seeking in young age. <br>Example - He went on sowing wild oats, he reaped suffering in his later life.</p>",
                    solution_hi: "<p>85.(c) irresponsible pleasure seeking in young age.<br>उदाहरण - He went on sowing wild oats, he reaped suffering in his later life./वह युवावस्था में गैरजिम्मेदार तरीके से व्यवहार करता चला गया ,उसने अपने बाद के जीवन में दुख भोगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>I prefer <span style=\"text-decoration: underline;\">walking than riding</span>.</p>",
                    question_hi: "<p>86. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>I prefer <span style=\"text-decoration: underline;\">walking than riding</span>.</p>",
                    options_en: [
                        "<p>walking with riding</p>",
                        "<p>walking above riding</p>",
                        "<p>walking from riding</p>",
                        "<p>walking to riding</p>"
                    ],
                    options_hi: [
                        "<p>walking with riding</p>",
                        "<p>walking above riding</p>",
                        "<p>walking from riding</p>",
                        "<p>walking to riding</p>"
                    ],
                    solution_en: "<p>86.(d) walking to riding<br>&lsquo;To&rsquo; is a fixed preposition used after the verb &lsquo;prefer&rsquo;. Hence, &lsquo;walking to riding&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>86.(d) walking to riding<br>&lsquo;To&rsquo; एक fixed preposition है जिसका प्रयोग verb &lsquo;prefer&rsquo; के बाद किया जाता है। इसलिए, &lsquo;walking to ride&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the word which means the same as the group of words given.<br>One who studies election trends by means of opinion polls.</p>",
                    question_hi: "<p>87. Select the word which means the same as the group of words given.<br>One who studies election trends by means of opinion polls.</p>",
                    options_en: [
                        "<p>Entomologist</p>",
                        "<p>Psephologist</p>",
                        "<p>Demagogue</p>",
                        "<p>Eugenist</p>"
                    ],
                    options_hi: [
                        "<p>Entomologist</p>",
                        "<p>Psephologist</p>",
                        "<p>Demagogue</p>",
                        "<p>Eugenist</p>"
                    ],
                    solution_en: "<p>87.(b) <strong>Psephologist</strong>- the statistical study of elections and trends in voting. <br><strong>Entomologist-</strong> one who studies insects<br><strong>Demagogue-</strong> a political leader who seeks support by appealing to popular desires and prejudices rather than by using rational argument.<br><strong>Eugenist-</strong> a specialist in Eugenics bringing about improvement in the type of offspring produced.</p>",
                    solution_hi: "<p>87.(b) <strong>Psephologist</strong>- चुनाव और मतदान में रुझान का सांख्यिकीय अध्ययन।&nbsp;<br><strong>Entomologist</strong>- जो कीड़ों का अध्ययन करता है<br><strong>Demagogue</strong>- एक राजनीतिक नेता जो तर्कसंगत तर्क का उपयोग करने के बजाय लोकप्रिय इच्छाओं और prejudices की अपील करके समर्थन चाहता है।<br><strong>Eugenist</strong>- यूजीनिक्स में एक विशेषज्ञ जो उत्पादित संतानों (offspring) में सुधार करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the option that expresses the given sentence in active voice.<br>The matches are being shown live on Sports TV.</p>",
                    question_hi: "<p>88. Select the option that expresses the given sentence in active voice.<br>The matches are being shown live on Sports TV.</p>",
                    options_en: [
                        "<p>Sports TV is showing the matches live.</p>",
                        "<p>Sports TV may be showing the matches live.</p>",
                        "<p>Sports TV will be showing the matches live.</p>",
                        "<p>Sports TV can be showing the matches live.</p>"
                    ],
                    options_hi: [
                        "<p>Sports TV is showing the matches live.</p>",
                        "<p>Sports TV may be showing the matches live.</p>",
                        "<p>Sports TV will be showing the matches live.</p>",
                        "<p>Sports TV can be showing the matches live.</p>"
                    ],
                    solution_en: "<p>88.(a) Sports TV is showing the matches live.(Correct)<br>(b) Sports TV <span style=\"text-decoration: underline;\">may be</span> showing the matches live.(Incorrect Verb)<br>(c) Sports TV <span style=\"text-decoration: underline;\">will be</span> showing the matches live.(Incorrect Tense)<br>(d) Sports TV <span style=\"text-decoration: underline;\">can be</span> showing the matches live.(Incorrect Verb)</p>",
                    solution_hi: "<p>88.(a) Sports TV is showing the matches live.(Correct)<br>(b) Sports TV <span style=\"text-decoration: underline;\">may be</span> showing the matches live.(गलत Verb)<br>(c) Sports TV <span style=\"text-decoration: underline;\">will be</span> showing the matches live.(गलत Tense)<br>(d) Sports TV <span style=\"text-decoration: underline;\">can be</span> showing the matches live.(गलत Verb)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate synonym of the given word.<br>Frontier</p>",
                    question_hi: "<p>89. Select the most appropriate synonym of the given word.<br>Frontier</p>",
                    options_en: [
                        "<p>Edge</p>",
                        "<p>landmark</p>",
                        "<p>boundary</p>",
                        "<p>comer</p>"
                    ],
                    options_hi: [
                        "<p>Edge</p>",
                        "<p>landmark</p>",
                        "<p>boundary</p>",
                        "<p>comer</p>"
                    ],
                    solution_en: "<p>89.(c) <strong>boundary</strong><br><strong>Frontier</strong>- the line where one country joins another, border<br><strong>Boundary-</strong> a dividing line<br><strong>Landmark-</strong> an object that is easily recognized from a distance<br><strong>Comer-</strong> a person who arrives somewhere</p>",
                    solution_hi: "<p>89.(c) <strong>boundary</strong><br><strong>Frontier-</strong> वह रेखा जहाँ एक देश दूसरे देश को जोड़ता है <br><strong>Boundary-</strong> एक विभाजन रेखा<br><strong>Landmark-</strong> एक वस्तु जिसे दूर से आसानी से पहचाना जा सकता है<br><strong>Comer-</strong> वह व्यक्ति जो कहीं से आता हो</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one-word substitute for the underlined word.<br>Ms Marry is a <span style=\"text-decoration: underline;\">barbarous</span> lady.</p>",
                    question_hi: "<p>90. Select the option that can be used as a one-word substitute for the underlined word.<br>Ms Marry is a <span style=\"text-decoration: underline;\">barbarous</span> lady.</p>",
                    options_en: [
                        "<p>dull</p>",
                        "<p>calm</p>",
                        "<p>matured</p>",
                        "<p>brutal</p>"
                    ],
                    options_hi: [
                        "<p>dull</p>",
                        "<p>calm</p>",
                        "<p>matured</p>",
                        "<p>brutal</p>"
                    ],
                    solution_en: "<p>90.(d) <strong>Brutal</strong>- cruel, violent, and completely without feelings.<br><strong>Barbarous</strong>- extremely cruel or unpleasant.<br><strong>Matured-</strong> to become more developed mentally and emotionally and behave in a responsible way.<br><strong>Dull</strong>- not interesting or exciting in any way.</p>",
                    solution_hi: "<p>90.(d) <strong>Brutal</strong> (क्रूर/निर्दयी)- cruel, violent, and completely without feelings.<br><strong>Barbarous </strong>(क्रूर/असभ्य)- extremely cruel or unpleasant.<br><strong>Matured</strong> (परिपक्व)- to become more developed mentally and emotionally and behave in a responsible way.<br><strong>Dull</strong> (नीरस)- not interesting or exciting in any way.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate ANTONYM of the given word.<br>Counterfeit</p>",
                    question_hi: "<p>91. Select the most appropriate ANTONYM of the given word.<br>Counterfeit</p>",
                    options_en: [
                        "<p>Unreal</p>",
                        "<p>Authentic</p>",
                        "<p>Fake</p>",
                        "<p>Imitation</p>"
                    ],
                    options_hi: [
                        "<p>Unreal</p>",
                        "<p>Authentic</p>",
                        "<p>Fake</p>",
                        "<p>Imitation</p>"
                    ],
                    solution_en: "<p>91.(b) <strong>Authentic</strong>- genuine or real, not fake.<br><strong>Counterfeit-</strong> made to deceive by closely resembling something genuine.<br><strong>Unreal-</strong> not real or imaginary.<br><strong>Fake-</strong> not genuine.<br><strong>Imitation-</strong> something made to resemble another, often as a substitute.</p>",
                    solution_hi: "<p>91.(b) <strong>Authentic</strong> (प्रामाणिक) - genuine or real, not fake.<br><strong>Counterfeit</strong> (नक़ली) - made to deceive by closely resembling something genuine.<br><strong>Unreal</strong> (अवास्तविक) - not real or imaginary.<br><strong>Fake</strong> (नकली) - not genuine.<br><strong>Imitation</strong> (नकल) - something made to resemble another, often as a substitute.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option to fill in the blank.<br>How do you_______the photos and files you have lost in your computer ?</p>",
                    question_hi: "<p>92. Select the most appropriate option to fill in the blank.<br>How do you_______the photos and files you have lost in your computer ?</p>",
                    options_en: [
                        "<p>retire</p>",
                        "<p>retrieve</p>",
                        "<p>receive</p>",
                        "<p>remove</p>"
                    ],
                    options_hi: [
                        "<p>retire</p>",
                        "<p>retrieve</p>",
                        "<p>receive</p>",
                        "<p>remove</p>"
                    ],
                    solution_en: "<p>92.(b) retrieve<br>&lsquo;Retrieve&rsquo; means get or bring (something) back from somewhere. The given sentence states a question: &ldquo;How do you retrieve the photos and files you have lost in your computer?&rdquo; Hence, &lsquo;retrieve&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(b) retrieve<br>&lsquo;Retrieve&rsquo; का अर्थ है कहीं से (कुछ) वापस लाना या प्राप्त करना। दिया गए sentence एक question पूछा गया है: &ldquo;How do you retrieve the photos and files you have lost in your computer?&rdquo;। अतः, &lsquo;retrieve&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate homophone to fill in the blank. <br>Looking up, she realised she had swum _______than she&rsquo;d thought.</p>",
                    question_hi: "<p>93. Select the most appropriate homophone to fill in the blank. <br>Looking up, she realised she had swum _______than she&rsquo;d thought.</p>",
                    options_en: [
                        "<p>farther</p>",
                        "<p>furthest</p>",
                        "<p>farthest</p>",
                        "<p>further</p>"
                    ],
                    options_hi: [
                        "<p>farther</p>",
                        "<p>furthest</p>",
                        "<p>farthest</p>",
                        "<p>further</p>"
                    ],
                    solution_en: "<p>93.(a) farther<br>&lsquo;Farther&rsquo; is the comparative degree of &lsquo;far&rsquo;, indicating at a greater distance. The given sentence states that looking up, she realised she had swum farther than she&rsquo;d thought. Hence, &lsquo;farther&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(a) farther<br>&lsquo;Far&rsquo; का comparative degree &lsquo;Farther&rsquo; होता है, जो अधिक दूरी को दर्शाता है। दिए गए sentence में कहा गया है कि ऊपर देखने पर उसे एहसास हुआ कि उसने जितना सोचा था उससे कहीं ज़्यादा दूर तैर चुकी है। अतः, &lsquo;farther&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate synonym of the given word. <br>Concise</p>",
                    question_hi: "<p>94. Select the most appropriate synonym of the given word. <br>Concise</p>",
                    options_en: [
                        "<p>Brief</p>",
                        "<p>Grief</p>",
                        "<p>Relief</p>",
                        "<p>Belief</p>"
                    ],
                    options_hi: [
                        "<p>Brief</p>",
                        "<p>Grief</p>",
                        "<p>Relief</p>",
                        "<p>Belief</p>"
                    ],
                    solution_en: "<p>94.(a) <strong>Brief</strong>- using only a few words.<br><strong>Concise-</strong> expressing information clearly in a few words.<br><strong>Grief-</strong> deep sorrow or sadness.<br><strong>Relief-</strong> feeling of comfort.<br><strong>Belief-</strong> acceptance that something is true.</p>",
                    solution_hi: "<p>94.(a) <strong>Brief</strong> (संक्षिप्त) - using only a few words.<br><strong>Concise </strong>(संक्षेप में) - expressing information clearly in a few words.<br><strong>Grief</strong> (शोक) - deep sorrow or sadness.<br><strong>Relief</strong> (राहत) - feeling of comfort.<br><strong>Belief</strong> (विश्वास) - acceptance that something is true.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "95. Select the INCORRECTLY spelt word.",
                    question_hi: "95. Select the INCORRECTLY spelt word.",
                    options_en: [
                        " Impertinent ",
                        " Unreasonable ",
                        " Despondent ",
                        " Complimentery "
                    ],
                    options_hi: [
                        " Impertinent ",
                        " Unreasonable ",
                        " Despondent ",
                        " Complimentery "
                    ],
                    solution_en: "95.(d) Complimentery<br />‘Complimentary’ is the correct spelling.",
                    solution_hi: "95.(d) Complimentery<br />‘Complimentary’ सही spelling है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: [
                        "<p>discreetly</p>",
                        "<p>occasionally</p>",
                        "<p>significantly</p>",
                        "<p>gradually</p>"
                    ],
                    options_hi: [
                        "<p>discreetly</p>",
                        "<p>occasionally</p>",
                        "<p>significantly</p>",
                        "<p>gradually</p>"
                    ],
                    solution_en: "<p>96.(c) significantly<br>&lsquo;Significantly&rsquo; means in a way that is large or important enough to be noticed. The given passage states that the internet has significantly reshaped the way information is accessed and shared. Hence, &lsquo;significantly&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) significantly<br>&lsquo;Significantly&rsquo; का अर्थ है कि यह इतना large या important है कि इस पर ध्यान दिया जा सके। दिए गए passage में कहा गया है कि internet ने information तक पहुँचने तथा उसे share करने के तरीके को महत्वपूर्ण रूप से नया आकार दिया है। अतः, &lsquo;significantly&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: [
                        "<p>hesitantly</p>",
                        "<p>periodically</p>",
                        "<p>scarcely</p>",
                        "<p>seamlessly</p>"
                    ],
                    options_hi: [
                        "<p>hesitantly</p>",
                        "<p>periodically</p>",
                        "<p>scarcely</p>",
                        "<p>seamlessly</p>"
                    ],
                    solution_en: "<p>97.(d) seamlessly<br>&lsquo;Seamlessly&rsquo; means without any sudden changes, interruptions, or problems. The given passage states that with a vast array of websites and social media platforms, individuals can seamlessly connect with others globally. Hence, &lsquo;seamlessly&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(d) seamlessly<br>&lsquo;Seamlessly&rsquo; का अर्थ है बिना किसी अचानक परिवर्तन, रुकावट(interruption) या समस्या के। दिए गए passage में कहा गया है कि websites और social media platforms की एक विशाल शृंखला(vast array) के साथ, व्यक्ति वैश्विक स्तर पर दूसरों के साथ सहजता(seamlessly) से जुड़ सकते हैं। अतः, &lsquo;seamlessly&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: [
                        "<p>numerous</p>",
                        "<p>abstract</p>",
                        "<p>irrelevant</p>",
                        "<p>intriguing</p>"
                    ],
                    options_hi: [
                        "<p>numerous</p>",
                        "<p>abstract</p>",
                        "<p>irrelevant</p>",
                        "<p>intriguing</p>"
                    ],
                    solution_en: "<p>98.(a) numerous<br>&lsquo;Numerous&rsquo; means many. The given passage states that this digital age also brings numerous challenges. Hence, &lsquo;numerous&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(a) numerous<br>&lsquo;Numerous&rsquo; का अर्थ है बहुत से। दिए गए passage में कहा गया है कि यह digital age अनेक चुनौतियाँ(challenges) भी लेकर आता है। अतः, &lsquo;numerous&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: [
                        "<p>pressing</p>",
                        "<p>soothing</p>",
                        "<p>negligible</p>",
                        "<p>dormant</p>"
                    ],
                    options_hi: [
                        "<p>pressing</p>",
                        "<p>soothing</p>",
                        "<p>negligible</p>",
                        "<p>dormant</p>"
                    ],
                    solution_en: "<p>99.(a) pressing<br>&lsquo;Pressing&rsquo; means requiring immediate attention. The given passage states that the spread of misinformation has become a pressing issue, as false content can quickly gain traction. Hence, &lsquo;pressing&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) pressing<br>&lsquo;Pressing&rsquo; का अर्थ है तत्काल ध्यान देने की आवश्यकता। दिए गए passage में कहा गया है कि गलत सूचना(misinformation) का प्रसार एक गंभीर मुद्दा(pressing issue) बन गया है, क्योंकि गलत कंटेन्ट शीघ्र ही लोकप्रिय हो जाती है। अतः, &lsquo;pressing&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :</strong><br>The internet has (96) ______reshaped the way information is accessed and shared. With a vast array of websites and social media platforms, individuals can (97) ______connect with others globally. However, this digital age also brings (98) ______challenges. The spread of misinformation has become a (99) ______ issue, as false content can quickly gain traction. It is essential for users to (100)_____ assess the credibility of sources before believing and sharing information. In this era of rapid data dissemination, cultivating media literacy is of utmost importance.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: [
                        "<p>meticulously</p>",
                        "<p>hesitantly</p>",
                        "<p>reluctantly</p>",
                        "<p>impulsively</p>"
                    ],
                    options_hi: [
                        "<p>meticulously</p>",
                        "<p>hesitantly</p>",
                        "<p>reluctantly</p>",
                        "<p>impulsively</p>"
                    ],
                    solution_en: "<p>100.(a) Meticulously<br>&lsquo;Meticulously&rsquo; means doing things very carefully and with great attention to detail. The given passage states that it is essential for users to meticulously assess the credibility of sources before believing and sharing information. Hence, &lsquo;meticulously&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) Meticulously<br>&lsquo;Meticulously&rsquo; का अर्थ है किसी भी काम को बहुत सावधानी और विस्तार से करना। दिए गए passage में कहा गया है कि उपयोगकर्ताओं के लिए यह आवश्यक है कि जानकारी पर विश्वास करने और उसे साझा करने से पहले स्रोतों की विश्वसनीयता(credibility) का बारीकी से आकलन कर लें। अतः, &lsquo;meticulously&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>