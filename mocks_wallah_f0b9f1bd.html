<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that would come next in the given series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943033964.png\" alt=\"rId5\" width=\"223\" height=\"72\"></p>",
                    question_hi: "<p>1. उस विकल्प का चयन करें जो दी गई श्रृंखला में आगे आएगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943033964.png\" alt=\"rId5\" width=\"223\" height=\"72\"></p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034068.png\" alt=\"rId6\" width=\"86\" height=\"73\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034163.png\" alt=\"rId7\" width=\"87\" height=\"74\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034308.png\" alt=\"rId8\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034415.png\" alt=\"rId9\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034068.png\" alt=\"rId6\" width=\"86\" height=\"73\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034163.png\" alt=\"rId7\" width=\"87\" height=\"74\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034308.png\" alt=\"rId8\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034415.png\" alt=\"rId9\"></p>"],
                    solution_en: "<p>1.(d) The correct option will be <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034415.png\" alt=\"rId9\"><br>Because the arrow is revolving in a clockwise direction.</p>",
                    solution_hi: "<p>1.(d) सही विकल्प होगा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034415.png\" alt=\"rId9\"><br>क्योंकि तीर दक्षिणावर्त दिशा में घूम रहा है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "2. Select the option that is related to the third word in the same way as the second word is related to the ﬁrst word.<br />Shirt : Apparel :: Necklace : ?",
                    question_hi: "2. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है।<br />कमीज : परिधान :: हार : ?",
                    options_en: [" Cham ", " Neck ", 
                                " Gold ", " Jewellery "],
                    options_hi: [" चाम", " गर्दन",
                                " सोना", " आभूषण"],
                    solution_en: "2.(d) A shirt is an apparel, similarly, a necklace is a jewellery.",
                    solution_hi: "2.(d) कमीज एक परिधान है, वैसे ही हार एक आभूषण है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the option that is best represented by the given venn diagram.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034506.png\" alt=\"rId10\" width=\"130\" height=\"119\"></p>",
                    question_hi: "<p>3. उस विकल्प का चयन करें जो दिए गए वेन आरेख द्वारा सर्वोत्तम रूप से दर्शाया जा सकता है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034506.png\" alt=\"rId10\" width=\"130\" height=\"119\"></p>",
                    options_en: ["<p>Doctor, Man, Student</p>", "<p>Family, Parents, Children</p>", 
                                "<p>Table, Chair, Furniture</p>", "<p>Gold, Silver, Ornaments</p>"],
                    options_hi: ["<p>डॉक्टर, आदमी, छात्र</p>", "<p>परिवार, माता-पिता, बच्चे</p>",
                                "<p>मेज, कुर्सी, फर्नीचर</p>", "<p>सोना, चांदी, गहने</p>"],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034506.png\" alt=\"rId10\" width=\"130\" height=\"119\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034506.png\" alt=\"rId10\" width=\"130\" height=\"119\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. Four options have been given, out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "4. चार विकल्प दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ।",
                    options_en: [" (32,13)", " (51, 24)", 
                                " (42, 15)", " (72, 45)"],
                    options_hi: [" (32,13)", " (51, 24)",
                                " (42, 15)", " (72, 45)"],
                    solution_en: "4.(a) Out of the four given options, three are alike, (51,24), (42,15), (72,45), because in all these three cases, the difference between the first and second number is <br /><math display=\"inline\"><mo>⇒</mo></math> (51 - 24) = (42 - 15) = (72 - 45) = 27,<br />But, for the number pair, (32,13), the difference = 19<br />So, the odd one is (32,13)",
                    solution_hi: "4.(a) दिए गए चार विकल्पों में से तीन, (51, 24), (42, 15), (72, 45), एक जैसे हैं, क्योंकि इन तीनों स्थितियों में पहली और दूसरी संख्या के बीच का अंतर है <br /><math display=\"inline\"><mo>⇒</mo></math> (51 - 24) = (42 - 15) = (72 - 45) = 27,<br />लेकिन, संख्या (32,13) के लिए, अंतर = 19 है,<br />तो, विषम (32,13) है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. If in a certain code language, REWARI is coded as TGYCTK. Then how is DELHI coded in the same code language?",
                    question_hi: "5. यदि एक निश्चित कूट भाषा में, REWARI को TGYCTK के रूप में कोडित किया जाता है। तो उसी कोड भाषा में DELHI को कैसे कोडित किया जाएगा?",
                    options_en: [" FGNJK", " FGMJK", 
                                " FGOJK", " FGLJK"],
                    options_hi: [" FGNJK", " FGMJK",
                                " FGOJK", " FGLJK"],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034620.png\" alt=\"rId11\" width=\"178\" height=\"97\"><br>Similarly, DELHI will be coded in the same code language as <math display=\"inline\"><mo>&#8594;</mo></math> FGNJK<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034713.png\" alt=\"rId12\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034620.png\" alt=\"rId11\" width=\"178\" height=\"97\"><br>इसी प्रकार DELHI को उसी कोड भाषा में कोडित किया जाएगा जैसे <math display=\"inline\"><mo>&#8594;</mo></math> FGNJK<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034713.png\" alt=\"rId12\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a row of students, one student is tenth from either end of the row. How many students are there in the row?</p>",
                    question_hi: "<p>6. छात्रों की एक पंक्ति में एक छात्र पंक्ति के दोनों छोर से दसवें स्थान पर है। पंक्ति में कितने छात्र हैं?</p>",
                    options_en: ["<p>20</p>", "<p>22</p>", 
                                "<p>19</p>", "<p>16</p>"],
                    options_hi: ["<p>20</p>", "<p>22</p>",
                                "<p>19</p>", "<p>16</p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034815.png\" alt=\"rId13\" width=\"240\" height=\"87\"><br>In a row of students, one student is tenth from either end of the row. <br>So, from the above diagram, we can say that there are (9+1+9) = 19 students in the row.</p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943034916.png\" alt=\"rId14\" width=\"244\" height=\"63\"><br>विद्यार्थियों की एक पंक्ति में, एक विद्यार्थी पंक्ति के किसी भी छोर से दसवें स्थान पर है।<br>अतः, उपरोक्त आरेख से हम कह सकते हैं कि पंक्ति में (9+1+9) = 19 विद्यार्थी हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "7. Four letter clusters have been given. out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "7. चार अक्षर समूह दिए गए हैं। जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ।",
                    options_en: [" PSRQ", " VYXW", 
                                " CGEF", " JMLK"],
                    options_hi: [" PSRQ", " VYXW",
                                " CGEF", " JMLK"],
                    solution_en: "7.(c) Out of the four letter clusters three are alike: PSRQ, VYXW, JMLK<br />Because all these three letter-cluster follows the pattern,<br />Fourth letter = first letter +1<br />Third letter = fourth letter +1<br />Second letter = third letter +1<br />But the letter-cluster, CGEF is different from the other three.",
                    solution_hi: "7.(c) चार अक्षर समूहों में से तीन एक जैसे हैं: PSRQ, VYXW, JMLK<br />क्योंकि ये तीनों अक्षर-समूह पैटर्न का अनुसरण करते हैं,<br />चौथा अक्षर = पहला अक्षर+1<br />तीसरा अक्षर = चौथा अक्षर+1<br />दूसरा अक्षर = तीसरा अक्षर+1<br />लेकिन अक्षर समूह, CGEF अन्य तीन से भिन्न है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "8. Select the option that is related to the third word in the same way as the second word is related to the ﬁrst word.<br />Bihar : Jharkhand :: Chattisgarh :?",
                    question_hi: "8. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है।<br />बिहार : झारखंड :: छत्तीसगढ़ :?",
                    options_en: [" Maharashtra ", " Madhya Pradesh ", 
                                " Ranchi", " Raipur "],
                    options_hi: [" महाराष्ट्र", " मध्य प्रदेश",
                                " रांची", " रायपुर"],
                    solution_en: "8.(b) Jharkhand came into existence after being separated from Bihar. Similarly, Chhattisgarh was separated from Madhya pradesh.",
                    solution_hi: "8.(b) बिहार से अलग होकर झारखंड अस्तित्व में आया। इसी तरह छत्तीसगढ़ को मध्य प्रदेश से अलग कर दिया गया।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>1. All the cars are four-wheelers.<br>2. All the four-wheelers are vehicles.<br><strong>Conclusions:</strong><br>I All the vehicles are four wheelers.<br>II. All the cars are vehicles.</p>",
                    question_hi: "<p>9. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है। भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होता हो। बताइये कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>1. सभी कारें चौपहिया हैं।<br>2. सभी चौपहिया वाहन हैं।<br><strong>निष्कर्ष:</strong><br>I सभी वाहन चौपहिया हैं।<br>II.सभी कार वाहन हैं।</p>",
                    options_en: ["<p>Only I</p>", "<p>Both I and II</p>", 
                                "<p>Only II</p>", "<p>Either I or II</p>"],
                    options_hi: ["<p>केवल I</p>", "<p>I और II दोनों</p>",
                                "<p>केवल II</p>", "<p>या तो I अथवा II</p>"],
                    solution_en: "<p>9.(c) From the given statements we can draw the following venn diagram<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035031.png\" alt=\"rId15\" width=\"194\" height=\"128\"><br>So, from the diagram we can say that only conclusion II follows.</p>",
                    solution_hi: "<p>9.(c) दिए गए कथनों से हम निम्नलिखित वेन आरेख बना सकते हैं<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035189.png\" alt=\"rId16\" width=\"204\" height=\"135\"><br>अत: आरेख से हम कह सकते हैं कि केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. Select the number from the given options that can replace the question mark (?) below.<br />If 243 (222) 317, then 548 (?) 621",
                    question_hi: "10. दिए गए विकल्पों में से वह संख्या चुनिए जो नीचे दिए गए प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकती है। <br />यदि 243 (222) 317, तो 548 (?) 621",
                    options_en: [" 211", " 209", 
                                " 210", " 219"],
                    options_hi: [" 211", " 209",
                                " 210", " 219"],
                    solution_en: "10.(d) Given, 243 (222) 317,<br />i.e.<math display=\"inline\"><mo>{</mo></math>(317 - 243) × 3} = 74 × 3 = 222<br />Then, 548 (?) 621 <math display=\"inline\"><mo>⇒</mo></math><br />= <math display=\"inline\"><mo>{</mo><mo>(</mo><mn>621</mn><mo>-</mo><mn>548</mn><mo>)</mo><mo>×</mo><mn>3</mn><mo>}</mo><mo>=</mo><mn>73</mn><mo>×</mo><mn>3</mn><mo>=</mo><mn>219</mn></math><br />So, the pattern will be 548 (219) 621.",
                    solution_hi: "10.(d) दिया गया है, 243 (222) 317,<br />i.e. <math display=\"inline\"><mo>{</mo><mo>(</mo><mn>317</mn><mo>-</mo><mn>243</mn><mo>)</mo><mo>×</mo><mn>3</mn><mo>}</mo><mo>=</mo><mn>74</mn><mo>×</mo><mn>3</mn><mo>=</mo><mn>222</mn></math><br />तब, 548 (?) 621 <math display=\"inline\"><mo>⇒</mo></math><br />= <math display=\"inline\"><mo>{</mo><mo>(</mo><mn>621</mn><mo>-</mo><mn>548</mn><mo>)</mo><mo>×</mo><mn>3</mn><mo>}</mo><mo>=</mo><mn>73</mn><mo>×</mo><mn>3</mn><mo>=</mo><mn>219</mn></math><br />तो, पैटर्न होगा, 548 (219) 621.",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. How many D\'s are there in the following series which are immediately followed by W but NOT immediately preceded by K?<br />KDCWKDWNKGDWWDHKVDWZDW",
                    question_hi: "11. निम्नलिखित श्रृंखला में ऐसे कितने D हैं जिनके ठीक बाद W है लेकिन ठीक पहले K नहीं है?<br />KDCWKDWNKGDWWDHKVDWZDW",
                    options_en: [" 1", " 3", 
                                " 2", " 4"],
                    options_hi: [" 1", " 3",
                                " 2", " 4"],
                    solution_en: "11.(b) The given series is,<br />KDCWKDWNKGDWWDHKVDWZDW<br />So, clearly total 3 D\'s are there in the above series which are immediately followed by W but not immediately preceded by K.",
                    solution_hi: "11.(b) दी गई श्रृंखला है,<br />KDCWKDWNKGDWWDHKVDWZDW<br />तो, स्पष्ट रूप से उपरोक्त श्रृंखला में कुल 3 D हैं जिनके ठीक बाद W है लेकिन ठीक पहले K नहीं है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. In a certain code language CRUDE is written as BSTED. How is MOIST written in that language?",
                    question_hi: "12. एक निश्चित भाषा में CRUDE को BSTED लिखा जाता है। उस भाषा में MOIST कैसे लिखा जाएगा ?",
                    options_en: [" NNJRU", " LPHTS", 
                                " LNHRS", " NPJTU "],
                    options_hi: [" NNJRU", " LPHTS",
                                " LNHRS", " NPJTU "],
                    solution_en: "12.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035423.png\" alt=\"rId17\" /><br />Similarly,<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035516.png\" alt=\"rId18\" /><br />So, MOIST will be written in that language as : LPHTS",
                    solution_hi: "12.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035423.png\" alt=\"rId17\" /><br />उसी प्रकार,<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035516.png\" alt=\"rId18\" /><br />इसलिए, MOIST उस भाषा में इस प्रकार लिखा जाएगा : LPHTS",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. Indicating to a woman, a man said, \"Her father is the only son of my father.\" How is the man related to the woman?",
                    question_hi: "13. एक महिला की ओर इशारा करते हुए, एक आदमी ने कहा \"उसके पिता मेरे पिता के इकलौते पुत्र हैं।\" पुरुष का महिला से क्या संबंध है?",
                    options_en: [" Grandfather ", " Brother ", 
                                " Son ", " Father "],
                    options_hi: [" दादा", " भाई",
                                " पुत्र ", " पिता "],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035628.png\" alt=\"rId19\" width=\"86\" height=\"204\"><br>From the above diagram, we can say that the man is the father of the woman.</p>",
                    solution_hi: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035717.png\" alt=\"rId20\" width=\"84\" height=\"213\"><br>उपरोक्त आरेख से हम कह सकते हैं कि पुरुष महिला का पिता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Five persons A, B, C, D and E are sitting one above the other on a ladder (not necessarily in the same order). B is sitting above A with one person sitting between them. Only two persons are sitting between A and C. If C is not sitting at top. Then who is sitting in the middle?</p>",
                    question_hi: "<p>14. पांच व्यक्ति A, B, C, D और E एक सीढ़ी पर एक के ऊपर एक बैठे हैं (जरूरी नहीं कि इसी क्रम में हों)। B, A के ऊपर बैठा है और उनके बीच एक व्यक्ति बैठा है। A और C के बीच केवल दो व्यक्ति बैठे हैं। यदि C शीर्ष पर नहीं बैठा है। फिर मध्य में कौन बैठा है?</p>",
                    options_en: ["<p>B</p>", "<p>E</p>", 
                                "<p>D</p>", "<p>C</p>"],
                    options_hi: ["<p>B</p>", "<p>E</p>",
                                "<p>D</p>", "<p>C</p>"],
                    solution_en: "<p>14.(a) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035790.png\" alt=\"rId21\" width=\"231\" height=\"204\"><br>We can see in the diagram that B is sitting in the middle.</p>",
                    solution_hi: "<p>14.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035790.png\" alt=\"rId21\" width=\"231\" height=\"204\"><br>हम आरेख में देख सकते हैं कि B बीच में बैठा है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "15. A team is to be selected from 13 players P1, P2, P3, P4, P5, P6, P7, P8, P9, P10, P11, P12 and P13. There will be seven players in the team. P2 cannot be selected with P1, P6 or P4. P7 cannot be selected with P2, P10, P11 or P13. If P8 and P13 both are selected, then P5 must be selected. P4 cannot be selected with P2, P6, P12 or P11.<br />Which of the following is a connected selection of the team?",
                    question_hi: "15. 13 खिलाड़ियों P1, P2, P3, P4, P5, P6, P7, P8, P9, P10, P11, P12 और P13 में से एक टीम का चयन किया जाना है। टीम में सात खिलाड़ी होंगे। P1, P6 अथवा P4 के साथ P2 को नही चुना जा सकता है। P2, P10, P11 अथवा P13 के साथ P7 को नही चुना जा सकता है। यदि P8 और P13 दोनों को चुना जाता है तो P5 को भी अवश्य चुना जाएगा ।<br />निम्नलिखित में से कौन कौन चयनित टीम में है?",
                    options_en: [" P1, P3, P4, P5, P6, P8, P9", " P1, P3, P4, P5, P8, P9, P13", 
                                " P2, P3, P5, P7, P8, P9, P13", " P1, P6, P11, P12, P13, P3, P4"],
                    options_hi: [" P1, P3, P4, P5, P6, P8, P9", " P1, P3, P4, P5, P8, P9, P13",
                                " P2, P3, P5, P7, P8, P9, P13", " P1, P6, P11, P12, P13, P3, P4"],
                    solution_en: "15.(b) From the given instructions, we can summarize that if P7 is in the team then P13 cannot be selected for the team, also if P4 is in the team then P2, P6, P12, P11 cannot be selected for the team.<br />In option (a)<math display=\"inline\"><mo>→</mo></math> P1, P3, P4, P5, P6, P8, P9 → P4 and P6 are together, so it is wrong.<br />In option (c)<math display=\"inline\"><mo>→</mo></math> P2, P3, P5, P7, P8, P9, P13 → P7 and P13 are together, so it is wrong.<br />In option (d)<math display=\"inline\"><mo>→</mo></math> P1, P6, P11, P12, P13, P3, P4 → again P4 and P6 are together, so it is wrong.<br />Then the only option (b) which is correct, is: P1, P3, P4, P5, P8, P9, P13.",
                    solution_hi: "15.(b) दिए गए निर्देश से हम संक्षेप में बता सकते हैं कि यदि P7 टीम में है तो P13 को टीम के लिए नहीं चुना जा सकता है, यदि P4 टीम में है तो P2, P6, P12, P11 को टीम के लिए नहीं चुना जा सकता है।<br />विकल्प (a) में P1, P3, P4, P5, P6, P8, P9, P4 और P6 एक साथ हैं, इसलिए यह गलत है।<br />विकल्प (c) में P2, P3, P5, P7, P8, P9, P13 <math display=\"inline\"><mo>→</mo></math> P7 और P13 एक साथ हैं, इसलिए यह गलत है।<br />विकल्प (d) में P1, P6, P11, P12, P13, P3, P4 फिर से P4 और P6 एक साथ हैं, इसलिए यह गलत है।<br />तब एकमात्र विकल्प (b) जो सही है वह है:  P1, P3, P4, P5, P8, P9, P13.",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "16. Cariappa, Kargil, Katari, Manekshaw and Subrto are five different sedans in a sainik school. Kargil is to the right of Subrata and Katari is to the left of Cariappa and right of Kargil. Subrata is to the right of Manekshaw. Which sadan is in the middle?",
                    question_hi: "16. करियप्पा, कारगिल, कटारी, मानेकशॉ और सुब्रतो एक सैनिक स्कूल में पांच अलग-अलग सदन हैं। कारगिल सुब्रत के दाईं ओर है और कटारी करियप्पा के बाईं ओर है और कारगिल के दाईं ओर है। सुब्रत, मानेकशॉ के दायीं ओर है। बीच में कौन सा सदन है?",
                    options_en: [" Katari ", " Subrto ", 
                                " Kargil ", " Cariappa "],
                    options_hi: [" कटारी", " सुब्रतो",
                                " कारगिल", " करियप्पा"],
                    solution_en: "16.(c) From the given information we can draw the following diagram,<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943035922.png\" alt=\"rId22\" /><br />Here we can see the sadan ‘Kargil’ is in the middle.",
                    solution_hi: "16.(c) दी गई जानकारी से हम निम्नलिखित आरेख बना सकते हैं,<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036012.png\" alt=\"rId23\" /><br />तो, यहाँ हम देख सकते हैं कि सदन \'कारगिल\' बीच में है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "17. Select the venn diagram that best represents the relationship between the given set of classes.<br />Apple, Mango, Fruits",
                    question_hi: "17. वेन आरेख का चयन कीजिये जो दिए गए वर्गों के बीच संबंध का सबसे अच्छा प्रतिनिधित्व करता है।<br />सेब, आम, फल",
                    options_en: [" <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036105.png\" alt=\"rId24\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036182.png\" alt=\"rId25\" />", 
                                " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036280.png\" alt=\"rId26\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036387.png\" alt=\"rId27\" />"],
                    options_hi: [" <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036105.png\" alt=\"rId24\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036182.png\" alt=\"rId25\" />",
                                " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036280.png\" alt=\"rId26\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036387.png\" alt=\"rId27\" />"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036507.png\" alt=\"rId28\" width=\"224\" height=\"128\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036639.png\" alt=\"rId29\" width=\"240\" height=\"137\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. A statement is given followed by two arguments. Decide which of the arguments is/are strong with respect to the statement.<br><strong>Statement:</strong><br>Life expectancy of Indians is increasing.<br><strong>Arguments:</strong><br>I. Yes. People are getting better medical facilities.<br>II. Yes. People are doing more physical exercises now.</p>",
                    question_hi: "<p>18. एक कथन के बाद दो तर्क दिए गए हैं। बताइये कि कथन के संबंध में कौन सा तर्क अनुसरण करता हैं।<br><strong>कथन:</strong><br>भारतीयों की जीवन प्रत्याशा बढ़ रही है।<br><strong>तर्क:</strong><br>I. हां, लोगों को बेहतर चिकित्सा सुविधा मिल रही है।<br>II. हां, लोग अब अधिक शारीरिक व्यायाम कर रहे हैं।</p>",
                    options_en: ["<p>Neither I nor II is strong</p>", "<p>Both arguments I and II are strong</p>", 
                                "<p>Only argument II is strong.</p>", "<p>Only argument I is strong.</p>"],
                    options_hi: ["<p>न तो I और न ही II अनुसरण करते है</p>", "<p>तर्क I और II दोनों अनुसरण करते हैं।</p>",
                                "<p>केवल तर्क II अनुसरण करता है।</p>", "<p>केवल तर्क I अनुसरण करता है।</p>"],
                    solution_en: "<p>18.(b) Here both the conclusions i.e. medical facilities and physical exercises directly influence the life expectancy of a person. So, both arguments I and II are strong.</p>",
                    solution_hi: "<p>18.(b) यहाँ दोनों निष्कर्ष, अर्थात चिकित्सा सुविधाएं और शारीरिक व्यायाम, एक व्यक्ति की जीवन प्रत्याशा को सीधे प्रभावित करते हैं। अत: तर्क I और II दोनों प्रबल हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. Select the word from the options, which is similar to the given words in a certain manner:<br />Stable, Burrow, Nest",
                    question_hi: "19. विकल्पों में से उस शब्द का चयन कीजिए जो एक निश्चित तरीके से दिए गए शब्दों के समान है।<br />अस्तबल, मांद, घोंसला",
                    options_en: [" Den ", " Slum ", 
                                " City ", " Herd "],
                    options_hi: [" खोह", " गंदी बस्ती",
                                " शहर ", " झुण्ड "],
                    solution_en: "19.(a) Stable, burrow, nest all are the places where animals stay. Similarly from the given options, den means wild mammal’s home.",
                    solution_hi: "19.(a) अस्तबल, मांद, घोसला सभी ऐसे स्थान हैं जहां जानवर रहते हैं। इसी प्रकार दिए गए विकल्पों में से खोह का अर्थ है जंगली स्तनपायी का घर।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "20. Select the number from among the given options that can replace the question mark (?) in the following series.<br />22, 23, 26, 27, 30, 31, ?",
                    question_hi: "20. दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सकती है।<br />22, 23, 26, 27, 30, 31, ?",
                    options_en: [" 33", " 34", 
                                " 35", " 31"],
                    options_hi: [" 33", " 34",
                                " 35", " 31"],
                    solution_en: "20.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036731.png\" alt=\"rId30\" /><br />mber from among the given options that can replace the question mark in the above series is 34.",
                    solution_hi: "20.(b)<br /> <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036731.png\" alt=\"rId30\" /><br />अतः दिए गए विकल्पों में से वह संख्या जो उपरोक्त श्रृंखला में प्रश्नवाचक चिन्ह को प्रतिस्थापित कर सकती है, 34 है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. Select the most appropriate option to ﬁll in the blank.<br />W S O K ___",
                    question_hi: "21. रिक्त स्थान में के लिए सबसे उपयुक्त विकल्प का चयन कीजिये। <br />W S O K ___",
                    options_en: [" L ", " G", 
                                " H ", " T"],
                    options_hi: [" L ", " G",
                                " H ", " T"],
                    solution_en: "21.(b) W S O K ___<br />Here the second letter = place value of first letter - 4 <br />the third letter = place value of second letter - 4 <br />the fourth letter = place value of third letter - 4 <br />So, the fifth letter will be = place value of fourth letter - 4 = (11 - 4) = 7 = G",
                    solution_hi: "21.(b) W S O K ___<br />यहाँ दूसरा अक्षर = पहले अक्षर का स्थानीय मान - 4<br />तीसरा अक्षर = दूसरे अक्षर का स्थानीय मान - 4<br />चौथा अक्षर = तीसरे अक्षर का स्थानीय मान - 4<br />तो, पाँचवाँ अक्षर होगा = चौथे अक्षर का स्थानीय मान - 4 = (11 - 4) = 7 = G",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "22. Arrange the given words in alphabetical order.<br />A) mild B) moderate C) severe D) profound ",
                    question_hi: "22. दिए गए शब्दों को वर्णानुक्रम में व्यवस्थित करें।<br />A) मृदु B) मध्यम C) गंभीर D) प्रगाढ़",
                    options_en: [" A, B, D, C", " A, C, B, D", 
                                " A, B, C, D", " A, D, B, C"],
                    options_hi: [" A, B, D, C", " A, C, B, D",
                                " A, B, C, D", " A, D, B, C"],
                    solution_en: "22.(a) After arranging the given words in alphabetical order,<br />Mild, Moderate, Severe, Profound <math display=\"inline\"><mo>→</mo></math> A, B, D, C will be the correct sequence.",
                    solution_hi: "22.(a) दिए गए शब्दों को वर्णानुक्रम में व्यवस्थित करने के बाद,<br />Mild, Moderate, Severe, Profound <math display=\"inline\"><mo>→</mo></math> A, B, D, C सही क्रम होगा।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "23. Select the number from above the given options that can replace the question mark (?) in the following series.<br />7, 12, 19, ?, 39 ",
                    question_hi: "23. दिए गए विकल्पों में से उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है।<br />7, 12, 19, ?, 39 ",
                    options_en: [" 24", " 26", 
                                " 28", " 29"],
                    options_hi: [" 24", " 26",
                                " 28", " 29"],
                    solution_en: "23.(c) Here the pattern is as follows:<br />7+5 = 12, 12+7 = 19, 19+9 = 28, 28+11 = 39<br />So, the number that can replace the question mark in the following series is 28.",
                    solution_hi: "23.(c) यहाँ पैटर्न इस प्रकार है:<br />7+5 = 12, 12+7 = 19, 19+9 = 28, 28+11 = 39<br />अतः, निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह को प्रतिस्थापित करने वाली संख्या 28 है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. D has a brother A. D is the son of C, B is C&rsquo;s father. In terms of relationship, what is A of B?</p>",
                    question_hi: "<p>24. D का एक भाई A है। D, C का पुत्र है, B, C का पिता है। संबंध के अनुसार, B का A क्या है?</p>",
                    options_en: ["<p>Son</p>", "<p>Grandfather</p>", 
                                "<p>Brother</p>", "<p>Grandson</p>"],
                    options_hi: ["<p>पुत्र</p>", "<p>दादा</p>",
                                "<p>भाई</p>", "<p>पोता</p>"],
                    solution_en: "<p>24.(d) From the given information we can draw the following diagram,<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036824.png\" alt=\"rId31\" width=\"170\" height=\"181\"><br>So, A is the grandson of B.</p>",
                    solution_hi: "<p>24.(d) दी गई जानकारी से हम निम्नलिखित आरेख बना सकते हैं,<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727943036824.png\" alt=\"rId31\" width=\"170\" height=\"181\"><br>अत: A, B का पोता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. If <math display=\"inline\"><mo>&#215;</mo></math> stands for addition, &divide; stands for subtraction, + stands for multiplication and - stands for division, then 10&times;4&divide;4-2+1= ?</p>",
                    question_hi: "<p>25. यदि <math display=\"inline\"><mo>&#215;</mo></math> का अर्थ योग है, &divide; का अर्थ घटाना है, + का अर्थ गुणा है और - का अर्थ भाग है तो 10&times;4&divide;4-2+1= ?</p>",
                    options_en: ["<p>40</p>", "<p>12.5</p>", 
                                "<p>5</p>", "<p>12</p>"],
                    options_hi: ["<p>40</p>", "<p>12.5</p>",
                                "<p>5</p>", "<p>12</p>"],
                    solution_en: "<p>25.(d) After putting the correct signs,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><mo>&#215;</mo><mn>4</mn><mo>&#247;</mo><mn>4</mn><mo>-</mo><mn>2</mn><mo>+</mo><mn>1</mn></math><br>= <math display=\"inline\"><mn>10</mn><mo>+</mo><mn>4</mn><mo>-</mo><mn>4</mn><mo>&#247;</mo><mn>2</mn><mo>&#215;</mo><mn>1</mn></math><br>= <math display=\"inline\"><mn>14</mn><mo>-</mo><mn>2</mn></math> = 12</p>",
                    solution_hi: "<p>25.(d) सही संकेत लगाने के बाद,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><mo>&#215;</mo><mn>4</mn><mo>&#247;</mo><mn>4</mn><mo>-</mo><mn>2</mn><mo>+</mo><mn>1</mn></math><br>= <math display=\"inline\"><mn>10</mn><mo>+</mo><mn>4</mn><mo>-</mo><mn>4</mn><mo>&#247;</mo><mn>2</mn><mo>&#215;</mo><mn>1</mn></math><br>= <math display=\"inline\"><mn>14</mn><mo>-</mo><mn>2</mn></math> = 12</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>