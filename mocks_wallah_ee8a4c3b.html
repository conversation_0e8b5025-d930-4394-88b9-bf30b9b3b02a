<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math>, then find the value of b.</p>",
                    question_hi: "<p>1. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math>, तो b का मान ज्ञात कीजिये ?</p>",
                    options_en: ["<p>-5</p>", "<p>5</p>", 
                                "<p>2</p>", "<p>-2</p>"],
                    options_hi: ["<p>-5</p>", "<p>5</p>",
                                "<p>2</p>", "<p>-2</p>"],
                    solution_en: "<p>1.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mrow><mo>(</mo><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>(</mo><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow></mfrac></math>= a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>+</mo><mn>12</mn><mo>-</mo><mn>12</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>18</mn><mo>-</mo><mn>12</mn></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>-</mo><mn>12</mn><msqrt><mn>6</mn></msqrt></mrow><mn>6</mn></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>5</mn><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt><mo>=</mo><mi>a</mi><mo>+</mo><mi>b</mi><msqrt><mn>6</mn></msqrt></math><br>On comparing both sides we get, b = - 2</p>",
                    solution_hi: "<p>1.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mrow><mo>(</mo><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>(</mo><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow></mfrac></math>= a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>+</mo><mn>12</mn><mo>-</mo><mn>12</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>18</mn><mo>-</mo><mn>12</mn></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>-</mo><mn>12</mn><msqrt><mn>6</mn></msqrt></mrow><mn>6</mn></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>5</mn><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt><mo>=</mo><mi>a</mi><mo>+</mo><mi>b</mi><msqrt><mn>6</mn></msqrt></math><br>दोनों तरफ की तुलना करने पर हमें प्राप्त होता है,<br>b = - 2</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. The missing number in the following table is ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010074419.png\" alt=\"rId5\" width=\"160\" height=\"86\"></p>",
                    question_hi: "<p>2. निम्नलिखित तालिका में लुप्त संख्या क्या है? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010074419.png\" alt=\"rId5\" width=\"160\" height=\"86\"></p>",
                    options_en: ["<p>15</p>", "<p>39</p>", 
                                "<p>12</p>", "<p>37</p>"],
                    options_hi: ["<p>15</p>", "<p>39</p>",
                                "<p>12</p>", "<p>37</p>"],
                    solution_en: "<p>2.(b) <strong>Logic column wise :-</strong> <br>(R<sub>1</sub>)<sup>2</sup> -&nbsp;(R<sub>2</sub>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn>3</mn></msub></math><br>In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup></math>column &rArr; 4&sup2; - 3&sup2; =16 - 9 = 7<br>In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mi>n</mi><mi>d</mi></mrow></msup></math>column &rArr; 6&sup2; - 5&sup2; = 36 - 25 = 11<br>Similarly, <br>In&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mrow><mi>r</mi><mi>d</mi></mrow></msup></math> column &rArr; 8&sup2; - 5&sup2; = 64 - 25 = <strong>39</strong></p>",
                    solution_hi: "<p>2.(b) तर्क :स्तम्भ के अनुसार :- <br>(R<sub>1</sub>)<sup>2</sup> -&nbsp;(R<sub>2</sub>)<sup>2</sup> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn>3</mn></msub></math><br>पहले स्तम्भ में &rArr; 4&sup2; - 3&sup2; =16 - 9 = 7<br>दूसरे स्तम्भ में &rArr; 6&sup2; - 5&sup2; = 36 - 25 = 11<br>इसी प्रकार,<br>तीसरे स्तम्भ में&rArr; 8&sup2; - 5&sup2; = 64 - 25 = <strong>39</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Which is the most popular festival among the Garos tribe of Meghalaya?</p>",
                    question_hi: "<p>3. मेघालय की गारो जनजाति के बीच सबसे लोकप्रिय त्योहार कौन सा है?</p>",
                    options_en: ["<p>Wangala Festival</p>", "<p>Yaoshang Festival</p>", 
                                "<p>Porag Festival</p>", "<p>Nuakhai Festival</p>"],
                    options_hi: ["<p>वांगला महोत्सव</p>", "<p>योशांग महोत्सव</p>",
                                "<p>पोराग महोत्सव</p>", "<p>नुआखाई महोत्सव</p>"],
                    solution_en: "<p>3.(a) <strong>Wangala Festival</strong> (a 100 drums festival) - A harvest festival held in honour of Saljong, the Sun-God. Nuakhai (Odisha)- It is celebrated to welcome the approaching new season and welcome the season&rsquo;s new rice. Parag (Assam)- It is a five-day post -harvest festival celebrated by the Misings of Assam. This festival is also known as the Nara Singha Bihu festival. Yaoshang (Manipur)- It is celebrated in Manipur for five days in spring. Thabal Chongba (Dancing in the Moonlight) dance is performed in this festival.</p>",
                    solution_hi: "<p>3.(a) <strong>वांगला महोत्सव</strong> (100 ड्रमों का त्यौहार) - सूर्य-देवता सालजोंग के सम्मान में आयोजित एक फसल उत्सव। नुआखाई (ओडिशा) - आने वाले नए मौसम का स्वागत करने और मौसम के नए चावल का स्वागत करने के लिए मनाया जाता है। पराग (असम) - यह असम के मिसिंग्स द्वारा मनाया जाने वाला पांच दिवसीय कटाई के बाद का त्योहार है। इस त्योहार को नारा सिंहा बिहू उत्सव के नाम से भी जाना जाता है। योशांग (मणिपुर) - यह मणिपुर में वसंत ऋतु में पांच दिनों तक मनाया जाता है। इस उत्सव में थबल चोंगबा (चांदनी में नृत्य) नृत्य किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. If a : (b + c) = 1 : 3 and c : (a + b) = 5 : 7, find the value of b : (c + a).</p>",
                    question_hi: "<p>4. यदि a : (b + c) = 1 : 3 और c : (a + b) = 5 : 7 है, तो b : (c + a) का मान ज्ञात कीजिए |</p>",
                    options_en: ["<p>4 : 5</p>", "<p>3 : 5</p>", 
                                "<p>1 : 2</p>", "<p>3 : 4</p>"],
                    options_hi: ["<p>4 : 5</p>", "<p>3 : 5</p>",
                                "<p>1 : 2</p>", "<p>3 : 4</p>"],
                    solution_en: "<p>4.(c) <br><math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mi>b</mi><mo>+</mo><mi>c</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>3</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>9</mn></mfrac></math>&hellip;&hellip; e.q .(1)<br><math display=\"inline\"><mfrac><mrow><mi>c</mi></mrow><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math>&hellip;&hellip; e.q .(2)<br>from e.q .(1) and e.q .(2) , we get <br>a = 3 , b = 4 and c = 5<br>So , b : (c + a) = <math display=\"inline\"><mn>4</mn><mi>&#160;</mi><mo>:</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>)</mo></math> = 1 : 2</p>",
                    solution_hi: "<p>4.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>a</mi><mrow><mi>b</mi><mo>+</mo><mi>c</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>3</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>9</mn></mfrac></math>&hellip;&hellip; समीकरण(1)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>c</mi><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math>&nbsp;&hellip;&hellip; e.q .(2)<br>समीकरण (1) और समीकरण (2) से हम पाते हैं <br>a = 3 , b = 4 और c = 5<br>इसलिए, b : (c + a) = <math display=\"inline\"><mn>4</mn><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mo>(</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>)</mo></math> = 1 : 2</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. Read the given statements and conclusions carefully and decide which of the given conclusions logically follows (s) from the statements.<br><strong>Statements1: </strong>Some carts are horse carts.<br><strong>Statements 2 :</strong> All carts are handcart<br><strong>Conclusion I: </strong>All horse carts are handcarts<br><strong>Conclusion II:</strong> Some handcarts are horse carts.</p>",
                    question_hi: "<p>5. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन करें, और तय करें कि कौन से निष्कर्ष तार्किक रूप से कथनों का पालन करते हैं?<br><strong>कथन 1 :</strong> कुछ गाड़ी घोड़ागाड़ी हैं।<br><strong>कथन 2 : </strong>सभी गाडी ठेलागाड़ी हैं।<br><strong>निष्कर्ष I :</strong> सभी घोड़ागाड़ी ठेलागाड़ी हैं।<br><strong>निष्कर्ष II :</strong> कुछ ठेलागाड़ी घोड़ागाड़ी है।</p>",
                    options_en: ["<p>Only conclusion I follows</p>", "<p>Only conclusion II follows.</p>", 
                                "<p>neither conclusion I Nor II follows.</p>", "<p>Both conclusion I and II follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I लागू होता है</p>", "<p>केवल निष्कर्ष II लागू होता है।</p>",
                                "<p>न तो । और न ही II लागू होते हैं।</p>", "<p>I और II दोनों लागू होते हैं।</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010074664.png\" alt=\"rId6\" width=\"181\" height=\"76\"><br>Only conclusion II follow.</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010074830.png\" alt=\"rId7\" width=\"203\" height=\"82\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. Which of the following is produced as a waste product during the process of photosynthesis?</p>",
                    question_hi: "<p>6. प्रकाश संश्लेषण की प्रक्रिया के दौरान निम्नलिखित में से किसका उत्पादन अपशिष्ट उत्पाद के रूप में होता है?</p>",
                    options_en: ["<p>Urea</p>", "<p>oxygen</p>", 
                                "<p>carbon dioxide</p>", "<p>Nitrogen</p>"],
                    options_hi: ["<p>यूरिया</p>", "<p>ऑक्सीजन</p>",
                                "<p>कार्बन डाइऑक्साइड</p>", "<p>नाइट्रोजन</p>"],
                    solution_en: "<p>6.(b) <strong>Oxygen. Photosynthesis </strong>converts carbon dioxide and water into oxygen and glucose in the presence of sunlight and chlorophyll. Glucose is used as food by the plant and oxygen is a by-product. The process of photosynthesis is commonly written as : <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mn>6</mn><msub><mi>CO</mi><mn>2</mn></msub><mo>+</mo><mn>6</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi><mo>&#8594;</mo><msub><mi mathvariant=\"normal\">C</mi><mn>6</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>12</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>6</mn></msub><mo>+</mo><mn>6</mn><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub></math>.</p>",
                    solution_hi: "<p>6.(b) <strong>ऑक्सीजन। प्रकाश संश्लेषण </strong>कार्बन डाइऑक्साइड और जल को सूर्य के प्रकाश और क्लोरोफिल की उपस्थिति में ऑक्सीजन और ग्लूकोज में परिवर्तित करता है। ग्लूकोज का उपयोग पौधे द्वारा भोजन के रूप में किया जाता है और ऑक्सीजन एक उप-उत्पाद है। प्रकाश संश्लेषण की प्रक्रिया को आमतौर पर इस प्रकार लिखा जाता है : <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mn>6</mn><msub><mi>CO</mi><mn>2</mn></msub><mo>+</mo><mn>6</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi><mo>&#8594;</mo><msub><mi mathvariant=\"normal\">C</mi><mn>6</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>12</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>6</mn></msub><mo>+</mo><mn>6</mn><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. If J = 17 and N = 13, then JUNIOR = ?</p>",
                    question_hi: "<p>7. यदि J = 17 और N = 13, तब JUNIOR = ?</p>",
                    options_en: ["<p>1751318129</p>", "<p>1761318129</p>", 
                                "<p>1771318129</p>", "<p>1741318129</p>"],
                    options_hi: ["<p>1751318129</p>", "<p>1761318129</p>",
                                "<p>1771318129</p>", "<p>1741318129</p>"],
                    solution_en: "<p>7.(b) <strong>Logic :</strong> Place value of opposite of alphabets are written.<br>JUNIOR = <strong>1761318129</strong></p>",
                    solution_hi: "<p>7.(b) <strong>तर्क</strong> : अक्षरों के विपरीत अक्षरों का स्थितीय मान लिखा जाता है।<br>JUNIOR = <strong>1761318129</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. Name the duct that originates from the male testicle and carries sperm upward in the body.</p>",
                    question_hi: "<p>8. उस वाहिनी का नाम बताइए जो पुरुष अंडकोष से निकलती है और शुक्राणु को शरीर में ऊपर की ओर ले जाती है।</p>",
                    options_en: ["<p>Epididymis</p>", "<p>Sperm duct</p>", 
                                "<p>Ejaculatory ducts</p>", "<p>Urethra</p>"],
                    options_hi: ["<p>अधिवृषण</p>", "<p>शुक्राणु वाहिनी</p>",
                                "<p>स्खलन नलिकाएं</p>", "<p>मूत्रमार्ग</p>"],
                    solution_en: "<p>8.(b) <strong>Sperm duct </strong>(Vas deferens). It carries the sperms from the testes to the urethra. It is made of fibrous tissue and muscle tissue. Other parts of male reproductive system - External Organs (Penis, scrotum, Testicles), Internal Organs (Prostate, Urethra, Vas deferens).</p>",
                    solution_hi: "<p>8.(b) <strong>शुक्राणु वाहिनी</strong> (वास डेफेरेंस)। यह शुक्राणुओं को वृषण से मूत्रमार्ग तक ले जाता है। यह रेशेदार ऊतक और मांसपेशी ऊतक से बना होता है। पुरुष प्रजनन प्रणाली के अन्य भाग - बाह्य अंग (वृषण, वृषणकोश, अंडकोष), आंतरिक अंग (प्रोस्टेट, मूत्रमार्ग, वास डेफेरेंस)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. What should come in place of \'?\' in the given series? <br>&ndash;1.3, &ndash;0.8, &ndash;0.3,?, 0.7, 1.2</p>",
                    question_hi: "<p>9.दी गई श्रृंखला में \'?\' के स्थान पर क्या आना चाहिए? <br>&ndash;1.3, &ndash;0.8, &ndash;0.3,?, 0.7, 1.2</p>",
                    options_en: ["<p>0.2</p>", "<p>0.4</p>", 
                                "<p>0.3</p>", "<p>0.1</p>"],
                    options_hi: ["<p>0.2</p>", "<p>0.4</p>",
                                "<p>0.3</p>", "<p>0.1</p>"],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010074957.png\" alt=\"rId8\" width=\"295\" height=\"48\"></p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010074957.png\" alt=\"rId8\" width=\"295\" height=\"48\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. Which of the following home appliances does NOT use an electric motor?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सा घरेलू उपकरण, विद्युत मोटर का उपयोग नहीं करता है?</p>",
                    options_en: ["<p>Electric washing machine</p>", "<p>Electric mixer</p>", 
                                "<p>Electric iron</p>", "<p>Electric fan</p>"],
                    options_hi: ["<p>विद्युत वाशिंग मशीन</p>", "<p>विद्युत मिक्सर</p>",
                                "<p>विद्युत आयरन</p>", "<p>बिजली के पंखे</p>"],
                    solution_en: "<p>10.(c) <strong>Electric iron. Heating effect of Electric Current</strong> - When an electric current is passed through a conductor, it generates heat due to the hindrance caused by the conductor to the flowing current. DC motor - Converts electrical energy into mechanical energy. The electric iron was invented in 1882, by Henry Seely White. The DC motor works on the principle of &ldquo;Faraday\'s law of electromagnetic induction\".</p>",
                    solution_hi: "<p>10.(c) <strong>विद्युत आयरन।</strong> विद्युत धारा का<br>ऊष्मीय प्रभाव - जब किसी चालक में विद्युत धारा प्रवाहित की जाती है, तो प्रवाहित धारा में चालक द्वारा उत्पन्न बाधा के कारण यह ऊष्मा उत्पन्न करता है। DC मोटर - विद्युत ऊर्जा को यांत्रिक ऊर्जा में परिवर्तित करता है। विद्युत आयरन का आविष्कार 1882 में हेनरी सीली व्हाइट ने किया था। DC मोटर \"फैराडे के विद्युत चुम्बकीय प्रेरण के नियम\" के सिद्धांत पर काम करती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. If x + y + z = 0, then the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mrow><mi>y</mi><mi>z</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mi>z</mi><mi>x</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mi>x</mi><mi>y</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>11. यदि x + y + z = 0 तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mrow><mi>y</mi><mi>z</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mi>z</mi><mi>x</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mi>x</mi><mi>y</mi></mrow></mfrac></math>&nbsp;का मान क्या होगा ?</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>11.(c) Given , x + y + z = 0<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mrow><mi>y</mi><mi>z</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mi>z</mi><mi>x</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mi>x</mi><mi>y</mi></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><msup><mi>y</mi><mn>3</mn></msup><mo>+</mo><msup><mi>z</mi><mn>3</mn></msup><mo>&#160;</mo></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac></math> = 3</p>",
                    solution_hi: "<p>11.(c) दिया गया ,x + y + z = 0<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mrow><mi>y</mi><mi>z</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mi>z</mi><mi>x</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mi>x</mi><mi>y</mi></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><msup><mi>y</mi><mn>3</mn></msup><mo>+</mo><msup><mi>z</mi><mn>3</mn></msup><mo>&#160;</mo></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi></mrow><mrow><mi>x</mi><mi>y</mi><mi>z</mi></mrow></mfrac></math> = 3</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. ______, present in the centre of a flower, forms the female reproductive part.</p>",
                    question_hi: "<p>12. पुष्प के मध्य में स्थित,_____मादा प्रजनन भाग होता है।</p>",
                    options_en: ["<p>Stamens</p>", "<p>Petals</p>", 
                                "<p>Sepals</p>", "<p>Carpels</p>"],
                    options_hi: ["<p>पुंकेसर</p>", "<p>पंखुड़ी</p>",
                                "<p>बाह्यदल</p>", "<p>अंडप</p>"],
                    solution_en: "<p>12.(d) <strong>Carpels.</strong> It is surrounded by male reproductive structures called stamens. The carpels and stamens are surrounded by petals. The petals are protected by sepals, which are green. Carpels are divided into three parts namely ovary, style and stigma.</p>",
                    solution_hi: "<p>12.(d) <strong>अंडप।</strong> यह पुंकेसर नामक नर प्रजनन संरचनाओं से घिरा होता है। अंडप और पुंकेसर पंखुड़ियों से घिरे होते हैं। पंखुड़ियाँ बाह्यदलों द्वारा संरक्षित होती हैं, जो हरे रंग की होती हैं। अंडप को अंडाशय, वर्तिका और वर्तिकाग्र नामक तीन भागों में विभाजित किया गया है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. What should come in place of \'?\' in the given series? <br>25, 125, 62.5, 250, 125, 375, ?</p>",
                    question_hi: "<p>13. दी गई श्रृंखला में \'?\' के स्थान पर क्या आना चाहिए?<br>25, 125, 62.5, 250, 125, 375, ?</p>",
                    options_en: ["<p>165</p>", "<p>185</p>", 
                                "<p>187.5</p>", "<p>175.7</p>"],
                    options_hi: ["<p>165</p>", "<p>185</p>",
                                "<p>187.5</p>", "<p>175.7</p>"],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010075128.png\" alt=\"rId9\" width=\"362\" height=\"52\"></p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010075128.png\" alt=\"rId9\" width=\"362\" height=\"52\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. Which of the following organelles is/are known as the \'suicide bags\' of a cell?</p>",
                    question_hi: "<p>14. निम्नलिखित में से किस कोशिकांग को कोशिका की \'आत्मघाती थैली\' के रूप में जाना जाता है?</p>",
                    options_en: ["<p>Ribosomes</p>", "<p>Golgi apparatus</p>", 
                                "<p>Endoplasmic reticulum</p>", "<p>Lysosomes</p>"],
                    options_hi: ["<p>राइबोसोम</p>", "<p>गॉल्जीकाय</p>",
                                "<p>अंतः- प्रद्रव्यी जालिका</p>", "<p>लाइसोसोम</p>"],
                    solution_en: "<p>14.(d) <strong>Lysosomes</strong> (suicidal bags) are sphere-shaped cell organelle which found within the eukaryotic animal cells and has the capability of breaking down many types of biomolecules of the cell. <strong>Ribosome</strong> - An intercellular structure made of both RNA and protein, and it is the site of protein synthesis in the cell. <strong>Golgi apparatus</strong> is a central intracellular membrane-bound organelle with key functions in trafficking, processing, and sorting of newly synthesized membrane and secretory proteins and lipids. <strong>Endoplasmic reticulum (ER)</strong> - A large organelle composed of membranous sheets and tubules that start near the nucleus and stretch throughout the cell.</p>",
                    solution_hi: "<p>14.(d) <strong>लाइसोसोम</strong> (suicidal bags) गोले के आकार के कोशिका अंग हैं जो यूकैरियोटिक जन्तु कोशिकाओं के भीतर पाए जाते हैं और कोशिका के कई प्रकार के जैव-अणुओं को तोड़ने की क्षमता रखते हैं। <strong>राइबोसोम</strong> - RNA और प्रोटीन दोनों से बनी एक अंतरकोशिकीय संरचना, और यह कोशिका में प्रोटीन संश्लेषण का स्थल है। <strong>गोल्गी तंत्र </strong>एक केंद्रीय अंतःकोशिकीय झिल्ली-बद्ध अंग है जो नए संश्लेषित झिल्ली और स्रावी प्रोटीन और लिपिड की तस्करी, प्रसंस्करण और छँटाई में प्रमुख कार्यों के साथ है। <strong>अंतर्द्रव्यी जालिका/एंडोप्लाज्मिक रेटिकुलम</strong> (ER) - झिल्लीदार शीटों और नलिकाओं से बना एक बड़ा अंगक जो केंद्रक के पास से शुरू होता है और पूरे कोशिका में फैला होता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of a number is 10 more than <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> of the second number. If 8 is subtracted from <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math> of the first number, then it becomes 4 less than <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> of the second number. What is the sum of the two numbers ?</p>",
                    question_hi: "<p>15. एक संख्या का <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> , दूसरी संख्या के <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>से 10 अधिक है। यदि पहली संख्या के <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>में से 8 घटा दिया जाए, तो वह दूसरी संख्या के <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>से 4 कम हो जाती है। दो संख्याओं का योग क्या है ?</p>",
                    options_en: ["<p>56</p>", "<p>55</p>", 
                                "<p>54</p>", "<p>57</p>"],
                    options_hi: ["<p>56</p>", "<p>55</p>",
                                "<p>54</p>", "<p>57</p>"],
                    solution_en: "<p>15.(d) Let first number = x <br>And 2nd number = y<br>A/Q ,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>5</mn></mfrac></math>-10= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>2</mn></mfrac></math>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>5</mn></mfrac></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>2</mn></mfrac></math> = 10<br><math display=\"inline\"><mo>&#8658;</mo></math> 6x - 5y=100 ------(1)<br>And ,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>7</mn></mfrac></math> - 8 + 4 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>2</mn></mfrac></math>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>7</mn></mfrac></math>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>2</mn></mfrac></math>=4<br><math display=\"inline\"><mo>&#8658;</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>7</mn><mi>y</mi><mo>=</mo><mn>56</mn></math> -----------(2)<br>On solving both equation (1) and (2) , we get<br>y = 22 and&nbsp; x = 35<br>x + y = 35 + 22 = 57</p>",
                    solution_hi: "<p>15.(d) माना , पहली संख्या = X <br>और दूसरी संख्या = Y<br>प्रश्न के अनुसार , <br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>5</mn></mfrac></math>-10= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>2</mn></mfrac></math>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>5</mn></mfrac></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>2</mn></mfrac></math> = 10<br><math display=\"inline\"><mo>&#8658;</mo><mn>6</mn><mi>x</mi><mo>-</mo><mn>5</mn><mi>y</mi><mo>=</mo><mn>100</mn></math> ------(1)<br>और ,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>7</mn></mfrac></math> - 8 + 4 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>2</mn></mfrac></math>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>7</mn></mfrac></math>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>2</mn></mfrac></math>=4<br><math display=\"inline\"><mo>&#8658;</mo></math> 6x-7y=56 -----------(2)<br>समीकरण (1) और (2) दोनों को हल करने पर, हमे प्राप्त होता है - <br>y = 22 और&nbsp; x = 35<br>x + y = 35 + 22 = 57</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. What was the name of the space shuttle that landed man on the moon?</p>",
                    question_hi: "<p>16. उस अंतरिक्ष यान का नाम क्या है जिससे मनुष्य ने चंद्रमा पर कदम रखा ?</p>",
                    options_en: ["<p>Challenger</p>", "<p>Apollo</p>", 
                                "<p>Eagle</p>", "<p>Columbia</p>"],
                    options_hi: ["<p>चैलेंजर</p>", "<p>अपोलो</p>",
                                "<p>ईगल</p>", "<p>कोलंबिया</p>"],
                    solution_en: "<p>16.(b) <strong>Apollo</strong> <strong>11</strong> was the American spaceflight that first landed humans on the Moon. Commander Neil Armstrong and lunar module pilot Buzz Aldrin landed the Apollo Lunar Module Eagle in 1969. <strong>Challenger</strong> was a Space Shuttle orbiter, and launched on its maiden flight in 1983. Columbia was the first of five Space Shuttle orbiters to fly in space, debuting the Space Shuttle launch vehicle on its maiden flight in 1981.</p>",
                    solution_hi: "<p>16.(b) <strong>अपोलो 11</strong> अमेरिकी अंतरिक्ष उड़ान थी जिसने पहली बार मनुष्यों को चंद्रमा पर उतारा था। कमांडर नील आर्मस्ट्रांग और चंद्र मॉड्यूल पायलट बज़ एल्ड्रिन ने 1969 में अपोलो लूनर मॉड्यूल ईगल को उतारा। <strong>चैलेंजर</strong> एक स्पेस शटल ऑर्बिटर था, और 1983 में अपनी पहली उड़ान पर लॉन्च किया गया था। कोलंबिया अंतरिक्ष में उड़ान भरने वाले पांच स्पेस शटल ऑर्बिटरों में से पहला था, जिसने 1981 में अपनी पहली उड़ान में स्पेस शटल लॉन्च वाहन की शुरुआत की थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. Study the given diagram carefully and answer the question that follows.<br>The numbers in different sections indicate the number of people.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010075333.png\" alt=\"rId10\" width=\"134\" height=\"139\"> <br>How many men are Actors but NOT Singers ?</p>",
                    question_hi: "<p>17. दिए गए आरेख का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्नों के उत्तर दीजिए।<br>विभिन्न वर्गों में संख्या लोगों की संख्या दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010075585.png\" alt=\"rId11\" width=\"139\" height=\"155\"> <br>कितने पुरुष (Men) अभिनेता (Actors) हैं लेकिन गायक(Singers) नहीं हैं?</p>",
                    options_en: ["<p>8</p>", "<p>3</p>", 
                                "<p>26</p>", "<p>11</p>"],
                    options_hi: ["<p>8</p>", "<p>3</p>",
                                "<p>26</p>", "<p>11</p>"],
                    solution_en: "<p>17.(a) Number of men who are actors but not singers = 8</p>",
                    solution_hi: "<p>17.(a) पुरुषों की संख्या जो अभिनेता हैं लेकिन गायक नहीं हैं = 8</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "18. Energy flow through the various trophic levels in the ecosystem is________.",
                    question_hi: "18. पारिस्थितिक तंत्र में विभिन्न पोषी स्तरों के माध्यम से ऊर्जा प्रवाह ___________ |",
                    options_en: [" random", " bidirectional", 
                                " reduced with each higher trophic level", " increased with each higher trophic level"],
                    options_hi: [" यादृच्छिक होता है", " द्विदिशीय होता है",
                                " प्रत्येक उच्चतर पोषी स्तर के साथ कम हो जाता है", " प्रत्येक उच्चतर पोषी स्तर के साथ बढ़ जाता है।"],
                    solution_en: "18.(c) Ten percent law of energy transfer - Given by Lindemann in 1942. This law states that only 10 percent of energy in a food chain out of the total energy is transferred from one trophic level to another. The rest of the energy is utilized for other metabolic processes and some are released as heat.  ",
                    solution_hi: "18.(c) ऊर्जा हस्तांतरण का दस प्रतिशत नियम - 1942 में लिंडमैन द्वारा दिया गया। यह नियम बताता है कि कुल ऊर्जा में से एक खाद्य श्रृंखला में केवल 10 प्रतिशत ऊर्जा एक पोषण स्तर से दूसरे में स्थानांतरित होती है। शेष ऊर्जा का उपयोग अन्य चयापचय प्रक्रियाओं के लिए किया जाता है और कुछ उष्ण के रूप में मुक्त की जाती हैं। ",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. In the given letter-cluster pairs, the first letter-cluster is related to the second letter-cluster following a certain logic. Study the given pairs carefully, and from the given options, select the pair that follows the same logic.<br>YVQ : TQL<br>WKG : RFB</p>",
                    question_hi: "<p>19. दिए गए अक्षर-समूह युग्म में पहला अक्षर-समूह एक निश्चित तर्क के आधार पर दूसरे अक्षर समूह से संबंधित है। दिए गए युग्मों का ध्यानपूर्वक अध्ययन कीजिए, और दिए गए विकल्पों में से उस युग्म का चयन करें, जो समान तर्क का पालन करता है।<br>YVQ : TQL<br>WKG : RFB</p>",
                    options_en: ["<p>SIY : NDT</p>", "<p>DUR : APM</p>", 
                                "<p>FHL : CIU</p>", "<p>QHK : JSP</p>"],
                    options_hi: ["<p>SIY : NDT</p>", "<p>DUR : APM</p>",
                                "<p>FHL : CIU</p>", "<p>QHK : JSP</p>"],
                    solution_en: "<p>19.(a)<br>In YVQ : TQL <math display=\"inline\"><mo>&#8658;</mo></math> Y - 5 = T , V - 5 = Q , <br>Q - 5 = L<br>In WKG : RFB <math display=\"inline\"><mo>&#8658;</mo></math> W - 5 = R, K - 5 = F,<br>G - 5 = B<br>Similarly,<br>In SIY : NDT <math display=\"inline\"><mo>&#8658;</mo></math> S - 5 = N, I - 5 = D, Y - 5 = T</p>",
                    solution_hi: "<p>19.(a)<br>YVQ : TQL में <math display=\"inline\"><mo>&#8658;</mo></math> Y - 5 = T , V - 5 = Q, Q - 5 = L<br>WKG : RFB में <math display=\"inline\"><mo>&#8658;</mo></math> W - 5 = R, K - 5 = F, G - 5 = B<br>इसी प्रकार,<br>SIY : NDT में <math display=\"inline\"><mo>&#8658;</mo></math> S - 5 = N, I - 5 = D, Y - 5 = T</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. A man bought a horse for a certain amount and sold it at a loss of 8% . If he had received Rs. 1,800 more, he would have gained 14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> % . The cost price of the horse is..</p>",
                    question_hi: "<p>20. एक आदमी ने एक निश्चित राशि में एक घोड़ा खरीदा और उसे 8% की हानि पर बेच दिया। अगर उसे 1,800 रूपये अधिक मिलते है, उसे&nbsp;14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> % का लाभ होता। घोड़े का क्रय मूल्य ज्ञात कीजिये ?</p>",
                    options_en: ["<p>Rs 5000</p>", "<p>Rs 8000</p>", 
                                "<p>Rs 3500</p>", "<p>Rs 6000</p>"],
                    options_hi: ["<p>Rs 5000</p>", "<p>Rs 8000</p>",
                                "<p>Rs 3500</p>", "<p>Rs 6000</p>"],
                    solution_en: "<p>20.(b)<br>Let the CP of horse = 100 units<br>Loss = 8%, SP = 92 units<br>Profit = 14.5%, SP = 114.5 units <br>Difference of SP = 114.5 - 92 = 22.5units <math display=\"inline\"><mo>&#8658;</mo></math> 1800<br>CP = <math display=\"inline\"><mfrac><mrow><mn>1800</mn></mrow><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times;100 = Rs 8,000</p>",
                    solution_hi: "<p>20.(b)<br>माना घोड़े का क्रय मूल्य = 100 इकाई <br>हानि = 8%, विक्रय मूल्य = 92इकाई <br>लाभ = 14.5%, विक्रय मूल्य = 114.5 इकाई <br>विक्रय मूल्य का अंतर = 114.5 - 92 <br>= 22.5इकाई <math display=\"inline\"><mo>&#8658;</mo></math> 1800<br>लागत मूल्य = <math display=\"inline\"><mfrac><mrow><mn>1800</mn></mrow><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times;100 = रु 8,000</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. Provision of &lsquo;First Past the Post&rsquo; in the Indian Constitution has been adopted from the constitution of :</p>",
                    question_hi: "<p>21. भारतीय संविधान में \'फर्स्ट पास्ट द पोस्ट\' का प्रावधान किस संविधान से अपनाया गया है?</p>",
                    options_en: ["<p>Ireland</p>", "<p>France</p>", 
                                "<p>Britain</p>", "<p>USA</p>"],
                    options_hi: ["<p>आयरलैंड</p>", "<p>फ्रांस</p>",
                                "<p>ब्रिटेन</p>", "<p>USA</p>"],
                    solution_en: "<p>21.(c) <strong>Britain.</strong> Other Features - Parliamentary government, Rule of Law, Legislative procedure, Single citizenship, Cabinet system, Prerogative writs, Parliamentary privileges, Bicameralism.</p>",
                    solution_hi: "<p>21.(c) <strong>ब्रिटेन।</strong> अन्य विशेषताएं - संसदीय <br>सरकार, कानून का शासन, विधायी प्रक्रिया, एकल नागरिकता, कैबिनेट प्रणाली, विशेषाधिकार रिट, संसदीय विशेषाधिकार, द्विसदनीयता।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. If the digits of each number are written in reverse order and then the numbers are arranged in descending order, the position of how many numbers will NOT change?<br>7846, 6354, 8745, 4653</p>",
                    question_hi: "<p>22.यदि प्रत्येक संख्या के अंकों को उल्टे क्रम में लिखा जाए, और इस प्रकार प्राप्त संख्याओं को अवरोही क्रम में व्यवस्थित किया जाए, तो कितनी संख्याओं की स्थिति अपरिवर्तित रहेगी?<br>7846, 6354, 8745, 4653</p>",
                    options_en: ["<p>Three</p>", "<p>Two</p>", 
                                "<p>Zero</p>", "<p>One</p>"],
                    options_hi: ["<p>तीन</p>", "<p>दो</p>",
                                "<p>शून्य</p>", "<p>एक</p>"],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010075893.png\" alt=\"rId12\" width=\"305\" height=\"135\"><br>From the above figure, we can clearly see that,Only 2 number&rsquo;s position will not change.</p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010076174.png\" alt=\"rId13\" width=\"338\" height=\"162\"><br>उपरोक्त आकृति से, हम स्पष्ट रूप से देख सकते हैं कि, सिर्फ 2 संख्याओं की स्थिति अपरिवर्तित रहेगी</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. Colourless plastids are called _____.</p>",
                    question_hi: "<p>23. रंगहीन लवक (Colourless plastids) को _____ कहा जाता है।</p>",
                    options_en: ["<p>leucoplastids</p>", "<p>apicoplastids</p>", 
                                "<p>chloroplastids</p>", "<p>chromoplastids</p>"],
                    options_hi: ["<p>ल्यूकोप्लास्टिड्स (leucoplastids)</p>", "<p>एपिकोप्लास्टिड्स (apicoplastids)</p>",
                                "<p>क्लोरोप्लास्टिड्स (chloroplastids)</p>", "<p>क्रोमोप्लास्टिड्स (chromoplastids)</p>"],
                    solution_en: "<p>23.(a) <strong>Leucoplastids</strong> - They are the colorless plastids found in the endosperm, tubers, cotyledons, roots, and other non-photosynthetic parts of plants. <strong>Apicoplastids</strong> - It plays a vital role in lipid metabolism. <strong>Chloroplastids</strong> - They are plant cell organelles that convert light energy into relatively stable chemical energy via the photosynthetic process. <strong>Chromoplastids</strong> - They are responsible for the yellow, orange, and red colors of many flowers and fruits.</p>",
                    solution_hi: "<p>23.(a) <strong>ल्यूकोप्लास्टिड्</strong> - रंगहीन प्लास्टिड होते हैं जो एंडोस्पर्म, कंद, बीजपत्र, जड़ों और पौधों के अन्य गैर-प्रकाश संश्लेषक ऊतकों में पाए जाते हैं। <strong>एपिकोप्लास्टिड्</strong> - लिपिड चयापचय में महत्वपूर्ण भूमिका निभाते हैं। <strong>क्लोरोप्लास्टिड्</strong> - पादप कोशिका अंग हैं जो प्रकाश ऊर्जा को प्रकाश संश्लेषक प्रक्रिया के माध्यम से अपेक्षाकृत स्थिर रासायनिक ऊर्जा में परिवर्तित करते हैं। <strong>क्रोमोप्लास्टिड्</strong> - कई फूलों और फलों के पीले, नारंगी और लाल रंग के लिए जिम्मेदार होते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. If a = 2b = 3c and a + b + c = 121 then <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>a</mi><mo>&#178;</mo><mo>+</mo><mi>b</mi><mo>&#178;</mo><mo>+</mo><mi>c</mi><mo>&#178;</mo></msqrt></math> is:</p>",
                    question_hi: "<p>24. यदि a = 2b = 3c और a + b + c = 121 तब <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>a</mi><mo>&#178;</mo><mo>+</mo><mi>b</mi><mo>&#178;</mo><mo>+</mo><mi>c</mi><mo>&#178;</mo></msqrt></math> &nbsp;क्या होगा ?</p>",
                    options_en: ["<p>75</p>", "<p>77</p>", 
                                "<p>73</p>", "<p>72</p>"],
                    options_hi: ["<p>75</p>", "<p>77</p>",
                                "<p>73</p>", "<p>72</p>"],
                    solution_en: "<p>24.(b) Given , a = 2b = 3c <br>The ratio between a , b and c = 6x : 3x: 2x<br>And a + b + c = 121 ------(i)<br>Putting the value of a , b and c in equation (i)<br>6x + 3x + 2x = 121 <br>11x = 121 <math display=\"inline\"><mo>&#8658;</mo></math> x = 11<br>Now , a = 66 , b = 33 , c = 22<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>a</mi><mo>&#178;</mo><mo>+</mo><mi>b</mi><mo>&#178;</mo><mo>+</mo><mi>c</mi><mo>&#178;</mo></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>66</mn><mo>&#178;</mo><mo>+</mo><mn>33</mn><mo>&#178;</mo><mo>+</mo><mn>22</mn><mo>&#178;</mo></msqrt></math> = 77</p>",
                    solution_hi: "<p>24.(b) दिया गया है, a = 2b = 3c<br>a , b और c के बीच का अनुपात = 6x : 3x : 2x<br>और a +b+ c = 121 -------(i)<br>समीकरण (i) में a, b और c का मान रखने पर<br>6x+ 3x+ 2x = 121<br>11x = 121 <math display=\"inline\"><mo>&#8658;</mo></math> x = 11<br>अब, a = 66,b = 33, c = 22<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>a</mi><mo>&#178;</mo><mo>+</mo><mi>b</mi><mo>&#178;</mo><mo>+</mo><mi>c</mi><mo>&#178;</mo></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>66</mn><mo>&#178;</mo><mo>+</mo><mn>33</mn><mo>&#178;</mo><mo>+</mo><mn>22</mn><mo>&#178;</mo></msqrt></math> = 77</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. What is the name of India\'s first privately developed fully cryogenic rocket engine?</p>",
                    question_hi: "<p>25. भारत के पहले निजी तौर पर विकसित पूर्णतः क्रायोजेनिक रॉकेट इंजन का नाम क्या है?</p>",
                    options_en: ["<p>Dhawan-1</p>", "<p>Kalam-1</p>", 
                                "<p>Sarabhai-1</p>", "<p>Bhabha-1</p>"],
                    options_hi: ["<p>धवन -1</p>", "<p>कलाम -1</p>",
                                "<p>साराभाई -1</p>", "<p>भाभा-1</p>"],
                    solution_en: "<p>25.(a) <strong>Dhawan-1:</strong> Developed by Skyroot aerospace (a private limited company). Cryogenic Engine : It works in extremely low temperatures (below -150 degree Centigrade) to lift and place the heavier objects in space. Skyroot Aerospace: Founder - Pawan Kumar Chandana and Naga Bharath Daka, Headquarters - Hyderabad, Telangana.</p>",
                    solution_hi: "<p>25.(a) <strong>धवन -1: </strong>स्काईरूट एयरोस्पेस (एक प्राइवेट लिमिटेड कंपनी) द्वारा विकसित। क्रायोजेनिक इंजन: यह भारी वस्तुओं को अंतरिक्ष में उठाने और रखने के लिए बेहद कम तापमान (-150 डिग्री सेंटीग्रेड से नीचे) में काम करता है। स्काईरूट एयरोस्पेस: संस्थापक - पवन कुमार चंदना और नागा भारत डाका, मुख्यालय - हैदराबाद, तेलंगाना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. P and Q alone can complete a piece of work in 9 days and 12 days, respectively. In how many days will the work be completed if they work on alternate days starting with Q?</p>",
                    question_hi: "<p>26. P और Q अकेले एक कार्य को क्रमशः 9 दिनों और 12 दिनों में पूरा कर सकते हैं। यदि वे Q से शुरू करके वैकलपिक दिनों में कार्य करते हैं तो कार्य कितने दिनों में पूरा होगा?</p>",
                    options_en: ["<p>10<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", 
                                "<p>11<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>10<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>10<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                                "<p>11<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>10<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>26.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010076396.png\" alt=\"rId14\" width=\"212\" height=\"132\"><br>Working alternatively starting with Q <br>In first two days work done by Q and P = 8 units + 6 units = 14 units<br>Now in five similar cycle of 2 days, work done = 14 &times; 5 = 70 units<br>Now work left= 72 - 70 = 2 units<br>Then, <br>Time taken by Q on 11th day = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi><mi>d</mi><mi>a</mi><mi>y</mi></math><br>Total days = 2 &times; 5 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 10<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>26.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010076676.png\" alt=\"rId15\" width=\"214\" height=\"145\"><br>Q से शुरू करके एकांतर रूप से कार्य करने पर, <br>पहले दो दिनों में Q और P द्वारा किया गया कार्य = 8 इकाई + 6 इकाई = 14 इकाई<br>अब 2 दिनों के पांच समान चक्रों में किया गया कार्य = 14 &times; 5 = 70 इकाई<br>अब बचा काम = 72 - 70 = 2 इकाई<br>फिर,11वें दिन Q को लगा समय = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन<br>कुल दिन = 2 &times; 5 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 10<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. Which of the following states is&nbsp;the origin of the folk music traditions of Saikuti zai ?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन सा राज्य सैकुटी ज़ाई ( Saikuti zai) की लोक संगीत परंपराओं का उद्गम स्थल है?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Mizoram</p>", 
                                "<p>Telangana</p>", "<p>Goa</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>मिजोरम</p>",
                                "<p>तेलंगाना</p>", "<p>गोवा</p>"],
                    solution_en: "<p>27.(b) <strong>Mizoram.</strong> Saikuti zai - The regional folk songs and the richest heritage of Mizos. Saikuti - a poetess from Mizoram, composed songs in praise of warriors, brave hunters, great warriors and young men who aspire to be hunters.</p>",
                    solution_hi: "<p>27.(b) <strong>मिजोरम।</strong> सैकुटी ज़ाई - क्षेत्रीय लोक गीत और मिज़ोस की सबसे समृद्ध विरासत है। सैकुटी - मिजोरम की एक कवयित्री हैं ,इन्होंने योद्धाओं, बहादुर शिकारियों, महान योद्धाओं और शिकारी बनने की इच्छा रखने वाले युवाओं की प्रशंसा में गीत लिखे।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. A certain distance (d) is covered by a cyclist at a certain speed. If a jogger covers half the distance in double the time(t), then the ratio of the speed of the cyclist to the speed of the jogger is.</p>",
                    question_hi: "<p>28. एक निश्चित दूरी (d) एक साइकिल चालक द्वारा एक निश्चित गति से तय की जाती है। यदि एक घुड़दौड़ समय (t) के दुगुने समय में आधी दूरी तय करता है, तो साइकिल चालक की गति का घुड़दौड़ की गति से अनुपात कितना है?</p>",
                    options_en: ["<p>2 : 1</p>", "<p>4 : 1</p>", 
                                "<p>3 : 4</p>", "<p>1 : 2</p>"],
                    options_hi: ["<p>2 : 1</p>", "<p>4 : 1</p>",
                                "<p>3 : 4</p>", "<p>1 : 2</p>"],
                    solution_en: "<p>28.(b) Distance is the same <br>Then, speed is inversely proportional to the time.<br>Now , Ratio of the speed = 2 : <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 4 : 1</p>",
                    solution_hi: "<p>28.(b) दूरी समान है,<br>फिर, गति समय के व्युत्क्रमानुपाती होती है।<br>अब, गति का अनुपात = 2 : <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rarr; 4 : 1</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. PETA India started in the year ____.</p>",
                    question_hi: "<p>29. भारत में PETA कब शुरू हुआ ?</p>",
                    options_en: ["<p>2000</p>", "<p>2003</p>", 
                                "<p>2002</p>", "<p>2001</p>"],
                    options_hi: ["<p>2000</p>", "<p>2003</p>",
                                "<p>2002</p>", "<p>2001</p>"],
                    solution_en: "<p>29.(a) <strong>2000.</strong> People for the Ethical Treatment of Animals (PETA) is the largest animal rights organization in the world, and PETA entities have more than 9 million members and supporters globally. PETA: principle - animals are not ours to eat, wear, experiment on or use for entertainment. Founded in 1980 by Ingrid Newkirk and Alex Pacheco.</p>",
                    solution_hi: "<p>29.(a) <strong>2000</strong> । पीपल फॉर द एथिकल ट्रीटमेंट ऑफ एनिमल्स (PETA ) दुनिया का सबसे बड़ा पशु अधिकार संगठन है, और PETA संस्थाओं मे विश्&zwj;व स्तर पर 9 मिलियन से अधिक सदस्य और समर्थक हैं। PETA : सिद्धांत - जानवर हमारे खाने, पहनने, प्रयोग करने या मनोरंजन के लिए उपयोग करने के लिए नहीं हैं। इसे 1980 में इंग्रिड न्यूकिर्क(Ingrid Newkirk) और एलेक्स पचेको (Alex Pacheco) द्वारा स्थापित किया गया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. A batsman scored runs in the last 5 innings as follows. If the average score is 43, then find his score in the 4th innings.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010076807.png\" alt=\"rId16\" width=\"312\" height=\"65\"></p>",
                    question_hi: "<p>30. एक बल्लेबाज पिछली 5 पारियों में निम्नानुसार रन बनाता है। यदि औसत स्कोर 43 है, तो चौथी (4th) पारी में उसका स्कोर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010076807.png\" alt=\"rId16\" width=\"312\" height=\"65\"></p>",
                    options_en: ["<p>12</p>", "<p>8</p>", 
                                "<p>4</p>", "<p>10</p>"],
                    options_hi: ["<p>12</p>", "<p>8</p>",
                                "<p>4</p>", "<p>10</p>"],
                    solution_en: "<p>30.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>92</mn><mo>+</mo><mn>53</mn><mo>+</mo><mn>35</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>27</mn></mrow><mn>5</mn></mfrac></math> = 43<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>207</mn><mo>+</mo><mi>x</mi><mo>=</mo><mn>215</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mn>215</mn><mo>-</mo><mn>207</mn><mo>=</mo><mn>8</mn></math></p>",
                    solution_hi: "<p>30.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>92</mn><mo>+</mo><mn>53</mn><mo>+</mo><mn>35</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>27</mn></mrow><mn>5</mn></mfrac></math> = 43<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>207</mn><mo>+</mo><mi>x</mi><mo>=</mo><mn>215</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mn>215</mn><mo>-</mo><mn>207</mn><mo>=</mo><mn>8</mn></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. A battery of 12V supplies a current of 3 A to a lamp connected to it. The energy supplied to it in 15 minutes will be:</p>",
                    question_hi: "<p>31. 12V की एक बैटरी द्वारा इससे जुड़े एक लैम्प को 3 A की धारा की आपूर्ति की जाती है। लैम्प में 15 मिनट में आपूर्ति की गई ऊर्जा कितनी होगी?</p>",
                    options_en: ["<p>32000 J</p>", "<p>32150 J</p>", 
                                "<p>32400 J</p>", "<p>540 J</p>"],
                    options_hi: ["<p>32000 J</p>", "<p>32150 J</p>",
                                "<p>32400 J</p>", "<p>540 J</p>"],
                    solution_en: "<p>31.(c) <strong>32400 J.</strong> <br>The energy supplied to the lamp can be<br>calculated using the formula:<br>Energy = Power x Time.<br>Now, Power = Voltage &times; Current <br>= 12V &times; 3A = 36W.<br>The time for which the energy is supplied is 15 minutes, which is equivalent to 900 seconds.<br>Substituting these values in the formula for energy, we get:<br>Energy = Power &times; Time <br>= 36W &times; 900s = 32,400 J</p>",
                    solution_hi: "<p>31.(c)<strong> 32400 J.</strong><br>लैंप को आपूर्ति की गई ऊर्जा की गणना सूत्र का उपयोग करके की जा सकती है:<br>ऊर्जा = शक्ति &times; समय।<br>अब, पावर = वोल्टेज &times; धारा = 12V &times; 3A = 36W<br>जिस समय के लिए ऊर्जा की आपूर्ति की जाती है<br>वह 15 मिनट है, जो 900 सेकेंड के बराबर है।<br>इन मूल्यों को ऊर्जा के सूत्र में प्रतिस्थापित करने पर, हम प्राप्त करते हैं:<br>ऊर्जा = शक्ति &times; समय = 36W &times; 900s <br>= 32,400 J।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. The ratio between the present ages of P and Q is 7 : 5, respectively. The ratio between P\'s age 5 years ago and Q\'s age 5 years hence is 1 : 1. What is the ratio between P\'s age 5 years hence and Q\'s age 5 years ago?</p>",
                    question_hi: "<p>32. P और Q की वर्तमान आयु के बीच क्रमशः 7 : 5 का अनुपात है। 5 वर्ष पहले P की उम्र और 5 वर्ष बाद Q की उम्र के बीच का अनुपात 1:1 है। 5 वर्ष बाद P की उम्र और 5 वर्ष पहले Q की उम्र के बीच का अनुपात क्या है?</p>",
                    options_en: ["<p>1 : 2</p>", "<p>2 : 1</p>", 
                                "<p>3 : 2</p>", "<p>2 : 3</p>"],
                    options_hi: ["<p>1 : 2</p>", "<p>2 : 1</p>",
                                "<p>3 : 2</p>", "<p>2 : 3</p>"],
                    solution_en: "<p>32.(b)<br>Let present age of P and Q = 7x and 5x <br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi><mo>-</mo><mn>5</mn></mrow><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math>&rArr; 7x - 5 = 5x + 5 <br><math display=\"inline\"><mo>&#8658;</mo></math> 2x = 10 &rArr; x = 5<br>Present age of P = 7 &times; 5 = 35 years <br>Present age of Q = 5 &times; 5 = 25 years<br>Therefore required ratio = (35 + 5): <br>(25 - 5) <math display=\"inline\"><mo>=</mo></math> 40:20 = 2 : 1</p>",
                    solution_hi: "<p>32.(b) प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi><mo>-</mo><mn>5</mn></mrow><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 7x - 5 = 5x + 5 &rArr; 2x = 10 &rArr; x = 5<br>P की वर्तमान आयु = 7 &times; 5 = 35 वर्ष<br>Q की वर्तमान आयु = 5 &times; 5 = 25 वर्ष<br>इसलिए अभीष्ट अनुपात = (35 + 5) : (25 - 5) <br><math display=\"inline\"><mo>=</mo></math> 40 :20 = 2 : 1</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Teejan Bai is an exponent of Pandavani, a traditional performing art form, form, Chhattisgarh in which she enacts tales from the ______with musical accompaniments.</p>",
                    question_hi: "<p>33. तीजन बाई छत्तीसगढ़ के एक पारंपरिक रंगमंचीय कला स्वरूप, पंडवानी में निष्णात हैं, जिसमें वह संगीतकारों के साथ _____ की कथाओं पर अभिनय करती हैं।</p>",
                    options_en: ["<p>Katha Sarit Sagar</p>", "<p>Abhijnana Sakuntalam</p>", 
                                "<p>Mahabharata</p>", "<p>Ramayana</p>"],
                    options_hi: ["<p>कथा सरित्सागर</p>", "<p>अभिज्ञान शाकुन्तलम्</p>",
                                "<p>महाभारत</p>", "<p>रामायण</p>"],
                    solution_en: "<p>33.(c) <strong>Mahabharata.</strong> Teejan Bai (born 8 August 1956) is an exponent of Pandavani, a traditional performing art form, from Chhattisgarh, in which she enacts tales from the Mahabharata, with musical accompaniments. Awards : Padma Shri (1988), Padma Bhushan (2003), Padma Vibhushan (2019) and Sangeet Natak Akademi Award (1995).</p>",
                    solution_hi: "<p>33.(c) <strong>महाभारत।</strong> तीजन बाई (जन्म 8 अगस्त 1956) छत्तीसगढ़ की पारंपरिक प्रदर्शन कला पंडवानी की प्रतिपादक हैं, जिसमें वह संगीत संगत के साथ महाभारत की कहानियों का मंचन करती हैं। पुरस्कार: पद्म श्री (1988), पद्म भूषण (2003), पद्म विभूषण (2019) और संगीत नाटक अकादमी पुरस्कार (1995)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. The angles of depression of two houses of the same height from the top of a building are 45&deg; and 30&deg; towards the east. If the two houses are 50 m apart, what will be the height of the building in meters?</p>",
                    question_hi: "<p>34. एक भवन के शीर्ष से समान ऊँचाई वाले दो घरों के अवनमन कोण पूर्व की ओर 45&deg;और 30&deg; हैं। यदि दोनों घर 50 मीटर की दूरी पर हैं, तो भवन की ऊंचाई मीटर में क्या होगी?</p>",
                    options_en: ["<p>50 (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></math>)</p>", "<p>25 (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></math>)</p>", 
                                "<p>45 (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></math>)</p>", "<p>35(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></math>)</p>"],
                    options_hi: ["<p>50 (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></math>)</p>", "<p>25 (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></math>)</p>",
                                "<p>45 (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></math>)</p>", "<p>35(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></math>)</p>"],
                    solution_en: "<p>34.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077009.png\" alt=\"rId17\" width=\"193\" height=\"93\"><br>In <math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math><br>tan 45&deg; = <math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> &rArr;1= <math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> &rArr;BC = h<br>In <math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>D</mi></math><br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mi>h</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>50</mn></mrow></mfrac></math> &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mrow><mi>h</mi><mo>+</mo><mn>50</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo><msqrt><mn>3</mn></msqrt><mi>h</mi><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mi>h</mi><mi>&#160;</mi><mo>+</mo><mn>50</mn></math> &rArr; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> - 1)h = 50<br><math display=\"inline\"><mo>&#8658;</mo><mi>h</mi><mo>=</mo><mfrac><mrow><mn>50</mn></mrow><mrow><mo>(</mo><msqrt><mn>3</mn><mi>&#160;</mi></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mn>25</mn><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mi>&#160;</mi><mi>m</mi></math></p>",
                    solution_hi: "<p>34.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077009.png\" alt=\"rId17\" width=\"193\" height=\"93\"><br><math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math> में,<br>tan 45&deg; = <math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> &rArr; 1=&nbsp;<math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> &rArr; BC = h<br><math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>D</mi></math> में,<br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mi>h</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>50</mn></mrow></mfrac></math> &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mrow><mi>h</mi><mo>+</mo><mn>50</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo><msqrt><mn>3</mn></msqrt><mi>h</mi><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mi>h</mi><mi>&#160;</mi><mo>+</mo><mn>50</mn></math> &rArr; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> - 1)h = 50<br><math display=\"inline\"><mo>&#8658;</mo><mi>h</mi><mo>=</mo><mfrac><mrow><mn>50</mn></mrow><mrow><mo>(</mo><msqrt><mn>3</mn><mi>&#160;</mi></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mn>25</mn><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mi>&#160;</mi><mi>m</mi></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. Read the given statement and the arguments carefully and select the appropriate answer from the given options.<br><strong>Statement:</strong><br>Farmers in Village X should switch to crops that require comparatively less water for irrigation as compared to sugarcane.<br><strong>Arguments:</strong><br>I. Availability of water for irrigation has been on a constant decline in Village X for the last few years.<br>II. Main source of income of farmers in Village X is through sale of sugarcane crop to the nearby sugar factories.</p>",
                    question_hi: "<p>35. दिए गए कथन और तर्कों का ध्यानपूर्वक अध्ययन करें और दिए गए विकल्पों में से उपयुक्त उत्तर का चयन करें।<br><strong>कथन:</strong><br>गांव X के किसानों को उन फसलों की ओर रुख करना चाहिए जिनके लिए गन्ने की तुलना में सिंचाई के लिए कम पानी की आवश्यकता होती है।<br><strong>तर्क:</strong><br>I. पिछले कुछ वर्षों से गांब X में सिंचाई के लिए पानी की उपलब्धता में लगातार कमी आ रही है। II. गांव X में किसानों की आय का मुख्य स्रोत पास के चीनी मिलों को गन्ने की फसल की बिक्री है।</p>",
                    options_en: ["<p>II weakens while I strengthens the statement.</p>", "<p>I weakens while II strengthens the statement.</p>", 
                                "<p>Both I and II weaken the statement.</p>", "<p>Both I and II strengthen the statement.</p>"],
                    options_hi: ["<p>II कथन का समर्थन नहीं करता है, जबकि । कथन का समर्थन करता है।</p>", "<p>I कथन का समर्थन नहीं करता है, जबकि II कथन का समर्थन करता है।</p>",
                                "<p>I और II दोनों कथन का समर्थन नहीं करते हैं।</p>", "<p>I और II दोनों कथन का समर्थन करते हैं।</p>"],
                    solution_en: "<p>35.(d) Both Arguments I and II strengthen the statement.</p>",
                    solution_hi: "<p>35.(d) दोनों तर्क I और II कथन का समर्थन करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. Find the points of intersection of the line 4x - 3y = 10 and the circle <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>y</mi><mn>2</mn></msup></math>- 2x + 4y - 20 = 0</p>",
                    question_hi: "<p>36. रेखा 4x - 3y = 10 और वृत्त <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>y</mi><mn>2</mn></msup></math> - 2x + 4y - 20 = 0 के प्रतिच्छेदन बिंदु ज्ञात कीजिए।</p>",
                    options_en: ["<p>(4, 2) and (- 2, - 6)</p>", "<p>(-2, 3) and (4, 2)</p>", 
                                "<p>(- 2, 3) and (4, 3)</p>", "<p>(2, -3) and (4, 2)</p>"],
                    options_hi: ["<p>(4, 2) और (-2, -6)</p>", "<p>(- 2, 3) और (4, 2)</p>",
                                "<p>(- 2, 3) और (4, 3)</p>", "<p>(2, - 3) और (4, 2)</p>"],
                    solution_en: "<p>36.(a) <br>To find the points of intersection of the line 4x - 3y = 10 and the circle<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>y</mi><mn>2</mn></msup></math>- 2x + 4y - 20 = 0<br>We have to put <math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>y</mi><mo>+</mo><mn>10</mn></mrow><mn>4</mn></mfrac></math> in the equation of the circle :<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>25</mn><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mn>100</mn><mi>y</mi><mo>-</mo><mn>300</mn><mo>=</mo><mn>0</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><mi>y</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>(</mo><mi>y</mi><mo>+</mo><mn>6</mn><mo>)</mo><mo>=</mo><mn>0</mn></math><br>Then the values of y = - 6, 2 and the values of x = - 2, 4 ;<br>So, the points of intersections are : (4 , 2) and (- 2 , - 6) ;</p>",
                    solution_hi: "<p>36.(a) रेखा 4x - 3y = 10 और वृत्त <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>y</mi><mn>2</mn></msup></math>- 2x + 4y - 20 = 0 के प्रतिच्छेदन बिंदु ज्ञात करने के लिए ,<br>हमें वृत्त के समीकरण में <math display=\"inline\"><mi>x</mi><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>y</mi><mo>+</mo><mn>10</mn></mrow><mn>4</mn></mfrac></math> रखना है:<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>25</mn><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mn>100</mn><mi>y</mi><mo>-</mo><mn>300</mn><mo>=</mo><mn>0</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><mi>y</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>(</mo><mi>y</mi><mo>+</mo><mn>6</mn><mo>)</mo><mo>=</mo><mn>0</mn></math><br>तब y का मान = - 6, 2 और x के मान = - 2, 4 ;<br>इसलिए, प्रतिच्छेदन के बिंदु हैं: (4 , 2) और (- 2 , - 6)</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. Lactometers, which are used to determine the purity of sample of milk, works on which principle ?</p>",
                    question_hi: "<p>37.लैक्टोमीटर, जो दूध के नमूने की शुद्धता निर्धारित करने के लिए उपयोग किया जाता है, किस सिद्धांत पर काम करता है?</p>",
                    options_en: ["<p>Principle of scalar chain</p>", "<p>Archimedes principle</p>", 
                                "<p>Principle of periscope</p>", "<p>Principle of potentiometer</p>"],
                    options_hi: ["<p>अदिश श्रृंखला के सिद्धांत</p>", "<p>आर्किमिडीज के सिद्धांत</p>",
                                "<p>पेरिस्कोप के सिद्धांत</p>", "<p>विभवमापी का सिद्धांत</p>"],
                    solution_en: "<p>37.(b) <strong>Archimedes principle.</strong> Hydrometer (used to determine the density of liquids). It is also based on the Archimedes principle. The hydrometer basically consists of a weighted, sealed, long-necked glass bulb immersed in the measured liquid; the flotation depth shows the liquid density, and the neck can be calibrated to read the actual gravity value. <strong>Principle of periscope</strong> - It is based on the Laws of Reflection which states that the light from the object falls on a mirror. Principle of potentiometer - The potential difference between any two points of the potentiometer wire is directly proportional to the length of wire.</p>",
                    solution_hi: "<p>37.(b) <strong>आर्किमिडीज़ सिद्धांत।</strong> हाइड्रोमीटर (तरल पदार्थों का घनत्व निर्धारित करने के लिए उपयोग किया जाता है)। यह भी आर्किमिडीज़ सिद्धांत पर आधारित है। हाइड्रोमीटर में मूल रूप से मापे गए तरल में डूबा हुआ एक भारित, सीलबंद, लंबी गर्दन वाला ग्लास बल्ब होता है; प्लवनशीलता गहराई तरल घनत्व को दर्शाती है, और वास्तविक गुरुत्वाकर्षण मूल्य को पढ़ने के लिए गर्दन को कैलिब्रेट किया जा सकता है। <strong>पेरिस्कोप का सिद्धांत</strong> - यह परावर्तन के नियमों पर आधारित है जो बताता है कि वस्तु से प्रकाश दर्पण पर पड़ता है। पोटेंशियोमीटर का सिद्धांत - पोटेंशियोमीटर तार के किन्हीं दो बिंदुओं के बीच संभावित अंतर सीधे तार की लंबाई के समानुपाती होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. Simplify <math display=\"inline\"><msqrt><mn>625</mn><mi>&#160;</mi></msqrt></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>324</mn></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>33</mn><mn>2</mn></msup></math>.</p>",
                    question_hi: "<p>38.<math display=\"inline\"><msqrt><mn>625</mn><mi>&#160;</mi></msqrt></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>324</mn></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>33</mn><mn>2</mn></msup></math>. को सरल कीजिए।</p>",
                    options_en: ["<p>1609</p>", "<p>1906</p>", 
                                "<p>1096</p>", "<p>1069</p>"],
                    options_hi: ["<p>1609</p>", "<p>1906</p>",
                                "<p>1096</p>", "<p>1069</p>"],
                    solution_en: "<p>38.(c)<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mi>&#160;</mi><msqrt><mn>625</mn></msqrt><mo>-</mo><msqrt><mn>324</mn></msqrt><mo>+</mo><msup><mn>33</mn><mn>2</mn></msup></math> = 25 - 18 + 1089 = 1096</p>",
                    solution_hi: "<p>38.(c)<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mi>&#160;</mi><msqrt><mn>625</mn></msqrt><mo>-</mo><msqrt><mn>324</mn></msqrt><mo>+</mo><msup><mn>33</mn><mn>2</mn></msup></math> = 25 - 18 + 1089 = 1096</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. Al-Biruni the persian scholar, accompanied which invader to Afghanistan and India?</p>",
                    question_hi: "<p>39. फारसी विद्वान अल बरुनी किस आक्रमणकारी के साथ अफगानिस्तान और भारत आया था?</p>",
                    options_en: ["<p>Alexander</p>", "<p>Allaudin Khilji</p>", 
                                "<p>Muhammad Ghori</p>", "<p>Mahmud Ghazni</p>"],
                    options_hi: ["<p>सिकंदर</p>", "<p>अलाउद्दीन खिलजी</p>",
                                "<p>मुहम्मद गौरी</p>", "<p>महमूद गजनवी</p>"],
                    solution_en: "<p>39.(d) <strong>Mahmud Ghazni</strong> was the first Turkish who invaded India. He attacked India seventeen times between 1000 and 1027 AD. His first invasion of India - 1001 AD. Mahmud&rsquo;s most important expedition - Somnath temple in 1025 AD located on the coast of Kathiawar (Gujarat). <strong>Al-Biruni&rsquo;s</strong> works were - &ldquo;Kitab-ul-Hind \'\' and &ldquo;Tahkik-e- Hind \'\'. Another famous scholar in the court of Mahmud Ghaznavi was Firdausi who wrote &lsquo;&lsquo;Shahnamah&rsquo;&rsquo;.</p>",
                    solution_hi: "<p>39.(d) <strong>महमूद गजनवी </strong>प्रथम तुर्की शासक जिसने भारत पर आक्रमण किया था। उसने 1000 से 1027 ई. के बीच भारत पर सत्रह बार आक्रमण किया। भारत पर उसका प्रथम आक्रमण - 1001 ई.। महमूद का सबसे महत्वपूर्ण अभियान - 1025 ई. में काठियावाड़ (गुजरात) के तट पर स्थित सोमनाथ मंदिर था । अल बरुनी <strong>की रचनाएँ </strong>- \'\'किताब-उल-हिन्द\'\' और \'\'तहकीक-ए-हिन्द\'\'। महमूद गजनवी के दरबार में एक अन्य प्रसिद्ध विद्वान फ़िरदौसी थे जिन्होंने \'\'शाहनामा\'\' लिखा था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. If 67 = 1764 and 93 = 729, then which of the given options will be the value of 74 = ?</p>",
                    question_hi: "<p>40. यदि 67 = 1764 और 93 = 729, तो दिए गए विकल्पों में से 74 का मान होगा ?</p>",
                    options_en: ["<p>972</p>", "<p>847</p>", 
                                "<p>784</p>", "<p>567</p>"],
                    options_hi: ["<p>972</p>", "<p>847</p>",
                                "<p>784</p>", "<p>567</p>"],
                    solution_en: "<p>40.(c)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup></math> = 1764<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>9</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></math> = 729<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup></math> = 784</p>",
                    solution_hi: "<p>40.(c)</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup></math> = 1764<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>9</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></math> = 729<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup></math> = 784</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. The cause of Hepatitis A is a</p>",
                    question_hi: "<p>41. हेपेटाइटिस A का कारण क्या है ?</p>",
                    options_en: ["<p>virus</p>", "<p>bacteria</p>", 
                                "<p>protozoa</p>", "<p>mosquito bite</p>"],
                    options_hi: ["<p>विषाणु</p>", "<p>जीवाणु</p>",
                                "<p>प्रोटोजोआ</p>", "<p>मच्छर काटना</p>"],
                    solution_en: "<p>41.(a) <strong>Virus. Hepatitis A</strong> - It is a viral infection that affects the liver and is caused by the hepatitis A virus (HAV). The virus is transmitted through contaminated food or water or by close contact with an infected person. <strong>Bacteria</strong> - They are single-celled microorganisms that can be found in various environments and play essential roles in ecosystems, some being beneficial while others can cause diseases. <strong>Protozoa</strong> - They are a group of single-celled eukaryotes, either free-living or parasitic, that feed on organic matter such as other microorganisms or organic tissues and debris.</p>",
                    solution_hi: "<p>41.(a) <strong>विषाणु । हेपेटाइटिस A</strong> - यह एक वायरल संक्रमण है जो यकृत को प्रभावित करता है और हेपेटाइटिस A विषाणु (HAV) के कारण होता है। यह विषाणु दूषित भोजन या पानी से या किसी संक्रमित व्यक्ति के निकट संपर्क से फैलता है। <strong>जीवाणु</strong> - ये एकल-कोशिका वाले सूक्ष्मजीव हैं जो विभिन्न वातावरण में पाए जा सकते हैं और पारिस्थितिक तंत्र में आवश्यक भूमिका निभाते हैं, कुछ फायदेमंद होते हैं जबकि अन्य बीमारियों का कारण बन सकते हैं। <strong>प्रोटोजोआ</strong> - ये एकल-कोशिका वाले यूकेरियोट्स का एक समूह हैं, जो या तो मुक्त-जीवित या परजीवी हैं, जो अन्य सूक्ष्मजीवों या कार्बनिक ऊतकों और मलबे जैसे कार्बनिक पदार्थों का इस्तेमाल भोजन के रूप में करते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. Which of the following terms will replace the question mark (?) in the given series to make it logically complete?<br>RZ, BX, FU, ? , NL</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन सा पद दी गई श्रृंखला को तार्किक रूप से पूर्ण बनाने के लिए प्रश्न चिह्न (?) को प्रतिस्थापित करेगा? <br>RZ, BX, FU, ? , NL</p>",
                    options_en: ["<p>SL</p>", "<p>UN</p>", 
                                "<p>GS</p>", "<p>JQ</p>"],
                    options_hi: ["<p>SL</p>", "<p>UN</p>",
                                "<p>GS</p>", "<p>JQ</p>"],
                    solution_en: "<p>42.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077205.png\" alt=\"rId18\" width=\"231\" height=\"95\"></p>",
                    solution_hi: "<p>42.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077205.png\" alt=\"rId18\" width=\"231\" height=\"95\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. The valency of nitrogen in <math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>NH</mi><mn>3</mn></msub></math> is -</p>",
                    question_hi: "<p>43. <math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>NH</mi><mn>3</mn></msub></math>में नाइट्रोजन की संयोजकता कितनी है?</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>1</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>1</p>", "<p>3</p>"],
                    solution_en: "<p>43.(d) <strong>3</strong>. Nitrogen (N, Atomic number: 7) has 5 electrons in its valence shell. It can therefore accept 3 electrons to complete its octet. Valency of hydrogen is 1. Ammonia (<math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>NH</mi><mn>3</mn></msub></math>) is an inorganic compound composed of a single nitrogen atom covalently bonded to three hydrogen atoms and mainly used in agriculture as fertilizer. It is also used as a refrigerant gas and in the manufacture of plastics, explosives, textiles, pesticides, dyes and other chemicals.</p>",
                    solution_hi: "<p>43.(d) <strong>3</strong> । नाइट्रोजन (N, परमाणु संख्या: 7) के संयोजकता कोश में 5 इलेक्ट्रॉन होते हैं। इसलिए यह अपना अष्टक पूरा करने के लिए 3 इलेक्ट्रॉन ग्रहण कर सकता है। हाइड्रोजन की संयोजकता 1 है। अमोनिया (<math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>NH</mi><mn>3</mn></msub></math>) एक अकार्बनिक यौगिक है जो एक एकल नाइट्रोजन परमाणु से बना है जो सहसंयोजक रूप से तीन हाइड्रोजन परमाणुओं से जुड़ा होता है और मुख्य रूप से कृषि में उर्वरक के रूप में उपयोग किया जाता है। इसका उपयोग रेफ्रिजरेंट गैस के रूप में और प्लास्टिक, विस्फोटक, कपड़ा, कीटनाशक, रंग और अन्य रसायनों के निर्माण में भी किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. Read the given statement and arguments carefully and select the appropriate answer from the given options.<br><strong>Statement:</strong><br>Unemployment among youth has emerged as the biggest challenge in post-COVID-19 times.<br><strong>Arguments:</strong><br>I. Many small-scale industries stopped working during the COVID period.<br>II. People do not want to work in small scale industries.</p>",
                    question_hi: "<p>44. दिए गए कथनों और तर्कों को ध्यानपूर्वक पढ़िए और दिए गए विकल्पों में से उपयुक्त उत्तर का चयन कीजिए।<br><strong>कथन:</strong><br>COVID-19 के बाद के समय में युवाओं के लिए बेरोजगारी सबसे बड़ी चुनौती बनकर उभरी है। <br><strong>तर्कः</strong><br>I. कई लघु उद्योगों ने COVID अवधि के दौरान काम करना बंद कर दिया। <br>II. लोग लघु उद्योगों में काम करना नहीं चाहते हैं।</p>",
                    options_en: ["<p>I weakens while II strengthens the statement.</p>", "<p>Both I and II strengthen the statement.</p>", 
                                "<p>II weakens while I strengthens the statement.</p>", "<p>Both I and II weaken the statement.</p>"],
                    options_hi: ["<p>I कथन का समर्थन नहीं करता है, जबकि II कथन का समर्थन करता है।</p>", "<p>I और II दोनों कथन का समर्थन करते हैं।</p>",
                                "<p>II कथन का समर्थन नहीं करता है, जबकि । कथन का समर्थन करता है।</p>", "<p>I और II दोनों कथन का समर्थन नहीं करते हैं।</p>"],
                    solution_en: "<p>44.(b) Both arguments are directly related to the statement.<br>Therefore , correct answer (b)</p>",
                    solution_hi: "<p>44.(b) दोनों तर्क सीधे कथन से संबंधित हैं |<br>इसलिए, सही उत्तर (b) है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. Which of the following is not an acid - base indicator?</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन सा अम्ल-क्षारक सूचक नहीं है?</p>",
                    options_en: ["<p>Phenolphthalein</p>", "<p>starch paper</p>", 
                                "<p>methyl orange</p>", "<p>litmus</p>"],
                    options_hi: ["<p>फीनॉलफ्थेलिन</p>", "<p>स्टार्च पेपर</p>",
                                "<p>मिथाइल ऑरेंज</p>", "<p>लिटमस</p>"],
                    solution_en: "<p>45.(b) <strong>Starch paper</strong>. Acid-Base Indicators - Substances whose solutions change color due to changes in pH. They are usually weak acids or bases, but their conjugate base or acid forms have different colors due to differences in their absorption spectra. Examples - Methyl violet (Yellow on acid, Blue on base), Thymol blue - (Red on acid, Yellow on base), Methyl orange - (Red on acid, Yellow on base), Bromocresol green - (Yellow on acid, Blue on base).</p>",
                    solution_hi: "<p>45.(b) <strong>स्टार्च पेपर</strong> । अम्ल-क्षार सूचक - वे पदार्थ जिनके विलयन का रंग pH में परिवर्तन के कारण बदल जाता है। वे आमतौर पर दुर्बल अम्ल या क्षार होते हैं, लेकिन उनके संयुग्मित क्षार या अम्ल के रूपों में उनके अवशोषण स्पेक्ट्रा में अंतर के कारण अलग-अलग रंग होते हैं। उदाहरण - मिथाइल वायलेट (अम्ल पर पीला, क्षार पर नीला), थाइमोल ब्लू - (अम्ल पर लाल, क्षार पर पीला), मिथाइल ऑरेंज- (अम्ल पर लाल, क्षार पर पीला), ब्रोमोक्रेसोल ग्रीन- (अम्ल पर पीला, क्षार पर नीला)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. Read the given statement and the arguments carefully and select the appropriate answer from the given options.<br><strong>Statement:</strong><br>\"If waste management capabilities of Hospital X are dealt with, we will definitely win the \'excellence in service\' award for year 22-23.\" One of the board of directors from Hospital X.<br><strong>Arguments:</strong><br>I. In four separate incidents in the year 22-23, 15 patients lost their lives and 46 were injured due to various incidents such as fire breakout in ICU, lack of oxygen etc.<br>II. Hospital A located in the nearby city has won \'excellence in service\' award for a record five times.</p>",
                    question_hi: "<p>46. दिए गए कथन और तर्कों को ध्यानपूर्वक पढ़िए और दिए गए विकल्पों में से उपयुक्त उत्तर का चयन कीजिए।<br><strong>कथन:</strong><br>\"यदि अस्पताल X की अपशिष्ट प्रबंधन क्षमताओं से निपटा जाता है, तो हम निश्चित रूप से वर्ष 22-23 के लिए \'सेवा में उत्कृष्टता\' पुरस्कार जीतेंगे।\" अस्पताल X के निदेशक मंडल में से एक।<br><strong>तर्क:</strong><br>I. वर्ष 22-23 में चार अलग-अलग घटनाओं में, ICU में आग लगने, ऑक्सीजन की कमी आदि जैसी विभिन्न घटनाओं के कारण 15 रोगियों की जान चली गई और 46 घायल हो गए।<br>II. पास के शहर में स्थित अस्पताल A ने रिकॉर्ड पांच बार \'सेवा में उत्कृष्टता\' पुरस्कार जीता है।</p>",
                    options_en: ["<p>II weakens while I strengthens the statement.</p>", "<p>I weakens while II strengthens the statement.</p>", 
                                "<p>II weakens while I is a neutral argument</p>", "<p>I weakens while II is a neutral Argument</p>"],
                    options_hi: ["<p>तर्क II कथन का समर्थन नहीं करता है जबकि तर्क I कथन का समर्थन करता है।</p>", "<p>तर्क I कथन का समर्थन नहीं करता है जबकि तर्क II कथन का समर्थन करता है।</p>",
                                "<p>तर्क II कथन का समर्थन नहीं करता है जबकि I एक तटस्थ तर्क है।</p>", "<p>तर्क I कथन का समर्थन नहीं करता है जबकि II एक तटस्थ तर्क है।</p>"],
                    solution_en: "<p>46.(d) I weakens while II is a neutral argument</p>",
                    solution_hi: "<p>46.(d) तर्क I कथन का समर्थन नहीं करता है जबकि II एक तटस्थ तर्क है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. PNG file is a/an :</p>",
                    question_hi: "<p>47. PNG फ़ाइल एक है:</p>",
                    options_en: ["<p>video file</p>", "<p>image file</p>", 
                                "<p>batch file</p>", "<p>audio file</p>"],
                    options_hi: ["<p>वीडियो फ़ाइल (Video File)</p>", "<p>छवि फ़ाइल (Image File)</p>",
                                "<p>बैच फ़ाइल (Batch File)</p>", "<p>ऑडियो फाइल (Audio File)</p>"],
                    solution_en: "<p>47.(b)<strong> Image file.</strong> The Portable Network Graphic (PNG) file format is ideal for digital art (flat images, logos, icons, etc.), and uses 24-bit color as a foundation.</p>",
                    solution_hi: "<p>47.(b) <strong>छवि फ़ाइल</strong> (Image File) । पोर्टेबल नेटवर्क ग्राफिक (PNG) फ़ाइल फॉर्मेट डिजिटल कला (file format is ideal for digital art) फ्लैट छवियों (flat image), लोगो (logo), आइकन (icon), आदि के लिए आदर्श है, और नींव (foundation) के रूप में 24- बिट रंग का उपयोग करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. Six friends M, N, O, P, Q and R are seated around a round table facing the centre. P is an immediate neighbour of both N and R. Q is an immediate neighbour of both M and O. N is not seated at either side of O. Where is M seated?</p>",
                    question_hi: "<p>48. छह दोस्त, M, N, O, P, Q और R एक गोल मेज के चारों ओर मेज के केंद्र की ओर मुख करके बैठे हैं। P, N और R दोनों के ठीक बगल में बैठा है। Q, M और O दोनों के ठीक बगल में बैठा है। N O के किसी भी ओर ठीक बगल में नहीं बैठा है। M कहां बैठा है?</p>",
                    options_en: ["<p>Beside R</p>", "<p>Beside P</p>", 
                                "<p>Beside N</p>", "<p>Beside O</p>"],
                    options_hi: ["<p>R के बगल में</p>", "<p>P के बगल में</p>",
                                "<p>N के बगल में</p>", "<p>O के बगल में</p>"],
                    solution_en: "<p>48.(c) <br>As per instruction given in the question<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077436.png\" alt=\"rId19\" width=\"138\" height=\"132\"><br>We can see from the above arrangement that M sits beside N and Q . thus, correct answer is N</p>",
                    solution_hi: "<p>48.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077436.png\" alt=\"rId19\" width=\"138\" height=\"132\"><br>उपरोक्त व्यवस्था से हम देख सकते हैं कि M, N और Q के पास बैठा है इस प्रकार, सही उत्तर N है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. Name the Indian classical dance form which is believed to be revealed by Lord Brahma to Bharata, a famous sage, who then codified this sacred dance in a Sanskrit text called &lsquo;Natya Shastra&rsquo;.</p>",
                    question_hi: "<p>49. उस भारतीय शास्त्रीय नृत्य का नाम बताइए, जिसके बारे में माना जाता है कि वह एक प्रसिद्ध ऋषि भरत को भगवान ब्रह्मा द्वारा प्रकट किया गया था, जिन्होंने तब इस पवित्र नृत्य को \'नाट्य शास्त्र\' नामक संस्कृत पाठ में संहिताबद्ध किया था।</p>",
                    options_en: ["<p>Kathak</p>", "<p>Odissi</p>", 
                                "<p>Bharatanatyam</p>", "<p>Kathakali</p>"],
                    options_hi: ["<p>कथक</p>", "<p>ओडिसी</p>",
                                "<p>भरतनाट्यम</p>", "<p>कथकली</p>"],
                    solution_en: "<p>49.(c) <strong>Bharatanatyam</strong> (Tamil Nadu). It is the oldest of the classical dances originated from Tamil Nadu. It is formerly known as &lsquo;Sadariya&rsquo;. Kathak (Uttar Pradesh) revolves around the concept of storytelling. Odissi dance (Odisha) pays great importance to Lord Jagannath who is considered to be the god of the universe and is worshiped by the people of Orissa. Kathakali (Kerala) is a &lsquo;story play&rsquo; genre of art.</p>",
                    solution_hi: "<p>49.(c) <strong>भरतनाट्यम</strong> (तमिलनाडु)। यह तमिलनाडु से उत्पन्न शास्त्रीय नृत्यों में सबसे पुराना है। इसे पहले \'सदरिया\' के नाम से जाना जाता था। कथक (उत्तर प्रदेश) कहानी कहने की अवधारणा के इर्द-गिर्द है। ओडिसी नृत्य (ओडिशा) भगवान जगन्नाथ को अत्यधिक महत्व प्रदान करता है, जिन्हें ब्रह्मांड का देवता माना जाता है और उड़ीसा के लोगों द्वारा उनकी पूजा की जाती है। कथकली (केरल) कला की एक \'कहानी नाटक\' शैली है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. Four friends, Ankita, Shruti, Payal and Mehak, are sitting at a rectangular table, each at one corner. Ankita is sitting to the right of Shruti, who is sitting diagonally opposite to Payal. Where is Ankita sitting with respect to Mehak?</p>",
                    question_hi: "<p>50. चार मित्र अंकिता, श्रुति, पायल और महक प्रत्येक एक कोने पर एक आयताकार मेज पर बैठे हैं। अंकिता श्रुति के दायें बैठी है, जो पायल के तिर्यक रूप से सामने बैठी है। अंकिता महक के सापेक्ष कहाँ बैठी है?</p>",
                    options_en: ["<p>Directly opposite</p>", "<p>Diagonally opposite</p>", 
                                "<p>Immediate right</p>", "<p>Immediate left</p>"],
                    options_hi: ["<p>ठीक सामने</p>", "<p>विकर्णतः विपरीत</p>",
                                "<p>तत्काल दायें</p>", "<p>तत्काल बाएं</p>"],
                    solution_en: "<p>50.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077632.png\" alt=\"rId20\" width=\"144\" height=\"130\"><br>Ankita is sitting diagonally opposite to Mehak.</p>",
                    solution_hi: "<p>50.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077769.png\" alt=\"rId21\" width=\"119\" height=\"119\"><br>अंकिता महक के विकर्णतः विपरीत बैठी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. Which of the following is a non-metallic form of mineral ?</p>",
                    question_hi: "<p>51. निम्नलिखित में से कौन सा खनिज का अधात्विक रूप है?</p>",
                    options_en: ["<p>Bauxite</p>", "<p>Mica</p>", 
                                "<p>Silver</p>", "<p>Lead</p>"],
                    options_hi: ["<p>बॉक्साइट</p>", "<p>अभ्रक</p>",
                                "<p>चांदी</p>", "<p>लेड</p>"],
                    solution_en: "<p>51.(b) <strong>Mica.</strong> Some other important Non Metallic Minerals - Limestone, gypsum, coal, dolomite, phosphate, salt, and granite. Metallic Minerals - Iron and steel minerals (Magnetite, hematite, goethite, limonite, siderite), Aluminum minerals (gibbsite, boehmite, diaspore, Bauxite), Copper minerals (chalcopyrite, chalcocite), Zinc minerals (sphalerite), Tungsten minerals (wolframite, scheelite), Silver minerals (native silver, argentite). Tungsten (Very high melting point (3422&deg; Celsius)) is used in making&nbsp;the filament of an incandescent bulb.</p>",
                    solution_hi: "<p>51.(b) <strong>अभ्रक।</strong> कुछ अन्य महत्वपूर्ण अधात्विक खनिज - चूना पत्थर, जिप्सम, कोयला, डोलोमाइट, फॉस्फेट, लवण और ग्रेनाइट। धात्विक खनिज - लोहा और इस्पात खनिज (मैग्नेटाइट, हेमेटाइट, गोएथाइट, लिमोनाइट, सिडेराइट), एल्यूमीनियम खनिज (जिबसाइट, बोहेमाइट, डायस्पोर, बॉक्साइट), कॉपर खनिज (चाल्कोपाइराइट, चेल्कोसाइट), जिंक खनिज (स्फेलेराइट), टंगस्टन खनिज (वोल्फ्रामाइट, स्कीलाइट), सिल्वर खनिज (प्राकृत सिल्वर, अर्जेन्टाइट)। टंगस्टन (बहुत उच्च गलनांक (3422&deg; सेल्सियस)) का उपयोग इनकैंडीसेंट बल्ब के फिलामेंट को बनाने में किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. Suraj starts walking 9 m towards the east, then turns left. He walks 5 m, turns right and again walks 5 m. Then he turns right and walks 8 m. He then again turns right and walks 5 m. Again, he turns right and walks 3 m. Which direction is he facing now ?&nbsp;(All turns are 90 degree turns only)</p>",
                    question_hi: "<p>52. सूरज 9 m पूर्व की ओर चलना प्रारंभ करता है, फिर बाएं मुड़ता है। वह 5 m चलता है, दाएं मुड़ता है और फिर 5 m चलता है। फिर वह दायें मुड़ता है और 8 m चलता है। वह फिर दायें मुड़ता है और 5 m चलता है। फिर से वह दायें मुड़ता है और 3 m चलता है। अब उसका मुख किस दिशा की ओर है? <br>(सभी मोड़ केवल 90 डिग्री के मोड़ हैं)</p>",
                    options_en: ["<p>North</p>", "<p>West</p>", 
                                "<p>South</p>", "<p>East</p>"],
                    options_hi: ["<p>उत्तर</p>", "<p>पश्चिम</p>",
                                "<p>दक्षिण</p>", "<p>पूर्व</p>"],
                    solution_en: "<p>52.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077913.png\" alt=\"rId22\" width=\"160\" height=\"107\"><br>Now, we can clearly see that he is facing in the North direction.</p>",
                    solution_hi: "<p>52.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010077913.png\" alt=\"rId22\" width=\"160\" height=\"107\"><br>अब, हम स्पष्ट रूप से देख सकते हैं कि उसका मुख उत्तर दिशा की ओर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. A sum of money becomes 8 times of itself in 3 years at compound interest compounded annually. The rate of interest is : _____.</p>",
                    question_hi: "<p>53. एक राशि वार्षिक चक्रवृद्धि ब्याज पर 3 वर्षों में स्वयं का 8 गुना हो जाती है। ब्याज दर कितनी है ?</p>",
                    options_en: ["<p>100%</p>", "<p>Data inadequate</p>", 
                                "<p>8%</p>", "<p>5%</p>"],
                    options_hi: ["<p>100%</p>", "<p>डेटा अपर्याप्त</p>",
                                "<p>8%</p>", "<p>5%</p>"],
                    solution_en: "<p>53.(a) P = 1 , A = 8 , Time = 3 year <br>Now time 3 year so that <br><math display=\"inline\"><mroot><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>8</mn><mn>3</mn></mroot></math>= 1 : 2 <br>R % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math> &times; 100 = 100%</p>",
                    solution_hi: "<p>53.(a) P = 1 , A = 8 , समय = 3 वर्ष<br>अब समय 3 साल है तो <br><math display=\"inline\"><mroot><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>8</mn><mn>3</mn></mroot></math> = 1 : 2&nbsp;<br>R % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math>&nbsp;&times; 100 = 100%</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. Which of the following Vedas talks about the treatment of diseases?</p>",
                    question_hi: "<p>54. निम्नलिखित में से कौन सा वेद रोगों के उपचार की बात करता है?</p>",
                    options_en: ["<p>Atharva Veda</p>", "<p>Yajur Veda</p>", 
                                "<p>Sama Veda</p>", "<p>Rig Veda</p>"],
                    options_hi: ["<p>अथर्ववेद</p>", "<p>यजुर्वेद</p>",
                                "<p>सामवेद</p>", "<p>ऋग्वेद</p>"],
                    solution_en: "<p>54.(a) <strong>Atharva Veda</strong> (&ldquo;Veda of magical formulas\") includes Samhitas, Brahmana text and three primary Upanishads - Mundaka, Mandukya and Prashna Upanishads. Rig Veda (&ldquo;Veda of Verses&rdquo;) - The oldest of the Vedas and deals with a huge collection of hymns, poems and shlokas. It talks about the origin of the world, the importance of the Gods etc. Yajur Veda (&ldquo;Veda of Sacrificial Formulas&rdquo;) - Deals with religious rituals and worship of the Gods. Sama Veda (&ldquo;Veda of Chants&rdquo;) - The shortest of all Vedas and deals with the praise and invocation of various Gods in musical form.</p>",
                    solution_hi: "<p>54.(a) <strong>अथर्ववेद</strong> (\"जादुई सूत्रों का वेद\") में संहिता, ब्राह्मण पाठ और तीन प्राथमिक उपनिषद शामिल हैं - मुंडक, मांडूक्य और प्रश्न उपनिषद। ऋग्वेद (\"छंदों का वेद\") - वेदों में सबसे पुराना और भजनों, कविताओं और श्लोकों के विशाल संग्रह से संबंधित है। यह दुनिया की उत्पत्ति, देवताओं के महत्व आदि के बारे में बात करता है। यजुर्वेद (\"बलि सूत्रों का वेद\") - धार्मिक अनुष्ठानों और देवताओं की पूजा से संबंधित है। सामवेद (\"मंत्रों का वेद\") - सभी वेदों में सबसे छोटा और संगीतमय रूप में विभिन्न देवताओं की स्तुति और आह्वान से संबंधित है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. If every 4th letter going from left to right of the English alphabet is deleted, then what would be the 5th letter from&nbsp;the right in the new series obtained?</p>",
                    question_hi: "<p>55. यदि अंग्रेजी वर्णमाला के बाएँ से दाएँ जाने वाले प्रत्येक चौथे अक्षर को हटा दिया जाए, तो प्राप्त नई श्रृंखला में दाएँ से पाँचवाँ अक्षर क्या होगा?</p>",
                    options_en: ["<p>E</p>", "<p>F</p>", 
                                "<p>U</p>", "<p>V</p>"],
                    options_hi: ["<p>E</p>", "<p>F</p>",
                                "<p>U</p>", "<p>V</p>"],
                    solution_en: "<p>55.(c) If every 4th letter going from left to right of the English alphabet is deleted, then the 24th letter is deleted also. 5th from the right = 26 - 5 = 21 = U</p>",
                    solution_hi: "<p>55.(c) यदि अंग्रेजी वर्णमाला के बाएँ से दाएँ जाने वाले प्रत्येक चौथे अक्षर को हटा दिया जाए, तो 24वें अक्षर को भी हटा दिया जाता है।<br>दायें से 5वां = 26 - 5 = 21 = U</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. All 30 students of a class are standing in a straight row facing the north. Ashok is 16th from the right end, while Aruna is 19th from the left end. How many students stand between Ashok and Aruna?</p>",
                    question_hi: "<p>56. एक कक्षा के सभी 30 विद्यार्थी उत्तर की ओर मुख करके एक सीधी पंक्ति में खड़े हैं। अशोक दायें छोर से 16वें स्थान पर है, जबकि अरुणा बाएं छोर से 19वें स्थान पर है। अशोक और अरुणा के बीच कितने विद्यार्थी खड़े हैं?</p>",
                    options_en: ["<p>One</p>", "<p>Four</p>", 
                                "<p>Three</p>", "<p>Two</p>"],
                    options_hi: ["<p>एक</p>", "<p>चार</p>",
                                "<p>तीन</p>", "<p>दो</p>"],
                    solution_en: "<p>56.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010078069.png\" alt=\"rId23\" width=\"211\" height=\"119\"><br>So, total number of students between Ashok and Aruna = 19 - 16 = 3</p>",
                    solution_hi: "<p>56.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010078240.png\" alt=\"rId24\" width=\"319\" height=\"144\"><br>इसलिए, अशोक और अरुणा के बीच छात्रों की कुल संख्या = 19 &ndash; 16 = 3</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57. Who wrote the famous Bengali novel &lsquo;Pather Panchali&rsquo;?</p>",
                    question_hi: "<p>57. प्रसिद्ध बंगाली उपन्यास \'पाथेर पांचाली\' किसने लिखा था?</p>",
                    options_en: ["<p>Sharat Chandra Chattopaydhy</p>", "<p>Rabindra Nath Tagore</p>", 
                                "<p>Bibhutibhushan Bandopadhyay</p>", "<p>Bankim Chandra Chattopadhay</p>"],
                    options_hi: ["<p>शरत चंद्र चट्टोपाध्याय</p>", "<p>रविंद्रनाथ टैगोर</p>",
                                "<p>विभूतिभूषण बंदोपाध्याय</p>", "<p>बंकिम चंद्र चट्टोपाध्याय</p>"],
                    solution_en: "<p>57.(c) <strong>Bibhutibhushan Bandopadhyay. Other books</strong>: &ldquo;Chander Pahar\", &ldquo;Aranyak&rdquo;, &ldquo;Adarsha Hindu Hotel\", \"Hire Manik Jale&rdquo;. Sharat Chandra Chattopaydhy (&ldquo;Devdas&rdquo;, &ldquo;Charitraheen&rdquo;, &ldquo;Parineeta&rdquo;). Rabindra Nath Tagore (&ldquo;Gitanjali&rdquo;, &ldquo;Kabuliwala&rdquo;, &ldquo;Gora&rdquo;, &ldquo;The Post Office&rdquo;, &ldquo;My Reminiscences&rdquo;). Bankim Chandra Chattopadhay : &ldquo;Anandamath&rdquo;, &ldquo;Kapalkundala&rdquo;, &ldquo;Vande Mataram&rdquo;, &ldquo;Mrinalini&rdquo;.</p>",
                    solution_hi: "<p>57.(c) <strong>विभूतिभूषण बंदोपाध्याय। अन्य पुस्तकें:</strong> \"चंदर पहाड़\", \"अरण्यक\", \"आदर्श हिंदू होटल\", \" हायर माणिक जले\"। शरत चंद्र चट्टोपाध्याय (\"देवदास\", \"चरित्रहीन\", \"परिणीता\")। रवीन्द्र नाथ टैगोर (\"गीतांजलि\", \" काबुलीवाला&rdquo;, &ldquo;गोरा&rdquo;, &ldquo;द पोस्ट ऑफिस&rdquo;, &ldquo;माई रिमिनिसेंस&rdquo;)। बंकिम चंद्र चट्टोपाध्याय: &ldquo;आनंदमठ&rdquo;, &ldquo;कपालकुंडला&rdquo;, &ldquo;वंदे मातरम्&rdquo;, &ldquo;मृणालिनी&rdquo;।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. A, B and C are well known authors in Hindi, English and Punjabi not necessarily in the same order. Read the given statement and decide which of the following conclusions logically follows from the statements. <br><strong>Statements:</strong> <br>1. C does not write in English. <br>2. The book of essays is in Punjabi <br>3. The book of poems in English has won an award.<br>4. A is a novelist. <br>5. B is not an essayist.</p>",
                    question_hi: "<p>58. A, B और C हिंदी, अंग्रेजी और पंजाबी में जाने-माने लेखक हैं, जरूरी नहीं कि इसी क्रम में हों। दिए गए कथन को पढ़ें और बताइये कि निम्नलिखित में से कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong> <br>1. C अंग्रेजी में नहीं लिखता है।<br>2. निबंध की किताब पंजाबी में है।<br>3.अंग्रेजी में कविताओं की पुस्तक ने एक पुरस्कार जीता है। <br>4. A उपन्यासकार हैं।<br>5. B एक निबंधकार नहीं है ।</p>",
                    options_en: ["<p>C writes poems in English.</p>", "<p>B writes poems in Hindi.</p>", 
                                "<p>B writes poems in English.</p>", "<p>A writes essays in Punjabi.</p>"],
                    options_hi: ["<p>C अंग्रेजी में कविता लिखता है।</p>", "<p>B हिन्दी में कविताएँ लिखता है।</p>",
                                "<p>B अंग्रेजी में कविताएँ लिखता है।</p>", "<p>A पंजाबी में निबंध लिखता है।</p>"],
                    solution_en: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010078357.png\" alt=\"rId25\" width=\"178\" height=\"74\"></p>",
                    solution_hi: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010078464.png\" alt=\"rId26\" width=\"163\" height=\"92\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. On 28th November 2024, which online learning platform was unveiled by the Chief of the Army Staff General for the Indian Army?</p>",
                    question_hi: "<p>59. 28 नवंबर 2024 को भारतीय सेना के प्रमुख जनरल द्वारा किस ऑनलाइन लर्निंग प्लेटफॉर्म का उद्घाटन किया गया?</p>",
                    options_en: ["<p>Eklavya</p>", "<p>Army Learn</p>", 
                                "<p>Shakti</p>", "<p>Army Connect</p>"],
                    options_hi: ["<p>एकलव्य</p>", "<p>आर्मी लर्न</p>",
                                "<p>शक्ति</p>", "<p>आर्मी कनेक्ट</p>"],
                    solution_en: "<p>59.(a) <strong>Eklavya.</strong> General Upendra Dwivedi, Chief of the Army Staff, launched the \"Eklavya\" online learning platform for the Indian Army on 28th November 2024. This platform, developed under the aegis of Headquarters Army Training Command with Army War College as the sponsor agency, is hosted on the Army Data Network and features a scalable architecture capable of integrating multiple training establishments and offering diverse courses.</p>",
                    solution_hi: "<p>59.(a) <strong>एकलव्य।</strong> भारतीय सेना के प्रमुख जनरल उपेन्द्र द्विवेदी ने 28 नवंबर 2024 को भारतीय सेना के लिए \"एकलव्य\" ऑनलाइन लर्निंग प्लेटफॉर्म का उद्घाटन किया। यह प्लेटफॉर्म, जो मुख्यालय आर्मी ट्रेनिंग कमांड के तहत और आर्मी वार कॉलेज द्वारा प्रायोजित किया गया है, आर्मी डेटा नेटवर्क पर होस्ट किया गया है और इसमें एक स्केलेबल आर्किटेक्चर है जो विभिन्न प्रशिक्षण संस्थानों को एकीकृत करने और विविध पाठ्यक्रमों की पेशकश करने में सक्षम है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. Out of four brothers, M,N, O, and P. M and N play cricket and football. N and O play cricket and hockey, O and P play hockey and basketball, P and M play football and basketball. Who plays football, basketball and hockey?</p>",
                    question_hi: "<p>60. चार भाइयों में से, M,N, O, एवं P में से M और N क्रिकेट और फुटबॉल खेलते हैं। N और O क्रिकेट और हॉकी खेलते हैं, O और P हॉकी और बास्केटबॉल खेलते हैं, P और M फुटबॉल और बास्केटबॉल खेलते हैं। फुटबॉल, बास्केटबॉल और हॉकी कौन खेलता है?</p>",
                    options_en: ["<p>P</p>", "<p>O</p>", 
                                "<p>N</p>", "<p>M</p>"],
                    options_hi: ["<p>P</p>", "<p>O</p>",
                                "<p>N</p>", "<p>M</p>"],
                    solution_en: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010078600.png\" alt=\"rId27\" width=\"314\" height=\"104\"></p>",
                    solution_hi: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010078747.png\" alt=\"rId28\" width=\"270\" height=\"156\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Two valves P and B can fill a sump in 37<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> minutes and 45 minutes respectively. Both valves are opened. The sump will be filled in just 30 minutes, if valve B is turned off after :</p>",
                    question_hi: "<p>61. दो पाइप P और B एक टंकी को क्रमश: 37<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>मिनट और 45 मिनट में भर सकते हैं। दोनों पाइप खुले हैं। टंकी 30 मिनट में भर जायेगी, यदि पाइप B को________ मिनट बाद बंद कर दिया जाता हैं।</p>",
                    options_en: ["<p>9 min</p>", "<p>6 min</p>", 
                                "<p>10 min</p>", "<p>5 min</p>"],
                    options_hi: ["<p>9 मिनट</p>", "<p>6 मिनट</p>",
                                "<p>10 मिनट</p>", "<p>5 मिनट</p>"],
                    solution_en: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010078892.png\" alt=\"rId29\" width=\"168\" height=\"146\"><br>Work done by P in 30 min <br>= 6 &times; 30 = 180 unit<br>Remaining work = 225 - 180 = 45 unit which is completed by B in <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 9min<br>So, valve B turned off after 9 min.</p>",
                    solution_hi: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010079052.png\" alt=\"rId30\" width=\"141\" height=\"133\"><br>P द्वारा 30 मिनट में किया गया कार्य = 6 &times; 30 <br>= 180 इकाई<br>शेष कार्य = 225 &ndash; 180 = 45 इकाई, जो B द्वारा पूरा किया जाता है = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 9 मिनट में<br>इसलिए, वाल्व B 9 मिनट के बाद बंद हो गया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. Read the given statements and conclusions carefully and decide which of the conclusions logically follow(s) from the statements.<br><strong>Statement:</strong><br>P = Q<math display=\"inline\"><mi>&#160;</mi><mo>&#8805;</mo></math> R &le; S &gt; T &lt; U<br><strong>Conclusions:</strong> <br>I. Q &gt; U</p>\n<p>II. R &le; P</p>",
                    question_hi: "<p>62. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़िए और तय कीजिये कि कौन-सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong> <br>P = Q <math display=\"inline\"><mo>&#8805;</mo></math> R &le; S &gt; T &lt; U<br><strong>निष्कर्ष:</strong> <br>I. Q &gt; U <br>II. R &le; P</p>",
                    options_en: ["<p>Only conclusion II is true</p>", "<p>Neither conclusion I nor II is true</p>", 
                                "<p>Both conclusions I and II are true</p>", "<p>Only conclusion I is true</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II सत्य है</p>", "<p>न तो निष्कर्ष I और न ही II सत्य है</p>",
                                "<p>दोनों निष्कर्ष I और II सत्य हैं</p>", "<p>केवल निष्कर्ष I सत्य है</p>"],
                    solution_en: "<p>62.(a)<br><strong>Given statement: </strong>P = Q<math display=\"inline\"><mi>&#160;</mi><mo>&#8805;</mo></math> R &le; S &gt; T &lt; U<br>I. Q &gt; UFalse (as Q <math display=\"inline\"><mo>&#8805;</mo></math> R &le; S &gt; T &lt; U)<br>II. R &le; PTrue (as P = Q <math display=\"inline\"><mo>&#8805;</mo></math> R)<br>Only conclusion II is true.</p>",
                    solution_hi: "<p>62.(a) <strong>दिया गया कथन:</strong> <br>P = Q <math display=\"inline\"><mo>&#8805;</mo></math> R &le; S &gt; T &lt; U<br>I. Q &gt; U असत्य (चूंकि Q <math display=\"inline\"><mo>&#8805;</mo></math> R &le; S &gt; T &lt; U)<br>II. R &le; P सत्य (चूंकि P = Q <math display=\"inline\"><mo>&#8805;</mo></math> R)<br>केवल निष्कर्ष II सत्य है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. Which of the following activities is environmentally friendly?</p>",
                    question_hi: "<p>63. निम्नलिखित में से कौन सी गतिविधि पर्यावरण के अनुकूल है ?</p>",
                    options_en: ["<p>Using polythene bags for shopping</p>", "<p>Construction of skyscrapers</p>", 
                                "<p>Using ACs and coolers during the summer</p>", "<p>Using solar-power for generation of electricity</p>"],
                    options_hi: ["<p>खरीदारी के लिए पॉलीथिन बैग का उपयोग करना</p>", "<p>गगनचुंबी इमारतों का निर्माण</p>",
                                "<p>गर्मी के दिनों में AC और कूलर का उपयोग करना</p>", "<p>बिजली के उत्पादन के लिए सौर ऊर्जा का उपयोग</p>"],
                    solution_en: "<p>63.(d) As a renewable source of power, solar energy has an important role in reducing greenhouse gas emissions and mitigating climate change, which is critical to protecting humans, wildlife, and ecosystems. Solar energy can also improve air quality and reduce water use from energy production. Solar power is one of the most environmentally friendly energy sources.</p>",
                    solution_hi: "<p>63.(d) बिजली के नवीकरणीय स्रोत के रूप में, ग्रीनहाउस गैस उत्सर्जन को कम करने और जलवायु परिवर्तन को कम करने में सौर ऊर्जा की महत्वपूर्ण भूमिका है, जो मानव, वन्यजीव और पारिस्थितिक तंत्र की रक्षा के लिए महत्वपूर्ण है। सौर ऊर्जा भी हवा की गुणवत्ता में सुधार कर सकती है और ऊर्जा उत्पादन से पानी के उपयोग को कम कर सकती है। सौर ऊर्जा सबसे अधिक पर्यावरण के अनुकूल ऊर्जा स्रोतों में से एक है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. If sin<math display=\"inline\"><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>=</mo><msqrt><mn>5</mn></msqrt></math> , then the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>3</mn></msup><mi>&#952;</mi><mo>+</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>3</mn></msup><mi>&#952;</mi></math> is :</p>",
                    question_hi: "<p>64. यदि <math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>=</mo><msqrt><mn>5</mn></msqrt></math> है तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>3</mn></msup><mi>&#952;</mi><mo>+</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>3</mn></msup><mi>&#952;</mi></math> का मान ज्ञात कीजिये ?</p>",
                    options_en: ["<p>3<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt></mrow></mfrac></math></p>", 
                                "<p>0</p>", "<p><math display=\"inline\"><mn>2</mn><msqrt><mn>5</mn></msqrt></math></p>"],
                    options_hi: ["<p>3<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt></mrow></mfrac></math></p>",
                                "<p>0</p>", "<p><math display=\"inline\"><mn>2</mn><msqrt><mn>5</mn></msqrt></math></p>"],
                    solution_en: "<p>64.(d)<br>By the using this formula <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010079223.png\" alt=\"rId31\" width=\"177\" height=\"123\"><br>sin<math display=\"inline\"><mi>&#952;</mi></math> + cosec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>sin<math display=\"inline\"><mi>&#952;</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>So that , <br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo></mrow><mn>3</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>=</mo><mn>2</mn><msqrt><mn>5</mn></msqrt></math></p>",
                    solution_hi: "<p>64.(d)</p>\n<p>इस सूत्र के प्रयोग से,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010079223.png\" alt=\"rId31\" width=\"167\" height=\"116\"><br>sin<math display=\"inline\"><mi>&#952;</mi></math> + cosec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>sin<math display=\"inline\"><mi>&#952;</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n<p>तो ,<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo></mrow><mn>3</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>=</mo><mn>2</mn><msqrt><mn>5</mn></msqrt></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. Which popular folk dance of Himachal pradesh has a Yagya performed by the dancers at the end of the dance performance as a homage to the Gods and Goddesses?</p>",
                    question_hi: "<p>65. हिमाचल प्रदेश के इनमें से किस लोकप्रिय लोक नृत्य में, नृत्य प्रस्तुति के अंत में देवी-देवताओं को समर्पण की भावना से नर्तकियों द्वारा यज्ञ किया जाता है ?</p>",
                    options_en: ["<p>Jawara</p>", "<p>Bihu</p>", 
                                "<p>Kalbelia</p>", "<p>Nati</p>"],
                    options_hi: ["<p>जवार</p>", "<p>बिहू</p>",
                                "<p>कालबेलिया</p>", "<p>नाटी</p>"],
                    solution_en: "<p>65.(d) <strong>Nati</strong> - It is performed by a group of people with accompanying musicians. The dance group comprises both men and women. <strong>Kalbelia</strong> dance (known by the names of \'Sapera Dance\' or \'Snake Charmer Dance\') is a folk dance of Rajasthan.</p>",
                    solution_hi: "<p>65.(d) <strong>नाटी</strong> - यह संगीतकारों के साथ लोगों के एक समूह द्वारा प्रस्तुत किया जाता है। नृत्य समूह में पुरुष और महिला दोनों शामिल हैं। <strong>कालबेलिया</strong> नृत्य (\'सपेरा नृत्य\' या \'स्नेक चार्मर नृत्य\' के नाम से जाना जाता है) राजस्थान का एक लोक नृत्य है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. Select the correct combination of mathematical signs that can sequentially replace the * and balance the given equation. <br>32 * 16 * 8 * 2 * 3 * 5 * 5</p>",
                    question_hi: "<p>66. गणितीय चिह्नों के सही संयोजन का चयन करें जो क्रमिक रूप से * को प्रतिस्थापित कर सकता है और दिए गए समीकरण को संतुलित कर सकता है।<br>32 * 16 * 8 * 2 * 3 * 5 * 5</p>",
                    options_en: ["<p><math display=\"inline\"><mo>-</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo><mo>,</mo><mi>&#160;</mi><mo>&#247;</mo><mo>,</mo><mi>&#160;</mi><mo>&#215;</mo><mo>,</mo><mi>&#160;</mi><mo>=</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo></math></p>", "<p><math display=\"inline\"><mo>+</mo><mo>,</mo><mi>&#160;</mi><mo>-</mo><mo>,</mo><mi>&#160;</mi><mo>&#247;</mo><mo>,</mo><mi>&#160;</mi><mo>&#215;</mo><mo>,</mo><mi>&#160;</mi><mo>=</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo></math></p>", 
                                "<p><math display=\"inline\"><mo>-</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo><mo>,</mo><mi>&#160;</mi><mo>&#247;</mo><mo>,</mo><mi>&#160;</mi><mo>=</mo><mo>,</mo><mi>&#160;</mi><mo>&#215;</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo></math></p>", "<p><math display=\"inline\"><mo>-</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo><mo>,</mo><mi>&#160;</mi><mo>&#215;</mo><mo>,</mo><mi>&#160;</mi><mo>=</mo><mo>,</mo><mi>&#160;</mi><mo>&#247;</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mo>-</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo><mo>,</mo><mi>&#160;</mi><mo>&#247;</mo><mo>,</mo><mi>&#160;</mi><mo>&#215;</mo><mo>,</mo><mi>&#160;</mi><mo>=</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo></math></p>", "<p><math display=\"inline\"><mo>+</mo><mo>,</mo><mi>&#160;</mi><mo>-</mo><mo>,</mo><mi>&#160;</mi><mo>&#247;</mo><mo>,</mo><mi>&#160;</mi><mo>&#215;</mo><mo>,</mo><mi>&#160;</mi><mo>=</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo></math></p>",
                                "<p><math display=\"inline\"><mo>-</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo><mo>,</mo><mi>&#160;</mi><mo>&#247;</mo><mo>,</mo><mi>&#160;</mi><mo>=</mo><mo>,</mo><mi>&#160;</mi><mo>&#215;</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo></math></p>", "<p><math display=\"inline\"><mo>-</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo><mo>,</mo><mi>&#160;</mi><mo>&#215;</mo><mo>,</mo><mi>&#160;</mi><mo>=</mo><mo>,</mo><mi>&#160;</mi><mo>&#247;</mo><mo>,</mo><mi>&#160;</mi><mo>+</mo></math></p>"],
                    solution_en: "<p>66.(c) 32 * 16 * 8 * 2 * 3 * 5 * 5<br>By hit and trial method, go through option (c)<br>32 - 16 + 8 &divide; 2 = 3 &times; 5 + 5<br><math display=\"inline\"><mo>&#8658;</mo></math> 32 - 16 + 4 = 15 + 5 &rArr; 20 = 20 (satisfy)</p>",
                    solution_hi: "<p>66.(c) 32 * 16 * 8 * 2 * 3 * 5 * 5<br>हिट एंड ट्रायल विधि से, विकल्प (c) के द्वारा <br>32 - 16 + 8 &divide; 2 = 3 &times; 5 + 5<br>32 - 16 + 4 = 15 + 5 <math display=\"inline\"><mo>&#8658;</mo></math> 20 = 20 (संतुष्ट)</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. The kanjira is a frame drum of South India. It consists of a skin (usually iguana) stretched and pasted on a circular _____frame.</p>",
                    question_hi: "<p>67. कंजीरा, दक्षिण भारत का फ्रेम ड्रम है। इसमें चमड़ा (सामान्यतः इगुआना) लगा होता है , जिसे वृत्ताकार _______ के फ्रेम पर फैलाकर चिपकाया जाता है।</p>",
                    options_en: ["<p>brass</p>", "<p>&nbsp;dried pumpkin</p>", 
                                "<p>Wooden</p>", "<p>Steel</p>"],
                    options_hi: ["<p>पीतल</p>", "<p>सूखे कद्दू</p>",
                                "<p>लकड़ी</p>", "<p>स्टील</p>"],
                    solution_en: "<p>67.(c) <strong>Wooden.</strong> Kanjira is a percussion instrument made of wood of jackfruit tree, lizard skin, goatskin, and metal. This folk instrument is found in various parts of South India.</p>",
                    solution_hi: "<p>67.(c) <strong>लकड़ी।</strong> कंजीरा कटहल के पेड़ की लकड़ी, छिपकली की खाल, बकरी की खाल और धातु से बना एक ताल वाद्य यंत्र है। यह लोकवाद्य दक्षिण भारत के विभिन्न भागों में पाया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68.<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>05</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>15</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>053</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mn>0</mn><mo>.</mo><mn>005</mn></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>015</mn></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>0053</mn></mrow><mn>2</mn></msup></mrow></mfrac></msqrt></math> find the value ?</p>",
                    question_hi: "<p>68. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>05</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>15</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>053</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mn>0</mn><mo>.</mo><mn>005</mn></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>015</mn></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>0053</mn></mrow><mn>2</mn></msup></mrow></mfrac></msqrt></math>&nbsp;का मान ज्ञात कीजिए</p>",
                    options_en: ["<p><math display=\"inline\"><msup><mrow><mn>10</mn></mrow><mrow><mn>4</mn></mrow></msup></math></p>", "<p><math display=\"inline\"><msup><mrow><mn>10</mn></mrow><mrow></mrow></msup></math></p>", 
                                "<p><math display=\"inline\"><msup><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p><math display=\"inline\"><msup><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></msup></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msup><mrow><mn>10</mn></mrow><mrow><mn>4</mn></mrow></msup></math></p>", "<p><math display=\"inline\"><msup><mrow><mn>10</mn></mrow><mrow></mrow></msup></math></p>",
                                "<p><math display=\"inline\"><msup><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p><math display=\"inline\"><msup><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></msup></math></p>"],
                    solution_en: "<p>68.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>05</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>15</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>053</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mn>0</mn><mo>.</mo><mn>005</mn></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>015</mn></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>0053</mn></mrow><mn>2</mn></msup></mrow></mfrac></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>0</mn><mo>.</mo><mn>0025</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>0225</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>002809</mn><mi>&#160;</mi></mrow><mrow><mfrac><mn>1</mn><mn>100</mn></mfrac><mo>(</mo><mn>0</mn><mo>.</mo><mn>0025</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>0225</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>002809</mn><mo>)</mo></mrow></mfrac></msqrt></math> = 10</p>",
                    solution_hi: "<p>68.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>05</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>15</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>053</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mn>0</mn><mo>.</mo><mn>005</mn></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>015</mn></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>0053</mn></mrow><mn>2</mn></msup></mrow></mfrac></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>0</mn><mo>.</mo><mn>0025</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>0225</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>002809</mn><mi>&#160;</mi></mrow><mrow><mfrac><mn>1</mn><mn>100</mn></mfrac><mo>(</mo><mn>0</mn><mo>.</mo><mn>0025</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>0225</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>002809</mn><mo>)</mo></mrow></mfrac></msqrt></math> = 10</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Which of the following festivals is associated with the state of Odisha?</p>",
                    question_hi: "<p>69. निम्न में से कौन सा त्योहार ओडिशा में मनाया जाता है?</p>",
                    options_en: ["<p>Ambubachi</p>", "<p>Pongtu</p>", 
                                "<p>Raja Parba</p>", "<p>Porag</p>"],
                    options_hi: ["<p>अंबुबाची</p>", "<p>पोंगटू</p>",
                                "<p>रज पर्व</p>", "<p>पराग</p>"],
                    solution_en: "<p>69.(c) <strong>Raja Parba</strong> / Mithuna Sankranti / Swing Festival (three day long festival). Other Festivals of Odisha - Durga Pooja, Kalinga Mahotsav, Mahabisuva Sankranti, Magha Saptmi, Chhau Festival. Festivals of Assam - Bihu, Baishagu, Ali-Ai-Ligang, Baikho, Rongker, Rajini Gabra Harni Gabra, Bohaggiyo Bishu, Ambubashi Mela, Porag and Jonbeel Mela.</p>",
                    solution_hi: "<p>69.(c) <strong>रज पर्व</strong>/ मिथुन संक्रांति/ स्विंग फेस्टिवल (तीन दिवसीय त्योहार)। ओडिशा के अन्य त्यौहार - दुर्गा पूजा, कलिंग महोत्सव, महाबिसुवा संक्रांति, माघ सप्तमी, छऊ महोत्सव। असम के त्यौहार - बिहू, बैशागू , अली-ऐ-लिगंग, बैखो, रोंगकर, रजनी गबरा हरनी गबरा, बोहागियो बिशु, अंबुबाशी मेला, पोराग और जोनबील मेला।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. If the mean and the mode of a data set are 5 and 8, respectively, then, using the relevant empirical relation, find the median of the same data set.</p>",
                    question_hi: "<p>70. यदि किसी आकड़ो का एक समुच्चय का माध्य और बहुलक क्रमशः 5 और 8 हैं, तो प्रासंगिक मूलानुपाती संबंध का उपयोग करके, उसी आंकड़ा समुच्चय का माध्यिका ज्ञात करें।</p>",
                    options_en: ["<p>6</p>", "<p>7</p>", 
                                "<p>14</p>", "<p>3.5</p>"],
                    options_hi: ["<p>6</p>", "<p>7</p>",
                                "<p>14</p>", "<p>3.5</p>"],
                    solution_en: "<p>70.(a)<br><strong>Formula</strong> <math display=\"inline\"><mo>&#8594;</mo></math> Mode = 3Median - 2 Mean<br>8 = 3Median <math display=\"inline\"><mo>-</mo></math> 2 &times; 5<br>Median = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>10</mn></mrow><mn>3</mn></mfrac></math> = 6</p>",
                    solution_hi: "<p>70.(a)<br><strong>Formula </strong><math display=\"inline\"><mo>&#8594;</mo></math> बहुलक = 3 &times; माध्यिका - 2 माध्य<br>8 = 3 &times; माध्यिका <math display=\"inline\"><mo>-</mo></math> 2 &times; 5<br>माध्यिका = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>10</mn></mrow><mn>3</mn></mfrac></math> = 6</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. The minimum age required to contest the election for the President of India is</p>",
                    question_hi: "<p>71. भारत के राष्ट्रपति पद पर निर्वाचन हेतु उम्मीदवार के लिए वांछित न्यूनतम आयु कितनी है?</p>",
                    options_en: ["<p>35 Years</p>", "<p>32 years</p>", 
                                "<p>25 years</p>", "<p>21 years</p>"],
                    options_hi: ["<p>35 वर्ष</p>", "<p>32 वर्ष</p>",
                                "<p>25 वर्ष</p>", "<p>21 वर्ष</p>"],
                    solution_en: "<p>71.(a) <strong>35 Years. </strong>Article 58 (Part V) - Qualifications for election of President- He should be a citizen of India, completed the age of 35 years, and qualified for election as a member of the LokSabha. A person shall not be eligible for election as President if he holds any office of profit under the Government of India or the Government of any State or under any local or other authority.</p>",
                    solution_hi: "<p>71.(a) <strong>35 वर्ष।</strong> अनुच्छेद 58 (भाग V) - राष्ट्रपति के चुनाव के लिए योग्यता- वह भारत का नागरिक होना चाहिए, 35 वर्ष की आयु पूरी कर चुका हो, और लोकसभा के सदस्य के रूप में चुनने योग्य हो। ऐसा व्यक्ति राष्ट्रपति के रूप में चुनाव के लिए पात्र नहीं होगा यदि वह भारत सरकार या किसी राज्य सरकार या किसी स्थानीय या अन्य प्राधिकरण के अधीन लाभ का कोई पद धारण किया है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. If 1 is added to each digit of the number 870548362, what will be the sum of the fourth, fifth and sixth digit when counted from left?</p>",
                    question_hi: "<p>72. यदि संख्या 870548362 के प्रत्येक अंक में 1 जोड़ दिया जाए, तो बाएं से गिनने पर चोथे, पांचवें और छठे अंक का योगफल कितना होगा?</p>",
                    options_en: ["<p>17</p>", "<p>24</p>", 
                                "<p>20</p>", "<p>16</p>"],
                    options_hi: ["<p>17</p>", "<p>24</p>",
                                "<p>20</p>", "<p>16</p>"],
                    solution_en: "<p>72.(c) Number = 870548362<br>If 1 is added to each digit, then,<br>Number = 981659473<br>Sum of the fourth, fifth and sixth <br>= 6 + 5 + 9 = 20</p>",
                    solution_hi: "<p>72.(c) संख्या = 870548362<br>यदि प्रत्येक अंक में 1 जोड़ा जाए, तो,<br>संख्या = 981659473<br>चौथे, पांचवें और छठे का योग = 6 + 5 + 9 = 20</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. What is the usual shape for an average fixed cost curve?</p>",
                    question_hi: "<p>73. औसत स्थिर लागत वक्र का सामान्य आकार कैसी होती है?</p>",
                    options_en: ["<p>Rectangular hyperbola</p>", "<p>Inverted V-shaped</p>", 
                                "<p>U-shaped</p>", "<p>Horizontal line</p>"],
                    options_hi: ["<p>आयताकार अतिपरवलय (Rectangular hyperbola)</p>", "<p>उलटा V आकृति (Inverted V-shaped)</p>",
                                "<p>U-आकृति (U-shaped)</p>", "<p>क्षैतिज रेखा (Horizontal line)</p>"],
                    solution_en: "<p>73.(a) <strong>Rectangular hyperbola</strong>. The average fixed cost (AFC) is the fixed cost that does not change with the change in the number of goods and services produced by a company. It is downward sloping because fixed costs are distributed over a larger volume when the quantity produced increases. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010079366.png\" alt=\"rId32\" width=\"135\" height=\"99\"></p>",
                    solution_hi: "<p>73.(a) <strong>आयताकार अतिपरवलय</strong>। औसत निश्चित लागत (AFC), वह निश्चित लागत है जो किसी कंपनी द्वारा उत्पादित वस्तुओं और सेवाओं की संख्या में परिवर्तन के साथ नहीं बदलती है। यह नीचे की ओर झुका हुआ वक्र है क्योंकि उत्पादित मात्रा में वृद्धि होने पर निश्चित लागत को&nbsp;बड़ी मात्रा में वितरित किया जाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010079540.png\" alt=\"rId33\" width=\"129\" height=\"98\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. Sohan is facing north-west. He turns 90&deg; clockwise, then 360&deg; anticlockwise and then 90&deg; clockwise. In which direction is Sohan facing now ?</p>",
                    question_hi: "<p>74. सोहन का मुख उत्तर-पश्चिम की ओर है। वह 90&deg; दक्षिणावर्त, फिर 360&deg; वामावर्त और फिर 90&deg; दक्षिणावर्त मुड़ता है। सोहन का मुख अब किस दिशा में है?</p>",
                    options_en: ["<p>North - west</p>", "<p>North - east</p>", 
                                "<p>South - west</p>", "<p>South - east</p>"],
                    options_hi: ["<p>उत्तर - पश्चिम</p>", "<p>उत्तर - पूर्व</p>",
                                "<p>दक्षिण - पश्चिम</p>", "<p>दक्षिण - पूर्व</p>"],
                    solution_en: "<p>74.(d)<br>Clockwise = 90&deg; + 90&deg; = 180&deg;<br>Anticlockwise = 360&deg;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010079676.png\" alt=\"rId34\" width=\"152\" height=\"137\"><br>360&deg; - 180&deg; = 180&deg; anticlockwise<br>Sohan is facing in the South-east direction.</p>",
                    solution_hi: "<p>74.(d) दक्षिणावर्त = 90&deg; + 90&deg; = 180&deg;<br>वामावर्त = 360&deg;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010079676.png\" alt=\"rId34\" width=\"152\" height=\"137\"><br>360&deg; - 180&deg; = 180&deg; वामावर्त<br>सोहन का मुख दक्षिण-पूर्व दिशा में है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75. Which state launched the \'KSRTC Arogya\' health insurance scheme?</p>",
                    question_hi: "<p>75. किस राज्य ने \'KSRTC आरोग्य\' स्वास्थ्य बीमा योजना शुरू की?</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>Karnataka</p>", 
                                "<p>Kerala</p>", "<p>Andhra Pradesh</p>"],
                    options_hi: ["<p>तमिलनाडु</p>", "<p>कर्नाटक</p>",
                                "<p>केरल</p>", "<p>आंध्र प्रदेश</p>"],
                    solution_en: "<p>75.(b) <strong>Karnataka</strong> Chief Minister Siddaramaiah launched the &lsquo;KSRTC Arogya&rsquo; health insurance scheme to support public transport employees. This scheme offers cashless medical treatment to over 34,000 KSRTC (Karnataka State Road Transport Corporation employees and their families, benefiting around 1.5 lakh people.</p>",
                    solution_hi: "<p>75.(b) <strong>कर्नाटक</strong> के मुख्यमंत्री सिद्धारमैया ने सार्वजनिक परिवहन कर्मचारियों की सहायता के लिए \'KSRTC आरोग्य\' स्वास्थ्य बीमा योजना शुरू की। यह योजना 34,000 से अधिक KSRTC (कर्नाटक राज्य सड़क परिवहन निगम के कर्मचारियों और उनके परिवारों को कैशलेस चिकित्सा उपचार प्रदान करती है, जिससे लगभग 1.5 लाख लोग लाभान्वित होते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. The number whose only factors are 1 and the number itself is called a/an ____ number.</p>",
                    question_hi: "<p>76. वह संख्या जिसके केवल गुणनखंड 1 हैं और वह संख्या स्वयं _____ संख्या कहलाती है।</p>",
                    options_en: ["<p>composite</p>", "<p>prime</p>", 
                                "<p>even</p>", "<p>odd</p>"],
                    options_hi: ["<p>संमिश्रित</p>", "<p>अभाज्य संख्या</p>",
                                "<p>सम</p>", "<p>विषम</p>"],
                    solution_en: "<p>76.(b) The number whose only factors are 1 and the number itself is called a Prime number.<br>e.g. 2,3,5,7,......</p>",
                    solution_hi: "<p>76.(b) वह संख्या जिसका केवल गुणनखंड 1 हो और वह संख्या ही अभाज्य संख्या कहलाती हो। <br>उदाहरण के लिए 2,3,5,7,......</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. Who ranked first in the Forbes 50 Over 50 Global List 2025?</p>",
                    question_hi: "<p>77. फोर्ब्स 50 ओवर 50 ग्लोबल लिस्ट 2025 में कौन पहले स्थान पर है?</p>",
                    options_en: ["<p>Quarraisha Abdool Karim</p>", "<p>Rafaela Aponte-Diamant</p>", 
                                "<p>Urmila Asher</p>", "<p>Kiran Mazumdar-Shaw</p>"],
                    options_hi: ["<p>क्वारैशा अब्दुल करीम</p>", "<p>राफैला अपोंटे-डायमेंट</p>",
                                "<p>उर्मिला अशर</p>", "<p>किरण मजूमदार-शॉ</p>"],
                    solution_en: "<p>77.(a) <strong>Quarraisha Abdool Karim</strong> is an infectious diseases epidemiologist. Rafaela Aponte-Diamant is the co-owner of the Mediterranean Shipping Company (MSC) based in Geneva, Switzerland, ranked 2nd. The list features three remarkable Indian women: Urmila Asher, popularly known as \'Gujju Ben\' and the founder of Gujju Ben Na Nasta, ranked 3rd; Kiran Mazumdar-Shaw, a billionaire and founder of Biocon India Private Limited, ranked 32nd; and Sheela Patel, a renowned activist, ranked 37th.</p>",
                    solution_hi: "<p>77.(a) <strong>क्वारैशा अब्दुल करीम</strong> एक संक्रामक रोग महामारी विज्ञानी हैं। राफैला अपोंटे-डायमेंट स्विट्जरलैंड के जिनेवा में स्थित मेडिटेरेनियन शिपिंग कंपनी (MSC) की सह-मालिक हैं, जो दूसरे स्थान पर हैं। सूची में तीन उल्लेखनीय भारतीय महिलाएँ शामिल हैं: उर्मिला अशर, जिन्हें \'गुज्जू बेन\' के नाम से जाना जाता है और गुज्जू बेन नास्ता की संस्थापक हैं, तीसरे स्थान पर हैं; किरण मजूमदार-शॉ, एक अरबपति और बायोकॉन इंडिया प्राइवेट लिमिटेड की संस्थापक, 32वें स्थान पर हैं; और शीला पटेल, एक प्रसिद्ध कार्यकर्ता, 37वें स्थान पर हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. How many factors of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>7</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>4</mn></msup><mo>&#215;</mo><mn>7</mn></math> are even ?</p>",
                    question_hi: "<p>78. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>7</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>4</mn></msup><mo>&#215;</mo><mn>7</mn></math>&nbsp;के कितने सम गुणज धनात्मक है ।</p>",
                    options_en: ["<p>280</p>", "<p>320</p>", 
                                "<p>84</p>", "<p>40</p>"],
                    options_hi: ["<p>280</p>", "<p>320</p>",
                                "<p>84</p>", "<p>40</p>"],
                    solution_en: "<p>78.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>7</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>4</mn></msup><mo>&#215;</mo><mn>7</mn></math><br>Even number of factors <br>= 7 &times; (3 + 1)(4 + 1)(1 + 1)<br>= 7 &times; 4 &times; 5 &times; 2 = 280</p>",
                    solution_hi: "<p>78.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>7</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>4</mn></msup><mo>&#215;</mo><mn>7</mn></math><br>सम संख्याओ का गुणनखंड <br>= 7 &times; (3 + 1)(4 + 1)(1 + 1)<br>= 7 &times; 4 &times; 5 &times; 2 = 280</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. The following statement applies to which law from the given options?<br>&lsquo;The line that joins any planet to the sun sweeps equal areas in equal intervals of time.&rsquo;</p>",
                    question_hi: "<p>79. निम्नलिखित कथन दिए गए विकल्पों में से किस नियम पर लागू होता है?<br>\'\'किसी भी ग्रह को सूर्य से मिलाने वाली रेखा समय के समान अंतराल में समान क्षेत्र तय करती है।\'</p>",
                    options_en: ["<p>Law of Periods</p>", "<p>Law of Areas</p>", 
                                "<p>Law of Motion</p>", "<p>Law of Orbits</p>"],
                    options_hi: ["<p>परिक्रमण काल का नियम (Law of Periods)</p>", "<p>क्षेत्रफलों का नियम (Law of Areas)</p>",
                                "<p>गति का नियम (Law of Motion)</p>", "<p>कक्षाओं का नियम (Law of Orbits)</p>"],
                    solution_en: "<p>79.(b) <strong>Law of Areas.</strong> Kepler\'s laws of planetary motion are three laws that describe the motion of planets around the Sun. Law of Orbits - All planets move in elliptical orbits, with the Sun at one focus. Law of Areas - A line segment joining a planet and the Sun sweeps out equal areas during equal intervals of time. Law of Periods - The square of the period of any planet is proportional to the cube of the semimajor axis of its orbit.</p>",
                    solution_hi: "<p>79.(b) <strong>क्षेत्रफलों का नियम।</strong> ग्रहों की गति के अनुसार केप्लर के तीन नियम हैं जो सूर्य के चारों ओर ग्रहों की गति का वर्णन करते हैं। कक्षाओं का नियम - सभी ग्रह दीर्घवृत्ताकार कक्षाओं में घूमते हैं, जिसमें सूर्य एक केंद्र पर स्थित होता है। क्षेत्रफलों का नियम -\' किसी भी ग्रह को सूर्य से मिलाने वाली रेखा समान समय अंतराल में समान क्षेत्र तय करती है। परिक्रमण काल का नियम - किसी भी ग्रह के परिक्रमण काल का वर्ग उसकी कक्षा के अर्धप्रमुख अक्ष के घन के समानुपाती होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. The areas of three adjacent faces of a solid cuboid are 66 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math>, 108 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math>and 198 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math>. What is the volume (in cm<sup>3</sup>) of the cuboid?</p>",
                    question_hi: "<p>80. एक ठोस घनाभ के तीन आसन्न फलकों के क्षेत्रफल 66 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math>, 108 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math>और 198 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math>हैं। घनाभ का आयतन (cm<sup>3</sup> में) ज्ञात कीजिए ।</p>",
                    options_en: ["<p>594</p>", "<p>1188</p>", 
                                "<p>1782</p>", "<p>891</p>"],
                    options_hi: ["<p>594</p>", "<p>1188</p>",
                                "<p>1782</p>", "<p>891</p>"],
                    solution_en: "<p>80.(b) Area of adjacent faces , <br>l &times; b =11 &times; 6 = 66 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>b &times; h = 6 &times; 18 = 108 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>h &times; l = 18 &times; 11 = 198 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>Length = 11 cm , breadth = 6 cm and height = 18 cm<br>Now, Volume of cuboid <br>= lbh = 11 &times; 6 &times; 18 = 1188 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mn>3</mn></msup></math></p>",
                    solution_hi: "<p>80.(b) आसन्न फलकों का क्षेत्रफल,<br>l &times; b =11 &times; 6 = 66 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>b &times; h = 6 &times; 18 = 108 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>h &times; l = 18 &times; 11 = 198 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>लंबाई = 11 cm, चौड़ाई = 6 cm <br>और ऊंचाई = 18 cm<br>अब, घनाभ का आयतन = <math display=\"inline\"><mi>l</mi><mi>b</mi><mi>h</mi></math><br>= lbh = 11 &times; 6 &times; 18 = 1188 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mn>3</mn></msup></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. At which position on the principal axis does a concave mirror form a highly diminished, real an inverted image of an object?</p>",
                    question_hi: "<p>81. मुख्य अक्ष पर किस स्थिति में एक अवतल दर्पण किसी वस्तु का अत्यधिक छोटा, वास्तविक और उल्टा प्रतिबिंब बनाता है?</p>",
                    options_en: ["<p>2F</p>", "<p>beyond 2F</p>", 
                                "<p>between F and 2F</p>", "<p>F</p>"],
                    options_hi: ["<p>2F</p>", "<p>2F से दूर</p>",
                                "<p>F और 2F के मध्य</p>", "<p>F</p>"],
                    solution_en: "<p>81.(d) <strong>Focus (F). </strong>Other positions of image in Concave mirror explained in the following syntax - Placement of the object (Image obtained): At infinity (Highly diminished, real and inverted). Beyond the center of curvature (Diminished, real and inverted). At the center of curvature (Same size as that of the object, real and inverted). Between the center of curvature and principal of focus (Enlarged, real and inverted). Between the principal focus and the pole (Image is obtained behind the mirror, highly enlarged, virtual and erect).</p>",
                    solution_hi: "<p>81.(d) <strong>फोकस (F)।</strong> अवतल दर्पण में प्रतिबिंब की अन्य स्थितियों को निम्नलिखित वाक्य-विन्यास में समझाया गया है - वस्तु का स्थान (प्राप्त प्रतिबिंब): अनंत पर (अत्यधिक कम, वास्तविक और उल्टा)। वक्रता के केंद्र से परे (कम, वास्तविक और उल्टा)। वक्रता के केंद्र में (वस्तु के समान आकार, वास्तविक और उल्टा)। वक्रता केंद्र और फोकस के सिद्धांत के बीच (बड़ा, वास्तविक और उल्टा)। मुख्य फ़ोकस और ध्रुव के बीच (प्रतिबिंब दर्पण के पीछे प्राप्त होता है, अत्यधिक बढ़ा हुआ, आभासी और सीधा)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. Find area of a triangle whose base measures 126.4 cm and height 54.8 cm.</p>",
                    question_hi: "<p>82. उस त्रिभुज का क्षेत्रफल ज्ञात कीजिए जिसका आधार 126.4 सेमी और ऊँचाई 54.8 सेमी है।</p>",
                    options_en: ["<p>3343.36 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>9267.72 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", 
                                "<p>6926.72 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>3463.36 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    options_hi: ["<p>3343.36 cm&sup2;</p>", "<p>9267.72 cm&sup2;</p>",
                                "<p>6926.72 cm&sup2;</p>", "<p>3463.36 cm&sup2;</p>"],
                    solution_en: "<p>82.(d) Area of triangle<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 126.4 &times; 54.8 = 3463.36</p>",
                    solution_hi: "<p>82.(d) त्रिभुज का क्षेत्रफल <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 126.4 &times; 54.8 = 3463.36</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. If the time period of a sound wave is 0.02 s, then what is its frequency?</p>",
                    question_hi: "<p>83. यदि किसी ध्वनि तरंग का आवर्तकाल 0.02 सेकंड है, तो उसकी आवृत्ति क्या है?</p>",
                    options_en: ["<p>100 Hz</p>", "<p>500 Hz</p>", 
                                "<p>25 Hz</p>", "<p>50 Hz</p>"],
                    options_hi: ["<p>100 हर्ट्ज</p>", "<p>500 हर्ट्ज</p>",
                                "<p>25 हर्ट्ज</p>", "<p>50 हर्ट्ज</p>"],
                    solution_en: "<p>83.(d) <strong>50 Hz</strong>. Given, <br>the time period = 0.02s.<br>The frequency (f) of a wave is the reciprocal of time (T),<br>Frequency (f) = 1/time period = 1/T<br>Frequency (f) = 1/0.02 = 50 Hz.</p>",
                    solution_hi: "<p>83.(d) <strong>50 Hz</strong>. दिया गया,<br>समयावधि = 0.02s.<br>तरंग की आवृत्ति समय का व्युत्क्रम है,<br>आवृत्ति (f) = 1/समयावधि = 1/T<br>आवृत्ति (f) = 1/0.02 = 50 हर्ट्ज़।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. The value of&nbsp;<img src=\"data:image/png;base64,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\" width=\"146\" height=\"21\">is:</p>",
                    question_hi: "<p>84. <img src=\"data:image/png;base64,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\" width=\"132\" height=\"19\">का मान ज्ञात कीजिए ।</p>",
                    options_en: ["<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"29\"></p>", "<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"24\"></p>", 
                                "<p><img src=\"data:image/png;base64,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\" width=\"101\" height=\"25\"></p>", "<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"26\"></p>"],
                    options_hi: ["<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"29\"></p>", "<p><img src=\"data:image/png;base64,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\" width=\"99\" height=\"23\"></p>",
                                "<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAATMAAABMCAYAAAABW1D+AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAACAmSURBVHhe7Z17UFTJ1cDPAEPkIQwICCJiMaiosdQsiGY1hkKlfJQv1DXGACVLtFYXitWsKdbSqjVxF4rNosTSpLDig2hEWdcXuBLKqFFBXR+FsPKQhyMvBRxkADOMc74/vhlqpu/tO/fO3EHB+6s6/5x7u2/P3L6nu093n5YhIoKExHtKeno6/PGPfyTVEoOMrq4ucCCVEhISEoMRyZhJSEgMCSRjJiEhMSSQST4zCQmJoYDUM5OQkBgSSMZMQkJiSCAZMwkJiSGBZMwkJCSGBJIxk5CQGBJIxkxCQmJIIBkzCQmJIYFkzCQkJIYEkjGTkJAYEkjGTEJCYkggGTMJCYkhgWTMJCQkhgSSMZOQkBgSSMZMQkJiSCAZMwkJiSGBZMwkJHjQ2toKubm5sGrVKhg9ejQ4OjqCTCbrFz8/P5g5cyZkZGRAVVUVvK0wgX19fXD37l3YtWsXzJw5E7y9vc3K6ejoCKNHj4bly5fDoUOHoLW1lcxi8IISEhKs6PV6vHr1KkZERCAACBJfX1/MyspCjUZDZmsX2tra8PPPP0cXFxdGWSxJREQEXr16FfV6PZmt1bx69Qpnz57NeJbY4urqinfv3kVERMmYSUiwoFKpMDo6mvHxCBUPDw88fvw46nQ68hGioNVqMSMjA+VyOePZQmXFihXY3NxMPsIqnjx5gv7+/oxniC2SMZOQ4ODChQvo6urK+HBskYSEBNF7ac3NzaIYXFMJCgrC+/fvk48SzPXr19HJyYmRv9hiaswkn5mEhAn5+fmwdOlS6OnpIS/ZxOHDh2HZsmXQ2dlJXrKKhoYGmDdvHhQXF5OXbEKlUkFUVBTcvn2bvCSI8vJy0Ol0pNquSMZMQsLA7du34eOPPwa9Xk9e6kehUMDGjRuhoKAAVCoVNDU1QVVVFRw+fBhWrlwJDg70T6q4uBj+8Ic/QF9fH3lJEA0NDbB48WIoLy8nL/Ujl8vho48+gry8PKivrzcr57x588jbzVCr1bB+/XpoaGggL/Hmzp07pMr+kN1DCYn3EZVKhWFhYYxhjOlw5tixY6jVasmkZrS1tWFcXBwjvakcOHCATMabnp4eXLJkCSNPozg4OOCePXssDmlVKhWuWLGCkd5UNm3ahH19fWRSi2g0GtGHvzTh7TNTq9WYl5eHsbGx6Ovra5aJm5sbTp06FdPS0rCiosJuDk6+5Ofno0wm6y/f119/Td4iKnq9HisrK3Hnzp0YGRmJXl5eZv+Pl5cXRkZGYmZmJj59+pRMLsGBTqfDkpISTE5OxgkTJjCc2wEBARgbG4t5eXkWP1o+6PV63LFjB+NDMUpISAhWVFSQyajo9XrMzs5GBwcHRl4AgKGhoVbXiQMHDjDyM4qPjw8WFxeTSajodDrMysqiltPZ2Rlv3rxJJrOISqXCwMBARn4ymQyLiorI20WD1ZipVCpMSEhgFIZLfH198dChQ9jT00NmZ3fa2toY0+f2MmY6nQ5PnjyJY8aMYfwHXDJz5kwsLy8ns3trJCYmMsoohsydOxe7urrIx/FCp9Ph8ePHGQ0nl8jlcty2bRt2dnaS2fGmuroaAwICGHkDACoUCiwtLSWTWESv1+OePXsY+RnFmvppr3JyGfLNmzcLXrJBc/77+/vjkydPyNtFw8yY6fV6PHLkCKMlFCJKpRJv375tmq1dob0MayqLJSorKxlGU6hs27btrRh8U+w5DLDWmLW3t+OCBQsY+fGVoKAgq+vdrl27GPkZJTMzk7ydN62trTh58mRGngCAkZGRqFarySRU9Ho9pqSkMPIBw9Dy9OnTZBLetLS0UIfYwcHB+OzZMzIJJ7m5uYx8AABnz56Nr169Im8XjX5jptVqcevWrYwCWCNyuRzz8vIEW3RrOH36NGs3WWxjVlRUhAqFgvEca2T9+vWiDI+shTYMEEOsMWb19fXUj16IuLq64oULF8jsOVGr1RgZGcnICwAwLCwMW1payCSC+Prrrxn5Gstq9PXwoba2lvrOduzYYfO3RiunNUPDTz/9lJEPAGBiYiJ5q6gAGqx+eno64+Gm4ubmhtHR0ZiUlIRJSUkYGRnJ2YOztbXgQ2lpKdXAiGnMbty4QX2OUSZPnowbNmzApKQkjI6ORjc3N8Y9pmKtc1UMaMMAMUSoMVOr1Zy9RAcHB5wxYwbveid0uHX37l3qmrK0tDTydsFw/de5ubnk7VSys7MZ6cFG/5spZWVl1Dou5Fvi6vUfPHiQvF1UABHx8uXLrL0bMAwbL1++zOrg12q1eObMGQwKCmKkA4Ojtrq6mkwmCnfu3EEfHx/GM40i5AVwUVdXhyEhIYz8wdAD/eqrr1h7WTqdDi9fvoxKpZKRDgwf6dmzZ8lkAwJtGCCGCDFmfX19uGnTJkYeYOG/1Wq1mJubix4eHox0YGhYWltbyWSsnDhxgpEerOyRsMG1Ep5vHe3q6sK5c+cy0gMAZmdnk7dbhXH7kVwux6CgIAwJCcE1a9ZgUlIS5uTkkLdTaWlpYf1enJyc8Pr16+TtogJsznOj8B0Otbe3Y1RUFCM9GLqWYvdAbt68yWnIQEBF4YJrGjw8PBzr6urIJAy4fEFC/SZiwTYMmD17NtbU1GBTU5NN0tbWxnvIQ2tEfXx8eM2i1dXVYXh4OCM9CBx6abVabGhowO+++w5TU1Nx6tSpqFQqRXFWNzU1USeL+NZRWu8xMDAQa2trydvfKrdv32bdHxoYGIgqlYq8XVSANtUbHR0t6EOrr6/HcePGMfJRKBRYVlZG3m41fLea8K0oXNBa7fDwcEEvhtZgyGQyLCwsJG+3K7RhwKeffkreald6enowJiaGUQ5XV1csKSkhb6dCq3f2HBUI4cGDBzh8+HBG+UBAHT148CAjLQBgbGys6B0FW8nPz2eUEwz2hE/HyBYcDh8+TK6jBVdXV0hPTwdPT0/yEpXg4GDYuXMnqQa1Wg0XL14k1YLp6+uDjIwMu2w1YaOzsxOysrJINSgUCvj73/8Oo0ePJi9RGTFiBHz55Zcgk8nM9IgIJ0+eHNBwMa9evYLq6mpSDZGRkaTKrly9ehUuX75MquGzzz6DGTNmkGoqwcHBsH//fsbK++bmZsjNzTXTvQ1evHgBGo2GVAMA8KpDiAglJSWkGgAA5s+fD05OTqT6rfLjjz+SKgAAmDRpEri5uZFqUXG4f/8+qYOlS5fC1KlTSbVFoqKiIDg4mFTDnTt3bNqn9ezZM1i4cCFs376dc6uJmNy6dYt1f9pXX30F06dPJ9UWmTVrFutHeu3aNXj+/DmpthvPnj2DFy9emOlcXFxg/PjxZjp7QjPiAQEBEB8fzzD6loiKioI1a9aQajh58uRbj9d15coVxu8EQ4chLCyMVDNQq9Xw6NEjUg0uLi7wi1/8glS/VV6/fg0PHjwg1QAD1Fg6aLVaM4VMJoP4+HirLL6npyeMHTuWVENDQwN0dXWRaov09vZCRkYGhISEsG6olcvl8O2330JgYCB5ySZ0Oh0cOXKEUQkjIiJg9erVZjq+eHp6QnR0NKmGlpYWePbsGam2G1VVVdDb22umCwgIgDFjxpjp7ElTUxNcuXKFVMPcuXNZ648lnJycWI1gZWUlsDXWA0VNTQ384x//INUAADBlyhQIDQ0l1QxaWlqgtraWVIO3tzcEBASQ6reKRqOB+vp6Uj1gjSVjV+yoUaPg5z//Oanmhbu7O2vP7Pnz5/D69WtSTeXNmzdQVFQEU6ZMge3bt7NuzPXx8YFLly7BRx99BI6OjuRlm1CpVHD9+nVSDXFxcTBixAhSzZuoqChwcHAAX19fCAoKgsjISPjtb38revm5KC0tJVUQFhYmyKVgKz/++CPrJuYlS5ZY1YiCwTiQBhkRoaCgwEw3UHR3d8OOHTugubmZvAQAAAkJCbz+88bGRnj58iWphtDQUFAoFKQawOCSKSkpgZSUFAgLCwNnZ2ezaLOjRo2CVatWwalTp6C7u5tMbjWNjY2gUqlINaOx7OzshPPnz8PGjRshLCwM3N3dzcrn7e0N06ZNgy+++AJ++uknePPmjVl+VKqqqvDEiRP4+9//HkNDQ3HhwoVWO+pozuWQkBBBiw9pszemzkSjA542W8TXucrG2bNnGfnZeyvGQNDb24uLFi1i/DYx1lMJIS0tjVEGb29vQfsfSfr6+jA2NpaRb3h4OHZ0dJC325X29nZcuXIloyxGmTNnDu/JNdoyGjbnvzXRZh0cHHDDhg2CJrRonDt3jpE/AOCiRYtQo9FgcXExzpw5k3HdkvCN2su6N9NaXrx4gZMmTWIURuhMBs2YyeVy3Lt3r9maN3sYM7aPbdGiRdjb20veOqigrQEayPVuNIMqhtH585//zMjXViMphJ6eHjx06BB1/RtYsaiXtjI/Li6u/x4xos2yfVtCYfv/wfBuadulhIiHhwf+61//oi65YQwzbeHu3bvw008/kWpRZjJWrFgBtbW1kJycbNdhGc2J+eGHH8KwYcNI9aDi6dOnjGGPt7c3jBs3zkxnTzo7O+Hx48ekGsaPH08dNvGFzaH+8uVLaGxsJNWi8ObNG3j+/DlcunQJ1q5dC56enpCYmAivXr0ibwUwOP1zc3NZJ4JoVFRUkCoAAJgwYQIAAHR0dMCSJUvg888/Z3XH8KWvrw9SUlJg7dq1VgWQ1Ol0cO/ePVINYLALbO9cKK9evYK1a9fC1q1bGX5fYPOZWUtnZydkZmYynObOzs7wm9/8xkwnhIiICCgpKYH8/HxeU9m2QvvY2D6UwYZKpWJUgpCQEPD39wcgfC3jxo0zO4HI0dERxo0bBykpKVBSUmL1h9PR0QFqtZpUw9ixYxkOfKH4+fkxfG6ICE+ePDHT2UJ3dzfMmzcPZDIZODk5wciRI2HhwoVw8uRJzv9EqVTCf/7zH1i8eDF5iQoiUlcBODo6QktLCyxfvpx1iYu1nD59GmJjYwUbtK6uLlY/qD349ttvIS4ujlFGUYxZb28vbN26lXXGcdmyZRAeHk6qOXFwcIC5c+dCSUkJlJaWQmRkpM0VnS8tLS2sSxeCgoLMdGBomUtLS1k/fnd3d5g5cyZ88803rE7RtwHbGqDx48eDTCaD7du3g6enJ8yaNQv27dsHNTU1Zstg9Ho91NTUwL59+2DWrFng4+MD2dnZDONoifb2dtaeiy0TK0aGDx8OLi4upBra29tJldX09vYyerdcyOVy2LZtG9y7dw8iIiLIy5x0d3dTe5UvX76E9evXs05UAQBMmzYN9u/fD2VlZdDU1NQ/g5yWlgZKpZK83QxrIuLSZl3ZMEbr/e677/qj4DY1NUFpaSns37+fl704ffo0fPzxx+YGjRx3CkGv12NxcTF1/6GPjw8+evSITCYqYvvMioqKzII8AovfpaenB/ft2yco7tb8+fPfajwzmq8qODjYJl9LWFgYPnz4kHwcFZqT+Ny5c+StgqHVBVP/kq0IjTji6+uLX3zxBdbV1VF9PTS49mSSddQoK1assLjFydK+YaOkp6fzLnNBQQEjPSlKpRK///57Xn65trY23LJlCyMPUvbs2dNfRsHGrLe3F6uqqjA9PZ3zz1AoFKJs1LUErQJba8zYPrYxY8ZgU1MTIiJWVFRY7cx0cHDArKwsXi9TbGjOfzFESOgd2uzcYDFmtMkpPiI0QCeXMSPFmrpladZVSJiib775hpHeVFJTU62K41dcXMy5D9vZ2RmvXbuGyNeYCT3QcyADNNIqsLXGjG32yGjMxIpptnHjRqterC1w7REkRalUYlxcHCYlJWFcXByGhoaybgg3Fb4hn9j+XyEfDRcdHR2sG8+FRPKwBJ8eiCXZvXu3xbMEUIAxc3BwwOzsbN69KFMshWBau3YtYwkIiV6vx3Xr1jHS2lo2I/fv36dG5gEAjImJQeRrzGgGgxS5XI7p6ekD+qHSyiamMZs7dy7++9//phoyPz+//nApGzZswClTplj8+JOSknhVaLGgbQA2ypgxY/D48ePU5SdarRbPnTuH06ZNY6Q1Cp9lB2z/r1jGjPbxi2nMzp49i8uWLcP9+/fjlStX+qOFlJWVYU5ODi5YsMDiuwdDPDtL75/2e0ixNTYeV5grPucA6A3nYZw6dQp37tyJa9aswZCQEJTL5WbDQFugRVgBw5Ab+RozIa26QqHAzz77rH9YZm/ENmZsa2UCAgLQz8+PoU9ISKD6J/isObIlJLNQ2NbOGUXIEECr1eLu3bsZeRhl/vz5nIZjsBszPnR2dvLy91jySdF+j6mIEQ0XLRyUsmvXLvL2AUev1+PmzZsZZTOKXq/nZ8y4omVyyUDEuxfbmFk6JgwMBvvSpUucFdEI16nTAzFBghyr44HHB8WG3kJk4mPHjpFJ+nkfjJkRS3H3TP09bNB+j6mIFZyR6xwAMRY0iwFXNNxnz57xWzT78uVL0Ov1EBoaCnFxcZCUlARxcXEWp3gzMzNh8eLF0NHRQV4atCgUCvjhhx8gJiaG13IRf39/yM/PZ91k3tbWBgcOHGCszbMHW7ZsgfT0dIiLi4MJEyaAm5sbJCUlQWpqKq/fYYpMJoPU1FRISkoiLwEAwF//+lfGGqD3kVmzZsHFixepi4G1Wi1kZGQIXt5ixNvbm7VeWcPIkSNh/vz5pBoAAGpra6GlpYVUDzihoaHwy1/+klQDAEBdXR2/pRnd3d3UWRKNRoM5OTmcwymhgR6FMNA9M65eBxePHj1ibaXfxWihfKEdfcYVIvl96pkZoR26AxZ8UrTfY6/fxeVXLSgoIG9/K7DVHzCcL8CrZ+bq6krdQuTm5gaJiYlQV1cHK1euJC8DGBbh5eTkkOpBR0xMDMTGxpJqXkyePBk2b95MqqGxsVHUFdwDiVKpZH3nOp0Obty4QarfW5YuXUqtN1qtFi5cuECqAQBg2LBh4OPjQ6r7CQ0NBXd3d1JtNcHBweDq6kqqAQBYtym+DSZNmkSqAAwLo3kZMz54e3vD0aNHYf369eQlAMMWhLq6OlL9zsG1ZSohIYF1hTlfVq1axTrkuHbt2oAMNcVGJpPB8uXLWYeptgbktIa+vj7WkDZOTk6sZRwo5HI5bNiwgVqGBw8esIbIcnJy4tzTLPae2pEjR4KXlxepBjD8t+8Co0aNYjW4lZWV4hkzMPTS/vSnP7H+yY2NjaKEz7Y3Hh4epArA4PsSskGYjeDgYNZYcVVVVaz7FQcDISEhMHLkSFINra2trL4gtrrR09MjSrTd169fs+YTGBjIaRQGArZ4a0YeP35M9TEaN5QPBI6OjtQR2GBAVGMGhg82OTmZVAMAQGFhIWsLNBgIDQ0FX19fUi2I4cOHQ0hICKkWHLzyXcLDwwO8vb1JNTx9+pQ19r1cLidVAIah6VCGFoUZAOB///sftecjxp7V9wXRjRkAwKJFi/ojMZjC1QK9K9DG5D/72c8Yh2ZYA1tL29bWBk1NTaR6UDBs2DBBRt7f3591qC7G729qaoK2tjZSTX2nAwktCjMYVgvQzipQKpXU4SnvCKzvAZ6envYxZn5+fqwfrVqtfueXaXh5eTHCyICIQ5XB3I0XA3d3d1ZjJkZki97eXiDPtAALftB3HdowHgx+IjFRq9WsvWng6FED/H+oovb2dmhuboZHjx7B6dOn4Z///CdkZ2eLPuJ4/vw56+lsgYGB9jFmbm5urIeMvH79mrUg7xIjRoxg9Zs1NjayOpffd7ic7mw9WV9fX9Zeuxgf5vPnzxnDVZlMRjUGfX198PTpUzh//jxkZGRAfHx8f0z6v/3tb+TtNsEVzsfHxwdGjRpFqgEM/xft4JOKigrW8wGshRaeCQBg4sSJpArA0KucMWNG/2+YMmUKrF69GtavXw8HDx6kGkdrefr0KakCMPS+mbVNBIyB6wYj/v7+rH6tzs5O1lZfKGxnVnp5eVE/OLFQq9VmLWdhYSGrg14o3d3drPG9goKCWHuy7u7urL6j2tpaq07wMoUtqObIkSNZ3ycYhvdz5syBpUuXwvbt2+Ho0aNQWVkJ3d3dUFJSIuoMM1ccND8/P2oU4+HDh1OPNqyoqOAdQ4wP5eXljMYADCsV2N4ZGGL9+fn5kWoAAKivrxc1YKNOp4OioiJSDWDwaTvs2rWrv0Xy8PCAwsJC8j7B0FqhYcOGsU6rvksoFArWY7HEWAWt0+lYZy09PT2pldlWNBoN/PrXvwYvLy9Gy8l2LJhQ6uvrWf09tDVQw4YNg2nTppFqqKmpYQTFFAItbDPXxI27uzt1hvHKlSui+PGMVFdXQ01NDakGMEyaDR8+nFT3M2/ePFIFYJgFvnXrFqm2Ci5DYRqNmIT2PkHk8oEhUjLtQORRo0aBw5dfftnfInV1dcF///tf8j7BaDQa1jVlCoWCdebrXUImk8GvfvUrUg0dHR3w8OFDUi0ItVrNuvhw4sSJrOvPxIA25O/o6GDtJQrlxo0brK05V1TVqKgohlO7tbUVqqqqzHRCaG1thbt375JqmD59OtVQcPV6GhoaqFFchWI88o7Ws7d0MvkHH3xAnTw4evSoKP7Gx48fs0aKBgBYsGABdf0ZWDjg98KFC6KMAAAALl68yNpJAtoEwOXLl20eh9+/f5/VmE2fPn1QTDWHh4ez9iDPnj3L+uHy5cGDB6zGLCIigrMy24JMJoMPPviAVAMY3rUtQ6nOzk44c+YMqQaFQgEffvghqe5n4sSJjB4RIsKFCxesLg/tLE5ar8YI13WxfD5PnjyBQ4cOkWoAHv8VGHodS5YsIdUAhsXJp06dItWCQETIyclhHTU4OztTn21kypQprA0mAEBRUZEoHSSVSgV79+4l1QCG/9CI2R4nmUyGhYWF5JYo3mi1Wly9ejVj75Rx/5TYiL03Ew3he2JiYhh5WopywEVfXx+uXbuWkadY+xK5uHnzJjo7OzOeHRAQgNXV1eTtvMnPz2cN3xwTE8MZLUWv12NCQgIjnbX7VGl1jk94HK5oEWDDXlwjtLIZJTExkVcsMto7BMO5tHV1dWQS3pSUlFCj51p6l8gjPI+Qc0LZ0Gq1mJSUxMjXKImJiYiGVpBx0ZaH0zbVWltRLWEPY4aIeOLECUaewCNeF41r166xVkY+lcVW1Go1RkZGMp4NAJiSkiI4BBAiYn19PY4bN46Rn0wmw/z8fPJ2BoWFhayGcMeOHYLLQwvcx/e30TYvA8+AkzS0Wi1u3bqVkadRhDRkfX19mJiYyMjDKNYGc6C9RxDwLpFHOHFrg5HqdDrcs2cPIz+jmIbRAtrhDNbEuSotLaXGG7KmkvLBXsasra0NIyIiGPmCIVidkBdDqzC29oKFcOzYMcbzQUC4a1O4Qi0vWbKEl3GmRYQQWh7af6tQKLCsrIy8nZXW1lacPHkyIw+jhISECD5IWKPRsPY+TUVoFFZalBKjzJkzB5ubm8lkVOrq6ljDjBuF77tEQ+8sJSWFkYepCP1uenp6MDU1lZGPqZgGOIXMzEzGDUZJT0+nhv4xRa/X46VLl6iGbPLkydja2komEwV7GTM0hEhma/FBQHTW6upq6ocipLKwHbQCxGErXKjVapwzZw4jPRh6CGfPnuX1YXEFmxTai6H9v3zLw/Xf8u2VGaGVxSh8Q8LzPfnI2p4UbeRjFA8PD8zNzeU0GjqdDo8fP84ZtkuhUOC9e/fIpJyoVCrOITsAYEREhMVDXfR6PZaUlFA7E0ZZvXq12e8ErlbW+PCSkhLWiqHX67G8vByXLl3KSGcUoRVcKPY0Zn19fbhp0yZG3kZRKpV45swZ1oqj0WjwL3/5C7XrLTTKrK3GDC30nAEAf/e731FdARqNBrOysjg/AKFhwC35QrZs2YJtbW1kMtRqtZibm0stS1hYGKpUKjIZJ3oL0XON4uLigvHx8ZiXl4f19fXY1NSEKpUKCwoKMDk5mdfxg+Hh4YLLZ0RIOTdu3IgFBQWoUqmwqakJS0tLcffu3RbLKLR3bIqlOmaUadOm4f79+7GsrKz/HIUrV65gWlqaxYYAKL1QQI6uuqm4ublhdHQ0JiUlYVJSEkZHR6ObmxvjPlORy+W8x9zWYk9jhoauLpevwvg7IyMj+/+bGTNmcLae1lQWMYwZ8mjZgeWAFlrvx1SEDiGM8Kl7pqdFRUdHc57zaUvjack/I4awfYRC0Wq1uGvXLkbeYklmZiZr54UvYp1iRhNaY9AfaValUnGOn4WKj48PFhcXmz/NDtjbmKHBoG3cuJHxDGtELpdjXl6e4MoiljHT6/V45MgRToMgVPgenUajurraokHjI2I0nvb4f4ySkJCAGo2GfKRV2KOccrkcjxw5IrhusmHpeDhrZeXKldje3k4+DpE8nUmj0eAnn3zCyECorFixgtVy2oOBMGZoaLX37t1rU+Wx5TxRsYyZkdu3b/PqznOJh4cHnj9/XpTKz+WL4yNBQUFW/7dsVFZWWvTZ8BWlUonFxcWi/E8k5eXlopSTjy9LKHxPqeIjHh4eePz4cU4fPuMMAL1ejw8fPsT58+czMrQkERERePXqVbu8NBoDZcyMNDU14SeffGJxqGYqLi4umJGRYdF5zIXYxgwNPc59+/ZZ9KGQ4uLigrt378bOzk4yS5swOqaFlEcul+O2bdtELwsaylNcXGy1sVAqlXjixAmbeq18sKWcYWFh+P3333MaCVt5/PgxxsXFCfpmjOLr64tZWVm8erQMY2ZKS0sL5uTk4LJly1inhAMCAnDZsmWYk5NjcXHiUKO3txcLCwsxPj4ex44da/aiHBwccOzYsRgfH48//PCD3Suzreh0OqyoqMC0tDScOnUqenl5mb1nNzc3nDBhAiYnJ+OtW7fs/nt0Oh2WlJRgcnIyTpgwgdEbDggIwNjYWMzLy+NVycVArVZjXl4erlu3rv+AW9MyyeVyDAkJwXXr1uGxY8fe2vfQ0tKCx44dw9jYWAwMDGQYEF9fX4yOjsbMzEysq6sb0I5Hb28vFhUVYWpqKms9M75ba8snQ2v3j0hISEi8QzD2ZkpISEgMRiRjJiEhMST4P8vKxOR5j0deAAAAAElFTkSuQmCC\" width=\"101\" height=\"25\"></p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>", "<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"26\"></p>"],
                    solution_en: "<p>84.(d) <img src=\"data:image/png;base64,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\" width=\"160\" height=\"23\"><br>= 326 - 10 + 0.<img src=\"data:image/png;base64,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\" width=\"90\" height=\"19\"><br>= 316 + <math display=\"inline\"><mfrac><mrow><mn>786</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>193</mn><mo>-</mo><mn>19</mn></mrow><mn>900</mn></mfrac></math><br><math display=\"inline\"><mo>=</mo></math> 316 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>786</mn><mn>999</mn></mfrac><mo>-</mo><mfrac><mn>174</mn><mn>900</mn></mfrac></math><br>= <math display=\"inline\"><mn>316</mn><mo>+</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>78600</mn><mo>-</mo><mn>19314</mn></mrow><mn>99900</mn></mfrac></math><br><math display=\"inline\"><mo>=</mo><mn>316</mn><mo>+</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>59286</mn><mn>99900</mn></mfrac></math>= 316 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>59286</mn><mo>+</mo><mn>59</mn></mrow><mn>99900</mn></mfrac></math><br>= <math display=\"inline\"><mn>316</mn><mo>+</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>59345</mn><mn>99900</mn></mfrac></math>= <img src=\"data:image/png;base64,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\" width=\"88\" height=\"25\">&nbsp;</p>",
                    solution_hi: "<p>84.(d) <img src=\"data:image/png;base64,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\" width=\"160\" height=\"23\"><br>= 326 - 10 + 0.<img src=\"data:image/png;base64,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\" width=\"90\" height=\"19\"><br>= 316 + <math display=\"inline\"><mfrac><mrow><mn>786</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>193</mn><mo>-</mo><mn>19</mn></mrow><mn>900</mn></mfrac></math><br><math display=\"inline\"><mo>=</mo></math> 316 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>786</mn><mn>999</mn></mfrac><mo>-</mo><mfrac><mn>174</mn><mn>900</mn></mfrac></math><br>= <math display=\"inline\"><mn>316</mn><mo>+</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>78600</mn><mo>-</mo><mn>19314</mn></mrow><mn>99900</mn></mfrac></math><br><math display=\"inline\"><mo>=</mo><mn>316</mn><mo>+</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>59286</mn><mn>99900</mn></mfrac></math>= 316 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>59286</mn><mo>+</mo><mn>59</mn></mrow><mn>99900</mn></mfrac></math><br>= <math display=\"inline\"><mn>316</mn><mo>+</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>59345</mn><mn>99900</mn></mfrac></math>= <img src=\"data:image/png;base64,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\" width=\"88\" height=\"25\">&nbsp;</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. Since a sound wave is a disturbance, which moves in the medium by setting the neighbouring particles into motion, they are categorised in which wave category?</p>",
                    question_hi: "<p>85. चूंकि ध्वनि तरंग एक विक्षोभ है, जो माध्यम में मौजूद आसपास के कणों को गतिमान करके संचारित होती है, उन्हें किस तरंग श्रेणी में वर्गीकृत किया जाता है?</p>",
                    options_en: ["<p>Radio waves</p>", "<p>Electromagnetic waves</p>", 
                                "<p>Microwaves</p>", "<p>Mechanical waves</p>"],
                    options_hi: ["<p>रेडियो तरंगें</p>", "<p>विद्युतचुम्बकीय तरंगें</p>",
                                "<p>सूक्ष्म तरंगें (माइक्रोवेव)</p>", "<p>यांत्रिक तरंगें</p>"],
                    solution_en: "<p>85.(d) <strong>Mechanical waves</strong> - It is a disturbance in matter that transfers energy through the medium. Types of mechanical wave : Transverse waves - Particles of the medium vibrate up and down perpendicular to the direction of the wave. Longitudinal waves - Particles of the medium vibrate back and forth parallel to the direction of the wave.</p>",
                    solution_hi: "<p>85.(d) <strong>यांत्रिक तरंगें </strong>- यह पदार्थ में एक विक्षोभ है जिन्हे ऊर्जा स्थानांतरित करने के लिए एक माध्यम की आवश्यकता होती है। यांत्रिक तरंग के प्रकार - अनुप्रस्थ तरंगें - माध्यम के कण तरंग की दिशा के लंबवत ऊपर और नीचे कंपन करते हैं। अनुदैर्ध्य तरंगें - माध्यम के कण तरंग की दिशा के समानांतर आगे-पीछे कंपन करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>34</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>]</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>11</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>15</mn></mrow><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>20</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>]</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn></mrow></mfrac></math> is :</p>",
                    question_hi: "<p>86. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>34</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>]</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>11</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>15</mn></mrow><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>20</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>]</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>86.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>34</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>]</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>11</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>15</mn></mrow><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>20</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>]</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>34</mn><mo>.</mo><mn>45</mn><mo>+</mo><mn>11</mn><mo>.</mo><mn>75</mn><mo>)</mo><mo>(</mo><mn>34</mn><mo>.</mo><mn>45</mn><mo>-</mo><mn>11</mn><mo>.</mo><mn>75</mn><mo>)</mo><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>15</mn></mrow><mrow><mn>25</mn><mo>.</mo><mn>75</mn><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>+</mo><mn>20</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mo>.</mo><mn>2</mn><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>15</mn></mrow><mrow><mn>25</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>46</mn><mo>.</mo><mn>2</mn><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>86.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>34</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>]</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>11</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>15</mn></mrow><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>20</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>]</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>34</mn><mo>.</mo><mn>45</mn><mo>+</mo><mn>11</mn><mo>.</mo><mn>75</mn><mo>)</mo><mo>(</mo><mn>34</mn><mo>.</mo><mn>45</mn><mo>-</mo><mn>11</mn><mo>.</mo><mn>75</mn><mo>)</mo><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>15</mn></mrow><mrow><mn>25</mn><mo>.</mo><mn>75</mn><mo>(</mo><mn>25</mn><mo>.</mo><mn>75</mn><mo>+</mo><mn>20</mn><mo>.</mo><mn>45</mn><mo>)</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mo>.</mo><mn>2</mn><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>15</mn></mrow><mrow><mn>25</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>46</mn><mo>.</mo><mn>2</mn><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. Find the odd one. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010079875.png\" alt=\"rId35\" width=\"284\" height=\"84\"></p>",
                    question_hi: "<p>87. अलग को पहचानिए <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010080114.png\" alt=\"rId36\" width=\"272\" height=\"83\"></p>",
                    options_en: ["<p>Figure 3</p>", "<p>Figure 2</p>", 
                                "<p>Figure 4</p>", "<p>Figure 1</p>"],
                    options_hi: ["<p>चित्र 3</p>", "<p>चित्र 2</p>",
                                "<p>चित्र 4</p>", "<p>चित्र 1</p>"],
                    solution_en: "<p>87.(a) All are symmetric figure, but 3 is different.</p>",
                    solution_hi: "<p>87.(a) सभी सममित चित्र हैं, लेकिन 3 भिन्न है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. ____ does NOT contain a carbonyl group.</p>",
                    question_hi: "<p>88. _______ में कार्बोनिल समूह नहीं है।</p>",
                    options_en: ["<p>Aldehyde</p>", "<p>Carboxyl acid</p>", 
                                "<p>Ketone</p>", "<p>Ethanol</p>"],
                    options_hi: ["<p>एल्डिहाइड</p>", "<p>कार्बोक्सिल अम्ल</p>",
                                "<p>कीटोन</p>", "<p>एथेनॉल</p>"],
                    solution_en: "<p>88.(d) <strong>Ethanol</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CH</mi><mn>3</mn></msub><msub><mi>CH</mi><mn>2</mn></msub><mi>OH</mi></math>) - It belongs to a functional group called Alcohol (-OH). Other members of Alcohol: Methanol (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CH</mi><mn>3</mn></msub><mi>OH</mi></math>), Propenol (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>7</mn></msub><mi>OH</mi></math>), Butenol (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>9</mn></msub><mi>OH</mi></math>). Carbonyl group - It is a functional group in organic chemistry, having a double bond between a carbon atom and an oxygen atom (-C=O). Its primary members are Aldehydes (R-CHO), Ketones (R-C(O)-R&rsquo;), and Carboxylic acids (-COOH).</p>",
                    solution_hi: "<p>88.(d) <strong>इथेनॉल</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CH</mi><mn>3</mn></msub><msub><mi>CH</mi><mn>2</mn></msub><mi>OH</mi></math>) - यह ऐल्कोहॉल (-OH) नामक कार्यात्मक समूह से संबंधित है। ऐल्कोहॉल के अन्य सदस्य: मेथनॉल (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CH</mi><mn>3</mn></msub><mi>OH</mi></math>), प्रोपेनॉल (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>7</mn></msub><mi>OH</mi></math>), ब्यूटेनॉल (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>9</mn></msub><mi>OH</mi></math>)। कार्बोनिल समूह - यह कार्बनिक रसायन विज्ञान में एक कार्यात्मक समूह है, जिसमें कार्बन परमाणु और ऑक्सीजन परमाणु (-C=O) के बीच द्विबंध होता है। इसके प्राथमिक सदस्य एल्डिहाइड (R-CHO), केटोन्स (R-C(O)-R\'), और कार्बोक्जिलिक अम्ल (-COOH) हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. Find the least six-digit number that is exactly divisible by 8, 10, 12 and 16.</p>",
                    question_hi: "<p>89. छह अंकों की वह छोटी से छोटी संख्या ज्ञात कीजिए, जो 8, 10, 12 और 16 से पूर्णतः विभाज्य हो</p>",
                    options_en: ["<p>100040</p>", "<p>100020</p>", 
                                "<p>100060</p>", "<p>100080</p>"],
                    options_hi: ["<p>100040</p>", "<p>100020</p>",
                                "<p>100060</p>", "<p>100080</p>"],
                    solution_en: "<p>89.(d) <br>Smallest 6-digit number = 100000<br>L.C.M of 8, 10, 12, 16 = 240<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010080342.png\" alt=\"rId37\" width=\"134\" height=\"138\"><br>240 &ndash; 160 = 80<br>Required number = 100000 + 80 <br>= 100080</p>",
                    solution_hi: "<p>89.(d)<br>6 अंकों की सबसे छोटी संख्या = 100000<br>8, 10, 12, 16 का लघुत्तम समापवर्त्य = 240<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010080342.png\" alt=\"rId37\" width=\"134\" height=\"138\"><br>240 &ndash; 160 = 80<br>आवश्यक संख्या = 100000 + 80 = 100080</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. Which of the following has the largest atomic radius?</p>",
                    question_hi: "<p>90. निम्नलिखित में से किसकी परमाणु त्रिज्या सबसे बड़ी है?</p>",
                    options_en: ["<p>Iodine</p>", "<p>Fluorine</p>", 
                                "<p>Chlorine</p>", "<p>Oxygen</p>"],
                    options_hi: ["<p>आयोडीन</p>", "<p>फ्लोरीन</p>",
                                "<p>क्लोरीन</p>", "<p>ऑक्सीजन</p>"],
                    solution_en: "<p>90.(a) <strong>Iodine. Fluorine &lt; Oxygen &lt; Chlorine &lt; Iodine.</strong> The atomic radius increases down the group because new shells are being added as we go down the group. The atomic radius is defined as the shortest distance between the atom\'s nuclei and the outermost shell of the atom.</p>",
                    solution_hi: "<p>90.(a)<strong> आयोडीन । फ्लोरीन &lt; ऑक्सीजन &lt; क्लोरीन &lt; आयोडीन ।</strong> समूह में नीचे जाने पर परमाणु त्रिज्या बढ़ती है क्योंकि समूह में नीचे जाने पर नए कोश जुड़ते जाते हैं । परमाणु त्रिज्या (Atomic Radius) को परमाणु के नाभिक और परमाणु के सबसे बाहरी खोल के बीच की सबसे छोटी दूरी के रूप में परिभाषित किया गया है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. M1, M2, M3, M4 and M5 are five men sitting in a line facing towards the South. L1, L2, L3, L4 and L5 are five women sitting in a second line, parallel to the first line, facing towards the north. M2, who is to the immediate left of M4, is opposite to L5. M3 and L2 are diagonally opposite each other M5 is opposite L3, who is to the immediate right of L1 ,L4, who is to the immediate right of L3, is opposite M4. L1 is at one end of the line. <br>Which of the following pairs are diagonally opposite each other?</p>",
                    question_hi: "<p>91. M1, M2, M3, M4 और M5 पांच व्यक्ति एक पंक्ति में दक्षिण की ओर मुख करके बैठे हैं। L1, L2, L3, L4 और L5 पांच महिलाएं हैं, जो पहली पंक्ति के समानांतर दूसरी पंक्ति में उत्तर की ओर मुख करके बैठी हैं। M2, जो M4 के ठीक बाईं ओर है, L5 के विपरीत है। M3 और L2 एक दूसरे के विकर्णतः सामने हैं। M5, L3 के सामने है, जो L1 के ठीक दायें है। L4, जो L3 के ठीक दायें है, M4 के सामने है , L1 पंक्ति के एक सिरे पर है। <br>निम्नलिखित में से कौन-सा युग्म एक दूसरे के विकर्णत: सम्मुख है?</p>",
                    options_en: ["<p>M5 and L3</p>", "<p>M1 and L2</p>", 
                                "<p>M3 and L1</p>", "<p>M1 and L1</p>"],
                    options_hi: ["<p>M5 और L3</p>", "<p>M1 और L2</p>",
                                "<p>M3 और L1</p>", "<p>M1 और L1</p>"],
                    solution_en: "<p>91.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010080499.png\" alt=\"rId38\" width=\"154\" height=\"94\"><br>From the above arrangement, we can see that M1 and L1 are sitting diagonally opposite each other.</p>",
                    solution_hi: "<p>91.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739010080499.png\" alt=\"rId38\" width=\"154\" height=\"94\"><br>उपरोक्त व्यवस्था से, हम देख सकते हैं कि M1 और L1 एक दूसरे के विकर्णतः सामने बैठे हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. Which of the following is the chemical reaction involved in the baking soda preparation?</p>",
                    question_hi: "<p>92. निम्नलिखित में से किस रासायनिक अभिक्रिया के द्वारा बेकिंग सोडा का निर्माण किया जाता है?</p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>NaHCO</mi><mn>3</mn></msub><mo>+</mo><msup><mi mathvariant=\"normal\">H</mi><mo>+</mo></msup><mo>&#8594;</mo><msub><mi>CO</mi><mn>2</mn></msub><mo>+</mo><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math> + Sodium salt of acid</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mn>2</mn><mi>NaCl</mi><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi><mo>(</mo><mi mathvariant=\"normal\">l</mi><mo>)</mo><mo>&#8594;</mo><mn>2</mn><mi>NaOH</mi><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><msub><mi>Cl</mi><mn>2</mn></msub><mo>(</mo><mi mathvariant=\"normal\">g</mi><mo>)</mo><mo>+</mo><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mo>(</mo><mi mathvariant=\"normal\">g</mi><mo>)</mo></math></p>", 
                                "<p>NaCl+<math display=\"inline\"><msub><mrow><mi>H</mi></mrow><mrow><mn>2</mn></mrow></msub></math>O+<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CO</mi><mn>2</mn></msub></math>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>NH</mi><mn>3</mn></msub></math>&rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mo>&#160;</mo><msub><mi>NH</mi><mn>4</mn></msub></math>Cl+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>NaHCO</mi><mn>3</mn></msub></math></p>", "<p>Ca<math display=\"inline\"><msub><mrow><mo>(</mo><mi>O</mi><mi>H</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msub></math> + <math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Cl</mi><mn>2</mn></msub></math>&rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CaOCl</mi><mn>2</mn></msub><mo>&#160;</mo></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>NaHCO</mi><mn>3</mn></msub><mo>+</mo><msup><mi mathvariant=\"normal\">H</mi><mo>+</mo></msup><mo>&#8594;</mo><msub><mi>CO</mi><mn>2</mn></msub><mo>+</mo><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math>अम्ल का सोडियम लवण</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mn>2</mn><mi>NaCl</mi><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi><mo>(</mo><mi mathvariant=\"normal\">l</mi><mo>)</mo><mo>&#8594;</mo><mn>2</mn><mi>NaOH</mi><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><msub><mi>Cl</mi><mn>2</mn></msub><mo>(</mo><mi mathvariant=\"normal\">g</mi><mo>)</mo><mo>+</mo><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mo>(</mo><mi mathvariant=\"normal\">g</mi><mo>)</mo></math></p>",
                                "<p>NaCl+<math display=\"inline\"><msub><mrow><mi>H</mi></mrow><mrow><mn>2</mn></mrow></msub></math>O+<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CO</mi><mn>2</mn></msub></math>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>NH</mi><mn>3</mn></msub></math>&rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mo>&#160;</mo><msub><mi>NH</mi><mn>4</mn></msub></math>Cl+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>NaHCO</mi><mn>3</mn></msub></math></p>", "<p>Ca<math display=\"inline\"><msub><mrow><mo>(</mo><mi>O</mi><mi>H</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msub></math> + <math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Cl</mi><mn>2</mn></msub></math>&rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CaOCl</mi><mn>2</mn></msub><mo>&#160;</mo></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math></p>"],
                    solution_en: "<p>92.(c) Baking soda is also known as Sodium bicarbonate. It is basic in nature. Reduces the acidity in the stomach. Used in the process of washing as a water softener.</p>",
                    solution_hi: "<p>92.(c) बेकिंग सोडा को सोडियम बाइकार्बोनेट के नाम से भी जाना जाता है। I इसकी प्रकृति क्षारीय है। पेट में अम्लता को कम करता है। पानी मृदुकारी के रूप में धोने की प्रक्रिया में उपयोग किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. Find three numbers such that their ratio is 3 : 4 : 5 and their HCF is 7.</p>",
                    question_hi: "<p>93. ऐसी तीन संख्याएँ ज्ञात कीजिए जिनका अनुपात 3 : 4 : 5 हो और उनका म. स. 7 हो ?</p>",
                    options_en: ["<p>12; 16; 20</p>", "<p>6; 8 ; 10</p>", 
                                "<p>21; 28; 35</p>", "<p>24; 32; 40</p>"],
                    options_hi: ["<p>12; 16; 20</p>", "<p>6; 8 ; 10</p>",
                                "<p>21; 28; 35</p>", "<p>24; 32; 40</p>"],
                    solution_en: "<p>93.(c)<br>1st Number = 3 &times; 7 = 21<br>2nd Number = 4 &times; 7 = 28<br>3rd Number = 5 &times; 7 = 35</p>",
                    solution_hi: "<p>93.(c)</p>\n<p>पहली संख्या = 3 &times; 7 = 21<br>दूसरी संख्या = 4 &times; 7 = 28<br>तीसरी संख्या = 5 &times; 7 = 35</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. Which of the following is a balanced food in itself?</p>",
                    question_hi: "<p>94. निम्न में से कौन सा अपने आप में एक संतुलित आहार है?</p>",
                    options_en: ["<p>Pulses</p>", "<p>Milk</p>", 
                                "<p>Meat</p>", "<p>Honey</p>"],
                    options_hi: ["<p>दालें</p>", "<p>दूध</p>",
                                "<p>मांस</p>", "<p>शहद</p>"],
                    solution_en: "<p>94.(b) <strong>Milk. Nutrients in Milk</strong> - Calcium, Riboflavin, Phosphorus, Vitamins A and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>B</mi><mn>12</mn></msub></math>, Potassium, Magnesium, Zinc, Iodine, lactic acid. Balanced diet contains all of the essential elements that the human body needs. The accepted average calorie requirement in India is 2400 calories per person per day in rural areas and 2100 calories per person per day in urban areas.</p>",
                    solution_hi: "<p>94.(b) <strong>दूध। दूध में पोषक तत्व</strong> - कैल्शियम, राइबोफ्लेविन, फास्फोरस, विटामिन A और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>B</mi><mn>12</mn></msub></math>, पोटेशियम, मैग्नीशियम, जिंक, आयोडीन, लैक्टिक अम्ल। संतुलित आहार में वे सभी आवश्यक तत्व होते हैं जिनकी मानव शरीर को आवश्यकता होती है। भारत में स्वीकृत औसत कैलोरी आवश्यकता ग्रामीण क्षेत्रों में प्रति व्यक्ति प्रति दिन 2400 कैलोरी और शहरी क्षेत्रों में प्रति व्यक्ति प्रति दिन 2100 कैलोरी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. In the given letter-cluster pairs, the first letter-cluster is related to the second letter-cluster following a certain logic. Study the given pairs carefully, and from the given options, select the pair that follows the same logic.<br>MCD : PZG<br>LKG : OHJ</p>",
                    question_hi: "<p>95. दिए गए अक्षर-समूह युग्म में, पहला अक्षर-समूह एक निश्चित तर्क के आधार पर दूसरे अक्षर-समूह से संबंधित है। दिए गए युग्मों का ध्यानपूर्वक अध्ययन कीजिए, और दिए गए विकल्पों में से उस युग्म का चयन करें, जो समान तर्क का पालन करता है।<br>MCD : PZG<br>LKG : OHJ</p>",
                    options_en: ["<p>NYB : QVY</p>", "<p>TJQ : WGT</p>", 
                                "<p>PBX : SEA</p>", "<p>QDH : TGK</p>"],
                    options_hi: ["<p>NYB : QVY</p>", "<p>TJQ : WGT</p>",
                                "<p>PBX : SEA</p>", "<p>QDH : TGK</p>"],
                    solution_en: "<p>95.(b)<br>In MCD : PZG <math display=\"inline\"><mo>&#8658;</mo></math> M + 3 = P , C - 3 = Z , <br>D + 3 = G<br>In, LKG : OHJ <math display=\"inline\"><mo>&#8658;</mo></math> L + 3 = O , K - 3 = H ,<br>G + 3 = J<br>Similarly, <br>In TJQ : WGT <math display=\"inline\"><mo>&#8658;</mo></math> T + 3 = W , J - 3 = G ,<br>Q + 3 = T</p>",
                    solution_hi: "<p>95.(b) MCD : PZG <math display=\"inline\"><mo>&#8658;</mo></math> M + 3 = P , <br>C - 3 = Z , D + 3 = G<br>LKG : OHJ <math display=\"inline\"><mo>&#8658;</mo></math> L + 3 = O , K - 3 = H , <br>G + 3 = J<br>इसी प्रकार,<br>TJQ : WGT <math display=\"inline\"><mo>&#8658;</mo></math> T + 3 = W , J - 3 = G , <br>Q + 3 = T</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. Which of the following is NOT an allotrope of carbon?</p>",
                    question_hi: "<p>96. निम्नलिखित में से कौन कार्बन का अपरूप नहीं है?</p>",
                    options_en: ["<p>Diamond</p>", "<p>Fullerenes</p>", 
                                "<p>Graphite</p>", "<p>Carbon dioxide</p>"],
                    options_hi: ["<p>हीरा</p>", "<p>फुलरीन</p>",
                                "<p>ग्रैफाइट</p>", "<p>कार्बन डाइऑक्साइड</p>"],
                    solution_en: "<p>96.(d) <strong>Carbon dioxide</strong>. Allotropes - The different forms of the same element. Allotropes of carbon are diamond, graphite, graphene, carbon nanotubes, fullerenes, and carbon nanobuds. Carbon - It is nonmetallic and tetravalent (its atom making four electrons available to form covalent chemical bonds), discovered by Antoine Lavoisier in 1789. Fullerenes (C-60 Buckminsterfullerene) was discovered by Richard Smalley in 1985.</p>",
                    solution_hi: "<p>96.(d) <strong>कार्बन डाइऑक्साइड।</strong> एलोट्रोप्स - एक ही तत्व के विभिन्न रूप। कार्बन के अपररूप हीरा, ग्रेफाइट, ग्रेफीन, कार्बन नैनोट्यूब, फुलरीन और कार्बन नैनोबड हैं। कार्बन - यह अधात्विक और चतुष्संयोजक है (इसका परमाणु चार इलेक्ट्रॉनों को सहसंयोजक रासायनिक बंध बनाने के लिए उपलब्ध कराता है), इसकी खोज 1789 में एंटोनी लावोइसियर ने की थी। फुलरीन (C-60 बकमिनस्टरफुलरीन) की खोज रिचर्ड स्माले ने 1985 में की थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. If the angle of a sector in a pie diagram is <math display=\"inline\"><msup><mrow><mn>135</mn></mrow><mrow><mi>o</mi></mrow></msup><mo>,</mo><mi>&#160;</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>n</mi><mi>&#160;</mi></math> it is equivalent to _________ of the pie diagram.</p>",
                    question_hi: "<p>97. यदि एक पाई आरेख में किसी वृत्तखंड का कोण 135&deg; है, तो यह पाई आरेख के ___________ के बराबर है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>97.(a) <math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac></math></p>",
                    solution_hi: "<p>97.(a) <math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. Which Indian River that starts from Gangotri is polluted largely by human activities and industries?</p>",
                    question_hi: "<p>98. गंगोत्री से निकलने वाली कौन सी भारतीय नदी मानवीय गतिविधियों और उद्योगों से प्रदूषित हुई है?</p>",
                    options_en: ["<p>Krishna</p>", "<p>Brahmaputra</p>", 
                                "<p>Cauvery</p>", "<p>Ganga</p>"],
                    options_hi: ["<p>कृष्णा</p>", "<p>ब्रह्मपुत्र</p>",
                                "<p>कावेरी</p>", "<p>गंगा</p>"],
                    solution_en: "<p>98.(d) <strong>Ganga,</strong> originates as Bhagirathi - Gangotri glacier in district UttarKashi, Uttarakhand at an elevation of 7,010 m. Alaknanda joins Bhagirathi at Devprayag. From Devapryag the river is called Ganga. Tributaries - Yamuna, Ramganga, Gomati, Ghaghra. Brahamputra - origin - Chemayungdung glacier (Tibet), known as Jamuna in Bangladesh. Tributaries - Dhansiri, Subansiri, Lohit, Dibang. Krishna river is Peninsular India\'s second largest river. The Cauvery River is also known as the &ldquo;Ganga of South India\'\'.</p>",
                    solution_hi: "<p>98.(d) <strong>गंगा,</strong> उत्तराखंड के उत्तरकाशी जिले में 7,010 मीटर की ऊंचाई पर भागीरथी - गंगोत्री ग्लेशियर के रूप में निकलती है। अलकनंदा नदी देवप्रयाग में भागीरथी से मिलती है। देवप्रयाग से निकलने पर इस नदी को गंगा कहा जाता है। सहायक नदियाँ - यमुना, रामगंगा, गोमती, घाघरा। ब्रह्मपुत्र - उत्पत्ति - चेमायुंगडुंग ग्लेशियर (तिब्बत) ,जिसे बांग्लादेश में जमुना के नाम से जाना जाता है। सहायक नदियाँ - धनसिरी, सुबनसिरी, लोहित, दिबांग। कृष्णा नदी प्रायद्वीपीय भारत की दूसरी सबसे बड़ी नदी है। कावेरी नदी को \"दक्षिण भारत की गंगा\" भी कहा जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. ABCD is a parallelogram in which &ang;A = x + <math display=\"inline\"><msup><mrow><mn>20</mn></mrow><mrow><mi>o</mi></mrow></msup></math> and &ang;C = 3x -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mi>o</mi></msup></math>.The value of x is _________</p>",
                    question_hi: "<p>99. ABCD एक समांतर चतुर्भुज है जिसमें &ang;A = x + <math display=\"inline\"><msup><mrow><mn>20</mn></mrow><mrow><mi>o</mi></mrow></msup></math> और &ang;C = 3x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mi>o</mi></msup></math>. है। x का मान __________ है।</p>",
                    options_en: ["<p><math display=\"inline\"><msup><mrow><mn>15</mn></mrow><mrow><mi>o</mi></mrow></msup></math></p>", "<p><math display=\"inline\"><msup><mrow><mn>30</mn></mrow><mrow><mi>o</mi></mrow></msup></math></p>", 
                                "<p><math display=\"inline\"><msup><mrow><mn>40</mn></mrow><mrow><mi>o</mi></mrow></msup></math></p>", "<p><math display=\"inline\"><msup><mrow><mn>60</mn></mrow><mrow><mi>o</mi></mrow></msup></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msup><mrow><mn>15</mn></mrow><mrow><mi>o</mi></mrow></msup></math></p>", "<p><math display=\"inline\"><msup><mrow><mn>30</mn></mrow><mrow><mi>o</mi></mrow></msup></math></p>",
                                "<p><math display=\"inline\"><msup><mrow><mn>40</mn></mrow><mrow><mi>o</mi></mrow></msup></math></p>", "<p><math display=\"inline\"><msup><mrow><mn>60</mn></mrow><mrow><mi>o</mi></mrow></msup></math></p>"],
                    solution_en: "<p>99.(a)<br>Angle A will be equal to the angle C , both are opposite angles of a parallelogram <br>x + 20&deg; = 3x - 10&deg; &rArr; 2x = 30&deg; &rArr; x = 15&deg;</p>",
                    solution_hi: "<p>99.(a) कोण A कोण C के बराबर होगा, दोनों समांतर चतुर्भुज के विपरीत कोण हैं,<br>x + 20&deg; = 3x - 10&deg;<br>2x = 30&deg; <math display=\"inline\"><mo>&#8658;</mo></math> x = 15&deg;</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Which country was declared malaria-free by the WHO in January 2025?</p>",
                    question_hi: "<p>100. जनवरी 2025 में WHO ने किस देश को मलेरिया मुक्त घोषित किया है?</p>",
                    options_en: ["<p>India</p>", "<p>Georgia</p>", 
                                "<p>Sri Lanka</p>", "<p>Brazil</p>"],
                    options_hi: ["<p>भारत</p>", "<p>जॉर्जिया</p>",
                                "<p>श्रीलंका</p>", "<p>ब्राज़ील</p>"],
                    solution_en: "<p>100.(b) <strong>Georgia</strong> has become the 45th country and the first in the WHO European Region to attain malaria-free status. To achieve certification, a country must prove that the chain of indigenous transmission has been interrupted for at least three consecutive years. Georgia: Capital - Tbilisi.</p>",
                    solution_hi: "<p>100.(b) <strong>जॉर्जिया</strong> मलेरिया मुक्त दर्जा प्राप्त करने वाला 45वाँ देश और WHO यूरोपीय क्षेत्र का पहला देश बन गया है। प्रमाणन प्राप्त करने के लिए, किसी देश को यह साबित करना होगा कि स्वदेशी संचरण की श्रृंखला कम से कम लगातार तीन वर्षों तक बाधित रही है। जॉर्जिया: राजधानी - त्बिलिसी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>