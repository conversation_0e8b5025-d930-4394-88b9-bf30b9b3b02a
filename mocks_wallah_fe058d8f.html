<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate ANTONYM of the given word.<br>Lethargic</p>",
                    question_hi: "<p>1. Select the most appropriate ANTONYM of the given word.<br>Lethargic</p>",
                    options_en: ["<p>Active</p>", "<p>Languid</p>", 
                                "<p>Moderate</p>", "<p>Angry</p>"],
                    options_hi: ["<p>Active</p>", "<p>Languid</p>",
                                "<p>Moderate</p>", "<p>Angry</p>"],
                    solution_en: "<p>1.(a) <strong>Active</strong>- engaging or ready to engage in physical or mental activity.<br><strong>Lethargic</strong>- feeling unwilling and unable to do anything.<br><strong>Languid</strong>- weak or faint from illness or fatigue.<br><strong>Moderate</strong>- average in amount, intensity, or degree.</p>",
                    solution_hi: "<p>1.(a) <strong>Active </strong>(सक्रिय)- engaging or ready to engage in physical or mental activity.<br><strong>Lethargic </strong>(सुस्त)- feeling unwilling and unable to do anything.<br><strong>Languid </strong>(कमजोर)- weak or faint from illness or fatigue.<br><strong>Moderate </strong>(औसत दर्जे का) - average in amount, intensity, or degree.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate ANTONYM of the underlined word in the following sentence. <br>It was difficult to accommodate the <span style=\"text-decoration: underline;\">avariciousness </span>of the people.</p>",
                    question_hi: "<p>2. Select the most appropriate ANTONYM of the underlined word in the following sentence. <br>It was difficult to accommodate the <span style=\"text-decoration: underline;\">avariciousness </span>of the people.</p>",
                    options_en: ["<p>Generosity</p>", "<p>Criticism</p>", 
                                "<p>Hatred</p>", "<p>Envy</p>"],
                    options_hi: ["<p>Generosity</p>", "<p>Criticism</p>",
                                "<p>Hatred</p>", "<p>Envy</p>"],
                    solution_en: "<p>2.(a) <strong>Generosity</strong>- the quality of being kind and giving.<br><strong>Avariciousness</strong>- extreme greed for wealth or material gain.<br><strong>Criticism</strong>- the expression of disapproval based on perceived faults.<br><strong>Hatred</strong>- intense dislike.<br><strong>Envy</strong>- desire to have a quality, possession, or other desirable thing belonging to (someone else).</p>",
                    solution_hi: "<p>2.(a) <strong>Generosity </strong>(उदारता/दानशीलता) - the quality of being kind and giving.<br><strong>Avariciousness </strong>(लोभी/लोलुपता) - extreme greed for wealth or material gain.<br><strong>Criticism </strong>(आलोचना)- the expression of disapproval based on perceived faults.<br><strong>Hatred </strong>(घृणा)- intense dislike.<br><strong>Envy </strong>(ईर्ष्या)- desire to have a quality, possession, or other desirable thing belonging to (someone else).</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate ANTONYM of the given word from the following sentence. <br>Barren <br>\"Can you believe how much traffic there is today?\", grumbled Tom, glancing at the congested road ahead.</p>",
                    question_hi: "<p>3. Select the most appropriate ANTONYM of the given word from the following sentence. <br>Barren <br>\"Can you believe how much traffic there is today?\", grumbled Tom, glancing at the congested road ahead.</p>",
                    options_en: ["<p>Believe</p>", "<p>Grumbled</p>", 
                                "<p>Glancing</p>", "<p>Congested</p>"],
                    options_hi: ["<p>Believe</p>", "<p>Grumbled</p>",
                                "<p>Glancing</p>", "<p>Congested</p>"],
                    solution_en: "<p>3.(d) <strong>Congested</strong>- overcrowded or blocked, especially with traffic or people.<br><strong>Barren</strong>- bleak and lifeless..<br><strong>Believe</strong>- to accept something as true or real.<br><strong>Grumbled</strong>- complained about something in a bad-tempered way.<br><strong>Glancing</strong>- taking a brief or quick look.</p>",
                    solution_hi: "<p>3.(d) <strong>Congested </strong>(भीड़भाड़)- overcrowded or blocked, especially with traffic or people.<br><strong>Barren </strong>(बंजर)- bleak and lifeless..<br><strong>Believe </strong>(विश्वास करना)- to accept something as true or real.<br><strong>Grumbled </strong>(शिकायत की)- complained about something in a bad-tempered way.<br><strong>Glancing </strong>(नज़र डालना)- taking a brief or quick look.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. Select the most appropriate ANTONYM of the given word. <br />Hostile ",
                    question_hi: "4. Select the most appropriate ANTONYM of the given word. <br />Hostile ",
                    options_en: [" Incoherent ", " Doubtful ", 
                                " Eager ", " Friendly"],
                    options_hi: [" Incoherent ", " Doubtful ",
                                " Eager ", " Friendly"],
                    solution_en: "<p>4.(d) <strong>Friendly</strong>- kind and pleasant in behavior towards others.<br><strong>Hostile</strong>- showing or feeling opposition or dislike; unfriendly.<br><strong>Incoherent</strong>- unclear or difficult to understand.<br><strong>Doubtful</strong>- feeling uncertain or lacking confidence.<br><strong>Eager</strong>- keen or enthusiastic about something.</p>",
                    solution_hi: "<p>4.(d) <strong>Friendly </strong>(मैत्रीपूर्ण/मिलनसार)- kind and pleasant in behavior towards others.<br><strong>Hostile </strong>(शत्रुतापूर्ण)- showing or feeling opposition or dislike; unfriendly.<br><strong>Incoherent </strong>(असंगत)- unclear or difficult to understand.<br><strong>Doubtful </strong>(संदेहास्पद)- feeling uncertain or lacking confidence.<br><strong>Eager </strong>(उत्सुक)- keen or enthusiastic about something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "5. Select the most appropriate ANTONYM of the given word.<br />Foment",
                    question_hi: "5. Select the most appropriate ANTONYM of the given word.<br />Foment",
                    options_en: [" Proximity ", " Remoteness ", 
                                " Separation ", " Regulate"],
                    options_hi: [" Proximity ", " Remoteness ",
                                " Separation ", " Regulate"],
                    solution_en: "<p>5.(d) <strong>Regulate</strong>- to control or maintain something according to rules or laws.<br><strong>Foment</strong>- to stir up trouble or rebellion.<br><strong>Proximity</strong>- the state of being near in space or time.<br><strong>Remoteness</strong>- the state of being far away in distance or time.<br><strong>Separation</strong>- the act of moving or being apart from something or someone.</p>",
                    solution_hi: "<p>5.(d) <strong>Regulate </strong>(संयमित रखना)- to control or maintain something according to rules or laws.<br><strong>Foment </strong>(भड़काना)- to stir up trouble or rebellion.<br><strong>Proximity </strong>(समीपता)- the state of being near in space or time.<br><strong>Remoteness </strong>(सुदूरता)- the state of being far away in distance or time.<br><strong>Separation </strong>(पृथक्करण)- the act of moving or being apart from something or someone.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate ANTONYM of the given word.<br>Intelligible</p>",
                    question_hi: "<p>6. Select the most appropriate ANTONYM of the given word.<br>Intelligible</p>",
                    options_en: ["<p>Scriptable</p>", "<p>Embezzle</p>", 
                                "<p>Incoherent</p>", "<p>Deliberate</p>"],
                    options_hi: ["<p>Scriptable</p>", "<p>Embezzle</p>",
                                "<p>Incoherent</p>", "<p>Deliberate</p>"],
                    solution_en: "<p>6.(c) <strong>Incoherent</strong>- unclear or difficult to understand.<br><strong>Intelligible</strong>- able to be understood.<br><strong>Scriptable</strong>- able to be written or expressed in a script or code.<br><strong>Embezzle</strong>- to secretly take money that belongs to an organization or business you work for.<br><strong>Deliberate</strong>- done consciously and intentionally.</p>",
                    solution_hi: "<p>6.(c) <strong>Incoherent </strong>(असंगत)- unclear or difficult to understand.<br><strong>Intelligible </strong>(बोधगम्य)- able to be understood.<br><strong>Scriptable </strong>(लिपिबद्ध करने योग्य)- able to be written or expressed in a script or code.<br><strong>Embezzle </strong>(गबन करना)- to secretly take money that belongs to an organization or business you work for.<br><strong>Deliberate </strong>(सोचा-समझा)- done consciously and intentionally.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate ANTONYM of the underlined word to fill in the blank. <br>Ram <span style=\"text-decoration: underline;\">failed </span>initially but after a few years, he _________ in every field.</p>",
                    question_hi: "<p>7. Select the most appropriate ANTONYM of the underlined word to fill in the blank. <br>Ram <span style=\"text-decoration: underline;\">failed </span>initially but after a few years, he _________ in every field.</p>",
                    options_en: ["<p>elevated</p>", "<p>succeeded</p>", 
                                "<p>enjoyed</p>", "<p>reserved</p>"],
                    options_hi: ["<p>elevated</p>", "<p>succeeded</p>",
                                "<p>enjoyed</p>", "<p>reserved</p>"],
                    solution_en: "<p>7.(b) <strong>Succeeded</strong>- achieved the desired aim or result.<br><strong>Failed</strong>- did not succeed in achieving a desired result.<br><strong>Elevated</strong>- raised to a higher position or level.<br><strong>Enjoyed</strong>- took pleasure or satisfaction in something.<br><strong>Reserved</strong>- kept something for a particular purpose or person.</p>",
                    solution_hi: "<p>7.(b) <strong>Succeeded </strong>(सफल/कामयाब होना)- achieved the desired aim or result.<br><strong>Failed </strong>(असफल होना)- did not succeed in achieving a desired result.<br><strong>Elevated </strong>(पदोन्नत होना)- raised to a higher position or level.<br><strong>Enjoyed </strong>(आनंदित होना)- took pleasure or satisfaction in something.<br><strong>Reserved </strong>(सुरक्षित)- kept something for a particular purpose or person.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate ANTONYM of the given word. <br>Laudable</p>",
                    question_hi: "<p>8. Select the most appropriate ANTONYM of the given word. <br>Laudable</p>",
                    options_en: ["<p>Commendable</p>", "<p>Severe</p>", 
                                "<p>Deplorable</p>", "<p>Humorous</p>"],
                    options_hi: ["<p>Commendable</p>", "<p>Severe</p>",
                                "<p>Deplorable</p>", "<p>Humorous</p>"],
                    solution_en: "<p>8.(c) <strong>Deplorable</strong>- shockingly bad.<br><strong>Laudable</strong>- deserving praise and admiration.<br><strong>Commendable</strong>- worthy of praise or approval.<br><strong>Severe</strong>- very intense or harsh in degree or manner.<br><strong>Humorous</strong>- causing laughter or amusement.</p>",
                    solution_hi: "<p>8.(c) <strong>Deplorable </strong>(निंदनीय) - shockingly bad.<br><strong>Laudable </strong>(प्रशंसनीय) - deserving praise and admiration.<br><strong>Commendable </strong>(सराहनीय) - worthy of praise or approval.<br><strong>Severe </strong>(कठोर/गम्भीर )- very intense or harsh in degree or manner.<br><strong>Humorous </strong>(हास्यपूर्ण/मज़ाकिया) - causing laughter or amusement.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate ANTONYM of the given word.<br>Dissolve</p>",
                    question_hi: "<p>9. Select the most appropriate ANTONYM of the given word.<br>Dissolve</p>",
                    options_en: ["<p>Vanish</p>", "<p>Fade</p>", 
                                "<p>Solve</p>", "<p>Appear</p>"],
                    options_hi: ["<p>Vanish</p>", "<p>Fade</p>",
                                "<p>Solve</p>", "<p>Appear</p>"],
                    solution_en: "<p>9.(d) <strong>Appear</strong>- to become visible or come into view.<br><strong>Dissolve</strong>- to be absorbed by a liquid.<br><strong>Vanish</strong>- to disappear suddenly and completely.<br><strong>Fade</strong>- to gradually lose brightness or strength.<br><strong>Solve</strong>- to find an answer or solution to a problem or question.</p>",
                    solution_hi: "<p>9.(d) <strong>Appear </strong>(प्रकट होना)- to become visible or come into view.<br><strong>Dissolve </strong>(घुल जाना)- to be absorbed by a liquid.<br><strong>Vanish </strong>(गायब होना)- to disappear suddenly and completely.<br><strong>Fade </strong>(धुंधला होना)- to gradually lose brightness or strength.<br><strong>Solve </strong>(समाधान करना)- to find an answer or solution to a problem or question.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate ANTONYM of the given word.<br>Nominal</p>",
                    question_hi: "<p>10. Select the most appropriate ANTONYM of the given word.<br>Nominal</p>",
                    options_en: ["<p>Nasty</p>", "<p>Sound</p>", 
                                "<p>Constant</p>", "<p>Significant</p>"],
                    options_hi: ["<p>Nasty</p>", "<p>Sound</p>",
                                "<p>Constant</p>", "<p>Significant</p>"],
                    solution_en: "<p>10.(d) <strong>Significant</strong>- sufficiently great or important to be worthy of attention.<br><strong>Nominal</strong>- existing in name only.<br><strong>Nasty</strong>- very unpleasant, offensive, or harmful.<br><strong>Sound</strong>- in good condition.<br><strong>Constant</strong>- occurring continuously or repeatedly.</p>",
                    solution_hi: "<p>10.(d) <strong>Significant </strong>(महत्त्वपूर्ण)- sufficiently great or important to be worthy of attention.<br><strong>Nominal </strong>(नाममात्र का)- existing in name only.<br><strong>Nasty </strong>(घृणित)- very unpleasant, offensive, or harmful.<br><strong>Sound </strong>(अच्छाखासा)- in good condition.<br><strong>Constant </strong>(निरंतर)- occurring continuously or repeatedly.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate ANTONYM of the given word.<br>Concede</p>",
                    question_hi: "<p>11. Select the most appropriate ANTONYM of the given word.<br>Concede</p>",
                    options_en: ["<p>Surrender</p>", "<p>Battle</p>", 
                                "<p>Avow</p>", "<p>Deny</p>"],
                    options_hi: ["<p>Surrender</p>", "<p>Battle</p>",
                                "<p>Avow</p>", "<p>Deny</p>"],
                    solution_en: "<p>11.(d) <strong>Deny</strong>- to refuse to admit the truth or existence of something.<br><strong>Concede</strong>- to acknowledge or admit something, often reluctantly.<br><strong>Surrender</strong>- to give up or yield to another\'s power or control.<br><strong>Battle</strong>- a fight or struggle between opposing forces.<br><strong>Avow</strong>- to declare or affirm openly.</p>",
                    solution_hi: "<p>11.(d) <strong>Deny </strong>(ग़लत ठहराना)- to refuse to admit the truth or existence of something.<br><strong>Concede </strong>(स्वीकार करना)- to acknowledge or admit something, often reluctantly.<br><strong>Surrender </strong>(आत्मसमर्पण करना)- to give up or yield to another\'s power or control.<br><strong>Battle </strong>(युद्ध)- a fight or struggle between opposing forces.<br><strong>Avow </strong>(स्वीकार करना/खुलकर कहना)- to declare or affirm openly.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate ANTONYM of the given word.<br>Bombastic</p>",
                    question_hi: "<p>12. Select the most appropriate ANTONYM of the given word.<br>Bombastic</p>",
                    options_en: ["<p>Simple</p>", "<p>Wicked</p>", 
                                "<p>Elevated</p>", "<p>Pastoral</p>"],
                    options_hi: ["<p>Simple</p>", "<p>Wicked</p>",
                                "<p>Elevated</p>", "<p>Pastoral</p>"],
                    solution_en: "<p>12.(a) <strong>Simple</strong>- easy to understand or uncomplicated.<br><strong>Bombastic</strong>- high-sounding but with little meaning.<br><strong>Wicked</strong>- morally wrong or bad.<br><strong>Elevated</strong>- raised to a higher position or level.<br><strong>Pastoral</strong>- related to the countryside or rural life.</p>",
                    solution_hi: "<p>12.(a) <strong>Simple </strong>(साधारण)- easy to understand or uncomplicated.<br><strong>Bombastic</strong> (आडंबरपूर्ण)- high-sounding but with little meaning.<br><strong>Wicked </strong>(दुष्ट)- morally wrong or bad.<br><strong>Elevated </strong>(पदोन्नत करना/ऊपर उठाना)- raised to a higher position or level.<br><strong>Pastoral </strong>(ग्रामीण)- related to the countryside or rural life.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate ANTONYM of the given word.<br>Forgive</p>",
                    question_hi: "<p>13. Select the most appropriate ANTONYM of the given word.<br>Forgive</p>",
                    options_en: ["<p>Unburden</p>", "<p>Requite</p>", 
                                "<p>Clear</p>", "<p>Loathe</p>"],
                    options_hi: ["<p>Unburden</p>", "<p>Requite</p>",
                                "<p>Clear</p>", "<p>Loathe</p>"],
                    solution_en: "<p>13.(d) <strong>Loathe</strong>- to feel intense dislike or disgust for something.<br><strong>Forgive</strong>- to stop feeling angry or resentful towards someone for an offense.<br><strong>Unburden</strong>- to relieve oneself of a burden or responsibility.<br><strong>Requite</strong>- to return a favor or respond to a kindness.<br><strong>Clear</strong>- to remove or get rid of something.</p>",
                    solution_hi: "<p>13.(d) <strong>Loathe</strong> (घृणा करना)- to feel intense dislike or disgust for something.<br><strong>Forgive </strong>(क्षमा करना)- to stop feeling angry or resentful towards someone for an offense.<br><strong>Unburden</strong> (भारमुक्त होना)- to relieve oneself of a burden or responsibility.<br><strong>Requite </strong>(प्रतिकार करना/लौटाना)- to return a favor or respond to a kindness.<br><strong>Clear </strong>(साफ करना/छुटकारा पाना)- to remove or get rid of something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>The pool was too _________ at this end for kids to play. [SHALLOW]</p>",
                    question_hi: "<p>14. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>The pool was too _________ at this end for kids to play. [SHALLOW]</p>",
                    options_en: ["<p>green</p>", "<p>blue</p>", 
                                "<p>deep</p>", "<p>cold</p>"],
                    options_hi: ["<p>green</p>", "<p>blue</p>",
                                "<p>deep</p>", "<p>cold</p>"],
                    solution_en: "<p>14.(c) <strong>Deep</strong>- extending far down from the top or surface.<br><strong>Shallow</strong>- having little depth.<br><strong>Green</strong>- a color often associated with nature and freshness.<br><strong>Blue</strong>- a color resembling that of the clear sky or ocean.<br><strong>Cold</strong>- having a low temperature.</p>",
                    solution_hi: "<p>14.(c) <strong>Deep </strong>(गहरा)- extending far down from the top or surface.<br><strong>Shallow </strong>(उथला)- having little depth.<br><strong>Green </strong>(हरा)- a color often associated with nature and freshness.<br><strong>Blue </strong>(नीला)- a color resembling that of the clear sky or ocean.<br><strong>Cold </strong>(ठंडा)- having a low temperature.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank. <br>She left the party ______ because of her parents&rsquo; strict time restrictions. [late]</p>",
                    question_hi: "<p>15. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank. <br>She left the party ______ because of her parents&rsquo; strict time restrictions. [late]</p>",
                    options_en: ["<p>exit</p>", "<p>early</p>", 
                                "<p>small</p>", "<p>suffer</p>"],
                    options_hi: ["<p>exit</p>", "<p>early</p>",
                                "<p>small</p>", "<p>suffer</p>"],
                    solution_en: "<p>15.(b) <strong>Early</strong>- happening before the usual or expected time.<br><strong>Late</strong>- occurring after the expected or usual time.<br><strong>Exit</strong>- a way out or the act of leaving.<br><strong>Small</strong>- of limited size, amount, or extent.<br><strong>Suffer</strong>- to experience pain, distress, or hardship.</p>",
                    solution_hi: "<p>15.(b) <strong>Early </strong>(शीघ्र)- happening before the usual or expected time.<br><strong>Late </strong>(विलंब)- occurring after the expected or usual time.<br><strong>Exit </strong>(निकासी)- a way out or the act of leaving.<br><strong>Small </strong>(सूक्ष्म/छोटा)- of limited size, amount, or extent.<br><strong>Suffer </strong>(कष्ट सहना)- to experience pain, distress, or hardship.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>