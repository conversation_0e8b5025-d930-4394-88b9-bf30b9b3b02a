<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Six letters J, K, L, M, N and O are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the letter on the face opposite to J.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017211401.png\" alt=\"rId4\" width=\"140\" height=\"50\"></p>",
                    question_hi: "<p>1. एक पासे के विभिन्न फलकों पर छ: अक्षर J, K, L, M, N और O लिखे गए हैं। नीचे दिए गए चित्र में इस पासे की दो स्थितियों को दर्शाया गया है। दिए गए विकल्पों में से J के विपरीत फलक पर लिखा अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017211401.png\" alt=\"rId4\" width=\"140\" height=\"50\"></p>",
                    options_en: [
                        "<p>M</p>",
                        "<p>O</p>",
                        "<p>L</p>",
                        "<p>N</p>"
                    ],
                    options_hi: [
                        "<p>M</p>",
                        "<p>O</p>",
                        "<p>L</p>",
                        "<p>N</p>"
                    ],
                    solution_en: "<p>1.(d) From the dice 1 and 2 opposite faces are <br>N <math display=\"inline\"><mo>&#8596;</mo></math> J, M &harr; K, O &harr; L</p>",
                    solution_hi: "<p>1.(d) पासे से 1 और 2 विपरीत फलक हैं<br>N <math display=\"inline\"><mo>&#8596;</mo></math> J, M &harr; K, O &harr; L</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>I _ _ I _ I _ _ I _ J _</p>",
                    question_hi: "<p>2. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>I _ _ I _ I _ _ I _ J _</p>",
                    options_en: [
                        "<p>I J I I J J I</p>",
                        "<p>J I I I J J I</p>",
                        "<p>J I J I J I I</p>",
                        "<p>I I J J J I J</p>"
                    ],
                    options_hi: [
                        "<p>I J I I J J I</p>",
                        "<p>J I I I J J I</p>",
                        "<p>J I J I J I I</p>",
                        "<p>I I J J J I J</p>"
                    ],
                    solution_en: "<p>2.(c)<br>Series <math display=\"inline\"><mo>&#8658;</mo></math> I <span style=\"text-decoration: underline;\"><strong>J I</strong></span> / I <span style=\"text-decoration: underline;\"><strong>J</strong></span> I/ <span style=\"text-decoration: underline;\"><strong>I J</strong></span> I / <strong><span style=\"text-decoration: underline;\">I</span></strong> J <span style=\"text-decoration: underline;\"><strong>I</strong></span></p>",
                    solution_hi: "<p>2.(c)<br>श्रंखला <math display=\"inline\"><mo>&#8658;</mo></math> I <span style=\"text-decoration: underline;\"><strong>J I</strong></span> / I <span style=\"text-decoration: underline;\"><strong>J</strong></span> I/ <span style=\"text-decoration: underline;\"><strong>I J</strong></span> I / <strong><span style=\"text-decoration: underline;\">I</span></strong> J <span style=\"text-decoration: underline;\"><strong>I</strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Read the given statements and conclusions carefully. Assuming that the information&nbsp;given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Few doctors are buildings.<br>Most buildings are papers.<br>All papers are frogs.<br><strong>Conclusions :</strong><br>(I) Few doctors are papers.<br>(II) Some frogs are doctors.<br>(III) Some buildings are frogs.</p>",
                    question_hi: "<p>3. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। कथनों में दी गई सूचना को सत्य मानते हुए, चाहे यह सामान्यतया ज्ञात तथ्यों से असंगत प्रतीत होती हो, तय कीजिए कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता करते है/हैं।<br><strong>कथन :</strong> <br>कुछ डॉक्टर, बिल्डिंग हैं। <br>अधिकांश बिल्डिंग, कागज हैं।<br>सभी कागज, मेढक हैं।<br><strong>निष्कर्ष :</strong><br>(I) कुछ डॉक्टर, कागज हैं। <br>(II) कुछ मेढक, डॉक्टर हैं।<br>(III) कुछ बिल्डिंग, मेढक हैं।</p>",
                    options_en: [
                        "<p>Only conclusion III follows</p>",
                        "<p>Either conclusion I or conclusion II follows</p>",
                        "<p>All the conclusions I, II and III follow</p>",
                        "<p>Only conclusion I follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष ।II अनुसरण करता है</p>",
                        "<p>या तो निष्कर्ष I या निष्कर्ष II अनुसरण करता है</p>",
                        "<p>निष्कर्ष ।, ॥ और III सभी अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017211512.png\" alt=\"rId5\" width=\"291\" height=\"70\"><br>Only conclusion III follows.</p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017211653.png\" alt=\"rId6\" width=\"254\" height=\"70\"><br>केवल निष्कर्ष III अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Which of the following numbers will replace the question mark (?) in the given series ?<br>55, 62, ?, 72, 75, 82</p>",
                    question_hi: "<p>4. निम्नलिखित विकल्&zwj;पों में से कौन सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आएगी ?<br>55, 62, ?, 72, 75, 82</p>",
                    options_en: [
                        "<p>68</p>",
                        "<p>71</p>",
                        "<p>69</p>",
                        "<p>65</p>"
                    ],
                    options_hi: [
                        "<p>68</p>",
                        "<p>71</p>",
                        "<p>69</p>",
                        "<p>65</p>"
                    ],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017211830.png\" alt=\"rId7\" width=\"239\" height=\"70\"></p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017211830.png\" alt=\"rId7\" width=\"239\" height=\"70\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language,<br>A + B means \'A is the brother of B\'.<br>A - B means \'A is the father of B\'.<br>A ✕ B means \'A is the mother of B\'.<br>A &divide; B means \'A is the wife of B\'.<br>Based on the above, how is P related to T if \'P ✕ Q + R &divide; S - T\'?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में,<br>A + B का अर्थ है कि \'A, B का भाई है।<br>A - B का अर्थ है कि \'A, B का पिता है।<br>A &times; B का अर्थ है कि \'A, B की माता है\'।<br>A&divide;B का अर्थ है कि \'A, B की पत्नी है।<br>उपर्युक्त के आधार पर, यदि \'P &times; Q + R &divide; S - T\' है, तो P का T से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Sister</p>",
                        "<p>Wife</p>",
                        "<p>Mother</p>",
                        "<p>Mother\'s mother</p>"
                    ],
                    options_hi: [
                        "<p>बहन</p>",
                        "<p>पत्नी</p>",
                        "<p>माता</p>",
                        "<p>नानी</p>"
                    ],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017211987.png\" alt=\"rId8\" width=\"142\" height=\"130\"><br>P is mother&rsquo;s mother of T.</p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017211987.png\" alt=\"rId8\" width=\"140\" height=\"130\"><br>P, T की नानी है।.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. A, B, C, D, E, F, G and H are sitting around a circular table facing out to the centre (but not necessarily in the same order). D is sitting to the immediate left of A and third to the right of H. B is sitting third to the right of F. F is the immediate neighbour of C and G. C is sitting third to the right of A.&nbsp;Who is sitting fourth to the right of A?</p>",
                    question_hi: "<p>6. A, B, C, D, E, F, G और H एक वृत्ताकार मेज के चारों ओर केंद्र की ओर पीठ करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। D, A के ठीक बाएँ और H के दाएँ से तीसरे स्थान पर बैठा है। B, F के दाएँ से तीसरे स्थान पर बैठा है। C और G का निकटतम पड़ोसी F है। C, A के दाएँ से तीसरे स्थान पर बैठा है।&nbsp;A के दाएँ से चौथे स्थान पर कौन बैठा है?</p>",
                    options_en: [
                        "<p>H</p>",
                        "<p>B</p>",
                        "<p>D</p>",
                        "<p>C</p>"
                    ],
                    options_hi: [
                        "<p>H</p>",
                        "<p>B</p>",
                        "<p>D</p>",
                        "<p>C</p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212123.png\" alt=\"rId9\" width=\"140\" height=\"140\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212123.png\" alt=\"rId9\" width=\"140\" height=\"140\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, &lsquo;BEDROOM&rsquo; is written as &lsquo;28&rsquo; and &lsquo;MOON&rsquo; is written as &lsquo;16&rsquo;. How will &lsquo;CHAIN&rsquo; be written in that language?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में &lsquo;BEDROOM&rsquo; को &lsquo;28&rsquo; और &lsquo;MOON&rsquo; को &lsquo;16&rsquo; के रूप में लिखा जाता है। उस भाषा में &lsquo;CHAIN&rsquo; कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>23</p>",
                        "<p>24</p>",
                        "<p>20</p>",
                        "<p>26</p>"
                    ],
                    options_hi: [
                        "<p>23</p>",
                        "<p>24</p>",
                        "<p>20</p>",
                        "<p>26</p>"
                    ],
                    solution_en: "<p>7.(c)<strong> Logic :-</strong> (Number of letters)&times;4 <br>BEDROOM:- (7) &times; 4 = 28<br>MOON:- (4) &times; 4 = 16<br>Similarly,<br>CHAIN :- (5) &times; 4 = 20</p>",
                    solution_hi: "<p>7.(c) <strong>तर्क :- </strong>(अक्षरों की संख्या) &times; 4<br>BEDROOM:- (7) &times; 4 = 28<br>MOON:- (4) &times; 4 = 16<br>इसी प्रकार,<br>CHAIN :- (5) &times; 4 = 20</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Aman drives 5 km from point A towards the south. He then takes a left turn and drives 5 km. He then takes a right turn and drives 5 km. He again takes a right turn and drives 10 km. He finally takes a right turn and drives 10 km to reach point B. How much and in which direction does he have to drive to return to point A?&nbsp;(All the turns are 90&deg; turns only.)</p>",
                    question_hi: "<p>8. अमन बिंदु A से दक्षिण की ओर 5 km ड्रा इव करता है। फिर वह बाएं मुड़ता है और 5 km ड्राइव करता है। फिर वह दाएं मुड़ता है और 5 km ड्राइव करता है। वह फिर से दाएं मुड़ता है और 10 km ड्राइव करता है। अंत में वह दाएं मुड़ता है और बिंदु B तक पहुँचने के लिए 10 km ड्राइव करता है। बिंदु A पर लौटने के लिए उसे कितना और किस दिशा में ड्राइव करना होगा? (सभी मोड़ केवल 90&deg; के मोड़ हैं।)</p>",
                    options_en: [
                        "<p>5 km towards the north</p>",
                        "<p>5 km towards the west</p>",
                        "<p>5 km towards the south</p>",
                        "<p>5 km towards the east</p>"
                    ],
                    options_hi: [
                        "<p>उत्तर दिशा में 5 km</p>",
                        "<p>पश्चिम दिशा में 5 km</p>",
                        "<p>दक्षिण दिशा में 5 km</p>",
                        "<p>पूर्व दिशा में 5 km</p>"
                    ],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212245.png\" alt=\"rId10\" width=\"208\" height=\"125\"><br>He has to drive 5 km towards the east direction to reach point A.</p>",
                    solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212245.png\" alt=\"rId10\" width=\"208\" height=\"125\"><br>बिंदु A तक पहुँचने के लिए उसे पूर्व दिशा की ओर 5 किमी गाड़ी चलानी होगी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, &lsquo;BROWSE&rsquo; is written as &lsquo;ZJMEIW&rsquo; and &lsquo;ROAD&rsquo; is written as &lsquo;JMAX&rsquo;. How will &lsquo;BUDGET&rsquo; be written in that language ?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में, \'BROWSE\' को \'ZJMEIW\' लिखा जाता है और \'ROAD\' को \'JMAX\' लिखा जाता है। उस भाषा में \'BUDGET\' को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>ZGXUWH</p>",
                        "<p>ZGXWHU</p>",
                        "<p>ZGXUHW</p>",
                        "<p>ZGXHWU</p>"
                    ],
                    options_hi: [
                        "<p>ZGXUWH</p>",
                        "<p>ZGXWHU</p>",
                        "<p>ZGXUHW</p>",
                        "<p>ZGXHWU</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212346.png\" alt=\"rId11\" width=\"117\" height=\"150\">&nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212443.png\" alt=\"rId12\" width=\"78\" height=\"150\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212520.png\" alt=\"rId13\" width=\"118\" height=\"152\"></p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212346.png\" alt=\"rId11\" width=\"117\" height=\"150\">&nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212443.png\" alt=\"rId12\" width=\"78\" height=\"150\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212520.png\" alt=\"rId13\" width=\"118\" height=\"152\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>1205 &times; 5 + 35 &ndash; 197 &divide; 18 = ?</p>",
                    question_hi: "<p>10. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>1205 &times; 5 + 35 &ndash; 197 &divide; 18 = ?</p>",
                    options_en: [
                        "<p>3482</p>",
                        "<p>3752</p>",
                        "<p>3824</p>",
                        "<p>3284</p>"
                    ],
                    options_hi: [
                        "<p>3482</p>",
                        "<p>3752</p>",
                        "<p>3824</p>",
                        "<p>3284</p>"
                    ],
                    solution_en: "<p>10.(b) <strong>Given:</strong> 1205 &times; 5 + 35 &ndash; 197 &divide; 18 = ?<br>As per the instructions given in question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>1205 &divide; 5 - 35 + 197 &times; 18 = ?<br>241 - 35 + 197 &times; 18<br>241 - 35 + 3546<br>3787 - 35 = 3752</p>",
                    solution_hi: "<p>10.(b) <strong>दिया गया है: </strong>1205 &times; 5 + 35 &ndash; 197 &divide; 18 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>1205 &divide; 5 - 35 + 197 &times; 18 = ?<br>241 - 35 + 197 &times; 18<br>241 - 35 + 3546<br>3787 - 35 = 3752</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, &lsquo;legs hands ears&rsquo; is coded as &lsquo;du ph bq&rsquo;, &lsquo;hands eyes hearts&rsquo; is coded as &lsquo;ak fd du&rsquo;, &lsquo;ears hearts arms&rsquo; is coded as &lsquo;fd bq dy&rsquo;. What is the code for &lsquo;eyes&rsquo; in that language? (Note: All the codes are two-letter coded only.)</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में \'legs hands ears\' को \'du ph bq\' के रूप में कूटबद्ध किया जाता है, \'hands eyes hearts\' को \'ak fd du\' के रूप में कूटबद्ध किया जाता है, \'ears hearts arms\' को \'fd bq dy\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'eyes\' के लिए कूट क्या है? (नोट: सभी कूट केवल दो-अक्षरों में कूटबद्ध हैं।)</p>",
                    options_en: [
                        "<p>fd</p>",
                        "<p>ph</p>",
                        "<p>ak</p>",
                        "<p>du</p>"
                    ],
                    options_hi: [
                        "<p>fd</p>",
                        "<p>ph</p>",
                        "<p>ak</p>",
                        "<p>du</p>"
                    ],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212678.png\" alt=\"rId14\" width=\"281\" height=\"100\"><br>The code of &lsquo;eyes&rsquo; = &lsquo;ak&rsquo;.</p>",
                    solution_hi: "<p>11.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212678.png\" alt=\"rId14\" width=\"281\" height=\"100\"><br>&lsquo;eyes&rsquo; का कोड = &lsquo;ak&rsquo;.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the correct option that indicates the arrangement of the following words in a logical and meaningful order.&nbsp;<br>1. Block <br>2. Province <br>3. District <br>4. Country <br>5. Street <br>6. Village</p>",
                    question_hi: "<p>12. निम्नलिखित शब्दों को तार्किक और सार्थक क्रम में व्यवस्थित करने वाले सही विकल्प का चयन करें।<br>1. ब्लॉक<br>2. प्रांत<br>3. जिला<br>4. देश<br>5. सड़क<br>6. गांव</p>",
                    options_en: [
                        "<p>5, 1, 6, 3, 2, 4</p>",
                        "<p>5, 6, 1, 3, 4, 2</p>",
                        "<p>5, 6, 1, 3, 2, 4</p>",
                        "<p>5, 6, 1, 2, 3, 4</p>"
                    ],
                    options_hi: [
                        "<p>5, 1, 6, 3, 2, 4</p>",
                        "<p>5, 6, 1, 3, 4, 2</p>",
                        "<p>5, 6, 1, 3, 2, 4</p>",
                        "<p>5, 6, 1, 2, 3, 4</p>"
                    ],
                    solution_en: "<p>12.(c)<br>The correct order is <br>Street(5) &rarr; Village(6) &rarr; Block(1) &rarr; District(3) &rarr; Province(2) &rarr; Country(4)</p>",
                    solution_hi: "<p>12.(c) <br>सही क्रम है<br>सड़क(5) &rarr; गाँव(6) &rarr; ब्लाक(1) &rarr; ज़िला(3) &rarr; प्रांत(2) &rarr; देश(4)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Which of the following terms will replace the question mark (?) in the given series?<br>HDLB, LBPZ, PZTX, ? , XVBT</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?<br>HDLB, LBPZ, PZTX, ? , XVBT</p>",
                    options_en: [
                        "<p>TVWC</p>",
                        "<p>TXXV</p>",
                        "<p>TWXC</p>",
                        "<p>TWWV</p>"
                    ],
                    options_hi: [
                        "<p>TVWC</p>",
                        "<p>TXXV</p>",
                        "<p>TWXC</p>",
                        "<p>TWWV</p>"
                    ],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212825.png\" alt=\"rId15\" width=\"269\" height=\"91\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017212825.png\" alt=\"rId15\" width=\"269\" height=\"91\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. If 10 October 2003 was a Friday, then what was the day of the week on 8 October 2015?</p>",
                    question_hi: "<p>14. यदि 10 अक्टूबर 2003 को शुक्रवार था, तो 8 अक्टूबर 2015 को सप्ताह का कौन-सा दिन था?</p>",
                    options_en: [
                        "<p>Friday</p>",
                        "<p>Thursday</p>",
                        "<p>Tuesday</p>",
                        "<p>Monday</p>"
                    ],
                    options_hi: [
                        "<p>शुक्रवार</p>",
                        "<p>गुरुवार</p>",
                        "<p>मंगलवार</p>",
                        "<p>सोमवार</p>"
                    ],
                    solution_en: "<p>14.(b) 10 October 2003 was Friday. On moving to 2015 the number of odd days =&nbsp;+2 + 1 + 1 + 1 + 2 + 1 +1 + 1 + 2 + 1 + 1 + 1 = 15. On dividing 15 by 7, the remainder = 1. Friday + 1 = Saturday .We have reached till 10 October 2015. But we have to go 8 October 2015, the number of days in between = 2 .Saturday - 2 = Thursday.</p>",
                    solution_hi: "<p>14.(b) 10 अक्टूबर 2003 को शुक्रवार था. 2015 में जाने पर विषम दिनों की संख्या =&nbsp;+2 + 1 + 1 + 1 + 2 + 1 +1 + 1 + 2 + 1 + 1 + 1 = 15. 15 को 7 से विभाजित करने पर शेष = 1. शुक्रवार + 1 = शनिवार. हम 10 अक्टूबर 2015 तक पहुँच गए हैं. लेकिन हमें 8 अक्टूबर 2015 तक जाना है, बीच में दिनों की संख्या = 2 .शनिवार - 2 = गुरुवार.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out. (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>15. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।&nbsp;(नोट: गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>11 &ndash; 363</p>",
                        "<p>13 &ndash; 507</p>",
                        "<p>15 &ndash; 450</p>",
                        "<p>9 &ndash; 243</p>"
                    ],
                    options_hi: [
                        "<p>11 &ndash; 363</p>",
                        "<p>13 &ndash; 507</p>",
                        "<p>15 &ndash; 450</p>",
                        "<p>9 &ndash; 243</p>"
                    ],
                    solution_en: "<p>15.(c) <strong>Logic: </strong>(1st number)&sup2; &times; 3 = 2nd number <br>11 &ndash; 363 :- 11&sup2; &times; 3 = 121 &times; 3 = 363<br>13 &ndash; 507 :- 13&sup2; &times; 3 = 169 &times; 3 = 507<br>9 &ndash; 243 :- 9&sup2; &times; 3 = 81 &times; 3 = 243<br>But<br>15 &ndash; 450 :- 15&sup2; &times; 3 = 225 &times; 3 = 675 <math display=\"inline\"><mo>&#8800;</mo></math> 450</p>",
                    solution_hi: "<p>15.(c) <strong>तर्क: </strong>(पहली संख्या)&sup2; &times; 3 = दूसरी संख्या <br>11 &ndash; 363 :- 11&sup2; &times; 3 = 121 &times; 3 = 363<br>13 &ndash; 507 :- 13&sup2; &times; 3 = 169 &times; 3 = 507<br>9 &ndash; 243 :- 9&sup2; &times; 3 = 81 &times; 3 = 243<br>लेकिन<br>15 &ndash; 450 :- 15&sup2; &times; 3 = 225 &times; 3 = 675 <math display=\"inline\"><mo>&#8800;</mo></math> 450</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213061.png\" alt=\"rId16\" width=\"297\" height=\"60\"></p>",
                    question_hi: "<p>16. विकल्पों में दिए गए उस चित्र की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213061.png\" alt=\"rId16\" width=\"297\" height=\"60\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213168.png\" alt=\"rId17\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213266.png\" alt=\"rId18\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213448.png\" alt=\"rId19\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213597.png\" alt=\"rId20\" width=\"61\" height=\"60\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213168.png\" alt=\"rId17\" width=\"61\" height=\"61\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213266.png\" alt=\"rId18\" width=\"61\" height=\"61\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213448.png\" alt=\"rId19\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213597.png\" alt=\"rId20\" width=\"59\" height=\"59\"></p>"
                    ],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213448.png\" alt=\"rId19\" width=\"72\" height=\"72\"></p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213448.png\" alt=\"rId19\" width=\"72\" height=\"72\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17.Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?&nbsp;(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>17. निम्नलिखित चार विकल्पों में से तीन एक निश्चित तरीके से एकसमान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन सा विकल्प है जो उस समूह से संबंधित नहीं है?&nbsp;(ध्यान दें: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 लें - 13 पर विभिन्&zwj;न गणितीय संक्रियाएं जैसे 13 को जोड़ना / घटाना / गुणा करना आदि की जा सकती हैा । 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>56 &ndash; 31 &ndash; 205</p>",
                        "<p>88 &ndash; 23 &ndash; 245</p>",
                        "<p>22 &ndash; 13 &ndash; 85</p>",
                        "<p>18 &ndash; 12 &ndash; 72</p>"
                    ],
                    options_hi: [
                        "<p>56 &ndash; 31 &ndash; 205</p>",
                        "<p>88 &ndash; 23 &ndash; 245</p>",
                        "<p>22 &ndash; 13 &ndash; 85</p>",
                        "<p>18 &ndash; 12 &ndash; 72</p>"
                    ],
                    solution_en: "<p>17.(c)<br><strong>Logic:-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi>st</mi></msup><mi>no</mi><mo>.</mo></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup></math>no.) &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup></math>no. =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mi>rd</mi></msup></math> no.<br>(56 - 31 - 205) :- (56 + 31) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 31 &rArr; 174 + 31 = 205<br>(88 - 23 - 245) :- (88 + 23) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 23 &rArr; 222 + 23 =245<br>(18 - 12 - 72) :- (18 + 12) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 12 &rArr; 60 + 12 = 72<br>But, <br>(22 - 13 - 85) :- (22 + 13) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 13 &rArr; 70 + 13 = 83 &ne; 85</p>",
                    solution_hi: "<p>17.(c)<br><strong>तर्क :-</strong> (पहली संख्या + दूसरी संख्या ) <math display=\"inline\"><mo>&#215;</mo></math> 2 + दूसरी संख्या = तीसरी संख्या <br>(56 - 31 - 205) :- (56 + 31) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 31 &rArr; 174 + 31 = 205<br>(88 - 23 - 245) :- (88 + 23) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 23 &rArr; 222 + 23 =245<br>(18 - 12 - 72) :- (18 + 12) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 12 &rArr; 60 + 12 = 72<br>लेकिन ,<br>(22 - 13 - 85) :- (22 +&nbsp; 13) &times; 2 + 13 &rArr; 70 + 13 = 83 &ne; 85</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. The sequence of folding a piece of paper and the manner in which the folded paper is punched is shown in the following figures. How would this paper look when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213783.png\" alt=\"rId21\" width=\"248\" height=\"80\"></p>",
                    question_hi: "<p>18. निम्नलिखित आकृतियों में एक कागज को मोड़ने के क्रम और आखिर में उस में छेद करने के तरीके को दर्शाया गया है। खोलने पर यह कागज़ कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017213783.png\" alt=\"rId21\" width=\"248\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214001.png\" alt=\"rId22\" width=\"76\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214146.png\" alt=\"rId23\" width=\"78\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214336.png\" alt=\"rId24\" width=\"43\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214479.png\" alt=\"rId25\" width=\"44\" height=\"70\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214001.png\" alt=\"rId22\" width=\"77\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214146.png\" alt=\"rId23\" width=\"77\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214336.png\" alt=\"rId24\" width=\"43\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214479.png\" alt=\"rId25\" width=\"43\" height=\"70\"></p>"
                    ],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214336.png\" alt=\"rId24\" width=\"62\" height=\"101\"></p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214336.png\" alt=\"rId24\" width=\"62\" height=\"101\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the set in which the numbers are related in the same way as are the numbers of&nbsp;the following sets. (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(5, 25, 50)<br>(8, 64, 128)</p>",
                    question_hi: "<p>19. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं।&nbsp;(ध्यान दें : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(5, 25, 50)<br>(8, 64, 128)</p>",
                    options_en: [
                        "<p>(12, 144, 288)</p>",
                        "<p>(12, 134, 288)</p>",
                        "<p>(12, 144, 298)</p>",
                        "<p>(12, 144, 278)</p>"
                    ],
                    options_hi: [
                        "<p>(12, 144, 288)</p>",
                        "<p>(12, 134, 288)</p>",
                        "<p>(12, 144, 298)</p>",
                        "<p>(12, 144, 278)</p>"
                    ],
                    solution_en: "<p>19.(a) <strong>Logic :-</strong> (1st number &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi>nd</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi></msqrt></math>) &times; 2 = 3rd number<br>(5, 25, 50) :- (5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>25</mn></msqrt></math>) &times; 2 &rArr; (5 &times; 5) &times; 2 = 50<br>(8, 64, 128) :- (8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math>) &times; 2 &rArr; (8 &times; 8) &times; 2 = 128<br>Similarly,<br>(12, 144, 288) :- (12 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math>) &times; 2 &rArr; (12 &times; 12) &times; 2 = 288</p>",
                    solution_hi: "<p>19.(a) <strong>तर्क :-</strong> (पहली संख्या &times;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2342;&#2370;&#2360;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math>) &times; 2 = तीसरी संख्या<br>(5, 25, 50) :- (5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>25</mn></msqrt></math>) &times; 2 &rArr; (5 &times; 5) &times; 2 = 50<br>(8, 64, 128) :- (8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math>) &times; 2 &rArr; (8 &times; 8) &times; 2 = 128<br>इसी प्रकार,<br>(12, 144, 288) :- (12 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math>) &times; 2 &rArr; (12 &times; 12) &times; 2 = 288</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option in which the given figure (X) is embedded as its part. (Rotation is NOT allowed.)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214612.png\" alt=\"rId26\" width=\"103\" height=\"70\"></p>",
                    question_hi: "<p>20. उस विकल्प का चयन कीजिए, जिसमें दी गई आकृति (X) उसके भाग के रूप में सन्निहित है। (घूर्णन की अनुमति नहीं है।)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214612.png\" alt=\"rId26\" width=\"103\" height=\"70\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214710.png\" alt=\"rId27\" width=\"117\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214831.png\" alt=\"rId28\" width=\"118\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214938.png\" alt=\"rId29\" width=\"143\" height=\"67\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215018.png\" alt=\"rId30\" width=\"120\" height=\"66\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214710.png\" alt=\"rId27\" width=\"117\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214831.png\" alt=\"rId28\" width=\"118\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017214938.png\" alt=\"rId29\" width=\"139\" height=\"65\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215018.png\" alt=\"rId30\" width=\"125\" height=\"69\"></p>"
                    ],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215128.png\" alt=\"rId31\" width=\"91\" height=\"78\"></p>",
                    solution_hi: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215128.png\" alt=\"rId31\" width=\"93\" height=\"79\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.&nbsp;(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Happy : Ecstatic</p>",
                    question_hi: "<p>21. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।&nbsp;(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>प्रसन्न : आनंदित</p>",
                    options_en: [
                        "<p>Polite : Courteous</p>",
                        "<p>Loyal : Betray</p>",
                        "<p>Honest : Deceptive</p>",
                        "<p>Progress : Stagnation</p>"
                    ],
                    options_hi: [
                        "<p>नम्र : भद्र</p>",
                        "<p>वफादार : विश्वासघात</p>",
                        "<p>ईमानदार : भ्रामक</p>",
                        "<p>प्रगति : ठहराव</p>"
                    ],
                    solution_en: "<p>21.(a) As happy and ecstatic are synonyms of each other, similarly Polite and Courteous are synonyms of each other.</p>",
                    solution_hi: "<p>21.(a) जिस प्रकार प्रसन्न और आनंदित एक दूसरे के पर्यायवाची हैं, उसी प्रकार नम्र और भद्र एक दूसरे के पर्यायवाची हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215300.png\" alt=\"rId32\" width=\"84\" height=\"100\"></p>",
                    question_hi: "<p>22. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215300.png\" alt=\"rId32\" width=\"84\" height=\"100\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215476.png\" alt=\"rId33\" width=\"101\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215623.png\" alt=\"rId34\" width=\"101\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215739.png\" alt=\"rId35\" width=\"101\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215860.png\" alt=\"rId36\" width=\"101\" height=\"25\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215476.png\" alt=\"rId33\" width=\"101\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215623.png\" alt=\"rId34\" width=\"101\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215739.png\" alt=\"rId35\" width=\"101\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215860.png\" alt=\"rId36\" width=\"101\" height=\"25\"></p>"
                    ],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215860.png\" alt=\"rId36\" width=\"101\" height=\"25\"></p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215860.png\" alt=\"rId36\" width=\"100\" height=\"25\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. 9 is related to 47 following a certain logic. Following the same logic, 12 is related to 62. To which of the following is 17 related, following the same logic ?&nbsp;(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/ subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>23. एक निश्चित तर्क का अनुसरण करते हुए 9 का संबंध 47 से है। समान तर्क का अनुसरण करते हुए 12 का संबंध 62 से है। समान तर्क का अनुसरण करते हुए निम्नलिखित में से किसका संबंध 17 से है?&nbsp;(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>97</p>",
                        "<p>87</p>",
                        "<p>85</p>",
                        "<p>75</p>"
                    ],
                    options_hi: [
                        "<p>97</p>",
                        "<p>87</p>",
                        "<p>85</p>",
                        "<p>75</p>"
                    ],
                    solution_en: "<p>23.(b) <strong>Logic :-</strong> (1st number) &times; 5 + 2 = (2nd number)<br>(9, 47) :- (9) &times; 5 + 2 = 47<br>(12, 62) :- (12) &times; 5 + 2 = 62<br><strong>Similarly,</strong><br>(17 , 87) :- (17) &times; 5 + 2 = 87</p>",
                    solution_hi: "<p>23.(b) <strong>तर्क :-</strong> (पहली संख्या) &times; 5 + 2 = (दूसरी संख्या)<br>(9, 47) :- (9) &times; 5 + 2 = 47<br>(12, 62) :- (12) &times; 5 + 2 = 62<br>इसी प्रकार,<br>(17 , 87) :- (17) &times; 5 + 2 = 87</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215985.png\" alt=\"rId37\" width=\"102\" height=\"120\"></p>",
                    question_hi: "<p>24. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017215985.png\" alt=\"rId37\" width=\"102\" height=\"120\"></p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>18</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>18</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>24.(a) Total number of triangle = 16<br>ABC, DB&rsquo;B, AC&rsquo;D, AC&rsquo;E, ADE, HIF, HD&rsquo;F JKL, GVU, GWT, GXS, GYR, NMO,NA&rsquo;P, NZQ, NYR<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216136.png\" alt=\"rId38\" width=\"123\" height=\"167\"></p>",
                    solution_hi: "<p>24.(a) त्रिभुज की कुल संख्या = 16<br>ABC, DB&rsquo;B, AC&rsquo;D, AC&rsquo;E, ADE, HIF, HD&rsquo;F JKL, GVU, GWT, GXS, GYR, NMO,NA&rsquo;P, NZQ, NYR<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216136.png\" alt=\"rId38\" width=\"123\" height=\"167\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Three of the following four are alike in a certain way and thus form a group. Which is&nbsp;the one that does NOT belong to that group?&nbsp;(Note: The odd one out is not based on the number of consonants/vowels or their&nbsp;position in the letter cluster.)</p>",
                    question_hi: "<p>25. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक&nbsp;समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?&nbsp;(ध्यान दें: असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>DGL</p>",
                        "<p>FIM</p>",
                        "<p>QTY</p>",
                        "<p>MPU</p>"
                    ],
                    options_hi: [
                        "<p>DGL</p>",
                        "<p>FIM</p>",
                        "<p>QTY</p>",
                        "<p>MPU</p>"
                    ],
                    solution_en: "<p>25.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216309.png\" alt=\"rId39\" width=\"97\" height=\"70\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216437.png\" alt=\"rId40\" width=\"94\" height=\"70\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216529.png\" alt=\"rId41\" width=\"98\" height=\"70\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216616.png\" alt=\"rId42\" width=\"98\" height=\"70\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216309.png\" alt=\"rId39\" width=\"97\" height=\"70\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216437.png\" alt=\"rId40\" width=\"94\" height=\"70\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216529.png\" alt=\"rId41\" width=\"98\" height=\"70\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216616.png\" alt=\"rId42\" width=\"98\" height=\"70\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which chromosome is involved in the production of \'masked\' mRNAs for early development?</p>",
                    question_hi: "<p>26. प्रारंभिक विकास के लिए \'मास्क्ड\' (masked) mRNAs के उत्पादन में कौन-सा गुणसूत्र शामिल होता<br>है?</p>",
                    options_en: [
                        "<p>Polytene chromosome</p>",
                        "<p>Lampbrush chromosome</p>",
                        "<p>Autosomal chromosome</p>",
                        "<p>Sex chromosome</p>"
                    ],
                    options_hi: [
                        "<p>पॉलीटीन गुणसूत्र</p>",
                        "<p>लैम्पब्रश गुणसूत्र</p>",
                        "<p>ऑटोसोमल गुणसूत्र</p>",
                        "<p>लिंग गुणसूत्र</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>Lampbrush chromosome. </strong>These are found in oocytes of animals like sharks, amphibians, reptiles, and birds (most distinct during the Diplotene stage). Polytene Chromosomes - First seen by Balbiani (1881) in salivary glands of larva of Chironomus insect (midges &mdash; belongs to order Diptera). An autosome is one of the numbered chromosomes, as opposed to the sex chromosomes. Humans have 22 pairs of autosomes and one pair of sex chromosomes (XX or XY).</p>",
                    solution_hi: "<p>26.(b) <strong>लैम्पब्रश गुणसूत्र। </strong>ये शार्क, उभयचर, सरीसृप और पक्षियों (डिप्लोटीन चरण के दौरान सबसे अधिक विशिष्ट) जैसे जीवों के अंडकोशिकाओं (oocytes) में पाए जाते हैं। पॉलीटीन गुणसूत्र - इसे पहली बार बाल्बियानी (1881) द्वारा चिरोनोमस कीट [मिज (midges)- डिप्टेरा गण से संबंधित] के लार्वा की लार ग्रंथियों में देखा गया। ऑटोसोम एक क्रमांकित गुणसूत्र है, जो लिंग गुणसूत्र के विपरीत है। मनुष्यों में 22 जोड़े ऑटोसोम और एक जोड़ी लिंग गुणसूत्र (XX या XY) होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following sports is played by Siddharth Suchde?</p>",
                    question_hi: "<p>27. सिद्धार्थ सुचडे (Siddharth Suchde) निम्नलिखित में से कौन-सा खेल खेलते हैं?</p>",
                    options_en: [
                        "<p>Football</p>",
                        "<p>Badminton</p>",
                        "<p>Tennis</p>",
                        "<p>Squash</p>"
                    ],
                    options_hi: [
                        "<p>फुटबॉल</p>",
                        "<p>बैडमिंटन</p>",
                        "<p>टेनिस</p>",
                        "<p>स्क्वैश</p>"
                    ],
                    solution_en: "<p>27.(d) <strong>Squash.</strong> It is played by two players for singles matches or four players for doubles matches. Other Players: Squash - Saurav Ghosal, Dipika Pallikal, Anahat Singh. Football - Sunil Chhetri, Gurpreet Singh Sandhu. Badminton - Pullela Gopichand, Saina Nehwal, PV Sindhu. Tennis - Sania Mirza, Rohan Bopanna.</p>",
                    solution_hi: "<p>27.(d) <strong>स्क्वैश । </strong>यह एकल मैचों के लिए दो खिलाड़ियों और युगल मैचों के लिए चार खिलाड़ियों द्वारा खेला जाता है। अन्य खिलाड़ी: स्क्वैश - सौरव घोषाल, दीपिका पल्लीकल, अनाहत सिंह। फुटबॉल - सुनील छेत्री, गुरप्रीत सिंह संधू। बैडमिंटन - पुलेला गोपीचंद, साइना नेहवाल, पीवी सिंधु। टेनिस - सानिया मिर्जा, रोहन बोपन्ना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following Articles of the Indian Constitution CANNOT be suspended even during an emergency declared on the grounds of war or external aggression?</p>",
                    question_hi: "<p>28. युद्ध या बाहरी आक्रमण के आधार पर घोषित आपातकाल के दौरान भी भारतीय संविधान के निम्नलिखित में से कौन-से अनुच्छेद को निलंबित नहीं किया जा सकता है?</p>",
                    options_en: [
                        "<p>Articles 15 and 17</p>",
                        "<p>Articles 20 and 21</p>",
                        "<p>Articles 14 and 16</p>",
                        "<p>Articles 30 and 32</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 15 और 17</p>",
                        "<p>अनुच्छेद 20 और 21</p>",
                        "<p>अनुच्छेद 14 और 16</p>",
                        "<p>अनुच्छेद 30 और 32</p>"
                    ],
                    solution_en: "<p>28.(b) <strong>Articles 20 and 21. </strong>Article 14 - Equality before law. Article 15 - Prohibition of discrimination on grounds of religion, race, caste, sex or place of birth. Article 16 - Equality of opportunity in matters of public employment. Article 17 - Abolition of Untouchability. Article 20 - Protection in respect of conviction for offences. Article 21 - Protection of life and personal liberty. Article 30 - Right of minorities to establish and administer educational institutions.</p>",
                    solution_hi: "<p>28.(b) <strong>अनुच्छेद 20 और 21. </strong>अनुच्छेद 14 - विधि के समक्ष समानता। अनुच्छेद 15 - धर्म, मूलवंश, जाति, लिंग या जन्म स्थान के आधार पर भेदभाव का निषेध। अनुच्छेद 16 - सार्वजनिक रोजगार के मामलों में अवसर की समानता। अनुच्छेद 17 - अस्पृश्यता का उन्मूलन। अनुच्छेद 20 - अपराधों के लिए दोषसिद्धि के संबंध में संरक्षण। अनुच्छेद 21 - जीवन और व्यक्तिगत स्वतंत्रता का संरक्षण। अनुच्छेद 30 - अल्पसंख्यकों को शैक्षणिक संस्थानों की स्थापना और प्रशासन का अधिकार।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. What is the title of the autobiography written by the renowned American professional boxer, Mike Tyson?</p>",
                    question_hi: "<p>29. प्रसिद्ध अमेरिकी पेशेवर मुक्केबाज माइक टायसन द्वारा लिखी गई आत्मकथा का शीर्षक क्या है?</p>",
                    options_en: [
                        "<p>The Greatest: My Own Story</p>",
                        "<p>Undisputed Truth: My Autobiography</p>",
                        "<p>Undisputed Life: My Story</p>",
                        "<p>Undeniable Story: My Life</p>"
                    ],
                    options_hi: [
                        "<p>द ग्रेटेस्टः माई ओन स्टोरी</p>",
                        "<p>अनडिस्प्यूटेड त्रुथ: माई आटोबॉयोग्राफी</p>",
                        "<p>अनडिस्प्यूटेड लाइफः माई स्टोरी</p>",
                        "<p>अनडिनायएबल स्टोरीः माई लाइफ</p>"
                    ],
                    solution_en: "<p>29.(b) <strong>Undisputed Truth: My Autobiography. </strong>His other important books include &lsquo;Undisputed Truth&rsquo;, &lsquo;Iron Ambition: My Life With Cus D\'amato&rsquo;, &lsquo;CenterStage: Twelve of My Most Fascinating Interviews&rsquo; etc.</p>",
                    solution_hi: "<p>29.(b) <strong>अनडिस्प्यूटेड ट्रुथः माई आटोबॉयोग्राफी।</strong> उनकी अन्य महत्वपूर्ण पुस्तकों में \'अनडिस्प्यूटेड ट्रुथ\', \'आयरन एंबिशन: माई लाइफ विद क्यूस डी\'मैटो\', \'सेंटरस्टेज: ट्वेल्व ऑफ माई मोस्ट फैसिनेटिंग इंटरव्यूज\' आदि शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who asked James Rennel to prepare the map of Hindustan?</p>",
                    question_hi: "<p>30. जेम्स रेनेल को हिंदुस्तान का मानचित्र तैयार करने के लिए किसने कहा था?</p>",
                    options_en: [
                        "<p>Lord Ripen</p>",
                        "<p>Warren Hastings</p>",
                        "<p>Lord Dalhousie</p>",
                        "<p>Robert Clive</p>"
                    ],
                    options_hi: [
                        "<p>लॉर्ड रिपेन</p>",
                        "<p>वारेन हेस्टिंग्स</p>",
                        "<p>लॉर्ड डलहौजी</p>",
                        "<p>रॉबर्ट क्लाइव</p>"
                    ],
                    solution_en: "<p>30.(d) <strong>Robert Clive </strong>served twice as Governor of Bengal (1758-60 and 1764-67) and also led the Battle of Plassey on behalf of the British East India Company in 1757. James Rennel was the first Surveyor General of Bengal (1767-1777) and conducted the first comprehensive geographical survey of much of India. He is best known for his \"Bengal Atlas\" (1779) and \"Memoir of a Map of Hindoostan\" (1782).</p>",
                    solution_hi: "<p>30.(d) <strong>रॉबर्ट क्लाइव</strong> ने दो बार (1758-60 और 1764-67) बंगाल के गवर्नर के रूप में कार्य किया और 1757 में ब्रिटिश ईस्ट इंडिया कंपनी की ओर से प्लासी के युद्ध का नेतृत्व भी किया। जेम्स रेनेल बंगाल के पहले सर्वेयर जनरल (1767-1777) थे और उन्होंने भारत के अधिकांश भाग का पहला व्यापक भौगोलिक सर्वेक्षण किया था। उन्हें उनके \"बंगाल एटलस\" (1779) और \"मेमोयर ऑफ़ ए मैप ऑफ़ हिंदोस्तान\" (1782) के लिए जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following is the most dominant species of the Tropical Deciduous Forests ?</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन सी उष्णकटिबंधीय पर्णपाती वनों की सबसे प्रमुख प्रजाति है?</p>",
                    options_en: [
                        "<p>Coconut</p>",
                        "<p>Rubber</p>",
                        "<p>Mulberry</p>",
                        "<p>Teak</p>"
                    ],
                    options_hi: [
                        "<p>नारियल</p>",
                        "<p>रबड़</p>",
                        "<p>शहतूत</p>",
                        "<p>सागौन</p>"
                    ],
                    solution_en: "<p>31.(d) <strong>Teak.</strong> Tropical Dry Deciduous Vegetation occurs in an irregular wide strip running from the foot of the Himalayas to Kanyakumari, excluding Rajasthan, the Western Ghats, and West Bengal. Examples of species found in these forests include Khair, Bamboo, Palas, Amaltas, and Babool.</p>",
                    solution_hi: "<p>31.(d) <strong>सागौन। </strong>उष्णकटिबंधीय शुष्क पर्णपाती वनस्पति हिमालय की तलहटी से कन्याकुमारी तक फैली एक अनियमित चौड़ी पट्टी में पाई जाती है, जिसमें राजस्थान, पश्चिमी घाट और पश्चिम बंगाल शामिल नहीं हैं। इन वनों में पाई जाने वाली प्रजातियों के उदाहरणों में खैर, बांस, पलास, अमलतास और बबूल शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. In which state of India was the Neeru-Meeru programme introduced for construction of water harvesting structures?</p>",
                    question_hi: "<p>32. भारत के किस राज्य में जल संग्रहण संरचनाओं के निर्माण के लिए नीरू-मीरू कार्यक्रम शुरू किया गया था?</p>",
                    options_en: [
                        "<p>Andhra Pradesh</p>",
                        "<p>Rajasthan</p>",
                        "<p>Uttar Pradesh</p>",
                        "<p>Punjab</p>"
                    ],
                    options_hi: [
                        "<p>आंध्र प्रदेश</p>",
                        "<p>राजस्थान</p>",
                        "<p>उत्तर प्रदेश</p>",
                        "<p>पंजाब</p>"
                    ],
                    solution_en: "<p>32.(a)<strong> Andhra Pradesh. </strong>The Neeru-Meeru (Water and You) program was launched by the Water Conservation Mission (WCM), on May 1, 2000. It was a Water conservation and poverty alleviation initiative.</p>",
                    solution_hi: "<p>32.(a) <strong>आंध्र प्रदेश।</strong> नीरू-मीरू (पानी और आप) कार्यक्रम को जल संरक्षण मिशन (WCM) द्वारा 1 मई, 2000 को शुरू किया गया था। यह एक जल संरक्षण और गरीबी उन्मूलन पहल थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Who of the following is famous for playing the musical instrument Kanjira?</p>",
                    question_hi: "<p>33. निम्नलिखित में से कौन संगीत वाद्ययंत्र कंजिरा (Kanjira) के वादन के लिए प्रसिद्ध है?</p>",
                    options_en: [
                        "<p>Kishan Maharaj</p>",
                        "<p>Pannalal Ghosh</p>",
                        "<p>Pudukkotai Dakshinamurthi Pillai</p>",
                        "<p>S.V. Rajarao</p>"
                    ],
                    options_hi: [
                        "<p>किशन महाराज (Kishan Maharaj)</p>",
                        "<p>पन्नालाल घोष (Pannalal Ghosh)</p>",
                        "<p>पुदुक्कोट्टई दक्षिणामूर्ति पिल्लई (Pudukkotai Dakshinamurthi Pillai)</p>",
                        "<p>एस.वी. राजाराव (S.V. Rajarao)</p>"
                    ],
                    solution_en: "<p>33.(c) <strong>Pudukkottai Dakshinamurthi Pillai. </strong>His Awards: Kalaimamani award (1985), Sahitya Kala Parishad award (1992). Other Indian Kanjira players: Anandan Sivamani, Jamey Haddad, Natarajan Ganesh Kumar.</p>",
                    solution_hi: "<p>33.(c) <strong>पुदुक्कोट्टई दक्षिणामूर्ति पिल्लई।</strong> उनके पुरस्कार: कलैमामणि पुरस्कार (1985), साहित्य कला परिषद पुरस्कार (1992)। अन्य भारतीय कंजीरा वादक: आनंदन शिवमणि, जेमी हद्दाद, नटराजन गणेश कुमार।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. The Environment (Protection) Act, 1986 empowers the _________ to establish authorities charged with the mandate of preventing environmental pollution in all its forms.</p>",
                    question_hi: "<p>34. पर्यावरण (संरक्षण) अधिनियम, 1986 _________ को पर्यावरण प्रदूषण के सभी रूपों की रोकथाम के लिए अधिकृत प्राधिकरण स्थापित करने का अधिकार देता है।</p>",
                    options_en: [
                        "<p>State Government</p>",
                        "<p>Central Government</p>",
                        "<p>Supreme Court</p>",
                        "<p>local municipalities</p>"
                    ],
                    options_hi: [
                        "<p>राज्य सरकार</p>",
                        "<p>केंद्र सरकार</p>",
                        "<p>सर्वोच्च न्यायालय</p>",
                        "<p>स्थानीय नगर पालिका</p>"
                    ],
                    solution_en: "<p>34.(b) <strong>Central Government. </strong>The Environment (Protection) Act, 1986 was enacted to safeguard and enhance the environment. The Central Government has the authority to: protect and improve the environment, set emission and discharge standards, regulate industrial locations, manage hazardous wastes, and protect public health and welfare. Some key environmental acts include the Biological Diversity Act (2002), Wild Life (Protection) Act (1972), Forest Conservation Act (1980), Indian Forest Act (1927), and Energy Conservation Act (2001).</p>",
                    solution_hi: "<p>34.(b) <strong>केंद्र सरकार। </strong>पर्यावरण (संरक्षण) अधिनियम, 1986 पर्यावरण की सुरक्षा और संवर्धन के लिए अधिनियमित किया गया था। केंद्र सरकार के पास निम्नलिखित अधिकार हैं: पर्यावरण की सुरक्षा और सुधार, उत्सर्जन और निर्वहन मानक निर्धारित करना, औद्योगिक स्थानों को विनियमित करना, खतरनाक अपशिष्टों का प्रबंधन करना और सार्वजनिक स्वास्थ्य और कल्याण की रक्षा करना। कुछ प्रमुख पर्यावरण अधिनियमों में जैविक विविधता अधिनियम (2002), वन्य जीवन (संरक्षण) अधिनियम (1972), वन संरक्षण अधिनियम (1980), भारतीय वन अधिनियम (1927) और ऊर्जा संरक्षण अधिनियम (2001) शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Dr. B. C. Roy Trophy is an Indian football tournament for :</p>",
                    question_hi: "<p>35. डॉ. बी.सी. रॉय ट्रॉफी एक भारतीय फुटबॉल टूर्नामेंट है जो _______ के लिए है।</p>",
                    options_en: [
                        "<p>under-14 players</p>",
                        "<p>under-21 players</p>",
                        "<p>under-19 players</p>",
                        "<p>under-17 players</p>"
                    ],
                    options_hi: [
                        "<p>14 वर्ष से कम आयु वाले खिलाड़ियों</p>",
                        "<p>21 वर्ष से कम आयु वाले खिलाड़ियों</p>",
                        "<p>19 वर्ष से कम आयु वाले खिलाड़ियों</p>",
                        "<p>17 वर्ष से कम आयु वाले खिलाड़ियों</p>"
                    ],
                    solution_en: "<p>35.(c) <strong>under-19 players. </strong>The Junior National Football Championship, also known as BC Roy Trophy, is an Indian football tournament held for players under-15 years of age representing the states of India. It was formerly held for the U-19 age group before being converted into an U-15 tournament from the 2023-24 edition.</p>",
                    solution_hi: "<p>35.(c) <strong>19 वर्ष से कम</strong> आयु वाले खिलाड़ियों। जूनियर नेशनल फुटबॉल चैंपियनशिप, जिसे बी.सी रॉय ट्रॉफी के नाम से भी जाना जाता है, भारत के राज्यों का प्रतिनिधित्व करने वाले 15 वर्ष से कम आयु के खिलाड़ियों के लिए आयोजित एक भारतीय फुटबॉल टूर्नामेंट है। इसे पहले अंडर-19 आयु वर्ग के लिए आयोजित किया जाता था, जिसे 2023-24 संस्करण से अंडर-15 टूर्नामेंट में बदल दिया गया है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Who among the following was honoured by the Sangeet Natak Akademi for his contribution to the Bharatanatyam dance form ?</p>",
                    question_hi: "<p>36. निम्नलिखित में से किसे भरतनाट्यम नृत्य शैली में उनके योगदान के लिए संगीत नाटक अकादमी द्वारा सम्मानित किया गया ?</p>",
                    options_en: [
                        "<p>Maulik Shah</p>",
                        "<p>Akham Lakshmi Devi</p>",
                        "<p>Surupa Sen</p>",
                        "<p>Radha Sridhar</p>"
                    ],
                    options_hi: [
                        "<p>मौलिक शाह</p>",
                        "<p>अखम लक्ष्मी देवी</p>",
                        "<p>सुरूपा सेन</p>",
                        "<p>राधा श्रीधर</p>"
                    ],
                    solution_en: "<p>36.(d) <strong>Radha Sridhar.</strong> She received this award in 2018. Other exponents of the sangeet natak academy awards (2018) include: Shri Maulik Shah for Kathak, Akham Lakshmi Devi for Manipuri dance, Shrimati Surupa Sen for Odissi dance.</p>",
                    solution_hi: "<p>36.(d) <strong>राधा श्रीधर </strong>को यह पुरस्कार 2018 में प्रदान किया गया था। संगीत नाटक अकादमी पुरस्कार (2018) के अन्य प्रतिपादकों में शामिल हैं: कथक के लिए श्री मौलिक शाह, मणिपुरी नृत्य के लिए अखम लक्ष्मी देवी, ओडिसी नृत्य के लिए श्रीमती सुरूपा सेन।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Unemployment that occurs during recessions and depressions are called as ________.</p>",
                    question_hi: "<p>37. अमंदी (recessions) और महामंदी (depressions) के दौरान होने वाली बेरोजगारी को ________कहा जाता है।</p>",
                    options_en: [
                        "<p>Disguised unemployment</p>",
                        "<p>Frictional unemployment</p>",
                        "<p>Seasonal unemployment</p>",
                        "<p>Cyclical unemployment</p>"
                    ],
                    options_hi: [
                        "<p>प्रच्छन्न बेरोजगारी (Disguised unemployment)</p>",
                        "<p>घर्षनात्मक बेरोजगारी (Frictional unemployment)</p>",
                        "<p>मौसमी बेरोजगारी (Seasonal unemployment)</p>",
                        "<p>चक्रीय बेरोजगारी (Cyclical unemployment)</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>Cyclical unemployment - </strong>It occurs due to fluctuations in the overall economy. Disguised unemployment is unemployment with low productivity that does not affect aggregate output. Frictional unemployment is a form of unemployment reflecting the gap between someone voluntarily leaving a job and finding another. Seasonal unemployment is when people who work in seasonal jobs become unemployed when demand for labor decreases.</p>",
                    solution_hi: "<p>37.(d) <strong>चक्रीय बेरोजगारी (Cyclical unemployment) -</strong> यह समग्र अर्थव्यवस्था में उतार-चढ़ाव के कारण होता है। प्रच्छन्न बेरोजगारी कम उत्पादकता वाली बेरोजगारी है जो कुल उत्पादन को प्रभावित नहीं करती है। घर्षनात्मक बेरोजगारी, बेरोजगारी का एक ऐसा रूप है जो किसी व्यक्ति द्वारा स्वेच्छा से नौकरी छोड़ने और दूसरी नौकरी खोजने के बीच के अंतर को दर्शाता है। मौसमी बेरोजगारी तब होती है जब मौसमी नौकरियों में काम करने वाले लोग श्रम की मांग कम होने पर बेरोजगार हो जाते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. If the recipient of Employee provident Fund (EPF) withdrawal does not provide his&nbsp;PAN, TDS on the withdrawal will be ________, instead of the maximum marginal rate as&nbsp;per new budget of 2023.</p>",
                    question_hi: "<p>38. यदि कर्मचारी भविष्य निधि (EPF) निकासी का प्राप्तकर्ता अपना पैन प्रदान नहीं करता है, तो निकासी पर टीडीएस, 2023 के नए बजट के अनुसार अधिकतम सीमांत दर के बजाय ________ होगा।</p>",
                    options_en: [
                        "<p>20%</p>",
                        "<p>30%</p>",
                        "<p>51%</p>",
                        "<p>40%</p>"
                    ],
                    options_hi: [
                        "<p>20%</p>",
                        "<p>30%</p>",
                        "<p>51%</p>",
                        "<p>40%</p>"
                    ],
                    solution_en: "<p>38.(a) <strong>20%. </strong>The Employees&rsquo; Provident Fund Organisation (EPFO), under the administrative control of the Ministry of Labour and Employment, regulates the norms for the employees&rsquo; contributory provident fund. Under the EPF scheme, the employee contributes 12% of their basic pay, and the employer matches this contribution. The employer&rsquo;s EPF contribution is divided into two parts: 8.33% goes to the pension scheme, and 3.67% goes to the EPF corpus.</p>",
                    solution_hi: "<p>38.(a) <strong>20% । </strong>श्रम एवं रोजगार मंत्रालय के प्रशासनिक नियंत्रण के अंतर्गत कर्मचारी भविष्य निधि संगठन (EPFO) कर्मचारियों के अंशदायी भविष्य निधि के मानदंडों को नियंत्रित करता है। EPF योजना के तहत, कर्मचारी अपने मूल वेतन का 12% योगदान देता है, और नियोक्ता इस योगदान के बराबर योगदान देता है। नियोक्ता का EPF योगदान दो भागों में विभाजित है: 8.33% पेंशन योजना में जाता है, और 3.67% EPF कोष में जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following devices is used to accelerate charged particles to high&nbsp;velocities?</p>",
                    question_hi: "<p>39. आवेशित कणों को उच्च वेग तक त्वरित करने के लिए निम्नलिखित में से किस उपकरण का प्रयोग किया जाता है?</p>",
                    options_en: [
                        "<p>cosmotron</p>",
                        "<p>cyclotron</p>",
                        "<p>copatron</p>",
                        "<p>cryotron</p>"
                    ],
                    options_hi: [
                        "<p>कॉस्मोट्रॉन (Cosmotron)</p>",
                        "<p>साइक्लोट्रॉन (Cyclotron)</p>",
                        "<p>कोपेट्रॉन (Copatron)</p>",
                        "<p>क्रायोट्रॉन (Cryotron)</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>Cyclotron.</strong> It is a device invented by E.O. Lawrence in 1929, that is used for accelerating charged particles (such as protons, deutron or &alpha;&ndash;particles) to high velocities. It consists of two semi-circular hollow metallic disks DD, called dees, on account of their shape resembling the letter D of the English alphabet.</p>",
                    solution_hi: "<p>39.(b)<strong> साइक्लोट्रॉन (Cyclotron)।</strong> यह 1929 में ई.ओ. लॉरेंस द्वारा आविष्कार किया गया एक उपकरण है, जिसका उपयोग आवेशित कणों (जैसे प्रोटॉन, ड्यूट्रॉन या &alpha;-कण) को उच्च वेग तक त्वरित करने के लिए किया जाता है। इसमें दो अर्ध-वृत्ताकार खोखली धातु की डिस्क DD होती हैं, जिन्हें डीज़ कहा जाता है, क्योंकि उनका आकार अंग्रेजी वर्णमाला के अक्षर D जैसा होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Who among the following famous personalities was awarded the prestigious German Peace Prize ?</p>",
                    question_hi: "<p>40. निम्नलिखित प्रसिद्ध व्यक्तियों में से किसे प्रतिष्ठित जर्मन शांति पुरस्कार से सम्मानित किया गया?</p>",
                    options_en: [
                        "<p>Amit Shah</p>",
                        "<p>Lalji Mukherjee</p>",
                        "<p>Salman Rushdie</p>",
                        "<p>Saurabh Shukla</p>"
                    ],
                    options_hi: [
                        "<p>अमित शाह</p>",
                        "<p>लालजी मुखर्जी</p>",
                        "<p>सलमान रुश्दी</p>",
                        "<p>सौरभ शुक्ला</p>"
                    ],
                    solution_en: "<p>40.(c) <strong>Salman Rushdie.</strong> Salman Rushdie, the renowned Indian-British author, was awarded the prestigious German Peace Prize (Friedenspreis des Deutschen Buchhandels) in 2004. Books by Salman Rushdie: &lsquo;&rsquo;Satanic Verses&rsquo;&rsquo;, &lsquo;&rsquo;Midnight\'s Children&rsquo;&rsquo;, &lsquo;&rsquo;Victory City&rsquo;&rsquo;, &lsquo;&rsquo;The Moor\'s Last Sigh&rsquo;&rsquo;, &lsquo;&rsquo;The Ground Beneath Her Feet: A Novel&rsquo;&rsquo;, &lsquo;&rsquo;Shalimar the Clown: A Novel&rsquo;&rsquo;.</p>",
                    solution_hi: "<p>40.(c) <strong>सलमान रुश्दी। </strong>प्रसिद्ध भारतीय-ब्रिटिश लेखक सलमान रुश्दी को 2004 में प्रतिष्ठित जर्मन शांति पुरस्कार (फ़्रिडेन्सपेरिस डेस डॉयचे बुचंडेल्स) से सम्मानित किया गया था। सलमान रुश्दी की पुस्तकें: &lsquo;सैटेनिक वर्सेज&rsquo;, &lsquo;मिडनाइट्स चिल्ड्रन&rsquo;, &lsquo;विक्ट्री सिटी&rsquo;, &lsquo;द मूर्स लास्ट साइ&rsquo;, &lsquo;द ग्राउंड बिनीथ हर फीट: एक नॉवेल&rsquo;, &lsquo;शालीमार द क्लाउन: एक नॉवेल&rsquo;।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Two masses of 1 kg and 2 kg were dropped from a height of 3.2 metres. Their respective velocities just before touching the ground will be:&nbsp;(Assume acceleration due to gravity g = 10 m/s&sup2;)</p>",
                    question_hi: "<p>41. 1 kg और 2 kg के दो पिंड 3.2 मीटर की ऊँचाई से गिराए जाते हैं। जमीन को छूने से ठीक पहले उनके सापेक्ष वेग ज्ञात कीजिए। (मान लीजिए गुरुत्वीय त्वरण g = 10 m/s&sup2; )</p>",
                    options_en: [
                        "<p>8 m/s and 4 m/s</p>",
                        "<p>3.2 m/s for both</p>",
                        "<p>4 m/s and 8 m/s</p>",
                        "<p>8 m/s for both</p>"
                    ],
                    options_hi: [
                        "<p>8 m/s और 4 m/s</p>",
                        "<p>दोनों का 3.2 m/s</p>",
                        "<p>4 m/s और 8 m/s</p>",
                        "<p>दोनों का 8 m/s</p>"
                    ],
                    solution_en: "<p>41.(d)<strong> 8 m/s for both</strong>. We can calculate the velocities of the two masses just before touching the ground using the equation of motion for free fall: 𝑣 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi>gh</mi></msqrt></math><br>Where:<br>v = velocity just before touching the ground<br>g = acceleration due to gravity (10 m/s&sup2;)<br>h = height from which the masses were dropped (3.2m)<br>For both masses (1 kg and 2 kg), the velocity will be the same because the equation is independent of mass:<br>v= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">&#10005;</mi><mn>10</mn><mi mathvariant=\"normal\">&#10005;</mi><mn>3</mn><mo>.</mo><mn>2</mn></msqrt></math><br>𝑣 = <math display=\"inline\"><msqrt><mn>64</mn></msqrt></math><br>𝑣 = 8 m/s<br>Hence, the velocities of both masses just before touching the ground will be 8 m/s.</p>",
                    solution_hi: "<p>41.(d) <strong>दोनों का 8 मीटर/सेकंड। </strong>हम मुक्त रूप से गिरने की गति के समीकरण का उपयोग करके जमीन को छूने से ठीक पहले दो पिंड के वेग की गणना कर सकते हैं: 𝑣 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi>gh</mi></msqrt></math><br>जहाँ:<br>v = ज़मीन को छूने से ठीक पहले उनके सापेक्ष वेग<br>g = गुरुत्वाकर्षण के कारण त्वरण (10 m/s&sup2;)<br>h = ऊँचाई जहाँ से द्रव्यमान गिराए गए (3.2m) दोनों पिंडों (1 kg और 2 kg) के लिए, वेग समान होगा क्योंकि समीकरण पिंड से स्वतंत्र है:<br>v= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">&#10005;</mi><mn>10</mn><mi mathvariant=\"normal\">&#10005;</mi><mn>3</mn><mo>.</mo><mn>2</mn></msqrt></math><br>𝑣 = <math display=\"inline\"><msqrt><mn>64</mn></msqrt></math><br>𝑣 = 8 m/s<br>अतः, ज़मीन को छूने से ठीक पहले दोनों पिंडों का वेग 8 मीटर/सेकेंड होगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. __________ is a lively dance form of the Bundelkhand region. Its performance begins with the beating of mridang and gradually gains momentum dotted with prose and poetic dialogues.</p>",
                    question_hi: "<p>42. ___________ बुंदेलखंड क्षेत्र की एक जीवंत नृत्य शैली है। इसका प्रदर्शन मृदंग की थाप से शुरू होता है और धीरे-धीरे गद्य और काव्यात्मक संवादों के साथ गति पकड़ता है।</p>",
                    options_en: [
                        "<p>Matki</p>",
                        "<p>Gangaur</p>",
                        "<p>Rai</p>",
                        "<p>Swang</p>"
                    ],
                    options_hi: [
                        "<p>मटकी</p>",
                        "<p>गणगौर</p>",
                        "<p>राय</p>",
                        "<p>स्वांग</p>"
                    ],
                    solution_en: "<p>42.(d)<strong> Swang </strong>- A popular folk dance - theatre form performed in Rajasthan, Haryana, Uttar Pradesh and Malwa region of Madhya Pradesh. Other dances: Rai - This dance of Bundelkhand is performed by a man disguised as a woman to the accompaniment of musical instruments like dholak and nagara. Matki - A community dance from the Malwa region, performed by women. Gangaur - Performed during the nine days of Gangaur festival in the Nimar region in honour of their deity Ranubai and Dhaniyer Suryadev.</p>",
                    solution_hi: "<p>42.(d) <strong>स्वांग </strong>- राजस्थान, हरियाणा, उत्तर प्रदेश और मध्य प्रदेश के मालवा क्षेत्र में किया जाने वाला एक लोकप्रिय लोक नृत्य - नाट्य शैली। अन्य नृत्य: राय - बुन्देलखण्ड का यह नृत्य एक पुरुष द्वारा महिला के वेश में ढोलक और नगाड़ा जैसे संगीत वाद्ययंत्रों के साथ किया जाता है। मटकी - यह मालवा क्षेत्र का एक सामुदायिक नृत्य है, जो महिलाओं द्वारा किया जाता है। गणगौर - निमाड़ क्षेत्र में गणगौर उत्सव के अवसर पर नौ दिनों के दौरान उनके देवता रणुबाई और धनियार सूर्यदेव के सम्मान में किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which inert gas is used in double-glazed windows to fill the space between the panes?</p>",
                    question_hi: "<p>43. खिड़की के कांच के बीच की जगह को भरने के लिए डबल-ग्लेज़ विंडो में किस अक्रिय गैस का उपयोग किया जाता है?</p>",
                    options_en: [
                        "<p>Xenon</p>",
                        "<p>Radon</p>",
                        "<p>Argon</p>",
                        "<p>Helium</p>"
                    ],
                    options_hi: [
                        "<p>जीनॉन</p>",
                        "<p>रेडॉन</p>",
                        "<p>आर्गन</p>",
                        "<p>हीलियम</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>Argon</strong> is used in double-glazed windows because it has low thermal conductivity, which enhances the insulating properties of the window. This helps to reduce heat transfer, making the windows more energy-efficient by minimizing heat loss in the winter and heat gain in the summer. The noble gases are the naturally occurring elements in group 18 of the periodic table : Helium (He), Neon (Ne), Argon (Ar), Krypton (Kr), Xenon (Xe), and Radon (Rn).</p>",
                    solution_hi: "<p>43.(c) <strong>आर्गन</strong> का उपयोग डबल-ग्लेज़्ड खिड़कियों में किया जाता है क्योंकि इसमें कम तापीय चालकता होती है, जो खिड़की के इन्सुलेटिंग गुणों को बढ़ाती है। इससे ऊष्मा स्थानांतरण को कम करने में मदद मिलती है, तथा सर्दियों में ऊष्मा की ह्रास को कम करता है तथा गर्मियों में ऊष्मा प्राप्ति को कम करता है जिससे खिड़कियां अधिक ऊर्जा-कुशल बन जाती हैं। आवर्त सारणी के समूह 18 में प्राकृतिक रूप से पाए जाने वाले तत्व उत्कृष्ट गैस (नोबल गैसें) हैं: हीलियम (He), नियॉन (Ne), आर्गन (Ar), क्रिप्टन (Kr), ज़ेनॉन (Xe), और रेडॉन (Rn)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Phytoplanktons are ________.</p>",
                    question_hi: "<p>44. पादपप्&zwj;लवक (फाइटोप्लांकटन) ________ होते हैं।</p>",
                    options_en: [
                        "<p>primary consumers of lake ecosystem</p>",
                        "<p>tertiary consumers of pond ecosystem</p>",
                        "<p>producers in pond ecosystem</p>",
                        "<p>secondary consumers of ocean ecosystem</p>"
                    ],
                    options_hi: [
                        "<p>झील पारिस्थितिकी तंत्र के प्राथमिक उपभोक्ता</p>",
                        "<p>तालाब पारिस्थितिकी तंत्र के तृतीयक उपभोक्ता</p>",
                        "<p>तालाब पारिस्थितिकी तंत्र में उत्पादक</p>",
                        "<p>महासागर पारिस्थितिकी तंत्र के द्वितीयक उपभोक्ता</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Producers in pond ecosystem. </strong>The autotrophic components include the phytoplankton, some algae and the floating, submerged and marginal plants found at the edges. The consumers are represented by the zooplankton, the free swimming and bottom dwelling forms. The decomposers are the fungi, bacteria and flagellates especially abundant in the bottom of the pond.</p>",
                    solution_hi: "<p>44.(c) <strong>तालाब पारिस्थितिकी तंत्र में उत्पादक।</strong> स्वपोषी घटकों में पादपप्&zwj;लवक (फाइटोप्लांकटन), कुछ शैवाल और किनारों पर पाए जाने वाले तैरते, जलमग्न और सीमांत पौधे शामिल हैं। उपभोक्ताओं को ज़ोप्लांकटन, फ्री-स्विमिंग और निचले आवास रूपों द्वारा दर्शाया जाता है। अपघटक कवक, बैक्टीरिया और फ्लैगेलेट्स हैं जो विशेष रूप से तालाब के तल में प्रचुर मात्रा में पाए जाते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Shovana Narayan is associated with which of these dance forms?</p>",
                    question_hi: "<p>45. शोवना नारायण निम्न में से किस नृत्य शैली से संबंधित हैं?</p>",
                    options_en: [
                        "<p>Kathakali</p>",
                        "<p>Kathak</p>",
                        "<p>Bharatnatyam</p>",
                        "<p>Mohiniyattam</p>"
                    ],
                    options_hi: [
                        "<p>कथकली</p>",
                        "<p>कथक</p>",
                        "<p>भरतनाट्यम</p>",
                        "<p>मोहिनीअट्टम</p>"
                    ],
                    solution_en: "<p>45.(b) <strong>Kathak.</strong> Her Award: Padma Shri (1992). Other Important exponents in : Kathak (Uttar Pradesh) - Birju Maharaj, Gopi Krishna, Sitara Devi, Uma Sharma. Kathakali (Kerala) - Kalamandalam Gopi, Kalamandalam Krishna Prasad. Bharatanatyam (Tamil Nadu) - Meenakshi Sundaram Pillai, Balasarswati. Mohiniattam (Kerala) - Jayaprabha Menon, Samitha Rajan.</p>",
                    solution_hi: "<p>45.(b) <strong>कथक। </strong>इनको प्राप्त पुरस्कार: पद्म श्री (1992)। अन्य महत्वपूर्ण प्रतिपादक: कथक (उत्तर प्रदेश) - बिरजू महाराज, गोपी कृष्ण, सितारा देवी, उमा शर्मा। कथकली (केरल) - कलामंडलम गोपी, कलामंडलम कृष्ण प्रसाद। भरतनाट्यम (तमिलनाडु) - मीनाक्षी सुंदरम पिल्लई, बालासरस्वती। मोहिनीअट्टम (केरल) - जयप्रभा मेनन, समिथा राजन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is the name of India&rsquo;s first electric vertical take-off and landing (eVTOL) air taxi?</p>",
                    question_hi: "<p>46. भारत की पहली इलेक्ट्रिक वर्टिकल टेक-ऑफ और लैंडिंग (eVTOL) एयर टैक्सी का नाम क्या है?</p>",
                    options_en: [
                        "<p>Akash</p>",
                        "<p>Shunya</p>",
                        "<p>Vayu</p>",
                        "<p>Tejas</p>"
                    ],
                    options_hi: [
                        "<p>आकाश</p>",
                        "<p>शून्य</p>",
                        "<p>वायु</p>",
                        "<p>तेजस</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Shunya</strong>. Sarla Aviation, a Bengaluru-based aerospace startup, has unveiled &lsquo;Shunya.&rsquo; It is designed for short trips of 20-30 kilometers, accommodating up to six passengers with a maximum payload of 680 kg.</p>",
                    solution_hi: "<p>46.(b) <strong>शून्य। </strong>बेंगलुरु स्थित एयरोस्पेस स्टार्टअप सरला एविएशन ने \'शून्य\' का अनावरण किया है। इसे 20-30 किलोमीटर की छोटी यात्राओं के लिए डिज़ाइन किया गया है, जिसमें अधिकतम 680 किलोग्राम पेलोड के साथ छह यात्री बैठ सकते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Name the dance of Rajasthan which uses a fake horse ride as a prop.</p>",
                    question_hi: "<p>47. राजस्थान के उस नृत्य का नाम बताएं जिसमें टेक के रूप में नकली घोड़े की सवारी का उपयोग किया जाता है।</p>",
                    options_en: [
                        "<p>Pavri Naach</p>",
                        "<p>Dindi and Kala</p>",
                        "<p>Ghode Modni</p>",
                        "<p>Kacchi Ghodi</p>"
                    ],
                    options_hi: [
                        "<p>पवरी नाच</p>",
                        "<p>डिंडी और काला</p>",
                        "<p>घोडे मोदनी</p>",
                        "<p>कच्&zwj;छी घोड़ी</p>"
                    ],
                    solution_en: "<p>47.(d) <strong>Kacchi Ghodi.</strong> Other dances in Rajasthan - Bhavai Folk dance, Chari Folk Dance, Chakri, Fire Folk dance, Gair, Ghoomar, Gawri, Kalbelia, Kathputli Folk Dance, Terah Taali. Folk dances of India - Pavri Nach, Dindi and Kala - Maharashtra; Bagurumba - Assam; Jata-Jatin, Bakho-Bakhain, and Panwariya - Bihar; and Ghode Modini - Goa.</p>",
                    solution_hi: "<p>47.(d) <strong>कच्&zwj;छी घोड़ी।</strong> राजस्थान के अन्य नृत्य - भवई लोक नृत्य, चरी लोक नृत्य, चकरी, अग्नि लोक नृत्य, गैर, घूमर, गवरी, कालबेलिया, कठपुतली लोक नृत्य, तेरह ताली। भारत के लोक नृत्य - पावरी नाच, दिंडी और कला - महाराष्ट्र; बगुरुम्बा - असम; जट-जटिन, बखो-बखैन, और पंवरिया - बिहार; और घोडे मोदिनी - गोवा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Who inaugurated the National Turmeric Board in New Delhi in January 2025?</p>",
                    question_hi: "<p>48. जनवरी 2025 में नई दिल्ली में राष्ट्रीय हल्दी बोर्ड का उद्घाटन किसने किया?</p>",
                    options_en: [
                        "<p>Nirmala Sitharaman</p>",
                        "<p>Narendra Modi</p>",
                        "<p>Piyush Goyal</p>",
                        "<p>Amit Shah</p>"
                    ],
                    options_hi: [
                        "<p>निर्मला सीतारमण</p>",
                        "<p>नरेंद्र मोदी</p>",
                        "<p>पीयूष गोयल</p>",
                        "<p>अमित शाह</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>Piyush Goyal.</strong> Shri Goyal announced Shri Palle Ganga Reddy as its first Chairperson. The headquarters of the Board has been set up at Nizamabad. India is the largest producer, consumer and exporter of turmeric in the world. India has more than 62% share of world trade. During 2023-24, 1.62 lakh tonnes of turmeric and turmeric products valued at 226.5 million USD was exported.</p>",
                    solution_hi: "<p>48.(c) <strong>पीयूष गोयल।</strong> श्री गोयल ने श्री पल्ले गंगा रेड्डी को इसके पहले अध्यक्ष के रूप में घोषित किया। बोर्ड का मुख्यालय निजामाबाद में स्थापित किया गया है। भारत दुनिया में हल्दी का सबसे बड़ा उत्पादक, उपभोक्ता और निर्यातक है। भारत का विश्व व्यापार में 62% से अधिक हिस्सा है। 2023-24 के दौरान, 226.5 मिलियन अमरीकी डॉलर मूल्य की 1.62 लाख टन हल्दी और हल्दी उत्पादों का निर्यात किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Who among the following is the wife of Chandragupta I.</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन चंद्रगुप्त प्रथम की पत्नी है?</p>",
                    options_en: [
                        "<p>Kumarikanya</p>",
                        "<p>Kanyadevi</p>",
                        "<p>Kumaradevi</p>",
                        "<p>Kanyakumari</p>"
                    ],
                    options_hi: [
                        "<p>कुमारीकान्या</p>",
                        "<p>कन्यादेवी</p>",
                        "<p>कुमारदेवी</p>",
                        "<p>कन्याकुमारी</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>Kumaradevi.</strong> Chandragupta I was a Gupta ruler, and his wife belonged to the Lichchhavi clan. He was the first ruler of the Gupta dynasty to adopt the title of Maharajadhiraja. Samudragupta was the son of Chandragupta I and Queen Kumaradevi.</p>",
                    solution_hi: "<p>49.(c) <strong>कुमारदेवी। </strong>चंद्रगुप्त प्रथम, एक गुप्त शासक था और उसकी पत्नी लिच्छवी वंश से थीं। वह महाराजाधिराज की उपाधि धारण करने वाले गुप्त वंश का पहला शासक था। समुद्रगुप्त, चंद्रगुप्त प्रथम और रानी कुमारदेवी के पुत्र थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The dry cell is made up of an outer __________ container that acts as the anode.</p>",
                    question_hi: "<p>50. शुष्क सेल एक बाहरी कंटेनर __________ से बना होता है जो एनोड के रूप में कार्य करता है।</p>",
                    options_en: [
                        "<p>manganese</p>",
                        "<p>lead</p>",
                        "<p>nickel</p>",
                        "<p>zinc</p>"
                    ],
                    options_hi: [
                        "<p>मैंगनीज</p>",
                        "<p>लेड</p>",
                        "<p>निकल</p>",
                        "<p>जिंक</p>"
                    ],
                    solution_en: "<p>50.(d) <strong>zinc.</strong> A dry cell is a type of zinc-carbon battery composed of the following parts : Carbon Rod - Positioned centrally, this rod functions as the cathode. Electrolyte - A paste made of ammonium chloride surrounds the zinc anode, facilitating the flow of ions. Manganese Dioxide Shield - This shield surrounds the cell, helping to improve the efficiency and longevity of the battery.</p>",
                    solution_hi: "<p>50.(d) <strong>जिंक। </strong>शुष्क सेल एक प्रकार की जिंक-कार्बन बैटरी है जो निम्नलिखित भागों से बनी होती है: कार्बन रॉड - केंद्र में स्थित, यह रॉड कैथोड के रूप में कार्य करती है। इलेक्ट्रोलाइट - अमोनियम क्लोराइड से बना पेस्ट जिंक एनोड को घेरता है, जिससे आयनों का प्रवाह सुगम होता है। मैंगनीज डाइऑक्साइड शील्ड - यह शील्ड सेल को घेरती है, जो बैटरी की दक्षता और बैटरी लाइफ में सुधार करने में मदद करती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The angle of elevation of a lamp post changes from 30&deg; to 60&deg; when a person walks 30 m towards it. Find the height of the lamp post.</p>",
                    question_hi: "<p>51. जब कोई व्यक्ति 30 मीटर चलता है तो लैम्प पोस्ट का उन्नयन कोण 30&deg; से 60&deg; तक बदल जाता है। लैम्प पोस्ट की ऊँचाई ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                        "<p>5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                        "<p>15<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;m</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                        "<p>5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                        "<p>15<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;m</p>"
                    ],
                    solution_en: "<p>51.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216841.png\" alt=\"rId43\" width=\"157\" height=\"100\"><br>Height of the lamp post = AB<br>In <math display=\"inline\"><mi>&#916;</mi></math>ABC,<br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>=</mo><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br>&rArr; AB =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>BC<br>In <math display=\"inline\"><mi>&#916;</mi></math>ABD,<br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>C</mi><mo>+</mo><mn>30</mn></mrow></mfrac></math><br>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>=</mo><mfrac><mrow><msqrt><mn>3</mn><mo>&#160;</mo></msqrt><mi>B</mi><mi>C</mi></mrow><mrow><mi>B</mi><mi>C</mi><mo>+</mo><mn>30</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>BC + 30 = 3 BC<br>&rArr; BC = 15 m<br>AB = 15<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>m</mi></math></p>",
                    solution_hi: "<p>51.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216841.png\" alt=\"rId43\" width=\"157\" height=\"100\"><br>लैम्प पोस्ट की ऊँचाई = AB<br><math display=\"inline\"><mi>&#916;</mi></math>ABC में,<br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>=</mo><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br>&rArr; AB =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>BC<br><math display=\"inline\"><mi>&#916;</mi></math>ABD में,<br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>C</mi><mo>+</mo><mn>30</mn></mrow></mfrac></math><br>&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>=</mo><mfrac><mrow><msqrt><mn>3</mn><mo>&#160;</mo></msqrt><mi>B</mi><mi>C</mi></mrow><mrow><mi>B</mi><mi>C</mi><mo>+</mo><mn>30</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>BC + 30 = 3 BC<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>BC</mi><mo>=</mo><mn>15</mn><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2368;&#2335;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi></math><br>AB = 15<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2368;&#2335;&#2352;</mi></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The simple interest on a certain sum of money at rate of interest 6% per annum for 2 years is Rs. 960. What is the compound interest (compounding annually) on the same sum for the same period and at the same rate of interest ?</p>",
                    question_hi: "<p>52. एक निश्चित धनराशि पर 6% वार्षिक की ब्याज दर पर, 2 वर्षों के लिए साधारण ब्याज 960 रुपए है। समान अवधि और समान ब्याज दर पर समान धनराशि पर चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) कितना होगा?</p>",
                    options_en: [
                        "<p>Rs. 985.4</p>",
                        "<p>Rs. 988.8</p>",
                        "<p>Rs.1122.2</p>",
                        "<p>Rs.1025.1</p>"
                    ],
                    options_hi: [
                        "<p>985.4 रुपए</p>",
                        "<p>988.8 रुपए</p>",
                        "<p>1122.2 रुपए</p>",
                        "<p>1025.1 रुपए</p>"
                    ],
                    solution_en: "<p>52.(b)<br>Let principal be 100%<br>6% &times; 2 = 12% -------------- ₹960<br>100% -------------- <math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>&times;100 = ₹8,000<br>Successive rate for 2 years = 6 + 6 + <math display=\"inline\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 12.36%<br>CI on the same sum for the same period at the same rate = 8000&times;<math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>53</mn></mrow><mrow><mn>50</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - 8,000 = 8000 &times; 12.36% = ₹ 988.8.</p>",
                    solution_hi: "<p>52.(b)<br>मान लीजिए मूलधन 100% है<br>6% &times; 2 = 12% -------------- ₹960<br>100% -------------- <math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 100 = ₹8,000<br>2 वर्षों के लिए क्रमिक दर = 6 + 6 + <math display=\"inline\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 12.36%<br>समान राशि पर समान अवधि के लिए समान दर पर चक्रवृद्धि ब्याज = 8000&times;<math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>53</mn></mrow><mrow><mn>50</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - 8,000 = 8000 &times; 12.36% = ₹ 988.8.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. A boat can travel 16.9 km downstream in 52 min. If the speed of the current is 3 km/h, then how much time (in hours) will the boat take to travel 84 km upstream ?</p>",
                    question_hi: "<p>53. एक नाव 52 min में धारा के अनुकूल 16.9 km की यात्रा कर सकती है। यदि धारा की गति 3 km/h है, तो नाव को धारा के प्रतिकूल 84 km की दूरी तय करने में कितना समय (घंटों में) लगेगा ?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>13.5</p>",
                        "<p>6.22</p>",
                        "<p>7.5</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>13.5</p>",
                        "<p>6.22</p>",
                        "<p>7.5</p>"
                    ],
                    solution_en: "<p>53.(c)<br>Downstream speed = <math display=\"inline\"><mfrac><mrow><mn>16</mn><mo>.</mo><mn>9</mn></mrow><mrow><mfrac><mrow><mn>52</mn></mrow><mrow><mn>60</mn></mrow></mfrac></mrow></mfrac></math> = 19.5 km/h<br>Speed of (boat + current) = 19.5<br>Speed of boat = 19.5 - 3 = 16.5 km/h<br>Time taken upstream = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mrow><mn>16</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>84</mn><mrow><mn>13</mn><mo>.</mo><mn>5</mn></mrow></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>84</mn><mrow><mn>13</mn><mo>.</mo><mn>5</mn></mrow></mfrac></mstyle></math> = 6.22</p>",
                    solution_hi: "<p>53.(c)<br>धारा के अनुकूल गति = <math display=\"inline\"><mfrac><mrow><mn>16</mn><mo>.</mo><mn>9</mn></mrow><mrow><mfrac><mrow><mn>52</mn></mrow><mrow><mn>60</mn></mrow></mfrac></mrow></mfrac></math> = 19.5 km/h<br>(नाव + धारा) की गति = 19.5<br>नाव की गति = 19.5 - 3 = 16.5 km/h<br>धारा के प्रतिकूल लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>16</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>84</mn><mrow><mn>13</mn><mo>.</mo><mn>5</mn></mrow></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>84</mn><mrow><mn>13</mn><mo>.</mo><mn>5</mn></mrow></mfrac></mstyle></math> = 6.22</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. A man has to discharge a debt of ₹15,600 which is due in 3 years at 4% simple interest per annum. If he pays this amount in equal installments of annual payment, find the amount for annual payment.</p>",
                    question_hi: "<p>54. एक आदमी को ₹15,600 का कर्ज चुकाना है जो कि 3 साल में 4% वार्षिक साधारण ब्याज पर देय है। यदि वह इस राशि का भुगतान वार्षिक भुगतान की समान किश्तों में करता है, तो वार्षिक भुगतान की राशि ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹5,200</p>",
                        "<p>₹5,100</p>",
                        "<p>₹5,400</p>",
                        "<p>₹5,000</p>"
                    ],
                    options_hi: [
                        "<p>₹5,200</p>",
                        "<p>₹5,100</p>",
                        "<p>₹5,400</p>",
                        "<p>₹5,000</p>"
                    ],
                    solution_en: "<p>54.(d) Rate = 4% = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>&#8594;</mo><mi>I</mi><mi>n</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>s</mi><mi>t</mi></mrow><mrow><mn>25</mn><mo>&#8594;</mo><mi>&#160;</mi><mi>I</mi><mi>n</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>l</mi><mi>m</mi><mi>e</mi><mi>n</mi><mi>t</mi></mrow></mfrac></math><br>Debt = 25 &times; 3 + 1(2 + 1) = 75 + 3 = 78 unit<br>Now, 78 unit = ₹15,600<br>Then, 25 unit = <math display=\"inline\"><mfrac><mrow><mn>15600</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math> &times; 25 = ₹5,000</p>",
                    solution_hi: "<p>54.(d) दर = 4% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#8594;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2348;&#2381;&#2351;&#2366;&#2332;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>25</mn><mo>&#8594;</mo><mi>&#2325;&#2367;&#2360;&#2381;&#2340;</mi></mrow></mfrac></math><br>ऋण = 25 &times; 3 +1(2 + 1) = 75 + 3 = 78 इकाई <br>अब , 78 इकाई = ₹15,600<br>, 25 इकाई = <math display=\"inline\"><mfrac><mrow><mn>15600</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math> &times; 25 = ₹5,000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If the intersection point of the equations 3p &minus; 2q + 23 = 0 and &minus;2p + q &minus; 13 = 0 on the&nbsp;graph is (p, q), then the value of (3p + q) is equal to:</p>",
                    question_hi: "<p>55. यदि ग्राफ पर समीकरण 3p &minus; 2q + 23 = 0 और &minus;2p + q &minus; 13 = 0 का प्रतिच्छेदन बिंदु (p, q) है, तो&nbsp;(3p + q) का मान ___ होगा।</p>",
                    options_en: [
                        "<p>-1</p>",
                        "<p>-2</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>-1</p>",
                        "<p>-2</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>55.(b)<br>3p &minus; 2q + 23 = 0 -------(i)<br>&minus;2p + q &minus; 13 = 0 -------(ii)<br>Equation (i) + 2 &times; equation(ii) we get;<br>3p &minus; 2q + 23 + 2(&minus;2p + q &minus; 13) = 0<br>- p - 3 = 0<br>p = - 3<br>Put, p = - 3 in equation (i) we get;<br>3(-3) &minus; 2q + 23 = 0<br>&minus; 2q = -14<br>q = 7<br>Then, (3p + q) = 3 &times; - 3 + 7 = - 2</p>",
                    solution_hi: "<p>55.(b)<br>3p &minus; 2q + 23 = 0 -------(i)<br>&minus;2p + q &minus; 13 = 0 -------(ii)<br>समीकरण (i) + 2 &times; समीकरण (ii) हमें मिलता है;<br>3p &minus; 2q + 23 + 2(&minus;2p + q &minus; 13) = 0<br>- p - 3 = 0<br>p = - 3<br>समीकरण (i) में p = - 3 रखने पर हमें प्राप्त होता है;<br>3(-3) &minus; 2q + 23 = 0<br>&minus; 2q = -14<br>q = 7<br>तब , (3p + q) = 3 &times; - 3 + 7 = - 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. The number 611611611611 is:<br>(a) divisible by both 6 and 11<br>(b) neither divisible by 6 nor by 11<br>(c) divisible by 6 only<br>(d) divisible by 11 only</p>",
                    question_hi: "<p>56. संख्या 611611611611 _________ है।<br>(a) 6 और 11 दोनों से विभाज्य<br>(b) न तो 6 सेऔर न ही 11 सेविभाज्य<br>(c) केवल 6 से विभाज्य<br>(d) केवल 11 से विभाज्य</p>",
                    options_en: [
                        "<p>(b)</p>",
                        "<p>(d)</p>",
                        "<p>(a)</p>",
                        "<p>(c)</p>"
                    ],
                    options_hi: [
                        "<p>(b)</p>",
                        "<p>(d)</p>",
                        "<p>(a)</p>",
                        "<p>(c)</p>"
                    ],
                    solution_en: "<p>56.(b)<br>611611611611<br>As per question check divisibility by 6 and 11<br>For divisibility of 6 it must be divisible by 2 and 3<br>611611611611 is not divisible by 2 hence it is not divisible by 6 also<br>now , for divisibility of 11 , sum of alternate digits<br>1 + 6 + 1 + 1 + 6 + 1 = 16<br>6 + 1 + 1 + 6 + 1 + 1 = 16<br>Difference of sum of alternate digits = 16 - 16 = 0<br>Hence it is divisible by 11 only.</p>",
                    solution_hi: "<p>56.(b)<br>611611611611<br>प्रश्न के अनुसार 6 और 11 से विभाज्यता की जाँच करें<br>6 की विभाज्यता के लिए इसे 2 और 3 से विभाज्य होना चाहिए<br>611611611611 , 2 से विभाज्य नहीं है इसलिए यह 6 से भी विभाज्य नहीं है<br>अब, 11 की विभाज्यता के लिए, एकान्तर अंकों का योग<br>1 + 6 + 1 + 1 + 6 + 1 = 16<br>6 + 1 + 1 + 6 + 1 + 1 = 16<br>एकान्तर अंकों के योग का अंतर = 16 - 16 = 0<br>अतः यह केवल 11 से विभाज्य है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Ajay spends 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% of his income on different household items. If his income increases by 25% and expenditure increase by 20%, then the effect on his savings will be:</p>",
                    question_hi: "<p>57. अजय अपनी आय का 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% विभिन्न घरेलू मदों पर खर्च करता है। यदि उसकी आय 25% बढ़ जाती है और व्यय 20% बढ़ जाता है, तो उसकी बचत पर कितना प्रभाव पड़ेगा?</p>",
                    options_en: [
                        "<p>28%</p>",
                        "<p>61%</p>",
                        "<p>49%</p>",
                        "<p>35%</p>"
                    ],
                    options_hi: [
                        "<p>28%</p>",
                        "<p>61%</p>",
                        "<p>49%</p>",
                        "<p>35%</p>"
                    ],
                    solution_en: "<p>57.(d) 66<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math><br>Let the initial income of Ajay be 300<br>Ratio -&nbsp; &nbsp; initial : final <br>Income -&nbsp; &nbsp;300 : 375<br>Expendi. - 200 : 240<br>--------------------------------------<br>Savings -&nbsp; 100 : 135<br>Effect on saving (increased) = <math display=\"inline\"><mfrac><mrow><mn>135</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 35%</p>",
                    solution_hi: "<p>57.(d) 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math><br>माना अजय की प्रारंभिक आय 300 है<br>अनुपात - प्रारंभिक : अंतिम<br>&nbsp; आय&nbsp; &nbsp;-&nbsp; &nbsp;300&nbsp; &nbsp; &nbsp;:&nbsp; 375<br>&nbsp; &nbsp;व्यय&nbsp; -&nbsp; &nbsp;200&nbsp; &nbsp; &nbsp;:&nbsp; 240<br>------------------------------------------<br>बचत - 100 : 135<br>बचत पर प्रभाव (बढ़ा हुआ) = <math display=\"inline\"><mfrac><mrow><mn>135</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 35%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Scales, pencils and erasers are available in packets of 6, 10 and 12 respectively. If a shopkeeper wants to buy equal number of these items, then what is the minimum number of packets of scales, pencils and erasers respectively he needs to buy ?</p>",
                    question_hi: "<p>58. स्केल, पैन्सिल तथा रबड़ क्रमशः 6, 10 तथा 12 के पैकेटों में उपलब्ध हैं। यदि कोई दुकानदार तीनों वस्तुएँ समान संख्या में खरीदना चाहता है, तो उसे स्केल, पैन्सिल तथा रबड़ के क्रमशः कम-से-कम कितने पैकेट खरीदने होंगे ?</p>",
                    options_en: [
                        "<p>9, 7, 5</p>",
                        "<p>8, 8, 8</p>",
                        "<p>10, 6, 5</p>",
                        "<p>6, 5, 10</p>"
                    ],
                    options_hi: [
                        "<p>9, 7, 5</p>",
                        "<p>8, 8, 8</p>",
                        "<p>10, 6, 5</p>",
                        "<p>6, 5, 10</p>"
                    ],
                    solution_en: "<p>58.(c)<br>According to question,<br>LCM (6, 10, 12) = 60<br>Number of packets of scales = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 10<br>Number of packets of pencil = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 6<br>Number of packets of erasers = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5</p>",
                    solution_hi: "<p>58.(c)<br>प्रश्न के अनुसार,<br>LCM (6, 10, 12) = 60<br>स्केल के पैकेटों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 10<br>पेंसिल के पैकेटों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 6<br>रबड़ के पैकेटों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Simplify the given expression.<br>[{9 &times; 81 + (142 &divide; 2)} + 350 -155 + 5] &divide; [(25 &times; 25) - 125]</p>",
                    question_hi: "<p>59. दिए गए व्यंजक को सरल कीजिए।<br>[{9 &times; 81 + (142 &divide; 2)} +350 -155 + 5] &divide; [(25 &times; 25) - 125]</p>",
                    options_en: [
                        "<p>-1</p>",
                        "<p>2</p>",
                        "<p>-2</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>-1</p>",
                        "<p>2</p>",
                        "<p>-2</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>59.(b)<br>[{9 &times; 81 + (142 &divide; 2)} +350 -155 + 5] &divide; [(25 &times; 25) - 125]<br>[{729 + 71} + 200] &divide; [625 - 125]<br>[800 + 200] &divide; 500<br>1000 &divide; 500 = 2</p>",
                    solution_hi: "<p>59.(b)<br>[{9 &times; 81 + (142 &divide; 2)} +350 -155 + 5] &divide; [(25 &times; 25) - 125]<br>[{729 + 71} + 200] &divide; [625 - 125]<br>[800 + 200] &divide; 500<br>1000 &divide; 500 = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Find the value of:<br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> tan&sup2;60&deg; + 3 cos&sup2;30&deg; - 2 sec&sup2;30&deg; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> cot&sup2;60&deg;] &divide; [sin 60&deg; cos 30&deg; - cos 60&deg; sin 30&deg;]</p>",
                    question_hi: "<p>60. [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> tan&sup2;60&deg; + 3 cos&sup2;30&deg; - 2 sec&sup2;30&deg; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> cot&sup2;60&deg;] &divide; [sin 60&deg; cos 30&deg; - cos 60&deg; sin 30&deg;] का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>6<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>6<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>60.(a) <br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> tan&sup2;60&deg; + 3 cos&sup2;30&deg; - 2 sec&sup2;30&deg; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> cot&sup2;60&deg;] &divide; [sin 60&deg; cos 30&deg; - cos 60&deg; sin 30&deg;]<br>[<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)&sup2; + 3 &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mstyle></math>)&sup2;- 2 &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math>)&sup2; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math>)&sup2;] &divide; sin(60&deg;- 30&deg;)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mn>4</mn><mo>+</mo><mfrac><mn>9</mn><mn>4</mn></mfrac><mo>-</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>]</mo></math> &divide; sin30&deg;<br><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>20</mn><mn>3</mn></mfrac></mstyle></math>&nbsp;= 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>60.(a) <br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> tan&sup2;60&deg; + 3 cos&sup2;30&deg; - 2 sec&sup2;30&deg; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> cot&sup2;60&deg;] &divide; [sin 60&deg; cos 30&deg; - cos 60&deg; sin 30&deg;]<br>[<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)&sup2; + 3 &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mstyle></math>)&sup2;- 2 &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math>)&sup2; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math>)&sup2;] &divide; sin(60&deg;- 30&deg;)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mo>[</mo><mn>4</mn><mo>+</mo><mfrac><mn>9</mn><mn>4</mn></mfrac><mo>-</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>]</mo></mstyle></math>&nbsp;&divide; sin30&deg;<br><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>20</mn><mn>3</mn></mfrac></mstyle></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. One-quarter of a circular pizza of radius 21 cm was removed from the whole pizza. What is the perimeter (in cm) of the remaining pizza? (Use &pi; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>61. 21 cm त्रिज्या वाले वृत्ताकार पिज्जा का एक-चौथाई भाग, संपूर्ण पिज्जा से निकाल दिया जाता है। शेष पिज्जा का परिमाप (cm में) कितना होगा? (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>) का उपयोग कीजिए।</p>",
                    options_en: [
                        "<p>99</p>",
                        "<p>141</p>",
                        "<p>128</p>",
                        "<p>131</p>"
                    ],
                    options_hi: [
                        "<p>99</p>",
                        "<p>141</p>",
                        "<p>128</p>",
                        "<p>131</p>"
                    ],
                    solution_en: "<p>61.(b) Remaining perimeter of pizza = 2<math display=\"inline\"><mi>&#960;</mi></math>r &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>270</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></mstyle></math> + 2r<br>= 2 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> + 2 &times; 21<br>= 99 + 42 = 141 cm</p>",
                    solution_hi: "<p>61.(b) पिज़्ज़ा का शेष परिमाप = 2<math display=\"inline\"><mi>&#960;</mi></math>r &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>270</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></mstyle></math> + 2r<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 21 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> + 2 &times; 21<br>= 99 + 42 = 141 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Two chords AB and CD of a circle are produced to intersect each other at a point P&nbsp;outside the circle. If AB = 7 cm, BP = 4.2 cm and PD = 2.8 cm, then the length of CD is:</p>",
                    question_hi: "<p>62. एक वृत्त की दो जीवाएँ AB और CD, एक दूसरे को वृत्त के बाहर, बिंदु P पर प्रतिच्छेद करने के लिए&nbsp;बढ़ायी जाती हैं। यदि AB = 7 cm, BP = 4.2 cm और PD = 2.8 cm है, तो CD की लंबाई ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>16 cm</p>",
                        "<p>18 cm</p>",
                        "<p>14 cm</p>",
                        "<p>11 cm</p>"
                    ],
                    options_hi: [
                        "<p>16 cm</p>",
                        "<p>18 cm</p>",
                        "<p>14 cm</p>",
                        "<p>11 cm</p>"
                    ],
                    solution_en: "<p>62.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216979.png\" alt=\"rId44\" width=\"214\" height=\"110\"><br>Using intersecting secant theorem, <br>BP&times;AP = DP &times; CP<br>4.2 &times; 11.2 = 2.8 &times; (2.8+x)<br><math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math> = (2.8+x)<br>16.8 = 2.8+x<br>x = 16.8 - 2.8 = 14 cm</p>",
                    solution_hi: "<p>62.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017216979.png\" alt=\"rId44\" width=\"214\" height=\"110\"><br>प्रतिच्छेदी सेकेंट प्रमेय(intersecting secant theorem) का उपयोग करते हुए, <br>BP &times; AP = DP &times; CP<br>4.2 &times; 11.2 = 2.8 &times; (2.8 + x)<br><math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math> = (2.8+x)<br>16.8 = 2.8 + x<br>x = 16.8 - 2.8 = 14 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Which of the following can be the value of k, if <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>88</mn><mo>&#247;</mo><mn>8</mn><mo>&#215;</mo><mi mathvariant=\"normal\">k</mi><mo>-</mo><mn>3</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn></mrow><mn>2</mn></msup><mo>-</mo><mn>7</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math> = 1 ?</p>",
                    question_hi: "<p>63. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>88</mn><mo>&#247;</mo><mn>8</mn><mo>&#215;</mo><mi>k</mi><mo>-</mo><mn>3</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn></mrow><mn>2</mn></msup><mo>-</mo><mn>7</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math> = 1 है तो निम्नलिखित में से k का मान क्या हो सकता है?</p>",
                    options_en: [
                        "<p>1,10</p>",
                        "<p>4,7</p>",
                        "<p>3,10</p>",
                        "<p>2,7</p>"
                    ],
                    options_hi: [
                        "<p>1,10</p>",
                        "<p>4,7</p>",
                        "<p>3,10</p>",
                        "<p>2,7</p>"
                    ],
                    solution_en: "<p>63.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>88</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>8</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">k</mi><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold\">&#160;</mi><mn>3</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>3</mn></mrow><mrow><msup><mn>6</mn><mn>2</mn></msup><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold\">&#160;</mi><mn>7</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>5</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><msup><mi mathvariant=\"bold\">k</mi><mn>2</mn></msup></mrow></mfrac></math> = 1<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>11</mn><mo mathvariant=\"bold\">&#160;</mo><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">k</mi><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mo mathvariant=\"bold\">&#160;</mo><mn>9</mn><mo mathvariant=\"bold\">&#160;</mo></mrow><mrow><mn>36</mn><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mo mathvariant=\"bold\">&#160;</mo><mn mathvariant=\"bold\">3</mn><mn>5</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><msup><mi mathvariant=\"bold\">k</mi><mn>2</mn></msup></mrow></mfrac></mstyle></math> = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> 11k - 9 = 1 + k&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> k&sup2; - 11k + 10 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k&sup2; - 10k - k + 10 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k(k - 10) - 1(k - 10) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (k - 10)(k - 1) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k = 10 and 1</p>",
                    solution_hi: "<p>63.(a) <math display=\"inline\"><mfrac><mrow><mn>88</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">k</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn></mrow><mrow><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>7</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">k</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = 1<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>11</mn><mo mathvariant=\"bold\">&#160;</mo><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">k</mi><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mo mathvariant=\"bold\">&#160;</mo><mn>9</mn><mo mathvariant=\"bold\">&#160;</mo></mrow><mrow><mn>36</mn><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mo mathvariant=\"bold\">&#160;</mo><mn mathvariant=\"bold\">3</mn><mn>5</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><msup><mi mathvariant=\"bold\">k</mi><mn>2</mn></msup></mrow></mfrac></mstyle></math> = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> 11k - 9 = 1 + k&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> k&sup2; - 11k + 10 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k&sup2; - 10k - k + 10 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k(k - 10) - 1(k - 10) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (k - 10)(k - 1) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k = 10 and 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. When &lsquo;X&rsquo; is added to each of the numbers 7, 11, 18 and 23, then the number so obtained are in proportion. What is the mean proportional between the (x - 1) and (2x -10) ?</p>",
                    question_hi: "<p>64. जब संख्या 7, 11, 18 और 23 में प्रत्येक में &lsquo;X&rsquo; जोड़ा जाता है, तो इस प्रकार प्राप्त संख्याएं समानुपात में होती है। (x - 1) और (2x - 10) का माध्यानुपाती क्या है?</p>",
                    options_en: [
                        "<p>78</p>",
                        "<p>98</p>",
                        "<p>48</p>",
                        "<p>68</p>"
                    ],
                    options_hi: [
                        "<p>78</p>",
                        "<p>98</p>",
                        "<p>48</p>",
                        "<p>68</p>"
                    ],
                    solution_en: "<p>64.(c)<br>Value of <math display=\"inline\"><mi>x</mi></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">d</mi><mo mathvariant=\"normal\">-</mo><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">c</mi></mrow><mrow><mo mathvariant=\"normal\">(</mo><mi mathvariant=\"normal\">a</mi><mo mathvariant=\"normal\">+</mo><mi mathvariant=\"normal\">d</mi><mo mathvariant=\"normal\">)</mo><mo mathvariant=\"normal\">-</mo><mo mathvariant=\"normal\">(</mo><mi mathvariant=\"normal\">b</mi><mo mathvariant=\"normal\">+</mo><mi mathvariant=\"normal\">c</mi><mo mathvariant=\"normal\">)</mo></mrow></mfrac><mo stretchy=\"true\">|</mo></mstyle><mo>=</mo><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mfrac><mrow><mn mathvariant=\"normal\">7</mn><mo mathvariant=\"normal\">&#215;</mo><mn mathvariant=\"normal\">23</mn><mo mathvariant=\"normal\">-</mo><mn mathvariant=\"normal\">11</mn><mo mathvariant=\"normal\">&#215;</mo><mn mathvariant=\"normal\">18</mn></mrow><mrow><mo mathvariant=\"normal\">(</mo><mn mathvariant=\"normal\">7</mn><mo mathvariant=\"normal\">+</mo><mn mathvariant=\"normal\">23</mn><mo mathvariant=\"normal\">)</mo><mo mathvariant=\"normal\">-</mo><mo mathvariant=\"normal\">(</mo><mn mathvariant=\"normal\">11</mn><mo mathvariant=\"normal\">+</mo><mn mathvariant=\"normal\">18</mn><mo mathvariant=\"normal\">)</mo></mrow></mfrac><mo stretchy=\"true\">|</mo></mstyle><mo>=</mo><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mfrac><mrow><mn mathvariant=\"normal\">161</mn><mo mathvariant=\"normal\">-</mo><mn mathvariant=\"normal\">198</mn></mrow><mrow><mo mathvariant=\"normal\">(</mo><mn mathvariant=\"normal\">30</mn><mo mathvariant=\"normal\">)</mo><mo mathvariant=\"normal\">-</mo><mo mathvariant=\"normal\">(</mo><mn mathvariant=\"normal\">29</mn><mo mathvariant=\"normal\">)</mo></mrow></mfrac><mo stretchy=\"true\">|</mo></mstyle><mo>=</mo><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mfrac><mrow><mo mathvariant=\"normal\">-</mo><mn mathvariant=\"normal\">37</mn></mrow><mn mathvariant=\"normal\">1</mn></mfrac><mo stretchy=\"true\">|</mo></mstyle><mo>=</mo><mn>37</mn></math></p>\n<p>Then, mean proportion of (<math display=\"inline\"><mi>x</mi></math> - 1) and (2x -10) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi>x</mi><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>10</mn><mo>)</mo></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>37</mn><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>74</mn><mo>-</mo><mn>10</mn><mo>)</mo></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>36</mn><mo>&#215;</mo><mn>64</mn></msqrt></math> = 6 &times; 8 = 48</p>",
                    solution_hi: "<p>64.(c)<br><math display=\"inline\"><mi>x</mi></math> का मान = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">d</mi><mo mathvariant=\"normal\">-</mo><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">c</mi></mrow><mrow><mo mathvariant=\"normal\">(</mo><mi mathvariant=\"normal\">a</mi><mo mathvariant=\"normal\">+</mo><mi mathvariant=\"normal\">d</mi><mo mathvariant=\"normal\">)</mo><mo mathvariant=\"normal\">-</mo><mo mathvariant=\"normal\">(</mo><mi mathvariant=\"normal\">b</mi><mo mathvariant=\"normal\">+</mo><mi mathvariant=\"normal\">c</mi><mo mathvariant=\"normal\">)</mo></mrow></mfrac><mo stretchy=\"true\">|</mo></mstyle><mo>=</mo><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mfrac><mrow><mn mathvariant=\"normal\">7</mn><mo mathvariant=\"normal\">&#215;</mo><mn mathvariant=\"normal\">23</mn><mo mathvariant=\"normal\">-</mo><mn mathvariant=\"normal\">11</mn><mo mathvariant=\"normal\">&#215;</mo><mn mathvariant=\"normal\">18</mn></mrow><mrow><mo mathvariant=\"normal\">(</mo><mn mathvariant=\"normal\">7</mn><mo mathvariant=\"normal\">+</mo><mn mathvariant=\"normal\">23</mn><mo mathvariant=\"normal\">)</mo><mo mathvariant=\"normal\">-</mo><mo mathvariant=\"normal\">(</mo><mn mathvariant=\"normal\">11</mn><mo mathvariant=\"normal\">+</mo><mn mathvariant=\"normal\">18</mn><mo mathvariant=\"normal\">)</mo></mrow></mfrac><mo stretchy=\"true\">|</mo></mstyle><mo>=</mo><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mfrac><mrow><mn mathvariant=\"normal\">161</mn><mo mathvariant=\"normal\">-</mo><mn mathvariant=\"normal\">198</mn></mrow><mrow><mo mathvariant=\"normal\">(</mo><mn mathvariant=\"normal\">30</mn><mo mathvariant=\"normal\">)</mo><mo mathvariant=\"normal\">-</mo><mo mathvariant=\"normal\">(</mo><mn mathvariant=\"normal\">29</mn><mo mathvariant=\"normal\">)</mo></mrow></mfrac><mo stretchy=\"true\">|</mo></mstyle><mo>=</mo><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mfrac><mrow><mo mathvariant=\"normal\">-</mo><mn mathvariant=\"normal\">37</mn></mrow><mn mathvariant=\"normal\">1</mn></mfrac><mo stretchy=\"true\">|</mo></mstyle><mo>=</mo><mn>37</mn></math></p>\n<p>तब, अभीष्ट माध्य =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi>x</mi><mo>-</mo><mn>1</mn><mo>)</mo><mi>&#160;</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>10</mn><mo>)</mo></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>37</mn><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>74</mn><mo>-</mo><mn>10</mn><mo>)</mo></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>36</mn><mo>&#215;</mo><mn>64</mn></msqrt></math> = 6 &times; 8 = 48</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A person mixes two liquids, x and y. One litre of x weighs approximately 900 g, and one litre of y weighs approximately 750 g. If one litre of the mixture weighs approximately 800 g, then the percentage of x in the mixture is:</p>",
                    question_hi: "<p>65. एक व्यक्ति दो तरल पदार्थ x और y मिलाता है। एक लीटर x का वजन लगभग 900 g है, और एक लीटर y का वजन लगभग 750 g है। यदि एक लीटर मिश्रण का वजन लगभग 800 g है, तो मिश्रण में x का कितना प्रतिशत होगा?</p>",
                    options_en: [
                        "<p>30%</p>",
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>50%</p>",
                        "<p>27<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>"
                    ],
                    options_hi: [
                        "<p>30%</p>",
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>50%</p>",
                        "<p>27<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>"
                    ],
                    solution_en: "<p>65.(b)<br><strong id=\"docs-internal-guid-94be6bd0-7fff-59ff-dd87-b69f547e89d2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdlmJebMrLgYEcMzU26D2k-nJySuxsEZC_CFCw-8W93_DO7yUUr6Ut0fyIkuq0WWrvngIo8CmymWhmvL2-0OBz8tbWn_2a0vWujQ5P4TbSWij5otd4bZWhxH2mpq2Ct7wUZ8ljfqw?key=olxogH1gMHElKS8ywQQSLb7L\" width=\"195\" height=\"121\"></strong><br><math display=\"inline\"><mo>&#8658;</mo></math>ratio of x and y = 1 : 2<br>Hence, <br>Percentage of x&nbsp;in the mixture = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 100 = 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> %</p>",
                    solution_hi: "<p>65.(b)<br><strong id=\"docs-internal-guid-94be6bd0-7fff-59ff-dd87-b69f547e89d2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdlmJebMrLgYEcMzU26D2k-nJySuxsEZC_CFCw-8W93_DO7yUUr6Ut0fyIkuq0WWrvngIo8CmymWhmvL2-0OBz8tbWn_2a0vWujQ5P4TbSWij5otd4bZWhxH2mpq2Ct7wUZ8ljfqw?key=olxogH1gMHElKS8ywQQSLb7L\" width=\"207\" height=\"128\"></strong><br><math display=\"inline\"><mo>&#8658;</mo></math>x और y का अनुपात = 1 : 2<br>इस तरह, <br>मिश्रण में x&nbsp;का प्रतिशत =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 100 = 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Two rectangular sheets of paper, each 60 cm &times; 36 cm, are made into two right circular cylinders, one by rolling the paper along its length and the other along the breadth. The ratio of the volumes of the two cylinders, thus formed, is:</p>",
                    question_hi: "<p>66. कागज के दो आयताकार शीटों से, जिनमें से प्रत्येक 60 cm &times; 36 cm के हैं, से एक शीट को उसकी लंबाई के अनुदिश और दूसरे शीट को उसकी चौड़ाई के अनुदिश मोड़कर दो लंब वृत्तीय बेलन बनाए जाते हैं। इस प्रकार बने दोनों बेलनों के आयतन का अनुपात ज्ञात करें।</p>",
                    options_en: [
                        "<p>5 : 6</p>",
                        "<p>8 : 3</p>",
                        "<p>7 : 4</p>",
                        "<p>5 : 3</p>"
                    ],
                    options_hi: [
                        "<p>5 : 6</p>",
                        "<p>8 : 3</p>",
                        "<p>7 : 4</p>",
                        "<p>5 : 3</p>"
                    ],
                    solution_en: "<p>66.(d)<br><strong>Case 1:</strong> When the sheet of paper is rolled along its length to form a cylinder, <br>then the circumference of the base of the cylinder = length of the rectangular sheet <br>height of the cylinder = breadth of the rectangular sheet.<br>Let <math display=\"inline\"><mi>r</mi></math>adius = r and height = h = 36 cm<br>Circumference of base = 60 cm<br>2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi></math> = 60 cm<br><math display=\"inline\"><mi>r</mi></math> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math>cm<br>Volume of the cylinder formed (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>) = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math> = &pi; &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math>&times; 36&hellip;&hellip;. (i) .<br><strong>Case 2: </strong>When the sheet of paper is rolled along its breadth to form a cylinder, then the circumference of the base of the cylinder = the breadth of the rectangular sheet <br>height of the cylinder = length of the rectangular sheet.<br>Let radius = <math display=\"inline\"><mi>R</mi></math> and height = H = 60 cm<br>Circumference of base = 36 cm<br>2<math display=\"inline\"><mi>&#960;</mi><mi>R</mi></math> = 36 cm<br>R = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mi>&#960;</mi></mrow></mfrac></math> cm<br>Volume of the cylinder formed (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>) = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>H</mi></math> = &pi; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mi>&#960;</mi></mfrac></mstyle></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mi>&#960;</mi></mfrac></mstyle></math>&times; 60 .....(ii)<br>Now,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>&nbsp;= <math display=\"inline\"><mi>&#960;</mi></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math>&times; 36 : &pi; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mi>&#960;</mi></mfrac></mstyle></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mi>&#960;</mi></mfrac></mstyle></math> &times; 60<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>&nbsp;= 5 : 3<br>Thus, the ratio of the volumes of the two cylinders so formed is 5 : 3.</p>",
                    solution_hi: "<p>66.(d)<br><strong>Case 1</strong>: जब एक बेलन बनाने के लिए कागज की शीट को उसकी लंबाई के अनुदिश घुमाया जाता है, <br>तो बेलन के आधार की परिधि = आयताकार शीट की लंबाई <br>बेलन की ऊँचाई = आयताकार शीट की चौड़ाई।<br>माना त्रिज्या = r और ऊँचाई = h = 36cm<br>आधार की परिधि = 60 cm<br>2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi></math> = 60 cm<br><math display=\"inline\"><mi>r</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math> cm<br>बेलन का आयतन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>) =&nbsp;<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math> = &pi; &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math> &times; 36&hellip;&hellip;. (i) .<br><strong>Case 2:</strong> जब एक बेलन बनाने के लिए कागज की शीट को उसकी चौड़ाई के अनुदिश घुमाया जाता है, तो बेलन के आधार की परिधि = आयताकार शीट की चौड़ाई <br>बेलन की ऊंचाई = आयताकार शीट की लंबाई।<br>माना त्रिज्या = R और ऊँचाई = H = 60 cm<br>आधार की परिधि = 36 cm<br>2<math display=\"inline\"><mi>&#960;</mi><mi>R</mi></math> = 36 cm<br>R = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mi>&#960;</mi></mrow></mfrac></math> cm<br>सिलेंडर का आयतन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>) =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;R</mi><mn>2</mn></msup><mi mathvariant=\"normal\">H</mi></math> = &pi; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mi>&#960;</mi></mfrac></mstyle></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mi>&#960;</mi></mfrac></mstyle></math> &times; 60 .....(ii)<br>अब,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>&nbsp;= <math display=\"inline\"><mi>&#960;</mi></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mi>&#960;</mi></mfrac></mstyle></math>&times; 36 : &pi; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mi>&#960;</mi></mfrac></mstyle></math>&times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mi>&#960;</mi></mfrac></mstyle></math> &times; 60<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>&nbsp;= 5 : 3<br>इस प्रकार बने दोनों सिलेंडरों के आयतन का अनुपात 5 : 3 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A can complete a work in 7 hours, B and C can complete it in <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> hours, and C and A can complete it in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></math>hours. How much time will B take to complete the work alone ?</p>",
                    question_hi: "<p>67. A एक काम को 7 घंटे में पूरा कर सकता है, B और C इसे <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे में पूरा कर सकते हैं, और C और A इसे&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></math> घंटे में पूरा कर सकते हैं। B को अकेले इस काम को पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>22 hours</p>",
                        "<p>20 hours</p>",
                        "<p>21 hours</p>",
                        "<p>24 hours</p>"
                    ],
                    options_hi: [
                        "<p>22 घंटे</p>",
                        "<p>20 घंटे</p>",
                        "<p>21 घंटे</p>",
                        "<p>24 घंटे</p>"
                    ],
                    solution_en: "<p>67.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017217179.png\" alt=\"rId46\" width=\"173\" height=\"135\"><br>Efficiency of (C + A - B -C) = 6 - 4 <br>Efficiency of (A - B) = 2 unit<br>Efficiency of B = A - 2<br>So, efficiency of B = 3 - 2 = 1 unit<br>Work done by B = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 21 hours</p>",
                    solution_hi: "<p>67.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017217284.png\" alt=\"rId47\" width=\"164\" height=\"135\"><br>(C + A - B -C) की क्षमता = 6 - 4 <br>(A - B) की क्षमता = 2 इकाई<br>B की क्षमता = A - 2<br>तो, B की दक्षता = 3 - 2 = 1 इकाई<br>B द्वारा किया गया कार्य = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 21 घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. If the median and the mode of a set of data are 12 and 15 respectively, then find the value of thrice of the mean of the same data set.</p>",
                    question_hi: "<p>68. यदि किसी आँकड़ों के समुच्चय की माध्यिका और बहुलक क्रमशः 12 और 15 हैं, तो उसी आँकड़ा समुच्चय के माध्य का तीन गुना मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>68.(a)<br>Mode = 3 Median - 2 mean<br>&rArr; 15 = 3 &times; 12 - 2 mean<br>&rArr; 2 mean = 36 - 15<br>&rArr; mean =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>21</mn><mn>2</mn></mfrac></mstyle></math><br>Now, 3 mean = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>68.(a)<br>बहुलक = 3 माध्यिका - 2 माध्य<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>15</mn><mo>=</mo><mn>3</mn><mo>&#215;</mo><mn>12</mn><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>=</mo><mi mathvariant=\"normal\">&#160;</mi><mn>36</mn><mo>-</mo><mn>15</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi><mo>=</mo><mfrac><mn>21</mn><mn>2</mn></mfrac></math><br>अब, 3 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>63</mn><mn>2</mn></mfrac></mstyle></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A person wants to buy 10 chairs. The total cost of 10 chairs is ₹4,500. After bargaining, the shopkeeper agrees to decrease the cost of each chair by 20%. Then, the person purchased 10% more chairs. What will be the cost of the chairs that he purchased ?</p>",
                    question_hi: "<p>69. एक व्यक्ति 10 कुर्सियां खरीदना चाहता है। 10 कुर्सियों का कुल मूल्य ₹4,500 है। सौदेबाजी के बाद, दुकानदार प्रत्येक कुर्सी के मूल्य में 20% की कमी करने के लिए सहमत होता है जिसके बाद व्यक्ति ने 10% कुर्सियां अधिक खरीदीं। उसके द्वारा खरीदी गई कुर्सियों का मूल्य क्या होगा?</p>",
                    options_en: [
                        "<p>₹3,960</p>",
                        "<p>₹3,260</p>",
                        "<p>₹3,900</p>",
                        "<p>₹4,100</p>"
                    ],
                    options_hi: [
                        "<p>₹3,960</p>",
                        "<p>₹3,260</p>",
                        "<p>₹3,900</p>",
                        "<p>₹4,100</p>"
                    ],
                    solution_en: "<p>69.(a) <br>Cost of 1 chair = <math display=\"inline\"><mfrac><mrow><mn>4500</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 450<br>Cost of 1 chair after 20% decrement = 450 &times; 80% = 360<br>Number of chairs purchased by him = 10 &times; 110% = 11<br>Total cost of chairs that he purchased = 360 &times; 11 = ₹3960</p>",
                    solution_hi: "<p>69.(a) <br>1 कुर्सी की कीमत = <math display=\"inline\"><mfrac><mrow><mn>4500</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 450<br>20% कमी के बाद 1 कुर्सी की कीमत = 450 &times; 80% = 360<br>उसके द्वारा खरीदी गई कुर्सियों की संख्या = 10 &times; 110% = 11<br>उसके द्वारा खरीदी गई कुर्सियों की कुल लागत = 360 &times; 11 = ₹3960</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A thief running at 7 km/h is chased by a policeman whose speed is 12 km/h. If the thief is 280 m ahead of the policeman, then the time required for the policeman to catch the thief will be:</p>",
                    question_hi: "<p>70. 7 km/h की गति से भाग रहे एक चोर का पीछा एक पुलिसकर्मी करता है, जिसकी गति 12 km/h है। यदि चोर पुलिस वाले से 280 मीटर आगे है, तो पुलिसकर्मी को चोर को पकड़ने में कितना समय लगेगा?</p>",
                    options_en: [
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi></mrow><mrow><mn>25</mn></mrow></mfrac></math> minutes</p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> minutes</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>9</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> minutes</p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> minutes</p>"
                    ],
                    options_hi: [
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi></mrow><mrow><mn>25</mn></mrow></mfrac></math> मिनट</p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> मिनट</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>9</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> मिनट</p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> मिनट</p>"
                    ],
                    solution_en: "<p>70.(a)<br>Relative speed, when travelling in same direction = 12- 7 = 5 km/hr<br>Then, time required for the policeman to catch the thief = (<math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>280</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 60) min =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>84</mn><mn>25</mn></mfrac></mstyle></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>9</mn><mn>25</mn></mfrac></mstyle></math>&nbsp;minutes</p>",
                    solution_hi: "<p>70.(a)<br>समान दिशा में यात्रा करते समय सापेक्ष गति = 12- 7 = 5 किमी/घंटा<br>तो, पुलिसकर्मी को चोर को पकड़ने में लगा समय = (<math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>280</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 60) मिनट =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>84</mn><mn>25</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mstyle displaystyle=\"false\"><mfrac><mn>9</mn><mn>25</mn></mfrac></mstyle></math> मिनट</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A pipe can fill an empty tank in 5 minutes and another pipe can empty it in 6 minutes. If both pipes are opened simultaneously, how much time (in minutes) will it take to fill the empty tank?</p>",
                    question_hi: "<p>71. एक पाइप एक खाली टंकी को 5 मिनट में भर सकता है और दूसरा पाइप इसे 6 मिनट में खाली कर सकता है। यदि दोनों पाइप को एक साथ खोल दिया जाए, तो खाली टंकी को भरने में कितना समय (मिनट में) लगेगा?</p>",
                    options_en: [
                        "<p>33 min</p>",
                        "<p>30 min</p>",
                        "<p>35 min</p>",
                        "<p>25 min</p>"
                    ],
                    options_hi: [
                        "<p>33 मिनट</p>",
                        "<p>30 मिनट</p>",
                        "<p>35 मिनट</p>",
                        "<p>25 मिनट</p>"
                    ],
                    solution_en: "<p>71.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017217492.png\" alt=\"rId48\" width=\"189\" height=\"100\"><br>Net efficiency of A and B = 6 - 5 = 1 unit<br>Time taken together to fill tank = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 30 min.</p>",
                    solution_hi: "<p>71.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017217794.png\" alt=\"rId49\" width=\"191\" height=\"110\"><br>A और B की शुद्ध दक्षता = 6 - 5 = 1 इकाई<br>टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 30 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If the odds in favour of an event A are 3 : 4 and the odds against another independent event B are 7 : 4 find the probability that at least one of the event will happen :</p>",
                    question_hi: "<p>72. यदि एक घटना A के पक्ष में ऑड्स 3 : 4 हैं और एक अन्य स्वतंत्र घटना B के विपक्ष में ऑड्स 7 : 4 हैं, तो प्रायिकता ज्ञात कीजिए कि कम से कम एक ईवेंट घटित होगा।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>72.(b) Probability of A = P(A) <br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>7</mn></mfrac></mstyle></math> and P<strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi mathvariant=\"bold\">A</mi><mo mathvariant=\"bold\">&#175;</mo></mover></math>)</strong> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>7</mn></mfrac></mstyle></math><br>Probability of B = P(B) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>11</mn></mfrac></mstyle></math><br>and P<strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi mathvariant=\"bold\">B</mi><mo mathvariant=\"bold\">&#175;</mo></mover></math>)</strong> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>11</mn></mfrac></mstyle></math><br>The required event can be defined as that A takes place and B does not + B takes place and A doesn\'t + A and B both takes place :-&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>11</mn></mfrac><mo>+</mo><mfrac><mn>4</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>11</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>11</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>77</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>77</mn></mfrac><mo>+</mo><mfrac><mn>12</mn><mn>77</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>11</mn></mfrac></math></p>",
                    solution_hi: "<p>72.(b) <br>A की संभावना = P(A) = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>7</mn></mfrac></mstyle></math><br>और P<strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi mathvariant=\"bold\">A</mi><mo mathvariant=\"bold\">&#175;</mo></mover></math>)</strong>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>7</mn></mfrac></mstyle></math><br>B की संभावना= P(B) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>11</mn></mfrac></mstyle></math><br>और P<strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi mathvariant=\"bold\">B</mi><mo mathvariant=\"bold\">&#175;</mo></mover></math>)</strong>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>11</mn></mfrac></mstyle></math><br>आवश्यक घटना को इस प्रकार परिभाषित किया जा सकता है कि A घटित होता है और B नहीं घटित होता है + B होता है और A नहीं होता है + <br>A और B दोनों होते हैं:-<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>11</mn></mfrac><mo>+</mo><mfrac><mn>4</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>11</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>11</mn></mfrac></mstyle></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>21</mn><mn>77</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>77</mn></mfrac><mo>+</mo><mfrac><mn>12</mn><mn>77</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>11</mn></mfrac></mstyle></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. In a group of 200 members, 45% are teenagers and the remaining are adults. The average weight of the whole group is 62 kg. If the average weight of teenagers is 56 kg, then the nearest integral value of the average weight of adults is ____________ .</p>",
                    question_hi: "<p>73. 200 सदस्यों के एक समूह में, 45% किशोर हैं और शेष वयस्क हैं। पूरे समूह का औसत वजन 62 kg है। यदि किशोरों का औसत वजन 56 kg है, तो वयस्कों के औसत वजन का निकटतम पूर्णांकीय मान (integral value), ___________ है।</p>",
                    options_en: [
                        "<p>84 kg</p>",
                        "<p>65 kg</p>",
                        "<p>79 kg</p>",
                        "<p>67 kg</p>"
                    ],
                    options_hi: [
                        "<p>84 kg</p>",
                        "<p>65 kg</p>",
                        "<p>79 kg</p>",
                        "<p>67 kg</p>"
                    ],
                    solution_en: "<p>73.(d) Number of teenager = 200 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>100</mn></mfrac></math> = 90 <br>Number of adult = 200 - 90 = 110<br>By using allegation method <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017217954.png\" alt=\"rId50\" width=\"165\" height=\"110\"><br><math display=\"inline\"><mo>&#8658;</mo></math> 62 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>56</mn><mo>&#215;</mo><mn>9</mn><mo>+</mo><mn>11</mn><mi>X</mi></mrow><mrow><mn>9</mn><mo>+</mo><mn>11</mn></mrow></mfrac></mstyle></math>&nbsp;&rArr; 62 &times; 20 = 504 + 11x<br><math display=\"inline\"><mo>&#8658;</mo></math>1240 - 504 = 11x<br><math display=\"inline\"><mo>&#8658;</mo></math>736 = 11x &rArr; x = 66.9 ≃ 67kg</p>",
                    solution_hi: "<p>73.(d) किशोर की संख्या = 200 &times; <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 90 <br>वयस्कों की संख्या = 200 - 90 = 110<br>एलीगेशन विधि का प्रयोग करके <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017218098.png\" alt=\"rId51\" width=\"176\" height=\"133\"><br><math display=\"inline\"><mo>&#8658;</mo></math> 62 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>56</mn><mo>&#215;</mo><mn>9</mn><mo>+</mo><mn>11</mn><mi>X</mi></mrow><mrow><mn>9</mn><mo>+</mo><mn>11</mn></mrow></mfrac></mstyle></math> &rArr; 62 &times; 20 = 504 + 11x<br><math display=\"inline\"><mo>&#8658;</mo></math> 1240 - 504 = 11x<br><math display=\"inline\"><mo>&#8658;</mo></math> 736 = 11x &rArr; x = 66.9 ≃ 67kg</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Two offers were being made to Swaroop for a watch with a marked price of ₹1,600. Either two successive discounts of 20%, or two discounts of 30% and 10% after each other is offered. If Swaroop opted for the better plan over the other, how much more must he have saved?</p>",
                    question_hi: "<p>74. स्वरूप को ₹1,600 अंकित मूल्य वाली एक घड़ी के लिए दो ऑफर दिए जाते हैं। या तो वह 20% की दो क्रमिक छूट ले सकता है, या एक के बाद एक 30% और 10% की दो छूट ले सकता है। यदि स्वरूप उनमें से बेहतर प्लान का चुनाव करता है, तो वह कितनी अधिक बचत कर सकता है?</p>",
                    options_en: [
                        "<p>₹22</p>",
                        "<p>₹35</p>",
                        "<p>₹16</p>",
                        "<p>₹30</p>"
                    ],
                    options_hi: [
                        "<p>₹22</p>",
                        "<p>₹35</p>",
                        "<p>₹16</p>",
                        "<p>₹30</p>"
                    ],
                    solution_en: "<p>74.(c)<br>Two successive discount of 20% = 20 + 20 - <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36%<br>Two successive discount of 30% and 10% = 30 + 10 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 37%<br>Required amount = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>37</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 16</p>",
                    solution_hi: "<p>74.(c)<br>20% की दो क्रमिक छूट = 20 + 20 - <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36%<br>30% और 10% की दो क्रमिक छूट = 30 + 10 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 37%<br>आवश्यक राशि = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>37</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 16</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A and B are two stations 420 km apart. A train starts from A at 11 am and travels towards B at the speed of 70 kmph. Another train starts from B at 12 noon and travels towards A at the speed of 30 kmph. At what time do they meet?</p>",
                    question_hi: "<p>75. दो स्टेशन A और B एक-दूसरे से 420 km की दूरी पर हैं। एक रेलगाड़ी 11 am पर A से शुरू होती है और 70 kmph की चाल से B की ओर यात्रा करती है। एक दूसरी रेलगाड़ी दोपहर 12 बजे B से शुरू होती है और 30 kmph की चाल से A की ओर यात्रा करती है। दोनों रेलगाड़ियां एक-दूसरे से किस समय पर मिलेंगी?</p>",
                    options_en: [
                        "<p>3.30 pm</p>",
                        "<p>1.30 pm</p>",
                        "<p>2 pm</p>",
                        "<p>3 pm</p>"
                    ],
                    options_hi: [
                        "<p>3.30 pm</p>",
                        "<p>1.30 pm</p>",
                        "<p>2 pm</p>",
                        "<p>3 pm</p>"
                    ],
                    solution_en: "<p>75.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017218251.png\" alt=\"rId52\" width=\"292\" height=\"64\"><br>Distance traveled by train towards B in 1 hr(at 12 pm) = 70 &times; 1 = 70 km<br>Remaining distance = 420 - 70 = 350 km<br>Relative speed of train , when travelling towards each other = 70 + 30 = 100 km/hr<br>Time taken to meet each other =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>350</mn><mn>100</mn></mfrac></mstyle></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></math> = 3 hr 30 minute<br>Required meeting time = 12 + 3:30 = 3:30 pm</p>",
                    solution_hi: "<p>75.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742017218251.png\" alt=\"rId52\" width=\"292\" height=\"64\"><br>ट्रेन द्वारा B की ओर 1 घंटे में (दोपहर 12 बजे) तय की गई दूरी = 70 &times; 1 = 70 km<br>शेष दूरी = 420 - 70 = 350 km<br>एक दूसरे की ओर यात्रा करते समय ट्रेन की सापेक्ष गति = 70 + 30 = 100 km/hr<br>एक दूसरे से मिलने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>350</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></math> = 3 घंटा 30 मिनट<br>आवश्यक मिलने का समय = 12 + 3:30 = 3:30 pm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>Most of the committee / are here to / decide the fate of / this ancient building.</p>",
                    question_hi: "<p>76. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>Most of the committee / are here to / decide the fate of / this ancient building.</p>",
                    options_en: [
                        "<p>Most of the committee</p>",
                        "<p>this ancient building.</p>",
                        "<p>are here to.</p>",
                        "<p>decide the fate of</p>"
                    ],
                    options_hi: [
                        "<p>Most of the committee</p>",
                        "<p>this ancient building.</p>",
                        "<p>are here to.</p>",
                        "<p>decide the fate of</p>"
                    ],
                    solution_en: "<p>76.(c) are here to<br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;Most of the committee&rsquo; is a singular subject that will take &lsquo;is&rsquo; as a singular verb. Hence, &lsquo;is here to&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c) are here to<br>&ldquo;Subject-Verb Agreement&rdquo; नियम के अनुसार, singular subject के साथ हमेशा singular verb का प्रयोग होता है और plural subject के साथ हमेशा plural verb का प्रयोग होता है। दिए गए sentence में, &lsquo;Most of the committee&rsquo; singular subject है इसलिए &lsquo;is&rsquo; singular verb के रूप में प्रयोग होगा। इसलिए, &lsquo;is here to&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the word that is incorrectly spelt.</p>",
                    question_hi: "<p>77. Identify the word that is incorrectly spelt.</p>",
                    options_en: [
                        "<p>already</p>",
                        "<p>library</p>",
                        "<p>installment</p>",
                        "<p>boundry</p>"
                    ],
                    options_hi: [
                        "<p>already</p>",
                        "<p>library</p>",
                        "<p>installment</p>",
                        "<p>boundry</p>"
                    ],
                    solution_en: "<p>77.(d) boundry.<br>Correct spelling - Boundary</p>",
                    solution_hi: "<p>77.(d) boundry.<br>सही spelling - Boundary</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Find a word that is the synonym of:<br>Repeal</p>",
                    question_hi: "<p>78. Find a word that is the synonym of:<br>Repeal</p>",
                    options_en: [
                        "<p>acceptance</p>",
                        "<p>cancellation</p>",
                        "<p>rejection</p>",
                        "<p>dejection</p>"
                    ],
                    options_hi: [
                        "<p>acceptance</p>",
                        "<p>cancellation</p>",
                        "<p>rejection</p>",
                        "<p>dejection</p>"
                    ],
                    solution_en: "<p>78.(b) <strong>cancellation</strong><br><strong>Repeal </strong>- revoke or annul<br><strong>Dejection </strong>- a sad and depressed state; low spirits.<br><strong>Rejection -</strong> the dismissing or refusing of a proposal, idea, etc.</p>",
                    solution_hi: "<p>78.(b) <strong>cancellation -</strong> रद्द करना<br><strong>Repeal &ndash;</strong> निरस्त या रद्द करना<br><strong>Dejection -</strong> नाखुश, निराश या आशाहीन होने का भाव।<br><strong>Rejection - </strong>किसी प्रस्ताव, विचार आदि को खारिज या अस्वीकार करना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The patient has been advised to rest by the doctors <strong><span style=\"text-decoration: underline;\">attending for him</span></strong></p>",
                    question_hi: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.&nbsp;<br>The patient has been advised to rest by the doctors <strong><span style=\"text-decoration: underline;\">attending for him</span></strong></p>",
                    options_en: [
                        "<p>attending to him</p>",
                        "<p>attending on him</p>",
                        "<p>attending him</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>attending to him</p>",
                        "<p>attending on him</p>",
                        "<p>attending him</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>79.(b)<strong> Attend on means </strong>: taking care of.</p>",
                    solution_hi: "<p>79.(b)<strong> Attend on means</strong> : ख्याल रखना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that can be used as a one-word substitute for the italicised segment in the given sentence.<br>Tin is easy to shape in any desired form.</p>",
                    question_hi: "<p>80. Select the option that can be used as a one-word substitute for the italicised segment in the given sentence.<br>Tin is easy to shape in any desired form.</p>",
                    options_en: [
                        "<p>rigid</p>",
                        "<p>immutable</p>",
                        "<p>malleable</p>",
                        "<p>gullible</p>"
                    ],
                    options_hi: [
                        "<p>rigid</p>",
                        "<p>immutable</p>",
                        "<p>malleable</p>",
                        "<p>gullible</p>"
                    ],
                    solution_en: "<p>80.(c) <strong>Malleable-</strong> easy to shape in any desired form.<br><strong>Rigid</strong>- unable to bend.<br><strong>Immutable-</strong> unable to be changed.<br><strong>Gullible-</strong> easily persuaded to believe something.</p>",
                    solution_hi: "<p>80.(c) <strong>Malleable</strong> (लचीला) - easy to shape in any desired form.<br><strong>Rigid</strong> (कठोर) - unable to bend.<br><strong>Immutable</strong> (अडिग) - unable to be changed.<br><strong>Gullible</strong> (भोला-भाला) - easily persuaded to believe something.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the following sentence in passive voice.<br>Most lizards rely on camouflage to hide from their enemies.</p>",
                    question_hi: "<p>81. Select the option that expresses the following sentence in passive voice.<br>Most lizards rely on camouflage to hide from their enemies.</p>",
                    options_en: [
                        "<p>Camouflage has been relied on by most lizards to hide from&nbsp;their enemies.</p>",
                        "<p>Camouflage is relied on by most lizards to hide from their&nbsp;enemies.</p>",
                        "<p>Camouflage was relied on by most lizards to hide from their&nbsp;enemies.</p>",
                        "<p>Most lizards are being relied on by camouflage to hide from&nbsp;their enemies.</p>"
                    ],
                    options_hi: [
                        "<p>Camouflage has been relied on by most lizards to hide from&nbsp;their enemies.</p>",
                        "<p>Camouflage is relied on by most lizards to hide from their&nbsp;enemies.</p>",
                        "<p>Camouflage was relied on by most lizards to hide from their&nbsp;enemies.</p>",
                        "<p>Most lizards are being relied on by camouflage to hide from&nbsp;their enemies.</p>"
                    ],
                    solution_en: "<p>81.(b) Camouflage is relied on by most lizards to hide from their enemies. (Correct)<br>(a) Camouflage <span style=\"text-decoration: underline;\">has been relied</span> on by most lizards to hide from their enemies. (Incorrect Tense)<br>(c) Camouflage <span style=\"text-decoration: underline;\">was relied</span> on by most lizards to hide from their&nbsp;enemies. (Incorrect Tense)<br>(d) Most lizards <span style=\"text-decoration: underline;\">are being relied</span> on by camouflage to hide from their enemies. (Incorrect Tense)</p>",
                    solution_hi: "<p>81.(b) Camouflage is relied on by most lizards to hide from&nbsp;their enemies. (Correct) <br>(a) Camouflage <span style=\"text-decoration: underline;\">has been relied</span> on by most lizards to hide from their enemies. (गलत tense (present perfect) का प्रयोग किया गया है। is relied (present simple) का प्रयोग होगा I )&nbsp;<br>(c) Camouflage <span style=\"text-decoration: underline;\">was relied</span> on by most lizards to hide from their&nbsp;enemies. (गलत tense (simple past) का प्रयोग किया गया है । is relied (present simple) का प्रयोग होगा I )<br>(d) Most lizards <span style=\"text-decoration: underline;\">are being relied</span> on by camouflage to hide from their enemies. (गलत tense (present continuous) का प्रयोग किया गया है। is relied (present simple) का प्रयोग होगा I )</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Children head back to the village with bundles of firewood on their heads.<br>Q. After school, the children take the sheep into the forest to graze. <br>R. The cheerful voices fade as the sun goes down the Horizon. <br>S. Along with the other village kids Romi hugs the trees and climbs the trees there.</p>",
                    question_hi: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Children head back to the village with bundles of firewood on their heads.<br>Q.After school, the children take the sheep into the forest to graze. <br>R. The cheerful voices fade as the sun goes down the Horizon. <br>S. Along with the other village kids Romi hugs the trees and climbs the trees there.</p>",
                    options_en: [
                        "<p>QSRP</p>",
                        "<p>QSPR</p>",
                        "<p>QRPS</p>",
                        "<p>RPSQ</p>"
                    ],
                    options_hi: [
                        "<p>QSRP</p>",
                        "<p>QSPR</p>",
                        "<p>QRPS</p>",
                        "<p>RPSQ</p>"
                    ],
                    solution_en: "<p>82.(a) QSRP<br>Sentence Q will be the starting line as it contains the main idea of the parajumble i.e. Children, who take sheep to graze. And, Sentence S states what Romi does after going to the forest. So, S will follow Q. Further, Sentence R states about how the evening goes and Sentence P states that the children go back to the village after that. So, P will follow R. Going through the options, option (a) has the correct sequence.</p>",
                    solution_hi: "<p>82.(a) QSRP<br>Sentence Q starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;Children, who take sheep to graze&rsquo; शामिल है। हालांकि, Sentence S बताता है कि Romi जंगल में जाने के बाद क्या करता है। तो,Q के बाद S आएगा। आगे, Sentence R बताता है कि शाम (evening ) कैसे गुजरती है और Sentence P कहता है कि बच्चे उसके बाद गाँव वापस चले जाते हैं। इसलिए, R के बाद P आएगा। options के माध्यम से जाने पर , option (a) में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate homophone to fill in the blank.<br>The hospital is ________ the river.</p>",
                    question_hi: "<p>83. Select the most appropriate homophone to fill in the blank.<br>The hospital is ________ the river.</p>",
                    options_en: [
                        "<p>bi</p>",
                        "<p>buy</p>",
                        "<p>bye</p>",
                        "<p>by</p>"
                    ],
                    options_hi: [
                        "<p>bi</p>",
                        "<p>buy</p>",
                        "<p>bye</p>",
                        "<p>by</p>"
                    ],
                    solution_en: "<p>83.(d) By<br>&lsquo;By&rsquo; means beside a place. The given sentence states that the hospital is beside the river. Hence, &lsquo;by&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>83.(d) By<br>&lsquo;By&rsquo; का अर्थ है किसी स्थान के पास। दिए गए sentence में कहा गया है कि अस्पताल नदी के किनारे है। अतः, &lsquo;by&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. The underlined word in the sentence is not spelt correctly. Identify the correct spelling from the options given below. <br>The boys seem irresponsibly <span style=\"text-decoration: underline;\">insaucient</span> about bullying the juniors.</p>",
                    question_hi: "<p>84. The underlined word in the sentence is not spelt correctly. Identify the correct spelling from the options given below. <br>The boys seem irresponsibly <span style=\"text-decoration: underline;\">insaucient</span> about bullying the juniors.</p>",
                    options_en: [
                        "<p>Insouceant</p>",
                        "<p>Insuociant</p>",
                        "<p>Insouciant</p>",
                        "<p>Insoucient</p>"
                    ],
                    options_hi: [
                        "<p>Insouceant</p>",
                        "<p>Insuociant</p>",
                        "<p>Insouciant</p>",
                        "<p>Insoucient</p>"
                    ],
                    solution_en: "<p>84.(c) Insouciant <br>&lsquo;Insouciant&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>84.(c) Insouciant <br>&lsquo;Insouciant&rsquo; सही spelling है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the option that expresses the following sentence in passive voice.<br>Who is creating this mess?</p>",
                    question_hi: "<p>85. Select the option that expresses the following sentence in passive voice.<br>Who is creating this mess?</p>",
                    options_en: [
                        "<p>By whom is this mess being created?</p>",
                        "<p>Who has created this mess?</p>",
                        "<p>By whom has this mess been created?</p>",
                        "<p>By whom this mess being created?</p>"
                    ],
                    options_hi: [
                        "<p>By whom is this mess being created?</p>",
                        "<p>Who has created this mess?</p>",
                        "<p>By whom has this mess been created?</p>",
                        "<p>By whom this mess being created?</p>"
                    ],
                    solution_en: "<p>85.(a) By whom is this mess being created? (Correct)<br>(b) Who <span style=\"text-decoration: underline;\">has created</span> this mess? (Incorrect Tense)<br>(c) By whom <span style=\"text-decoration: underline;\">has</span> this mess been created? (Incorrect Verb)<br>(d) By whom this mess being created? (The verb &lsquo;is&rsquo; is missing)</p>",
                    solution_hi: "<p>85.(a) By whom is this mess being created? (Correct)<br>(b) Who <span style=\"text-decoration: underline;\">has created</span> this mess? (गलत tense (present perfect) का प्रयोग किया गया है। (is this mess being) (present continuous) का प्रयोग होगा I )<br>(c) By whom <span style=\"text-decoration: underline;\">has</span> this mess been created? (गलत tense (present perfect) का प्रयोग किया गया है । (is this mess being) (present continuous) का प्रयोग होगा I )<br>(d) By whom this mess being created? (गलत verb (being created) (is this mess being created) का प्रयोग होगा । )</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate meaning of the underlined idiom in the given sentence. <br>Only those having <span style=\"text-decoration: underline;\">blue blood</span> can dethrone the evil autocrat and restore equilibrium.</p>",
                    question_hi: "<p>86. Select the most appropriate meaning of the underlined idiom in the given sentence. <br>Only those having <span style=\"text-decoration: underline;\">blue blood</span> can dethrone the evil autocrat and restore equilibrium.</p>",
                    options_en: [
                        "<p>Bitter relations</p>",
                        "<p>Vigilant nature</p>",
                        "<p>Political ideology</p>",
                        "<p>Aristocratic lineage</p>"
                    ],
                    options_hi: [
                        "<p>Bitter relations</p>",
                        "<p>Vigilant nature</p>",
                        "<p>Political ideology</p>",
                        "<p>Aristocratic lineage</p>"
                    ],
                    solution_en: "<p>86.(d) <strong>Blue blood- </strong>aristocratic lineage.</p>",
                    solution_hi: "<p>86.(d) <strong>Blue blood</strong> - aristocratic lineage./कुलीन वंश</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. So he recruited French commanders to teach the European style of warfare.<br>B. Tipu was one of the first few leaders to realise the danger of the British monarchy.<br>C. Good leaders are able to plan for the future. <br>D. He understood that the British fought differently from the Indians.</p>",
                    question_hi: "<p>87. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. So he recruited French commanders to teach the European style of warfare.<br>B. Tipu was one of the first few leaders to realise the danger of the British monarchy.<br>C. Good leaders are able to plan for the future. <br>D. He understood that the British fought differently from the Indians.</p>",
                    options_en: [
                        "<p>CBDA</p>",
                        "<p>BCDA</p>",
                        "<p>BCAD</p>",
                        "<p>CBAD</p>"
                    ],
                    options_hi: [
                        "<p>CBDA</p>",
                        "<p>BCDA</p>",
                        "<p>BCAD</p>",
                        "<p>CBAD</p>"
                    ],
                    solution_en: "<p>87.(a) CBDA<br>Sentence C will be the starting line as it contains the main subject of the parajumble i.e. Good leaders. And, Sentence B states about Tipu who was among those leaders. So, B will follow . Further, Sentence D states what he understood about the British and Sentence A states What he did then. So, A will follow D. Going through the options, option (a) has the correct sequence.</p>",
                    solution_hi: "<p>87.(a) CBDA<br>Sentence C प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विषय यानी अच्छे नेता शामिल हैं। Sentence B में टीपू के बारे में बताया गया है जो उन नेताओं में से थे। तो, C के बाद B आएगा। इसके अलावा, Sentence D बताता है कि उसने अंग्रेजों के बारे में क्या समझा और Sentence A ने कहा कि उसने तब क्या किया। इसलिए, D के बाद A आएगा । विकल्पों के माध्यम से, विकल्प (a) में सही क्रम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. &nbsp;Four alternatives are given for the idiom/phrase underlined in the sentence. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase. <br>His boss was always <strong><span style=\"text-decoration: underline;\">breathing down his neck</span>.</strong></p>",
                    question_hi: "<p>88. &nbsp;Four alternatives are given for the idiom/phrase underlined in the sentence. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase. <br>His boss was always <strong><span style=\"text-decoration: underline;\">breathing down his neck</span></strong>.</p>",
                    options_en: [
                        "<p>abusing and ill-treating him</p>",
                        "<p>watching all his actions closely</p>",
                        "<p>shouting loudly at him</p>",
                        "<p>giving him strenuous work</p>"
                    ],
                    options_hi: [
                        "<p>abusing and ill-treating him</p>",
                        "<p>watching all his actions closely</p>",
                        "<p>shouting loudly at him</p>",
                        "<p>giving him strenuous work</p>"
                    ],
                    solution_en: "<p>88.(b) Watching all his actions closely.</p>",
                    solution_hi: "<p>88.(b) Watching all his actions closely/उसके हर कार्य पर पैनी नजर रखना </p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>A person who is forgetful and unable to concentrate or think clearly</p>",
                    question_hi: "<p>89.&nbsp;Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>A person who is forgetful and unable to concentrate or think clearly</p>",
                    options_en: [
                        "<p>Scatterbrain</p>",
                        "<p>Systematic</p>",
                        "<p>illogical</p>",
                        "<p>Intelligent</p>"
                    ],
                    options_hi: [
                        "<p>Scatterbrain</p>",
                        "<p>Systematic</p>",
                        "<p>illogical</p>",
                        "<p>Intelligent</p>"
                    ],
                    solution_en: "<p>89.(a) <strong>Scatterbrain</strong><br><strong>Scatterbrain-</strong> A person who is forgetful and unable to concentrate or think carefully<br><strong>Systematic</strong>- done using a fixed plan or method<br><strong>Intelligent-</strong> having or showing the ability to understand, learn and think; clever</p>",
                    solution_hi: "<p>89.(a) <strong>Scatterbrain</strong><br><strong>Scatterbrain</strong>(भुलक्कड़ व्यक्ति) - A person who is forgetful and unable to concentrate or think carefully<br><strong>Systematic</strong>(व्यवस्थित) - done using a fixed plan or method<br><strong>Intelligent</strong>(बुद्धिमान) - having or showing the ability to understand, learn and think; clever</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct.<br>Americans are accustomed of drinking coffee with their meals.</p>",
                    question_hi: "<p>90. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct.<br>Americans are accustomed of drinking coffee with their meals.</p>",
                    options_en: [
                        "<p>Americans are</p>",
                        "<p>drinking coffee</p>",
                        "<p>with their meals.</p>",
                        "<p>accustomed of</p>"
                    ],
                    options_hi: [
                        "<p>Americans are</p>",
                        "<p>drinking coffee</p>",
                        "<p>with their meals.</p>",
                        "<p>accustomed of</p>"
                    ],
                    solution_en: "<p>90.(d) accustomed of<br>Replace &ldquo;of&rdquo; by &ldquo;to&rdquo;. Use &ldquo;accustomed to&rdquo;. If you are accustomed to something, you are used to it.</p>",
                    solution_hi: "<p>90.(d) accustomed of<br>&ldquo;of&rdquo; को &ldquo;to&rdquo; में बदलें। &ldquo;accustomed to&rdquo;. का प्रयोग करें। यदि आप किसी चीज के आदि हैं, तो आप इसके अभ्यस्त हैं ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the given word.<br>Entail</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the given word.<br>Entail</p>",
                    options_en: [
                        "<p>Necessitate</p>",
                        "<p>Eliminate</p>",
                        "<p>Flaw</p>",
                        "<p>Assume </p>"
                    ],
                    options_hi: [
                        "<p>Necessitate</p>",
                        "<p>Eliminate</p>",
                        "<p>Flaw</p>",
                        "<p>Assume </p>"
                    ],
                    solution_en: "<p>91.(a)<strong> Necessitate- </strong>to make something necessary.<br><strong>Entail- </strong>to involve or require as a necessary step or consequence.<br><strong>Eliminate-</strong> to remove or get rid of something.<br><strong>Flaw-</strong> an imperfection or defect.<br><strong>Assume-</strong> to take for granted or suppose.</p>",
                    solution_hi: "<p>91.(a) <strong>Necessitate (जरूरत) - </strong>to make something necessary.<br><strong>Entail (आवश्यक) </strong>- to involve or require as a necessary step or consequence.<br><strong>Eliminate (हटाना) -</strong> to remove or get rid of something.<br><strong>Flaw (गलती) - </strong>an imperfection or defect.<br><strong>Assume (मान लेना) -</strong> to take for granted or suppose.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Which is the right set of ANTONYMS from the options for &lsquo;Confidential&rsquo;?</p>",
                    question_hi: "<p>92. Which is the right set of ANTONYMS from the options for &lsquo;Confidential&rsquo;?</p>",
                    options_en: [
                        "<p>Public, Concealed</p>",
                        "<p>Offstage, Sneaking</p>",
                        "<p>Hushed, Common</p>",
                        "<p>Familiar, Revealed</p>"
                    ],
                    options_hi: [
                        "<p>Public, Concealed</p>",
                        "<p>Offstage, Sneaking</p>",
                        "<p>Hushed, Common</p>",
                        "<p>Familiar, Revealed</p>"
                    ],
                    solution_en: "<p>92.(d) <strong>Familiar, Revealed</strong><br><strong>Familiar</strong>- well-known or easily recognized.<br><strong>Revealed-</strong> made known or disclosed.<br><strong>Confidential-</strong> intended to be kept secret or private.</p>",
                    solution_hi: "<p>92.(d) <strong>Familiar,</strong> Revealed<br><strong>Familiar </strong>(परिचित) - well-known or easily recognized.<br><strong>Revealed</strong> (प्रकट करना) - made known or disclosed.<br><strong>Confidential</strong> (गोपनीय) - intended to be kept secret or private.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>During the preparation for competitive exams, Meena <span style=\"text-decoration: underline;\">avoided</span> going to parties.</p>",
                    question_hi: "<p>93. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>During the preparation for competitive exams, Meena <span style=\"text-decoration: underline;\">avoided</span> going to parties.</p>",
                    options_en: [
                        "<p>steered clear of</p>",
                        "<p>cracked a joke</p>",
                        "<p>fired a cracker</p>",
                        "<p>whispered a secret</p>"
                    ],
                    options_hi: [
                        "<p>steered clear of</p>",
                        "<p>cracked a joke</p>",
                        "<p>fired a cracker</p>",
                        "<p>whispered a secret</p>"
                    ],
                    solution_en: "<p>93.(a) steered clear of<br>The phrase &lsquo;steer clear of&rsquo; means to deliberately avoid something. Hence, &lsquo;steered clear of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(a) steered clear of<br>Phrase &lsquo;steer clear of&rsquo; का अर्थ है जानबूझकर किसी चीज़ से बचना। इसलिए, &lsquo;steered clear of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>His directions to the driver were ___________and helped him to reach on time.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>His directions to the driver were ___________and helped him to reach on time.</p>",
                    options_en: [
                        "<p>expletive</p>",
                        "<p>implicate</p>",
                        "<p>explicit</p>",
                        "<p>implicit</p>"
                    ],
                    options_hi: [
                        "<p>expletive</p>",
                        "<p>implicate</p>",
                        "<p>explicit</p>",
                        "<p>implicit</p>"
                    ],
                    solution_en: "<p>94.(c) <strong>explicit</strong> <br><strong>Expletive </strong>- an oath or swear word.<br><strong>Implicate</strong> - show (someone) to be involved in a crime.<br><strong>Explicit - </strong>stated clearly and in detail, leaving no room for confusion or doubt.<br><strong>Implicit - </strong>suggested though not directly expressed</p>",
                    solution_hi: "<p>94.(c) <strong>explicit</strong> <br><strong>Expletive</strong> -एक शपथ या शपथ लेना । <br><strong>Implicate</strong> -किसी अपराध में शामिल होना <br><strong>Explicit -</strong> स्पष्ट रूप से और विस्तार से कहा गया है कि भ्रम या संदेह के लिए कोई जगह नहीं छोड़ना<br><strong>Implicit -</strong> सुझाव देना, हालांकि प्रत्यक्ष रूप से व्यक्त नहीं किया गया है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Pick a word opposite in meaning to <br>Equivocal</p>",
                    question_hi: "<p>95. Pick a word opposite in meaning to <br>Equivocal</p>",
                    options_en: [
                        "<p>logical</p>",
                        "<p>diplomatic</p>",
                        "<p>clear</p>",
                        "<p>perfidious</p>"
                    ],
                    options_hi: [
                        "<p>logical</p>",
                        "<p>diplomatic</p>",
                        "<p>clear</p>",
                        "<p>perfidious</p>"
                    ],
                    solution_en: "<p>95.(c) <strong>Clear</strong>- easy to see, hear or understand.Logical- seeming natural, reasonable, or sensible <br><strong>Equivocal- </strong>open to more than one interpretation; ambiguous.<br><strong>Logical - </strong>covered, appropriate or intelligent<br><strong>Diplomatic</strong>- having or showing an ability to deal with people in a sensitive and tactful way.<br><strong>Perfidious</strong>- deceitful and untrustworthy.</p>",
                    solution_hi: "<p>95.(c) <strong>Clear</strong>- देखने, सुनने या समझने में आसान<br><strong>Equivocal</strong>- अनिश्चित या अस्पष्ट<br><strong>Logical </strong>-स्वाभाविक, उचित या समझदार प्रतीत होना <br><strong>Diplomatic</strong>-संवेदनशील और चातुर्यपूर्ण तरीके से लोगों के साथ व्यवहार करने की क्षमता होना या दिखाना<br><strong>Perfidious</strong>- धोखेबाज, जिस पर भरोसा नहीं किया जा सकता</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.96</p>",
                    question_hi: "<p>96.<strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.96</p>",
                    options_en: [
                        "<p>decided</p>",
                        "<p>invented</p>",
                        "<p>settled</p>",
                        "<p>identified</p>"
                    ],
                    options_hi: [
                        "<p>decided</p>",
                        "<p>invented</p>",
                        "<p>settled</p>",
                        "<p>identified</p>"
                    ],
                    solution_en: "<p>96.(d) identified<br>&lsquo;Identify&rsquo; means to recognize or be able to say who or what somebody/something is. The given passage states that Dr Hemmings has been conducting research on the big cat phenomenon and has already identified the remains of some wild animals. Hence, &lsquo;identified&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) identified<br>&lsquo;Identify&rsquo; का अर्थ है व्&zwj;यक्ति या वस्&zwj;तु की पहचान करना । दिए गए passage में कहा गया है कि Dr Hemmings, big cat phenomenon पर शोध कर रहे हैं और अब तक कुछ जंगली जानवरों के अवशेषों की पहचान कर चुके हैं। इसलिए, &lsquo;Identify&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.97</p>",
                    question_hi: "<p>97. <strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.97</p>",
                    options_en: [
                        "<p>creatures</p>",
                        "<p>mortals</p>",
                        "<p>individuals</p>",
                        "<p>people</p>"
                    ],
                    options_hi: [
                        "<p>creatures</p>",
                        "<p>mortals</p>",
                        "<p>individuals</p>",
                        "<p>people</p>"
                    ],
                    solution_en: "<p>97.(a) creatures<br>&lsquo;Creature&rsquo; means a living thing such as an animal, a bird, a fish or an insect, but not a plant. The given passage talks about the remains of some wild animals that may have been eaten by creatures far larger than any of the country&rsquo;s known carnivores. Hence, &lsquo;creatures&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) creatures<br>&lsquo;Creature&rsquo; का अर्थ है एक जीवित वस्तु जैसे कि जानवर, पक्षी, मछली या कीड़ा, न की एक पेड़। दिया ग passage कुछ जंगली जानवरों के अवशेषों के बारे में बात करता है जो शायद देश के किसी भी ज्ञात मांसाहारी से ज्यादा बड़े जीवों द्वारा खाए गए हों। इसलिए, &lsquo;Creature&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98.<strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.98</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.98</p>",
                    options_en: [
                        "<p>related</p>",
                        "<p>concerned</p>",
                        "<p>connected</p>",
                        "<p>involved</p>"
                    ],
                    options_hi: [
                        "<p>related</p>",
                        "<p>concerned</p>",
                        "<p>connected</p>",
                        "<p>involved</p>"
                    ],
                    solution_en: "<p>98.(d) involved<br>&lsquo;Involve&rsquo; means to cause somebody/something to take part in or be concerned with something. The given passage states that the project has involved an analysis of twenty skeletal animal remains. Hence, &lsquo;involved&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) involved<br>&lsquo;Involve&rsquo; का अर्थ है किसी चीज़ में भाग लेना या किसी चीज़ से संबंधित होना। दिए गए passage में कहा गया है कि project में बीस जानवरों के कंकाल वाले अवशेषों का विश्लेषण शामिल है। इसलिए, &lsquo;Involve&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.99</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.99</p>",
                    options_en: [
                        "<p>redeemed</p>",
                        "<p>revoked</p>",
                        "<p>reclaimed</p>",
                        "<p>recovered</p>"
                    ],
                    options_hi: [
                        "<p>redeemed</p>",
                        "<p>revoked</p>",
                        "<p>reclaimed</p>",
                        "<p>recovered</p>"
                    ],
                    solution_en: "<p>99.(d) recovered<br>&lsquo;Recover&rsquo; means to to get back something lost or spent. The given passage states that . the project has involved an analysis of twenty skeletal animal remains recovered from across Gloucestershire and other nearby counties. Hence, &lsquo;recovered&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) recovered<br>&lsquo;Recover&rsquo; का अर्थ है खोई हुई वस्तु को वापस पाना। इस project में Gloucestershire और आसपास के अन्य counties(किसी राज्य या देश का एक विशिष्ट क्षेत्र) से बरामद बीस जानवरों के कंकाल के अवशेषों का विश्लेषण शामिल है। इसलिए, &lsquo;Recover&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.100</p>",
                    question_hi: "<p>100.<strong>Cloze Test :</strong><br>Over the past year, Dr Hemmings has been conducting research on the big cat phenomenon and has already ____96____ the remains of some wild animals that may have been eaten by ____97____ far larger than any of the country&rsquo;s known carnivores. The project has ____98____ an analysis of twenty skeletal animal remains ____99____ from across Gloucestershire and other nearby counties. The bones were selected because the ____100____ of their death led people to believe that these animals may have been killed by a big cat.<br>Select the most appropriate option to fill in blank No.100</p>",
                    options_en: [
                        "<p>opportunities</p>",
                        "<p>potential</p>",
                        "<p>affairs</p>",
                        "<p>circumstances</p>"
                    ],
                    options_hi: [
                        "<p>opportunities</p>",
                        "<p>potential</p>",
                        "<p>affairs</p>",
                        "<p>circumstances</p>"
                    ],
                    solution_en: "<p>100.(d) circumstances<br>&lsquo;Circumstances&rsquo; means he facts and events that affect what happens in a particular situation. The given passage states that the bones were selected because the circumstances of their death led people to believe that these animals may have been killed by a big cat. Hence, &lsquo;circumstances&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) circumstances<br>Circumstances&rsquo;का अर्थ उन तथ्यों और घटनाओं से है जो किसी विशेष स्थिति में होने वाली घटनाओं को प्रभावित करते हैं। दिए गए passage में कहा गया है कि हड्डियों का चयन इसलिए किया गया क्योंकि उनकी मृत्यु की परिस्थितियों ने लोगों को यह विश्वास दिलाया कि ये जानवर एक बड़े जानवर द्वारा मारे गए होंगे। इसलिए, &lsquo;Circumstances&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>