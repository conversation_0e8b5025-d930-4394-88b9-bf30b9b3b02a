<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If (2sin A + cosec A) = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>, 0&deg; &lt; A &lt; 90&deg;, then the value of 2(sin<sup>4</sup>A + cos<sup>4</sup>A) is:</p>",
                    question_hi: "<p>1. यदि (2sin A + cosec A) = 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>, 0&deg; &lt; A &lt; 90&deg; है, तो 2(sin<sup>4</sup>A + cos<sup>4</sup>A) का मान क्या होगा ?</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>0</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>4</p>", "<p>0</p>"],
                    solution_en: "<p>1.(a) 2sin A + cosec A = 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>,&nbsp;<br>at A = 45&deg;<br>Then, 2(sin<sup>4</sup>A + cos<sup>4</sup>A) = 1</p>",
                    solution_hi: "<p>1.(a) 2sin A + cosec A = 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>,&nbsp;<br>at A = 45&deg;<br>Then, 2(sin<sup>4</sup>A + cos<sup>4</sup>A) = 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Solve the following <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>40</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>50</mn><mo>&#176;</mo></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><msup><mn>50</mn><mo>&#8728;</mo></msup></mrow><mrow><mi>sec</mi><msup><mn>40</mn><mo>&#8728;</mo></msup></mrow></mfrac></math> - 4cos 50&deg; cosec 40&deg;</p>",
                    question_hi: "<p>2. निम्नलिखित को हल कीजिए : <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>40</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>50</mn><mo>&#176;</mo></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><msup><mn>50</mn><mo>&#8728;</mo></msup></mrow><mrow><mi>sec</mi><msup><mn>40</mn><mo>&#8728;</mo></msup></mrow></mfrac></math> - 4cos 50&deg; cosec 40&deg;</p>",
                    options_en: ["<p>- 1</p>", "<p>- 2</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>-1</p>", "<p>-2</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>2.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>40</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>50</mn><mo>&#176;</mo></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><msup><mn>50</mn><mo>&#8728;</mo></msup></mrow><mrow><mi>sec</mi><msup><mn>40</mn><mo>&#8728;</mo></msup></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mn>4</mn><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>50</mn><mo>&#176;</mo><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>40</mn><mo>&#176;</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>50</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>50</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mn>50</mn><mo>&#176;</mo></mrow><mrow><mi>cosec</mi><mn>50</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mn>4</mn><mi>cos</mi><mn>50</mn><mo>&#176;</mo><mi>sec</mi><mn>50</mn><mo>&#176;</mo></math> &rArr; - 2</p>",
                    solution_hi: "<p>2.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>40</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>50</mn><mo>&#176;</mo></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><msup><mn>50</mn><mo>&#8728;</mo></msup></mrow><mrow><mi>sec</mi><msup><mn>40</mn><mo>&#8728;</mo></msup></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mn>4</mn><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>50</mn><mo>&#176;</mo><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>40</mn><mo>&#176;</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>50</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>50</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mn>50</mn><mo>&#176;</mo></mrow><mrow><mi>cosec</mi><mn>50</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mn>4</mn><mi>cos</mi><mn>50</mn><mo>&#176;</mo><mi>sec</mi><mn>50</mn><mo>&#176;</mo></math> &rArr; - 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Solve the following sin0&deg; sin30&deg; sin45&deg; sin60&deg; sin90&deg;</p>",
                    question_hi: "<p>3. हल कीजिए : sin0&deg; sin30&deg; sin45&deg; sin60&deg; sin90&deg;</p>",
                    options_en: ["<p>0</p>", "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p>1</p>", "<p>4</p>"],
                    options_hi: ["<p>0</p>", "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p>1</p>", "<p>4</p>"],
                    solution_en: "<p>3.(a) sin0&deg; sin30&deg; sin45&deg; sin60&deg; sin90&deg; = 0 (sin0&deg; = 0)</p>",
                    solution_hi: "<p>3.(a) sin0&deg; sin30&deg; sin45&deg; sin60&deg; sin90&deg; = 0 (sin0&deg; = 0)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If (cos<sup>2</sup>&theta; - 1)(1 + tan<sup>2</sup>&theta;) + 2 tan<sup>2</sup>&theta; = 1, 0&deg; &lt; &theta; &lt; 90&deg;, then &theta; is</p>",
                    question_hi: "<p>4. यदि (cos<sup>2</sup>&theta; - 1)(1 + tan<sup>2</sup>&theta;) + 2tan<sup>2</sup>&theta; = 1 है, जहाँ 0&deg; &lt; &theta; &lt; 90&deg;, तो &theta; का मान होगा :</p>",
                    options_en: ["<p>45&deg;</p>", "<p>60&deg;</p>", 
                                "<p>30&deg;</p>", "<p>90&deg;</p>"],
                    options_hi: ["<p>45&deg;</p>", "<p>60&deg;</p>",
                                "<p>30&deg;</p>", "<p>90&deg;</p>"],
                    solution_en: "<p>4.(a) (cos<sup>2</sup>&theta; - 1)(1 + tan<sup>2</sup>&theta;) + 2 tan<sup>2</sup>&theta; = 1, 0&deg; &lt; &theta; &lt; 90&deg;<br>(-sin<sup>2</sup>&theta;)(sec<sup>2</sup>&theta;) + 2 tan<sup>2</sup>&theta; = 1<br>-tan<sup>2</sup>&theta; + 2tan<sup>2</sup>&theta; = 1 &rArr; tan<sup>2</sup>&theta; = 1 &rArr; &theta; = 45&deg;</p>",
                    solution_hi: "<p>4.(a) (cos<sup>2</sup>&theta; - 1)(1 + tan<sup>2</sup>&theta;) + 2 tan<sup>2</sup>&theta; = 1, 0&deg; &lt; &theta; &lt; 90&deg;<br>(-sin<sup>2</sup>&theta;)(sec<sup>2</sup>&theta;) + 2 tan<sup>2</sup>&theta; = 1<br>-tan<sup>2</sup>&theta; + 2tan<sup>2</sup>&theta; = 1 <br>&nbsp;tan<sup>2</sup>&theta; = 1 , &theta; = 45&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>30</mn><mo>&#176;</mo><mi>sin</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>60</mn><mo>&#176;</mo><mi>cos</mi><mn>30</mn><mo>&#176;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mo>-</mo><mi>tan</mi><mn>45</mn><mo>&#176;</mo></math> is:</p>",
                    question_hi: "<p>5. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>30</mn><mo>&#176;</mo><mi>sin</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>60</mn><mo>&#176;</mo><mi>cos</mi><mn>30</mn><mo>&#176;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> - tan 45&deg;&nbsp; का मान है :</p>",
                    options_en: ["<p>0</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p>5</p>", "<p>2</p>"],
                    options_hi: ["<p>0</p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                                "<p>5</p>", "<p>2</p>"],
                    solution_en: "<p>5.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>30</mn><mo>&#176;</mo><mi>sin</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>60</mn><mo>&#176;</mo><mi>cos</mi><mn>30</mn><mo>&#176;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> - tan 45&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>30</mn><mo>&#176;</mo><mi>sin</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>30</mn><mo>&#176;</mo><mi>sin</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> - tan 45&deg; = 0&deg;</p>",
                    solution_hi: "<p>5.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>30</mn><mo>&#176;</mo><mi>sin</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>60</mn><mo>&#176;</mo><mi>cos</mi><mn>30</mn><mo>&#176;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> - tan 45&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>30</mn><mo>&#176;</mo><mi>sin</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>30</mn><mo>&#176;</mo><mi>sin</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> - tan 45&deg; = 0&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If A lies in the third quadrant, and 20tanA = 21, then the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>sinA</mi><mo>-</mo><mn>2</mn><mi>cosA</mi></mrow><mrow><mn>4</mn><mi>cosA</mi><mo>-</mo><mfrac><mn>5</mn><mn>7</mn></mfrac><mi>sinA</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>6. यदि A तीसरे चतुर्थांश में स्थित है तथा 20 tanA = 21 है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>sinA</mi><mo>-</mo><mn>2</mn><mi>cosA</mi></mrow><mrow><mn>4</mn><mi>cosA</mi><mo>-</mo><mfrac><mn>5</mn><mn>7</mn></mfrac><mi>sinA</mi></mrow></mfrac></math>&nbsp;का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p>1</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>65</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p>1</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>65</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>6.(b) In the third quadrant tanA and cotA are positive. <br>tanA = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>sinA</mi><mo>-</mo><mn>2</mn><mi>cosA</mi></mrow><mrow><mn>4</mn><mi>cosA</mi><mo>-</mo><mn>57</mn><mi>sinA</mi></mrow></mfrac></math> ; Divide numerator and denominator by cosA.<br>We get, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>tanA</mi><mo>-</mo><mn>2</mn></mrow><mrow><mn>4</mn><mo>-</mo><mfrac><mn>5</mn><mn>7</mn></mfrac><mi>tanA</mi></mrow></mfrac></math> = 1</p>",
                    solution_hi: "<p>6.(b) <br>तीसरे चतुर्थांश में tanA और cotA धनात्मक होते हैं।<br>tanA = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>sinA</mi><mo>-</mo><mn>2</mn><mi>cosA</mi></mrow><mrow><mn>4</mn><mi>cosA</mi><mo>-</mo><mn>57</mn><mi>sinA</mi></mrow></mfrac></math> ;<br>अंश और हर को cosA से भाग देने पर। <br>हम पाते हैं की, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>tanA</mi><mo>-</mo><mn>2</mn></mrow><mrow><mn>4</mn><mo>-</mo><mfrac><mn>5</mn><mn>7</mn></mfrac><mi>tanA</mi></mrow></mfrac></math>&nbsp;= 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The value of 4[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>secA</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>secA</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math>] is:</p>",
                    question_hi: "<p>7. 4 [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>secA</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>secA</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math>] का मान है :</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>8</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>8</p>", "<p>1</p>"],
                    solution_en: "<p>7.(c) 4[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>secA</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>secA</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math>] <br>= 4[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sec</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>2</mn><mi>secA</mi><mo>)</mo><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sec</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>2</mn><mi>secA</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math>] = 8</p>",
                    solution_hi: "<p>7.(c) 4[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>secA</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>secA</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math>] <br>= 4[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sec</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>2</mn><mi>secA</mi><mo>)</mo><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sec</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>2</mn><mi>secA</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math>] = 8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><msup><mi>&#952;cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> - 1 is:</p>",
                    question_hi: "<p>8. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><msup><mi>&#952;cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> - 1 का मान है :</p>",
                    options_en: ["<p>-2sin<sup>2</sup>&theta;cos<sup>2</sup>&theta;</p>", "<p>-1</p>", 
                                "<p>0</p>", "<p>1</p>"],
                    options_hi: ["<p>-2sin<sup>2</sup>&theta;cos<sup>2</sup>&theta;</p>", "<p>-1</p>",
                                "<p>0</p>", "<p>1</p>"],
                    solution_en: "<p>8.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><msup><mi>&#952;cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> - 1&nbsp;<br>Put &theta; = 0&deg;,we get <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><msup><mi>&#952;cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> -1 = 0</p>",
                    solution_hi: "<p>8.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><msup><mi>&#952;cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> - 1 <br>&theta; = 0&deg; रखें।,<br>हम पाते हैं की, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><msup><mi>&#952;cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> - 1 = 0</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. What is the value of sin 30&deg; + cos 30&deg; - tan 45&deg; ?</p>",
                    question_hi: "<p>9. sin 30&deg; + cos 30&deg; - tan 45&deg; का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>9.(a) sin 30&deg; + cos 30&deg; - tan 45&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> - 1 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>9.(a) sin 30&deg; + cos 30&deg; - tan 45&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> - 1 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If 3sec<sup>2</sup>&theta; + tan&theta; = 7, 0&deg; &lt; &theta; &lt; 90&deg;,then the value of&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cos&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cot&#952;</mi></mrow></mfrac></math>&nbsp; is:</p>",
                    question_hi: "<p>10. यदि 3sec<sup>2</sup>&theta; + tan&theta; = 7 है, जहाँ 0&deg; &lt; &theta; &lt; 90&deg; है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cos&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cot&#952;</mi></mrow></mfrac></math>&nbsp;का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>10.(c) 3sec<sup>2</sup>&theta; + tan&theta; = 7 <br>sec<sup>2</sup>&theta; = 1 + tan<sup>2</sup>&theta;<br>3tan<sup>2</sup>&theta; + tan&theta; - 4 = 0 &rArr; tan&theta; = 45&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cos&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cot&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mn>90</mn><mo>&#176;</mo><mo>+</mo><mi>cos</mi><mn>45</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>90</mn><mo>&#176;</mo><mo>+</mo><mi>cot</mi><mn>45</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow><mrow><mn>1</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>10.(c) 3sec<sup>2</sup>&theta; + tan&theta; = 7 <br>sec<sup>2</sup>&theta; = 1 + tan<sup>2</sup>&theta;<br>3tan<sup>2</sup>&theta; + tan&theta; - 4 = 0 &rArr; tan&theta; = 45&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cos&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cot&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mn>90</mn><mo>&#176;</mo><mo>+</mo><mi>cos</mi><mn>45</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>90</mn><mo>&#176;</mo><mo>+</mo><mi>cot</mi><mn>45</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow><mrow><mn>1</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>2</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>+</mo><mi>cosA</mi></mrow><mi>cosA</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>12</mn></mfrac></math>, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math> is:</p>",
                    question_hi: "<p>11. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>+</mo><mi>cosA</mi></mrow><mi>cosA</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>12</mn></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math> का मान क्या होगा ?</p>",
                    options_en: ["<p>-5</p>", "<p>1</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>-5</p>", "<p>1</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>+</mo><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mi>cosA</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>12</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math> and sinA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>-</mo><mn>12</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>11.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>+</mo><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mi>cosA</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>12</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math> and sinA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>-</mo><mn>12</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If cot&theta; + tan&theta; = 2sec&theta;, 0&deg; &lt; &theta; &lt; 90&deg;, then the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mi>sec&#952;</mi></mrow><mrow><mi>cot</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cosec&#952;</mi></mrow></mfrac></math> is</p>",
                    question_hi: "<p>12. यदि cot&theta; + tan&theta; = 2sec&theta;, 0&deg; &lt; &theta; &lt; 90&deg; है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mi>sec&#952;</mi></mrow><mrow><mi>cot</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cosec&#952;</mi></mrow></mfrac></math> का मान क्या होगा?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi></msqrt><mo>-</mo><mn>1</mn></mrow><mn>11</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>5</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>5</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>11</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi></msqrt><mo>-</mo><mn>1</mn></mrow><mn>11</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>5</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>5</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>11</mn></mfrac></math></p>"],
                    solution_en: "<p>12.(a) cot&theta; + tan&theta; = 2sec&theta;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cos&#952;</mi><mi>sin&#952;</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sin&#952;</mi><mi>cos&#952;</mi></mfrac></math> = 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>cos&#952;</mi></mfrac></math><br>cos<sup>2</sup>&theta; + sin<sup>2</sup>&theta; = 2sin&theta;<br>1 - sin<sup>2</sup>&theta; + sin<sup>2</sup>&theta; = 2sin&theta;<br>Sin&theta; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rArr; &theta; = 30&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mi>sec&#952;</mi></mrow><mrow><mi>cot</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cosec&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi>sec</mi><mn>30</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>cosec</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mrow><mrow><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>11</mn></mfrac></math></p>",
                    solution_hi: "<p>12.(a) cot&theta; + tan&theta; = 2sec&theta;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cos&#952;</mi><mi>sin&#952;</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sin&#952;</mi><mi>cos&#952;</mi></mfrac></math> = 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>cos&#952;</mi></mfrac></math><br>cos<sup>2</sup>&theta; + sin<sup>2</sup>&theta; = 2sin&theta;<br>1 - sin<sup>2</sup>&theta; + sin<sup>2</sup>&theta; = 2sin&theta;<br>Sin&theta; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rArr; &theta; = 30&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mi>sec&#952;</mi></mrow><mrow><mi>cot</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cosec&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi>sec</mi><mn>30</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>cosec</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mrow><mrow><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>11</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If 5cos<sup>2</sup>&theta; + 1 = 3sin<sup>2</sup>&theta;, 0&deg; &lt; &theta; &lt; 90&deg;, then what is the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan&#952;</mi><mo>+</mo><mi>sec&#952;</mi></mrow><mrow><mi>cot&#952;</mi><mo>+</mo><mi>cosec&#952;</mi></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>13. यदि 5cos<sup>2</sup>&theta; + 1 = 3sin<sup>2</sup>&theta;, 0&deg; &lt; &theta; &lt; 90&deg; है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan&#952;</mi><mo>+</mo><mi>sec&#952;</mi></mrow><mrow><mi>cot&#952;</mi><mo>+</mo><mi>cosec&#952;</mi></mrow></mfrac></math> का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>13.(b) 5cos<sup>2</sup>&theta; + 1 = 3sin<sup>2</sup>&theta;<br>5(1 - sin<sup>2</sup>&theta;) + 1 = 3sin<sup>2</sup>&theta;<br>6 = 8sin<sup>2</sup>&theta; &rArr; sin&theta; = 60&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan&#952;</mi><mo>+</mo><mi>sec&#952;</mi></mrow><mrow><mi>cot&#952;</mi><mo>+</mo><mi>cosec&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>sec</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>cosec</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>2</mn></mrow><mrow><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>+</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>13.(b) 5cos<sup>2</sup>&theta; + 1 = 3sin<sup>2</sup>&theta;<br>5(1 - sin<sup>2</sup>&theta;) + 1 = 3sin<sup>2</sup>&theta;<br>6 = 8sin<sup>2</sup>&theta; &rArr; sin&theta; = 60&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan&#952;</mi><mo>+</mo><mi>sec&#952;</mi></mrow><mrow><mi>cot&#952;</mi><mo>+</mo><mi>cosec&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>sec</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>cosec</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>2</mn></mrow><mrow><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>+</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Solve the following (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math>) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math>)<sup>2</sup></p>",
                    question_hi: "<p>14. निम्नलिखित को हल कीजिए (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math>) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math>)<sup>2</sup></p>",
                    options_en: ["<p>2</p>", "<p>1</p>", 
                                "<p>0</p>", "<p>- 1</p>"],
                    options_hi: ["<p>2</p>", "<p>1</p>",
                                "<p>0</p>", "<p>-1</p>"],
                    solution_en: "<p>14.(c) (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math>) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><msup><mn>27</mn><mo>&#8728;</mo></msup></mrow><mrow><mi>sin</mi><msup><mn>27</mn><mo>&#8728;</mo></msup></mrow></mfrac></math>) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><msup><mn>63</mn><mo>&#8728;</mo></msup></mrow><mrow><mi>sin</mi><msup><mn>63</mn><mo>&#8728;</mo></msup></mrow></mfrac></math>)<sup>2</sup> = 1 - 1 = 0</p>",
                    solution_hi: "<p>14.(c) (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math>) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><msup><mn>27</mn><mo>&#8728;</mo></msup></mrow><mrow><mi>sin</mi><msup><mn>27</mn><mo>&#8728;</mo></msup></mrow></mfrac></math>) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><msup><mn>63</mn><mo>&#8728;</mo></msup></mrow><mrow><mi>sin</mi><msup><mn>63</mn><mo>&#8728;</mo></msup></mrow></mfrac></math>)<sup>2</sup> = 1 - 1 = 0</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. If 6tan&theta; - 5<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi>sec&#952;</mi></math> + 12 cot&theta; = 0, 0&deg; &lt; &theta; &lt; 90&deg;, then the value of (cosec&theta; + sec&theta;) is:</p>",
                    question_hi: "<p>15. यदि 6tan&theta; - 5<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi>sec&#952;</mi></math> + 12 cot&theta; = 0, 0&deg; &lt; &theta; &lt; 90&deg; है, तो (cosec&theta; + sec&theta;) का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>(</mo><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>(</mo><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>3</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>(</mo><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>(</mo><mn>3</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>3</mn></mfrac></math></p>"],
                    solution_en: "<p>15.(b) 6tan&theta; - 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>sec&theta; + 12cot&theta; = 0<br>let &theta; = 60&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> 6tan60&deg; - 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>sec 60&deg; + 12cot60&deg; = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &times; 2 + 12 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 0 = 0<br>Cosec&theta; + sec&theta; = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> + 2 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>(3 + <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>)</p>",
                    solution_hi: "<p>15.(b) 6tan&theta; - 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>sec&theta; + 12cot&theta; = 0<br><strong id=\"docs-internal-guid-c5f70890-7fff-3d18-bd47-fedd01b47ff0\"><math display=\"inline\"><mo>&#8658;</mo></math> </strong>माना की,<strong id=\"docs-internal-guid-dfed463f-7fff-087b-6551-4797a44db785\"> </strong>&nbsp;= 60&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> 6tan60&deg; - 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>sec 60&deg; + 12cot60&deg; = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &times; 2 + 12 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 0 = 0<br>Cosec&theta; + sec&theta; = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> + 2 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>(3 + <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. The value of (cosecA + cotA +1)(cosecA - cotA + 1) - 2cosecA is:</p>",
                    question_hi: "<p>16. (cosecA + cotA +1)(cosecA - cotA + 1) - 2cosecA का मान है :</p>",
                    options_en: ["<p>4cosecA</p>", "<p>2</p>", 
                                "<p>2cosecA</p>", "<p>0</p>"],
                    options_hi: ["<p>4cosecA</p>", "<p>2</p>",
                                "<p>2cosecA</p>", "<p>0</p>"],
                    solution_en: "<p>16.(b) ([cosecA + cotA] + 1)<br>([cosecA - cotA] + 1) - 2cosecA<br><math display=\"inline\"><mo>&#8658;</mo></math>cosec<sup>2</sup>A - cot<sup>2</sup>A + cosecA + cotA + <br>cosecA - cotA + 1 - 2cosecA<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 + 2cosecA + 1 - 2cosecA = 2</p>",
                    solution_hi: "<p>16.(b) ([cosecA + cotA] + 1)([cosecA - cotA] + 1) - 2cosecA<br>cosec<sup>2</sup>A - cot<sup>2</sup>A + cosecA + cotA + cosecA - cotA + 1 - 2cosecA<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 + 2cosecA + 1 - 2cosecA &rArr; 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. Solve the following<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin</mi><mn>22</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>68</mn><mo>&#176;</mo></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>cot</mi><msup><mn>75</mn><mo>&#8728;</mo></msup></mrow><mrow><mn>5</mn><mi>tan</mi><msup><mn>15</mn><mo>&#8728;</mo></msup></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mi>tan</mi><mn>45</mn><mo>&#176;</mo><mi>tan</mi><mn>20</mn><mo>&#176;</mo><mi>tan</mi><mn>40</mn><mo>&#176;</mo><mi>tan</mi><mn>50</mn><mo>&#176;</mo><mi>tan</mi><mn>70</mn><mo>&#176;</mo></mrow><mn>5</mn></mfrac></math>&nbsp;</p>",
                    question_hi: "<p>17. हल कीजिए । <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin</mi><mn>22</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>68</mn><mo>&#176;</mo></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>cot</mi><msup><mn>75</mn><mo>&#8728;</mo></msup></mrow><mrow><mn>5</mn><mi>tan</mi><msup><mn>15</mn><mo>&#8728;</mo></msup></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mi>tan</mi><mn>45</mn><mo>&#176;</mo><mi>tan</mi><mn>20</mn><mo>&#176;</mo><mi>tan</mi><mn>40</mn><mo>&#176;</mo><mi>tan</mi><mn>50</mn><mo>&#176;</mo><mi>tan</mi><mn>70</mn><mo>&#176;</mo></mrow><mn>5</mn></mfrac></math>&nbsp;</p>",
                    options_en: ["<p>1</p>", "<p>3</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>1</p>", "<p>3</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p>17.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin</mi><mn>22</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>68</mn><mo>&#176;</mo></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>cot</mi><msup><mn>75</mn><mo>&#8728;</mo></msup></mrow><mrow><mn>5</mn><mi>tan</mi><msup><mn>15</mn><mo>&#8728;</mo></msup></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mi>tan</mi><mn>45</mn><mo>&#176;</mo><mi>tan</mi><mn>20</mn><mo>&#176;</mo><mi>tan</mi><mn>40</mn><mo>&#176;</mo><mi>tan</mi><mn>50</mn><mo>&#176;</mo><mi>tan</mi><mn>70</mn><mo>&#176;</mo></mrow><mn>5</mn></mfrac></math> = 0 <br>(cos&theta; = sin (90<math display=\"inline\"><mo>&#176;</mo></math> - &theta;) and tan 45&deg; = 1)</p>",
                    solution_hi: "<p>17.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin</mi><mn>22</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>68</mn><mo>&#176;</mo></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>cot</mi><msup><mn>75</mn><mo>&#8728;</mo></msup></mrow><mrow><mn>5</mn><mi>tan</mi><msup><mn>15</mn><mo>&#8728;</mo></msup></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mi>tan</mi><mn>45</mn><mo>&#176;</mo><mi>tan</mi><mn>20</mn><mo>&#176;</mo><mi>tan</mi><mn>40</mn><mo>&#176;</mo><mi>tan</mi><mn>50</mn><mo>&#176;</mo><mi>tan</mi><mn>70</mn><mo>&#176;</mo></mrow><mn>5</mn></mfrac></math> = 0 <br>(cos&theta; = sin (90<math display=\"inline\"><mo>&#176;</mo></math> - &theta;) and tan 45&deg; = 1)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>(</mo><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>18. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>(</mo><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math>&nbsp;का मान क्या होगा ?</p>",
                    options_en: ["<p>4</p>", "<p>3</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>3</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>18.(b) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>(</mo><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>(</mo><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> &nbsp;= 3</p>",
                    solution_hi: "<p>18.(b) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>(</mo><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>(</mo><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> &nbsp;= 3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. The value of cos 10&deg; cos 30&deg; cos 50&deg; cos 70&deg; cos 90&deg; is:</p>",
                    question_hi: "<p>19. cos 10&deg; cos 30&deg; cos 50&deg; cos 70&deg; cos 90&deg; का मान है :</p>",
                    options_en: ["<p>3</p>", "<p>0</p>", 
                                "<p>5</p>", "<p>1</p>"],
                    options_hi: ["<p>3</p>", "<p>0</p>",
                                "<p>5</p>", "<p>1</p>"],
                    solution_en: "<p>19.(b) <strong>Short-Trick:</strong> cos 90&deg; = 0 <br>Therefore, <br>cos10&deg;cos30&deg;cos50&deg;cos70&deg;cos 90&deg; = 0</p>",
                    solution_hi: "<p>19.(b) cos 90&deg; = 0 <br>इसलिए,<br>cos10&deg;cos30&deg;cos50&deg;cos70&deg;cos 90&deg;&nbsp;= 0</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20. The value of (cosec 30&deg; - tan 45&deg;)cot 60&deg; tan 30&deg; is:</p>",
                    question_hi: "<p>20. (cosec 30&deg; - tan 45&deg;)cot 60&deg; tan 30&deg; का मान क्या है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>20.(a) <br>(cosec 30&deg; - tan 45&deg;)cot 60&deg;tan 30&deg; <br>= (2 - 1)(<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>20.(a) <br>(cosec 30&deg; - tan 45&deg;)cot 60&deg;tan 30&deg; <br>= (2 - 1)(<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>