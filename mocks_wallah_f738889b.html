<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The OECD (The Organisation for Economic Co-operation and Development) headquarters are situated in:</p>",
                    question_hi: "<p>1. OECD (आर्थिक सहयोग और विकास संगठन) का मुख्यालय कहाँ स्थित हैं?</p>",
                    options_en: ["<p>Paris</p>", "<p>Geneva</p>", 
                                "<p>Washington DC</p>", "<p>Tokyo</p>"],
                    options_hi: ["<p>पेरिस</p>", "<p>जिनेवा</p>",
                                "<p>वाशिंगटन डी.सी</p>", "<p>टोक्यो</p>"],
                    solution_en: "<p>16.(a) <strong>Paris</strong>. OECD : Founded in - 1961, Total members - 38, <strong>Objective</strong> - To enhance economic progress and world trade. India is not a member. <strong>Some international Organisations and their headquarters :</strong> United Nations - New York, World Bank - Washington D.C, World Health Organisations (WHO) - Geneva.</p>",
                    solution_hi: "<p>16.(a) <strong>पेरिस</strong> । OECD : स्थापना - 1961, कुल सदस्य - 38, <strong>उद्देश्य</strong> - आर्थिक प्रगति और विश्व व्यापार को बढ़ाना। भारत इसका सदस्य नहीं है।<strong> कुछ अंतर्राष्ट्रीय संगठन और उनके मुख्यालय:</strong> संयुक्त राष्ट्र - न्यूयॉर्क, विश्व बैंक - वाशिंगटन डी.सी., विश्व स्वास्थ्य संगठन (WHO) - जिनेवा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Of which of the following organisations is India NOT a member ?</p>",
                    question_hi: "<p>2. निम्नलिखित में से किस संगठन का भारत सदस्य नहीं है</p>",
                    options_en: ["<p>Shanghai Cooperation organisation</p>", "<p>G 20</p>", 
                                "<p>United Nations</p>", "<p>North Atlantic Treaty Organisation</p>"],
                    options_hi: ["<p>शंघाई सहयोग संगठन (SCO)</p>", "<p>G 20</p>",
                                "<p>संयुक्त राष्ट्र (UN)</p>", "<p>उत्तरी अटलांटिक संधि संगठन (NATO)</p>"],
                    solution_en: "<p>2.(d) <strong>North Atlantic Treaty Organisation (NATO)</strong> - A military alliance established in April 1949. Headquarters - Brussels (Belgium). Members - 31. Latest member - Finland. Shanghai Cooperation organization (SCO) : Founded in 2001, Headquarters - Beijing (China), Member States - 9 (China, India, Kazakhstan, Kyrgyzstan, Russia, Pakistan, Tajikistan, Uzbekistan and Iran). G20 (Group of Twenty) - An intergovernmental forum comprising 19 countries and the European Union (EU). United Nations (UN) - Founded in 1945, Headquarters - New York, Members- 193 (Latest - South Sudan).</p>",
                    solution_hi: "<p>2.(d) <strong>उत्तरी अटलांटिक संधि संगठन (NATO) -</strong> अप्रैल 1949 में एक सैन्य गठबंधन स्थापित हुआ। मुख्यालय - ब्रुसेल्स (बेल्जियम)। सदस्य - 31 । नवीनतम सदस्य - फ़िनलैंड। शंघाई सहयोग संगठन (SCO): स्थापना : 2001 में, मुख्यालय - बीजिंग (चीन), सदस्य देश - 9 (चीन, भारत, कजाकिस्तान, किर्गिस्तान, रूस, पाकिस्तान, ताजिकिस्तान, उज्बेकिस्तान और ईरान)। G20 (ग्रुप ऑफ ट्वेंटी) - एक अंतरसरकारी मंच जिसमें 19 देश और यूरोपीय संघ (EU) शामिल हैं। संयुक्त राष्ट्र (UN) - 1945 में स्थापित हुआ, मुख्यालय - न्यूयॉर्क, सदस्य - 193 (नवीनतम - दक्षिण सूडान)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The number of non-permanent members of the UN Security Council is :</p>",
                    question_hi: "<p>18. संयुक्त राष्ट्र सुरक्षा परिषद के अस्थाई सदस्यों की संख्या कितनी है?</p>",
                    options_en: ["<p>9</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>11</p>"],
                    options_hi: ["<p>9</p>", "<p>10</p>",
                                "<p>12</p>", "<p>11</p>"],
                    solution_en: "<p>3.(b) <strong>10.</strong> The UN Security Council - One of the six main organs of the United Nations (UN) which was established in 1945. It has 15 members. 5 permanent members with veto power (China, France, Russian, United Kingdom and United States of America). 10 non-permanent members (Elected each year for a two-year term).</p>",
                    solution_hi: "<p>3.(b) <strong>10 । </strong>संयुक्त राष्ट्र सुरक्षा परिषद - संयुक्त राष्ट्र (UN) के छह मुख्य अंगों में से एक, जिसकी स्थापना 1945 में हुई थी। इसके 15 सदस्य हैं। वीटो शक्ति वाले 5 स्थायी सदस्य (चीन, फ्रांस, रूस, ब्रिटेन और संयुक्त राज्य अमेरिका)। 10 गैर-स्थायी सदस्य (प्रत्येक वर्ष दो वर्ष के कार्यकाल के लिए निर्वाचित किये जाते है)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. What is G - 7 ?</p>",
                    question_hi: "<p>4. G - 7 क्या है?</p>",
                    options_en: ["<p>7 sister states of India</p>", "<p>7 seas of the world</p>", 
                                "<p>7 continents of the world</p>", "<p>7 IMF- described advanced economies in the world</p>"],
                    options_hi: ["<p>भारत का 7 सिस्टर राज्य</p>", "<p>दुनिया के 7 समुद्र</p>",
                                "<p>विश्व के 7 महाद्वीप</p>", "<p>7 IMF- विश्व में उन्नत अर्थव्यवस्थाओं का वर्णन करता है ।</p>"],
                    solution_en: "<p>4.(d)<strong> 7 IMF - described advanced economies in the world. Group of Seven</strong> (G-7) is an intergovernmental organization made up of the world\'s largest IMF advanced economies - France, Germany, Italy, Japan, United States, United Kingdom, and Canada. <strong>Founded</strong> in 1975. The group meets <strong>once a year </strong>to talk about topics like international security, energy policy, and global economic governance. The secretariat and offices of the G7 are not permanent, and the Presidency rotates every year.</p>",
                    solution_hi: "<p>4.(d) <strong>7 IMF - विश्व में उन्नत अर्थव्यवस्थाओं का वर्णन करता है। ग्रुप ऑफ सेवन</strong> (G-7) एक अंतरसरकारी संगठन है जो दुनिया की सबसे बड़ी IMF उन्नत अर्थव्यवस्थाओं - फ्रांस, जर्मनी, इटली, जापान, संयुक्त राज्य अमेरिका, यूनाइटेड किंगडम और कनाडा से मिलकर बना है। स्थापना-1975 । यह समूह अंतरराष्ट्रीय सुरक्षा, ऊर्जा नीति और वैश्विक आर्थिक प्रशासन जैसे विषयों पर बात करने के लिए साल में एक बार बैठक करते है। G-7 का सचिवालय और अधिकार स्थायी नहीं हैं, और इसकी अध्यक्षता हर साल बदलती रहती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. What is the driving force and executive body of the European Union (EU)?</p>",
                    question_hi: "<p>5. यूरोपीय संघ (EU) की प्रेरक शक्ति और कार्यकारी निकाय क्या है?</p>",
                    options_en: ["<p>European Parliament</p>", "<p>Council of the European Union</p>", 
                                "<p>European Commission</p>", "<p>Court of Auditors</p>"],
                    options_hi: ["<p>यूरोपीय संसद</p>", "<p>यूरोपीय संघ की परिषद</p>",
                                "<p>यूरोपीय आयोग</p>", "<p>लेखा परीक्षकों का न्यायालय</p>"],
                    solution_en: "<p>5.(c) <strong>European Commission -</strong> Upholds the common interest of the EU as a whole and serves as the EU\'s executive. It is the executive arm of the Union. <strong>The European Union (EU) </strong>- A political and economic union consisting of 27 member states subject to the membership\'s obligations and privileges. The E.U came into existence under the Maastricht Treaty in 1993. Headquarter-Brussels.</p>",
                    solution_hi: "<p>5.(c) <strong>यूरोपीय आयोग ।</strong> समग्र रूप से यूरोपीय संघ के सामान्य हित को बनाये रखता है और यूरोपीय संघ की कार्यकारिणी के रूप में कार्य करता है। यह संघ की कार्यकारी शाखा है। <strong>यूरोपीय संघ (EU) - </strong>एक राजनीतिक और आर्थिक संघ जिसमें 27 सदस्य देश शामिल हैं जो सदस्यता के दायित्वों और विशेषाधिकारों के अधीन हैं। EU, 1993 में मास्ट्रिच संधि के तहत अस्तित्व में आया। <strong>मुख्यालय</strong>-ब्रुसेल्स।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. The Universal Declaration of Human Right (UDHR) is a historic document that was adopted by the United Nations on.</p>",
                    question_hi: "<p>6. मानव अधिकार की सार्वभौम घोषणा (UDHR) एक ऐतिहासिक दस्तावेज है जिसे संयुक्त राष्ट्र द्वारा अपनाया गया था</p>",
                    options_en: ["<p>1 January 1948</p>", "<p>1 December 1948</p>", 
                                "<p>31 December 1945</p>", "<p>10 December 1948</p>"],
                    options_hi: ["<p>1 जनवरी 1948</p>", "<p>1 दिसंबर 1948</p>",
                                "<p>31 दिसंबर 1945</p>", "<p>10 दिसंबर 1948</p>"],
                    solution_en: "<p>6.(d) <strong>10 December 1948. </strong>The <strong>Universal Declaration of Human Right </strong>(UDHR) was accepted by the General Assembly during its<strong> third session</strong> at the Palais de Chaillot in Paris, France. The Declaration consists of <strong>30 articles</strong> about human rights and individual liberty. <br>Member Countries: 193.</p>",
                    solution_hi: "<p>6.(d)<strong> 10 दिसंबर 1948। </strong>मानव अधिकार की सार्वभौम घोषणा (UDHR) को फ्रांस के पेरिस में पैलैस डी चैलोट में अपने तीसरे सत्र के दौरान महासभा द्वारा स्वीकार किया गया था। घोषणा में मानवाधिकार और व्यक्तिगत स्वतंत्रता के बारे में 30 लेख शामिल हैं। सदस्य देश: 193</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Jean Henry Durant , a Swiss businessman, was the main inspiration for the setup of which international humanitarian organisation?</p>",
                    question_hi: "<p>7. जीन हेनरी ड्यूरेंट, एक स्विस व्यवसायी, किस अंतर्राष्ट्रीय मानवतावादी संगठन की स्थापना के लिए मुख्य प्रेरणा थे?</p>",
                    options_en: ["<p>World Food Programme</p>", "<p>World Health Organisation</p>", 
                                "<p>International Committee of the Red Cross</p>", "<p>Global Forum for Disaster Reduction</p>"],
                    options_hi: ["<p>विश्व खाद्य कार्यक्रम</p>", "<p>विश्व स्वास्थ्य संगठन</p>",
                                "<p>रेड क्रॉस की अंतर्राष्ट्रीय समिति</p>", "<p>आपदा न्यूनीकरण के लिए वैश्विक मंच</p>"],
                    solution_en: "<p>7.(c) <strong>International Committee of the Red Cross. </strong>Jean Henry Durant won the Nobel Peace Prize (1901). <strong>International Committee of the Red Cross </strong>(ICRC) - Established on 17 February 1863 ensuring humanitarian protection and assistance for victims of war and other situations of violence. <strong>World Food Programme: </strong>Establish - 19 December 1961. Headquarter - Rome (Italy). <strong>World Health Organisation: </strong>Establish - 7 April 1948. Headquarter - Geneva (Switzerland).</p>",
                    solution_hi: "<p>7.(c) <strong>रेड क्रॉस की अंतर्राष्ट्रीय समिति।</strong> जीन हेनरी डुरैंट ने नोबेल शांति पुरस्कार (1901) जीता।<strong> रेड क्रॉस की अंतर्राष्ट्रीय समिति</strong> (ICRC) - युद्ध और हिंसा की अन्य स्थितियों के पीड़ितों के लिए मानवीय सुरक्षा और सहायता सुनिश्चित करने के लिए 17 फरवरी 1863 को स्थापित की गई। <strong>विश्व खाद्य कार्यक्रम: </strong>स्थापना - 19 दिसम्बर 1961। मुख्यालय - रोम (इटली)। <strong>विश्व स्वास्थ्य संगठन:</strong> स्थापना - 7 अप्रैल 1948। मुख्यालय - जिनेवा (स्विट्जरलैंड)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. In which year was the International Labour Organisation (ILO) established?</p>",
                    question_hi: "<p>8. अंतर्राष्ट्रीय श्रम संगठन (ILO) की स्थापना किस वर्ष हुई थी?</p>",
                    options_en: ["<p>1919</p>", "<p>1921</p>", 
                                "<p>1931</p>", "<p>1909</p>"],
                    options_hi: ["<p>1919</p>", "<p>1921</p>",
                                "<p>1931</p>", "<p>1909</p>"],
                    solution_en: "<p>8.(a) <strong>1919. International Labour Organisation</strong> (ILO) - It was established as part of the Treaty of Versailles that ended World War I (1914-1918), to promote rights at work, encourage decent employment opportunities, enhance social protection and strengthen dialogue on work-related issues. In 1946, the ILO became a specialized agency of the United Nations. Headquarter - Geneva (Switzerland).</p>",
                    solution_hi: "<p>8.(a) <strong>1919 । अंतर्राष्ट्रीय श्रम संगठन (ILO) -</strong> इसकी स्थापना प्रथम विश्व युद्ध (1914-1918) को समाप्त करने वाली वर्साय की संधि के हिस्से के रूप में की गई थी, ताकि कार्य पर अधिकारों को बढ़ावा दिया जा सके, सभ्य रोजगार के अवसरों को प्रोत्साहित किया जा सके, सामाजिक सुरक्षा बढ़ाई जा सके और कार्य से संबंधित मुद्दों पर बातचीत को मजबूत किया जा सके। 1946 में, ILO संयुक्त राष्ट्र की एक विशेष एजेंसी बन गई। मुख्यालय - जिनेवा (स्विट्जरलैंड)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which UN organization deals with illicit trafficking and drug abuse?</p>",
                    question_hi: "<p>9. संयुक्त राष्ट्र का कौन सा संगठन अवैध तस्करी और नशीली दवाओं के दुरुपयोग से संबंधित है?</p>",
                    options_en: ["<p>UNEP</p>", "<p>UNODC</p>", 
                                "<p>UNICEF</p>", "<p>UNFPA</p>"],
                    options_hi: ["<p>UNEP</p>", "<p>UNODC</p>",
                                "<p>UNICEF</p>", "<p>UNFPA</p>"],
                    solution_en: "<p>9.(b) <strong>UNODC </strong>(United Nations Office on Drugs and Crime): Established - 1997. Headquarter - Vienna (Austria). <strong>UNICEF </strong>(United Nations Children\'s Fund): Established - 1946. Headquarter - New York (US). <strong>UNEP </strong>(United Nations Environment Programme): Established - 1972. Headquarter - Nairobi (Kenya). <strong>UNFPA </strong>(United Nations Population Fund): Established - 1969. Headquarter - New York (United States).</p>",
                    solution_hi: "<p>9.(b) <strong>UNODC </strong>(ड्रग्स और अपराध पर संयुक्त राष्ट्र कार्यालय): स्थापना - 1997। मुख्यालय - वियना (ऑस्ट्रिया)। <strong>UNICEF </strong>(संयुक्त राष्ट्र बाल कोष): स्थापना - 1946। मुख्यालय - न्यूयॉर्क (संयुक्त राज्य अमेरिका)। UNEP (संयुक्त राष्ट्र पर्यावरण कार्यक्रम): स्थापना - 1972। मुख्यालय - नैरोबी (केन्या)। <strong>UNFPA </strong>(संयुक्त राष्ट्र जनसंख्या कोष): स्थापना - 1969। मुख्यालय - न्यूयॉर्क (संयुक्त राज्य अमेरिका)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The headquarters of the UNEP is located at:</p>",
                    question_hi: "<p>10. UNEP का मुख्यालय कहाँ स्थित है?</p>",
                    options_en: ["<p>Mandaluyong, Philippines</p>", "<p>Johannesburg, South Africa</p>", 
                                "<p>Nairobi, Kenya</p>", "<p>Frankfurt, Germany</p>"],
                    options_hi: ["<p>मंडालुयोंग, फिलीपींस</p>", "<p>जोहान्सबर्ग, दक्षिण अफ्रीका</p>",
                                "<p>नैरोबी, केन्या</p>", "<p>फ्रैंकफर्ट, जर्मनी</p>"],
                    solution_en: "<p>10.(c) <strong>Nairobi, Kenya. United Nations Environmetal Programme (UNEP) - </strong>It is responsible for coordinating responses to environmental issues within the United Nations. Founded - 5 June 1972. India joined UNEP in 2016 with its office in New Delhi. Major Campaigns - Beat plastic pollution, Beat Pollution, Climate Action Summit, UN75, World Environment Day, Wild for Life.</p>",
                    solution_hi: "<p>10.(c) <strong>नैरोबी, केन्या। संयुक्त राष्ट्र पर्यावरण कार्यक्रम (UNEP) - </strong>यह संयुक्त राष्ट्र में पर्यावरणीय मुद्दों पर प्रतिक्रियाओं के समन्वय के लिए उत्तरदायी है। स्थापना - 5 जून 1972। भारत 2016 में नई दिल्ली में अपने कार्यालय के साथ UNEP में शामिल हुआ। प्रमुख अभियान - प्लास्टिक प्रदूषण को हराएं, प्रदूषण को हराएं, जलवायु कार्रवाई शिखर सम्मेलन, UN75, विश्व पर्यावरण दिवस, वाइल्ड फॉर लाइफ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. The headquarters of which of the following international organisations is situated at Washington DC?</p>",
                    question_hi: "<p>11. निम्नलिखित में से किस अंतर्राष्ट्रीय संगठन का मुख्यालय वाशिंगटन DC में स्थित है?</p>",
                    options_en: ["<p>Organization for Economic Cooperation and Development (OECD)</p>", "<p>European Central Bank (ECB)</p>", 
                                "<p>International Labour Organization (ILO)</p>", "<p>International Monetary Fund (IMF)</p>"],
                    options_hi: ["<p>आर्थिक सहयोग और विकास संगठन (OECD)</p>", "<p>यूरोपीय सेंट्रल बैंक (ECB)</p>",
                                "<p>अंतर्राष्ट्रीय श्रम संगठन (ILO)</p>", "<p>अंतर्राष्ट्रीय मुद्रा कोष (IMF)</p>"],
                    solution_en: "<p>11.(d) <strong>International Monetary Fund -</strong> Founded in 1944 after the Bretton wood conference. Organization for Economic Cooperation and Development - Founded in 1961 and Headquarter - Paris, France. European Central Bank - Founded in 1998 and Headquarter - Frankfurt, Germany. International Labour Organization - Founded in 1919 and Headquarter - Geneva, Switzerland.</p>",
                    solution_hi: "<p>11.(d) <strong>अंतर्राष्ट्रीय मुद्रा कोष - </strong>ब्रेटन वुड सम्मेलन के बाद 1944 में स्थापित किया गया। आर्थिक सहयोग और विकास संगठन - 1961 में स्थापित और मुख्यालय - पेरिस, फ्रांस। यूरोपीय सेंट्रल बैंक - 1998 में स्थापित और मुख्यालय - फ्रैंकफर्ट, जर्मनी। अंतर्राष्ट्रीय श्रम संगठन - 1919 में स्थापित और मुख्यालय - जिनेवा, स्विट्जरलैंड।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. &ldquo;Jagritam Aharnisham&rdquo; or &ldquo;Always Alert&rdquo; is the motto of which organization?</p>",
                    question_hi: "<p>12. \"जागृतम अहर्निशम\" या \"ऑलवेज अलर्ट\" किस संगठन का आदर्श वाक्य है?</p>",
                    options_en: ["<p>The Coast Guard</p>", "<p>Research and Analysis Wing</p>", 
                                "<p>The Indian Army</p>", "<p>Intelligence Bureau </p>"],
                    options_hi: ["<p>तटरक्षक बल</p>", "<p>अनुसंधान और विश्लेषण विंग</p>",
                                "<p>भारतीय सेना</p>", "<p>इंटेलिजेंस ब्यूरो</p>"],
                    solution_en: "<p>12.(d) <strong>Intelligence Bureau - </strong>India\'s internal security and counter-intelligence agency under the Ministry of Home Affairs. <strong>Formed </strong>- 1887, <strong>Headquarters </strong>- New Delhi. <strong>The Coast Guard:</strong> Formed - 18 August 1978, <strong>Motto </strong>- Vayam Raksamah, Headquarters - New Delhi. <strong>The Indian Army: Founded </strong>- 26 January 1950 {(1 April 1895 - first established)}, <strong>Headquarters </strong>- New Delhi, <strong>Motto </strong>- Seva Paramo Dharmah.<strong> Research and Analysis Wing: Formed </strong>- 21 September 1968, <strong>Headquarters </strong>- New Delhi, <strong>Motto </strong>- Dharmo Raksati Raksitah.</p>",
                    solution_hi: "<p>12.(d) <strong>इंटेलिजेंस ब्यूरो -</strong> गृह मंत्रालय के अधीन भारत की आंतरिक सुरक्षा और काउंटर-इंटेलिजेंस एजेंसी। <strong>गठन </strong>- 1887, <strong>मुख्यालय </strong>- नई दिल्ली। <strong>तटरक्षक बल:</strong> गठन - 18 अगस्त 1978, <strong>आदर्श वाक्य - </strong>वयम रक्षमः, <strong>मुख्यालय </strong>- नई दिल्ली। <strong>भारतीय सेना: स्थापना -</strong> 26 जनवरी 1950 {(1 अप्रैल 1895 - पहली बार स्थापना की गई )}, <strong>मुख्यालय </strong>- नई दिल्ली, <strong>आदर्श वाक्य - </strong>सेवा परमो धर्मः। <strong>रिसर्च एण्ड एनालाईसिस विंग:</strong> <strong>गठन </strong>- 21 सितंबर 1968, मुख्यालय - नई दिल्ली, <strong>आदर्श वाक्य - </strong>धर्मो रक्षति रक्षितः।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Where is the headquarters of the International Civil aviation Organisation (ICAO) situated?</p>",
                    question_hi: "<p>13. अंतर्राष्ट्रीय नागरिक उड्डयन संगठन (ICAO) का मुख्यालय कहाँ स्थित है?</p>",
                    options_en: ["<p>Madrid</p>", "<p>Lisbon</p>", 
                                "<p>Montreal</p>", "<p>Ottawa</p>"],
                    options_hi: ["<p>मैड्रिड</p>", "<p>लिस्बन</p>",
                                "<p>मॉन्ट्रियल</p>", "<p>ओटावा</p>"],
                    solution_en: "<p>13.(c) <strong>Montreal </strong>(Canada).<strong> International Civil Aviation Organization</strong> is a specialized agency of the United Nations that coordinates the principles and techniques of international air navigation, and fosters the planning and development of international air transport to ensure safe and orderly growth. <strong>Founded</strong>: 7 December 1944.</p>",
                    solution_hi: "<p>13.(c) <strong>मॉट्रियल </strong>(कनाडा)। <strong>अंतर्राष्ट्रीय नागरिक उड्डयन संगठन</strong> संयुक्त राष्ट्र की एक विशेष एजेंसी है जो अंतर्राष्ट्रीय हवाई नेविगेशन के सिद्धांतों और तकनीकों का समन्वय करती है तथा सुरक्षित और व्यवस्थित विकास सुनिश्चित करने के लिए अंतर्राष्ट्रीय हवाई परिवहन की योजना और विकास को बढ़ावा देती है। <strong>स्थापना:</strong> 7 दिसंबर 1944।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following groups is India NOT a member of?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन सा समूह का सदस्य भारत नहीं है?</p>",
                    options_en: ["<p>The Shanghai Cooperation Organisation (SCO)</p>", "<p>The South Asian Association for Regional Cooperation (SAARC)</p>", 
                                "<p>The Association of Southeast Asian Nations</p>", "<p>The Bay of Bengal Initiative for Multi-Sectoral Technical and Economic Cooperation</p>"],
                    options_hi: ["<p>शंघाई सहयोग संगठन (SCO)</p>", "<p>क्षेत्रीय सहयोग के लिए दक्षिण एशियाई संघ (SAARC)</p>",
                                "<p>दक्षिण पूर्व एशियाई राष्ट्रों का संघ (ASEAN)</p>", "<p>बहु-क्षेत्रीय तकनीकी और आर्थिक सहयोग के लिए बंगाल की खाड़ी पहल (BIMSTEC)</p>"],
                    solution_en: "<p>14.(c) <strong>The Association of Southeast Asian Nations </strong>(ASEAN) - <strong>Established - 8 August 1967</strong> in Bangkok Thailand. Member countries:- Ten Southeast Asian Countries &ndash; Brunei, Cambodia, Indonesia, Laos, Malaysia, Myanmar, the Philippines, Singapore, Thailand and Vietnam. The Shanghai Cooperation Organisation (SCO): Established - 15 June 2001, Headquarters - Beijing (China). The South Asian Association for Regional Cooperation (SAARC):- Established 8 December 1985, Headquarters - Kathmandu (Nepal).</p>",
                    solution_hi: "<p>14.(c)<strong> दक्षिण पूर्व एशियाई राष्ट्रों का संघ</strong> (ASEAN) - <strong>स्थापना - 8 अगस्त 1967</strong> को बैंकॉक थाईलैंड में। सदस्य देश: - दस दक्षिण पूर्व एशियाई देश - ब्रुनेई, कंबोडिया, इंडोनेशिया, लाओस, मलेशिया, म्यांमार, फिलीपींस, सिंगापुर, थाईलैंड और वियतनाम। शंघाई सहयोग संगठन (SCO): स्थापना - 15 जून 2001, मुख्यालय - बीजिंग (चीन)। दक्षिण एशियाई क्षेत्रीय सहयोग संघ (सार्क):- स्थापना 8 दिसंबर 1985, मुख्यालय - काठमांडू (नेपाल)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following principal organs of the United Nations is NOT situated in New York?</p>",
                    question_hi: "<p>15. संयुक्त राष्ट्र का निम्नलिखित में से कौन सा प्रमुख अंग न्यूयॉर्क में स्थित नहीं है?</p>",
                    options_en: ["<p>Secretariat</p>", "<p>Economic and Social Council</p>", 
                                "<p>International Court of Justice</p>", "<p>Trusteeship Council</p>"],
                    options_hi: ["<p>सचिवालय</p>", "<p>आर्थिक और सामाजिक परिषद</p>",
                                "<p>अंतर्राष्ट्रीय न्यायालय</p>", "<p>न्यास परिषद</p>"],
                    solution_en: "<p>15.(c)<strong> International Court of Justice</strong>. It is situated at the Peace Palace in The Hague (Netherlands). It is the only one of the six principal organs of the United Nations not located in New York (United States of America). six principal organs of the United Nations :- General Assembly, Security Council, Economic and Social Council, Trusteeship Council, International Court of Justice, Secretariat.</p>",
                    solution_hi: "<p>15.(c) <strong>अंतर्राष्ट्रीय न्यायालय।</strong> यह हेग (नीदरलैंड) में पीस पैलेस में स्थित है। यह संयुक्त राष्ट्र के छह प्रमुख अंगों में से एकमात्र ऐसा अंग है जो न्यूयॉर्क (संयुक्त राज्य अमेरिका) में स्थित नहीं है। संयुक्त राष्ट्र के छह प्रमुख अंग:- महासभा, सुरक्षा परिषद, आर्थिक एवं सामाजिक परिषद, न्यास परिषद, अंतर्राष्ट्रीय न्यायालय, सचिवालय।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>