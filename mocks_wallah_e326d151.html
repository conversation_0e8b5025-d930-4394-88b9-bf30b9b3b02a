<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>101 &divide; 48 &minus; 13013 &times; 13 + 12 = ?</p>",
                    question_hi: "<p>1. यदि \'+\' और \'&minus;\' को आपस में बदल दिया जाए, और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न-चिन्ह (?) के स्थान पर क्या आयेगा?<br>101 &divide; 48 &minus; 13013 &times; 13 + 12 = ?</p>",
                    options_en: ["<p>5836</p>", "<p>5835</p>", 
                                "<p>5837</p>", "<p>5834</p>"],
                    options_hi: ["<p>5836</p>", "<p>5835</p>",
                                "<p>5837</p>", "<p>5834</p>"],
                    solution_en: "<p>1.(c) <strong>Given:</strong> 101 &divide; 48 &minus; 13013 &times; 13 + 12<br>As per instructions given in the question, after interchanging the symbol &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; we get.<br>101 &times; 48 + 13013 &divide; 13 - 12<br>101 &times; 48 + 1001 - 12<br>4848 + 1001 - 12<br>5849 - 12 = 5837</p>",
                    solution_hi: "<p>1.(c) <strong>दिया गया है</strong>: 101 &divide; 48 &minus; 13013 &times; 13 +12<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'&times;\' और \'&divide;\' तथा \'+\' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है।<br>101 &times; 48 + 13013 &divide; 13 - 12<br>101 &times; 48 + 1001 - 12<br>4848 + 1001 - 12<br>5849 - 12 = 5837</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; are interchanged?<br>4 + 9 &times; 48 &ndash; 6 &divide; 7 = ?</p>",
                    question_hi: "<p>2. निम्नलिखित समीकरण में, यदि \'&divide;\' और \'&ndash;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'+\' को आपस में बदल दिया जाए, तो \'?\' के स्थान पर क्या आएगा?<br>4 + 9 &times; 48 &ndash; 6 &divide; 7 = ?</p>",
                    options_en: ["<p>30</p>", "<p>27</p>", 
                                "<p>41</p>", "<p>37</p>"],
                    options_hi: ["<p>30</p>", "<p>27</p>",
                                "<p>41</p>", "<p>37</p>"],
                    solution_en: "<p>2.(d) <strong>Given:</strong> 4 + 9 &times; 48 &ndash; 6 &divide; 7 = ?<br>As per the instruction given in the question, after interchanging the symbols &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; we get.<br>4 &times; 9 + 48 &divide; 6 - 7 = ? <br>4 &times; 9 + 8 - 7<br>36 + 8 - 7<br>44 - 7 = 37</p>",
                    solution_hi: "<p>2.(d)<strong> दिया गया है:</strong> 4 + 9 &times; 48 &ndash; 6 &divide; 7 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीकों \'&divide;\' और \'-\' तथा \'&times;\' और \'+\' को आपस में बदलने पर हमें प्राप्त होता है।<br>4 &times; 9 + 48 &divide; 6 - 7 = ? <br>4 &times; 9 + 8 - 7<br>36 + 8 - 7<br>44 - 7 = 37</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. What will come in place of \'?\' in the following equation, if \'+\' and \'-\' are interchanged and \'&times;\' and \'&divide;\' are interchanged?<br>63 &times; 9 + 21 - 4 &divide; 5 = ?</p>",
                    question_hi: "<p>3. यदि \'+\' और \'-\' को परस्पर बदल दिया जाए और \'&times;\' और \'&divide;\' को परस्पर बदल दिया जाए, तो निम्नलिखित समीकरण में \'?\' के स्थान पर कितना मान आएगा?<br>63 &times; 9 + 21 - 4 &divide; 5 = ?</p>",
                    options_en: ["<p>4</p>", "<p>7</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>4</p>", "<p>7</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>3.(c) <strong>Given:</strong> 63 &times; 9 + 21 - 4 &divide; 5 = ?<br>As per the instruction given in the question, after interchanging the symbol \'+\' and \'-\' and \'&times;\' and \'&divide;\' we get&nbsp;<br>63 &divide; 9 - 21 + 4 &times; 5 = ?<br>7 - 21 + 4 &times; 5 <br>7 - 21 + 20 <br>27 - 21 = 6</p>",
                    solution_hi: "<p>3.(c) <strong>दिया गया है: </strong>63 &times; 9 + 21 - 4 &divide; 5 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>63 &divide; 9 - 21 + 4 &times; 5 = ?<br>7 - 21 + 4 &times; 5 <br>7 - 21 + 20 <br>27 - 21 = 6</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the number that will come in the place of the question mark(?), if \'+\' and \' -\' are interchanged and \'&times;\' and \'&divide;\' are interchanged<br>31 &divide; 3 - 186 &times; 6 + 17 = ?</p>",
                    question_hi: "<p>4. यदि निम्नलिखित समीकरण में \'+\' और &lsquo;-&rsquo; को आपस में बदल दिया जाए और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो प्रश्न चिह्न (?) के स्थान पर कौन-सी संख्या आएगी ?<br>31 &divide; 3 - 186 &times; 6 + 17 = ?</p>",
                    options_en: ["<p>103</p>", "<p>96</p>", 
                                "<p>118</p>", "<p>107</p>"],
                    options_hi: ["<p>103</p>", "<p>96</p>",
                                "<p>118</p>", "<p>107</p>"],
                    solution_en: "<p>4.(d) <strong>Given:</strong> 31 &divide; 3 - 186 &times; 6 + 17 = ?<br>As per instruction given in question, after interchanging the symbol \'+\' and \' -\' and \'&times;\' and \'&divide;\' we get<br>31 &times; 3 + 186 &divide; 6 - 17 = ?<br>31 &times; 3 + 31 - 17<br>93 + 31 - 17<br>124 - 17 = 107</p>",
                    solution_hi: "<p>4.(d) <strong>दिया गया है:</strong> 31 &divide; 3 - 186&nbsp; 6 + 17 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीक \'+\' और \'-\' और \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>31 &times; 3 + 186 &divide; 6 - 17 = ?<br>31 &times; 3 + 31 - 17<br>93 + 31 - 17<br>124 - 17 = 107</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. What will come in place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>6 &divide; 8 &ndash; 91 &times; 13 + 30 = ?</p>",
                    question_hi: "<p>5. यदि \'+\' और \'&ndash;\' को परस्पर बदल दिया जाए और \'&times;\' और \'&divide;\' को परस्पर बदल दिया जाए, तो निम्नलिखित समीकरण में \'?\' के स्थान पर कितना मान आएगा?<br>6 &divide; 8 &ndash; 91 &times; 13 + 30 = ?</p>",
                    options_en: ["<p>20</p>", "<p>25</p>", 
                                "<p>23</p>", "<p>22</p>"],
                    options_hi: ["<p>20</p>", "<p>25</p>",
                                "<p>23</p>", "<p>22</p>"],
                    solution_en: "<p>5.(b) <strong>Given:</strong> 6 &divide; 8 &ndash; 91 &times; 13 + 30 = ?<br>As per the instructions given in the question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&ndash;&lsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; <br>We get,<br>6 &times; 8 + 91 &divide; 13 - 30 = ?<br>6 &times; 8 + 7 - 30 <br>48 + 7 - 30<br>55 - 30 = 25</p>",
                    solution_hi: "<p>5.(b) <strong>दिया गया है:</strong> 6 &divide; 8 &ndash; 91 &times; 13 + 30 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने के बाद <br>हम पाते हैं,<br>6 &times; 8 + 91 &divide; 13 - 30 = ?<br>6 &times; 8 + 7 - 30 <br>48 + 7 - 30<br>55 - 30 = 25</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>99 &divide; 41 + 208 &times; 2 &ndash; 35 = ?</p>",
                    question_hi: "<p>6. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>99 &divide; 41 + 208 &times; 2 &ndash; 35 = ?</p>",
                    options_en: ["<p>3990</p>", "<p>3890</p>", 
                                "<p>3980</p>", "<p>3390</p>"],
                    options_hi: ["<p>3990</p>", "<p>3890</p>",
                                "<p>3980</p>", "<p>3390</p>"],
                    solution_en: "<p>6.(a) <strong>Given:</strong> 99 &divide; 41 + 208 &times; 2 &ndash; 35 = ?<br>As per the instructions given in question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide; we get.<br>99 &times; 41 - 208 &divide; 2 + 35 = ?<br>99 &times; 41 - 104 + 35<br>4059 - 104 + 35<br>4094 - 104 = 3990</p>",
                    solution_hi: "<p>6.(a) <strong>दिया गया है:</strong> 99 &divide; 41 +208 &times; 2 &ndash; 35 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide; को आपस में बदलने पर हमें प्राप्त होता है।<br>99 &times; 41 - 208 &divide; 2 + 35 = ?<br>99 &times; 41 - 104 + 35<br>4059 - 104 + 35<br>4094 - 104 = 3990</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?</p>",
                    question_hi: "<p>7. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?</p>",
                    options_en: ["<p>1974</p>", "<p>1749</p>", 
                                "<p>1947</p>", "<p>1497</p>"],
                    options_hi: ["<p>1974</p>", "<p>1749</p>",
                                "<p>1947</p>", "<p>1497</p>"],
                    solution_en: "<p>7.(b) <strong>Given:</strong> 1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?<br>As per the instructions given in the question, after interchanging the symbols &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>1064 &divide; 2 + 653 &times; 2 - 89 = ?<br>532 + 653 &times; 2 - 89 <br>532 + 1306 - 89<br>1838 - 89 = 1749</p>",
                    solution_hi: "<p>7.(b)<strong> दिया गया है:</strong> 1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीकों \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>1064 &divide; 2 + 653 &times; 2 - 89 = ?<br>532 + 653 &times; 2 - 89 <br>532 + 1306 - 89<br>1838 - 89 = 1749</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; are interchanged?<br>37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?</p>",
                    question_hi: "<p>8. निम्नलिखित समीकरण में, यदि \'&divide;\' और \'&ndash;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'+\' को आपस में बदल दिया जाए, तो \'?\' के स्थान पर क्या आएगा?<br>37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?</p>",
                    options_en: ["<p>41</p>", "<p>55</p>", 
                                "<p>37</p>", "<p>47</p>"],
                    options_hi: ["<p>41</p>", "<p>55</p>",
                                "<p>37</p>", "<p>47</p>"],
                    solution_en: "<p>8.(a) <strong>Given:</strong> 37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?<br>As per the instructions given in question , after interchanging the symbol &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; we get.<br>37 - 56 &divide; 7 + 2 &times; 6 = ?<br>37 - 8 + 2 &times; 6 <br>37 - 8 + 12<br>49 - 8 = 41</p>",
                    solution_hi: "<p>8.(a) <strong>दिया गया है:</strong> 37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'&divide;\' और \'-\' तथा \'&times;\' और \'+\' को आपस में बदलने पर हमें प्राप्त होता है।<br>37 - 56 &divide; 7 + 2 &times; 6 = ?<br>37 - 8 + 2 &times; 6 <br>37 - 8 + 12<br>49 - 8 = 41</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged? <br>272 &times; 16 + 97 &minus; 23 &divide; 4 = ?</p>",
                    question_hi: "<p>9. यदि &lsquo;+&rsquo; और &lsquo;&minus;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न-चिन्ह (?) के स्थान पर क्या आएगा?<br>272 &times; 16 + 97 &minus; 23 &divide; 4 = ?</p>",
                    options_en: ["<p>21</p>", "<p>22</p>", 
                                "<p>2</p>", "<p>12</p>"],
                    options_hi: ["<p>21</p>", "<p>22</p>",
                                "<p>2</p>", "<p>12</p>"],
                    solution_en: "<p>9.(d) <strong>Given:</strong> 272 &times; 16 + 97 &minus; 23 &divide; 4 = ?<br>As per the instructions given in question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide; we get.<br>272 &divide; 16 - 97 + 23 &times; 4 = ?<br>17 - 97 + 23 &times; 4 <br>17 - 97 + 92<br>109 - 97 = 12</p>",
                    solution_hi: "<p>9.(d) <strong>दिया गया है</strong>: 272 &times; 16+ 97 &minus; 23 &divide; 4 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \' +\' और \'-\' तथा \'&times;\' और \'&divide; को आपस में बदलने पर हमें प्राप्त होता है।<br>272 &divide; 16 - 97 + 23 &times; 4 = ?<br>17 - 97 + 23 &times; 4 <br>17 - 97 + 92<br>109 - 97 = 12</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the number that will come in the place of the question mark (?), if &lsquo;+&rsquo; and &lsquo; &ndash; &lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged <br>121 &times; 11 &ndash; 28 &divide; 3 + 17 = ?</p>",
                    question_hi: "<p>10. यदि निम्&zwj;नलिखित समीकरण में \'+\' और \'-\' को आपस में बदल दिया जाए और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो प्रश्न चिह्न (?) के स्थान पर कौन-सी संख्या आएगी?<br>121 &times; 11 &ndash; 28 &divide; 3 + 17 = ?</p>",
                    options_en: ["<p>85</p>", "<p>91</p>", 
                                "<p>78</p>", "<p>64</p>"],
                    options_hi: ["<p>85</p>", "<p>91</p>",
                                "<p>78</p>", "<p>64</p>"],
                    solution_en: "<p>10.(c) <strong>Given:</strong> 121 &times; 11 &ndash; 28 &divide; 3 + 17 = ? <br>As per the instructions given in question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo; &ndash; &lsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>121 &divide; 11 + 28 &times; 3 - 17 = ? <br>11 + 28 &times; 3 - 17<br>11 + 84 - 17 <br>95 - 17 = 78</p>",
                    solution_hi: "<p>10.(c) <strong>दिया गया है: </strong>121 &times; 11 &ndash; 28 &divide; 3 + 17 = ? <br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>121 &divide; 11 + 28 &times; 3 - 17 = ? <br>11 + 28 &times; 3 - 17<br>11 + 84 - 17 <br>95 - 17 = 78</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the number that will come in the place of the question mark (?), if &lsquo;+&rsquo; and &lsquo; &ndash; &lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged&nbsp;<br>93 &ndash; 7 &divide; 65 &times; 13 + 34 = ?</p>",
                    question_hi: "<p>11. यदि निम्&zwj;नलिखित समीकरण में \'+\' और \'-\' को आपस में बदल दिया जाए और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो प्रश्न चिह्न (?) के स्थान पर कौन-सी संख्या आएगी ?<br>93 &ndash; 7 &divide; 65 &times; 13 + 34 = ?</p>",
                    options_en: ["<p>87</p>", "<p>94</p>", 
                                "<p>99</p>", "<p>104</p>"],
                    options_hi: ["<p>87</p>", "<p>94</p>",
                                "<p>99</p>", "<p>104</p>"],
                    solution_en: "<p>11.(b) <strong>Given</strong> : 93 &ndash; 7 &divide; 65 &times; 13 + 34 = ?<br>As per instructions given in the question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo; &ndash; &lsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>93 + 7 &times; 65 &divide; 13 - 34 = ?<br>93 + 7 &times; 5 - 34<br>93 + 35 - 34<br>128 - 34 = 94</p>",
                    solution_hi: "<p>11.(b)<strong> दिया गया है :</strong> 93 &ndash; 7 &divide; 65 &times; 13 +34 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>93 + 7 &times; 65 &divide; 13 - 34 = ?<br>93 + 7 &times; 5 - 34<br>93 + 35 - 34<br>128 - 34 = 94</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Which two numbers (not digits) should be interchanged to make the given equation correct? <br>19 + 125 &divide; 25 &ndash; 100 &divide; 5 + 4 &times; 3 = 52</p>",
                    question_hi: "<p>12. दिए गए समीकरण को सही करने के लिए किन दो संख्याओं (अंक नहीं) को आपस में बदलना चाहिए?<br>19 + 125 &divide; 25 &ndash; 100 &divide; 5 + 4 &times; 3 = 52</p>",
                    options_en: ["<p>19 and 4</p>", "<p>125 and 100</p>", 
                                "<p>25 and 5</p>", "<p>4 and 5</p>"],
                    options_hi: ["<p>19 and 4</p>", "<p>125 and 100</p>",
                                "<p>25 and 5</p>", "<p>4 and 5</p>"],
                    solution_en: "<p>12.(c)<br><strong>Given:</strong> 19 + 125 &divide; 25 &ndash; 100 &divide; 5 + 4 &times; 3 = 52<br>After checking all options one by one , only option (c) satisfied.<br>19 + 125 &divide; 5 &ndash; 100 &divide; 25 + 4 &times; 3 = 52<br>19 + 25 - 4 + 4 &times; 3 <br>19 + 25 - 4 +12<br>56 - 4 = 52<br>52 = 52 (L.H.S = R.H.S)</p>",
                    solution_hi: "<p>12.(c)<br><strong>दिया गया:</strong> 19 + 125 &divide; 25 &ndash; 100 &divide; 5 + 4 &times; 3 = 52<br>एक-एक करके सभी विकल्पों की जांच करने के बाद, केवल विकल्प (c) संतुष्ट करता है।<br>19 + 125 &divide; 5 &ndash; 100 &divide; 25 + 4 &times; 3 = 52<br>19 + 25 - 4 + 4 &times; 3 <br>19 + 25 - 4 +12<br>56 - 4 = 52<br>52 = 52 (L.H.S = R.H.S)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Which number will come in the place of the question mark (?) in the following equation if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>20 + 10 &times; 2 &minus; 4 &divide; 5 = ?</p>",
                    question_hi: "<p>13. यदि निम्नलिखित समीकरण में &lsquo;+&rsquo; और &lsquo;&minus;&rsquo; को आपस में बदल दिया जाता है तथा &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाता है, तो प्रश्न चिह्न (?) के स्थान कौन-सी संख्या आएगी?<br>20 + 10 &times; 2 &minus; 4 &divide; 5 = ?</p>",
                    options_en: ["<p>45</p>", "<p>30</p>", 
                                "<p>35</p>", "<p>40</p>"],
                    options_hi: ["<p>45</p>", "<p>30</p>",
                                "<p>35</p>", "<p>40</p>"],
                    solution_en: "<p>13.(c) <strong>Given:</strong> 20 + 10 &times; 2 &minus; 4 &divide; 5 = ?<br>As per instructions given in question after interchanging the symbol &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; we get.<br>20 - 10 &divide; 2 + 4 &times; 5 = ?<br>20 - 5 + 4 &times; 5&nbsp;<br>20 - 5 + 20<br>40 - 5 = 35</p>",
                    solution_hi: "<p>13.(c) <strong>दिया गया है: </strong>20+ 10 &times; 2 &minus; 4 &divide; 5 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार प्रतीक \'&times;\' और \'&divide;\' तथा \'+ \' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है।<br>20 - 10 &divide; 2 + 4 &times; 5 = ?<br>20 - 5 + 4 &times; 5&nbsp;<br>20 - 5 + 20<br>40 - 5 = 35</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>1205 &times; 5 + 35 &ndash; 197 &divide; 18 = ?</p>",
                    question_hi: "<p>14. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>1205 &times; 5 + 35 &ndash; 197 &divide; 18 = ?</p>",
                    options_en: ["<p>3482</p>", "<p>3752</p>", 
                                "<p>3824</p>", "<p>3284</p>"],
                    options_hi: ["<p>3482</p>", "<p>3752</p>",
                                "<p>3824</p>", "<p>3284</p>"],
                    solution_en: "<p>14.(b) <strong>Given:</strong> 1205 &times; 5 + 35 &ndash; 197 &divide; 18 = ?<br>As per the instructions given in question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>1205 &divide; 5 - 35 + 197 &times; 18 = ?<br>241 - 35 + 197 &times; 18<br>241 - 35 + 3546<br>3787 - 35 = 3752</p>",
                    solution_hi: "<p>14.(b)<strong> दिया गया है: </strong>1205 &times; 5 + 35 &ndash; 197 &divide; 18 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>1205 &divide; 5 - 35 + 197 &times; 18 = ?<br>241 - 35 + 197 &times; 18<br>241 - 35 + 3546<br>3787 - 35 = 3752</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. What will come in place of the question mark (?) in the following equation if &lsquo;+&rsquo; and &lsquo;&times;&rsquo; are interchanged and &lsquo;&ndash;&rsquo; and &lsquo;&divide;&rsquo; are interchanged? <br>16 &minus; 2 &times; 42 + 13 &divide; 12 = ?</p>",
                    question_hi: "<p>15. यदि \'+\' और \'&times;\' को आपस में बदल दिया जाए और \'&ndash;\' और \'&divide;\' को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>16 &minus; 2 &times; 42 + 13 &divide; 12 = ?</p>",
                    options_en: ["<p>560</p>", "<p>542</p>", 
                                "<p>950</p>", "<p>762</p>"],
                    options_hi: ["<p>560</p>", "<p>542</p>",
                                "<p>950</p>", "<p>762</p>"],
                    solution_en: "<p>15.(b) <strong>Given:</strong> 16 &minus; 2 &times; 42 + 13 &divide; 12 = ?<br>As per the instructions given in question, after interchanging the symbol &lsquo;&ndash;&rsquo; and &lsquo;&divide; and &lsquo;+&rsquo; and &lsquo;&times;&rsquo; we get.<br>16 &divide; 2 + 42 &times; 13 - 12 = ?<br>8 + 42 &times; 13 - 12<br>8 + 546 - 12<br>554 - 12 = 542</p>",
                    solution_hi: "<p>15.(b) <strong>दिया गया है: </strong>16 &minus; 2 &times; 42 + 13 &divide; 12 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'-\' और \'&divide; तथा \' +\' और \'&times;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>16 &divide; 2 + 42 &times; 13 - 12 = ?<br>8 + 42 &times; 13 - 12<br>8 + 546 - 12<br>554 - 12 = 542</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>