<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128461482.png\" alt=\"rId5\" width=\"197\" height=\"105\"></p>",
                    question_hi: "<p>1. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128461482.png\" alt=\"rId5\" width=\"197\" height=\"105\"></p>",
                    options_en: ["<p>10</p>", "<p>8</p>", 
                                "<p>11</p>", "<p>9</p>"],
                    options_hi: ["<p>10</p>", "<p>8</p>",
                                "<p>11</p>", "<p>9</p>"],
                    solution_en: "<p>1.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128461684.png\" alt=\"rId6\" width=\"202\" height=\"129\"><br>There are 8 triangles = ABE, ABD, BED, ACD, AEC, EDC, BEC, ABC.</p>",
                    solution_hi: "<p>1.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128461684.png\" alt=\"rId6\" width=\"202\" height=\"129\"><br>8 त्रिभुज हैं = ABE, ABD, BED, ACD, AEC, EDC, BEC, ABC.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the option that can replace the question mark (?) in the following series.<br>OA33, RD36, UG39, XJ42, ?</p>",
                    question_hi: "<p>2. उस विकल्प का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आ सकता है।<br>OA33, RD36, UG39, XJ42, ?</p>",
                    options_en: ["<p>CN41</p>", "<p>AM45</p>", 
                                "<p>BO43</p>", "<p>BN44</p>"],
                    options_hi: ["<p>CN41</p>", "<p>AM45</p>",
                                "<p>BO43</p>", "<p>BN44</p>"],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128461949.png\" alt=\"rId7\" width=\"344\" height=\"102\"></p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128461949.png\" alt=\"rId7\" width=\"347\" height=\"103\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462103.png\" alt=\"rId8\" width=\"110\" height=\"131\"></p>",
                    question_hi: "<p>3. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) सन्निहित है (आकृति को घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462103.png\" alt=\"rId8\" width=\"110\" height=\"131\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462304.png\" alt=\"rId9\" width=\"104\" height=\"106\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462495.png\" alt=\"rId10\" width=\"104\" height=\"105\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462693.png\" alt=\"rId11\" width=\"104\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462848.png\" alt=\"rId12\" width=\"103\" height=\"105\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462304.png\" alt=\"rId9\" width=\"104\" height=\"106\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462495.png\" alt=\"rId10\" width=\"104\" height=\"105\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462693.png\" alt=\"rId11\" width=\"104\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462848.png\" alt=\"rId12\" width=\"103\" height=\"105\"></p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462495.png\" alt=\"rId10\" width=\"116\" height=\"117\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128462495.png\" alt=\"rId10\" width=\"116\" height=\"117\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, FIGURE is coded as &lsquo;VSUGJW&rsquo; and COPIED is coded as &lsquo;YMLSWX&rsquo;. What will be the code for BLANKET?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, FIGURE को \'VSUGJW\' के रूप में कूटबद्ध किया जाता है और COPIED को \'YMLSWX\' के रूप में कूटबद्ध किया जाता है। तो उसी कूट भाषा में BLANKET के लिए कूट क्या होगा?</p>",
                    options_en: ["<p>ZPANQWH</p>", "<p>ZPMNQHW</p>", 
                                "<p>ZPANQHW</p>", "<p>ZPNAQWH</p>"],
                    options_hi: ["<p>ZPANQWH</p>", "<p>ZPMNQHW</p>",
                                "<p>ZPANQHW</p>", "<p>ZPNAQWH</p>"],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128463231.png\" alt=\"rId14\" width=\"199\" height=\"111\"></p>\n<p>and<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128463593.png\" alt=\"rId15\" width=\"191\" height=\"103\"></p>\n<p>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128463892.png\" alt=\"rId16\" width=\"189\" height=\"87\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128463231.png\" alt=\"rId14\" width=\"199\" height=\"111\"></p>\n<p>और<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128463593.png\" alt=\"rId15\" width=\"191\" height=\"103\"></p>\n<p>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128463892.png\" alt=\"rId16\" width=\"189\" height=\"87\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. ‘Simple’ is related to ‘Basic’ in the same way as ‘Difficult’ is related to ‘________’.",
                    question_hi: "5. \'सरल\' का संबंध \'बुनियादी\' से उसी प्रकार है, जैसे \'मुश्किल\' का संबंध \'________\' से है।",
                    options_en: [" Trivial ", " Wrong  ", 
                                " Imaginary  ", " Complex"],
                    options_hi: [" मामूली", " गलत",
                                " काल्पनिक", " जटिल"],
                    solution_en: "5.(d)<br />As ‘Simple’ is the synonym of ‘Basic’ similarly ‘Difficult’ is the synonym of ‘Complex’",
                    solution_hi: "5.(d)<br />जैसे \'सरल\', \'बुनियादी\' का पर्याय है उसी प्रकार \'कठिन\', ‘जटिल’ का पर्याय है । ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the son of B&rsquo;;<br>&lsquo;A # B&rsquo; means &lsquo;A is the sister of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo;; and,<br>&lsquo;A @ B&rsquo; means &lsquo;A is the father of B&rsquo;.<br>Based on the above, how is M related to T if &lsquo;M # N @ L &times; P + T&rsquo;?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में,<br>&lsquo;A + B&rsquo; का अर्थ है \'&lsquo;A, B&rsquo; का पुत्र है\';<br>\'A # B\' का अर्थ है \'A, B की बहन है\';<br>&lsquo;A &times; B&rsquo; का अर्थ है \'&lsquo;A, B की पत्नी है\'; और,<br>\'A @ B\' का अर्थ है \'A, B का पिता है\'।<br>उपरोक्त के आधार पर, यदि \'M # N @ L &times; P + T\' है, तो M का T से क्या संबंध है?</p>",
                    options_en: ["<p>Son&rsquo;s wife&rsquo;s sister</p>", "<p>Son&rsquo;s wife&rsquo;s father&rsquo;s sister</p>", 
                                "<p>Son&rsquo;s wife&rsquo;s father</p>", "<p>Son&rsquo;s wife&rsquo;s father&rsquo;s mother</p>"],
                    options_hi: ["<p>पुत्र की पत्नी की बहन</p>", "<p>पुत्र की पत्नी के पिता की बहन</p>",
                                "<p>पुत्र की पत्नी के पिता</p>", "<p>पुत्र की पत्नी के पिता की माता</p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465025.png\" alt=\"rId20\" width=\"215\" height=\"104\"><br>Hence, M is the son\'s wife&rsquo;s father&rsquo;s sister of T.</p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465025.png\" alt=\"rId20\" width=\"215\" height=\"104\"><br>अतः, M, T के बेटे की पत्नी के पिता की बहन है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>229 &divide; 41 + 502 &times; 2 &ndash; 44 = ?</p>",
                    question_hi: "<p>7. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>229 &divide; 41 + 502 &times; 2 &ndash; 44 = ?</p>",
                    options_en: ["<p>9281</p>", "<p>9812</p>", 
                                "<p>9218</p>", "<p>9182</p>"],
                    options_hi: ["<p>9281</p>", "<p>9812</p>",
                                "<p>9218</p>", "<p>9182</p>"],
                    solution_en: "<p>7.(d)<br><strong>Given :-</strong> 229 <math display=\"inline\"><mo>&#247;</mo></math> 41 + 502 &times; 2 - 44 = ?<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get,<br>229 <math display=\"inline\"><mo>&#215;</mo></math> 41 - 502 &divide; 2 + 44&nbsp;<br>9389 - 251 + 44&nbsp;<br>9182&nbsp;</p>",
                    solution_hi: "<p>7.(d)<br><strong>दिया गया है:- </strong>229 <math display=\"inline\"><mo>&#247;</mo></math> 41 + 502 &times; 2 - 44 = ?<br>दिए गए निर्देश के अनुसार &lsquo;+&rsquo; और &lsquo;-&rsquo; तथा &lsquo;&times;&rsquo; और &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; को आपस में बदलने के बाद हमें प्राप्त होता है,<br>229 <math display=\"inline\"><mo>&#215;</mo></math> 41 - 502 &divide; 2 + 44&nbsp;<br>9389 - 251 + 44&nbsp;<br>9182&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the figure from among the given options that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465222.png\" alt=\"rId21\" width=\"416\" height=\"76\"></p>",
                    question_hi: "<p>8. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्नचिह्न (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465222.png\" alt=\"rId21\" width=\"416\" height=\"76\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465366.png\" alt=\"rId22\" width=\"105\" height=\"104\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465525.png\" alt=\"rId23\" width=\"105\" height=\"104\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465651.png\" alt=\"rId24\" width=\"105\" height=\"104\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465779.png\" alt=\"rId25\" width=\"105\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465366.png\" alt=\"rId22\" width=\"105\" height=\"104\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465525.png\" alt=\"rId23\" width=\"105\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465651.png\" alt=\"rId24\" width=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465779.png\" alt=\"rId25\" width=\"105\"></p>"],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465651.png\" alt=\"rId24\" width=\"110\"></p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128465651.png\" alt=\"rId24\" width=\"110\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the triad in which the numbers are related in the same way as are the numbers of the given triads.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(3, 5, 32)<br>(9, 1, 26)</p>",
                    question_hi: "<p>9. उस त्रिक का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित त्रिकों की संख्याएँ संबंधित हैं।<br>(नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना, पूर्ण संख्याओं पर संक्रिया किया जाना चाहिए। उदाहरण के लिए 13 :- 13 पर संक्रिया जैसे 13 में जोड़/घटाव/गुणा आदि किया जा सकता है। 13 को 1 और 3 में विभक्त कर और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)।<br>(3, 5, 32)<br>(9, 1, 26)</p>",
                    options_en: ["<p>(5, 6, 33)</p>", "<p>(2, 6, 30)</p>", 
                                "<p>(6, 1, 23)</p>", "<p>(4, 2, 28)</p>"],
                    options_hi: ["<p>(5, 6, 33)</p>", "<p>(2, 6, 30)</p>",
                                "<p>(6, 1, 23)</p>", "<p>(4, 2, 28)</p>"],
                    solution_en: "<p>9.(c)<br><strong>Logic</strong>:- (<math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo><mo>&#215;</mo></math> 2<sup>nd </sup><em>no.</em>) + 17 = 3<sup>rd</sup> <em>no</em>.<br>(3, 5, 32) :- 3 <math display=\"inline\"><mo>&#215;</mo></math> 5 + 17 &rArr; 15 + 17 = 32<br>(9, 1, 26) :- 9 <math display=\"inline\"><mo>&#215;</mo></math> 1 + 17 &rArr; 9 + 17 = 26<br>Similarly, <br>(6, 1, 23) :- 6 <math display=\"inline\"><mo>&#215;</mo></math> 1 + 17 &rArr; 6 + 17 = 23</p>",
                    solution_hi: "<p>9.(c)<br><strong>तर्क</strong>:- (पहली संख्या <math display=\"inline\"><mo>&#215;</mo></math> दूसरी संख्या) + 17 = तीसरी संख्या <br>(3, 5, 32) :- 3 <math display=\"inline\"><mo>&#215;</mo></math> 5 + 17 &rArr; 15 + 17 = 32<br>(9, 1, 26) :- 9 <math display=\"inline\"><mo>&#215;</mo></math> 1 + 17 &rArr; 9 + 17 = 26<br>इसी प्रकार,<br>(6, 1, 23) :- 6 <math display=\"inline\"><mo>&#215;</mo></math> 1 + 17 &rArr; 6 + 17 = 23</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10.  What will come in the place of ‘?’ in the following equation, if ‘+’ and ‘–‘ are interchanged and ‘×’ and ‘÷’ are interchanged?<br />4 – 5 ÷ 9 + 6 × 2 = ?",
                    question_hi: "10.  यदि निम्नलिखित समीकरण में, \'+\' और \'–\' को आपस में बदल दिया जाए, और \'×\' और \'÷\' को आपस में बदल दिया जाए, तो प्रश्न-चिह्न (?) के स्थान पर क्या आएगा?<br />4 – 5 ÷ 9 + 6 × 2 = ?",
                    options_en: [" 42 ", " 46 ", 
                                " 36", " 38"],
                    options_hi: [" 42 ", " 46 ",
                                " 36", " 38"],
                    solution_en: "10.(b)<br />Given :<math display=\"inline\"><mo>-</mo></math> 4 - 5 ÷ 9 + 6 × 2 = ?<br />As per given instruction after interchanging ‘+’ and ‘-’ and ‘×’ and ‘<math display=\"inline\"><mo>÷</mo></math>’ we get,<br /> 4 <math display=\"inline\"><mo>+</mo></math> 5 × 9 - 6 ÷ 2 <br />= 4 <math display=\"inline\"><mo>+</mo></math> 45 - 3 <br />= 46 ",
                    solution_hi: "10.(b)<br />दिया गया है:<math display=\"inline\"><mo>-</mo></math> 4 - 5 ÷ 9 + 6 × 2 = ?<br />दिए गए निर्देश के अनुसार ‘+’ और  ‘-’ तथा ‘×’ और  ‘<math display=\"inline\"><mo>÷</mo></math>’ को आपस में बदलने के बाद हमें प्राप्त होता है,<br /> 4 <math display=\"inline\"><mo>+</mo></math> 5 × 9 - 6 ÷ 2 <br />= 4 <math display=\"inline\"><mo>+</mo></math> 45 - 3 <br />= 46 ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Which of the following terms will replace the question mark (?) in the given series?<br>OPOR, NOPS, ?, LMRU, KLSV</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन-सा पद दी गई शृंखला में प्रश्न-चिह्न (?) का स्थान लेगा?<br>OPOR, NOPS, ?, LMRU, KLSV</p>",
                    options_en: ["<p>MNQT</p>", "<p>MNTQ</p>", 
                                "<p>MNQP</p>", "<p>NMQT</p>"],
                    options_hi: ["<p>MNQT</p>", "<p>MNTQ</p>",
                                "<p>MNQP</p>", "<p>NMQT</p>"],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466036.png\" alt=\"rId26\" width=\"315\" height=\"105\"></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466036.png\" alt=\"rId26\" width=\"315\" height=\"105\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>Y _ Y _ _ Y _ T _ Y _ Y</p>",
                    question_hi: "<p>12. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>Y _ Y _ _ Y _ T _ Y _ Y</p>",
                    options_en: ["<p>YTYTYT</p>", "<p>TTYYTT</p>", 
                                "<p>TYTYYT</p>", "<p>TYTYTY</p>"],
                    options_hi: ["<p>YTYTYT</p>", "<p>TTYYTT</p>",
                                "<p>TYTYYT</p>", "<p>TYTYTY</p>"],
                    solution_en: "<p>12.(c) <br>Series <math display=\"inline\"><mo>&#8658;</mo></math> Y<span style=\"text-decoration: underline;\"><strong>T</strong></span>Y/<span style=\"text-decoration: underline;\"><strong>YT</strong></span>Y/<span style=\"text-decoration: underline;\"><strong>Y</strong></span>T<span style=\"text-decoration: underline;\"><strong>Y</strong></span>/Y<span style=\"text-decoration: underline;\"><strong>T</strong></span>Y</p>",
                    solution_hi: "<p>12.(c) <br>संख्या <math display=\"inline\"><mo>&#8658;</mo></math> Y<span style=\"text-decoration: underline;\"><strong>T</strong></span>Y/<span style=\"text-decoration: underline;\"><strong>YT</strong></span>Y/<span style=\"text-decoration: underline;\"><strong>Y</strong></span>T<span style=\"text-decoration: underline;\"><strong>Y</strong></span>/Y<span style=\"text-decoration: underline;\"><strong>T</strong></span>Y</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the correct mirror image of the given figure when the mirror is placed at MN<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466246.png\" alt=\"rId27\" width=\"101\" height=\"130\"></p>",
                    question_hi: "<p>13. जब दर्पण को MN पर रखा जाता हो तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466246.png\" alt=\"rId27\" width=\"101\" height=\"130\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466435.png\" alt=\"rId28\" width=\"154\" height=\"32\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466584.png\" alt=\"rId29\" height=\"32\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466715.png\" alt=\"rId30\" height=\"32\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466841.png\" alt=\"rId31\" height=\"32\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466435.png\" alt=\"rId28\" height=\"32\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466584.png\" alt=\"rId29\" height=\"32\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466715.png\" alt=\"rId30\" height=\"32\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466841.png\" alt=\"rId31\" height=\"32\"></p>"],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466584.png\" alt=\"rId29\" height=\"32\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128466584.png\" alt=\"rId29\" height=\"40\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is nor allowed.)</p>",
                    question_hi: "<p>14. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है ?<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>82 &ndash; 46 &ndash; 10</p>", "<p>90 &ndash; 99 &ndash; 108</p>", 
                                "<p>67 &ndash; 45 &ndash; 23</p>", "<p>90 &ndash; 51 &ndash; 13</p>"],
                    options_hi: ["<p>82 &ndash; 46 &ndash; 10</p>", "<p>90 &ndash; 99 &ndash; 108</p>",
                                "<p>67 &ndash; 45 &ndash; 23</p>", "<p>90 &ndash; 51 &ndash; 13</p>"],
                    solution_en: "<p>14.(d)<br><strong>Logic:-</strong> equal difference between numbers.<br>(82 - 46 - 10) :- <math display=\"inline\"><mn>82</mn><mo>-</mo><mn>46</mn></math> = 36 and 46 - 10 = 36<br>(90 - 99 - 108) :- (<math display=\"inline\"><mn>99</mn><mo>-</mo><mn>90</mn></math>) = 9 and 108 - 99 = 9<br>(67 - 45 - 23) :- (<math display=\"inline\"><mn>67</mn><mo>-</mo><mn>45</mn></math>) = 22 and 45 - 23 = 22 <br>But<br>(90 - 51 - 13) :- (<math display=\"inline\"><mn>90</mn><mo>-</mo><mn>51</mn></math>) = 39 and 51 - 13 = 38</p>",
                    solution_hi: "<p>14.(d)<br><strong>तर्क:</strong>- संख्याओं के बीच समान अंतर।<br>(82 - 46 - 10) :- <math display=\"inline\"><mn>82</mn><mo>-</mo><mn>46</mn></math> = 36 and 46 - 10 = 36<br>(90 - 99 - 108) :- (<math display=\"inline\"><mn>99</mn><mo>-</mo><mn>90</mn></math>) = 9 and 108 - 99 = 9<br>(67 - 45 - 23) :- (<math display=\"inline\"><mn>67</mn><mo>-</mo><mn>45</mn></math>) = 22 and 45 - 23 = 22 <br>लेकिन <br>(90 - 51 - 13) :- (<math display=\"inline\"><mn>90</mn><mo>-</mo><mn>51</mn></math>) = 39 and 51 - 13 = 38</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467058.png\" alt=\"rId32\" width=\"316\" height=\"90\"></p>",
                    question_hi: "<p>15. एक कागज को नीचे दर्शाए अनुसार मोड़ा और काटा जाता है। खोले जाने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467058.png\" alt=\"rId32\" width=\"316\" height=\"90\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467193.png\" alt=\"rId33\" width=\"105\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467375.png\" alt=\"rId34\" height=\"105\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467498.png\" alt=\"rId35\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467663.png\" alt=\"rId36\" height=\"105\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467193.png\" alt=\"rId33\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467375.png\" alt=\"rId34\" height=\"105\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467498.png\" alt=\"rId35\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467663.png\" alt=\"rId36\" height=\"105\"></p>"],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467498.png\" alt=\"rId35\" height=\"108\"></p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467498.png\" alt=\"rId35\" height=\"108\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Based on the position in the English alphabetical order, three of the following letter-clusters are alike in some manner and one is different. Select the odd letter-cluster.<br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>16. अंग्रेजी वर्णमाला क्रम में स्थिति के आधार पर, निम्नलिखित में से तीन अक्षर-समूह किसी न किसी रूप में संगत हैं और एक असंगत है। असंगत अक्षर- समूह का चयन करें।<br>(नोटः असंगत, अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: ["<p>SUZY</p>", "<p>MPUT</p>", 
                                "<p>QSXW</p>", "<p>PRWV</p>"],
                    options_hi: ["<p>SUZY</p>", "<p>MPUT</p>",
                                "<p>QSXW</p>", "<p>PRWV</p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467881.png\" alt=\"rId37\" width=\"343\" height=\"80\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128468037.png\" alt=\"rId38\" height=\"80\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128468251.png\" alt=\"rId39\" height=\"80\"></p>\n<p>but<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128468430.png\" alt=\"rId40\" height=\"80\"></p>",
                    solution_hi: "<p>16.(b)</p>\n<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128467881.png\" alt=\"rId37\" width=\"343\" height=\"80\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128468037.png\" alt=\"rId38\" height=\"80\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128468251.png\" alt=\"rId39\" height=\"80\"></p>\n<p>लेकिन&nbsp;<strong id=\"docs-internal-guid-b1414020-7fff-8b92-c6a1-bf4b72923dcc\"> </strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128468430.png\" alt=\"rId40\" height=\"80\"></p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(108, 12, 9)<br>(114, 19, 6)</p>",
                    question_hi: "<p>17. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्&zwj;चय की संख्याएँ संबंधित हैं।<br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(108, 12, 9)<br>(114, 19, 6)</p>",
                    options_en: ["<p>(136, 17, 8)</p>", "<p>(144, 14, 8)</p>", 
                                "<p>(156, 18, 5)</p>", "<p>(148, 16, 9)</p>"],
                    options_hi: ["<p>(136, 17, 8)</p>", "<p>(144, 14, 8)</p>",
                                "<p>(156, 18, 5)</p>", "<p>(148, 16, 9)</p>"],
                    solution_en: "<p>17.(a)<br><strong>Logic:-</strong> <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">d</mi></mrow></msup><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">o</mi><mo>.</mo></math> &times; 3<sup>rd</sup> <em><strong>no</strong></em>. = 1<sup>st</sup><em><strong>no.</strong></em><br>(108, <math display=\"inline\"><mn>12</mn></math>, 9) :- 12 &times; 9 = 108<br>(114, 19, 6) :- 19 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 114<br>Similarly, <br>(136, 17, 8) :- 17 <math display=\"inline\"><mo>&#215;</mo></math> 8 = 136</p>",
                    solution_hi: "<p>17.(a)<br><strong>तर्क :- </strong>दूसरी संख्या <math display=\"inline\"><mo>&#215;</mo></math> तीसरी संख्या = पहली संख्या <br>(108, <math display=\"inline\"><mn>12</mn></math>, 9) :- 12 &times; 9 = 108<br>(114, 19, 6) :- 19 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 114<br>इसी प्रकार, <br>(136, 17, 8) :- 17 <math display=\"inline\"><mo>&#215;</mo></math> 8 = 136</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>17 &ndash; 19 &ndash; 16 &ndash; 20; 22 &ndash; 24 &ndash; 21 &ndash; 25</p>",
                    question_hi: "<p>18. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है ?<br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।)<br>17 &ndash; 19 &ndash; 16 &ndash; 20; 22 &ndash; 24 &ndash; 21 &ndash; 25</p>",
                    options_en: ["<p>15 &ndash; 17 &ndash; 14 &ndash; 18</p>", "<p>16 &ndash; 18 &ndash; 12 &ndash; 16</p>", 
                                "<p>11 &ndash; 13 &ndash; 10 &ndash; 15</p>", "<p>18 &ndash; 20 &ndash; 16 &ndash; 14</p>"],
                    options_hi: ["<p>15 &ndash; 17 &ndash; 14 &ndash; 18</p>", "<p>16 &ndash; 18 &ndash; 12 &ndash; 16</p>",
                                "<p>11 &ndash; 13 &ndash; 10 &ndash; 15</p>", "<p>18 &ndash; 20 &ndash; 16 &ndash; 14</p>"],
                    solution_en: "<p>18.(a)<br><strong>Logic:-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></math> + <strong>2 </strong>= 2<sup>nd</sup>no. , 2<sup>nd</sup>no. - 3 = 3<sup>rd</sup>no., 3<sup>rd</sup>no + <strong>4 </strong>= 4<sup>th</sup>no. <br>(17 <math display=\"inline\"><mo>-</mo></math> 19 - 16 - 20) :- <strong>17</strong> + 2 = <strong>19</strong>, 19 - 3 = <strong>16</strong>, 16 + 4 = <strong>20</strong><br>(22 <math display=\"inline\"><mo>-</mo></math> 24 - 21 - 25) :- <strong>22</strong> + 2 = <strong>24</strong>, 24 - 3 = <strong>21</strong>, 21 + 4 = <strong>25</strong><br>Similarly<br>(15 <math display=\"inline\"><mo>-</mo></math> 17 - 14 - 18) :- <strong>15</strong> + 2 = <strong>17</strong>, 17 - 3 = <strong>14</strong>, 14 + 4 = <strong>18</strong></p>",
                    solution_hi: "<p>18.(a)<br><strong>तर्क:-</strong> पहली संख्या + 2 = दूसरी संख्या , दूसरी संख्या - 3 = तीसरी संख्या, तीसरी संख्या + 4 = चौथी संख्या (17 <math display=\"inline\"><mo>-</mo></math> 19 - 16 - 20) :- <strong>17</strong> + 2 = <strong>19</strong>, 19 - 3 = <strong>16</strong>, 16 + 4 = <strong>20</strong><br>(22 <math display=\"inline\"><mo>-</mo></math> 24 - 21 - 25) :- <strong>22</strong> + 2 = <strong>24</strong>, 24 - 3 = <strong>21</strong>, 21 + 4 = <strong>25</strong><br>इसी प्रकार, <br>(15 <math display=\"inline\"><mo>-</mo></math> 17 - 14 - 18) :- <strong>15</strong> + 2 = <strong>17</strong>, 17 - 3 = <strong>14</strong>, 14 + 4 = <strong>18</strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusion(s) logically follow(s) from the statements.<br><strong>Statements:</strong><br>All pastries are chocolates.<br>Some chocolates are cookies.<br>All cookies are cakes.<br><strong>Conclusions:</strong><br>(I) No pastry is a cake.<br>(II) Some cakes are chocolates.</p>",
                    question_hi: "<p>19. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन कीजिए। कथनों में दी गई जानकारी को सत्य मानते हुए, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय कीजिए कि दिए गए निष्कर्ष(षों) षों में से कौन-सा/से निष्कर्ष, कथनों का तर्कसंगत रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>सभी पेस्ट्री , चॉकलेट हैं।<br>कुछ चॉकलेट, कुकीज़ हैं।<br>सभी कुकीज़, केक हैं।<br><strong>निष्कर्ष:</strong><br>(I) कोई भी पेस्ट्री , केक नहीं है।<br>(II) कुछ केक, चॉकलेट हैं।</p>",
                    options_en: ["<p>Only conclusion I follows.</p>", "<p>Both conclusions I and II follow.</p>", 
                                "<p>Neither conclusion I nor II follows.</p>", "<p>Only conclusion II follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>", "<p>निष्कर्ष (I) और (II) दोनों अनुसरण करते हैं।</p>",
                                "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>"],
                    solution_en: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128468738.png\" alt=\"rId41\" width=\"253\" height=\"130\"><br>Hence, conclusion II follows.</p>",
                    solution_hi: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469028.png\" alt=\"rId42\" height=\"130\"><br>अतः, निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469287.png\" alt=\"rId43\" width=\"466\" height=\"95\"></p>",
                    question_hi: "<p>20. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469287.png\" alt=\"rId43\" height=\"95\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469443.png\" alt=\"rId44\" width=\"105\" height=\"104\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469579.png\" alt=\"rId45\" width=\"105\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469713.png\" alt=\"rId46\" width=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469885.png\" alt=\"rId47\" width=\"105\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469443.png\" alt=\"rId44\" width=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469579.png\" alt=\"rId45\" width=\"105\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469713.png\" alt=\"rId46\" width=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469885.png\" alt=\"rId47\" width=\"105\"></p>"],
                    solution_en: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469443.png\" alt=\"rId44\" width=\"108\"></p>",
                    solution_hi: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128469443.png\" alt=\"rId44\" width=\"108\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21.  In a certain code language, ‘FRAMED’ is coded as ‘497621’ and ‘DREAMT’ is coded as ‘769542’. What is the code for ‘T’ in that language?",
                    question_hi: "21.  एक निश्चित कूट भाषा में, ‘FRAMED’ को ‘497621’ के रूप में कूटबद्ध किया जाता है और ‘DREAMT’ को ‘769542’ के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'T\' के लिए क्या कूट होगा?",
                    options_en: [" 5", " 1 ", 
                                " 4", " 7"],
                    options_hi: [" 5", " 1 ",
                                " 4", " 7"],
                    solution_en: "21.(a)<br />F R A M E D <math display=\"inline\"><mo>→</mo></math> 4 9 7 6 2 1<br />D R E A M T <math display=\"inline\"><mo>→</mo></math> 7 6 9 5 4 2<br />From above code for ‘T’ is 5",
                    solution_hi: "21.(a)<br />F R A M E D <math display=\"inline\"><mo>→</mo></math> 4 9 7 6 2 1<br />D R E A M T <math display=\"inline\"><mo>→</mo></math> 7 6 9 5 4 2<br />उपरोक्त कोडिंग से \'T\' के लिए कोड 5 है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In this question, three statements are given, followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follows/follow from the statements.<br><strong>Statements:</strong><br>All red are green.<br>All green are pink.<br>No pink is blue.<br><strong>Conclusions:</strong><br>I. No green is blue.<br>II. Some blue are red.</p>",
                    question_hi: "<p>22. इस प्रश्न में तीन कथन और उसके बाद दो निष्कर्ष I और II दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सर्वज्ञात तथ्यों से भिन्न प्रतीत होते हों, हों यह निर्णय करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन:</strong><br>सभी लाल, हरे हैं।<br>सभी हरे, गुलाबी हैं।<br>कोई गुलाबी, नीला नहीं है।<br><strong>निष्कर्ष:</strong><br>I. कोई हरा, नीला नहीं है।<br>II. कुछ नीले, लाल हैं।</p>",
                    options_en: ["<p>Only conclusion II follows.</p>", "<p>Neither conclusion I nor II follows.</p>", 
                                "<p>Only conclusion I follows.</p>", "<p>Both conclusions I and II follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II अनुसरण करता है।</p>", "<p>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष I अनुसरण करता है।</p>", "<p>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>"],
                    solution_en: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128470149.png\" alt=\"rId48\" width=\"274\" height=\"125\"><br>Hence, only conclusion I follows.</p>",
                    solution_hi: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128470466.png\" alt=\"rId49\" height=\"125\"><br>अतः, केवल निष्कर्ष I अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;SPIDER&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>23. यदि शब्द \'SPIDER\' के प्रत्येक अक्षर को अंग्रेजी वर्णमाला के क्रम में व्यवस्थित किया जाए तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा?</p>",
                    options_en: ["<p>Three</p>", "<p>None</p>", 
                                "<p>Two</p>", "<p>One</p>"],
                    options_hi: ["<p>तीन</p>", "<p>कोई नहीं</p>",
                                "<p>दो</p>", "<p>एक</p>"],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128470686.png\" alt=\"rId50\" width=\"154\" height=\"115\"><br>Hence, the position of only one letter remains unchanged.</p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128470686.png\" alt=\"rId50\" width=\"154\" height=\"115\"><br>अतः, केवल एक अक्षर की स्थिति अपरिवर्तित रहती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128470936.png\" alt=\"rId51\" width=\"353\" height=\"110\"></p>",
                    question_hi: "<p>24. एक कागज को नीचे दिखाए अनुसार मोड़ा और काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128470936.png\" alt=\"rId51\" width=\"353\" height=\"110\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471102.png\" alt=\"rId52\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471256.png\" alt=\"rId53\" height=\"105\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471373.png\" alt=\"rId54\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471562.png\" alt=\"rId55\" height=\"105\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471102.png\" alt=\"rId52\" width=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471256.png\" alt=\"rId53\" width=\"105\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471373.png\" alt=\"rId54\" width=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471562.png\" alt=\"rId55\" width=\"105\"></p>"],
                    solution_en: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471102.png\" alt=\"rId52\" width=\"110\"></p>",
                    solution_hi: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471102.png\" alt=\"rId52\" width=\"110\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the number from among the given options that can replace the question mark (?) in the following series.<br>30, 22, 36, 75, 178, ?</p>",
                    question_hi: "<p>25. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>30, 22, 36, 75, 178, ?</p>",
                    options_en: ["<p>343</p>", "<p>480</p>", 
                                "<p>361</p>", "<p>400</p>"],
                    options_hi: ["<p>343</p>", "<p>480</p>",
                                "<p>361</p>", "<p>400</p>"],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471733.png\" alt=\"rId56\" width=\"383\" height=\"75\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731128471733.png\" alt=\"rId56\" width=\"383\" height=\"75\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>