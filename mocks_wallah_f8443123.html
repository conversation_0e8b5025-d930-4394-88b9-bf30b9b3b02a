<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The perimeter of a square whose area is the same as the area of a semi-circle of radius&nbsp;2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm is:</p>",
                    question_hi: "<p>1. एक वर्ग का परिमाप ज्ञात करें, जिसका क्षेत्रफल 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> त्रिज्या के अर्धवृत्त के क्षेत्रफल के बराबर है।</p>",
                    options_en: ["<p>8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math> cm</p>", "<p>8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">&#960;</mi></msqrt></math> cm</p>", 
                                "<p>16<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math>&nbsp;cm</p>", "<p>4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math>&nbsp;cm</p>"],
                    options_hi: ["<p>8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math>&nbsp;cm</p>", "<p>8<math display=\"inline\"><msqrt><mn>2</mn><mi>&#960;</mi></msqrt></math> cm</p>",
                                "<p>16<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math>&nbsp;cm</p>", "<p>4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math>&nbsp;cm</p>"],
                    solution_en: "<p>1.(a) <br>According to the question,<br>a<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &pi;r<sup>2</sup><br>a<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &pi; &times; (2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>)<sup>2</sup> = 4&pi; <br>a = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mi mathvariant=\"normal\">&#960;</mi></msqrt></math>&nbsp;= 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math><br>Perimeter of squ<math display=\"inline\"><mi>a</mi></math>re (4a) = 8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math> cm</p>",
                    solution_hi: "<p>1.(a) <br>प्रश्न के अनुसार,<br>a<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &pi;r<sup>2</sup><br>a<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &pi; &times; (2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>)<sup>2</sup> = 4&pi; <br>a = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mi mathvariant=\"normal\">&#960;</mi></msqrt></math>&nbsp;= 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math><br>वर्ग का परिमाप (4a) = 8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">&#960;</mi></msqrt></math> सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A metallic solid cuboid of dimensions 36 cm &times; 22 cm &times; 12 cm is melted and recast in&nbsp;the form of cubes of side 6 cm. Find the number of the cubes so formed.</p>",
                    question_hi: "<p>2. 36 cm &times; 22 cm &times; 12 cm आयामों वाले एक धातु के ठोस घनाभ को पिघलाकर 6 cm भुजा वाले घनों के&nbsp;रूप में ढाला जाता है। इस प्रकार बने घनों की संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>45</p>", "<p>46</p>", 
                                "<p>43</p>", "<p>44</p>"],
                    options_hi: ["<p>45</p>", "<p>46</p>",
                                "<p>43</p>", "<p>44</p>"],
                    solution_en: "<p>2.(d)<br>Volume of cuboid (lbh) = 36 cm &times; 22 cm &times; 12 cm <br>Volume of each cube = 6 &times; 6 &times; 6 <br>So, required number of cubes = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#215;</mo><mn>22</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> = 44</p>",
                    solution_hi: "<p>2.(d)<br>घनाभ का आयतन (lbh) = 36 cm &times; 22 cm &times; 12 cm <br>प्रत्येक घन का आयतन = 6 &times; 6 &times; 6 <br>अतः, घनों की अपेक्षित संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#215;</mo><mn>22</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math>&nbsp;= 44</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A solid metallic cylinder of base radius 6 cm and height 10 cm is melted to form small cylinders, each of height 2.5 cm and base radius 2 cm. Find the number of small cylinders.</p>",
                    question_hi: "<p>3. 6 cm आधार त्रिज्या और 10 cm ऊंचाई वाले धातु के एक ठोस बेलन को पिघलाकर छोटे बेलन बनाए जाते हैं, जिनमें से प्रत्येक की ऊंचाई 2.5 cm और आधार त्रिज्या 2 cm है। छोटे बेलनों की संख्या ज्ञात करें।</p>",
                    options_en: ["<p>36</p>", "<p>32</p>", 
                                "<p>80</p>", "<p>360</p>"],
                    options_hi: ["<p>36</p>", "<p>32</p>",
                                "<p>80</p>", "<p>360</p>"],
                    solution_en: "<p>3.(a) <br>Let the number of small cylinder = n<br>Volume of the cylinder = &pi;r<sup>2</sup>h<br><strong id=\"docs-internal-guid-4297e710-7fff-e875-e6b8-cf78a3d12682\"></strong>Volume of bigger cylinder = n &times; volume of small cylinder <br>6 &times; 6 &times; 10 = n &times; 2 &times; 2 &times; 2.5<br>n = 36</p>",
                    solution_hi: "<p>3.(a) <br>माना छोटे बेलनों की संख्या = n<br>बेलन का आयतन = &pi;r<sup>2</sup>h<br>बडे बेलन का आयतन = n &times; छोटे बेलन का आयतन<br>6 &times; 6 &times; 10 = n &times; 2 &times; 2 &times; 2.5<br>n = 36</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The area of an isosceles right angled triangle is 16 m<sup>2</sup> . Its hypotenuse is _______.</p>",
                    question_hi: "<p>4. एक समद्विबाहु समकोण त्रिभुज का क्षेत्रफल 16 m&sup2; है। इसका कर्ण ___________ है।</p>",
                    options_en: ["<p>0.8 m</p>", "<p>8 m</p>", 
                                "<p>4 m</p>", "<p>8 cm</p>"],
                    options_hi: ["<p>0.8 m</p>", "<p>8 m</p>",
                                "<p>4 m</p>", "<p>8 cm</p>"],
                    solution_en: "<p>4.(b) <br>Area of isosceles right angle triangle = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow><mn>2</mn></mfrac></math>)<sup>2</sup><br>16 = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow><mn>2</mn></mfrac></math>)<sup>2</sup><br>Hypotenuse = 8 m</p>",
                    solution_hi: "<p>4.(b) <br>समद्विबाहु समकोण त्रिभुज का क्षेत्रफल = (<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math>)<sup>2</sup><br>16 = (<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math>)<sup>2</sup><br>कर्ण = 8 m</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Which of the following statements is INCORRECT about the circle?</p>",
                    question_hi: "<p>5. वृत्त के संबंध निलिखित में से कौन-सा कथन असत्य है?</p>",
                    options_en: ["<p>The area of a sector = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> (arc length &times; R)</p>", "<p>The radius of an incircle of an equilateral triangle of side \'s\' is <math display=\"inline\"><mfrac><mrow><mi>s</mi></mrow><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>", 
                                "<p>The length of an arc = <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>e</mi><mi>n</mi><mi>t</mi><mi>r</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi><mi>&#160;</mi><mi>m</mi><mi>a</mi><mi>d</mi><mi>e</mi><mi>&#160;</mi><mi>b</mi><mi>y</mi><mi>&#160;</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>&#160;</mi><mi>a</mi><mi>r</mi><mi>c</mi></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; &pi;r<sup>2</sup>.</p>", "<p>Circles are congruent if they have same radii while similar circles may have different radii.</p>"],
                    options_hi: ["<p>त्रिज्यखंड का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> (चाप की लंबाई &times; R)</p>", "<p>\'s\' भुजा वाले एक समबाहु त्रिभुज के अन्तर्वृत्त की त्रिज्या <math display=\"inline\"><mfrac><mrow><mi>s</mi></mrow><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> होगी।</p>",
                                "<p>चाप की लंबाई = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2330;&#2366;&#2346;</mi><mo>&#160;</mo><mi>&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</mi><mo>&#160;</mo><mi>&#2348;&#2344;&#2366;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351;</mi><mo>&#160;</mo><mi>&#2325;&#2379;&#2339;</mi></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; &pi;r<sup>2</sup></p>", "<p>यदि वृत्तों की त्रिज्याएँ समान हों तो वृत्त सर्वांगसम होते हैं जबकि समरूप वृत्तों की त्रिज्याएँ भिन्न-भिन्न हो सकती हैं।</p>"],
                    solution_en: "<p>5.(c) <br>Option &lsquo;c&rsquo; is false<br>Length of an arc = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 2&pi;r</p>",
                    solution_hi: "<p>5.(c) <br>विकल्प \'c\' गलत है<br>चाप की लंबाई = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 2&pi;r</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Find the cost of painting a ball that is in the shape of a sphere with a radius of 14 cm. The painting cost of the ball is ₹5 per square centimetre (take &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>).</p>",
                    question_hi: "<p>6. 14 cm त्रिज्या वाले गोले के आकार की एक गेंद को पेंट करने की लागत ज्ञात कीजिए, यदि गेंद की पेंटिंग की लागत ₹5 प्रति वर्ग सेंटीमीटर है ( &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> लें )।</p>",
                    options_en: ["<p>₹12,230</p>", "<p>₹12,320</p>", 
                                "<p>₹13,022</p>", "<p>₹13,220</p>"],
                    options_hi: ["<p>₹12,230</p>", "<p>₹12,320</p>",
                                "<p>₹13,022</p>", "<p>₹13,220</p>"],
                    solution_en: "<p>6.(b)<br>Area of sphere = 4&pi;r<sup>2</sup><br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 &times; 14 = 2464 cm<sup>2</sup><br>Cost of painting a ball = 2464 &times; 5 = ₹12,320</p>",
                    solution_hi: "<p>6.(b)<br>गोले का क्षेत्रफल = 4&pi;r<sup>2</sup><br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 &times; 14 = 2464 cm<sup>2</sup><br>एक गेंद को पेंट करने की लागत = 2464 &times; 5 = ₹12,320</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Two cubes, each of edge 5 cm, are joined face to face. Find the surface area of the&nbsp;cuboid thus formed.</p>",
                    question_hi: "<p>7. प्रत्येक 5 cm किनारे (edge) वाले दो घनों को उनके फलकों (face) की तरफ से एक-दूसरे के साथ&nbsp;जोड़ा जाता है। इस प्रकार बने घनाभ का पृष्ठीय क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: ["<p>250 cm&sup2;</p>", "<p>300 cm&sup2;</p>", 
                                "<p>125 cm&sup2;</p>", "<p>200 cm&sup2;</p>"],
                    options_hi: ["<p>250 cm&sup2;</p>", "<p>300 cm&sup2;</p>",
                                "<p>125 cm&sup2;</p>", "<p>200 cm&sup2;</p>"],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736749769271.png\" alt=\"rId4\" width=\"157\" height=\"92\"><br>Length of cuboid (l) = (5 + 5)cm = 10 cm<br>Breadth of cuboid (b) = 5 cm<br>Height of cuboid (h) = 5 cm<br>Surface area of cuboid = 2(lb + bh + hl)<br>= 2 &times; (10 &times; 5 + 5 &times; 5 + 5 &times; 10) <br>= 2(50 + 25 + 50) <br>= 250 cm<sup>2</sup></p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736749769271.png\" alt=\"rId4\" width=\"157\" height=\"92\"><br>घनाभ की लंबाई (l) = (5 + 5)cm = 10 सेमी<br>घनाभ की चौड़ाई (b) = 5 सेमी<br>घनाभ की ऊँचाई (h) = 5 सेमी<br>घनाभ का पृष्ठीय क्षेत्रफल = 2(lb + bh + hl)<br>= 2 &times; (10 &times; 5 + 5 &times; 5 + 5 &times; 10) <br>= 2(50 + 25 + 50) <br>= 250 सेमी<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The inner and outer radii of a hemispherical wooden bowl are 6 cm and 8 cm, respectively. Its entire surface has to be polished and the cost of polishing &pi; cm<sup>2</sup> is Rs.50. How much will it cost to polish the bowl?</p>",
                    question_hi: "<p>8. एक अर्धगोलाकार लकड़ी के कटोरे की भीतरी और बाहरी त्रिज्या क्रमशः 6 cm और 8 cm हैं। इसकी&nbsp;पूरी सतह को पॉलिश किया जाना है और &pi; cm<sup>2</sup> को पॉलिश करने की लागत Rs. 50 है। कटोरे को&nbsp;पॉलिश करने में कितना खर्च आएगा?</p>",
                    options_en: ["<p>Rs.11,600</p>", "<p>Rs.11,400</p>", 
                                "<p>Rs.10,000</p>", "<p>Rs.12,000</p>"],
                    options_hi: ["<p>Rs.11,600</p>", "<p>Rs.11,400</p>",
                                "<p>Rs.10,000</p>", "<p>Rs.12,000</p>"],
                    solution_en: "<p>8.(b)<br>Outer area of hemispherical bowl = 2&pi;(R)<sup>2</sup> = 2&pi; (8)<sup>2</sup> = 128&pi; cm<sup>2</sup><br>Inner area of hemispherical bowl = 2&pi; (r)<sup>2</sup> = 2&pi; (6)<sup>2</sup> = 72&pi; cm<sup>2</sup><br>Ring area of the top = &pi;(R<sup>2</sup> - r<sup>2</sup>) = &pi;(64 - 36) = 28&pi; cm<sup>2</sup><br>Total area to polished = 128&pi; + 72&pi; + 28&pi; = 228&pi; cm<sup>2</sup><br>Hence, cost to polish the bowl = 228&pi; = 228 &times; 50 = Rs. 11400</p>",
                    solution_hi: "<p>8..(b)<br>अर्धगोलाकार कटोरे का बाहरी क्षेत्रफल = 2&pi; (R)<sup>2</sup> = 2&pi; (8)<sup>2</sup> = 128&pi; सेमी<sup>2</sup><br>अर्धगोलाकार कटोरे का आंतरिक क्षेत्रफल = 2&pi; (r)<sup>2</sup> = 2&pi; (6)<sup>2</sup> = 72&pi; सेमी<sup>2</sup><br>शीर्ष वलय का क्षेत्रफल = &pi;(R<sup>2</sup> - r<sup>2</sup>) = &pi;(64 - 36) = 28&pi; सेमी<sup>2</sup><br>पॉलिश किया गया कुल क्षेत्रफल = 128&pi; + 72&pi; + 28&pi; = 228&pi; सेमी<sup>2</sup><br>अत:, कटोरे को पॉलिश करने की लागत = 228&pi; = 228 &times; 50 = Rs. 11400</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A covered wooden box has the inner measures as 128 cm, 90 cm, 25 cm and the thickness of wood is 5.5 cm. Find the volume of the wood.</p>",
                    question_hi: "<p>9. एक ढके हुए लकड़ी के बॉक्स की भीतरी माप 128 cm, 90 cm, 25 cm है और लकड़ी की मोटाई 5.5 cm है। लकड़ी का आयतन ज्ञात करें।</p>",
                    options_en: ["<p>192392 cm<sup>3</sup></p>", "<p>819832 cm<sup>3</sup></p>", 
                                "<p>329431 cm<sup>3</sup></p>", "<p>217404 cm<sup>3</sup></p>"],
                    options_hi: ["<p>192392 cm<sup>3</sup></p>", "<p>819832 cm<sup>3</sup></p>",
                                "<p>329431 cm<sup>3</sup></p>", "<p>217404 cm<sup>3</sup></p>"],
                    solution_en: "<p>9.(d)<br>Dimension of inner part of wooden box = 128 &times; 90 &times; 25 <br>Volume of inner part = 128 &times; 90 &times; 25 = 288000 cm<sup>3</sup><br>Dimension of outer part of wooden box = (128 + 11) &times; (90 + 11) &times; (25 + 11) <br>Volume of outer part of wooden box = 139 &times; 101 &times; 36 = 505404 cm<sup>3</sup><br>Volume of wood = volume of outer part - volume of inner part <br>= 505404 - 288000 <br>= 217404 cm<sup>3</sup></p>",
                    solution_hi: "<p>9.(d)<br>लकड़ी के बक्से के भीतरी भाग का आयाम = 128 &times; 90 &times; 25 <br>आंतरिक भाग का आयतन = 128 &times; 90 &times; 25 =288000 सेमी<sup>3</sup><br>लकड़ी के बक्से के बाहरी भाग का आयाम = (128 + 11) &times; (90 + 11) &times; (25 + 11) <br>लकड़ी के बक्से के बाहरी भाग का आयतन = 139 &times; 101 &times; 36 = 505404 सेमी<sup>3</sup><br>लकड़ी का आयतन = बाहरी भाग का आयतन - भीतरी भाग का आयतन <br>= 505404 - 288000 <br>= 217404 सेमी<sup>3</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A cylinder has a curved surface area equal to 350% of the curved surface area of another cylinder. If their radii are in the ratio 3 : 1, then the volume of the smaller cylinder is approximately _____% of the larger cylinder (the smaller cylinder has its radius and height smaller when compared to the larger).</p>",
                    question_hi: "<p>10. एक बेलन का वक्र पृष्ठीय क्षेत्रफल दूसरे बेलन के वक्र पृष्ठीय क्षेत्रफल के 350% के बराबर है। यदि उनकी त्रिज्याएँ 3 : 1 के अनुपात में हैं, तो छोटे आकार वाले बेलन का आयतन, बड़े आकार वाले बेलन का लगभग _____% है (बड़े आकार वाले बेलन की तुलना में छोटे आकार वाले बेलन की त्रिज्या और ऊँचाई कम है)।</p>",
                    options_en: ["<p>10.16</p>", "<p>8.86</p>", 
                                "<p>7.28</p>", "<p>9.52</p>"],
                    options_hi: ["<p>10.16</p>", "<p>8.86</p>",
                                "<p>7.28</p>", "<p>9.52</p>"],
                    solution_en: "<p>10.(d)<br>According to the question,<br>CSA of the cylinder = 2&pi;rh<br>2&pi;rh &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>100</mn></mfrac></math> = 2&pi;RH<br>h : H = 6 : 7 (where radii ratio are, 3 : 1)<br>Volume of the cylinder = &pi;r<sup>2</sup>h<br>Small cylinder volume = &pi;(1)<sup>2</sup> &times; 6<br><strong id=\"docs-internal-guid-c08a1efa-7fff-801c-d465-5d68453d985f\"></strong>Big cylinder volume = &pi;(3)<sup>2</sup> &times; 7<br>required% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#960;</mi><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#215;</mo><mn>6</mn></mrow><mrow><mi>&#160;</mi><mi>&#960;</mi><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math>&nbsp;&times; 100 = 9.52%</p>",
                    solution_hi: "<p>10.(d)<br>प्रश्न के अनुसार,<br>वेलन का वक्रपृष्ठीय क्षेत्रफल = 2&pi;rh<br>2&pi;rh &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>100</mn></mfrac></math> = 2&pi;RH<br>h : H = 6 : 7 (त्रिज्याएँ , 3 : 1 के अनुपात मे है।)<br>वेलन का आयतन = &pi;r<sup>2</sup>h<br>छोटे वेलन का आयतन = &pi;(1)<sup>2</sup> &times; 6<br>बडे़ वेलन का आयतन = &pi;(3)<sup>2</sup> &times; 7<br>आवश्यक% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#960;</mi><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#215;</mo><mn>6</mn></mrow><mrow><mi>&#160;</mi><mi>&#960;</mi><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> &times; 100 = 9.52%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The length of a rectangular shaped park exceeds its breadth by 15 m. If the perimeter of the park is 110 m, find the length of the park (in m).</p>",
                    question_hi: "<p>11. एक आयताकार पार्क की लंबाई उसकी चौड़ाई से 15m अधिक है। यदि पार्क की परिधि 110m है, तो पार्क की लंबाई (m में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>35</p>", "<p>40</p>", 
                                "<p>25</p>", "<p>20</p>"],
                    options_hi: ["<p>35</p>", "<p>40</p>",
                                "<p>25</p>", "<p>20</p>"],
                    solution_en: "<p>11.(a)<br>Let breadth of park be x&nbsp;m<br>Length = (x&nbsp;+ 15) m<br>Perimeter of rectangular park = 2[(x&nbsp;+ 15) + (x)] = 4x + 30<br>According to question,<br>4x&nbsp;+ 30 = 110 <br>&rArr; x = 20<br>Length of rectangular park = x&nbsp;+ 15 = 35 m</p>",
                    solution_hi: "<p>11.(a)<br>माना पार्क की चौड़ाई x&nbsp;मीटर है<br>लंबाई = (x&nbsp;+ 15) मीटर<br>आयताकार पार्क का परिमाप = 2[(x&nbsp;+ 15) + (x)] = 4x + 30<br>प्रश्न के अनुसार,<br>4x&nbsp;+ 30 = 110 <br>&rArr; x = 20<br>आयताकार पार्क की लंबाई = x&nbsp;+ 15 <br>= 35 मीटर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If the lateral surface area of a cube is 144 cm<sup>2</sup> , then find its volume.</p>",
                    question_hi: "<p>12. यदि किसी घन का पार्श्व पृष्ठीय क्षेत्रफल 144 cm<sup>2</sup> है, तो उसका आयतन ज्ञात कीजिए।</p>",
                    options_en: ["<p>864 cm<sup>3</sup></p>", "<p>1728 cm<sup>3</sup></p>", 
                                "<p>117.58 cm<sup>3</sup></p>", "<p>216 cm<sup>3</sup></p>"],
                    options_hi: ["<p>864 cm<sup>3</sup></p>", "<p>1728 cm<sup>3</sup></p>",
                                "<p>117.58 cm<sup>3</sup></p>", "<p>216 cm<sup>3</sup></p>"],
                    solution_en: "<p>12.(d) <br>Lateral surface area of cube = 4a<sup>2</sup><br>4a<sup>2</sup> = 144 <br>&rArr;&nbsp;a = 6 cm<br>Volume of cube (a<sup>3</sup>) = 6<sup>3</sup> = 216 cm<sup>3</sup></p>",
                    solution_hi: "<p>12.(d) <br>घन का पार्श्व पृष्ठीय क्षेत्रफल = 4a<sup>2</sup><br>4a<sup>2</sup> = 144 <br>&rArr; a = 6 सेमी<br>घन का आयतन(a<sup>3</sup>) = 6<sup>3</sup> = 216 सेमी<sup>3</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If the areas of three adjacent faces of a cuboid are 5 cm<sup>2</sup> , 15 cm<sup>2</sup> , 27 cm<sup>2</sup> , respectively, the surface area of the cuboid is:</p>",
                    question_hi: "<p>13. यदि एक घनाभ के तीन आसन्न फलकों का क्षेत्रफल क्रमशः 5 cm<sup>2</sup> , 15 cm<sup>2</sup> , 27 cm<sup>2</sup> है, तो घनाभ का पृष्ठीय क्षेत्रफल क्या होगा?</p>",
                    options_en: ["<p>75 cm<sup>2</sup></p>", "<p>47 cm<sup>2</sup></p>", 
                                "<p>45 cm<sup>2</sup></p>", "<p>94 cm<sup>2</sup></p>"],
                    options_hi: ["<p>75 cm<sup>2</sup></p>", "<p>47 cm<sup>2</sup></p>",
                                "<p>45 cm<sup>2</sup></p>", "<p>94 cm<sup>2</sup></p>"],
                    solution_en: "<p>13.(d) <br>Surface area of cuboid = 2(lb + bh + hl) <br>= 2(5 + 15 + 27) <br>= 94 cm<sup>2</sup></p>",
                    solution_hi: "<p>13.(d) <br>घनाभ का पृष्ठीय क्षेत्रफल = 2(lb + bh + hl) <br>= 2(5 + 15 + 27) <br>= 94 सेमी<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A closed cylindrical tank of radius 1.5 m and height 3 m is made from a sheet of metal,&nbsp;how much sheet of metal is required to do it?</p>",
                    question_hi: "<p>14. 1.5 मीटर त्रिज्या और 3 मीटर ऊँचाई वाली एक बंद बेलनाकार टंकी धातु की शीट से बनाई जाती है, इसे&nbsp;बनाने के लिए धातु की कितनी शीट की आवश्यकता होगी?</p>",
                    options_en: ["<p>42.24 square metre</p>", "<p>24.24 square metre</p>", 
                                "<p>42.42 square metre</p>", "<p>24.42 square metre</p>"],
                    options_hi: ["<p>42.24 वर्ग मीटर</p>", "<p>24.24 वर्ग मीटर</p>",
                                "<p>42.42 वर्ग मीटर</p>", "<p>24.42 वर्ग मीटर</p>"],
                    solution_en: "<p>14.(c)<br>Cylindrical tank is closed <br>So, total surface area cylindrical tank = 2&pi;r(r+h)<br>2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 1.5 (1.5 + 3) = 42.42 m<sup>2</sup><br>Hence, required metal used = 42.42 m<sup>2</sup></p>",
                    solution_hi: "<p>14.(c)<br>बेलनाकार टैंक बंद है <br>तो, बेलनाकार टैंक का कुल सतह क्षेत्रफल = 2&pi;r(r+h)<br>2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 1.5 (1.5 + 3) = 42.42 m<sup>2</sup><br>अतः, प्रयुक्त आवश्यक धातु = 42.42 m<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Two cubes have their volumes in the ratio 1 : 125. Find the ratio of their surface areas.</p>",
                    question_hi: "<p>15. दो घनों के आयतनों का अनुपात 1 : 125 है। उनके पृष्ठीय क्षेत्रफलों का अनुपात ज्ञात करें।</p>",
                    options_en: ["<p>1 : 125</p>", "<p>1 : 25</p>", 
                                "<p>1 : 5</p>", "<p>25 : 1</p>"],
                    options_hi: ["<p>1 : 125</p>", "<p>1 : 25</p>",
                                "<p>1 : 5</p>", "<p>25 : 1</p>"],
                    solution_en: "<p>15.(b) <br>Sides ratio = <math display=\"inline\"><mroot><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>125</mn><mn>3</mn></mroot></math> = 1 : 5<br>Surface area of cube = 6a<sup>2</sup> <br>Required ratio = 6 &times; (1)<sup>2</sup> : 6 &times; (5)<sup>2</sup> <br>= 1 : 25</p>",
                    solution_hi: "<p>15.(b) <br>भुजाओं का अनुपात = <math display=\"inline\"><mroot><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>125</mn><mn>3</mn></mroot></math> = 1 : 5<br>घन का पृष्ठीय क्षेत्रफल = 6a<sup>2</sup><br>आवश्यक अनुपात = 6 &times; (1)<sup>2</sup> : 6 &times; (5)<sup>2</sup> <br>= 1 : 25</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>