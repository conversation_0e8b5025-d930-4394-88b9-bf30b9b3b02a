<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option figure in which the given figure is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162508888.png\" alt=\"rId5\"></p>",
                    question_hi: "<p>1. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति इसके भाग के रूप में अंतर्निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162508888.png\" alt=\"rId5\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162508988.png\" alt=\"rId6\" width=\"101\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509087.png\" alt=\"rId7\" width=\"101\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509187.png\" alt=\"rId8\" width=\"102\" height=\"108\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509295.png\" alt=\"rId9\" width=\"103\" height=\"115\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162508988.png\" alt=\"rId6\" width=\"101\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509087.png\" alt=\"rId7\" width=\"101\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509187.png\" alt=\"rId8\" width=\"102\" height=\"108\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509295.png\" alt=\"rId9\" width=\"103\" height=\"115\"></p>"
                    ],
                    solution_en: "<p>1.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509454.png\" alt=\"rId10\" width=\"110\" height=\"108\"></p>",
                    solution_hi: "<p>1.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509454.png\" alt=\"rId10\" width=\"110\" height=\"108\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Four letter-clusters have been given, out of which three are alike in some manner and One is different. Select the letter-cluster that is different.<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster)</p>",
                    question_hi: "<p>2. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए।<br>(<strong>ध्यान दें : </strong>असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>PKRI</p>",
                        "<p>MNOL</p>",
                        "<p>AZCX</p>",
                        "<p>PLRI</p>"
                    ],
                    options_hi: [
                        "<p>PKRI</p>",
                        "<p>MNOL</p>",
                        "<p>AZCX</p>",
                        "<p>PLRI</p>"
                    ],
                    solution_en: "<p>2.(d)<br><strong id=\"docs-internal-guid-73c52c9a-7fff-4d38-57aa-215a2c5d33ec\"><strong id=\"docs-internal-guid-0bc975e4-7fff-1d4b-49f1-b76f582ce7fe\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXffPR7I45e_EPfTf3Z4nPf0zxntvwxtRkKPkyje4xWu725qupmxWZz1yD9vRg3LqV5GQd4rUZy4FbWaoqYAvt9yH54TMpseL4XwRV8goFzSCRqMNW8K7GDfSMbF-jzd1e1HL8LcTg?key=q9EK8SaztIsleOthyh8xx1kV\" width=\"165\" height=\"105\"></strong>,&nbsp;</strong><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509652.png\" alt=\"rId12\" width=\"181\" height=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509770.png\" alt=\"rId13\" width=\"163\" height=\"90\">,<br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509865.png\" alt=\"rId14\" width=\"170\" height=\"97\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162509960.png\" alt=\"rId15\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510054.png\" alt=\"rId16\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510161.png\" alt=\"rId17\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510259.png\" alt=\"rId18\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, \'CASE\' is written as \'FTBD\' and \'CAKE\' is written as \'FLBD\'. How will \'CALF&rsquo; be written in that language?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में, \'CASE\' को \'FTBD\' लिखा जाता है और &lsquo;CAKE\' को \'FLBD\' लिखा जाता है। इसी कूट भाषा में \'CALF&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: [
                        "<p>GMDB</p>",
                        "<p>GBMD</p>",
                        "<p>GMBD</p>",
                        "<p>GBDM</p>"
                    ],
                    options_hi: [
                        "<p>GMDB</p>",
                        "<p>GBMD</p>",
                        "<p>GMBD</p>",
                        "<p>GBDM</p>"
                    ],
                    solution_en: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510366.png\" alt=\"rId19\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510482.png\" alt=\"rId20\"><br>Similarly, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510592.png\" alt=\"rId21\"></p>",
                    solution_hi: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510366.png\" alt=\"rId19\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510482.png\" alt=\"rId20\"><br>इसी प्रकार,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510592.png\" alt=\"rId21\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(35, 5, 7)<br>(108, 12, 9)</p>",
                    question_hi: "<p>4. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में लग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(35, 5, 7)<br>(108, 12, 9)</p>",
                    options_en: [
                        "<p>(118, 15, 9)</p>",
                        "<p>(114, 14, 8)</p>",
                        "<p>(91, 13, 7)</p>",
                        "<p>(254, 17, 7)</p>"
                    ],
                    options_hi: [
                        "<p>(118, 15, 9)</p>",
                        "<p>(114, 14, 8)</p>",
                        "<p>(91, 13, 7)</p>",
                        "<p>(254, 17, 7)</p>"
                    ],
                    solution_en: "<p>4.(c) <strong>Logic :-</strong> (2nd number &times; 3rd number) = 1st number<br>(35 , 5 ,7) :- (5 &times; 7) = 35<br>(108, 12, 9) :- (12 &times; 9) = 108<br>Similarly,<br>(91, 13, 7) :- (13 &times; 7) = 91</p>",
                    solution_hi: "<p>4.(c)<strong> तर्क:-</strong> (दूसरी संख्या &times; तीसरी संख्या) = पहली संख्या<br>(35 , 5 ,7) :- (5 &times; 7) = 35<br>(108, 12, 9) :- (12 &times; 9) = 108<br>इसी प्रकार,<br>(91, 13, 7) :- (13 &times; 7) = 91</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the correct mirror image of the given figure when the mirror is placed at line MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510689.png\" alt=\"rId22\"></p>",
                    question_hi: "<p>5. दर्पण को चित्र में दिखाए अनुसार रेखा MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिम्ब का चयन करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510689.png\" alt=\"rId22\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510784.png\" alt=\"rId23\" width=\"100\" height=\"36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510872.png\" alt=\"rId24\" width=\"100\" height=\"36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510963.png\" alt=\"rId25\" width=\"103\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511073.png\" alt=\"rId26\" width=\"98\" height=\"40\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510784.png\" alt=\"rId23\" width=\"100\" height=\"36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510872.png\" alt=\"rId24\" width=\"100\" height=\"36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510963.png\" alt=\"rId25\" width=\"103\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511073.png\" alt=\"rId26\" width=\"98\" height=\"40\"></p>"
                    ],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510963.png\" alt=\"rId25\" width=\"103\" height=\"33\"></p>",
                    solution_hi: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162510963.png\" alt=\"rId25\" width=\"103\" height=\"33\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the correct option that indicates the arrangement of the following words in a logical and meaningful order. <br>1. River <br>2. Puddle <br>3. Pond <br>4. Lake <br>5. Ocean</p>",
                    question_hi: "<p>6. उस सही विकल्प का चयन करें जो निम्नलिखित शब्दों की व्यवस्था को तार्किक और अर्थपूर्ण क्रम में दर्शाता है।<br>1. नदी<br>2. बरसाती पोखर (Puddle)<br>3. तालाब (Pond)<br>4. झील<br>5. महासागर</p>",
                    options_en: [
                        "<p>5, 3, 4, 1, 2</p>",
                        "<p>5, 4, 3, 1, 2</p>",
                        "<p>5, 1, 4, 3, 2</p>",
                        "<p>5, 4, 1, 3, 2</p>"
                    ],
                    options_hi: [
                        "<p>5, 3, 4, 1, 2</p>",
                        "<p>5, 4, 3, 1, 2</p>",
                        "<p>5, 1, 4, 3, 2</p>",
                        "<p>5, 4, 1, 3, 2</p>"
                    ],
                    solution_en: "<p>6.(c) The correct order is <br>Ocean(5) &rarr; River(1) &rarr; Lake(4) &rarr; Pond(3) &rarr; Puddle(2)</p>",
                    solution_hi: "<p>6.(c) सही क्रम है<br>महासागर(5) &rarr; नदी(1) &rarr; झील(4) &rarr; तालाब (Pond) (3) &rarr; बरसाती पोखर (Puddle) (2)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the figure from among the given options that can replace the question mark (?) in the following series and complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511259.png\" alt=\"rId27\" width=\"494\" height=\"100\"></p>",
                    question_hi: "<p>7. दिए गए विकल्पों में से उस आकृति को चुनिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकती है और पैटर्न को पूरा कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511259.png\" alt=\"rId27\" width=\"494\" height=\"100\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511407.png\" alt=\"rId28\" width=\"102\" height=\"102\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511534.png\" alt=\"rId29\" width=\"100\" height=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511618.png\" alt=\"rId30\" width=\"101\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511708.png\" alt=\"rId31\" width=\"101\" height=\"101\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511407.png\" alt=\"rId28\" width=\"102\" height=\"102\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511534.png\" alt=\"rId29\" width=\"100\" height=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511618.png\" alt=\"rId30\" width=\"101\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511708.png\" alt=\"rId31\" width=\"101\" height=\"101\"></p>"
                    ],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511534.png\" alt=\"rId29\" width=\"100\" height=\"100\"></p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511534.png\" alt=\"rId29\" width=\"100\" height=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. 624 is related to 160 following a certain logic. Following the same logic, 448 is related to 116. To which of the following is 528 related, following the same logic? (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>8. एक निश्चित तर्क के अनुसार 624 का संबंध 160 से है। उसी तर्क का अनुसरण करते हुए, 448 का संबंध 116 से है। उसी तर्क का अनुसरण करते हुए, 528 का संबंध निम्नलिखित में से किससे है? <br>(ध्यान दें: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/ घटाना/ गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है|)</p>",
                    options_en: [
                        "<p>136</p>",
                        "<p>148</p>",
                        "<p>130</p>",
                        "<p>128</p>"
                    ],
                    options_hi: [
                        "<p>136</p>",
                        "<p>148</p>",
                        "<p>130</p>",
                        "<p>128</p>"
                    ],
                    solution_en: "<p>8.(a)<br><strong>Logic:-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mn>1</mn><mi>st</mi></msup><mi>no</mi><mo>.</mo></mrow><mn>4</mn></mfrac></math> + 4 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup></math>no. <br>(624, 160) :- <math display=\"inline\"><mfrac><mrow><mn>624</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 4 &rArr; 156 + 4 = 160<br>(448, 116) :- <math display=\"inline\"><mfrac><mrow><mn>448</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 4 &rArr; 112 + 4 = 116<br>similarly<br>(528, ?) :- <math display=\"inline\"><mfrac><mrow><mn>528</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 4 &rArr; 132 + 4 = 136</p>",
                    solution_hi: "<p>8.(a)<br><strong>तर्क :-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>4</mn></mfrac></math> + 4 = दूसरी संख्या <br>(624, 160) :- <math display=\"inline\"><mfrac><mrow><mn>624</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 4 &rArr; 156 + 4 = 160<br>(448, 116) :- <math display=\"inline\"><mfrac><mrow><mn>448</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 4 &rArr; 112 + 4 = 116<br>इसी प्रकार <br>(528, ?) :- <math display=\"inline\"><mfrac><mrow><mn>528</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 4 &rArr; 132 + 4 = 136</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the term from among the given options that can replace the question mark (?) in the following series.<br>YL 93, XK 88, WJ 83, VI 78, ?</p>",
                    question_hi: "<p>9. दिए गए विकल्पों में से उस पद का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकता है।<br>YL 93, XK 88, WJ 83, VI 78, ?</p>",
                    options_en: [
                        "<p>VG 74</p>",
                        "<p>UH 72</p>",
                        "<p>VI 74</p>",
                        "<p>UH 73</p>"
                    ],
                    options_hi: [
                        "<p>VG 74</p>",
                        "<p>UH 72</p>",
                        "<p>VI 74</p>",
                        "<p>UH 73</p>"
                    ],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511887.png\" alt=\"rId32\" width=\"393\" height=\"101\"></p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162511887.png\" alt=\"rId32\" width=\"393\" height=\"101\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the option that represents the letters that, when sequentially placed from left to right in the blanks below, will complete the letter series.<br>SA_C_ _S_D_AT_ _DCA_S_DC_T</p>",
                    question_hi: "<p>10. उस विकल्प का चयन कीजिए जो उन अक्षरों का निरूपण करते हैं, जिन्हें नीचे दिए गए रिक्त स्थान में क्रमिक रूप से बाएं से दाएं रखे जाने पर अक्षर श्रृंखला पूर्ण हो जाए।<br>SA_C_ _S_D_AT_ _DCA_S_DC_T</p>",
                    options_en: [
                        "<p>DATACSATAA</p>",
                        "<p>CASASSASAS</p>",
                        "<p>SASASSASAA</p>",
                        "<p>DTATSDADAA</p>"
                    ],
                    options_hi: [
                        "<p>DATACSATAA</p>",
                        "<p>CASASSASAS</p>",
                        "<p>SASASSASAA</p>",
                        "<p>DTATSDADAA</p>"
                    ],
                    solution_en: "<p>10.(a) <br>SA<span style=\"text-decoration: underline;\"><strong>D</strong></span>C<span style=\"text-decoration: underline;\"><strong>AT</strong></span> / S<span style=\"text-decoration: underline;\"><strong>A</strong></span>D<strong><span style=\"text-decoration: underline;\">C</span></strong>AT/ <span style=\"text-decoration: underline;\">S<strong>A</strong></span>DCA<span style=\"text-decoration: underline;\"><strong>T</strong></span> / S<span style=\"text-decoration: underline;\"><strong>A</strong></span>DC<span style=\"text-decoration: underline;\"><strong>A</strong></span>T</p>",
                    solution_hi: "<p>10.(a) <br>SA<span style=\"text-decoration: underline;\"><strong>D</strong></span>C<span style=\"text-decoration: underline;\"><strong>AT</strong></span> / S<span style=\"text-decoration: underline;\"><strong>A</strong></span>D<strong><span style=\"text-decoration: underline;\">C</span></strong>AT/ <span style=\"text-decoration: underline;\">S<strong>A</strong></span>DCA<span style=\"text-decoration: underline;\"><strong>T</strong></span> / S<span style=\"text-decoration: underline;\"><strong>A</strong></span>DC<span style=\"text-decoration: underline;\"><strong>A</strong></span>T</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. How many triangles are there in the figure given below?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512053.png\" alt=\"rId33\"></p>",
                    question_hi: "<p>11. नीचे दी गई आकृति में कितने त्रिभुज हैं?<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512053.png\" alt=\"rId33\"></p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>8</p>",
                        "<p>14</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>8</p>",
                        "<p>14</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512197.png\" alt=\"rId34\" width=\"191\" height=\"187\"><br>There are 10 triangles = ABC, AED, AFG, AIH, HKJ, KLQ, KMP, KMJ, PMJ, JON.</p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512197.png\" alt=\"rId34\" width=\"191\" height=\"187\"><br>10 त्रिभुज हैं = ABC, AED, AFG, AIH, HKJ, KLQ, KMP, KMJ, PMJ, JON.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Tigers, Snakes, Elephants</p>",
                    question_hi: "<p>12. उस वेन आरेख का चयन कीजिए जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।<br>बाघ, साँप, हाथी</p>",
                    options_en: [
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512291.png\" alt=\"rId35\"></p>",
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512411.png\" alt=\"rId36\"></p>",
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512538.png\" alt=\"rId37\"></p>",
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512631.png\" alt=\"rId38\"></p>"
                    ],
                    options_hi: [
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512291.png\" alt=\"rId35\"></p>",
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512411.png\" alt=\"rId36\"></p>",
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512538.png\" alt=\"rId37\"></p>",
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512631.png\" alt=\"rId38\"></p>"
                    ],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512737.png\" alt=\"rId39\" width=\"403\" height=\"93\"></p>",
                    solution_hi: "<p>12.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512846.png\" alt=\"rId40\" width=\"406\" height=\"97\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group ?<br>(<strong>Note :</strong> The odd letter-cluster is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>13. अंग्रेजी वर्णमाला क्रम पर आधारित, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। किस अक्षर-समूह का संबंध उस समूह से नहीं है ?<br>(<strong>नोट :</strong> असंगत अक्षर-समूह, व्यंजनों/स्वरों की संख्या या इस अक्षर-समूह में उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>RWT</p>",
                        "<p>PUR</p>",
                        "<p>TYU</p>",
                        "<p>NSP</p>"
                    ],
                    options_hi: [
                        "<p>RWT</p>",
                        "<p>PUR</p>",
                        "<p>TYU</p>",
                        "<p>NSP</p>"
                    ],
                    solution_en: "<p>13.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512948.png\" alt=\"rId41\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513088.png\" alt=\"rId42\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513184.png\" alt=\"rId43\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513292.png\" alt=\"rId44\"></p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162512948.png\" alt=\"rId41\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513088.png\" alt=\"rId42\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513184.png\" alt=\"rId43\"><br>लेकिन,<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513292.png\" alt=\"rId44\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster. <br>CRW : TIN :: DOZ : UFQ :: ALW : ?</p>",
                    question_hi: "<p>14. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है। <br>CRW : TIN :: DOZ : UFQ :: ALW : ?</p>",
                    options_en: [
                        "<p>JCN</p>",
                        "<p>RCN</p>",
                        "<p>RCM</p>",
                        "<p>JCM</p>"
                    ],
                    options_hi: [
                        "<p>JCN</p>",
                        "<p>RCN</p>",
                        "<p>RCM</p>",
                        "<p>JCM</p>"
                    ],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513390.png\" alt=\"rId45\" width=\"508\" height=\"134\"></p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513390.png\" alt=\"rId45\" width=\"508\" height=\"134\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. If the year is NOT a leap year, then how is the last day of the year and first day of the same year related?</p>",
                    question_hi: "<p>15. यदि कोई वर्ष लीप वर्ष न हो, तो उस वर्ष का अंतिम दिन और उसी वर्ष का पहला दिन किस प्रकार संबंधित है?</p>",
                    options_en: [
                        "<p>First day is Sunday, last day is Saturday</p>",
                        "<p>First day is Tuesday, last day is Saturday</p>",
                        "<p>First day is Sunday, last day is Friday</p>",
                        "<p>They are the same.</p>"
                    ],
                    options_hi: [
                        "<p>पहला दिन रविवार है, अंतिम दिन शनिवार है</p>",
                        "<p>पहला दिन मंगलवार है, अंतिम दिन शनिवार है</p>",
                        "<p>पहला दिन रविवार है, अंतिम दिन शुक्रवार है</p>",
                        "<p>वे एक ही हैं |</p>"
                    ],
                    solution_en: "<p>15.(d) They are the same</p>",
                    solution_hi: "<p>15.(d) वे एक ही हैं</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. What should come in place of \'?\' in the given series?<br>34 , 58 , 106 , 202 , 394 , ?</p>",
                    question_hi: "<p>16. दी गई श्रृंखला में प्रश्न चिन्ह \'?\' के स्थान पर क्या आना चाहिए?<br>34 , 58 , 106 , 202 , 394 , ?</p>",
                    options_en: [
                        "<p>778</p>",
                        "<p>798</p>",
                        "<p>792</p>",
                        "<p>774</p>"
                    ],
                    options_hi: [
                        "<p>778</p>",
                        "<p>798</p>",
                        "<p>792</p>",
                        "<p>774</p>"
                    ],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513534.png\" alt=\"rId46\" width=\"362\" height=\"70\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513534.png\" alt=\"rId46\" width=\"362\" height=\"70\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option figure that will replace the question mark (?) in the figure given below to&nbsp;complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513651.png\" alt=\"rId47\" width=\"125\" height=\"124\"></p>",
                    question_hi: "<p>17. उस विकल्प आकृति का चयन कीजिए जो नीचे दी गई आकृति में पैटर्न को पूरा करने के लिए प्रश्न-चिह्न (?) के स्थान पर आएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513651.png\" alt=\"rId47\" width=\"125\" height=\"124\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513784.png\" alt=\"rId48\" width=\"103\" height=\"107\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513887.png\" alt=\"rId49\" width=\"102\" height=\"102\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514034.png\" alt=\"rId50\" width=\"102\" height=\"104\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514156.png\" alt=\"rId51\" width=\"107\" height=\"109\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513784.png\" alt=\"rId48\" width=\"103\" height=\"107\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162513887.png\" alt=\"rId49\" width=\"102\" height=\"102\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514034.png\" alt=\"rId50\" width=\"102\" height=\"104\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514156.png\" alt=\"rId51\" width=\"107\" height=\"109\"></p>"
                    ],
                    solution_en: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514034.png\" alt=\"rId50\" width=\"100\" height=\"102\"></p>",
                    solution_hi: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514034.png\" alt=\"rId50\" width=\"100\" height=\"102\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514320.png\" alt=\"rId52\" width=\"353\" height=\"133\"></p>",
                    question_hi: "<p>18. कागज़ की एक वर्गाकार शीट को दर्शाई गई दिशाओं में बिंदुदार रेखा के अनुदिश क्रमशः मोड़ा जाता है और अंतिम बार मोड़ने के बाद फिर इसमें छेद किया जाता है। खोले जाने पर यह कागज़ कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514320.png\" alt=\"rId52\" width=\"353\" height=\"133\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514471.png\" alt=\"rId53\" width=\"101\" height=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514691.png\" alt=\"rId54\" width=\"99\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514910.png\" alt=\"rId55\" width=\"101\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515125.png\" alt=\"rId56\" width=\"102\" height=\"100\"></p>"
                    ],
                    options_hi: [
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514471.png\" alt=\"rId53\" width=\"101\" height=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514691.png\" alt=\"rId54\" width=\"99\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162514910.png\" alt=\"rId55\" width=\"101\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515125.png\" alt=\"rId56\" width=\"102\" height=\"100\"></p>"
                    ],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515125.png\" alt=\"rId56\" width=\"102\" height=\"100\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515125.png\" alt=\"rId56\" width=\"102\" height=\"100\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. If + means &minus; , &minus; means &times; , &times; means &divide; , &divide; means +, what will come in place of the question mark (?) in the given equation? <br>99 &times; 33 &ndash; 70 &divide; 90 + 100 = ?</p>",
                    question_hi: "<p>19. यदि + का अर्थ &minus; है, &minus; का अर्थ &times; है, &times; का अर्थ &divide; है, &divide; का अर्थ + है, तो दिए गए समीकरण में प्रश्न-चिह्न (?) के स्थान पर क्या आएगा?<br>99 &times; 33 &ndash; 70 &divide; 90 + 100 = ?</p>",
                    options_en: [
                        "<p>201</p>",
                        "<p>200</p>",
                        "<p>100</p>",
                        "<p>300</p>"
                    ],
                    options_hi: [
                        "<p>201</p>",
                        "<p>200</p>",
                        "<p>100</p>",
                        "<p>300</p>"
                    ],
                    solution_en: "<p>19.(b) <strong>Given :-</strong> 99 &times; 33 - 70 <math display=\"inline\"><mo>&#247;</mo></math> 90 + 100<br>As per given instruction after interchanging the sign we get,<br>99 <math display=\"inline\"><mo>&#247;</mo></math> 33 &times; 70 + 90 - 100<br>3 &times; 70 - 10<br>210 - 10 = 200</p>",
                    solution_hi: "<p>19.(b) <strong>दिया गया :-</strong> 99 &times; 33 - 70 <math display=\"inline\"><mo>&#247;</mo></math> 90 + 100<br>दिए गए निर्देश के अनुसार चिन्ह को आपस में बदलने पर हमें मिलता है<br>99 <math display=\"inline\"><mo>&#247;</mo></math> 33 &times; 70 + 90 - 100<br>3 &times; 70 - 10<br>210 - 10 = 200</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Six employees Dhiraj, Roshan, Satish, Pawan, Tarun and Niraj are sitting around a circular table, facing the centre (but not necessarily in the same order). Satish is sitting to the immediate right of Pawan. Niraj is sitting second to the left of Satish. Tarun is sitting to the immediate left of Niraj. Only Tarun is sitting between Niraj and Roshan. Who is sitting between Satish and Roshan?</p>",
                    question_hi: "<p>20. छः कर्मचारी धीरज, रोशन, सतीश, पवन, तरूण और नीरज एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। सतीश, पवन के ठीक दाएँ बैठा है। नीरज, सतीश के बाएँ से दूसरे स्थान पर बैठा है। तरूण, नीरज के ठीक बाएँ बैठा है। नीरज और रोशन के बीच में केवल तरूण बैठा है। सतीश और रोशन के बीच में कौन बैठा है?</p>",
                    options_en: [
                        "<p>Pawan</p>",
                        "<p>Niraj</p>",
                        "<p>Dhiraj</p>",
                        "<p>Tarun</p>"
                    ],
                    options_hi: [
                        "<p>पवन</p>",
                        "<p>नीरज</p>",
                        "<p>धीरज</p>",
                        "<p>तरूण</p>"
                    ],
                    solution_en: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515262.png\" alt=\"rId57\" width=\"325\" height=\"162\"><br>Dhiraj is sitting between Roshan and Satish</p>",
                    solution_hi: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515402.png\" alt=\"rId58\" width=\"313\" height=\"164\"><br>धीरज, रोशन और सतीश के बीच बैठा है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, then the resultant of which of the following will be 652 ?</p>",
                    question_hi: "<p>21. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo;, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; और &lsquo;D&rsquo; का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित में से किसका परिणाम 652 होगा ?</p>",
                    options_en: [
                        "<p>109 B 6 A 16 D 2 C 6</p>",
                        "<p>109 A 6 D 16 B 2 C 6</p>",
                        "<p>109 C 6 D 16 A 2 B 6</p>",
                        "<p>109 B 6 D 16 A 2 C 6</p>"
                    ],
                    options_hi: [
                        "<p>109 B 6 A 16 D 2 C 6</p>",
                        "<p>109 A 6 D 16 B 2 C 6</p>",
                        "<p>109 C 6 D 16 A 2 B 6</p>",
                        "<p>109 B 6 D 16 A 2 C 6</p>"
                    ],
                    solution_en: "<p>21.(d) After checking all the options one by one, only option (d) satisfies.<br>109 B 6 D 16 A 2 C 6<br>As per given instructions after interchanging the letter with sign we get<br>109 &times; 6 - 16 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 6<br>654 - 8 + 6 = 652</p>",
                    solution_hi: "<p>21.(d) सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (d) ही संतुष्ट करता है।<br>109 B 6 D 16 A 2 C 6<br>दिए गए निर्देशों के अनुसार अक्षर को चिह्न से बदलने पर हमें प्राप्त होता है<br>109 &times; 6 - 16 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 6<br>654 - 8 + 6 = 652</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Which of the following interchanges of signs would make the given equation correct? <br>3 &minus; 15 + 10 &divide; 2 &times; 7 = 43</p>",
                    question_hi: "<p>22. गणितीय चिह्नों का निम्नलिखित में से कौन-सा परस्पर विनिमय दिए गए समीकरण को सही (संतुलित) कर देगा?<br>3 &minus; 15 + 10 &divide; 2 &times; 7 = 43</p>",
                    options_en: [
                        "<p>&divide; and &times;</p>",
                        "<p>&minus; and &times;</p>",
                        "<p>&minus; and +</p>",
                        "<p>+ and &times;</p>"
                    ],
                    options_hi: [
                        "<p>&divide; और &times;</p>",
                        "<p>&minus; और &times;</p>",
                        "<p>&minus; और +</p>",
                        "<p>+ और &times;</p>"
                    ],
                    solution_en: "<p>22.(b) <strong>Given :-</strong> 3 - 15 + 10 <math display=\"inline\"><mo>&#247;</mo></math> 2 &times; 7 = 43<br>After going through the options, option (b) is satisfied. After interchanging &lsquo;-&rsquo; and &lsquo;&times;&rsquo;, we get<br>3 &times; 15 + 10 <math display=\"inline\"><mo>&#247;</mo></math> 2 - 7<br>45 + 5 - 7<br>45 - 2 = 43<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>22.(b) <strong>दिया गया :-</strong> 3 - 15 + 10 <math display=\"inline\"><mo>&#247;</mo></math> 2 &times; 7 = 43<br>विकल्पों की जांच करने पर विकल्प (b) संतुष्ट करता है । \'-\' और \'&times;\' को आपस में बदलने पर हमें मिलता है<br>3 &times; 15 + 10 <math display=\"inline\"><mo>&#247;</mo></math> 2 - 7<br>45 + 5 - 7<br>45 - 2 = 43<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. A woman said to a man, &ldquo;Your mother&rsquo;s husband&rsquo;s father is my husband&rdquo;. How is the woman related to the man?</p>",
                    question_hi: "<p>23. एक महिला ने एक पुरुष से कहा, \"तुम्हारी माँ के पति के पिता मेरे पति हैं\"। महिला का पुरुष से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Grandson</p>",
                        "<p>Grandmother</p>",
                        "<p>Mother</p>",
                        "<p>Wife</p>"
                    ],
                    options_hi: [
                        "<p>पोता</p>",
                        "<p>दादी</p>",
                        "<p>माता</p>",
                        "<p>पत्नी</p>"
                    ],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515541.png\" alt=\"rId59\" width=\"292\" height=\"231\"><br>Woman is the grandmother of Man.</p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515639.png\" alt=\"rId60\" width=\"319\" height=\"243\"><br>महिला पुरुष की दादी है.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Sally drives a mini school bus. She starts at the school gate and drives 7 km north for her first pick-up. She then turns right and drives 10 km picking up children along the way. She takes another right turn and drives 5 km. She then takes a final right to return to the school. How far would Sally need to drive to reach back to the school gate again?</p>",
                    question_hi: "<p>24. सैली एक मिनी स्कूल बस चलाती है। वह स्कूल गेट से शुरू करती है और अपनी पहली सवारी लेने के लिए 7 km उत्तर की ओर बस चलाती है। फिर वह दाएं मुड़ती है और रास्ते में बच्चों को लेने के लिए 10 km बस चलाती है। वह दुबारा दाएं मुड़ती है और 5 km बस चलाती है। फिर वह स्कूल लौटने के लिए अंतिम बार दाएं मुड़ती है। पुनः स्कूल गेट पर वापस पहुँचने के लिए सैली को कितनी दूर तक बस चलानी पड़ेगी ?</p>",
                    options_en: [
                        "<p>12 km</p>",
                        "<p>10 km</p>",
                        "<p>22 km</p>",
                        "<p>17 km</p>"
                    ],
                    options_hi: [
                        "<p>12 km</p>",
                        "<p>10 km</p>",
                        "<p>22 km</p>",
                        "<p>17 km</p>"
                    ],
                    solution_en: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515752.png\" alt=\"rId61\" width=\"293\" height=\"208\"><br>Required distance traveled towards school gate = 10 + 2 = 12 km.</p>",
                    solution_hi: "<p>24.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162515882.png\" alt=\"rId62\" width=\"343\" height=\"235\"><br>स्कूल गेट की ओर तय की गई आवश्यक दूरी = 10 + 2 = 12 किमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In a certain code language, &lsquo;FISH&rsquo; is coded as &lsquo;4268&rsquo; and &lsquo;RATS&rsquo; is coded as &lsquo;2357&rsquo;.What is the code for &lsquo;S&rsquo; in that language?</p>",
                    question_hi: "<p>25. एक निश्चित कूट भाषा में, \'FISH\' को \'4268\' के रूप में कूटबद्ध किया जाता है और \'RATS\' को \'2357\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'S\' के लिए कूट क्या है?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>7</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>7</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>25.(b)<br>&lsquo;FISH&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;4268&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;RATS&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;2357&rsquo; &hellip;&hellip;. (ii)<br>From (i) and (ii) &lsquo;S&rsquo; and &lsquo;2&rsquo; are common.<br>So, the code of &lsquo;S&rsquo; is &lsquo;2&rsquo;.</p>",
                    solution_hi: "<p>25.(b)</p>\n<p dir=\"ltr\">&lsquo;FISH&rsquo; &lsquo;4268&rsquo; &hellip;&hellip;&hellip; (i)</p>\n<p dir=\"ltr\">&lsquo;RATS&rsquo; &lsquo;2357&rsquo; &hellip;&hellip;. (ii)</p>\n<p dir=\"ltr\">&nbsp;(i) और (ii) से&nbsp; and \'s\' और \'2\' उभयनिष्ठ हैं।</p>\n<p dir=\"ltr\">इसलिए, \'S\' का कोड \'2\' है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The \'pawn\' is associated with which of the following games ?</p>",
                    question_hi: "<p>26. &lsquo;प्यादा\' निम्नलिखित में से किस खेल से संबंधित है ?</p>",
                    options_en: [
                        "<p>Cricket</p>",
                        "<p>Chess</p>",
                        "<p>Tennis</p>",
                        "<p>Badminton</p>"
                    ],
                    options_hi: [
                        "<p>क्रिकेट</p>",
                        "<p>शतरंज</p>",
                        "<p>टेनिस</p>",
                        "<p>बैडमिंटन</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>Chess. Sports Terminology: </strong>Chess - Bishop, King, Queen. Volleyball - Beach dig, Bump, Block, Bump pass, Decoy, Flare. Tennis - Ace, Crosscourt, Backhand, Drop Shot. Archery - Recurve bow, Limb, Fletching, Quiver, Nock, Riser. Badminton - Birdie, Fault, Net Shot, Service Court.</p>",
                    solution_hi: "<p>26.(b) <strong>शतरंज। खेल शब्दावली:</strong> शतरंज - बिशप, राजा, रानी। वॉलीबॉल - बीच डिग, बम्प, ब्लॉक, बम्प पास, डिकॉय, फ्लेयर। टेनिस - ऐस, क्रॉसकोर्ट, बैकहैंड, ड्रॉप शॉट। तीरंदाजी - रिकर्व बो , लिम्ब , फ्लेचिंग, क्विवर, नॉक, रिसर। बैडमिंटन - बर्डी, फॉल्ट, नेट शॉट, सर्विस कोर्ट।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. In which state is the Seshachalam Biosphere Reserve located?</p>",
                    question_hi: "<p>27. शेषचलम बायोस्फीयर रिजर्व किस राज्&zwj;य में स्थित है?</p>",
                    options_en: [
                        "<p>Andhra Pradesh</p>",
                        "<p>Maharashtra</p>",
                        "<p>Gujarat</p>",
                        "<p>Tamil Nadu</p>"
                    ],
                    options_hi: [
                        "<p>आंध्र प्रदेश</p>",
                        "<p>महाराष्ट्र</p>",
                        "<p>गुजरात</p>",
                        "<p>तमिलनाडु</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Andhra Pradesh. </strong>Some Biosphere reserve in India: Cold Desert (Himachal Pradesh), Nanda Devi (Uttrakhand), Khangchendzonga (Sikkim), Manas (Assam), Similipal (Odisha), Pachmarhi (Madhya Pradesh), Sundarban (West Bengal), Kachchh (Gujarat), Gulf of Mannar (Tamil Nadu). There are a total 18 Biosphere reserves in India, out of which 12 is recognised under UNESCO&rsquo;s Man and Biosphere programme.</p>",
                    solution_hi: "<p>27.(a)<strong> आंध्र प्रदेश ।</strong> भारत में कुछ बायोस्फीयर रिजर्व: शीत मरुस्थल (हिमाचल प्रदेश), नंदा देवी (उत्तराखंड), कंचनजंगा (सिक्किम), मानस (असम), सिमिलिपाल (ओडिशा), पचमढ़ी (मध्य प्रदेश), सुंदरबन (पश्चिम बंगाल), कच्छ (गुजरात) , मन्नार की खाड़ी (तमिलनाडु)। भारत में कुल 18 बायोस्फीयर रिजर्व हैं, जिनमें से 12 यूनेस्को के मैन एंड बायोस्फीयर कार्यक्रम के तहत मान्यता प्राप्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who is credited with inventing the reflecting telescope?</p>",
                    question_hi: "<p>28. परावर्तक दूरबीन के आविष्कार का श्रेय किसे दिया जाता है?</p>",
                    options_en: [
                        "<p>Isaac Newton</p>",
                        "<p>Johannes Kepler</p>",
                        "<p>Christiaan Huygens</p>",
                        "<p>Galileo Galilei</p>"
                    ],
                    options_hi: [
                        "<p>आइजैक न्यूटन</p>",
                        "<p>जोहान्स केप्लर</p>",
                        "<p>क्रिश्चियन हाइगेन्स</p>",
                        "<p>गैलीलियो गैलीली</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>Isaac Newton.</strong> In 1668, he devised a reflecting telescope. Instead of a lens, it used a single curved main mirror, together with a smaller flat mirror. Scientists and Discoveries: Johannes Kepler - The three laws of planetary motion; Christiaan Huygens - The Wave theory of light.</p>",
                    solution_hi: "<p>28.(a) <strong>आइजैक न्यूटन।</strong> 1668 में, उन्होंने एक परावर्तक दूरबीन का आविष्कार किया था। एक लेंस के जगह, इसमें एक छोटे से समतल दर्पण के साथ एकल गोलिये मुख्य दर्पण का उपयोग किया गया था। वैज्ञानिक और आविष्कार: योहानेस केप्लर - ग्रहीय गति के तीन नियम; क्रिश्चियन हाइगेंस - प्रकाश का तरंग सिद्धांत।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following was added in the Constitution of India in 1976 ?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किसे 1976 में भारत के संविधान में जोड़ा गया था ?</p>",
                    options_en: [
                        "<p>Fundamental rights</p>",
                        "<p>Right to education</p>",
                        "<p>Fundamental duty</p>",
                        "<p>Directive principle</p>"
                    ],
                    options_hi: [
                        "<p>मौलिक अधिकार</p>",
                        "<p>शिक्षा का अधिकार</p>",
                        "<p>मौलिक कर्तव्य</p>",
                        "<p>निदेशक सिद्धांत</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>Fundamental duty - </strong>It was added to the Constitution in 1976 through the 42nd Constitution Amendment Act on the recommendation of the Swaran Singh Committee. Other provisions added in 1976 : Socialist, Secular and Integrity in the Preamble; Added three new Directive Principles viz., Equal justice and free legal aid (Article 39A), Participation of workers in the management of industries (Article 43A), and Protection of the environment, forests, and wildlife (Article 48A); Provision for the creation of the All-India Judicial Service.</p>",
                    solution_hi: "<p>29.(c) <strong>मौलिक कर्तव्य - </strong>इसे 1976 में स्वर्ण सिंह समिति की सिफारिश पर 42वें संविधान संशोधन अधिनियम के माध्यम से संविधान में शामिल किया गया था। 1976 में जोड़े गए अन्य प्रावधान: प्रस्तावना में समाजवादी, धर्मनिरपेक्ष और अखंडता; तीन नए निदेशक सिद्धांत जोड़े गए, समान न्याय और नि:शुल्क विधिक सहायता (अनुच्छेद 39 A), उद्योगों के प्रबंधन में श्रमिकों की भागीदारी (अनुच्छेद 43 A), और पर्यावरण, वन और वन्यजीवों की सुरक्षा (अनुच्छेद 48 A); अखिल भारतीय न्यायिक सेवा के निर्माण का प्रावधान किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. When was the 1st Asian Kabaddi Championship held?</p>",
                    question_hi: "<p>30. पहली एशियाई कबड्डी चैम्पियनशिप कब आयोजित की गई थी?</p>",
                    options_en: [
                        "<p>1970</p>",
                        "<p>1985</p>",
                        "<p>1980</p>",
                        "<p>1975</p>"
                    ],
                    options_hi: [
                        "<p>1970</p>",
                        "<p>1985</p>",
                        "<p>1980</p>",
                        "<p>1975</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>1980. </strong>The 1st Asian Kabaddi Championship was held in 1980 and included as a demonstration game in the 9th Asian Games in New Delhi in 1982. The 9th Championship took place in the Republic of Korea, where India secured its 8th title. Kabaddi was also included in the South Asian Federation (SAF) Games started in 1984 in Dhaka, Bangladesh.</p>",
                    solution_hi: "<p>30.(c) <strong>1980.</strong> पहली एशियाई कबड्डी चैंपियनशिप 1980 में आयोजित की गई थी और इसे 1982 में नई दिल्ली में आयोजित 9वें एशियाई खेलों में एक प्रदर्शन खेल के रूप में शामिल किया गया था। 9वीं चैंपियनशिप कोरिया गणराज्य में हुई, जहाँ भारत ने अपना 8वाँ खिताब जीता। 1984 में बांग्लादेश के ढाका में शुरू हुए दक्षिण एशियाई महासंघ (SAF) खेलों में कबड्डी को भी शामिल किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who released the book &lsquo;Maha Kavithai&rsquo; by poet Vairamuthu on January 1, 2024?</p>",
                    question_hi: "<p>31. 1 जनवरी, 2024 को कवि वैरामुथु की पुस्तक \'महा कविथाई\' का विमोचन किसने किया?</p>",
                    options_en: [
                        "<p>M.K. Stalin</p>",
                        "<p>Arif Mohammad</p>",
                        "<p>Alok Kumar</p>",
                        "<p>P. Chidambaram</p>"
                    ],
                    options_hi: [
                        "<p>एम.के. स्टालिन</p>",
                        "<p>आरिफ मोहम्मद</p>",
                        "<p>आलोक कुमार</p>",
                        "<p>पी.चिदंबरम</p>"
                    ],
                    solution_en: "<p>31.(a)<strong> M.K. Stalin. </strong>Tamil Nadu Chief Minister M.K. Stalin released the book &lsquo;Maha Kavithai&rsquo;, authored by renowned poet Vairamuthu. The first copy was handed over to senior Congress leader P. Chidambaram.</p>",
                    solution_hi: "<p>31.(a)<strong> एम.के. स्टालिन। </strong>तमिलनाडु के मुख्यमंत्री एम.के. स्टालिन ने प्रसिद्ध कवि वैरामुथु द्वारा लिखित पुस्तक &lsquo;महा कविता&rsquo; का विमोचन किया। इसकी पहली प्रति वरिष्ठ कांग्रेस नेता पी. चिदंबरम को सौंपी गई।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. What is the molecular mass of an ozone molecule?</p>",
                    question_hi: "<p>32. ओजोन अणु का आणविक द्रव्यमान कितना होता है?</p>",
                    options_en: [
                        "<p>32</p>",
                        "<p>48</p>",
                        "<p>16</p>",
                        "<p>18</p>"
                    ],
                    options_hi: [
                        "<p>32</p>",
                        "<p>48</p>",
                        "<p>16</p>",
                        "<p>18</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>48 </strong>(The weight of oxygen = 16 &amp; weight of ozone (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">O</mi><mn>3</mn></msub></math>) = 48). Ozone - It is an inorganic molecule with the chemical formula <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">O</mi><mn>3</mn></msub></math>. It is a pale blue gas with a distinctively pungent smell. The molecular mass of a substance is the sum of the atomic masses of all the atoms in a molecule of the substance.</p>",
                    solution_hi: "<p>32.(b) <strong>48</strong> (ऑक्सीजन का भार = 16 और ओजोन का भार (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">O</mi><mn>3</mn></msub></math>) = 48)। ओजोन - यह रासायनिक सूत्र <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">O</mi><mn>3</mn></msub></math> वाला एक अकार्बनिक अणु है। यह एक विशिष्ट तीखी गंध वाली हल्के नीले रंग की गैस है। किसी पदार्थ का आणविक द्रव्यमान उस पदार्थ के एक अणु में मौजूद सभी परमाणुओं के परमाणु द्रव्यमान का योग होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. In which of the following states of India is the largest salt water lake, the Chilika lake, located?</p>",
                    question_hi: "<p>33. भारत के निम्नलिखित में से किस राज्य में खारे पानी की सबसे बड़ी झील, चिल्का झील स्थित है?</p>",
                    options_en: [
                        "<p>Odisha</p>",
                        "<p>Andhra Pradesh</p>",
                        "<p>Tamil Nadu</p>",
                        "<p>Kerala</p>"
                    ],
                    options_hi: [
                        "<p>ओडिशा</p>",
                        "<p>आंध्र प्रदेश</p>",
                        "<p>तमिलनाडु</p>",
                        "<p>केरल</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>Odisha.</strong> Chilika Lake is a brackish water lake and shallow lagoon with estuarine characteristics, located across the districts of Puri, Khurda, and Ganjam in Odisha. In 1981, it was designated as the first Indian wetland of international importance under the Ramsar Convention. Important lakes across India: Dal Lake (Jammu and Kashmir), Sambhar Lake (Rajasthan), Nainital Lake (Uttarakhand), and Vembanad Lake (Kerala).</p>",
                    solution_hi: "<p>33.(a) <strong>ओडिशा।</strong> चिलिका झील एक खारे पानी की झील और उथली लैगून है जो ज्वारनदमुखी विशेषताओं वाली है, यह झील ओडिशा के पुरी, खुर्दा और गंजाम जिलों में फैली हुई है। 1981 में, इसे रामसर कन्वेंशन के तहत अंतरराष्ट्रीय महत्व की पहली भारतीय आर्द्रभूमि के रूप में नामित किया गया था। भारत में महत्वपूर्ण झीलें: डल झील (जम्मू और कश्मीर), सांभर झील (राजस्थान), नैनीताल झील (उत्तराखंड), और वेम्बनाड झील (केरल)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following is the decreasing order of Padma Awards?</p>",
                    question_hi: "<p>34. निम्न से कौन-सा पद्म पुरस्कारों का घटता क्रम क्या है?</p>",
                    options_en: [
                        "<p>Padma Shri, Padma Vibhushan, Padma Bhushan</p>",
                        "<p>Padma Bhushan, Padma Shri, Padma Vibhushan</p>",
                        "<p>Padma Vibhushan, Padma Bhushan, Padma Shri</p>",
                        "<p>Padma Shri, Padma Bhushan, Padma Vibhushan</p>"
                    ],
                    options_hi: [
                        "<p>पद्म श्री, पद्म विभूषण, पद्म भूषण</p>",
                        "<p>पद्म भूषण, पद्म श्री, पद्म विभूषण</p>",
                        "<p>पद्म विभूषण, पद्म भूषण, पद्म श्री</p>",
                        "<p>पद्म श्री, पद्म भूषण, पद्म विभूषण</p>"
                    ],
                    solution_en: "<p>34.(c) <strong>Padma Vibhushan, Padma Bhushan, and Padma Shri.</strong> The Padma Vibhushan is awarded for exceptional and distinguished service, the Padma Bhushan for distinguished service of a high order, and the Padma Shri for notable service in various fields. These awards recognize contributions in areas such as arts, literature, science, public affairs, and social work.</p>",
                    solution_hi: "<p>34.(c) <strong>पद्म विभूषण, पद्म भूषण और पद्म श्री। </strong>पद्म विभूषण असाधारण और विशिष्ट सेवा के लिए दिया जाता है, पद्म भूषण उच्च कोटि की विशिष्ट सेवा के लिए और पद्म श्री विभिन्न क्षेत्रों में उल्लेखनीय सेवा के लिए दिया जाता है। ये पुरस्कार कला, साहित्य, विज्ञान, सार्वजनिक मामलों और सामाजिक कार्य जैसे क्षेत्रों में योगदान को मान्यता देते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who was the first to use the term \'horsepower\' that refers to the power produced by an engine ?</p>",
                    question_hi: "<p>35. \'हॉर्सपावर\' शब्द इंजन द्वारा उत्पादित शक्ति को दर्शाता है, इस शब्द का सबसे पहले उपयोग किसने किया था ?</p>",
                    options_en: [
                        "<p>Amedeo Avogadro</p>",
                        "<p>John Dalton</p>",
                        "<p>Albert Einstein</p>",
                        "<p>James Watt</p>"
                    ],
                    options_hi: [
                        "<p>एमेडियो अवोगाद्रो</p>",
                        "<p>जॉन डाल्टन</p>",
                        "<p>अल्बर्ट आइंस्टीन</p>",
                        "<p>जेम्स वॉट</p>"
                    ],
                    solution_en: "<p>35.(d) <strong>James Watt</strong> was a Scottish engineer and inventor. Watt defined one horsepower as the power required to lift 33,000 pounds by one foot in one minute, which is equivalent to about 746 watts. Amedeo Avogadro was an Italian chemist who is best known for Avogadro\'s Law. John Dalton was an English chemist and physicist who is best known for his work on atomic theory. Albert Einstein was a physicist who is best known for his theory of relativity and the famous equation E = mc&sup2;.</p>",
                    solution_hi: "<p>35.(d) <strong>जेम्स वाट</strong> एक स्कॉटिश इंजीनियर और आविष्कारक थे। वाट ने एक हॉर्सपावर को एक मिनट में 33,000 पाउंड वजन को एक फुट ऊपर उठाने के लिए आवश्यक शक्ति के रूप में परिभाषित किया, जो लगभग 746 वाट के बराबर है। एमेडियो अवोगाद्रो एक इटालियन रसायनज्ञ थे जो अवोगाद्रो के नियम के लिए जाने जाते हैं। जॉन डाल्टन एक अंग्रेज रसायनज्ञ और भौतिक विज्ञानी थे जो परमाणु सिद्धांत पर अपने काम के लिए जाने जाते हैं। अल्बर्ट आइंस्टीन एक भौतिक विज्ञानी थे जो अपने सापेक्षता के सिद्धांत और प्रसिद्ध समीकरण E = mc&sup2; के लिए जाने जाते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. What is the wavelength range of visible light?</p>",
                    question_hi: "<p>36. दृश्य प्रकाश की तरंग दैर्ध्य परास कितनी होती है?</p>",
                    options_en: [
                        "<p>1 to 2 &mu;m</p>",
                        "<p>0.7 to 0.9 &mu;m</p>",
                        "<p>0.2 to 0.3 &mu;m</p>",
                        "<p>0.4 to 0.7 &mu;m</p>"
                    ],
                    options_hi: [
                        "<p>1 से 2 &mu;m</p>",
                        "<p>0.7 से 0.9 &mu;m</p>",
                        "<p>0.2 से 0.3 &mu;m</p>",
                        "<p>0.4 से 0.7 &mu;m</p>"
                    ],
                    solution_en: "<p>36.(d) <strong>0.4 to 0.7 &mu;m.</strong> The visible spectrum encompasses all the colours perceivable by the human eye, from violet (shortest wavelength) to red (longest wavelength). Electromagnetic waves range: Radio (&gt; 0.1 m), Microwave (0.1m to 1 mm), Infra-red (1mm to 700 nm), Light (700 nm to 400 nm), Ultraviolet (400 nm to 1nm), X-rays (1 nm to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mrow><mo>-</mo><mn>3</mn></mrow></msup></math> nm), Gamma rays (&lt;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mrow><mo>-</mo><mn>3</mn></mrow></msup></math> nm).</p>",
                    solution_hi: "<p>36.(d) <strong>0.4 से 0.7 &mu;m.</strong> दृश्यमान स्पेक्ट्रम में बैंगनी (सबसे छोटी तरंग दैर्ध्य) से लाल (सबसे लंबी तरंग दैर्ध्य) रंग तक मानव नेत्र द्वारा देखे जाने योग्य सभी रंग शामिल हैं। विद्युत चुम्बकीय तरंगों का परास: रेडियो (&gt; 0.1 m), माइक्रोवेव (0.1 m से 1 mm), इन्फ्रा-रेड ( 1mm से 700 nm), प्रकाश (700 nm से 400 nm), पराबैंगनी (400 nm से 1nm), एक्स-रे (1 nm से <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mrow><mo>-</mo><mn>3</mn></mrow></msup></math> nm ), गामा किरणें (&lt;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mrow><mo>-</mo><mn>3</mn></mrow></msup></math> nm)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. What is celebrated on November 19 every year in India?</p>",
                    question_hi: "<p>37. भारत में हर साल 19 नवंबर को क्या मनाया जाता है?</p>",
                    options_en: [
                        "<p>National Unity Day</p>",
                        "<p>National Integration Day</p>",
                        "<p>National Democracy Day</p>",
                        "<p>National Freedom Day</p>"
                    ],
                    options_hi: [
                        "<p>राष्ट्रीय एकता दिवस</p>",
                        "<p>राष्ट्रीय एकीकरण दिवस</p>",
                        "<p>राष्ट्रीय लोकतंत्र दिवस</p>",
                        "<p>राष्ट्रीय स्वतंत्रता दिवस</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>National Integration Day.</strong></p>\n<p>It marks the birth anniversary of Indira Gandhi, the first woman Prime Minister of India.</p>",
                    solution_hi: "<p>37.(b) राष्ट्रीय एकीकरण दिवस<strong>। </strong>यह भारत की पहली महिला प्रधान मंत्री इंदिरा गांधी की जयंती का प्रतीक है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following is the autobiography of Bhishma Sahni?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन-सी भीष्म साहनी की आत्मकथा है?</p>",
                    options_en: [
                        "<p>Aaj ke Ateet</p>",
                        "<p>Aatma Parichaya</p>",
                        "<p>Aarhdakatha</p>",
                        "<p>Pankhhin</p>"
                    ],
                    options_hi: [
                        "<p>आज के अतीत</p>",
                        "<p>आत्म परिचय</p>",
                        "<p>अर्हदकथा</p>",
                        "<p>पंखहीन</p>"
                    ],
                    solution_en: "<p>38.(a) <strong>Aaj ke Ateet. </strong>Bhisham Sahni was an Indian writer, playwright in Hindi. Awards - Padma Bhushan (1998) and Sahitya Akademi Fellowship in 2002. Books by Bhisham Sahni: &lsquo;&rsquo;Middle India: Selected Short Stories&rsquo;&rsquo;, &lsquo;&rsquo;Madhavi&rsquo;&rsquo;, &lsquo;&rsquo;Balraj: My Brother&rsquo;&rsquo;, &lsquo;&rsquo;Shobha Yatra&rsquo;&rsquo;, &lsquo;&rsquo;Wangchoo&rsquo;&rsquo;, &lsquo;&rsquo;Patriyan&rsquo;&rsquo;, &lsquo;&rsquo;Dayan&rsquo;&rsquo;, &lsquo;&rsquo;Anthology of Hindi Short Stories&rsquo;&rsquo;.</p>",
                    solution_hi: "<p>38.(a) <strong>आज के अतीत। </strong>भीष्म साहनी एक भारतीय लेखक, हिंदी नाटककार थे। पुरस्कार - पद्म भूषण (1998) और 2002 में साहित्य अकादमी फ़ेलोशिप। भीष्म साहनी की पुस्तकें: \'\'मिडिल इंडिया: सेलेक्ट शॉर्ट स्टोरीज&rdquo;, \'\'माधवी\'\', \'\'बलराज: माई ब्रदर\'\', \'\'शोभा यात्रा\'\' \', \'\'वांगचू\'\', \'\'पैट्रियन\'\', &ldquo;डायन&rdquo;, \'\'एंथोलॉजी ऑफ हिंदी शॉर्ट स्टोरीज\'\'।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. The \'Make in India\' initiative primarily focuses on which sector?</p>",
                    question_hi: "<p>39. \'मेक इन इंडिया (Make in India)\' पहल मुख्य रूप से किस क्षेत्र पर केंद्रित है?</p>",
                    options_en: [
                        "<p>Manufacturing</p>",
                        "<p>Service</p>",
                        "<p>Agriculture</p>",
                        "<p>Import</p>"
                    ],
                    options_hi: [
                        "<p>विनिर्माण</p>",
                        "<p>सेवा</p>",
                        "<p>कृषि</p>",
                        "<p>आयात</p>"
                    ],
                    solution_en: "<p>39.(a) <strong>Manufacturing. </strong>Make in India Initiative: Launched on September 25, 2014, by Prime Minister Narendra Modi, initiative aims to transform India into a global manufacturing hub by boosting the manufacturing sector&rsquo;s contribution to GDP, creating jobs, attracting foreign investments, and improving the ease of doing business.</p>",
                    solution_hi: "<p>39.(a) <strong>विनिर्माण। </strong>मेक इन इंडिया पहल: 25 सितंबर, 2014 को प्रधान मंत्री नरेंद्र मोदी द्वारा शुरू की गई थी। इस पहल का उद्देश्य सकल घरेलू उत्पाद में विनिर्माण क्षेत्र के योगदान को बढ़ावा देकर, रोजगार सृजन, विदेशी निवेश आकर्षित करने और व्यापार करने में आसानी में सुधार करके भारत को वैश्विक विनिर्माण केंद्र में बदलना है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which of the following is the first Indian woman Grandmaster in Chess?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन शतरंज में पहली भारतीय महिला ग्रैंडमास्टर है?</p>",
                    options_en: [
                        "<p>Harika Dronavalli</p>",
                        "<p>S Vijayalakshmi</p>",
                        "<p>Tania Sachdev</p>",
                        "<p>R Vaishali</p>"
                    ],
                    options_hi: [
                        "<p>हरिका द्रोणावल्ली</p>",
                        "<p>एस. विजयालक्ष्मी</p>",
                        "<p>तानिया सचदेव</p>",
                        "<p>आर. वैशाली</p>"
                    ],
                    solution_en: "<p>40.(b) <strong>S Vijayalakshmi.</strong> She is an Indian chess player holding the FIDE titles of International Master (IM) and Woman Grandmaster (WGM). Notable Indian female chess players include Koneru Humpy, Vaishali Rameshbabu, Harika Dronavalli, Eesha Karavade, Divya Deshmukh, and Mary Ann Gomes.</p>",
                    solution_hi: "<p>40.(b) <strong>एस. विजयालक्ष्मी।</strong> वह एक भारतीय शतरंज खिलाड़ी हैं, जिन्होंने FIDE के अंतर्राष्ट्रीय मास्टर (IM) और महिला ग्रैंडमास्टर (WGM) की खिताब जीती हैं। अन्य उल्लेखनीय भारतीय महिला शतरंज खिलाड़ियों में कोनेरू हंपी, वैशाली रमेशबाबू, हरिका द्रोणावल्ली, ईशा करावडे, दिव्या देशमुख और मैरी एन गोम्स शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. On which of the following components of chloroplast is chlorophyll arranged?</p>",
                    question_hi: "<p>41. क्लोरोप्लास्ट के निम्नलिखित में से किस घटक पर क्लोरोफिल व्यवस्थित (arrange) होता है?</p>",
                    options_en: [
                        "<p>Thylakoids</p>",
                        "<p>Matrix</p>",
                        "<p>Stroma</p>",
                        "<p>Cell Membrane</p>"
                    ],
                    options_hi: [
                        "<p>थाइलेकोइड्स</p>",
                        "<p>मैट्रिक्स</p>",
                        "<p>स्ट्रोमा</p>",
                        "<p>कोशिका झिल्ली</p>"
                    ],
                    solution_en: "<p>41.(a) <strong>Thylakoids.</strong> Chloroplasts contain the thylakoid membrane which is the site of photosynthesis. The thylakoid membrane contains light-absorbing pigments called the chlorophyll. The thylakoids are arranged in stacks termed grana. The space limited by the inner membrane of the chloroplast is called the stroma. The chloroplasts contain chlorophyll and carotenoid pigments which are responsible for trapping light energy essential for photosynthesis.</p>",
                    solution_hi: "<p>41.(a) <strong>थाइलेकोइड्स।</strong> क्लोरोप्लास्ट में थाइलेकोइड झिल्ली होती है जो प्रकाश संश्लेषण का स्थल है। थायलाकोइड झिल्ली में प्रकाश-अवशोषित करने वाले वर्णक होते हैं जिन्हें क्लोरोफिल कहा जाता है। थाइलेकोइड्स को ग्रैना में व्यवस्थित किया जाता है। क्लोरोप्लास्ट की आंतरिक झिल्ली द्वारा सीमित स्थान को स्ट्रोमा कहा जाता है। क्लोरोप्लास्ट में क्लोरोफिल और कैरोटीनॉयड रंगद्रव्य होते हैं जो प्रकाश संश्लेषण के लिए आवश्यक प्रकाश ऊर्जा को बनाए रखने के लिए उत्तरदायी होते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The Non-Cooperation Movement was called off after which of the following incidents?</p>",
                    question_hi: "<p>42. निम्नलिखित में से किस घटना के बाद असहयोग आंदोलन बंद कर दिया गया था?</p>",
                    options_en: [
                        "<p>Outbreak of the First World War</p>",
                        "<p>Kakori train robbery</p>",
                        "<p>Chauri-Chaura incident</p>",
                        "<p>Komagata Maru incident</p>"
                    ],
                    options_hi: [
                        "<p>प्रथम विश्व युद्ध का प्रकोप</p>",
                        "<p>काकोरी ट्रेन डकैती</p>",
                        "<p>चौरी-चौरा कांड</p>",
                        "<p>कामागाटा मारू घटना</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>Chauri-Chaura incident</strong> occurred at Chauri Chaura in the Gorakhpur district of the United Province, (modern Uttar Pradesh) in British India on 4 February 1922, when a large group of protesters, participating in the Non-cooperation movement, clashed with police, who opened fire. The Komagata Maru incident took place in 1914 when a ship named Komagata Maru arrived in Vancouver, Canada, carrying Indian immigrants who were denied entry due to restrictive immigration laws. The Kakori train robbery incident occurred on August 9, 1925, when a group of revolutionaries looted a train near Kakori, Uttar Pradesh. First World War (1914-1918).</p>",
                    solution_hi: "<p>42.(c) <strong>चौरी-चौरा कांड, </strong>4 फरवरी 1922 में ब्रिटिश भारत में संयुक्त प्रांत (आधुनिक उत्तर प्रदेश) के गोरखपुर जिले के चौरी चौरा में हुआ था, जब असहयोग आंदोलन में भाग लेने वाले प्रदर्शनकारियों के एक बड़े समूह की पुलिस से झड़प हो गयी&nbsp; थी जिसके बाद पुलिस ने गोलियां चला दी थी। कोमागाटा मारू की घटना 1914 में हुई थी जब कोमागाटा मारू नामक एक जहाज वैंकूवर, कनाडा पहुंचा, जिसमें भारतीय अप्रवासी थे, जिन्हें प्रतिबंधात्मक आव्रजन कानूनों के कारण प्रवेश से वंचित कर दिया गया था। काकोरी ट्रेन डकैती की घटना 9 अगस्त, 1925 को हुई थी, जब क्रांतिकारियों के एक समूह ने उत्तर प्रदेश के काकोरी के पास एक ट्रेन को लूट लिया था। प्रथम विश्व युद्ध (1914-1918)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. A primitive form of cultivation called Bewar is practised in which state?</p>",
                    question_hi: "<p>43. बेवर नामक आदिम खेती किस राज्य में की जाती है?</p>",
                    options_en: [
                        "<p>Madhya Pradesh</p>",
                        "<p>Andhra Pradesh</p>",
                        "<p>Odisha</p>",
                        "<p>Mizoram</p>"
                    ],
                    options_hi: [
                        "<p>मध्य प्रदेश</p>",
                        "<p>आंध्र प्रदेश</p>",
                        "<p>ओड़िशा</p>",
                        "<p>मिजोरम</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Madhya Pradesh.</strong> Bewar is also called &lsquo;Dahiya&rsquo; in Madhya Pradesh. Other name of Primitive cultivation: &lsquo;Podu&rsquo; or &lsquo;Penda&rsquo; in Andhra Pradesh, &lsquo;Pama Dabi&rsquo; or &lsquo;Koman&rsquo; or Bringa&rsquo; in Odisha, &lsquo;Kumari&rsquo; in Western Ghats, &lsquo;Valre&rsquo; or &lsquo;Waltre&rsquo; in South-eastern Rajasthan, &lsquo;Khil&rsquo; in the Himalayan belt, &lsquo;Kuruwa&rsquo; in Jharkhand, and &lsquo;Jhumming&rsquo; in the North-eastern region.</p>",
                    solution_hi: "<p>43.(a) <strong>मध्य प्रदेश।</strong> मध्य प्रदेश में बेवर को &lsquo;दहिया&rsquo; भी कहा जाता है। आदिम खेती के अन्य नाम: आंध्र प्रदेश में &lsquo;पोडु&rsquo; या &lsquo;पेंडा&rsquo;, ओडिशा में &lsquo;पामा डाबी&rsquo; या &lsquo;कोमन&rsquo; या &lsquo;ब्रिंगा&rsquo;, पश्चिमी घाट में &lsquo;कुमारी&rsquo;, दक्षिण-पूर्वी राजस्थान में &lsquo;वलरे&rsquo; या &lsquo;वालट्रे&rsquo;, हिमालयी क्षेत्र में &lsquo;खिल&rsquo;, झारखंड में &lsquo;कुरुवा&rsquo; और उत्तर-पूर्वी क्षेत्र में &lsquo;झूमिंग&rsquo;।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following articles of the Indian Constitution deals with the Indian Parliament?</p>",
                    question_hi: "<p>44. भारतीय संविधान के निम्नलिखित में से कौन-से अनुच्छेद का संबंध भारतीय संसद से है?</p>",
                    options_en: [
                        "<p>40-70</p>",
                        "<p>12-40</p>",
                        "<p>79-122</p>",
                        "<p>1-12</p>"
                    ],
                    options_hi: [
                        "<p>40-70</p>",
                        "<p>12-40</p>",
                        "<p>79-122</p>",
                        "<p>1-12</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Articles 79 to 122</strong> in Part V of the Constitution deal with the organisation, composition, duration, officers, procedures, privileges and powers of the Parliament. According to Article 79 of the Constitution of India, the Parliament consists of the President of India and the two Houses of Parliament known as Council of States (Rajya Sabha) and House of the People (Lok Sabha).</p>",
                    solution_hi: "<p>44.(c) संविधान के भाग V के <strong>अनुच्छेद 79 से 122,</strong> संसद के संगठन, संरचना, अवधि, अधिकारी, प्रक्रिया, विशेषाधिकार और शक्तियों से संबंधित हैं। भारतीय संविधान के अनुच्छेद 79 के अनुसार, संसद में भारत के राष्ट्रपति तथा संसद के दो सदन (राज्य सभा और लोक सभा) शामिल होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Buddhadev Dasgupta is famous for playing which of the following musical instruments?</p>",
                    question_hi: "<p>45. बुद्धदेव दासगुप्ता निम्नलिखित में से किस संगीत वाद्ययंत्र को बजाने के लिए प्रसिद्ध हैं?</p>",
                    options_en: [
                        "<p>Sarod</p>",
                        "<p>Sarangi</p>",
                        "<p>Bansuri</p>",
                        "<p>Mridangam</p>"
                    ],
                    options_hi: [
                        "<p>सरोद</p>",
                        "<p>सारंगी</p>",
                        "<p>बांसुरी</p>",
                        "<p>मृदंगम</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Sarod.</strong> It is a stringed instrument, used in Hindustani music on the Indian subcontinent. Buddhadev Das Gupta was an Indian classical musician. His Awards: Padma Bhushan (2012) and Sangeet Natak Akademi Award (1993). Notable exponents of the Sarod : Ustad Amjad Ali Khan, Bahadur Khan, and Allaudin Khan.</p>",
                    solution_hi: "<p>45.(a) <strong>सरोद</strong> एक तार वाला वाद्ययंत्र है, जिसका उपयोग भारतीय उपमहाद्वीप में हिंदुस्तानी संगीत में किया जाता है। बुद्धदेव दास गुप्ता एक भारतीय शास्त्रीय संगीतकार थे। उनके पुरस्कार: पद्म भूषण (2012) और संगीत नाटक अकादमी पुरस्कार (1993)। सरोद के उल्लेखनीय प्रतिपादक: उस्ताद अमजद अली खान, बहादुर खान और अलाउद्दीन खान आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following goods needs further transformation in the economic process?</p>",
                    question_hi: "<p>46. निम्नलिखित में से किस वस्तु को आर्थिक प्रक्रिया में और परिवर्तन की आवश्यकता है?</p>",
                    options_en: [
                        "<p>Consumer durable goods</p>",
                        "<p>Finished goods</p>",
                        "<p>Intermediate goods</p>",
                        "<p>Capital goods</p>"
                    ],
                    options_hi: [
                        "<p>उपभोक्ता टिकाऊ माल</p>",
                        "<p>अंतिम माल</p>",
                        "<p>मध्यवर्ती माल</p>",
                        "<p>पूंजीगत माल</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>Intermediate goods :</strong> The raw materials that a firm buys from another firm which are completely used up in the process of production. Consumer durable goods can be used repeatedly and have a long life. An item that is meant for final use and will not pass through any more stages of production or transformations is called a final good. The capital goods add to, or maintain, the capital stock of an economy and thus make production of other commodities possible.</p>",
                    solution_hi: "<p>46.(c) <strong>मध्यवर्ती माल : </strong>जिस कच्चे माल को एक फर्म दूसरी फर्म से खरीदती है और जो उत्पादन की प्रक्रिया में पूरी तरह से उपयोग हो जाता है। उपभोक्ता टिकाऊ वस्तुएं बार-बार उपयोग की जा सकती हैं और उनका जीवनकाल लंबा होता है। वह वस्तु जिसे अंतिम उपयोग के लिए बनाया गया है और जो उत्पादन या परिवर्तन के किसी और चरण से नहीं गुजरेगी, उसे अंतिम वस्तु कहा जाता है। पूंजीगत माल अर्थव्यवस्था के पूंजी स्टॉक को बढ़ाती हैं या बनाए रखती हैं तथा इस प्रकार अन्य वस्तुओं का उत्पादन संभव बनाती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following animals was the emblem of the Chola dynasty?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन-सा जानवर चोल राजवंश का प्रतीक था?</p>",
                    options_en: [
                        "<p>Wolf</p>",
                        "<p>Horse</p>",
                        "<p>Elephant</p>",
                        "<p>Tiger</p>"
                    ],
                    options_hi: [
                        "<p>भेड़िया</p>",
                        "<p>घोड़ा</p>",
                        "<p>हाथी</p>",
                        "<p>बाघ</p>"
                    ],
                    solution_en: "<p>47.(d) <strong>Tiger.</strong> Three dynasties ruled during the Sangam Age. Cheras: Modern-Day City (Kerala), Ancient Capital (Vanji), Important Ports (Musiri, Tondi), Emblem (Bow and Arrow). Cholas: Modern-Day City (Tamil Nadu), Ancient Capital (Uraiyur, Puhar), Important Ports (Kaveripattinam). Pandyas: Modern-Day City (Tamil Nadu), Ancient Capital (Madurai), Important Ports {Muziris (Muchiri), Korkai, Kaveri}, Emblem (Carp).</p>",
                    solution_hi: "<p>47.(d) <strong>बाघ।</strong> संगम युग के दौरान तीन राजवंशों ने शासन किया। चेर: आधुनिक शहर (केरल), प्राचीन राजधानी (वज्जि), महत्त्वपूर्ण बंदरगाह (मुसिरी, तोंडी), प्रतीक (धनुष और बाण)। चोल: आधुनिक शहर (तमिलनाडु), प्राचीन राजधानी (उरैयूर, पुहार), महत्त्वपूर्ण बंदरगाह (कावेरीपत्तनम)। पांड्य: आधुनिक शहर (तमिलनाडु), प्राचीन राजधानी (मदुरै), महत्त्वपूर्ण बंदरगाह {मुज़िरिस (मुचिरी), कोरकई, कावेरी}, प्रतीक (मछली)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following is NOT generally used in milk adulteration?</p>",
                    question_hi: "<p>48. दूध में मिलावट के लिए निम्न में से किसका प्रयोग नहीं किया जाता है?</p>",
                    options_en: [
                        "<p>Urea</p>",
                        "<p>Starch</p>",
                        "<p>Saw dust</p>",
                        "<p>Detergent</p>"
                    ],
                    options_hi: [
                        "<p>यूरिया</p>",
                        "<p>स्टार्च</p>",
                        "<p>लकड़ी का बुरादा</p>",
                        "<p>डिटर्जेंट</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>Saw dust (Wood dust) -</strong> It is a by-product or waste product of woodworking operations such as sawing, sanding, milling and routing. Milk Adulteration - The process of intentionally degrading milk quality either by adding some inferior substances or by removing some valuable ingredient. Components of Milk Adulteration : Urea, Strach, Detergent, Sugar, Water, Milemine, etc.</p>",
                    solution_hi: "<p>48.(c) <strong>लकड़ी का बुरादा - </strong>यह लकड़ी काटने, रेतने, मिलिंग और रूटिंग जैसे लकड़ी के कामकाज के उप-उत्पाद या अपशिष्ट उत्पाद है। दूध में मिलावट - कुछ हानिकारक पदार्थ मिलाकर या कुछ मूल्यवान घटक निकालकर दूध की गुणवत्ता को जानबूझकर खराब करने की प्रक्रिया। दूध में मिलावट के घटक: यूरिया, स्टार्च, डिटर्जेंट, चीनी, जल , माइलामिन, आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. The GROW initiative, recently unveiled by NITI Aayog, focuses on:</p>",
                    question_hi: "<p>49. NITI आयोग द्वारा हाल ही में शुरू की गई \"GROW पहल\" का उद्देश्य क्या है?</p>",
                    options_en: [
                        "<p>Promoting renewable energy</p>",
                        "<p>Agroforestry development</p>",
                        "<p>Enhancing rural sanitation</p>",
                        "<p>Developing irrigation infrastructure</p>"
                    ],
                    options_hi: [
                        "<p>नवीकरणीय ऊर्जा को बढ़ावा देना</p>",
                        "<p>कृषि वानिकी विकास</p>",
                        "<p>ग्रामीण स्वच्छता बढ़ाना</p>",
                        "<p>सिंचाई अवसंरचना का विकास</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>Agroforestry development.</strong><br>The GROW initiative aims to revitalize India\'s wastelands through agroforestry, enhancing sustainable land use and improving rural livelihoods.</p>",
                    solution_hi: "<p>49.(b) <strong>कृषि वानिकी विकास। </strong>GROW पहल का उद्देश्य भारत की बंजर भूमि को कृषि वानिकी के माध्यम से पुनर्जीवित करना है, जिससे स्थायी भूमि उपयोग को बढ़ावा मिलता है और ग्रामीण आजीविका में सुधार होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the following National Parks is located in Madhya Pradesh?</p>",
                    question_hi: "<p>50. निम्नलिखित में से कौन-सा राष्ट्रीय उद्यान मध्य प्रदेश में स्थित है?</p>",
                    options_en: [
                        "<p>Nanda Devi</p>",
                        "<p>Periyar</p>",
                        "<p>Bandhavgarh</p>",
                        "<p>Silent Valley</p>"
                    ],
                    options_hi: [
                        "<p>नंदा देवी (Nanda Devi)</p>",
                        "<p>पेरियार (Periyar)</p>",
                        "<p>बांधवगढ़ (Bandhavgarh)</p>",
                        "<p>मौन घाटी (Silent Valley)</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>Bandhavgarh</strong> was declared a wildlife sanctuary in 1968, and it was upgraded to a national park and a tiger reserve in 1993. National Parks in Madhya Pradesh : Kanha National Park, Pench National Park, Satpura National Park, Panna National Park, Kuno National Park, Van Vihar National Park.</p>",
                    solution_hi: "<p>50.(c) <strong>बांधवगढ़</strong> को 1968 में वन्यजीव अभ्यारण्य घोषित किया गया था, और इसे 1993 में एक राष्ट्रीय उद्यान और बाघ अभ्यारण्य कर दिया गया। मध्य प्रदेश में राष्ट्रीय उद्यान : कान्हा राष्ट्रीय उद्यान, पेंच राष्ट्रीय उद्यान, सतपुड़ा राष्ट्रीय उद्यान, पन्ना राष्ट्रीय उद्यान, कूनो राष्ट्रीय उद्यान, वन विहार राष्ट्रीय उद्यान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A man spends 35% of his monthly income on food and four-thirteenths of the remaining income on transport. He incurs some other expenses, but also saves Rs.6,300 per month, the latter being equal to 20% of the balance remaining just after spending on food and transport. What is his monthly income (in Rs.)?</p>",
                    question_hi: "<p>51. एक आदमी अपनी मासिक आय का 35% भोजन पर और शेष आय का 4/13 भाग परिवहन पर खर्च करता है। वह कुछ अन्य खर्च भी करता है, लेकिन प्रति माह Rs.6,300 बचाता है, जो कि भोजन और परिवहन पर खर्च करने के बाद शेष राशि के 20% के बराबर है। उसकी मासिक आय (Rs. में) कितनी है?</p>",
                    options_en: [
                        "<p>63000</p>",
                        "<p>72,000</p>",
                        "<p>67500</p>",
                        "<p>70,000</p>"
                    ],
                    options_hi: [
                        "<p>63000</p>",
                        "<p>72,000</p>",
                        "<p>67500</p>",
                        "<p>70,000</p>"
                    ],
                    solution_en: "<p>51.(d) <br>Let the total income = 100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math><br>E<math display=\"inline\"><mi>x</mi></math>penditure of food = 100x &times; 35% = 35x<br>Remaining amount = 100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> - 35x = 65x<br>E<math display=\"inline\"><mi>x</mi></math>penditure of transport = 65x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>13</mn></mfrac></math> = 20x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>100</mn><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>35</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>20</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>)</mo></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 6300<br>100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> - 55x = 6300 &times; 5<br>45<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> = 6300 &times; 5, x = 700<br>So, his monthly income = 100 &times; 700 = 70000</p>",
                    solution_hi: "<p>51.(d) <br>माना कुल आय = 100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math><br>भोजन का व्यय = 100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> &times; 35% = 35x<br>शेष राशि = 100x&nbsp;- 35x = 65x<br>परिवहन का व्यय = 65x&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>13</mn></mfrac></math> = 20x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>100</mn><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>35</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>20</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>)</mo></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 6300<br>100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> - 55x = 6300 &times; 5<br>45<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> = 6300 &times; 5, x = 700<br>तो, उसकी मासिक आय = 100 &times; 700 = 70000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The value of <math display=\"inline\"><mfrac><mrow><mn>61</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>75</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>)</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mo>(</mo><mn>58</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mn>37</mn><mo>)</mo></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>52. <math display=\"inline\"><mfrac><mrow><mn>61</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>75</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>)</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mo>(</mo><mn>58</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mn>37</mn><mo>)</mo></mrow></mfrac></math> का मान क्या है ?</p>",
                    options_en: [
                        "<p>19</p>",
                        "<p>11</p>",
                        "<p>3</p>",
                        "<p>57</p>"
                    ],
                    options_hi: [
                        "<p>19</p>",
                        "<p>11</p>",
                        "<p>3</p>",
                        "<p>57</p>"
                    ],
                    solution_en: "<p>52.(c)<br>= <math display=\"inline\"><mfrac><mrow><mn>61</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>75</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>)</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mo>(</mo><mn>58</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mn>37</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>61</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>75</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mo>)</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>21</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>61</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>57</mn><mn>19</mn></mfrac></math> = 3</p>",
                    solution_hi: "<p>52.(c)<br>= <math display=\"inline\"><mfrac><mrow><mn>61</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>75</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#247;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>)</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mo>(</mo><mn>58</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mn>37</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>61</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>75</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mo>)</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>21</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>61</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>57</mn><mn>19</mn></mfrac></math> = 3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Fill pipe P is 21 times faster than fill pipe Q . If Q can fill a cistern in 110 minutes, find the time it takes to fill the cistern when both fill pipes are opened together.</p>",
                    question_hi: "<p>53. भरण पाइप P, भरण पाइप Q से 21 गुना तेज है। यदि Q एक टंकी को 110 मिनट में भर सकता है, तो दोनों भरण पाइपों को एक साथ खोलने पर टंकी को भरने में कितना समय लगेगा?</p>",
                    options_en: [
                        "<p>5 minutes</p>",
                        "<p>4 minutes</p>",
                        "<p>3 minutes</p>",
                        "<p>6 minutes</p>"
                    ],
                    options_hi: [
                        "<p>5 मिनट</p>",
                        "<p>4 मिनट</p>",
                        "<p>3 मिनट</p>",
                        "<p>6 मिनट</p>"
                    ],
                    solution_en: "<p>53.(a) <br>Ratio of pipe <math display=\"inline\"><mo>&#8594;</mo></math> P&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; Q<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math> 1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;21<br>Efficiency&nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math> 21&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 1<br>Given,<br>Total time to fill the cistern by Q = 110 minute</p>\n<p>so, total capacity of cistern = 110 unit</p>\n<p>Total efficiency of both pipe = 22<br>Time taken by both pipe to fill the cistern = <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> = 5 minutes</p>",
                    solution_hi: "<p>53.(a) <br>पाइप का अनुपात <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; P&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Q<br>समय&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 21<br>क्षमता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;21&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;1<br>दिया गया,<br>Q द्वारा टंकी को भरने के लिए कुल समय = 110 मिनट</p>\n<p>दोनों पाइपों की कुल दक्षता = 22<br>दोनों पाइपों द्वारा टंकी भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> = 5 मिनट</p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If Ram purchases a fan, he gets 8% discount, however, if he purchases three fans he gets 6% on the first, 10% on the second and 12% on the third. If the price paid by the Ram for three fans is ₹6,800, then what will be the marked price of each fan?</p>",
                    question_hi: "<p>54. यदि राम एक पंखा खरीदता है, तो उसे 8% की छूट मिलती है, हालाँकि, यदि वह तीन पंखे खरीदता है, तो उसे पहले पर 6%, दूसरे पर 10% और तीसरे पर 12% की छूट मिलती है। यदि राम द्वारा तीन पंखों के लिए भुगतान की गई राशि ₹6,800 है, तो प्रत्येक पंखे का अंकित मूल्य क्या होगा ?</p>",
                    options_en: [
                        "<p>₹2,400</p>",
                        "<p>₹2,500</p>",
                        "<p>₹2,450</p>",
                        "<p>₹2,550</p>"
                    ],
                    options_hi: [
                        "<p>₹2,400</p>",
                        "<p>₹2,500</p>",
                        "<p>₹2,450</p>",
                        "<p>₹2,550</p>"
                    ],
                    solution_en: "<p>54.(b)<br>Average discount = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>3</mn></mfrac></math>% or <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#8594;</mo><mi>Discount</mi></mrow><mrow><mn>75</mn><mo>&#8594;</mo><mi>MP</mi></mrow></mfrac></math><br>ATQ, 75 - 7 = 68 unit --------------- ₹6800<br>Then, 75 unit --------------- <math display=\"inline\"><mfrac><mrow><mn>6800</mn></mrow><mrow><mn>68</mn></mrow></mfrac></math>&times;75 = ₹7500<br>So, MP of each fan = <math display=\"inline\"><mfrac><mrow><mn>7500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = ₹2500</p>",
                    solution_hi: "<p>54.(b)<br>औसत छूट= <math display=\"inline\"><mfrac><mrow><mn>6</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>3</mn></mfrac></math>% या <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#8594;</mo><mi>&#2331;&#2370;&#2335;</mi></mrow><mrow><mn>75</mn><mo>&#8594;</mo><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math><br>प्रश्च के अनुसार, 75 - 7 = 68 इकाई --------------- ₹6800<br>तो , 75 इकाई --------------- <math display=\"inline\"><mfrac><mrow><mn>6800</mn></mrow><mrow><mn>68</mn></mrow></mfrac></math>&times;75 = ₹7500<br>इसलिए, प्रत्येक पंखो का अंकित मूल्य = <math display=\"inline\"><mfrac><mrow><mn>7500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = ₹2500</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The marks of 6 students are given. All the marks are out of 100.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516009.png\" alt=\"rId63\" width=\"560\" height=\"175\"> <br>Find the percentage scored by C.</p>",
                    question_hi: "<p>55. 6 छात्रों के अंक दिए गए हैं। सभी अंक 100 में से हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516126.png\" alt=\"rId64\" width=\"580\" height=\"206\"> <br>C द्वारा प्राप्त किया गया प्रतिशत ज्ञात करें।</p>",
                    options_en: [
                        "<p>75%</p>",
                        "<p>70%</p>",
                        "<p>68%</p>",
                        "<p>65%</p>"
                    ],
                    options_hi: [
                        "<p>75%</p>",
                        "<p>70%</p>",
                        "<p>68%</p>",
                        "<p>65%</p>"
                    ],
                    solution_en: "<p>55.(b) Let percentage scored by C = 70%<br>Net deviation = (+10 +10 +0 -10 +0 -10 )% = 0%<br>&there4;Percentage scored by C = 70 +0 = 70%</p>",
                    solution_hi: "<p>55.(b) माना C द्वारा प्राप्त प्रतिशत = 70%<br>शुद्ध विचलन = (+10 +10 +0 -10 +0 -10 )% = 0%<br>&there4;C द्वारा प्राप्त प्रतिशत = 70 + 0 = 70%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A dishonest shopkeeper claims to sell salt at a rate of ₹25/kg. The cost price of the salt is ₹25/kg. Not satisfied with this, he tries to make profit by removing 200 gm from each kg. What is the shopkeeper&rsquo;s gain percentage?</p>",
                    question_hi: "<p>56. एक बेईमान दुकानदार ₹25/ kg की दर से नमक बेचने का दावा करता है। नमक का क्रय मूल्य ₹ 25/ kg है। इससे संतुष्ट नहीं होने पर वह प्रत्येक kg से 200 gm निकालकर मुनाफा कमाने की कोशिश करता है। दुकानदार का लाभ प्रतिशत कितना है?</p>",
                    options_en: [
                        "<p>15%</p>",
                        "<p>25%</p>",
                        "<p>30%</p>",
                        "<p>20%</p>"
                    ],
                    options_hi: [
                        "<p>15%</p>",
                        "<p>25%</p>",
                        "<p>30%</p>",
                        "<p>20%</p>"
                    ],
                    solution_en: "<p>56.(b)<br>After removing 200 gm salt per kg<br>Shopkeeper give 800 gm for 1 kg<br>Hence, his gain % = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>800</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>56.(b)<br>प्रति कि.ग्रा. से 200 ग्राम नमक निकालकर<br>दुकानदार 1 किलो का 800 ग्राम देता है<br>अत: उसका लाभ % = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>800</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Seema can do a work in 120 days, Anjali can do the same work in 144 days and Soniya can do the same work in 180 days. If they do that work together and they are paid Rs. 3300, then what is the share of Anjali?</p>",
                    question_hi: "<p>57. सीमा एक काम को 120 दिनों में पूरा कर सकती है, अंजलि उसी काम को 144 दिनों में पूरा कर सकती है और सोनिया उसी काम को 180 दिनों में पूरा कर सकती है। यदि वे तीनों उस काम को एक साथ मिलकर करते हैं और उन्हें 3300 रुपए का भुगतान किया जाता है, तो भुगतान में से अंजलि का हिस्सा कितना है?</p>",
                    options_en: [
                        "<p>Rs. 1100</p>",
                        "<p>Rs. 2000</p>",
                        "<p>Rs. 2500</p>",
                        "<p>Rs. 1700</p>"
                    ],
                    options_hi: [
                        "<p>1100 रुपए</p>",
                        "<p>2000 रुपए</p>",
                        "<p>2500 रुपए</p>",
                        "<p>1700 रुपए</p>"
                    ],
                    solution_en: "<p>57.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516256.png\" alt=\"rId65\" width=\"328\" height=\"207\"><br>As we know that , Wages <math display=\"inline\"><mo>&#8733;</mo></math> efficiency <br>Share of Anjali = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math>&times;3300 = ₹1100</p>",
                    solution_hi: "<p>57.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516358.png\" alt=\"rId66\" width=\"329\" height=\"220\"><br>जैसा कि हम जानते हैं, मजदूरी &prop; दक्षता<br>अंजलि का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math>&times; 3300 = ₹1100</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. In the diagram, if <img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAAWCAYAAACVIF9YAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAALaSURBVFhH7ZetbyJBGIdfFlcgmKZJESUYRBMEAd+SYBAQBAYDwVIcAjQYEJgG/gEMFQgCAkNC0VDbBAkkYOrA7+1vbqbdXdiPy3GXlPRJJjsz3Zn3+13qkBXogpH482L5MfC7c/EGnmwyDoeDz86HVS87t0wh76eLfncu38Bms8mn/558Pk9qeaPRiB4fH+lwOPCd8yM9PT3xqTVQCM0ATzVi/9TQv2uXt7c3crlcmrvUzjCTiXM4DyS3280mVuDiVqvFV1qSySTlcjm6u7uj7XbLOhgG9kqlEu12O/6mPRDlaDRKLy8v7J79fk8PDw/k9/tJ6Gskczgc0vX1Nfl8PvaeZFd4p9OhUCjELnx/f+e7WuDh29tbviLKZDL08fHBFLALIlOtVpmiMALAqEQiQff392ytRi8ThoXDYfJ4PGwt3dzcsIkZCHev16NCocB3tMBJr6+vRwrAEWpvWiGyBNGKxWJ89zeVSoUNgZHMSCRCg8HgM9KS0+lkEzOen5+pXq9TMBikQCDAd78QKRKPx/nOVyTa7bbGw2ZMp1OazWZULpc/FTRCLxNBSKfTRw3L8jMBRVerlcaj+hSdTCa02WxY3YhC7/f7TAGRZnbAvSgB3GOFXiaeXq/3yDGmBiIN0CSsPArFkFZoBjAKTzjlTz4BeG88HrMMEfVjhl5mo9FgNa/H1MBut0vr9ZpSqRTzEgQjhaC8UFzUApqAcAKeWM/nc1oul2zPLupOacQpmajPU9liaCByularsW4GD4mB1qwGtYBO+bcNxggYg0zAU2AkE58X/Q+XkwYiOkhLhNyqhlALMERdNxCC6BeLRdsNRkQdtSs+0tAjm82yuTptT8nEGXzK1I0Ov5wQFQ2Kd2Sl0PEfhnx1dSUvFgv+F1lWosf2MTBXovu5Vg/9OQHOKLXCVzI7r9SRrNQR39HKEEN/Rv93MaA39Bewu/j8v2DHwHOhpLAcDoflX2NUzGz98VIyAAAAAElFTkSuQmCC\">, AG = GD = DH = HB, then which of the following is correct?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516467.png\" alt=\"rId67\" width=\"221\" height=\"187\"> <br>I) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>ACG &cong; &Delta;&Beta;&Epsilon;&Eta; <br>II) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE &cong; &Delta;DEB &cong; &Delta;DCA <br>III) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>HFB &cong; &Delta;AFG</p>",
                    question_hi: "<p>58. दिए गए आरेख में, यदि <img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAAWCAYAAACVIF9YAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAALaSURBVFhH7ZetbyJBGIdfFlcgmKZJESUYRBMEAd+SYBAQBAYDwVIcAjQYEJgG/gEMFQgCAkNC0VDbBAkkYOrA7+1vbqbdXdiPy3GXlPRJJjsz3Zn3+13qkBXogpH482L5MfC7c/EGnmwyDoeDz86HVS87t0wh76eLfncu38Bms8mn/558Pk9qeaPRiB4fH+lwOPCd8yM9PT3xqTVQCM0ATzVi/9TQv2uXt7c3crlcmrvUzjCTiXM4DyS3280mVuDiVqvFV1qSySTlcjm6u7uj7XbLOhgG9kqlEu12O/6mPRDlaDRKLy8v7J79fk8PDw/k9/tJ6Gskczgc0vX1Nfl8PvaeZFd4p9OhUCjELnx/f+e7WuDh29tbviLKZDL08fHBFLALIlOtVpmiMALAqEQiQff392ytRi8ThoXDYfJ4PGwt3dzcsIkZCHev16NCocB3tMBJr6+vRwrAEWpvWiGyBNGKxWJ89zeVSoUNgZHMSCRCg8HgM9KS0+lkEzOen5+pXq9TMBikQCDAd78QKRKPx/nOVyTa7bbGw2ZMp1OazWZULpc/FTRCLxNBSKfTRw3L8jMBRVerlcaj+hSdTCa02WxY3YhC7/f7TAGRZnbAvSgB3GOFXiaeXq/3yDGmBiIN0CSsPArFkFZoBjAKTzjlTz4BeG88HrMMEfVjhl5mo9FgNa/H1MBut0vr9ZpSqRTzEgQjhaC8UFzUApqAcAKeWM/nc1oul2zPLupOacQpmajPU9liaCByularsW4GD4mB1qwGtYBO+bcNxggYg0zAU2AkE58X/Q+XkwYiOkhLhNyqhlALMERdNxCC6BeLRdsNRkQdtSs+0tAjm82yuTptT8nEGXzK1I0Ov5wQFQ2Kd2Sl0PEfhnx1dSUvFgv+F1lWosf2MTBXovu5Vg/9OQHOKLXCVzI7r9SRrNQR39HKEEN/Rv93MaA39Bewu/j8v2DHwHOhpLAcDoflX2NUzGz98VIyAAAAAElFTkSuQmCC\">, AG = GD = DH = HB है, तो निम्न में से कौन सा/से सही है/हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516467.png\" alt=\"rId67\" width=\"221\" height=\"187\"><br>I) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>ACG &cong; &Delta;&Beta;&Epsilon;&Eta; <br>II) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE &cong; &Delta;DEB &cong; &Delta;DCA <br>III) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>HFB &cong; &Delta;AFG</p>",
                    options_en: [
                        "<p>I, II and III</p>",
                        "<p>I and III</p>",
                        "<p>Only I</p>",
                        "<p>I and II</p>"
                    ],
                    options_hi: [
                        "<p>&nbsp;I, II और III</p>",
                        "<p>I और III</p>",
                        "<p>केवल I</p>",
                        "<p>I और II</p>"
                    ],
                    solution_en: "<p>58.(d)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516467.png\" alt=\"rId67\" width=\"217\" height=\"184\"></p>\n<p dir=\"ltr\">I) For, &Delta;ACG &cong; &Delta;&Beta;&Epsilon;&Eta;&nbsp;</p>\n<p dir=\"ltr\">In the &Delta;AGC and &Delta;EHB</p>\n<p dir=\"ltr\">&ang;AGC = &ang;EHB = 90&deg;</p>\n<p dir=\"ltr\">Side (AG) = side(HB) &nbsp; &nbsp; [given]</p>\n<p dir=\"ltr\">And, CG = EH&nbsp; &nbsp; [lengths between two parallel lines]</p>\n<p dir=\"ltr\">So, by SAS theorem,</p>\n<p dir=\"ltr\">&Delta;ACG &cong; &Delta;&Beta;&Epsilon;&Eta; (true)</p>\n<p dir=\"ltr\">II) Now, for &nbsp;&Delta;CDE &cong; &Delta;DEB &cong; &Delta;DCA&nbsp;</p>\n<p dir=\"ltr\">In the &Delta;CDE and &Delta;DEB , CE ॥ AB (given)</p>\n<p dir=\"ltr\">DE = DE &nbsp; [common]</p>\n<p dir=\"ltr\">&ang;CED = &ang;BDE [alternate interior angle]</p>\n<p dir=\"ltr\">CE = DB</p>\n<p dir=\"ltr\">So, &Delta;CDE &cong; &Delta;DEB by SAS theorem,</p>\n<p dir=\"ltr\">Similarly,&nbsp;</p>\n<p dir=\"ltr\">In &Delta;CDE and &Delta;DCA , CE ॥ AB (given)</p>\n<p dir=\"ltr\">CD = CD &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; [common]</p>\n<p dir=\"ltr\">&ang;ECD = &ang;ADC &nbsp; [alternate interior angle]</p>\n<p dir=\"ltr\">CE = AD</p>\n<p dir=\"ltr\">So, &Delta;CDE &cong; &Delta;DCA by SAS theorem,</p>\n<p dir=\"ltr\">Hence, &Delta;CDE &cong; &Delta;DEB &cong; &Delta;DCA [true]</p>\n<p dir=\"ltr\">III) For, &Delta;HFB &cong; &Delta;AFG [false]</p>\n<p dir=\"ltr\">Only AG = HB [given]</p>\n<p dir=\"ltr\">∵ No other side or angles are equal&nbsp;</p>\n<p dir=\"ltr\">Therefore, option (d) is the correct answer.&nbsp;</p>",
                    solution_hi: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516467.png\" alt=\"rId67\" width=\"217\" height=\"184\"></p>\n<p dir=\"ltr\">I) &Delta;ACG &cong; &Delta;&Beta;&Epsilon;&Eta; के लिए,</p>\n<p dir=\"ltr\">&Delta;AGC और &Delta;EHB में&nbsp;</p>\n<p dir=\"ltr\">&ang;AGC = &ang;EHB = 90&deg;</p>\n<p dir=\"ltr\">भुजा (AG) = भुजा (HB) &nbsp; &nbsp; [दिया गया]</p>\n<p dir=\"ltr\">और , CG = EH &nbsp; [दो समांतर रेखाओं के बीच की दूरी]</p>\n<p dir=\"ltr\">तो , SAS प्रमेय द्वारा&nbsp;</p>\n<p dir=\"ltr\">&Delta;ACG &cong; &Delta;&Beta;&Epsilon;&Eta; (सत्य)</p>\n<p dir=\"ltr\">II) अब, &Delta;CDE &cong; &Delta;DEB &cong; &Delta;DCA के लिए&nbsp;</p>\n<p dir=\"ltr\">&Delta;CDE और &Delta;DEB मे&nbsp; , CE ॥ AB (दिया गया)</p>\n<p dir=\"ltr\">DE = DE&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; [उभयनिष्ठ ]</p>\n<p dir=\"ltr\">&ang;CED = &ang;BDE&nbsp; &nbsp; &nbsp; [एकान्तर अंतः कोण]</p>\n<p dir=\"ltr\">CE = DB</p>\n<p dir=\"ltr\">तो, SAS प्रमेय द्वारा &Delta;CDE &cong; &Delta;DEB,</p>\n<p dir=\"ltr\">इसी प्रकार,&nbsp;</p>\n<p dir=\"ltr\">&Delta;CDE और &Delta;DCA में , CE ॥ AB (दिया गया)</p>\n<p dir=\"ltr\">CD = CD &nbsp; [उभयनिष्ठ]</p>\n<p dir=\"ltr\">&ang;ECD = &ang;ADC &nbsp; &nbsp; &nbsp; &nbsp; [एकान्तर अंतः कोण]</p>\n<p dir=\"ltr\">CE = AD</p>\n<p dir=\"ltr\">तो , &Delta;CDE &cong; &Delta;DCA ,SAS प्रमेय द्वारा&nbsp;</p>\n<p dir=\"ltr\">अतः , &Delta;CDE &cong; &Delta;DEB &cong; &Delta;DCA [सत्य]</p>\n<p dir=\"ltr\">III) &Delta;HFB &cong; &Delta;AFG के लिए [असत्य]</p>\n<p dir=\"ltr\">केवल AG = HB &nbsp; &nbsp; [दिया गया ]</p>\n<p dir=\"ltr\">∵कोई अन्य भुजा या कोण समान नहीं हैं&nbsp;</p>\n<p dir=\"ltr\">इसलिए, विकल्प (d) सही उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The lines x + y = 10 and -3x + y = 2 have a unique solution. What is the distance (in units) between the unique solution and the point of intersection of the line x + y = 10 and the x-axis?</p>",
                    question_hi: "<p>59. रेखाओं x + y = 10 और -3x + y = 2 का एक अद्वितीय हल है। रेखा x + y = 10 के अद्वितीय हल और प्रतिच्छेदन बिंदु और x-अक्ष के बीच की दूरी (इकाई में) कितनी है?</p>",
                    options_en: [
                        "<p>10<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p>8<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p>10</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>10<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p>8<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p>10</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>59.(b)<br><strong>First step : -</strong><br>x + y = 10 &hellip;.(i)<br>- 3x + y =2 &hellip;&hellip;.(ii)<br>On subtract eq (ii) from (i) we get<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></math> = 10 - 2<br>4x&nbsp;= 8, x = 2 <br>Put the value of <math display=\"inline\"><mi>x</mi></math> in eq (i) we get,<br>y = 10 - 2 = 8<br>So the unique solution is (2, 8)<br><strong>2nd step : -</strong><br>To find the intersection point with x-axis put y = 0 in the equation x + y = 10<br>X = 10<br>thus, the point of intersection with the x-axis is (10,0)<br>Calculate the distance between the unique solution (2,8) and the point (10,0)<br>Distance = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">x</mi><mn>2</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">x</mi><mn>1</mn></msub><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">y</mi><mn>2</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">y</mi><mn>1</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>-</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>64</mn><mo>+</mo><mn>64</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>128</mn></msqrt></math><br>= 8<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                    solution_hi: "<p>59.(b)<br><strong>पहला चरण :-</strong><br>x + y = 10 &hellip;.(i)<br>- 3x + y =2 &hellip;&hellip;.(ii)<br>समीकरण (ii) को समीकरण (i) मे से घटाने पर हमे प्राप्त होता है<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></math> = 10 - 2<br>4<math display=\"inline\"><mi>x</mi></math> = 8, x = 2 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> का मान समीकरण (i) मे रखने पर हमे प्राप्त होता है<br>y = 10 - 2 = 8<br>तो अद्वितीय समाधान (2, 8) है । <br><strong>दूसरा चरण :-</strong><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> - अक्ष के साथ प्रतिच्छेदन बिंदु ज्ञात करने के लिए समीकरण x + y = 10 में y = 0 रखने पर <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> = 10<br>इस प्रकार, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> -अक्ष के साथ प्रतिच्छेदन बिंदु (10, 0) है<br>अद्वितीय समाधान (2,8) और बिंदु (10, 0) के बीच की दूरी की गणना करने पर <br>दूरी<strong id=\"docs-internal-guid-7729059b-7fff-7f2d-b324-e885dee196a7\"> </strong>&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">x</mi><mn>2</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">x</mi><mn>1</mn></msub><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">y</mi><mn>2</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">y</mi><mn>1</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>-</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>64</mn><mo>+</mo><mn>64</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>128</mn></msqrt></math><br>= 8<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If a + b + c = 13 and ab + bc + ca = 22, then the value of a&sup3; + b&sup3;+ c&sup3; - 3abc is ______.</p>",
                    question_hi: "<p>60. यदि a + b + c = 13 और ab + bc + ca = 22 है, तो a&sup3; + b&sup3; + c&sup3; - 3abc का मान ______ है।</p>",
                    options_en: [
                        "<p>1911</p>",
                        "<p>1225</p>",
                        "<p>1625</p>",
                        "<p>1339</p>"
                    ],
                    options_hi: [
                        "<p>1911</p>",
                        "<p>1225</p>",
                        "<p>1625</p>",
                        "<p>1339</p>"
                    ],
                    solution_en: "<p>60.(d) <br>a&sup3; + b&sup3; + c&sup3; - 3abc = (a + b + c) [(a + b + c)&sup2; - 3(ab + bc + ca)]<br>= 13 <math display=\"inline\"><mo>&#215;</mo></math> [(13)&sup2; - 3(22)]<br>= 13 <math display=\"inline\"><mo>&#215;</mo></math> [169 - 66]<br>= 13 <math display=\"inline\"><mo>&#215;</mo></math> 103 = 1339</p>",
                    solution_hi: "<p>60.(d) <br>a&sup3; + b&sup3; + c&sup3; - 3abc = (a + b + c) [(a + b + c)&sup2; - 3(ab + bc + ca)]<br>= 13 <math display=\"inline\"><mo>&#215;</mo></math> [(13)&sup2; - 3(22)]<br>= 13 <math display=\"inline\"><mo>&#215;</mo></math> [169 - 66]<br>= 13 <math display=\"inline\"><mo>&#215;</mo></math> 103 = 1339</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Study the given pie-chart and answer the question that follows.<br>The pie-chart represents the total number of valid votes obtained by four students who contested for school leadership. The total number of valid votes polled was 720.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516584.png\" alt=\"rId68\" width=\"321\" height=\"282\"> <br>What is the minimum number of votes obtained by any candidate?</p>",
                    question_hi: "<p>61. दिए गए पाई चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। यह पाई-चार्ट में स्कूल लीडरशिप के लिए चुनाव लड़ने वाले चार वि&zwnj;द्यार्थियों द्वारा प्राप्त वैध मतों की कुल संख्या का निरूपण करता है। डाले गए वैध मतों की कुल संख्या 720 थी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516666.png\" alt=\"rId69\" width=\"321\" height=\"285\"> <br>किसी भी उम्मीदवार को प्राप्त मतों की न्यूनतम संख्या क्या है?</p>",
                    options_en: [
                        "<p>120</p>",
                        "<p>200</p>",
                        "<p>160</p>",
                        "<p>240</p>"
                    ],
                    options_hi: [
                        "<p>120</p>",
                        "<p>200</p>",
                        "<p>160</p>",
                        "<p>240</p>"
                    ],
                    solution_en: "<p>61.(a)<br>Total number of votes (360&deg;) = 720<br>Minimum number of votes obtained by Yasin (60&deg;) = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 60&deg; = 120</p>",
                    solution_hi: "<p>61.(a)<br>कुल मतों की संख्या (360&deg;) = 720<br>यासीन को प्राप्त वोटों की न्यूनतम संख्या (60&deg;) = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 60&deg; = 120</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The fourth proportional to 4,a and 16a is 81. What is the value of a?</p>",
                    question_hi: "<p>62. 4, a और 16a का चतुर्थानुपाती 81 है। a का मान क्या है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>11</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>11</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>62.(b)<br>Fourth proportional to 4,a,16a = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>16</mn><mi mathvariant=\"normal\">a</mi><mo>&#215;</mo><mi mathvariant=\"normal\">a</mi></mrow><mn>4</mn></mfrac></math> = 4a&sup2;<br>81 = 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>4</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>62.(b)<br>4,a,16a का चतुर्थानुपाती = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>16</mn><mi mathvariant=\"normal\">a</mi><mo>&#215;</mo><mi mathvariant=\"normal\">a</mi></mrow><mn>4</mn></mfrac></math> = 4a&sup2;<br>81 = 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>4</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In a division sum, the divisor is 13 times the quotient and 6 times the remainder. If the remainder is 39, then the dividend is:</p>",
                    question_hi: "<p>63. एक भाग प्रश्न में भाजक , भागफल का 13 गुना और शेषफल का 6 गुना है। यदि शेषफल 39 है, तो भाज्य ज्ञात करें।</p>",
                    options_en: [
                        "<p>4,800</p>",
                        "<p>4,576</p>",
                        "<p>4,251</p>",
                        "<p>4,240</p>"
                    ],
                    options_hi: [
                        "<p>4,800</p>",
                        "<p>4,576</p>",
                        "<p>4,251</p>",
                        "<p>4,240</p>"
                    ],
                    solution_en: "<p>63.(c)<br>Let <br>Divisor = n , quotient = q, remainder = r <br>(Dividend)N = n.q + r<br>Given:<br>n = 13.q<br>n = 6.r<br>r = 39<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 39 &times; 6 = 234<br><math display=\"inline\"><mo>&#8658;</mo></math> q = 18<br><math display=\"inline\"><mo>&#8658;</mo></math> N = n &times; q + r<br><math display=\"inline\"><mo>&#8658;</mo></math> N = 234 &times; 18 + 39 <br><math display=\"inline\"><mo>&#8658;</mo></math> N = 4212 + 39 = 4251</p>",
                    solution_hi: "<p>63.(c)<br>माना<br>भाजक = n, भागफल = q, शेषफल = r <br>(भाज्य) N = n.q + r<br>दिया गया:<br>n= 13.q<br>n= 6.r<br>r= 39<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 39&times; 6 = 234<br><math display=\"inline\"><mo>&#8658;</mo></math> q = 18<br><math display=\"inline\"><mo>&#8658;</mo></math> N = n &times; q + r<br><math display=\"inline\"><mo>&#8658;</mo></math> N = 234 &times; 18 + 39 <br><math display=\"inline\"><mo>&#8658;</mo></math> N = 4212 + 39 = 4251</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cos</mi><mo>&#178;</mo><mi mathvariant=\"normal\">A</mi><mo>)</mo><mo>-</mo><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>1</mn><mo>)</mo><mi>cosec</mi><mo>&#178;</mo><mi mathvariant=\"normal\">A</mi></math>.</p>",
                    question_hi: "<p>64. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cos</mi><mo>&#178;</mo><mi mathvariant=\"normal\">A</mi><mo>)</mo><mo>-</mo><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>1</mn><mo>)</mo><mi>cosec</mi><mo>&#178;</mo><mi mathvariant=\"normal\">A</mi></math> का मान ज्ञात कीजिए |</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>-3</p>",
                        "<p>0</p>",
                        "<p>-1</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>-3</p>",
                        "<p>0</p>",
                        "<p>-1</p>"
                    ],
                    solution_en: "<p>64.(d) <strong>Given,</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cos</mi><mo>&#178;</mo><mi mathvariant=\"normal\">A</mi><mo>)</mo><mo>-</mo><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>1</mn><mo>)</mo><mi>cosec</mi><mo>&#178;</mo><mi mathvariant=\"normal\">A</mi></math><br>Put <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi mathvariant=\"normal\">A</mi></math> = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> (1 + 0 + 0 ) - (1 - 0 + 1 )1 = 1 - 2 = -1</p>",
                    solution_hi: "<p>64.(d) <strong>दिया गया है:</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cos</mi><mo>&#178;</mo><mi mathvariant=\"normal\">A</mi><mo>)</mo><mo>-</mo><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>1</mn><mo>)</mo><mi>cosec</mi><mo>&#178;</mo><mi mathvariant=\"normal\">A</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi mathvariant=\"normal\">A</mi></math> = 90&deg; रखने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> (1 + 0 + 0 ) - (1 - 0 + 1 )1 = 1 - 2 = -1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Rakesh rented out an amount to Samarth for 6 years at simple interest rate. At the end of the <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mi>th</mi></msup></math> year, Samarth paid <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>of the amount to Rakesh to clear out the amount. The rate of simple interest per annum was:</p>",
                    question_hi: "<p>65. राकेश ने समर्थ को 6 वर्ष के लिए साधारण ब्याज दर पर एक राशि ब्याज पर दी। छठे वर्ष के अंत में, समर्थ ने राशि चुकाने के लिए राकेश को राशि के <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> भाग का भुगतान किया। वार्षिक साधारण ब्याज की दर कितनी थी?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mn>2</mn><mfrac><mrow><mn>7</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>%</p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>%</p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mn>2</mn><mfrac><mrow><mn>7</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>%</p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>%</p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>65.(d) Let Principal = 7 unit <br>At the end of the <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mi>th</mi></msup></math> year, Samarth paid <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>of the amount to Rakesh to clear out the amount<br>Amount at the end of 6 year will be 8 unit <br>&there4; Simple Interest = 8 - 7 = 1 unit <br><math display=\"inline\"><mo>&#8658;</mo></math> 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">R</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>42</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>21</mn></mfrac></math>%</p>",
                    solution_hi: "<p>65.(d) माना , मूलधन = 7 इकाई <br>छठे वर्ष के अंत में, समर्थ ने राशि चुकाने के लिए राकेश को राशि में से <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> का भुगतान किया<br>6 वर्ष के अंत में राशि = 8 इकाई <br>&there4; साधारण ब्याज = 8 - 7 = 1 इकाई <br><math display=\"inline\"><mo>&#8658;</mo></math> 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">R</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>42</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>21</mn></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Sita takes a loan of Rs.35,000 at an interest rate of 10% compound interest, compounded annually. She agrees to pay two equal instalments in 2 years, one instalment at the end of each year. Find the value of each instalment.(Round off to the nearest integer)</p>",
                    question_hi: "<p>66. सीता Rs.35,000 का ऋण, वार्षिक रूप से चक्रवृद्धि होने वाली 10% वार्षिक चक्रवृद्धि ब्याज दर पर लेती है। वह 2 वर्षों में दो समान किश्तों में अर्थात् प्रत्येक वर्ष में अंत में एक किश्त का भुगतान करने के लिए सहमत है। प्रत्येक किश्त की राशि ज्ञात कीजिए। (उत्तर को निकटतम पूर्णांक तक पूर्णांकित करें)</p>",
                    options_en: [
                        "<p>₹20,167</p>",
                        "<p>₹40,167</p>",
                        "<p>₹10,167</p>",
                        "<p>₹30,167</p>"
                    ],
                    options_hi: [
                        "<p>₹20,167</p>",
                        "<p>₹40,167</p>",
                        "<p>₹10,167</p>",
                        "<p>₹30,167</p>"
                    ],
                    solution_en: "<p>66.(a) Rate = 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Principal : Installment<br>1st yr&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math> 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 11) <strong>&times; 11</strong><br>2nd yr <math display=\"inline\"><mo>&#8594;</mo></math> 10&sup2;&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 11&sup2;<br>------------------------------------<br>Total&nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 210&nbsp; &nbsp;:&nbsp; &nbsp; 242<br>Value of each installment = 121 units<br>According to the question,<br>210 units = Rs. 35,000<br>installment (121 units) <br>= <math display=\"inline\"><mfrac><mrow><mn>35000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>121</mn></mrow><mrow><mn>210</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60500</mn><mn>3</mn></mfrac></math> <br>= 20,166.66 <math display=\"inline\"><mo>&#8776;</mo></math> ₹ 20,167</p>",
                    solution_hi: "<p>66.(a) दर = 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; मूलधन : किस्त<br>प्रथम वर्ष&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 10&nbsp; &nbsp; :&nbsp; &nbsp; 11) &times; 11<br>द्वितीय वर्ष <math display=\"inline\"><mo>&#8594;</mo></math> 10&sup2;&nbsp; &nbsp;:&nbsp; &nbsp; 11&sup2;<br>--------------------------------------<br>कुल&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 210 :&nbsp; &nbsp; 242<br>प्रत्येक किस्त का मूल्य = 121 इकाई <br>प्रश्न के अनुसार,<br>210 इकाई = Rs. 35,000<br>किस्त (121 इकाई ) <br>= <math display=\"inline\"><mfrac><mrow><mn>35000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>121</mn></mrow><mrow><mn>210</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60500</mn><mn>3</mn></mfrac></math> <br>20,166.66 <math display=\"inline\"><mo>&#8776;</mo></math> ₹ 20,167</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A mixture of 120 litres contains water and milk in the ratio 5 : 7 respectively. How much water must be added to the mixture to make the ratio of water and milk 1 : 1 respectively ?</p>",
                    question_hi: "<p>67. 120 लीटर के एक मिश्रण में पानी और दूध का अनुपात क्रमशः 5 : 7 है। मिश्रण में पानी और दूध का अनुपात क्रमशः 1 : 1 करने के लिए मिश्रण में कितना पानी मिलाना होगा ?</p>",
                    options_en: [
                        "<p>10 litres</p>",
                        "<p>25 litres</p>",
                        "<p>15 litres</p>",
                        "<p>20 litres</p>"
                    ],
                    options_hi: [
                        "<p>10 लीटर</p>",
                        "<p>25 लीटर</p>",
                        "<p>15 लीटर</p>",
                        "<p>20 लीटर</p>"
                    ],
                    solution_en: "<p>67.(d)<br>Balancing the given ratio, we have ;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Milk&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Water&nbsp; &nbsp; &nbsp;Total<br>Old <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;7&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 12 <br>New <math display=\"inline\"><mo>&#8594;</mo></math> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>1</mn><mrow><mo>&#215;</mo><mn>5</mn></mrow></msub></math>&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>1</mn><mrow><mo>&#215;</mo><mn>5</mn></mrow></msub></math>&nbsp; &nbsp; &nbsp; &nbsp;10<br>Quantity of water to be added = 7-5 = 2 unit<br><math display=\"inline\"><mo>&#8658;</mo></math> 12 unit = 120 litres<br>Then, 2 unit = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>&times;2 = 20 litres</p>",
                    solution_hi: "<p>67.(d)<br>दिए गए अनुपात को संतुलित करने पर, हमारे पास है;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;दूध&nbsp; &nbsp; &nbsp; &nbsp; पानी&nbsp; &nbsp; &nbsp; &nbsp;कुल<br>पुराना<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 7&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 12 <br>नया&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>1</mn><mrow><mo>&#215;</mo><mn>5</mn></mrow></msub></math> :&nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>1</mn><mrow><mo>&#215;</mo><mn>5</mn></mrow></msub></math>&nbsp; &nbsp; &nbsp;10<br>मिलाये जाने वाले पानी की मात्रा = 7-5 = 2 इकाई<br><math display=\"inline\"><mo>&#8658;</mo></math> 12 इकाई = 120 लीटर<br>तब , 2 इकाई = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>&times;2 = 20 लीटर</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A woman working in a company was supplied a rectangular solid (cuboid shape) of wax with dimensions 70 cm , 44 cm and 20 cm to prepare cylindrical candles, each 14 cm in diameter and 20 cm of height. Find the number of candles.</p>",
                    question_hi: "<p>68. एक कंपनी में काम करने वाली एक महिला को प्रत्येक 14 cm व्यास और 20 cm ऊंचाई वाली बेलनाकार मोमबत्तियां तैयार करने के लिए 70 cm, 44 cm और 20 cm की विमाओं वाला मोम का एक आयताकार ठोस (घनाभ आकृति) दिया गया। निर्मित मोमबत्तियों की संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>15</p>",
                        "<p>20</p>",
                        "<p>10</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>15</p>",
                        "<p>20</p>",
                        "<p>10</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>68.(b)<br>Number of candles = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>volume</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>cuboidal</mi><mi mathvariant=\"normal\">&#160;</mi><mi>solid</mi></mrow><mrow><mi>volume</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>cylindrical</mi><mi mathvariant=\"normal\">&#160;</mi><mi>candle</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>70</mn><mo>&#215;</mo><mn>44</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>20</mn></mrow></mfrac></math> = 20</p>",
                    solution_hi: "<p>68.(b)<br>मोमबत्तियों की संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2328;&#2344;&#2366;&#2325;&#2366;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2336;&#2379;&#2360;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow><mrow><mi>&#2348;&#2375;&#2354;&#2344;&#2366;&#2325;&#2366;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2379;&#2350;&#2348;&#2340;&#2381;&#2340;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>70</mn><mo>&#215;</mo><mn>44</mn><mo>&#215;</mo><mn>20</mn></mrow><mstyle displaystyle=\"true\"><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>20</mn></mstyle></mfrac></math> = 20</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Which digits should come in the place of * and #, respectively, if the 7-digit number 62684*# is divisible by both 8 and 5 ?</p>",
                    question_hi: "<p>69. यदि 62684*#, 7 अंकों की एक ऐसी संख्या है जो 8 और 5 दोनों से विभाज्य है, तो क्रमशः * और # के स्थान पर कौन से अंक आने चाहिए?</p>",
                    options_en: [
                        "<p>2 and 0</p>",
                        "<p>4 and 0</p>",
                        "<p>5 and 0</p>",
                        "<p>0 and 5</p>"
                    ],
                    options_hi: [
                        "<p>2 और 0</p>",
                        "<p>4 और 0</p>",
                        "<p>5 और 0</p>",
                        "<p>0 और 5</p>"
                    ],
                    solution_en: "<p>69.(b)<br>Divisibility rule of 8: last three digits should be divisible by 8.<br>Divisibility rule of 5: last digit is either 0 or 5.<br>If last digit is 5:<br>The 62684*5 is not divisible by 8.<br>If last digit is 0:<br>62684*0 <br>The value of * can be 0, 4, 8.<br>By checking options, option (b) satisfied the given condition.</p>",
                    solution_hi: "<p>69.(b)<br>8 की विभाज्यता नियम: अंतिम तीन अंक 8 से विभाज्य होने चाहिए।<br>5 की विभाज्यता नियम: अंतिम अंक या तो 0 या 5 है।<br>यदि अंतिम अंक 5 है:<br>62684*5, 8 से विभाज्य नहीं है।<br>यदि अंतिम अंक 0 है:<br>62684*0 <br>* का मान 0, 4, 8 हो सकता है.<br>विकल्पों की जाँच करके, विकल्प (b) दी गई शर्त को पूरा करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. If 8cot&theta; = 7, then the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mi>cos&#952;</mi></mfrac></math> is :</p>",
                    question_hi: "<p>70. यदि 8cot&theta; = 7 है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mi>cos&#952;</mi></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>70.(a)<br>8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cot&#952;</mi></math> = 7 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cot&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Base</mi><mi>Perpendicular</mi></mfrac></math><br>Hence, Hypotenuse = <math display=\"inline\"><msqrt><mn>113</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mi>cos&#952;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mi>perpendicular</mi><mi>Hypotenuse</mi></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mi>Base</mi><mi>Hypotenuse</mi></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><msqrt><mn>113</mn></msqrt><mo>+</mo><mn>8</mn></mrow><msqrt><mn>113</mn></msqrt></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>113</mn></msqrt><mo>+</mo><mn>8</mn></mrow><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>70.(a)<br>8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cot&#952;</mi></math> = 7 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cot&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2354;&#2306;&#2348;</mi></mfrac></math> <br>अत:, कर्ण = <math display=\"inline\"><msqrt><mn>113</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mi>cos&#952;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2354;&#2306;&#2348;</mi></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><msqrt><mn>113</mn></msqrt><mo>+</mo><mn>8</mn></mrow><msqrt><mn>113</mn></msqrt></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>113</mn></msqrt><mo>+</mo><mn>8</mn></mrow><mn>7</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Two ships are sailing in the sea on two sides of a lighthouse. The angles of elevation of top of the lighthouse as observed from the two ships are <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>30</mn><mi mathvariant=\"normal\">o</mi></msup><mi mathvariant=\"normal\">&#160;</mi><mi>and</mi><mi mathvariant=\"normal\">&#160;</mi><msup><mn>45</mn><mi mathvariant=\"normal\">o</mi></msup></math> respectively. If the light house is 90 m high, then the distance between the two ships is_________.</p>",
                    question_hi: "<p>71. एक प्रकाशस्तंभ के दो किनारों पर समुद्र में दो जहाज चल रहे हैं। दो जहाजों से देखे गए प्रकाशस्तंभ के शीर्ष के उन्नयन कोण क्रमशः 30&deg; और 45&deg; हैं। यदि लाइट हाउस 90 मीटर ऊंचा है, तो दोनों जहाजों के बीच की दूरी _________ है।</p>",
                    options_en: [
                        "<p>90<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">m</mi></math></p>",
                        "<p>90(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+1) m</p>",
                        "<p>100 m</p>",
                        "<p>90 (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>-1) m</p>"
                    ],
                    options_hi: [
                        "<p>90<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">m</mi></math></p>",
                        "<p>90(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+1) m</p>",
                        "<p>100 m</p>",
                        "<p>90 (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>-1) m</p>"
                    ],
                    solution_en: "<p>71.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516769.png\" alt=\"rId70\"><br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;ADC</mi><mo>,</mo></math><br>tan45&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mi>DC</mi></mfrac><mo>=</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></math><br>DC = 90 m<br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;ABC</mi><mo>,</mo></math><br>tan30&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mi>BC</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>BC = 90<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m<br>Distance between two ships = 90 + 90<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>= 90 ( 1 +<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>) m</p>",
                    solution_hi: "<p>71.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739162516769.png\" alt=\"rId70\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;ADC</mi></math>में,<br>tan45&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mi>DC</mi></mfrac><mo>=</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></math><br>DC = 90 m<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;ABC</mi></math>में,<br>tan30&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mi>BC</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>BC = 90<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m<br>दो जहाजों के बीच की दूरी = 90 + 90<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = 90 (1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>) m</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow></mfrac><mo>)</mo></math> = 3, then the value of (x - 1)&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> is:</p>",
                    question_hi: "<p>72. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow></mfrac><mo>)</mo></math> = 3 , तो (x - 1)&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>72.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow></mfrac><mo>)</mo></math> = 3<br>Subtracting both side by 1, we have ;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow></mfrac><mo>)</mo></math> = 2<br>Let , (x&nbsp;- 1) = k<br>k - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac></math> = 2<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math> = 2&sup2; + 2 = 6<br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> = 6</p>",
                    solution_hi: "<p>72.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow></mfrac><mo>)</mo></math> = 3</p>\n<p dir=\"ltr\">दोनों पक्षों को 1 से घटाने पर, हमें प्राप्त होता है;<strong id=\"docs-internal-guid-dd8af99e-7fff-882e-53fa-704ec857bfd9\"></strong>&nbsp;;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow></mfrac><mo>)</mo></math> = 2<br>माना , (x - 1) = k<br>k - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac></math> = 2<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math> = 2&sup2; + 2 = 6<br>अतः,&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> = 6</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A policeman is chasing a thief at a speed of 12 km/h, and the thief is running at a speed of 8 km/h. If the policeman started 30 minutes late, find the time taken by the policeman to catch the thief.</p>",
                    question_hi: "<p>73. एक पुलिसकर्मी 12 km/h की चाल से एक चोर का पीछा कर रहा है, और चोर 8 km/h की चाल से भाग रहा है। यदि पुलिसकर्मी 30 मिनट देरी से पीछा करना शुरू करता है, तो चोर को पकड़ने में पुलिसकर्मी द्वारा लिया गया समय ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>60 minutes</p>",
                        "<p>120 minutes</p>",
                        "<p>90 minutes</p>",
                        "<p>100 minutes</p>"
                    ],
                    options_hi: [
                        "<p>60 minutes</p>",
                        "<p>120 minutes</p>",
                        "<p>90 minutes</p>",
                        "<p>100 minutes</p>"
                    ],
                    solution_en: "<p>73.(a) <br>Distance covered by thief in 30 min. = 4 km<br>Relative speed = 12 - 8 = 4 km/h<br>Time taken by the policeman to cover 4 km = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 1 hrs or 60 min.</p>",
                    solution_hi: "<p>73.(a) <br>चोर द्वारा 30 मिनट में तय की गई दूरी = 4 किमी<br>सापेक्ष गति = 12 - 8 = 4 किमी/घंटा<br>पुलिसकर्मी को 4 किमी की दूरी तय करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 1 घंटा या 60 मिनट</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If equation of line p is x + y = 5 and that of line q is x - y = 3, what are the coordinates of the point common to both the lines?</p>",
                    question_hi: "<p>74. यदि रेखा p का समीकरण x+y = 5 है और रेखा q का समीकरण x - y = 3 है, तो दोनों रेखाओं में उभयनिष्ठ बिंदु के निर्देशांक हैं?</p>",
                    options_en: [
                        "<p>(1, 4)</p>",
                        "<p>(2, 3)</p>",
                        "<p>(2,1)</p>",
                        "<p>(4,1)</p>"
                    ],
                    options_hi: [
                        "<p>(1, 4)</p>",
                        "<p>(2, 3)</p>",
                        "<p>(2,1)</p>",
                        "<p>(4,1)</p>"
                    ],
                    solution_en: "<p>74.(d)<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> + y = 5&hellip;..(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> - y = 3&hellip;&hellip;(ii)<br>On adding both equations we get,<br>2x&nbsp;= 8 &rArr; x = 4 <br>On putting the value of <math display=\"inline\"><mi>x</mi></math> in eq (i) we get<br>4 + y = 5<br>y = 1<br>Required coordinates = (4, 1)</p>",
                    solution_hi: "<p>74.(d)<br>प्रश्न के अनुसार,<br>x + y = 5&hellip;..(i)<br>x - y = 3&hellip;&hellip;(ii)<br>दोनों समीकरणों को जोड़ने पर,<br>2x&nbsp;= 8 &rArr; x = 4 <br>x के मान को समीकरण (i) में रखने पर, हमें मिलता है,<br>4 + y = 5<br>y = 1<br>आवश्यक निर्देशांक = (4, 1)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The sum of three consecutive numbers is 177. The mean of these three numbers is:</p>",
                    question_hi: "<p>75. तीन क्रमिक संख्याओं का योग 177 है। इन तीन संख्याओं का माध्य क्या होगा?</p>",
                    options_en: [
                        "<p>59</p>",
                        "<p>60</p>",
                        "<p>57</p>",
                        "<p>58</p>"
                    ],
                    options_hi: [
                        "<p>59</p>",
                        "<p>60</p>",
                        "<p>57</p>",
                        "<p>58</p>"
                    ],
                    solution_en: "<p>75.(a)<br>Let the first consecutive number be x .<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>177</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>177</mn><mo>-</mo><mn>3</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>174</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>58</mn></math><br>Numbers are 58 , 59 , 60<br>Hence, the mean = 59<math display=\"inline\"><mi>&#4966;</mi></math><br>Alternate<math display=\"inline\"><mi>&#4966;</mi></math><br>Mean = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Sum</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>the</mi><mi mathvariant=\"normal\">&#160;</mi><mi>given</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>177</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 59</p>",
                    solution_hi: "<p>75.(a)<br>माना, पहली क्रमागत संख्या x है।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>177</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>177</mn><mo>-</mo><mn>3</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>174</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>58</mn></math><br>संख्याएं 58, 59, 60 हैं<br>अतः माध्य = 59<br>एकांतर<math display=\"inline\"><mi>&#4966;</mi></math><br>माध्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2327;&#2312;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2351;&#2379;&#2327;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>177</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 59</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate synonym of the given word. <br>Jargon</p>",
                    question_hi: "<p>76. Select the most appropriate synonym of the given word. <br>Jargon</p>",
                    options_en: [
                        "<p>Terminology</p>",
                        "<p>Automobile</p>",
                        "<p>Music</p>",
                        "<p>Essay</p>"
                    ],
                    options_hi: [
                        "<p>Terminology</p>",
                        "<p>Automobile</p>",
                        "<p>Music</p>",
                        "<p>Essay</p>"
                    ],
                    solution_en: "<p>76.(a) <strong>Terminology-</strong> special words or expressions used in relation to a particular subject. <br><strong>Jargon- </strong>specialized language used by a specific group that is often hard for others to understand. <br><strong>Automobile-</strong> a powered vehicle used for transporting people or goods, commonly called a car. <br><strong>Music- </strong>an art form that combines sounds and rhythms to create melodies and express emotions.<br><strong>Essay-</strong> a short piece of writing on particular subject.</p>",
                    solution_hi: "<p>76.(a) <strong>Terminology </strong>(शब्दावली) - special words or expressions used in relation to a particular subject. <br><strong>Jargon</strong> (विशिष्ट शब्दावली) - specialized language used by a specific group that is often hard for others to understand. <br><strong>Automobile </strong>(मोटर-गाड़ी) - a powered vehicle used for transporting people or goods, commonly called a car. <br><strong>Music</strong> (संगीत) - an art form that combines sounds and rhythms to create melodies and express emotions.<br><strong>Essay </strong>(निबंध) - a short piece of writing on particular subject.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Parts of the following sentence have been given as options. Select the option that contains an error.<br>Hiking across an Himalayas is going to be a tough task for a beginner.</p>",
                    question_hi: "<p>77. Parts of the following sentence have been given as options. Select the option that contains an error.<br>Hiking across an Himalayas is going to be a tough task for a beginner.</p>",
                    options_en: [
                        "<p>task for a beginner.</p>",
                        "<p>Hiking across</p>",
                        "<p>going to be a tough</p>",
                        "<p>an Himalayas is</p>"
                    ],
                    options_hi: [
                        "<p>task for a beginner.</p>",
                        "<p>Hiking across</p>",
                        "<p>going to be a tough</p>",
                        "<p>an Himalayas is</p>"
                    ],
                    solution_en: "<p>77.(d) an Himalayas is<br>&lsquo;Himalayas&rsquo; is a mountain range and we use the definite article &lsquo;the&rsquo; before mountain ranges. Hence, &lsquo;the Himalayas is&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(d) an Himalayas is<br>&lsquo;Himalayas&rsquo; एक mountain range है और हम mountain ranges से पहले definite article &lsquo;the&rsquo; का प्रयोग करते हैं। अतः, &lsquo;the Himalayas is&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>The enigma of quantum mechanics remains inscrutably elusive <span style=\"text-decoration: underline;\">despite of decades</span> of intensive research by the world\'s leading physicists.</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>The enigma of quantum mechanics remains inscrutably elusive <span style=\"text-decoration: underline;\">despite of decades</span> of intensive research by the world\'s leading physicists.</p>",
                    options_en: [
                        "<p>despite of decades in</p>",
                        "<p>despite decades of</p>",
                        "<p>despite of decades to</p>",
                        "<p>despite decades with</p>"
                    ],
                    options_hi: [
                        "<p>despite of decades in</p>",
                        "<p>despite decades of</p>",
                        "<p>despite of decades to</p>",
                        "<p>despite decades with</p>"
                    ],
                    solution_en: "<p>78.(b) despite decades of<br>&lsquo;Despite&rsquo; does not take &lsquo;of&rsquo;. Therefore, &lsquo;of&rsquo; will be removed. Hence, &lsquo;despite decades of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(b) despite decades of<br>&lsquo;Despite&rsquo; के साथ &lsquo;of&rsquo; का प्रयोग नहीं किया जाता है। इसलिए, &lsquo;of&rsquo; को हटा दिया जाएगा। अतः, &lsquo;despite decades of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>79. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Mystery</p>",
                        "<p>Justice</p>",
                        "<p>Academy</p>",
                        "<p>Bycycle</p>"
                    ],
                    options_hi: [
                        "<p>Mystery</p>",
                        "<p>Justice</p>",
                        "<p>Academy</p>",
                        "<p>Bycycle</p>"
                    ],
                    solution_en: "<p>79.(d) Bycycle<br>&lsquo;Bicycle&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>79.(d) Bycycle<br>&lsquo;Bicycle&rsquo; सही spelling है। <br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>He has been growing weaker and his life now hangs <span style=\"text-decoration: underline;\"><strong>with</strong></span> a thread.</p>",
                    question_hi: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>He has been growing weaker and his life now hangs <span style=\"text-decoration: underline;\"><strong>with</strong></span> a thread.</p>",
                    options_en: [
                        "<p>on</p>",
                        "<p>to</p>",
                        "<p>by</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>on</p>",
                        "<p>to</p>",
                        "<p>by</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>80.(c) by. <br>&lsquo;Hang by a thread&rsquo; is an idiom that means to be in a precarious position. The given sentence states that he has been becoming weaker so he is in a precarious(dangerous) position. Hence, &lsquo;by&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(c) by. <br>&lsquo;Hang by a thread &rsquo;एक मुहावरा है जिसका अर्थ है - अनिश्चित स्थिति में होना। दिए गए वाक्य में कहा गया है कि वह कमजोर होता जा रहा है इसलिए वह अनिश्चित (खतरनाक) स्थिति में है। इसलिए, &lsquo;by&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the correct passive form of the given sentence.</p>\n<p>Why didn&rsquo;t your father accompany you?</p>",
                    question_hi: "<p>81. Select the correct passive form of the given sentence.</p>\n<p>Why didn&rsquo;t your father accompany you?</p>",
                    options_en: [
                        "<p>Why were you not accompanied by your father?</p>",
                        "<p>Why have you not been accompanied by your father?</p>",
                        "<p>Why didn&rsquo;t you be accompanied by your father?</p>",
                        "<p>Why wasn&rsquo;t your father accompanied by you?</p>"
                    ],
                    options_hi: [
                        "<p>Why were you not accompanied by your father?</p>",
                        "<p>Why have you not been accompanied by your father?</p>",
                        "<p>Why didn&rsquo;t you be accompanied by your father?</p>",
                        "<p>Why wasn&rsquo;t your father accompanied by you?</p>"
                    ],
                    solution_en: "<p>81.(a) Why were you not accompanied by your father?(Correct)<br>(b) Why <span style=\"text-decoration: underline;\">have</span> you not been accompanied by your father?(Incorrect Helping Verb)<br>(c) Why <span style=\"text-decoration: underline;\">didn&rsquo;t</span> you be accompanied by your father?(Incorrect Helping Verb)<br>(d) Why wasn&rsquo;t your father accompanied by you?(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>81.(a) Why were you not accompanied by your father?(Correct)<br>(b) Why <span style=\"text-decoration: underline;\">have</span> you not been accompanied by your father? (गलत Helping Verb)<br>(c) Why <span style=\"text-decoration: underline;\">didn&rsquo;t</span> you be accompanied by your father? (गलत Helping Verb)<br>(d) Why wasn&rsquo;t your father accompanied by you? (गलत Sentence Structure)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>A list of explanations of rare, technical or obsolete words.</p>",
                    question_hi: "<p>82. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>A list of explanations of rare, technical or obsolete words.</p>",
                    options_en: [
                        "<p>Dictionary</p>",
                        "<p>Glossary</p>",
                        "<p>Lexicon</p>",
                        "<p>catalogue</p>"
                    ],
                    options_hi: [
                        "<p>Dictionary</p>",
                        "<p>Glossary</p>",
                        "<p>Lexicon</p>",
                        "<p>catalogue</p>"
                    ],
                    solution_en: "<p>82.(b) Glossary. <br><strong>Dictionary - </strong>a book or electronic resource that lists the words of a language (typically in alphabetical order) and gives their meaning, or gives the equivalent words in a different language, often also providing information about pronunciation, origin, and usage.<br><strong>Glossary - </strong>an alphabetical list of words relating to a specific subject, text, or dialect, with explanations; a brief dictionary.<br><strong>Lexicon -</strong> the vocabulary of a person, language, or branch of knowledge.<br><strong>Catalogue - </strong>a complete list of items, typically one in alphabetical or other systematic order, in particular</p>",
                    solution_hi: "<p>82.(b) Glossary. <br><strong>Dictionary -</strong> एक पुस्तक या इलेक्ट्रॉनिक संसाधन जो किसी भाषा के शब्दों को सूचीबद्ध करता है (आमतौर पर वर्णानुक्रम में) और उनका अर्थ देता है, या एक अलग भाषा में equivalent शब्द देता है, अक्सर उच्चारण, मूल और उपयोग के बारे में जानकारी भी प्रदान करता है।<br><strong>Glossary -</strong> स्पष्टीकरण के साथ किसी विशिष्ट विषय, पाठ या बोली से संबंधित शब्दों की वर्णानुक्रमिक सूची; एक संक्षिप्त शब्दकोश।<br><strong>Lexicon - </strong>किसी व्यक्ति, भाषा या ज्ञान की शाखा की शब्दावली।<br><strong>Catalogue -</strong> वस्तुओं की एक पूरी सूची, आमतौर पर वर्णानुक्रम में या अन्य व्यवस्थित क्रम में, विशेष रूप से।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate ANTONYM of the given word.<br>Forbid</p>",
                    question_hi: "<p>83. Select the most appropriate ANTONYM of the given word.<br>Forbid</p>",
                    options_en: [
                        "<p>Bear</p>",
                        "<p>Operate</p>",
                        "<p>Collect</p>",
                        "<p>Allow</p>"
                    ],
                    options_hi: [
                        "<p>Bear</p>",
                        "<p>Operate</p>",
                        "<p>Collect</p>",
                        "<p>Allow</p>"
                    ],
                    solution_en: "<p>83.(d) <strong>Allow- </strong>let (someone) have or do something.<br><strong>Forbid- </strong>refuse to allow.<br><strong>Bear-</strong> carry the weight of<br><strong>Operate- </strong>control the functioning of (a machine, process, or system).<br><strong>Collect- </strong>bring together (a number of things).</p>",
                    solution_hi: "<p>83.(d) <strong>Allow</strong> (अनुमति देना) - let (someone) have or do something.<br><strong>Forbid</strong> (निषेध करना) - refuse to allow.<br><strong>Bear</strong> (वहन करना) - carry the weight of<br><strong>Operate </strong>(संचालित करना) - control the functioning of (a machine, process, or system).<br><strong>Collect</strong> (इकट्ठा करना) - bring together (a number of things).</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Sentences of a paragraph&mdash;labelled A, B, C, D&mdash;are given below in jumbled order.<br>Select the option that arranges the sentences in the correct logical order to form a meaningful and coherent paragraph.<br>A.&ldquo;A brilliant story about death and the fear of death,&rdquo; said the original jacket blurb on Don DeLillo&rsquo;s 1985 novel, White Noise, adding that the book &ldquo;is a comedy, of course.&rdquo;<br>B.The writer that inspired Netflix\'s latest hit, White Noise, is a dazzling chronicler of modern America, and one of the country&rsquo;s legendary novelists.<br>C.So this feels like a good time to look again at White Noise&rsquo;s author, and consider why Don DeLillo is one of the great novelists of our time.<br>D.This month, Noah Baumbach&rsquo;s Netflix film, White Noise, dazzles its way on to our screens, and we&rsquo;re promised &lsquo;a fascinating, invigorating spectacle&rsquo;, a &lsquo;thrillingly original&rsquo; blast of cinematic lustre.</p>",
                    question_hi: "<p>84. Sentences of a paragraph&mdash;labelled A, B, C, D&mdash;are given below in jumbled order.<br>Select the option that arranges the sentences in the correct logical order to form a meaningful and coherent paragraph.<br>A.&ldquo;A brilliant story about death and the fear of death,&rdquo; said the original jacket blurb on Don DeLillo&rsquo;s 1985 novel, White Noise, adding that the book &ldquo;is a comedy, of course.&rdquo;<br>B.The writer that inspired Netflix\'s latest hit, White Noise, is a dazzling chronicler of modern America, and one of the country&rsquo;s legendary novelists.<br>C.So this feels like a good time to look again at White Noise&rsquo;s author, and consider why Don DeLillo is one of the great novelists of our time.<br>D.This month, Noah Baumbach&rsquo;s Netflix film, White Noise, dazzles its way on to our screens, and we&rsquo;re promised &lsquo;a fascinating, invigorating spectacle&rsquo;, a &lsquo;thrillingly original&rsquo; blast of cinematic lustre.</p>",
                    options_en: [
                        "<p>A, D, B, C</p>",
                        "<p>C, D, A, B</p>",
                        "<p>D, A, B, C</p>",
                        "<p>B, A, D, C</p>"
                    ],
                    options_hi: [
                        "<p>A, D, B, C</p>",
                        "<p>C, D, A, B</p>",
                        "<p>D, A, B, C</p>",
                        "<p>B, A, D, C</p>"
                    ],
                    solution_en: "<p>84.(d) B, A, D, C<br>Sentence B will be the starting line as it introduces the main subject of the parajumble i.e. &lsquo;The writer that inspires Netflix&rsquo;s latest hit, White House&rsquo;. And, Sentence A states that the novel, White House is a brilliant story about death and the fear of death. So, A will follow B. Further, Sentence D states that the film, White House will be on to our screens this month &amp; Sentence C states that this feels like a good time to look again at White House&rsquo;s author. So, C will follow D. Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>84.(d) B, A, D, C<br>Sentence B प्रारम्भिक line होगी क्योंकि यह parajumble के main subject, &lsquo;The writer that inspires Netflix&rsquo;s latest hit, White House&rsquo; का परिचय देता है। और, Sentence A बताता है कि novel, White House मृत्यु एवं मृत्यु के भय के बारे में एक brilliant story है। इसलिए, B के बाद A आएगा। इसके अलावा, Sentence D बताता है कि film, White House इस महीने हमारी screen पर आएगी और sentence C बताता है कि White House के लेखक(author) को फिर से देखने का यह अच्छा समय है। इसलिए, D के बाद C आएगा। Options के माध्यम से जाने पर, option (d) में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the correctly spelt word.</p>",
                    question_hi: "<p>85. Select the correctly spelt word.</p>",
                    options_en: [
                        "<p>Bungalow</p>",
                        "<p>Banglo</p>",
                        "<p>Bunglow</p>",
                        "<p>Banglow</p>"
                    ],
                    options_hi: [
                        "<p>Bungalow</p>",
                        "<p>Banglo</p>",
                        "<p>Bunglow</p>",
                        "<p>Banglow</p>"
                    ],
                    solution_en: "<p>85.(a) Bungalow<br>&lsquo;Bungalow&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(a) Bungalow<br>&lsquo;Bungalow&rsquo; सही spelling है। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "86. Given below are four jumbled sentences. Pick the option that gives their correct order. <br />P: Big rainbows can be seen when the sun is close to horizon.<br />Q: Or you can notice a rainbow in the spray from a garden hose.<br />R: You can see a rainbow in the mist from a waterfall.<br />S; When you stand with a light source behind you and misty water before you, you can see a rainbow.",
                    question_hi: "86. Given below are four jumbled sentences. Pick the option that gives their correct order. <br />P: Big rainbows can be seen when the sun is close to horizon.<br />Q: Or you can notice a rainbow in the spray from a garden hose.<br />R; You can see a rainbow in the mist from a waterfall.<br />S: When you stand with a light source behind you and misty water before you, you can see a rainbow.",
                    options_en: [
                        " SPRQ ",
                        " SRQP",
                        " SPQR ",
                        " SQRP"
                    ],
                    options_hi: [
                        " SPRQ ",
                        " SRQP",
                        " SPQR ",
                        " SQRP"
                    ],
                    solution_en: "86.(a)  SPRQ<br />Sentence S will be the starting line as it contains the main idea of the parajumble i.e. formation of a rainbow. However, Sentence P states that big rainbows can be seen when the sun is close to horizon. So, P will follow S. Further, Sentence R states that you can see a rainbow in the mist from a waterfall & Sentence Q states that you can notice a rainbow in the spray from a garden hose. So, Q will follow R. Going through the options, only option a has the correct sequence.",
                    solution_hi: "86.(a)  SPRQ<br />वाक्य S शुरूआती पंक्ति (line) होगी क्योंकि इसमें parajumble का मुख्य विचार है-  यानी इंद्रधनुष का निर्माण । हालाँकि, वाक्य P बताता है कि बड़े इंद्रधनुष तब देखे जा सकते हैं जब सूर्य horizon (क्षितिज) के करीब होता है। तो, S के बाद P आएगा। आगे, वाक्य R कहता है कि आप एक झरने से धुंध में एक इंद्रधनुष देख सकते हैं और  वाक्य Q  बताता है कि आप एक बगीचे की नली से स्प्रे में एक इंद्रधनुष देख सकते हैं। इसलिए R के बाद Q आएगा। विकल्पों के माध्यम से जाने पर, केवल विकल्प a (SPRQ) में सही क्रम है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the idiom given in the following Question.<br>Stand-offish</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the idiom given in the following Question.<br>Stand-offish</p>",
                    options_en: [
                        "<p>angry</p>",
                        "<p>hilarious</p>",
                        "<p>indifferent</p>",
                        "<p>unmanageable</p>"
                    ],
                    options_hi: [
                        "<p>angry</p>",
                        "<p>hilarious</p>",
                        "<p>indifferent</p>",
                        "<p>unmanageable</p>"
                    ],
                    solution_en: "<p>87.(c) indifferent.<br>E.g.- I don&rsquo;t know why Neha has become stand-offish recently.</p>",
                    solution_hi: "<p>87.(c) indifferent./उदासीन।<br>उदहारण - I don&rsquo;t know why Neha has become stand-offish recently./मुझे नहीं पता कि नेहा हाल ही में stand-offish क्यों हो गई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate idiom to fill in the blank.<br>___________, French was the most difficult language for Shruti.</p>",
                    question_hi: "<p>88. Select the most appropriate idiom to fill in the blank.<br>___________, French was the most difficult language for Shruti.</p>",
                    options_en: [
                        "<p>Bear out</p>",
                        "<p>By far</p>",
                        "<p>Break into</p>",
                        "<p>Be in the air</p>"
                    ],
                    options_hi: [
                        "<p>Bear out</p>",
                        "<p>By far</p>",
                        "<p>Break into</p>",
                        "<p>Be in the air</p>"
                    ],
                    solution_en: "<p>88.(b) <strong>By far</strong><br>&lsquo;By far&rsquo; means a lot more than something. The given sentence states that by far French was the most difficult language for Shruti. Hence, &lsquo;By far&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>88.(b) <strong>By far</strong><br>&lsquo;By far&rsquo; का अर्थ है किसी चीज़ से कहीं ज़्यादा। दिए गए sentence में कहा गया है कि श्रुति के लिए अब तक French सबसे कठिन भाषा थी। अतः, &lsquo;By far&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the option that can be used as a one-word substitute for the given group of words.<br>Speech without prior preparation</p>",
                    question_hi: "<p>89. Select the option that can be used as a one-word substitute for the given group of words.<br>Speech without prior preparation</p>",
                    options_en: [
                        "<p>Debate</p>",
                        "<p>Extempore</p>",
                        "<p>Lecture</p>",
                        "<p>Rambling</p>"
                    ],
                    options_hi: [
                        "<p>Debate</p>",
                        "<p>Extempore</p>",
                        "<p>Lecture</p>",
                        "<p>Rambling</p>"
                    ],
                    solution_en: "<p>89.(b) <strong>Extempore- </strong>speech without prior preparation.<br><strong>Debate-</strong> a formal discussion where people present and argue different viewpoints on a specific topic.<br><strong>Lecture-</strong> a formal presentation given to an audience to teach or inform them about a specific topic.<br><strong>Rambling- </strong>speaking or writing in a confused or lengthy way, often without a clear direction or purpose.</p>",
                    solution_hi: "<p>89.(b) <strong>Extempore</strong> (बिना तैयारी के) - speech without prior preparation.<br><strong>Debate</strong> (वाद-विवाद) - a formal discussion where people present and argue different viewpoints on a specific topic.<br><strong>Lecture</strong> (भाषण/व्याख्यान) - a formal presentation given to an audience to teach or inform them about a specific topic.<br><strong>Rambling</strong> (असंबद्ध भाषण)- speaking or writing in a confused or lengthy way, often without a clear direction or purpose.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate synonym of the underlined word in the following sentence.<br>The new software offers <span style=\"text-decoration: underline;\">a plethora of</span> features to enhance productivity.</p>",
                    question_hi: "<p>90. Select the most appropriate synonym of the underlined word in the following sentence.<br>The new software offers <span style=\"text-decoration: underline;\">a plethora of</span> features to enhance productivity.</p>",
                    options_en: [
                        "<p>a scarcity of</p>",
                        "<p>a consequence of</p>",
                        "<p>a shortage of</p>",
                        "<p>an abundance of</p>"
                    ],
                    options_hi: [
                        "<p>a scarcity of</p>",
                        "<p>a consequence of</p>",
                        "<p>a shortage of</p>",
                        "<p>an abundance of</p>"
                    ],
                    solution_en: "<p>90.(d) an abundance of<br>The phrase &lsquo;plethora of&rsquo; means an abundance of something. The given sentence states that the new software offers an abundance of features to enhance productivity. Hence, &lsquo;an abundance of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(d) an abundance of<br>Phrase &lsquo;plethora of&rsquo; का अर्थ है किसी चीज़ की प्रचुरता। दिए गए sentence में कहा गया है कि नए software उत्पादकता बढ़ाने के लिए बहुत सारी सुविधाएँ प्रदान करते हैं। अतः, &lsquo;an abundance of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the correct active form of the given sentence.</p>\n<p>Let the fire be kept burning at night.</p>",
                    question_hi: "<p>91. Select the correct active form of the given sentence.</p>\n<p>Let the fire be kept burning at night.</p>",
                    options_en: [
                        "<p>You must burn the fire at night.</p>",
                        "<p>The fire should be burnt at night.</p>",
                        "<p>Let the fire burning at night</p>",
                        "<p>Keep the fire burning at night.</p>"
                    ],
                    options_hi: [
                        "<p>You must burn the fire at night.</p>",
                        "<p>The fire should be burnt at night.</p>",
                        "<p>Let the fire burning at night</p>",
                        "<p>Keep the fire burning at night.</p>"
                    ],
                    solution_en: "<p>91.(d) Keep the fire burning at night.(Correct)<br>(a) You must burn the fire at night.(Incorrect Sentence Structure)<br>(b) The fire should be burnt at night.(Incorrect Sentence Structure)<br>(c) Let the fire burning at night.(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>91.(d) Keep the fire burning at night.(Correct)<br>(a) You must burn the fire at night.(गलत Sentence Structure)<br>(b) The fire should be burnt at night.(गलत Sentence Structure)<br>(c) Let the fire burning at night.(गलत Sentence Structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option to fill in the blank.<br>We offer__________ to all who ask.</p>",
                    question_hi: "<p>92. Select the most appropriate option to fill in the blank.<br>We offer__________ to all who ask.</p>",
                    options_en: [
                        "<p>councilor</p>",
                        "<p>council</p>",
                        "<p>counselor</p>",
                        "<p>counsel</p>"
                    ],
                    options_hi: [
                        "<p>councilor</p>",
                        "<p>council</p>",
                        "<p>counselor</p>",
                        "<p>counsel</p>"
                    ],
                    solution_en: "<p>92.(d) counsel.<br>Counsel (Noun) - a piece of advice.</p>",
                    solution_hi: "<p>92.(d) counsel.<br>Counsel (Noun) - सलाह का एक रूप । </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the given word.<br>Powerful</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the given word.<br>Powerful</p>",
                    options_en: [
                        "<p>Strong</p>",
                        "<p>Manly</p>",
                        "<p>Impotent</p>",
                        "<p>Wild</p>"
                    ],
                    options_hi: [
                        "<p>Strong</p>",
                        "<p>Manly</p>",
                        "<p>Impotent</p>",
                        "<p>Wild</p>"
                    ],
                    solution_en: "<p>93.(c) <strong>Impotent- </strong>helpless or powerless.<br><strong>Powerful- </strong>having great strength or force.<br><strong>Strong-</strong> powerful.<br><strong>Manly- </strong>having the qualities that people think man should have.<br><strong>Wild-</strong> living in a state of nature.</p>",
                    solution_hi: "<p>93.(c) <strong>Impotent</strong> (शक्तिहीन) - helpless or powerless.<br><strong>Powerful</strong> (शक्तिशाली) - having great strength or force.<br><strong>Strong</strong> (मजबूत) - powerful.<br><strong>Manly</strong> (मर्दाना) - having the qualities that people think man should have.<br><strong>Wild </strong>(जंगली) - living in a state of nature.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Identify the segment in the sentence, which contains the grammatical error.<br>Hardly I had stepped out of the house when I got a call.</p>",
                    question_hi: "<p>94. Identify the segment in the sentence, which contains the grammatical error.<br>Hardly I had stepped out of the house when I got a call.</p>",
                    options_en: [
                        "<p>Hardly I had stepped</p>",
                        "<p>out of the house</p>",
                        "<p>when I got a call</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>Hardly I had stepped</p>",
                        "<p>out of the house</p>",
                        "<p>when I got a call</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>94.(a) Hardly I had stepped. <br>When adverbs like &lsquo;Hardly,Scarcely,Barely&rsquo; are used at the beginning of a sentence, they are always followed by a helping verb and not a noun or pronoun. Hence, &lsquo;Hardly had(verb) I stepped&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(a) Hardly I had stepped. <br>जब किसी वाक्य की शुरुआत में जैसे &lsquo;Hardly,Scarcely,Barely&rsquo; adverbs का उपयोग किया जाता है, तो उनके बाद हमेशा helping verb होती है न कि noun या pronoun । इसलिए,&lsquo;Hardly had (verb) I stepped&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>Shraddha wandered through the labyrinth of thoughts, seeking clarity _____ chaos.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>Shraddha wandered through the labyrinth of thoughts, seeking clarity _____ chaos.</p>",
                    options_en: [
                        "<p>for</p>",
                        "<p>upstairs</p>",
                        "<p>down</p>",
                        "<p>amidst</p>"
                    ],
                    options_hi: [
                        "<p>for</p>",
                        "<p>upstairs</p>",
                        "<p>down</p>",
                        "<p>amidst</p>"
                    ],
                    solution_en: "<p>95.(d) amidst<br>&lsquo;Amidst&rsquo; means in the middle of something. The given sentence states that Shraddha wandered through the labyrinth of thoughts, seeking clarity amidst chaos. Hence, &lsquo;amidst&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(d) amidst<br>&lsquo;Amidst&rsquo; का अर्थ है किसी चीज़ के बीच में होना। दिए गए sentence में कहा गया है कि श्रद्धा विचारों की भूलभुलैया (labyrinth) में भटक रही थी, अराजकता (chaos) के बीच स्पष्टता की तलाश में। अतः, &lsquo;amidst&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 96.</p>",
                    options_en: [
                        "<p>standards</p>",
                        "<p>targets</p>",
                        "<p>ideals</p>",
                        "<p>goals</p>"
                    ],
                    options_hi: [
                        "<p>standards</p>",
                        "<p>targets</p>",
                        "<p>ideals</p>",
                        "<p>goals</p>"
                    ],
                    solution_en: "<p>96.(c) ideals. <br>&lsquo;Ideals&rsquo; means an idea or principle that seems perfect to you and that you want to achieve. The given passage states that our national leaders extol the ideals of nationalism. Hence, &lsquo;ideals&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) ideals. <br>&lsquo;Ideals&rsquo; का अर्थ है एक विचार या सिद्धांत जो आपको सही लगता है और जिसे आप प्राप्त करना चाहते हैं। दिए गए passage में कहा गया है कि हमारे राष्ट्रीय नेता राष्ट्रवाद के आदर्शों की प्रशंसा करते हैं। इसलिए,&lsquo;Ideals&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong> Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 97.</p>",
                    question_hi: "<p>97.<strong> Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 97.</p>",
                    options_en: [
                        "<p>enlarge</p>",
                        "<p>dwell</p>",
                        "<p>insist</p>",
                        "<p>expatiate</p>"
                    ],
                    options_hi: [
                        "<p>enlarge</p>",
                        "<p>dwell</p>",
                        "<p>insist</p>",
                        "<p>expatiate</p>"
                    ],
                    solution_en: "<p>97.(b) dwell. <br>The phrase &lsquo;Dwell on&rsquo; means to think or talk about something for a long time. The given passage states that our national leaders often think or talk about the great sacrifices made by our leaders in the cause of India&rsquo;s freedom. Hence, &lsquo;dwell&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) dwell<br>&lsquo;Dwell on&rsquo; phrase verb का अर्थ है किसी चीज के बारे में लंबे समय तक सोचना या बात करना। दिए गए passage में कहा गया है कि हमारे राष्ट्रीय नेता अक्सर भारत की स्वतंत्रता के लिए हमारे नेताओं द्वारा किए गए महान बलिदानों के बारे में सोचते या बात करते हैं। इसलिए, &lsquo;Dwell&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98.<strong> Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 98.</p>",
                    question_hi: "<p>98.<strong> Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 98.</p>",
                    options_en: [
                        "<p>majority</p>",
                        "<p>groups</p>",
                        "<p>tribes</p>",
                        "<p>hordes</p>"
                    ],
                    options_hi: [
                        "<p>majority</p>",
                        "<p>groups</p>",
                        "<p>tribes</p>",
                        "<p>hordes</p>"
                    ],
                    solution_en: "<p>98.(c) tribes. <br>&lsquo;Tribes&rsquo; means a group of persons having a common character, occupation, or interest. The given passage states that we still have a group of persons of freedom fighters among us. Hence, &lsquo;tribes&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) tribes. <br>&lsquo;Tribes&rsquo; का अर्थ है एक सामान्य चरित्र, व्यवसाय या रुचि रखने वाले व्यक्तियों का समूह। दिया गया passage बताता है कि हमारे बीच अभी भी स्वतंत्रता सेनानियों के व्यक्तियों का एक समूह है। इसलिए, &lsquo;Tribes&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 99.</p>",
                    options_en: [
                        "<p>steadily</p>",
                        "<p>generally</p>",
                        "<p>normally</p>",
                        "<p>periodically</p>"
                    ],
                    options_hi: [
                        "<p>steadily</p>",
                        "<p>generally</p>",
                        "<p>normally</p>",
                        "<p>periodically</p>"
                    ],
                    solution_en: "<p>99.(a) steadily. <br>&lsquo;Steadily&rsquo; means gradually or slowly. The given passage talks about the freedom fighters whose number keeps increasing gradually. Hence, &lsquo;steadily&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) steadily. <br>&lsquo;Steadily&rsquo; का मतलब आहिस्ता-आहिस्ता या धीरे-धीरे। दिया गया passage स्वतंत्रता सेनानियों के बारे में बात करता है जिनकी संख्या धीरे-धीरे बढ़ती रहती है। इसलिए, &lsquo;Steadily&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 100.</p>",
                    question_hi: "<p>100.<strong> Cloze test:</strong><br>Our national leaders are still trapped in the slogans and cliches of the freedom struggle. Day in and day out, they extol the ___96___ of nationalism, patriotism and service to the motherland and ___97___ on the great sacrifices made by our leaders in the cause of India&rsquo;s freedom . Mercifully, we still have___98___ of freedom fighters among us, whose number ___99___ keeps increasing despite the country having won freedom half a century back. Politicians are___100___ to be down to earth people; with their finger on the pulse of the masses.<br>Select the most appropriate option to fill in the blank number 100.</p>",
                    options_en: [
                        "<p>required</p>",
                        "<p>supposed</p>",
                        "<p>observe</p>",
                        "<p>expected</p>"
                    ],
                    options_hi: [
                        "<p>required</p>",
                        "<p>supposed</p>",
                        "<p>observe</p>",
                        "<p>expected</p>"
                    ],
                    solution_en: "<p>100.(b) supposed. <br>&lsquo;Supposed&rsquo; means to have to, to have a duty or a responsibility to do. The given passage talks about politicians who are supposed to be down-to-earth people. Hence, &lsquo;supposed&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) supposed. <br>&lsquo;Supposed&rsquo; करने का अर्थ है, कोई कर्तव्य या उत्तरदायित्व निभाना। दिया गया passage उन राजनेताओं के बारे में बात करता है जिन्हें जमीन से जुड़े लोग माना जाता है। इसलिए, &lsquo;Supposed&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>