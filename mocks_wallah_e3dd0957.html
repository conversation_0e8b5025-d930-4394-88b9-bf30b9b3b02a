<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 50</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">50</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 49,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. When did Italian traveller Marco Polo visit Kerala?</p>",
                    question_hi: "<p>1. इतालवी यात्री मार्को पोलो केरल कब आया था?</p>",
                    options_en: ["<p>15<sup>th </sup>CE</p>", "<p>8<sup>th</sup> CE</p>", 
                                "<p>10<sup>th</sup> CE</p>", "<p>13<sup>th</sup> CE</p>"],
                    options_hi: ["<p>15वीं ईस्वी</p>", "<p>8वीं ईस्वी</p>",
                                "<p>10वीं ईस्वी</p>", "<p>13वीं ईस्वी</p>"],
                    solution_en: "<p>1.(d) <strong>13<sup>th</sup> CE</strong>. Marco Polo visited China (and also India) from his home base in Venice in the late thirteenth century. Tenth-eleventh centuries (973-1048) - Muhammad ibn Ahmad Abu Raihan al-Biruni (from Uzbekistan). Fourteenth century (1304 -77) - Ibn Battuta (from Morocco). Fifteenth century (1413 - 82) - Abd al-Razzaq Kamal al-Din ibn Ishaq al-Samarqandi (from Samarqand).</p>",
                    solution_hi: "<p>1.(d)<strong> 13वीं ईस्वी। </strong>मार्को पोलो ने तेरहवीं शताब्दी के अंत में वेनिस स्थित अपने गृह क्षेत्र से चीन (और भारत) का दौरा किया। दसवीं-ग्यारहवीं शताब्दी (973-1048) - मुहम्मद इब्न अहमद अबू रैहान अल-बिरूनी (उज्बेकिस्तान से)। चौदहवीं शताब्दी (1304 -77) - इब्न बतूता (मोरक्को से)। पंद्रहवीं शताब्दी (1413 - 82) - अब्द अल-रज्जाक कमाल अल-दीन इब्न इशाक अल-समरकंदी (समरकंद से)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. The Bombay Presidency Association was started by Badruddin Tyabji, Pherozshah Mehta and KT Telang in which of the following years?</p>",
                    question_hi: "<p>2. बॉम्बे प्रेसीडेंसी एसोसिएशन की शुरुआत बदरुद्दीन तैयबजी, फ़िरोज़शाह मेहता और के.टी. तेलंग द्वारा निम्नलिखित में से किस वर्ष में की गई थी?</p>",
                    options_en: ["<p>1887</p>", "<p>1885</p>", 
                                "<p>1886</p>", "<p>1888</p>"],
                    options_hi: ["<p>1887</p>", "<p>1885</p>",
                                "<p>1886</p>", "<p>1888</p>"],
                    solution_en: "<p>2.(b) <strong>1885</strong>. The Bombay Presidency Association: It was founded in response to Lytton\'s reactionary policies and the Ilbert Bill controversy. Other important associations: Poona Sarvajanik Sabha founded by Mahadev Govind Ranade on 2 April 1870. Madras Mahajana Sabha was founded by Palavai Rangaiah Naidu, Raja Savalai Ramaswami Mudaliar and Panapakkam Anandacharlu in 1884.</p>",
                    solution_hi: "<p>2.(b) <strong>1885</strong>. बॉम्बे प्रेसीडेंसी एसोसिएशन: इसकी स्थापना लिटन की प्रतिक्रियावादी नीतियों और इल्बर्ट बिल विवाद के जवाब में की गई थी। अन्य महत्वपूर्ण संघ: पूना सार्वजनिक सभा की स्थापना 2 अप्रैल 1870 को महादेव गोविंद रानाडे द्वारा की गई थी। मद्रास महाजन सभा की स्थापना 1884 में पलवई रंगैया नायडू, राजा सवलाई रामास्वामी मुदलियार और पानपक्कम आनंदचार्लू द्वारा की गई थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Mahatma Gandhi started the Dandi March in 1930 from ________.</p>",
                    question_hi: "<p>3. महात्मा गांधी ने 1930 में ________ से दांडी यात्रा शुरू की थी।</p>",
                    options_en: ["<p>Yerawada Ashram</p>", "<p>Dandi Ashram</p>", 
                                "<p>Surat Ashram</p>", "<p>Sabarmati Ashram</p>"],
                    options_hi: ["<p>यरवदा आश्रम</p>", "<p>दांडी आश्रम</p>",
                                "<p>सूरत आश्रम</p>", "<p>साबरमती आश्रम</p>"],
                    solution_en: "<p>3.(d) <strong>Sabarmati Ashram.</strong> The Salt Satyagraha was a mass civil disobedience movement initiated by Mahatma Gandhi against the salt tax imposed by the British government in India. It was started on 12th March 1930 and was ended by the Gandhi- Irwin pact in 1931. Mahatma Gandhi - Champaran Satyagraha (1917), Kheda satyagraha(1918), Ahmedabad mill strike (1918), Khilafat movement (1919), Non-Cooperation Movement (1920), Poona pact (1932), Quit India movement (1942).</p>",
                    solution_hi: "<p>3.(d) <strong>साबरमती आश्रम। </strong>नमक सत्याग्रह भारत में ब्रिटिश सरकार द्वारा लगाए गए नमक कर के खिलाफ महात्मा गांधी द्वारा शुरू किया गया एक सामूहिक सविनय अवज्ञा आंदोलन था। यह 12 मार्च 1930 को शुरू हुआ था और 1931 में गांधी-इरविन समझौते द्वारा समाप्त हुआ था। महात्मा गांधी - चंपारण सत्याग्रह (1917), खेड़ा सत्याग्रह (1918), अहमदाबाद मिल हड़ताल (1918), खिलाफत आंदोलन (1919), असहयोग आंदोलन (1920), पूना समझौता (1932), भारत छोड़ो आंदोलन (1942)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The famous Hindi novel &lsquo;Gaban&rsquo; is written by:</p>",
                    question_hi: "<p>4. प्रसिद्ध हिंदी उपन्यास \'गबन\' के उपन्यासकार _______ हैं।</p>",
                    options_en: ["<p>Dharmveer Bharati</p>", "<p>Amrita Pritam</p>", 
                                "<p>Munshi Premchand</p>", "<p>Mahadevi Verma</p>"],
                    options_hi: ["<p>धर्मवीर भारती</p>", "<p>अमृता प्रीतम</p>",
                                "<p>मुंशी प्रेमचंद</p>", "<p>महादेवी वर्मा</p>"],
                    solution_en: "<p>4.(c) <strong>Munshi Premchand.</strong> Other books - &lsquo;&rsquo;Deliverance and Other Stories&rsquo;&rsquo;, &lsquo;&rsquo;Premchand Ki Shreshtha Kahaniya&rsquo;&rsquo;, &lsquo;&rsquo;Prema&rsquo;&rsquo;, &lsquo;&rsquo;Pratigya&rsquo;&rsquo;, &lsquo;&rsquo;Karbala&rsquo;&rsquo;. Books by Amrita Pritam - &lsquo;&rsquo;The Skeleton and That Man&rsquo;&rsquo;, &lsquo;&rsquo;Raseedi Ticket&rsquo;&rsquo;, &lsquo;&rsquo;Unke Hastakshar&rsquo;&rsquo;, &lsquo;&rsquo;Blank Sheets&rsquo;&rsquo;. Books by Mahadevi Verma - &lsquo;&rsquo;Sketches from My Past&rsquo;&rsquo;, &lsquo;&lsquo;Path Ke Sathi&rsquo;&rsquo;.</p>",
                    solution_hi: "<p>4.(c) <strong>मुंशी प्रेमचंद। </strong>अन्य पुस्तकें - \'\'डिलीवरेंस एंड अदर स्टोरीज\'\', \'\'प्रेमचंद की श्रेष्ठ कहानियां\'\', \'\'प्रेमा\'\', \'\'प्रतिज्ञा\'\', \'\'कर्बला\'\'। अमृता प्रीतम की पुस्तकें - \'\'द स्केलेटन एंड दैट मैन\'\', \'\'रसीदी टिकट\'\', \'\'उनके हस्ताक्षर\'\', \'\'ब्लैंक शीट्स\'\'। महादेवी वर्मा की पुस्तकें - \'\'स्केचेस फ्रॉम माई पास्ट\'&rsquo;\', \'\'पथ के साथी\'\'।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. In which of the following states is the famous Rumtek monastery located?</p>",
                    question_hi: "<p>5. प्रसिद्ध रुमटेक मठ निम्नलिखित में से किस राज्य में स्थित है?</p>",
                    options_en: ["<p>Uttarakhand</p>", "<p>Assam</p>", 
                                "<p>Sikkim</p>", "<p>West Bengal</p>"],
                    options_hi: ["<p>उत्तराखंड</p>", "<p>असम</p>",
                                "<p>सिक्किम</p>", "<p>पश्चिम बंगाल</p>"],
                    solution_en: "<p>5.(c) <strong>Sikkim</strong>. Rumtek Monastery is also called the Dharma Chakra Centre. It is the seat-in-exile of the Gyalwang Karmapa, inaugurated in 1966 by the 16th Karmapa. Monasteries in Sikkim: Kartok Monastery, Lachen Monastery, Phodong Monastery, Tsuklakhang Monastery, Rinchenpong Monastery.</p>",
                    solution_hi: "<p>5.(c) <strong>सिक्किम</strong>। रुमटेक मठ को धर्म चक्र केंद्र भी कहा जाता है। यह ग्यालवांग करमापा की निर्वासित स्थल है, जिसका उद्घाटन 1966 में 16वें करमापा ने किया था। सिक्किम में मठ: कार्तोक मठ, लाचेन मठ, फोडांग मठ, त्सुक्लखांग मठ, रिनचेनपोंग मठ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Monolithic pillars of sandstone during Ashoka&rsquo;s reign, were crowned with animal figures like _________ elephant and lion along with inscriptions of Buddhist concepts.</p>",
                    question_hi: "<p>6. अशोक के शासनकाल के दौरान बलुआ पत्थर के एकाश्मक स्तंभों को बौद्ध अवधारणाओं के शिलालेखों के साथ-साथ _________ हाथी और शेर जैसे पशु आकृतियों से अभिषिक्त किया गया था।</p>",
                    options_en: ["<p>bull</p>", "<p>dog</p>", 
                                "<p>horse</p>", "<p>cat</p>"],
                    options_hi: ["<p>बैल</p>", "<p>कुत्ते</p>",
                                "<p>घोड़े</p>", "<p>बिल्ली</p>"],
                    solution_en: "<p>6.(a) <strong>bull</strong>. Important animals on capital of Ashoka: Sarnath Pillar (Varanasi) - Four Lions, Sanchi Pillar (Madhya Pradesh) - Four lions, but it is broken, Rampurva Pillar (Bihar) - Single Bull, Sankissa Pillar (Farrukhabad, Uttar Pradesh) - Single Elephant, Lauriya Nandangarh Pillar (West Champaran Bihar) - Single Lion.</p>",
                    solution_hi: "<p>6.(a) <strong>बैल।</strong> अशोक के स्तंभ शीर्ष पर महत्वपूर्ण पशु : सारनाथ स्तंभ (वाराणसी) - चार शेर, सांची स्तंभ (मध्य प्रदेश) - चार शेर, लेकिन यह टूटा हुआ है, रामपुरवा स्तंभ (बिहार) - एकल बैल, संकिसा स्तंभ (फर्रुखाबाद, उत्तर प्रदेश) - एकल हाथी, लौरिया नंदनगढ़ स्तंभ (पश्चिम चंपारण बिहार) - एकल शेर।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. In which of the following years did India start celebrating National Sports Day?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किस वर्ष में भारत ने राष्ट्रीय खेल दिवस मनाना शुरू किया था?</p>",
                    options_en: ["<p>2015</p>", "<p>2010</p>", 
                                "<p>2013</p>", "<p>2012</p>"],
                    options_hi: ["<p>2015</p>", "<p>2010</p>",
                                "<p>2013</p>", "<p>2012</p>"],
                    solution_en: "<p>7.(d) <strong>2012</strong>. The National Sports Day in India is celebrated on 29 August, on the birth anniversary of hockey player Major Dhyan Chand.</p>",
                    solution_hi: "<p>7.(d) <strong>2012</strong>. भारत में राष्ट्रीय खेल दिवस 29 अगस्त को हॉकी खिलाड़ी मेजर ध्यानचंद की जयंती पर मनाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. Padma Vibhushan awardee Kathak dancer and choreographer, Birju Maharaj belonged to which of the following Gharanas? ",
                    question_hi: "<p>8. पद्म विभूषण से सम्मानित कथक नर्तक और कोरियोग्राफर बिरजू महाराज निम्नलिखित में से किस घराने से संबंधित थे?</p>",
                    options_en: ["<p>Jaipur</p>", "<p>Kalka - Bindadin</p>", 
                                "<p>Gwalior</p>", "<p>Benaras</p>"],
                    options_hi: ["<p>जयपुर</p>", "<p>कालका - बिंदादीन</p>",
                                "<p>ग्वालियर</p>", "<p>बनारस</p>"],
                    solution_en: "<p>8.(b) <strong>Kalka - Bindadin. </strong>Birju Maharaj: He was associated with the Kathak (classical dance of Uttar Pradesh) dance form. Awards - Sangeet Natak Akademi Award (1964), Padma Vibhushan (1986), Kalidas Samman (1987). Exponents of Lucknow Gharana:- Lachhu Maharaj, Shambhu Maharaj, Maya Rao, Damayanti Joshi, etc.</p>",
                    solution_hi: "<p>8.(b) <strong>कालका - बिंदादीन।</strong> बिरजू महाराज: वह कथक (उत्तर प्रदेश का शास्त्रीय नृत्य) नृत्य शैली से जुड़े थे। पुरस्कार - संगीत नाटक अकादमी पुरस्कार (1964), पद्म विभूषण (1986), कालिदास सम्मान (1987)। लखनऊ घराने के प्रतिपादक:- लच्छू महाराज, शंभू महाराज, माया राव, दमयंती जोशी आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. Identify the correct statement in the context of five-year plans in India.<br />(i) In the first five-year plan, the top priority was given to agriculture. <br />(ii) In the second five-year plan, the top priority was given to industry. ",
                    question_hi: "<p>9. भारत में पंचवर्षीय योजनाओं के संदर्भ में सही कथन की पहचान करें। <br>(i) प्रथम पंचवर्षीय योजना में कृषि को सर्वोच्च प्राथमिकता दी गई। <br>(ii) दूसरी पंचवर्षीय योजना में उद्योग को सर्वोच्च प्राथमिकता दी गई।</p>",
                    options_en: ["<p>Only (ii)</p>", "<p>Both (i) and (ii)</p>", 
                                "<p>Only (i)</p>", "<p>Neither (i) nor (ii)</p>"],
                    options_hi: ["<p>केवल (ii)</p>", "<p>(i) और (ii) दोनों</p>",
                                "<p>केवल (i)</p>", "<p>न तो (i) और न ही (ii)</p>"],
                    solution_en: "<p>9.(b) <strong>Both (i) and (ii). </strong>First five-year plan (1951 to 1956): Focused on agrarian sector, dams, and irrigation, Led to the establishment of five IITs. It was based on the Harrod-Domar model. Second five-year plan (1956 - 1961) - The plan followed the Mahalanobis model. The Five Year Plans were formulated, implemented and regulated by a body known as the Planning Commission (Formed on 15 March 1950, Replaced by a think tank called NITI Aayog in 2015).</p>",
                    solution_hi: "<p>9.(b)<strong> (i) और (ii) दोनों।</strong> पहली पंचवर्षीय योजना (1951 से 1956): कृषि क्षेत्र, बांधों और सिंचाई पर ध्यान केंद्रित, पांच IIT की स्थापना का नेतृत्व किया। यह हैरोड-डोमर मॉडल पर आधारित था। दूसरी पंचवर्षीय योजना (1956 - 1961) - यह योजना महालनोबिस मॉडल पर आधारित थी। पंचवर्षीय योजनाओं का निर्माण, कार्यान्वयन और नियमन योजना आयोग (15 मार्च 1950 को गठित, 2015 में नीति आयोग नामक थिंक टैंक द्वारा प्रतिस्थापित) नामक एक निकाय द्वारा किया जाता था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Which of the following statements is/are true about ecological succession?<br />Statement 1: It is a gradual and clearly predictable change in the caste composition of a given area.<br />Statement 2: It is a method of ecological conservation.",
                    question_hi: "<p>10. पारिस्थितिक अनुक्रम के बारे में निम्नलिखित में से कौन-सा/से कथन सत्य है/हैं? <br>कथन 1: यह किसी दिए गए क्षेत्र के जाति-संघटन में क्रमिक और स्पष्ट पूर्वानुमेय परिवर्तन है। <br>कथन 2: यह पारिस्थितिकी संरक्षण की विधि है।</p>",
                    options_en: ["<p>Only statement 1 is true</p>", "<p>Only statement 2 is true</p>", 
                                "<p>Both statements 1 and 2 are false</p>", "<p>Both statement 1 and 2 are true</p>"],
                    options_hi: ["<p>केवल कथन 1 सत्य है</p>", "<p>केवल कथन 2 सत्य है</p>",
                                "<p>कथन 1 और 2 दोनों असत्य हैं</p>", "<p>कथन 1 और 2 दोनों सत्य हैं</p>"],
                    solution_en: "<p>10.(a) <strong>Only statement 1 is true.</strong> During succession some species colonise an area and their populations become more numerous, whereas populations of other species decline and even disappear. The entire sequence of communities that successively change in a given area are called Sere(s). Description of ecological succession usually focuses on changes in vegetation. However, these vegetational changes in turn affect food and shelter for various types of animals.</p>",
                    solution_hi: "<p>10.(a) <strong>केवल कथन 1 सत्य है। </strong>उत्तराधिकार के दौरान कुछ प्रजातियाँ एक क्षेत्र में बस जाती हैं और उनकी आबादी अधिक हो जाती है, जबकि अन्य प्रजातियों की आबादी घट जाती है और लुप्त भी हो जाती है। किसी दिए गए क्षेत्र में क्रमिक रूप से बदलने वाले समुदायों के संपूर्ण अनुक्रम को सेरे (Sere) कहा जाता है। पारिस्थितिक उत्तराधिकार का विवरण आमतौर पर वनस्पति में परिवर्तन पर केंद्रित होता है। हालाँकि, ये वनस्पति परिवर्तन विभिन्न प्रकार के जंतुओं के भोजन और आश्रय को प्रभावित करते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11.  The Constitution of India provides for a ________ integrated judicial system.",
                    question_hi: "<p>11. भारत का संविधान एक ________ एकीकृत न्यायिक प्रणाली प्रदान करता है।</p>",
                    options_en: ["<p>single-level</p>", "<p>multi-level</p>", 
                                "<p>dual-level</p>", "<p>three-level</p>"],
                    options_hi: ["<p>एकल-स्तरीय</p>", "<p>बहु-स्तरीय</p>",
                                "<p>द्वि-स्तरीय</p>", "<p>त्रि-स्तरीय</p>"],
                    solution_en: "<p>11.(a) <strong>Single-level</strong>. An integrated judicial system, meaning that the decisions made by higher courts are binding on the lower courts. Article 141 - Law declared by the Supreme Court to be binding on all courts. Appellate system means that a person can appeal to a higher court if they believe that the judgment passed by the lower court is not just.</p>",
                    solution_hi: "<p>11.(a) <strong>एकल-स्तरीय।</strong> एक एकीकृत न्यायिक प्रणाली, जिसका अर्थ है कि उच्च न्यायालयों द्वारा लिए गए निर्णय निचली अदालतों पर बाध्यकारी होंगें । अनुच्छेद 141 - उच्चतम न्यायालय द्वारा घोषित कानून सभी न्यायालयों पर बाध्यकारी होगा। अपीलीय प्रणाली का अर्थ है कि कोई व्यक्ति उच्च न्यायालय में अपील कर सकता है यदि उसे लगता है कि निचली अदालत द्वारा पारित निर्णय न्यायसंगत नहीं है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. In which state is the Gandhi Sagar Wildlife Sanctuary located?",
                    question_hi: "<p>12. गांधी सागर वन्यजीव अभयारण्य किस राज्&zwj;य में स्थित है?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Assam</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Karnataka</p>"],
                    options_hi: ["<p>नागालैंड</p>", "<p>असम</p>",
                                "<p>मध्य प्रदेश</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p>12.(c) <strong>Madhya Pradesh.</strong> Other important Wildlife sanctuaries: Madhya Pradesh - Kuno Wildlife Sanctuary, Bandhavgarh National Park, Kanha National Park. Assam - Kaziranga National Park, Chakrashila Wildlife Sanctuary, Hoollongapar Gibbon Sanctuary, Laokhowa Wildlife Sanctuary. Karnataka - The Dandeli wildlife sanctuary, Bhadra Wildlife Sanctuary. Nagaland - Fakim Wildlife Sanctuary, Pulie Badze Wildlife Sanctuary, Rangapahar Reserve Forest.</p>",
                    solution_hi: "<p>12.(c) <strong>मध्य प्रदेश। </strong>अन्य महत्वपूर्ण वन्यजीव अभयारण्य: मध्य प्रदेश - कूनो वन्यजीव अभयारण्य, बांधवगढ़ राष्ट्रीय उद्यान, कान्हा राष्ट्रीय उद्यान। असम - काजीरंगा राष्ट्रीय उद्यान, चक्रशिला वन्यजीव अभयारण्य, हुल्लोंगापार गिब्बन अभयारण्य, लाओखोवा वन्यजीव अभयारण्य। कर्नाटक - दांदेली वन्यजीव अभयारण्य, भद्रा वन्यजीव अभयारण्य। नागालैंड - फकीम वन्यजीव अभयारण्य, पुली बैज वन्यजीव अभयारण्य, रंगपहाड़ रिजर्व वन।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Ngozi Okonjo-Iweala is currently heading which of the following institutions?</p>",
                    question_hi: "<p>13. नगोजी ओकोंजो इवेला (Ngozi Okonjo-Iweala) वर्तमान में निम्नलिखित में किस संस्थान की कमान संभाल रही है ?</p>",
                    options_en: ["<p>IMF</p>", "<p>Asian Development Bank</p>", 
                                "<p>WTO</p>", "<p>World Bank</p>"],
                    options_hi: ["<p>आईएमएफ़ (IMF)</p>", "<p>एशियाई विकास बैंक (Asian Development Bank)</p>",
                                "<p>डब्ल्यूटीओ (WTO)</p>", "<p>विश्व बैंक (World Bank)</p>"],
                    solution_en: "<p>13.(c) <strong>WTO </strong>(World Trade Organization): Formation in - 1995, Headquarters - Geneva, Switzerland. International Monetary Fund (IMF): Founded in - 1945, Headquarters - Washington, D.C., U.S. Asian Development Bank: Formation in - 1966, Headquarters - Philippines. World bank: Established in - 1944, Headquarters - Washington, D.C., U.S.</p>",
                    solution_hi: "<p>13.(c) <strong>डब्ल्यूटीओ (WTO)</strong> (विश्व व्यापार संगठन): गठन - 1995, मुख्यालय - जिनेवा, स्विट्जरलैंड। अंतर्राष्ट्रीय मुद्रा कोष (IMF): स्थापना - 1945, मुख्यालय - वाशिंगटन, डी.सी., अमेरिका । एशियाई विकास बैंक: गठन - 1966, मुख्यालय - फिलीपींस। विश्व बैंक: स्थापना - 1944, मुख्यालय - वाशिंगटन, डी.सी., अमेरिका ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. National Sports University is situated in _________.</p>",
                    question_hi: "<p>14. राष्ट्रीय खेल विश्वविद्यालय कहॉं स्थित है?</p>",
                    options_en: ["<p>Imphal</p>", "<p>Itanagar</p>", 
                                "<p>New Delhi</p>", "<p>Chennai</p>"],
                    options_hi: ["<p>इंफाल</p>", "<p>ईटानगर</p>",
                                "<p>नई दिल्ली</p>", "<p>चेन्नई</p>"],
                    solution_en: "<p>14.(a) <strong>Imphal</strong>. National Sports University: Inaugurated on 16 March 2018, by Narendra Modi. Sports universities :- Lakshmibai National Institute Of Physical Education (Gwalior), Sri Sri Aniruddha Deva Sports University (Dibrugarh, Assam), Netaji Subhas National Institute of Sports (Patiala), Guru Gobind Singh Sports College (Lucknow).</p>",
                    solution_hi: "<p>14.(a) <strong>इंफाल</strong>। राष्ट्रीय खेल विश्वविद्यालय: 16 मार्च 2018 को नरेंद्र मोदी द्वारा उद्घाटन किया गया। खेल विश्वविद्यालय:- लक्ष्मीबाई राष्ट्रीय शारीरिक शिक्षा संस्थान (ग्वालियर), श्री श्री अनिरुद्ध देव खेल विश्वविद्यालय (डिब्रूगढ़, असम), नेताजी सुभाष राष्ट्रीय खेल संस्थान (पटियाला), गुरु गोबिंद सिंह खेल महाविद्यालय (लखनऊ)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. In which of the following years was the National Human Rights Commission formed?</p>",
                    question_hi: "<p>15. निम्नलिखित में से किस वर्ष में, राष्ट्रीय मानवाधिकार आयोग का गठन किया गया था?</p>",
                    options_en: ["<p>1965</p>", "<p>1980</p>", 
                                "<p>2000</p>", "<p>1993</p>"],
                    options_hi: ["<p>1965</p>", "<p>1980</p>",
                                "<p>2000</p>", "<p>1993</p>"],
                    solution_en: "<p>15.(d) <strong>1993</strong>. The National Human Rights Commission (NHRC) is composed of a former chief justice of the Supreme Court of India, a former judge of the Supreme Court, a former chief justice of a High Court and two other members who have knowledge and practical experience in matters relating to human rights.</p>",
                    solution_hi: "<p>15.(d) <strong>1993</strong>. राष्ट्रीय मानवाधिकार आयोग (NHRC) भारत के सर्वोच्च न्यायालय के एक पूर्व मुख्य न्यायाधीश, सर्वोच्च न्यायालय के एक पूर्व न्यायाधीश, एक उच्च न्यायालय के पूर्व मुख्य न्यायाधीश और दो अन्य सदस्यों से बना है जिनके पास मानवाधिकारों से संबंधित मामलों का ज्ञान और व्यावहारिक अनुभव है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Which of the following Articles lays down the duty of the State to raise the level of nutrition and the standard of living and to improve public health?</p>",
                    question_hi: "<p>16. निम्नलिखित में से कौन-सा अनुच्छेद राज्य के पोषण-स्तर और जीवन-स्तर को बढ़ाने और सार्वजनिक स्वास्थ्य में सुधार करने के कर्तव्य को निर्धारित करता है? .</p>",
                    options_en: ["<p>Article 44</p>", "<p>Article 46</p>", 
                                "<p>Article 45</p>", "<p>Article 47</p>"],
                    options_hi: ["<p>अनुच्छेद 44</p>", "<p>अनुच्छेद 46</p>",
                                "<p>अनुच्छेद 45</p>", "<p>अनुच्छेद 47</p>"],
                    solution_en: "<p>16.(d) <strong>Article 47</strong>. Other Important Articles: Article 44 - Uniform civil code for the citizens. Article 45 - Provision for early childhood care and education to children below the age of six years. Article 46 - Promotion of educational and economic interests of Scheduled Castes, Scheduled Tribes and other weaker sections.</p>",
                    solution_hi: "<p>16.(d) <strong>अनुच्छेद 47. </strong>अन्य महत्वपूर्ण अनुच्छेद: अनुच्छेद 44 - नागरिकों के लिए समान नागरिक संहिता। अनुच्छेद 45 - छह वर्ष से कम उम्र के बच्चों के लिए प्रारंभिक बचपन की देखभाल और शिक्षा का प्रावधान। अनुच्छेद 46 - अनुसूचित जाति, अनुसूचित जनजाति और अन्य कमजोर वर्गों के शैक्षिक और आर्थिक हितों को बढ़ावा देना।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17.   Chandrayaan-3 leaves Earth’s surface by burning a huge amount of fuel. The gases released then push the rocket upwards against gravity. Which law of motion is applied here? ",
                    question_hi: "<p>17. चंद्रयान-3, अत्यधिक मात्रा में ईंधन जलाकर पृथ्वी की सतह से प्रस्थान करता है। फिर निकलने वाली गैसें रॉकेट को गुरुत्वाकर्षण के विरुद्ध ऊपर की ओर धकेलती हैं। यहां गति का कौन-सा नियम लागू होता है?</p>",
                    options_en: ["<p>Newton&rsquo;s 1<sup>st</sup> law of motion</p>", "<p>Newton&rsquo;s law of gravitation</p>", 
                                "<p>Newton&rsquo;s 3<sup>rd</sup> law of motion</p>", "<p>Newton&rsquo;s 2<sup>nd</sup> law of motion</p>"],
                    options_hi: ["<p>न्यूटन का गति का पहला नियम</p>", "<p>न्यूटन का गुरुत्वाकर्षण नियम</p>",
                                "<p>न्यूटन का गति का तीसरा नियम</p>", "<p>न्यूटन का गति का दूसरा नियम</p>"],
                    solution_en: "<p>17.(c) The third law of motion states that the force exerted by a first object on a second object is equal and opposite in direction to the force exerted by the second object on the first object. The first law of motion is stated as follows: An object remains at rest or in uniform motion in a straight line unless an attempt is made to change its position by an external force.</p>",
                    solution_hi: "<p>17.(c) गति का तीसरा नियम कहता है कि किसी पहली वस्तु द्वारा दूसरी वस्तु पर लगाया गया बल, दूसरी वस्तु द्वारा पहली वस्तु पर लगाए गए बल के बराबर और विपरीत दिशा में होता है। गति का पहला नियम इस प्रकार बताया गया है: कोई वस्तु तब तक विराम की स्थिति में या एक सीधी रेखा में एकसमान गति में रहती है जब तक कि किसी बाह्य बल द्वारा उसकी स्थिति में परिवर्तन करने का प्रयास न किया जाए ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. At which of the following places did Gandhiji conduct his first experiment of Satyagraha in India? ",
                    question_hi: "<p>18. निम्नलिखित में से किस स्थान पर गांधीजी ने भारत में सत्याग्रह का अपना पहला प्रयोग किया था?</p>",
                    options_en: ["<p>Champaran</p>", "<p>Calcutta</p>", 
                                "<p>Cawnpore</p>", "<p>Ahmedabad</p>"],
                    options_hi: ["<p>चंपारण</p>", "<p>कलकत्ता</p>",
                                "<p>कानपुर</p>", "<p>अहमदाबाद</p>"],
                    solution_en: "<p>18.(a) <strong>Champaran</strong>. Mahatma Gandhi was convinced by Raj Kumar Shukla to visit Champaran in 1917 to abolish the forcible cultivation of Indigo. This bill was introduced by W. Maude. After the bill had passed the land revenue was reduced by 20-26%.</p>",
                    solution_hi: "<p>18.(a) <strong>चंपारण। </strong>राज कुमार शुक्ल ने महात्मा गांधी को नील की जबरन खेती को खत्म करने के लिए 1917 में चंपारण जाने के लिए राजी किया था। यह बिल डब्ल्यू मौड द्वारा पेश किया गया था। विधेयक पारित होने के बाद भू-राजस्व में 20-26% की कमी कर दी गई।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. Which of the following statements about Fundamental Duties is correct?. <br />I) Sardar Swaran Singh recommended the inclusion of a chapter on fundamental duties in the Constitution of India. <br />II) Promotion of cooperative societies is a fundamental duty. ",
                    question_hi: "<p>19. मौलिक कर्तव्यों के संबंध में निम्नलिखित में से कौन सा कथन सही है? <br>I) सरदार स्वर्ण सिंह ने भारत के संविधान में मौलिक कर्तव्यों पर एक अध्याय शामिल करने की अनुशंसा की। <br>II) सहकारी समितियों को बढ़ावा देना एक मौलिक कर्तव्य है।</p>",
                    options_en: ["<p>Only 2</p>", "<p>Both 1 and 2</p>", 
                                "<p>Only 1</p>", "<p>Neither 1 nor 2</p>"],
                    options_hi: ["<p>केवल 2</p>", "<p>1 और 2 दोनों</p>",
                                "<p>केवल 1</p>", "<p>न तो 1 और न ही 2</p>"],
                    solution_en: "<p>19.(c) <strong>Only 1.</strong> Fundamental Duties are mentioned in Part IVA (Article 51 A) of Indian Constitution. Article 51A (a) to abide by the Constitution and respect its ideals and institutions, the National Flag and the National Anthem; (b) to cherish and follow the noble ideals which inspired our national struggle for freedom; (c) to uphold and protect the sovereignty, unity and integrity of India; (d) to defend the country and render national service when called upon to do so.</p>",
                    solution_hi: "<p>19.(c) <strong>केवल 1.</strong> भारतीय संविधान के भाग IVA (अनुच्छेद 51 A) में मौलिक कर्तव्यों का उल्लेख किया गया है। अनुच्छेद 51A (a) संविधान का पालन करना और उसके आदर्शों और संस्थानों, राष्ट्रीय ध्वज और राष्ट्रगान का सम्मान करना; (b) उन महान आदर्शों को संजोना और उनका पालन करना जिन्होंने स्वतंत्रता के लिए हमारे राष्ट्रीय संघर्ष को प्रेरित किया; (c) भारत की संप्रभुता, एकता और अखंडता को बनाए रखना और उसकी रक्षा करना; (d) देश की रक्षा करना और बुलाए जाने पर राष्ट्रीय सेवा प्रदान करना।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. The Multidimensional Poverty Index of India does NOT consider which of the following for calculating the index? ",
                    question_hi: "<p>20 भारत का बहुआयामी गरीबी सूचकांक, सूचकांक की गणना के लिए निम्नलिखित में से किस पर विचार नहीं करता है?</p>",
                    options_en: ["<p>Environmental quality</p>", "<p>Health</p>", 
                                "<p>Education</p>", "<p>Standard of Living</p>"],
                    options_hi: ["<p>पर्यावरणीय गुणवत्ता</p>", "<p>स्वास्थ्य</p>",
                                "<p>शिक्षा</p>", "<p>जीवन स्तर</p>"],
                    solution_en: "<p>20.(a) <strong>Environmental quality. </strong>The National Multidimensional Poverty Index by NITI Aayog uses the internationally acclaimed Alkire Foster methodology with a difference that National MPI covers 12 indicators while global MPI covers 10 indicators. This analysis attempts to study the decline in poverty rates and number of multidimensionally poor people across various time periods.</p>",
                    solution_hi: "<p>20.(a) <strong>पर्यावरणीय गुणवत्ता।</strong> नीति आयोग द्वारा राष्ट्रीय बहुआयामी गरीबी सूचकांक अंतरराष्ट्रीय स्तर पर प्रशंसित अल्किरे फोस्टर पद्धति का उपयोग करता है, अंतर यह है कि राष्ट्रीय MPI 12 संकेतकों को कवर करता है जबकि वैश्विक MPI 10 संकेतकों को कवर करता है। यह विश्लेषण विभिन्न समयावधियों में गरीबी दर और बहुआयामी गरीब लोगों की संख्या में गिरावट का अध्ययन करने का प्रयास करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. Which of the following organizations was founded by Vinayak Damodar Savarkar in 1904?",
                    question_hi: "<p>21. 1904 में, विनायक दामोदर सावरकर ने निम्नलिखित में से किस संगठन की स्थापना की थी?</p>",
                    options_en: ["<p>Free India Society</p>", "<p>Jugantar</p>", 
                                "<p>Indian Independence League</p>", "<p>Abhinav Bharat Society</p>"],
                    options_hi: ["<p>आज़ाद भारत सोसाइटी</p>", "<p>जुगांतर</p>",
                                "<p>इंडियन इंडिपेंडेंस लीग</p>", "<p>अभिनव भारत सोसाइटी</p>"],
                    solution_en: "<p>21.(d) <strong>Abhinav Bharat Society</strong> (Young India Society) was a secret society of revolutionaries. It was established in the Bombay Presidency. It was founded by Vinayak Damodar Savarkar and his brother Ganesh Damodar Savarkar. Vinayak Damodar Savarkar was imprisoned in the Cellular Jail (Andaman Islands) in 1911. His books are &rsquo;The Indian war of independence, 1857&rsquo;, &lsquo;Essentials of Hindutva&rsquo;.</p>",
                    solution_hi: "<p>21.(d) <strong>अभिनव भारत सोसाइटी</strong> (यंग इंडिया सोसायटी) क्रांतिकारियों की एक गुप्त सोसायटी थी। इसकी स्थापना बॉम्बे प्रेसीडेंसी में हुई थी। इसकी स्थापना विनायक दामोदर सावरकर और उनके भाई गणेश दामोदर सावरकर ने की थी। विनायक दामोदर सावरकर को 1911 में सेल्यूलर जेल (अंडमान द्वीप) में कैद कर दिया गया था। \'द इंडियन वॉर ऑफ इंडिपेंडेंस, 1857\', \'एसेंशियल्स ऑफ हिंदुत्व\' उनकी किताबें हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. The first National Games after independence were organized in _________.</p>",
                    question_hi: "<p>22. स्वतंत्रता के बाद पहले राष्ट्रीय खेलों का आयोजन _________ में किया गया था।</p>",
                    options_en: ["<p>Jabalpur</p>", "<p>Madras</p>", 
                                "<p>Lucknow</p>", "<p>New Delhi</p>"],
                    options_hi: ["<p>जबलपुर</p>", "<p>मद्रास</p>",
                                "<p>लखनऊ</p>", "<p>नई दिल्ली</p>"],
                    solution_en: "<p>22.(c) <strong>Lucknow</strong>. National Games: First National Games organised in Lahore (1924). First Modern National Games - New Delhi (1985). They were hosted in various Places, including Bombay and Pune (1994), Bangalore and Mysore (1997), Imphal (1999), Punjab (2001), Andhra Pradesh (2002), Guwahati (2007), Jharkhand (2011), and Gujarat (2022). 37th National Games - Goa ( October 2023).</p>",
                    solution_hi: "<p>22.(c) <strong>लखनऊ</strong>। राष्ट्रीय खेल: प्रथम राष्ट्रीय खेल लाहौर में आयोजित (1924)। प्रथम आधुनिक राष्ट्रीय खेल - नई दिल्ली (1985)। उन्हें बॉम्बे और पुणे (1994), बैंगलोर और मैसूर (1997), इंफाल (1999), पंजाब (2001), आंध्र प्रदेश (2002), गुवाहाटी (2007), झारखंड (2011), और गुजरात (2022) सहित विभिन्न स्थानों पर आयोजित किया गया था। 37वें राष्ट्रीय खेल - गोवा (अक्टूबर 2023)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which of the following is an example of a joint sector industry?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन-सा, संयुक्त क्षेत्र के उद्योग का एक उदाहरण है?</p>",
                    options_en: ["<p>Hindustan Aeronautics Limited</p>", "<p>National Highway Authority of India</p>", 
                                "<p>Steel Authority of India Limited</p>", "<p>Oil India Limited</p>"],
                    options_hi: ["<p>हिंदुस्तान एरोनॉटिक्स लिमिटेड</p>", "<p>भारतीय राष्ट्रीय राजमार्ग प्राधिकरण</p>",
                                "<p>स्टील अथॉरिटी ऑफ़ इंडिया लिमिटेड</p>", "<p>ऑयल इंडिया लिमिटेड</p>"],
                    solution_en: "<p>23.(d) <strong>Oil India Limited.</strong> Joint Sector Industry is one of the classifications of Industries on the basis of ownership. These industries are owned and managed jointly by private firms and government agencies. The main examples of such joint sector companies included Cochin Refineries (1963), Madras Refineries (1965), Madras Fertilisers (1965), Tamil Nadu Fluorine &amp; Allied Chemicals Limited, etc.</p>",
                    solution_hi: "<p>23.(d) <strong>ऑयल इंडिया लिमिटेड।</strong> संयुक्त क्षेत्र उद्योग स्वामित्व के आधार पर उद्योगों के वर्गीकरणों में से एक है। इन उद्योगों का स्वामित्व और प्रबंधन निजी फर्मों और सरकारी एजेंसियों द्वारा संयुक्त रूप से किया जाता है। ऐसी संयुक्त क्षेत्र की कंपनियों के मुख्य उदाहरणों में कोचीन रिफाइनरीज (1963), मद्रास रिफाइनरीज (1965), मद्रास फर्टिलाइजर्स (1965), तमिलनाडु फ्लोरीन एंड अलाइड केमिकल्स लिमिटेड आदि शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Lalitha and Saroja Chidambaram are the noted ____________________ singers.</p>",
                    question_hi: "<p>24. ललिता और सरोजा चिदंबरम विख्यात____________ गायिका हैं।</p>",
                    options_en: ["<p>folk</p>", "<p>Sufi</p>", 
                                "<p>Hindustani classical</p>", "<p>Carnatic</p>"],
                    options_hi: ["<p>लोक (Folk)</p>", "<p>सूफी (Sufi)</p>",
                                "<p>हिन्&zwj;दुस्&zwj;तानी शास्&zwj;त्रीय (Hindustani Classical)</p>", "<p>कर्नाटक (Carnatic)</p>"],
                    solution_en: "<p>24.(d) <strong>Carnatic</strong>. Other personalities related to Carnatic music - Purandaradasa (Father of Carnatic music), Kanaka Dasa, Muthu Thandavar, Tyagaraja, Muthuswami Dikshitar, Syama Sastri, Aruna Sairam, Semmangudi Srinivasa Iyer, Lalgudi Gopala Jayaraman.</p>",
                    solution_hi: "<p>24.(d) <strong>कर्नाटक। </strong>कर्नाटक संगीत से संबंधित अन्य हस्तियाँ - पुरंदरदास (कर्नाटक संगीत के जनक), कनक दास, मुथु थंडावर, त्यागराज, मुथुस्वामी दीक्षितर, श्यामा शास्त्री, अरुणा साईराम, सेम्मानगुडी श्रीनिवास अय्यर, लालगुडी गोपाल जयारमन।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Vijayanagar Emperor Achyuta Deva Raya belonged to the __________ dynasty.</p>",
                    question_hi: "<p>25. विजयनगर के सम्राट अच्युत देव राय __________ वंश के थे।</p>",
                    options_en: ["<p>Sangama</p>", "<p>Saluva</p>", 
                                "<p>Tuluva</p>", "<p>Aravidu</p>"],
                    options_hi: ["<p>संगम</p>", "<p>सुलुव</p>",
                                "<p>तुलुव</p>", "<p>अराविडु</p>"],
                    solution_en: "<p>25.(c) <strong>Tuluva</strong>. Vijayanagara Empire (1336 -1646) - Established by Harihara and Bukka on the banks of river Tungabhadra. Ruled by 4 Dynasties - Sangama (1336-1485), Suluva (1485-1505), Tuluva (1505-1570), Aravidu (1570-1646). Other rulers of Tuluva dynasty: Krishnadevaraya, Sadasiva Raya.</p>",
                    solution_hi: "<p>25.(c) <strong>तुलुव। </strong>विजयनगर साम्राज्य (1336 -1646) - तुंगभद्रा नदी के तट पर हरिहर और बुक्का द्वारा स्थापित। 4 राजवंशों द्वारा शासित - संगम (1336-1485), सुलुव (1485-1505), तुलुव (1505-1570), अराविडु (1570-1646)। तुलुव वंश के अन्य शासक: कृष्णदेवराय, सदाशिव राय।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Who among the following is the Mohiniyattam dancer who has written the book \'Mohiniyattam: Charitravam Attaparakaravam\', and who received the prestigious Kalidas Samman in 1997?</p>",
                    question_hi: "<p>26. निम्नलिखित में से वह कौन-सी मोहिनीअट्टम नृत्यांगना हैं जिन्होंने \'मोहिनीअट्टम: चरित्रवम आट्टापराकरवम\' पुस्तक लिखी है, और जिनको 1997 में प्रतिष्ठित कालिदास सम्मान प्राप्त हुआ?</p>",
                    options_en: ["<p>Kalamandalam Kalyanikutty Amma</p>", "<p>Rukmini Devi Arundale</p>", 
                                "<p>Yamini Krishnamurthy</p>", "<p>Shobhana Narayan</p>"],
                    options_hi: ["<p>कलामंडलम कल्याणिकुट्टी अम्मा</p>", "<p>रुक्मिणी देवी अरुंडेल</p>",
                                "<p>यामिनी कृष्णमूर्ति</p>", "<p>शोभना नारायण</p>"],
                    solution_en: "<p>26.(a) <strong>Kalamandalam Kalyanikutty Amma. </strong>She got several titles and awards which are \"Kavayithri\' title given by Vallathol (1940), Sangeet Natak Akademi fellowship (1974), Kerala Sangeet Natak Akademi fellowship (1974), \'Keerthi Shanku\' title given by Kerala Kalamandalam (1980), \'Nrittapraveena\' title by Kerala Fine Arts Society (1982).</p>",
                    solution_hi: "<p>26.(a) <strong>कलामंडलम कल्याणिकुट्टी अम्मा। </strong>उन्हें कई उपाधियाँ और पुरस्कार मिले, जिनमें वलाथोल द्वारा दी गई \'कवयित्री\' उपाधि (1940), संगीत नाटक अकादमी फ़ेलोशिप (1974), केरल संगीत नाटक अकादमी फ़ेलोशिप (1974), केरल कलामंडलम द्वारा दी गई \'कीर्ति शंकु\' उपाधि (1980), \' केरल फाइन आर्ट्स सोसाइटी द्वारा \'नृतप्रवीणा\' शीर्षक (1982)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Patanjali is well known for the compilation of:</p>",
                    question_hi: "<p>27. पतंजलि __________ के संकलन के लिए विख्यात हैं।</p>",
                    options_en: ["<p>Ayurveda</p>", "<p>Yoga Sutra</p>", 
                                "<p>Bramhasutra</p>", "<p>Panchatantra</p>"],
                    options_hi: ["<p>आयुर्वेद</p>", "<p>योग सूत्र</p>",
                                "<p>ब्रह्मसूत्र</p>", "<p>पंचतंत्र</p>"],
                    solution_en: "<p>27.(b) <strong>Yoga Sutra. </strong>The aim of Pāta&ntilde;jala Yoga (popularly called &lsquo;Raja Yoga&rsquo;) is to attain citta-vṛtti-nirodha (cessation of mental modifications), leading to kaivalya (self-realisation). It is the Yoga for mind management and to realise the self by the process of dhyana (Meditation). Badarayana - Author of &lsquo;Brahma Sutra&rsquo; or &lsquo;Vedanta Sutra&rsquo;. Maharshi Charak - &ldquo;Charaka Samhita\'\' (a famous text of ayurveda).</p>",
                    solution_hi: "<p>27.(b) <strong>योग सूत्र।</strong> पतंजलि योग (जिसे लोकप्रिय रूप से \'राज योग\' कहा जाता है) का उद्देश्य चित्त-वृत्ति-निरोध (मानसिक संशोधनों की समाप्ति) प्राप्त करना है, जिससे कैवल्य (आत्म-साक्षात्कार) प्राप्त होता है। यह मन को नियंत्रित करने और ध्यान की प्रक्रिया द्वारा स्वयं का एहसास करने के लिए योग है। बदरायण - &lsquo;ब्रह्म सूत्र&rsquo; या &lsquo;वेदांत सूत्र&rsquo; के रचयिता। महर्षि चरक - \"चरक संहिता\" (आयुर्वेद का एक प्रसिद्ध ग्रन्थ)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. ________ is the largest slum in Asia.</p>",
                    question_hi: "<p>28. ________ एशिया की सबसे बड़ी झुग्गी बस्ती है।</p>",
                    options_en: ["<p>Dharavi</p>", "<p>Kathputli</p>", 
                                "<p>Baiganwadi</p>", "<p>Banganga</p>"],
                    options_hi: ["<p>धारावी</p>", "<p>कठपुतली</p>",
                                "<p>बैगनवाड़ी</p>", "<p>बाणगंगा</p>"],
                    solution_en: "<p>28.(a) <strong>Dharavi</strong>. The geographical area is just over 2.39 sq. km in size but supports a population of about 300,000 to a million people.</p>",
                    solution_hi: "<p>28.(a) <strong>धारावी। </strong>भौगोलिक क्षेत्र का आकार 2.39 वर्ग किमी से थोड़ा अधिक है, लेकिन यह लगभग 300,000 से दस लाख लोगों की आबादी का समर्थन करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following is an NBFC-MFI? (NBFC-MFI), which operates in joint liability group lending model of rural?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन-सा एक एन.बी.एफ.सी.-एम.एफ.आई. (NBFC-MFI) है, जो ग्रामीण के संयुक्त देयता समूह उधार मॉडल में काम करता है?</p>",
                    options_en: ["<p>Aarohan Financial Services Limited</p>", "<p>Fusion Microfinance Private Limited</p>", 
                                "<p>Equitas Small Finance Bank</p>", "<p>Bandhan Financial Services Private Limited</p>"],
                    options_hi: ["<p>आरोहण फाइनेंशियल सर्विसेज लिमिटेड</p>", "<p>फ्यूजन माइक्रोफाइनेंस प्राइवेट लिमिटेड</p>",
                                "<p>इक्विटास स्मॉल फाइनेंस बैंक</p>", "<p>बंधन फाइनेंशियल सर्विसेज प्राइवेट लिमिटेड</p>"],
                    solution_en: "<p>29.(b) <strong>Fusion Microfinance Private Limited.</strong> Microfinance - A type of banking service provided to those who face difficulties in getting loans from formal financial institutions. Microfinance companies in India- Annapurna Finance (2009), Asirvad Microfinance (2007), Bandhan Financial Services (2001), Fusion Microfinance (2010), Cashpor Micro Credit (2002), Suryoday Small Finance Bank (2008).</p>",
                    solution_hi: "<p>29.(b) <strong>फ्यूजन माइक्रोफाइनेंस प्राइवेट लिमिटेड।</strong> माइक्रोफाइनेंस - एक प्रकार की बैंकिंग सेवा जो उन लोगों को प्रदान की जाती है जिन्हें औपचारिक वित्तीय संस्थानों से ऋण प्राप्त करने में कठिनाइयों का सामना करना पड़ता है। भारत में माइक्रोफाइनेंस कंपनियां - अन्नपूर्णा फाइनेंस (2009), आशीर्वाद माइक्रोफाइनेंस (2007), बंधन फाइनेंशियल सर्विसेज (2001), फ्यूजन माइक्रोफाइनेंस (2010), कैशपोर माइक्रो क्रेडिट (2002), सूर्योदय स्मॉल फाइनेंस बैंक (2008)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who among the following used the term Evergreen Revolution for increasing agricultural production in India?</p>",
                    question_hi: "<p>30. निम्नलिखित में से किसने भारत में कृषि उत्पादन बढ़ाने के लिए सदाबहार क्रांति शब्द का प्रयोग किया था?</p>",
                    options_en: ["<p>Raj Krishna</p>", "<p>RKV Rao</p>", 
                                "<p>MS Swaminathan</p>", "<p>Norman Borlaug</p>"],
                    options_hi: ["<p>राज कृष्ण </p>", "<p>आर.के.वी. राव</p>",
                                "<p>एम.एस. स्वामीनाथन</p>", "<p>नॉर्मन बोरलॉग</p>"],
                    solution_en: "<p>30.(c) <strong>MS Swaminathan</strong> is known as the father of the green revolution in India. Awards - Padma Shri (1967), Ramon Magsaysay Award (1971), Padma Bhushan (1972), World Food Prize (1987), Padma Vibhushan (1989), Bharat Ratna (2024). Norman Borlaug is known as the father of the green revolution in the world.</p>",
                    solution_hi: "<p>30.(c) <strong>एम.एस. स्वामीनाथन </strong>को भारत में हरित क्रांति के जनक के रूप में जाना जाता है। पुरस्कार - पद्म श्री (1967), रेमन मैग्सेसे पुरस्कार (1971), पद्म भूषण (1972), विश्व खाद्य पुरस्कार (1987), पद्म विभूषण (1989), भारत रत्न (2024)। नॉर्मन बोरलॉग को विश्व में हरित क्रांति का जनक कहा जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Cell nucleus was discovered by ______________.</p>",
                    question_hi: "<p>31. कोशिका केंद्रक की खोज ______________ द्वारा की गई थी।</p>",
                    options_en: ["<p>Robert Brown</p>", "<p>Matthias Schleiden</p>", 
                                "<p>Rudolf Virchow</p>", "<p>Robert Hook</p>"],
                    options_hi: ["<p>रॉबर्ट ब्राउन (Robert Brown)</p>", "<p>मैथियास श्लेडेन (Matthias Schleiden)</p>",
                                "<p>रुडोल्फ विरचो (Rudolf Virchow)</p>", "<p>रॉबर्ट हुक (Robert Hook)</p>"],
                    solution_en: "<p>31.(a) <strong>Robert Brown. </strong>The cells of the human cheek have an outer membrane as the delimiting structure of the cell. Inside each cell is a dense membrane bound structure called nucleus. This nucleus contains the chromosomes which in turn contain the genetic material, DNA. The cell was first discovered and named by Rovert Hooke in 1665.</p>",
                    solution_hi: "<p>31.(a) <strong>रॉबर्ट ब्राउन।</strong> मनुष्य की गाल की कोशिका के संगठन में बाहर की तरफ केवल एक झिल्ली संरचना निकलती दिखाई पड़ती है। प्रत्येक कोशिका के अंदर एक घनी झिल्ली से बंधी संरचना होती है जिसे केन्द्रक कहते हैं। इसके केन्द्रक में गुणसूत्र होते हैं जिनमें आनुवंशिक पदार्थ, DNA होता है। कोशिका की खोज और नामकरण सबसे पहले 1665 में रॉबर्ट हुक ने किया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Where is the headquarters of the National Rifle Association of India situated?</p>",
                    question_hi: "<p>32.भारतीय राष्ट्रीय राइफल संघ का मुख्यालय कहाँ स्थित है?</p>",
                    options_en: ["<p>New Delhi</p>", "<p>Bengaluru</p>", 
                                "<p>Kolkata</p>", "<p>Mumbai</p>"],
                    options_hi: ["<p>नई दिल्ली</p>", "<p>बेंगलुरु</p>",
                                "<p>कोलकाता</p>", "<p>मुंबई</p>"],
                    solution_en: "<p>32.(a) <strong>New Delhi. </strong>National Rifle Association of India (NRAI) was founded on 17 April, 1951 with a view to promote and popularize the shooting sports in India as well as for self-defense teaching purposes. The first speaker of Lok Sabha, Sh. G.V. Mavlankar founded the NRAI and served as its inaugural president.</p>",
                    solution_hi: "<p>32.(a) <strong>नई दिल्ली।</strong> राष्ट्रीय राइफल एसोसिएशन ऑफ इंडिया (NRAI) की स्थापना 17 अप्रैल, 1951 को भारत में शूटिंग खेलों को बढ़ावा देने और लोकप्रिय बनाने के साथ-साथ आत्मरक्षा शिक्षण उद्देश्यों के लिए की गई थी। लोकसभा के प्रथम अध्यक्ष श्री. जी.वी. मावलंकर ने NRAI की स्थापना की और इसके उद्घाटन अध्यक्ष के रूप में कार्य किया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Baliyatra is the cultural festival of which Indian state?</p>",
                    question_hi: "<p>33. बालीयात्रा भारत के किस राज्य का सांस्कृतिक उत्सव है?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Odisha</p>", 
                                "<p>Jharkhand</p>", "<p>West Bengal</p>"],
                    options_hi: ["<p>केरल</p>", "<p>ओडिशा</p>",
                                "<p>झारखंड</p>", "<p>पश्चिम बंगाल</p>"],
                    solution_en: "<p>33.(b) <strong>Odisha</strong>. Baliyatra, literally meaning &lsquo;voyage to Bali&rsquo;, is a festival celebrated particularly in Cuttack with great fanfare. It is Asia\'s largest open trade fair. This festival is celebrated every year From the date of Kartika Purnima (full moon day on the day of Kartika) according to the Odia calendar.</p>",
                    solution_hi: "<p>33.(b) <strong>ओडिशा। </strong>बालीयात्रा, जिसका शाब्दिक अर्थ है \'बाली की यात्रा\', विशेष रूप से कटक में बड़ी धूमधाम से मनाया जाने वाला त्योहार है। यह एशिया का सबसे बड़ा खुला व्यापार मेला है। यह त्योहार उड़िया कैलेंडर के अनुसार प्रत्येक वर्ष कार्तिक पूर्णिमा (कार्तिक के दिन पूर्णिमा) की तिथि से मनाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following ministries is the implementing ministry for &lsquo;PM CARES for Children Scheme&rsquo;?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन सा मंत्रालय \'पीएम केयर फॉर चिल्ड्रन स्कीम\' के लिए कार्यान्वयन मंत्रालय है?</p>",
                    options_en: ["<p>Ministry of Social Justice and Empowerment</p>", "<p>Ministry of Health and Family Welfare</p>", 
                                "<p>Ministry of Home Affairs</p>", "<p>Ministry of Women and Child Development</p>"],
                    options_hi: ["<p>सामाजिक न्याय और अधिकारिता मंत्रालय</p>", "<p>स्वास्थ्य और परिवार कल्याण मंत्रालय</p>",
                                "<p>गृह मंत्रालय</p>", "<p>महिला एवं बाल विकास मंत्रालय</p>"],
                    solution_en: "<p>34.(d) <strong>Ministry of Women and Child Development.</strong> The objective of the Scheme is to ensure comprehensive care and protection of Children in a sustained manner, and enable their well being through health insurance, empower them through education and equip them for self-sufficient existence with financial support on reaching 23 years of age.</p>",
                    solution_hi: "<p>34.(d) <strong>महिला एवं बाल विकास मंत्रालय। </strong>योजना का उद्देश्य निरंतर तरीके से बच्चों की व्यापक देखभाल और सुरक्षा सुनिश्चित करना और स्वास्थ्य बीमा के माध्यम से उनकी भलाई को सक्षम करना, उन्हें शिक्षा के माध्यम से सशक्त बनाना और 23 वर्ष की आयु तक पहुंचने पर वित्तीय सहायता के साथ आत्मनिर्भर अस्तित्व के लिए तैयार करना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Who was the author of ‘The Great Indian Novel’?",
                    question_hi: "<p>35. \'द ग्रेट इंडियन नॉवेल\' के लेखक कौन थे?</p>",
                    options_en: [" Anees Salim ", " Anuj Dhar ", 
                                " Salim Khan ", "<p>Shashi Tharoor</p>"],
                    options_hi: ["<p>अनीस सलीम</p>", "<p>अनुज धर</p>",
                                "<p>सलीम खान</p>", "<p>शशि थरूर</p>"],
                    solution_en: "<p>35.(d) <strong>Shashi Tharoor.</strong> Other books: &lsquo;&rsquo;India: From Midnight to the Millennium&rsquo;&rsquo;, &lsquo;&rsquo;Why I Am a Hindu&rsquo;&rsquo;, &lsquo;&rsquo;Nehru: The Invention of India&rsquo;&rsquo;, &lsquo;&rsquo;Show Business&rsquo;&rsquo;, &lsquo;&rsquo;The Five Dollar Smile&rsquo;&rsquo;, &lsquo;&rsquo;The Paradoxical Prime Minister&rsquo;&rsquo;, &lsquo;&rsquo;Tharoorosaurus&rsquo;&rsquo;, &lsquo;&rsquo;Kerala, God\'s Own Country&rsquo;&rsquo;, &lsquo;&rsquo;Riot: A Love Story&rsquo;&rsquo;.</p>",
                    solution_hi: "<p>35.(d) <strong>शशि थरूर।</strong> अन्य पुस्तकें: \'\'इंडिया: फ्रॉम मिडनाइट टू द मिलेनियम\'\', \'\'व्हाई आई एम ए हिंदू\'\', \'\'नेहरू: द इन्वेंशन ऑफ इंडिया\'\', \'\'शो बिजनेस\'\', \'\'द फाइव डॉलर स्माइल\'\' \', \'\'द पैराडॉक्सिकल प्राइम मिनिस्टर\'\', \'\'थारूरोसॉरस\'\', \'\'केरल, गॉड्स ओन कंट्री\'\', \'\'रायट: ए लव स्टोरी\'\'।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which cargo port in India has the biggest capacity from the followings?</p>",
                    question_hi: "<p>36. निम्नलिखित में से, भारत के किस कार्गो बंदरगाह की क्षमता सबसे अधिक है?</p>",
                    options_en: ["<p>Marmagao Port</p>", "<p>Jawaharlal Nehru Port</p>", 
                                "<p>Tuticorin Port</p>", "<p>New Mangalore Port</p>"],
                    options_hi: ["<p>मोरमुगाओ बंदरगाह</p>", "<p>जवाहरलाल नेहरू बंदरगाह</p>",
                                "<p>तूतीकोरिन बंदरगाह</p>", "<p>न्यूमैंगलोर बंदरगाह</p>"],
                    solution_en: "<p>36.(b) <strong>Jawaharlal Nehru Port </strong>(Nhava Sheva Port) (opened in 1989) is the largest container port in India. Other famous ports: Mundra (Kutch, Gujarat), Chennai (Tamil Nadu), Syama Prasad Mukherjee Port (Kolkata, West Bengal), Chidambaranar (Tuticorin, Tamil Nadu), Deendayal Port (Kandla, Gujarat), Visakhapatnam (Andhra Pradesh), Cochin (Kochi, Kerala), Hazira (Gujarat), Paradip (Odisha).</p>",
                    solution_hi: "<p>36.(b) <strong>जवाहरलाल नेहरू बंदरगाह </strong>(न्हावा शेवा बंदरगाह) (1989 में खोला गया) भारत का सबसे बड़ा कंटेनर बंदरगाह है। अन्य प्रसिद्ध बंदरगाह: मुंद्रा (कच्छ, गुजरात), चेन्नई (तमिलनाडु), श्यामा प्रसाद मुखर्जी बंदरगाह (कोलकाता, पश्चिम बंगाल), चिदंबरनार (तूतीकोरिन, तमिलनाडु), दीनदयाल बंदरगाह (कांडला, गुजरात), विशाखापत्तनम (आंध्र प्रदेश), कोचीन (कोच्चि, केरल), हजीरा (गुजरात), पारादीप (ओडिशा)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Part IV-A of the Constitution of India contains ___________________ Articles.</p>",
                    question_hi: "<p>37. भारत के संविधान के भाग IV-A में _________________ अनुच्&zwj;छेद शामिल हैं।</p>",
                    options_en: ["<p>four</p>", "<p>three</p>", 
                                "<p>one</p>", "<p>two</p>"],
                    options_hi: ["<p>चार</p>", "<p>तीन</p>",
                                "<p>एक</p>", "<p>दो</p>"],
                    solution_en: "<p>37.(c) <strong>one.</strong> Fundamental duties: Article 51A (f) to value and preserve the rich heritage of our composite culture; (g) to protect and improve the natural environment including forests, lakes, rivers and wildlife, and to have compassion for living creatures; (h) to develop the scientific temper, humanism and the spirit of inquiry and reform; (i) to safeguard public property and to abjure violence.</p>",
                    solution_hi: "<p>37.(c) <strong>एक।</strong> मौलिक कर्तव्य: अनुच्छेद 51A (f) हमारी समग्र संस्कृति की समृद्ध विरासत को महत्व देना और संरक्षित करना; (g) वनों, झीलों, नदियों और वन्य जीवन सहित प्राकृतिक पर्यावरण की रक्षा और सुधार करना और जीवित प्राणियों के प्रति दया रखना; (h) वैज्ञानिक स्वभाव, मानवतावाद और जांच और सुधार की भावना विकसित करना; (i) सार्वजनिक संपत्ति की रक्षा करना और हिंसा का त्याग करना।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Phytoplanktons are ________.</p>",
                    question_hi: "<p>38. पादपप्&zwj;लवक (फाइटोप्लांकटन) ________ होते हैं।</p>",
                    options_en: ["<p>primary consumers of lake ecosystem</p>", "<p>tertiary consumers of pond ecosystem</p>", 
                                "<p>producers in pond ecosystem</p>", "<p>secondary consumers of ocean ecosystem</p>"],
                    options_hi: ["<p>झील पारिस्थितिकी तंत्र के प्राथमिक उपभोक्ता</p>", "<p>तालाब पारिस्थितिकी तंत्र के तृतीयक उपभोक्ता</p>",
                                "<p>तालाब पारिस्थितिकी तंत्र में उत्पादक</p>", "<p>महासागर पारिस्थितिकी तंत्र के द्वितीयक उपभोक्ता</p>"],
                    solution_en: "<p>38.(c) <strong>Producers in pond ecosystem. </strong>The autotrophic components include the phytoplankton, some algae and the floating, submerged and marginal plants found at the edges. The consumers are represented by the zooplankton, the free swimming and bottom dwelling forms. The decomposers are the fungi, bacteria and flagellates especially abundant in the bottom of the pond.</p>",
                    solution_hi: "<p>38.(c) <strong>तालाब पारिस्थितिकी तंत्र में उत्पादक। </strong>स्वपोषी घटकों में पादपप्&zwj;लवक (फाइटोप्लांकटन), कुछ शैवाल और किनारों पर पाए जाने वाले तैरते, जलमग्न और सीमांत पौधे शामिल हैं। उपभोक्ताओं को ज़ोप्लांकटन, फ्री-स्विमिंग और निचले आवास रूपों द्वारा दर्शाया जाता है। अपघटक कवक, बैक्टीरिया और फ्लैगेलेट्स हैं जो विशेष रूप से तालाब के तल में प्रचुर मात्रा में पाए जाते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Who among the following was the founder of Raigarh gharana of Kathak?</p>",
                    question_hi: "<p>39. कथक के रायगढ़ घराने के संस्थापक निम्नलिखित में से कौन थे?</p>",
                    options_en: ["<p>Raja Chakradhar Singh</p>", "<p>Shambhu Maharaj</p>", 
                                "<p>Ishwari Prasad</p>", "<p>Janaki Prasad</p>"],
                    options_hi: ["<p>राजा चक्रधर सिंह</p>", "<p>शंभू महाराज</p>",
                                "<p>ईश्वरी प्रसाद</p>", "<p>जानकी प्रसाद</p>"],
                    solution_en: "<p>39.(a) <strong>Raja Chakradhar Singh.</strong> Raigarh is known as the &lsquo;Cultural capital of Chhattisgarh&rsquo;, Raigarh is famous for its dance form &ldquo;Kathak&rdquo; (as Raigarh Gharana) and classical music. Credit goes to Maharaja Chakradhar Singh under whose patronage Raigarh flourished as a centre of art and culture. Every year on the occasion of festival Ganesh Puja, Chakradhar Samaroh is organized in Raigarh.</p>",
                    solution_hi: "<p>39.(a) <strong>राजा चक्रधर सिंह।</strong> रायगढ़ को \'छत्तीसगढ़ की सांस्कृतिक राजधानी\' के रूप में जाना जाता है, रायगढ़ अपने नृत्य रूप \"कथक\" (रायगढ़ घराने के रूप में) और शास्त्रीय संगीत के लिए प्रसिद्ध है। इसका श्रेय महाराजा चक्रधर सिंह को जाता है जिनके संरक्षण में रायगढ़ कला और संस्कृति के केंद्र के रूप में विकसित हुआ। प्रतिवर्ष गणेश पूजा उत्सव के अवसर पर रायगढ़ में चक्रधर समारोह का आयोजन किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The Strait of Gibraltar connects which of the following Ocean with the Mediterranean Sea and separates Spain on the European continent from Morocco on the African continent?</p>",
                    question_hi: "<p>40. जिब्राल्टर जलडमरूमध्य निम्नलिखित में से किस महासागर को भूमध्य सागर से जोड़ता है और यूरोपीय महाद्वीप पर स्पेन को अफ्रीकी महाद्वीप पर मोरक्को से अलग करता है? </p>",
                    options_en: ["<p>Indian Ocean</p>", "<p>Pacific Ocean</p>", 
                                "<p>Southern Ocean</p>", "<p>Atlantic Ocean</p>"],
                    options_hi: ["<p>हिंद महासागर</p>", "<p>प्रशांत महासागर</p>",
                                "<p>दक्षिणी महासागर</p>", "<p>अटलांटिक महासागर</p>"],
                    solution_en: "<p>40.(d) <strong>Atlantic Ocean. </strong>The Mediterranean sea is an intercontinental sea located between Europe, North Africa, and Western Asia and connected to the Atlantic Ocean through the strait of Gibraltar. Name of Straits and Joining Seas - Sunda Strait (Java Sea &amp; Indian Ocean), Mesina Strait ( island of Sicily and the southern tip of mainland Italy), Bab-el-Mandeb Strait (Red Sea &amp; Gulf of Aden), North Channel (Irish Sea &amp; Atlantic Ocean).</p>",
                    solution_hi: "<p>40.(d) <strong>अटलांटिक महासागर।</strong> भूमध्य सागर एक अंतरमहाद्वीपीय समुद्र है जो यूरोप, उत्तरी अफ्रीका और पश्चिमी एशिया के बीच स्थित है और जिब्राल्टर जलडमरूमध्य के माध्यम से अटलांटिक महासागर से जुड़ा है। जलडमरूमध्य और जुड़ने वाले समुद्रों के नाम - सुंडा जलडमरूमध्य (जावा सागर और हिंद महासागर), मेसिना जलडमरूमध्य (सिसिली द्वीप और मुख्य भूमि इटली का दक्षिणी सिरा), बाब-अल-मंडेब जलडमरूमध्य (लाल सागर और अदन की खाड़ी), उत्तरी चैनल ( आयरिश सागर और अटलांटिक महासागर)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "41. Rajya Sabha enjoys which special power under Article 312 of the Constitution of India? ",
                    question_hi: "<p>41. राज्यसभा को भारत के संविधान के अनुच्छेद 312 के तहत कौन सी विशेष शक्ति प्राप्त है?</p>",
                    options_en: [" To put restriction on Government ", " To appoint members of Human Rights Commission ", 
                                "  To create new All India service ", " To extend special rights to certain people"],
                    options_hi: ["<p>सरकार पर प्रतिबंध लगाने की</p>", "<p>मानवाधिकार आयोग के सदस्यों की नियुक्ति करने की</p>",
                                "<p>नई अखिल भारतीय सेवा का सृजन करने की</p>", "<p>कुछ लोगों के लिए विशेष अधिकारों का विस्तार करने की</p>"],
                    solution_en: "<p>41.(c) Part XIV - Services under the Union and States. Important Articles: Article 309 - Recruitment and conditions of service of persons serving the Union or a State. Article 310 - Tenure of office of persons serving the Union or a State. Article 311 - Dismissal, removal or reduction in rank of persons employed in civil capacities under the Union or a State.</p>",
                    solution_hi: "<p>41.(c) भाग XIV - संघ और राज्यों के अधीन सेवाएँ। महत्वपूर्ण अनुच्छेद : अनुच्छेद 309 - संघ या राज्य की सेवा करने वाले व्यक्तियों की भर्ती और सेवा की शर्तें। अनुच्छेद 310 - संघ या राज्य की सेवा करने वाले व्यक्तियों के पद का कार्यकाल। अनुच्छेद 311 - संघ या राज्य के अधीन नागरिक क्षमताओं में कार्यरत व्यक्तियों की बर्खास्तगी, निष्कासन या पद में कमी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following is the outcome of globalization of a country&rsquo;s economy?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन-सा किसी देश की अर्थव्यवस्था के वैश्वीकरण का परिणाम है?</p>",
                    options_en: ["<p>Outsourcing</p>", "<p>Monetary policy reforms</p>", 
                                "<p>Dis-investment</p>", "<p>Fiscal policy reforms</p>"],
                    options_hi: ["<p>आउटसोर्सिंग</p>", "<p>मौद्रिक नीति सुधार</p>",
                                "<p>विनिवेश</p>", "<p>राजकोषीय नीति में सुधार</p>"],
                    solution_en: "<p>42.(a) <strong>Outsourcing. </strong>Globalization is the integration between countries through foreign trade and foreign investments by multinational corporations (MNCs). Peter Sutherland is known as the \'father of globalization\'. Globalization in India (1991) was done by Manmohan Singh under leadership of PV Narasimha Rao. The three main pillars of Reform were: Liberalization, Globalization, and Privatization.</p>",
                    solution_hi: "<p>42.(a) <strong>आउटसोर्सिंग। </strong>वैश्वीकरण बहुराष्ट्रीय निगमों (MNC) द्वारा विदेशी व्यापार और विदेशी निवेश के माध्यम से देशों के बीच एकीकरण है। पीटर सदरलैंड को \'वैश्वीकरण के जनक\' के रूप में जाना जाता है। भारत में वैश्वीकरण (1991) पी वी नरसिम्हा राव के नेतृत्व में मनमोहन सिंह द्वारा किया गया था। सुधार के तीन मुख्य स्तंभ थे: उदारीकरण, वैश्वीकरण और निजीकरण।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Music composer Debojyoti Mishra has won the Best Music Director award at the 20th Imagine India Film Festival in ________________________ for his work in film &lsquo;Bansuri: The Flute&rsquo;.</p>",
                    question_hi: "<p>43. संगीतकार देवज्योति मिश्रा ने फिल्म \'बांसुरी: द फ्लूट\' में अपने काम के लिए _______________में 20वें इमेजिन इंडिया फिल्म फेस्टिवल में सर्वश्रेष्ठ संगीत निर्देशक का पुरस्कार जीता है।</p>",
                    options_en: ["<p>Spain</p>", "<p>France</p>", 
                                "<p>Germany</p>", "<p>Italy</p>"],
                    options_hi: ["<p>स्&zwj;पेन</p>", "<p>फ्रांस</p>",
                                "<p>जर्मनी</p>", "<p>इटली</p>"],
                    solution_en: "<p>43.(a) <strong>Spain. </strong>Debojyoti Mishra is an Indian music director and film composer. He became popular with his minimalist classical compositions for the Hindi film Raincoat, directed by Rituparno Ghosh</p>",
                    solution_hi: "<p>43.(a) <strong>स्&zwj;पेन।</strong> देबज्योति मिश्रा एक भारतीय संगीत निर्देशक और फिल्म संगीतकार हैं। वह रितुपर्णो घोष द्वारा निर्देशित हिंदी फिल्म रेनकोट के लिए अपनी न्यूनतम शास्त्रीय रचनाओं से लोकप्रिय हो गए।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Consider the statements given below and choose the correct option regarding Indian folk and tribal dance styles.<br>i) Chailam is a folk dance of Mizoram.<br>ii) Hojagiri is a folk dance of Gujarat.<br>iii) Bidesiya is a folk dance of Bihar.</p>",
                    question_hi: "<p>44. नीचे दिए गए कथनों पर विचार कीजिए और भारतीय लोक और जनजातीय नृत्य शैलियों के संबंध में सही विकल्प का चयन कीजिए। <br>i) चैलम (Chailam), मिजोरम का लोक नृत्य है। <br>ii) होजागिरी, गुजरात का लोक नृत्य है। <br>iii) बिदेसिया, बिहार का लोक नृत्य है।</p>",
                    options_en: ["<p>Only (ii) is correct.</p>", "<p>Both (i) and (iii) are correct.</p>", 
                                "<p>Both (ii) and (iii) are correct.</p>", "<p>Only (i) is correct.</p>"],
                    options_hi: ["<p>केवल (ii) सही है।</p>", "<p>(i) और (iii), दोनों सही हैं।</p>",
                                "<p>(ii) और (iii), दोनों सही हैं।</p>", "<p>केवल (i) सही है।</p>"],
                    solution_en: "<p>44.(b) <strong>Both (i) and (iii) are correct. </strong>Folk dances of Indian states: Mizoram - Cheraw, Khuallam, Bamboo dance. West Bengal - Chhau, Mundari, Gambhira, Baul, Gajan, Kirtan, Rabha, Chaibari Nritya. Bihar - Jat -Jatin, Bidesiya, Domkach, Fagua. Gujarat - Palli Jag Garbo, Kahalya, Maniaro Raas / Kanabi Raas, Hudo, Vinchhudo.</p>",
                    solution_hi: "<p>44.(b) <strong>(i) और (iii), दोनों सही हैं।</strong> भारतीय राज्यों के लोक नृत्य: मिजोरम - चेराव, खुआल्लम, बांस नृत्य। पश्चिम बंगाल - छाऊ, मुंडारी, गंभीरा, बाउल, गाजन, कीर्तन, राभा, चैबारी नृत्य। बिहार - जट -जतिन, बिदेसिया, डोमकच, फगुआ। गुजरात - पल्ली जग गरबो, कहल्या, मनियारो रास/कनाबी रास, हुडो, विंचुडो।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "45. If NDP at factor cost = ₹1,050 crore <br />Subsidies = ₹40 crore <br />Net factor income from abroad = ₹10 crore <br />How much is NNP at factor cost?",
                    question_hi: "<p>45. यदि कारक लागत पर एन.डी.पी. = ₹1,050 करोड़ <br>सब्सिडी = ₹40 करोड़ <br>विदेश से शुद्ध कारक आय = ₹10 करोड़ है, तो कारक लागत पर एन.एन.पी. कितना है?</p>",
                    options_en: [" ₹1,060 crore ", "<p>₹1,100 crore</p>", 
                                "<p>₹1,090 crore</p>", "<p>₹1,040 crore</p>"],
                    options_hi: ["<p>₹1,060 करोड़</p>", "<p>₹1,100 करोड़</p>",
                                "<p>₹1,090 करोड़</p>", "<p>₹1,040 करोड़</p>"],
                    solution_en: "<p>45.(a) <strong>₹1,060 crore. </strong>Net national product value obtained by deduction of depreciation from GNP. NNP at factor cost (National Income) - Net values of goods and services contributed by all producers in the local territory of the country plus net factor income from outside. Net value added - Deduct the depreciation value from gross value added.</p>",
                    solution_hi: "<p>45.(a) <strong>₹1,060 करोड़। </strong>GNP से मूल्यह्रास की कटौती से प्राप्त शुद्ध राष्ट्रीय उत्पाद मूल्य। कारक लागत पर NNP (राष्ट्रीय आय) - देश के स्थानीय क्षेत्र में सभी उत्पादकों द्वारा योगदान की गई वस्तुओं और सेवाओं का शुद्ध मूल्य और बाहर से शुद्ध कारक आय। शुद्ध जोड़ा गया मूल्य - सकल जोड़े गए मूल्य से मूल्यह्रास मूल्य घटाएं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. In which of the following Articles of the Constitution of India is it mentioned that &ldquo;it shall be the duty of every citizen of India to abide by the Constitution and respect its ideals and institutions, the National Flag and the National Anthem&rdquo;?</p>",
                    question_hi: "<p>46. भारत के संविधान के निम्नलिखित में से किस अनुच्छेद में यह उल्लेख किया गया है कि \"यह भारत के प्रत्येक नागरिक का कर्तव्य होगा कि वह संविधान का पालन करे और उसके आदर्शों, संस्थाओं, राष्ट्र ध्वज और राष्ट्रगान का आदर करे\"?</p>",
                    options_en: ["<p>51A (f)</p>", "<p>51A (a)</p>", 
                                "<p>51A (k)</p>", "<p>51A (j)</p>"],
                    options_hi: ["<p>51A (f)</p>", "<p>51A (a)</p>",
                                "<p>51A (k)</p>", "<p>51A (j)</p>"],
                    solution_en: "<p>46.(b) <strong>51A (a).</strong> Fundamental Duties were added to the Constitution by 42nd Amendment Act of the Constitution in 1976 on the recommendation of the Swaran Singh Committee. Article 51(A) describes 11 fundamental duties. 10 fundamental duties came with the 42nd Amendment; the 11th fundamental duty was added by the 86th Amendment in 2002.</p>",
                    solution_hi: "<p>46.(b) <strong>51A (a) </strong>स्वर्ण सिंह समिति की सिफ़ारिश पर 1976 में संविधान के 42वें संशोधन अधिनियम द्वारा मौलिक कर्तव्यों को संविधान में जोड़ा गया। अनुच्छेद 51(A) में 11 मौलिक कर्तव्यों का वर्णन है। 42वें संशोधन के साथ 10 मौलिक कर्तव्य शामिल किए गए थे; तथा 2002 में 86वें संशोधन द्वारा 11वां मौलिक कर्तव्य जोड़ा गया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "47. Showcasing its wildlife and religious sites at the 74th parade, which of the following states’ tableau won 1st prize at the 2023 Republic Day? ",
                    question_hi: "47. 74वीं परेड में अपने वन्य जीवन और धार्मिक स्थलों को प्रदर्शित करते हुए, निम्नलिखित में से किस राज्य की झाँकी ने 2023 गणतंत्र दिवस पर प्रथम पुरस्कार जीता? ",
                    options_en: [" Jharkhand ", "  Uttarakhand", 
                                "  Gujarat", " Arunachal Pradesh"],
                    options_hi: ["<p>झारखंड</p>", "<p>उत्तराखंड</p>",
                                "<p>गुजरात</p>", "<p>अरुणाचल प्रदेश</p>"],
                    solution_en: "<p>47.(b) <strong>Uttarakhand. </strong>Republic Day, in India, national holiday celebrated annually to commemorate the adoption of the constitution of India on January 26, 1950. Between 1950 and 1954, Republic Day parades were held at Irwin Stadium (now National Stadium), Kingsway, Red Fort and Ramlila Maidan. King Tribhuvan Bir Bikram Shah of Nepal - Second Chief Guest of the Republic Day Parade in 1951.</p>",
                    solution_hi: "<p>47.(b) <strong>उत्तराखंड। </strong>भारत में गणतंत्र दिवस, 26 जनवरी 1950 को भारत के संविधान को अपनाने के उपलक्ष्य में प्रतिवर्ष मनाया जाने वाला राष्ट्रीय अवकाश है। 1950 और 1954 के बीच, गणतंत्र दिवस परेड इरविन स्टेडियम (अब नेशनल स्टेडियम), किंग्सवे, लाल किला और रामलीला मैदान में आयोजित की गईं। नेपाल के राजा त्रिभुवन बीर बिक्रम शाह - 1951 में गणतंत्र दिवस परेड के दूसरे मुख्य अतिथि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following dynasties ruled Magadha just before Chandragupta Maurya established the Maurya Empire in ancient India?</p>",
                    question_hi: "<p>48. चंद्रगुप्त मौर्य द्वारा प्राचीन भारत में मौर्य साम्राज्य की स्थापना के ठीक पहले निम्नलिखित में से किस राजवंश ने मगध पर शासन किया था?</p>",
                    options_en: ["<p>Brihadratha</p>", "<p>Pradyot</p>", 
                                "<p>Shishunaga</p>", "<p>Nand</p>"],
                    options_hi: ["<p>बृहद्रथ</p>", "<p>प्रद्योत</p>",
                                "<p>शिशुनाग</p>", "<p>नंद</p>"],
                    solution_en: "<p>48.(d) <strong>Nand. </strong>Mahapadma Nanda established the Nanda dynasty in Eastern Magadha. He established Pataliputra (Patna, Bihar) as his kingdom\'s capital. He is called the &ldquo;first historical emperor of India.&rdquo; Magadha Empire was ruled by following dynasties: Haryanka Dynasty (Bimbisara), Shishunaga Dynasty (Shishunaga), and Nanda Dynasty (Mahapadma Nanda). Alexander invaded North-Western India in 326 BC during the reign of Dhana Nanda (last emperor of Nanda).</p>",
                    solution_hi: "<p>48.(d) <strong>नंद।</strong> महापद्म नंद ने पूर्वी मगध में नंद वंश की स्थापना की। उन्होंने पाटलिपुत्र (पटना, बिहार) को अपने राज्य की राजधानी के रूप में स्थापित किया। उन्हें \"भारत का पहला ऐतिहासिक सम्राट\" कहा जाता है। मगध साम्राज्य पर निम्नलिखित राजवंशों का शासन था: हर्यक राजवंश (बिम्बिसार), शिशुनाग राजवंश (शिशुनाग), और नंद राजवंश (महापद्म नंद)। सिकंदर ने 326 ईसा पूर्व में धनानंद (नंद वंश के अंतिम सम्राट) के शासनकाल के दौरान उत्तर-पश्चिमी भारत पर आक्रमण किया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Match the columns with respect to the ranking of the given countries on the Human Development Index 2021/2022 out of 191 countries.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742296372299.png\" alt=\"rId6\" height=\"170\"></p>",
                    question_hi: "<p>49. मानव विकास सूचकांक 2021/2022 में 191 देशों में से दिए गए देशों के स्थान के संबंध में स्तंभों का मिलान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742296372450.png\" alt=\"rId7\" width=\"219\" height=\"170\"></p>",
                    options_en: [" 1-c, 2-b, 3-a", "  1-a, 2-c, 3-b", 
                                "<p>1-a, 2-b, 3-с</p>", "<p>1-c, 2-a, 3-b</p>"],
                    options_hi: ["<p>1-c, 2-b, 3-a</p>", "<p>1-a, 2-c, 3-b</p>",
                                "<p>1-a, 2-b, 3-с</p>", "<p>1-c, 2-a, 3-b</p>"],
                    solution_en: "<p>49.(a) <strong>1-c, 2-b, 3-a. </strong>Human Development Report (HDR): First released in 1990. It is published by the Human Development Report Office for the United Nations Development Programme (UNDP). Goal: To contribute toward the expansion of opportunities, choice and freedom.</p>",
                    solution_hi: "<p>49.(a) <strong>1-c, 2-b, 3-a. </strong>मानव विकास रिपोर्ट (HDR): पहली बार 1990 में जारी की गई। इसे संयुक्त राष्ट्र विकास कार्यक्रम (UNDP) के मानव विकास रिपोर्ट कार्यालय द्वारा प्रकाशित किया जाता है। लक्ष्य: अवसरों, विकल्प और स्वतंत्रता के विस्तार में योगदान करना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "<p>50. Chitrakoot Falls is a natural waterfall known as Niagara Falls of India, located on the _______ river in the state of Chhattisgarh, India.</p>",
                    question_hi: "<p>50. चित्रकोट जलप्रपात भारत के नियाग्रा जलप्रपात के रूप में जाना जाने वाला एक प्राकृतिक जलप्रपात है, जो भारत के छत्तीसगढ़ राज्य में _______ नदी पर स्थित है।</p>",
                    options_en: ["<p>Varahi River</p>", "<p>Indravati River</p>", 
                                "<p>Mandovi River</p>", "<p>Chandravanka River</p>"],
                    options_hi: ["<p>वराही नदी</p>", "<p>इंद्रावती नदी</p>",
                                "<p>मांडवी नदी</p>", "<p>चंद्रवंका नदी</p>"],
                    solution_en: "<p>50.(b) <strong>Indravati River. </strong>Waterfalls in india: Dudhsagar Falls (Goa), Athirappilly Water Falls (Kerala), Jog Falls ( Karnataka), Nohkalikai Falls (Meghalaya), Talakona Waterfall (Andhra Pradesh), Jonha Falls (Jharkhand), Suruli Falls (Tamil Nadu), Badaghagara Waterfall (Odisha).</p>",
                    solution_hi: "<p>50.(b) <strong>इंद्रावती नदी। </strong>भारत में झरने: दूधसागर झरना (गोवा), अथिराप्पिल्ली झरना (केरल), जोग झरना (कर्नाटक), नोहकलिकाई झरना (मेघालय), तालाकोना झरना (आंध्र प्रदेश), जोन्हा झरना (झारखंड), सुरुली झरना (तमिलनाडु), बडाघागरा झरना (ओडिशा)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>