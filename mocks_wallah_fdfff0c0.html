<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which among the following organizations is the best example of an agreement between oligopolists?</p>",
                    question_hi: "<p>1. निम्नलिखित में कौन सा संगठन ऑलिगोपॉलिस्ट्स (oligopolists) के बीच के अनुबंध का सर्वोत्तम उदाहरण है?</p>",
                    options_en: ["<p>OPEC</p>", "<p>UNO</p>", 
                                "<p>UNESCO</p>", "<p>WHO</p>"],
                    options_hi: ["<p>OPEC</p>", "<p>UNO</p>",
                                "<p>UNESCO</p>", "<p>WHO</p>"],
                    solution_en: "<p>1.(a) <strong>OPEC </strong>(Organization of the Petroleum Exporting Countries) is a permanent intergovernmental organization of oil-exporting developing nations that coordinates and unifies the petroleum policies of its Member Countries. Founded - 1960; Headquarters - Vienna, Austria.</p>",
                    solution_hi: "<p>1.(a) <strong>OPEC </strong>(पेट्रोलियम निर्यातक देशों का संगठन) तेल निर्यातक विकासशील देशों का एक स्थायी अंतरसरकारी संगठन है जो अपने सदस्य देशों को पेट्रोलियम नीतियों का निर्देशांक और एकजुट करता है। स्थापना - 1960; मुख्यालय - वियना, ऑस्ट्रिया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. International Fund for Agricultural Development (IFAD) was created in _______ for rural poverty reduction in developing countries.</p>",
                    question_hi: "<p>2. विकासशील देशों में ग्रामीण गरीबी को कम करने के लिए वर्ष _______ में इंटरनेशनल फंड फॉर एग्रीकल्चरल डेवलपमेंट (IFAD) का गठन किया गया था।</p>",
                    options_en: ["<p>1990</p>", "<p>1980</p>", 
                                "<p>1977</p>", "<p>1995</p>"],
                    options_hi: ["<p>1990</p>", "<p>1980</p>",
                                "<p>1977</p>", "<p>1995</p>"],
                    solution_en: "<p>2.(c) <strong>1977</strong>. International Fund for Agricultural Development (<strong>IFAD</strong>) is the only multilateral development organization that focuses solely on rural economies and food security. <strong>Headquarters </strong>- Rome, Italy. Parent organization - United Nations Economic and Social Council.</p>",
                    solution_hi: "<p>2.(c) <strong>1977</strong>। इंटरनेशनल फंड फॉर एग्रीकल्चरल डेवलपमेंट (<strong>IFAD</strong>) एकमात्र बहुपक्षीय विकास संगठन है जो पूरी तरह से ग्रामीण अर्थव्यवस्था और खाद्य सुरक्षा पर ध्यान केंद्रित करता है। <strong>मुख्यालय </strong>- रोम, इटली। मूल संगठन - संयुक्त राष्ट्र आर्थिक एवं सामाजिक परिषद।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. WTO sets the rules for:</p>",
                    question_hi: "<p>3. विश्व व्यापार संगठन इसके लिए नियम निर्धारित करता है:</p>",
                    options_en: ["<p>environment</p>", "<p>global trade</p>", 
                                "<p>terrorism</p>", "<p>global tour &amp; travel</p>"],
                    options_hi: ["<p>पर्यावरण</p>", "<p>वैश्विक व्यापार</p>",
                                "<p>आतंकवाद</p>", "<p>वैश्विक दौरा</p>"],
                    solution_en: "<p>3.(b) <strong>Global trade.</strong> The World Trade Organization (1 January 1995) - Organization that regulates and facilitates international trade between nations. <strong>Headquarters </strong>- Geneva, Switzerland. Environmental issues address by the United Nations Environment Programme. <strong>The Security Council -</strong> Counter - Terrorism Committee (CTC) sets the rules for terrorism.</p>",
                    solution_hi: "<p>3.(b) <strong>वैश्विक व्यापार</strong>। विश्व व्यापार संगठन (1 जनवरी 1995) - संगठन जो राष्ट्रों के बीच अंतर्राष्ट्रीय व्यापार को विनियमित और सुविधाजनक बनाता है। <strong>मुख्यालय </strong>- जिनेवा, स्विट्जरलैंड। संयुक्त राष्ट्र पर्यावरण कार्यक्रम द्वारा पर्यावरणीय मुद्दों का समाधान किया जाता है ।<strong> सुरक्षा परिषद -</strong> आतंकवाद रोधी समिति (CTC) आतंकवाद के लिए नियम लागू करती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The International Bank for Reconstruction and Development, an institution of the World Bank, was established in which year?</p>",
                    question_hi: "<p>4. विश्व बैंक की संस्था इंटरनेशनल बैंक फॉर रिकंस्ट्रक्शन एंड डेवलपमेंट की स्थापना किस वर्ष की गई थी?</p>",
                    options_en: ["<p>1944</p>", "<p>1946</p>", 
                                "<p>1945</p>", "<p>1943</p>"],
                    options_hi: ["<p>1944</p>", "<p>1946</p>",
                                "<p>1945</p>", "<p>1943</p>"],
                    solution_en: "<p>4.(a) <strong>1944. World Bank:-</strong> Headquarters - Washington, D.C., United States. Member Countries - 189 countries. <strong>Aim </strong>- To reduce poverty in middle - income and creditworthy poorer countries by promoting sustainable development through loans, guarantees and risk management products.</p>",
                    solution_hi: "<p>4.(a) <strong>1944 । विश्व बैंक:-</strong> मुख्यालय - वाशिंगटन, डी.सी., संयुक्त राज्य अमेरिका। सदस्य देश - 189 देश। <strong>उद्देश्य </strong>- ऋण, गारंटी और जोखिम प्रबंधन उत्पादों के माध्यम से सतत विकास को बढ़ावा देकर मध्यम आय और ऋण योग्य गरीब देशों में गरीबी को कम करना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following statements about the Association of Southeast Asian Nations(ASEAN) is INCORRECT?</p>",
                    question_hi: "<p>5. दक्षिण पूर्व एशियाई राष्ट्र संघ (ASEAN) के संबंध में निम्नलिखित में से कौन सा कथन गलत है?</p>",
                    options_en: ["<p>It started with three original member countries.</p>", "<p>It comprises a group of ten member states as of 2022.</p>", 
                                "<p>An aim of the Association is to accelerate economic growth, social progress and cultural development in the region.</p>", "<p>ASEAN was established on 8 August 1967 in Bangkok.</p>"],
                    options_hi: ["<p>यह तीन मूल सदस्य देशों के साथ शुरू हुआ।</p>", "<p>इसमें 2022 तक दस सदस्य राज्यों का एक समूह शामिल है।</p>",
                                "<p>संगठन का एक उद्देश्य क्षेत्र में आर्थिक विकास, सामाजिक प्रगति और सांस्कृतिक विकास में तेजी लाना है।</p>", "<p>ASEAN की स्थापना 8 अगस्त 1967 को बैंकॉक में हुई थी।</p>"],
                    solution_en: "<p>5.(a) <strong>Option A is incorrect. ASEAN</strong> (The Association of Southeast Asian Nations) was established with the signing of the ASEAN Declaration (Bangkok Declaration) by 5 founding countries (Indonesia, Malaysia, Philippines, Singapore and Thailand) on 8 August 1967. <strong>Member Nations -</strong> Total 10 (5 founding countries and Brunei, Vietnam, Laos, Myanmar and Cambodia). Headquarters - Jakarta (Indonesia).</p>",
                    solution_hi: "<p>5.(a) <strong>विकल्प A ग़लत है। ASEAN</strong> (दक्षिणपूर्व एशियाई देशों का संघ) की स्थापना 8 अगस्त 1967 को 5 संस्थापक देशों (इंडोनेशिया, मलेशिया, फिलीपींस, सिंगापुर और थाईलैंड) द्वारा ASEAN घोषणा (बैंकॉक घोषणा) पर हस्ताक्षर के साथ की गई थी। <strong>सदस्य राष्ट्र - </strong>कुल 10 (5 संस्थापक देश तथा ब्रुनेई, वियतनाम, लाओस, म्यांमार और कंबोडिया)। मुख्यालय - जकार्ता (इंडोनेशिया)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. WTO is a world organisation . Which of the following statements about WTO is INCORRECT?</p>",
                    question_hi: "<p>6. विश्व व्यापार संगठन एक वैश्विक संगठन है। विश्व व्यापार संगठन के बारे में निम्नलिखित में से कौन सा कथन गलत है?</p>",
                    options_en: ["<p>Its full form is World transport Organisation.</p>", "<p>It deals with the rules of trade between nations.</p>", 
                                "<p>Ngozi Okonjo - Iweala of Nigeria is the organisation&rsquo;s Seventh Director-General.</p>", "<p>Its top decision - making body is the Ministerial Conference.</p>"],
                    options_hi: ["<p>इसका पूर्ण रूप विश्व परिवहन संगठन है।</p>", "<p>यह राष्ट्रों के बीच व्यापार के नियमों से संबंधित है।</p>",
                                "<p>नगोजी ओकोंजो - नाइजीरिया के इवेला संगठन के सातवें महानिदेशक हैं।</p>", "<p>इसका शीर्ष निर्णय लेने वाला निकाय मंत्रिस्तरीय सम्मेलन है।</p>"],
                    solution_en: "<p>6.(a) <strong>Option A is incorrect. The World Trade Organization (WTO) -</strong> An intergovernmental organization which regulates international trade. It commenced on 1 January 1995 under the <strong>Marrakesh Agreement</strong>, signed by 123 nations on 15 April 1994, replacing the General Agreement on Tariffs and Trade (<strong>GATT</strong>), which was established in 1948. <strong>Headquarters </strong>- Geneva (Switzerland).</p>",
                    solution_hi: "<p>6.(a) <strong>विकल्प A ग़लत है। विश्व व्यापार संगठन (WTO) -</strong> एक अन्तर्शासकीय संगठन जो अंतर्राष्ट्रीय व्यापार को नियंत्रित करता है। इसकी शुरुआत 1 जनवरी 1995 को <strong>मारकेश समझौते</strong> <strong>(Marrakesh Agreement)</strong> के तहत हुई, जिस पर 15 अप्रैल 1994 को 123 देशों ने हस्ताक्षर किए थे, जो जनरल एग्रीमेंट ऑन टैरिफ एंड ट्रेड (<strong>GATT</strong>) की जगह लेता है, जिसे 1948 में स्थापित किया गया था। <strong>मुख्यालय </strong>- जिनेवा (स्विट्जरलैंड)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following is <strong>NOT </strong>a function of the International Monetary Fund (IMF)?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन अंतर्राष्ट्रीय मुद्रा कोष (IMF) का कार्य <strong>नहीं </strong>है?</p>",
                    options_en: ["<p>Providing loans to countries that have trouble meeting their international payments</p>", "<p>Monitoring the financial and economic policies of its member countries</p>", 
                                "<p>Creating innovative business models to increase the wealth of its member countries</p>", "<p>Assisting mainly low and middle income countries to effectively manage their economies</p>"],
                    options_hi: ["<p>उन देशों को ऋण प्रदान करना जिन्हें अपने अंतरराष्ट्रीय भुगतानों को पूरा करने में परेशानी होती है</p>", "<p>अपने सदस्य देशों की वित्तीय और आर्थिक नीतियों की निगरानी करना</p>",
                                "<p>अपने सदस्य देशों की संपत्ति बढ़ाने के लिए अभिनव व्यापार मॉडल बनाना</p>", "<p>मुख्य रूप से निम्न और मध्यम आय वाले देशों को उनकी अर्थव्यवस्थाओं को प्रभावी ढंग से प्रबंधित करने में सहायता करना</p>"],
                    solution_en: "<p>7.(c) <strong>Option (c) is correct. IMF: Started from -</strong> 27 December 1945. <strong>Headquarters </strong>- Washington D.C (United States). <strong>Membership </strong>- 190 countries (189 UN countries and Kosovo). The IMF has t<strong>hree critical missions:</strong> Furthering international monetary cooperation, encouraging the expansion of trade and economic growth, and discouraging policies that would harm prosperity.</p>",
                    solution_hi: "<p>7.(c) <strong>विकल्प (c) सही है। IMF: प्रारंभ - </strong>27 दिसंबर 1945। <strong>मुख्यालय </strong>- वाशिंगटन डी.सी. (संयुक्त राज्य अमेरिका)। सदस्यता - 190 देश (189 संयुक्त राष्ट्र देश और कोसोवो)। IMF के <strong>तीन महत्वपूर्ण मिशन </strong>हैं: अंतर्राष्ट्रीय मौद्रिक सहयोग को आगे बढ़ाना, व्यापार और आर्थिक विकास के विस्तार को प्रोत्साहित करना, और उन नीतियों को हतोत्साहित करना जो समृद्धि को हानि पहुंचाएंगी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The analysis of the interrelationship between the economy and the environment in the economic environmental accounting system was put forward by______ .</p>",
                    question_hi: "<p>8. आर्थिक पर्यावरण लेखांकन प्रणाली में अर्थव्यवस्था और पर्यावरण के बीच अंतर्संबंधों का विश्लेषण ________द्वारा प्रस्तुत किया गया था।</p>",
                    options_en: ["<p>Central Statistical Office</p>", "<p>National Environmental Engineering Institute</p>", 
                                "<p>Directorate of Economics and Statistics</p>", "<p>United Nations Statistical Office</p>"],
                    options_hi: ["<p>केंद्रीय सांख्यिकी कार्यालय</p>", "<p>राष्ट्रीय पर्यावरण इंजीनियरिंग संस्थान</p>",
                                "<p>अर्थशास्त्र और सांख्यिकी निदेशालय</p>", "<p>संयुक्त राष्ट्र सांख्यिकी कार्यालय</p>"],
                    solution_en: "<p>8.(d) <strong>United Nations Statistical Office. </strong>The System of Environmental Economic Accounting (SEEA) - A statistical system that measures the condition of the environment, the contribution of the environment to the economy and the impact of the economy on the environment.</p>",
                    solution_hi: "<p>8.(d) <strong>संयुक्त राष्ट्र सांख्यिकी कार्यालय।</strong> पर्यावरण आर्थिक लेखांकन प्रणाली (SEEA) - एक सांख्यिकीय प्रणाली जो पर्यावरण की स्थिति, अर्थव्यवस्था में पर्यावरण के योगदान और पर्यावरण पर अर्थव्यवस्था के प्रभाव को मापती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following Sustainable Development Goals (SDG) seeks to &lsquo;Protect, restore and promote sustainable use of terrestrial ecosystems, sustainably manage forests, combat desertification, and halt and reverse land degradation and halt biodiversity loss&rsquo; ?</p>",
                    question_hi: "<p>9. निम्नलिखित सतत विकास लक्ष्यों में से कौन सा (SDG) \'स्थलीय पारिस्थितिक तंत्र के सतत उपयोग की रक्षा, पुनर्स्थापना और बढ़ावा देने, वनों का स्थायी रूप से प्रबंधन करने, मरुस्थलीकरण का मुकाबला करने और भूमि क्षरण को रोकने और रिवर्स करने और जैव विविधता के नुकसान को रोकने\' का प्रयास करता है?</p>",
                    options_en: ["<p>SDG12</p>", "<p>SDG 7</p>", 
                                "<p>SDG 17</p>", "<p>SDG 15</p>"],
                    options_hi: ["<p>SDG12</p>", "<p>SDG7</p>",
                                "<p>SDG17</p>", "<p>SDG15</p>"],
                    solution_en: "<p>9.(d) <strong>SDG 15. SDG 12: </strong>Ensure sustainable consumption and production patterns. <strong>SDG 7</strong> focuses on global efforts to ensure access to affordable, reliable, sustainable and modern energy for all.</p>",
                    solution_hi: "<p>9.(d) <strong>SDG 15 । SDG 12 :&nbsp;</strong>सतत उपयोग और उत्पादन पैटर्न सुनिश्चित करें।<strong> SDG 7</strong> सभी के लिए सस्ती, विश्वसनीय, टिकाऊ और आधुनिक ऊर्जा तक पहुंच सुनिश्चित करने के वैश्विक प्रयासों पर केंद्रित है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The UN&rsquo;s Sustainable Development Goal 6 aims to ______.</p>",
                    question_hi: "<p>10. संयुक्त राष्ट्र के सतत विकास लक्ष्य 6 का उद्देश्य ________ है।</p>",
                    options_en: ["<p>ensure healthy lives and promote well-being for all at all ages</p>", "<p>ensure availability and sustainable management of water and sanitation for all</p>", 
                                "<p>end poverty in all forms everywhere</p>", "<p>take urgent action to combat climate change and its impact</p>"],
                    options_hi: ["<p>स्वस्थ जीवन सुनिश्चित करना और सभीउम्र में सभी के लिए कल्याण को बढ़ावा देना</p>", "<p>सभी के लिए पानी और स्वच्छता की उपलब्धता और टिकाऊ प्रबंधन सुनिश्चित करना</p>",
                                "<p>हर जगह सभी रूपों में गरीबी खत्म करो</p>", "<p>जलवायु परिवर्तन और इसके प्रभाव से निपटने के लिए तत्काल कार्रवाई करें</p>"],
                    solution_en: "<p>10.(b) <strong>SDG 1</strong> - end poverty in all forms everywhere. <strong>SDG 13 - </strong>take urgent action to combat climate change and its impact.</p>",
                    solution_hi: "<p>10.(b) <strong>SDG 1- </strong>प्रत्येक जगह सभी रूपों में गरीबी समाप्त करना। <strong>SDG 13 </strong>- जलवायु परिवर्तन और उसके प्रभाव से निपटने के लिए तत्काल कार्रवाई करना ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following institutions conducts the survey on employment and unemployment?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन सी संस्था रोजगार और बेरोजगारी पर सर्वेक्षण करती है?</p>",
                    options_en: ["<p>NITI Aayog</p>", "<p>National Institution of Economic and Social Research</p>", 
                                "<p>National Council of Applied Economic Research</p>", "<p>National Sample Survey Organisation</p>"],
                    options_hi: ["<p>नीति आयोग</p>", "<p>राष्ट्रीय आर्थिक और सामाजिक अनुसंधान संस्थान</p>",
                                "<p>नेशनल काउंसिल ऑफ एप्लाइड इकोनॉमिक रिसर्च</p>", "<p>राष्ट्रीय नमूना सर्वेक्षण संगठन</p>"],
                    solution_en: "<p>11.(d) <strong>National Sample Survey Organisation </strong>(NSSO). It is the largest independent organisation since 1950 in the country for conducting the socio-economic survey.</p>",
                    solution_hi: "<p>11.(d) <strong>राष्ट्रीय नमूना सर्वेक्षण संगठन</strong> (NSSO) द्वारा किया जाता है। सामाजिक -आर्थिक&nbsp;सर्वेक्षण करने के लिए 1950 के बाद से यह देश&nbsp;का सबसे बड़ा स्वतंत्र संगठन है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. With reference to the United Nations&rsquo; Sustainable Development Goals, which of the Following is the SDG 10?</p>",
                    question_hi: "<p>12. संयुक्त राष्ट्र के &lsquo;सतत विकास लक्ष्यों&rsquo; के संदर्भ में, निम्नलिखित में से कौन सा SDG 10 है ?</p>",
                    options_en: ["<p>Climate action</p>", "<p>Reduced inequalities</p>", 
                                "<p>Zero hunger</p>", "<p>Decent work and economic growth</p>"],
                    options_hi: ["<p>जलवायु क्रिया</p>", "<p>असमानताओं में कमी</p>",
                                "<p>शून्य भुखमरी</p>", "<p>उत्कृष्ट कार्य तथा आर्थिक विकास</p>"],
                    solution_en: "<p>12.(b)<strong> Reduced inequalities. SDG 10: Goal -</strong> \"reducing inequalities in income as well as those based on age, sex, disability, race, ethnicity, origin, religion or economic or other status within a country\". There are 17 Sustainable Development Goals established by the United Nations in 2015.</p>",
                    solution_hi: "<p>12.(b) <strong>असमानताओं में कमी. </strong>SDG 10: लक्ष्य - <strong>\"</strong>आय के साथ-साथ किसी देश के भीतर उम्र, लिंग, विकलांगता, नस्ल, मूल, धर्म या आर्थिक या अन्य स्थिति के आधार पर असमानताओं को कम करना।\" 2015 में संयुक्त राष्ट्र द्वारा स्थापित 17 सतत विकास लक्ष्य हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which among the following organisations releases the Index of Economic Freedom?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन सा संगठन आर्थिक स्वतंत्रता सूचकांक जारी करता है?</p>",
                    options_en: ["<p>International Monetary Fund</p>", "<p>Heritage Foundation</p>", 
                                "<p>Fraser Institute</p>", "<p>World Bank</p>"],
                    options_hi: ["<p>अंतर्राष्ट्रीय मुद्रा कोष</p>", "<p>विरासत फाउंडेशन</p>",
                                "<p>फ्रेजर इंस्टिट्यूट</p>", "<p>विश्व बैंक</p>"],
                    solution_en: "<p>13.(b) <strong>Heritage Foundation</strong> releases Index of Economic Freedom. The<strong> Index of Economic Freedom </strong>is an annual index and ranking created in 1995 by The Heritage Foundation and The Wall Street Journal to measure the degree of economic freedom in the world\'s nations.</p>",
                    solution_hi: "<p>13.(b) <strong>विरासत फाउंडेशन</strong> ने आर्थिक स्वतंत्रता का सूचकांक जारी किया।<strong> आर्थिक स्वतंत्रता का सूचकांक</strong> 1995 में द हेरिटेज फाउंडेशन और द वॉल स्ट्रीट जर्नल द्वारा दुनिया के देशों में आर्थिक स्वतंत्रता की डिग्री को मापने के लिए बनाया गया एक वार्षिक सूचकांक और रैंकिंग है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. India has been a member of the General Agreement on Tariffs and Trade (GATT) since_______.</p>",
                    question_hi: "<p>14. भारत _______ से जनरल एग्रीमेंट ऑन टैरिफ एंड ट्रेड (GATT) का सदस्य है।</p>",
                    options_en: ["<p>1962</p>", "<p>1948</p>", 
                                "<p>1990</p>", "<p>1985</p>"],
                    options_hi: ["<p>1962</p>", "<p>1948</p>",
                                "<p>1990</p>", "<p>1985</p>"],
                    solution_en: "<p>14.(b) <strong>1948.</strong> India has been a founding member of the WTO since 1 January 1995 and a member of GATT since 8 July 1948 . (GATT) was a legal multilateral agreement between many countries. Its overall purpose was to promote international trade by reducing or eliminating trade barriers such as tariffs or quotas. It was signed by 23 nations in Geneva on October 30, 1947, and took effect on January 1, 1948.</p>",
                    solution_hi: "<p>14. (b) <strong>1948 </strong>। भारत 1 जनवरी 1995 से WTO का संस्थापक सदस्य और 8 जुलाई 1948 से GATT का सदस्य है। (GATT ) कई देशों के बीच एक कानूनी बहुपक्षीय समझौता था। इसका उद्देश्य टैरिफ(दर ) या कोटा जैसी व्यापार बाधाओं को कम या समाप्त करके अंतर्राष्ट्रीय व्यापार को बढ़ावा देना था। 30 अक्टूबर, 1947 को जिनेवा में 23 देशों द्वारा इस पर हस्ताक्षर किए गए और 1 जनवरी, 1948 को प्रभावी हुआ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following is primarily an economic union of the post-soviet states located in eastern Europe, Western Asia and Central Asia?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन सा मुख्य रूप से पूर्वी यूरोप, पश्चिमी एशिया और मध्य एशिया में स्थित सोवियत के बाद के राज्यों का एक आर्थिक संघ है?</p>",
                    options_en: ["<p>Andean Community</p>", "<p>European Union</p>", 
                                "<p>Eurasian Economic Union</p>", "<p>CARICOM Single Market and Economy</p>"],
                    options_hi: ["<p>एंडियन समुदाय</p>", "<p>यूरोपीय संघ</p>",
                                "<p>यूरेशियन आर्थिक संघ</p>", "<p>कैरिकॉम एकल बाजार और अर्थव्यवस्था</p>"],
                    solution_en: "<p>15.(c) <strong>Eurasian Economic Union:-</strong> It was signed on 29 May 2014 by the leaders of Armenia, Belarus, Kazakhstan, Kyrgyzstan and Russia and came into force on 1 January 2015. <strong>Aim </strong>- It includes the free movement of goods, services, capital, and labor among member states. <strong>European Union</strong> - It is a group of 27 countries in Europe to make things better, easier and safer for people.</p>",
                    solution_hi: "<p>15.(c) <strong>यूरेशियन आर्थिक संघ :-</strong> इस पर 29 मई 2014 को आर्मेनिया, बेलारूस, कजाकिस्तान, किर्गिस्तान और रूस के नेताओं द्वारा हस्ताक्षर किए गए और यह 1 जनवरी 2015 को लागू हुआ। <strong>उद्देश्य </strong>- इसमें सदस्य राज्यों के बीच वस्तुओं, सेवाओं, पूंजी और श्रम का मुक्त आवागमन शामिल है।<strong> यूरोपीय संघ -</strong> यह लोगों के लिए चीजों को बेहतर, आसान और सुरक्षित बनाने के लिए यूरोप के 27 देशों का एक समूह है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>