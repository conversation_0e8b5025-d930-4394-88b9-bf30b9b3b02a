<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Who among the following is the hero of the Sanskrit historical poem, the \'Ramacharita\' written by Sandhyakar Nandi?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन संध्याकर नंदी की संस्कृत की एक ऐतिहासिक कविता, \'रामचरित\' का नायक है?</p>",
                    options_en: ["<p>Gopala</p>", "<p>Devapala</p>", 
                                "<p>Ramapala</p>", "<p>Dharmapala</p>"],
                    options_hi: ["<p>गोपाल</p>", "<p>देवपाल</p>",
                                "<p>रामपाल</p>", "<p>धर्मपाल</p>"],
                    solution_en: "<p>1.(c) <strong>Ramapala</strong> was the successor to the Pala king Shurapala II in the Bengal region of the Indian subcontinent, and 15th ruler of the Pala line. Sandhyakar Nandi was patronaged by Madanapala, ruler of Pala Dynasty. Ramacharitam is written in two distinct themes. Laid out in four chapters, the first part of the book describes the biography of Ramachandra, the son of Dasharatha, while the second part narrates the life of Rampala, the king of Gauda.</p>",
                    solution_hi: "<p>1.(c) <strong>रामपाल</strong> भारतीय उपमहाद्वीप के बंगाल क्षेत्र में पाल राजा शूरपाल द्वितीय के उत्तराधिकारी और पाल वंश के 15वें शासक थे। संध्याकर नंदी को पाल वंश के शासक मदनपाल ने संरक्षण दिया था। रामचरित दो अलग-अलग विषयों पर लिखा गया है। चार अध्यायों में विभाजित, पुस्तक के पहले भाग में दशरथ के पुत्र रामचंद्र की जीवनी का वर्णन है, जबकि दूसरे भाग में गौड़ के राजा रामपाल के जीवन का वर्णन है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. According to the Census of India 2011, which group of Union Territories recorded the highest sex ratio?",
                    question_hi: "2. भारत की जनगणना 2011 के अनुसार, केंद्र शासित प्रदेशों के किस समूह ने उच्चतम लिंगानुपात दर्ज किया गया?",
                    options_en: [" Puducherry, Delhi, Andaman and Nicobar Islands", " Puducherry, Lakshadweep, Andaman and Nicobar Islands", 
                                " Chandigarh, Puducherry, Lakshadweep", " Chandigarh, Puducherry, Delhi"],
                    options_hi: [" पुडुचेरी, दिल्ली, अंडमान और निकोबार द्वीप समूह", " पुडुचेरी, लक्षद्वीप, अंडमान और निकोबार द्वीप समूह",
                                " चंडीगढ़, पुडुचेरी, लक्षद्वीप", " चंडीगढ़, पुडुचेरी, दिल्ली"],
                    solution_en: "2.(b) The sex ratio, defined as the number of females per 1,000 males in a population. According to the 2011 Census, India’s sex ratio was 943 females per 1,000 males. In the union territories, the sex ratios were as follows: Puducherry (1,037), Lakshadweep (946), Andaman and Nicobar Islands (876). Among the states, the highest sex ratios were found in Kerala (1,084), Tamil Nadu (996), and Andhra Pradesh (993).",
                    solution_hi: "2.(b) लिंगानुपात, जनसंख्या में प्रति 1,000 पुरुषों पर महिलाओं की संख्या के रूप में परिभाषित किया गया है। 2011 की जनगणना के अनुसार, भारत का लिंगानुपात प्रति 1,000 पुरुषों पर 943 महिलाएं थीं। केंद्र शासित प्रदेशों में, लिंगानुपात इस प्रकार था: पुडुचेरी (1,037), लक्षद्वीप (946), अंडमान और निकोबार द्वीप समूह (876)। राज्यों में, सबसे अधिक लिंग अनुपात केरल (1,084), तमिलनाडु (996) और आंध्र प्रदेश (993) में पाया गया।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following two quantities have the same dimensions?</p>",
                    question_hi: "<p>3. निम्नलिखित में से किन दो राशियों की विमाएँ समान हैं?</p>",
                    options_en: ["<p>Work and torque</p>", "<p>Power and moment of inertia</p>", 
                                "<p>Work and angular displacement</p>", "<p>Power and radius of circular motion</p>"],
                    options_hi: ["<p>कार्य और बल आघूर्ण</p>", "<p>शक्ति और जड़त्व आघूर्ण</p>",
                                "<p>कार्य और कोणीय विस्थापन</p>", "<p>शक्ति और वृत्ताकार गति की त्रिज्या</p>"],
                    solution_en: "<p>3.(a) <strong>Work and torque</strong>. Both work and torque have the same dimensions, expressed as [ML<sup>2</sup>T<sup>&minus;2</sup>]. This is because both work and torque can be represented as the product of force and distance, with work defined as force times displacement (W = F&sdot;d) and torque defined as force times the perpendicular distance from the pivot point (&tau; = F&sdot;r). Since force has dimensions of [MLT<sup>&minus;</sup><sup>2</sup>], both work and torque ultimately have the same dimensional expression.</p>",
                    solution_hi: "<p>3.(a) <strong>कार्य और बल आघूर्ण।</strong> कार्य और बल आघूर्ण दोनों के विमाएँ समान हैं, जिन्हें [ML<sup>2</sup>T<sup>&minus;</sup><sup>2</sup>] के रूप में व्यक्त किया जाता है। ऐसा इसलिए है क्योंकि कार्य और बल आघूर्ण दोनों को बल और दूरी के गुणनफल के रूप में दर्शाया जाता है, जिसमें कार्य को बल और विस्थापन के गुणनफल (W = F&sdot;d) एवं बल आघूर्ण को बल तथा धुरी बिंदु से लंबवत दूरी के गुणनफल (&tau; = F&sdot;r) के रूप में परिभाषित किया जाता है। चूँकि बल के विमाएँ [MLT<sup>&minus;</sup><sup>2</sup>] हैं, इसलिए कार्य और बल आघूर्ण दोनों का अंततः एक ही विमाएँ अभिव्यक्ति है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The Doctrine of Lapse was an annexation policy devised by which of the following Governor-Generals of India?</p>",
                    question_hi: "<p>4. हड़प नीति भारत के निम्नलिखित में से किस गवर्नर-जनरल द्वारा तैयार की गई एक विलय नीति थी?</p>",
                    options_en: ["<p>Lord Wellesley</p>", "<p>Lord Dalhousie</p>", 
                                "<p>Lord Mayo</p>", "<p>Lord Canning</p>"],
                    options_hi: ["<p>लॉर्ड वैलेस्ली</p>", "<p>लॉर्ड डलहौजी</p>",
                                "<p>लॉर्ड मेयो</p>", "<p>लॉर्ड कैनिंग</p>"],
                    solution_en: "<p>4.(b) <strong>Lord Dalhousie.</strong> The Doctrine of Lapse was an annexation policy used by the British East India Company to expand its empire in India. Under this policy, if a ruler of a dependent state died without a male heir, the state would be annexed by the British. States annexed under the policy: Satara (1848), Jaitpur and Sambalpur (1849), Baghat (1850), Udaipur (1852), Jhansi (1854), Nagpur (1854), Tanjore, and Arcot (1855).</p>",
                    solution_hi: "<p>4.(b) <strong>लॉर्ड डलहौजी।</strong> हड़प नीति ब्रिटिश ईस्ट इंडिया कंपनी द्वारा भारत में अपने साम्राज्य का विस्तार करने के लिए इस्तेमाल की गई एक विलय नीति थी। इस नीति के तहत, यदि किसी आश्रित राज्य का शासक बिना किसी पुरुष उत्तराधिकारी के उसकी मृत्यु हो जाती है, तो उस राज्य को अंग्रेजों द्वारा विलय कर लिया जाता था। इस नीति के तहत विलय किए गए राज्य: सतारा (1848), जैतपुर और संबलपुर (1849), बघाट (1850), उदयपुर (1852), झांसी (1854), नागपुर (1854), तंजौर और अर्काट (1855) आदि है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following is a man-made ecosystem?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन-सा मानव निर्मित पारिस्थितिकी तंत्र है?</p>",
                    options_en: ["<p>Aquarium</p>", "<p>Dessert</p>", 
                                "<p>Forest</p>", "<p>Grassland</p>"],
                    options_hi: ["<p>मछलीघर</p>", "<p>रेगिस्तान</p>",
                                "<p>वन</p>", "<p>घास स्थल</p>"],
                    solution_en: "<p>5.(a) <strong>Aquarium.</strong> It is a facility or park where a collection of aquatic animals and plants is maintained for study, conservation, and public display. A man-made ecosystem, also known as an artificial or anthropogenic ecosystem, refers to a human-created environment where biotic and abiotic components interact to sustain life. Examples of man-made ecosystems include tree plantations, urban landscapes, Crop fields, managed ponds, zoos, and gardens.</p>",
                    solution_hi: "<p>5.(a) <strong>मछलीघर।</strong> यह एक ऐसी सुविधा या उद्यान है जहाँ अध्ययन, संरक्षण और सार्वजनिक प्रदर्शन के लिए जलीय जंतुओं और पौधों का संग्रह रखा जाता है। मानव निर्मित पारिस्थितिकी तंत्र, जिसे कृत्रिम या मानवजनित पारिस्थितिकी तंत्र के रूप में भी जाना जाता है, मानव निर्मित वातावरण को संदर्भित करता है जहाँ जीवन को बनाए रखने के लिए जैविक और अजैविक घटक परस्पर क्रिया करते हैं। मानव निर्मित पारिस्थितिकी तंत्र के उदाहरणों में वृक्षारोपण, शहरी परिदृश्य, फसल के खेत, प्रबंधित तालाब, चिड़ियाघर और उद्यान शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Who among the following famous Indian women cricketers announced retirement in June 2022?</p>",
                    question_hi: "<p>6. निम्नलिखित प्रसिद्ध भारतीय महिला क्रिकेटरों में से किसने जून 2022 में सन्&zwj;यास की घोषणा की थी?</p>",
                    options_en: ["<p>Smriti Mandhana</p>", "<p>Jhulan Goswami</p>", 
                                "<p>Anjum Chopra</p>", "<p>Mithali Raj</p>"],
                    options_hi: ["<p>स्मृति मंधाना</p>", "<p>झूलन गोस्वामी</p>",
                                "<p>अंजुम चोपड़ा</p>", "<p>मिताली राज</p>"],
                    solution_en: "<p>6.(d) <strong>Mithali Raj</strong> was the captain of the India women\'s national team from 2004 to 2022. Her awards include the Arjuna Award (2003), Padma Shri (2015), and Major Dhyan Chand Khel Ratna (2021).</p>",
                    solution_hi: "<p>6.(d) <strong>मिताली राज</strong> 2004 से 2022 तक भारत की महिला राष्ट्रीय टीम की कप्तान थीं। उनके पुरस्कारों में अर्जुन पुरस्कार (2003), पद्म श्री (2015) और मेजर ध्यानचंद खेल रत्न (2021) शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which state government honoured Vani Jairam with the MS Subbulakshmi Award for 2019?</p>",
                    question_hi: "<p>7. किस राज्य सरकार ने वाणी जयराम को 2019 के एम.एस. सुब्बुलक्ष्मी पुरस्कार से सम्मानित किया था?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Karnataka</p>", 
                                "<p>Odisha</p>", "<p>Tamil Nadu</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>कर्नाटक</p>",
                                "<p>ओड़िशा</p>", "<p>तमिलनाडु</p>"],
                    solution_en: "<p>7.(d) <strong>Tamil Nadu.</strong> Vani Jairam was an Indian playback singer, fondly referred to as the \"Meera of Modern India.\" She received the Padma Bhushan award in 2023. M.S. Subbulakshmi Award is a tribute to the legendary Carnatic singer, M.S. Subbulakshmi. She was the first musician ever to be awarded the Bharat Ratna in 1998.</p>",
                    solution_hi: "<p>7.(d) <strong>तमिलनाडु।</strong> वाणी जयराम भारतीय पार्श्व गायिका थीं, जिन्हें प्यार से \"आधुनिक भारत की मीरा\" कहा जाता था। उन्हें 2023 में पद्म भूषण पुरस्कार मिला। एम.एस. सुब्बुलक्ष्मी पुरस्कार महान कर्नाटक गायिका एम.एस. सुब्बुलक्ष्मी को श्रद्धांजलि है। वह 1998 में भारत रत्न से सम्मानित होने वाली प्रथम संगीतकार थीं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which region in India receives less rainfall in summer, but heavy rainfall in winter season due to the retreating north easterly winds?</p>",
                    question_hi: "<p>8. भारत के किस क्षेत्र में ग्रीष्म ऋतु में कम वर्षा होती है, लेकिन वापस लौट रही उत्तर-पूर्वी हवाओं के कारण सर्दियों के मौसम में भारी वर्षा होती है?</p>",
                    options_en: ["<p>Southern coastal region</p>", "<p>Western coastal region</p>", 
                                "<p>Northern coastal region</p>", "<p>Eastern coastal region</p>"],
                    options_hi: ["<p>दक्षिणी तटीय क्षेत्र</p>", "<p>पश्चिमी तटीय क्षेत्र</p>",
                                "<p>उत्तरी तटीय क्षेत्र</p>", "<p>पूर्वी तटीय क्षेत्र</p>"],
                    solution_en: "<p>8.(d) <strong>Eastern coastal region.</strong> The retreating or North-East monsoon brings heavy rainfall to the Coromandel coast from October to December. This monsoon, blowing from land to sea, gathers moisture and causes rain, making it the primary rainy season for southeastern India, including Tamil Nadu. The Coromandel coast lies between Pulicat Lake and Kanyakumari.</p>",
                    solution_hi: "<p>8.(d) <strong>पूर्वी तटीय क्षेत्र</strong>। उत्तर-पूर्वी मानसून या लौटता हुआ मानसून अक्टूबर से दिसंबर के बीच कोरोमंडल तट पर भारी वर्षा करता है। यह मानसून, जो स्थल से समुद्र की ओर चलता आद्र एकत्र करता है और वर्षा का मुख्य कारण बनता है, जिससे यह तमिलनाडु सहित दक्षिण-पूर्वी भारत के लिए प्राथमिक वर्षा ऋतु बन जाता है। कोरोमंडल तट पुलिकट झील और कन्याकुमारी के बीच स्थित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Match the sports in List I with the sportsperson in List II who were in news for their sports achievement in 2022:<br><strong id=\"docs-internal-guid-4cdbb2a3-7fff-06f4-2faf-53282838e737\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeU763wveCU5QWaQzysQ8Ge5jOnvxmmRUQ4RL6klSGrNUisZeAHrsmErEsco9d7IXNgX3EMIzQkAB5vcMxD6FuN6v323prviNHlXB-AFAm_HUdaCpZpKzexnv8OVO3CQm-tXFsXHQ?key=QzisIMwNc426IonWFglqztR4\" width=\"377\" height=\"139\"></strong></p>",
                    question_hi: "<p>9. सूची । में दिए गए खेलों का सूची ॥ में दिए गए खिलाड़ियों के साथ मिलान कीजिए, जो 2022 में अपनी खेल उपलब्धि के लिए खबरों में थे।<br><strong id=\"docs-internal-guid-f6c4f027-7fff-38ae-343c-fc620eee5e72\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXftYTauXlsyzePvnANpSJa3rLHS9q3n6GjQ5SI_i7e6WoHgoRpEzcwwAhCPg83yVMDNpgYzhwePYUtzH9QN3uIYmJBF8fsPpkFm4iY1f2ZH-OXQTEpdjBLP72hnlomK9SoqIE3nJw?key=QzisIMwNc426IonWFglqztR4\" width=\"234\" height=\"146\"></strong></p>",
                    options_en: ["<p>A-iv, B-ii, C-i, D-iii</p>", "<p>A-iii, B-iv, C-i, D-ii</p>", 
                                "<p>A-i, B-iv, C-ii, D-iii</p>", "<p>A-ii, B-iv, C-i, D-iii</p>"],
                    options_hi: ["<p>A-iv, B-ii, C-i, D-iii</p>", "<p>A-iii, B-iv, C-i, D-ii</p>",
                                "<p>A-i, B-iv, C-ii, D-iii</p>", "<p>A-ii, B-iv, C-i, D-iii</p>"],
                    solution_en: "<p>9.(d) <strong>A-ii, B-iv, C-i, D-iii</strong>. Sports and their players: Boxing - Paresh Lal Roy, Vijender Singh, Lovlina Borgohain, Amit Panghal, MC Mary Kom. Badminton - Satwiksairaj Rankireddy, Chirag Shetty, Anmol Kharb, Saina Nehwal. Shooting - Abhinav Bindra, Rajyavardhan Singh Rathore, Saurabh Chaudhary, Vijay Kumar, Anjali Bhagwat, Manu Bhaker. Chess &ndash; Koneru Humpy, Harika Dronavalli, Vaishali Ramesh Babu, Divya Deshmukh, Tania Sachdev, Gukesh D, Viswanathan Anand.</p>",
                    solution_hi: "<p>9.(d) <strong>A-ii, B-iv, C-i, D-iii.</strong> खेल एवं उनके खिलाड़ी: मुक्केबाजी - परेश लाल रॉय, विजेंदर सिंह, लवलीना बोरगोहेन, अमित पंघाल, एम.सी. मैरी कॉम। बैडमिंटन - सात्विकसाईराज रंकीरेड्डी, चिराग शेट्टी, अनमोल खरब, साइना नेहवाल। शूटिंग - अभिनव बिंद्रा, राज्यवर्धन सिंह राठौड़, सौरभ चौधरी, विजय कुमार, अंजलि भागवत, मनु भाकर। शतरंज - कोनेरू हम्पी, हरिका द्रोणावल्ली, वैशाली रमेश बाबू, दिव्या देशमुख, तानिया सचदेव, गुकेश डी, विश्वनाथन आनंद।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following are the two basic categories of an ecosystem?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन-सी पारिस्थितिकी तंत्र की दो बुनियादी श्रेणियां हैं ?</p>",
                    options_en: ["<p>Forest and Aquatic</p>", "<p>Terrestrial and Aquatic</p>", 
                                "<p>Lakes and Ponds</p>", "<p>Forest and Rivers</p>"],
                    options_hi: ["<p>वन और जलचर</p>", "<p>स्थलीय और जलचर</p>",
                                "<p>झील और तालाब</p>", "<p>वन और नदियाँ</p>"],
                    solution_en: "<p>10.(b) <strong>Terrestrial and Aquatic.</strong> An ecosystem is a chain of interactions between organisms and their environment. The term \"ecosystem\" was first coined by English botanist A.G. Tansley in 1935. A terrestrial ecosystem refers to a land-based community of organisms and the interactions between biotic and abiotic components in a specific area. An aquatic ecosystem involves interacting organisms that rely on each other and their water environment for nutrients and shelter.</p>",
                    solution_hi: "<p>10.(b) <strong>स्थलीय और जलचर।</strong> पारिस्थितिकी तंत्र जीवों और उनके पर्यावरण के बीच अंतःक्रियाओं की एक श्रृंखला है। \"पारिस्थितिकी तंत्र\" शब्द का पहली बार प्रयोग अंग्रेजी वनस्पतिशास्त्री ए.जी. टैन्सले ने 1935 में किया था। स्थलीय पारिस्थितिकी तंत्र जीवों के भूमि-आधारित समुदाय और एक विशिष्ट क्षेत्र में जैविक और अजैविक घटकों के बीच अंतःक्रियाओं को संदर्भित करता है। जलीय पारिस्थितिकी तंत्र में अंतःक्रियाशील जीव शामिल होते हैं जो पोषक तत्वों और आश्रय के लिए एक-दूसरे और अपने जलीय पर्यावरण पर निर्भर होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. For which of the following states was the Constitution (Scheduled Castes and Scheduled Tribes) Orders (Second Amendment) Bill, 2022, introduced in the Parliament?</p>",
                    question_hi: "<p>11. निम्नलिखित में से किस राज्य के लिए संविधान (अनुसूचित जाति और अनुसूचित जनजाति) आदेश (द्वितीय संशोधन) विधेयक, 2022 संसद में पेश किया गया था?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Bihar</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>बिहार</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>11.(c) <strong>Uttar Pradesh.</strong> This Bill was introduced in Lok Sabha on March 28, 2022. The Bill seeks to amend the Constitution (Scheduled Tribes) (Uttar Pradesh) Order, 1967 (ST Order) and the Constitution (Scheduled Castes) Order, 1950 (SC Order) with respect to its application to Uttar Pradesh. The Bill amends the SC order to exclude Gond community as a Scheduled Caste in four districts of Uttar Pradesh: (i) Chandauli, (ii) Kushinagar, (iii) Sant Kabir Nagar, and (iv) Sant Ravidas Nagar.</p>",
                    solution_hi: "<p>11.(c) <strong>उत्तर प्रदेश।</strong> यह विधेयक 28 मार्च, 2022 को लोकसभा में पेश किया गया था। विधेयक उत्तर प्रदेश में इसके लागू होने के संबंध में संविधान (अनुसूचित जनजाति) (उत्तर प्रदेश) आदेश, 1967 (ST आदेश ) और संविधान (अनुसूचित जाति) आदेश, 1950 (SC आदेश ) में संशोधन करने का प्रयास करता है। विधेयक उत्तर प्रदेश के चार जिलों में गोंड समुदाय को अनुसूचित जाति से बाहर करने के लिए SC आदेश में संशोधन करता है: (i) चंदौली, (ii) कुशीनगर, (iii) संत कबीर नगर और (iv) संत रविदास नगर।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following features of the Indian Constitution was borrowed from Irish Constitution?</p>",
                    question_hi: "<p>12. भारतीय संविधान की निम्नलिखित में से कौन-सी विशेषता आयरलैंड के संविधान से ली गई है?</p>",
                    options_en: ["<p>Directive Principles of State Policy</p>", "<p>Concurrent List</p>", 
                                "<p>Fundamental Rights</p>", "<p>Rule of Law</p>"],
                    options_hi: ["<p>राज्य के नीति निदेशक तत्&zwj;व</p>", "<p>समवर्ती सूची</p>",
                                "<p>मूल अधिकार</p>", "<p>कानून का शासन</p>"],
                    solution_en: "<p>12.(a) <strong>Directive Principles of State Policy.</strong> Borrowed features of Indian Constitution: Irish - Nomination of members to Rajya Sabha and method of election of President. US - Fundamental rights, independence of judiciary, judicial review. Australian - Concurrent List, freedom of trade, commerce and inter-course. British - Parliamentary government, Rule of Law, legislative procedure, single citizenship.</p>",
                    solution_hi: "<p>12.(a) <strong>राज्य के नीति निदेशक तत्&zwj;व।</strong> भारतीय संविधान की अन्य देशों से ली गई विशेषताएँ: आयरलैंड- राज्य सभा के सदस्यों का नामांकन और राष्ट्रपति के निर्वाचन की प्रक्रिया। अमेरिका - मौलिक अधिकार, न्यायपालिका की स्वतंत्रता, न्यायिक समीक्षा। ऑस्ट्रेलिया - समवर्ती सूची, व्यापार, वाणिज्य एवं इंटर-कोर्स की स्वतंत्रता। ब्रिटिश - संसदीय सरकार, कानून का शासन, विधायी प्रक्रिया, एकल नागरिकता।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. The Supreme Court of India comprises the Chief Justice and not more than ______ other Judges appointed by the President of India.</p>",
                    question_hi: "<p>13. भारत के सर्वोच्च न्यायालय में भारत के राष्ट्रपति द्वारा नियुक्त मुख्य न्यायाधीश और अधिकतम ______ अन्य न्यायाधीश शामिल होते हैं।</p>",
                    options_en: ["<p>37</p>", "<p>35</p>", 
                                "<p>38</p>", "<p>33</p>"],
                    options_hi: ["<p>37</p>", "<p>35</p>",
                                "<p>38</p>", "<p>33</p>"],
                    solution_en: "<p>13.(d) <strong>33.</strong> The judges of supreme court are appointed by the president under the Article 124 (2) of the Indian Constitution. Supreme Court Judges retire at the age of 65. As per Article 124 (3) - In order to be appointed as a Judge of the Supreme Court, a person must be a citizen of India and must have been, for at least five years, a Judge of a High Court or an Advocate of a High Court for at least 10 years or he must be, in the opinion of the President, a distinguished jurist.</p>",
                    solution_hi: "<p>13.(d) <strong>33.</strong> भारतीय संविधान के अनुच्छेद 124 (2) के तहत राष्ट्रपति द्वारा सर्वोच्च न्यायालय के न्यायाधीशों की नियुक्ति की जाती है। सर्वोच्च न्यायालय के न्यायाधीश 65 वर्ष की आयु में सेवानिवृत्त होते हैं। अनुच्छेद 124 (3) के अनुसार - सर्वोच्च न्यायालय के न्यायाधीश के रूप में नियुक्त होने के लिए, किसी व्यक्ति को भारत का नागरिक होना चाहिए और कम से कम पाँच वर्षों के लिए किसी उच्च न्यायालय का न्यायाधीश या कम से कम 10 वर्षों के लिए किसी उच्च न्यायालय का अधिवक्ता होना चाहिए या राष्ट्रपति की राय में उसे एक प्रतिष्ठित विधिवेत्ता होना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Ranjana Gauhar was awarded the Padma Shri Award (2003) for her contribution to which of the following Indian dance forms?</p>",
                    question_hi: "<p>14. रंजना गौहर को निम्नलिखित में से किस भारतीय नृत्य शैली में उनके योगदान के लिए पद्म श्री पुरस्कार (2003) से सम्मानित किया गया था?</p>",
                    options_en: ["<p>Kuchipudi</p>", "<p>Sattriya</p>", 
                                "<p>Odissi</p>", "<p>Manipuri</p>"],
                    options_hi: ["<p>कुचिपुड़ी</p>", "<p>सत्रीया</p>",
                                "<p>ओडिसी</p>", "<p>मणिपुरी</p>"],
                    solution_en: "<p>14.(c) <strong>Odissi.</strong> Ranjana Gauhar also received the Sangeet Natak Akademi Award in 2007. Other renowned Odissi dancers include Kelucharan Mohapatra, Mayadhar Raut, Sanjukta Panigrahi, Ratikant Mohapatra, Deba Prasad Das, Kumkum Mohanty, Pankaj Charan Das, and Aruna Mohanty.</p>",
                    solution_hi: "<p>14.(c) <strong>ओडिसी।</strong> रंजना गौहर को 2007 में संगीत नाटक अकादमी पुरस्कार से सम्मानित किया गया था। अन्य प्रसिद्ध ओडिसी नर्तकों में केलुचरण महापात्र, मायाधर राउत, संजुक्ता पाणिग्रही, रतिकांत महापात्र, देबा प्रसाद दास, कुमकुम मोहंती, पंकज चरण दास और अरुणा मोहंती शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Who won the paralympic bronze medal in archery for India at Tokyo in 2020?</p>",
                    question_hi: "<p>15. 2020 में टोक्यो में भारत के लिए तीरंदाजी में पैरालंपिक कांस्य पदक किसने जीता?</p>",
                    options_en: ["<p>Atanu Das</p>", "<p>Pravin Jadhav</p>", 
                                "<p>Deepika Kumari</p>", "<p>Harvinder Singh</p>"],
                    options_hi: ["<p>अतानु दास</p>", "<p>प्रवीण जाधव</p>",
                                "<p>दीपिका कुमारी</p>", "<p>हरविंदर सिंह</p>"],
                    solution_en: "<p>15.(d) <strong>Harvinder Singh</strong> is a double Paralympic medalist, having won gold at the 2024 Paris Paralympics and bronze at the 2020 Tokyo Paralympics. Some famous Indian archers include Deepika Kumari, Dola Banerjee, Tarundeep Rai, Atanu Das, Ankita Bhakat, Pravin Ramesh Jadhav, Jyothi Surekha Vennam, and Ojas Deotale.</p>",
                    solution_hi: "<p>15.(d) <strong>हरविंदर सिंह</strong> दोहरे पैरालंपिक पदक विजेता हैं, जिन्होंने 2024 पेरिस पैरालंपिक में स्वर्ण और 2020 टोक्यो पैरालंपिक में कांस्य पदक जीता है। कुछ प्रसिद्ध भारतीय तीरंदाजों में दीपिका कुमारी, डोला बनर्जी, तरुणदीप राय, अतानु दास, अंकिता भकत, प्रवीण रमेश जाधव, ज्योति सुरेखा वेन्नम और ओजस देवताले शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. Which of the following statements is correct about the composition of the agriculture sector and employment in GDP post- independence?",
                    question_hi: "16. स्वतंत्रता के बाद जीडीपी (GDP) में कृषि क्षेत्र के संघटन और रोजगार के संबंध में निम्नलिखित में से कौन-सा कथन सही है?",
                    options_en: ["  The proportion of employment has declined significantly but not the share of the agriculture sector in GDP.", " The proportion of both the agriculture sector and employment in GDP has increased significantly.", 
                                " The proportion of both the agriculture sector and employment in GDP has declined significantly.", " The proportion of GDP contributed by the agricultural sector declined significantly but not the employment depending on the agriculture sector."],
                    options_hi: [" जीडीपी (GDP) में रोजगार के अनुपात में उल्लेखनीय गिरावट आई है लेकिन कृषि क्षेत्र की भागीदारी में कमी नहीं आई है।  ", " जीडीपी (GDP) में कृषि क्षेत्र और रोजगार दोनों के अनुपात में उल्लेखनीय वृद्धि हुई है। ",
                                " जीडीपी (GDP) में कृषि क्षेत्र और रोजगार दोनों के अनुपात में उल्लेखनीय गिरावट आई है। ", " कृषि क्षेत्र द्वारा जीडीपी (GDP) में योगदान के अनुपात में उल्लेखनीय गिरावट आई है, लेकिन कृषि क्षेत्र पर निर्भर रोजगार में गिरावट नहीं आई है।"],
                    solution_en: "16.(d) As economies develop, the importance of agriculture decreases compared to sectors like manufacturing and services. GDP growth usually aligns with industrialization and urbanization, reducing agriculture\'s share in GDP. In India, agriculture\'s contribution dropped from 47.6% in 1960-61 to 14.4% in 2010-11, due to structural changes and increased employment in other sectors. But still according to the Periodic Labour Force Survey (PLFS) about 45.76% of the total workforce is engaged in agriculture and allied sector during 2022-23. ",
                    solution_hi: "16.(d) जैसे-जैसे अर्थव्यवस्थाएँ विकसित होती हैं, विनिर्माण और सेवाओं जैसे क्षेत्रों की तुलना में कृषि का महत्व कम होता जाता है। GDP वृद्धि आमतौर पर औद्योगीकरण और शहरीकरण के साथ संरेखित होती है, जिससे GDP में कृषि का हिस्सा कम होता जाता है। भारत में, संरचनात्मक परिवर्तनों और अन्य क्षेत्रों में रोजगार में वृद्धि के कारण कृषि का योगदान 1960-61 में 47.6% से घटकर 2010-11 में 14.4% हो गया है। लेकिन फिर भी आवधिक श्रम बल सर्वेक्षण (PLFS) के अनुसार 2022-23 के दौरान कुल कार्यबल का लगभग 45.76% कृषि और संबद्ध क्षेत्र में लगा हुआ है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which port is developed as a satellite port?</p>",
                    question_hi: "<p>17. किस बंदरगाह को सैटेलाइट बंदरगाह के रूप में विकसित किया गया है?</p>",
                    options_en: ["<p>Mormugao</p>", "<p>Jawaharlal Nehru</p>", 
                                "<p>Haldia</p>", "<p>New Mangalore</p>"],
                    options_hi: ["<p>मोरमुगाओ</p>", "<p>जवाहरलाल नेहरू</p>",
                                "<p>हल्दिया</p>", "<p>न्यू मैंगलोर</p>"],
                    solution_en: "<p>17.(b) <strong>Jawaharlal Nehru.</strong> The Jawaharlal Nehru Port Authority (JNPA) at Navi Mumbai is a premier container handling port in India, accounting for approximately 50% of the total containerized cargo volume across the major ports. Commonly known as Nhava Sheva Port, it was commissioned in 1989. Other significant ports in India include Mormugao Port in Goa, Haldia Port in West Bengal, and New Mangalore Port in Karnataka.</p>",
                    solution_hi: "<p>17.(b) <strong>जवाहरलाल नेहरू।</strong> नवी मुंबई स्थित जवाहरलाल नेहरू बंदरगाह प्राधिकरण (JNPA) भारत में एक प्रमुख कंटेनर हैंडलिंग बंदरगाह है, जो प्रमुख बंदरगाहों पर कुल कंटेनरीकृत कार्गो की मात्रा का लगभग 50% हिस्सा संभालता है। इसे आमतौर पर न्हावा शेवा बंदरगाह के रूप में जाना जाता है, इसे 1989 में चालू किया गया था। भारत के अन्य महत्वपूर्ण बंदरगाहों में गोवा में मोरमुगाओ बंदरगाह, पश्चिम बंगाल में हल्दिया बंदरगाह और कर्नाटक में न्यू मैंगलोर बंदरगाह शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which polyatomic ionic compound is a white, crystalline powder used in fire extinguishers and to neutralise acids and bases?</p>",
                    question_hi: "<p>18. कौन-सा बहुपरमाणुक आयनी यौगिक ऐसा सफेद, क्रिस्टलीय पाउडर होता है जिसका उपयोग अग्निशामक यंत्रों में और अम्ल तथा क्षार को उदासीन करने में किया जाता है?</p>",
                    options_en: ["<p>Sodium Bisulphite</p>", "<p>Sodium Thiosulphate</p>", 
                                "<p>Sodium Chromate</p>", "<p>Sodium Bicarbonate</p>"],
                    options_hi: ["<p>सोडियम बाइसल्फाइट (Sodium Bisulphite)</p>", "<p>सोडियम थायोसल्फेट (Sodium Thiosulphate)</p>",
                                "<p>सोडियम क्रोमेट (Sodium Chromate)</p>", "<p>सोडियम बाईकारबोनेट (Sodium Bicarbonate)</p>"],
                    solution_en: "<p>18.(d) <strong>Sodium Bicarbonate</strong> also known as Baking soda. Its chemical formula is NaHCO<sub>3</sub>. It is used as a pH buffering agent, an electrolyte replenisher, systemic alkalizer and in topical cleansing solutions. Sodium Bisulphite (NaHSO<sub>3</sub>) appears as white crystals or crystalline powder used in food processing, photography. Sodium Chromate (Na<sub>2</sub>CrO<sub>4</sub>) appears as a yellow crystalline solid used to make pigments for paints and inks. Sodium thiosulfate (Na<sub>2</sub>O<sub>3</sub>S<sub>2</sub>) is an inorganic sodium salt, It has a role as an antidote to cyanide poisoning, a nephroprotective agent and an antifungal drug.</p>",
                    solution_hi: "<p>18.(d) <strong>सोडियम बाईकारबोनेट</strong> (Sodium Bicarbonate) को बेकिंग सोडा के नाम से भी जाना जाता है। इसका रासायनिक सूत्र NaHCO<sub>3</sub> है। इसका उपयोग pH बफरिंग एजेंट, इलेक्ट्रोलाइट पुनःपूर्तिकर्ता, प्रणालीगत क्षारीयकर्ता और सामयिक सफाई समाधान के रूप में किया जाता है। सोडियम बाइसल्फाइट (NaHSO<sub>3</sub>) सफेद क्रिस्टल या क्रिस्टलीय पाउडर के रूप में दिखाई देता है जिसका उपयोग खाद्य प्रसंस्करण, फोटोग्राफी में किया जाता है। सोडियम क्रोमेट (Na<sub>2</sub>CrO<sub>4</sub>) एक पीले क्रिस्टलीय ठोस के रूप में दिखाई देता है जिसका उपयोग पेंट और स्याही के लिए पिगमेंट बनाने के लिए किया जाता है। सोडियम थायोसल्फेट (Na<sub>2</sub>O<sub>3</sub>S<sub>2</sub>) एक अकार्बनिक सोडियम नमक है, यह साइनाइड विषाक्तता के लिए एक मारक, एक नेफ्रोप्रोटेक्टिव घटक और एक एंटीफंगल दवा के रूप में मुख्य भूमिका निभाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Chhath Puja is the main festival of which state?</p>",
                    question_hi: "<p>19. छठ पूजा किस राज्य का प्रमुख त्यौहार है ?</p>",
                    options_en: ["<p>Bihar</p>", "<p>Tamil Nadu</p>", 
                                "<p>Karnataka</p>", "<p>Assam</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>तमिलनाडु</p>",
                                "<p>कर्नाटक</p>", "<p>असम</p>"],
                    solution_en: "<p>19.(a) <strong>Bihar.</strong> Chhath Puja, celebrated on the sixth day of Kartik in the Hindu calendar, is also known as Surya Shashthi. This bathing festival involves four days of abstinence and religious purity, marking a rigorous spiritual ritual. Other notable festivals in Bihar include Rajgir Mahotsav, Mahaveer Jayanti, Buddha Jayanti, and Makar Sankranti.</p>",
                    solution_hi: "<p>19.(a) <strong>बिहार।</strong> हिंदू कैलेंडर में कार्तिक के छठे दिन मनाई जाने वाली छठ पूजा को सूर्य षष्ठी के नाम से भी जाना जाता है। इस स्नान पर्व में चार दिनों तक संयम और धार्मिक शुद्धता का पालन किया जाता है, जो एक कठोर आध्यात्मिक अनुष्ठान का प्रतीक है। बिहार के अन्य उल्लेखनीय त्योहारों में राजगीर महोत्सव, महावीर जयंती, बुद्ध जयंती और मकर संक्रांति शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Vindhyashakti was the founder of which of the following dynasties?</p>",
                    question_hi: "<p>20. निम्नलिखित में से किस राजवंश की स्थापना विंध्यशक्ति द्वारा की गई थी?</p>",
                    options_en: ["<p>Pallava</p>", "<p>Maukhari</p>", 
                                "<p>Vakataka</p>", "<p>Chalukya</p>"],
                    options_hi: ["<p>पल्लव</p>", "<p>मौखरि</p>",
                                "<p>वाकाटक</p>", "<p>चालुक्य</p>"],
                    solution_en: "<p>20.(c) <strong>Vakataka</strong> dynasty originated from the Deccan in the mid-3rd century CE. Vakatakas were the most important successors of the Satavahanas in the Deccan and coexistent with the Guptas in northern India. Other dynasties and their founders: Pallava dynasty (Simhavishnu), Maukhari dynasty (Harivarman), and Chalukya dynasty (Pulakeshin I).</p>",
                    solution_hi: "<p>20.(c) <strong>वाकाटक</strong> वंश की उत्पत्ति तीसरी शताब्दी के मध्य में दक्कन से हुई थी। वाकाटक दक्कन में सातवाहनों के सबसे महत्वपूर्ण उत्तराधिकारी थे और उत्तर भारत में गुप्तों के साथ सह-अस्तित्व में थे। अन्य राजवंश एवं उनके संस्थापक: पल्लव वंश (सिंहविष्णु), मौखरी वंश (हरिवर्मन) और चालुक्य वंश (पुलकेशिन प्रथम)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which Indian singer wrote &lsquo;Raag Sarita&rsquo;?</p>",
                    question_hi: "<p>21. किस भारतीय गायक/गायिका ने \'राग सरिता\' लिखा था?</p>",
                    options_en: ["<p>Kesarbai Kerkar</p>", "<p>Balasaheb Poonchwale</p>", 
                                "<p>Kumar Gandharva</p>", "<p>Chintaman Raghunath Vyas</p>"],
                    options_hi: ["<p>केसरबाई केरकर</p>", "<p>बालासाहेब पूंछवाले</p>",
                                "<p>कुमार गंधर्व</p>", "<p>चिंतामन रघुनाथ व्यास</p>"],
                    solution_en: "<p>21.(d) <strong>Chintaman Raghunath Vyas</strong> was an Indian classical singer, renowned for his khyal style. He received the Padma Bhushan in 1992 and the Sangeet Natak Akademi Award in 1987. He researched traditional ragas and bandishes, composing over 200 bandishes in various ragas under the pen name Gunijaan. In tribute to his guru, Gunidas, he founded the Gunidas Sangeet Sammelan in 1977.</p>",
                    solution_hi: "<p>21.(d) <strong>चिंतामन रघुनाथ व्यास </strong>एक भारतीय शास्त्रीय गायक थे, जो अपनी ख्याल शैली के लिए प्रसिद्ध थे। उन्हें 1992 में पद्म भूषण और 1987 में संगीत नाटक अकादमी पुरस्कार मिला। उन्होंने पारंपरिक रागों और बंदिशों पर शोध किया, और गुनीजान नाम से विभिन्न रागों में 200 से अधिक बंदिशों की रचना की। अपने गुरु गुनीदास को श्रद्धांजलि देने के लिए उन्होंने 1977 में गुनीदास संगीत सम्मेलन की स्थापना की।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. In which of the following areas did the Uprising of Khurda in 1817 take place?</p>",
                    question_hi: "<p>22. 1817 में खुर्दा का विद्रोह निम्नलिखित में से किस क्षेत्र में किया गया था ?</p>",
                    options_en: ["<p>United Province</p>", "<p>Odisha</p>", 
                                "<p>Madras</p>", "<p>Bengal</p>"],
                    options_hi: ["<p>संयुक्त प्रांत</p>", "<p>ओडिशा</p>",
                                "<p>मद्रास</p>", "<p>बंगाल</p>"],
                    solution_en: "<p>22.(b) <strong>Odisha.</strong> The Khurda Revolt of 1817 was an armed uprising against the British, sparked by the rapid and disruptive changes introduced by the East India Company, which agitated the local population. This revolt also impacted other districts of Odisha, such as Cuttack. It is also known as the Paika Rebellion or Paika Bidroha, and it was led by Bakshi Jagabandhu.</p>",
                    solution_hi: "<p>22.(b) <strong>ओडिशा।</strong> 1817 में खुर्दा का विद्रोह अंग्रेजों के खिलाफ एक सशस्त्र विद्रोह था, जो ईस्ट इंडिया कंपनी द्वारा शुरू किए गए तेज़ और विध्वंसकारी परिवर्तनों से प्रेरित था, जिसने स्थानीय आबादी को आक्रोशित कर दिया था। इस विद्रोह ने ओडिशा के कटक जैसे अन्य जिलों को भी प्रभावित किया था। इसे पाइका विद्रोह के नाम से भी जाना जाता है, और इसका नेतृत्व बक्शी जगबंधु ने किया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. The central bank of India or Reserve Bank of India was created before Independence, in the year 1934. The recommendation to create a central bank was made by a commission called:</p>",
                    question_hi: "<p>23. भारत का केंद्रीय बैंक या भारतीय रिज़र्व बैंक स्वतंत्रता से पहले 1934 में बनाया गया था। एक केंद्रीय बैंक बनाने की सिफारिश _________ नामक एक आयोग द्वारा की गई थी।</p>",
                    options_en: ["<p>Dr. Raja Chelliah Committee</p>", "<p>Kothari Commission</p>", 
                                "<p>Narasimham Committee</p>", "<p>Hilton Young Commission</p>"],
                    options_hi: ["<p>डॉ. राजा चिल्लैया समिति</p>", "<p>कोठारी आयोग</p>",
                                "<p>नरसिम्हम समिति</p>", "<p>हिल्टन यंग आयोग</p>"],
                    solution_en: "<p>23.(d) <strong>Hilton Young Commission</strong>. It is also known as the Royal Commission on Indian Currency and Finance, was established in 1926. The Reserve Bank of India (RBI) is India\'s central bank and regulatory body overseeing the Indian banking system. It manages the control, issuance, and supply of the Indian rupee. The Reserve Bank of India (RBI) was established on April 1, 1935, in accordance with the provisions of the Reserve Bank of India Act, 1934.</p>",
                    solution_hi: "<p>23.(d)<strong> हिल्टन यंग आयोग</strong>। इसे भारतीय मुद्रा और वित्त पर रॉयल आयोग के रूप में भी जाना जाता है, इसकी स्थापना 1926 में हुई थी। भारतीय रिज़र्व बैंक (RBI) भारत का केंद्रीय बैंक और भारतीय बैंकिंग प्रणाली की देखरेख करने वाला नियामक निकाय है। यह भारतीय रुपये के नियंत्रण, जारीकरण और आपूर्ति का प्रबंधन करता है। भारतीय रिज़र्व बैंक (RBI) की स्थापना 1 अप्रैल, 1935 को भारतीय रिज़र्व बैंक अधिनियम, 1934 के प्रावधानों के अनुसार की गई थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Sundari trees are found in which type of forests in India?</p>",
                    question_hi: "<p>24. सुंदरी वृक्ष भारत में किस प्रकार के वनों में पाए जाते हैं?</p>",
                    options_en: ["<p>Mangrove forests</p>", "<p>Tropical deciduous forests</p>", 
                                "<p>The thorn forests and scrubs</p>", "<p>Tropical evergreen forests</p>"],
                    options_hi: ["<p>मैंग्रोव वनों में</p>", "<p>उष्णकटिबंधीय पर्णपाती वनों में</p>",
                                "<p>कंटक वनों और झाड़ियों में</p>", "<p>उष्णकटिबंधीय सदाबहार वनों में</p>"],
                    solution_en: "<p>24.(a) <strong>Mangrove forests.</strong> Sundari tree is the most abundant tree in the mangrove forests of the Sunderban delta in West Bengal and the Andaman and Nicobar Islands. The Sunderbans are named after the Sundari tree. Mangrove forests grow only at tropical and subtropical latitudes near the equator because they cannot withstand freezing temperatures. Uppu ponna, Boddu ponna, Urada, Mada, Telli Mada, Gundu mada, Kadili and Bella are some of the typical vegetation of the mangroves.</p>",
                    solution_hi: "<p>24.(a) <strong>मैंग्रोव वनों</strong> <strong>में</strong>। सुंदरी वृक्ष पश्चिम बंगाल और अंडमान एवं निकोबार द्वीप समूह में सुंदरबन डेल्टा के मैंग्रोव वनों में सबसे प्रचुर मात्रा में पाया जाने वाला वृक्ष है। सुंदरबन का नाम सुंदरी वृक्ष के नाम पर रखा गया है। मैंग्रोव वन भूमध्य रेखा के पास केवल उष्णकटिबंधीय और उपोष्णकटिबंधीय अक्षांशों पर उगते हैं क्योंकि वे ठंडे तापमान को सहन नहीं कर सकते है। उप्पू पोन्ना, बोड्डू पोन्ना, उरदा, मादा, तेली मादा, गुंडू मादा, कदिली और बेला मैंग्रोव की कुछ विशिष्ट वनस्पतियाँ हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Which team won the T20 cricket event, Syed Mushtaq Ali Trophy 2022-2023?</p>",
                    question_hi: "<p>25. टी-20 क्रिकेट प्रतियोगिता, 2022-2023 की सैयद मुश्ताक अली ट्रॉफी किस टीम ने जीती?</p>",
                    options_en: ["<p>Mumbai</p>", "<p>Punjab</p>", 
                                "<p>Vidarbha</p>", "<p>Himachal Pradesh</p>"],
                    options_hi: ["<p>मुंबई</p>", "<p>पंजाब</p>",
                                "<p>विदर्भ</p>", "<p>हिमाचल प्रदेश</p>"],
                    solution_en: "<p>25.(a) <strong>Mumbai.</strong> This was the 15th edition of the Syed Mushtaq Ali Trophy, an annual Twenty20 tournament in India organized by the Board of Control for Cricket in India (BCCI). Mumbai won this tournament by defeating Himachal Pradesh. The 16th edition, held in 2023-2024, was won by Punjab after they defeated Baroda. The inaugural competition in 2006&ndash;07 was won by Tamil Nadu, who defeated Punjab.</p>",
                    solution_hi: "<p>25.(a) <strong>मुंबई।</strong> यह सैयद मुश्ताक अली ट्रॉफी का 15वां संस्करण था, जो भारतीय क्रिकेट कंट्रोल बोर्ड (BCCI) द्वारा आयोजित भारत में एक वार्षिक ट्वेंटी-20 टूर्नामेंट है। मुंबई ने हिमाचल प्रदेश को हराकर यह टूर्नामेंट जीता था। 2023-2024 में आयोजित 16वें संस्करण को पंजाब ने बड़ौदा को हराकर जीता था। 2006-07 में उद्घाटन प्रतियोगिता तमिलनाडु ने जीती थी, जिसने पंजाब को हराया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>