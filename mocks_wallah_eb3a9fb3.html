<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. What protects the inner lining of the stomach from the action of acid, under normal <br>Conditions?</p>",
                    question_hi: "<p>1. सामान्य परिस्थितियों में आमाशय की आंतरिक परत को अम्ल की क्रिया से कौन बचाता है?</p>",
                    options_en: ["<p>Enzymes</p>", "<p>Gastric Glands</p>", 
                                "<p>Mucus</p>", "<p>Villi</p>"],
                    options_hi: ["<p>एंजाइम</p>", "<p>गैस्ट्रिक ग्रंथियां</p>",
                                "<p>श्लेष्म</p>", "<p>विल्ली</p>"],
                    solution_en: "<p>1.(c) <strong>Mucus. Enzymes - </strong>Proteins that help speed up chemical reactions in our bodies. Three types of enzymes:- Amylase, Lipase, Protease.<strong> Gastric Glands :</strong>- Inner lining of the stomach that secrete gastric juice and protective mucus<strong>. Villi :- </strong>Finger-like projections which are richly supplied with blood vessels.</p>",
                    solution_hi: "<p>1.(c) <strong>श्लेष्म। एंजाइम</strong> - प्रोटीन जो हमारे शरीर में रासायनिक प्रतिक्रियाओं को तीव्र करने में मदद करते हैं। तीन प्रकार के एंजाइम:- एमाइलेज, लाइपेज, प्रोटीज। <strong>जठर ग्रंथियाँ </strong>- आमाशय की आंतरिक परत की सुरक्षा के लिए जठर रस और श्लेष्म का स्राव करती है। <strong>विली</strong>(Villi):- उंगली जैसे उभार जिनमें प्रचुर मात्रा में रक्त वाहिकाएं होती हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. What helps in food digestion?</p>",
                    question_hi: "<p>2. भोजन के पाचन में क्या मदद करता है?</p>",
                    options_en: ["<p>Oxygen</p>", "<p>Carbon</p>", 
                                "<p>Helium</p>", "<p>Saliva</p>"],
                    options_hi: ["<p>ऑक्सीजन</p>", "<p>कार्बन</p>",
                                "<p>हीलियम</p>", "<p>लार</p>"],
                    solution_en: "<p>2.(d) <strong>Saliva</strong>. Digestion begins in the mouth, well before food reaches the stomach. Saliva contains special enzymes amylase which breaks down starch into sugars. It also contains an enzyme called <strong>lingual lipase,</strong> which breaks down fats. Digestion of protein starts in stomach.</p>",
                    solution_hi: "<p>2.(d) <strong>लार </strong>। भोजन के आमाशय में पहुंचने से बहुत पहले ही पाचन क्रिया मुंह में शुरू हो जाती है। लार में विशेष एंजाइम एमाइलेज होता है जो स्टार्च को शर्करा में तोड़ देता है। इसमें <strong>लिंगुअल लाइपेज</strong> नामक एंजाइम भी होता है, जो वसा को तोड़ने में मदद करता है। प्रोटीन का पाचन आमाशय में शुरू होता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The inner lining of the small intestine has numerous finger like projections that are called:</p>",
                    question_hi: "<p>3. छोटी आंत की आंतरिक परत में कई उँगलियों जैसे प्रक्षेपण होते हैं जिन्हें__________ कहा जाता है।</p>",
                    options_en: ["<p>Villi</p>", "<p>Tissues</p>", 
                                "<p>Cells</p>", "<p>Enzymes</p>"],
                    options_hi: ["<p>विल्ली</p>", "<p>ऊतक</p>",
                                "<p>कोशिका</p>", "<p>एंजाइम</p>"],
                    solution_en: "<p>3.(a) <strong>Villi</strong>. The Villi helps to increase the surface area for the absorption of the digested food. Each villus is covered by a layer of epithelium and contains blood vessels and lymphatic Vessels. Food spreads through the epithelium into the blood vessels. The <strong>cell </strong>is the basic structural and functional unit of life forms. <strong>Enzymes </strong>are proteins that help speed up metabolism or the chemical reactions in our bodies. <strong>Tissue </strong>is a group of cells that have similar structure and that function together as a unit.</p>",
                    solution_hi: "<p>3.(a) <strong>विल्ली </strong>(Villi)। विल्ली पचे हुए भोजन के अवशोषण के लिए सतह क्षेत्र को बढ़ाने में मदद करता है। प्रत्येक अंकुर उपकला की एक परत से ढका होता है और इसमें रक्त वाहिकाएं और लसीका वाहिकाएं होती हैं। भोजन उपकला के माध्यम से रक्त वाहिकाओं में फैलता है। <strong>कोशिका </strong>जीवन रूपों की बुनियादी संरचनात्मक और कार्यात्मक इकाई है। <strong>एंजाइम </strong>प्रोटीन होते हैं जो हमारे शरीर में उपापचय या रासायनिक अभिक्रियाओं को तेज करने में मदद करते हैं। <strong>ऊतक </strong>कोशिकाओं का एक समूह है जिनकी संरचना समान होती है और जो एक इकाई के रूप में मिलकर कार्य करते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which acid is produced by the human stomach?</p>",
                    question_hi: "<p>4. मनुष्य के आमाशय में कौन सा अम्ल उत्पन्न होता है?</p>",
                    options_en: ["<p>Lactic acid</p>", "<p>Sulphuric acid</p>", 
                                "<p>Citric acid</p>", "<p>Hydrochloric acid</p>"],
                    options_hi: ["<p>लैक्टिक अम्ल</p>", "<p>सल्फ्यूरिक अम्ल</p>",
                                "<p>साइट्रिक अम्ल</p>", "<p>हाइड्रोक्लोरिक अम्ल</p>"],
                    solution_en: "<p>4.(d)<strong> Hydrochloric acid.</strong> <strong>Lactic acid</strong> is mainly produced in muscle cells and red blood cells. <strong>Sulfuric acid</strong> is found in battery acid and in Earth\'s acid rain. <strong>Citric acid </strong>is found naturally in citrus fruits, especially lemons and limes.</p>",
                    solution_hi: "<p>4.(d) <strong>हाइड्रोक्लोरिक अम्ल</strong>। <strong>लैक्टिक अम्ल </strong>मुख्य रूप से मांसपेशियों की कोशिकाओं और लाल रक्त कोशिकाओं में निर्मित होता है।<strong> सल्फ्यूरिक अम्ल </strong>बैटरी अम्ल और पृथ्वी की अम्लीय वर्षा में पाया जाता है। <strong>साइट्रिक अम्ल</strong> खट्टे फलों, विशेषकर नीबू में प्राकृतिक रूप से पाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. Which one of the following is the correct sequence of steps in the Digestive System?",
                    question_hi: "5. निम्नलिखित में से कौन सा पाचन तंत्र में चरणों का सही क्रम है?",
                    options_en: [" Ingestion, Absorption, Egestion, Digestion ", " Ingestion, Digestion, Absorption, Egestion  ", 
                                " Egestion, Ingestion, Absorption,Digestion ", " Absorption, Egestion, Digestion,Ingestion "],
                    options_hi: [" अंतर्ग्रहण, अवशोषण, उत्सर्जन, पाचन", " अंतर्ग्रहण, पाचन, अवशोषण, उत्सर्जन",
                                " उत्सर्जन, अंतर्ग्रहण, अवशोषण, पाचन", " अवशोषण, उत्सर्जन, पाचन, अंतर्ग्रहण"],
                    solution_en: "5.(b) Ingestion, Digestion, Absorption, Egestion. Digestion process involves the following steps: Ingestion - Refers to the taking of food. Digestion - Breaking up the large food compounds into smaller ones. Absorption - Digested food is absorbed into the body. Egestion - Removal of undigested food materials from the body.<br /> ",
                    solution_hi: "5.(b) अंतर्ग्रहण, पाचन, अवशोषण, उत्सर्जन। पाचन प्रक्रिया में निम्नलिखित चरण शामिल हैं: अंतर्ग्रहण - भोजन ग्रहण करने को संदर्भित करता है। पाचन - बड़े खाद्य पदार्थो को छोटे टुकड़ो में तोड़ना है। अवशोषण - पचा हुआ भोजन शरीर में अवशोषित हो जाता है। उत्सर्जन - शरीर से अपचित खाद्य पदार्थों को बाहर निकालना।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. What can humans NOT digest?</p>",
                    question_hi: "<p>6. मनुष्य ____ का पाचन नहीं कर सकता है ?</p>",
                    options_en: ["<p>Amino Acids</p>", "<p>Cellulose</p>", 
                                "<p>Glycogen</p>", "<p>Starch</p>"],
                    options_hi: ["<p>अमीनो अम्ल</p>", "<p>सेल्यूलोज</p>",
                                "<p>ग्लाइकोजन</p>", "<p>स्टार्च</p>"],
                    solution_en: "<p>6.(b) <strong>cellulose</strong>. The human body does not have the digestive mechanism to break the monosaccharide (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>C</mi><msub><mi>H</mi><mn>2</mn></msub><mi>O</mi></math>) n bonds of cellulose. Plant cell wall is made up of cellulose. Amino acids are molecules that combine to form proteins. Amino acids are building blocks of proteins. Glycogen is the stored form of glucose that is made up of many connected glucose molecules. Starch is a carbohydrate commonly found in nature and one of the primary sources of food energy for human beings.</p>",
                    solution_hi: "<p>6.(b) <strong>सेल्यूलोज</strong> । मानव शरीर में सेल्यूलोज के मोनोसैकेराइड (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>C</mi><msub><mi>H</mi><mn>2</mn></msub><mi>O</mi></math>) n बंध को तोड़ने के लिए पाचन तंत्र नहीं है। पादप कोशिका भित्ति सेल्यूलोज की बनी होती है। अमीनो अम्ल ऐसे अणु होते हैं जो मिलकर प्रोटीन बनाते हैं। अमीनो अम्ल प्रोटीन के निर्माण खंड (building blocks) हैं। ग्लाइकोजन ग्लूकोज का संग्रहीत रूप है जो कई जुड़े हुए ग्लूकोज अणुओं से बना होता है। स्टार्च आमतौर पर प्रकृति में पाया जाने वाला कार्बोहाइड्रेट है और मनुष्य के लिए खाद्य ऊर्जा के प्राथमिक स्रोतों में से एक है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. After digestion, protein is converted into:</p>",
                    question_hi: "<p>7. पाचन के बाद, प्रोटीन _________में परिवर्तित हो जाता है</p>",
                    options_en: ["<p>carbohydrates</p>", "<p>amino acids</p>", 
                                "<p>small globules</p>", "<p>starch</p>"],
                    options_hi: ["<p>कार्बोहाइड्रेट</p>", "<p>अमीनो अम्ल</p>",
                                "<p>छोटे ग्लोब्यूल्स</p>", "<p>स्टार्च</p>"],
                    solution_en: "<p>7.(b) <strong>Amino acids</strong>. Once a protein source reaches your stomach, hydrochloric acid and enzymes called proteases break it down into smaller chains of amino acids. Amino acids contain amino (&ndash;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>N</mi><msub><mi>H</mi><mn>2</mn></msub></math>) and carboxyl (&ndash;COOH) functional groups. Carbohydrate breaks into Glucose or Fructose. Bone marrow contains fat in the form of small globules. Starch is a tasteless, odorless fine white powder that is insoluble in cold water, alcohol, and other solvents.</p>",
                    solution_hi: "<p>7.(b)<strong> अमीनो अम्ल</strong>। एक बार जब प्रोटीन का स्रोत आपके पेट में पहुंच जाता है, तो हाइड्रोक्लोरिक अम्ल और प्रोटीज नामक एंजाइम इसे अमीनो अम्ल की छोटी श्रृंखलाओं में तोड़ देते हैं। अमीनो अम्ल में अमीनो (-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>N</mi><msub><mi>H</mi><mn>2</mn></msub></math>) और कार्बोक्सिल (-COOH) कार्यात्मक समूह होते हैं। कार्बोहाइड्रेट ग्लूकोज या फ्रक्टोज में टूट जाता है। अस्थि मज्जा में छोटी-छोटी गोलिकाओं के रूप में वसा होती है। स्टार्च एक स्वादहीन, गंधहीन बारीक़ सफेद पाउडर है जो ठंडे जल, एलकोहल और अन्य विलायकों में अघुलनशील होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Where in the body is villi found?</p>",
                    question_hi: "<p>8. शरीर में विली कहाँ पाया जाता है</p>",
                    options_en: ["<p>Small intestine</p>", "<p>Liver</p>", 
                                "<p>Large intestine</p>", "<p>Stomach</p>"],
                    options_hi: ["<p>छोटी आंत</p>", "<p>यकृत</p>",
                                "<p>बड़ी आंत</p>", "<p>आमाशय</p>"],
                    solution_en: "<p>8.(a)<strong> Small intestine -</strong> Especially in the lower part of the duodenum and the jejunum are called plicae circulares. <strong>Villi </strong>- Small finger-like projections that absorb nutrients from the lumen in the small intestine. <strong>Liver </strong>- Removes waste products and foreign substances from the bloodstream, regulating blood sugar levels, and creating essential nutrients. <strong>Stomach </strong>- It is a J-shaped organ that digests food<strong>. Large intestine</strong> - Absorbs water, vitamins, and electrolytes from waste material.</p>",
                    solution_hi: "<p>8.(a) <strong>छोटी आंत -</strong> विशेष रूप से ग्रहणी और मध्यान्त्र के निचले भाग को प्लिका सर्कुलर कहा जाता है। <strong>विली </strong>- छोटी उंगली जैसे उभार जो छोटी आंत में लुमेन से पोषक तत्वों को अवशोषित करते हैं। यकृत - रक्तप्रवाह से अपशिष्ट उत्पादों और विदेशी पदार्थों को निकालता है, रक्त शर्करा के स्तर को नियंत्रित करता है और आवश्यक पोषक तत्व बनाता है। <strong>आमाशय </strong>- यह एक J-आकार का अंग है जो भोजन को पचाता है।<strong> बड़ी आंत </strong>- अपशिष्ट पदार्थों से जल , विटामिन और इलेक्ट्रोलाइट्स को अवशोषित करती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. In human beings, where does the complete digestion of carbohydrates, proteins and fats take place?</p>",
                    question_hi: "<p>9. मनुष्य में कार्बोहाइड्रेट, प्रोटीन और वसा का पूर्ण पाचन कहाँ होता है?</p>",
                    options_en: ["<p>Stomach</p>", "<p>Rectum</p>", 
                                "<p>Small intestine</p>", "<p>Large intestine</p>"],
                    options_hi: ["<p>आमाशय</p>", "<p>मलाशय</p>",
                                "<p>छोटी आंत</p>", "<p>बड़ी आंत</p>"],
                    solution_en: "<p>9.(c) <strong>small intestine</strong>. Digestion begins in the mouth with chewing and ends in the small intestine. <strong>Large intestine</strong> - responsible for processing indigestible food material (chyme) after most nutrients are absorbed in the small intestine. <strong>Rectum </strong>- to act as a temporary storehouse for feces. <strong>Stomach</strong> - creates digestive juices and breaks down food.</p>",
                    solution_hi: "<p>9.(c) <strong>छोटी आंत। </strong>पाचन मुंह में चबाने से शुरू होता है और छोटी आंत में समाप्त होता है। <strong>बड़ी आंत -</strong> छोटी आंत में अधिकांश पोषक तत्वों के अवशोषित होने के बाद अपाच्य खाद्य पदार्थ (chyme) के प्रसंस्करण के लिए जिम्मेदार होती है। <strong>मलाशय </strong>- मल के लिए एक अस्थायी भंडारगृह के रूप में कार्य करता है। <strong>अमाशय </strong>में पाचक रस बनता है और जो भोजन को तोड़ने का कार्य करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. The inner walls of the small intestine have thousands of finger-like outgrowths. These are called:",
                    question_hi: "10. छोटी आँत की भीतरी दीवारों में हज़ारों उँगलियों के आकार के बहिर्गमन होते हैं। इन्हें कहा जाता है:",
                    options_en: [" lumps", " myomas", 
                                " villi", " bronchi<br /> "],
                    options_hi: [" लुम्प्स ", " मायोमास",
                                " विली", " ब्रोंची"],
                    solution_en: "<p>10.(c) <strong>Villi </strong>- These structures greatly increase the surface area of the small intestine, allowing for efficient absorption of nutrients from digested food. <strong>Lumps </strong>- Any protuberance or swelling in the body. <strong>Myomas </strong>- Fibrous and non-cancerous tumors developed in the uterus. <strong>Bronchi</strong> - Tube which connects to the trachea and directs the air to the right and left lung.</p>",
                    solution_hi: "<p>10.(c)<strong>विली </strong>- ये संरचनाएं छोटी आंत के सतह क्षेत्र को काफी बढ़ा देती हैं, जिससे पचे हुए भोजन से पोषक तत्वों का कुशल अवशोषण संभव हो जाता है। <strong>गांठ </strong>- शरीर में कोई उभार या सूजन। <strong>मायोमास</strong> - गर्भाशय में विकसित रेशेदार और गैर-कैंसरयुक्त ट्यूमर। <strong>ब्रोंची </strong>- नली जो श्वासनली से जुड़ती होती है और हवा को दाएं और बाएं फेफड़े तक निर्देशित करती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Intestinal juice converts protein in food into which molecules?</p>",
                    question_hi: "<p>11. आंतों का रस भोजन में उपस्थित प्रोटीन को किन अणुओं में परिवर्तित करता है?</p>",
                    options_en: ["<p>Glycerol</p>", "<p>Fatty acid</p>", 
                                "<p>Amino acid</p>", "<p>Glucose</p>"],
                    options_hi: ["<p>ग्लिसरॉल</p>", "<p>वसा अम्ल</p>",
                                "<p>एमिनो अम्ल</p>", "<p>शर्करा</p>"],
                    solution_en: "<p>11.(c) <strong>Amino acid.</strong> Intestinal juices (including maltase, lactase, lipase etc) are secreted by intestinal glands. Enzymes present in intestinal juice convert complex carbohydrates into glucose, Fats into fatty acids and glycerol. <strong>Glycerol </strong>- A simple polyol compound. It is a colorless, odorless, viscous liquid that is sweet-tasting and non-toxic.<strong> Fatty acids</strong> - The building blocks of the fat in our bodies and in the food we eat. <strong>Glucose</strong> - The main type of sugar in the blood and is the major source of energy for the body\'s cells.</p>",
                    solution_hi: "<p>11.(c) <strong>अमीनो अम्ल ।</strong> अग्नाशयी रस (माल्टेज, लैक्टेज, लाइपेज आदि सहित) अग्न्याशय ग्रंथियों द्वारा स्रावित होता हैं। अग्नाशयी रस में मौजूद एंजाइम जटिल कार्बोहाइड्रेट को ग्लूकोज में, वसा को वसीय अम्ल और ग्लिसरॉल में परिवर्तित कर देते हैं। <strong>ग्लिसरॉल </strong>- एक साधारण पॉलीऑयल यौगिक है । यह एक रंगहीन, गंधहीन, चिपचिपा तरल पदार्थ है जो मीठा स्वाद वाला और बिना विषैला होता है। <strong>वसा अम्ल</strong> - हमारे शरीर में और हमारे द्वारा खाए जाने वाले भोजन में वसा के निर्माण खंड होते है । <strong>ग्लूकोज </strong>- रक्त में शर्करा का मुख्य प्रकार है और शरीर की कोशिकाओं के लिए ऊर्जा का प्रमुख स्रोत है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following organs of the digestive system has the shape similar to the English alphabet &ldquo;J&rdquo;?</p>",
                    question_hi: "<p>12. निम्नलिखित में से पाचन तंत्र के किस अंग का आकार अंग्रेजी वर्णमाला \"J\" के समान है?</p>",
                    options_en: ["<p>Intestine</p>", "<p>Liver</p>", 
                                "<p>Stomach</p>", "<p>Mouth</p>"],
                    options_hi: ["<p>आंत</p>", "<p>यकृत</p>",
                                "<p>आमाशय</p>", "<p>मुँह</p>"],
                    solution_en: "<p>12.(c) <strong>Stomach</strong> - Widest part of the digestive tube in which food is stored, churned, and mixed with gastric juices secreted by its lining. <strong>Intestine </strong>- A muscular tube which extends from the lower end of your stomach to anus, the lower opening of the digestive tract. <strong>Liver </strong>- Essential for digesting food and ridding the body of toxic substances. Digestion begins in the <strong>mouth</strong>.</p>",
                    solution_hi: "<p>12.(c) <strong>आमाशय</strong> - पाचन नली का सबसे चौड़ा भाग जिसमें भोजन एकत्र होता है, मथता है और उसकी परत से स्रावित जठर रस के साथ मिश्रित होता है। आंत - एक मांसपेशीय नली जो आपके आमाशय के निचले सिरे से गुदा तक फैली होती है, जो पाचन तंत्र का निचला द्वार है। <strong>यकृत </strong>- भोजन को पचाने और शरीर से विषाक्त पदार्थों को बाहर निकालने के लिए आवश्यक है। पाचन की शुरुआत मुँह से होती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which one of the following is the largest compartment of stomach in hoofed animals?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सा खुर वाले जानवरों में पेट का सबसे बड़ा भाग होता है?</p>",
                    options_en: ["<p>Abomas</p>", "<p>Rumen</p>", 
                                "<p>Reticulum</p>", "<p>Omasum</p>"],
                    options_hi: ["<p>अबोमास</p>", "<p>रुमेन</p>",
                                "<p>जालिका</p>", "<p>ओमासम</p>"],
                    solution_en: "<p>13.(b) <strong>Rumen.</strong> A ruminant animal has four compartments to the stomach. It includes the rumen (largest), reticulum, omasum and abomasum. <strong>Examples </strong>- Cattle, Sheep, Goats, buffalo and Deer.</p>",
                    solution_hi: "<p>13.(b) <strong>रुमेन। </strong>जुगाली करने वाले जानवर के अमाशय में चार भाग होते हैं। इसमें रुमेन (सबसे बड़ा), रेटिकुलम, ओमेसम और एबोमासम शामिल हैं। <strong>उदाहरण </strong>- मवेशी, भेड़, बकरी, भैंस और हिरण।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The process of digestion of food in humans begins in ______ and is completed in _____.</p>",
                    question_hi: "<p>14. मनुष्यों में भोजन के पाचन की प्रक्रिया ______ में शुरू होती है और _____ में पूरी होती है।</p>",
                    options_en: ["<p>mouth; small intestine</p>", "<p>food pipe; small intestine</p>", 
                                "<p>food pipe; large intestine</p>", "<p>stomach; large intestine</p>"],
                    options_hi: ["<p>मुँह; छोटी आँत</p>", "<p>भोजन नली; छोटी आँत</p>",
                                "<p>भोजन नली; बड़ी आँत</p>", "<p>पेट; बड़ी आँत</p>"],
                    solution_en: "<p>14.(a) <strong>mouth; small intestine.</strong> Digestion of food: Begins in the mouth through chewing and the action of saliva. <strong>Stomach</strong> - Digested food moves to the stomach where gastric juices break down proteins. <strong>Small Intestine -</strong> The process is completed in the small intestine, where bile and pancreatic enzymes digest fats, carbohydrates, and proteins, and nutrients are absorbed.<strong> Large Intestine -</strong> The remaining waste material enters the large intestine for water absorption, and the waste is eliminated through the rectum and anus.</p>",
                    solution_hi: "<p>14.(a) <strong>मुँह; छोटी आंत।</strong> <strong>भोजन का पाचन:</strong> मुंह में चबाने और लार की क्रिया से शुरू होता है। <strong>आमाशय </strong>(पेट) - पचा हुआ भोजन पेट में जाता है जहां गैस्ट्रिक रस प्रोटीन को तोड़ता है। <strong>छोटी आंत -</strong> यह प्रक्रिया छोटी आंत में पूर्ण होती है, जहां पित्तरस और अग्नाशयी एंजाइम वसा, कार्बोहाइड्रेट और प्रोटीन को पचाते हैं और पोषक तत्वों को अवशोषित करते हैं। <strong>बड़ी आंत - </strong>बचा हुआ अपशिष्ट पदार्थ जल अवशोषण के लिए बड़ी आंत में प्रवेश करता है, और अपशिष्ट मलाशय तथा गुदा के माध्यम से बाहर निकल जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In the human body, digestion of fat takes place primarily in the:</p>",
                    question_hi: "<p>15. मानव शरीर में, वसा का पाचन मुख्य रूप _____ में होता है।</p>",
                    options_en: ["<p>kidneys</p>", "<p>large intestine</p>", 
                                "<p>lungs</p>", "<p>small intestine</p>"],
                    options_hi: ["<p>गुर्दे</p>", "<p>बड़ी आंत</p>",
                                "<p>फेफड़ों</p>", "<p>छोटी आंत</p>"],
                    solution_en: "<p>15.(d<strong>) Small intestine. </strong>It is the longest part of the digestive system which extends from the stomach to the large intestine. It is divided into three parts (Duodenum, jejunum and ileum). Large intestine - The walls of the large intestine absorb water. Functions: Absorption of water, minerals and some salts.The Digestive system of humans consists of the mouth (Digestion begin), buccal cavity, pharynx, esophagus, stomach, small intestine, large intestine, rectum, and digestive gland. Lengths: Small intestine - 7.5 meter, Large intestine - 1.5 meter.</p>",
                    solution_hi: "<p>15.(d) <strong>छोटी आंत।</strong> यह पाचन तंत्र का सबसे लंबा भाग है जो आमाशय से बड़ी आंत तक फैला होता है। यह तीन भागों (डुओडेनम, जेजुनम और इलियम) में विभाजित होता है। बड़ी आंत - बड़ी आंत में दीवारें जल को अवशोषित करती हैं। कार्य: जल , खनिजों और कुछ लवणों का अवशोषण करना । मनुष्य के पाचन तंत्र में मुंह (पाचन प्रारंभ), मुख गुहा, ग्रसनी, अन्नप्रणाली, आमाशय , छोटी आंत, बड़ी आंत, मलाशय और पाचन ग्रंथि शामिल हैं। लंबाई: छोटी आंत - 7.5 मीटर, बड़ी आंत - 1.5 मीटर।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>