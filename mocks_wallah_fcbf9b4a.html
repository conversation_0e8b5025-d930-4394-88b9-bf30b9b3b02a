<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 22</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">22</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 20
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 21,
                end: 21
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Four of earliest civilisations / of the world / were located / on the banks of or near large rivers.</p>",
                    question_hi: "<p>1. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Four of earliest civilisations / of the world / were located / on the banks of or near large rivers.</p>",
                    options_en: ["<p>were located on the banks of</p>", "<p>of the world</p>", 
                                "<p>Four of earliest civilisations</p>", "<p>or near large rivers</p>"],
                    options_hi: ["<p>were located on the banks of</p>", "<p>of the world</p>",
                                "<p>Four of earliest civilisations</p>", "<p>or near large rivers</p>"],
                    solution_en: "<p>1.(c) Four of earliest civilisations.<br>The word earliest is a superlative degree and we always use the article \"<strong>the</strong>\" before a superlative degree. so we will put the article &lsquo;<strong>the</strong>&rsquo; before \"earliest\". Hence, \"four of the earliest civilisations\" is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(c) Four of earliest civilisations.<br>&lsquo;Earliest&rsquo; word एक superlative degree है और हम हमेशा superlative degree से पहले article \"<strong>the</strong>\" का प्रयोग करते हैं। इसलिए हम \"earliest\" से पहले article &lsquo;<strong>the</strong>&rsquo; को रखेंगे। अतः, &lsquo;four of the earliest civilisations&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>No sooner did Priya / get her report card / when she started jumping / with joy.</p>",
                    question_hi: "<p>2. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>No sooner did Priya / get her report card / when she started jumping / with joy.</p>",
                    options_en: ["<p>No sooner did Priya</p>", "<p>with joy</p>", 
                                "<p>get her report card</p>", "<p>when she started jumping</p>"],
                    options_hi: ["<p>No sooner did Priya</p>", "<p>with joy</p>",
                                "<p>get her report card</p>", "<p>when she started jumping</p>"],
                    solution_en: "<p>2.(d) when she started jumping.<br>&ldquo;<strong>No sooner&hellip;..than</strong>&rdquo; is a fixed conjunction pair. Hence, &lsquo;<strong>when</strong>&rsquo; will be replaced by &lsquo;<strong>than</strong>&rsquo; to make the given sentence grammatically correct.</p>",
                    solution_hi: "<p>2.(d) when she started jumping.<br>&ldquo;<strong>No sooner&hellip;..than</strong>&rdquo; एक fixed conjunction pair है। अतः, दिए गए sentence को grammatically सही बनाने के लिए \'<strong>when</strong>\' के स्थान पर \'<strong>than</strong>\' का प्रयोग किया जाएगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The weather / is much / more warmer / than it was a few days before.</p>",
                    question_hi: "<p>3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The weather / is much / more warmer / than it was a few days before.</p>",
                    options_en: ["<p>more warmer</p>", "<p>than it was a few days before.</p>", 
                                "<p>The weather</p>", "<p>is much</p>"],
                    options_hi: ["<p>more warmer</p>", "<p>than it was a few days before.</p>",
                                "<p>The weather</p>", "<p>is much</p>"],
                    solution_en: "<p>3.(a) more warmer. <br>When we do a comparison between two things, we don&rsquo;t use &lsquo;more&rsquo; with a comparative degree (warmer) because it becomes a case of <strong>superfluousness</strong>(not necessary). Hence, &lsquo;warmer&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(a) more warmer. <br>जब हम दो चीजों के बीच comparison करते हैं, तो हम comparative degree (warmer) के साथ &lsquo;more&rsquo; का प्रयोग नहीं करते हैं, क्योंकि यह <strong>superfluousness </strong>(not necessary) का case बन जाता है। अतः, &lsquo;warmer&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The visitors complained / at the poor accommodation / they were provided / during the tour.</p>",
                    question_hi: "<p>4. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The visitors complained / at the poor accommodation / they were provided / during the tour.</p>",
                    options_en: ["<p>they were provided</p>", "<p>at the poor accommodation</p>", 
                                "<p>The visitors complained</p>", "<p>during the tour</p>"],
                    options_hi: ["<p>they were provided</p>", "<p>at the poor accommodation</p>",
                                "<p>The visitors complained</p>", "<p>during the tour</p>"],
                    solution_en: "<p>4.(b) at the poor accommodation. <br>There is a prepositional error in the given sentence. The preposition &lsquo;<strong>at</strong>&rsquo; after &lsquo;complained&rsquo; should be replaced with &lsquo;<strong>about</strong>&rsquo;.&lsquo;Complain about&rsquo; means to express one&rsquo;s dissatisfaction with something. Similarly, in the given sentence, the visitors are showing their dissatisfaction with the poor accommodation provided to them. Hence, &lsquo;complained about the poor accommodation&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(b) at the poor accommodation. <br>दिए गए sentence में एक prepositional error है। &lsquo;Complained&rsquo; के बाद आने वाले preposition &lsquo;<strong>at</strong>&rsquo; के स्थान &lsquo;<strong>about</strong>&rsquo; का प्रयोग होना चाहिए। &lsquo;Complain about&rsquo; का अर्थ है किसी चीज़ के प्रति अपनी dissatisfaction व्यक्त करना। इसी तरह, दिए गए sentence में, visitors उन्हें प्रदान किए गए घटिया आवास (poor accommodation) के प्रति अपनी असंतुष्टि दिखा रहे हैं। अतः, &lsquo;complained about the poor accommodation&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If there is no error, select &lsquo;No error&rsquo;. <br>Modern science have / broken many myths / about our food and diet.</p>",
                    question_hi: "<p>5. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If there is no error, select &lsquo;No error&rsquo;. <br>Modern science have / broken many myths / about our food and diet.</p>",
                    options_en: ["<p>Modern science have</p>", "<p>about our food and diet</p>", 
                                "<p>broken many myths</p>", "<p>No error</p>"],
                    options_hi: ["<p>Modern science have</p>", "<p>about our food and diet</p>",
                                "<p>broken many myths</p>", "<p>No error</p>"],
                    solution_en: "<p>5.(a) Modern science have.<br>According to the &ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;Modern Science&rsquo; will be considered as a singular subject that will take &lsquo;<strong>has</strong>&rsquo; as a singular verb. Hence, &lsquo;Modern science has&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(a) Modern science have.<br>&ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo; के अनुसार, &lsquo;singular subject&rsquo; के साथ हमेशा &lsquo;singular verb&rsquo; तथा &lsquo;plural subject&rsquo; के साथ हमेशा &lsquo;plural verb&rsquo; का प्रयोग होता है। दिए गए sentence में, &lsquo;Modern Science&rsquo; को singular subject माना जाएगा जिसके साथ singular verb &lsquo;<strong>has</strong>&rsquo; का प्रयोग होगा। अतः, &lsquo;Modern science has&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Ranbir could not went / to the award ceremony / as he was busy / shooting / for a film / in Maldives.</p>",
                    question_hi: "<p>6. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Ranbir could not went / to the award ceremony / as he was busy / shooting / for a film / in Maldives.</p>",
                    options_en: ["<p>for a film</p>", "<p>Ranbir could not went</p>", 
                                "<p>to the award ceremony</p>", "<p>as he was busy</p>"],
                    options_hi: ["<p>for a film</p>", "<p>Ranbir could not went</p>",
                                "<p>to the award ceremony</p>", "<p>as he was busy</p>"],
                    solution_en: "<p>6.(b) Ranbir could not went.<br><span style=\"text-decoration: underline;\">Modal verbs</span> like can, will, could, shall, etc. generally take the <span style=\"text-decoration: underline;\">base form of a verb</span> with them. However, &lsquo;go&rsquo; is the base form of &lsquo;went&rsquo;. Hence, &lsquo;Ranbir could not go&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(b) Ranbir could not went.<br><span style=\"text-decoration: underline;\">Modal verbs</span> जैसे can, will, could, shall, आदि के साथ आमतौर पर <span style=\"text-decoration: underline;\">verb</span> की &lsquo;<span style=\"text-decoration: underline;\">base form</span>&rsquo; का प्रयोग होता है। हालाँकि, \'go\' \'went\' का base form है। अतः, \'Ranbir could not go\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>At the last meeting / which took place in November, / half the members submitted / their resignations on protest.</p>",
                    question_hi: "<p>7. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>At the last meeting / which took place in November, / half the members submitted / their resignations on protest.</p>",
                    options_en: ["<p>At the last meeting</p>", "<p>their resignations on protest</p>", 
                                "<p>half the members submitted</p>", "<p>which took place in November</p>"],
                    options_hi: ["<p>At the last meeting</p>", "<p>their resignations on protest</p>",
                                "<p>half the members submitted</p>", "<p>which took place in November</p>"],
                    solution_en: "<p>7.(b) their resignations on protest. <br>There is a prepositional error in the given sentence. The preposition &lsquo;<strong>on</strong>&rsquo; before &lsquo;protest&rsquo; must be <strong>replaced </strong>with &lsquo;<strong>in</strong>&rsquo; because &lsquo;in&rsquo; is used to show the condition of somebody/something. Hence, &lsquo;their resignations in protest&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(b) their resignations on protest. <br>दिए गए sentence में एक prepositional error है। \'Protest\' से पहले आए preposition &lsquo;<strong>on</strong>&rsquo; के स्थान पर &lsquo;<strong>in</strong>&rsquo; का प्रयोग होना चाहिए, क्योंकि &lsquo;in&rsquo; का प्रयोग किसी व्यक्ति/वस्तु की स्थिति (condition) को दर्शाने के लिए किया जाता है। अतः, &lsquo;their resignations in protest&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If there is no error, select &lsquo;No error&rsquo; as your answer. <br>My brother / who live in Pune / is arriving tomorrow.</p>",
                    question_hi: "<p>8. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If there is no error, select &lsquo;No error&rsquo; as your answer. <br>My brother / who live in Pune / is arriving tomorrow.</p>",
                    options_en: ["<p>My brother</p>", "<p>is arriving tomorrow</p>", 
                                "<p>who live in Pune</p>", "<p>No error</p>"],
                    options_hi: ["<p>My brother</p>", "<p>is arriving tomorrow</p>",
                                "<p>who live in Pune</p>", "<p>No error</p>"],
                    solution_en: "<p>8.(c) who live in Pune. <br>According to the &ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;Brother&rsquo; is a singular subject that will take &lsquo;lives&rsquo; as a singular verb. Hence, &lsquo;who lives in Pune&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(c) <br>&ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo; के अनुसार, एक singular subject के साथ हमेशा singular verb का तथा plural subject के साथ हमेशा plural verb का प्रयोग होता है। दिए गए sentence में, &lsquo;Brother&rsquo; एक singular subject है जिसके साथ &lsquo;lives&rsquo; singular verb का प्रयोग होगा। अतः, &lsquo;who lives in Pune&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>I never miss / a cricket match / as I am fond of cricket / from childhood.</p>",
                    question_hi: "<p>9. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>I never miss / a cricket match / as I am fond of cricket / from childhood.</p>",
                    options_en: ["<p>a cricket match</p>", "<p>as I am fond of cricket</p>", 
                                "<p>from childhood</p>", "<p>I never miss</p>"],
                    options_hi: ["<p>a cricket match</p>", "<p>as I am fond of cricket</p>",
                                "<p>from childhood</p>", "<p>I never miss</p>"],
                    solution_en: "<p>9.(c) from childhood.<br>&lsquo;<strong>Since</strong>&rsquo; is used to describe an action or situation that began in the past and continues in the present. For example, they have been married since 1990. Similarly, in the given sentence, the narrator was fond of cricket in the past and he has this fondness in the present so he never misses a match. Hence, &lsquo;since childhood&rsquo; is the most appropriate answer. <br>.</p>",
                    solution_hi: "<p>9.(c) from childhood. <br>&lsquo;<strong>Since</strong>&rsquo; का प्रयोग किसी ऐसे action या situation का वर्णन करने के लिए किया जाता है जिसकी शुरुआत past में हुई हो और present में जारी हो। उदाहरण के लिए, वे 1990 से विवाहित (married) हैं। इसी तरह, दिए गए sentence में, narrator, past में cricket का शौकीन (fond) था एवं उसे present में भी यह शौक है, इसलिए वह कभी भी कोई match नहीं miss करता । अतः, &lsquo;since childhood&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>One of / the biggest enterprise / in India / is declaring a lockout.</p>",
                    question_hi: "<p>10. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>One of / the biggest enterprise / in India / is declaring a lockout.</p>",
                    options_en: ["<p>in India</p>", "<p>One of</p>", 
                                "<p>is declaring a lockout</p>", "<p>the biggest enterprise</p>"],
                    options_hi: ["<p>in India</p>", "<p>One of</p>",
                                "<p>is declaring a lockout</p>", "<p>the biggest enterprise</p>"],
                    solution_en: "<p>10.(d) the biggest enterprise. <br>&ldquo;<span style=\"text-decoration: underline;\"><strong>One of the</strong> + Superlative degree of the adjective(best, worst, etc) + <strong>Plural Subject</strong></span>&rdquo; is grammatically the correct structure. For example, one of the best men, one of the greatest <strong>kings</strong>, one of the worst <strong>phones</strong>, etc. Similarly, &lsquo;one of the biggest <strong>enterprises</strong>&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(d) the biggest enterprise.<br>&ldquo;<span style=\"text-decoration: underline;\"><strong>One of the</strong> + Superlative degree of the adjective(best, worst, etc) + <strong>Plural Subject</strong></span>&rdquo; grammatically सही structure है। उदाहरण के लिए, one of the best men, one of the greatest <strong>kings</strong>, one of the worst <strong>phones</strong>, इत्यादि। इस प्रकार, &lsquo;one of the biggest <strong>enterprises</strong>&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The army / marched fearless / towards / the enemy cantonment. ",
                    question_hi: "11. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The army / marched fearless / towards / the enemy cantonment. ",
                    options_en: [" marched fearless ", " the enemy cantonment ", 
                                " The army ", " towards "],
                    options_hi: [" marched fearless ", " the enemy cantonment ",
                                " The army ", " towards "],
                    solution_en: "11.(a) marched fearless.<br />An adverb is a word that modifies or describes a verb. For example he sings loudly, he acted cowardly, etc. However, we can make an adverb by adding ‘-ly’ to an adjective like loud is an adjective but loudly is an adverb. Similarly, in the given sentence, the verb ‘marched’ will be modified by an adverb ‘fearlessly’. Hence, ‘marched fearlessly’ is the most appropriate answer.",
                    solution_hi: "11.(a) marched fearless.<br />Adverb, किसी verb को modify या describe करता है, उदाहरण के लिए, he sings loudly, he acted cowardly आदि। हालाँकि, हम किसी adjective में ‘-ly’ add करके adverb बना सकते हैं, जैसे \'loud\' एक adjective है लेकिन \'loudly\' एक adverb है। इसी प्रकार, दिए गए sentence में, adverb ‘fearlessly’, verb ‘marched’ को modify करता है। अतः, ‘marched fearlessly’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "12. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options.  <br />Several prominent film stars / have appeared on television / on behalf of awareness / about hand hygiene. ",
                    question_hi: "12. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options.  <br />Several prominent film stars / have appeared on television / on behalf of awareness / about hand hygiene. ",
                    options_en: [" have appeared on television ", " about hand hygiene ", 
                                " Several prominent film stars ", " on behalf of awareness "],
                    options_hi: [" have appeared on television ", " about hand hygiene ",
                                " Several prominent film stars ", " on behalf of awareness "],
                    solution_en: "12.(d) on behalf of awareness. <br />The phrase ‘on behalf of the’ means as a representative of someone or something. However, in the given sentence, article ‘the’ is missing from the phrase. Hence, ‘on behalf of the awareness’ is the most appropriate answer.",
                    solution_hi: "12.(d) on behalf of awareness. <br />Phrase ‘on behalf of’ का अर्थ है किसी व्यक्ति या वस्तु के प्रतिनिधि (representative) के रूप में। हालाँकि, दिए गए sentence में, phrase से article ‘the’ missing है। अतः, ‘on behalf of the awareness’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "13. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />Mr. Rao asked the newcomer / to his office / if he will minded / working late that day. ",
                    question_hi: "13. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />Mr. Rao asked the newcomer / to his office / if he will minded / working late that day. ",
                    options_en: [" Mr. Rao asked the newcomer ", " working late that day ", 
                                " to his office ", " if he will minded "],
                    options_hi: [" Mr. Rao asked the newcomer ", " working late that day ",
                                " to his office ", " if he will minded "],
                    solution_en: "13.(d) if he will minded.<br />The given sentence is in the past tense(asked) so the verb must be used in its simple past form(would) and not in the future tense(will). Hence, ‘if he would mind’ is the most appropriate answer.",
                    solution_hi: "13.(d) if he will minded.<br />दिया गया sentence, past tense(asked) में है, इसलिए verb का प्रयोग simple past form(would) में किया जाना चाहिए, न कि future tense(will) में। अतः, ‘if he would mind’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "14. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The concept became / too much clear to me / after my tutor / showed me the diagram. ",
                    question_hi: "14. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The concept became / too much clear to me / after my tutor / showed me the diagram. ",
                    options_en: [" too much clear to me ", " showed me the diagram ", 
                                " The concept became ", " after my tutor "],
                    options_hi: [" too much clear to me ", " showed me the diagram ",
                                " The concept became ", " after my tutor "],
                    solution_en: "14.(a) too much clear to me.<br />The use of the phrase ‘too much’ is not necessary for the sentence because it is used before uncountable nouns like too much money and ‘clear’ is not a noun. Hence, ‘became clearer to me’ is the most appropriate answer.",
                    solution_hi: "14.(a) too much clear to me.<br />दिए गए sentence के लिए phrase ‘too much’  का प्रयोग अनावश्यक है क्योंकि इसका प्रयोग uncountable nouns से पहले किया जाता है, जैसे too much money तथा  ‘clear’  noun नहीं है। अतः, ‘became clearer to me’  सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>It is misleading / to imagine that / computers can think / same like human beings.</p>",
                    question_hi: "<p>15. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>It is misleading / to imagine that / computers can think / same like human beings.</p>",
                    options_en: ["<p>computers can think</p>", "<p>same like human beings</p>", 
                                "<p>It is misleading</p>", "<p>to imagine that</p>"],
                    options_hi: ["<p>computers can think</p>", "<p>same like human beings</p>",
                                "<p>It is misleading</p>", "<p>to imagine that</p>"],
                    solution_en: "<p>15.(b) same like human beings. <br>We generally use &lsquo;like&rsquo; when we compare two things, for example Tarun looks like Vijay. But, in the given sentence, we are not comparing anything, instead we are showing a similarity between computers &amp; human beings so we will use the conjunction &lsquo;<strong>as</strong>&rsquo; in place of &lsquo;<strong>like</strong>&rsquo;. Hence, &lsquo;<strong>same as</strong> human beings&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(b) same like human beings. <br>हम सामान्यतः &lsquo;like&rsquo; का प्रयोग तब करते हैं जब दो चीज़ों को compare करते हैं, उदाहरण के लिए, &ldquo;Tarun looks like Vijay&rdquo;। लेकिन, दिए गए sentence में, हम किसी चीज़ की तुलना नहीं कर रहे हैं, बल्कि हम computers तथा human के बीच एक समानता (similarity) दिखा रहे हैं, इसलिए हम &lsquo;<strong>like</strong>&rsquo; के स्थान पर conjunction &lsquo;<strong>as</strong>&rsquo; का प्रयोग करेंगे। अतः, &lsquo;<strong>same as</strong> human beings&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If there is no error, select ‘No error’. <br />The balloon flew up / as soon as / the man cutting the string.  ",
                    question_hi: "16. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If there is no error, select ‘No error’. <br />The balloon flew up / as soon as / the man cutting the string.  ",
                    options_en: [" The balloon flew up ", " the man cutting the string ", 
                                " No error ", " as soon as "],
                    options_hi: [" The balloon flew up ", " the man cutting the string ",
                                " No error ", " as soon as "],
                    solution_en: "16.(b) the man cutting the string. <br />The given sentence is in the past tense(flew) so the verb must be used in its simple past form(cut) and not in the present continuous form(cutting). Hence, ‘the man cut the string’ is the most appropriate answer.",
                    solution_hi: "16.(b) the man cutting the string. <br />दिया गया sentence, past tense(flew) में है, इसलिए verb का प्रयोग भी simple past form(cut) में होना चाहिए, न कि  present continuous form(cutting) में। अतः,‘the man cut the string’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17.The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The Committee agreed / that small businesses / has been adversely affected / by COVID 19.</p>",
                    question_hi: "<p>17. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The Committee agreed / that small businesses / has been adversely affected / by COVID 19.</p>",
                    options_en: ["<p>has been adversely affected</p>", "<p>by COVID-19</p>", 
                                "<p>that small businesses</p>", "<p>The Committee agreed</p>"],
                    options_hi: ["<p>has been adversely affected</p>", "<p>by COVID-19</p>",
                                "<p>that small businesses</p>", "<p>The Committee agreed</p>"],
                    solution_en: "<p>17.(a) has been adversely affected.<br>According to the &ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;<strong>businesses</strong>&rsquo; is a <strong>plural subject</strong> that will take &lsquo;<strong>have</strong>&rsquo; as a <strong>plural verb</strong>. Hence, &lsquo;have been adversely affected &rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>17.(a) has been adversely affected.<br>&ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo; के अनुसार, singular subject के साथ हमेशा singular verb का तथा plural subject के साथ हमेशा plural verb का प्रयोग होता है। दिए गए sentence में, &lsquo;<strong>businesses</strong>&rsquo; एक <strong>plural subject</strong> है जिसके साथ <strong>plural verb</strong> &lsquo;<strong>have</strong>&rsquo; का प्रयोग होगा। अतः, \'&lsquo;have been adversely affected&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The food / in the new eatery / is much best than / the one in RK Puram.</p>",
                    question_hi: "<p>18. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>The food / in the new eatery / is much best than / the one in RK Puram.</p>",
                    options_en: ["<p>in the new eatery</p>", "<p>is much best than</p>", 
                                "<p>the one in RK Puram</p>", "<p>The food</p>"],
                    options_hi: ["<p>in the new eatery</p>", "<p>is much best than</p>",
                                "<p>the one in RK Puram</p>", "<p>The food</p>"],
                    solution_en: "<p>18.(b) is much best than.<br>We always use a <strong>comparative degree</strong> of the adjective like better, worse, greater, etc. when we do a <strong>comparison </strong>between two things. Similarly, in the given sentence, the food of two different places is being compared. However, best, worst, greatest, etc. are some superlative degree adjectives. Hence, &lsquo;<strong>is much better than</strong>&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(b) is much best than.<br>हम हमेशा किसी दो चीज़ों की तुलना (<strong>comparison</strong>) करते समय adjective के <strong>comparative degree</strong> का प्रयोग करते हैं, जैसे &mdash; better, worse, greater आदि। इस प्रकार, दिए गए sentence में दो अलग-अलग स्थानों के भोजन (food) की तुलना की जा रही है। हालाँकि, best, worst, greatest, आदि कुछ superlative degree adjectives हैं। अतः, &lsquo;<strong>is much better than</strong>&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>After entering the town, / go straight and then / take a sharpest turn / towards your left / to reach the mall.</p>",
                    question_hi: "<p>19. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>After entering the town, / go straight and then / take a sharpest turn / towards your left / to reach the mall.</p>",
                    options_en: ["<p>take a sharpest turn</p>", "<p>towards your left</p>", 
                                "<p>After entering the town</p>", "<p>to reach the mall</p>"],
                    options_hi: ["<p>take a sharpest turn</p>", "<p>towards your left</p>",
                                "<p>After entering the town</p>", "<p>to reach the mall</p>"],
                    solution_en: "<p>19.(a) take a sharpest turn. <br>Sharpest is the superlative degree of an adjective but we need a <strong>positive degree(sharp)</strong> in the given sentence. Hence, &lsquo;take a sharp turn&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>19.(a) take a sharpest turn. <br>&lsquo;Sharpest&rsquo;, एक adjective की superlative degree है, लेकिन हमें दिए गए sentence में <strong>positive degree(sharp) </strong>की आवश्यकता है। अतः, &lsquo;take a sharp turn&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>All those students / who are participating / in the play / have been notify / to stay back for rehearsal / after 3:00 p.m.</p>",
                    question_hi: "<p>20. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>All those students / who are participating / in the play / have been notify / to stay back for rehearsal / after 3:00 p.m.</p>",
                    options_en: ["<p>to stay back for rehearsal</p>", "<p>after 3:00 p.m.</p>", 
                                "<p>have been notify</p>", "<p>who are participating</p>"],
                    options_hi: ["<p>to stay back for rehearsal</p>", "<p>after 3:00 p.m.</p>",
                                "<p>have been notify</p>", "<p>who are participating</p>"],
                    solution_en: "<p>20.(c) have been notify. <br>&ldquo;Has/Have been + V<sub>3</sub>&rdquo; is the correct grammatical structure for the given sentence. Hence, &lsquo;have been notified(V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>20.(c) have been notify. <br>&ldquo;Has/Have been + V<sub>3</sub>&rdquo; दिए गए sentence के लिए सही grammatical structure है। अतः, &lsquo;have been notified(V<sub>3</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Raman went / with his wife / to the village / to sold their land.</p>",
                    question_hi: "<p>21. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Raman went / with his wife / to the village / to sold their land.</p>",
                    options_en: ["<p>to the village</p>", "<p>Raman went</p>", 
                                "<p>to sold their land</p>", "<p>with his wife</p>"],
                    options_hi: ["<p>to the village</p>", "<p>Raman went</p>",
                                "<p>to sold their land</p>", "<p>with his wife</p>"],
                    solution_en: "<p>21.(c) to sold their land.<br>The preposition &lsquo;<strong>to</strong>&rsquo; always takes the <strong><span style=\"text-decoration: underline;\">first form of the verb</span>(V<sub>1</sub>)</strong> with it. Hence, &lsquo;<strong>to sell(V<sub>1</sub>)</strong> their land&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(c) to sold their land.<br>Preposition &lsquo;<strong>to</strong>&rsquo; का प्रयोग हमेशा verb की first form के साथ किया जाता है। अतः, &lsquo;<strong>to sell(V<sub>1</sub>)</strong> their land&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "misc",
                    question_en: "<p>22. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Mithila art tradition is passed on / from one generation through a next / by children watching and helping / their mothers and grandmothers.</p>",
                    question_hi: "<p>22. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br>Mithila art tradition is passed on / from one generation through a next / by children watching and helping / their mothers and grandmothers.</p>",
                    options_en: ["<p>from one generation through a next</p>", "<p>Mithila art tradition is passed on</p>", 
                                "<p>their mothers and grandmothers</p>", "<p>by children watching and helping</p>"],
                    options_hi: ["<p>from one generation through a next</p>", "<p>Mithila art tradition is passed on</p>",
                                "<p>their mothers and grandmothers</p>", "<p>by children watching and helping</p>"],
                    solution_en: "<p>22.(a) from one generation through a next. <br>There is a prepositional error in the given sentence. The preposition &lsquo;<strong>through</strong>&rsquo; must be replaced with &lsquo;<strong>to</strong>&rsquo;. Hence, &lsquo;from one generation <strong>to </strong>the next&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) from one generation through a next. <br>दिए गए sentence में एक &lsquo;prepositional error&rsquo; है। यहाँ preposition &lsquo;<strong>through</strong>&rsquo; के स्थान पर &lsquo;<strong>to</strong>&rsquo; का प्रयोग किया जाना चाहिए। अतः, &lsquo;from one generation <strong>to </strong>the next&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>