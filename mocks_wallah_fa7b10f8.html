<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate ANTONYM of the underlined word in the following sentence. <br>It was difficult to accommodate the <span style=\"text-decoration: underline;\">avariciousness</span> of the people.</p>",
                    question_hi: "<p>1. Select the most appropriate ANTONYM of the underlined word in the following sentence. <br>It was difficult to accommodate the <span style=\"text-decoration: underline;\">avariciousness</span> of the people.</p>",
                    options_en: ["<p>Generosity</p>", "<p>Criticism</p>", 
                                "<p>Hatred</p>", "<p>Envy</p>"],
                    options_hi: ["<p>Generosity</p>", "<p>Criticism</p>",
                                "<p>Hatred</p>", "<p>Envy</p>"],
                    solution_en: "<p>1.(a) <strong>Generosity</strong>- the quality of being kind and giving.<br><strong>Avariciousness</strong>- extreme greed for wealth or material gain.<br><strong>Criticism</strong>- the expression of disapproval based on perceived faults.<br><strong>Hatred</strong>- intense dislike.<br><strong>Envy</strong>- desire to have a quality, possession, or other desirable thing belonging to (someone else).</p>",
                    solution_hi: "<p>1.(a) <strong>Generosity </strong>(उदारता/दानशीलता) - the quality of being kind and giving.<br><strong>Avariciousness </strong>(लोभी/लोलुपता) - extreme greed for wealth or material gain.<br><strong>Criticism </strong>(आलोचना)- the expression of disapproval based on perceived faults.<br><strong>Hatred </strong>(घृणा)- intense dislike.<br><strong>Envy </strong>(ईर्ष्या)- desire to have a quality, possession, or other desirable thing belonging to (someone else).</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the option that expresses the given sentence in passive voice.<br>He has written a best-selling novel.</p>",
                    question_hi: "<p>2. Select the option that expresses the given sentence in passive voice.<br>He has written a best-selling novel.</p>",
                    options_en: ["<p>A best-selling novel was being written by him</p>", "<p>A best-selling novel has been written by him.</p>", 
                                "<p>A best-selling novel was written by him.</p>", "<p>A best-selling novel would have been written by him.</p>"],
                    options_hi: ["<p>A best-selling novel was being written by him</p>", "<p>A best-selling novel has been written by him.</p>",
                                "<p>A best-selling novel was written by him.</p>", "<p>A best-selling novel would have been written by him.</p>"],
                    solution_en: "<p>2.(b) A best-selling novel has been written by him. (Correct)<br>(a) A best-selling novel <span style=\"text-decoration: underline;\">was being written</span> by him. (Incorrect Tense)<br>(c) A best-selling novel <span style=\"text-decoration: underline;\">was written</span> by him. (Incorrect Tense)<br>(d) A best-selling novel <span style=\"text-decoration: underline;\">would have been written</span> by him. (Incorrect use of modal)</p>",
                    solution_hi: "<p>2.(b) A best-selling novel has been written by him. (Correct)<br>(a) A best-selling novel <span style=\"text-decoration: underline;\">was being written</span> by him. (गलत Tense)<br>(c) A best-selling novel <span style=\"text-decoration: underline;\">was written</span> by him. (गलत Tense)<br>(d) A best-selling novel <span style=\"text-decoration: underline;\">would have been written</span> by him. (Modal का गलत प्रयोग)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br>P) her face <br>Q) I have <br>R) for a year <br>S) not seen</p>",
                    question_hi: "<p>3. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>P) her face <br>Q) I have <br>R) for a year <br>S) not seen</p>",
                    options_en: ["<p>PQRS</p>", "<p>PSQR</p>", 
                                "<p>SPQR</p>", "<p>QSPR</p>"],
                    options_hi: ["<p>PQRS</p>", "<p>PSQR</p>",
                                "<p>SPQR</p>", "<p>QSPR</p>"],
                    solution_en: "<p>3.(d) QSPR<br>The correct sentence is : &ldquo;I have not seen her face for a year.&rdquo;</p>",
                    solution_hi: "<p>3.(d) QSPR<br>&ldquo;I have not seen her face for a year.&rdquo; सही sentence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate ANTONYM of the given word from the following sentence. <br>Barren <br>\"Can you believe how much traffic there is today?\", grumbled Tom, glancing at the congested road ahead.</p>",
                    question_hi: "<p>4. Select the most appropriate ANTONYM of the given word from the following sentence. <br>Barren <br>\"Can you believe how much traffic there is today?\", grumbled Tom, glancing at the congested road ahead.</p>",
                    options_en: ["<p>Believe</p>", "<p>Grumbled</p>", 
                                "<p>Glancing</p>", "<p>Congested</p>"],
                    options_hi: ["<p>Believe</p>", "<p>Grumbled</p>",
                                "<p>Glancing</p>", "<p>Congested</p>"],
                    solution_en: "<p>4.(d) <strong>Congested</strong>- overcrowded or blocked, especially with traffic or people.<br><strong>Barren</strong>- bleak and lifeless..<br><strong>Believe</strong>- to accept something as true or real.<br><strong>Grumbled</strong>- complained about something in a bad-tempered way.<br><strong>Glancing</strong>- taking a brief or quick look.</p>",
                    solution_hi: "<p>4.(d) <strong>Congested </strong>(भीड़भाड़)- overcrowded or blocked, especially with traffic or people.<br><strong>Barren </strong>(बंजर)- bleak and lifeless..<br><strong>Believe </strong>(विश्वास करना)- to accept something as true or real.<br><strong>Grumbled </strong>(शिकायत की)- complained about something in a bad-tempered way.<br><strong>Glancing </strong>(नज़र डालना)- taking a brief or quick look.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate option to substitute the underlined word segment in the given sentence. <br>A <span style=\"text-decoration: underline;\">herd of cattle</span> consists of related females, cubs, and a small number of adult males.</p>",
                    question_hi: "<p>5. Select the most appropriate option to substitute the underlined word segment in the given sentence. <br>A <span style=\"text-decoration: underline;\">herd of cattle</span> consists of related females, cubs, and a small number of adult males.</p>",
                    options_en: ["<p>pride of lions</p>", "<p>pack of wolves</p>", 
                                "<p>school of fish</p>", "<p>flock of birds</p>"],
                    options_hi: ["<p>pride of lions</p>", "<p>pack of wolves</p>",
                                "<p>school of fish</p>", "<p>flock of birds</p>"],
                    solution_en: "<p>5.(a) pride of lions<br>&lsquo;Cub&rsquo; is a young lion. Hence, &lsquo;pride of lions&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(a) pride of lions<br>&lsquo;Cub&rsquo; एक शेर का बच्चा होता है। अतः, &lsquo;pride of lions&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate idiom for the given group of words.<br>A time when money might unexpectedly be needed</p>",
                    question_hi: "<p>6. Select the most appropriate idiom for the given group of words.<br>A time when money might unexpectedly be needed</p>",
                    options_en: ["<p>A rainy day</p>", "<p>A raw deal</p>", 
                                "<p>A rash of something</p>", "<p>A rap over the knuckles</p>"],
                    options_hi: ["<p>A rainy day</p>", "<p>A raw deal</p>",
                                "<p>A rash of something</p>", "<p>A rap over the knuckles</p>"],
                    solution_en: "<p>6.(a) <strong>A rainy day-</strong> a time when money might unexpectedly be needed.<br>E.g.- She saved some money for a rainy day, just in case of an emergency.</p>",
                    solution_hi: "<p>6.(a) <strong>A rainy day-</strong> a time when money might unexpectedly be needed./ऐसा समय जब अचानक धन की आवश्यकता पड़ सकती है।<br>E.g.- She saved some money for a rainy day, just in case of an emergency.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who does not believe in God</p>",
                    question_hi: "<p>7. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who does not believe in God</p>",
                    options_en: ["<p>Amateur</p>", "<p>Anarchy</p>", 
                                "<p>Autocrat</p>", "<p>Atheist</p>"],
                    options_hi: ["<p>Amateur</p>", "<p>Anarchy</p>",
                                "<p>Autocrat</p>", "<p>Atheist</p>"],
                    solution_en: "<p>7.(d) <strong>Atheist</strong>- a person who does not believe in God.<br><strong>Amateur</strong>- a person who engages in a pursuit, especially a sport, on an unpaid rather than a professional basis.<br><strong>Anarchy</strong>- a state of disorder due to absence or non-recognition of authority.<br><strong>Autocrat</strong>- a ruler who has absolute power.</p>",
                    solution_hi: "<p>7.(d) <strong>Atheist </strong>(नास्तिक)- a person who does not believe in God.<br><strong>Amateur </strong>(अव्यवसायी)- a person who engages in a pursuit, especially a sport, on an unpaid rather than a professional basis.<br><strong>Anarchy </strong>(अराजकता)- a state of disorder due to absence or non-recognition of authority.<br><strong>Autocrat </strong>(तानाशाह)- a ruler who has absolute power.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "8. Select the INCORRECTLY spelt word.",
                    question_hi: "8. Select the INCORRECTLY spelt word.",
                    options_en: [" Measerement ", " Region ", 
                                " Confidential", " Strengths"],
                    options_hi: [" Measerement ", " Region ",
                                " Confidential", " Strengths"],
                    solution_en: "8.(a) Measerement<br />\'Measurement\' is the correct spelling.",
                    solution_hi: "8.(a) Measerement<br />\'Measurement\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate option that can substitute the underlined word in the given sentence. <br>I saw a shabby looking man living in the <span style=\"text-decoration: underline;\">cavity</span> of a mountain.</p>",
                    question_hi: "<p>9. Select the most appropriate option that can substitute the underlined word in the given sentence. <br>I saw a shabby looking man living in the <span style=\"text-decoration: underline;\">cavity</span> of a mountain.</p>",
                    options_en: ["<p>elevation</p>", "<p>height</p>", 
                                "<p>mound</p>", "<p>hole</p>"],
                    options_hi: ["<p>elevation</p>", "<p>height</p>",
                                "<p>mound</p>", "<p>hole</p>"],
                    solution_en: "<p>9.(d) hole<br>The given sentence states that I saw a shabby looking man living in the hole of a mountain. Hence, \'hole\' is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(d) hole<br>दिए गए sentence में कहा गया है कि मैंने एक दरिद्र दिखने वाले(shabby looking) व्यक्ति को पहाड़ की गुफा में रहते हुए देखा। अतः, \'hole\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. <br />A.The door of the flat was ajar. <br />B.The taxi stopped. <br />C.Mr. Satterthwaite flung himself out and raced up the stone stairs to the second floor like a young athlete. <br />D. He pushed it open, and the great voice welcomed him.",
                    question_hi: "10. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. <br />A.The door of the flat was ajar. <br />B.The taxi stopped. <br />C.Mr. Satterthwaite flung himself out and raced up the stone stairs to the second floor like a young athlete. <br />D. He pushed it open, and the great voice welcomed him.",
                    options_en: [" BACD ", " ABCD ", 
                                " BCAD", " DCBA"],
                    options_hi: [" BACD ", " ABCD ",
                                " BCAD", " DCBA"],
                    solution_en: "10.(c) BCAD<br />Sentence B will be the starting line as it introduces the main idea of the parajumble i.e. the taxi stopped. And, Sentence C states that Mr. Satterthwaite came out hurriedly and raced up the stone stairs to the second floor. So, C will follow B. Further, Sentence A states that the door of the flat was slightly open & Sentence D states that he pushed it open, and the great voice welcomed him. So, D will follow A. Going through the options, option (c) has the correct sequence.",
                    solution_hi: "10.(c) BCAD<br />Sentence B प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार ‘the taxi stopped’ का परिचय देता है। और, Sentence C बताता है कि Mr. Satterthwaite जल्दी से बाहर आए और पत्थर की सीढ़ियों से दूसरे मंजिल पर चले गए। इसलिए, B के बाद C आएगा। इसके अलावा, Sentence A कहता है कि flat का दरवाज़ा थोड़ा खुला था, और Sentence D बताता है कि उन्होंने इसे धक्का देकर खोला, और महान आवाज़ ने उनका स्वागत किया। इसलिए, A के बाद D आएगा। अतः options के माध्यम से जाने पर, option ‘c’ में सही sequence है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the option that expresses the given sentence in passive voice. <br>He wrote an inspiring speech for the event.</p>",
                    question_hi: "<p>11. Select the option that expresses the given sentence in passive voice. <br>He wrote an inspiring speech for the event.</p>",
                    options_en: ["<p>An inspiring speech was being written by him for the event.</p>", "<p>An inspiring speech was written by him for the event.</p>", 
                                "<p>An inspiring speech is written by him for the event.</p>", "<p>An inspiring speech has been written by him for the event.</p>"],
                    options_hi: ["<p>An inspiring speech was being written by him for the event.</p>", "<p>An inspiring speech was written by him for the event.</p>",
                                "<p>An inspiring speech is written by him for the event.</p>", "<p>An inspiring speech has been written by him for the event.</p>"],
                    solution_en: "<p>11.(b) An inspiring speech was written by him for the event. (Correct)<br>(a) An inspiring speech <span style=\"text-decoration: underline;\">was being</span> written by him for the event. (Incorrect Helping Verb)<br>(c) An inspiring speech <span style=\"text-decoration: underline;\">is written</span> by him for the event. (Incorrect Tense)<br>(d) An inspiring speech <span style=\"text-decoration: underline;\">has been written</span> by him for the event. (Incorrect Tense)</p>",
                    solution_hi: "<p>11.(b) An inspiring speech was written by him for the event. (Correct)<br>(a) An inspiring speech <span style=\"text-decoration: underline;\">was bein</span>g written by him for the event. (गलत Helping Verb)<br>(c) An inspiring speech <span style=\"text-decoration: underline;\">is written</span> by him for the event. (गलत Tense)<br>(d) An inspiring speech <span style=\"text-decoration: underline;\">has been written</span> by him for the event. (गलत Tense)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>It is nothing <span style=\"text-decoration: underline;\">else your pride which makes</span> you say such a thing.</p>",
                    question_hi: "<p>12. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>It is nothing <span style=\"text-decoration: underline;\">else your pride which makes</span> you say such a thing.</p>",
                    options_en: ["<p>else your pride that makes</p>", "<p>else your pride which make</p>", 
                                "<p>else but your pride which make</p>", "<p>else but your pride which makes</p>"],
                    options_hi: ["<p>else your pride that makes</p>", "<p>else your pride which make</p>",
                                "<p>else but your pride which make</p>", "<p>else but your pride which makes</p>"],
                    solution_en: "<p>12.(d) else but your pride which makes<br>&lsquo;Nothing &hellip; but&rsquo; or &lsquo;Nothing else &hellip; but&rsquo; means only. The given sentence needs the conjunction &lsquo;but&rsquo; after &lsquo;nothing else&rsquo; to make it grammatically correct. Hence, &lsquo;else but your pride which makes&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(d) else but your pride which makes<br>&lsquo;Nothing &hellip; but&rsquo; अथवा &lsquo;Nothing else &hellip; but&rsquo; का अर्थ होता है केवल(only)। दिए गए sentence को grammatically सही बनाने के लिए &lsquo;nothing else&rsquo; के बाद conjunction &lsquo;but&rsquo; की आवश्यकता है। अतः, &lsquo;else but your pride which makes&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate synonym of the underlined word in the given sentence. <br>This is a clear example of <span style=\"text-decoration: underline;\">conflict</span> of egos.</p>",
                    question_hi: "<p>13. Select the most appropriate synonym of the underlined word in the given sentence. <br>This is a clear example of <span style=\"text-decoration: underline;\">conflict</span> of egos.</p>",
                    options_en: ["<p>clash</p>", "<p>mingling</p>", 
                                "<p>infliction</p>", "<p>crash</p>"],
                    options_hi: ["<p>clash</p>", "<p>mingling</p>",
                                "<p>infliction</p>", "<p>crash</p>"],
                    solution_en: "<p>13.(a) <strong>Clash</strong>- a violent confrontation or disagreement.<br><strong>Conflict</strong>- a serious disagreement or argument.<br><strong>Mingling</strong>- mixing or blending together.<br><strong>Infliction</strong>- the action of causing something unpleasant, such as pain or suffering.<br><strong>Crash</strong>- a violent collision, typically involving vehicles.</p>",
                    solution_hi: "<p>13.(a) <strong>Clash </strong>(टकराव)- a violent confrontation or disagreement.<br><strong>Conflict </strong>(विरोध/द्वंद्व)- a serious disagreement or argument.<br><strong>Mingling </strong>(मिश्रित होना)- mixing or blending together.<br><strong>Infliction </strong>(प्रताड़ना)- the action of causing something unpleasant, such as pain or suffering.<br><strong>Crash </strong>(टक्कर)- a violent collision, typically involving vehicles.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>Mr. Adhil <span style=\"text-decoration: underline;\">called back but he wasn&rsquo;t able to</span> find the scooter part he needed.</p>",
                    question_hi: "<p>14. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>Mr. Adhil <span style=\"text-decoration: underline;\">called back but he wasn&rsquo;t able to</span> find the scooter part he needed.</p>",
                    options_en: [" called down but he wasn’t able to  ", " called off but he wasn’t able to ", 
                                " called around but he wasn’t able to ", " called up to but he wasn’t able to"],
                    options_hi: [" called down but he wasn’t able to  ", " called off but he wasn’t able to ",
                                " called around but he wasn’t able to ", " called up to but he wasn’t able to"],
                    solution_en: "14.(c) called around but he wasn’t able to<br />‘Call around’ means to call multiple people to find information about something. The given sentence states that Mr. Adhil called around but he wasn’t able to find the scooter part he needed. Hence, \'called around but he wasn’t able to\' is the most appropriate answer.",
                    solution_hi: "14.(c) called around but he wasn’t able to<br />‘Call around’ का अर्थ है किसी चीज़ के बारे में जानकारी प्राप्त करने के लिए कई लोगों को call करना। दिए गए sentence में कहा गया है कि Mr. Adhil ने की लोगों को call किया लेकिन उन्हें scooter का वह part नहीं मिल पाया जिसकी उन्हें जरूरत थी। अतः, \'called around but he wasn’t able to\' सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate meaning of the underlined idiom in the following sentence. <br>Instead of confusing others, let us <span style=\"text-decoration: underline;\">call a spade a spade</span> in front of the audience.</p>",
                    question_hi: "<p>15. Select the most appropriate meaning of the underlined idiom in the following sentence. <br>Instead of confusing others, let us <span style=\"text-decoration: underline;\">call a spade a spade</span> in front of the audience.</p>",
                    options_en: ["<p>Pretend superiority</p>", "<p>Remain silent</p>", 
                                "<p>Speak truthfully</p>", "<p>Be at strife</p>"],
                    options_hi: ["<p>Pretend superiority</p>", "<p>Remain silent</p>",
                                "<p>Speak truthfully</p>", "<p>Be at strife</p>"],
                    solution_en: "<p>15.(c) <strong>Call a spade a spade-</strong> speak truthfully.</p>",
                    solution_hi: "<p>15.(c) <strong>Call a spade a spade- </strong>speak truthfully./सत्य बोलना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. Select the INCORRECTLY spelt word. ",
                    question_hi: "16. Select the INCORRECTLY spelt word.",
                    options_en: [" Connoisseur ", " Neccessary", 
                                " Privilege ", " Dilemma<br /> "],
                    options_hi: [" Connoisseur ", " Neccessary",
                                " Privilege ", " Dilemma"],
                    solution_en: "16.(b) Neccessary<br />\'Necessary\' is the correct spelling.",
                    solution_hi: "16.(b) Neccessary<br />\'Necessary\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Select the most appropriate phrasal verb to fill in the blank. <br />We need to _______a solution to this problem before it gets worse. ",
                    question_hi: "17. Select the most appropriate phrasal verb to fill in the blank. <br />We need to _______a solution to this problem before it gets worse.",
                    options_en: [" cut down on  ", " break down", 
                                " break up", " come up with"],
                    options_hi: [" cut down on  ", " break down",
                                " break up", " come up with"],
                    solution_en: "17.(d) come up with<br />‘Come up with’ means to suggest or think of an idea or plan. The given sentence states that we need to come up with a solution to this problem before it gets worse. Hence, \'come up with\' is the most appropriate answer.",
                    solution_hi: "17.(d) come up with<br />‘Come up with’ का अर्थ है किसी विचार या योजना के बारे में सुझाव देना या सोचना। दिए गए sentence में कहा गया है कि हमें इस problem के बदतर होने से पहले इसका solution ढूंढ़ना होगा। अतः, \'come up with\' सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate meaning of the underlined word in the given sentence. <br>This building is <span style=\"text-decoration: underline;\">colossal</span> and it can hold a village&rsquo;s population in it.</p>",
                    question_hi: "<p>18. Select the most appropriate meaning of the underlined word in the given sentence. <br>This building is <span style=\"text-decoration: underline;\">colossal</span> and it can hold a village&rsquo;s population in it.</p>",
                    options_en: ["<p>very huge</p>", "<p>temporary</p>", 
                                "<p>very beautiful</p>", "<p>puny</p>"],
                    options_hi: ["<p>very huge</p>", "<p>temporary</p>",
                                "<p>very beautiful</p>", "<p>puny</p>"],
                    solution_en: "<p>18.(a) Colossal- very huge.</p>",
                    solution_hi: "<p>18.(a) Colossal - very huge./बहुत विशाल।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate synonym of the given word. <br>Maverick</p>",
                    question_hi: "<p>19. Select the most appropriate synonym of the given word. <br>Maverick</p>",
                    options_en: ["<p>Conformist</p>", "<p>Orthodox</p>", 
                                "<p>Bohemian</p>", "<p>Follower</p>"],
                    options_hi: ["<p>Conformist</p>", "<p>Orthodox</p>",
                                "<p>Bohemian</p>", "<p>Follower</p>"],
                    solution_en: "<p>19.(c) <strong>Bohemian </strong>- a person who lives in a very informal way without following accepted rules of behaviour.<br><strong>Maverick</strong>- a person who thinks and acts independently of and differently from others.<br><strong>Conformist</strong>- someone who follows established customs or norms.<br><strong>Orthodox</strong>- adhering to traditional or established beliefs or practices.<br><strong>Follower</strong>- a person who supports and admires a particular person or set of ideas.</p>",
                    solution_hi: "<p>19.(c) <strong>Bohemian </strong>(रूढ़िमुक्त)- a person who lives in a very informal way without following accepted rules of behaviour.<br><strong>Maverick </strong>(मनमौजी)- a person who thinks and acts independently of and differently from others.<br><strong>Conformist </strong>(अनुवर्ती)- someone who follows established customs or norms.<br><strong>Orthodox </strong>(रुढ़िवादी)- adhering to traditional or established beliefs or practices.<br><strong>Follower </strong>(अनुयायी)- a person who supports and admires a particular person or set of ideas.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "20. Identify the error in the given sentence. <br />Camels tend to move slowly than other domestic animals in the plains. ",
                    question_hi: "20.  Identify the error in the given sentence. <br />Camels tend to move slowly than other domestic animals in the plains.",
                    options_en: [" domestic animals in the plains.", " than other", 
                                " move slowly ", " Camels tend to"],
                    options_hi: [" domestic animals in the plains.", " than other",
                                " move slowly ", " Camels tend to"],
                    solution_en: "20.(c) move slowly<br />We use comparative degree with ‘than’ to compare two persons, things or animals. The given sentence makes a comparison between the movement of camels and other domestic animals. ‘Slower’ will be used instead of ‘slowly’. Hence, ‘move slower’ is the most appropriate answer.",
                    solution_hi: "20.(c) move slowly<br />‘Than’ के साथ comparative degree का प्रयोग हम दो person, thing या animal की तुलना करने के लिए करते हैं। दिए गए sentence में camels के movement की तुलना अन्य domestic animals से की गई है। \'slowly\' के स्थान पर  \'slower\' का प्रयोग किया जाएगा। अतः, ‘move slower’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice. <br>Select the most appropriate option to fill in blank no. 21</p>",
                    question_hi: "<p>21. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no. 21</p>",
                    options_en: ["<p>apprehensive</p>", "<p>apologetic</p>", 
                                "<p>indifferent</p>", "<p>excellent</p>"],
                    options_hi: ["<p>apprehensive</p>", "<p>apologetic</p>",
                                "<p>indifferent</p>", "<p>excellent</p>"],
                    solution_en: "<p>21.(c) indifferent <br>&lsquo;Indifferent&rsquo; means having no particular interest or sympathy. The given passage states that the mathematician Federico Ardila-Mantilla grew up in Colombia, an indifferent student but gifted in math. Hence, \'indifferent\' is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(c) indifferent <br>&lsquo;Indifferent&rsquo; का अर्थ है कोई विशेष रुचि या सहानुभूति न होना। दिए गए passage में बताया गया है कि गणितज्ञ Federico Ardila-Mantilla कोलंबिया में पले-बढ़े, एक सामान्य छात्र थे लेकिन mathematics में प्रतिभाशाली थे। अतः, \'indifferent\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no. 22</p>",
                    question_hi: "<p>22. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no. 22</p>",
                    options_en: ["<p>dissuaded</p>", "<p>suggested</p>", 
                                "<p>removed</p>", "<p>dispirited</p>"],
                    options_hi: ["<p>dissuaded</p>", "<p>suggested</p>",
                                "<p>removed</p>", "<p>dispirited</p>"],
                    solution_en: "<p>22.(b) suggested<br>&lsquo;Suggested&rsquo; means to mention an idea or plan to consider. The given passage states that he was failing most of his classes at his high school in Bogot&aacute; when someone suggested him to apply to MIT. Hence, \'suggested\' is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(b) suggested<br>&lsquo;Suggested&rsquo; का अर्थ है किसी विचार या योजना का उल्लेख करना। दिए गए passage में बताया गया है कि वह Bogota में अपने high school की अधिकांश कक्षाओं में fail हो रहे थे, तभी किसी ने उन्हें MIT में आवेदन करने का सुझाव दिया। अतः, \'suggested\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no. 23</p>",
                    question_hi: "<p>23. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no. 23</p>",
                    options_en: ["<p>compare</p>", "<p>malign</p>", 
                                "<p>strangulate</p>", "<p>differentiate</p>"],
                    options_hi: ["<p>compare</p>", "<p>malign</p>",
                                "<p>strangulate</p>", "<p>differentiate</p>"],
                    solution_en: "<p>23.(a) compare<br>The given passage states that one of his professors was known to compare his audience to a herd of cows. Hence, &lsquo;compare&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(a) compare<br>दिए गए passage में बताया गया है कि उनके एक professor अपने audience की तुलना गायों के झुंड से करने के लिए जाने जाते थे। अतः, &lsquo;compare&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no. 24.</p>",
                    question_hi: "<p>24. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no.24.</p>",
                    options_en: ["<p>introversion</p>", "<p>molestation</p>", 
                                "<p>quantification</p>", "<p>interrogation</p>"],
                    options_hi: ["<p>introversion</p>", "<p>molestation</p>",
                                "<p>quantification</p>", "<p>interrogation</p>"],
                    solution_en: "<p>24.(a) introversion<br>&lsquo;Introversion&rsquo; means the quality of being shy and quiet, and preferring to spend time alone. The given passage states that part of it had to do with his own introversion. Hence, &lsquo;introversion&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(a) introversion<br>&lsquo;Introversion&rsquo; का अर्थ है शर्मीला और शांत स्वभाव होना, तथा अकेले समय बिताना पसंद करना। दिए गए passage में कहा गया है कि इसकी एक वजह उनका अपना अंतर्मुखी स्वभाव(introversion) था। अतः, &lsquo;introversion&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no. 25.</p>",
                    question_hi: "<p>25. <strong>Cloze test:</strong> <br>The mathematician Federico Ardila-Mantilla grew up in Colombia, an (21) ________ student but gifted in math. He was failing most of his classes at his high school in Bogot&aacute; when someone (22) ________ him to apply to MIT. He had not heard of the school. To his surprise, he got in, and he went on scholarship. Mathematically, he did well. One of his professors, an acid-tongued theoretician known to (23) _____ his audience to a herd of cows, routinely tucked &lsquo;open&rsquo; math problems into homework assignments, without telling the students. These had never been solved by anyone. Ardila solved one. He went on to receive his bachelor&rsquo;s and Ph.D. in math from MIT. <br>But his academic experience was also one of isolation. Part of it had to do with his own (24) _______. Part of it was cultural. As a Latino, he was very much in the minority in the department, and he did not feel comfortable in American mathematical spaces. No one had tried to explicitly (25) ______ him, yet he felt alone. In math, collaborating with others opens up new kinds of learning and thinking. But in his nine years at MIT, Ardila worked with others only twice.<br>Select the most appropriate option to fill in blank no. 25.</p>",
                    options_en: ["<p>exclude</p>", "<p>fabricate</p>", 
                                "<p>incorporate</p>", "<p>stratify</p>"],
                    options_hi: ["<p>exclude</p>", "<p>fabricate</p>",
                                "<p>incorporate</p>", "<p>stratify</p>"],
                    solution_en: "<p>25.(a) exclude<br>&lsquo;Exclude&rsquo; means to not have somebody as a part of. The given passage states that no one had tried to explicitly exclude him, yet he felt alone. Hence, \'exclude\' is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(a) exclude<br>&lsquo;Exclude&rsquo; का अर्थ है बहिष्कृत करना। दिए गए passage में कहा गया है कि किसी ने भी उन्हें स्पष्ट रूप से बहिष्कृत करने की कोशिश नहीं की थी, फिर भी वह अकेला महसूस करते थे। अतः, \'exclude\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>