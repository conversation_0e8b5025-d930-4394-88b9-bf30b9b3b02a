<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. Which of the following pairs is NOT coprime ?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन सा युग्म सह - अभाज्य नहीं है ?</p>",
                    options_en: ["<p>(11, 13)</p>", "<p>(15, 17)</p>", 
                                "<p>(17, 23)</p>", "<p>(17, 34)</p>"],
                    options_hi: ["<p>(11, 13)</p>", "<p>(15, 17)</p>",
                                "<p>(17, 23)</p>", "<p>(17, 34)</p>"],
                    solution_en: "<p>1.(d) (11 , 13) is co-prime because <br>H.C.F of 11 and 13 = 1<br>(15 , 17) is co-prime because <br>H.C.F of 15 and 17 = 1<br>(17 , 23) is co-prime because <br>H.C.F of 17 and 23 = 1<br>(17 , 34) is not co-prime because <br>H.C.F of 17 and 34 = 17</p>",
                    solution_hi: "<p>1.(d)<br>(11, 13) सह-अभाज्य है क्योंकि 11 और 13 का&nbsp;म.स. = 1<br>(15, 17) सह-अभाज्य है क्योंकि 15 और 17 का&nbsp;म.स. = 1 <br>(17 , 23) सह-अभाज्य है क्योंकि 17 और 23 का&nbsp;म.स. = 1 <br>(17, 34) सह-अभाज्य नहीं है क्योंकि 17 और 34&nbsp;का म.स. = 17</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. Select the option that is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to the third number.<br>11 : 130 :: 7 : 58 :: 15 : ?</p>",
                    question_hi: "<p>2. उस विकल्प का चयन करें जो पांचवीं संख्या से उसी प्रकार संबंधित है जैसे दूसरी संख्या पहली संख्या से संबंधित है और चौथी संख्या तीसरी संख्या से संबंधित है।<br>11 : 130 :: 7 : 58 :: 15 : ?</p>",
                    options_en: ["<p>234</p>", "<p>248</p>", 
                                "<p>216</p>", "<p>224</p>"],
                    options_hi: ["<p>234</p>", "<p>248</p>",
                                "<p>216</p>", "<p>224</p>"],
                    solution_en: "<p>2.(a)<br><strong>Logic :&nbsp;</strong>(First Number)<sup>2</sup> + 9 = Second Number<br>11 : 130 &rArr;&nbsp;(11)<sup>2</sup> + 9 = 130 <br>7 : 58 &rArr; (7)<sup>2</sup> + 9 = 58<br>Similarly,<br>(15 : ?) &rArr; (15)<sup>2</sup> + 9 = <strong>234</strong></p>",
                    solution_hi: "<p>2.(a) <br><strong>तर्क : </strong>(पहली संख्या)<sup>2</sup> + 9 = दूसरी संख्या <br>11 : 130 &rArr;&nbsp;(11)<sup>2</sup> + 9 = 130 <br>7 : 58 &rArr; (7)<sup>2</sup> + 9 = 58<br>इसी प्रकार,<br>(15 : ?) &rArr; (15)<sup>2</sup> + 9 = <strong>234</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Which of the following is a popular dance and music combination of Andhra Pradesh that is similar to the stick dance ?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन आंध्र प्रदेश का एक लोकप्रिय नृत्य और संगीत संयोजन है जो छड़ी नृत्य के समान है ?</p>",
                    options_en: ["<p>Neuleu</p>", "<p>Kolannalu</p>", 
                                "<p>Villu Pattu</p>", "<p>Dandiya</p>"],
                    options_hi: ["<p>न्यूल्यू (Neuleu)</p>", "<p>कोलानालु (Kolannalu)</p>",
                                "<p>विल्लू पट्टू (Villu Pattu)</p>", "<p>डांडिया (Dandiya)</p>"],
                    solution_en: "<p>3.(b) <strong>Kolannalu</strong> (Kolattam) - It is a rural art usually performed during village festivals. Villu Pattu - It is an ancient form of a musical story-telling method performed in Southern India. Dandiya- It is the folk dance of Gujarat. It is performed during the time of the Navaratri festival.</p>",
                    solution_hi: "<p>3.(b) <strong>कोलानालु </strong>(कोलट्टम) - यह एक ग्रामीण कला है जो आमतौर पर गाँव के त्योहारों के दौरान प्रदर्शित की जाती है। विल्लू पट्टू - यह दक्षिणी भारत में प्रस्तुत की जाने वाली संगीतमय कहानी कहने की पद्धति का एक प्राचीन रूप है। डांडिया- यह गुजरात का लोक नृत्य है। यह नवरात्रि उत्सव के दौरान किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4.&nbsp;Read the given statements and conclusions carefully and decide which of the conclusions logically follow(s) from the statements.</p>\n<p><strong id=\"docs-internal-guid-3bb59eee-7fff-a26b-9cc3-aa14b4265a56\"></strong><strong>Statements:</strong> <br>L &gt; M = N &ge; O, P &ge; Q &le; R = O<br><strong>Conclusions:</strong> <br>I. N &ge; O <br>II. L &gt; R <br>III. M &ge; Q</p>",
                    question_hi: "<p>4.&nbsp;दिए गए कथनों और निष्कर्षों को ध्यान से पढ़िए और तय कीजिये कि कौन-सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।</p>\n<p><strong>कथन:</strong> <br>L &gt; M = N &ge; O, P &ge; Q &le; R = O<br><strong>निष्कर्ष:</strong> <br>I. N &ge; O <br>II. L &gt; R <br>III. M &ge; Q</p>",
                    options_en: ["<p>Only conclusion II is true</p>", "<p>Only conclusion III is true</p>", 
                                "<p>Only conclusion I is true</p>", "<p>All conclusions I, II and III are true</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II सत्य है</p>", "<p>केवल निष्कर्ष III सत्य है</p>",
                                "<p>केवल निष्कर्ष I सत्य है</p>", "<p>सभी निष्कर्ष I, II और III सत्य हैं</p>"],
                    solution_en: "<p>4.(d)<br>I. N &ge; O&nbsp; &nbsp; &nbsp; &nbsp;True (as, L &gt; M = N &ge; O )<br>II. L &gt; R&nbsp; &nbsp; &nbsp; &nbsp;True<br>(as, L &gt; M = N &ge; O, P &ge; Q &le; R = O)<br>III. M &ge; Q&nbsp; &nbsp; &nbsp;True<br>(as M = N &ge; O, P &ge; Q &le; R = O)<br>All conclusions I, II and III are true</p>",
                    solution_hi: "<p>4.(d)<br>I. N &ge; O&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;सही (चूँकि L &gt; M = N &ge; O)<br>II. L &gt; R&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;सही <br>(चूँकि L &gt; M = N &ge; O, P &ge; Q &le; R = O)<br>III. M &ge; Q&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;सही<br>(चूँकि M = N &ge; O, P &ge; Q &le; R = O)<br>सभी निष्कर्ष I, II और III सत्य हैं</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. A convex lens forms a real, inverted and diminished image. The position of the object is:</p>",
                    question_hi: "<p>5. एक उत्तल लेंस एक वास्तविक, उल्टा और छोटा प्रतिबिंब निर्मित करता है। वस्तु की स्थिति क्या है ?</p>",
                    options_en: ["<p>at infinity</p>", "<p>between O and F</p>", 
                                "<p>between F and 2F</p>", "<p>beyond 2F</p>"],
                    options_hi: ["<p>अनंत पर</p>", "<p>O और F के बीच</p>",
                                "<p>F और 2F के बीच</p>", "<p>2F से परे</p>"],
                    solution_en: "<p>5.(d) <strong>Beyond 2F.</strong> Other Positions of Convex Lens: Placement of Object (Image Obtained) - At infinity (At F<sub>2</sub>, real, Inverted and highly diminished ). Object at 2F (At 2F<sub>2</sub> , real, inverted and Same size). Object between Focus and 2F (Beyond 2F<sub>2</sub>, real, inverted and magnified), Object at Focus (At infinity, real, inverted and magnified). The object is between Centre and Focus (Behind the lens, Virtual, erect and magnified).</p>",
                    solution_hi: "<p>5.(d) <strong>2F से परे ।</strong> उत्तल लेंस की अन्य स्थितियाँ: वस्तु का स्थान (प्राप्त प्रतिबिम्ब) - अनंत पर (F<sub>2</sub> पर, वास्तविक, उल्टा और अत्यधिक छोटा)। 2F पर वस्तु (2F<sub>2</sub> पर, वास्तविक, उलटा और समान आकार)। फोकस और 2F के बीच की वस्तु (2F<sub>2</sub> से परे, वास्तविक, उल्टा और आवर्धित), फोकस पर वस्तु (अनंत पर, वास्तविक, उल्टा और आवर्धित)। वस्तु केंद्र और फोकस के बीच (लेंस के पीछे, आभासी, सीधा और आवर्धित)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. Find the smallest natural number that can be divided by the first five 2-digit positive even numbers without leaving a remainder.</p>",
                    question_hi: "<p>6. वह सबसे छोटी प्राकृत संख्या ज्ञात कीजिए जिसे प्रथम पाँच 2-अंकीय धनात्मक सम संख्याओं से बिना शेषफल छोड़े विभाजित किया जा सके।</p>",
                    options_en: ["<p>5340</p>", "<p>5560</p>", 
                                "<p>5040</p>", "<p>5120</p>"],
                    options_hi: ["<p>5340</p>", "<p>5560</p>",
                                "<p>5040</p>", "<p>5120</p>"],
                    solution_en: "<p>6.(c) First five 2 digit positive even number = 10, 12, 14, 16, 18 <br>LCM of 10, 12, 14, 16, 18 = 5040<br>So, smallest natural number = 5040</p>",
                    solution_hi: "<p>6.(c) पहले पांच 2 अंकों की धनात्मक सम संख्या = 10, 12, 14, 16, 18<br>10, 12, 14, 16, 18 का लघुत्तम समापवर्त्य (LCM) = 5040<br>अत: सबसे छोटी प्राकृत संख्या = 5040</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. Write the option that matches the relationships shown in the first pair instead of the question mark.<br>F : P : : J : ??</p>",
                    question_hi: "<p>7. प्रथम युग्म में दर्शाए गए सम्बन्धों से मेल खाने वाले विकल्प को प्रश्नवाचक चिह्न के स्थान पर लिखिए <br>F : P : : J : ??</p>",
                    options_en: ["<p>T</p>", "<p>Z</p>", 
                                "<p>H</p>", "<p>R</p>"],
                    options_hi: ["<p>T</p>", "<p>Z</p>",
                                "<p>H</p>", "<p>R</p>"],
                    solution_en: "<p>7.(a) <br><strong>Logic :-&nbsp;</strong>First letter + 10 = Second letter<br>F + 10 = P<br>Similarly, J + 10 = T</p>",
                    solution_hi: "<p>7.(a) <br><strong>तर्क :-&nbsp;</strong>पहला अक्षर + 10 = दूसरा अक्षर<br>F + 10 = P<br>उसी प्रकार <br>J + 10 = T</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. The &lsquo;Vyas Samman&rsquo;, a literary award was first awarded in the year _______.</p>",
                    question_hi: "<p>8. \'व्यास सम्मान\', एक साहित्यिक पुरस्कार पहली बार वर्ष _____में प्रदान किया गया था।</p>",
                    options_en: ["<p>1989</p>", "<p>1999</p>", 
                                "<p>1990</p>", "<p>1991</p>"],
                    options_hi: ["<p>1989</p>", "<p>1999</p>",
                                "<p>1990</p>", "<p>1991</p>"],
                    solution_en: "<p>8.(d) <strong>1991. Vyas Samman</strong> (second highest literary award, after the Jnanpith award) is awarded annually by the KK Birla Foundation. Eligibility for the award - The literary work must be in the Hindi language and have been published in the past 10 years. The first Vyas Samman Awardee was Ram Vilas Sharma (1991).</p>",
                    solution_hi: "<p>8.(d) <strong>1991। व्यास सम्मान</strong> (ज्ञानपीठ पुरस्कार के बाद दूसरा सर्वोच्च साहित्यिक पुरस्कार) केके बिड़ला फाउंडेशन द्वारा प्रतिवर्ष प्रदान किया जाता है। पुरस्कार के लिए पात्रता - साहित्यिक कृति हिंदी भाषा में होनी चाहिए और पिछले 10 वर्षों में प्रकाशित हुई हो। प्रथम व्यास सम्मान पुरस्कार विजेता राम विलास शर्मा (1991) थे।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9.&nbsp;Read the given statements and conclusions carefully and decide which of the conclusions logically follow(s) from the statements.</p>\n<p><strong id=\"docs-internal-guid-7fd81488-7fff-a7ec-3336-94424488d460\"></strong><strong>Statements:</strong><br>M <math display=\"inline\"><mo>&#8804;</mo></math> N &lt; O , A &ge; B &lt; C = O<br><strong>Conclusions:</strong><br>I. N &gt; B<br>II. C &gt; M</p>",
                    question_hi: "<p>9. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़िए और तय कीजिये कि कौन-सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।</p>\n<p><strong id=\"docs-internal-guid-e155c8aa-7fff-101a-e2dd-60c245e5304f\"></strong><strong>कथन:</strong><br>M <math display=\"inline\"><mo>&#8804;</mo></math> N &lt; O , A &ge; B &lt; C = O<br><strong>निष्कर्ष:</strong><br>I. N &gt; B<br>II. C &gt; M</p>",
                    options_en: ["<p>Both the conclusions I and II follow.</p>", "<p>Only conclusion I follows</p>", 
                                "<p>Neither conclusion I nor II follows</p>", "<p>Only conclusion II follows.</p>"],
                    options_hi: ["<p>निष्कर्ष I और II दोनों पालन करते हैं।</p>", "<p>केवल निष्कर्ष । पालन करता है।</p>",
                                "<p>न तो निष्कर्ष । और न ही ॥ पालन करता है।</p>", "<p>केवल निष्कर्ष ॥ पालन करता है।</p>"],
                    solution_en: "<p>9.(d) As per direction of question we can arrange all in this way<br>M <math display=\"inline\"><mo>&#8804;</mo></math> N &lt; O = C &gt; B &le; A<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623021552.png\" alt=\"rId4\" width=\"146\" height=\"55\"><br>Assume that<br><strong>Statement (i)</strong><br>N &gt; B &rArr; In the above expression we can not move from N to B. (because gate is closed from the side of N to B) so this is incorrect.<br><strong>Statement (ii)</strong><br>C &gt; M &rArr; In the above expression we can move from C to M. So this is correct.</p>",
                    solution_hi: "<p>9.(d) प्रश्न की दिशा के अनुसार हम सभी को&nbsp;इस प्रकार व्यवस्थित कर सकते हैं<br>M <math display=\"inline\"><mo>&#8804;</mo></math> N &lt; O = C &gt; B &le; A<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623021552.png\" alt=\"rId4\" width=\"146\" height=\"55\"><br>मान लो की <br><strong>कथन (i)</strong> <br>N &gt; B &rArr; उपरोक्त व्यंजक में हम N से B की ओर नहीं जा सकते (क्योंकि N की ओर से द्वार बंद है। तो यह गलत है।<br><strong>कथन (ii)</strong><br>C &gt; M &rArr; उपरोक्त व्यंजक में हम C से M तक जा सकते हैं। अतः यह सही है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. The resistance of a conductor is inversely proportional to its :</p>",
                    question_hi: "<p>10. एक चालक का प्रतिरोध किसके व्युत्क्रमानुपाती होता है ?</p>",
                    options_en: ["<p>area of cross section</p>", "<p>length</p>", 
                                "<p>temperature</p>", "<p>resistivity</p>"],
                    options_hi: ["<p>अनुप्रस्थ काट के क्षेत्रफल</p>", "<p>लंबाई</p>",
                                "<p>तापमान</p>", "<p>प्रतिरोधकता</p>"],
                    solution_en: "<p>10.(a)<strong> Area of cross section.</strong> Resistance of the conductor is proportional to the length of wire and inversely proportional to the area of cross-section. Resistance of the conductor <strong>(R) = &rho; <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">l</mi></mrow><mrow><mi mathvariant=\"bold-italic\">A</mi></mrow></mfrac></math>, R &prop;<math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">l</mi></mrow><mrow><mi mathvariant=\"bold-italic\">A</mi></mrow></mfrac></math>.</strong> Where &rho; is the resistivity of the conductor, L is its length and A is the cross sectional area of the conductor. Electrical Resistivity is a measure of how strongly it opposes the flow of current. It is denoted by the symbol \"&rho;\" (rho) and is measured in ohm-meters (&Omega;&middot;m).</p>",
                    solution_hi: "<p>10.(a) <strong>अनुप्रस्थ काट के क्षेत्रफल। </strong>चालक का प्रतिरोध तार की लंबाई के समानुपाती और अनुप्रस्थ काट के क्षेत्रफल के व्युत्क्रमानुपाती होता है। चालक का प्रतिरोध <strong>(R) = &rho; <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">l</mi></mrow><mrow><mi mathvariant=\"bold-italic\">A</mi></mrow></mfrac></math>, R &prop;<math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">l</mi></mrow><mrow><mi mathvariant=\"bold-italic\">A</mi></mrow></mfrac></math></strong>, जहां &rho; = चालक की प्रतिरोधकता , l = लंबाई और A = अनुप्रस्थ काट का क्षेत्रफल है। विद्युत प्रतिरोधकता इस बात का माप है कि यह धारा के प्रवाह का कितनी दृढ़ता से विरोध करता है। इसे प्रतीक \"&rho;\" (rho) द्वारा दर्शाया जाता है और इसे ओम-मीटर (&Omega;&middot;m) में मापा जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. If the 8 - digit number 3x5479y4 is divisible by 88 and the 8-digit number 425139z2 is divisible by 9, then what is&nbsp;the greatest possible value of (3x + 2y - z) ?</p>",
                    question_hi: "<p>11. यदि 8 अंको की संख्या 3x5479y4, 88 से विभाज्य है, और 8 अंको की संख्या 425139z2, 9 से विभाज्य है, तो (3x + 2y - z) का अधिकतम संभावित मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>25</p>", "<p>33</p>", 
                                "<p>35</p>", "<p>37</p>"],
                    options_hi: ["<p>25</p>", "<p>33</p>",
                                "<p>35</p>", "<p>37</p>"],
                    solution_en: "<p>11.(b) If 3x5479y4 is divisible by 88 it must be divisible 11 and 8<br>Divisibility rule for 8 &rarr;&nbsp;if last three digit number of any number divisible by 8 it will be divisible by 8<br>For 3x5479y4 &rArr; Y = 0, 4,8<br>(for greatest value we take Y = 8)<br>Divisibility rule for 11 &rarr; difference between sum of digit of odd place and sum of digit of even place should be 0 or multiple of 11<br>3 + 5 + 7 + y - (x + 4 + 9 + 4) = 0 or 11<br>15 + y - (x + 17) = 0&nbsp;<br>&rArr; 15 + y = (x +17)<br>Put Y = 8 we get X = 6 <br>Divisibility rule for 9 &rarr; sum of all digit is divisible by 9, 425139z2 <br>&rarr; 4 + 2 + 5 + 1 + 3 + 9 + z + 2 = 26 + z<br>= Z = 1 = (3x + 2y - z) = (3 &times; 6 + 2 &times; 8 - 1) = 33</p>",
                    solution_hi: "<p>11.(b) यदि 3x5479y4, 88 से विभाज्य है तो यह 11 और 8 विभाज्य होना चाहिए<br>8 के लिए विभाज्यता नियम &rarr; यदि किसी संख्या के अंतिम तीन अंक 8 से विभाज्य हो तो वह 8 से विभाज्य होगी 3x5479y4 के लिए<br>Y = 0, 4,8 (सबसे बड़े मान के लिए हम लेते हैं,&nbsp;Y = 8), <br>11 के लिए विभाज्यता नियम &rarr; विषम स्थान के अंक और सम स्थान के अंक के योग का अंतर 0 या 11 का गुणक होना चाहिए<br>3 + 5 + 7 + y - (x + 4 + 9 + 4) = 0 or 11<br>15 + y - (x + 17) = 0 <br>&rArr; 15 + y = (x + 17)<br>Y = 8 रखने पर हमें प्राप्त होता है, X = 6 <br>9 का विभाज्यता नियम <br>&rarr; सभी अंकों का योग 9 से विभाज्य है<br>425139z2 &rarr; 4 + 2 + 5 + 1 + 3 + 9 + z + 2 <br>= 26 + z इसलिए, Z = 1&nbsp;<br>(3x + 2y - z) = (3 &times; 6 + 2 &times; 8 - 1) = 33</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. Select the number triad that is different from the rest.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/ subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>12. उस संख्या त्रिक का चयन करें जो बाकी से अलग है। (नोट: संक्रियाएं, संख्याओं को उसके संघटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर की जानी चाहिए। उदाहरणार्थ - 13 पर की जाने वाली सभी संक्रियाएं जैसे जोड़ना / घटाना / गुणा करना आदि, 13 - पर ही की जानी चाहिए। 13 को 1 और 3 में विभाजित करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>(34, 22, 12)</p>", "<p>(99, 79, 60)</p>", 
                                "<p>(47, 33, 21)</p>", "<p>(79, 61, 45)</p>"],
                    options_hi: ["<p>(34, 22, 12)</p>", "<p>(99, 79, 60)</p>",
                                "<p>(47, 33, 21)</p>", "<p>(79, 61, 45)</p>"],
                    solution_en: "<p>12.(b)<br><strong>Logic :-</strong> [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>first</mi><mo>&#160;</mo><mi>num</mi><mo>+</mo><mi>third</mi><mi mathvariant=\"normal\">&#160;</mi><mi>num</mi></mrow><mn>2</mn></mfrac></math> - 1&nbsp;= second number]<br>(34, 22, 12) &rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>34</mn></mrow><mn>2</mn></mfrac></math> = 23 - 1 = 22 <br>(47, 33, 21) &rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>47</mn><mo>+</mo><mn>21</mn></mrow><mn>2</mn></mfrac></math> = 34 - 1 = 33 <br>(79, 61, 45) &rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>79</mn><mo>+</mo><mn>45</mn></mrow><mn>2</mn></mfrac></math> = 62 - 1 = 61<br>(99, 79, 60) &rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>99</mn><mo>+</mo><mn>60</mn></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>159</mn><mn>2</mn></mfrac></math> - 1<br>(not followed)</p>",
                    solution_hi: "<p>12.(b)<br><strong>तर्क :-</strong> [<math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>&#2340;&#2368;&#2360;&#2352;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mn>2</mn></mfrac></math> - 1&nbsp;= दूसरी संख्या]<br>(34, 22, 12) &rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>34</mn></mrow><mn>2</mn></mfrac></math> = 23 - 1 = 22 <br>(47, 33, 21) &rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>47</mn><mo>+</mo><mn>21</mn></mrow><mn>2</mn></mfrac></math> = 34 - 1 = 33 <br>(79, 61, 45) &rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>79</mn><mo>+</mo><mn>45</mn></mrow><mn>2</mn></mfrac></math> = 62 - 1 = 61<br>(99, 79, 60) &rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>99</mn><mo>+</mo><mn>60</mn></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>159</mn><mn>2</mn></mfrac></math> - 1<br>(अनुसरण नहीं किया गया)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. In which state is the five day religious festival of \'Shad Nongkrem\' celebrated ?</p>",
                    question_hi: "<p>13. \'शाद नोंगक्रेम\' का पांच दिवसीय धार्मिक उत्सव किस राज्य में मनाया जाता है ?</p>",
                    options_en: ["<p>Meghalaya</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Tripura</p>"],
                    options_hi: ["<p>मेघालय</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>त्रिपुरा</p>"],
                    solution_en: "<p>13.(a) <strong>Meghalaya. Shad Nongkrem</strong> is celebrated generally in the month of November, every year at Smit (cultural centre of the Khasi Hills) to appease the all-powerful Goddess &lsquo;Ka Blei Synshar&rsquo; in the hope of gaining a bountiful harvest and prosperity. Other festivals of Meghalaya - Shad Suk Mynsiem, Behdienkhlam Festival, Shad Sukra, Wangala Festival.</p>",
                    solution_hi: "<p>13.(a) <strong>मेघालय। शाद नोंगक्रेम</strong> आम तौर पर हर साल नवंबर के महीने में स्मित (खासी पहाड़ियों का सांस्कृतिक केंद्र) में भरपूर फसल और समृद्धि प्राप्त करने की आशा में सर्व-शक्तिशाली देवी \'का ब्ली सिंशर\' को प्रसन्न करने के लिए मनाया जाता है। मेघालय के अन्य त्यौहार - शाद सुक म्यनसीम, बेहदीनखलम महोत्सव, शाद सुकरा, वांगला महोत्सव।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. If a ray of light is incident passing through the centre of curvature of a concave mirror, then the angle between the incident ray and the reflected ray will be equal to:</p>",
                    question_hi: "<p>14. यदि प्रकाश की किरण अवतल दर्पण के वक्रता केंद्र से होकर गुजरती है, तो आपतित किरण और परावर्तित किरण के बीच बनने वाला कोण ______के बराबर होगा ?</p>",
                    options_en: ["<p>0&deg;</p>", "<p>150&deg;</p>", 
                                "<p>90&deg;</p>", "<p>180&deg;</p>"],
                    options_hi: ["<p>0&deg;</p>", "<p>150&deg;</p>",
                                "<p>90&deg;</p>", "<p>180&deg;</p>"],
                    solution_en: "<p>14.(a) <strong>0&deg;.</strong> As the ray of light passes through the Center of Curvature of a Concave mirror, it strikes the mirror along the normal (i.e. it incidences onto the mirror at 90 degrees and 0 degrees with normal). Hence the Incident ray coincides with the normal. Therefore the Angle of Incidence is 0 degrees.</p>",
                    solution_hi: "<p>14.(a) <strong>0&deg; ।</strong> जैसे ही प्रकाश की किरण एक अवतल दर्पण के वक्रता केंद्र से होकर गुजरती है, यह दर्पण पर अभिलम्ब के साथ टकराती है (अर्थात यह दर्पण पर 90 डिग्री और अभिलम्ब के साथ 0 डिग्री पर आपतित होती है)। अतः आपतित किरण अभिलम्ब के संपाती है। इसलिए आपतन कोण 0 डिग्री है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. What is the sum of <br>7xy + 5yz - 3zx, 4yz + 9zx - 4y, - 3xy + 5x - 2xy ?&nbsp;</p>",
                    question_hi: "<p>15. 7xy + 5yz - 3zx, 4yz + 9zx - 4y, - 3xy + 5x - 2xy का योगफल ज्ञात करें ?</p>",
                    options_en: ["<p>5x - 4y + 2xy + 9yz + 6zx</p>", "<p>5x - 4y - 2xy - 9yz + 6zx</p>", 
                                "<p>5x - 4y + 6x + 9yz + 6zx</p>", "<p>5x - 4y + 6xy + 9yz - 6zx</p>"],
                    options_hi: ["<p>5x - 4y + 2xy + 9yz + 6zx</p>", "<p>5x - 4y - 2xy - 9yz + 6zx</p>",
                                "<p>5x - 4y + 6x + 9yz + 6zx</p>", "<p>5x - 4y + 6xy + 9yz - 6zx</p>"],
                    solution_en: "<p>15.(a) Sum of these numbers,<br>(7xy + 5yz - 3zx) + (4yz + 9zx - 4y) + (- 3xy + 5x - 2xy)<br>&rArr; 7xy + 5yz - 3zx + 4yz + 9zx - 4y - 3xy + 5x - 2xy<br>&rArr; 7xy - 3xy - 2xy + 5yz + 4yz - 3zx + 9zx - 4y + 5x<br>&rArr; 2xy + 9yz + 6zx - 4y + 5x<br>= 5x - 4y + 2xy + 9yz + 6zx</p>",
                    solution_hi: "<p>15.(a) इन संख्याओं का योग,<br>(7xy + 5yz - 3zx) + (4yz + 9zx - 4y) + (- 3xy + 5x - 2xy)<br>&rArr; 7xy + 5yz - 3zx + 4yz + 9zx - 4y - 3xy + 5x - 2xy<br>&rArr; 7xy - 3xy - 2xy + 5yz + 4yz - 3zx + 9zx - 4y + 5x<br>&rArr; 2xy + 9yz + 6zx - 4y + 5x<br>= 5x - 4y + 2xy + 9yz + 6zx</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. In a certain code language , NAMAN is written as 92. How will CARPET be written as in that language ?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में NAMAN को 92 लिखा जाता है। उसी भाषा में CARPET को किस प्रकार लिखा जाएगा ?&nbsp;</p>",
                    options_en: ["<p>99</p>", "<p>97</p>", 
                                "<p>96</p>", "<p>98</p>"],
                    options_hi: ["<p>99</p>", "<p>97</p>",
                                "<p>96</p>", "<p>98</p>"],
                    solution_en: "<p>16.(a) <strong>Logic :</strong> Sum of place values of opposite letters.<br>CARPET = 24 + 26 + 9 +11 + 22 + 7 = 99</p>",
                    solution_hi: "<p>16.(a) <strong>तर्क : </strong>विपरीत अक्षरों के स्थानीय मानों का योग।<br>CARPET = 24 + 26 + 9 +11 + 22 + 7 = 99</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. From among the following ______learnt music from Swami Haridas for eleven years.</p>",
                    question_hi: "<p>17. निम्न में से ______ने स्वामी हरिदास से ग्यारह वर्ष तक संगीत सीखा था।</p>",
                    options_en: ["<p>Tanarang</p>", "<p>Ramdas</p>", 
                                "<p>Tansen</p>", "<p>Baiju Bawra</p>"],
                    options_hi: ["<p>तनरंग</p>", "<p>रामदास</p>",
                                "<p>तानसेन</p>", "<p>बैजू बावरा</p>"],
                    solution_en: "<p>17.(c) <strong>Tansen.</strong> Akbar considered him as a Navaratnas (nine jewels), and gave him the title Mian. Tansen is remembered for his epic Dhrupad compositions. Tansen was the title given to him by Raja Vikramjit of Gwalior. Tansen was a court musician in the darbar of Raja Ramachandra of Bandavagarh. Tansen Samaroh (Gwalior district, Madhya Pradesh, 4-day musical extravaganza).</p>",
                    solution_hi: "<p>17.(c) <strong>तानसेन । </strong>अकबर ने उन्हें नवरत्न (नौ रत्न) माना और उन्हें मियां की उपाधि दी। तानसेन को उनकी महाकाव्य ध्रुपद रचनाओं के लिए याद किया जाता है। तानसेन की उपाधि उन्हें ग्वालियर के राजा विक्रमजीत ने दी थी। तानसेन बाँदवगढ़ के राजा रामचन्द्र के दरबार में दरबारी संगीतकार थे। तानसेन समारोह - (ग्वालियर जिला, मध्य प्रदेश, 4 दिवसीय संगीत समारोह)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. The induced emf developed in a conductor when it is moved in a direction parallel to the magnetic field is</p>",
                    question_hi: "<p>18. चुंबकीय क्षेत्र के समानांतर दिशा में गति करने पर एक चालक में उत्पन्न प्रेरित विद्युत वाहक बल (EMF) ______होता है</p>",
                    options_en: ["<p>Minimum</p>", "<p>Zero</p>", 
                                "<p>Maximum</p>", "<p>Infinity</p>"],
                    options_hi: ["<p>न्यूनतम</p>", "<p>शून्य</p>",
                                "<p>अधिकतम</p>", "<p>अनंत</p>"],
                    solution_en: "<p>18.(b) <strong>Zero.</strong> The magnetic field linked with the conductor does not change when it moves parallel to the magnetic field. Hence, emf is not induced. According to Faraday\'s law of electromagnetic induction &rArr; e = - N <math display=\"inline\"><mfrac><mrow><mi>d</mi><mi>&#160;</mi><mi>&#934;</mi></mrow><mrow><mi>d</mi><mi>t</mi></mrow></mfrac></math>, &Phi; = BA cos&theta;, then e = - N<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>d</mi><mo>(</mo><mi>B</mi><mi>A</mi><mo>&#160;</mo><mi>cos</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mi>d</mi><mi>t</mi></mrow></mfrac></math>, Where N = number of turns, e = induced e.m.f and B is the magnetic field strength over an area A, at an angle &theta;. When magnetic field lines are parallel to the conductor then the angle between them is 0&deg;. If we put &theta; = 0&deg; then cos 0&deg; = 0, Hence e = 0.</p>",
                    solution_hi: "<p>18.(b) <strong>शून्य। </strong>चालक के संपर्क में चुंबकीय क्षेत्र, चुंबकीय क्षेत्र के समानांतर गति करने पर नहीं बदलता है। इसलिए, विद्युत वाहक बाल emf प्रेरित नहीं होता है। फैराडे के विद्युत चुंबकीय प्रेरण के नियमानुसार &rArr; e = - N <math display=\"inline\"><mfrac><mrow><mi>d</mi><mi>&#160;</mi><mi>&#934;</mi></mrow><mrow><mi>d</mi><mi>t</mi></mrow></mfrac></math>, &Phi; = BA cos&theta; तो e = - N<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>d</mi><mo>(</mo><mi>B</mi><mi>A</mi><mo>&#160;</mo><mi>cos</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mi>d</mi><mi>t</mi></mrow></mfrac></math>, जहां N = घुमावों की संख्या, e = प्रेरण और B एक कोण &theta; पर एक क्षेत्र A पर चुंबकीय क्षेत्र की शक्ति है। जब चुंबकीय क्षेत्र रेखाएँ चालक के समानांतर होती हैं तो उनके बीच का कोण 0&deg; होता है। यदि हम &theta; = 0&deg; रखते हैं तो cos 0&deg; = 0 होगा, अत: e = 0 होगा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. Solve the following:<br><math display=\"inline\"><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><mi>&#160;</mi><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></msqrt></msqrt></msqrt></msqrt></math></p>",
                    question_hi: "<p>19. निम्नलिखित को हल कीजिये :<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><mi>&#160;</mi><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></msqrt></msqrt></msqrt></msqrt></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#177;</mo><mi>&#160;</mi><msqrt><mn>31</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#177;</mo><mi>&#160;</mi><msqrt><mn>29</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#177;</mo><mi>&#160;</mi><msqrt><mn>30</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#177;</mo><mi>&#160;</mi><msqrt><mn>28</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#177;</mo><mi>&#160;</mi><msqrt><mn>31</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#177;</mo><mi>&#160;</mi><msqrt><mn>29</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#177;</mo><mi>&#160;</mi><msqrt><mn>30</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#177;</mo><mi>&#160;</mi><msqrt><mn>28</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>19.(b) Let<br><math display=\"inline\"><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><mi>&#160;</mi><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></msqrt></msqrt></msqrt></msqrt></math> = X -------- (i)<br>Squaring on both sides of the above equation.<br>&rArr; 7 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>+</mo><mo>&#160;</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></msqrt></msqrt></msqrt></math> = X<sup>2</sup><br>&rArr; 7 + X = X<sup>2</sup> &rArr; X = X<sup>2 </sup>- 7&nbsp;<br>Putting the value of <math display=\"inline\"><mi>X</mi></math> in equation (i)<br>So that, <math display=\"inline\"><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><mi>&#160;</mi><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></msqrt></msqrt></msqrt></msqrt></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#177;</mo><msqrt><mn>29</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>19.(b) माना की, <br><math display=\"inline\"><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><mi>&#160;</mi><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></msqrt></msqrt></msqrt></msqrt></math> = x --------- (i)<br>उपरोक्त समीकरण के दोनों पक्षों का वर्ग करने पर,<br>&rArr; 7 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>+</mo><mo>&#160;</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></msqrt></msqrt></msqrt></math> = X<sup>2</sup><br>&rArr; 7 + X = X<sup>2</sup> &rArr; X = X<sup>2 </sup>- 7&nbsp;<br>X का मान समीकरण (i) में रखने पर<br><math display=\"inline\"><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><msqrt><mn>7</mn><mo>+</mo><mi>&#160;</mi><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></msqrt></msqrt></msqrt></msqrt></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#177;</mo><msqrt><mn>29</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. In a certain code language. &lsquo;MFBTF&rsquo; is written as &lsquo;LEASE\' and \'UNISE\' is written as &lsquo;TMHRD\'. How will &lsquo;TRICK\' be written in that language ?</p>",
                    question_hi: "<p>20. एक निश्चित कोड भाषा में &lsquo;MFBTF&rsquo; को &lsquo;LEASE\' लिखा जाता है और \'UNISE\' को &lsquo;TMHRD\' लिखा जाता है। उस भाषा में &lsquo;TRICK\' को क्या लिखा जाएगा ?</p>",
                    options_en: ["<p>USDJL</p>", "<p>SQHBJ</p>", 
                                "<p>USJDL</p>", "<p>SSHJB</p>"],
                    options_hi: ["<p>USDJL</p>", "<p>SQHBJ</p>",
                                "<p>USJDL</p>", "<p>SSHJB</p>"],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623021707.png\" alt=\"rId5\" width=\"126\" height=\"68\">&nbsp; &nbsp;, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623021850.png\" alt=\"rId6\" width=\"127\" height=\"68\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623021947.png\" alt=\"rId7\" width=\"124\" height=\"68\"></p>",
                    solution_hi: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623021707.png\" alt=\"rId5\" width=\"126\" height=\"68\">&nbsp; &nbsp;, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623021850.png\" alt=\"rId6\" width=\"127\" height=\"68\"><br>इसी प्रकार, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623021947.png\" alt=\"rId7\" width=\"124\" height=\"68\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. In which of the following states did NESTLE India Ltd. set up its first factory in 1961 ?</p>",
                    question_hi: "<p>21. निम्नलिखित में से किस राज्य में नेस्ले इंडिया लिमिटेड ने 1961 में अपना पहला कारखाना स्थापित किया था ?</p>",
                    options_en: ["<p>Madhya Pradesh</p>", "<p>Punjab</p>", 
                                "<p>Kerala</p>", "<p>Haryana</p>"],
                    options_hi: ["<p>मध्य प्रदेश</p>", "<p>पंजाब</p>",
                                "<p>केरल</p>", "<p>हरियाणा</p>"],
                    solution_en: "<p>21.(b) <strong>Punjab. NESTLE India Ltd. -</strong> Indian subsidiary of Nestl&eacute; which is a Swiss multinational company. The company was founded on 28th March 1959 in New Delhi. Headquartered in Gurgaon, Haryana. The second plant was set up at Choladi in Tamil Nadu in 1967.</p>",
                    solution_hi: "<p>21.(b) <strong>पंजाब। नेस्ले इंडिया लिमिटेड -</strong> नेस्ले की भारतीय सहायक कंपनी जो एक स्विस बहुराष्ट्रीय कंपनी है। कंपनी की स्थापना 28 मार्च 1959 को नई दिल्ली में हुई थी। मुख्यालय गुड़गांव, हरियाणा में है। दूसरा संयंत्र 1967 में तमिलनाडु के चोलाडी में स्थापित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. What is the sum of the digits placed at the even places when counted from left to right in the number 479812356 ?</p>",
                    question_hi: "<p>22. संख्या 479812356 में बाएं से दाएं गिनने पर सम स्थानों पर रखे गए अंकों का योग क्या है ?</p>",
                    options_en: ["<p>18</p>", "<p>26</p>", 
                                "<p>22</p>", "<p>3</p>"],
                    options_hi: ["<p>18</p>", "<p>26</p>",
                                "<p>22</p>", "<p>3</p>"],
                    solution_en: "<p>22.(c) Sum of the digits at the even places = 7 + 8 + 2 + 5 = 22</p>",
                    solution_hi: "<p>22.(c) सम स्थानों के अंकों का योग = 7 + 8 + 2 + 5 = 22</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. Consider a conductor in the plane of the paper with current going from bottom to the top. What will be the direction of the magnetic field lines to the left of the conductor ?</p>",
                    question_hi: "<p>23. एक चालक को कागज के तल में रखा गया है जिसमें धारा नीचे से ऊपर की ओर जा रही है। चालक के बाईं ओर चुंबकीय क्षेत्र रेखाओं की दिशा क्या होगी ?<strong id=\"docs-internal-guid-afe2a425-7fff-02f7-9bf7-10abbebb869c\"><br></strong></p>",
                    options_en: ["<p>Perpendicular to the plane of the paper and going in.</p>", "<p>Opposite to the direction of the current.</p>", 
                                "<p>In the direction of the current.</p>", "<p>Perpendicular to the plane of the paper and coming out.</p>"],
                    options_hi: ["<p>कागज के तल के लम्बवत और अंदर की ओर।</p>", "<p>धारा की दिशा के विपरीत।</p>",
                                "<p>धारा की दिशा में।</p>", "<p dir=\"ltr\">कागज के तल के लम्बवत और बाहर की ओर।</p>"],
                    solution_en: "<p>23.(d) To determine the direction of the magnetic field lines, we apply the Right-Hand Thumb Rule: Place your right-hand thumb in the direction of the current (bottom to top in the plane of the paper). The curl of your fingers represents the direction of the magnetic field lines. When you do this: On the left side of the conductor, the magnetic field lines will emerge perpendicular to the plane of the paper and coming out.</p>",
                    solution_hi: "<p>23.(d) चुंबकीय क्षेत्र रेखाओं की दिशा निर्धारित करने के लिए, दाहिने हाथ के अंगूठे के नियम (Right-Hand Thumb Rule) का उपयोग करें: अपना दाहिना हाथ इस प्रकार रखें कि आपका अंगूठा धारा की दिशा (कागज के तल में नीचे से ऊपर) में हो। आपकी उंगलियों का घुमाव चुंबकीय क्षेत्र रेखाओं की दिशा को दर्शाता है। ऐसा करने पर: चालक के बाईं ओर चुंबकीय क्षेत्र रेखाएं कागज के तल के लम्बवत और बाहर की ओर निकलेंगी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. Find the value of <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>04</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>+</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math></p>",
                    question_hi: "<p>24. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>04</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>+</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math>&nbsp;का मान ज्ञात करे</p>",
                    options_en: ["<p>9</p>", "<p>6</p>", 
                                "<p>7</p>", "<p>8</p>"],
                    options_hi: ["<p>9</p>", "<p>6</p>",
                                "<p>7</p>", "<p>8</p>"],
                    solution_en: "<p>24.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>04</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>+</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>04</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>[</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow></mfrac></math>&times; 8 = 8</p>",
                    solution_hi: "<p>24.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>04</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>+</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>04</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>[</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>02</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow></mfrac></math>&times; 8 = 8</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25.&nbsp;What should come in place of \'?\' in the given series?&nbsp;</p>\n<p><strong id=\"docs-internal-guid-091cdf12-7fff-2541-8dc2-48f0fab03303\"></strong>123, 123, 110, 110, 121, 121, 108, 108, ?, ?</p>",
                    question_hi: "<p>25. दी गई श्रृंखला में \'?\' के स्थान पर क्या आना चाहिए?&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>\n<p>123, 123, 110, 110, 121, 121, 108, 108, ?, ?</p>",
                    options_en: ["<p>123, 123</p>", "<p>119, 119</p>", 
                                "<p>97, 97</p>", "<p>127, 127</p>"],
                    options_hi: ["<p>123, 123</p>", "<p>119, 119</p>",
                                "<p>97, 97</p>", "<p>127, 127</p>"],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022112.png\" alt=\"rId8\" width=\"321\" height=\"88\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022112.png\" alt=\"rId8\" width=\"321\" height=\"88\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. In which year was the first radio programme broadcast in India ?</p>",
                    question_hi: "<p>26. भारत में पहला रेडियो कार्यक्रम किस वर्ष प्रसारित किया गया था ?</p>",
                    options_en: ["<p>1936</p>", "<p>1957</p>", 
                                "<p>1930</p>", "<p>1923</p>"],
                    options_hi: ["<p>1936</p>", "<p>1957</p>",
                                "<p>1930</p>", "<p>1923</p>"],
                    solution_en: "<p>26.(d) <strong>1923.</strong> Radio broadcasting in India began with the setting up of the Radio Club of Bombay. In 1956, All India Radio (established in 1936) became Akashwani. In 1959, Doordarshan was launched. Prasar Bharati is a statutory autonomous body established under the Prasar Bharati Act, 1990, for public radio and TV broadcasting in India.</p>",
                    solution_hi: "<p>26.(d)<strong> 1923 । </strong>भारत में रेडियो प्रसारण की शुरुआत रेडियो क्लब ऑफ बॉम्बे की स्थापना के साथ हुई। 1956 में, ऑल इंडिया रेडियो (1936 में स्थापित) का नाम बदलकर आकाशवाणी कर दिया गया। 1959 में दूरदर्शन की शुरुआत हुई। प्रसार भारती भारत में सार्वजनिक रेडियो और टीवी प्रसारण के लिए प्रसार भारती अधिनियम, 1990 के तहत स्थापित एक वैधानिक स्वायत्त निकाय है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. The point where the entire weight of an object acts is known as the ______.</p>",
                    question_hi: "<p>27. वह बिंदु जहाँ किसी वस्तु का संपूर्ण भार कार्य करता है, उसे ______कहा जाता है।</p>",
                    options_en: ["<p>centre of friction</p>", "<p>centre of mass</p>", 
                                "<p>centre of gravity</p>", "<p>centre of pressure</p>"],
                    options_hi: ["<p>घर्षण केंद्र</p>", "<p>द्रव्यमान केंद्र</p>",
                                "<p>गुरुत्व केंद्र</p>", "<p>दाब केंद्र</p>"],
                    solution_en: "<p>27.(c) <strong>Centre of gravity. </strong>Centre of mass - It is the average position of all the parts of the system, weighted according to their masses. Centre of pressure - refers to the point where the resultant force due to fluid pressure acts on a submerged or partially submerged object.</p>",
                    solution_hi: "<p>27.(c) <strong>गुरुत्व केंद्र । </strong>द्रव्यमान केंद्र - यह निकाय के सभी भागों की औसत स्थिति है, जो उनके द्रव्यमान के अनुसार भारित होती है। दाब केंद्र - यह उस बिंदु को संदर्भित करता है जहां तरल पदार्थ के दाब के कारण परिणामी बल एक जलमग्न या आंशिक रूप से जलमग्न वस्तु पर कार्य करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. If <math display=\"inline\"><mi>&#952;</mi></math> is an acute angle, and cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math>, then the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mi>sin</mi><mi>&#952;</mi><mo>+</mo><mn>12</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow><mrow><mn>12</mn><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mn>5</mn><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>28. यदि <math display=\"inline\"><mi>&#952;</mi></math> एक न्यून कोण है, और cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mi>sin</mi><mi>&#952;</mi><mo>+</mo><mn>12</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow><mrow><mn>12</mn><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mn>5</mn><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>28.(a) <math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac><mo>=</mo><mfrac><mi>B</mi><mi>H</mi></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>13</mn><mn>2</mn></msup><mo>-</mo><msup><mn>12</mn><mn>2</mn></msup></msqrt></math> = 5<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mi>sin</mi><mi>&#952;</mi><mo>+</mo><mn>12</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow><mrow><mn>12</mn><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mn>5</mn><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>13</mn></mfrac><mo>+</mo><mn>12</mn><mo>&#215;</mo><mfrac><mn>13</mn><mn>12</mn></mfrac></mrow><mrow><mn>12</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mn>5</mn><mo>&#215;</mo><mfrac><mn>13</mn><mn>5</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>13</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>13</mn></mrow></mfrac></math> = 1</p>",
                    solution_hi: "<p>28.(a) <math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac><mo>=</mo><mfrac><mi>B</mi><mi>H</mi></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>13</mn><mn>2</mn></msup><mo>-</mo><msup><mn>12</mn><mn>2</mn></msup></msqrt></math> = 5<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mi>sin</mi><mi>&#952;</mi><mo>+</mo><mn>12</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow><mrow><mn>12</mn><mi>tan</mi><mi>&#952;</mi><mo>+</mo><mn>5</mn><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>13</mn></mfrac><mo>+</mo><mn>12</mn><mo>&#215;</mo><mfrac><mn>13</mn><mn>12</mn></mfrac></mrow><mrow><mn>12</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mn>5</mn><mo>&#215;</mo><mfrac><mn>13</mn><mn>5</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>13</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>13</mn></mrow></mfrac></math> = 1</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. What should come in place of \'?\' in the given series?&nbsp;</p>\n<p>3, 7, 13, 21, 31, ?</p>",
                    question_hi: "<p>29. दी गई श्रृंखला में \'?\' के स्थान पर क्या आना चाहिए?&nbsp;</p>\n<p>3, 7, 13, 21, 31, ?</p>",
                    options_en: ["<p>44</p>", "<p>41</p>", 
                                "<p>42</p>", "<p>43</p>"],
                    options_hi: ["<p>44</p>", "<p>41</p>",
                                "<p>42</p>", "<p>43</p>"],
                    solution_en: "<p>29.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022233.png\" alt=\"rId9\" width=\"220\" height=\"44\"></p>",
                    solution_hi: "<p>29.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022233.png\" alt=\"rId9\" width=\"220\" height=\"44\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. Sachin Tendulkar scored his 100th international cricket century against which team ?</p>",
                    question_hi: "<p>30. सचिन तेंदुलकर ने अपना 100वां अंतरराष्ट्रीय क्रिकेट शतक किस टीम के खिलाफ&nbsp;बनाया ?</p>",
                    options_en: ["<p>Bangladesh</p>", "<p>England</p>", 
                                "<p>Pakistan</p>", "<p>Australia</p>"],
                    options_hi: ["<p>बांग्लादेश</p>", "<p>इंगलैंड</p>",
                                "<p>पाकिस्तान</p>", "<p>ऑस्ट्रेलिया</p>"],
                    solution_en: "<p>30.(a) <strong>Bangladesh.&nbsp;</strong>Sachin Tendulkar (Master Blaster) - Became the first and only cricketer (As of June 2023) to score a hundred international centuries. Awards: The Bharat Ratna (2014), The Padma Vibhushan (2008), The Padma Shri (1998), The Khel Ratna Award (1997), The Arjuna Award (1994). He is the youngest recipient to receive&nbsp;the Bharat Ratna.</p>",
                    solution_hi: "<p>30.(a) <strong>बंगलादेश । </strong>सचिन तेंदुलकर (मास्टर ब्लास्टर) - सौ अंतरराष्ट्रीय शतक बनाने वाले (जून 2023 तक) पहले और एकमात्र क्रिकेटर बने। पुरस्कार: भारत रत्न (2014), पद्म विभूषण (2008), पद्म श्री (1998), खेल रत्न पुरस्कार (1997), अर्जुन पुरस्कार (1994)। वह भारत रत्न पाने वाले सबसे युवा प्राप्तकर्ता हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. The power input to the circuit when charge of Q coulomb is flowing for t seconds for a potential difference of V volt is _______.</p>",
                    question_hi: "<p>31. V वोल्ट के विभवांतर के लिए, Q कूलॉम का आवेश, t सेकंड तक प्रवाहित होने पर, परिपथ में निविष्ट शक्ति (power input) _______ होगी।</p>",
                    options_en: ["<p>P = <math display=\"inline\"><mfrac><mrow><mi>V</mi><mi>t</mi></mrow><mrow><mi>Q</mi></mrow></mfrac></math></p>", "<p>P = VQt</p>", 
                                "<p>P = <math display=\"inline\"><mfrac><mrow><mi>V</mi><mi>Q</mi></mrow><mrow><mi>t</mi></mrow></mfrac></math></p>", "<p>P = <math display=\"inline\"><mfrac><mrow><mi>Q</mi><mi>t</mi></mrow><mrow><mi>V</mi></mrow></mfrac></math></p>"],
                    options_hi: ["<p>P = <math display=\"inline\"><mfrac><mrow><mi>V</mi><mi>t</mi></mrow><mrow><mi>Q</mi></mrow></mfrac></math></p>", "<p>P = VQt</p>",
                                "<p>P = <math display=\"inline\"><mfrac><mrow><mi>V</mi><mi>Q</mi></mrow><mrow><mi>t</mi></mrow></mfrac></math></p>", "<p>P = <math display=\"inline\"><mfrac><mrow><mi>Q</mi><mi>t</mi></mrow><mrow><mi>V</mi></mrow></mfrac></math></p>"],
                    solution_en: "<p>31.(c) P = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>VQ</mi><mi mathvariant=\"normal\">t</mi></mfrac></math>.<br>Concept : Power (Watt) - The rate of doing work, it is the work done in unit time. <br>Formulas - P (watts) = V &times; I, P = i<sup>2</sup>R, <br>P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>R</mi></mrow></mfrac></math>.<br>As we know, <br>Charge (Q) = Current &times; Time.<br>Then, Current = <math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>h</mi><mi>a</mi><mi>r</mi><mi>g</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>T</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math> <br>Power (P) = Voltage (V) &times; Current (i) Power (P) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>V</mi><mi>o</mi><mi>l</mi><mi>t</mi><mi>a</mi><mi>g</mi><mi>e</mi><mo>&#215;</mo><mi>C</mi><mi>h</mi><mi>a</mi><mi>r</mi><mi>g</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>T</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math></p>",
                    solution_hi: "<p>31.(c) P = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>VQ</mi><mi mathvariant=\"normal\">t</mi></mfrac></math>.<br>अवधारणा : शक्ति (वाट) - कार्य करने की दर, इकाई समय में किया गया कार्य है।<br>सूत्र - P (वाट) = V &times; I, P = i<sup>2</sup>R, P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>R</mi></mrow></mfrac></math>.<br>जैसा की हम जानते है, <br>आवेश(Q) = धारा &times; समय.<br>तब, धारा = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2357;&#2375;&#2358;</mi><mrow><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow></mfrac></math> <br>शक्ति (P) = वोल्टेज (V) &times; धारा(i) <br>शक्ति (P) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2379;&#2354;&#2381;&#2335;&#2375;&#2332;</mi><mo>&#215;</mo><mi>&#2310;&#2357;&#2375;&#2358;</mi></mrow><mrow><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. If the perimeter of one face of a cube is 24 cm, then its volume is:</p>",
                    question_hi: "<p>32. यदि एक घन के एक फलक का परिमाप 24 सेमी है, तो उसका आयतन कितना होगा ?</p>",
                    options_en: ["<p>180 cm<sup>3</sup></p>", "<p>154 cm<sup>3</sup></p>", 
                                "<p>200 cm<sup>3</sup></p>", "<p>216 cm<sup>3</sup></p>"],
                    options_hi: ["<p>180 सेमी&sup3;</p>", "<p>154 सेमी&sup3;</p>",
                                "<p>200 सेमी&sup3;</p>", "<p>216 सेमी&sup3;</p>"],
                    solution_en: "<p>32.(d) Perimeter of one face of cube<br>= 4a = 24 cm &rArr; a = 6 cm<br>Then volume =<strong> </strong>a<sup>3 </sup>= 6<sup>3</sup> = 216 cm<sup>3</sup></p>",
                    solution_hi: "<p>32.(d) घन के एक फलक का परिमाप = 4a <br>24 सेमी = 4a &rArr;&nbsp;a = 6 सेमी<br>तब आयतन =<strong> </strong>a<sup>3 </sup>= 6<sup>3</sup> = 216 सेमी&sup3;</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Select the option that represents the letters that, when placed from left to right in the same sequence in the blanks below, will complete the letter series.<br>B _ M _ Q _ V M W _ B V M W _ B V _ _ T</p>",
                    question_hi: "<p>33. उस विकल्प का चयन कीजिए, जिसमें दिए गए अक्षरों को समान क्रम में बाएं से दाएं की ओर नीचे दी गई अक्षर शृंखला के रिक्त स्थानों में भरने पर शृंखला पूर्ण हो जाएगी।<br>B _ M _ Q _ V M W _ B V M W _ B V _ _ T</p>",
                    options_en: ["<p>VWBRSMW</p>", "<p>VWVRRMW</p>", 
                                "<p>VWVRSMR</p>", "<p>VWBVSWM</p>"],
                    options_hi: ["<p>VWBRSMW</p>", "<p>VWVRRMW</p>",
                                "<p>VWVRSMR</p>", "<p>VWBVSWM</p>"],
                    solution_en: "<p>33.(a) B<strong>V</strong>M<strong>W</strong>Q/<strong>B</strong>VMW<strong>R</strong>/BVMW<strong>S</strong>/BV<strong>MW</strong>T<br>This series makes a pair of<strong> VWBRSMW.</strong></p>",
                    solution_hi: "<p>33.(a) <br>B<strong>V</strong>M<strong>W</strong>Q/<strong>B</strong>VMW<strong>R</strong>/BVMW<strong>S</strong>/BV<strong>MW</strong>T<br>यह श्रृंखला <strong>VWBRSMW </strong>की एक जोड़ी बनाती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. Who among the following is the author of the novel &lsquo;Red Earth and Pouring Rain&rsquo; ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन \'रेड अर्थ एंड पोरिंग रेन \' उपन्यास के लेखक हैं ?</p>",
                    options_en: ["<p>Suketu Mehta</p>", "<p>Vikram Chandra</p>", 
                                "<p>Arundhati Roy</p>", "<p>Samanta Bhadra</p>"],
                    options_hi: ["<p>सुकेतु मेहता</p>", "<p>विक्रम चन्द्र</p>",
                                "<p>अरुंधति रॉय</p>", "<p>सामंत भद्रा</p>"],
                    solution_en: "<p>34.(b)<strong> Vikram Chandra. Books: Vikram Chandra - </strong>&ldquo;Love and longing in Bombay&rdquo;, &ldquo;Sacred Games&rdquo;. Arundhati Roy (Man booker prize in 1997) - &ldquo;The God of Small Things&rdquo;, &ldquo;The Ministry of Utmost Happiness&rdquo;, &ldquo;The cost of living&rdquo;, &ldquo;Capitalism: A Ghost Story&rdquo;, &ldquo;Power Politics&rdquo;, &ldquo;Walking with Comrades&rdquo;, &ldquo;My Seditious Heart&rdquo;, &ldquo;The End of Imagination&rdquo;, &ldquo;Azadi&rdquo;, &ldquo;Ek Tha Doctor Ek Tha Sant&rdquo;, &ldquo;War Talk&rdquo;, &ldquo;The Shape of the Beast&rdquo;. Suketu Mehta - &ldquo;Maximum City: Bombay Lost and Found&rdquo;, <br>&ldquo;Herzerfrischend heiter&rdquo;.</p>",
                    solution_hi: "<p>34.(b) <strong>विक्रम चंद्र। पुस्तकें: विक्रम चंद्रा -</strong> \"लव एण्ड लॉगिंग इन बाम्बे&rsquo;&rsquo;, \"सेक्रेड गेम्स\"। अरुंधति रॉय (1997 में मैन बुकर पुरस्कार) - \"द गॉड ऑफ स्मॉल थिंग्स\", \"द मिनिस्ट्री ऑफ अटमोस्ट हैप्पीनेस\", \"द कॉस्ट ऑफ लिविंग\", \"कैपिटलिज्म: ए घोस्ट स्टोरी\", \"पावर पॉलिटिक्स\", \"वॉकिंग विद कॉमरेड्स\", \"माई सेडिटियस हार्ट\" , \"द एंड ऑफ़ इमेजिनेशन\", \"आज़ादी\", \"एक था डॉक्टर एक था संत\", \"वॉर टॉक\", \"द शेप ऑफ़ द बीस्ट\"। सुकेतु मेहता - \"मैक्सिमम सिटी: बॉम्बे लॉस्ट एंड फाउंड\", \"हर्जेरफ्रिस्केंड हेइटर\"।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. If a conductor follows the Ohm&rsquo;s law, then the graph between current and voltage will be a/an _______.</p>",
                    question_hi: "<p>35. यदि एक चालक ओम के नियम का पालन करता है, तो धारा और वोल्टेज के बीच का ग्राफ _______ होगा।<strong id=\"docs-internal-guid-ea3e637e-7fff-49cb-31c3-e1c64adb5766\"><br></strong></p>",
                    options_en: ["<p>ellipse</p>", "<p>hyperbola</p>", 
                                "<p>parabola</p>", "<p>straight line</p>"],
                    options_hi: ["<p>दीर्घवृत्त</p>", "<p>हाइपरबोला</p>",
                                "<p>परबोला</p>", "<p dir=\"ltr\">सीधी रेखा</p>"],
                    solution_en: "<p>35.(d) <strong>straight line. </strong>According to Ohm\'s Law, the relationship between current (I) and voltage (V) is given by: V = IR, where R is the resistance (a constant). This equation represents a linear relationship between V and I. Therefore, if a conductor follows Ohm\'s Law, the graph of current versus voltage will be a straight line.</p>",
                    solution_hi: "<p>35.(d) <strong>सीधी रेखा। </strong>ओम के नियम के अनुसार, धारा (I) और वोल्टेज (V) के बीच संबंध इस प्रकार है: V = IR, जहां R प्रतिरोध है (जो एक स्थिर मान है)। यह समीकरण V और I के बीच एक रैखिक (linear) संबंध दर्शाता है। इसलिए, यदि कोई चालक ओम के नियम का पालन करता है, तो धारा और वोल्टेज के बीच का ग्राफ एक सीधी रेखा होगा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. If the volume of a sphere is 36&pi; cm<sup>3</sup>, then the diameter of the sphere is:</p>",
                    question_hi: "<p>36. यदि एक गोले का आयतन 36&pi; सेमी&sup3; है, तो गोले का व्यास है:</p>",
                    options_en: ["<p>3 cm</p>", "<p>9 cm</p>", 
                                "<p>27 cm</p>", "<p>6 cm</p>"],
                    options_hi: ["<p>3 सेमी</p>", "<p>9 सेमी</p>",
                                "<p>27 सेमी</p>", "<p>6 सेमी</p>"],
                    solution_en: "<p>36.(d) Volume of sphere <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi; R<sup>3</sup> (where R = radius)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi; R<sup>3</sup> = 36 &pi;<br>&rArr; R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mfrac><mrow><mn>36</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>4</mn></mfrac><mn>3</mn></mroot></math> = 3cm, <br>Hence, the diameter of the sphere&nbsp; &rArr; 2R = 2 &times; 3 = 6 cm</p>",
                    solution_hi: "<p>36.(d) <br>गोले का आयतन&nbsp; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi; R<sup>3</sup> (जहाँ R = त्रिज्या)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi; R<sup>3</sup> = 36 &pi;<br>&rArr; R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mfrac><mrow><mn>36</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>4</mn></mfrac><mn>3</mn></mroot></math> = 3 सेमी<br>अत: गोले का व्यास = 2R = 2 &times; 3 = 6 cm सेमी</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37.&nbsp;Find out missing number in the following:</p>\n<p><strong id=\"docs-internal-guid-a85bb838-7fff-69f3-75ad-097976ad7c58\"></strong>&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022365.png\" alt=\"rId10\" width=\"135\" height=\"127\"></p>",
                    question_hi: "<p>37. निम्नलिखित में लुप्त संख्या ज्ञात कीजिए?</p>\n<p><strong id=\"docs-internal-guid-b266c9d4-7fff-b2bb-2e4d-14d55d3fbfce\"></strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022365.png\" alt=\"rId10\" width=\"135\" height=\"127\"></p>",
                    options_en: ["<p>190</p>", "<p>808</p>", 
                                "<p>910</p>", "<p>901</p>"],
                    options_hi: ["<p>190</p>", "<p>808</p>",
                                "<p>910</p>", "<p>901</p>"],
                    solution_en: "<p>37.(c) <br><strong>Logic :-</strong> Consecutive number series<br>So we get, 12, 34, 56, 78, <span style=\"text-decoration: underline;\"><strong>910</strong></span>, 1112</p>",
                    solution_hi: "<p>37.(c) <strong>तर्क :-</strong> क्रमागत संख्या श्रंखला<br>तो हमें मिलता है, 12, 34, 56, 78, <span style=\"text-decoration: underline;\"><strong>910</strong></span>, 1112</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. Which of the following books is NOT written by Dr. APJ Abdul Kalam ?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन सी पुस्तक डॉ एपीजे अब्दुल कलाम द्वारा नहीं लिखी गई है ?</p>",
                    options_en: ["<p>Wings of Fire : An Autobiography</p>", "<p>Ignited Minds : Unleashing the Power Within India</p>", 
                                "<p>India 2020 : A Vision for the New Millennium</p>", "<p>The Discovery of India</p>"],
                    options_hi: ["<p>विंग्स ऑफ फायर : एन आटोबायोग्राफी</p>", "<p>इग्नाइटेड माइंड्स : अनलीशिंग द पावर विदिन इंडिया</p>",
                                "<p>इंडिया 2020 : ए विजन फॉर द न्यू मिलेनियम</p>", "<p>डिस्कवरी ऑफ इंडिया</p>"],
                    solution_en: "<p>38.(d)<strong> &ldquo;The Discovery of India&rdquo; -</strong> It was written by Jawahar Lal Nehru. List of famous books written by APJ Abdul Kalam - &ldquo;Developments in Fluid Mechanics and Space Technology&rdquo;, &ldquo;India 2020: A Vision for the New Millennium&rdquo;, &ldquo;Wings of Fire: An Autobiography&rdquo;, &ldquo;Ignited Minds: Unleashing the Power Within India&rdquo;, &ldquo;The Luminous Sparks&rdquo;, &ldquo;Mission India&rdquo;, &ldquo;Inspiring Thoughts&rdquo;, &ldquo;Indomitable Spirit&rdquo;, &ldquo;Envisioning an Empowered Nation&rdquo;, &rdquo;You Are Born To Blossom: Take My Journey Beyond&rdquo;, &ldquo;Turning Points: A journey through challenges&rdquo;, &ldquo;Target 3 Billion&rdquo;, &ldquo;India Wins Freedom&rdquo;.</p>",
                    solution_hi: "<p>38.(d) <strong>डिस्कवरी ऑफ इंडिया - </strong>यह जवाहर लाल नेहरू द्वारा लिखी गयी थी। एपीजे अब्दुल कलाम द्वारा लिखित प्रसिद्ध पुस्तकों की सूची - \"डेवेलोपमेंट्स इन फ्लूइड मकैनिक्स एण्ड स्पेस टेक्नॉलजी\", \"इंडिया 2020: ए विजन फॉर द न्यू मिलेनियम\", \"विंग्स ऑफ फायर: एन ऑटोबायोग्राफी\", \"इग्नाइटेड माइंड्स: अनलीशिंग द पावर विदिन इंडिया\", \"द ल्यूमिनस स्पार्क्स\", \"मिशन इंडिया\", \"इंस्पायरिंग थॉट्स\", \"इंडोमाइटेबल स्पिरिट\", \"एनविजनिंग एन एम्पावर्ड नेशन\", \"यू आर बॉर्न टू ब्लॉसम: टेक माई जर्नी बियॉन्ड\", \"टर्निंग पॉइंट्स: ए जर्नी थ्रू चैलेंजेस\", \"टारगेट 3 बिलियन\", \"इंडिया विंस फ्रीडम\"।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. The sum of the atomic masses of all the atoms in a molecule of the substance is-</p>",
                    question_hi: "<p>39. पदार्थ के एक अणु में मौजूद सभी परमाणुओं के परमाणु द्रव्यमानों के योग को क्या कहा जाता है ?</p>",
                    options_en: ["<p>Formula mass</p>", "<p>Atomic mass</p>", 
                                "<p>Mole</p>", "<p>Molecular mass</p>"],
                    options_hi: ["<p>सूत्र द्रव्यमान</p>", "<p>परमाणु द्रव्यमान</p>",
                                "<p>मोल</p>", "<p>आण्विक द्रव्यमान</p>"],
                    solution_en: "<p>39.(d) <strong>Molecular mass. </strong>The Atomic Mass of an element is the average mass of the atoms of an element measured in atomic mass unit (amu, also known as dalton, Da). The Formula Mass of a substance is defined as the sum of the atomic masses of constituent atoms in an ionic compound. One mole is defined as the amount of substance containing as many elementary entities (atoms, molecules, ions, electrons, radicals, etc.) as there are atoms in 12 grams of Carbon - 12 (N<sub>A</sub> = 6.023 &times; 10<sup>23</sup> = Avogadro\'s number).</p>",
                    solution_hi: "<p>39.(d) <strong>आण्विक द्रव्यमान</strong> (Molecular mass)। किसी तत्व के परमाणुओं का औसत वजन उसका परमाणु द्रव्यमान होता है, जिसे परमाणु द्रव्यमान इकाइयों (amu, जिसे डाल्टन, Da भी कहा जाता है) में मापा जाता है। किसी पदार्थ के सूत्र द्रव्यमान को आयनिक यौगिक में घटक परमाणुओं के परमाणु द्रव्यमान के योग के रूप में परिभाषित किया गया है। एक मोल को पदार्थ की उस मात्रा के रूप में परिभाषित किया जाता है जिसमें उतनी ही प्राथमिक इकाइयाँ (परमाणु, अणु, आयन, इलेक्ट्रॉन, रेडिकल, आदि) होती हैं जितनी 12 ग्राम कार्बन - 12 में परमाणु होते हैं (N<sub>A</sub> = 6.023 &times; 10<sup>23</sup> = आवोगाद्रो की संख्या)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. If each interior angle of a regular polygon is 135&deg;, then the number of sides that polygon has is :</p>",
                    question_hi: "<p>40. यदि एक नियमित बहुभुज का प्रत्येक आंतरिक कोण 135&deg; है, तो उस बहुभुज की भुजाओं की संख्या क्या है ?</p>",
                    options_en: ["<p>15</p>", "<p>10</p>", 
                                "<p>8</p>", "<p>12</p>"],
                    options_hi: ["<p>15</p>", "<p>10</p>",
                                "<p>8</p>", "<p>12</p>"],
                    solution_en: "<p>40.(c)<br>Interior angles of a regular polygon&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mi>n</mi></mfrac></math> &times; 180&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mi>n</mi></mfrac></math> &times; 180 = 135&deg;<br>180n - 360&deg; = 135n<br>180n - 135n = 360&deg;<br>45n = 360&deg; &rArr; n = 8<br>Polygon has 8 sides.</p>",
                    solution_hi: "<p>40.(c) एक नियमित बहुभुज के आंतरिक कोण = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mi>n</mi></mfrac></math> &times; 180&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mi>n</mi></mfrac></math> &times; 180 = 135&deg;<br>180n - 360&deg; = 135n<br>180n - 135n = 360&deg;<br>45n = 360&deg; &rArr; n = 8<br>बहुभुज की 8 भुजाएँ होती हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. A statement is given followed by two assumptions numbered I and II. You have to assume everything in the statement to be true and decide which of the assumptions is/are implicit in the statement.<br><strong>Statement:</strong><br>The Union Road Transport and Highways Ministry is gearing up to build a super expressway to connect Delhi and Mumbai, which would allow commuters to finish the 1,400 km long journey within 12 hours.<br><strong>Assumptions:</strong><br>I. The road between the two cities is in very bad shape.<br>II. Currently there are no roads to connect these two cities of India.</p>",
                    question_hi: "<p>41. एक कथन के आगे दो पूर्वधारणाएँ I और II दी गई हैं। आपको कथन में दी गई प्रत्येक बात को सत्य मानना है और यह निर्णय करना है कि कौन-सी पूर्वधारणा कथन में निहित है/हैं।<br><strong>कथन:</strong><br>केंद्रीय सड़क परिवहन और राजमार्ग मंत्रालय दिल्ली और मुंबई को जोड़ने के लिए एक सुपर एक्सप्रेसवे बनाने की तैयारी कर रहा है, जो यात्रियों को 12 घंटे के भीतर 1,400 किलोमीटर लंबी यात्रा पूरी करने की अनुमति देगा।<br><strong>अनुमान:</strong><br>I. दो शहरों के बीच सड़क की हालत बहुत खराब है।<br>I।. वर्तमान में भारत के इन दोनों शहरों को जोड़ने&nbsp;के लिए कोई सड़क नहीं है।</p>",
                    options_en: ["<p>Only assumption I is implicit</p>", "<p>Neither assumption I nor II is implicit</p>", 
                                "<p>Only assumption II is implicit</p>", "<p>Both assumptions I and II are implicit</p>"],
                    options_hi: ["<p>केवल धारणा I निहित है</p>", "<p>न तो धारणा I और न ही II निहित है</p>",
                                "<p>केवल धारणा II निहित है</p>", "<p>दोनों धारणाएं I और II अंतर्निहित हैं</p>"],
                    solution_en: "<p>41.(b) According to the statement, Neither assumption I nor II is implicit</p>",
                    solution_hi: "<p>41.(b) कथन के अनुसार, न तो पूर्वधारणा I और न ही II निहित है</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. Which of the following is not a salient feature of the Indian Constitution ?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन सी भारतीय संविधान की एक प्रमुख विशेषता नहीं है ?</p>",
                    options_en: ["<p>establishment of federalism</p>", "<p>fundamental right</p>", 
                                "<p>parliamentary form of government</p>", "<p>promotion of feudalism</p>"],
                    options_hi: ["<p>संघवाद की स्थापना</p>", "<p>मूल अधिकार</p>",
                                "<p>सरकार का संसदीय स्वरूप</p>", "<p>सामंतशाही को प्रोत्साहन</p>"],
                    solution_en: "<p>42.(d) <strong>Promotion of Feudalism.</strong> Salient Features of Indian Constitution - Lengthiest written constitution, Drawn from various sources, Preamble, Democratic System, Republic, Union of states, Fundamental rights and duties, Directive principles of state policy, clear division of powers, Independent Judiciary, Bicameral Legislature, Dual Government, Single Citizenship.</p>",
                    solution_hi: "<p>42.(d) <strong>सामंतशाही को प्रोत्साहन। </strong>भारतीय संविधान की मुख्य विशेषताएं - सबसे लंबा लिखित संविधान, विभिन्न स्रोतों से लिया गया, प्रस्तावना, लोकतांत्रिक प्रणाली, गणराज्य, राज्यों का संघ, मौलिक अधिकार और कर्तव्य, राज्य के नीति निर्देशक सिद्धांत, शक्तियों का स्पष्ट विभाजन, स्वतंत्र न्यायपालिका, द्विसदनीय विधानमंडल, दोहरी सरकार, एकल नागरिकता।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. In an alloy, if one of the metals is mercury, the alloy is known as:</p>",
                    question_hi: "<p>43. किसी मिश्रधातु में, यदि धातुओं में से एक पारा है, तो मिश्रधातु को ______के रूप में जाना जाता है:</p>",
                    options_en: ["<p>Amalgam</p>", "<p>Bronze</p>", 
                                "<p>Brass</p>", "<p>Solder</p>"],
                    options_hi: ["<p>अमलगम</p>", "<p>कांस्य</p>",
                                "<p>पीतल</p>", "<p>टाँका</p>"],
                    solution_en: "<p>43.(a) <strong>Amalgam. </strong>It is an alloy of mercury with one or more metals. Example - Sodium along with liquid mercury is called sodium amalgam. An alloy is a homogeneous mixture of two or more metals, or a metal and a nonmetal. Bronze - An alloy of copper and tin. Brass - An alloy of copper and zinc. Solder - An alloy of lead and tin.</p>",
                    solution_hi: "<p>43.(a) <strong>अमलगम। </strong>यह एक या अधिक धातुओं के साथ पारे का मिश्र धातु है। उदाहरण - तरल पारे के साथ सोडियम को सोडियम अमलगम कहा जाता है। मिश्र धातु दो या दो से अधिक धातुओं, या एक धातु और एक अधातु का एक सजातीय मिश्रण है। कांस्य - तांबा और टिन का एक मिश्र धातु। पीतल - तांबा और जस्ता का एक मिश्र धातु। सोल्डर - सीसा और टिन का एक मिश्र धातु।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. Find the number of sides in a regular polygon if its interior angle is 160&deg;.</p>",
                    question_hi: "<p>44. एक नियमित बहुभुज में भुजाओं की संख्या ज्ञात कीजिए यदि इसका आंतरिक कोण 160&deg; है ।</p>",
                    options_en: ["<p>17</p>", "<p>14</p>", 
                                "<p>15</p>", "<p>18</p>"],
                    options_hi: ["<p>17</p>", "<p>14</p>",
                                "<p>15</p>", "<p>18</p>"],
                    solution_en: "<p>44.(d)<br>Each interior angle of a regular polygon <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>&#215;</mo><mn>180</mn></mrow><mi>n</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>&#215;</mo><mn>180</mn></mrow><mi>n</mi></mfrac></math> =160<br>&rArr; 180n - 360 = 160n<br>&rArr; 180n - 160n = 360<br>&rArr; 20n = 360 &rArr; n = 18<br>Number of sides in the polygon = 18</p>",
                    solution_hi: "<p>44.(d) एक समबहुभुज के प्रत्येक आंतरिक कोण&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>&#215;</mo><mn>180</mn></mrow><mi>n</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>&#215;</mo><mn>180</mn></mrow><mi>n</mi></mfrac></math> = 160<br>&rArr; 180n - 360 = 160n<br>&rArr; 180n - 160n = 360<br>&rArr; 20n = 360 &rArr; n = 18<br>बहुभुज में भुजाओं की संख्या = 18</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. In this question, a statement is followed by two conclusions numbered I and II. You have to assume everything in the statement to be true and decide which of them logically follows beyond a reasonable doubt from the information given in the statement.<br><strong>Statement:</strong><br>Providing LPG connections to BPL (Below Poverty Level) households will ensure universal coverage of cooking gas in country X.<br><strong>Conclusions:</strong><br>I. Usage of traditional cooking fuels such as firewood, coal, etc., is a common practice in rural parts of country X.<br>II. No other mode of cooking is as effective as using LPG.</p>",
                    question_hi: "<p>45. इस प्रश्न में, एक कथन और उसके बाद दो निष्कर्ष I और ॥ दिए गए हैं। कथन में दी गई जानकारी को सत्य मानते \'हुए विचार करें, और बताएं कि दिए गए निष्कर्षों में से कौन से निसंदेह कथन में दी गई जानकारी का तार्किक रूप से पालन करते हैं ?<br><strong>कथन:</strong><br>बीपीएल (निर्धनता स्तर से नीचे) परिवारों को एलपीजी कनेक्शन प्रदान करने से देश X में रसोई गैस का सार्वभौमिक कवरेज सुनिश्चित होगा।<br><strong>निष्कर्ष:</strong><br>I. भोजन पकाने के पारंपरिक ईंधनों, जैसे जलाऊ लकड़ी, कोयला, आदि का उपयोग, देश X के . ग्रामीण हिस्सों में एक सामान्य बात है।<br>॥. खाना पकाने का कोई भी अन्य तरीका, एलपीजी के उपयोग जितना प्रभावी नहीं है।</p>",
                    options_en: ["<p>Neither conclusion I nor II follows</p>", "<p>Both conclusions I and II follow</p>", 
                                "<p>Only conclusion I follows</p>", "<p>Only conclusion II follows</p>"],
                    options_hi: ["<p>न तो निष्कर्ष I और न ही II अनुसरण करता है</p>", "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष I अनुसरण करता है</p>", "<p>केवल निष्कर्ष II अनुसरण करता है</p>"],
                    solution_en: "<p>45.(a) According to the given statement, because conclusion I and II are not related to the statement. Hence, neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>45.(a) दिए गए कथन के अनुसार, क्योंकि निष्कर्ष I और II कथन से संबंधित नहीं हैं, इसलिए, न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. Which of the following Articles discusses the veto power of the President of India ?</p>",
                    question_hi: "<p>46. निम्न में से किस अनुच्छेद में भारतीय राष्ट्रपति के निषेधाधिकार का वर्णन है ?</p>",
                    options_en: ["<p>Article 145</p>", "<p>Article 114</p>", 
                                "<p>Article 111</p>", "<p>Article 122</p>"],
                    options_hi: ["<p>अनुच्छेद 145</p>", "<p>अनुच्छेद 114</p>",
                                "<p>अनुच्छेद 111</p>", "<p>अनुच्छेद 122</p>"],
                    solution_en: "<p>46.(c) <strong>Article 111.</strong> Article 145 - Rules of Court, Article 114 - Appropriation Bills, Article 122 - Courts not to inquire into proceedings of Parliament. Veto Power of President - President of India can either reject the bill, return the bill or withhold his/her assent to the bill.</p>",
                    solution_hi: "<p>46.(c) <strong>अनुच्छेद 111 । </strong>अनुच्छेद 145 - न्यायालय के नियम, अनुच्छेद 114 - विनियोग विधेयक, अनुच्छेद 122 - न्यायालय संसद की कार्यवाही की जांच नहीं करेंगे। राष्ट्रपति की वीटो शक्ति - भारत के राष्ट्रपति या तो विधेयक को अस्वीकार कर सकते हैं, विधेयक को वापस कर सकते हैं या विधेयक पर अपनी सहमति रोक सकते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. Which of the following reactions does NOT represent a neutralisation reaction ?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन सा प्रतिक्रिया न्यूट्रलाइजेशन प्रतिक्रिया (उदासीनीकरण प्रतिक्रिया) नहीं दर्शाती है ?</p>",
                    options_en: ["<p>Na<sub>2</sub>CO<sub>3</sub> + HCl &rarr; NaCl + CO<sub>2</sub> + H<sub>2</sub>O</p>", "<p>CH₃COOH + NaOH &rarr; CH₃COONa + H₂O</p>", 
                                "<p>H<sub>2</sub>SO<sub>4 </sub>+ Ca(OH)<sub>2</sub> &rarr; CaSO<sub>4</sub> + 2H<sub>2</sub>O</p>", "<p>HCl + NaOH &rarr; NaCl + H<sub>2</sub>O</p>"],
                    options_hi: ["<p>Na<sub>2</sub>CO<sub>3</sub> + HCl &rarr; NaCl + CO<sub>2</sub> + H<sub>2</sub>O</p>", "<p>CH₃COOH + NaOH &rarr; CH₃COONa + H₂O</p>",
                                "<p>H<sub>2</sub>SO<sub>4 </sub>+ Ca(OH)<sub>2</sub> &rarr; CaSO<sub>4</sub> + 2H<sub>2</sub>O</p>", "<p>HCl + NaOH &rarr; NaCl + H₂O</p>"],
                    solution_en: "<p>47.(a) <strong>Na<sub>2</sub>CO<sub>3</sub> + HCl &rarr; NaCl + CO<sub>2</sub> + H<sub>2</sub>O.</strong> In a neutralization reaction, an acid reacts with a base to form salt and water. The general form is: Acid + Base &rarr; Salt + Water. The first reaction involves the production of a gas (CO<sub>2</sub>), indicating it is an acid-carbonate reaction, while the other three reactions are true neutralization reactions.</p>",
                    solution_hi: "<p>47.(a) <strong>Na<sub>2</sub>CO<sub>3</sub> + HCl &rarr; NaCl + CO<sub>2</sub> + H<sub>2</sub>O.</strong>&nbsp; न्यूट्रलाइजेशन अभिक्रिया में, एक अम्ल (acid) और एक क्षार (base) मिलकर नमक (salt) और पानी (water) उत्पन्न करते हैं। सामान्य रूप में: अम्ल + क्षार &rarr; नमक + पानी। पहली प्रतिक्रिया में गैस (CO₂) का उत्पादन हो रहा है, जो यह संकेत करता है कि यह एक अम्ल-कार्बोनेट प्रतिक्रिया है, जबकि बाकी तीन प्रतिक्रियाएँ सच्ची न्यूट्रलाइजेशन प्रतिक्रियाएँ हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. If x + y + z = 11 and xy + yz + zx = 42 , then the value of x&sup2; + y&sup2; + z&sup2; is:</p>",
                    question_hi: "<p>48. यदि x + y + z = 11 और xy + yz + zx = 42, तो x&sup2; + y&sup2; + z&sup2; का मान है:</p>",
                    options_en: ["<p>37</p>", "<p>43</p>", 
                                "<p>41</p>", "<p>39</p>"],
                    options_hi: ["<p>37</p>", "<p>43</p>",
                                "<p>41</p>", "<p>39</p>"],
                    solution_en: "<p>48.(a) (x + y + z)<sup>2</sup><br>= x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2(xy + yz + zx)<br>11<sup>2</sup> = x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2 &times; 42&nbsp;<br>121 - 84 = x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup><br>37 = x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup></p>",
                    solution_hi: "<p>48.(a) (x + y + z)<sup>2</sup><br>= x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2(xy + yz + zx)<br>11<sup>2</sup> = x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2 &times; 42&nbsp;<br>121 - 84 = x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup><br>37 = x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. This question consists of a statement followed by two arguments I and II. Read the statement and the arguments carefully and select the appropriate answer from the given options.<br><strong>Statement:</strong><br>Eating frozen foods is advantageous.<br><strong>Arguments:</strong><br>I. Frozen foods sometimes may have more vitamins and minerals compared to fresh food, because fresh foods lose vitamins and minerals over time while freezing preserves nutrients.<br>II. Sodium present in frozen foods can lead to increased blood pressure level.</p>",
                    question_hi: "<p>49. इस प्रश्न में एक कथन और उसके बाद दो तर्क I और II शामिल हैं। कथन और तर्कों को ध्यान से पढ़ें और दिए गए विकल्पों में से उपयुक्त उत्तर का चयन करें।<br><strong>कथन:</strong><br>फ्रोजन फूड्स खाना फायदेमंद होता है।<br><strong>तर्क:</strong><br>I. जमे हुए खाद्य पदार्थों में कभी-कभी ताजे खाद्य पदार्थों की तुलना में अधिक विटामिन और खनिज हो सकते हैं, क्योंकि ताजे खाद्य-पदार्थों में मौजूद . विटामिन और खनिज समय के साथ नष्ट हो सकते हैं, जबकि जमाए जाने से पोषक तत्व संरक्षित होते हैं।<br>II. जमे हुए खाद्य पदार्थों में मौजूद सोडियम रक्तचाप के स्तर को बढ़ाने का कारण हो सकता है।</p>",
                    options_en: ["<p>II weakens while I strengthens the statement</p>", "<p>Both I and II strengthen the statement</p>", 
                                "<p>I weakens while II strengthens the statement</p>", "<p>Both I and II weaken the statement</p>"],
                    options_hi: ["<p>II कथन का समर्थन नहीं करता है , जबकि I कथन का समर्थन करता है</p>", "<p>I और II दोनों कथन का समर्थन करते है</p>",
                                "<p>I कथन का समर्थन नहीं करता है , जबकि II कथन का समर्थन करता है</p>", "<p>I और II दोनों कथन का समर्थन नहीं करते है</p>"],
                    solution_en: "<p>49.(a)<strong> Arguments 1 :- </strong>It says fresh foods lose vitamins and minerals over time while freezing preserves nutrients. <br>So it can be possible <br><strong>Argument 2 :-</strong> it says Frozen foods can lead to increase blood pressure level so it can not be possible.<br>Hence II Argument weakens while I strengthens the statements.</p>",
                    solution_hi: "<p>49.(a)<strong> तर्क 1 :-</strong> यह कहता है कि ताजे खाद्य-पदार्थों में मौजूद विटामिन और खनिज समय के साथ नष्ट हो सकते हैं, जबकि जमाए जाने से पोषक तत्व संरक्षित होते हैं तो यह संभव हो सकता है |<br><strong>तर्क 2 :- </strong>यह कहता है कि जमे हुए खाद्य पदार्थ रक्तचाप के स्तर को बढ़ा सकते हैं इसलिए यह संभव नहीं हो सकता है।<br>इसलिए II तर्क कमजोर होता है जबकि तर्क I कथन का समर्थन करता है |</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. Yajurveda is ______ fold.</p>",
                    question_hi: "<p>50. यजुर्वेद की कितनी शाखाएं है ;</p>",
                    options_en: ["<p>5</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>5</p>", "<p>2</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>50.(b) <strong>The Yajurveda</strong> is broadly grouped into <strong>two</strong> &ndash; The black or dark (Krishna) Yajurveda and the white or bright (Shukla) Yajurveda. The Yajurveda is known as &lsquo;Veda of Sacrificial Formulas&rsquo; and contains prose formulas applicable to various rites, along with verses intended for a similar purpose. Types of Vedas &ndash; Rigveda (Hymns of Praise) - The oldest Veda, written by Ved Vyasa (Mantras-10552, Mandalas-10 and Suktas - 1028), Samaveda (Music), Yajurveda (Sacrifices), and Atharva Veda (Magic) newest Veda.</p>",
                    solution_hi: "<p>50.(b) <strong>यजुर्वेद</strong> को विशेष तौर पर <strong>दो</strong> भागों में बांटा गया है - काला या गहरा (कृष्ण) यजुर्वेद और सफेद या चमकीला (शुक्ल) यजुर्वेद। यजुर्वेद को \'यज्ञ सूत्रों के वेद\' के रूप में जाना जाता है, जिसमें समान उद्देश्य के लिए इच्छित छंदों के साथ-साथ विभिन्न संस्कारों के लिए अपनाए जाने वाले गद्य सूत्र भी शामिल हैं। वेदों के प्रकार &ndash; ऋग्वेद (स्तुति के भजन) &ndash; सबसे प्राचीन वेद, वेद व्यास द्वारा लिखित ऋग्वेद (मंत्र - 10552, मंडल - 10 और सूक्त - 1028), सामवेद (संगीत), यजुर्वेद (यज्ञ), और अथर्ववेद (जादू) - नवीनतम वेद ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. What is the process by which sodium hydroxide is prepared?</p>",
                    question_hi: "<p>51. सोडियम हाइड्रॉक्साइड (NaOH) तैयार करने की प्रक्रिया क्या है?<strong id=\"docs-internal-guid-cd0f8880-7fff-f59a-fc74-6fc4c5207a93\"><br></strong></p>",
                    options_en: ["<p>Solvay&rsquo;s process</p>", "<p>Chlor-alkali process</p>", 
                                "<p>Contact process</p>", "<p>Haber&rsquo;s process</p>"],
                    options_hi: ["<p>सॉल्वे प्रक्रिया</p>", "<p>क्लोर-क्षार प्रक्रिया</p>",
                                "<p>संपर्क प्रक्रिया</p>", "<p>हैबर प्रक्रिया</p>"],
                    solution_en: "<p>51.(b) <strong>Chlor-alkali process.</strong> In the chlor-alkali process, sodium chloride (NaCl) is electrolyzed to produce sodium hydroxide (NaOH), chlorine gas (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Cl</mi><mn>2</mn></msub></math>), and hydrogen gas (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math>). Solvay&rsquo;s process: This process is used to manufacture sodium carbonate (Na₂CO₃) from sodium chloride, limestone, and ammonia. It does not produce sodium hydroxide. Contact process: This process is used to produce sulfuric acid (H₂SO₄) from sulfur dioxide (SO₂). It involves the oxidation of sulfur dioxide to sulfur trioxide, followed by its reaction with water. Sodium hydroxide is not produced in this process. Haber&rsquo;s process: This process is used to synthesize ammonia (NH₃) from nitrogen (N₂) and hydrogen (H₂) gases. Sodium hydroxide is not involved in this process either.</p>",
                    solution_hi: "<p>51. (b) <strong>क्लोर-क्षार प्रक्रिया में,</strong> सोडियम क्लोराइड (NaCl) को इलेक्ट्रोलाइज़ किया जाता है, जिससे सोडियम हाइड्रॉक्साइड (NaOH), क्लोरीन गैस (Cl₂) और हाइड्रोजन गैस (H₂) उत्पन्न होते हैं। सॉल्वे प्रक्रिया: यह प्रक्रिया सोडियम कार्बोनेट (Na₂CO₃) को सोडियम क्लोराइड, चूना पत्थर और अमोनिया से बनाने के लिए उपयोग की जाती है। इसमें सोडियम हाइड्रॉक्साइड नहीं बनता है। संपर्क प्रक्रिया: यह प्रक्रिया सल्फ्यूरिक एसिड (H₂SO₄) को सल्फर डाइऑक्साइड (SO₂) से बनाने के लिए उपयोग की जाती है। इसमें सल्फर डाइऑक्साइड का ऑक्सीकरण करके सल्फर ट्राईऑक्साइड तैयार किया जाता है, फिर उसे पानी से मिलाकर सल्फ्यूरिक एसिड बनता है। इसमें सोडियम हाइड्रॉक्साइड का उत्पादन नहीं होता है। हैबर प्रक्रिया: यह प्रक्रिया नाइट्रोजन (N₂) और हाइड्रोजन (H₂) गैसों से अमोनिया (NH₃) बनाने के लिए उपयोग की जाती है। इसमें सोडियम हाइड्रॉक्साइड शामिल नहीं है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. The expansion of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">a</mi><mo>-</mo><mn>4</mn><mi mathvariant=\"normal\">b</mi><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mn>2</mn></msup></math> is:</p>",
                    question_hi: "<p>52. व्यंजक <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">a</mi><mo>-</mo><mn>4</mn><mi mathvariant=\"normal\">b</mi><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mn>2</mn></msup></math> विस्तारित रूप क्या होगा ?</p>",
                    options_en: ["<p>9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math>+ 16b&sup2; + 4c&sup2; - 24ab + 8bc + 12ac</p>", "<p>9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> + 16b&sup2; + 4c&sup2; - 24ab + 16bc - 6ac</p>", 
                                "<p>9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math>+ 16b&sup2;+ 4c&sup2; + 12ab - 8bc + 6ac</p>", "<p>9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> + 16b&sup2; + 4c&sup2; - 24ab + 16bc - 12ac</p>"],
                    options_hi: ["<p>9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math>+ 16b&sup2; + 4c&sup2; - 24ab + 8bc + 12ac</p>", "<p>9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> + 16b&sup2; + 4c&sup2; - 24ab + 16bc - 6ac</p>",
                                "<p>9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math>+ 16b&sup2; + 4c&sup2; + 12ab - 8bc + 6ac</p>", "<p>9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> + 16b&sup2; + 4c&sup2; - 24ab + 16bc - 12ac</p>"],
                    solution_en: "<p>52.(d) (3a - 4b - 2c)<sup>2</sup><br><strong id=\"docs-internal-guid-23e3f7be-7fff-5013-c357-31dcccca8b84\"></strong>= (3a)<sup>2 </sup>+ (-4b)<sup>2 </sup>+ (-2c)<sup>2</sup> + 2(3a)(-4b) + 2(-4b)(-2c) + 2(-2c)(3a)<br>(3a - 4b - 2c)<sup>2</sup> = 9a<sup>2</sup> + 16b<sup>2 </sup>+ 4c<sup>2</sup> - 24ab + 16bc - 12ac</p>",
                    solution_hi: "<p>52.(d) (3a - 4b - 2c)<sup>2</sup><br><strong id=\"docs-internal-guid-23e3f7be-7fff-5013-c357-31dcccca8b84\"></strong>= (3a)<sup>2 </sup>+ (-4b)<sup>2 </sup>+ (-2c)<sup>2</sup> + 2(3a)(-4b) + 2(-4b)(-2c) + 2(-2c)(3a)<br>(3a - 4b - 2c)<sup>2</sup> = 9a<sup>2</sup> + 16b<sup>2 </sup>+ 4c<sup>2</sup> - 24ab + 16bc - 12ac</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. Choose the figure that best represents the relationship among the classes given below.<br>Mother, Children, Human, Father</p>",
                    question_hi: "<p>53. उस आकृति का चयन करें, जो निम्नलिखित श्रेणियों के बीच के संबंध को सर्वोत्तम ढंग से निरूपित करती हो। <br>माता, बच्चे, मानव, पिता</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022554.png\" alt=\"rId11\" width=\"206\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022650.png\" alt=\"rId12\" width=\"185\" height=\"84\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022781.png\" alt=\"rId13\" width=\"135\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022883.png\" alt=\"rId14\" width=\"98\" height=\"98\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022554.png\" alt=\"rId11\" width=\"206\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022650.png\" alt=\"rId12\" width=\"185\" height=\"84\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022781.png\" alt=\"rId13\" width=\"135\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022883.png\" alt=\"rId14\" width=\"98\" height=\"98\"></p>"],
                    solution_en: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623022991.png\" alt=\"rId15\" width=\"218\" height=\"84\"></p>",
                    solution_hi: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023091.png\" alt=\"rId16\" width=\"226\" height=\"94\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. In Jainism, the word \'Jain\' is derived from the Sanskrit word \'Jina\', which means_________,implying one who has transcended all human passions.</p>",
                    question_hi: "<p>54. जैन धर्म में, \'जैन\' शब्द संस्कृत शब्द \'जिन्\' से लिया गया है, जिसका अर्थ है _____,अर्थात् जिसने सभी मानवीय भावावेशों पर विजय प्राप्त कर ली हो ।</p>",
                    options_en: ["<p>Conqueror</p>", "<p>Immortal</p>", 
                                "<p>Vigour</p>", "<p>Candour</p>"],
                    options_hi: ["<p>विजेता</p>", "<p>अमर</p>",
                                "<p>फुर्ती</p>", "<p>खरापन</p>"],
                    solution_en: "<p>54.(a) <strong>Conqueror. Jainism</strong> was founded by Aadinath/Rishabh Dev but the real founder is Mahavir swami. <strong>5 Mahavarta of Jainism</strong> - Ahimsa (Non-injury to living being), Satya (Do not speak a lie), Asteya (Do not steal), Aparigraha (Do not acquire property), and Brahmacharya (Observe continence). <strong>1st Jain council</strong> - In Patliputra (3rd Century B.C. presided by Sthulabhadra). <strong>2nd Jain Council</strong> - At Vallabhi (512 A.D. presided by Devardhi). <strong>Jain literature</strong> - Acharanga Sutra, Kalpa-Sutra, Bhagwati Sutra.</p>",
                    solution_hi: "<p>54.(a) <strong>विजेता। जैन</strong> धर्म की स्थापना आदिनाथ/ऋषभ देव ने की थी लेकिन वास्तविक संस्थापक महावीर स्वामी थे । <strong>जैन धर्म के 5 महावर्त </strong>- अहिंसा (जीवित प्राणी को चोट न पहुँचाना), सत्य (झूठ न बोलना), अस्तेय (चोरी न करना), अपरिग्रह (संपत्ति अर्जित न करना), और ब्रह्मचर्य (संयम का पालन करना)। पहली जैन परिषद - पाटलिपुत्र में (तीसरी शताब्दी ईसा पूर्व स्थूलभद्र की अध्यक्षता में)। द्वितीय जैन परिषद - वल्लभी में (512 ई. में देवार्धि की अध्यक्षता में)। जैन साहित्य - आचारांग सूत्र, कल्प-सूत्र, भगवती सूत्र।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. Which of the following options represents metals that do NOT react with cold water?</p>",
                    question_hi: "<p>55. निम्नलिखित में से कौन-सा विकल्प उन धातुओं को दर्शाता है जो ठंडे जल के साथ अभिक्रिया नहीं करती है?</p>",
                    options_en: ["<p>Sodium, calcium and zinc</p>", "<p>Sodium, iron and potassium</p>", 
                                "<p>Sodium, calcium and potassium</p>", "<p>Aluminium, iron and zinc</p>"],
                    options_hi: ["<p>सोडियम, कैल्शियम और जिंक</p>", "<p>सोडियम, आयरन और पोटैशियम</p>",
                                "<p>सोडियम, कैल्शियम और पोटैशियम</p>", "<p>ऐल्युमिनियम, आयरन और जिंक</p>"],
                    solution_en: "<p>55.(d) <strong>Aluminium, iron and zinc.</strong> Sodium, calcium, and potassium react vigorously with cold water, producing hydrogen gas and forming alkaline hydroxides. Iron and zinc do not react with cold water, but they can react with steam at higher temperatures. Aluminium does not react with cold water due to the formation of a protective oxide layer, but it can react with hot water under certain conditions.</p>",
                    solution_hi: "<p>55. (d) <strong>ऐल्युमिनियम, आयरन और जिंक। </strong>सोडियम, कैल्शियम और पोटेशियम ठंडे पानी के साथ तीव्र प्रतिक्रिया करते हैं, हाइड्रोजन गैस उत्पन्न करते हैं और क्षारीय हाइड्रॉक्साइड बनाते हैं। लोहे और जिंक की ठंडे पानी के साथ कोई प्रतिक्रिया नहीं होती, लेकिन वे उच्च तापमान पर भाप के साथ प्रतिक्रिया कर सकते हैं। एल्यूमीनियम ठंडे पानी के साथ प्रतिक्रिया नहीं करता है क्योंकि उस पर एक सुरक्षात्मक ऑक्साइड परत बन जाती है, लेकिन यह कुछ परिस्थितियों में गरम पानी के साथ प्रतिक्रिया कर सकता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56 The sum of the roots of the quadratic equation 4x&sup2; + 7x - 21 = 0 is:</p>",
                    question_hi: "<p>56. द्विघात समीकरण 4x&sup2; + 7x - 21 = 0 के मूलों का योग है:</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>21</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math></p>", "<p>-21</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>21</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math></p>", "<p>-21</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> </p>"],
                    solution_en: "<p>56.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>21</mn><mo>=</mo><mn>0</mn></math><br>Sum of the roots = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mi mathvariant=\"normal\">b</mi></mrow><mi mathvariant=\"normal\">a</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>56.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>21</mn><mo>=</mo><mn>0</mn></math><br>मूलों का योग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mi mathvariant=\"normal\">b</mi></mrow><mi mathvariant=\"normal\">a</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>4</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57. P, Q, R and S are facing north and A, B, C and D are facing them, not necessarily in the same order. S is sitting second to the right of R. The person who faces Q is not a neighbour of B, D is facing S and sitting next to B. A is sitting on an extreme position. P and C are not facing each other. R is at one end but C is not. Who is facing the person second to the right of Q?</p>",
                    question_hi: "<p>57. P, Q, R और S उत्तर की ओर उन्मुख हैं और A, B, C और D उनका सामना कर रहे हैं, जरूरी नहीं कि इसी क्रम में हों। S, R के दायें से दूसरे स्थान पर बैठा है। वह व्यक्ति जो Q की ओर उन्मुख है, B का पड़ोसी नहीं है, D का मुख S की ओर है और वह B के बगल में बैठा है। A अंतिम स्थिति में बैठा है। P और C एक दूसरे की ओर उन्मुख नहीं हैं। R एक छोर पर है लेकिन C नहीं है। Q के दायें से दूसरे स्थान पर बैठे व्यक्ति की ओर कौन उन्मुख है?</p>",
                    options_en: ["<p>C</p>", "<p>B</p>", 
                                "<p>A</p>", "<p>D</p>"],
                    options_hi: ["<p>C</p>", "<p>B</p>",
                                "<p>A</p>", "<p>D</p>"],
                    solution_en: "<p>57.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023238.png\" alt=\"rId17\" width=\"78\" height=\"89\"><br>B is facing the person second to the right of Q</p>",
                    solution_hi: "<p>57.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023238.png\" alt=\"rId17\" width=\"78\" height=\"89\"><br>B, Q के दायें से दूसरे स्थान पर बैठे व्यक्ति की ओर उन्मुख है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. Which of the following is NOT a terrestrial planet?</p>",
                    question_hi: "<p>58. निम्नलिखित में से कौन सा स्थलीय ग्रह नहीं है?</p>",
                    options_en: ["<p>Mars</p>", "<p>Uranus</p>", 
                                "<p>Venus</p>", "<p>Earth</p>"],
                    options_hi: ["<p>मंगल ग्रह</p>", "<p>अरुण</p>",
                                "<p>शुक्र</p>", "<p>पृथ्वी</p>"],
                    solution_en: "<p>58.(b) <strong>Uranus.</strong> It is the seventh planet from the Sun. It is an ice planet. Terrestrial planet: The planets which have a compact, rocky surface like Earth\'s terra firma. Example: Mercury, Venus, Earth, and Mars. Jovian Planet - These are planets in our solar system&rsquo;s outer parts and are also known as gas giants. Example: Neptune, Uranus, Saturn, and Jupiter.</p>",
                    solution_hi: "<p>58.(b) <strong>अरुण ग्रह।</strong> यह सूर्य से सातवाँ ग्रह है। यह एक बर्फीला ग्रह है। पार्थिव या आन्तरिक ग्रह: वे ग्रह जिनकी सतह पृथ्वी की टेरा फ़िरमा जैसी सघन, चट्टानी है। उदाहरण: बुध, शुक्र, पृथ्वी और मंगल। बाह्य ग्रह - ये हमारे सौर मंडल के बाहरी हिस्सों में स्थित ग्रह हैं और इन्हें गैस दानव के रूप में भी जाना जाता है। उदाहरण: नेपच्यून, यूरेनस, शनि और बृहस्पति।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. If the electronic configuration of elements A\' and B\' are <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mo>,</mo><mn>2</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mn>2</mn><msup><mi mathvariant=\"normal\">p</mi><mn>6</mn></msup><mo>,</mo><mn>3</mn><msup><mi mathvariant=\"normal\">s</mi><mn>1</mn></msup></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mo>,</mo><mn>2</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mn>2</mn><msup><mi mathvariant=\"normal\">p</mi><mn>6</mn></msup><mo>,</mo><mn>3</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mn>3</mn><msup><mi mathvariant=\"normal\">p</mi><mn>4</mn></msup></math> respectively, then the formula of the compound formed by the combination of these elements will be:</p>",
                    question_hi: "<p>59. यदि तत्व A&rsquo; और B&rsquo; का इलेक्ट्रोनिक विन्यास <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mo>,</mo><mn>2</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mn>2</mn><msup><mi mathvariant=\"normal\">p</mi><mn>6</mn></msup><mo>,</mo><mn>3</mn><msup><mi mathvariant=\"normal\">s</mi><mn>1</mn></msup></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mo>,</mo><mn>2</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mn>2</mn><msup><mi mathvariant=\"normal\">p</mi><mn>6</mn></msup><mo>,</mo><mn>3</mn><msup><mi mathvariant=\"normal\">s</mi><mn>2</mn></msup><mn>3</mn><msup><mi mathvariant=\"normal\">p</mi><mn>4</mn></msup></math> है, तो इन तत्वों के संयोजन द्वारा निर्मित यौगिक का सूत्र क्या होगा?</p>",
                    options_en: ["<p>AB3</p>", "<p>A2B</p>", 
                                "<p>AB2</p>", "<p>AB</p>"],
                    options_hi: ["<p>AB3</p>", "<p>A2B</p>",
                                "<p>AB2</p>", "<p>AB</p>"],
                    solution_en: "<p>59.(b) <strong>A2B. </strong>Electronic configuration - It illustrates how electrons can be distributed in atomic orbitals. Configuration of electrons involves three terms - Energy level, Orbital type, No. of electrons present in the orbital. 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">s</mi><mn>1</mn></msup></math> - 1 in front of the s denotes the energy level. s indicates the type of orbital. Power 1 represents the no. of electrons in the orbital.</p>",
                    solution_hi: "<p>59.(b) <strong>A2B ।</strong> इलेक्ट्रॉनिक विन्यास - यह दर्शाता है कि परमाणु कक्षकों में इलेक्ट्रॉनों को कैसे वितरित किया जा सकता है। इलेक्ट्रॉनों के विन्यास में तीन पद शामिल होते हैं - ऊर्जा स्तर, कक्षीय प्रकार, कक्ष में मौजूद इलेक्ट्रॉनों की संख्या।। 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">s</mi><mn>1</mn></msup></math> - 1, s के सामने ऊर्जा स्तर को दर्शाता है। s, कक्षीय के प्रकार को दर्शाता है। पावर 1 कक्षको में इलेक्ट्रॉनों की संख्या का प्रतिनिधित्व करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. The third proportional of (a + b) and (a + b)&sup2; is</p>",
                    question_hi: "<p>60. (a + b) और (a + b)&sup2; का तृतीयानुपाती ज्ञात कीजिए</p>",
                    options_en: ["<p>(a + b)&sup3;</p>", "<p>(a + b)</p>", 
                                "<p>a</p>", "<p>b</p>"],
                    options_hi: ["<p>(a + b)&sup3;</p>", "<p>(a + b)</p>",
                                "<p>a</p>", "<p>b</p>"],
                    solution_en: "<p>60.(a) Third proportional = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">b</mi><mo>&#178;</mo></mrow><mi mathvariant=\"normal\">a</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>)</mo></mrow><mn>4</mn></msup><mrow><mo>(</mo><mi>a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>)</mo></mrow></mfrac></math> = (a + b)&sup3;</p>",
                    solution_hi: "<p>60.(a) तृतीयानुपाती = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">b</mi><mo>&#178;</mo></mrow><mi mathvariant=\"normal\">a</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>)</mo></mrow><mn>4</mn></msup><mrow><mo>(</mo><mi>a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>)</mo></mrow></mfrac></math> = (a + b)&sup3;</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Eight persons A to H, not necessarily in the same order, are sitting at a round table facing the centre. There are two persons between F and D. H is not an immediate neighbour of A or F. A is third to the right of F. There are three persons between H and G. C sits second to the left of E. Who is third to the right of D?</p>",
                    question_hi: "<p>61. आठ व्यक्ति A से H, आवश्यक नहीं इसी क्रम में हों, एक गोल मेज पर केंद्र की ओर मुख करके बैठे हैं। F और D के बीच दो व्यक्ति हैं। H, A या F का निकटतम पड़ोसी नहीं है। A, F के दायें से तीसरे स्थान पर है। H और G के बीच तीन व्यक्ति हैं। C, E के बाएं से दूसरे स्थान पर बैठा है। D के दाईं ओर तीसरे स्थान पर कौन है?</p>",
                    options_en: ["<p>F</p>", "<p>A</p>", 
                                "<p>H</p>", "<p>E</p>"],
                    options_hi: ["<p>F</p>", "<p>A</p>",
                                "<p>H</p>", "<p>E</p>"],
                    solution_en: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023362.png\" alt=\"rId18\" width=\"138\" height=\"133\"><br>F is third to the right of D</p>",
                    solution_hi: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023362.png\" alt=\"rId18\" width=\"138\" height=\"133\"><br>F, D के दायें से तीसरे स्थान पर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. The Bay of Bengal\'s islands of Andaman in the north and the Nicobar in the south are separated by a waterbody&nbsp;which is called the _________ channel.</p>",
                    question_hi: "<p>62. बंगाल की खाड़ी के उत्तर में अंडमान द्वीप समूह, और दक्षिण में निकोबार द्वीप समूह, एक जल निकाय द्वारा पृथक किए गए हैं, जिसे _________ चैनल कहा जाता है।</p>",
                    options_en: ["<p>twenty degree</p>", "<p>forty degree</p>", 
                                "<p>ten degree</p>", "<p>thirty degree</p>"],
                    options_hi: ["<p>बीस डिग्री</p>", "<p>चालीस डिग्री</p>",
                                "<p>दस डिग्री</p>", "<p>तीस डिग्री</p>"],
                    solution_en: "<p>62.(c) <strong>Ten degrees.</strong> Nine Degree Channel - Separates the island of Minicoy from the Lakshadweep. Eight Degree Channel - Separates the islands of Minicoy and Maldives. Duncan Passage - Separates South Andaman and Little Andaman.</p>",
                    solution_hi: "<p>62.(c) <strong>दस डिग्री।</strong> नौ डिग्री चैनल - मिनिकॉय द्वीप को लक्षद्वीप से अलग करता है। आठ डिग्री चैनल - मिनिकॉय और मालदीव के द्वीपों को अलग करता है। डंकन जलसन्धि - दक्षिणी अंडमान और लघु अंडमान को अलग करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. At what temperature does gypsum get converted into Plaster of Paris?</p>",
                    question_hi: "<p>63. जिप्सम किस तापमान पर प्लास्टर ऑफ पेरिस में परिवर्तित हो जाता है?</p>",
                    options_en: ["<p>473 K</p>", "<p>273 K</p>", 
                                "<p>373 K</p>", "<p>573 K</p>"],
                    options_hi: ["<p>473 K</p>", "<p>273 K</p>",
                                "<p>373 K</p>", "<p>573 K</p>"],
                    solution_en: "<p>63.(c) <strong>373 K. Plaster of Paris</strong> (calcium sulfate hemihydrate) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CaSO</mi><mn>4</mn></msub><mo>.</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math>) is prepared by heating gypsum (calcium sulfate dihydrate) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CaSO</mi><mn>4</mn></msub><mo>.</mo><mn>2</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math>) at 100 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>120</mn><mi mathvariant=\"normal\">o</mi></msup><mi mathvariant=\"normal\">C</mi></math> resulting in loss of water of crystallization. <strong>Reaction:</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CaSO</mi><mn>4</mn></msub><mo>.</mo><mn>2</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math> &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CaSO</mi><mn>4</mn></msub><mo>.</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi><mo>+</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math> . It is commonly used to make the false ceiling, as a sculpturing material and in plasters used in the treatment of fractured bones.</p>",
                    solution_hi: "<p>63.(c) <strong>373 K । प्लास्टर ऑफ पेरिस </strong>(कैल्शियम सल्फेट हेमीहाइड्रेट) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CaSO</mi><mn>4</mn></msub><mo>.</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math>), जिप्सम (कैल्शियम सल्फेट डाइहाइड्रेट) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CaSO</mi><mn>4</mn></msub><mo>.</mo><mn>2</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math>) को 100 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>120</mn><mi mathvariant=\"normal\">o</mi></msup><mi mathvariant=\"normal\">C</mi></math> पर गर्म करके तैयार किया जाता है जिसके परिणामस्वरूप क्रिस्टलीकरण जल की हानि होती है। <strong>अभिक्रिया :</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CaSO</mi><mn>4</mn></msub><mo>.</mo><mn>2</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math> &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CaSO</mi><mn>4</mn></msub><mo>.</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi><mo>+</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math> । यह आमतौर पर फॉल्स सीलिंग बनाने के लिए, मूर्तिकला सामग्री के रूप में और फ्रैक्चर हड्डियों के इलाज में इस्तेमाल होने वाले प्लास्टर में प्रयोग किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. Krishna has a few coins of 1 rupee, 50 paise and 25 paise in the ratio:<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> If the number of 25 paise coins is 100, then the total amount with Krishna is:</p>",
                    question_hi: "<p>64. कृष्णा के पास 1 रुपए, 50 पैसे और 25 पैसे के कुछ सिक्के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>:<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>:<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> के अनुपात में हैं। यदि 25 पैसे के सिक्कों की संख्या 100 है, तो कृष्णा के पास मौजूद कुल राशि कितनी है ?</p>",
                    options_en: ["<p>₹100</p>", "<p>₹75</p>", 
                                "<p>₹125</p>", "<p>₹120</p>"],
                    options_hi: ["<p>₹100</p>", "<p>₹75</p>",
                                "<p>₹125</p>", "<p>₹120</p>"],
                    solution_en: "<p>64.(c) <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 8 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 8 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 8 = 2 : 4 : 4<br>25 - paise coin <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mi>unit</mi><mo>)</mo><mo>&#8594;</mo><mn>100</mn></math> <br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mi>unit</mi><mo>&#8594;</mo><mn>25</mn></math><br>Now, total amount<br>= (1 &times; 2 &times; 25) + (0.5 &times; 4 &times; 25) + (0.25 &times; 4 &times; 25)<br>= 50 + 50 + 25 = ₹125</p>",
                    solution_hi: "<p>64.(c)<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 8 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 8 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 8 = 2 : 4 : 4<br>25 पैसे का सिक्का (4 इकाई ) <math display=\"inline\"><mo>&#8594;</mo></math> 100<br>1 इकाई <math display=\"inline\"><mo>&#8594;</mo></math> 25<br>अब, कुल राशि = (1 &times; 2 &times; 25) + (0.5 &times; 4 &times; 25) + (0.25 &times; 4 &times; 25)<br>= 50 + 50 + 25&nbsp; = ₹125</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. Six persons, A, B, C, D, E and F, are sitting around a circular table. All are facing the center of the table. Only two persons are sitting between D and A. F is to the immediate right of C. E is second to the right of F. E is second to the left of A. Who is sitting to the immediate right of F?</p>",
                    question_hi: "<p>65. छह व्यक्ति A, B, C, D, E और F एक वृत्ताकार मेज के चारों ओर बैठे हैं। सभी का मुख मेज के केंद्र की ओर है। D और A के बीच केवल दो व्यक्ति बैठे हैं। F, C के ठीक दायें बैठा है। E, F के दायें से दूसरे स्थान पर है। E, A के बायें से दूसरे स्थान पर है। F के ठीक दायें कौन बैठा है?</p>",
                    options_en: ["<p>E</p>", "<p>C</p>", 
                                "<p>A</p>", "<p>D</p>"],
                    options_hi: ["<p>E</p>", "<p>C</p>",
                                "<p>A</p>", "<p>D</p>"],
                    solution_en: "<p>65.(d) As per given instructions in the question, we can arrange all persons in the following way.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023499.png\" alt=\"rId19\" width=\"179\" height=\"111\"><br>We can clearly see in the above diagram that D is the immediate right of F.</p>",
                    solution_hi: "<p>65.(d) प्रश्न में दिए गए निर्देशों के अनुसार, हम सभी व्यक्तियों को निम्न प्रकार से व्यवस्थित कर सकते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023652.png\" alt=\"rId20\" width=\"164\" height=\"113\"><br>उपरोक्त आरेख में हम स्पष्ट रूप से देख सकते हैं कि D, F के ठीक दायें है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. Which river system\'s tributaries are Purna and Girna Rivers?</p>",
                    question_hi: "<p>66. पूर्णा और गिरना नदियां निम्न में से किस नदी प्रणाली की सहायक नदियां हैं?</p>",
                    options_en: ["<p>Kaveri</p>", "<p>Tapti</p>", 
                                "<p>Godavari</p>", "<p>Narmada</p>"],
                    options_hi: ["<p>कावेरी</p>", "<p>ताप्ती</p>",
                                "<p>गोदावरी</p>", "<p>नर्मदा</p>"],
                    solution_en: "<p>66.(b) <strong>Tapti .</strong> Tapti river is the second largest westward draining river of the Peninsula. Origin - Near Multai reserve forest in Betul district of Madhya Pradesh. Tributaries - the Suki, the Gomai, the Arunavati and the Aner which joins it from right, and those joining from left are the Vaghur, the Amravati, the Buray, the Panjhra, the Bori, the Girna, the Purna, the Mona and the Sipna. Godavari river (Dakshin ganga).</p>",
                    solution_hi: "<p>66.(b) <strong>ताप्ती। </strong>ताप्ती नदी प्रायद्वीप की पश्चिम की ओर बहने वाली दूसरी सबसे बड़ी नदी है। उदगम स्थल - मध्य प्रदेश के बैतूल जिले में मुलताई आरक्षित वन क्षेत्र के पास से । सहायक नदियाँ - सूकी, गोमई, अरुणावती और अनेर जो इसे दाहिनी ओर से जोड़ती हैं, और जो बाएँ से जुड़ती हैं वे हैं वाघुर, अमरावती, बुराय, पंझरा, बोरी, गिरणा, पूर्णा, मोना और सिपना। गोदावरी नदी (दक्षिणी गंगा)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. What are regenerative cells used by Hydra for?</p>",
                    question_hi: "<p>67. हाइड्रा द्वारा पुनर्योजी कोशिकाओं का उपयोग किस लिए किया जाता है?</p>",
                    options_en: ["<p>Hydra uses regenerative cells for reproduction by spore formation.</p>", "<p>Hydra uses regenerative cells for reproduction by fragmentation.</p>", 
                                "<p>Hydra uses regenerative cells for reproduction by budding.</p>", "<p>Hydra uses regenerative cells for reproduction by sexual reproduction.</p>"],
                    options_hi: ["<p>हाइड्रा बीजाणु निर्माण द्वारा प्रजनन के लिए पुनर्योजी कोशिकाओं का उपयोग करता है।</p>", "<p>हाइड्रा विखंडन द्वारा प्रजनन के लिए पुनर्योजी कोशिकाओं का उपयोग करता है।</p>",
                                "<p>हाइड्रा नवोदित द्वारा प्रजनन के लिए पुनर्योजी कोशिकाओं का उपयोग करता है।</p>", "<p>हाइड्रा यौन प्रजनन द्वारा प्रजनन के लिए पुनर्योजी कोशिकाओं का उपयोग करता है।</p>"],
                    solution_en: "<p>67. (c) Hydra uses regenerative cells, also known as interstitial cells, for asexual reproduction through a process called budding. These regenerative cells are multipotent stem cells capable of dividing and differentiating into various cell types. During budding, a small outgrowth develops on the parent Hydra\'s body due to the rapid division of these cells. This bud gradually grows and forms tentacles and a mouth, becoming a miniature version of the adult Hydra. Once fully developed, it detaches from the parent and lives independently. The regenerative cells play a crucial role in forming the structures needed for the developing bud, highlighting Hydra\'s remarkable ability to regenerate and reproduce efficiently.</p>",
                    solution_hi: "<p>67. (c) हाइड्रा पुनर्योजी कोशिकाओं का उपयोग करता है, जिन्हें अंतरालीय कोशिकाएँ भी कहा जाता है, नवोदित नामक प्रक्रिया के माध्यम से अलैंगिक प्रजनन के लिए। ये पुनर्योजी कोशिकाएँ बहुशक्तिशाली स्टेम कोशिकाएँ हैं जो विभिन्न प्रकार की कोशिकाओं में विभाजित और विभेदित होने में सक्षम हैं। नवोदित होने के दौरान, इन कोशिकाओं के तेजी से विभाजन के कारण मूल हाइड्रा के शरीर पर एक छोटा सा प्रकोप विकसित होता है। यह कली धीरे-धीरे बढ़ती है और तंबू और मुंह बनाती है, जो वयस्क हाइड्रा का लघु संस्करण बन जाता है। एक बार पूरी तरह से विकसित होने के बाद, यह मूल से अलग हो जाता है और स्वतंत्र रूप से रहता है। पुनर्योजी कोशिकाएं विकासशील कली के लिए आवश्यक संरचनाओं के निर्माण में महत्वपूर्ण भूमिका निभाती हैं, तथा हाइड्रा की पुनर्योजी और कुशलतापूर्वक प्रजनन करने की उल्लेखनीय क्षमता को उजागर करती हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. X and Y can complete a piece of work in 8 days and 12 days, respectively. If they work on alternate days, with X working on the first day, how long will it take the duo to complete the same work?</p>",
                    question_hi: "<p>68. X और Y एक कार्य को क्रमश: 8 दिन और 12 दिन में पूरा कर सकते हैं। यदि वे वैकल्पिक दिनों में कार्य करते हैं, जिसमें X पहले दिन कार्य करता है, तो दोनों को कार्य को पूरा करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mn>9</mn><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mn>9</mn><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>68.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023770.png\" alt=\"rId21\" width=\"149\" height=\"121\"><br>Work done by X and Y in 2 days = 3 + 2 =5 units<br>So, the work done by X and Y in 8 days = 5 &times; 4 = 20 units<br>Work done on 9th day = 3 units<br>Time taken by Y to complete remaining work = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> day<br>Time taken by (X + Y) to complete the work = 9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi>&#160;</mi><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mo>.</mo></math></p>",
                    solution_hi: "<p>68.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623023907.png\" alt=\"rId22\" width=\"147\" height=\"130\"><br>X और Y द्वारा 2 दिनों में किया गया कार्य = 3 + 2 = 5 इकाई<br>अतः, X और Y द्वारा 8 दिनों में किया गया कार्य = 5 &times; 4 = 20 इकाई<br>9वें दिन किया गया कार्य = 3 इकाई<br>शेष कार्य को पूरा करने में Y द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> दिन<br>कार्य को पूरा करने में (X + Y) द्वारा लिया गया समय = 9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Riya walks 25 m towards north. Then she turns right and walks 30 m, then turns right and walks 40 m. She then turns left and walks 15 m. Finally, she turns left and walks 15 m. In which direction and at a distance of how many meters is she now from the starting point? <br>(All turns are 90&deg; turns only)</p>",
                    question_hi: "<p>69. रिया उत्तर की ओर 25 m चलती है। फिर वह दाएं मुड़ती है, और 30m चलती है, फिर दाएं मुड़ती है और 40m चलती है। फिर वह बाएं मुड़ती है, और 15 m चलती है। अंत में, वह बाएं मुड़ती है, और 15m चलती है। अब वह आरंभ बिंदु से किस दिशा में और कितने मीटर की दूरी पर है? <br>(सभी मोड़ केवल 90&deg; डिग्री वाले मोड़ हैं)</p>",
                    options_en: ["<p>45 m, west</p>", "<p>45 m, east</p>", 
                                "<p>15 m, south</p>", "<p>15 m, north</p>"],
                    options_hi: ["<p>45 मीटर, पश्चिम</p>", "<p>45 मीटर, पूर्व</p>",
                                "<p>15 मीटर, दक्षिण</p>", "<p>15 मीटर, उत्तर</p>"],
                    solution_en: "<p>69.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024024.png\" alt=\"rId23\" width=\"148\" height=\"149\"><br>Required distance = 30 + 15 = 45 m, east.</p>",
                    solution_hi: "<p>69.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024024.png\" alt=\"rId23\" width=\"148\" height=\"149\"><br>आवश्यक दूरी = 30 + 15 = 45 मीटर, पूर्व।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. Who was awarded the 2024 Jamsetji Tata Award by the Indian Society for Quality (ISQ)?<strong id=\"docs-internal-guid-ce030771-7fff-adf8-ad30-42cce8848d19\"><br></strong></p>",
                    question_hi: "<p>70. इंडियन सोसाइटी फॉर क्वालिटी (ISQ) द्वारा 2024 जमशेदजी टाटा पुरस्कार से किसे सम्मानित किया गया?</p>",
                    options_en: ["<p>Ratan Tata</p>", "<p>Kiran Mazumdar-Shaw</p>", 
                                "<p>Narayana Murthy</p>", "<p>Sundar Pichai</p>"],
                    options_hi: ["<p>रतन टाटा</p>", "<p>किरण मजूमदार-शॉ</p>",
                                "<p>नारायण मूर्ति</p>", "<p>सुंदर पिचाई</p>"],
                    solution_en: "<p>70. (b) <strong>Kiran Mazumdar-Shaw,</strong> Chairperson of Biocon Group, was conferred the 2024 Jamsetji Tata Award by the Indian Society for Quality (ISQ) at the ISQ Annual Conference 2024 held in Bengaluru, Karnataka.</p>",
                    solution_hi: "<p>70. (b) बायोकॉन ग्रुप की चेयरपर्सन <strong>किरण मजूमदार-शॉ</strong> को बेंगलुरु, कर्नाटक में आयोजित आईएसक्यू वार्षिक सम्मेलन 2024 में इंडियन सोसाइटी फॉर क्वालिटी (आईएसक्यू) द्वारा 2024 जमशेदजी टाटा पुरस्कार से सम्मानित किया गया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. If a child inherits a Y chromosome from the father, what will be the sex of the child?<strong id=\"docs-internal-guid-519e34c9-7fff-7193-5bdf-937fe336ec06\"><br></strong></p>",
                    question_hi: "<p>71. यदि एक बच्चा पिता से Y गुणसूत्र विरासत में प्राप्त करता है, तो उस बच्चे का लिंग क्या होगा?</p>",
                    options_en: ["<p>Hermaphrodite</p>", "<p>&nbsp;Female</p>", 
                                "<p>Male</p>", "<p dir=\"ltr\">Cannot be determined</p>"],
                    options_hi: ["<p>हर्माफ्रोडाइट</p>", "<p>महिला</p>",
                                "<p>पुरुष</p>", "<p dir=\"ltr\">निर्धारित नहीं किया जा सकता</p>\n<p>&nbsp;</p>"],
                    solution_en: "<p>71. (c) <strong>Male. </strong>The sex of a child is determined by the combination of sex chromosomes inherited from the parents. Females have two X chromosomes (XX), while males have one X and one Y chromosome (XY). If a child inherits a Y chromosome from the father and an X chromosome from the mother, the child will develop as male (XY). If the child inherits an X chromosome from both parents, the child will develop as female (XX).</p>",
                    solution_hi: "<p>71. (c) <strong>पुरुष।</strong> बच्चे का लिंग उस संयोजन के द्वारा निर्धारित होता है जो सेक्स गुणसूत्रों का माता-पिता से विरासत में प्राप्त होता है। महिलाओं में दो X गुणसूत्र (XX) होते हैं, जबकि पुरुषों में एक X और एक Y गुणसूत्र (XY) होते हैं। यदि एक बच्चा पिता से Y गुणसूत्र और माता से X गुणसूत्र प्राप्त करता है, तो बच्चा पुरुष (XY) के रूप में विकसित होगा। यदि बच्चा दोनों माता-पिता से X गुणसूत्र प्राप्त करता है, तो बच्चा महिला (XX) के रूप में विकसित होगा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. A man travels 80 km in three hours. He further travels for two more hours. Find the distance travelled in the latter two hours, if his average speed for the entire journey is 30 km/h.</p>",
                    question_hi: "<p>72. एक आदमी तीन घंटे में 80 किमी की यात्रा करता है। वह आगे और दो घंटे की यात्रा करता है। यदि पूरी यात्रा के लिए उसकी औसत गति 30 किमी/घंटा है। तो बाद के दो घंटों में तय की गई दूरी ज्ञात कीजिए,</p>",
                    options_en: ["<p>70 km</p>", "<p>150 km</p>", 
                                "<p>120 km</p>", "<p>90 km</p>"],
                    options_hi: ["<p>70 किमी</p>", "<p>150 किमी</p>",
                                "<p>120 किमी</p>", "<p>90 किमी</p>"],
                    solution_en: "<p>72.(a) Let the distance travelled latter = x km., <br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>dist</mi><mo>.</mo></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>3</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = 30<br><math display=\"inline\"><mo>&#8658;</mo></math> 80 + x = 150 <br><math display=\"inline\"><mo>&#8658;</mo></math> x = 70 km</p>",
                    solution_hi: "<p>72.(a)<br>माना बाद में तय की गई दूरी = x किमी <br>औसत चाल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>&#160;&#160;</mo></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>3</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = 30<br><math display=\"inline\"><mo>&#8658;</mo></math> 80 + x = 150 <br><math display=\"inline\"><mo>&#8658;</mo></math> x = 70 किमी</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. Vignesh puts a watch on a table in such a way that it reads 9 :15 a.m. Hour hand points to the south direction. Which direction does the minute hand face?</p>",
                    question_hi: "<p>73. विग्नेश एक मेज पर एक घड़ी को कुछ इस तरह रखता है कि सुबह के 9 :15 के समय घंटे वाली सुई दक्षिण दिशा की ओर इंगित करती है। मिनट वाली सुई किस दिशा में है ?</p>",
                    options_en: ["<p>West</p>", "<p>East</p>", 
                                "<p>North</p>", "<p>South</p>"],
                    options_hi: ["<p>पश्चिम</p>", "<p>पूर्व</p>",
                                "<p>उत्तर</p>", "<p>दक्षिण</p>"],
                    solution_en: "<p>73.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024148.png\" alt=\"rId24\" width=\"207\" height=\"127\"><br>From the above diagram it is clearly seen that the minute hand faces in the <strong>North </strong>direction.</p>",
                    solution_hi: "<p>73.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024246.png\" alt=\"rId25\" width=\"188\" height=\"119\"><br>उपरोक्त आरेख से यह स्पष्ट रूप से देखा जा सकता है कि मिनट की सूई <strong>उत्तर </strong>दिशा की ओर है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. Which word was selected as the Oxford Word of the Year for 2024?<strong id=\"docs-internal-guid-5b6aa594-7fff-2bea-c831-6d48e0d75607\"><br></strong></p>",
                    question_hi: "<p>74. 2024 के लिए ऑक्सफोर्ड वर्ड ऑफ द ईयर के रूप में किस शब्द को चुना गया?</p>",
                    options_en: ["<p>Climate change</p>", "<p>Brain rot</p>", 
                                "<p>Social media</p>", "<p dir=\"ltr\">Pandemic</p>"],
                    options_hi: ["<p>जलवायु परिवर्तन</p>", "<p>ब्रेन रोट</p>",
                                "<p>सोशल मीडिया</p>", "<p>महामारी</p>"],
                    solution_en: "<p>74. (b) <strong>Brain rot.</strong> In December 2024, Oxford University Press (OUP) selected &lsquo;Brain rot,&rsquo; a noun referring to the deterioration of a person&rsquo;s mental or intellectual state, often attributed to the overconsumption of content that is seen as trivial or intellectually unchallenging, as the Oxford Word of the Year for 2024.</p>",
                    solution_hi: "<p>74. (b) <strong>ब्रेन रोट।</strong> दिसंबर 2024 में, ऑक्सफोर्ड यूनिवर्सिटी प्रेस (OUP) ने \'ब्रेन रोट\' को चुना, जो एक संज्ञा है जो किसी व्यक्ति की मानसिक या बौद्धिक स्थिति में गिरावट को संदर्भित करती है, जिसे अक्सर तुच्छ या बौद्धिक रूप से चुनौतीपूर्ण नहीं मानी जाने वाली सामग्री के अत्यधिक उपभोग के लिए जिम्मेदार ठहराया जाता है, जिसे 2024 के लिए ऑक्सफोर्ड वर्ड ऑफ द ईयर के रूप में चुना गया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75. Which of the following is a method of reproduction in plants, where only a single parent is involved?<strong id=\"docs-internal-guid-f4fc0a5c-7fff-59a2-6c7c-e03dabba2ea6\"><br></strong></p>",
                    question_hi: "<p>75. निम्नलिखित में से कौन सा पौधों में प्रजनन की एक विधि है, जिसमें केवल एक ही माता-पिता शामिल होते हैं?</p>",
                    options_en: ["<p>Sexual reproduction</p>", "<p>Asexual reproduction</p>", 
                                "<p>Fertilisation</p>", "<p>Pollination</p>"],
                    options_hi: ["<p>यौन प्रजनन</p>", "<p>&nbsp;अवयविक प्रजनन</p>",
                                "<p>निषेचन</p>", "<p dir=\"ltr\">परागण</p>"],
                    solution_en: "<p>75. (b) <strong>Asexual reproduction</strong> in plants involves only a single parent and does not require the fusion of male and female gametes. The offspring produced are genetically identical to the parent, as they are formed through processes like vegetative propagation, budding, or spore formation. In contrast, sexual reproduction requires two parent plants, with the male and female gametes combining to produce genetically diverse offspring. Fertilisation, which occurs after pollination, is the fusion of male and female gametes to form a zygote, marking a crucial step in sexual reproduction. Pollination is the transfer of pollen from the male to the female part of a flower, enabling fertilisation, but it does not itself result in reproduction. Therefore, asexual reproduction is the method where only one parent is involved.</p>",
                    solution_hi: "<p>75. (b) <strong>अवयविक प्रजनन</strong> में केवल एक ही माता-पिता शामिल होता है और इसमें पुरुष और महिला युग्मजों के मिलन की आवश्यकता नहीं होती। इस प्रकार के प्रजनन में उत्पन्न संतानें माता-पिता के समान आनुवंशिक रूप से समान होती हैं, क्योंकि ये वनस्पतिक प्रचार, कल्ले से उत्पत्ति, या बीजाणु निर्माण जैसी प्रक्रियाओं के द्वारा बनती हैं। इसके विपरीत, यौन प्रजनन में दो माता-पिता पौधों की आवश्यकता होती है, जिसमें पुरुष और महिला युग्मजों का संयोजन आनुवंशिक रूप से विविध संतानें उत्पन्न करता है। निषेचन, जो परागण के बाद होता है, पुरुष और महिला युग्मजों का मिलन होता है, जिससे युग्मज बनता है, जो यौन प्रजनन में एक महत्वपूर्ण कदम होता है। परागण, पुरुष के पराग को फूल के महिला भाग में स्थानांतरित करने की प्रक्रिया है, जो निषेचन की अनुमति देती है, लेकिन यह खुद प्रजनन में परिणत नहीं होता। इस प्रकार, अवयविक प्रजनन वह विधि है जिसमें केवल एक माता-पिता शामिल होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. If the numerator of a fraction is increased by 30% and its denominator is decreased by 35%, the value of the fraction becomes <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math>. Find the original fraction.</p>",
                    question_hi: "<p>76. यदि किसी भिन्न के अंश में 30% की वृद्धि की जाती है और उसके हर में 35% की कमी की जाती है, तो भिन्न का मान <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math>.हो जाता है। मूल भिन्न ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>76.(d) Let , fraction = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi mathvariant=\"normal\">X</mi></mrow><mrow><mn>100</mn><mi mathvariant=\"normal\">Y</mi></mrow></mfrac></math><br>A/Q , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>130</mn><mi mathvariant=\"normal\">X</mi></mrow><mrow><mn>65</mn><mi mathvariant=\"normal\">Y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>15</mn></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">X</mi><mi mathvariant=\"normal\">Y</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>65</mn></mrow><mrow><mn>15</mn><mo>&#215;</mo><mn>130</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></p>",
                    solution_hi: "<p>76.(d) माना भिन्न = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi mathvariant=\"normal\">X</mi></mrow><mrow><mn>100</mn><mi mathvariant=\"normal\">Y</mi></mrow></mfrac></math><br>प्रश्न के अनुसार , <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>130</mn><mi mathvariant=\"normal\">X</mi></mrow><mrow><mn>65</mn><mi mathvariant=\"normal\">Y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>15</mn></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">X</mi><mi mathvariant=\"normal\">Y</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>65</mn></mrow><mrow><mn>15</mn><mo>&#215;</mo><mn>130</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. If &lsquo;+&rsquo; means &ldquo;<math display=\"inline\"><mo>-</mo></math>&rdquo;, &ldquo;-&rdquo; means &ldquo;&divide;&rdquo;, &lsquo;&times;&rsquo;means &rdquo;+&rdquo; and &ldquo;&divide;&rdquo; means &rdquo;&times;&rdquo; what will be the value of the following expression?<br>[{(6 <math display=\"inline\"><mo>&#215;</mo></math> 9) + (3 &divide; 2)} - (7 + 4)] &divide; 2</p>",
                    question_hi: "<p>77. यदि &lsquo;+&rsquo; का अर्थ &ldquo;<math display=\"inline\"><mo>-</mo></math>&rdquo;, &ldquo;-&rdquo; का अर्थ &ldquo;&divide;&rdquo;, &lsquo;&times;&rsquo; का अर्थ &rdquo;+&rdquo; और &ldquo;&divide;&rdquo; का अर्थ &rdquo;&times;&rdquo; है तो निम्नलिखित व्यंजक का मान क्या होगा?<br>[{(6 <math display=\"inline\"><mo>&#215;</mo></math> 9) + (3 &divide; 2)} - (7 + 4)] &divide; 2</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>4</p>", "<p>2</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>4</p>", "<p>2</p>"],
                    solution_en: "<p>77.(a) <br>[{(6 <math display=\"inline\"><mo>&#215;</mo></math> 9) + (3 &divide; 2)} - (7 + 4)] &divide; 2<br>According to question,<br>[{(6 + 9) - (3 &times; 2)} &divide; (7 - 4)] &times; 2<br><math display=\"inline\"><mo>&#8658;</mo></math> [{(15) - (6)} &divide; (3)] &times; 2 &rArr; [ 3 &times; 2] = 6</p>",
                    solution_hi: "<p>77.(a) <br>[{(6 <math display=\"inline\"><mo>&#215;</mo></math> 9) + (3 &divide; 2)} - (7 + 4)] &divide; 2\\<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> [{(6 + 9) - (3 &times; 2)} &divide; (7 - 4)] &times; 2<br><math display=\"inline\"><mo>&#8658;</mo></math> [{(15) - (6)} &divide; (3)] &times; 2 &rArr; [3 &times; 2] = 6</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. Who became the first woman officer to serve as the Director-General of the Armed Forces Medical Services (DG AFMS)?</p>",
                    question_hi: "<p>78. सशस्त्र सेना चिकित्सा सेवा (DG AFMS) की महानिदेशक के रूप में सेवा करने वाली पहली महिला अधिकारी कौन बनीं?</p>",
                    options_en: ["<p>Parisha Santoshi</p>", "<p>Punita Arora</p>", 
                                "<p>Arti Sarin</p>", "<p>Shaliza Dhami</p>"],
                    options_hi: ["<p>परीशा संतोषी</p>", "<p>पुनीता अरोड़ा</p>",
                                "<p>आरती सरीन</p>", "<p>शालिजा धामी</p>"],
                    solution_en: "<p>78. (c) <strong>Arti Sarin.</strong> Surgeon Vice Admiral Arti Sarin became the first woman to serve as Director General, Armed Forces Medical Services (DGAFMS). The DGAFMS oversees medical policy matters for the Armed Forces and reports to the Ministry of Defence. Before this role, she held key positions, including DG Medical Services for the Navy and Air Force, and Director of Armed Forces Medical College, Pune.</p>",
                    solution_hi: "<p>78. (c) <strong>आरती सरीन।</strong> सर्जन वाइस एडमिरल आरती सरीन सशस्त्र सेना चिकित्सा सेवा (DGAFMS) की महानिदेशक के रूप में सेवा करने वाली पहली महिला बनीं। DGAFMS सशस्त्र बलों के लिए चिकित्सा नीति मामलों की देखरेख करता है और रक्षा मंत्रालय को रिपोर्ट करता है। इस भूमिका से पहले, उन्होंने नौसेना और वायु सेना के लिए DG चिकित्सा सेवा और सशस्त्र बल चिकित्सा महाविद्यालय, पुणे के निदेशक सहित प्रमुख पदों पर कार्य किया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. Which of the following is a viral infection that can be transmitted through sexual contact?</p>",
                    question_hi: "<p>79. निम्नलिखित में से कौन सा एक वायरल संक्रमण है जो यौन संपर्क के माध्यम से फैल सकता है?<strong id=\"docs-internal-guid-24baf019-7fff-6e43-9ade-85e0f4325f48\"><br></strong></p>",
                    options_en: ["<p>Tuberculosis</p>", "<p>Gonorrhoea</p>", 
                                "<p>Syphilis</p>", "<p dir=\"ltr\">HIV-AIDS</p>\n<p>&nbsp;</p>"],
                    options_hi: ["<p>तपेदिक</p>", "<p>गोनोरिया</p>",
                                "<p>सिफलिस</p>", "<p dir=\"ltr\">HIV-AIDS</p>\n<p>&nbsp;</p>"],
                    solution_en: "<p>79.(d) <strong>HIV-AIDS.</strong> HIV (Human Immunodeficiency Virus) is a viral infection that can be transmitted through sexual contact, as well as through other means like sharing needles or from mother to child during childbirth or breastfeeding. HIV can lead to AIDS (Acquired Immunodeficiency Syndrome), a condition where the immune system is severely weakened. Tuberculosis (TB) is a bacterial infection, not viral, and it primarily affects the lungs, though it can spread to other parts of the body. Gonorrhoea and Syphilis are bacterial infections that can also be transmitted through sexual contact, but they are not viral.</p>",
                    solution_hi: "<p>79.(d) <strong>HIV-AIDS ।</strong> HIV (ह्यूमन इम्यूनोडेफिशिएंसी वायरस) एक वायरल संक्रमण है जो यौन संपर्क के माध्यम से फैल सकता है, साथ ही अन्य तरीकों से भी फैल सकता है जैसे सुइयों का साझा करना या बच्चे को प्रसव या स्तनपान के दौरान मां से। HIV AIDS (एक्वायर्ड इम्यूनोडेफिशिएंसी सिंड्रोम) में बदल सकता है, जो एक स्थिति है जिसमें इम्यून सिस्टम गंभीर रूप से कमजोर हो जाता है। तपेदिक (TB) एक बैक्टीरियल संक्रमण है, जो वायरल नहीं है, और यह मुख्य रूप से फेफड़ों को प्रभावित करता है, हालांकि यह शरीर के अन्य हिस्सों में भी फैल सकता है। गोनोरिया और सिफलिस बैक्टीरियल संक्रमण हैं जो यौन संपर्क के माध्यम से फैल सकते हैं, लेकिन ये वायरल नहीं हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. If the cost price of an article is ₹2,500 and its selling price is ₹2,375, then the loss percentage is:</p>",
                    question_hi: "<p>80. यदि किसी वस्तु का क्रय मूल्य 2,500 रुपये है और उसका विक्रय मूल्य 2,375 रुपये है, तो हानि प्रतिशत है:</p>",
                    options_en: ["<p>3%</p>", "<p>5%</p>", 
                                "<p>4%</p>", "<p>6%</p>"],
                    options_hi: ["<p>3%</p>", "<p>5%</p>",
                                "<p>4%</p>", "<p>6%</p>"],
                    solution_en: "<p>80.(b) Cost price = ₹2500 <br>Selling price = ₹ 2375 <br>Loss Percentage = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>SP</mi></mrow><mi>CP</mi></mfrac></math> &times; 100<br><math display=\"inline\"><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>-</mo><mn>2375</mn></mrow><mn>2500</mn></mfrac></math> &times; 100 = 5 % loss</p>",
                    solution_hi: "<p>80.(b) क्रय मूल्य = ₹ 2500 <br>विक्रय मूल्य = ₹ 2375 <br>प्रतिशत हानि =<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>SP</mi></mrow><mi>CP</mi></mfrac></math> &times; 100<br><math display=\"inline\"><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>-</mo><mn>2375</mn></mrow><mn>2500</mn></mfrac></math> &times; 100 = 5 % हानि</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. Which two signs should be interchanged to make the following&nbsp;equation correct?<br>4 <math display=\"inline\"><mo>&#247;</mo></math> 8 + 5 &ndash; 6 &times; 2 = 34</p>",
                    question_hi: "<p>81. निम्नलिखित समीकरण को सही बनाने के लिए किन दो चिह्नों को आपस में बदलना चाहिए?<br>4 <math display=\"inline\"><mo>&#247;</mo></math> 8 + 5 &ndash; 6 &times; 2 = 34</p>",
                    options_en: ["<p>+, &ndash;</p>", "<p><math display=\"inline\"><mo>&#247;</mo></math>, &times;</p>", 
                                "<p><math display=\"inline\"><mo>&#247;</mo></math>, +</p>", "<p><math display=\"inline\"><mo>&#215;</mo></math>, +</p>"],
                    options_hi: ["<p>+, &ndash;</p>", "<p><math display=\"inline\"><mo>&#247;</mo></math>, &times;</p>",
                                "<p><math display=\"inline\"><mo>&#247;</mo></math>, +</p>", "<p><math display=\"inline\"><mo>&#215;</mo></math>, +</p>"],
                    solution_en: "<p>81.(b) In these type of questions, we will check through options one by one, and on doing so option (b) gets satisfied.<br>4 <math display=\"inline\"><mo>&#247;</mo></math> 8 + 5 &ndash; 6 &times; 2 = 34<br>Putting option (b) in above equation, we get<br>4 <math display=\"inline\"><mo>&#215;</mo></math> 8 + 5 &ndash; 6 &divide; 2<br>= 4 <math display=\"inline\"><mo>&#215;</mo></math> 8 + 5 &ndash; 3<br>= 32 + 5 &ndash; 3 = 34 = RHS</p>",
                    solution_hi: "<p>81.(b) इस प्रकार के प्रश्नों में हम एक-एक करके विकल्पों की जाँच करेंगे और ऐसा करने पर विकल्प (b) संतुष्ट हो जाता है।<br>4 <math display=\"inline\"><mo>&#247;</mo></math> 8 + 5 &ndash; 6 &times; 2 = 34<br>उपरोक्त समीकरण में विकल्प (b) रखने पर, हमें प्राप्त होता है<br>4 <math display=\"inline\"><mo>&#215;</mo></math> 8 + 5 &ndash; 6 &divide; 2<br>= 4 <math display=\"inline\"><mo>&#215;</mo></math> 8 + 5 &ndash; 3<br>= 32 + 5 &ndash; 3 = 34 = RHS</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. Which day is celebrated as &lsquo;National Handloom Day&rsquo; every year?</p>",
                    question_hi: "<p>82. हर साल किस दिन को &lsquo;राष्ट्रीय हथकरघा दिवस&rsquo; के रूप में मनाया जाता है?</p>",
                    options_en: ["<p>5 August</p>", "<p>6 August</p>", 
                                "<p>7 August</p>", "<p>8 August</p>"],
                    options_hi: ["<p>5 अगस्त</p>", "<p>6 अगस्त</p>",
                                "<p>7 अगस्त</p>", "<p>8 अगस्त</p>"],
                    solution_en: "<p>82.(c) <strong>7 August.</strong> National Handloom Day, celebrated on August 7th, honors India&rsquo;s handloom heritage and supports artisans. Launched in 2015 to mark the Swadeshi Movement&rsquo;s centenary, it promotes indigenous crafts and empowers weavers. The 2024 theme focuses on integrating technology and innovation to expand market access and enhance skill development for artisans.</p>",
                    solution_hi: "<p>82.(c) <strong>7 अगस्त </strong>को मनाया जाने वाला राष्ट्रीय हथकरघा दिवस भारत की हथकरघा विरासत का सम्मान करता है और कारीगरों का समर्थन करता है। स्वदेशी आंदोलन की शताब्दी को चिह्नित करने के लिए 2015 में शुरू किया गया, यह स्वदेशी शिल्प को बढ़ावा देता है और बुनकरों को सशक्त बनाता है। 2024 की थीम बाजार तक पहुँच का विस्तार करने और कारीगरों के लिए कौशल विकास को बढ़ाने के लिए प्रौद्योगिकी और नवाचार को एकीकृत करने पर केंद्रित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. What is the generic name given to members of the cat family?</p>",
                    question_hi: "<p>83. बिल्ली प्रजाति के सदस्यों को दिया जाने वाला वर्गीय नाम क्या है ?</p>",
                    options_en: ["<p>Feline</p>", "<p>Caprine</p>", 
                                "<p>Canine</p>", "<p>Bovine</p>"],
                    options_hi: ["<p>फेलाइन</p>", "<p>कैप्रिन</p>",
                                "<p>कैनाइन</p>", "<p>बोवाइन</p>"],
                    solution_en: "<p>83. (a) <strong>Feline.</strong> The cat (Felis Catus) is a domestic species of small carnivorous mammal. Felidae Animal : Cat, Caracal, Lion, Serval, Leopard, Tiger, Lynxes, Jaguar, Cheetah, Panthera, Cougar. Caprine : Sheep, Ovis, Goat, Chamois, Caprini, Oreamnos, Pyrenean chamois, Serows, Nilgiri tahr. Canine (Dog family): German Shepherd, Labrador Retriever, Siberian Husky, Bulldog, Golden Retriever, Pomeranian. Bovine (Cow family) : Cattle, Bison, Domestic yak, Domestic water buffalo, Bos, Bubalus.</p>",
                    solution_hi: "<p>83. (a) <strong>फेलाइन।</strong> बिल्ली (Felis Catus) छोटे मांसाहारी स्तनपायी की एक घरेलू प्रजाति है। Felidae पशु : बिल्ली, कैराकल, शेर, सर्वल, लेपर्ड, बाघ, लैंक्सेस, जगुआर, चीता, पेंथेरा, कौगर। Caprine: भेड़, ओविस, बकरी, चामोइस, कैप्रिनी, ओरेमनोस, पाइरेनियन चामोइस, सेरोज़, नीलगिरि तहर। Canine (कुत्ते की प्रजाति) : जर्मन शेफर्ड, लैब्राडोर रिट्रीवर, साइबेरियन हस्की, बुलडॉग, गोल्डन रिट्रीवर, पोमेरेनियन। Bovine (गाय की प्रजाति) : मवेशी, बाइसन, पालतू याक, पालतू जलीय भैंस, बोस, बुबलस।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. Venkat bought a second-hand scooter and spent 10% of the cost on its repairs. He sold the scooter for a profit of 2,200. How much did he spend on repairs if he made a profit of 20%?</p>",
                    question_hi: "<p>84. वेंकट ने एक पुराना स्कूटर ख़रीदा, और इसकी मरम्मत पर क्रय मूल्य के 10% के बराबर राशि खर्च की। उसने स्कूटर को 2,200 के लाभ पर बेचा। यदि उसे 20% का लाभ हुआ तो उसने मरम्मत पर कितना खर्च किया?</p>",
                    options_en: ["<p>Rs. 1,200</p>", "<p>Rs. 750</p>", 
                                "<p>Rs. 1,000</p>", "<p>Rs. 400</p>"],
                    options_hi: ["<p>1,200 रुपये</p>", "<p>750 रुपये</p>",
                                "<p>1,000 रुपये</p>", "<p>400 रुपये</p>"],
                    solution_en: "<p>84.(c) <br>Let the C.P of the scooter = <math display=\"inline\"><mi>&#8377;</mi></math>100x <br>Total cost after repairing = <math display=\"inline\"><mi>&#8377;</mi></math>110x<br>S.P = 110x + 2200 and he made a profit of 20% , <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>110</mn><mi mathvariant=\"normal\">x</mi></math> + 2200 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math> &times; 110x<br>&rArr; 132x - 110x = 2200<br>&rArr; 22x = 2200<br>&rArr; x = 100<br>C.P = <math display=\"inline\"><mi>&#8377;</mi></math> 10,000 and repair cost<br>= <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 10000 = ₹1000</p>",
                    solution_hi: "<p>84.(c) माना स्कूटर का क्रय मूल्य = ₹ 100x <br>मरम्मत के बाद कुल लागत = <math display=\"inline\"><mi>&#8377;</mi></math> 110x<br>विक्रय मूल्य = 110x + 2200 और उसने 20% का लाभ कमाया,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>110</mn><mi mathvariant=\"normal\">x</mi></math> + 2200 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math> &times; 110x<br>&rArr; 132x - 110x = 2200<br>&rArr; 22x = 2200<br>&rArr; x = 100<br>क्रय मूल्य = <math display=\"inline\"><mi>&#8377;</mi></math>10,000 और मरम्मत की लागत <br>= <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 10000 = ₹1000</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. How many such pairs of letters are there in the word &lsquo;CLUSTERS&rsquo; (in both the forward and backward directions) which have as many letters between them in the word as there are in the English alphabetical order?</p>",
                    question_hi: "<p>85. शब्द \'CLUSTERS\' (आगे और पीछे दोनों दिशाओं में) में अक्षरों के ऐसे कितने जोड़े हैं जिनके बीच उतने ही अक्षर हैं जितने अंग्रेजी वर्णमाला के क्रम में हैं?</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>0</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>0</p>", "<p>3</p>"],
                    solution_en: "<p>85.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024336.png\" alt=\"rId26\" width=\"285\" height=\"83\"><br>Here there are 3 pairs</p>",
                    solution_hi: "<p>85.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024336.png\" alt=\"rId26\" width=\"285\" height=\"83\"><br>यहाँ 3 जोड़े हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. _______ has three active forms: retinal, retinol and retinoic acid.</p>",
                    question_hi: "<p>86. _____ के तीन सक्रिय रूप हैं: रेटिनल, रेटिनॉल और रेटिनोइक अम्ल</p>",
                    options_en: ["<p>Vitamin C</p>", "<p>Vitamin A</p>", 
                                "<p>Vitamin B</p>", "<p>Vitamin D</p>"],
                    options_hi: ["<p>विटामिन C</p>", "<p>विटामिन A</p>",
                                "<p>विटामिन B</p>", "<p>विटामिन D</p>"],
                    solution_en: "<p>86. (b) <strong>Vitamin A.</strong> Vitamin was discovered by Casimir Funk. Types of Vitamin - Fat soluble (A, D, E, K) and Water soluble (B, C). Vitamin A : Source - Nuts, Eggs, Fish, Liver, Beet Greens, Green leafy vegetables; Deficiency Disease - Dry Skin, Dry Eyes, Night Blindness, Infertility. Source of Vitamin C (Ascorbic acid) - Fresh fruits, black currant, broccoli, goat milk. Source of Vitamin D (Calciferol) - Fish, egg, liver, beef, cod, chicken breast. Source of Vitamin B (Thiamine) - Fresh fruits, potatoes, sweet potatoes, peas, corn, cashew nuts, wheat, milk, black beans.</p>",
                    solution_hi: "<p>86.(b) <strong>विटामिन A ।</strong> विटामिन की खोज कासिमिर फंक ने की थी। विटामिन के प्रकार - वसा में घुलनशील (A, D, E, K) और जल में घुलनशील (B, C)। विटामिन A : स्रोत - मेवे, अंडे, मछली, लीवर, चुकंदर का साग, हरी पत्तेदार सब्जियाँ; कमी से होने वाले रोग - शुष्क त्वचा, शुष्क आँखें, रतौंधी, बांझपन। विटामिन C (एस्कॉर्बिक एसिड) के स्रोत - ताजे फल, काली किशमिश, ब्रोकोली, बकरी का दूध। विटामिन D (कैल्सीफेरॉल) का स्रोत - मछली, अंडा, लीवर, गौमांस (beef), कॉड, चिकन ब्रेस्ट। विटामिन B (थियामिन) का स्रोत - ताजे फल, आलू, शकरकंद, मटर, मक्का, काजू, गेहूं, दूध, काली सेम।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. A shadow of a tower standing on a level ground is found to be 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> meters longer when the Sun\'s altitude is 30&deg; when it is 60&deg;. The height of the tower is:</p>",
                    question_hi: "<p>87. सूर्य का उन्नयन कोण 60&deg; से बदलकर 30&deg; होने पर , भूतल पर स्थित किसी मीनार की छाया की लम्बाई में 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> मीटर की वृद्धि हो जाती है। मीनार की ऊंचाई ज्ञात कीजिये।</p>",
                    options_en: ["<p>70 m</p>", "<p>60 m</p>", 
                                "<p>40 m</p>", "<p>50 m</p>"],
                    options_hi: ["<p>70 m</p>", "<p>60 m</p>",
                                "<p>40 m</p>", "<p>50 m</p>"],
                    solution_en: "<p>87.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024471.png\" alt=\"rId27\" width=\"207\" height=\"129\"><br>In triangle ABD,<br>tan60&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>BD</mi></mfrac><mi mathvariant=\"normal\">&#160;</mi></math> &rArr; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>h<br>In triangle ABC,<br>tan30&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mrow><mi>BD</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>CD</mi></mrow></mfrac></math> &rArr; AB= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br>Now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi mathvariant=\"normal\">h</mi><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi mathvariant=\"normal\">h</mi><mo>=</mo><mi mathvariant=\"normal\">h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></math> &rArr; 3h - h = 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>2</mn><mi mathvariant=\"normal\">h</mi><mo>=</mo><mn>40</mn><msqrt><mn>3</mn></msqrt><mo>&#8658;</mo><mi mathvariant=\"normal\">h</mi><mo>=</mo><mn>20</mn><msqrt><mn>3</mn></msqrt></math><br>Now, height of the tower (AB) <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi mathvariant=\"normal\">h</mi><mo>=</mo><msqrt><mn>3</mn></msqrt><mo>&#215;</mo><mn>20</mn><msqrt><mn>3</mn></msqrt><mo>=</mo><mn>20</mn><mo>&#215;</mo><mn>3</mn><mo>=</mo><mn>60</mn><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">m</mi></math></p>",
                    solution_hi: "<p>87.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024471.png\" alt=\"rId27\" width=\"207\" height=\"129\"><br>त्रिभुज ABD में,<br>tan 60&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>BD</mi></mfrac><mi mathvariant=\"normal\">&#160;</mi></math> &rArr; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>h<br>त्रिभुज ABC में ,<br>tan30&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mrow><mi>BD</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>CD</mi></mrow></mfrac></math> &rArr; AB= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br>अब,&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi mathvariant=\"normal\">h</mi><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi mathvariant=\"normal\">h</mi><mo>=</mo><mi mathvariant=\"normal\">h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></math> &rArr; 3h - h = 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>2</mn><mi mathvariant=\"normal\">h</mi><mo>=</mo><mn>40</mn><msqrt><mn>3</mn></msqrt><mo>&#8658;</mo><mi mathvariant=\"normal\">h</mi><mo>=</mo><mn>20</mn><msqrt><mn>3</mn></msqrt></math><br>अब , मीनार की ऊंचाई (AB)<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi mathvariant=\"normal\">h</mi><mo>=</mo><msqrt><mn>3</mn></msqrt><mo>&#215;</mo><mn>20</mn><msqrt><mn>3</mn></msqrt><mo>=</mo><mn>20</mn><mo>&#215;</mo><mn>3</mn><mo>=</mo><mn>60</mn><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">m</mi></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. Two college friends A and B who met after a couple of years had a doubt about the birthday of their common friend C. According to A, C&rsquo;s birthday is after 12th March but before 15th March. But B remembered that C&rsquo;s birthday is after 13th March but before 17th March. Assuming both of them to be correct, on which date is C&rsquo;s birthday?</p>",
                    question_hi: "<p>88. कॉलेज के दो मित्र A और B, जो कुछ वर्षों के बाद मिले थे, उनके सामान्य मित्र C के जन्मदिन के बारे में संदेह था। A के अनुसार, C का जन्मदिन 12 मार्च के बाद लेकिन 15 मार्च से पहले है। लेकिन B को याद आया कि C का जन्मदिन 13 मार्च के बाद लेकिन 17 मार्च से पहले है। दोनों को सही मानते हुए, C का जन्मदिन किस तारीख को है?</p>",
                    options_en: ["<p>16th March</p>", "<p>14th March</p>", 
                                "<p>13th March</p>", "<p>15th March</p>"],
                    options_hi: ["<p>16 मार्च</p>", "<p>14 मार्च</p>",
                                "<p>13 मार्च</p>", "<p>15 मार्च</p>"],
                    solution_en: "<p>88.(b) According to A, C&rsquo;s birthday is in between 12 - 15<br>According to B, C&rsquo;s birthday is in between 13 - 17<br>Assuming both of them to be correct, we can say birthday on 14th March.<br>i.e. 14th March.</p>",
                    solution_hi: "<p>88.(b) A के अनुसार, C का जन्मदिन 12 - 15 के बीच होता है। <br>B के अनुसार, C का जन्मदिन 13 - 17 के बीच है। <br>दोनों को सही मानते हुए, हम कह सकते हैं कि जन्मदिन 14 मार्च को है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. Which of the following is a natural ecosystem?</p>",
                    question_hi: "<p>89. निम्नलिखित में से कौन सा एक प्राकृतिक पारिस्थितिकी तंत्र है?</p>",
                    options_en: ["<p>Crop field</p>", "<p>Aquarium</p>", 
                                "<p>Garden</p>", "<p>Forest</p>"],
                    options_hi: ["<p>फसल का खेत</p>", "<p>जलाशय</p>",
                                "<p>बाग</p>", "<p dir=\"ltr\">वन</p>"],
                    solution_en: "<p>89.(d) <strong>Forest.</strong> A natural ecosystem refers to an ecosystem that exists without human intervention and is driven by natural processes. A forest is a natural ecosystem where plants, animals, microorganisms, and other factors like climate and soil interact naturally to form a balanced environment. Crop fields, aquariums, and gardens are all examples of human-made ecosystems or artificial ecosystems, where human activities such as farming, animal care, or plant cultivation influence the environment.</p>",
                    solution_hi: "<p>89.(d) <strong>वन ।</strong> प्राकृतिक पारिस्थितिकी तंत्र वह पारिस्थितिकी तंत्र है जो मानव हस्तक्षेप के बिना अस्तित्व में होता है और प्राकृतिक प्रक्रियाओं द्वारा संचालित होता है। वन एक प्राकृतिक पारिस्थितिकी तंत्र है जहाँ पौधे, जानवर, सूक्ष्मजीव और अन्य तत्व जैसे जलवायु और मिट्टी प्राकृतिक रूप से आपस में इंटरैक्ट करते हैं और एक संतुलित पर्यावरण का निर्माण करते हैं। फसल के खेत, जलाशय और बाग मानव-निर्मित पारिस्थितिकी तंत्र के उदाहरण हैं, जहाँ मानव गतिविधियाँ जैसे खेती, पशु देखभाल या पौधों की खेती पर्यावरण को प्रभावित करती हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. My father is presently 25 years older than me. The sum of our ages 5 years ago was 39 years. Find my present age.</p>",
                    question_hi: "<p>90. मेरे पिता इस समय मुझसे 25 वर्ष बड़े हैं। 5 वर्ष पूर्व हमारे आयु का योग 39 वर्ष था। मेरी वर्तमान आयु ज्ञात कीजिए।</p>",
                    options_en: ["<p>15 years</p>", "<p>13 years</p>", 
                                "<p>12 years</p>", "<p>20 years</p>"],
                    options_hi: ["<p>15 वर्ष</p>", "<p>13 वर्ष</p>",
                                "<p>12 वर्ष</p>", "<p>20 वर्ष</p>"],
                    solution_en: "<p>90.(c) <br>Let the present age of son = x years<br>And the present age of father = (x + 25) years<br>According to the question,<br>Sum of their age 5 years ago = 39<br>(x - 5) + (x + 25 - 5) = 39<br>2x + 15 = 39<br>x = 12<br>Present age of son = 12 years</p>",
                    solution_hi: "<p>90.(c) माना पुत्र की वर्तमान आयु = x वर्ष<br>तथा पिता की वर्तमान आयु = (x + 25) वर्ष<br>प्रश्न के अनुसार,<br>5 वर्ष पूर्व उनकी आयु का योग = 39<br>(x - 5) + (x + 25 - 5) = 39<br>2x + 15 = 39 <math display=\"inline\"><mo>&#8658;</mo></math> x = 12<br>पुत्र की वर्तमान आयु = 12 वर्ष</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. Five boxes containing different items, namely scales, pens, tapes, erasers and pencils, are kept one above the other, not necessarily in the same order. The box with pens is on the topmost position. The box with erasers is the only box between the boxes with pencils and scales. There is only one box below the box with scales and that is the box with tapes. Which box is immediately below the box containing pencils?</p>",
                    question_hi: "<p>91. तराजू, कलम, टेप, रबड़ और पेंसिल आदि विभिन्न वस्तुओं के पांच डिब्बों को एक के ऊपर एक रखा गया है, किंतु उनका इसी क्रम में होना अनिवार्य नहीं है। कलम के डिब्बे को सबसे ऊपर रखा गया है। केवल रबड़ के डिब्बे को, पेंसिल के डिब्बे और तराजू के डिब्बे के बीच रखा गया है। तराजू के डिब्बे के नीचे केवल एक डिब्बा है, और वह टेप का डिब्बा है। पेंसिल के डिब्बे के ठीक नीचे कौन सा डिब्बा है?</p>",
                    options_en: ["<p>Erasers</p>", "<p>Scales</p>", 
                                "<p>Tapes</p>", "<p>Pens</p>"],
                    options_hi: ["<p>रबड़ का</p>", "<p>स्केल</p>",
                                "<p>टेप</p>", "<p>पेन</p>"],
                    solution_en: "<p>91.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024571.png\" alt=\"rId28\"><br>So, Required answer = Eraser</p>",
                    solution_hi: "<p>91.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024699.png\" alt=\"rId29\"><br>अत: आवश्यक उत्तर = रबड़</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. Which of the following materials is likely to be non-biodegradable?</p>",
                    question_hi: "<p>92. निम्नलिखित में से कौन सी सामग्री गैर-जैवनिम्नीकरणीय होने की संभावना है?</p>",
                    options_en: ["<p>Wood</p>", "<p>Vegetable peels</p>", 
                                "<p>Plastic</p>", "<p>Paper</p>"],
                    options_hi: ["<p>लकड़ी</p>", "<p>सब्जी के छिलके</p>",
                                "<p>प्लास्टिक</p>", "<p>कागज</p>"],
                    solution_en: "<p>92.(c) <strong>Plastic </strong>is a non-biodegradable material, meaning it does not decompose naturally in the environment over time. It can persist for hundreds or even thousands of years, causing pollution. Wood and vegetable peels are biodegradable, meaning they can decompose naturally through the action of microorganisms. Paper is also biodegradable and decomposes relatively quickly compared to plastic.</p>",
                    solution_hi: "<p>92.(c) <strong>प्लास्टिक</strong> एक गैर-बायोडिग्रेडेबल सामग्री है, जिसका अर्थ है कि यह समय के साथ पर्यावरण में स्वाभाविक रूप से विघटित नहीं होती है। यह सैकड़ों या हज़ारों सालों तक बनी रह सकती है, जिससे प्रदूषण होता है। लकड़ी और सब्जी के छिलके बायोडिग्रेडेबल होते हैं, जिसका अर्थ है कि वे सूक्ष्मजीवों की क्रिया के माध्यम से स्वाभाविक रूप से विघटित हो सकते हैं। कागज भी बायोडिग्रेडेबल है और प्लास्टिक की तुलना में अपेक्षाकृत जल्दी विघटित होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. The frequency distribution here gives the monthly consumption of electricity of 60 consumers of a locality. What is the median of the data?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024806.png\" alt=\"rId30\" width=\"314\" height=\"155\"></p>",
                    question_hi: "<p>93. दिया गया बारंबारता बंटन एक इलाके के 60 उपभोक्ताओं की मासिक विद्युत खपत को दर्शाता है। इन आंकड़ों की माध्यिका ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623024925.png\" alt=\"rId31\" width=\"251\" height=\"163\"></p>",
                    options_en: ["<p>80.5</p>", "<p>83.1</p>", 
                                "<p>82.4</p>", "<p>81.2</p>"],
                    options_hi: ["<p>80.5</p>", "<p>83.1</p>",
                                "<p>82.4</p>", "<p>81.2</p>"],
                    solution_en: "<p>93.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623025083.png\" alt=\"rId32\" width=\"327\" height=\"123\"><br>Total frequency (N) = 60<br>N/2 = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> =30<br>So, the C.F. corresponding to this is <br>(80 - 90)<br>Now, <br>l = 80 , CF = 29 , f = 20 and h = 10<br>Median = l + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mi mathvariant=\"normal\">N</mi><mn>2</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>CF</mi></mrow><mi mathvariant=\"normal\">f</mi></mfrac><mi mathvariant=\"normal\">&#160;</mi></math>&times; h<br>= 80 + <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>29</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 10<br>= <math display=\"inline\"><mn>80</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn><mo>=</mo><mn>80</mn><mo>.</mo><mn>5</mn></math></p>",
                    solution_hi: "<p>93.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623025191.png\" alt=\"rId33\" width=\"308\" height=\"154\"><br>कुल आवृत्ति (N) = 60<br>N/2 = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> =30<br>इसलिए , संचयी आवृत्ति (CF) इसके अनुरूप है (80-90)<br>अब,<br>l = 80 , CF = 29 , f = 20 और h = 10<br>माध्यिका = l + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mi mathvariant=\"normal\">N</mi><mn>2</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>CF</mi></mrow><mi mathvariant=\"normal\">f</mi></mfrac><mi mathvariant=\"normal\">&#160;</mi></math>&times; h<br>= 80 + <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>29</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 10<br>= <math display=\"inline\"><mn>80</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn><mo>=</mo><mn>80</mn><mo>.</mo><mn>5</mn></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. There are five boxes K, L, M, N and O arranged one above the other. Box M is placed above box N. Box O is placed below bow K. Box N is placed above K. Box L is placed below O. Which among the following is the bottom most box?</p>",
                    question_hi: "<p>94. पांच बॉक्स K, L, M, N और O हैं जिन्हें एक के ऊपर एक व्यवस्थित किया गया है। बॉक्स M को बॉक्स N के ऊपर रखा गया है। बॉक्स O को K के नीचे रखा गया है। बॉक्स N को K के ऊपर रखा गया है। बॉक्स L को O के नीचे रखा गया है। निम्नलिखित में से सबसे नीचे वाला बॉक्स कौन सा है?</p>",
                    options_en: ["<p>O</p>", "<p>L</p>", 
                                "<p>K</p>", "<p>M</p>"],
                    options_hi: ["<p>O</p>", "<p>L</p>",
                                "<p>K</p>", "<p>M</p>"],
                    solution_en: "<p>94.(b) After arranging the persons as persons as per directions given in question, we get following arrangement :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623025288.png\" alt=\"rId34\"><br>Clearly, we can see that L is the bottom most box.</p>",
                    solution_hi: "<p>94.(b) व्यक्तियों को प्रश्न में दिए गए निर्देशों के अनुसार व्यवस्थित करने के बाद, हमें निम्नलिखित व्यवस्था मिलती है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737623025394.png\" alt=\"rId35\"><br>स्पष्ट रूप से, हम देख सकते हैं कि L सबसे नीचे का डिब्बा है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. Which of the following is an input device ?</p>",
                    question_hi: "<p>95. निम्न में से कौन एक इनपुट उपकरण है ?</p>",
                    options_en: ["<p>Inkjet printer</p>", "<p>Optical character reader</p>", 
                                "<p>Headphone</p>", "<p>Projector</p>"],
                    options_hi: ["<p>इंकजेट प्रिंटर</p>", "<p>ऑप्टिकल कैरेक्टर रीडर</p>",
                                "<p>हेडफोन</p>", "<p>प्रोजेक्टर</p>"],
                    solution_en: "<p>95. (b) <strong>Optical character reader.</strong> It detects pencil marks or any printed text and converts them into computer -readable codes. An inkjet printer (Output device) is a computer peripheral that produces hard copies of a text document or photo by spraying droplets of ink onto paper. A projector (Output device) or image projector is an optical device that projects an image onto a surface, commonly a projection screen. Headphones are a type of hardware output device that can be connected to a computer\'s line-out or speakers port, as well as wirelessly using Bluetooth.</p>",
                    solution_hi: "<p>95.(b) <strong>ऑप्टिकल कैरेक्टर रीडर ।</strong> एक ऐसा उपकरण है जो पेंसिल के निशान या किसी मुद्रित टेक्स्ट का पता लगाता है और उन्हें कंप्यूटर पठनीय (readable) कोड में परिवर्तित करता है। एक इंकजेट प्रिंटर (आउटपुट डिवाइस) एक कंप्यूटर परिधीय है जो कागज पर स्याही की बूंदों को छिड़ककर एक टेक्स्ट दस्तावेज़ या फोटो की हार्ड कॉपी तैयार करता है। प्रोजेक्टर (आउटपुट डिवाइस) या इमेज प्रोजेक्टर एक ऑप्टिकल डिवाइस है जो एक छवि को सतह पर प्रोजेक्ट करता है, आमतौर पर एक प्रोजेक्शन स्क्रीन। हेडफ़ोन एक प्रकार का हार्डवेयर आउटपुट डिवाइस है जिसे कंप्यूटर के लाइन-आउट या स्पीकर पोर्ट से कनेक्ट किया जा सकता है, साथ ही ब्लूटूथ का उपयोग करके वायरलेस तरीके से भी जोड़ा जा सकता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. If the interest is compounded half-yearly, what will a sum of ₹ 16,000 become after one year at 20% per annum compounded annually ?</p>",
                    question_hi: "<p>96. यदि ब्याज की गणना अर्द्ध - वार्षिक चक्रवृद्धि आधार पर होती है, तो ₹16,000 की धनराशि 20% वार्षिक चक्रवृद्धि ब्याज की दर से एक वर्ष बाद कितनी हो जाएगी ?</p>",
                    options_en: ["<p>₹19,480</p>", "<p>₹19,720</p>", 
                                "<p>₹19,360</p>", "<p>₹19,200</p>"],
                    options_hi: ["<p>₹19,480</p>", "<p>₹19,720</p>",
                                "<p>₹19,360</p>", "<p>₹19,200</p>"],
                    solution_en: "<p>96.(c) Rate of interest for 6 months <br>= <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 10%<br>And time according to rate <br>= 1 &times; 2 = 2 years<br>So, amount after 1 year <br>= 16000 &times; <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>100</mn></mfrac></math> = ₹ 19360</p>",
                    solution_hi: "<p>96.(c)<br>6 महीने के लिए ब्याज दर = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 10%<br>तथा, दर के अनुसार समय = 1 &times; 2 = 2 वर्ष<br>इसलिए, एक वर्ष बाद राशि <br>= 16000 &times; <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>100</mn></mfrac></math> = ₹19360</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. Study the following arrangement carefully to answer these questions ?</p>\n<p>(LEFT) 2 8 3 6 0 8 1 5 3 7 3 4 9 6 2 4 3 2 5 6 4 0 7 (RIGHT)<br>Based on the arrangement given. Three of the following four groups of numbers (given in options) are alike in a certain way and so form a group. Which option does NOT belong to that group?</p>",
                    question_hi: "<p>97.&nbsp;इन प्रश्नों का उत्तर देने के लिए निम्नलिखित व्यवस्था का ध्यानपूर्वक अध्ययन करें?<strong id=\"docs-internal-guid-28b7caaf-7fff-8b5b-5245-3afa0984803a\"></strong></p>\n<p>(बाएं) 2 8 3 6 0 8 1 5 3 7 3 4 9 6 2 4 3 2 5 6 4 0 7 (दाएं).&nbsp;<br>दी गई व्यवस्था के आधार पर, संख्याओं के निम्नलिखित चार समूहों में से तीन (विकल्पों में दिए गए) एक निश्चित तरीके से समान हैं और इसलिए एक समूह बनाते हैं। कौन सा विकल्प उस समूह से संबंधित नहीं है?</p>",
                    options_en: ["<p>013</p>", "<p>462</p>", 
                                "<p>746</p>", "<p>230</p>"],
                    options_hi: ["<p>013</p>", "<p>462</p>",
                                "<p>746</p>", "<p>230</p>"],
                    solution_en: "<p>97.(b) (LEFT) 2 8 3 6 0 8 1 5 3 7 3 4 9 6 2 4 3 2 5 6 4 0 7 (RIGHT)<br><strong>Rule :</strong> digits are placed alternately.<br>But 462 does not follow the rule.</p>",
                    solution_hi: "<p>97.(b) (बाएं) 2 8 3 6 0 8 1 5 3 7 3 4 9 6 2 4 3 2 5 6 4 0 7 (दाएं)<br><strong>नियम : </strong>अंकों को वैकल्पिक रूप से रखा जाता है। लेकिन 462 नियम का पालन नहीं करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. The first Indian Communication satellite is</p>",
                    question_hi: "<p>98. पहला भारतीय संचार उपग्रह कौन सा है?</p>",
                    options_en: ["<p>Aryabhata</p>", "<p>Rohini</p>", 
                                "<p>MOM</p>", "<p>APPLE</p>"],
                    options_hi: ["<p>आर्यभट्ट</p>", "<p>रोहिणी</p>",
                                "<p>MOM</p>", "<p>एप्प्ल</p>"],
                    solution_en: "<p>98.(d) <strong>APPLE</strong> (Ariane Passenger Pay load Experiment):- It was successfully launched by Ariane-1, from Kourou, French Guiana on 19 June 1981. Aryabhatta is India\'s first satellite. Mars Orbiter Mission (MOM) is India\'s first Mars mission. Rohini (July 18, 1980 ) launched from Sriharikota Range. India\'s first satellite launched from an Indian -made launch vehicle.</p>",
                    solution_hi: "<p>98.(d) <strong>एप्पल</strong> (एरियन पैसेंजर पेलोड एक्सपेरिमेंट):- इसे 19 जून 1981 को कौरौ, फ्रेंच गुयाना से एरियान-1 द्वारा सफलतापूर्वक लॉन्च किया गया था। आर्यभट्ट भारत का पहला उपग्रह है। मार्स ऑर्बिटर मिशन (MOM) भारत का पहला मंगल मिशन है। रोहिणी (जुलाई 18, 1980) को श्रीहरिकोटा रेंज से लॉन्च किया गया। भारत का पहला उपग्रह भारतीय निर्मित प्रक्षेपण यान से प्रक्षेपित किया गया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. Ramesh invested ₹1,232 at 5% p.a. rate of simple interest in a bank. What amount will he get after 3 years?</p>",
                    question_hi: "<p>99. रमेश ने किसी बैंक में 5% वार्षिक साधारण ब्याज दर पर ₹1,232 की राशि निवेश की। 3 वर्ष बाद, उसे प्राप्त होने वाला मिश्रधन ज्ञात कीजिए ।</p>",
                    options_en: ["<p>₹1,285.80</p>", "<p>₹2,145.80</p>", 
                                "<p>₹1,848.80</p>", "<p>₹1,416.80</p>"],
                    options_hi: ["<p>₹1,285.80</p>", "<p>₹2,145.80</p>",
                                "<p>₹1,848.80</p>", "<p>₹1,416.80</p>"],
                    solution_en: "<p>99.(d) Principal = ₹1232 , Rate%&nbsp;= 5% , Time = 3 years<br>S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1232</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = ₹184.80<br>Amount = P + S.I = 1232 + 184.80 <br>= ₹1,416.80</p>",
                    solution_hi: "<p>99.(d)<br>मूलधन = ₹1232, दर% = 5%, समय = 3 वर्ष<strong id=\"docs-internal-guid-6bf05789-7fff-6402-5bf2-c3646ce06f93\"> </strong><br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1232</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = ₹184.80<br>मिश्रधन = P + S.I = 1232 + 184.80 <br>= ₹1,416.80</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.&nbsp;Study the given digit-letter-symbol sequence carefully and answer the question that follows.&nbsp;<strong id=\"docs-internal-guid-b73cbc3d-7fff-7672-585c-d425ec14e739\"></strong></p>\n<p>(Left) 6 $ + S r 3 B 6 A * ? # c &lt; Z R &amp; d &amp; G ! q (Right)<br>If the given sequence is written in the reverse order, then which element will be <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mi>th</mi></msup></math> to the left of the <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mi>th</mi></msup></math> element from the right end?</p>",
                    question_hi: "<p>100. दिए गए अंक-अक्षर-प्रतीक अनुक्रम का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।&nbsp;</p>\n<p>(बाएँ) 6 $ + S r 3 B 6 A * ? # c &lt; Z R &amp; d &amp; G ! q (दाएँ)<br>यदि दिए गए अनुक्रम को विपरीत क्रम में लिखा जाता है, तो कौन सा पद दायें छोर से 8वें पद के बाएं से 5वें स्थान पर होगा?</p>",
                    options_en: ["<p>B</p>", "<p>c</p>", 
                                "<p>+</p>", "<p>Z</p>"],
                    options_hi: ["<p>B</p>", "<p>c</p>",
                                "<p>+</p>", "<p>Z</p>"],
                    solution_en: "<p>100.(b)<br>(Left) 6 $ + S r 3 B 6 A * ? # c &lt; Z R &amp; d &amp;G ! q (Right)<br>If this is written in reverse order then simply left and right interchanges as <br>(Right) 6 $ + S r 3 B 6 A * ? # c &lt; Z R &amp; d &amp; G ! q (Left)<br>Required element is c .</p>",
                    solution_hi: "<p>100.(b)<br>(बाएं) 6 $ + S r 3 B 6 A * ? # c &lt; Z R &amp; d &amp; G ! q (दाएं)<br>यदि इसे उल्टे क्रम में लिखा जाता है, तो बस बाएँ और दाएँ आपस में बदल जाते हैं:<br>(दाएं) 6 $ + S r 3 B 6 A * ? # c &lt; Z R &amp; d &amp; G ! q (बाएं)<br>आवश्यक पद c है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>