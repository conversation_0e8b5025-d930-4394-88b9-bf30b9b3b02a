<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "1. P and Q together can complete a piece of work in 6 days. If P can alone complete the work in 18 days, then the number of days required for Q to finish the work is:",
                    question_hi: "1. P और Q मिलकर एक कार्य को 6 दिनों में पूरा कर सकते हैं। यदि P अकेले 18 दिनों में कार्य पूरा कर सकता है, तो Q को कार्य पूरा करने के लिए कितने दिनों की आवश्यकता होगी?",
                    options_en: [" 10 days", "  8 days", 
                                "  11days", " 9 days"],
                    options_hi: ["  10 दिन", "  8 दिन",
                                "  11दिन", " 9 दिन"],
                    solution_en: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535012591.png\" alt=\"rId4\" width=\"200\"><br>Efficiency of Q = 3 - 1 = 2<br>Time taken to complete the work by Q alone = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mo>=</mo><mn>9</mn></math> days</p>",
                    solution_hi: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535012761.png\" alt=\"rId5\" width=\"200\"><br>Q की दक्षता = 3 - 1 = 2<br>अकेले Q द्वारा कार्य पूरा करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mo>=</mo><mn>9</mn></math> दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "2. Ram can copy 60 pages in 15 hours. If Ram and Riya together can copy 180 pages in 30 hours, then in how many hours can Riya copy 20 pages?",
                    question_hi: "2. राम 15 घंटे में 60 पेज कॉपी कर सकता है। यदि राम और रिया मिलकर 30 घंटे में 180 पेज कॉपी कर सकते हों, तो रिया 20 पेज कितने घंटे में कॉपी कर सकती है? ",
                    options_en: [" 29 hours ", " 12 hours", 
                                " 20 hours ", " 10 hours"],
                    options_hi: [" 29 घंटे ", "<p>12 घंटे</p>",
                                "<p>20 घंटे</p>", "<p>10 घंटे</p>"],
                    solution_en: "<p>2.(d)<br>Ram can copy <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 4 pages per hour . <br>Ram and Riya together can copy <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = 6 pages per hour ,<br>So, riya can copy 2 pages per hour .<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 10 hours</p>",
                    solution_hi: "<p>2.(d)<br>राम प्रति घंटे <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 4 पेज कॉपी कर सकता है।<br>राम और रिया मिलकर <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = 6 पेज प्रति घंटा कॉपी कर सकते हैं,<br>तो, रिया प्रति घंटे 2 पेज कॉपी कर सकती है।<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 10 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. 4 women and 7 men earn a total of ₹11,480 in 7 days, while 10 women and 17 men earn a total of 36,360 in 9 days. How much will 11 women and 9 men together earn (in ₹) in 13 days?</p>",
                    question_hi: "<p>3. 4 महिलाएं और 7 पुरुष 7 दिन में कुल ₹11,480 कमाते हैं, जबकि 10 महिलाएं और 17 पुरुष 9 दिन में कुल ₹ 36,360 कमाते हैं। 11 महिलाएं और 9 पुरुष मिलकर 13 दिन में कितना (₹ में) कमाएंगे?</p>",
                    options_en: ["<p>42770</p>", "<p>42640</p>", 
                                "<p>42510</p>", "<p>42900</p>"],
                    options_hi: ["<p>42770</p>", "<p>42640</p>",
                                "<p>42510</p>", "<p>42900</p>"],
                    solution_en: "<p>3.(b)&nbsp;According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>4</mn><mi mathvariant=\"normal\">W</mi><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">M</mi><mo>)</mo><mo>&#215;</mo><mn>7</mn></mrow><mrow><mo>(</mo><mn>10</mn><mi mathvariant=\"normal\">W</mi><mo>+</mo><mn>17</mn><mi mathvariant=\"normal\">M</mi><mo>)</mo><mo>&#215;</mo><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11480</mn><mn>36360</mn></mfrac></math> </p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>4</mn><mi mathvariant=\"normal\">W</mi><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">M</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>10</mn><mi mathvariant=\"normal\">W</mi><mo>+</mo><mn>17</mn><mi mathvariant=\"normal\">M</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>41</mn><mn>101</mn></mfrac></math><br>101 &times; (4W + 7M) = 41 &times; (10W + 17M)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">W</mi><mi mathvariant=\"normal\">M</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math><br>Now, (4W + 7M) &times; 7 = (4 &times; 5 + 7 &times; 3) &times; 7 = 287 units<br>287 unit = ₹ 11480 <math display=\"inline\"><mo>&#8658;</mo></math> 1 unit = ₹40<br>So, 11 women and 9 men together earn in 13 days <br>= (11W + 9M) &times; 13 = (11 &times; 5 + 9 &times; 3) &times; 13 = 82 &times; 13 = 1066 units<br>1066 units = 1066 &times; 40 = ₹ 42,640</p>",
                    solution_hi: "<p>3.(b)<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>4</mn><mi mathvariant=\"normal\">W</mi><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">M</mi><mo>)</mo><mo>&#215;</mo><mn>7</mn></mrow><mrow><mo>(</mo><mn>10</mn><mi mathvariant=\"normal\">W</mi><mo>+</mo><mn>17</mn><mi mathvariant=\"normal\">M</mi><mo>)</mo><mo>&#215;</mo><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11480</mn><mn>36360</mn></mfrac></math></p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>4</mn><mi mathvariant=\"normal\">W</mi><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">M</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>10</mn><mi mathvariant=\"normal\">W</mi><mo>+</mo><mn>17</mn><mi mathvariant=\"normal\">M</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>41</mn><mn>101</mn></mfrac></math><br>101 &times; (4W + 7M) = 41 &times; (10W + 17M)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">W</mi><mi mathvariant=\"normal\">M</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math><br>अब , (4W + 7M) &times; 7 = (4 &times; 5 + 7 &times; 3) &times; 7 = 287 इकाई<br>287 इकाई = ₹ 11480 <math display=\"inline\"><mo>&#8658;</mo></math> 1 इकाई= ₹40<br>तो, 11 महिलाएं और 9 पुरुष मिलकर 13 दिनों में कमाते हैं<br>= (11W + 9M) &times; 13 = (11 &times; 5 + 9 &times; 3) &times; 13 = 82 &times; 13 = 1066 इकाई<br>1066 इकाई = 1066 &times; 40 = ₹ 42,640</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "4. A can complete a work in 9 days and B in 12 days. If they work together on it for 5 days, then the fraction of the work left is:",
                    question_hi: "<p>4. A एक कार्य को 9 दिनों में और B, 12 दिनों में पूरा कर सकता है। यदि वे इस पर 5 दिनों तक एक साथ मिलकर कार्य करते हैं, तो कार्य का शेष भाग______है।</p>",
                    options_en: [" <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math> ", "  <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535012886.png\" alt=\"rId6\" width=\"300\"><br>Work done in 5 days by A and B together = 5 &times; (4 + 3) = 35 units <br>Remaining work = 36 -&nbsp;35 = 1 unit<br>So, required fraction of work left = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535013012.png\" alt=\"rId7\" width=\"300\"><br>A और B द्वारा 5 दिनों में किया गया कार्य = 5 &times; (4 + 3) = 35 इकाई<br>शेष कार्य = 36 -&nbsp;35 = 1 इकाई <br>अत:, कार्य का आवश्यक अंश शेष है = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. A, B and C together can complete a work in 12 days. A as well as B alone can do the same work in 36 days. In how many days can C alone complete the same work?",
                    question_hi: "<p>5. A, B और C मिलकर एक कार्य 12 दिन में पूरा कर सकते हैं। A और B अकेले उसी कार्य को 36 दिन में पूरा कर सकते हैं। C अकेले उसी कार्य को कितने दिन में पूरा कर सकता है?</p>",
                    options_en: [" 48 ", "<p>25</p>", 
                                "<p>12</p>", "<p>36</p>"],
                    options_hi: ["<p>48</p>", "<p>25</p>",
                                "<p>12</p>", "<p>36</p>"],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535013285.png\" alt=\"rId8\" width=\"250\"><br>Efficiency of C = 3 -&nbsp;(1 + 1) = 1 unit<br>So, whole work done by C alone = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 36 days</p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535013546.png\" alt=\"rId9\" width=\"250\"><br>C की दक्षता = 3 -&nbsp;(1 + 1) = 1 इकाई&nbsp;<br>अतः, C द्वारा अकेले कार्य पूरा करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 36 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "6. 6 labourers can finish a work in 16 days. 10 labourers are available, but the work is to be finished by 8 days. How many more labourers are to be called to finish the work in time?",
                    question_hi: "<p>6. 6 मजदूर किसी काम को 16 दिनों में पूरा कर सकते हैं। काम के लिए 10 मजदूर उपलब्ध हैं, लेकिन ये काम 8 दिन में खत्म करना है। इस काम को समय पर पूरा करने के लिए और कितने मजदूरों को बुलाया जाना है?</p>",
                    options_en: [" 2 ", " 0", 
                                " 4", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>0</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>6.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><mo>&#215;</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><mo>=</mo><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><mo>&#215;</mo><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub></math><br>6 &times; 16 = (10 + x) &times; 8<br>8x = 96 - 80&nbsp;<br>x = 2 more labour needed.</p>",
                    solution_hi: "<p>6.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><mo>&#215;</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><mo>=</mo><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><mo>&#215;</mo><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub></math><br>6 &times; 16 = (10 + x) &times; 8<br>8x = 96 - 80&nbsp;<br>x = 2 और श्रमिकों की आवश्यकता होगी ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7. R, S and T can finish a work in 20, 15 and 10 days, respectively. R works on all days and S and T work on alternate days with T starting the work on the first day. In how many days is the work finished?",
                    question_hi: "<p>7. R, S और T एक कार्य को क्रमशः 20, 15 और 10 दिनों में पूरा कर सकते हैं। R शुरू से लेकर अंत तक कार्य करता है और S और T एकांतर दिनों में बारी-बारी से कार्य करते हैं, जिसमें T पहले दिन से कार्य शुरू करता है। कार्य कितने दिनों में पूरा होता है?</p>",
                    options_en: [" <math display=\"inline\"><mfrac><mrow><mn>61</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> ", "  <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>52</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>57</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>61</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>52</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>57</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535013740.png\" alt=\"rId10\" width=\"300\"><br>According to question,<br>Series of work = (R + T),(R + S),(R + T)......<br>Work done in 1 cycle (2 days) = (3 + 6) + (3 + 4) = 16 units<br>Work done in 3 cycle (6 days) = 16 &times; 3 = 48 units<br>Remaining work = 60 -&nbsp;48 = 12 units<br>7th day work done by (R + T) = 9 units<br>Remaining work = 12 -&nbsp;9 = 3 units<br>Required time = 6 + 1 + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>7</mn></mfrac></math> days</p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535013864.png\" alt=\"rId11\" width=\"300\"><br>प्रश्न के अनुसार,<br>कार्य चक्र = (R + T),(R + S),(R + T)......<br>1 चक्र (2 दिन) में किया गया कार्य = (3 + 6) + (3 + 4) = 16 इकाई<br>3 चक्र (6 दिन) में किया गया कार्य = 16 &times; 3 = 48 इकाई<br>शेष कार्य = 60 -&nbsp;48 = 12 इकाई<br>7वें दिन (R + T) द्वारा किया गया कार्य = 9 इकाई<br>शेष कार्य = 12 -&nbsp;9 = 3 इकाई<br>आवश्यक समय = 6 + 1 + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>7</mn></mfrac></math> दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "8. 4 men and 6 women can complete a work in 8 days, while 3 men and 7 women can complete it in 10 days. In how many days will 10 women complete it?",
                    question_hi: "8. 4 पुरुष और 6 महिलाएं एक कार्य को 8 दिनों में पूरा कर सकती हैं, जबकि 3 पुरुष और 7 महिलाएं उसी कार्य को 10 दिनों में पूरा कर सकती हैं। 10 महिलाएं इस कार्य को कितने दिनों में पूरा करेंगी ?",
                    options_en: [" 44 ", " 40", 
                                " 15", " 16"],
                    options_hi: [" 44 ", " 40",
                                " 15", " 16"],
                    solution_en: "<p>8.(b)&nbsp;Let efficiency of man be M and efficiency of woman be W.<br>According to question,<br>(4M + 6W) &times; 8 = (3M + 7W) &times; 10<br>32M + 48W = 30M + 70W<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">M</mi><mi mathvariant=\"normal\">W</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>1</mn></mfrac></math><br>Total work = (4 &times; 11 + 6 &times; 1) &times; 8 = 400<br>time required to complete work by 10 women = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>10</mn><mo>&#215;</mo><mn>1</mn></mrow></mfrac></math> = 40 days</p>",
                    solution_hi: "<p>8.(b)&nbsp;माना पुरुष की दक्षता M और महिला की दक्षता W है।<br>प्रश्न के अनुसार,<br>(4M + 6W) &times; 8 = (3M + 7W) &times; 10<br>32M + 48W = 30M + 70W<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">M</mi><mi mathvariant=\"normal\">W</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>1</mn></mfrac></math><br>कुल कार्य = (4 &times; 11 + 6 &times; 1) &times; 8 = 400<br>10 महिलाओं द्वारा कार्य पूरा करने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>10</mn><mo>&#215;</mo><mn>1</mn></mrow></mfrac></math>&nbsp;= 40 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. 4 men and 6 women can complete a work in 8 days, while 3 men and 7 women can complete it in 10 days. In how many days will 25 women complete it?",
                    question_hi: "<p>9. 4 पुरुष और 6 महिलाएँ एक काम को 8 दिनों में पूरा कर सकते हैं, जबकि 3 पुरुष और 7 महिलाएँ 10 दिनों में पूरा कर सकते हैं। 25 महिलाएँ इसे कितने दिनों में पूरा करेंगी?</p>",
                    options_en: [" 20 ", " 16 ", 
                                " 25", "<p>18</p>"],
                    options_hi: ["<p>20</p>", "<p>16</p>",
                                "<p>25</p>", "<p>18</p>"],
                    solution_en: "<p>9.(b)&nbsp;(4M + 6W) &times; 8 = (3M + 7W) &times; 10<br>2M = 22W <math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">M</mi><mi mathvariant=\"normal\">W</mi></mfrac><mo>=</mo><mfrac><mn>11</mn><mn>1</mn></mfrac></math><br>Total work = (4M + 6W) &times; 8<br>= (4 &times; 11 + 6 &times; 1) &times; 8 = 400 units<br>Required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>25</mn><mo>&#215;</mo><mn>1</mn></mrow></mfrac></math> = 16 days</p>",
                    solution_hi: "<p>9.(b)&nbsp;(4M + 6W) &times; 8 = (3M + 7W) &times; 10<br>2M = 22W <math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">M</mi><mi mathvariant=\"normal\">W</mi></mfrac><mo>=</mo><mfrac><mn>11</mn><mn>1</mn></mfrac></math><br>कुल कार्य = (4M + 6W) &times; 8<br>= (4 &times; 11 + 6 &times; 1) &times; 8 = 400 units<br>आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>25</mn><mo>&#215;</mo><mn>1</mn></mrow></mfrac></math>&nbsp;= 16 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. A group of men decided to do a job in 13 days but 9 men left the work after each day. The work, as a result, got completed in 16 days. How many men were initially in the group?",
                    question_hi: "<p>10. पुरुषों के एक समूह ने 13 दिनों में एक कार्य करने का फैसला किया लेकिन प्रत्येक दिन के बाद 9 पुरुष कार्य छोड़ते गए। परिणामतः, कार्य 16 दिनों में पूरा हो पाया। प्रारंभ में समूह में कितने पुरुष थे?</p>",
                    options_en: [" 378 ", " 360 ", 
                                "<p>330</p>", "<p>380</p>"],
                    options_hi: ["<p>378</p>", "<p>360</p>",
                                "<p>330</p>", "<p>380</p>"],
                    solution_en: "<p>10.(b)&nbsp;Let the number of men be \'x\'&nbsp;, <br>x &times; 13 = x + (x - 9) + (x - 18) &hellip;&hellip;+ (x - 135)<br>13x&nbsp;= 16x - 1080<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1080</mn><mn>3</mn></mfrac></math> = 360 men</p>",
                    solution_hi: "<p>10.(b)&nbsp;माना कि पुरुषों की संख्या \'x\' है ,<br>x &times; 13 = x + (x - 9) + (x - 18) &hellip;&hellip;+ (x - 135)<br>13x = 16x - 1080<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1080</mn><mn>3</mn></mfrac></math> = 360 पुरुष</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11. Raju and Rajat working together take 5 days to complete a piece of work. If Raju alone can do this work in 7 days, how long would Rajat take to complete the same work?",
                    question_hi: "<p>11. राजू और रजत एक साथ कार्य करते हुए एक कार्य को पूरा करने में 5 दिन का समय लेते हैं। यदि राजू अकेला इस कार्य को 7 दिन में कर सकता है, तो रजत उसी कार्य को कितने दिन में पूरा करेगा?</p>",
                    options_en: [" 18 days", " 16.5 days", 
                                " 17 days ", " 17.5 days"],
                    options_hi: ["<p>18 दिन</p>", "<p>16.5 दिन</p>",
                                "<p>17 दिन</p>", "<p>17.5 दिन</p>"],
                    solution_en: "<p>11.(d)&nbsp;(Raju + Rajat) &times; 5 days = Raju &times; 7 days&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Raju</mi><mi>Rajat</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> <br>Total work = Raju &times; 7 = 35 units<br>Required number of days = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 17.5 days</p>",
                    solution_hi: "<p>11.(d)&nbsp;(राजू + रजत) &times; 5 दिन = राजू &times; 7 दिन<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2352;&#2366;&#2332;&#2370;</mi><mi>&#2352;&#2332;&#2340;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math><br>कुल कार्य = राजू &times; 7 = 35 इकाई<br>आवश्यक दिनों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 17.5 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. P and Q together complete a job in 4<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days. R and S complete the same job in 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>9</mn></mfrac></math> days. If P, Q, R and S work together, how many days do they need to complete the same job?</p>",
                    question_hi: "<p>12. P और Q मिलकर किसी कार्य को 4<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन में पूरा करते हैं। R और S उसी कार्य को 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>9</mn></mfrac></math> दिन में पूरा करते हैं। यदि P, Q, R और S एक साथ कार्य करते हैं, तो उसी कार्य को पूरा करने के लिए उन्हें कितने दिनों की आवश्यकता होगी ?</p>",
                    options_en: ["<p>2<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>", 
                                "<p>1<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>2<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                                "<p>1<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535013985.png\" alt=\"rId12\" width=\"250\"><br>Time taken to complete whole work done by P + Q + R + S together =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mrow><mn>10</mn><mo>+</mo><mn>9</mn></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>19</mn></mfrac></math> days</p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535014133.png\" alt=\"rId13\" width=\"250\"><br>P + Q + R +S द्वारा पूरा कार्य पूरा करने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mrow><mn>10</mn><mo>+</mo><mn>9</mn></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>19</mn></mfrac></math> दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A can finish a work in 60 days. He works at it for 15 days and then B alone finishes the remaining work in 48 days. In how much time can A and B working together finish the work?</p>",
                    question_hi: "<p>13. A किसी कार्य को 60 दिनों में पूरा कर सकता है। वह इस पर 15 दिनों तक काम करता है और फिर B अकेले शेष कार्य को 48 दिनों में पूरा करता है। A और B मिलकर काम करके उस कार्य को कितने समय में पूरा कर सकते हैं?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>171</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math> days</p>", 
                                "<p>51 days</p>", "<p>45 days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>171</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math> दिन</p>",
                                "<p>51 दिन</p>", "<p>45 दिन</p>"],
                    solution_en: "<p>13.(a)&nbsp;According to the question,<br>A &times; 60 = A &times; 15 + B &times; 48 <math display=\"inline\"><mo>&#8658;</mo></math> 45 A = 48 B<br>Efficiency <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">B</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>15</mn></mfrac></math><br>Now, total work = 60 &times; efficiency of A = 60 &times; 16 = 960 Units <br>So, Time taken by A and B together to complete the work = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mrow><mn>16</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>31</mn></mfrac></math>days</p>",
                    solution_hi: "<p>13.(a)<br>प्रश्न के अनुसार,<br>A &times; 60 = A &times; 15 + B &times; 48 <math display=\"inline\"><mo>&#8658;</mo></math> 45 A = 48 B<br>दक्षता <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">B</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>15</mn></mfrac></math><br>अब, कुल कार्य = 60 &times; A की दक्षता = 60 &times; 16 = 960 इकाइयाँ<br>तो, A और B द्वारा कार्य पूरा करने में लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mrow><mn>16</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>31</mn></mfrac></math>दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A can complete <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of a work in 7 days and B can complete <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> of the same work in 10 days. In how many days can both A and B together complete the work?</p>",
                    question_hi: "<p>14. A किसी कार्य का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> भाग 7 दिन में पूरा कर सकता है और B उसी कार्य का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> भाग 10 दिन में पूरा कर सकता है। A और B दोनों मिलकर उस कार्य को कितने दिन में पूरा कर सकते हैं?</p>",
                    options_en: [" 12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>  ", "<p>11<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>14.(c)<br>Time taken by A to complete the work = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = 21 days<br>Time taken by B to complete the work = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></mrow></mfrac></math> = 35 days<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535014273.png\" alt=\"rId14\" width=\"200\"><br>So, Time taken by A and B together to complete the work = <math display=\"inline\"><mfrac><mrow><mn>105</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 13<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> days</p>",
                    solution_hi: "<p>14.(c)<br>A द्वारा पूरा काम करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = 21 दिन<br>B द्वारा पूरा काम करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></mrow></mfrac></math> = 35 दिन<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535014429.png\" alt=\"rId15\" width=\"200\"><br>अतः, A और B द्वारा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>105</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 13<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. A can do&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of a piece of work in 32 days, B can do 37<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>% of the same work in 24 days, while C can do 60% of the same work in 48 days. B and C together started and worked for x days. After x days, B left the work and A joined C and both completed the remaining work in (x + 8) days. If the ratio of the work done by (B + C) together to the work done by (A + C) together is 9 : 11, then what fraction of the same work can be completed by C alone in 3.5 x days?</p>",
                    question_hi: "<p>15 A किसी कार्य का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> भाग 32 दिन में कर सकता है, B उसी कार्य का 37<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%, कार्य 24 दिन में कर सकता है, जबकि C उसी कार्य का 60% कार्य 48 दिन में कर सकता है। B और C ने मिलकर काम करना शुरू किया और x दिन तक कार्य किया। x दिन के बाद, B ने कार्य छोड़ दिया और A, C के साथ शामिल हुआ और दोनों ने शेष कार्य को (x + 8) दिन में पूरा किया। यदि (B + C) द्वारा मिलकर किए गए कार्य और (A + C) द्वारा मिलकर किए गए कार्य का अनुपात 9 : 11 है, तो 3.5x दिन में अकेले C द्वारा उस कार्य का कितना भाग पूरा किया जा सकता है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>15.(c)<br>Time taken by A to complete the work = <math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = 96 days <br>Time taken by B to complete the work = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></mrow></mfrac></math> = 64 days <br>Time taken by C to complete the work = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></mfrac></math> = 80 days <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535014569.png\" alt=\"rId16\" width=\"250\"><br>According to the question,<br>(15 + 12)x + 22 &times; (x + 8) = 960<br>49<math display=\"inline\"><mi>x</mi></math> + 176 = 960<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>784</mn><mn>49</mn></mfrac></math> = 16<br>Now, 3.5 &times; x&nbsp;= 3.5 &times; 16 = 56 days <br>Hence, work done in 56 days by C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>960</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math>th part</p>",
                    solution_hi: "<p>15.(c)<br>A द्वारा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = 96 दिन&nbsp;<br>B द्वारा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></mrow></mfrac></math> = 64 दिन<br>C द्वारा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></mfrac></math> = 80 दिन<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723535014677.png\" alt=\"rId17\"><br>प्रश्न के अनुसार,<br>(15 + 12)x + 22 &times; (x + 8) = 960<br>49x&nbsp;+ 176 = 960<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>784</mn><mn>49</mn></mfrac></math> = 16<br>अब , 3.5 &times; x&nbsp;= 3.5 &times; 16 = 56 दिन<br>अतः, C द्वारा 56 दिनों में किया गया कार्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>960</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math>वां भाग</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>