<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Good grief</span></strong></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Good grief</span></strong></p>\n",
                    options_en: ["<p>To dig a hole</p>\n", "<p>To be very systematic</p>\n", 
                                "<p>To try everything possible</p>\n", "<p>An expression of surprise or frustration</p>\n"],
                    options_hi: ["<p>To dig a hole</p>\n", "<p>To be very systematic</p>\n",
                                "<p>To try everything possible</p>\n", "<p>An expression of surprise or frustration</p>\n"],
                    solution_en: "<p>1.(d) <strong>Good grief</strong> - <span style=\"font-family: Cambria Math;\">An expression of surprise or frustration</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g. Oh God, good grief we are again stuck in a traffic jam. </span></p>\n",
                    solution_hi: "<p>1.(d) <strong>Good grief </strong>- <span style=\"font-family: Cambria Math;\">An expression of surprise or frustration/</span><span style=\"font-family: Nirmala UI;\">&#2310;&#2358;&#2381;&#2330;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2366;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2367;&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> - Oh God, good grief we are again stuck in a traffic jam./</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2327;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2360;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2376;&#2347;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Temerity</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Temerity</span></strong></p>\n",
                    options_en: ["<p>Diffidence</p>\n", "<p>Audacity</p>\n", 
                                "<p>Assurance</p>\n", "<p>Poison</p>\n"],
                    options_hi: ["<p>Diffidence</p>\n", "<p>Audacity</p>\n",
                                "<p>Assurance</p>\n", "<p>Poison</p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Temerity </strong>- </span><span style=\"font-family: Cambria Math;\">To do with confidence and boldness</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">diffidence&nbsp; - modesty or shyness resulting from a lack of&nbsp; self - confidence</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Audacity - courage or confidence of a kind that other people find shocking </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Assurance - certainty about something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Poison - a substance that by its chemical action can kill or injure a living thing</span></p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Temerity(</span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2366;&#2357;&#2354;&#2366;&#2346;&#2344;</span></strong><span style=\"font-family: Cambria Math;\"><strong>)</strong> - </span><span style=\"font-family: Cambria Math;\">To do with confidence and boldness</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">Diffidence(</span>&#2310;&#2340;&#2381;&#2350;&#2360;&#2306;&#2358;&#2351;)-modesty or shyness resulting from a lack of&nbsp; self - confidence</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Audacity(</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2361;&#2360;</span><span style=\"font-family: Cambria Math;\">) - courage or confidence of a kind that other people find shocking </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Assurance(</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\">) - certainty about something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Poison(</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;</span><span style=\"font-family: Cambria Math;\">) - a substance that by its chemical action can kill or injure a living thing</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> Some parts of a few sentences have been jumbled up, and labelled A, B, C and D. </span><span style=\"font-family: Cambria Math;\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The Ramayana is one of the largest ancient</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>A. </strong>24,000 verses divided into seven Khanda (parts)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>B. </strong>the first and the seventh being later additions.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>C.</strong> epics in world literature. It consists of nearly</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>D.</strong> It belongs to the genre of Itihasa, narratives of past events, interspersed with teachings on the goals of human life.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> Some parts of a few sentences have been jumbled up, and labelled A, B, C and D. </span><span style=\"font-family: Cambria Math;\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The Ramayana is one of the largest ancient</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>A. </strong>24,000 verses divided into seven Khanda (parts)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>B. </strong>the first and the seventh being later additions.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>epics in world literature. It consists of nearly</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>D. </strong>It belongs to the genre of Itihasa, narratives of past events, interspersed with teachings on the goals of human life.</span></p>\n",
                    options_en: ["<p>DCBA</p>\n", "<p>CABD</p>\n", 
                                "<p>BDCA</p>\n", "<p>ABDC</p>\n"],
                    options_hi: ["<p>DCBA</p>\n", "<p>CABD</p>\n",
                                "<p>BDCA</p>\n", "<p>ABDC</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(b)</span><span style=\"font-family: Cambria Math;\"> CABD</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence C will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family: Cambria Math;\">Ramayana is one of the largest ancient epics in world literature. </span><span style=\"font-family: Cambria Math;\">However, Sentence A states that it has </span><span style=\"font-family: Cambria Math;\">24,000 verses divided into seven Khanda</span><span style=\"font-family: Cambria Math;\">. So, A will follow C. Further, Sentence B states that </span><span style=\"font-family: Cambria Math;\">the first and the seventh Khanda being later additions </span><span style=\"font-family: Cambria Math;\">and Sentence D states that </span><span style=\"font-family: Cambria Math;\">it belongs to the genre of Itihasa</span><span style=\"font-family: Cambria Math;\">. So, D will follow B. Going through the options, option b has the correct sequence.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(b) </span><span style=\"font-family: Cambria Math;\"> CABD</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence C </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> parajumble </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;</span><span style=\"font-family: Cambria Math;\">Ramayana is one of the largest ancient epics in world literature&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;&#2366;&#2305;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, Sentence A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 24,000 </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, C, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">, Sentence B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2340;&#2357;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;&#2396;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence D </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2340;&#2367;&#2361;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2376;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, B, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">D </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> options </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> , option b </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Some parts of a few sentences have been jumbled up, and </span><span style=\"font-family: Cambria Math;\">labelled </span><span style=\"font-family: Cambria Math;\">A, B, C and D. </span><span style=\"font-family: Cambria Math;\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mr. Summers spoke frequently </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>A.</strong> the one that had been constructed when the first people settled down to make a village here</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>B. </strong>liked to upset even as much tradition as was represented by the black box. There was a</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>to the villagers about making a new box, but no one</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>D.</strong> story that the present box had been made with some pieces of the box that had preceded it</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Some parts of a few sentences have been jumbled up, and </span><span style=\"font-family: Cambria Math;\">labelled </span><span style=\"font-family: Cambria Math;\">A, B, C and D. </span><span style=\"font-family: Cambria Math;\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mr. Summers spoke frequently </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>A. </strong>the one that had been constructed when the first people settled down to make a village here</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>B.</strong> liked to upset even as much tradition as was represented by the black box. There was a</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>C.</strong> to the villagers about making a new box, but no one</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>D. </strong>story that the present box had been made with some pieces of the box that had preceded it</span></p>\n",
                    options_en: ["<p>BDAC</p>\n", "<p>DACB</p>\n", 
                                "<p>ACDB</p>\n", "<p>CBDA</p>\n"],
                    options_hi: ["<p>BDAC</p>\n", "<p>DACB</p>\n",
                                "<p>ACDB</p>\n", "<p>CBDA</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(d) </span><span style=\"font-family: Cambria Math;\"> CBDA</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence C will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family: Cambria Math;\">Mr. Summers spoke frequently to the villagers about making a new box. </span><span style=\"font-family: Cambria Math;\">However, Sentence B states that </span><span style=\"font-family: Cambria Math;\">no one liked to upset</span><span style=\"font-family: Cambria Math;\">. So, B will follow C. Further, Sentence D states that </span><span style=\"font-family: Cambria Math;\">there was a story that the present box had been made with some pieces of the box that had preceded it </span><span style=\"font-family: Cambria Math;\">and Sentence A states that </span><span style=\"font-family: Cambria Math;\"> the one that had been constructed when the first people settled down to make a village here</span><span style=\"font-family: Cambria Math;\">. So, A will follow D. Going through the options, option d has the correct sequence.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(d) </span><span style=\"font-family: Cambria Math;\"> CBDA</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence C </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> parajumble </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;</span><span style=\"font-family: Cambria Math;\">Mr. Summers spoke frequently to the villagers about making a new box&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;&#2366;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, Sentence B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2375;&#2358;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2360;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">, Sentence D </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2369;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence A </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2366;&#2305;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, D, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> options </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, option d </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Parts of the following sentence are given as options. Identify the part that contains a </span><span style=\"font-family: Cambria Math;\">grammatical error. If there is no error, select \"No error&rdquo;.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ashwathama, Drona\'s / only son, has had developed / a fondness for lavish living.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">Parts of the following sentence are given as options. Identify the part that contains a </span><span style=\"font-family: Cambria Math;\">grammatical error. If there is no error, select \"No error&rdquo;.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ashwathama, Drona\'s / only son, has had developed / a fondness for lavish living.</span></p>\n",
                    options_en: ["<p>No error</p>\n", "<p>a fondness for lavish living.</p>\n", 
                                "<p>only son, has had developed</p>\n", "<p>Ashwathama, Drona\'s</p>\n"],
                    options_hi: ["<p>No error</p>\n", "<p>a fondness for lavish living.</p>\n",
                                "<p>only son, has had developed</p>\n", "<p>Ashwathama, Drona\'s</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The given sentence is in the present perfect tense so the verb must be used in the present perfect form(has + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">) and not in the past tense(had). Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">Drona\'s only son, has developed(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> sentence present perfect tense </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> verb </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> present perfect form(has + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> past tense(had) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">Drona\'s only son, has developed(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\">Select the most appropriate \'one word\' for the expression given below. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Place where bees are kept.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\">Select the most appropriate \'one word\' for the expression given below. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Place where bees are kept.</span></p>\n",
                    options_en: ["<p>Hive</p>\n", "<p>Apiary</p>\n", 
                                "<p>Net</p>\n", "<p>Aviary</p>\n"],
                    options_hi: ["<p>Hive</p>\n", "<p>Apiary</p>\n",
                                "<p>Net</p>\n", "<p>Aviary</p>\n"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Apiary - Place where bees are kept</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hive - a structure where bees live</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Net - something that is used for catching or holding things</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Aviary - Place where birds are kept</span></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Apiary - Place where bees are kept/</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2350;&#2325;&#2381;&#2326;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hive - a structure where bees live/</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2369;&#2350;&#2325;&#2381;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2340;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Net(</span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">) - something that is used for catching or holding things</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Aviary(</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2325;&#2381;&#2359;&#2368;&#2358;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">) - Place where birds are kept</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the word that is closest in meaning (SYNONYM) to the word given below. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Frequent</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> Select the word that is closest in meaning (SYNONYM) to the word given below. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Frequent</span></strong></p>\n",
                    options_en: ["<p>Intermittent</p>\n", "<p>Broken</p>\n", 
                                "<p>Ceasing</p>\n", "<p>Inconsistent</p>\n"],
                    options_hi: ["<p>Intermittent</p>\n", "<p>Broken</p>\n",
                                "<p>Ceasing</p>\n", "<p>Inconsistent</p>\n"],
                    solution_en: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Frequent - happening at short intervals</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Broken - not functioning properly</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ceasing - To stop or end</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Inconsistent - something varying continuously</span></p>\n",
                    solution_hi: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Frequent(</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2344;&#2352;&#2366;&#2357;&#2352;&#2381;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\">) - happening at short intervals</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Broken(</span><span style=\"font-family: Nirmala UI;\">&#2335;&#2370;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2347;&#2370;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\">) - not functioning properly</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ceasing(</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2325;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - To stop or end</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Inconsistent(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2306;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\">) - something varying continuously</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Fill in the blank with an appropriate option.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I dump the tray on the side and start ______up the dishwasher.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">Fill in the blank with an appropriate option.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I dump the tray on the side and start ______up the dishwasher.</span></p>\n",
                    options_en: ["<p>tantalizing</p>\n", "<p>upbringing</p>\n", 
                                "<p>alienating</p>\n", "<p>loading</p>\n"],
                    options_hi: ["<p>tantalizing</p>\n", "<p>upbringing</p>\n",
                                "<p>alienating</p>\n", "<p>loading</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Loading</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">to put a quantity of something into or onto somebody/something</span><span style=\"font-family: Cambria Math;\">. The given sentence states that </span><span style=\"font-family: Cambria Math;\">I dump the tray on the side and start loading up the dishwasher</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">loading</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Loading</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2368;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2366;&#2354;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2375;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2370;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2358;&#2357;&#2377;&#2358;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2370;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">loading</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The actors </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">is to shoot</span> </span><span style=\"font-family: Cambria Math;\">the scene three times.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The actors </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">is to shoot</span> </span><span style=\"font-family: Cambria Math;\">the scene three times.</span></p>\n",
                    options_en: ["<p>No improvement</p>\n", "<p>had to shooting</p>\n", 
                                "<p>has to shoot</p>\n", "<p>had to shoot</p>\n"],
                    options_hi: ["<p>No improvement</p>\n", "<p>had to shooting</p>\n",
                                "<p>has to shoot</p>\n", "<p>had to shoot</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The given sentence talks about an action that happened in the past tense so it will have a verb in the past form(had). Hence, &lsquo;had to shoot&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> action </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> past tense </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> verb past form(had) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;had to shoot&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Select the most appropriate antonym of the give</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">TARDY</span></strong></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> Select the most appropriate antonym of the give</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">TARDY</span></strong></p>\n",
                    options_en: ["<p>BELATED</p>\n", "<p>OVERDUE</p>\n", 
                                "<p>DELINQUENT</p>\n", "<p>PROMPT</p>\n"],
                    options_hi: ["<p>BELATED</p>\n", "<p>OVERDUE</p>\n",
                                "<p>DELINQUENT</p>\n", "<p>PROMPT</p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">TARDY - slow or late in happening or arriving</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">BELATED - coming or happening later than should have been</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">OVERDUE - not done or happened when expected or needed</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">DELINQUENT - usually a young person who regularly performs illegal or immoral acts</span></p>\n",
                    solution_hi: "<p>10.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">TARDY(</span><span style=\"font-family: Nirmala UI;\">&#2343;&#2368;&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\">) - slow or late in happening or arriving</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">BELATED(</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2354;&#2306;&#2348;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - coming or happening later than should have been</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">OVERDUE - not done or happened when expected or needed/</span><span style=\"font-family: Nirmala UI;\">&#2336;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2366;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">DELINQUENT(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2352;&#2366;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2357;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> ) - usually a young person who regularly performs illegal or immoral acts</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Choose the option that is the correct indirect form of the sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;Who is it?&rdquo; Vikram asked, his euphoria fading.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\"> Choose the option that is the correct indirect form of the sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;Who is it?&rdquo; Vikram asked, his euphoria fading.</span></p>\n",
                    options_en: ["<p>With a faded euphoria Vikram asked who is it.</p>\n", "<p>As his euphoria faded, Vikram asks as to who was it.</p>\n", 
                                "<p>With his euphoria faded, Vikram asked who it is.</p>\n", "<p>With his euphoria fading, Vikram asked who it was.</p>\n"],
                    options_hi: ["<p>With a faded euphoria Vikram asked who is it.</p>\n", "<p>As his euphoria faded, Vikram asks as to who was it.</p>\n",
                                "<p>With his euphoria faded, Vikram asked who it is.</p>\n", "<p>With his euphoria fading, Vikram asked who it was.</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.(d)</span><span style=\"font-family: Cambria Math;\"> With his euphoria fading, Vikram asked who it was.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) With a faded euphoria Vikram asked who </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is</span></span><span style=\"font-weight: 400;\"> it.(Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) As his euphoria faded, Vikram </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">asks</span></span><span style=\"font-weight: 400;\"> as to who was it.(Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) With his euphoria </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">faded</span></span><span style=\"font-weight: 400;\">, Vikram asked who it is.(Incorrect Verb)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(d) </span><span style=\"font-family: Cambria Math;\">With his euphoria fading, Vikram asked who it was.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) With a faded euphoria Vikram asked who </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is</span></span><span style=\"font-weight: 400;\"> it.(&#2327;&#2354;&#2340; Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) As his euphoria faded, Vikram </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">asks</span></span><span style=\"font-weight: 400;\"> as to who was it.(&#2327;&#2354;&#2340; Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) With his euphoria </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">faded</span></span><span style=\"font-weight: 400;\">, Vikram asked who it is.(&#2327;&#2354;&#2340; Verb)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> Find the part of the given sentence that has an error in it. If there is no error, choose </span><span style=\"font-family: \'Cambria Math\';\">\'No error\'.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Democrats are often more receptive and accommodating of foreign people and cultures.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> Find the part of the given sentence that has an error in it. If there is no error, choose </span><span style=\"font-family: \'Cambria Math\';\">\'No error\'.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Democrats are often more receptive and accommodating of foreign people and cultures.</span></p>\n",
                    options_en: ["<p>No error</p>\n", "<p>Democrats are often more receptive</p>\n", 
                                "<p>foreign people and cultures.</p>\n", "<p>and accommodating of</p>\n"],
                    options_hi: ["<p>No error</p>\n", "<p>Democrats are often more receptive</p>\n",
                                "<p>foreign people and cultures.</p>\n", "<p>and accommodating of</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">We generally use the same form of the verb before and after the conjunction &lsquo;and&rsquo;. Hence, &lsquo;accommodating&rsquo; must be replaced with &lsquo;accommodative&rsquo; &amp; &lsquo;receptive and accommodative of&rsquo; becomes the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> conjunction &lsquo;and&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> verb </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, \'accommodating\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'accommodative\' </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'receptive and accommodative of\' </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">One who goes on foot</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">One who goes on foot</span></strong></p>\n",
                    options_en: ["<p>Hypochondriac</p>\n", "<p>Vigilant</p>\n", 
                                "<p>Pedestrian</p>\n", "<p>Cynic</p>\n"],
                    options_hi: ["<p>Hypochondriac</p>\n", "<p>Vigilant</p>\n",
                                "<p>Pedestrian</p>\n", "<p>Cynic</p>\n"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Pedestrian - One who goes on foot</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hypochondriac - a person who is always worried about his or her own health</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vigilant - keeping a careful watch for possible danger or difficulties</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cynic - a person who believes that people only do things for themselves, rather than to help others</span></p>\n",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Pedestrian(</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2376;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) - One who goes on foot</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hypochondriac(</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2381;&#2352;&#2350;&#2367;</span><span style=\"font-family: Cambria Math;\"> ) - a person who is always worried about his or her own health</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vigilant(</span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2327;&#2352;&#2370;&#2325;</span><span style=\"font-family: Cambria Math;\">) - keeping a careful watch for possible danger or difficulties</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cynic(</span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2306;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\">) - a person who believes that people only do things for themselves, rather than to help others</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Select the most appropriate antonym of the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Sporadic</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate antonym of the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Sporadic</span></strong></p>\n",
                    options_en: ["<p>Scattered</p>\n", "<p>Random</p>\n", 
                                "<p>Rare</p>\n", "<p>Systematic</p>\n"],
                    options_hi: ["<p>Scattered</p>\n", "<p>Random</p>\n",
                                "<p>Rare</p>\n", "<p>Systematic</p>\n"],
                    solution_en: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Sporadic - appearing or happening at irregular intervals in time</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Scattered - spread over a large area</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Random - Something that is lacking in order, plan, or purpose</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Rare - not common or frequent</span></p>\n",
                    solution_hi: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Sporadic(</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\">) - appearing or happening at irregular intervals in time</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Scattered(</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2326;&#2375;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - spread over a large area</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Random(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - Something that is lacking in order, plan, or purpose</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Rare(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) - not common or frequent</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the most appropriate one-word substitution for the given group of words. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">An argument or statement seeming reasonable or probable</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate one-word substitution for the given group of words. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">An argument or statement seeming reasonable or probable</span></strong></p>\n",
                    options_en: ["<p>Incorrigible</p>\n", "<p>Corrigible</p>\n", 
                                "<p>Congruent</p>\n", "<p>Plausible</p>\n"],
                    options_hi: ["<p>Incorrigible</p>\n", "<p>Corrigible</p>\n",
                                "<p>Congruent</p>\n", "<p>Plausible</p>\n"],
                    solution_en: "<p>15.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Plausible - An argument or statement seeming reasonable or probable</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Incorrigible - that cannot be corrected</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Corrigible - capable of being corrected</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Congruent - similar to or in agreement with something</span></p>\n",
                    solution_hi: "<p>15.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Plausible(</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2306;&#2360;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\">) - An argument or statement seeming reasonable or probable</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Incorrigible(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2306;&#2358;&#2379;&#2343;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> ) - that cannot be corrected</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Corrigible(</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2358;&#2379;&#2343;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\">) - capable of being corrected</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Congruent(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\">) - similar to or in agreement with something</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\">Choose the correctly spelt word.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\">Choose the correctly spelt word.</span></p>\n",
                    options_en: ["<p>MINARET</p>\n", "<p>PERTIMENT</p>\n", 
                                "<p>OCCURED</p>\n", "<p>RELEVENT</p>\n"],
                    options_hi: ["<p>MINARET</p>\n", "<p>PERTIMENT</p>\n",
                                "<p>OCCURED</p>\n", "<p>RELEVENT</p>\n"],
                    solution_en: "<p>16.(a) &ldquo;<span style=\"font-family: Cambria Math;\">MINARET</span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Cambria Math;\">is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MINARET - a slender tower</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">PERTINENT - relevant or applicable to a particular matter</span></p>\n",
                    solution_hi: "<p>16.(a) &ldquo;<span style=\"font-family: Cambria Math;\">MINARET</span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">Choose the incorrectly spelt word.</span></p>\n",
                    question_hi: "<p>17. <span style=\"font-family: Cambria Math;\">Choose the incorrectly spelt word.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">SPLENDID</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">MEAGRE</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">MARRIAGEABLE</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">CONSCENT</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">SPLENDID</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">MEAGRE</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">MARRIAGEABLE</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">CONSCENT</span></p>\n"],
                    solution_en: "<p>17.(d) <span style=\"font-family: Cambria Math;\">CONSCENT</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;</span><span style=\"font-family: Cambria Math;\">CONSENT</span><span style=\"font-family: Cambria Math;\">&rdquo;</span><span style=\"font-family: Cambria Math;\"> is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SPLENDID - magnificent , very impressive</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MEAGRE - insufficient</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CONSENT - agreement&nbsp;</span></p>\n",
                    solution_hi: "<p>17.(d) <span style=\"font-family: Cambria Math;\">CONSCENT</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;</span><span style=\"font-family: Cambria Math;\">CONSENT</span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">18.</span><span style=\"font-family: Cambria Math;\"> Select the correct passive form of the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ravish likes chocolate.</span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\"> Select the correct passive form of the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ravish likes chocolate.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Chocolate was liked by Ravish.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Chocolate is liked by Ravish.</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Chocolate were liked by Ravish.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> Chocolate is being liked by Ravish.</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Chocolate was liked by Ravish.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Chocolate is liked by Ravish.</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">Chocolate were liked by Ravish.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> Chocolate is being liked by Ravish.</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18.(b) </span><span style=\"font-family: Cambria Math;\">Chocolate is liked by Ravish.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) </span><span style=\"font-weight: 400;\">Chocolate </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was liked</span></span><span style=\"font-weight: 400;\"> by Ravish.(Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><span style=\"font-weight: 400;\">Chocolate </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">were liked</span></span><span style=\"font-weight: 400;\"> by Ravish.(Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) </span><span style=\"font-weight: 400;\">Chocolate is </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">being</span></span><span style=\"font-weight: 400;\"> liked by Ravish.(Incorrect Verb)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(b) </span><span style=\"font-family: Cambria Math;\">Chocolate is liked by Ravish.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) </span><span style=\"font-weight: 400;\">Chocolate </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was liked</span></span><span style=\"font-weight: 400;\"> by Ravish.(&#2327;&#2354;&#2340; Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><span style=\"font-weight: 400;\">Chocolate </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">were liked</span></span><span style=\"font-weight: 400;\"> by Ravish.(&#2327;&#2354;&#2340; Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) </span><span style=\"font-weight: 400;\">Chocolate is </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">being</span></span><span style=\"font-weight: 400;\"> liked by Ravish.(&#2327;&#2354;&#2340; Verb)</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19.</span><span style=\"font-family: Cambria Math;\"> In the following question, out of the given four alternatives, select the alternative which best expresses the meaning of the Idiom/Phrase. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">To jump out of one\'s skin</span></strong></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> In the following question, out of the given four alternatives, select the alternative which best expresses the meaning of the Idiom/Phrase. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">To jump out of one\'s skin</span></strong></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">To argue with someone</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">To feel immense pain</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">To show one\'s real side</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">To be extremely surprised</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">To argue with someone</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">To feel immense pain</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">To show one\'s real side</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">To be extremely surprised</span></p>\n"],
                    solution_en: "<p>19.(d) <span style=\"font-family: Cambria Math;\">To jump out of one\'s skin - To be extremely surprised</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g. My friends made me jump out of my skin on my birthday.</span></p>\n",
                    solution_hi: "<p>19.(d) <span style=\"font-family: Cambria Math;\">To jump out of one\'s skin - To be extremely surprised/</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2361;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2366;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> - My friends made me jump out of my skin on my birthday./</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2360;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2381;&#2350;&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2333;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2358;&#2381;&#2330;&#2352;&#2381;&#2351;&#2330;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20.</span><span style=\"font-family: Cambria Math;\"> Fill in the blank with an appropriate option. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Lecanemab was recently reported to have a small but______ effect in reducing cognitive decline.</span></p>\n",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> Fill in the blank with an appropriate option. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Lecanemab was recently reported to have a small but______ effect in reducing cognitive decline.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">optional</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">marginal</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">microscopic</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">significant</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">optional</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">marginal</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">microscopic</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">significant</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Significant</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">important or large enough to be noticed</span><span style=\"font-family: Cambria Math;\">. The given sentence states that </span><span style=\"font-family: Cambria Math;\">Lecanemab was recently reported to have a small but significant effect in reducing cognitive decline.</span><span style=\"font-family: Cambria Math;\"> Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">significant</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Significant</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2381;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2366;&#2344;&#2375;&#2350;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2332;&#2381;&#2334;&#2366;&#2344;&#2366;&#2340;&#2381;&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2367;&#2352;&#2366;&#2357;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2379;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, \'</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21. <strong>Cloze Test</strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 21.</span></p>\n",
                    question_hi: "<p>21.<strong> Cloze Test</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 21.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">watched</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">saw</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">looked</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">observed</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">watched</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">saw</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">looked</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">observed</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Looked&rsquo; means </span><span style=\"font-family: Cambria Math;\">to seem or appear</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">with his slight build and his simple white clothes, Narad looked harmless</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;looked&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Looked&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2350;&#2370;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2336;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2347;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2346;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2352;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;&#2343;&#2366;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;looked&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22.<strong> Cloze Test</strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 22.</span></p>\n",
                    question_hi: "<p>22. <strong>Cloze Test</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 22.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">reputation</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">disapproval</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">dismay</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">censure</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">reputation</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">disapproval</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">dismay</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">censure</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Reputation&rsquo; means </span><span style=\"font-family: Cambria Math;\">the opinion that people in general have about what somebody/something is like</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">he had quite a reputation</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">reputation</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Reputation&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2347;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2359;&#2381;&#2336;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">reputation</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23. <strong>Cloze Test</strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______ was to travel from court to court and world to world, collecting gossip and (25)________ mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 23.</span></p>\n",
                    question_hi: "<p>23. <strong>Cloze Test</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 23.</span></p>\n",
                    options_en: ["<p>brooding</p>\n", "<p>pleasant</p>\n", 
                                "<p>peasant</p>\n", "<p>formidable</p>\n"],
                    options_hi: ["<p>brooding</p>\n", "<p>pleasant</p>\n",
                                "<p>peasant</p>\n", "<p>formidable</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Formidable</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">strong and powerful, so difficult to deal with</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">he had powerful family connections and was a formidable devotee of Lord Vishnu</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">formidable</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Formidable</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2332;&#2348;&#2370;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2325;&#2381;&#2340;&#2367;&#2358;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2346;&#2335;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2336;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2325;&#2381;&#2340;&#2367;&#2358;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2352;&#2367;&#2357;&#2366;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2327;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2381;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">formidable</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24. <strong>Cloze Test</strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 24.</span></p>\n",
                    question_hi: "<p>24.<strong> Cloze Test</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 24.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">activity</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">action</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">bustle</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">movement</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">activity</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">action</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">bustle</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">movement</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Activity</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">a situation in which there is a lot of action or movement</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">his favourite activity was to travel from court to court and world to world</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">activity</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Activity</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2360;&#2306;&#2342;&#2368;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;&#2357;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2342;&#2366;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2342;&#2366;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2344;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2344;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">activity</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25. <strong>Cloze Test</strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 25.</span></p>\n",
                    question_hi: "<p>25. <strong>Cloze Test</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">With his slight build and his simple white clothes, Narad (21) _______harmless, but he had </span><span style=\"font-family: Cambria Math;\">quite a (22)_________. He had powerful family connections and was a (23)_______. of Lord Vishnu. His favourite (24)_______. world, collecting gossip and (25)________devotee was to travel from court to court and world to mayhem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 25.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">spreading</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">scattering</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">diffusing</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">advancing</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">spreading</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">scattering</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">diffusing</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">advancing</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Spreading</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">to affect a larger area or a bigger group of people</span><span style=\"font-family: Cambria Math;\">. The given passage talks about Narad&rsquo;s activit of </span><span style=\"font-family: Cambria Math;\">collecting gossip and spreading mayhem</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;spreading&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Spreading</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2370;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2352;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2346;&#2358;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2335;&#2381;&#2336;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;&#2366;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2376;&#2354;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;&#2357;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;spreading&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>