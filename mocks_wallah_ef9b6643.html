<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">12:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 25</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">25</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1.Given are jumbled sentences. Select the option that gives their correct order</span></p> <p><span style=\"font-family:Times New Roman\">1. Before presenting a paper it is advisable to choose a subject of your interest.</span></p> <p><span style=\"font-family:Times New Roman\">P. When presenting your paper speak distinctly and pleasantly.</span></p> <p><span style=\"font-family:Times New Roman\">Q. Next, \'read up\' about it, before you write the paper.</span></p> <p><span style=\"font-family:Times New Roman\">R. Then prior to writing make a clear plan to ensure orderly presentation.</span></p> <p><span style=\"font-family:Times New Roman\">S. While reading, you should make notes.</span></p> <p><span style=\"font-family:Times New Roman\">6. In this way, the interest of the listeners is enhanced and you are appreciated. ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>RSQP           </span></p>",
                        " <p> PQRS          </span></p>",
                        " <p> QSRP           </span></p>",
                        " <p> SQRP</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) QSRP</span></p> <p><span style=\"font-family:Times New Roman\">Sentence Q mentions what must be done before writing the paper & Sentence S mentions notes should be made. So, S will follow Q. Further, Sentence R mentions the set order of sentences & SentenceP mentions what must be done while presenting the paper. So, P will follow R. Going through the options, option c has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(c) QSRP</span></p> <p><span style=\"font-family:Times New Roman\">Sentence Q mentions what must be done before writing the paper & Sentence S mentions notes should be made. So, S will follow Q. Further, Sentence R mentions the set order of sentences & SentenceP mentions what must be done while presenting the paper. So, P will follow R. Going through the options, option c has the correct sequence.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p><span style=\"font-family: \'times new roman\', times, serif;\">2.Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required, select no improvement.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">It is so tough a competition that you just cannot get through it unless </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">you do not work hard</span></span><span style=\"font-family: Times New Roman;\">. </span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>you will work hard</p>",
                        "<p>you work hard</p>",
                        "<p>you do work hard</p>",
                        "<p>no improvement</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) We cannot use &lsquo;not&rsquo; with &lsquo;unless&rsquo; because it makes the sentence superfluous(not necessary). Hence, &lsquo;you work hard&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) We cannot use &lsquo;not&rsquo; with &lsquo;unless&rsquo; because it makes the sentence superfluous(not necessary). Hence, &lsquo;you work hard&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3.Select the active form of the given sentence</span></p> <p><span style=\"font-family:Times New Roman\">Will the children of the slum areas be helped by us? ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> Will the children of the slum areas help us?</span></p>",
                        " <p> Shall we help the children of the slum areas?</span></p>",
                        " <p> Shall the children of the slum areas be helped?</span></p>",
                        " <p> We shall help the children of the slum areas? </span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\">a. Will the children of the slum areas help us? (Meaning has changed)</span></p> <p><span style=\"font-family:Times New Roman\">b. Shall we help the children of the slum areas? (Correct)</span></p> <p><span style=\"font-family:Times New Roman\">c. Shall the children of the slum areas be helped? (doer is missing)</span></p> <p><span style=\"font-family:Times New Roman\">d. We shall help the children of the slum areas? (Not interrogative)</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\">a. Will the children of the slum areas help us? (Meaning has changed)</span></p> <p><span style=\"font-family:Times New Roman\">b. Shall we help the children of the slum areas? (Correct)</span></p> <p><span style=\"font-family:Times New Roman\">c. Shall the children of the slum areas be helped? (doer is missing)</span></p> <p><span style=\"font-family:Times New Roman\">d. We shall help the children of the slum areas? (Not interrogative)</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: " <p>4.Select the synonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">HOODLUM ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>pioneer        </span></p>",
                        " <p>criminal        </span></p>",
                        " <p>devotee</span></p>",
                        " <p>scholar</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Hoodlum- criminal</span></p> <p><span style=\"font-family:Times New Roman\">Pioneer- </span><span style=\"font-family:Times New Roman\">a person who is one of the first to develop an area of human knowledge, culture, etc.</span></p> <p><span style=\"font-family:Times New Roman\">Devotee- </span><span style=\"font-family:Times New Roman\">a person who likes somebody/something very much</span></p> <p><span style=\"font-family:Times New Roman\">Scholar- a person who studies and has a lot of knowledge about a particular subject</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) Hoodlum- criminal</span></p> <p><span style=\"font-family:Times New Roman\">Pioneer- </span><span style=\"font-family:Times New Roman\">a person who is one of the first to develop an area of human knowledge, culture, etc.</span></p> <p><span style=\"font-family:Times New Roman\">Devotee- </span><span style=\"font-family:Times New Roman\">a person who likes somebody/something very much</span></p> <p><span style=\"font-family:Times New Roman\">Scholar- a person who studies and has a lot of knowledge about a particular subject</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5.Select the antonym of the following word</p>\r\n<p><span style=\"font-family: Times New Roman;\">ALLURE </span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>repulse</p>",
                        "<p>develop</p>",
                        "<p>entice</p>",
                        "<p>decoy</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a) Allure-entice</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Repulse- </span><span style=\"font-family: Times New Roman;\">drive back (an attack or attacker) by force.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Decoy- </span><span style=\"font-family: Times New Roman;\">a person or object that is used in order to trick somebody/something into doing what you want, going where you want</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) Allure-entice</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Repulse- </span><span style=\"font-family: Times New Roman;\">drive back (an attack or attacker) by force.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Decoy- </span><span style=\"font-family: Times New Roman;\">a person or object that is used in order to trick somebody/something into doing what you want, going where you want</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6.Select the synonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">IRK ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>bore</span></p>",
                        " <p>insult</span></p>",
                        " <p>urge</span><span style=\"font-family:Times New Roman\">     </span></p>",
                        " <p>annoy</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) Irk- annoy</span></p> <p><span style=\"font-family:Times New Roman\">Bore- </span><span style=\"font-family:Times New Roman\">to make somebody feel bored</span></p> <p><span style=\"font-family:Times New Roman\">Urge- </span><span style=\"font-family:Times New Roman\">to force somebody/something to go in a certain direction</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(d) Irk- annoy</span></p> <p><span style=\"font-family:Times New Roman\">Bore- </span><span style=\"font-family:Times New Roman\">to make somebody feel bored</span></p> <p><span style=\"font-family:Times New Roman\">Urge- </span><span style=\"font-family:Times New Roman\">to force somebody/something to go in a certain direction</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p>7.Select the most appropriate meaning of given idiom</span></p> <p><span style=\"font-family:Times New Roman\">Saving for a rainy day  ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> Saving money for later</span><span style=\"font-family:Times New Roman\">              </span></p>",
                        " <p> Saving money to enjoy</span></p>",
                        " <p> Saving money to study  </span></p>",
                        " <p> None of these</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) Saving money for later</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(a) Saving money for later</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8.Select the wrongly spelt word</p>\n",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>loathsome</p>\n",
                        "<p>liable<span style=\"font-family: Times New Roman;\"> </span></p>\n",
                        "<p>literature<span style=\"font-family: Times New Roman;\"> </span></p>\n",
                        "<p>lizzard</p>\n"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Lizard is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">loathsome - extremely unpleasant</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">liable - legally answerable</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) Lizard is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">loathsome - extremely unpleasant</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">liable - legally answerable</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9.Select the most appropriate option to fill in the blanks</span></p> <p><span style=\"font-family:Times New Roman\">My helper took fifteen minutes to mop____ after washing the car. ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> up</span></p>",
                        " <p> of</span></p>",
                        " <p> out</span></p>",
                        " <p> in</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) ‘Mop up’ means to clean up something that has spilled by using a mop or a cloth/clean up, wipe up as the cleaner wash the car in the given sentence. Hence, ‘up’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(a) ‘Mop up’ means to clean up something that has spilled by using a mop or a cloth/clean up, wipe up as the cleaner wash the car in the given sentence. Hence, ‘up’ is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p>10.Select the most appropriate meaning of given idiom</span></p> <p><span style=\"font-family:Times New Roman\">Take a rain check ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> To cross check</span></p>",
                        " <p> To get the weather report</span></p>",
                        " <p> Postpone a plan</span></p>",
                        " <p> To practice</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Postpone a plan</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(c) Postpone a plan</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p>11.Select the wrongly spelt word ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>prerequisite</span></p>",
                        " <p>privelige    </span></p>",
                        " <p>profession      </span></p>",
                        " <p>progression</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Privilege is the correct spelling</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) Privilege is the correct spelling</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12.Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required, select no improvement.</p>\r\n<p><span style=\"font-family: Times New Roman;\">The money is to be used </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">for the benefit of the poor</span></span><span style=\"font-family: Times New Roman;\">. </span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>for benefit of poor people</p>",
                        "<p>for the benefit of the poors</p>",
                        "<p>for the benefit of the poor people</p>",
                        "<p>no improvement</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) We will place people after poor to grammatically correct the given sentence. Hence, &lsquo;for the benefit of the poor people&rsquo; is the most appropriate answer. </span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) We will place people after poor to grammatically correct the given sentence. Hence, &lsquo;for the benefit of the poor people&rsquo; is the most appropriate answer. </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>Directions (13-17): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Many of us believe that science is something modern,(13)........ The truth is that (14)......... has been using science for (15)...............very long time.However, it has (16).......... A greater effect on human lives in the last 25 (17)..........30 years than in the hundreds of years since the invention of the plough.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 13.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q13.  </span></p>",
                    options_en: [
                        "<p>unless<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>if <span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>though</p>",
                        "<p>yet</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Though is used to bring a contrasting clause in a sentence. Hence, &lsquo;though&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) Though is used to bring a contrasting clause in a sentence. Hence, &lsquo;though&rsquo; is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>Directions (13-17): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Many of us believe that science is something modern,(13)........ The truth is that (14)......... has been using science for (15)...............very long time.However, it has (16).......... A greater effect on human lives in the last 25 (17)..........30 years than in the hundreds of years since the invention of the plough.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 14.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q14.</span></p>",
                    options_en: [
                        "<p>people<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>man</p>",
                        "<p>men<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>women</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) All the other options except &lsquo;man&rsquo; do not fit in the context of the passage. Hence, &lsquo;man&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) All the other options except &lsquo;man&rsquo; do not fit in the context of the passage. Hence, &lsquo;man&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>Directions (13-17): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Many of us believe that science is something modern,(13)........ The truth is that (14)......... has been using science for (15)...............very long time.However, it has (16).......... A greater effect on human lives in the last 25 (17)..........30 years than in the hundreds of years since the invention of the plough.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 15.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q15.</span></p>",
                    options_en: [
                        "<p>the</p>",
                        "<p>a</p>",
                        "<p>that<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>an</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) We generally use the article &lsquo;a&rsquo; with the &lsquo;long time&rsquo;. Hence, &lsquo;a&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) We generally use the article &lsquo;a&rsquo; with the &lsquo;long time&rsquo;. Hence, &lsquo;a&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>Directions (13-17): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Many of us believe that science is something modern,(13)........ The truth is that (14)......... has been using science for (15)...............very long time. However, it has (16).......... a greater effect on human lives in the last 25 (17)..........30 years than in the hundreds of years since the invention of the plough.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 16.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q16.</span></p>",
                    options_en: [
                        "<p>have</p>",
                        "<p>had</p>",
                        "<p>has<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>has been</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) The given sentence is in the past tense. Hence, &lsquo;had&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) The given sentence is in the past tense. Hence, &lsquo;had&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>Directions (13-17): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Many of us believe that science is something modern,(13)........ The truth is that (14)......... has been using science for (15)...............very long time.However, it has (16).......... A greater effect on human lives in the last 25 (17)..........30 years than in the hundreds of years since the invention of the plough.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 17.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q17.</span></p>",
                    options_en: [
                        "<p>and</p>",
                        "<p>or<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>either</p>",
                        "<p>neither</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) All the other options except &lsquo;or&rsquo; do not fit in the context of the passage. Hence, &lsquo;or&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) All the other options except &lsquo;or&rsquo; do not fit in the context of the passage. Hence, &lsquo;or&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18.Select the word which means the same as the group of words given</span></p> <p><span style=\"font-family:Times New Roman\">A person, animal or plant much below the usual height ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>wizard</span></p>",
                        " <p>dwarf</span></p>",
                        " <p>creature</span></p>",
                        " <p>witch</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Dwarf- a person, animal, or plant much below the usual height</span></p> <p><span style=\"font-family:Times New Roman\">Wizard- </span><span style=\"font-family:Times New Roman\">a man who is believed to have magic powers</span></p> <p><span style=\"font-family:Times New Roman\">Creature- </span><span style=\"font-family:Times New Roman\">a living thing such as an animal, a bird, a fish, or an insect, but not a plant</span></p> <p><span style=\"font-family:Times New Roman\">Witch- </span><span style=\"font-family:Times New Roman\"> a woman who is thought to have magic powers</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) Dwarf- a person, animal, or plant much below the usual height</span></p> <p><span style=\"font-family:Times New Roman\">Wizard- </span><span style=\"font-family:Times New Roman\">a man who is believed to have magic powers</span></p> <p><span style=\"font-family:Times New Roman\">Creature- </span><span style=\"font-family:Times New Roman\">a living thing such as an animal, a bird, a fish, or an insect, but not a plant</span></p> <p><span style=\"font-family:Times New Roman\">Witch- </span><span style=\"font-family:Times New Roman\"> a woman who is thought to have magic powers</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: " <p>19.Select the most appropriate option to fill in the blanks</span></p> <p><span style=\"font-family:Times New Roman\">The man asked me if I was known____ any ATM in the area. ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> of</span><span style=\"font-family:Times New Roman\">     </span></p>",
                        " <p> as</span><span style=\"font-family:Times New Roman\">           </span></p>",
                        " <p> for</span><span style=\"font-family:Times New Roman\">      </span></p>",
                        " <p> about</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) ‘Known of’ means that you are aware of something as ATM in the given sentence. Hence, ‘of’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(a) ‘Known of’ means that you are aware of something as ATM in the given sentence. Hence, ‘of’ is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20.Given are jumbled sentences.Select the option that gives their correct order</span></p> <p><span style=\"font-family:Times New Roman\">1. Mary and Jo were amateur singers. </span></p> <p><span style=\"font-family:Times New Roman\">P. He called them for auditions the following week. </span></p> <p><span style=\"font-family:Times New Roman\">Q. So they approached a leading music company. </span></p> <p><span style=\"font-family:Times New Roman\">R. They wanted to try their luck at playback singing. </span></p> <p><span style=\"font-family:Times New Roman\">S. But the manager said that they would have to qualify an audition test. </span></p> <p><span style=\"font-family:Times New Roman\">6. Fortunately they qualified the test.  ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> RSPQ         </span></p>",
                        " <p> QPRS           </span></p>",
                        " <p> PRSQ </span><span style=\"font-family:Times New Roman\">   </span></p>",
                        " <p> RQSP </span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) RQSP</span></p> <p><span style=\"font-family:Times New Roman\">Sentence R mentions Mary and Jo were amateur singers so they wanted their luck as play singers & Sentence Q mentions so they approached a leading music company. So, Q will follow R. Further, Sentence S mentions the manager asked them to qualify test & Sentence P mentions they asked them to come for an audition for the following week. Going through the options, option d has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(d) RQSP</span></p> <p><span style=\"font-family:Times New Roman\">Sentence R mentions Mary and Jo were amateur singers so they wanted their luck as play singers & Sentence Q mentions so they approached a leading music company. So, Q will follow R. Further, Sentence S mentions the manager asked them to qualify test & Sentence P mentions they asked them to come for an audition for the following week. Going through the options, option d has the correct sequence.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: " <p>21.Select the passive form of the given sentence</span></p> <p><span style=\"font-family:Times New Roman\">Did the noise frighten you? ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> Did you frighten the noise?</span></p>",
                        " <p> Was the noise frightened by you?</span></p>",
                        " <p> Were you frightened by the noise?</span></p>",
                        " <p> Were you frighten by the noise?</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">a. Did you frighten the noise? (the meaning of this sentence is different from the one given)</span></p> <p><span style=\"font-family:Times New Roman\">b. Was the noise frightened by you? (the meaning of this sentence is different from the one given)</span></p> <p><span style=\"font-family:Times New Roman\">c. Were you frightened by the noise? (Correct)</span></p> <p><span style=\"font-family:Times New Roman\">d. Were you frighten by the noise? (The verb frighten is not in the third form of the verb)</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">a. Did you frighten the noise? (the meaning of this sentence is different from the one given)</span></p> <p><span style=\"font-family:Times New Roman\">b. Was the noise frightened by you? (the meaning of this sentence is different from the one given)</span></p> <p><span style=\"font-family:Times New Roman\">c. Were you frightened by the noise? (Correct)</span></p> <p><span style=\"font-family:Times New Roman\">d. Were you frighten by the noise? (The verb frighten is not in the third form of the verb)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: " <p>22.Select the antonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">BIZARRE ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>soft</span><span style=\"font-family:Times New Roman\">            </span></p>",
                        " <p>usual             </span></p>",
                        " <p>gentel</span><span style=\"font-family:Times New Roman\">  </span></p>",
                        " <p>sane</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Bizarre-very strange or unusual</span></p> <p><span style=\"font-family:Times New Roman\">Genteel- </span><span style=\"font-family:Times New Roman\"> kind and calm</span></p> <p><span style=\"font-family:Times New Roman\">Sane- </span><span style=\"font-family:Times New Roman\">mentally normal</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) Bizarre-very strange or unusual</span></p> <p><span style=\"font-family:Times New Roman\">Genteel- </span><span style=\"font-family:Times New Roman\"> kind and calm</span></p> <p><span style=\"font-family:Times New Roman\">Sane- </span><span style=\"font-family:Times New Roman\">mentally normal</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.In the sentence identify the segment that contains grammatical error</p>\r\n<p><span style=\"font-family: Times New Roman;\">Knowledge and wisdom makes an individual truly complete and self assured. </span></p>\n",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>Knowledge and wisdom makes</p>\n",
                        "<p>an individual truly complete</p>\n",
                        "<p>and self assured</p>\n",
                        "<p>No error</p>\n"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a) Knowledge and wisdom is the plural noun, so plural form of verb will be used. Hence, make will replace makes.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) Knowledge and wisdom is the plural noun, so plural form of verb will be used. Hence, make will replace makes.&nbsp;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.In the sentence identify the segment that contains grammatical error</p>\r\n<p><span style=\"font-family: Times New Roman;\">Our football team comprises of eleven skilled players. </span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>Our football team</p>",
                        "<p>comprises of</p>",
                        "<p>eleven skilled players</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)&nbsp; \'Comprise of\'&nbsp; is a superfluous condition. W</span><span style=\"font-family: \'Times New Roman\';\">e use e</span><span style=\"font-family: \'Times New Roman\';\">ither&nbsp; comprise or consist of to make the sentence grammatically correct.&nbsp;</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)&nbsp; \'Comprise of\'&nbsp; is a superfluous condition. W</span><span style=\"font-family: \'Times New Roman\';\">e use e</span><span style=\"font-family: \'Times New Roman\';\">ither&nbsp; comprise or consist of to make the sentence grammatically correct.&nbsp;</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25.Select the word which means the same as the group of words given</span></p> <p><span style=\"font-family:Times New Roman\">The belief that events are predetermined and therefore cannot be changed ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> fatalism         </span></p>",
                        " <p>chasm</span><span style=\"font-family:Times New Roman\">          </span></p>",
                        " <p>autism</span><span style=\"font-family:Times New Roman\">    </span></p>",
                        " <p> prism</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) Fatalism- the belief that events are predetermined and therefore cannot be changed</span></p> <p><span style=\"font-family:Times New Roman\">Chasm- </span><span style=\"font-family:Times New Roman\">a deep hole in the ground</span></p> <p><span style=\"font-family:Times New Roman\">Autism- </span><span style=\"font-family:Times New Roman\">a mental condition in which a person finds it difficult to communicate or form relationships with other people</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(a) Fatalism- the belief that events are predetermined and therefore cannot be changed</span></p> <p><span style=\"font-family:Times New Roman\">Chasm- </span><span style=\"font-family:Times New Roman\">a deep hole in the ground</span></p> <p><span style=\"font-family:Times New Roman\">Autism- </span><span style=\"font-family:Times New Roman\">a mental condition in which a person finds it difficult to communicate or form relationships with other people</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>