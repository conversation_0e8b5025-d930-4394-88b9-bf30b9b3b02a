<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">15:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 6</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">6</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 4
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 5,
                end: 5
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following was the first development bank in India?</p>",
                    question_hi: "<p>1.<span style=\"font-family: Palanquin Dark;\"> निम्नलिखित में से कौन भारत का पहला विकास बैंक था?</span></p>",
                    options_en: ["<p>National Housing Bank</p>", "<p>Export Import Bank of India</p>", 
                                "<p>Industrial Finance Corporation of India</p>", "<p>Industrial Development Bank of India</p>"],
                    options_hi: ["<p>राष्ट्रीय आवास बैंक</p>", "<p>एक्सपोर्ट इम्पोर्ट बैंक ऑफ इंडिया</p>",
                                "<p>भारतीय औद्योगिक वित्त निगम</p>", "<p>भारतीय औद्योगिक विकास बैंक</p>"],
                    solution_en: "<p>1.(c) <span style=\"font-family: Palanquin Dark;\">Industrial Finance Corporation of India founded on </span><strong><span style=\"font-family: Palanquin Dark;\">1st July 1948</span></strong><span style=\"font-family: Palanquin Dark;\">. National Housing Bank was founded on</span><span style=\"font-family: Palanquin Dark;\"> <strong>9th July 1988</strong></span><span style=\"font-family: Palanquin Dark;\">. Export Import Bank of India was founded in </span><span style=\"font-family: Palanquin Dark;\"><strong>March 1982</strong>.</span><span style=\"font-family: Palanquin Dark;\"> Industrial Development Bank of India was founded on </span><span style=\"font-family: Palanquin Dark;\"><strong>1st July 1964</strong>. </span></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    solution_hi: "<p>1.(c)<span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">भारतीय औद्योगिक वित्त निगम की स्थापना </span><strong><span style=\"font-family: Palanquin Dark;\">1 जुलाई 1948</span></strong><span style=\"font-family: Palanquin Dark;\"> को हुई थी। राष्ट्रीय आवास बैंक की स्थापना </span><strong><span style=\"font-family: Palanquin Dark;\">9 जुलाई 1988</span></strong><span style=\"font-family: Palanquin Dark;\"> को हुई थी। भारतीय निर्यात आयात बैंक की स्थापना </span><span style=\"font-family: Palanquin Dark;\"><strong>मार्च 1982</strong> </span><span style=\"font-family: Palanquin Dark;\">में हुई थी। भारतीय औद्योगिक विकास बैंक की स्थापना </span><strong><span style=\"font-family: Palanquin Dark;\">1 जुलाई 1964</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>को हुई थी।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. <span style=\"font-family: Palanquin Dark;\">The system of MSP (Minimum Support Price) was first introduced for ______ in 1966-67 and later expanded to include other essential food crops.</span></p>",
                    question_hi: "<p>2.<span style=\"font-family: Palanquin Dark;\"> MSP (न्यूनतम समर्थन मूल्य) की प्रणाली को पहली बार 1966-67 में ______ के लिए पेश किया गया था और बाद में अन्य आवश्यक खाद्य फसलों को शामिल करने के लिए इसका विस्तार किया गया।</span></p>",
                    options_en: ["<p>jowar</p>", "<p>bajra</p>", 
                                "<p>wheat</p>", "<p>ragi</p>"],
                    options_hi: ["<p>ज्वार</p>", "<p>बाजरा</p>",
                                "<p>गेहूं</p>", "<p>रागी</p>"],
                    solution_en: "<p>2.(c) <span style=\"font-family: Palanquin Dark;\">The system of MSP (Minimum Support Price) was first introduced for Wheat in 1966-67. </span><span style=\"font-family: Palanquin Dark;\">Minimum Support Price (MSP) is a form of market intervention by the Government of India to insure agricultural producers against any sharp fall in farm prices. Crops covered by MSPs include:</span><span style=\"font-family: Palanquin Dark;\"> <strong>7 types</strong></span><span style=\"font-family: Palanquin Dark;\"> of </span><strong><span style=\"font-family: Palanquin Dark;\">cereals</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(paddy, wheat, maize, bajra, jowar, ragi and barley),</span><span style=\"font-family: Palanquin Dark;\"> <strong>5 types</strong></span><span style=\"font-family: Palanquin Dark;\"> of</span><span style=\"font-family: Palanquin Dark;\"> <strong>pulses</strong></span><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(chana, arhar/tur, urad, moong and masoor),</span><span style=\"font-family: Palanquin Dark;\"> <strong>7 oilseeds</strong></span><span style=\"font-family: Palanquin Dark;\"> (rapeseed-mustard, groundnut, soyabean, sunflower, sesamum, safflower, niger seed),</span><span style=\"font-family: Palanquin Dark;\"> <strong>4 commercial crops</strong></span><span style=\"font-family: Palanquin Dark;\"> (cotton, sugarcane, copra, raw jute). </span></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    solution_hi: "<p>2.(c) <span style=\"font-family: Palanquin Dark;\">MSP (न्यूनतम समर्थन मूल्य) की प्रणाली पहली बार 1966-67 में गेहूं के लिए पेश की गई थी। न्यूनतम समर्थन मूल्य (MSP) कृषि उत्पादकों को कृषि कीमतों में किसी भी तेज गिरावट के खिलाफ बीमा करने के लिए भारत सरकार द्वारा बाजार में हस्तक्षेप का एक रूप है। MSP द्वारा कवर की जाने वाली फसलों में शामिल हैं: </span><strong><span style=\"font-family: Palanquin Dark;\">7 प्रकार</span></strong><span style=\"font-family: Palanquin Dark;\"> के </span><strong><span style=\"font-family: Palanquin Dark;\">अनाज</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(धान, गेहूं, मक्का, बाजरा, ज्वार, रागी और जौ), </span><span style=\"font-family: Palanquin Dark;\"><strong>5 प्रकार</strong> की <strong>दालें</strong></span><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(चना, अरहर / तुअर, उड़द, मूंग और मसूर),</span><span style=\"font-family: Palanquin Dark;\"> <strong>7 तिलहन</strong> </span><span style=\"font-family: Palanquin Dark;\">(रेपसीड-सरसों) , मूंगफली, सोयाबीन, सूरजमुखी, तिल, कुसुम, </span><span style=\"font-family: Palanquin Dark;\">रामतिल या \'काला तिल</span><span style=\"font-family: Palanquin Dark;\">), </span><strong><span style=\"font-family: Palanquin Dark;\">4 व्यावसायिक फसलें</span></strong><span style=\"font-family: Palanquin Dark;\"> (कपास, गन्ना, खोपरा (सूखा नारियल), कच्चा जूट)।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. <span style=\"font-family: Palanquin Dark;\">The Central Board of Directors of the Reserve Bank of India are appointed for a term of ______ years</span></p>",
                    question_hi: "<p>3.<span style=\"font-family: Palanquin Dark;\"> भारतीय रिजर्व बैंक के केंद्रीय निदेशक मंडल की नियुक्त ________ वर्ष की अवधि किया जाता है</span></p>",
                    options_en: ["<p>three</p>", "<p>two</p>", 
                                "<p>four</p>", "<p>five</p>"],
                    options_hi: ["<p>तीन</p>", "<p>दो</p>",
                                "<p>चार</p>", "<p>पांच</p>"],
                    solution_en: "<p>3.(c) <span style=\"font-family: Palanquin Dark;\">The Central Board of Directors of the Reserve Bank of India are appointed for a term of four years. </span><span style=\"font-family: Palanquin Dark;\">Shri Shaktikanta Das is the present (May 2022) Governor of RBI. RBI was established on 1st April 1935 on the recommendation of Hilton Young&rsquo;s Commission. RBI was nationalised on 1st January 1949. </span><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    solution_hi: "<p>3.(c) <span style=\"font-family: Palanquin Dark;\">भारतीय रिजर्व बैंक के केंद्रीय निदेशक मंडल को चार साल की अवधि के लिए नियुक्त किया जाता है। श्री शक्तिकांत दास RBI के वर्तमान (मई 2022) गवर्नर हैं। RBI की स्थापना 1 अप्रैल 1935 को हिल्टन यंग आयोग की सिफारिश पर की गई थी। 1 जनवरी 1949 को RBI का राष्ट्रीयकरण किया गया था।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">4. </span><span style=\"font-family: Times New Roman;\">which of the following sustainable development Goals ensures availability and sustainable management of water and sanitation for all&nbsp;</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">4. </span><span style=\"font-family: Baloo;\">निम्नलिखित में से कौन सा सतत विकास लक्ष्य सभी के लिए पानी और स्वच्छता की उपलब्धता और सतत प्रबंधन सुनिश्चित करता है।</span></p>",
                    options_en: ["<p>SDG 5</p>", "<p>SDG 6</p>", 
                                "<p>SDG 4</p>", "<p>SDG 7</p>"],
                    options_hi: ["<p>SDG 5</p>", "<p>SDG 6</p>",
                                "<p>SDG 4</p>", "<p>SDG 7</p>"],
                    solution_en: "<p>4.(b) <span style=\"font-family: Times New Roman;\">The 17 sustainable development goals (SDGs) to transform our world:<br></span><span style=\"font-family: Times New Roman;\">GOAL 4: Quality Education&nbsp;<br></span><span style=\"font-family: Times New Roman;\">GOAL 5: Gender Equality<br></span><span style=\"font-family: Times New Roman;\">GOAL 6: Clean Water and Sanitation<br></span><span style=\"font-family: Times New Roman;\">GOAL 7: Affordable and Clean Energy </span></p>\n<p><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>4.(b) <span style=\"font-family: Baloo;\">दुनिया को बदलने के लिए 17 सतत विकास लक्ष्य (SDGs):<br></span><span style=\"font-family: Baloo;\">लक्ष्य 4: गुणवत्तापूर्ण शिक्षा,<br></span><span style=\"font-family: Baloo;\">लक्ष्य 5: लैंगिक समानता,<br></span><span style=\"font-family: Baloo;\">लक्ष्य 6: स्वच्छ जल और स्वच्छता<br></span><span style=\"font-family: Baloo;\">लक्ष्य 7: वहनीय और स्वच्छ ऊर्जा</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5.<span style=\"font-family: Baloo;\"> Which of the given statements about the Union Budget is/are true?&nbsp;<br></span><span style=\"font-family: Baloo;\">1. Vote on Account deals only with the expenditure side of the government budget.&nbsp;<br></span><span style=\"font-family: Baloo;\">2. Vote on Account and Interim Budget are not the same.&nbsp;<br></span><span style=\"font-family: Baloo;\">3. An Interim Budget gives a complete financial statement, similar to a full budget.&nbsp;</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Palanquin Dark;\"> केंद्रीय बजट के बारे में दिए गए कथनों में से कौन से सत्य हैं?<br></span><span style=\"font-family: Palanquin Dark;\">1. लेखानुदान केवल सरकारी बजट के व्यय पक्ष से संबंधित है।<br></span><span style=\"font-family: Palanquin Dark;\">2. लेखानुदान और अंतरिम बजट समान नहीं होते हैं।<br></span><span style=\"font-family: Palanquin Dark;\">3. एक अंतरिम बजट एक पूर्ण बजट के समान एक पूर्ण वित्तीय विवरण देता है।</span></p>",
                    options_en: ["<p>Only 1</p>", "<p>Only 3</p>", 
                                "<p>1, 2 and 3</p>", "<p>1 and 2</p>"],
                    options_hi: ["<p>केवल 1</p>", "<p>केवल 3</p>",
                                "<p>1, 2 और 3</p>", "<p>1 और 2</p>"],
                    solution_en: "<p>5.(c) <span style=\"font-family: Palanquin Dark;\">According to</span><span style=\"font-family: Palanquin Dark;\"> <strong>Article 112</strong></span><span style=\"font-family: Palanquin Dark;\"> of the Indian Constitution, the Union Budget of a year, also referred to as the annual financial statement, is a statement of the estimated receipts and expenditure of the government for that particular year. The Union Budget for Financial Year 2022-23 this year aims to strengthen the infrastructure with its focus on </span><strong><span style=\"font-family: Palanquin Dark;\">four priorities</span></strong><span style=\"font-family: Palanquin Dark;\">: PM GatiShakti, Inclusive Development, Productivity Enhancement &amp; Investment, </span><span style=\"font-family: Palanquin Dark;\">Sunrise opportunities,</span><span style=\"font-family: Palanquin Dark;\"> Energy Transition, and Climate Action, Financing of investments.</span></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    solution_hi: "<p>5.(c) <span style=\"font-family: Palanquin Dark;\">भारतीय संविधान के </span><span style=\"font-family: Palanquin Dark;\"><strong>अनुच्छेद 112</strong> </span><span style=\"font-family: Palanquin Dark;\">के अनुसार, एक वर्ष का केंद्रीय बजट, जिसे वार्षिक वित्तीय विवरण भी कहा जाता है, उस विशेष वर्ष के लिए सरकार की अनुमानित प्राप्तियों और व्यय का विवरण होता है। इस वर्ष वित्तीय वर्ष 2022-23 के केंद्रीय बजट का उद्देश्य <strong>चार प्राथमिकताओं</strong> पर अपना ध्यान केंद्रित करते हुए बुनियादी ढांचे को मजबूत करना है: पीएम गतिशक्ति, समावेशी विकास, उत्पादकता वृद्धि और निवेश, </span><span style=\"font-family: Baloo;\">उदीयमान अवसर</span><span style=\"font-family: Baloo;\">,</span><span style=\"font-family: Baloo;\"> </span><span style=\"font-family: Palanquin Dark;\">नियोजित-ऊर्जा-संक्रमण</span><span style=\"font-family: Palanquin Dark;\">, और जलवायु कार्रवाई, निवेश का वित्तपोषण आदि।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "misc",
                    question_en: "<p>6. <span style=\"font-family: Palanquin Dark;\">The GDP deflator is also called:&nbsp;</span></p>",
                    question_hi: "<p>6.<span style=\"font-family: Palanquin Dark;\"> GDP डिफ्लेटर को _________भी कहा जाता है।</span></p>",
                    options_en: ["<p>implicit inflation index</p>", "<p>explicit inflation index</p>", 
                                "<p>implicit price deflator</p>", "<p>explicit price deflator</p>"],
                    options_hi: ["<p>निहित मुद्रास्फीति सूचकांक</p>", "<p>स्पष्ट मुद्रास्फीति सूचकांक</p>",
                                "<p>निहित मूल्य डिफ्लेटर</p>", "<p>स्पष्ट मूल्य डिफ्लेटर</p>"],
                    solution_en: "<p>6.(c) <span style=\"font-family: Palanquin Dark;\">The GDP (Gross Domestic Project) deflator is also called</span><span style=\"font-family: Palanquin Dark;\"> <strong>implicit price deflator</strong></span><span style=\"font-family: Palanquin Dark;\"> . </span><strong><span style=\"font-family: Palanquin Dark;\">Gross domestic product (GDP)</span></strong><span style=\"font-family: Palanquin Dark;\"> is the monetary value of all finished goods and services made within a country during a specific period. The GDP deflator, also called implicit price deflator, is a measure of inflation.</span></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    solution_hi: "<p>6.(c) <span style=\"font-family: Palanquin Dark;\">GDP (सकल घरेलू परियोजना) डिफ्लेटर को <strong>निहित मूल्य डिफ्लेटर</strong> भी कहा जाता है। <strong>सकल घरेलू उत्पाद (GDP)</strong> एक विशिष्ट अवधि के दौरान किसी देश के भीतर किए गए सभी तैयार माल और सेवाओं का मौद्रिक मूल्य है। GDP अपस्फीतिकारक, जिसे निहित मूल्य अपस्फीतिकारक भी कहा जाता है, मुद्रास्फीति का एक उपाय है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>