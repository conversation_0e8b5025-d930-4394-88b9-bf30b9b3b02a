<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.&nbsp;(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Proud : Arrogant</p>",
                    question_hi: "<p>1. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।&nbsp;(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>गौरवान्वित : अभिमानी</p>",
                    options_en: ["<p>Authentic : Genuine</p>", "<p>Offend : Praise</p>", 
                                "<p>Trust : Doubt</p>", "<p>Wise : Ignorant</p>"],
                    options_hi: ["<p>प्रामाणिक : वास्तविक</p>", "<p>अपमान : प्रशंसा</p>",
                                "<p>भरोसा : संदेह</p>", "<p>बुद्धिमान : अज्ञानी</p>"],
                    solution_en: "<p>1.(a) As Proud and Arrogant are synonyms of each other, similarly Authentic and Genuine are synonyms of each other.</p>",
                    solution_hi: "<p>1.(a) जिस प्रकार गौरवान्वित और अभिमानी एक दूसरे के पर्यायवाची हैं, उसी प्रकार प्रामाणिक और वास्तविक एक दूसरे के पर्यायवाची हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Choose the alternative which closely resembles the mirror image of the given&nbsp;combination.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851249783.png\" alt=\"rId4\" width=\"104\" height=\"110\"></p>",
                    question_hi: "<p>2. उस विकल्प का चयन कीजिए जो दिए गए संयोजन के दर्पण में निर्मित प्रतिबिंब से सर्वाधिक मिलता जुलता हो।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851249783.png\" alt=\"rId4\" width=\"104\" height=\"110\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851249883.png\" alt=\"rId5\" width=\"65\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851249992.png\" alt=\"rId6\" width=\"60\" height=\"20\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250060.png\" alt=\"rId7\" width=\"65\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250258.png\" alt=\"rId8\" width=\"66\" height=\"25\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851249883.png\" alt=\"rId5\" width=\"65\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851249992.png\" alt=\"rId6\" width=\"60\" height=\"20\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250060.png\" alt=\"rId7\" width=\"65\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250258.png\" alt=\"rId8\" width=\"66\" height=\"25\"></p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250258.png\" alt=\"rId8\" width=\"67\" height=\"25\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250258.png\" alt=\"rId8\" width=\"67\" height=\"25\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair.&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए।&nbsp;(नोट : संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: ["<p>24 : 4</p>", "<p>36 :24</p>", 
                                "<p>12 : 8</p>", "<p>72 : 48</p>"],
                    options_hi: ["<p>24 : 4</p>", "<p>36 :24</p>",
                                "<p>12 : 8</p>", "<p>72 : 48</p>"],
                    solution_en: "<p>3.(a)<br><strong>Logic:-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup><mi>no</mi><mo>.</mo></math>) &times; 1.5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi>st</mi></msup></math>no.<br>(36 : 24):- (24) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = 36<br>(12 : 8):- (8) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = 12<br>(72 : 48):- (48) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = 72<br>but<br>(24 : 4):- (4) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = 6 &ne; 24</p>",
                    solution_hi: "<p>3.(a)<br><strong>तर्क :- </strong>(दूसरी संख्या) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = पहली संख्या <br>(36 : 24):- (24) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = 36<br>(12 : 8):- (8) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = 12<br>(72 : 48):- (48) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = 72<br>लेकिन,D <br>(24 : 4):- (4) <math display=\"inline\"><mo>&#215;</mo></math> 1.5 = 6 &ne; 24</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Which of the following letter-clusters will replace the question mark (?) in the given series?<br>PTPA, OSOB, NRNC, ?, LPLE</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन सा अक्षर-समूह दी गई श्रृंखला में प्रश्न चिह्न (?) का स्थान लेगा?<br>PTPA, OSOB, NRNC, ?, LPLE</p>",
                    options_en: ["<p>MQND</p>", "<p>MQMC</p>", 
                                "<p>NQMD</p>", "<p>MQMD</p>"],
                    options_hi: ["<p>MQND</p>", "<p>MQMC</p>",
                                "<p>NQMD</p>", "<p>MQMD</p>"],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250364.png\" alt=\"rId9\" width=\"376\" height=\"118\"></p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250364.png\" alt=\"rId9\" width=\"376\" height=\"118\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement.&nbsp;<br><strong>Statements :</strong> <br>All paints are houses. <br>All houses are wood. <br>Some streets are wood. <br><strong>Conclusion (I) :</strong> Some streets are houses. <br><strong>Conclusion (II) :</strong> All paints are wood</p>",
                    question_hi: "<p>5. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong> <br>सभी पेंट, घर हैं। <br>सभी घर, लकड़ी हैं।<br>कुछ सड़कें, लकड़ी हैं। <br><strong>निष्कर्ष (I) : </strong>कुछ सड़कें, घर हैं। <br><strong>निष्कर्ष (II) : </strong>सभी पेंट, लकड़ी हैं।</p>",
                    options_en: ["<p>Neither conclusion (I) nor (II) follows.</p>", "<p>Both conclusions (I) and (II) follow</p>", 
                                "<p>Only conclusion (I) follows.</p>", "<p>Only conclusion (II) follows.</p>"],
                    options_hi: ["<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>", "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                                "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>", "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>"],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250550.png\" alt=\"rId10\" width=\"257\" height=\"90\"><br>Only conclusion II follow.</p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250769.png\" alt=\"rId11\" width=\"259\" height=\"90\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a code language, \'DILIP\' is coded as 46-36-30-36-22 and \'PRINCE\' is coded as 22-18-36 26-48-44. How will \'FIROZ\' be coded in the same language ?</p>",
                    question_hi: "<p>6. एक कूट भाषा में, \'DILIP\' को 46-36-30-36-22 के रूप में कूटबद्ध किया जाता है और \'PRINCE\' को 22-18-36-26-48-44 के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'FIROZ\' को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: ["<p>48-36-18-38-6</p>", "<p>56-36-18-32-52</p>", 
                                "<p>42-36-18-24-2</p>", "<p>2-36-18-42-52</p>"],
                    options_hi: ["<p>48-36-18-38-6</p>", "<p>56-36-18-32-52</p>",
                                "<p>42-36-18-24-2</p>", "<p>2-36-18-42-52</p>"],
                    solution_en: "<p>6.(c) <strong>Logic :-</strong> (Place value of opposite letter) &times; 2<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851250960.png\" alt=\"rId12\" width=\"128\" height=\"150\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251071.png\" alt=\"rId13\" width=\"152\" height=\"150\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251170.png\" alt=\"rId14\" width=\"116\" height=\"150\"></p>",
                    solution_hi: "<p>6.(c) <strong>तर्क :- </strong>(विपरीत अक्षर का स्थानीय मान) &times; 2<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251281.png\" alt=\"rId15\" width=\"115\" height=\"150\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251375.png\" alt=\"rId16\" width=\"136\" height=\"151\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251492.png\" alt=\"rId17\" width=\"117\" height=\"150\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the son of B&rsquo;,<br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the brother of B&rsquo;,<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo;,<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the daughter of B&rsquo;.<br>Based on the above, how is L related to M if &lsquo;L &ndash; N &divide; O &times; M + P&rsquo; ?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में,<br>&lsquo;A + B&rsquo; का अर्थ है कि &lsquo;A , B का पुत्र है&rsquo;,<br>&lsquo;A &ndash; B&rsquo; का अर्थ है कि &lsquo;A, B का भाई है&rsquo;,<br>&lsquo;A &times; B&rsquo; का अर्थ है कि &lsquo;A, B की पत्नी है,<br>&lsquo;A &divide; B&rsquo; का अर्थ है कि &lsquo;A, B की पुत्री है&rsquo;।<br>उपरोक्त के आधार पर, यदि &lsquo;L &ndash; N &divide; O &times; M + P&rsquo; है, तो L का M से क्या संबंध है?</p>",
                    options_en: ["<p>Father</p>", "<p>Brother</p>", 
                                "<p>Father&rsquo;s father</p>", "<p>Son</p>"],
                    options_hi: ["<p>पिता</p>", "<p>भाई</p>",
                                "<p>दादा</p>", "<p>पुत्र</p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251593.png\" alt=\"rId18\" width=\"151\" height=\"135\"><br>L is the son of M.</p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251593.png\" alt=\"rId18\" width=\"151\" height=\"135\"><br>L, M का पुत्र है.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Which of the following numbers will replace the question mark (?) in the given series ?<br>214, 111, 179, 126,144, 141, 109, 156, ?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>214, 111, 179, 126,144, 141, 109, 156, ?</p>",
                    options_en: ["<p>61</p>", "<p>96</p>", 
                                "<p>89</p>", "<p>74</p>"],
                    options_hi: ["<p>61</p>", "<p>96</p>",
                                "<p>89</p>", "<p>74</p>"],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251727.png\" alt=\"rId19\" width=\"349\" height=\"106\"></p>",
                    solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251727.png\" alt=\"rId19\" width=\"349\" height=\"106\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. How many people like either only spicy or only sweet as per the given Venn diagram ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251867.png\" alt=\"rId20\" width=\"176\" height=\"170\"></p>",
                    question_hi: "<p>9. दिए गए वेन आरेख के अनुसार कितने लोग या तो केवल मसालेदार या केवल मीठा पसंद करते हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851251967.png\" alt=\"rId21\" width=\"169\" height=\"180\"> <br>Sweet - मीठा, Sour - खट्टा, Spicy - मसालेदार</p>",
                    options_en: ["<p>110</p>", "<p>120</p>", 
                                "<p>135</p>", "<p>144</p>"],
                    options_hi: ["<p>110</p>", "<p>120</p>",
                                "<p>135</p>", "<p>144</p>"],
                    solution_en: "<p>9.(b)<br>No. of people like either only spicy or only sweet = 65 + 55 = 120</p>",
                    solution_hi: "<p>9.(b)<br>केवल मसालेदार या केवल मीठा पसंद करने वाले लोगों की संख्या = 65 + 55 = 120</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the option that represents the letters that when placed from left to right in the blanks below will complete the given letter series.<br>MN_OJKM_QOJK_NQ_JKMNQO_KMN</p>",
                    question_hi: "<p>10. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर दी गई अक्षर श्रृंखला पूरी हो जाएगी।<br>MN_OJKM_QOJK_NQ_JKMNQO_KMN</p>",
                    options_en: ["<p>QMNJO</p>", "<p>QNJMO</p>", 
                                "<p>QNMJO</p>", "<p>QNMOJ</p>"],
                    options_hi: ["<p>QMNJO</p>", "<p>QNJMO</p>",
                                "<p>QNMJO</p>", "<p>QNMOJ</p>"],
                    solution_en: "<p>10.(d)<br>MN<span style=\"text-decoration: underline;\"><strong>Q</strong></span>OJK/M<span style=\"text-decoration: underline;\"><strong>N</strong></span>QOJK/<span style=\"text-decoration: underline;\"><strong>M</strong></span>NQ<span style=\"text-decoration: underline;\"><strong>O</strong></span>JK/MNQO<span style=\"text-decoration: underline;\"><strong>J</strong></span>K/MN</p>",
                    solution_hi: "<p>10.(d)<br>MN<span style=\"text-decoration: underline;\"><strong>Q</strong></span>OJK/M<span style=\"text-decoration: underline;\"><strong>N</strong></span>QOJK/<span style=\"text-decoration: underline;\"><strong>M</strong></span>NQ<span style=\"text-decoration: underline;\"><strong>O</strong></span>JK/MNQO<span style=\"text-decoration: underline;\"><strong>J</strong></span>K/MN</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Six letters I, O, A, Y, Z and X are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to O.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252071.png\" alt=\"rId22\" width=\"158\" height=\"77\"></p>",
                    question_hi: "<p>11. एक पासे के विभिन्न फलकों पर छह अक्षर I, O, A, Y, Z और X लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। O के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252071.png\" alt=\"rId22\" width=\"158\" height=\"77\"></p>",
                    options_en: ["<p>A</p>", "<p>Z</p>", 
                                "<p>X</p>", "<p>Y</p>"],
                    options_hi: ["<p>A</p>", "<p>Z</p>",
                                "<p>X</p>", "<p>Y</p>"],
                    solution_en: "<p>11.(d)<br>From the two dice the opposite faces are<br>I &harr; X , A &harr; Z , O &harr; Y</p>",
                    solution_hi: "<p>11.(d)<br>दोनों पासों के विपरीत फलक हैं<br>I &harr; X , A &harr; Z , O &harr; Y</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. A, B, C, D, E, F and G are sitting around a circular table facing the centre (but not necessarily in the same order). G sits third to the right of A. D sits third to the right of G. E is an immediate neighbour of both A and F. C sits third to the right of F. How many people sit between E and D when counted from the right of E ?</p>",
                    question_hi: "<p>12. A, B, C, D, E, F और G एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। G, A के दाएं से तीसरे स्थान पर बैठा है। D, G के दाएं से तीसरे स्थान पर बैठा है। A और F दोनों का निकटतम पड़ोसी E है। C, F के दाएं से तीसरे स्थान पर बैठा है। E के दाएं से गिनने पर E और D के बीच में कितने व्यक्ति बैठे हैं ?</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252193.png\" alt=\"rId23\" width=\"143\" height=\"115\"><br>Four people sit between E and D when counted from the right of E.</p>",
                    solution_hi: "<p>12.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252193.png\" alt=\"rId23\" width=\"143\" height=\"115\"><br>E के दाईं ओर से गिनती करने पर E और D के बीच चार व्यक्ति बैठे हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. If \'A\' stands for &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for &rsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation ?<br>91 B 2 D 22 A 11 C 8 = ?</p>",
                    question_hi: "<p>13. यदि \'A\' का अर्थ \'<math display=\"inline\"><mo>&#247;</mo></math> \' B\' का अर्थ \'x\', \'C\' का अर्थ &lsquo;+&lsquo;और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>91 B 2 D 22 A 11 C 8 = ?</p>",
                    options_en: ["<p>128</p>", "<p>188</p>", 
                                "<p>148</p>", "<p>168</p>"],
                    options_hi: ["<p>128</p>", "<p>188</p>",
                                "<p>148</p>", "<p>168</p>"],
                    solution_en: "<p>13.(b)<br><strong>Given :- </strong>91 B 2 D 22 A 11 C 8<br>As per given instruction after interchanging the letter with sign we get<br>91 &times; 2 - 22 <math display=\"inline\"><mo>&#247;</mo></math> 11 + 8<br>182 - 2 + 8 = 188</p>",
                    solution_hi: "<p>13.(b)<br><strong>दिया गया :-</strong> 91 B 2 D 22 A 11 C 8<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>91 &times; 2 - 22 <math display=\"inline\"><mo>&#247;</mo></math> 11 + 8<br>182 - 2 + 8 = 188</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different. The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>14. चार अक्षर समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं। उस असमान विकल्प को चुनिए। अक्षर समूह में, असमान विकल्प व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>RTTUWW</p>", "<p>NOPPSS</p>", 
                                "<p>ACCDFF</p>", "<p>IKKLNN</p>"],
                    options_hi: ["<p>RTTUWW</p>", "<p>NOPPSS</p>",
                                "<p>ACCDFF</p>", "<p>IKKLNN</p>"],
                    solution_en: "<p>14.(b)&nbsp;By observing four options we can see that in each option except option (b) the 2nd letter is repeated two times.<br>Hence, option (b) is an odd one out.</p>",
                    solution_hi: "<p>14.(b)&nbsp;चार विकल्पों को देखकर हम देख सकते हैं कि विकल्प (b) को छोड़कर प्रत्येक विकल्प में दूसरा अक्षर दो बार दोहराया गया है।<br>इसलिए, विकल्प (b) असमान है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. The position of how many letters will remain unchanged if all the letters in the word BINDER are arranged in English alphabetical order ?</p>",
                    question_hi: "<p>15. यदि BINDER शब्द के सभी अक्षरों को वर्णानुक्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित बनी रहेगी ?</p>",
                    options_en: ["<p>None</p>", "<p>One</p>", 
                                "<p>Two</p>", "<p>Three</p>"],
                    options_hi: ["<p>किसी का भी नहीं</p>", "<p>एक</p>",
                                "<p>दो</p>", "<p>तीन</p>"],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252272.png\" alt=\"rId24\" width=\"193\" height=\"124\"><br>The position of the two letters remain unchanged.</p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252272.png\" alt=\"rId24\" width=\"193\" height=\"124\"><br>दोनों अक्षरों का स्थान अपरिवर्तित रहता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252365.png\" alt=\"rId25\" width=\"264\" height=\"90\"></p>",
                    question_hi: "<p>16. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252365.png\" alt=\"rId25\" width=\"264\" height=\"90\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252455.png\" alt=\"rId26\" width=\"74\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252549.png\" alt=\"rId27\" width=\"70\" height=\"71\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252648.png\" alt=\"rId28\" width=\"71\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252735.png\" alt=\"rId29\" width=\"69\" height=\"70\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252455.png\" alt=\"rId26\" width=\"73\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252549.png\" alt=\"rId27\" width=\"69\" height=\"70\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252648.png\" alt=\"rId28\" width=\"71\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252735.png\" alt=\"rId29\" width=\"69\" height=\"70\"></p>"],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252648.png\" alt=\"rId28\" width=\"71\" height=\"70\"></p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252648.png\" alt=\"rId28\" width=\"72\" height=\"71\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. If 3 April 1996 was Wednesday, then what was the day of the week on 7 April 2001 ?</p>",
                    question_hi: "<p>17. यदि 3 अप्रैल 1996 को बुधवार था, तो 7 अप्रैल 2001 को सप्ताह का कौन-सा दिन रहा होगा ?</p>",
                    options_en: ["<p>Saturday</p>", "<p>Sunday</p>", 
                                "<p>Friday</p>", "<p>Monday</p>"],
                    options_hi: ["<p>शनिवार</p>", "<p>रविवार</p>",
                                "<p>शुक्रवार</p>", "<p>सोमवार</p>"],
                    solution_en: "<p>17.(a)<br>3 April 1996 is Wednesday. On going to 3 April 2001 number of odd days are <br>+1 + 1 + 1 + 2 + 1 = 6. We have reached till 3 April 2001, we have to reach 7 April, number of days between = 4. Total number of odd days = 6 + 4 = 10.On dividing 10 by 7 remainder = 3. Wednesday + 3 = Saturday.</p>",
                    solution_hi: "<p>17.(a) <br>3 अप्रैल 1996 को बुधवार है. 3 अप्रैल 2001 को विषम दिनों की संख्या है <br>+1 + 1 + 1 + 2 + 1 = 6. हम 3 अप्रैल 2001 तक पहुँच चुके हैं, हमें 7 अप्रैल तक पहुँचना है, बीच के दिनों की संख्या = 4. विषम दिनों की कुल संख्या = 6 + 4 = 10, 10 को 7 से विभाजित करने पर शेष = 3. बुधवार + 3 = शनिवार।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, &lsquo;revise the lesson&rsquo; is written as &lsquo;rx cd st&rsquo; and &lsquo;the lesson plan&rsquo; is written as &lsquo;cd nq rx&rsquo;. How is &lsquo;revise&rsquo; written in the given language ?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में \'revise the lesson\' को \'rx cd st\' लिखा जाता है और \'the lesson plan\' को \'cd nq rx\' लिखा जाता है। दी गई भाषा में \'revise\' कैसे लिखा जाता है ?</p>",
                    options_en: ["<p>nq</p>", "<p>cd</p>", 
                                "<p>st</p>", "<p>rx</p>"],
                    options_hi: ["<p>nq</p>", "<p>cd</p>",
                                "<p>st</p>", "<p>rx</p>"],
                    solution_en: "<p>18.(c) revise the lesson &rarr; rx cd st&hellip;&hellip;..(i)<br>the lesson plan &rarr; cd nq rx&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;the lesson&rsquo; and &lsquo;cd rx&rsquo; are common. The code of &lsquo;revise&rsquo; = &lsquo;st&rsquo;</p>",
                    solution_hi: "<p>18.(c) revise the lesson &rarr; rx cd st&hellip;&hellip;..(i)<br>the lesson plan &rarr; cd nq rx&hellip;&hellip;.(ii)<br>(i) और (ii) से \'the lesson\' और \'cd rx\' उभय-निष्ठ हैं। \'revise\' का कोड = \'st\'</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Which figure should replace the question mark (?) if the following series were to be continued?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252871.png\" alt=\"rId30\" width=\"303\" height=\"60\"></p>",
                    question_hi: "<p>19. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252871.png\" alt=\"rId30\" width=\"303\" height=\"60\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252990.png\" alt=\"rId31\" width=\"65\" height=\"65\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253089.png\" alt=\"rId32\" width=\"66\" height=\"65\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253228.png\" alt=\"rId33\" width=\"66\" height=\"65\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253399.png\" alt=\"rId34\" width=\"68\" height=\"66\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851252990.png\" alt=\"rId31\" width=\"66\" height=\"65\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253089.png\" alt=\"rId32\" width=\"67\" height=\"65\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253228.png\" alt=\"rId33\" width=\"67\" height=\"66\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253399.png\" alt=\"rId34\" width=\"66\" height=\"65\"></p>"],
                    solution_en: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253399.png\" alt=\"rId34\" width=\"72\" height=\"70\"></p>",
                    solution_hi: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253399.png\" alt=\"rId34\" width=\"72\" height=\"70\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Three of the following number-pairs are alike in some manner and hence form a group. Which number-pair does not belong to that group?&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>20. निम्नलिखित संख्या-युग्मों में से तीन किसी तरह से समान हैं और इसलिए एक समूह बनाते हैं। कौन-सा संख्या-युग्म उस समूह से संबंधित नहीं है ?&nbsp;(नोट : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रियाएं जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>14 - 121</p>", "<p>21 - 361</p>", 
                                "<p>18 - 256</p>", "<p>11 - 81</p>"],
                    options_hi: ["<p>14 - 121</p>", "<p>21 - 361</p>",
                                "<p>18 - 256</p>", "<p>11 - 81</p>"],
                    solution_en: "<p>20.(a) <br><strong>Logic:-</strong> (1st number - 2)&sup2; = 2nd number<br>(21 - 361):- (21 - 2)&sup2; &rArr; (19)&sup2; = 361<br>(18 - 256) :- (18 - 2)&sup2; &rArr; (16)&sup2; = 256<br>(11 - 81):- (11 -2)&sup2; &rArr; (9)&sup2; = 81<br>But,<br>(14 - 121) :- (14 - 2)&sup2; &rArr; (12)&sup2; = 144(Not 121)</p>",
                    solution_hi: "<p>20.(a) <br><strong>तर्क:-</strong> (पहली संख्या - 2)&sup2; = दूसरी संख्या<br>(21 - 361):- (21 - 2)&sup2; &rArr; (19)&sup2; = 361<br>(18 - 256) :- (18 - 2)&sup2; &rArr; (16)&sup2; = 256<br>(11 - 81):- (11 -2)&sup2; &rArr; (9)&sup2; = 81<br>लेकिन,<br>(14 - 121) :- (14 - 2)&sup2; &rArr; (12)&sup2; = 144 (121 नहीं)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253500.png\" alt=\"rId35\" width=\"114\" height=\"100\"></p>",
                    question_hi: "<p>21. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253500.png\" alt=\"rId35\" width=\"114\" height=\"100\"></p>",
                    options_en: ["<p>16</p>", "<p>17</p>", 
                                "<p>18</p>", "<p>19</p>"],
                    options_hi: ["<p>16</p>", "<p>17</p>",
                                "<p>18</p>", "<p>19</p>"],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253617.png\" alt=\"rId36\" width=\"168\" height=\"157\"><br>There are 19 triangles = AA&rsquo;D, DEF, A&rsquo;BC, FHC, FGC, CIK, JIK, CKJ, XYU, XWV, XVU, XVY, TUY, UST, SS&rsquo;R, POQ, PP&rsquo;Q, NLM, MM&rsquo;Q .</p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253617.png\" alt=\"rId36\" width=\"168\" height=\"157\"><br>कुल 19 त्रिभुज हैं = AA&rsquo;D, DEF, A&rsquo;BC, FHC, FGC, CIK, JIK, CKJ, XYU, XWV, XVU, XVY, TUY, UST, SS&rsquo;R, POQ, PP&rsquo;Q, NLM, MM&rsquo;Q .</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. 100 is related to 20 following a certain logic. Following the same logic, 250 is related to&nbsp;50. To which of the following is 550 related, following the same logic?&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g. 13- Operations on 13 such as&nbsp;adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and&nbsp;3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>22. एक निश्चित तर्क का अनुसरण करते हुए 100, 20 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 250,&nbsp;50 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 550 निम्नलिखत में से किससे संबंधित है?&nbsp;नोट: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएँ की&nbsp;जानी चाहिए। उदाहरण के लिए 13-13 पर संक्रियाएँ जैसे 13 को जोड़ना/घटाना/गुणा&nbsp;करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर&nbsp;गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>105</p>", "<p>115</p>", 
                                "<p>110</p>", "<p>100</p>"],
                    options_hi: ["<p>105</p>", "<p>115</p>",
                                "<p>110</p>", "<p>100</p>"],
                    solution_en: "<p>22.(c) <br><strong>Logic :-</strong> (2nd number) &times; 5 = 1st number<br>(100, 20) :- (20) &times; 5 = 100<br>(250 , 50) :- (50) &times; 5 = 250<br>Similarly,<br>(550, 110) :- (110) &times; 5 = 550</p>",
                    solution_hi: "<p>22.(c) <strong>तर्क :-</strong> (दूसरी संख्या) &times; 5 = पहली संख्या<br>(100, 20) :- (20) &times; 5 = 100<br>(250 , 50) :- (50) &times; 5 = 250<br>इसी प्रकार,<br>(550, 110) :- (110) &times; 5 = 550</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option figure in which the given figure is embedded as its part (rotation is NOT allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253725.png\" alt=\"rId37\" width=\"77\" height=\"80\"></p>",
                    question_hi: "<p>23. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति इसके भाग के रूप में अंतर्निहित है&nbsp;(घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253725.png\" alt=\"rId37\" width=\"77\" height=\"80\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253826.png\" alt=\"rId38\" width=\"68\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253954.png\" alt=\"rId39\" width=\"61\" height=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254065.png\" alt=\"rId40\" width=\"62\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254170.png\" alt=\"rId41\" width=\"54\" height=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253826.png\" alt=\"rId38\" width=\"68\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851253954.png\" alt=\"rId39\" width=\"61\" height=\"80\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254065.png\" alt=\"rId40\" width=\"62\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254170.png\" alt=\"rId41\" width=\"54\" height=\"80\"></p>"],
                    solution_en: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254285.png\" alt=\"rId42\" width=\"69\" height=\"100\"></p>",
                    solution_hi: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254285.png\" alt=\"rId42\" width=\"69\" height=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the set in which the numbers are related in the same way as are the numbers of the following sets.&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(245, 49, 5)<br>(144, 9, 16)</p>",
                    question_hi: "<p>24. उस समुच्चय का चयन कीजिए जिसमें संख्याएँ ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए&nbsp;समुच्चयों की संख्याएँ संबंधित हैं।&nbsp;नोट : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएँ की&nbsp;जानी चाहिए। उदाहरण के लिए 13-13 पर संक्रियाएँ जैसे 13 को जोड़ना/घटाना/गुणा&nbsp;करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर&nbsp;गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(245, 49, 5)<br>(144, 9, 16)</p>",
                    options_en: ["<p>(186, 21, 8)</p>", "<p>(246, 9, 28)</p>", 
                                "<p>(200, 8,25)</p>", "<p>(312, 24, 9)</p>"],
                    options_hi: ["<p>(186, 21, 8)</p>", "<p>(246, 9, 28)</p>",
                                "<p>(200, 8,25)</p>", "<p>(312, 24, 9)</p>"],
                    solution_en: "<p>24.(c) <br><strong>Logic :-</strong> (2nd number &times; 3rd number) = 1st number<br>(245, 49, 5) :- (49 &times; 5) = 245<br>(144, 9, 16) :- (9 &times; 16) = 144<br>Similarly,<br>(200 , 8, 25) :- (8 &times; 25) = 200</p>",
                    solution_hi: "<p>24.(c) <strong>तर्क :- </strong>(दूसरी संख्या &times; तीसरी संख्या) = पहली संख्या<br>(245, 49, 5) :- (49 &times; 5) = 245<br>(144, 9, 16) :- (9 &times; 16) = 144<br>इसी प्रकार,<br>(200 , 8, 25) :- (8 &times; 25) = 200</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. 10 years ago, a man&rsquo;s age was 5 times of his son&rsquo;s age. 2 years hence, twice his age will be equal to 4 times the age of his son. What is the present age (in years) of the son?</p>",
                    question_hi: "<p>25. 10 वर्ष पहले, एक आदमी की आयु उसके पुत्र की आयु की 5 गुना थी। 2 वर्ष बाद, उसकी आयु का दोगुना उसके पुत्र की आयु के 4 गुना के बराबर होगी। पुत्र की वर्तमान आयु (वर्षों में) क्या है?</p>",
                    options_en: ["<p>14</p>", "<p>16</p>", 
                                "<p>20</p>", "<p>18</p>"],
                    options_hi: ["<p>14</p>", "<p>16</p>",
                                "<p>20</p>", "<p>18</p>"],
                    solution_en: "<p>25.(a) <br>Let the present age of the man and his son be 5x&nbsp;+ 10 and x + 10 <br>According to the question<br>2(5x&nbsp;+ 10 + 2) = (x + 10 + 2)4 <br>(5x&nbsp;+ 12) = (x + 12)2<br>5x&nbsp;- 2x = 24 - 12<br>3x&nbsp;= 12, x = 4<br>Hence, the present age of the son = 4 + 10 = 14 years</p>",
                    solution_hi: "<p>25.(a) <br>माना कि आदमी और उसके बेटे की वर्तमान आयु 5x&nbsp;+ 10 और x +10 है <br>प्रश्न के अनुसार<br>2(5x&nbsp;+ 10 + 2) = (x + 10 + 2)4 <br>(5x&nbsp;+ 12) = (x + 12)2<br>5x&nbsp;- 2x = 24 - 12<br>3x&nbsp;= 12, x = 4<br>अत: पुत्र की वर्तमान आयु = 4 + 10 = 14 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The All India Kabaddi Federation came into existence in the year ____________ and compiled standard rules.</p>",
                    question_hi: "<p>26. ऑल इंडिया कबड्डी फेडरेशन (All India Kabaddi Federation) वर्ष__________में अस्तित्व में आया और इसने मानक नियमों का संकलन किया।</p>",
                    options_en: ["<p>1951</p>", "<p>1952</p>", 
                                "<p>1953</p>", "<p>1950</p>"],
                    options_hi: ["<p>1951</p>", "<p>1952</p>",
                                "<p>1953</p>", "<p>1950</p>"],
                    solution_en: "<p>26.(d) <strong>1950.</strong> <br>Kabaddi is a combative sport played with seven players on each side. The players on the defensive side are called \"Antis,\" while the player on the offense is known as the \"Raider.\" Kabaddi Federations and Establishment Years : Amateur Kabaddi Federation of India (AKFI) was established in 1972, International Kabaddi Federation was founded in 2004, with its headquarters in Jaipur.</p>",
                    solution_hi: "<p>26.(d) <strong>1950.</strong> <br>कबड्डी एक संघर्षपूर्ण खेल है, जिसमें प्रत्येक पक्ष में सात खिलाड़ी होते हैं। रक्षात्मक पक्ष के खिलाड़ियों को \"एंटीस\" कहा जाता है, जबकि आक्रामक पक्ष के खिलाड़ी को \"रेडर\" कहा जाता है। कबड्डी संघ और स्थापना वर्ष: एमेच्योर कबड्डी फेडरेशन ऑफ इंडिया (AKFI) की स्थापना 1972 में हुई थी तथा अंतर्राष्ट्रीय कबड्डी महासंघ की स्थापना 2004 में हुई थी, जिसका मुख्यालय जयपुर में है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which ministry declared 2025 as the \"Year of Reforms\"?</p>",
                    question_hi: "<p>27 किस मंत्रालय ने 2025 को \"सुधारों का वर्ष\" घोषित किया है?</p>",
                    options_en: ["<p>Ministry of Home Affairs</p>", "<p>Ministry of Defence</p>", 
                                "<p>Ministry of External Affairs</p>", "<p>Ministry of Finance</p>"],
                    options_hi: ["<p>गृह मंत्रालय</p>", "<p>रक्षा मंत्रालय</p>",
                                "<p>विदेश मंत्रालय</p>", "<p>वित्त मंत्रालय</p>"],
                    solution_en: "<p>27.(b) Ministry of Defence. This decision was unanimously taken during a meeting chaired by Rajnath Singh, with the objective of transforming the triservices into a technologically-advanced combatready force capable of multi-domain integrated operations. The Department of Defence became the Ministry of Defence under a Cabinet Minister in August 1947.</p>",
                    solution_hi: "<p>27.(b) रक्षा मंत्रालय। यह निर्णय राजनाथ सिंह की अध्यक्षता में हुई बैठक के दौरान सर्वसम्मति से लिया गया, जिसका उद्देश्य तीनों सेनाओं को तकनीकी रूप से उन्नत लड़ाकू बल में बदलना है, जो बहु-डोमेन एकीकृत संचालन में सक्षम हो। अगस्त 1947 में रक्षा विभाग कैबिनेट मंत्री के अधीन रक्षा मंत्रालय बन गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28.This famous classical dancer and choreographer is known for popularizing the Odissi dance form and founding the Nrityagram dance village. Who is she?</p>",
                    question_hi: "<p>28. निम्नलिखित में से किस प्रसिद्ध शास्त्रीय नृत्यांगना और कोरियोग्राफर को ओडिसी नृत्य शैली को लोकप्रिय बनाने और नृत्यग्राम डांस विलेज (Nrityagram dance village) की स्थापना के लिए जाना जाता है।</p>",
                    options_en: ["<p>Sonal Mansingh</p>", "<p>Protima Gauri</p>", 
                                "<p>Rukmini Devi Arundale</p>", "<p>Sanjukta Panigrahi</p>"],
                    options_hi: ["<p>सोनल मानसिंह (Sonal Mansingh)</p>", "<p>प्रोतिमा गौरी (Protima Gauri)</p>",
                                "<p>रुक्मिणी देवी अरुंडेल (Rukmini Devi Arundale)</p>", "<p>संजुक्ता पाणिग्रही (Sanjukta Panigrahi)</p>"],
                    solution_en: "<p>28.(b) <strong>Protima Gauri. </strong>Sanjukta Panigrahi (Odissi) : Awards - Padma Shri (1975), Sangeet Natak Akademi Award (1976). Sonal Mansingh (Bharatanatyam and Odissi) : Awards - Padma Bhushan (1992), Padma Vibhushan (2003). Rukmini Devi Arundale (Bharatanatyam) : Awards - Padma Bhushan (1956), Sangeet Natak Akademi Award (1957).</p>",
                    solution_hi: "<p>28.(b) <strong>प्रोतिमा गौरी। </strong>संजुक्ता पाणिग्रही (ओडिसी): पुरस्कार - पद्म श्री (1975), संगीत नाटक अकादमी पुरस्कार (1976)। सोनल मानसिंह (भरतनाट्यम और ओडिसी): पुरस्कार - पद्म भूषण (1992), पद्म विभूषण (2003)। रुक्मिणी देवी अरुंडेल (भरतनाट्यम) : पुरस्कार - पद्म भूषण (1956), संगीत नाटक अकादमी पुरस्कार (1957)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who of the following was famous for Punjabi folk song and for singing a long breathless alaap?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन पंजाबी लोक गीत और बिना सांस लिए आलाप गाने के लिए प्रसिद्ध थी/था?</p>",
                    options_en: ["<p>Pranjal Dahiya</p>", "<p>Gurmeet Bawa</p>", 
                                "<p>Nimrat Khaira</p>", "<p>Sheenam Katholic</p>"],
                    options_hi: ["<p>प्रांजल दहिया</p>", "<p>गुरमीत बावा</p>",
                                "<p>निमरत खैरा</p>", "<p>शीनम कैथोलिक</p>"],
                    solution_en: "<p>29.(b)<strong> Gurmeet Bawa.</strong> She was known as Lambi hek di malika. She was the first Punjabi female singer to sing on Indian public service broadcaster, Doordarshan. Award: Padma Bhushan (2022).</p>",
                    solution_hi: "<p>29.(b) <strong>गुरमीत बावा।</strong> वह \'लंबी हेक दी मल्लिका\' के नाम से जानी जाती थीं। वह भारतीय सार्वजनिक सेवा प्रसारक, दूरदर्शन पर गाने वाली पहली पंजाबी महिला गायिका थीं। पुरस्कार: पद्म भूषण (2022)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In any tennis game, if both players win three points each in a game (i.e. score is 40-40), then it\'s called a _______ .</p>",
                    question_hi: "<p>30. किसी भी टेनिस खेल में, यदि दोनों खिलाड़ी एक खेल में तीन-तीन अंक जीतते हैं (अर्थात स्कोर 40-40 है), तो इसे __________ कहा जाता है।</p>",
                    options_en: ["<p>equal</p>", "<p>deuce</p>", 
                                "<p>back</p>", "<p>rattle</p>"],
                    options_hi: ["<p>इक्वल (equal)</p>", "<p>ड्यूस (deuce)</p>",
                                "<p>बैक (back)</p>", "<p>रेटल (rattle)</p>"],
                    solution_en: "<p>30.(b) <strong>Deuce.</strong> Tennis scoring system - A player or team has to win 4 points to win a game. Any game starts at 0-0 and the zero point in tennis is called love. The progression of points occurs as follows: First point - 15, Second point - 30, Third point - 40, Fourth point - Game. After deuce, the player who wins the next point has advantage.</p>",
                    solution_hi: "<p>30.(b) <strong>ड्यूस।</strong> टेनिस स्कोरिंग सिस्टम - किसी खिलाड़ी या टीम को गेम जीतने के लिए 4 अंक जीतने होते हैं। कोई भी गेम 0-0 से शुरू होता है और टेनिस में शून्य अंक को लव (love) कहा जाता है। अंकों की प्रगति इस प्रकार होती है: पहला अंक - 15, दूसरा अंक - 30, तीसरा अंक - 40, चौथा अंक - गेम। ड्यूस के बाद, अगला अंक जीतने वाले खिलाड़ी को लाभ मिलता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following is the highest award for dance in India?</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन-सा भारत में नृत्य के लिए दिया जाने वाला सर्वोच्च पुरस्कार है ?</p>",
                    options_en: ["<p>Guru Debaprasad Award</p>", "<p>Nritya Shiromani</p>", 
                                "<p>Kalidas Samman</p>", "<p>Sangeet Natak Akademi Award</p>"],
                    options_hi: ["<p>गुरु देबप्रसाद पुरस्कार</p>", "<p>नृत्यशिरोमणि</p>",
                                "<p>कालिदास सम्मान</p>", "<p>संगीत नाटक अकादमी पुरस्कार</p>"],
                    solution_en: "<p>31.(d) <strong>Sangeet Natak Akademi Award </strong>is given by the Sangeet Natak Akademi and was first awarded in 1952. It consists of a cash prize of ₹1,00,000, a citation, an angavastram (a shawl), and a tamrapatra (a brass plaque). The National Nritya Shiromani Award is given to outstanding dance performers and musicians from India and abroad. The Kalidas Samman is presented by the Government of Madhya Pradesh.</p>",
                    solution_hi: "<p>31.(d) <strong>संगीत नाटक अकादमी पुरस्कार</strong>, संगीत नाटक अकादमी द्वारा दिया जाता है और इसे पहली बार 1952 में प्रदान किया गया था। इसमें ₹1,00,000 का नकद पुरस्कार, एक प्रशस्ति पत्र, एक अंगवस्त्रम (एक शॉल) और एक ताम्रपत्र (पीतल की पट्टिका) शामिल है। राष्ट्रीय नृत्य शिरोमणि पुरस्कार भारत और विदेश के उत्कृष्ट नृत्य कलाकारों और संगीतकारों को दिया जाता है। कालिदास सम्मान मध्य प्रदेश सरकार द्वारा प्रदान किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Identify the Mughal emperor who was imprisoned for the rest of his life in Agra after the war of succession among his sons.</p>",
                    question_hi: "<p>32. उस मुगल बादशाह की पहचान करें, जिसे उसके बेटों के बीच हुए उत्तराधिकार के युद्ध के बाद आगरा में जीवन भर के लिए कैद कर दिया गया।</p>",
                    options_en: ["<p>Aurangzeb</p>", "<p>Bahadur Shah I</p>", 
                                "<p>Shah Jahan</p>", "<p>Jahandar Shah</p>"],
                    options_hi: ["<p>औरंगजेब</p>", "<p>बहादुर शाह प्रथम</p>",
                                "<p>शाहजहाँ</p>", "<p>जहाँदार शाह</p>"],
                    solution_en: "<p>32.(c)<strong> Shah Jahan </strong>(Mirza Shahab-ud-din Baig Muhammad Khan Khurram). He was the fifth Mughal emperor and reigned from 1628 to 1658. Aurangzeb declared himself emperor in 1658 and imprisoned Shah Jahan in Agra Fort until his death in January 1666. Some notable works of Shah Jahan : Red fort of Delhi, Taj Mahal, The Jama Masjid of Delhi, The Pearl Mosque (Moti Masjid) of Agra, The Peacock Throne etc.</p>",
                    solution_hi: "<p>32.(c) <strong>शाहजहाँ</strong> (मिर्ज़ा शहाब-उद-दीन बेग मुहम्मद खान खुर्रम), पाँचवां मुगल सम्राट था और इसने 1628 से 1658 तक शासन किया। औरंगजेब ने 1658 में स्वयं को सम्राट घोषित कर दिया और शाहजहाँ को जनवरी 1666 में उसकी मृत्यु तक आगरा किले में कैद रखा। शाहजहाँ के कुछ उल्लेखनीय कार्य: दिल्ली का लाल किला, ताजमहल, दिल्ली की जामा मस्जिद, आगरा का मोती मस्जिद, मयूर सिंहासन आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. In which Indian state is the \'International Lusophone Festival\' hosted?</p>",
                    question_hi: "<p>33.\'अंतर्राष्ट्रीय लूसोफोन महोत्सव (\'International Lusophone Festival)\' का आयोजन किस भारतीय राज्य में किया जाता है?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Goa</p>", 
                                "<p>Rajasthan</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>केरल</p>", "<p>गोवा</p>",
                                "<p>राजस्थान</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>33.(b) <strong>Goa. </strong>The International Lusophone Festival seeks to strengthen India&rsquo;s connection with the Lusophone world (Portugeese-speaking people). Goa has historical ties to the Lusophone world and Portuguese cultural institutions. Famous festivals of Goa : Goa Carnival, Fatorpa Zatra, Three Kings Feast, Easter, Shigmo, Shirgao Jatra, Sao Joao Festival, Sangodd, and Chikalkalo.</p>",
                    solution_hi: "<p>33.(b) <strong>गोवा। </strong>अंतर्राष्ट्रीय लुसोफोन महोत्सव का उद्देश्य लुसोफोन दुनिया (पुर्तगाली भाषी लोग) के साथ भारत के संबंध को मजबूत करना है। गोवा का लुसोफोन दुनिया और पुर्तगाली सांस्कृतिक संस्थाओं के साथ ऐतिहासिक संबंध है। गोवा के प्रसिद्ध त्यौहार: गोवा कार्निवल, फतोरपा जात्रा, तीन राजाओं की दावत, ईस्टर, शिग्मो, शिरगाओ जात्रा, साओ जोआओ महोत्सव, सांगोद और चिखल कालो।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. All India Kisan Sabha was founded in 1936 at Indian National Congress (INC) ________ Session as All India Kisan Congress.</p>",
                    question_hi: "<p>34. अखिल भारतीय किसान सभा की स्थापना 1936 में भारतीय राष्ट्रीय कांग्रेस (INC) के __________ अधिवेशन में अखिल भारतीय किसान कांग्रेस के रूप में की गई थी।</p>",
                    options_en: ["<p>Lucknow</p>", "<p>Agra</p>", 
                                "<p>Bombay</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>लखनऊ</p>", "<p>आगरा</p>",
                                "<p>बॉम्बे</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>34.(a) <strong>Lucknow. </strong>Swami Sahajananda Saraswati, who founded the Bihar Provincial Kisan Sabha in 1929, also led the formation of the All India Kisan Sabha (AIKS) during the Indian National Congress\' Lucknow Session in 1936. The AIKS, also known as the Akhil Bharatiya Kisan Sabha.</p>",
                    solution_hi: "<p>34.(a) <strong>लखनऊ।</strong> स्वामी सहजानंद सरस्वती, जिन्होंने 1929 में बिहार प्रांतीय किसान सभा की स्थापना की थी, साथ ही इन्होंने 1936 ईस्वी में भारतीय राष्ट्रीय कांग्रेस के लखनऊ अधिवेशन के दौरान अखिल भारतीय किसान सभा (AIKS) के गठन का भी नेतृत्व किया। AIKS को अखिल भारतीय किसान सभा के रूप में भी जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. According to the data from the Ministry of Mines, the largest resources of gold ore (primary) are located in which of the following Indian states?</p>",
                    question_hi: "<p>35. खान मंत्रालय के आंकड़ों के अनुसार, सोने के अयस्क (प्राथमिक) के सबसे बड़े संसाधन निम्नलिखित में से किस भारतीय राज्य में स्थित हैं ?</p>",
                    options_en: ["<p>West Bengal</p>", "<p>Bihar</p>", 
                                "<p>Rajasthan</p>", "<p>Karnataka</p>"],
                    options_hi: ["<p>पश्चिम बंगाल</p>", "<p>बिहार</p>",
                                "<p>राजस्थान</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p>35.(b)<strong> Bihar. </strong>In India, the largest resources of gold ore (primary) are located in Bihar (44%) followed by Rajasthan (25%), Karnataka (21%), West Bengal (3%), Andhra Pradesh (3% ), Jharkhand (2 %).</p>",
                    solution_hi: "<p>35.(b) <strong>बिहार।</strong> भारत में, सोने के अयस्क के सबसे बड़े संसाधन भंडार बिहार (44%) में स्थित हैं, इसके बाद राजस्थान (25%), कर्नाटक (21%), पश्चिम बंगाल (3%), आंध्र प्रदेश (3%), झारखंड (2%) का स्थान है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36.The Office of the Registrar General and Census Commissioner comes under which of the following ministries of Government of India</p>",
                    question_hi: "<p>36. महापंजीयक एवं जनगणना आयुक्त का कार्यालय भारत सरकार के निम्नलिखित में से किस मंत्रालय के अंतर्गत आता है?</p>",
                    options_en: ["<p>Ministry of Commerce and Industry</p>", "<p>Ministry of Home Affairs</p>", 
                                "<p>Ministry of Statistics and Program Implementation</p>", "<p>Ministry of Labour and Employment</p>"],
                    options_hi: ["<p>वाणिज्य एवं उद्योग मंत्रालय</p>", "<p>गृह मंत्रालय</p>",
                                "<p>सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय</p>", "<p>श्रम और रोजगार मंत्रालय</p>"],
                    solution_en: "<p>36.(b) <strong>Ministry of Home Affairs. </strong>The Registrar General and Census Commissioner of India is responsible for organizing, conducting, and analyzing the results of the demographic surveys of India, including the Census of India and the Linguistic Survey of India. The Registrar General and Census Commissioner of India was founded in 1961 by the Ministry of Home Affairs as an organization to conduct demographic surveys in India. The position is typically held by a civil servant with the rank of Joint Secretary.</p>",
                    solution_hi: "<p>36.(b)<strong> गृह मंत्रालय।</strong> भारत के महापंजीयक और जनगणना आयुक्त भारत की जनगणना और भारतीय भाषाई सर्वेक्षण सहित भारत के जनसांख्यिकीय सर्वेक्षणों के परिणामों को व्यवस्थित करने, संचालित करने और उनका विश्लेषण करने के लिए उत्तरदायी हैं। भारत के रजिस्ट्रार जनरल और जनगणना आयुक्त की स्थापना 1961 में गृह मंत्रालय द्वारा भारत में जनसांख्यिकीय सर्वेक्षण करने के लिए एक संगठन के रूप में की गई थी। यह पद आमतौर पर संयुक्त सचिव के पद वाले सिविल सेवक के पास होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following statements is INCORRECT regarding the Coriolis force?</p>",
                    question_hi: "<p>37. कोरिऑलिस बल (Coriolis force) के संबंध में निम्नलिखित में से कौन-सा कथन गलत है?</p>",
                    options_en: ["<p>It deflects the wind to the right in the north and left in the south.</p>", "<p>It is directly proportional to the angle of altitude</p>", 
                                "<p>Deflection is less when the wind is high</p>", "<p>It is absent at the equator.</p>"],
                    options_hi: ["<p>यह पवन को उत्तर में दायीं ओर और दक्षिण में बायीं ओर विक्षेपित करता है।</p>", "<p>यह तुंगता कोण के समानुपाती होता है</p>",
                                "<p>पवन तेज होने पर विक्षेपण कम होता है।</p>", "<p>यह भूमध्य रेखा पर अनुपस्थित होता है।</p>"],
                    solution_en: "<p>37.(c) <strong>Deflection is less when the wind is high.</strong> Coriolis force : An apparent force caused by the earth&rsquo;s rotation. The Coriolis force acts perpendicular to the pressure gradient force (pressure gradient force is perpendicular to an isobar). It is maximum at the poles and is absent at the equator. The Coriolis force also affects ocean currents.</p>",
                    solution_hi: "<p>37.(c) <strong>पवन तेज होने पर विक्षेपण कम होता है। </strong>कोरिओलिस बल, पृथ्वी के घूर्णन के कारण लगने वाला एक काल्पनिक बल है। कोरिओलिस बल दाब प्रवणता बल (दाब प्रवणता बल एक समदाब रेखा के लंबवत होता है) के लंबवत कार्य करता है । यह ध्रुवों पर अधिकतम होता है तथा भूमध्य रेखा पर अनुपस्थित होता है। कोरिओलिस बल समुद्री धाराओं को भी प्रभावित करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. The concept of Public Interest Litigation originated in _______________.</p>",
                    question_hi: "<p>38. जनहित याचिका की अवधारणा की उत्पत्ति _______________ में हुई थी।</p>",
                    options_en: ["<p>Switzerland</p>", "<p>Iceland</p>", 
                                "<p>USA</p>", "<p>New Zealand</p>"],
                    options_hi: ["<p>स्विट्ज़रलैंड</p>", "<p>आइसलैंड</p>",
                                "<p>संयुक्त राज्य अमेरिका</p>", "<p>न्यूज़ीलैंड</p>"],
                    solution_en: "<p>38.(c) <strong>USA.</strong> Public Interest Litigation (PIL) means litigation filed in a court of law, for the protection of &ldquo;Public Interest&rdquo;, such as Pollution, Terrorism, Road safety, Constructional hazards etc. The concept of PIL was initially sown in India by Justice Krishna Iyer, in 1976 in Mumbai Kamagar Sabha vs. Abdul Thai. Any citizen can file a public case by filing a petition: Under Art 32 of the Indian Constitution, in the Supreme Court. Under Art 226 of the Indian Constitution, in the High Court. Under section 133 of the Criminal Procedure Code, in the Court of Magistrate.</p>",
                    solution_hi: "<p>38.(c) <strong>संयुक्त राज्य अमेरिका। </strong>जनहित याचिका (PIL) का अर्थ है प्रदूषण, आतंकवाद, सड़क सुरक्षा, निर्माण संबंधी खतरे आदि जैसे \"सार्वजनिक हित\" की सुरक्षा के लिए कानून की अदालत में दायर किया गया मुकदमा। भारत में PIL की अवधारणा को सबसे पहले जस्टिस कृष्ण अय्यर ने 1976 में मुंबई कामगार सभा बनाम अब्दुल थाई मामले में पेश किया था। कोई भी नागरिक भारतीय संविधान के अनुच्छेद 32 के तहत सर्वोच्च न्यायालय में, अनुच्छेद 226 के तहत उच्च न्यायालय में, तथा दंड प्रक्रिया संहिता की धारा 133 के तहत मजिस्ट्रेट की अदालत में याचिका दायर करके सार्वजनिक मामला दायर कर सकता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following is issued by commercial banks at a discount on face value?</p>",
                    question_hi: "<p>39. निम्नलिखित में से क्या वाणिज्यिक बैंकों द्वारा अंकित मूल्य (face value) पर छूट पर जारी किया जाता&nbsp;है ?</p>",
                    options_en: ["<p>Commercial papers</p>", "<p>Treasury bills</p>", 
                                "<p>Certificates of deposits</p>", "<p>Promissory notes</p>"],
                    options_hi: ["<p>वाणिज्यिक पत्र</p>", "<p>राजकोष बिल</p>",
                                "<p>जमा प्रमाणपत्र</p>", "<p>वचन पत्र</p>"],
                    solution_en: "<p>39.(c) <strong>Certificates of deposits</strong> are unsecured, negotiable, short-term instruments in bearer form, issued by commercial banks and development financial institutions. Commercial papers - It is issued by large companies for short-term funding at lower interest rates, with maturities of 15 days to one year. Treasury bills - They are short-term government securities maturing in less than one year.</p>",
                    solution_hi: "<p>39.(c) <strong>जमा प्रमाणपत्र </strong>असुरक्षित, परक्राम्य, वाहक रूप में अल्पकालिक साधन हैं, जो वाणिज्यिक बैंकों और विकास वित्तीय संस्थानों द्वारा जारी किए जाते हैं। वाणिज्यिक पत्र - यह बड़ी कंपनियों द्वारा कम ब्याज दरों पर अल्पकालिक वित्तपोषण के लिए जारी किया जाता है, जिसकी परिपक्वता अवधि 15 दिन से एक वर्ष तक होती है। ट्रेजरी बिल - ये अल्पकालिक सरकारी प्रतिभूतियाँ हैं जो एक वर्ष से कम समय में परिपक्व होती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which of the following statement(s) is/are INCORRECT vis-&agrave;-vis Fundamental Duties?<br>1) The 44th amendment Act introduced Fundamental duties in the Constitution.<br>2) Fundamental Duties were expanded by the 86th amendment act.<br>3) Article 51A (a) entails respect for its ideals and institutions, national flag and the national anthem.<br>4) Fundamental Duty obliges parents to provide opportunities for education to their child between 6-14 years of age.</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन-सा/कौन-से कथन मौलिक कर्तव्यों के विषय में गलत है/हैं?<br>1) 44वें संशोधन अधिनियम ने संविधान में मौलिक कर्तव्यों को पेश किया।<br>2) 86वें संशोधन अधिनियम द्वारा मौलिक कर्तव्यों का विस्तार किया गया।<br>3) अनुच्छेद 51A(a) में इसके आदर्शों और संस्थानों, राष्ट्रीय ध्वज और राष्ट्रगान का सम्मान शामिल है।<br>4) मौलिक कर्तव्य माता-पिता को 6-14 वर्ष की आयु के अपने बच्चे को शिक्षा के अवसर प्रदान करने के लिए बाध्य करते हैं।</p>",
                    options_en: ["<p>1</p>", "<p>2 and 3</p>", 
                                "<p>2 and 4</p>", "<p>Only 3</p>"],
                    options_hi: ["<p>1</p>", "<p>2 और 3</p>",
                                "<p>2 और 4</p>", "<p>केवल 3</p>"],
                    solution_en: "<p>40.(a) <strong>1.</strong> The 44th Constitutional Amendment Act of 1978 was enacted to undo many changes made by the 42nd Amendment during the Emergency in India. Key provisions include restoring judicial review, protecting civil liberties, and ensuring that the right to property is no longer a fundamental right but a legal right. Fundamental Duties are added by 42nd Amendment Act 1976. Later on, one more duty was added by 86th Amendment Act, 2002.</p>",
                    solution_hi: "<p>40.(a) <strong>1. </strong>1978 का 44वाँ संविधान संशोधन अधिनियम भारत में आपातकाल के दौरान 42वें संशोधन द्वारा किए गए कई परिवर्तनों को पूर्ववत करने के लिए लागू किया गया था। मुख्य प्रावधानों में न्यायिक समीक्षा को बहाल करना, नागरिक स्वतंत्रता की रक्षा करना और यह सुनिश्चित करना शामिल है कि संपत्ति का अधिकार अब मौलिक अधिकार नहीं बल्कि कानूनी अधिकार है। 42वें संशोधन अधिनियम 1976 द्वारा मौलिक कर्तव्यों को जोड़ा गया है। बाद में, 86वें संशोधन अधिनियम, 2002 द्वारा एक और कर्तव्य जोड़ा गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following institutions has contributed mainly in improving the economic status of the poor women in rural areas ?</p>",
                    question_hi: "<p>41. निम्नलिखित में से किस संस्था ने ग्रामीण क्षेत्रों में मुख्यत: गरीब महिलाओं की आर्थिक स्थिति को सुधारने में योगदान दिया है?</p>",
                    options_en: ["<p>Indira Awas Yojana</p>", "<p>Integrated Development Project</p>", 
                                "<p>Self-help Group</p>", "<p>Rural Health Scheme</p>"],
                    options_hi: ["<p>इंदिरा आवास योजना</p>", "<p>एकीकृत विकास परियोजना</p>",
                                "<p>स्वयं सहायता समूह</p>", "<p>ग्रामीण स्वास्थ्य योजना</p>"],
                    solution_en: "<p>41.(c) <strong>Self-Help Groups </strong>are informal associations of people who choose to come together to find ways to improve their living conditions.They help the poor and marginalized by building skills for employment and income generation. SHGs also provide loans without requiring collateral. Indira Awas Yojana: Provides housing subsidies to poor families. Integrated Development Project: A broader rural development initiative. Rural Health Scheme: Focuses on healthcare services.</p>",
                    solution_hi: "<p>41.(c) <strong>स्वयं सहायता समूह</strong> ऐसे लोगों का अनौपचारिक संगठन है जो अपनी जीवन स्थितियों को बेहतर बनाने के लिए एक साथ मिलकर काम करना पसंद करते हैं। वे रोजगार और आय सृजन के लिए कौशल विकसित करके गरीबों और वंचितों की मदद करते हैं। SHG बिना संपार्श्विक की आवश्यकता के भी ऋण प्रदान करते हैं। इंदिरा आवास योजना: गरीब परिवारों को आवास सब्सिडी प्रदान करती है। एकीकृत विकास परियोजना: एक व्यापक ग्रामीण विकास पहल है। ग्रामीण स्वास्थ्य योजना: स्वास्थ्य सेवाओं पर ध्यान केंद्रित करती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Identify the INCORRECT pair regarding the material media and their refractive index?</p>",
                    question_hi: "<p>42. भौतिक माध्यम (material media) और उनके अपवर्तनांक के संबंध में गलत युग्म की पहचान करें?</p>",
                    options_en: ["<p>Kerosene &ndash; 1.44</p>", "<p>Benzene &ndash; 2.42</p>", 
                                "<p>Sapphire &ndash; 1.77</p>", "<p>Ruby &ndash; 1.71</p>"],
                    options_hi: ["<p>केरोसीन&ndash; 1.44</p>", "<p>बेंजीन&ndash; 2.42</p>",
                                "<p>नीलम&ndash; 1.77</p>", "<p>रूबी&ndash; 1.71</p>"],
                    solution_en: "<p>42.(b) <strong>Benzene &ndash; 2.42.</strong> The refractive index of an optical medium is a dimensionless number that gives the indication of the light bending ability of that medium. Absolute refractive index of some material media: Benzene - 1.50, Rock salt - 1.54, Carbon disulphide - 1.63, Dense flint glass - 1.65, Diamond - 2.42, Crown glass - 1.52, Turpentine oil - 1.47, Water - 1.33, Alcohol - 1.36, Fused quartz - 1.46, Air - 1.0003.</p>",
                    solution_hi: "<p>42.(b)<strong> बेंजीन - 2.42.</strong> किसी प्रकाशीय माध्यम का अपवर्तनांक एक आयामहीन संख्या है जो उस माध्यम की प्रकाश को मोड़ने की क्षमता का संकेत देती है। कुछ भौतिक माध्यमों का निरपेक्ष अपवर्तनांक: बेंजीन - 1.50, सेंधा नमक - 1.54, कार्बन डाइसल्फ़ाइड - 1.63, संघन फ्लिंट कांच&nbsp; - 1.65, हीरा - 2.42, क्राउन ग्लास - 1.52, तारपीन का तेल - 1.47, जल - 1.33, एल्कोहल - 1.36, फ्यूज्ड क्वार्ट्ज - 1.46, वायु - 1.0003.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which article has a similar provision to that of Article 32 and deals with writ jurisdiction?</p>",
                    question_hi: "<p>43. निम्नलिखित में से कौन-सा अनुच्छेद रिट अधिकारिता से संबंधित है और अनुच्छेद 32 के समान प्रावधान रखता है?</p>",
                    options_en: ["<p>Article 227</p>", "<p>Article 228</p>", 
                                "<p>Article 225</p>", "<p>Article 226</p>"],
                    options_hi: ["<p>अनुच्छेद 227</p>", "<p>अनुच्छेद 228</p>",
                                "<p>अनुच्छेद 225</p>", "<p>अनुच्छेद 226</p>"],
                    solution_en: "<p>43.(d) <strong>Article 226. </strong>In India, writs are issued by the Supreme Court under Article 32 of the Constitution of India and by the High Courts under Article 226 of the Constitution of India. There are 5 types of writs issued in India - Habeas Corpus (to have the body of), Mandamus (we command), Certiorari (to be certified or to be informed), Prohibition (to forbid) and Quo-Warranto (by what authority or warrant).</p>",
                    solution_hi: "<p>43.(d)<strong> अनुच्छेद 226.</strong> भारत में, भारतीय संविधान के अनुच्छेद 32 के तहत सर्वोच्च न्यायालय द्वारा और भारतीय संविधान के अनुच्छेद 226 के तहत उच्च न्यायालयों द्वारा रिट जारी की जाती हैं। भारत में 5 प्रकार की रिट जारी की जाती हैं - बंदी प्रत्यक्षीकरण (शरीर प्रस्तुत करना), परमादेश (आदेश देने के लिए), उत्प्रेषण (प्रमाणित किया जाना या सूचित किया जाना), निषेध (मना करना) और अधिकार-पृच्छा (किस अधिकार या वारंट द्वारा)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which metalloid in the carbon group is chemically similar to its group neighbours tin and silicon?</p>",
                    question_hi: "<p>44. कार्बन समूह में कौन सा उपधातु रासायनिक रूप से अपने समूह पड़ोसियों टिन और सिलिकॉन के&nbsp;समान होता है?</p>",
                    options_en: ["<p>Germanium</p>", "<p>Flerovium</p>", 
                                "<p>Lead</p>", "<p>Arsenic</p>"],
                    options_hi: ["<p>जर्मेनियम</p>", "<p>फ्लेरोवियम</p>",
                                "<p>लेड</p>", "<p>आर्सेनिक</p>"],
                    solution_en: "<p>44.(a) <strong>Germanium (Ge) i</strong>s a metalloid in the carbon group (Group 14) of the periodic table, and it is chemically similar to its group neighbors tin (Sn) and silicon (Si). This similarity arises because all three elements share similar electron configurations and chemical properties, such as forming covalent bonds and having semiconductor characteristics.</p>",
                    solution_hi: "<p>44.(a) <strong>जर्मेनियम (Ge) </strong>आवर्त सारणी के कार्बन समूह (समूह 14) में एक उपधातु है, और यह रासायनिक रूप से अपने समूह पड़ोसी तत्वों टिन (Sn) और सिलिकॉन (Si) के समान है। यह समानता इसलिए उत्पन्न होती है क्योंकि तीनों तत्व समान इलेक्ट्रॉन विन्यास और रासायनिक गुण साझा करते हैं, जैसे सहसंयोजक बंध बनाना और अर्धचालक विशेषताएँ होना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Who was the first feudatory of Pallavas ?</p>",
                    question_hi: "<p>45. पल्लवों का प्रथम सामंत (feudatory) कौन था?</p>",
                    options_en: ["<p>Harshvardhan</p>", "<p>Rajendra</p>", 
                                "<p>Ashoka</p>", "<p>Vijayalaya</p>"],
                    options_hi: ["<p>हर्षवर्द्धन (Harshvardhan)</p>", "<p>राजेंद्र (Rajendra)</p>",
                                "<p>अशोक (Ashoka)</p>", "<p>विजयालय (Vijayalaya)</p>"],
                    solution_en: "<p>45.(d) <strong>Vijayalaya </strong>was the founder of the Chola Empire. Simhavishnu was the founder of the Pallava dynasty. The Pallavas established their authority over southern Andhra Pradesh and northern Tamil Nadu, with their capital at Kanchi. Harshvardhan belongs to the Pushyabhuti dynasty.</p>",
                    solution_hi: "<p>45.(d) <strong>विजयालय </strong>चोल साम्राज्य का संस्थापक था। सिंहविष्णु पल्लव वंश के संस्थापक थे। पल्लवों ने दक्षिणी आंध्र प्रदेश और उत्तरी तमिलनाडु पर अपना अधिकार स्थापित किया, जिसकी राजधानी कांची थी। हर्षवर्धन पुष्यभूति वंश से संबंधित हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. In 1835, who developed the process of applying a thin layer of metallic silver to one side of a clear glass pane and created the first mirror ?</p>",
                    question_hi: "<p>46. 1835 में, एक स्पष्ट कांच के फलक के एक तरफ धातुचांदी की एक पतली परत चढ़ाने की प्रक्रिया किसने विकसित की और पहला दर्पण बनाया ?</p>",
                    options_en: ["<p>CV Raman</p>", "<p>CH Townes</p>", 
                                "<p>Victor Francis Hess</p>", "<p>Justus von Liebig</p>"],
                    options_hi: ["<p>सी.वी. रमन (CV Raman)</p>", "<p>सी.एच. टाउन्स (CH Townes)</p>",
                                "<p>विक्टर फ्रांसिस हेस (Victor Francis Hess)</p>", "<p>जस्टस वॉन लेबिगो (Justus von Liebig)</p>"],
                    solution_en: "<p>46.(d) <strong>Justus von Liebig. </strong>He was a German scientist who made major contributions to the theory, practice, and pedagogy of chemistry, as well as to agricultural and biological chemistry. C.V. Raman: Indian physicist known for his work on light scattering (Raman effect). His Awards: Nobel Prize (1930), and Bharat Ratna (1954). C.H. Townes: American physicist who worked on quantum electronics and lasers. His Award: Nobel Prize (1964). Victor Francis Hess: Austrian-American physicist who discovered cosmic rays. His Award: Nobel Prize (1936).</p>",
                    solution_hi: "<p>46.(d) <strong>जस्टस वॉन लेबिगो </strong>(Justus von Liebig) एक जर्मन वैज्ञानिक थे जिन्होंने रसायन विज्ञान के सिद्धांत, अभ्यास और शिक्षाशास्त्र के साथ-साथ कृषि और जैविक रसायन विज्ञान में प्रमुख योगदान दिया। सी.वी. रमन: भारतीय भौतिक विज्ञानी जिन्हें प्रकाश प्रकीर्णन (रमन प्रभाव) पर उनके काम के लिए जाना जाता है। उनको प्राप्त पुरस्कार: नोबेल पुरस्कार (1930), और भारत रत्न (1954)। सी.एच. टाउनेस: अमेरिकी भौतिक विज्ञानी जिन्होंने क्वांटम इलेक्ट्रॉनिक्स और लेजर पर काम किया। उनको प्राप्त पुरस्कार: नोबेल पुरस्कार (1964)। विक्टर फ्रांसिस हेस: ऑस्ट्रियाई-अमेरिकी भौतिक विज्ञानी जिन्होंने ब्रह्मांडीय किरणों की खोज की। उनको प्राप्त पुरस्कार: नोबेल</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Who passed away in January 2025, known for his work as a poet, journalist, filmmaker, and former Rajya Sabha member?</p>",
                    question_hi: "<p>47. जनवरी 2025 में किसका निधन हो गया, जो कवि, पत्रकार, फिल्म निर्माता और पूर्व राज्यसभा सदस्य के रूप में अपने काम के लिए जाने जाते थे?</p>",
                    options_en: ["<p>Pritish Nandy</p>", "<p>Shashi Tharoor</p>", 
                                "<p>Khushwant Singh</p>", "<p>Harish Salve</p>"],
                    options_hi: ["<p>प्रीतीश नंदी</p>", "<p>शशि थरूर</p>",
                                "<p>खुशवंत सिंह</p>", "<p>हरीश साल्वे</p>"],
                    solution_en: "<p>47.(a) <strong>Pritish Nandy.</strong> He authored 40+ poetry books and translated works in multiple languages. His poem \"Calcutta If You Must Exile Me\" is a modern Indian literature classic.</p>",
                    solution_hi: "<p>47.(a) <strong>प्रीतीश नंदी।</strong> उन्होंने 40 से अधिक कविता पुस्तकें लिखीं और कई भाषाओं में कृतियों का अनुवाद किया। उनकी कविता \"कलकत्ता इफ यू मस्ट एक्साइल मी\" आधुनिक भारतीय साहित्य की एक क्लासिक है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "48. Which dancing form is classified as Ekaharya?",
                    question_hi: "48. निम्नलिखित में से किस नृत्य शैली को एकहार्य (Ekaharya) के रूप में वर्गीकृत किया गया है?",
                    options_en: [" Bharatnatyam", " Odissi ", 
                                " Sattriya", " Kathak"],
                    options_hi: ["  भरतनाट्यम", "  ओडिशी",
                                "  सत्रीया नृत्य", " कथक"],
                    solution_en: "<p>48.(a) <strong>Bharatnatyam.</strong> It originated in the temples of South India, particularly, Tamil Nadu. It was traditionally performed by Devadasis, and thus was also known as Dasiattam. Other Facts - Bharatanatyam includes three basic forms - Melattur, Pandanallur and Vazhuvoor. Abhinaya - Facial expressions in Bharatanatyam. Mangalam - End of a dance. Thillana - Final part of a recital. Aramandi - a fundamental dance position in Bharatanatyam that involves half sitting.</p>",
                    solution_hi: "<p>48.(a) <strong>भरतनाट्यम। </strong>इसकी उत्पत्ति दक्षिण भारत के मंदिरों, विशेष रूप से तमिलनाडु में हुई थी। इसे पारंपरिक रूप से देवदासियों द्वारा किया जाता था, और इसलिए इसे दासीअट्टम के नाम से भी जाना जाता था। अन्य तथ्य - भरतनाट्यम में तीन मूल रूप शामिल हैं - मेलत्तुर, पंडानल्लूर और वझुवूर। अभिनय - भरतनाट्यम में चेहरे के भाव। मंगलम - नृत्य का अंत। थिलाना - गायन का अंतिम भाग। अरमंडी - भरतनाट्यम की एक मौलिक नृत्य स्थिति जिसमें आधा बैठा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. In an electrical circuit, the ammeter reading decreases to ________________ when the length of the wire is doubled.</p>",
                    question_hi: "<p>49. एक विद्युत परिपथ में, तार की लंबाई दोगुनी करने पर ऐमीटर की रीडिंग घटकर _________ हो जाती है</p>",
                    options_en: ["<p>one-sixth</p>", "<p>one-half</p>", 
                                "<p>one-fourth</p>", "<p>one-third</p>"],
                    options_hi: ["<p>एक-छठवाँ भाग</p>", "<p>आधी</p>",
                                "<p>एक-चौथाई</p>", "<p>एक-तिहाई</p>"],
                    solution_en: "<p>49.(b) <strong>one-half. </strong>According to Ohm\'s law, the resistance 𝑅 of a conductor is directly proportional to its length 𝑙 and inversely proportional to its area of cross-section 𝐴. Doubling the length increases the resistance, which reduces the current, leading to a halving of the ammeter reading. That is, R &prop; 𝑙 and R &prop;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>A</mi></mfrac></math>. we get R &prop;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>A</mi></mfrac></mstyle></math> or, R = &rho; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>A</mi></mfrac></mstyle></math>.</p>",
                    solution_hi: "<p>49.(b) <strong>आधी।</strong> ओम के नियम के अनुसार, किसी चालक का प्रतिरोध 𝑅 उसकी लंबाई 𝑙 के समानुपाती तथा उसके अनुप्रस्थ काट के क्षेत्रफल 𝐴 के व्युत्क्रमानुपाती होता है। लंबाई को दोगुना करने से प्रतिरोध बढ़ जाता है, जिससे धारा कम हो जाती है, जिससे एमीटर रीडिंग आधी हो जाती है। अर्थात्, R &prop; 𝑙 और R &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>A</mi></mfrac></math>. हमें R &prop;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>A</mi></mfrac></mstyle></math> या, R = &rho;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>A</mi></mfrac></mstyle></math> मिलता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which Indian Grandmaster recently defeated both Magnus Carlsen and Fabiano Caruana in an online blitz competition?</p>",
                    question_hi: "<p>50. किस भारतीय ग्रैंडमास्टर ने हाल ही में एक ऑनलाइन ब्लिट्ज प्रतियोगिता में मैग्नस कार्लसन और फबियानो कारुआना दोनों को हराया?</p>",
                    options_en: ["<p>R Praggnanandhaa</p>", "<p>Arjun Erigaisi</p>", 
                                "<p>D Gukesh</p>", "<p>Vidit Gujrathi</p>"],
                    options_hi: ["<p>आर प्रज्ञानानंदा</p>", "<p>अर्जुन एरिगैसी</p>",
                                "<p>डी गुकेश</p>", "<p>विदित गुजराती</p>"],
                    solution_en: "<p>50. (b)<strong> Arjun Erigaisi</strong> <br>Indian Grandmaster Arjun Erigaisi achieved a remarkable feat by defeating World No. 1 Magnus Carlsen and World No. 2 Fabiano Caruana in the Titled Tuesday weekly blitz competition conducted by Chess.com. Erigaisi finished with 10 points out of 11 rounds, showcasing his exceptional talent in the online chess community.</p>",
                    solution_hi: "<p>50. (b) <strong>अर्जुन एरिगैसी ।</strong> <br>भारतीय ग्रैंडमास्टर अर्जुन एरिगैसी ने Chess.com द्वारा आयोजित टाइटल्ड ट्यूसडे साप्ताहिक ब्लिट्ज प्रतियोगिता में विश्व नंबर 1 मैग्नस कार्लसन और विश्व नंबर 2 फबियानो कारुआना को हराकर एक उल्लेखनीय उपलब्धि हासिल की। एरिगैसी ने 11 राउंड में से 10 अंक हासिल किए, जिससे उनकी ऑनलाइन शतरंज समुदाय में असाधारण प्रतिभा प्रदर्शित हुई।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The marked price of a battery-operated rickshaw is ₹90,000. It is available for sale at two successive discounts of 10% and 5%. What is the selling price (in₹) of the battery-operated rickshaw?</p>",
                    question_hi: "<p>51. बैटरी चालित रिक्शा का अंकित मूल्य ₹90,000 है। यह 10% और 5% की दो क्रमागत छूट पर बिक्री के लिए उपलब्ध है। बैटरी चालित रिक्शा का विक्रय मूल्य (₹ में) क्या है?</p>",
                    options_en: ["<p>76,950</p>", "<p>78,950</p>", 
                                "<p>77,850</p>", "<p>75,850</p>"],
                    options_hi: ["<p>76,950</p>", "<p>78,950</p>",
                                "<p>77,850</p>", "<p>75,850</p>"],
                    solution_en: "<p>51.(a)<br>SP of the rickshaw = 90000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>19</mn><mn>20</mn></mfrac></mstyle></math> = 76,950</p>",
                    solution_hi: "<p>51.(a)<br>रिक्शा का विक्रय मूल्य = 90000 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>19</mn><mn>20</mn></mfrac></mstyle></math> = 76,950</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If 321y72 is a multiple of 6, where y is a digit, what is the least value of y?</p>",
                    question_hi: "<p>52. यदि 321y72, 6 का एक गुणज है, जहाँ y एक अंक है, तो y का न्यूनतम मान क्या होगा?</p>",
                    options_en: ["<p>3</p>", "<p>0</p>", 
                                "<p>7</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>0</p>",
                                "<p>7</p>", "<p>2</p>"],
                    solution_en: "<p>52.(b) A number is divisible by 6 if it is divisible by both 2 and 3:<br><strong>Divisibility of 2 </strong>: A number is divisible by 2 if it is an even number, <br><strong>Divisibility of 3 :</strong> A number is divisible by 3 if the sum of all digits of that number is divisible by 3.<br>Now, 321y72<br><math display=\"inline\"><mo>&#8658;</mo></math>3 + 2 + 1 + y + 7 + 2 = 15 + y<br>The least value of y is 0</p>",
                    solution_hi: "<p>52.(b) एक संख्या 6 से तभी विभाज्य होती है यदि वह 2 और 3 दोनों से विभाज्य हो:<br><strong>2 से विभाज्यता:</strong> एक संख्या 2 से विभाज्य होती है यदि वह एक सम संख्या है, <br><strong>3 से विभाज्यता:</strong> एक संख्या 3 से विभाज्य होती है यदि उस संख्या के सभी अंकों का योग 3 से विभाज्य हो।<br>अब, 321y72<br><math display=\"inline\"><mo>&#8658;</mo></math>3 + 2 + 1 + y + 7 + 2 = 15 + y<br>y का न्यूनतम मान 0 है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The area of the canvas cloth needed to erect a right conical tent of height 20ft. and circular base of circumference 30&pi; ft. is:</p>",
                    question_hi: "<p>53. 20 ft ऊंचाई वाले और 30&pi; ft की परिधि के वृत्तीय आधार वाले एक लंब वृत्तीय शंक्वाकार तम्बू को खड़ा करने के लिए आवश्यक कैनवास कपड़े का क्षेत्रफल ज्ञात करें।</p>",
                    options_en: ["<p>455&pi; sq.ft.</p>", "<p>375&pi; sq.ft.</p>", 
                                "<p>589&pi; sq.ft.</p>", "<p>470&pi; sq.ft.</p>"],
                    options_hi: ["<p>455&pi; sq.ft.</p>", "<p>375&pi; sq.ft.</p>",
                                "<p>589&pi; sq.ft.</p>", "<p>470&pi; sq.ft.</p>"],
                    solution_en: "<p>53.(b)<br>Circumference of circular base of cone = 30&pi; ft.<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;r</mi></math> = 30&pi; ft.<br><math display=\"inline\"><mi>r</mi></math> = 15 ft.<br>Heig<math display=\"inline\"><mi>h</mi></math>t (h) of cone = 20 ft.<br>S<math display=\"inline\"><mi>l</mi></math>ant height of cone (l) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>r</mi><mo>&#178;</mo><mo>+</mo><mi>h</mi><mo>&#178;</mo></msqrt></math><br>S<math display=\"inline\"><mi>l</mi></math>ant height of cone (l) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn><mo>+</mo><mn>400</mn></msqrt></math> = 25 ft.<br>Area of canvas needed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>25</mn><mi mathvariant=\"normal\">&#160;</mi></math>= 375&pi; sq.ft.</p>",
                    solution_hi: "<p>53.(b)<br>शंकु के वृत्ताकार आधार की परिधि = 30&pi; ft.<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;r</mi></math> = 30&pi; ft.<br><math display=\"inline\"><mi>r</mi></math> = 15 ft.<br>शंकु की ऊँचाई (<math display=\"inline\"><mi>h</mi></math>) = 20 ft.<br>शंकु की तिरछी ऊंचाई (<math display=\"inline\"><mi>l</mi></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>r</mi><mo>&#178;</mo><mo>+</mo><mi>h</mi><mo>&#178;</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>225</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>400</mn></msqrt></math> = 25 ft.<br>कैनवास कपड़े का आवश्यक क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>25</mn><mi mathvariant=\"normal\">&#160;</mi></math>= 375&pi; sq.ft.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If x = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>, then find the value of (x&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></mstyle></math>).</p>",
                    question_hi: "<p>54. यदि x = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> है, तो (x&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></mstyle></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>188</p>", "<p>194</p>", 
                                "<p>186</p>", "<p>196</p>"],
                    options_hi: ["<p>188</p>", "<p>194</p>",
                                "<p>186</p>", "<p>196</p>"],
                    solution_en: "<p>54.(b) <br><strong>Given x</strong> = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 7 + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>x +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>x</mi></mfrac></mstyle></math> = 14<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></mstyle></math> = 14&sup2; - 2 = 196 - 2 = 194</p>",
                    solution_hi: "<p>54.(b) <strong>Given x </strong>= 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 7 + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>x +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>x</mi></mfrac></mstyle></math> = 14<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></mstyle></math> = 14&sup2; - 2 = 196 - 2 = 194</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Two numbers are, respectively, 30% and 60% more than a third number. The ratio of the two numbers is:</p>",
                    question_hi: "<p>55. दो संख्याएं एक तीसरी संख्या से क्रमशः 30% और 60% अधिक हैं। दो संख्याओं का अनुपात कितना है?</p>",
                    options_en: ["<p>13 : 16</p>", "<p>12 : 13</p>", 
                                "<p>11 : 15</p>", "<p>13 : 17</p>"],
                    options_hi: ["<p>13 : 16</p>", "<p>12 : 13</p>",
                                "<p>11 : 15</p>", "<p>13 : 17</p>"],
                    solution_en: "<p>55.(a) Let third number = 100<br>Then, two number = 100 &times; 130% = 130 and 100 &times; 160% = 160<br>Ratio of two numbers = 130 : 160 = 13 : 16</p>",
                    solution_hi: "<p>55.(a)<br>माना तीसरी संख्या = 100<br>फिर, दो संख्याएँ = 100 &times; 130% = 130 और 100 &times; 160% = 160<br>दो संख्याओं का अनुपात = 130 : 160 = 13 : 16</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. For what least value of n,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mi mathvariant=\"normal\">n</mi></mrow></msup><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi></math> is completely divided by 6, where n is an integer ?</p>",
                    question_hi: "<p>56. n के किस न्यूनतम मान के लिए, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mi mathvariant=\"normal\">n</mi></mrow></msup><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi></math> 6 से पूर्णतः विभाज्य है, जहां n एक पूर्णांक है?</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p>56.(b) <strong>Given:</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mi mathvariant=\"normal\">n</mi></mrow></msup></math> + 2n<br>Put n = 3, <br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mo>&#215;</mo><mn>3</mn></mrow></msup></math>+ 2 &times; 3 = 64 + 6 = 70 (not divisible by 6)<br>Put n = 1<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn></mrow></msup></math>+ 2 &times; 1 = 4 + 2 = 6 (divisible by 6)<br>Put n = 0<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mo>&#215;</mo><mn>0</mn></mrow></msup></math>+ 2 &times; 0 = 1 + 0 = 1 (not divisible by 6)<br>Put n = 2<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mo>&#215;</mo><mn>2</mn></mrow></msup></math>+ 2 &times; 2 = 16 + 4 = 20 (not divisible by 6)<br>It is clear that the correct answer is option (b)</p>",
                    solution_hi: "<p>56.(b) <br>दिया गया: <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn><mi>n</mi></mrow></msup></math> + 2n<br>n = 3 रखने पर, <br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mo>&#215;</mo><mn>3</mn></mrow></msup></math>+ 2 &times; 3 = 64 + 6 = 70 (6 से विभाज्य नहीं)<br>n = 1 रखने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn></mrow></msup></math>+ 2 &times; 1 = 4 + 2 = 6 (6 से विभाज्य)<br>n = 0 रखने पर,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mo>&#215;</mo><mn>0</mn></mrow></msup></math>+ 2 &times; 0 = 1 + 0 = 1 (6 से विभाज्य नहीं)<br>n = 2 रखने पर,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mrow><mn>2</mn><mo>&#215;</mo><mn>2</mn></mrow></msup></math>+ 2 &times; 2 = 16 + 4 = 20 (6 से विभाज्य नहीं)<br>यह स्पष्ट है कि सही उत्तर विकल्प (b) है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. 47 masons can dig a 35 m long trench in one day. How many masons should be employed for digging a 105 m long trench of the same type in one day?</p>",
                    question_hi: "<p>57. 47 राजमिस्त्री एक दिन में 35 m लंबी खाई खोद सकते हैं। एक दिन में इसी प्रकार की 105 m लंबी खाई खोदने के लिए कितने राजमिस्त्रीयों की आवश्यकता होगी?</p>",
                    options_en: ["<p>94</p>", "<p>70</p>", 
                                "<p>141</p>", "<p>128</p>"],
                    options_hi: ["<p>94</p>", "<p>70</p>",
                                "<p>141</p>", "<p>128</p>"],
                    solution_en: "<p>57.(c) <br>Let required masons be <math display=\"inline\"><mi>x</mi></math><br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>35</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mn>105</mn></mfrac></mstyle></math> &rArr; x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>47</mn><mo>&#215;</mo><mn>105</mn></mrow><mn>35</mn></mfrac></mstyle></math> = 141</p>",
                    solution_hi: "<p>57.(c) <br>माना आवश्यक राजमिस्त्री <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mn>105</mn></mfrac></mstyle></math> &rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>47</mn><mo>&#215;</mo><mn>105</mn></mrow><mn>35</mn></mfrac></mstyle></math> = 141</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. <math display=\"inline\"><mo>&#9651;</mo></math>ABC is a right-angled triangle with &ang;ABC = 90&deg;. If m(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mi>AB</mi></menclose></math>) = 28 cm, and m(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mi>BC</mi></menclose></math>) = 96 cm, find the area (in cm&sup2;) of the circumcircle of △ABC. (Use &pi; = 3.14.)</p>",
                    question_hi: "<p>58. <math display=\"inline\"><mo>&#9651;</mo></math>ABC समकोण त्रिभुज है जिसमें &ang;ABC = 90&deg; है। यदि m(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mi>AB</mi></menclose></math>) = 28 cm, और m(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mi>BC</mi></menclose></math>) = 96 cm है, तो △ABC के परिवृत्त का क्षेत्रफल (cm&sup2; में) ज्ञात करें। ( &pi; = 3.14 का उपयोग करें।)</p>",
                    options_en: ["<p>7,850</p>", "<p>8,164</p>", 
                                "<p>7,693</p>", "<p>7,536</p>"],
                    options_hi: ["<p>7,850</p>", "<p>8,164</p>",
                                "<p>7,693</p>", "<p>7,536</p>"],
                    solution_en: "<p>58.(a)<br><strong id=\"docs-internal-guid-3fae738b-7fff-bfc2-1aee-c245e66e3c85\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcX2XtLGNVuw9J0cOa9hzQXJSX-J5qKtW6mJKxR2_UFG28brEMzlAL5GBl7q393INLNW96h6uLMtHkm1j9OXtcvPbHdr90AB_j7QT7EyQu1ktnMfKN7K3ZX-NtzXGoVuwAUx_0DRg?key=rpjzVTaBGZxKiw0pUORUVpY3\" width=\"173\" height=\"157\"></strong><br>AC =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>A</mi><msup><mi>B</mi><mn>2</mn></msup><mo>+</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><msup><mn>28</mn><mn>2</mn></msup><mo>+</mo><msup><mn>96</mn><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mn>784</mn><mo>+</mo><mn>9216</mn></msqrt><mo>=</mo><mn>100</mn></math>&nbsp;<br>Circumradius = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>H</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>100</mn><mn>2</mn></mfrac></mstyle></math>= 50 cm<br>Area of circumcircle = <math display=\"inline\"><mi>&#960;</mi></math> &times; 50 &times; 50 = 3.14 &times; 2500 = 7850 cm&sup2;</p>",
                    solution_hi: "<p>58.(a)<br><strong id=\"docs-internal-guid-3fae738b-7fff-bfc2-1aee-c245e66e3c85\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcX2XtLGNVuw9J0cOa9hzQXJSX-J5qKtW6mJKxR2_UFG28brEMzlAL5GBl7q393INLNW96h6uLMtHkm1j9OXtcvPbHdr90AB_j7QT7EyQu1ktnMfKN7K3ZX-NtzXGoVuwAUx_0DRg?key=rpjzVTaBGZxKiw0pUORUVpY3\" width=\"167\" height=\"151\"></strong><br>AC =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>A</mi><msup><mi>B</mi><mn>2</mn></msup><mo>+</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><msup><mn>28</mn><mn>2</mn></msup><mo>+</mo><msup><mn>96</mn><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mn>784</mn><mo>+</mo><mn>9216</mn></msqrt><mo>=</mo><mn>100</mn></math><br>परिवृत्त त्रिज्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;&#160;</mo></mrow><mn>2</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>100</mn><mn>2</mn></mfrac></mstyle></math> = 50 cm<br>परिवृत्त का क्षेत्रफल = <math display=\"inline\"><mi>&#960;</mi></math> &times; 50 &times; 50 = 3.14 &times; 2500 = 7850 cm&sup2;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A 135 m long train crosses a man walking at a speed of 10.6 km/h in the opposite direction in 12 seconds. What is the speed (in km/h) of the train?</p>",
                    question_hi: "<p>59. एक 135 m लंबी रेलगाड़ी, विपरीत दिशा में 10.6 km/h की चाल से चल रहे एक व्यक्ति को 12 सेकंड में पार करती है। रेलगाड़ी की चाल (km/h में) कितनी है?</p>",
                    options_en: ["<p>29.9</p>", "<p>31.9</p>", 
                                "<p>32.1</p>", "<p>34.4</p>"],
                    options_hi: ["<p>29.9</p>", "<p>31.9</p>",
                                "<p>32.1</p>", "<p>34.4</p>"],
                    solution_en: "<p>59.(a) Let the speed of the train be <math display=\"inline\"><mi>x</mi></math> km/h<br>Relative speed = (<math display=\"inline\"><mi>x</mi></math> + 10.6) km/h <br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math>Speed =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>Distance</mi><mi>time</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>135</mn><mn>12</mn></mfrac></mstyle></math>m/sec =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>135</mn><mo>&#160;</mo></mrow><mn>12</mn></mfrac></mstyle></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mn>5</mn></mfrac></mstyle></math> = 40.5 km/h<br>Now,<br><math display=\"inline\"><mo>&#8658;</mo></math>(x + 10.6) = 40.5 km/h<br><math display=\"inline\"><mo>&#8658;</mo></math>x = 29.9 km/h</p>",
                    solution_hi: "<p>59.(a) माना ट्रेन कि गति <math display=\"inline\"><mi>x</mi></math> km/h है।<br>सापेक्ष गति = (<math display=\"inline\"><mi>x</mi></math> + 10.6) km/h <br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math>गति =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#2360;&#2350;&#2351;</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>135</mn><mn>12</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>135</mn><mn>12</mn></mfrac></mstyle></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mn>5</mn></mfrac></mstyle></math> = 40.5 km/h<br>अब,<br><math display=\"inline\"><mo>&#8658;</mo></math>(x + 10.6) = 40.5 km/h<br><math display=\"inline\"><mo>&#8658;</mo></math>x = 29.9 km/h</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A man can row a boat at speed 8 km/h in still water. It takes thrice as long to row upstream as it does to row downstream. Find the speed of the stream (in km/h).</p>",
                    question_hi: "<p>60. एक आदमी स्थिर जल में 8 km/h की चाल से नाव चला सकता है। यदि उसे धारा के प्रतिकूल जाने में धारा के अनुकूल जाने में लगने वाले समय से तीन गुना अधिक समय लगता है। तो धारा की चाल (km/h में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>6</p>", "<p>4</p>", 
                                "<p>5</p>", "<p>3</p>"],
                    options_hi: ["<p>6</p>", "<p>4</p>",
                                "<p>5</p>", "<p>3</p>"],
                    solution_en: "<p>60.(b) speed of boat = 8 km/h<br>Let speed of current be y <br>According to the question,<br>3 <math display=\"inline\"><mo>&#215;</mo></math> upstream speed = downstream speed<br>3(8 - y) = (8 + y)<br>24 - 3y = 8 + y<br>4y = 16 <math display=\"inline\"><mo>&#8658;</mo></math> y = 4 km/h</p>",
                    solution_hi: "<p>60.(b)<br>नाव की गति = 8 km/h<br>माना धारा की गति = y <br>प्रश्न के अनुसार,<br>3 <math display=\"inline\"><mo>&#215;</mo></math> धारा के प्रतिकूल गति = धारा के अनुकूल गति <br>3(8 - y) = (8 + y)<br>24 - 3y = 8 + y<br>4y = 16<math display=\"inline\"><mo>&#8658;</mo></math>y = 4 km/h</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If 29 &times; 2 &times; 742 <math display=\"inline\"><mo>&#247;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2809</mn></msqrt></math> = y + 784, then find the value of y.</p>",
                    question_hi: "<p>61. यदि 29 &times; 2 &times; 742 <math display=\"inline\"><mo>&#247;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2809</mn></msqrt></math> = y + 784 है, तो y का मान ज्ञात कीजिए ?</p>",
                    options_en: ["<p>26</p>", "<p>28</p>", 
                                "<p>35</p>", "<p>36</p>"],
                    options_hi: ["<p>26</p>", "<p>28</p>",
                                "<p>35</p>", "<p>36</p>"],
                    solution_en: "<p>61.(b) 29 &times; 2 &times; 742<math display=\"inline\"><mo>&#247;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2809</mn></msqrt></math> = y + 784 <br>29 &times; 2 &times; 742 <math display=\"inline\"><mo>&#247;</mo></math> 53 = y + 784<br>29 &times; 2 &times; 14 = y + 784<br>y = 812 - 784 = 28</p>",
                    solution_hi: "<p>61.(b) 29 &times; 2 &times; 742 <math display=\"inline\"><mo>&#247;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2809</mn></msqrt></math> = y + 784 <br>29 &times; 2 &times; 742 <math display=\"inline\"><mo>&#247;</mo></math> 53 = y + 784<br>29 &times; 2 &times; 14 = y + 784<br>y = 812 - 784 = 28</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The strength of a school increases and decreases every alternate year by 15%. If it started with an increase in 2012, then the strength of the school in the beginning of 2015, as compared to that in 2012, had (correct to two decimal places):</p>",
                    question_hi: "<p>62. एक स्कूल की छात्र संख्&zwj;या प्रत्येक एकांतर वर्ष में 15% बढ़ती और घटती है। यदि वृद्धि की शुरुआत 2012 में हुई, तो 2012 की तुलना में, 2015 की शुरुआत में स्कूल की छात्र संख्&zwj;या में ________(दो दशमलव स्थान तक सही) हुई।</p>",
                    options_en: ["<p>Decreased by 12.41%</p>", "<p>Increased by 12.41 %</p>", 
                                "<p>Decreased by 13.85 %</p>", "<p>Increased by 13.85 %</p>"],
                    options_hi: ["<p>12.41% की कमी</p>", "<p>12.41 % की वृद्धि</p>",
                                "<p>13.85 % की कमी</p>", "<p>13.85 % की वृद्धि</p>"],
                    solution_en: "<p>62.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254635.png\" alt=\"rId44\" width=\"173\" height=\"30\"><br><strong>+15 %&nbsp; &nbsp; &nbsp;- 15 %&nbsp; &nbsp; &nbsp;+ 15%</strong><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>17</mn><mn>20</mn></mfrac></mstyle></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>23</mn><mn>20</mn></mfrac></mstyle></math><br>Let the total student increased in school in 2012 to 2015 be <math display=\"inline\"><mi>x</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>23</mn><mn>20</mn></mfrac></mstyle><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>17</mn><mn>20</mn></mfrac></mstyle><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>23</mn><mn>20</mn></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>8993</mn><mn>8000</mn></mfrac><mi>x</mi></mstyle></math><br>Student increase % = <math display=\"inline\"><mfrac><mrow><mn>993</mn><mi>&#160;</mi></mrow><mrow><mn>8000</mn></mrow></mfrac></math>&times; 100 = 12.41%</p>",
                    solution_hi: "<p>62.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254635.png\" alt=\"rId44\" width=\"173\" height=\"30\"><br>+15 %&nbsp; &nbsp;- 15 %&nbsp; &nbsp;+ 15%<br>&nbsp; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>17</mn><mn>20</mn></mfrac></mstyle></math>&nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>23</mn><mn>20</mn></mfrac></mstyle></math>&nbsp; &nbsp;<br>माना 2012 से 2015 तक स्कूल में कुल विद्यार्थियों की संख्या में वृद्धि <math display=\"inline\"><mi>x</mi></math> है।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>23</mn><mn>20</mn></mfrac></mstyle><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>17</mn><mn>20</mn></mfrac></mstyle><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>23</mn><mn>20</mn></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>8993</mn><mn>8000</mn></mfrac></mstyle><mi>x</mi></math><br>छात्र वृद्धि% = <math display=\"inline\"><mfrac><mrow><mn>993</mn><mi>&#160;</mi></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 12.41%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. if 7 cos&sup2;.&theta; + 5 sin&sup2;&theta; - 6 = 0 , (0&deg; &lt; &theta; &lt; 90&deg;), then what is the value of 1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mstyle displaystyle=\"false\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></mstyle></msqrt></math> ?</p>",
                    question_hi: "<p>63. यदि 7 cos&sup2;&theta; + 5 sin&sup2;&theta; - 6 = 0 है, जहां (0&deg; &lt; &theta; &lt; 90&deg;) , तो 1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mstyle displaystyle=\"false\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></mstyle></msqrt></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> + 2</p>", "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> + 1</p>", 
                                "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1</p>", "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 2</p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> + 2</p>", "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> + 1</p>",
                                "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1</p>", "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 2</p>"],
                    solution_en: "<p>63.(a) <br>7 cos&sup2; &theta; + 5 sin&sup2; &theta; - 6 = 0<br>Let &theta; = 45&deg;<br>7 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></math> + 5 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></math> - 6 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>12</mn><mn>2</mn></mfrac></mstyle></math>- 6 = 6 - 6 = 0<br>Now,<br>1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></msqrt></math> = 1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>45</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>45</mn><mo>&#176;</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>45</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>45</mn><mo>&#176;</mo></mrow></mfrac></msqrt></mstyle></math><br>= 1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi></mrow><mrow><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow></mfrac></msqrt></math> = 1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mfrac><mrow><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>+</mo><mo>&#160;</mo><mn>1</mn><mi>&#160;</mi></mrow><mrow><msqrt><mn>2</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow></mfrac></msqrt></mstyle></math><br>Now, 1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mi>&#160;</mi><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mi>&#160;</mi><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></msqrt></math> = 1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> + 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> + 2</p>",
                    solution_hi: "<p>63.(a) <br>7 cos&sup2;&theta; + 5 sin&sup2; &theta; - 6 = 0<br>माना ,&theta; = 45&deg;<br>7 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></math> + 5 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></math> - 6 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>12</mn><mn>2</mn></mfrac></mstyle></math> -6 = 6 - 6 = 0<br>अब ,<br>1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></msqrt></math> = 1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>45</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>45</mn><mo>&#176;</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>45</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>45</mn><mo>&#176;</mo></mrow></mfrac></msqrt></mstyle></math><br>= 1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi></mrow><mrow><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow></mfrac></msqrt></math> = 1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mfrac><mrow><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>+</mo><mo>&#160;</mo><mn>1</mn><mi>&#160;</mi></mrow><mrow><msqrt><mn>2</mn></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow></mfrac></msqrt></mstyle></math><br>अतः , 1 + <math display=\"inline\"><msqrt><mfrac><mrow><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mi>&#160;</mi><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mi>&#160;</mi><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></msqrt></math> = 1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> + 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> + 2</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A fraction <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>3</mn><mi>&#160;</mi></mrow><mn>5</mn></mfrac></math> is decreased by <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>5</mn></mfrac></mstyle></math>. The resulting fraction is then increased by 20%. What is the percentage decrease/increase in the value of the original fraction?</p>",
                    question_hi: "<p>64. एक भिन्न <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> में से&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mi>&#160;</mi></mrow><mn>5</mn></mfrac></mstyle></math> घटाया जाता है। परिणामी भिन्न को फिर 20% बढ़ा दिया जाता है। मूल भिन्न के मान में हुई प्रतिशत कमी/वृद्धि ज्ञात कीजिए।</p>",
                    options_en: ["<p>Increase, 60%</p>", "<p>Increase, 50%</p>", 
                                "<p>Decrease, 60%</p>", "<p>Decrease, 50%</p>"],
                    options_hi: ["<p>वृद्धि, 60%</p>", "<p>वृद्धि, 50%</p>",
                                "<p>कमी, 60%</p>", "<p>कमी, 50%</p>"],
                    solution_en: "<p>64.(c)<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>5</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mn>5</mn></mfrac></mstyle></math><br>After 20% increment = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>6</mn><mn>5</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>&#160;</mo><mn>6</mn><mi>&#160;</mi></mrow><mn>25</mn></mfrac></mstyle></math><br>Ratio - initial : final<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>&#160;</mo><mn>6</mn><mi>&#160;</mi></mrow><mn>25</mn></mfrac></mstyle></math> = 5 : 2<br>decrease% = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 60%</p>",
                    solution_hi: "<p>64.(c)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mi>&#160;</mi></mrow><mn>5</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>5</mn></mfrac></mstyle></math><br>20% वृद्धि के बाद = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mn>5</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>6</mn><mn>25</mn></mfrac></mstyle></math><br>अनुपात - प्रारंभिक : अंतिम<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mn>25</mn></mfrac></mstyle></math> = 5 : 2<br>कमी% = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 60%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The side BC of ∆ABC is produced to a point D. If AC = BC and &ang;BAC = 70&deg;, then find the value of 2.5&ang;ACD &minus; 1.5&ang;ABC.</p>",
                    question_hi: "<p>65. ∆ABC की भुजा BC को बिंदु D तक बढ़ाया गया है। यदि AC = BC और &ang;BAC = 70&deg; है, तो 2.5&ang;ACD &minus; 1.5&ang;ABC का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>245&deg;</p>", "<p>235&deg;</p>", 
                                "<p>225&deg;</p>", "<p>230&deg;</p>"],
                    options_hi: ["<p>245&deg;</p>", "<p>235&deg;</p>",
                                "<p>225&deg;</p>", "<p>230&deg;</p>"],
                    solution_en: "<p>65.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254742.png\" alt=\"rId45\" width=\"200\" height=\"123\"><br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;ABC</mi></math><br>&ang;A + &ang;B + &ang;C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>180</mn><mo>&#176;</mo></math><br>&ang;ACD = &ang;A + &ang;B &hellip; (exterior angle property)<br>&ang;ACD = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>140</mn><mo>&#176;</mo></math> <br>now,<br>2.5 <math display=\"inline\"><mo>&#215;</mo></math> &ang;ACD - 1.5 &ang;ABC<br>2.5 <math display=\"inline\"><mo>&#215;</mo></math>140&deg; - 1.5 &times; 70&deg; = 350&deg; - 105&deg; = 245&deg;</p>",
                    solution_hi: "<p>65.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851254742.png\" alt=\"rId45\" width=\"200\" height=\"123\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;ABC</mi></math> में<br>&ang;A + &ang;B + &ang;C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>180</mn><mo>&#176;</mo></math><br>&ang;ACD = &ang;A + &ang;B &hellip; (बाह्य कोण गुण)<br>&ang;ACD = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>140</mn><mo>&#176;</mo></math> <br>अब,<br>2.5 <math display=\"inline\"><mo>&#215;</mo></math> &ang;ACD - 1.5 &ang;ABC<br>2.5 <math display=\"inline\"><mo>&#215;</mo></math> 140&deg; - 1.5 &times; 70&deg; = 350&deg; - 105&deg; = 245&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A dishonest dealer sells his goods at 20% loss on the cost price but uses a faulty weight that weighs 25% less. His profit or loss percentage (rounded off to 2 decimal places) is:</p>",
                    question_hi: "<p>66. एक बेईमान डीलर अपने सामान को क्रय मूल्य पर 20% हानि पर बेचता है लेकिन एक दोषपूर्ण वजन का उपयोग करता है जिसका वजन 25% कम होता है। उसका लाभ या हानि प्रतिशत (दो दशमलव स्थानों तक पूर्णांकित) ज्ञात कीजिए।</p>",
                    options_en: ["<p>6.67%, loss</p>", "<p>3. 33%, profit</p>", 
                                "<p>3.33%, loss</p>", "<p>6.67%, profit</p>"],
                    options_hi: ["<p>6.67%, हानि</p>", "<p>3. 33%, लाभ</p>",
                                "<p>3.33%, हानि</p>", "<p>6.67%, लाभ</p>"],
                    solution_en: "<p>66.(d)<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> Initial : Final <br>price <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp;4<br>Weight <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 3&nbsp; &nbsp;:&nbsp; &nbsp; 4<br>-----------------------------------------<br>Final <math display=\"inline\"><mo>&#8594;</mo></math> &nbsp; 15&nbsp; &nbsp;:&nbsp; &nbsp;16<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac><mo>&#215;</mo></math>100 = 6.67%</p>",
                    solution_hi: "<p>66.(d)<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> प्रारंभिक : अंतिम <br>कीमत <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 4<br>वजन <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 4<br>-----------------------------------------<br>अंतिम <math display=\"inline\"><mo>&#8594;</mo></math> 15 : 16<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac><mo>&#215;</mo></math>100 = 6.67%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. In a bank, 6% interest is given on the deposited amount. The bank then invests the money in the stock market and is able to earn ₹30,000 for every ₹3,00,000. How much money is the bank earning finally if the total amount deposited in the bank is ₹60,00,000 ?</p>",
                    question_hi: "<p>67. एक बैंक में जमा राशि पर 6% ब्याज दिया जाता है। बैंक फिर शेयर बाजार में पैसा निवेश करता है और प्रत्येक ₹3,00,000 पर ₹30,000 अर्जित करता है। यदि बैंक में कुल जमा राशि ₹60,00,000 है, तो बैंक अंत में कितना लाभ अर्जित कर रहा है?</p>",
                    options_en: ["<p>₹3,00,000</p>", "<p>₹6,40,000</p>", 
                                "<p>₹2,40,000</p>", "<p>₹3,60,000</p>"],
                    options_hi: ["<p>₹3,00,000</p>", "<p>₹6,40,000</p>",
                                "<p>₹2,40,000</p>", "<p>₹3,60,000</p>"],
                    solution_en: "<p>67.(c) Interest gained by bank on deposited amount = <math display=\"inline\"><mfrac><mrow><mn>30000</mn></mrow><mrow><mn>300000</mn></mrow></mfrac></math> &times; 100 = 10%<br>Bank give by bank on deposited amount = 6%<br>Required profit to earn by Bank = 6000000 &times; (10 - 6)% = ₹240000</p>",
                    solution_hi: "<p>67.(c) जमा राशि पर बैंक द्वारा प्राप्त ब्याज = <math display=\"inline\"><mfrac><mrow><mn>30000</mn></mrow><mrow><mn>300000</mn></mrow></mfrac></math> &times; 100 = 10%<br>जमा राशि पर बैंक द्वारा दिया जाने वाला शुल्क = 6%<br>बैंक द्वारा अर्जित करने के लिए आवश्यक लाभ = 6000000 &times; (10 - 6)% = ₹240000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. From a circle with the radius of 15.75 cm, a sector with the arc length of 11 cm is cut off. Find the area (in cm&sup2;) of this sector.</p>",
                    question_hi: "<p>68. 15.75 cm त्रिज्या वाले एक वृत्त से 11 cm की चाप लंबाई वाला त्रिज्यखंड काटा जाता है। इस त्रिज्यखंड का क्षेत्रफल (cm&sup2; में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>86.525</p>", "<p>86.625</p>", 
                                "<p>86.875</p>", "<p>86.125</p>"],
                    options_hi: ["<p>86.525</p>", "<p>86.625</p>",
                                "<p>86.875</p>", "<p>86.125</p>"],
                    solution_en: "<p>68.(b)<br>Area of sector = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; l &times; r<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 11 &times; 15.75 = 86.625 cm&sup2;</p>",
                    solution_hi: "<p>68.(b)<br>त्रिज्यखंड का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; l &times; r<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 11 &times; 15.75 = 86.625 cm&sup2;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. An equal sum of money was lent to P and Q at 8.6% per annum for a period of 7 years and 8 years, respectively. If the difference in interest paid by them was ₹172, then the sum lent to each was:</p>",
                    question_hi: "<p>69. P और Q को समान धनराशि क्रमशः 7 वर्ष और 8 वर्ष की अवधि के लिए 8.6% वार्षिक दर पर ऋण पर दी गई थी। यदि उनके द्वारा भुगतान किए गए ब्याज में ₹172 का अंतर था, तो प्रत्येक को ऋण पर दी गई धनराशि कितनी थी?</p>",
                    options_en: ["<p>₹2,000</p>", "<p>₹3,000</p>", 
                                "<p>₹2,150</p>", "<p>₹185</p>"],
                    options_hi: ["<p>₹2,000</p>", "<p>₹3,000</p>",
                                "<p>₹2,150</p>", "<p>₹185</p>"],
                    solution_en: "<p>69.(a)<br>For P = 7 &times; 8.6%<br>For Q = 8 &times; 8.6%<br>difference (8.6 &times; 1 units) = ₹172<br>Required sum (100 units) = <math display=\"inline\"><mfrac><mrow><mn>172</mn></mrow><mrow><mn>8</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> &times; 100 = ₹2000</p>",
                    solution_hi: "<p>69.(a)<br>P के लिए = 7 &times; 8.6%<br>Q के लिए = 8 &times; 8.6%<br>अंतर (8.6 &times; 1 इकाई) = ₹172<br>आवश्यक योग (100 इकाई) = <math display=\"inline\"><mfrac><mrow><mn>172</mn></mrow><mrow><mn>8</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> &times; 100 = ₹2000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The rice sold by a grocer contained 20% of low-quality rice. What quantity of good- quality rice should be added to 175 kg of mixed rice so that the percentage of low- quality rice remains 10% ?</p>",
                    question_hi: "<p>70. एक पंसारी द्वारा बेचे जाने वाले चावल में 20% निम्न-गुणवत्ता वाले चावल हैं। 175 kg मिश्रित चावल में कितनी मात्रा में अच्छी गुणवत्ता वाले चावल मिलाए जाने चाहिए ताकि निम्न गुणवत्ता वाले चावल का प्रतिशत 10% बना रहे ?</p>",
                    options_en: ["<p>175 kg</p>", "<p>150 kg</p>", 
                                "<p>100 kg</p>", "<p>125 kg</p>"],
                    options_hi: ["<p>175 kg</p>", "<p>150 kg</p>",
                                "<p>100 kg</p>", "<p>125 kg</p>"],
                    solution_en: "<p>70.(a)<br>Using Alligation method, we have ; <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851255049.png\" alt=\"rId47\" width=\"289\" height=\"108\"><br>1 unit --------------- 175 kg<br>So, the required quantity of good- quality rice = 175 kg</p>",
                    solution_hi: "<p>70.(a)<br>एलीगेशन विधि का उपयोग करते हुए, हमारे पास है;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851255225.png\" alt=\"rId48\" width=\"292\" height=\"104\"><br>1 इकाई --------------- 175 kg<br>तो, अच्छी गुणवत्ता वाले चावल की आवश्यक मात्रा = 175 kg</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. The largest number that can divide 147, 183 and 271 leaving remainders 11, 13 and 16, respectively, is:</p>",
                    question_hi: "<p>71. वह बड़ी से बड़ी संख्या निम्न में से कौन सी है, जिससे 147, 183 और 271 को भाग देने पर शेषफल क्रमशः 11, 13 और 16 प्राप्त होते है ?</p>",
                    options_en: ["<p>14</p>", "<p>17</p>", 
                                "<p>15</p>", "<p>16</p>"],
                    options_hi: ["<p>14</p>", "<p>17</p>",
                                "<p>15</p>", "<p>16</p>"],
                    solution_en: "<p>71.(b) According to question,<br>147 - 11 = 136, 183 - 13 = 170 and 271 - 16 = 255<br>136 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>3</mn></msup></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>17<br>170 = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>17<br>255 = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>17<strong id=\"docs-internal-guid-75565667-7fff-22f1-27a3-79f36496c1f0\"> </strong><br>HCF of (136, 170 and 255) = 17</p>",
                    solution_hi: "<p>71.(b) प्रश्न के अनुसार,<br>147 - 11 = 136, 183 - 13 = 170 और 271 - 16 = 255<br>136 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>3</mn></msup></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>17<br>170 = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>17<br>255 = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>17<br>(136, 170 और 255) का HCF = 17</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The average weight of 10 students is increased by half a kg when one of the students weighing 51 kg is replaced by a new student. Find the weight of the new student.</p>",
                    question_hi: "<p>72. 51 kg वजन वाले एक छात्र के स्थान पर नया छात्र आ जाने पर 10 छात्रों का औसत वजन आधा kg बढ़ जाता है। नए छात्र का वजन ज्ञात करें।</p>",
                    options_en: ["<p>56 kg</p>", "<p>75 kg</p>", 
                                "<p>45 kg</p>", "<p>66 kg</p>"],
                    options_hi: ["<p>56 kg</p>", "<p>75 kg</p>",
                                "<p>45 kg</p>", "<p>66 kg</p>"],
                    solution_en: "<p>72.(a) Increase average = 10 &times; 0.5 = 5 kg<br>So, new student weight = 51 + 5 = 56 kg</p>",
                    solution_hi: "<p>72.(a) औसत वृद्धि = 10 &times; 0.5 = 5 किलोग्राम<br>तो, नए छात्र का वजन = 51 + 5 = 56 किलोग्राम</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. If sin(X - Y) = sinXcosY - cosXsinY, then the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>B</mi><mo>-</mo><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math>:</p>",
                    question_hi: "<p>73. यदि sin(X - Y) = sinXcosY - cosXsinY, है, तो&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>B</mi><mo>-</mo><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></mstyle></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math></p>"],
                    solution_en: "<p>73.(d)<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>B</mi><mo>-</mo><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></mstyle></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>A</mi><mo>-</mo><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math></p>",
                    solution_hi: "<p>73.(d)<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>B</mi><mo>-</mo><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>B</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mi>A</mi><mo>-</mo><mi>C</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>C</mi></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The following bar chart shows the year-wise percentage hike in the fuel price with respect to the previous year<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851255787.png\" alt=\"rId51\" width=\"255\" height=\"221\"> <br>In how many years did the fuel prices NOT rise?</p>",
                    question_hi: "<p>74. निम्न बार-चार्ट पिछले वर्ष के संदर्भ में ईंधन की कीमत में वर्ष-वार प्रतिशत वृद्धि दर्शाता है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741851255902.png\" alt=\"rId52\" width=\"254\" height=\"220\"> <br>Fuel price hike (in %) = ईंधन की कीमत में वृद्धि (% में) <br>कितने वर्षों में ईंधन की कीमतों में कोई वृद्धि नहीं हुई?</p>",
                    options_en: ["<p>0</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>5</p>"],
                    options_hi: ["<p>0</p>", "<p>1</p>",
                                "<p>2</p>", "<p>5</p>"],
                    solution_en: "<p>74.(a)<br>From the bar chart we can see that every year there is a rise in fuel prices.</p>",
                    solution_hi: "<p>74.(a)<br>बार चार्ट से हम देख सकते हैं कि हर साल ईंधन की कीमतों में बढ़ोतरी हो रही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Which of the following fractions is the largest?<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>53</mn></mfrac><mo>,</mo><mfrac><mn>41</mn><mn>80</mn></mfrac><mo>,</mo><mfrac><mn>29</mn><mn>79</mn></mfrac></math></p>",
                    question_hi: "<p>75. निम्नलिखित में से कौन सा भिन्न सबसे बड़ा है?<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>53</mn></mfrac><mo>,</mo><mfrac><mn>41</mn><mn>80</mn></mfrac><mo>,</mo><mfrac><mn>29</mn><mn>79</mn></mfrac></mstyle></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>79</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>53</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>79</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>53</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>75.(d) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>53</mn></mfrac><mo>,</mo><mfrac><mn>41</mn><mn>80</mn></mfrac><mo>,</mo><mfrac><mn>29</mn><mn>79</mn></mfrac></mstyle></math><br>0.42 , 0.13 , 0.51, 0.36<br>Hence, <math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> is the largest fraction.</p>",
                    solution_hi: "<p>75.(d) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>53</mn></mfrac><mo>,</mo><mfrac><mn>41</mn><mn>80</mn></mfrac><mo>,</mo><mfrac><mn>29</mn><mn>79</mn></mfrac></mstyle></math><br>0.42 , 0.13 , 0.51, 0.36<br>अतः, <math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> सबसे बड़ा भिन्न है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Parts of the given sentence have been underlined. One of them contains an error. Select the option that has the segment with a grammatical error.<br>The art historian argued that the painting\'s value <span style=\"text-decoration: underline;\">has been greatly underestimated</span>, and that <span style=\"text-decoration: underline;\">its unique style and technique</span> deserved <span style=\"text-decoration: underline;\">recognition from the art community and the public alike</span>.</p>",
                    question_hi: "<p>76. Parts of the given sentence have been underlined. One of them contains an error. Select the option that has the segment with a grammatical error.<br>The art historian argued that the painting\'s value <span style=\"text-decoration: underline;\">has been greatly underestimated</span>, and that <span style=\"text-decoration: underline;\">its unique style and technique</span> deserved <span style=\"text-decoration: underline;\">recognition from the art community and the public alike</span>.</p>",
                    options_en: ["<p>its unique style and technique</p>", "<p>has been greatly underestimated</p>", 
                                "<p>recognition from the art community</p>", "<p>and the public alike</p>"],
                    options_hi: ["<p>its unique style and technique</p>", "<p>has been greatly underestimated</p>",
                                "<p>recognition from the art community</p>", "<p>and the public alike</p>"],
                    solution_en: "<p>76.(b) has been greatly underestimated<br>The given sentence is in the past tense, so the helping verb of past perfect tense (had) will be used in place of &lsquo;has&rsquo;. Hence, &lsquo;had been greatly underestimated&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(b) has been greatly underestimated<br>दिया गया sentence past tense में है, इसलिए &lsquo;has&rsquo; के स्थान पर past perfect tense ​​की helping verb, had का प्रयोग किया जाएगा। इसलिए, &lsquo;had been greatly underestimated&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate synonym of the underlined word. <br>Ramya is an <span style=\"text-decoration: underline;\">ardent</span> follower of secularism.</p>",
                    question_hi: "<p>77. Select the most appropriate synonym of the underlined word. <br>Ramya is an <span style=\"text-decoration: underline;\">ardent</span> follower of secularism.</p>",
                    options_en: ["<p>committed</p>", "<p>forced</p>", 
                                "<p>unhappy</p>", "<p>temporary</p>"],
                    options_hi: ["<p>committed</p>", "<p>forced</p>",
                                "<p>unhappy</p>", "<p>temporary</p>"],
                    solution_en: "<p>77.(a)<strong> Committed</strong>- dedicated or devoted to a cause or activity.<br><strong>Ardent- </strong>having or showing strong enthusiasm or passion.<br><strong>Forced-</strong> done under pressure or obligation.<br><strong>Unhappy- </strong>feeling or showing discontent.<br><strong>Temporary-</strong> lasting for a short period.</p>",
                    solution_hi: "<p>77.(a) <strong>Committed</strong> (प्रतिबद्ध) - dedicated or devoted to a cause or activity.<br><strong>Ardent</strong> (उत्साही) - having or showing strong enthusiasm or passion.<br><strong>Forced</strong> (बलपूर्ण) - done under pressure or obligation.<br><strong>Unhappy</strong> (अप्रसन्न) - feeling or showing discontent.<br><strong>Temporary</strong> (क्षणिक)- lasting for a short period.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78.Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;.<br>In yesterday&rsquo;s fire accident, <span style=\"text-decoration: underline;\">her feets are</span> burnt badly.</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;.<br>In yesterday&rsquo;s fire accident, <span style=\"text-decoration: underline;\">her feets</span> are burnt badly.</p>",
                    options_en: ["<p>her feet was</p>", "<p>her feet were</p>", 
                                "<p>her foots were</p>", "<p>No substitution</p>"],
                    options_hi: ["<p>her feet was</p>", "<p>her feet were</p>",
                                "<p>her foots were</p>", "<p>No substitution</p>"],
                    solution_en: "<p>78.(b) her feet were<br>&lsquo;Yesterday&rsquo; indicates past tense. &lsquo;Feets&rsquo; is incorrect as &lsquo;feet&rsquo; is the correct plural form of &lsquo;foot&rsquo;. Hence, &lsquo;her feet were&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(b) her feet were<br>&lsquo;Yesterday&rsquo; past tense को दर्शाता है। &lsquo;Feets&rsquo; incorrect है क्योंकि &lsquo;feet&rsquo; &lsquo;foot&rsquo; का सही plural form है। इसलिए, &lsquo;her feet were&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the one which can be substituted for the given words/ sentence : <br>A person extremely desirous of money</p>",
                    question_hi: "<p>79. Choose the one which can be substituted for the given words/ sentence : <br>A person extremely desirous of money</p>",
                    options_en: ["<p>avaricious</p>", "<p>fervent</p>", 
                                "<p>extravagant</p>", "<p>miser</p>"],
                    options_hi: ["<p>avaricious</p>", "<p>fervent</p>",
                                "<p>extravagant</p>", "<p>miser</p>"],
                    solution_en: "<p>79.(a)<strong> &ldquo;Avaricious&rdquo; </strong>- extreme greed.<br><strong>&ldquo;Fervent&rdquo; </strong>- having or showing great warmth. <br><strong>&ldquo;Extravagant&rdquo;</strong>- is spending too much money. <br><strong>&ldquo;Miser&rdquo; </strong>- one who doesn&rsquo;t spend a lot.</p>",
                    solution_hi: "<p>79.(a) <strong>Avaricious - </strong>अत्यधिक लालच <br><strong>Fervent</strong> (उत्साही)- जोश से भरा हुआ <br><strong>Extravagant</strong> (फिजूलखर्ची) - जो बहुत ज्यादा खर्च करता हो।<br><strong>Miser</strong>(कंजूस) - वह जो ज्यादा खर्च नहीं करता हो ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in passive voice..<br>Usnis has not used the computer for weeks.</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in passive voice.<br>Usnis has not used the computer for weeks.</p>",
                    options_en: ["<p>The computer did not used by Usnis for weeks.</p>", "<p>The computer has not been used by Usnis for weeks.</p>", 
                                "<p>The computer was not been used by Usnis for weeks.</p>", "<p>The computer has not being used by Usnis for weeks.</p>"],
                    options_hi: ["<p>The computer did not used by Usnis for weeks.</p>", "<p>The computer has not been used by Usnis for weeks.</p>",
                                "<p>The computer was not been used by Usnis for weeks.</p>", "<p>The computer has not being used by Usnis for weeks.</p>"],
                    solution_en: "<p>80.(b) The computer has not been used by Usnis for weeks. (Correct)<br>(a) The computer <strong>did not used</strong> by Usnis for weeks. (Incorrect tense)<br>(c) The computer <strong>was not been used</strong> by Usnis for weeks. (Incorrect tense)<br>(d) The computer <strong>has not being used</strong> by Usnis for weeks. (Incorrect tense)</p>",
                    solution_hi: "<p>80.(b) The computer has not been used by Usnis for weeks. (Correct) <br>(a) The computer <strong>did not used</strong> by Usnis for weeks. (गलत verb (<strong>did not used</strong>) का प्रयोग किया गया है। has not been used) का प्रयोग होगा I )<br>(c) The computer <strong>was not been used</strong> by Usnis for weeks. (गलत verb (<strong>was not been used</strong>) का प्रयोग किया गया है। has not been used) का प्रयोग होगा | )<br>(d) The computer <strong>has not being used</strong> by Usnis for weeks. (गलत verb (<strong>has not being used</strong>) का प्रयोग किया गया है) has not been used) का प्रयोग होगा | )</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Some consider this as an eco-feminist movement where women are actively involved.<br>Q.An illiterate women Gora Devi a tribal leader from the Chamoli district mobilized the women to protect their forests.<br>R. So they adopted the Chipko movement which promotes nonviolence.<br>S. The Bhutiyas knew they had to save their forests.</p>",
                    question_hi: "<p>81. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Some consider this as an eco-feminist movement where women are actively involved.<br>Q.An illiterate women Gora Devi a tribal leader from the Chamoli district mobilized the women to protect their forests.<br>R. So they adopted the Chipko movement which promotes nonviolence.<br>S. The Bhutiyas knew they had to save their forests.</p>",
                    options_en: ["<p>QPSR</p>", "<p>QPRS</p>", 
                                "<p>PSRQ</p>", "<p>SRQP</p>"],
                    options_hi: ["<p>QPSR</p>", "<p>QPRS</p>",
                                "<p>PSRQ</p>", "<p>SRQP</p>"],
                    solution_en: "<p>81.(d) SRQP<br>Sentence S will be the starting line as it contains the main subject of the parajumble i.e. The Bhutiyas, who had to save their forests. And, Sentence R states what they adopted to save it. So, R will follow S. Further, Sentence Q states about an illiterate lady who is the tribal leader and Sentence P states how it was considered as it involved women. So, P will follow Q. Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>81.(d) SRQP<br>Sentence S starting line होगी क्योंकि इसमें parajumble का मुख्य विषय &lsquo;The Bhutiyas, who had to save their forests&rsquo; शामिल है। Sentence R बताता है कि उन्होंने इसे बचाने के लिए क्या अपनाया। तो, S के बाद R आएगा । आगे, Sentence Q एक अनपढ़ महिला के बारे में बताता है जो आदिवासी नेता है। और वाक्य P हमें बताता है कि क्योंकि इसमें महिलाएं शामिल थीं तो इस आंदोलन को कैसा माना गया । इसलिए, Q के बाद P आएगा। options के माध्यम से जाने पर , option (d) में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the correctly spelt word.</p>",
                    question_hi: "<p>82. Select the correctly spelt word.</p>",
                    options_en: ["<p>machinary</p>", "<p>dispensery</p>", 
                                "<p>dictionery</p>", "<p>directory</p>"],
                    options_hi: ["<p>machinary</p>", "<p>dispensery</p>",
                                "<p>dictionery</p>", "<p>directory</p>"],
                    solution_en: "<p>82.(d) directory<br>&lsquo;Directory&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>82.(d) directory<br>&lsquo;Directory&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. From among the words given in bold, select the <strong>INCORRECTLY</strong> spelt word in the following sentence.<br>Almost all the governors of Bengal strongly <strong>resented</strong> the special <strong>privileges</strong> enjoyed by English company as it meant a huge loss to the <strong>provinciel exchequer</strong>.</p>",
                    question_hi: "<p>83. From among the words given in bold, select the <strong>INCORRECTLY</strong> spelt word in the following sentence.<br>Almost all the governors of Bengal strongly <strong>resented</strong> the special <strong>privileges</strong> enjoyed by English company as it meant a huge loss to the <strong>provinciel exchequer</strong>.</p>",
                    options_en: ["<p>provinciel</p>", "<p>resented</p>", 
                                "<p>exchequer</p>", "<p>privileges</p>"],
                    options_hi: ["<p>provinciel</p>", "<p>resented</p>",
                                "<p>exchequer</p>", "<p>privileges</p>"],
                    solution_en: "<p>83.(a) provinciel<br>Provincial is the correct spelling.</p>",
                    solution_hi: "<p>83.(a) provinciel<br>Provincial सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct.&nbsp;<br>One should respect the religions of others as much as his own .</p>",
                    question_hi: "<p>84. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>One should respect the religions of others as much as his own .</p>",
                    options_en: ["<p>One should respect</p>", "<p>the religions of others</p>", 
                                "<p>as much as his own</p>", "<p>No error</p>"],
                    options_hi: ["<p>One should respect</p>", "<p>the religions of others</p>",
                                "<p>as much as his own</p>", "<p>No error</p>"],
                    solution_en: "<p>84.(c) as much as his own <br>Pronoun have used as &lsquo;one&rsquo; in the beginning , we do not have to use &lsquo;with his own&rsquo; in part c. Replace &ldquo;his own&rdquo; by one&rsquo;s own</p>",
                    solution_hi: "<p>84.(c) as much as his own <br>Pronoun को शुरुआत में &lsquo;one&rsquo; के रूप में प्रयोग किया है, हमें part c में &lsquo;with his Own&rsquo; प्रयोग नहीं करना है। &ldquo;his own&rdquo; के स्थान पर \"one&rsquo;s own\" का प्रयोग होगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate meaning of the given idiom. <br>Go down in flames</p>",
                    question_hi: "<p>85. Select the most appropriate meaning of the given idiom. <br>Go down in flames</p>",
                    options_en: ["<p>To crash and burn</p>", "<p>To destroy completely</p>", 
                                "<p>To fail spectacularly</p>", "<p>To burn down completely</p>"],
                    options_hi: ["<p>To crash and burn</p>", "<p>To destroy completely</p>",
                                "<p>To fail spectacularly</p>", "<p>To burn down completely</p>"],
                    solution_en: "<p>85.(c) <strong>Go down in flames - </strong>to fail spectacularly.<br>E.g.- The project went down in flames after the team missed all their deadlines.</p>",
                    solution_hi: "<p>85.(c) <strong>Go down in flames -</strong> to fail spectacularly./शानदार ढंग से विफल होना।<br>E.g.- The project went down in flames after the team missed all their deadlines.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>He <span style=\"text-decoration: underline;\"><strong>absented</strong></span> from the meeting.</p>",
                    question_hi: "<p>86. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>He <strong><span style=\"text-decoration: underline;\">absented</span></strong> from the meeting.</p>",
                    options_en: ["<p>was absent</p>", "<p>absented himself</p>", 
                                "<p>took absence</p>", "<p>No improvement</p>"],
                    options_hi: ["<p>was absent</p>", "<p>absented himself</p>",
                                "<p>took absence</p>", "<p>No improvement</p>"],
                    solution_en: "<p>86.(b) absented himself</p>",
                    solution_hi: "<p>86.(b) absented himself</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Four alternatives are given for the idiom/phrase underlined in the sentence. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase. <br>My father <span style=\"text-decoration: underline;\">strained every nerve</span> to enable me to get settled in life.</p>",
                    question_hi: "<p>87. Four alternatives are given for the idiom/phrase underlined in the sentence. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase. <br>My father <span style=\"text-decoration: underline;\">strained every nerve</span> to enable me to get settled in life.</p>",
                    options_en: ["<p>worked very hard</p>", "<p>spent a large amount</p>", 
                                "<p>tried all tricks</p>", "<p>bribed several persons</p>"],
                    options_hi: ["<p>worked very hard</p>", "<p>spent a large amount</p>",
                                "<p>tried all tricks</p>", "<p>bribed several persons</p>"],
                    solution_en: "<p>87.(a) worked very hard.<br>Example - Students strain every nerve to clear the exams.</p>",
                    solution_hi: "<p>87.(a) worked very hard.<br>उदाहरण - Students strain every nerve to clear the exams./छात्र परीक्षा पास करने के लिए हर संभव प्रयास करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. And Vasco Da Gama came to India in 1498.<br>Q. Europeans had long enjoyed spices imported from India by the Arabs, the Portuguese and the Spaniards.<br>R. They developed sea routes and navigational tools that made it easier for them to sail upon the open ocean.<br>S. They set out for India by going through the Atlantic Ocean, Christopher Columbus ended up in America in 1492.</p>",
                    question_hi: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. And Vasco Da Gama came to India in 1498.<br>Q. Europeans had long enjoyed spices imported from India by the Arabs, the Portuguese and the Spaniards.<br>R. They developed sea routes and navigational tools that made it easier for them to sail upon the open ocean.<br>S. They set out for India by going through the Atlantic Ocean, Christopher Columbus ended up in America in 1492.</p>",
                    options_en: ["<p>QPSR</p>", "<p>QRSP</p>", 
                                "<p>QSPR</p>", "<p>RSPQ</p>"],
                    options_hi: ["<p>QPSR</p>", "<p>QRSP</p>",
                                "<p>QSPR</p>", "<p>RSPQ</p>"],
                    solution_en: "<p>88.(b) QRSP<br>Sentence Q will be the starting line as it contains the main idea of the parajumble i.e. Spices, imported from India. And, Sentence R states how they made it easy to import species. So, R will follow Q. Further, Sentence S states about Christopher who set out for India and Sentence P states about Vasco Da Gama, who came to India. So, P will follow S. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>88.(b) QRSP<br>Sentence Q starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;Spices, imported from India&rsquo; शामिल है। Sentence R बताता है कि कैसे उन्होंने प्रजातियों को आयात करना आसान बना दिया। तो, Q के बाद R आएगा। आगे, Sentence S क्रिस्टोफर के बारे में बताता है जो भारत के लिए निकला और Sentence P वास्को डी गामा के बारे में बताता है, जो भारत आया था। तो, S के बाद Pआएगा। options के माध्यम से जाने पर , option (b) में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "89. Select the option that can be used as a one-word substitute for the given group of words. <br />An open space usually rectangular and enclosed in a building",
                    question_hi: "89. Select the option that can be used as a one-word substitute for the given group of words. <br />An open space usually rectangular and enclosed in a building",
                    options_en: [" Quinton  ", " Quadrangle  ", 
                                " Quintessential  ", " Quadruped "],
                    options_hi: [" Quinton  ", " Quadrangle  ",
                                " Quintessential  ", " Quadruped "],
                    solution_en: "<p>89.(b) <strong>Quadrangle- </strong>an open space usually rectangular and enclosed in a building.<br><strong>Quinton</strong>- queen\'s town or settlement.<br><strong>Quintessential-</strong> representing the most perfect or typical example of a quality or class.<br><strong>Quadruped-</strong> any animal that has four feet.</p>",
                    solution_hi: "<p>89.(b) <strong>Quadrangle</strong> (चौकोना आँगन या चौक) - an open space usually rectangular and enclosed in a building.<br><strong>Quinton</strong> (क्विंटन) - queen\'s town or settlement.<br><strong>Quintessential </strong>(सर्वोत्कृष्ट) - representing the most perfect or typical example of a quality or class.<br><strong>Quadruped </strong>(चतुष्पद जानवर) - any animal that has four feet.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. The app gets Larry&rsquo;s parents involved in the learning and improvement process, sending them advice about Larry&rsquo;s behaviours and progress, and recommending motivational _______ they might undertake that are _______ to Larry&rsquo;s situation and the program&rsquo;s _________ about his attitudes and behaviours.</p>",
                    question_hi: "<p>90. The app gets Larry&rsquo;s parents involved in the learning and improvement process, sending them advice about Larry&rsquo;s behaviours and progress, and recommending motivational _______ they might undertake that are _______ to Larry&rsquo;s situation and the program&rsquo;s _________ about his attitudes and behaviours.</p>",
                    options_en: ["<p>intermissions, directed, injunctions</p>", "<p>interpolations, disrupted, inventions</p>", 
                                "<p>interventions, targeted, inferences</p>", "<p>interruptions, disillusioned, invasions</p>"],
                    options_hi: ["<p>intermissions, directed, injunctions</p>", "<p>interpolations, disrupted, inventions</p>",
                                "<p>interventions, targeted, inferences</p>", "<p>interruptions, disillusioned, invasions</p>"],
                    solution_en: "<p>90.(c) Interventions means the action of becoming intentionally involved in a difficult situation, in order to improve it or prevent it from getting worse.And intervention is available in option c only so c is the correct answer.</p>",
                    solution_hi: "<p>90.(c) Interventions का अर्थ है- किसी कठिन परिस्थिति में जानबूझकर शामिल होने की क्रिया, ताकि उसमें सुधार किया जा सके या उसे बिगड़ने से रोका जा सके और intervention केवल विकल्प (c) में उपलब्ध है इसलिए (c) सही उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.&nbsp;<br>Pay attention.</p>",
                    question_hi: "<p>91.In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>Pay attention.</p>",
                    options_en: ["<p>Heed</p>", "<p>Glance at</p>", 
                                "<p>Overlook</p>", "<p>Repair</p>"],
                    options_hi: ["<p>Heed</p>", "<p>Glance at</p>",
                                "<p>Overlook</p>", "<p>Repair</p>"],
                    solution_en: "<p>91.(a) heed.<br>Heed - pay attention to; take notice of.</p>",
                    solution_hi: "<p>91.(a) heed<br>Heed - ध्यान देना; या सूचना लेना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the option that expresses the given sentence in passive voice.<br>They propose to build a dam for irrigation purposes</p>",
                    question_hi: "<p>92. Select the option that expresses the given sentence in passive voice.<br>They propose to build a dam for irrigation purposes</p>",
                    options_en: ["<p>Build a dam for irrigation purposes.</p>", "<p>For the irrigation purposes a dam has been built.</p>", 
                                "<p>Irrigation dam is built.</p>", "<p>It is proposed to build a dam for irrigation purposes.</p>"],
                    options_hi: ["<p>Build a dam for irrigation purposes.</p>", "<p>For the irrigation purposes a dam has been built.</p>",
                                "<p>Irrigation dam is built.</p>", "<p>It is proposed to build a dam for irrigation purposes.</p>"],
                    solution_en: "<p>92.(d) It is proposed to build a dam for irrigation purposes. (Correct)<br>(a) Build a dam for irrigation purposes. (Tone has changed to authoritative)<br>(b) For the irrigation purposes a dam has been built. (Meaning has changed)<br>(c) Irrigation dam is built. (Meaning has changed)</p>",
                    solution_hi: "<p>92.(d) It is proposed to build a dam for irrigation purposes. (Correct)<br>(a) Build a dam for irrigation purposes.(Sentence का meaning original sentence से different है)<br>(b) For the irrigation purposes a dam has been built. (Sentence का meaning original sentence से different है)<br>(c) Irrigation dam is built. (Sentence का meaning original sentence से different है)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Choose the word opposite in meaning to the given word.<br>Relentless</p>",
                    question_hi: "<p>93. Choose the word opposite in meaning to the given word.<br>Relentless</p>",
                    options_en: ["<p>merciless</p>", "<p>yielding</p>", 
                                "<p>monotonous</p>", "<p>incisive</p>"],
                    options_hi: ["<p>merciless</p>", "<p>yielding</p>",
                                "<p>monotonous</p>", "<p>incisive</p>"],
                    solution_en: "<p>93.(b) <strong>yielding</strong><br><strong>Relentless </strong>- for describing something that\'s harsh, unforgiving, and persistent,<br>(a) <strong>merciless </strong>- showing no mercy. <br>(c) <strong>monotonous</strong> - dull, tedious, and repetitious; lacking in variety and interest. <br>(d) <strong>incisive -</strong> (of a person or mental process) intelligently analytical and clear-thinking.</p>",
                    solution_hi: "<p>93.(b) <strong>yielding</strong><br><strong>Relentless - </strong>means for describing something that\'s harsh, unforgiving, and persistent,<br>(a) <strong>merciless-</strong> कोई दया ना दिखाना। <br>(c)<strong> monotonous </strong>- नीरस, थकाऊ और दोहरावदार; विविधता और रुचि में कमी। <br>(d) <strong>incisive -</strong> किसी व्यक्ति की स्पष्ट सोच।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Fill in the blank with most appropriate word<br>I am ______ of joining the armed forces.</p>",
                    question_hi: "<p>94. Fill in the blank with most appropriate word<br>I am ______ of joining the armed forces.</p>",
                    options_en: ["<p>interested</p>", "<p>keen</p>", 
                                "<p>desirous</p>", "<p>eager</p>"],
                    options_hi: ["<p>interested</p>", "<p>keen</p>",
                                "<p>desirous</p>", "<p>eager</p>"],
                    solution_en: "<p>94.(c) desirous <br>Desirous means wanting something very much. However, desirous always takes the preposition &lsquo;of&rsquo; with it. The given sentence states that the narrator is desirous of joining the armed forces. Hence, &lsquo;desirous&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(c) desirous <br>Desirous का अर्थ है किसी वस्तु की अत्यधिक चाह या &lsquo;इच्छुक होना&rsquo; । हालाँकि, desirous के साथ हमेशा preposition &lsquo;of&rsquo; होता है। दिए गए वाक्य में कहा गया है कि narrator &lsquo;सशस्त्र बलों में शामिल होने को इच्छुक है&rsquo;। इसलिए, &lsquo;desirous&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate ANTONYM of the given word.<br>Confound</p>",
                    question_hi: "<p>95. Select the most appropriate ANTONYM of the given word.<br>Confound</p>",
                    options_en: ["<p>Expire</p>", "<p>Suppress</p>", 
                                "<p>Discern</p>", "<p>Assure</p>"],
                    options_hi: ["<p>Expire</p>", "<p>Suppress</p>",
                                "<p>Discern</p>", "<p>Assure</p>"],
                    solution_en: "<p>95.(c) <strong>Discern</strong>- to recognize or understand something clearly.<br><strong>Confound-</strong> to confuse or perplex someone.<br><strong>Expire-</strong> to come to an end or cease to be valid.<br><strong>Suppress-</strong> to prevent or restrain something.<br><strong>Assure-</strong> to make someone feel certain or confident.</p>",
                    solution_hi: "<p>95.(c) <strong>Discern</strong> (पहचानना) - to recognize or understand something clearly.<br><strong>Confound</strong> (उलझाना) - to confuse or perplex someone.<br><strong>Expire</strong> (समाप्त होना) - to come to an end or cease to be valid.<br><strong>Suppress</strong> (अवरोध करना) - to prevent or restrain something.<br><strong>Assure</strong> (आश्वासन देना) - to make someone feel certain or confident.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.&nbsp;<br>Select the most appropriate option to fill in the blank number 96.</p>",
                    question_hi: "<p>96.<strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.&nbsp;<br>Select the most appropriate option to fill in the blank number 96.</p>",
                    options_en: ["<p>who</p>", "<p>whether</p>", 
                                "<p>which</p>", "<p>that</p>"],
                    options_hi: ["<p>who</p>", "<p>whether</p>",
                                "<p>which</p>", "<p>that</p>"],
                    solution_en: "<p>96.(d) that<br>&lsquo;That&rsquo; is used to introduce a new clause in a sentence. The given passage states that the postal service is the government agency that <span style=\"text-decoration: underline;\">handles the mail</span>(new clause). Hence, &lsquo;that&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) that<br>&lsquo;That&rsquo; का प्रयोग वाक्य में new clause (नए उपवाक्य) को प्रस्तुत करने के लिए किया जाता है। दिए गए passage में कहा गया है कि डाक सेवा सरकारी एजेंसी है जो mail (new clause) को संभालती है। इसलिए, &lsquo;that&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.<br>Select the most appropriate option to fill in the blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.&nbsp;<br>Select the most appropriate option to fill in the blank number 97.</p>",
                    options_en: ["<p>being delivered</p>", "<p>to be delivered</p>", 
                                "<p>to have delivered</p>", "<p>to deliver</p>"],
                    options_hi: ["<p>being delivered</p>", "<p>to be delivered</p>",
                                "<p>to have delivered</p>", "<p>to deliver</p>"],
                    solution_en: "<p>97.(d) to deliver<br>&lsquo;Preposition<span style=\"text-decoration: underline;\"> to + first form of the verb</span>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>)&rdquo; is grammatically the correct structure for the given sentence which is in the present tense. Hence, &lsquo;to deliver(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(d) to deliver<br>&lsquo;Preposition <span style=\"text-decoration: underline;\">to + first form of the verb</span>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>)&rdquo; दिए गए वाक्य के लिए व्याकरणिक रूप से सही संरचना है जो present tense में है। इसलिए,&lsquo;to deliver(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.<br>Select the most appropriate option to fill in the blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.<br>Select the most appropriate option to fill in the blank number 98.</p>",
                    options_en: ["<p>less quickly</p>", "<p>too quickly</p>", 
                                "<p>so quickly that</p>", "<p>as quickly as</p>"],
                    options_hi: ["<p>less quickly</p>", "<p>too quickly</p>",
                                "<p>so quickly that</p>", "<p>as quickly as</p>"],
                    solution_en: "<p>98.(d) as quickly as<br>The phrase &lsquo;as quickly as&rsquo; means as fast as you are able. The given passage states that&nbsp;its goal is to see that your mail gets to its destination as fast(quickly) as it is possible. Hence, &lsquo;as quickly as&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) as quickly as<br>वाक्यांश(phrase) &lsquo;as quickly as&rsquo; का अर्थ है- जितना तेज़ आप कर सकते हैं। दिया गया passage बताता है कि&nbsp;इसका लक्ष्य यह देखना है कि आपका मेल अपने गंतव्य तक जितनी जल्दी हो सके (जल्दी से) पहुंच जाए। इसलिए, &lsquo;as quickly as&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.<br>Select the most appropriate option to fill in the blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.<br>Select the most appropriate option to fill in the blank number 99.</p>",
                    options_en: ["<p>back out</p>", "<p>check out</p>", 
                                "<p>come in</p>", "<p>rely on</p>"],
                    options_hi: ["<p>back out</p>", "<p>check out</p>",
                                "<p>come in</p>", "<p>rely on</p>"],
                    solution_en: "<p>99.(d) rely on<br>The phrase &lsquo;rely on&rsquo; means to need(someone or something) for support, help, etc. The given passage states that people rely on(need support of) the postal service to deliver important letters. Hence, &lsquo;rely on&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) rely on<br>वाक्यांश(phrase) &lsquo;rely on&rsquo; का अर्थ समर्थन, सहायता आदि के लिए (किसी या कुछ) की आवश्यकता है। दिए गए passage में कहा गया है कि लोग महत्वपूर्ण पत्र वितरित करने के लिए डाक सेवा पर भरोसा करते हैं । इसलिए, &lsquo;rely on&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.<br>Select the most appropriate option to fill in the blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze test:</strong><br>The postal service is the government agency (96)_________handles the mail. Its job is (97)________letters and packages to people and businesses all over the world. Its goal is to see that your mail gets to its destination (98)________ possible. People (99)_________ the postal service to deliver important letters and even valuables, (100)_________time and to the right person.<br>Select the most appropriate option to fill in the blank number 100.</p>",
                    options_en: ["<p>to</p>", "<p>for</p>", 
                                "<p>at</p>", "<p>on</p>"],
                    options_hi: ["<p>to</p>", "<p>for</p>",
                                "<p>at</p>", "<p>on</p>"],
                    solution_en: "<p>100.(d) on<br>The phrase &lsquo;on time&rsquo; means not too late or too early, punctual. The given passage states that the postal service delivers important letters and even valuables on time. Hence, &lsquo;on&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) on<br>&lsquo;On time&rsquo; वाक्यांश( phrase) का अर्थ है - ना बहुत देर या ना बहुत जल्दी, समय का पाबंद। दिए गए passage में कहा गया है कि डाक सेवा समय पर महत्वपूर्ण पत्र और कीमती सामान भी वितरित करती है। इसलिए, &lsquo;on&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>