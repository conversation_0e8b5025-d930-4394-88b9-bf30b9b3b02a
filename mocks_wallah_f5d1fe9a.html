<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Pipes A and B can fill a tank in 15 hours and 25 hours, respectively, whereas pipe C can empty the full tank in 40 hours. All three pipes are opened together, but pipe A is closed after 5 hours. After how many hours will the remaining part of the tank be filled ?</p>",
                    question_hi: "<p>1. पाइप A और B एक टंकी को क्रमशः 15 घंटे और 25 घंटे में भर सकते हैं, जबकि पाइप C पूरी टंकी को 40 घंटे में खाली कर सकता है। तीनों पाइप एक साथ खोले जाते हैं, लेकिन पाइप A, 5 घंटे बाद बंद हो जाता है। टंकी का शेष भाग कितने घंटे बाद भर जाएगा ?</p>",
                    options_en: ["<p>41<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p>43<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", 
                                "<p>44<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p>39<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>41<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p>43<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                                "<p>44<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p>39<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>1.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926594699.png\" alt=\"rId4\" width=\"303\" height=\"159\"><br>All three pipes capacity of after 5 hours = 5 (40 + 24 - 15) = 245 units <br>Remaining capacity = 600 - 245 = 355 units<br>According to the question,<br>Remaining tank can be filled = <math display=\"inline\"><mfrac><mrow><mn>355</mn></mrow><mrow><mn>24</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>355</mn><mn>9</mn></mfrac><mo>=</mo><mfrac><mn>394</mn><mn>9</mn></mfrac><mo>&#160;</mo></math>hours</p>",
                    solution_hi: "<p>1.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926594824.png\" alt=\"rId5\" width=\"308\" height=\"172\"><br>5 घंटे के बाद तीनों पाइपों की क्षमता = 5 (40 + 24 - 15) = 245 इकाई<br>शेष क्षमता = 600 - 245 = 355 इकाई<br>प्रश्न के अनुसार,<br>शेष टंकी को भरने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>355</mn></mrow><mrow><mn>24</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>355</mn><mn>9</mn></mfrac><mo>=</mo><mfrac><mn>394</mn><mn>9</mn></mfrac><mo>&#160;</mo></math>घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Two pipes can fill a tank in 12 hours and 18 hours, respectively, while a third pipe can empty it in 8 hours. How long (in hours) will it take to fill the empty tank if all three pipes are opened simultaneously ?</p>",
                    question_hi: "<p>2. दो पाइप एक टैंक को क्रमशः 12 घंटे और 18 घंटे में भर सकते हैं, जबकि तीसरा पाइप इसे 8 घंटे में खाली कर सकता है। यदि तीनों पाइप एक साथ खोल दिए जाते हैं, तो खाली टैंक को भरने में कितना समय (घंटों में) लगेगा ?</p>",
                    options_en: ["<p>48</p>", "<p>24</p>", 
                                "<p>36</p>", "<p>72</p>"],
                    options_hi: ["<p>48</p>", "<p>24</p>",
                                "<p>36</p>", "<p>72</p>"],
                    solution_en: "<p>2.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926594936.png\" alt=\"rId6\" width=\"238\" height=\"135\"><br>Efficiency of A, B and C = 6 + 4 - 9 = 1 unit<br>Time taken to fill the empty tank = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 72 hours</p>",
                    solution_hi: "<p>2.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926595085.png\" alt=\"rId7\" width=\"192\" height=\"136\"><br>A, B और C की दक्षता = 6 + 4 - 9 = 1 इकाई<br>खाली टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 72 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Pipe X can fill a tank in 60 hours while pipe Y can fill the tank in 72 hours. Both pipes are opened together for 20 hours. How much of the tank is left empty ?</p>",
                    question_hi: "<p>3. पाइप X एक टंकी को 60 घंटे में भर सकती है जबकि पाइप Y उस टंकी को 72 घंटे में भर सकता है। दोनों पाइपों को एक साथ 20 घंटे के लिए खोला जाता है। टंकी का कितना भाग खाली बचेगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>243</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>243</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926595243.png\" alt=\"rId8\" width=\"181\" height=\"135\"><br>Tank fill in 20hr by both the pipe = (6 + 5) &times; 20 = 220<br>Part of tank is left empty = <math display=\"inline\"><mfrac><mrow><mn>360</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>220</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>360</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    solution_hi: "<p>3.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926595358.png\" alt=\"rId9\" width=\"158\" height=\"133\"><br>दोनों पाइपों द्वारा 20 घंटे में टंकी भरना = (6 + 5) &times; 20 = 220<br>जितनी टंकी का भाग खाली छोड़ा गया = <math display=\"inline\"><mfrac><mrow><mn>360</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>220</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>360</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Pipes A and B can fill a tank in 6 hours and 8 hours, respectively. Both pipes are opened together for 3 hours. After that pipe A is closed, and B continues to fill the tank. In how many hours will the tank be filled ?</p>",
                    question_hi: "<p>4. पाइप A और B एक टैंक को क्रमशः 6 घंटे और 8 घंटे में पूरा भर सकते हैं। दोनों पाइपों को एक साथ 3 घंटे के लिए खोल दिया जाता है। उसके बाद पाइप A को बंद कर दिया जाता है, और पाइप B टैंक को भरना जारी रखता है। टैंक कितने घंटे में पूरा भर जायेगा ?</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>3</p>", "<p>6</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>3</p>", "<p>6</p>"],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926595473.png\" alt=\"rId10\" width=\"237\" height=\"163\"><br>Water filled by pipe A and B in 3 hours = (4 + 3) &times; 3 = 21 units<br>Remaining capacity of tank = 24 - 21 = 3 units<br>Water filled by pipe B alone = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 1 hours<br>Total time to fill the tank = 3 + 1 = 4 hours</p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926595599.png\" alt=\"rId11\" width=\"241\" height=\"171\"><br>पाइप A और B द्वारा 3 घंटे में भरा गया पानी = (4 + 3) &times; 3 = 21 इकाई<br>टैंक की शेष क्षमता = 24 - 21 = 3 इकाई<br>अकेले पाइप B द्वारा भरा गया पानी = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 1 घंटा<br>टंकी भरने में लगा कुल समय = 3 + 1 = 4 घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Pipe Q can fill the tank in 60 hours while pipe R may fill in 45 hours. Q and R pipes are opened together for 6 hours after which pipe W is also opened to empty the tank. All three pipes are opened simultaneously for 24 hours to reach the half level mark. How much time (in hours) will pipe W alone take to empty the entire tank ?</p>",
                    question_hi: "<p>5. पाइप Q, किसी टंकी को 60 घंटे में भर सकता है, जबकि पाइप R इसे 45 घंटे में भर सकता है। Q और R पाइप, 6 घंटे के लिए एक साथ खोले जाते हैं, जिसके बाद टंकी को खाली करने के लिए पाइप W को भी खोला जाता है। आधे स्तर के निशान तक पहुंचने के लिए तीनों पाइपों को 24 घंटे के लिए एक साथ खोला जाता है। अकेले पाइप W द्वारा पूरी टंकी को खाली करने में लगने वाला समय (घंटे में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>48</p>", "<p>42</p>", 
                                "<p>36</p>", "<p>30</p>"],
                    options_hi: ["<p>48</p>", "<p>42</p>",
                                "<p>36</p>", "<p>30</p>"],
                    solution_en: "<p>5.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926595721.png\" alt=\"rId12\" width=\"264\" height=\"164\"><br>Efficiency of Q and R in 1 hour = 3 + 4 = 7 unit<br>Water filled by Q and R in 6 hours = 7 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 42 units<br>Now <br>Q and R along with pipe W opened then tank will be half filled in 24 hours i.e. 90 unit<br>Water filled by Q and R in 24 hour + previous 6 hours = 7 <math display=\"inline\"><mo>&#215;</mo></math> 24 + 42 unit = 168 + 42 = 210 unit<br>But it was only 90 unit filled <br>Hence water emptied by W in 24 hours = 210 - 90 = 120 unit<br>Efficiency of W in 1 hour = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 5 unit<br>Hence,<br>Time taken by W to empty entire tank = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 36 hours</p>",
                    solution_hi: "<p>5.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926595851.png\" alt=\"rId13\" width=\"257\" height=\"154\"><br>1 घंटे में Q और R की दक्षता = 3 + 4 = 7 इकाई<br>Q और R द्वारा 6 घंटे में भरा गया पानी = 7 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 42 इकाई<br>अब<br>Q और R को पाइप W के साथ खोला गया तो टंकी 24 घंटे में आधी यानी 90 इकाई भर जाएगी<br>पिछले 6 घंटे और 24 घंटे में Q और R द्वारा भरा गया पानी = 7 <math display=\"inline\"><mo>&#215;</mo></math> 24 + 42 इकाई = 168 + 42 = 210 इकाई<br>लेकिन यह केवल 90 इकाई ही भरा था <br>अतः 24 घंटे में W द्वारा खाली किया गया पानी = 210 - 90 = 120 इकाई<br>1 घंटे में W की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 5 इकाई<br>इस तरह,<br>W द्वारा संपूर्ण टैंक खाली करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 36 hours</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Pipe X can fill a tank in 9 hours and Pipe Y can fill it in 21 hours. If they are opened on alternate hours and Pipe X is opened first, in how many hours shall the tank be full ?</p>",
                    question_hi: "<p>6. पाइप X एक टंकी को 9 घंटे में भर सकता है और पाइप Y इसे 21 घंटे में भर सकता है। यदि उन्हें एकांतर घंटों में (बारी-बारी से एक-एक घंटे पर) खोला जाता है और पाइप X को पहले खोला जाता है, तो टंकी कितने घंटों में पूरी भर जाएगी ?</p>",
                    options_en: ["<p>10<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>10<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>6.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926595961.png\" alt=\"rId14\" width=\"209\" height=\"151\"><br>If Pipes are opened on alternate hr , than <br>In 2hr , tank fill in = (7 + 3) = 10 unit <br><math display=\"inline\"><mo>&#8658;</mo></math> 12 hr = 60 unit <br>Remaining 3 unit fill by pipe X in <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hr<br><math display=\"inline\"><mo>&#8756;</mo></math> Total time taken to full the tank = 12 hr + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hr = 12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hr</p>",
                    solution_hi: "<p>6.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926596056.png\" alt=\"rId15\" width=\"171\" height=\"150\"><br>यदि पाइप वैकल्पिक घंटे पर खोले जाते हैं, तो <br>2 घंटे में, टैंक भरेगा = (7 + 3) = 10 इकाई <br><math display=\"inline\"><mo>&#8658;</mo></math> 12 घंटे = 60 इकाई <br>शेष 3 इकाई , पाइप X द्वारा <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे में भरा जाएगा <br><math display=\"inline\"><mo>&#8756;</mo></math> टैंक को पूरा भरने में लगा कुल समय = 12 घंटे + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>घंटे = 12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. At a school building, there is an overhead tank. To fill this tank 50 buckets of water are required. Assume that the capacity of the bucket is reduced to two-fifth of the present. How many buckets of water are required to fill the same tank&nbsp; ?</p>",
                    question_hi: "<p>7. एक स्कूल बिल्डिंग में एक ओवरहेड टैंक है। इस टैंक को भरने के लिए 50 बाल्टी पानी की आवश्यकता होती है। मान लीजिए कि बाल्टी की क्षमता इसकी वर्तमान क्षमता का दो-पाँचवां भाग कर दी जाती है। तो उसी टैंक को भरने के लिए कितनी बाल्टी पानी की आवश्यकता होगी ?</p>",
                    options_en: ["<p>62.5</p>", "<p>20</p>", 
                                "<p>125</p>", "<p>60</p>"],
                    options_hi: ["<p>62.5</p>", "<p>20</p>",
                                "<p>125</p>", "<p>60</p>"],
                    solution_en: "<p>7.(c) Let the initial capacity of bucket = 5 <br>After reducing two-fifth of the initial , the capacity of bucket = 2 <br>Total capacity of the tank = 5 &times; 50 = 250 <br>After reduction , required no of bucket to fill the same tank = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>250</mn><mn>2</mn></mfrac></math> = 125</p>",
                    solution_hi: "<p>7.(c) <br>माना बाल्टी की प्रारंभिक क्षमता = 5 <br>प्रारंभिक का दो-पाँचवाँ हिस्सा कम करने के बाद, बाल्टी की क्षमता = 2 <br>टैंक की कुल क्षमता = 5 &times; 50 = 250 <br>कमी के बाद, उसी टंकी को भरने के लिए आवश्यक बाल्टी की संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>250</mn><mn>2</mn></mfrac></math> = 125</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Two inlet pipes, P1 and P2, can fill a cistern in 20 hours and 30 hours, respectively. They were opened at the same time, but pipe P1 had to be closed 5 hours before the cistern was full. How many hours in total did it take for the two pipes to fill the cistern ?</p>",
                    question_hi: "<p>8. दो इनलेट पाइप, P1 और P2, एक टंकी को क्रमश: 20 घंटे और 30 घंटे में भर सकते हैं। उन्हें एक ही समय पर खोला गया, लेकिन टंकी के पूरा भरने से 5 घंटे पहले पाइप P1 को बंद करना पड़ा। दोनों पाइपों के द्वारा टंकी को भरने में कुल कितने घंटे लगे ?</p>",
                    options_en: ["<p>15 hours</p>", "<p>9 hours</p>", 
                                "<p>12 hours</p>", "<p>10 hours</p>"],
                    options_hi: ["<p>15 घंटे</p>", "<p>9 घंटे</p>",
                                "<p>12 घंटे</p>", "<p>10 घंटे</p>"],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926596257.png\" alt=\"rId16\" width=\"211\" height=\"125\"><br>Total efficiency of pipe (P1 + P2) = 3 + 2 = 5 units<br>Water filled by pipe P2 in 5 hours = 2 &times; 5 = 10 units<br>amount of water to be filled by both pipe = 60 - 10 = 50 unit<br>Time taken to fill 50 unit water by both pipe = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 10 hours<br>Hence, total time taken = 5 hrs + 10 hrs = 15 hrs</p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926596369.png\" alt=\"rId17\" width=\"190\" height=\"128\"><br>पाइप (P1 +P2) की कुल दक्षता = 3 + 2 = 5 इकाई<br>पाइप P2 द्वारा 5 घंटे में भरा गया पानी = 2 &times; 5 = 10 इकाई<br>दोनों पाइपों द्वारा भरे जाने वाले पानी की मात्रा = 60 - 10 = 50 इकाई<br>दोनों पाइप द्वारा 50 इकाई पानी भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 10 घंटे<br>अतः, लिया गया कुल समय = 5 घंटे + 10 घंटे = 15 घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A tank can be filled by pipe A in 4 hours and pipe B in 6 hours. At 8:00 a.m., pipe A was opened. At what time will the tank be filled if pipe B is opened at 9:00 a.m.?</p>",
                    question_hi: "<p>9. एक टंकी को पाइप A द्वारा 4 घंटे में और पाइप B द्वारा 6 घंटे में भरा जा सकता है। 8 : 00 a.m. पर पाइप A को खोला गया। यदि पाइप B को 9 : 00 a.m. पर खोला जाए तो टंकी किस समय भर जाएगी ?</p>",
                    options_en: ["<p>10 : 16 a.m.</p>", "<p>10 : 22 a.m.</p>", 
                                "<p>10 : 48 a.m.</p>", "<p>10 : 18 a.m.</p>"],
                    options_hi: ["<p>10 : 16 a.m.</p>", "<p>10 : 22 a.m.</p>",
                                "<p>10 : 48 a.m.</p>", "<p>10 : 18 a.m.</p>"],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926596476.png\" alt=\"rId18\" width=\"187\" height=\"119\"><br>According to the question,<br>Pipe A and B work done in 2 hours = (3 &times; 2) + 2 = 8 units<br>Remaining work = 12 - 8 = 4 units<br>Time taken by A and B(remaining work) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> hours = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 60 = 48 minutes<br>Hence, time to fill the tank = 8 : 00 a.m. + 2 hours + 48 minutes = 10 : 48 a.m.</p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926596611.png\" alt=\"rId19\" width=\"175\" height=\"125\"><br>प्रश्न के अनुसार,<br>पाइप A और B का 2 घंटे में किया गया कार्य = (3 &times; 2) + 2 = 8 इकाई<br>शेष कार्य = 12 - 8 = 4 इकाई<br>A और B द्वारा लिया गया समय (शेष कार्य) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> घंटे = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 60 = 48 मिनट<br>अत:, टंकी भरने का समय = 8 : 00 a.m. + 2 घंटे + 48 मिनट = 10 : 48 a.m.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Three pipes, P, Q and R, together take four hours to fill a tank. All the three pipes were opened at the same time. After three hours, P was closed, and Q and R filled the remaining tank in two hours. How many hours will P alone take to fill the tank ?</p>",
                    question_hi: "<p>10. तीन पाइप, P, Q और R मिलकर एक टैंक को भरने में चार घंटे का समय लेते हैं। तीनों पाइपों को एक साथ खोला जाता है। तीन घंटे के बाद, P को बंद कर दिया जाता है, और Q तथा R शेष टैंक को दो घंटों में भर देते हैं। P अकेले टैंक को भरने में कितने घंटे का समय लेगा ?</p>",
                    options_en: ["<p>8</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>9</p>"],
                    options_hi: ["<p>8</p>", "<p>10</p>",
                                "<p>12</p>", "<p>9</p>"],
                    solution_en: "<p>10.(a) Part filled in 3 hours = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>&nbsp;<br>Remaining part = 1 - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>(Q + R)&rsquo;s 1 hours work = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math> <br>P&rsquo;s 1 hours work = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math> <br>So, P alone can fill the tank = 8 hours </p>",
                    solution_hi: "<p>10.(a) 3 घंटे में भरा भाग = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>शेष भाग = 1 - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>(Q + R) का 1 घंटे का काम = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>P का 1 घंटे का काम = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>तो, P अकेले टंकी को भर सकता है = 8 hours</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Two pipes A and B can fill a tank in 20 and 30 hours, respectively. Both pipes are opened to fill the tank, but when the tank is one-third full, a leak develops through which one-fourth of the water supplied by both pipes goes out. Find the total time (in hours) taken to fill the tank.</p>",
                    question_hi: "<p>11. दो पाइप A और B एक टंकी को क्रमशः 20 और 30 घंटे में भर सकते हैं। टंकी को भरने के लिए दोनों पाइप खोले जाते हैं, लेकिन जब टंकी एक तिहाई भर जाती है, तो एक रिसाव विकसित होता है जिससे दोनों पाइपों द्वारा भरा गया एक-चौथाई पानी निकल जाता है। टंकी को भरने में लगने वाला कुल समय (घंटे में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>14</p>", 
                                "<p>11<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>14</p>",
                                "<p>11<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(a) <br>Two pipes can separately fill a tank in 20 hrs and 30 hrs respectively<br>Time taken by the two pipes to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>20</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi>&#160;</mi></mrow></mfrac></math> = 12 hours <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of the tank if filled = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>3</mn></mfrac></math> = 4 hours <br>Now , <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> of the supplied water leak out<br><math display=\"inline\"><mo>&#8658;</mo></math> the filler pipes earlier efficiency = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math> units <br>Work done complete = (12 - 4) &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac><mo>=</mo><mn>10</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></math> hours <br>So, Total time = 4 + 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>&nbsp;hours</p>",
                    solution_hi: "<p>11.(a) दो पाइप अलग-अलग एक टैंक को क्रमशः 20 घंटे और 30 घंटे में भर सकते हैं<br>दोनों पाइपों द्वारा टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>20</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi>&#160;</mi></mrow></mfrac></math> = 12 घंटे <br>यदि टंकी का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> भाग भरा हुआ है = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>3</mn></mfrac></math> = 4 घंटे <br>अब, आपूर्ति किए गए पानी में से <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> लीक हो गए हैं<br><math display=\"inline\"><mo>&#8658;</mo></math> भराव पाइप की पिछली दक्षता = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math> इकाई<br>पूरा कार्य = (12 - 4) &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac><mo>=</mo><mn>10</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></math>&nbsp;घंटे <br>अत: कुल समय = 4 + 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. One pipe can fill a tank 6 times faster than another pipe. If both the pipes together can fill the tank in 40 minutes, then in how many minutes will the slower pipe alone be able to fill the tank ?</p>",
                    question_hi: "<p>12. एक पाइप, दूसरे पाइप की तुलना में किसी टंकी को 6 गुना तेजी से भर सकता है। यदि दोनों पाइप एक साथ टंकी को 40 मिनट में भर सकते हैं, तो धीमा पाइप अकेले टंकी को कितने मिनट में भरेगा ?</p>",
                    options_en: ["<p>275</p>", "<p>285</p>", 
                                "<p>290</p>", "<p>280</p>"],
                    options_hi: ["<p>275</p>", "<p>285</p>",
                                "<p>290</p>", "<p>280</p>"],
                    solution_en: "<p>12.(d) <br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math> pipe<sub>1&nbsp;</sub> :&nbsp; pipe<sub>2</sub><br>Efficiency <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;6<br>Capacity of tank = 40 &times; 7 = 280 min <br>Time taken by slower pipe to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 280 min</p>",
                    solution_hi: "<p>12.(d) <br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> पाइप<sub>1</sub>&nbsp; :&nbsp; पाइप<sub>2</sub><br>क्षमता&nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;6&nbsp;<br>टैंक की क्षमता = 40 &times; 7 = 280 मिनट <br>धीमे पाइप द्वारा अकेले टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 280 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Pipe A takes <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of the time required by pipe B to fill an empty tank individually. When an outlet pipe C is also opened simultaneously with pipes A and B, it takes <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> more time to fill the empty tank than it takes when only pipe A and pipe B are opened together. If it takes 40 hours to fill the tank when all the three pipes are opened simultaneously, in what time (in hours) will pipe C empty the full tank, operating alone ?</p>",
                    question_hi: "<p>13. पाइप A एक खाली टैंक को अकेले भरने में, पाइप B द्वारा लिए गए आवश्यक समय का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> समय लेता है। जब एक निर्गम पाइप C को पाइप A और B के साथ एकसाथ खोला जाता है, तो खाली टैंक को भरने में केवल पाइप A और पाइप B को एक साथ खोलने में लगने वाले समय की तुलना में <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> अधिक समय लगता है। यदि तीनों पाइपों को एकसाथ खोलने पर टैंक को भरने में 40 घंटे लगते हैं, तो अकेले कार्य करते हुए पाइप C कितने समय में (घंटों में) पूरा टैंक खाली कर देगा ?</p>",
                    options_en: ["<p>50</p>", "<p>45</p>", 
                                "<p>65</p>", "<p>75</p>"],
                    options_hi: ["<p>50</p>", "<p>45</p>",
                                "<p>65</p>", "<p>75</p>"],
                    solution_en: "<p>13.(a) <br>Let pipe A fill an empty tank in 4hr , and pipe B fill an empty tank in 5hr<br>Let Total capacity of tank (LCM 4 ,5) = 20<br>Efficiency of Pipe A and Pipe B = 5 unit and 4 unit <br>Time taken by both pipe to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>hr<br>Again , Given C is an outlet pipe , and when A , B and C open together , it will take <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> more time to fill <br>the tank than it takes when only pipe A and pipe B are opened together<br>&there4; Time taken by Pipe A , B and C together to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 4hr&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926596870.png\" alt=\"rId20\" width=\"221\" height=\"180\"><br>Here efficiency of c = (A + B) - (A + B + C) = 9 - 5 = 4<br>Now, Total capacity of tank = 5 &times; 40 = 200 <br>Time required by pipe C to empty the full tank = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 50 hr</p>",
                    solution_hi: "<p>13.(a) <br>माना पाइप A एक खाली टैंक को 4 घंटे में भरता है, <br>और पाइप B एक खाली टैंक को 5 घंटे में भरता है।<br>माना टैंक की कुल क्षमता (LCM 4, 5) = 20<br>पाइप A और पाइप B की कार्यक्षमता = 5 इकाई और 4 इकाई <br>दोनों पाइप द्वारा टैंक को भरने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> घंटे।<br>दिया गया है कि C एक निकास पाइप है, और जब A, B और C को एक साथ खोला जाता है, तो टैंक को भरने में पाइप A और पाइप B को एक साथ खोलने की तुलना में <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> अधिक समय लगेगा।<br>&there4; पाइप A, B और C द्वारा टैंक को भरने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 4 घंटे।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926596977.png\" alt=\"rId21\" width=\"185\" height=\"172\"><br>अतः C की कार्यक्षमता = (A + B) - (A + B + C) = 9 - 5 = 4<br>अब, टैंक की कुल क्षमता = 5 &times; 40 = 200<br>पाइप C को पूर्ण टैंक को खाली करने में लगने वाला समय = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 50 घंटे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Pipe A can fill a cistern in 4 hours and Pipe B can fill the same cistern in 5 hours. Pipe C can empty a full cistern in 3 hours. If all three pipes are opened together, then the time (in minutes) taken to fill the tank is: (round to the nearest minute)</p>",
                    question_hi: "<p>14. पाइप A एक टंकी को 4 घंटे में भर सकता है और पाइप B उसी टंकी को 5 घंटे में भर सकता है। पाइप C पूरी टंकी को 3 घंटे में खाली कर सकता है। यदि सभी तीन पाइप एक साथ खोले जाते हैं, तो टंकी को भरने में कितना समय (मिनट में) लगेगा? (निकटतम मिनट तक पूर्णांकित)</p>",
                    options_en: ["<p>625</p>", "<p>445</p>", 
                                "<p>800</p>", "<p>514</p>"],
                    options_hi: ["<p>625</p>", "<p>445</p>",
                                "<p>800</p>", "<p>514</p>"],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926597116.png\" alt=\"rId22\" width=\"260\" height=\"122\"><br>Efficiency of all pipe together = 15 + 12 - 20 = 7 unit<br>Time taken to fill tank together = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo></math> 60 minutes = 514 .28 &asymp; 514 minute</p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926597232.png\" alt=\"rId23\" width=\"273\" height=\"141\"><br>सभी पाइपों की कुल क्षमता= 15 + 12 - 20 = 7 unit<br>टंकी को एक साथ भरने में लगा समय =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo></math> 60 मिनट = 514 .28 &asymp; 514 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. An inlet pipe can fill a water storage tank in 11 hours and an outlet pipe can empty the completely filled tank in 15 hours .If both pipes opened simultaneously. The time taken to fill the empty tank (in hrs) is :</p>",
                    question_hi: "<p>15. एक इनलेट पाइप किसी जल भंडारण टंकी को 11 घंटे में भर सकता है और एक आउटलेट पाइप पूरी तरह से भरी हुई टंकी को 15 घंटे में खाली कर सकता है। यदि दोनों पाइप को एक साथ खोल दिया जाए, तो खाली टंकी को भरने में लगने वाला समय (घंटे में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>45<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p>49<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>40</p>"],
                    options_hi: ["<p>45<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p>49<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>40</p>"],
                    solution_en: "<p>15.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926597342.png\" alt=\"rId24\" width=\"230\" height=\"139\"><br>Efficiency of both pipes in 1 hour = 15 - 11 = 4 unit<br>Time taken to fill the tank =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>165</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 41<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> hours</p>",
                    solution_hi: "<p>15.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731926597476.png\" alt=\"rId25\" width=\"225\" height=\"140\"><br>1 घंटे में दोनों पाइपों की क्षमता = 15 - 11 = 4 इकाई<br>टंकी को भरने में लगा समय =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>165</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 41<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> घंटे</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>