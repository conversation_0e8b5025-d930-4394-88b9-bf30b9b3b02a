<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The Meenakshi Temple is located in:</p>",
                    question_hi: "<p>1. मीनाक्षी मंदिर कहा स्थित है:</p>",
                    options_en: ["<p>Goa</p>", "<p>Kerala</p>", 
                                "<p>Odisha</p>", "<p>Tamil Nadu</p>"],
                    options_hi: ["<p>गोवा</p>", "<p>केरल</p>",
                                "<p>ओडिशा</p>", "<p>तमिलनाडु</p>"],
                    solution_en: "<p>1.(d) <strong>Tamil Nadu. Meenakshi Temple </strong>is a historic and iconic temple dedicated to Goddess Meenakshi (a form of Parvati) and her consort Lord Sundareswarar (a form of Shiva). <strong>Other Famous temples in India -</strong> Tirupati Balaji Temple (Andhra Pradesh); Konark Sun Temple (Odisha); The Sree Padmanabhaswamy Temple (Kerala); Shri Saptakoteshwar Temple (Goa).</p>",
                    solution_hi: "<p>1.(d) <strong>तमिलनाडु। मीनाक्षी मंदिर </strong>एक ऐतिहासिक और प्रतिष्ठित मंदिर है जो देवी मीनाक्षी (पार्वती का एक रूप) और उनके पति भगवान सुंदरेश्वर (शिव का एक रूप) को समर्पित है। <strong>भारत में अन्य प्रसिद्ध मंदिर -</strong> तिरूपति बालाजी मंदिर (आंध्र प्रदेश); कोणार्क सूर्य मंदिर (ओडिशा); श्री पद्मनाभस्वामी मंदिर (केरल); श्री सप्तकोटेश्वर मंदिर (गोवा)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. The Brihadisvara temple is in which state of India?</p>",
                    question_hi: "<p>2. बृहदीश्वर मंदिर भारत के किस राज्य में है?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Tamil Nadu</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>मध्य प्रदेश</p>",
                                "<p>तमिलनाडु</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>2.(c) <strong>Tamil Nadu. </strong>Brihadisvara temple (Thanjai Periya Kovil) is a Hindu temple dedicated to Shiva built in Dravidian architecture style. The temple was built by the Raja Raja Chola I (Chola Empire) in the 11th century. It is a UNESCO World Heritage Site (1987). More temples in TamilNadu: Kapaleeswarar Temple - Chennai, Meenakshi Temple - Madurai, Ramanathaswamy Temple - Rameswaram, Nataraja Temple - Chidambaram.</p>",
                    solution_hi: "<p>2.(c) <strong>तमिलनाडु</strong>। बृहदीश्वर मंदिर (थंजई पेरिया कोविल) द्रविड़ वास्तुकला शैली में निर्मित शिव को समर्पित एक हिंदू मंदिर है। मंदिर का निर्माण 11वीं शताब्दी में राजा राजा चोल प्रथम (चोल साम्राज्य) द्वारा किया गया था। यह यूनेस्को विश्व धरोहर स्थल (1987) है। तमिलनाडु में अन्य मंदिर: कपालेश्वर मंदिर - चेन्नई, मीनाक्षी मंदिर - मदुरै, रामनाथस्वामी मंदिर - रामेश्वरम, नटराज मंदिर - चिदंबरम।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which group of monuments consists of the Vitthala Temple?</p>",
                    question_hi: "<p>3. स्मारकों के किस समूह में विट्ठल मंदिर है?</p>",
                    options_en: ["<p>Khajuraho Group of Monuments</p>", "<p>Group of Monuments at Hampi</p>", 
                                "<p>Group of Monuments at Pattadakal</p>", "<p>Group of Monuments at Mahabalipuram</p>"],
                    options_hi: ["<p>खजुराहो स्मारकों का समूह</p>", "<p>हम्पी में स्मारकों का समूह</p>",
                                "<p>पट्टाडकली में स्मारकों का समूह</p>", "<p>महाबलीपुरम में स्मारकों का समूह</p>"],
                    solution_en: "<p>3.(b) <strong>Group of Monuments at Hampi </strong>. The monuments at Hampi were built by the founder of Vijay Nagar dynasty Harihara and Bukka. Vitthala Temple is also recognized as Shri Vijaya Vittala Temple. Hampi is situated on the banks of the Tungabhadra River in the eastern part of central Karnataka. Group of Monuments at Hampi - It was included as a UNESCO World Heritage Site -1986. Khajuraho Group of Monuments - Madhya Pradesh. Group of Monuments at Pattadakal - Karnataka. Group of Monuments at Mahabalipuram - Tamil Nadu.</p>",
                    solution_hi: "<p>3.(b) <strong>हम्पी में स्मारकों का समूह। </strong>हम्पी के स्मारकों का निर्माण विजय नगर राजवंश के संस्थापक हरिहर और बुक्का ने करवाया था। विट्ठल मंदिर को श्री विजया विट्ठल मंदिर के नाम से भी जाना जाता है। हम्पी मध्य कर्नाटक के पूर्वी भाग में तुंगभद्रा नदी के तट पर स्थित है। हम्पी में स्मारकों का समूह - इसे यूनेस्को विश्व धरोहर स्थल -1986 के रूप में शामिल किया गया था। खजुराहो स्मारक समूह - मध्य प्रदेश। पट्टडकल में स्मारकों का समूह - कर्नाटक । महाबलीपुरम में स्मारकों का समूह - तमिलनाडु।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following pairs is correct?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन सा जोड़ा सही है?</p>",
                    options_en: ["<p>Khajuraho Temple- Andhra Pradesh</p>", "<p>Venkateswara Temple- Odisha</p>", 
                                "<p>Tijara Mandir- Rajasthan</p>", "<p>Lingraj Temple- Madhya Pradesh</p>"],
                    options_hi: ["<p>खजुराहो मंदिर- आंध्र प्रदेश</p>", "<p>वेंकटेश्वर मंदिर- उड़ीसा</p>",
                                "<p>तिजारा मंदिर- राजस्थान</p>", "<p>लिंगराज मंदिर- मध्य प्रदेश</p>"],
                    solution_en: "<p>4.(c) <strong>Option C is correct. </strong>Tijara Mandir: Dedicated to Chandra Prabhu Bhagwan (Eighth tirthankara). <strong>Other Temples: </strong>Khajuraho - Madhya Pradesh. Venkateswara - Tirupati, Andhra Pradesh. Lingaraj - Bhubaneswar, Odisha. Meenakshi - Madurai, Tamil Nadu. Somnath - Gujarat. Padmanabhaswamy - Thiruvananthapuram, Kerala. Konark Sun temple - Odisha.</p>",
                    solution_hi: "<p>4.(c) <strong>विकल्प C सही है।</strong> तिजारा मंदिर: चंद्र प्रभु (आठवें तीर्थंकर) भगवान को समर्पित। <strong>अन्य मंदिर: </strong>खजुराहो - मध्य प्रदेश। वेंकटेश्वर-तिरुपति, आंध्र प्रदेश। लिंगराज-भुवनेश्वर, उड़ीसा । मीनाक्षी - मदुरै, तमिलनाडु। सोमनाथ - गुजरात। पद्मनाभस्वामी - तिरुवनंतपुरम, केरल। कोणार्क सूर्य मन्दिर - उड़ीसा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which temple is known as &lsquo;black pagoda&rsquo;?</p>",
                    question_hi: "<p>5. किस मंदिर को \'ब्लैक पगोडा\' के नाम से जाना जाता है?</p>",
                    options_en: ["<p>Konark Sun Temple</p>", "<p>Suryanar Temple</p>", 
                                "<p>Shani Shingnapur</p>", "<p>Martand Sun Temple</p>"],
                    options_hi: ["<p>कोणार्क सूर्य मंदिर</p>", "<p>सूर्यनार मंदिर</p>",
                                "<p>शनि शिंगणापुर</p>", "<p>मर्तण्ड सूर्य मंदिर</p>"],
                    solution_en: "<p>5.(a) <strong>Konark Sun Temple</strong> (Odisha): Built in the 13th century by King Narasimhadeva I (Eastern Ganga dynasty). Designed in the shape of a colossal chariot. Dedicated to Sun God. Architectural style - Kalinga architecture. Architect - Bishu Maharana. It was declared a UNESCO world heritage site in 1984.</p>",
                    solution_hi: "<p>5.(a) <strong>कोणार्क सूर्य मंदिर </strong>(उड़ीसा): 13वीं शताब्दी में राजा नरसिम्हदेव प्रथम (पूर्वी गंगा राजवंश) द्वारा निर्माण करवाया गया था । एक विशाल रथ के आकार में डिज़ाइन किया गया था यह सूर्य देव को समर्पित है । स्थापत्य शैली - कलिंग वास्तुकला। वास्तुकार - बिशु महराणा । यूनेस्को द्वारा इसे 1984 में विश्व धरोहर स्थल घोषित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which world heritage site was built by King Narasimhadeva I of the Eastern Ganga dynasty?</p>",
                    question_hi: "<p>6. पूर्वी गंग वंश के राजा नरसिंहदेव प्रथम द्वारा किस विश्व धरोहर स्थल का निर्माण करवाया गया था?</p>",
                    options_en: ["<p>Sarnath</p>", "<p>Lepakshi</p>", 
                                "<p>Hampi</p>", "<p>Sun Temple</p>"],
                    options_hi: ["<p>सारनाथ</p>", "<p>लेपाक्षी</p>",
                                "<p>हम्पी</p>", "<p>सूर्य मंदिर</p>"],
                    solution_en: "<p>6.(d) <strong>Sun Temple</strong> (13<sup>th</sup> Century, Odisha). <strong>Konark sun temple -</strong> It is also called Black Pagoda and declared as a UNESCO (United Nations Educational, Scientific and Cultural Organization) world heritage site in 1984. <strong>Important Monuments in Odisha: </strong>Jagannath Temple, Tara Tarini Temple, Udaygiri and Khandagiri Caves, Lingaraja Temple. <strong>Sarnath </strong>(Uttar Pradesh) - Dhamek Stupa (Spot of Buddha\'s first sermon).<strong> Lepakshi temple</strong> (Andhra Pradesh) - Characterised by hanging pillars and cave chambers. <strong>Hampi </strong>(Karnataka) - Capital city of Vijayanagara Empire.</p>",
                    solution_hi: "<p>6.(d) <strong>सूर्य मंदिर</strong> (13वीं शताब्दी, ओडिशा)। <strong>कोणार्क सूर्य मंदिर -</strong> इसे ब्लैक पैगोडा भी कहा जाता है और 1984 में इसे यूनेस्को (संयुक्त राष्ट्र शैक्षिक, वैज्ञानिक और सांस्कृतिक संगठन) द्वारा विश्व धरोहर स्थल घोषित किया गया था । <strong>ओडिशा में महत्वपूर्ण स्मारक: </strong>जगन्नाथ मंदिर, तारा तारिणी मंदिर, उदयगिरि और खंडगिरि गुफाएं, लिंगराज मंदिर। सारनाथ (उत्तर प्रदेश) - धमेक स्तूप (बुद्ध का प्रथम उपदेश स्थल)। <strong>लेपाक्षी मंदिर</strong> (आंध्र प्रदेश) - इसकी विशेषता लटकते खंभे और गुफा कक्ष हैं। <strong>हम्पी </strong>(कर्नाटक) - विजयनगर साम्राज्य की राजधानी है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The style of temple architecture that became popular in Northern India is known as:</p>",
                    question_hi: "<p>7. मंदिर वास्तुकला की शैली जो उत्तरी भारत में लोकप्रिय हुई, कहलाती है:</p>",
                    options_en: ["<p>Gupta</p>", "<p>Dravida</p>", 
                                "<p>Vesara</p>", "<p>Nagara</p>"],
                    options_hi: ["<p>गुप्ता</p>", "<p>द्रविड़</p>",
                                "<p>वेसर</p>", "<p>नागर</p>"],
                    solution_en: "<p>7.(d) <strong>Nagara</strong>. Nagara in the north and Dravida in the south. At times, the Vesara style of temples is also found as an independent style, created through the selective mixing of the Nagara and Dravida orders. In this style of the temple, there is only one peak or Shikhara. The major examples of the temples of Nagara Style are Sun Temple at Modhera, Sun Temple at Khajuraho, Jagannath Temple at Puri.</p>",
                    solution_hi: "<p>7.(d) <strong>नागर</strong>। उत्तर में नागर और दक्षिण में द्रविड़। कभी-कभी, मंदिरों की वेसर शैली एक स्वतंत्र शैली के रूप में भी पाई जाती है, जो नागर और द्रविड़ आदेशों के चयनात्मक मिश्रण के माध्यम से बनाई गई है। इस शैली के मंदिर में केवल एक शिखर होता है। नागर शैली के मंदिरों के प्रमुख उदाहरण मोढेरा का सूर्य मंदिर, खजुराहो का सूर्य मंदिर, पुरी का जगन्नाथ मंदिर हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Where is the dargah of sufi mystic Sheikh Fariduddin Ganjshaker located?</p>",
                    question_hi: "<p>8. सूफी फकीर शेख फरीदुद्दीन गंजशकर की दरगाह कहाँ स्थित है?</p>",
                    options_en: ["<p>Pakpattan (Pakistan)</p>", "<p>Lucknow</p>", 
                                "<p>Delhi</p>", "<p>Ajmer (Rajasthan)</p>"],
                    options_hi: ["<p>पाकपट्टन (पाकिस्तान)</p>", "<p>लखनऊ</p>",
                                "<p>दिल्ली</p>", "<p>अजमेर (राजस्थान)</p>"],
                    solution_en: "<p>8.(a) <strong>Pakpattan (Pakistan).</strong> Nizamuddin Dargah of the Sufi saint Khwaja Nizamuddin Auliya (1238&ndash;1325 CE). Situated in the Nizamuddin West area of Delhi. Dargah of Hazrat Naseeruddin Chirag-e-Dehlvi - Delhi. Dargah of Hazrat Amir Khusro - Delhi. Ajmer Sharif Dargah of Sufi Saint Khwaja Moinuddin Chishti - Ajmer ( Rajasthan).</p>",
                    solution_hi: "<p>8.(a) <strong>पाकपट्टन (पाकिस्तान)।</strong> सूफी संत ख्वाजा निज़ामुद्दीन औलिया (1238-1325 ई.) की निज़ामुद्दीन दरगाह दिल्ली के निज़ामुद्दीन पश्चिम क्षेत्र में स्थित है। हजरत नसीरुद्दीन चिराग-ए-देहलवी की दरगाह - दिल्ली। हजरत अमीर खुसरो की दरगाह - दिल्ली। सूफी संत ख्वाजा मोइनुद्दीन चिश्ती की अजमेर शरीफ दरगाह - अजमेर (राजस्थान)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. To which of the following deities is the Dilwara Temple in Mount Abu dedicated?</p>",
                    question_hi: "<p>9. माउंट आबू में दिलवाड़ा मंदिर निम्नलिखित में से किस देवता को समर्पित है?</p>",
                    options_en: ["<p>Jagannath</p>", "<p>Adinath</p>", 
                                "<p>Badrinath</p>", "<p>Kedarnath</p>"],
                    options_hi: ["<p>जगन्नाथ</p>", "<p>आदिनाथ</p>",
                                "<p>बद्रीनाथ</p>", "<p>केदारनाथ</p>"],
                    solution_en: "<p>9.(b) <strong>Adinath</strong>. The <strong>Dilwara Temples</strong> are a group of five Jain temples located in Mount Abu (Rajasthan). The oldest temple in the complex is the Vimal Vasahi Temple, which was built in 1021 AD and is dedicated to the first Jain Tirthankara, Adinath. The other four temples in the complex are the Luna Vasahi Temple, the Pittalhar Temple, the Parshvanath Temple, and the Mahavira Temple.</p>",
                    solution_hi: "<p>9.(b) <strong>आदिनाथ। दिलवाड़ा मंदिर</strong> माउंट आबू (राजस्थान) में स्थित पांच जैन मंदिरों का एक समूह है। परिसर में सबसे पुराना मंदिर विमल वसाही मंदिर है, जिसे 1021 ई. में बनाया गया था और यह प्रथम जैन तीर्थंकर आदिनाथ को समर्पित है। परिसर में अन्य चार मंदिर लूना वसाही मंदिर, पित्तलहर मंदिर, पार्श्वनाथ मंदिर और महावीर मंदिर हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which part of a Hindu temple incorporates space for a large number of worshippers?</p>",
                    question_hi: "<p>10. हिंदू मंदिर का कौन सा भाग बड़ी संख्या में उपासकों के लिए स्थान प्रदान करता है?</p>",
                    options_en: ["<p>Vimana</p>", "<p>Shikhar</p>", 
                                "<p>Garbhagriha</p>", "<p>Mandapa</p>"],
                    options_hi: ["<p>विमान</p>", "<p>शिखर</p>",
                                "<p>गर्भगृह</p>", "<p>मंडप</p>"],
                    solution_en: "<p>10.(d) <strong>Mandapa </strong>- It is a pillared hall or pavilion that serves as a gathering space for devotees during religious ceremonies and rituals. <strong>Vimana </strong>are mythological flying palaces or chariots described in Hindu texts and Sanskrit epics. <strong>Shikhara </strong>style is based on a tall pyramidal or a curvilinear structure. The innermost part of a Hindu temple where the idol of the deity presides is known as <strong>Garbhagriha</strong>.</p>",
                    solution_hi: "<p>10.(d) <strong>मंडप </strong>- यह एक स्तंभित सभागार या मंडप है जो धार्मिक समारोहों और अनुष्ठानों के समय भक्तों के लिए एक सभागार के रूप में कार्य करता है। <strong>विमान </strong>हिंदू ग्रंथों और संस्कृत महाकाव्यों में वर्णित पौराणिक उड़ने वाले महल या रथ हैं। <strong>शिखर </strong>शैली ऊंचे पिरामिडनुमा या वक्ररेखीय संरचना पर आधारित है। हिंदू मंदिर का सबसे भीतरी हिस्सा जहां देवता की मूर्ति होती है, <strong>गर्भगृह </strong>कहलाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following temples was built by the Pallava rulers?</p>",
                    question_hi: "<p>11. निम्नलिखित में से किस मंदिर का निर्माण पल्लव वंश के शासकों द्वारा कराया गया था?</p>",
                    options_en: ["<p>Konark Sun temple</p>", "<p>Lingaraja temple</p>", 
                                "<p>Dilwara temple</p>", "<p>Kanchipuram Kailashnath temple</p>"],
                    options_hi: ["<p>कोणार्क सूर्य मंदिर</p>", "<p>लिंगराज मंदिर</p>",
                                "<p>दिलवाड़ा मंदिर</p>", "<p>कांचीपुरम कैलाशनाथ मंदिर</p>"],
                    solution_en: "<p>11.(d) <strong>Kanchipuram Kailashnath temple - </strong>Built by the ruler Narasimhavarman II at the beginning of the 8th century. Dedicated to - Lord Shiva. Konark Sun Temple - Built in 1250 CE during the reign of the Eastern Ganga King Narasimhadeva-I. Lingaraja temple - Built by the Somavanshi king Yayati I in 11th century AD.</p>",
                    solution_hi: "<p>11.(d) <strong>कांचीपुरम कैलाशनाथ मंदिर -</strong> 8वीं शताब्दी के प्रारंभ में शासक नरसिंहवर्मन द्वितीय द्वारा निर्माण किया गया था। समर्पित - भगवान शिव। कोणार्क सूर्य मंदिर - पूर्वी गंगा राजा नरसिम्हादेव-। के शासनकाल के दौरान 1250 ई. में बनाया गया। लिंगराज मंदिर - 11वीं शताब्दी में सोमवंशी राजा ययाति प्रथम द्वारा बनाया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Where is the UNESCO world heritage site, Kandariya Mahadev temple located?</p>",
                    question_hi: "<p>12. यूनेस्को की विश्व धरोहर स्थल, कंदरिया महादेव मंदिर कहाँ स्थित है?</p>",
                    options_en: ["<p>Mandsaur</p>", "<p>Mahabalipuram</p>", 
                                "<p>Khajuraho</p>", "<p>Bhubaneswar</p>"],
                    options_hi: ["<p>मंदसौर</p>", "<p>महाबलीपुरम</p>",
                                "<p>खजुराहो</p>", "<p>भुवनेश्वर</p>"],
                    solution_en: "<p>12.(c) <strong>Khajuraho</strong>. Also known as the &lsquo;Great God of the Cave&rsquo; built by king Dhandadeva of Chandela dynasty. This temple, along with the other temples of Khajuraho (Madhya Pradesh), was designated as a UNESCO World Heritage Site in 1986. The Group of Monuments at Mahabalipuram (Tamil Nadu) - UNESCO World Heritage Site in 1984.</p>",
                    solution_hi: "<p>12.(c) <strong>खजुराहो।</strong> इसे चंदेल वंश के राजा धनदेव द्वारा निर्मित \'गुफा के महान देवता\' के रूप में भी जाना जाता है। इस मंदिर को, खजुराहो (मध्य प्रदेश) के अन्य मंदिरों के साथ, 1986 में यूनेस्को विश्व धरोहर स्थल के रूप में नामित किया गया था। महाबलीपुरम (तमिलनाडु) में स्मारकों का समूह - 1984 में यूनेस्को विश्व धरोहर स्थल में शामिल किया गया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. The Kedarnath temple is located in the ________ district of Uttarakhand.</p>",
                    question_hi: "<p>13. केदारनाथ मंदिर उत्तराखंड के ________ जिले में स्थित है।</p>",
                    options_en: ["<p>Uttarkashi</p>", "<p>Rudraprayag</p>", 
                                "<p>Haridwar</p>", "<p>Tehri Garhwal</p>"],
                    options_hi: ["<p>उत्तरकाशी</p>", "<p>रुद्रप्रयाग</p>",
                                "<p>हरिद्वार</p>", "<p>टिहरी गढ़वाल</p>"],
                    solution_en: "<p>13.(b) <strong>Rudraprayag </strong>- It is one of the Panch Prayag (five confluences) of Alaknanda River, the point of confluence of rivers Alaknanda and Mandakini. Kedarnath is one of the four Chota char Dham that also includes Badrinath, Gangotri, and Yamunotri. It is one of the twelve jyotirlingas of Shiva. The temple is located on the Garhwal Himalayan range near the <strong>Mandakini river.</strong></p>",
                    solution_hi: "<p>13.(b) <strong>रुद्रप्रयाग </strong>- यह अलकनंदा नदी के पंच प्रयागों (पांच संगमों) में से एक है, जो अलकनंदा और मंदाकिनी नदियों का संगम स्थल है। <strong>केदारनाथ </strong>चार छोटे धामों में से एक है जिसमें बद्रीनाथ, गंगोत्री और यमुनोत्री भी शामिल हैं। यह शिव के बारह ज्योतिर्लिंगों में से एक है। यह मंदिर <strong>मंदाकिनी नदी </strong>के पास गढ़वाल हिमालय श्रृंखला पर स्थित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In which of the following cities is the Mahakaleshwar temple situated?</p>",
                    question_hi: "<p>14. निम्नलिखित में से किस शहर में महाकालेश्वर मंदिर स्थित है?</p>",
                    options_en: ["<p>Varanasi</p>", "<p>Bhubaneswar</p>", 
                                "<p>Ujjain</p>", "<p>Bhopal</p>"],
                    options_hi: ["<p>वाराणसी</p>", "<p>भुवनेश्वर</p>",
                                "<p>उज्जैन</p>", "<p>भोपाल</p>"],
                    solution_en: "<p>14.(c) <strong>Ujjain</strong>. The present structure is situated on the side of Shipra river and was built by the Maratha general Ranoji Shinde in 1734 CE.<strong> Other Jyotirlingas:</strong> Somnath (Gujrat), Mallikarjun (Andhra Pradesh), Omkar/Amaleshwar (Madhya Pradesh), Kedarnath (Uttarakhand), Bhimashankar (Maharashtra), Vishveshwar (Uttar Pradesh), Trimbakeshwar (Maharashtra), Vaidyanath/Vaijnath (Maharashtra), Nagesh/Nagnath (Maharashtra), Rameshwaram (Tamil Nadu), Ghrushneshwar (Maharashtra).</p>",
                    solution_hi: "<p>14.(c) <strong>उज्जैन</strong>। वर्तमान संरचना क्षिप्रा नदी के किनारे स्थित है और इसका निर्माण 1734 ई. में मराठा शासक रानोजी शिंदे द्वारा करवाया गया था। <strong>अन्य ज्योतिर्लिंग</strong>: सोमनाथ (गुजरात), मल्लिकार्जुन (आंध्र प्रदेश), ओंकार/अमलेश्वर (मध्य प्रदेश), केदारनाथ (उत्तराखंड), भीमाशंकर (महाराष्ट्र), विश्वेश्वर (उत्तर प्रदेश), त्र्यंबकेश्वर (महाराष्ट्र), वैद्यनाथ/वैजनाथ (महाराष्ट्र), नागेश/नागनाथ (महाराष्ट्र), रामेश्वरम (तमिलनाडु), घृष्णेश्वर (महाराष्ट्र)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Where is the Mahabodhi temple situated?</p>",
                    question_hi: "<p>15. महाबोधि मंदिर कहां स्थित है?</p>",
                    options_en: ["<p>Varanasi</p>", "<p>Bodh Gaya</p>", 
                                "<p>Patna</p>", "<p>Agra</p>"],
                    options_hi: ["<p>वाराणसी</p>", "<p>बोधगया</p>",
                                "<p>पटना</p>", "<p>आगरा</p>"],
                    solution_en: "<p>15.(b) <strong>Bodh Gaya</strong> (Bihar). The Mahabodhi Temple (located on the banks of the Niranjana River) is one of the four holy sites related to the life of the Lord Buddha, and particularly to the attainment of Enlightenment. First temple was established in the 3rd century BC by Mauryan emperor Ashoka. <strong>Famous Temples in Bihar:</strong> Vishnupad Mandir and Mangala Gowri Temple (Gaya), Pawapuri Jal Mandir (Nalanda) and Mithila Shakti Peeth (Darbhanga).</p>",
                    solution_hi: "<p>15.(b)<strong> बोधगया (बिहार)।</strong> महाबोधि मंदिर (निरंजना नदी के तट पर स्थित) भगवान बुद्ध के जीवन और विशेष रूप से ज्ञान प्राप्ति से संबंधित चार पवित्र स्थलों में से एक है। प्रथम मंदिर की स्थापना तीसरी शताब्दी ईसा पूर्व में मौर्य सम्राट अशोक ने की थी।<strong> बिहार में प्रसिद्ध मंदिर:</strong> विष्णुपद मंदिर और मंगला गौरी मंदिर (गया), पावापुरी जल मंदिर (नालंदा) और मिथिला शक्ति पीठ (दरभंगा)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>