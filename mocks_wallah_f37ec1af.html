<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following rivers of India does not originate from the Western Ghat?</p>",
                    question_hi: "<p>1. भारत की निम्नलिखित में से कौन सी नदी पश्चिमी घाट से नहीं निकलती है?</p>",
                    options_en: ["<p>Kaveri</p>", "<p>Tapti</p>", 
                                "<p>Krishna</p>", "<p>Godavari</p>"],
                    options_hi: ["<p>कावेरी</p>", "<p>ताप्ती</p>",
                                "<p>कृष्णा</p>", "<p>गोदावरी</p>"],
                    solution_en: "<p>1.(b) <strong>Tapti river -</strong> It rises near Multai in the Betul of Madhya Pradesh and flows for about 724 km before outfalling into the Arabian Sea through the Gulf of Cambay. The major river systems originating in the <strong>Western Ghats -</strong> Godavari, Kaveri, Krishna, Thamirabarani and Tungabhadra rivers. <strong>Godavari River -</strong> It rises from Trimbakeshwar in the Nashik district of Maharashtra. <strong>Krishna River -</strong> It originates near Mahabaleshwar, Jor village (Sahyadri), Maharashtra.</p>",
                    solution_hi: "<p>1.(b)<strong> ताप्ती नदी - </strong>यह मध्य प्रदेश के बैतूल में मुलताई के पास से निकलती है और कैम्बे की खाड़ी से होते हुए अरब सागर में गिरने से पहले लगभग 724 किमी तक प्रवाहित होती है। <strong>पश्चिमी घाट </strong>से निकलने वाली प्रमुख नदी योजनाएँ - गोदावरी, कावेरी, कृष्णा, थामिराबरानी और तुंगभद्रा नदियाँ हैं। <strong>गोदावरी नदी -</strong> यह महाराष्ट्र के नासिक जिले में त्र्यंबकेश्वर से निकलती है। <strong>कृष्णा नदी -</strong> इसका उद्गम महाबलेश्वर के समीप जोर गांव (सह्याद्रि) महाराष्ट्र से होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Hemavati is the tributary of _____ river.</p>",
                    question_hi: "<p>2. हेमावती _____ नदी की सहायक नदी है।</p>",
                    options_en: ["<p>Kaveri</p>", "<p>Krishna</p>", 
                                "<p>Godavari</p>", "<p>Mahanadi</p>"],
                    options_hi: ["<p>कावेरी</p>", "<p>कृष्णा</p>",
                                "<p>गोदावरी</p>", "<p>महानदी</p>"],
                    solution_en: "<p>2.(a) <strong>Kaveri -</strong> It rises at Talakaveri on the Brahmagiri range in the Western Ghats in Karnataka and flows for about 800 km before its outfall into the Bay of Bengal. <strong>Tributaries -</strong> Arkavathy, Shimsha, Kapila, Shimsha, Honnuhole, Amaravati, Lakshmana Kabini. <strong>Other river Tributaries - Krishna</strong> (Ghataprabha, Malaprabha, Bhima, Tungabhadra and Musi); <strong>Godavari</strong> (Pravara, Purna, Manjra, Penganga, Wardha, Wainganga); <strong>Mahanadi </strong>(Seonath, Jonk, Hasdeo, Mand).</p>",
                    solution_hi: "<p>2.(a) <strong>कावेरी</strong> - यह कर्नाटक में पश्चिमी घाट में ब्रह्मगिरि पर्वतमाला पर तालाकावेरी से निकलती है और बंगाल की खाड़ी में गिरने से पहले लगभग 800 किमी तक प्रवाहित होती है। <strong>सहायक नदियाँ -</strong> अर्कावथी, शिमशा, कपिला, शिमशा, होन्नुहोल, अमरावती, लक्ष्मण काबिनी। <strong>अन्य सहायक नदियाँ - कृष्णा </strong>(घाटप्रभा, मालाप्रभा, भीमा, तुंगभद्रा और मुसी); <strong>गोदावरी </strong>(प्रवरा, पूर्णा, मंजरा, पेंगंगा, वर्धा, वैनगंगा); <strong>महानदी </strong>(शिवनाथ, जोंक, हसदेव, मांड)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which river is associated with the Dhuandhar falls?</p>",
                    question_hi: "<p>3. धुंआधार जलप्रपात का संबंध किस नदी से है ?</p>",
                    options_en: ["<p>Tapi</p>", "<p>Mahanadi</p>", 
                                "<p>Narmada</p>", "<p>Kaveri</p>"],
                    options_hi: ["<p>तापी</p>", "<p>महानदी</p>",
                                "<p>नर्मदा</p>", "<p>कावेरी</p>"],
                    solution_en: "<p>3.(c) <strong>Narmada -</strong> It passes through Madhya Pradesh, Maharashtra, and Gujarat. <strong>Dhuandhar Falls</strong> (Jabalpur district, Madhya Pradesh) - A popular tourist attraction. <strong>Tapti - </strong>It originates in the Satpura Range and empties into the Arabian Sea. <strong>Mahanadi -</strong> The third largest of peninsular India after Godavari and Krishna, and the largest river of Odisha state.<strong> Kaveri -</strong> A major river in southern India that flows through the states of Karnataka and Tamil Nadu.</p>",
                    solution_hi: "<p>3.(c) <strong>नर्मदा - </strong>यह मध्य प्रदेश, महाराष्ट्र और गुजरात से होकर गुजरती है। <strong>धुआंधार जलप्रपात</strong> (जबलपुर जिला, मध्य प्रदेश) - एक लोकप्रिय पर्यटक आकर्षण । <strong>ताप्ती -</strong> यह सतपुड़ा रेंज से निकलती है और अरब सागर में गिरती है। <strong>महानदी -</strong> गोदावरी और कृष्णा के बाद प्रायद्वीपीय भारत की तीसरी सबसे बड़ी नदी और ओडिशा राज्य की सबसे बड़ी नदी। <strong>कावेरी -</strong> दक्षिणी भारत की एक प्रमुख नदी जो कर्नाटक और तमिलनाडु राज्यों से होकर बहती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following is NOT a tributary of Krishna?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन कृष्णा की सहायक नदी नहीं है?</p>",
                    options_en: ["<p>Ghatprabha</p>", "<p>Manjira</p>", 
                                "<p>Tungabhadra</p>", "<p>Musi</p>"],
                    options_hi: ["<p>घाटप्रभा</p>", "<p>मंजीरा</p>",
                                "<p>तुंगभद्रा</p>", "<p>मूसी</p>"],
                    solution_en: "<p>4.(b) <strong>Manjira </strong>(Tributary of the river Godavari) - It passes through the states of Maharashtra, Karnataka, and Telangana. <strong>Krishna River</strong> originates near Mahabaleshwar, which is located near Jor village (Maharashtra).<strong> Tributaries of Krishna -</strong> the Ghataprabha, Malaprabha, Bhima, Tungabhadra, Musi.</p>",
                    solution_hi: "<p>4.(b) <strong>मंजीरा </strong>(गोदावरी नदी की सहायक नदी) - यह महाराष्ट्र, कर्नाटक और तेलंगाना राज्यों से होकर गुजरती है। <strong>कृष्णा नदी</strong> का उद्गम महाबलेश्वर के पास से होता है, तथा यह जोर गांव (महाराष्ट्र) के पास स्थित है। <strong>कृष्णा की सहायक नदियाँ -</strong> घाटप्रभा, मालप्रभा, भीमा, तुंगभद्रा, मूसी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. The river Tapi has its source at:</p>",
                    question_hi: "<p>5. तापी नदी का उद्गम स्थल है:</p>",
                    options_en: ["<p>Mansarovar lake</p>", "<p>Satpura</p>", 
                                "<p>Amarkantak</p>", "<p>Brahmagiri</p>"],
                    options_hi: ["<p>मानसरोवर झील</p>", "<p>सतपुड़ा</p>",
                                "<p>अमरकंटक</p>", "<p>ब्रह्मगिरि</p>"],
                    solution_en: "<p>5.(b) <strong>Satpura. Tapi river</strong> rises near Multai in the Betul district of Madhya Pradesh. It is the second largest west flowing river in India, after the Narmada River. <strong>Rivers in India and</strong> <strong>origin </strong>: Ganga river (Gangotri Glacier), Indus river (near Mansarovar lake), Narmada river (Amarkantak Plateau), Godavari river (Brahmagiri hills).</p>",
                    solution_hi: "<p>5.(b) <strong>सतपुड़ा। तापी नदी</strong> मध्य प्रदेश के बैतूल जिले में मुलताई के समीप से निकलती है। नर्मदा नदी के बाद भारत की पश्चिम की ओर बहने वाली यह दूसरी सबसे बड़ी नदी है। <strong>भारत में नदियाँ और उद्गम स्थल</strong>: गंगा नदी (गंगोत्री ग्लेशियर), सिंधु नदी (मानसरोवर झील के पास), नर्मदा नदी (अमरकंटक पठार), गोदावरी नदी (ब्रह्मगिरि पहाड़ियाँ)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. The largest coastal salt water lake of India lies in which of the following states?</p>",
                    question_hi: "<p>6. भारत की सबसे बड़ी तटीय खारे पानी की झील निम्नलिखित में से किस राज्य में स्थित है?</p>",
                    options_en: ["<p>Odisha</p>", "<p>West Bengal</p>", 
                                "<p>Andhra Pradesh</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>पश्चिम बंगाल</p>",
                                "<p>आंध्र प्रदेश</p>", "<p>केरल</p>"],
                    solution_en: "<p>6.(a) <strong>Odisha. Chilika Lake </strong>is a brackish water lagoon. In 1981, Chilika Lake was designated the first Indian wetland of international importance under the Ramsar Convention. <strong>Other Lakes in India </strong>- Wular Lake (Jammu and Kashmir), Vembanad Lake (Kerala), Pulicat Lake (Andhra Pradesh), Cholamu Lake (Sikkim).</p>",
                    solution_hi: "<p>6.(a) <strong>ओडिशा । चिल्का झील</strong> एक खारे पानी की झील है। 1981 में, चिल्का झील को रामसर सम्मलेन के तहत अंतर्राष्ट्रीय महत्व की पहली भारतीय आर्द्रभूमि नामित किया गया था। <strong>भारत में अन्य झीलें</strong> - वुलर झील (जम्मू और कश्मीर), वेम्बनाड झील (केरल), पुलिकट झील (आंध्र प्रदेश), चोलामू झील (सिक्किम)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following describes a drainage pattern resembling a dome like structure?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन एक गुंबद जैसी संरचना के समान जल निकासी पैटर्न को निरूपित करता है?</p>",
                    options_en: ["<p>Trellis</p>", "<p>Dendritic</p>", 
                                "<p>Rectangular</p>", "<p>Radial</p>"],
                    options_hi: ["<p>जालीनुमा (Trellis)</p>", "<p>वृक्षनुमा (Dendritic)</p>",
                                "<p>आयताकार (Rectangular)</p>", "<p>त्रिज्जीय (Radial)</p>"],
                    solution_en: "<p>7.(d) <strong>Radial. Trellis drainage pattern - </strong>When the primary tributaries of rivers flow parallel to each other and secondary tributaries join them at right angles. <strong>Dendritic drainage</strong> <strong>pattern - </strong>The drainage pattern resembling the branches of a tree. <strong>Rectangular drainage</strong> pattern - The tributary streams make sharp bends and enter the main stream at high angles.</p>",
                    solution_hi: "<p>7.(d) <strong>त्रिज्जीय (Radial) । जालीनुमा (Trellis) जल निकासी पैटर्न - </strong>जब नदियों की प्राथमिक सहायक नदियाँ एक दूसरे के समानांतर बहती हैं और द्वितीयक सहायक नदियाँ समकोण पर उनसे मिलती हैं। <strong>वृक्षनुमा जल निकासी पैटर्न -</strong> एक पेड़ की शाखाओं जैसा दिखने वाला जल निकासी पैटर्न। <strong>आयताकार जल</strong> <strong>निकासी पैटर्न -</strong> सहायक नदियाँ तीव्र मोड़ बनाती हैं और ऊँचे कोणों पर मुख्य धारा में प्रवेश करती हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. On which of the following rivers is the Idukki Hydroelectric Project built?</p>",
                    question_hi: "<p>8. इडुक्की (Idukki) जलविद्युत परियोजना इनमें से किस नदी पर बनाई गई है?</p>",
                    options_en: ["<p>Ganga</p>", "<p>Periyar</p>", 
                                "<p>Tapti</p>", "<p>Kosi</p>"],
                    options_hi: ["<p>गंगा</p>", "<p>पेरियार</p>",
                                "<p>ताप्ती</p>", "<p>कोसी</p>"],
                    solution_en: "<p>8.(b) <strong>Periyar</strong>. It flows through the Indian state of Kerala and is the longest river in the state. Idukki Arch Dam stands between the two mountains &ndash; Kuravanmala (839 m) and Kurathimala (925 m). Tehri Dam (Uttarakhand) was constructed in 2005 on the Bhagirathi River. Ukai Dam(Gujarat) - Tapti River. Sapta Koshi high dam project - Kosi river (Sorrow of Bihar).</p>",
                    solution_hi: "<p>8.(b) <strong>पेरियार</strong>। यह भारत के केरल राज्य से होकर प्रवाहित होने वाली राज्य की सबसे लंबी नदी है। इडुक्की आर्क बांध दो पर्वतों - कुरावनमाला (839 मीटर) और कुराथिमाला (925 मीटर) के बीच स्थित है। टेहरी बांध (उत्तराखंड) का निर्माण 2005 में भागीरथी नदी पर किया गया था। उकाई बांध (गुजरात) - ताप्ती नदी। सप्त कोशी उच्च बांध परियोजना - कोसी नदी (बिहार का शोक)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Hirakud dam is built on the river:</p>",
                    question_hi: "<p>9. हीराकुंड बांध किस नदी पर बना है?</p>",
                    options_en: ["<p>Mahanadi</p>", "<p>Brahmaputra</p>", 
                                "<p>Godavari</p>", "<p>Ganga</p>"],
                    options_hi: ["<p>महानदी</p>", "<p>ब्रह्मपुत्र</p>",
                                "<p>गोदावरी</p>", "<p>गंगा</p>"],
                    solution_en: "<p>9.(a) <strong>Mahanadi.</strong> Jawaharlal Nehru inaugurated the Hirakud dam in 1957, calling it the temple of modern India. It is the longest dam in the world. <strong>Brahmaputra </strong>- Doyang Hep Dam (Purpose - Hydroelectric, Drinking / Water Supply). Tehri Dam (Uttrakhand) was constructed in 2005 on the Bhagirathi River.</p>",
                    solution_hi: "<p>9.(a) <strong>महानदी।</strong> जवाहरलाल नेहरू ने 1957 में हीराकुंड बांध का उद्घाटन किया और इसे आधुनिक भारत का मंदिर कहा। यह दुनिया का सबसे लंबा बांध है। <strong>ब्रह्मपुत्र </strong>- डोयांग हेप बांध (उद्देश्य - जलविद्युत, पेय/जल आपूर्ति)।टिहरी बांध (उत्तराखंड) का निर्माण 2005 में भागीरथी नदी पर किया गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Name the river on which the Indira Sagar Dam is built.</p>",
                    question_hi: "<p>10. उस नदी का नाम बताइए जिस पर इंदिरा सागर बांध बना है।</p>",
                    options_en: ["<p>Kaveri</p>", "<p>Ganges</p>", 
                                "<p>Yamuna</p>", "<p>Narmada</p>"],
                    options_hi: ["<p>कावेरी</p>", "<p>गंगा</p>",
                                "<p>यमुना</p>", "<p>नर्मदा</p>"],
                    solution_en: "<p>10.(d) <strong>Narmada</strong>. The Indira Sagar Dam is located in the Khandwa district of Madhya Pradesh. It flows through the states of Madhya Pradesh, Gujarat, and Maharashtra. The Narmada, Sharavati, Periyar and Tapti are the only long rivers, which flow west and make estuaries. Other rivers and dams: Kaveri River - Mettur Dam, Ganga River - Tehri Dam.</p>",
                    solution_hi: "<p>10.(d) <strong>नर्मदा</strong>। इंदिरा सागर बांध मध्य प्रदेश के खंडवा जिले में स्थित है। यह मध्य प्रदेश, गुजरात और महाराष्ट्र राज्यों से होकर प्रवाहित होती है। नर्मदा, शरावती, पेरियार और ताप्ती एकमात्र लंबी नदियाँ हैं, जो पश्चिम की ओर प्रवाहित होती हैं और मुहाना (estuaries) का निर्माण करती हैं। अन्य नदियाँ और बाँध: कावेरी नदी - मेट्टूर बाँध, गंगा नदी - टिहरी बाँध।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Triveni Sangam of Uttar Pradesh is the place of confluence of which of the following rivers?</p>",
                    question_hi: "<p>11. उत्तर प्रदेश का त्रिवेणी संगम निम्नलिखित में से किस नदी का संगम स्थल है?</p>",
                    options_en: ["<p>Ganga, Yamuna, Narmada</p>", "<p>Ganga, Yamuna, Tapti</p>", 
                                "<p>Ganga, Yamuna, Saraswati</p>", "<p>Ganga, Yamuna, Sindhu</p>"],
                    options_hi: ["<p>गंगा, यमुना, नर्मदा</p>", "<p>गंगा, यमुना, ताप्ती</p>",
                                "<p>गंगा, यमुना, सरस्वती</p>", "<p>गंगा, यमुना, सिंधु</p>"],
                    solution_en: "<p>11.(c) <strong>Ganga, Yamuna, Saraswati. Triveni Sangam </strong>- Considered a sacred site in Hinduism and is located in Prayagraj (Allahabad). <strong>Ganga -</strong> Originates from the Gangotri Glacier in Uttarakhand. <strong>Emptying </strong>- The Bay of Bengal. It flows through Uttarakhand, Uttar Pradesh, Bihar, Jharkhand, West Bengal in India. <strong>Yamuna </strong>- Originates from the Yamunotri Glacier in Uttarakhand. <strong>Saraswati </strong>- Originated from the Har-ki-Dun glacier in the Garhwal region of Uttarakhand.</p>",
                    solution_hi: "<p>11.(c) <strong>गंगा, यमुना, सरस्वती। त्रिवेणी संगम </strong>- हिंदू धर्म में एक पवित्र स्थल माना जाता है और यह प्रयागराज (इलाहाबाद) में स्थित है। <strong>गंगा </strong>- इसका उद्गम उत्तराखंड में गंगोत्री ग्लेशियर से होता है। <strong>मुहाना </strong>- बंगाल की खाड़ी। यह भारत में उत्तराखंड, उत्तर प्रदेश, बिहार, झारखंड, पश्चिम बंगाल से होकर प्रवाहित होती है। <strong>यमुना </strong>- इसका उद्गम उत्तराखंड में यमुनोत्री ग्लेशियर से होता है। <strong>सरस्वती </strong>- इसका उद्गम उत्तराखंड के गढ़वाल क्षेत्र में हर-की-दून ग्लेशियर से हुई है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. &lsquo;Tsomgo Lake&rsquo; is located in which state of India?</p>",
                    question_hi: "<p>12. \'त्सोमगो झील\' भारत के किस राज्य में स्थित है?</p>",
                    options_en: ["<p>Sikkim</p>", "<p>Nagaland</p>", 
                                "<p>Arunachal Pradesh</p>", "<p>Goa</p>"],
                    options_hi: ["<p>सिक्किम</p>", "<p>नागालैंड</p>",
                                "<p>अरुणाचल प्रदेश</p>", "<p>गोवा</p>"],
                    solution_en: "<p>12.(a) <strong>Sikkim. Tsomgo Lake</strong> (Changu Lake) is a glacial lake. <strong>Other lakes of India</strong>: Dal Lake (Jammu and Kashmir), Lake Pichola (Rajasthan), Vembanad Lake (Kerala), Chilika Lake (Odisha), Loktak Lake (Manipur), Nainital Lake (Uttarakhand), Wular Lake (Jammu and Kashmir), Sambhar Lake (Rajasthan), Hussain Sagar Lake (Telangana), Chandra Taal (Himachal Pradesh), Pulicat Lake (Andhra Pradesh).</p>",
                    solution_hi: "<p>12.(a) <strong>सिक्किम। त्सोम्गो झील</strong> (चांगु झील) एक हिमनद झील है। <strong>भारत की अन्य झीलें</strong>: डल झील (जम्मू और कश्मीर), पिछोला झील (राजस्थान), वेम्बनाड झील (केरल), चिल्का झील (ओडिशा), लोकतक झील (मणिपुर), नैनीताल झील (उत्तराखंड), वुलर झील (जम्मू और कश्मीर), सांभर झील (राजस्थान), हुसैन सागर झील (तेलंगाना), चंद्र ताल (हिमाचल प्रदेश), पुलिकट झील (आंध्र प्रदेश)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Where does River Pindar join Alaknanda?</p>",
                    question_hi: "<p>13. पिंडर नदी इनमे से किस स्थान पर अलकनंदा में मिलती है?</p>",
                    options_en: ["<p>Vishnuprayag</p>", "<p>Karnaprayag</p>", 
                                "<p>Rudraprayag</p>", "<p>Devprayag</p>"],
                    options_hi: ["<p>विष्णुप्रयाग</p>", "<p>कर्णप्रयाग</p>",
                                "<p>रुद्रप्रयाग</p>", "<p>देवप्रयाग</p>"],
                    solution_en: "<p>13.(b) <strong>Karnaprayag </strong>(Chamoli District, Uttarakhand) is one of the Panch Prayag of Alaknanda River, situated at the confluence of the Alaknanda and Pindar River. The five prayags are: Vishnuprayag, Nandprayag, Karnaprayag, Rudraprayag, and Devprayag.</p>",
                    solution_hi: "<p>13.(b) <strong>कर्णप्रयाग </strong>(चमोली जिला, उत्तराखंड) अलकनंदा नदी के पंच प्रयागों में से एक है, जो अलकनंदा और पिंडर नदी के संगम पर स्थित है। पांच प्रयाग हैं: विष्णुप्रयाग, नंदप्रयाग, कर्णप्रयाग, रुद्रप्रयाग और देवप्रयाग।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The river Mahananda is a tributary of :</p>",
                    question_hi: "<p>14. महानंदा नदी किसकी सहायक नदी है :</p>",
                    options_en: ["<p>the Damodar river</p>", "<p>the Yamuna river</p>", 
                                "<p>the Satluj river</p>", "<p>the Ganga river</p>"],
                    options_hi: ["<p>दामोदर नदी</p>", "<p>यमुना नदी</p>",
                                "<p>सतलुज नदी</p>", "<p>गंगा नदी</p>"],
                    solution_en: "<p>14.(d) <strong>The Ganga river -</strong> It is the largest river in India with a total length approximately 2525 km. <strong>Ganga Right Bank Tributaries :-</strong> Yamuna River, Chambal River, Banas River, Sindh River, Betwa River, Ken River, Son River. <strong>Ganga Left Bank Tributaries :-</strong> Ramganga River, Ghaghra River, Kali River, Gandak River, Burhi Gandak, Kosi River. <strong>Mahananda -</strong> A trans-boundary river that flows through the Indian states of Bihar and West Bengal, and Bangladesh.</p>",
                    solution_hi: "<p>14.(d) <strong>गंगा नदी -</strong> यह भारत की सबसे बड़ी नदी है जिसकी कुल लंबाई लगभग 2525 किमी है। <strong>गंगा के</strong> <strong>दाहिने किनारे की सहायक नदियाँ :- </strong>यमुना नदी, चम्बल नदी, बनास नदी, सिंध नदी, बेतवा नदी, केन नदी, सोन नदी।<strong> गंगा के</strong> <strong>बाएँ तट की सहायक नदियाँ :-</strong> रामगंगा नदी, घाघरा नदी, काली नदी, गंडक नदी, बूढ़ी गंडक, कोसी नदी। <strong>महानंदा -</strong> एक सीमा-पार नदी है जो भारतीय राज्यों बिहार और पश्चिम बंगाल और बांग्लादेश से होकर बहती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Mechi, a trans-boundary river flowing through India and Nepal is a tributary of __________ river.</p>",
                    question_hi: "<p>15. भारत और नेपाल दोनों देशों से होकर बहने वाली नदी, मेची (Mechi) इनमें से किसकी सहायक नदी है ?</p>",
                    options_en: ["<p>Gandak</p>", "<p>Godavari</p>", 
                                "<p>Mahananda</p>", "<p>Ganga</p>"],
                    options_hi: ["<p>गंडक</p>", "<p>गोदावरी</p>",
                                "<p>महानंदा</p>", "<p>गंगा</p>"],
                    solution_en: "<p>15.(c) <strong>Mahananda</strong>. It rises from Darjeeling Hill (West Bengal). <strong>Mechi river</strong> originates from Mahabharat Range (Nepal). Other tributaries of Mahananda - Balason, Kankai and River Kalindi. Rivers originating from Nepal and flows through India - Koshi river, Gandaki river, Rapti river, Karnali river, Mahakali river, Bagmati river.</p>",
                    solution_hi: "<p>15.(c) <strong>महानंदा</strong>। यह दार्जिलिंग हिल (पश्चिम बंगाल) से निकलती है। <strong>मेची नदी</strong> का उद्गम महाभारत श्रेणी (नेपाल) से होता है। महानंदा की अन्य सहायक नदियाँ - बालासोन, कनकई और कालिंदी नदी। नेपाल से निकलकर भारत में प्रवाहित होने वाली नदियाँ - कोशी नदी, गंडकी नदी, राप्ती नदी, करनाली नदी, महाकाली नदी, बागमती नदी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>