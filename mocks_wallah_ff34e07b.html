<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. In India, all the bills introduced and passed by both the houses of Lok Sabha and Rajya Sabha can come into force only after they receive the assent of:</p>",
                    question_hi: "<p>1. भारत में, लोकसभा और राज्यसभा के दोनों सदनों द्वारा पेश और पारित किए गए सभी विधेयक _________ की सहमति मिलने के बाद ही लागू हो सकते है।</p>",
                    options_en: ["<p>Supreme Court</p>", "<p>Prime Minister</p>", 
                                "<p>President</p>", "<p>Parliament</p>"],
                    options_hi: ["<p>उच्चतम न्यायालय</p>", "<p>प्रधानमंत्री</p>",
                                "<p>राष्ट्रपति</p>", "<p>संसद</p>"],
                    solution_en: "<p>1.(c) <strong>President. A bill</strong> is the draft of a legislative proposal when passed by both houses of Parliament and assented to by the President then it becomes an act of Parliament. Types of bills in India : Ordinary bill (Article 107) , Money bill ( Article 110) , Financial bill (Article 117), Constitutional Amendment Bill (Article 368) , Ordinance Bill (Article 123).</p>",
                    solution_hi: "<p>1.(c) <strong>राष्ट्रपति। विधेयक</strong> एक विधायी प्रस्ताव का प्रारूप है, जब संसद के दोनों सदनों द्वारा पारित किया जाता है और राष्ट्रपति द्वारा सहमति दी जाती है तो यह संसद का एक अधिनियम बन जाता है। भारत में विधेयकों के प्रकार: साधारण विधेयक (अनुच्छेद 107), धन विधेयक (अनुच्छेद 110), वित्त विधेयक (अनुच्छेद 117), संवैधानिक संशोधन विधेयक (अनुच्छेद 368), अध्यादेश विधेयक (अनुच्छेद 123)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. Select the option that is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to the third number.<br>49 : 13 :: 56 : 11 :: 39 :?</p>",
                    question_hi: "<p>2. विकल्प का चयन कीजिए, जो पांचवीं संख्या से उसी प्रकार संबंधित है, जिस प्रकार दूसरी संख्या पहली संख्या से और चौथी संख्या तीसरी संख्या से संबंधित है।<br>49 : 13 :: 56 : 11 :: 39 :?</p>",
                    options_en: ["<p>15</p>", "<p>14</p>", 
                                "<p>12</p>", "<p>16</p>"],
                    options_hi: ["<p>15</p>", "<p>14</p>",
                                "<p>12</p>", "<p>16</p>"],
                    solution_en: "<p>2.(c) <br><strong>Logic: </strong>a : (ten&rsquo;s place digit of a + unit digit of a) we get,<br>49 : (4 + 9) = 13<br>56 : (5 + 6) = 11<br>Similarly, <strong>39 : (3 + 9) = 12</strong></p>",
                    solution_hi: "<p>2.(c) <br><strong>तर्क :</strong> a : (a के दहाई के स्थान का अंक + a का इकाई अंक) <br>हम प्राप्त करते हैं,<br>49 : (4 + 9) = 13<br>56 : (5 + 6) = 11<br>इसी प्रकार, <strong>39 : (3 + 9) = 12</strong></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3.The famous Haji Ali Dargah is located in which of the following cities?</p>",
                    question_hi: "<p>3. प्रसिद्ध हाजी अली दरगाह निम्नलिखित में से किस शहर में स्थित है?</p>",
                    options_en: ["<p>Delhi</p>", "<p>Ajmer</p>", 
                                "<p>Mumbai</p>", "<p>Hyderabad</p>"],
                    options_hi: ["<p>दिल्ली</p>", "<p>अजमेर</p>",
                                "<p>मुंबई</p>", "<p>हैदराबाद</p>"],
                    solution_en: "<p>3.(c) <strong>The Haji Ali Dargah</strong> is a Mosque and Dargah located in<strong> Mumbai. Delhi</strong> - Jama Masjid, Fatehpur Masjid, Lotus Temple, Moti Masjid, and Hijron ka Khanqah. <strong>Ajmer</strong> - Dargah Sharif, Brahma Temple, and Adhai Din ka Jhopra. <strong>Hyderabad</strong> - Jagannath Temple, Ashtalakshmi, Mecca Masjid.</p>",
                    solution_hi: "<p>3.(c) <strong>हाजी अली दरगाह</strong> मुंबई में स्थित एक मस्जिद और दरगाह है। <strong>दिल्ली</strong> - जामा मस्जिद, फतेहपुर मस्जिद, लोटस टेंपल, मोती मस्जिद और हिज्रों का खानकाह। <strong>अजमेर</strong> - दरगाह शरीफ, ब्रह्मा मंदिर और अढाई दिन का झोपड़ा। <strong>हैदराबाद</strong> - जगन्नाथ मंदिर, अष्टलक्ष्मी, मक्का मस्जिद।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. Find the equation of the tangents to the circle x<sup>2</sup> + y<sup>2</sup> = 9 at x = 2.</p>",
                    question_hi: "<p>4. वृत्त x<sup>2</sup> + y<sup>2</sup> = 9 के बिंदु x = 2 पर खींची गई स्पर्शरेखाओ के समीकरण ज्ञात कीजिये ?</p>",
                    options_en: ["<p>-2x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn><mi>y</mi></msqrt></math> = 9</p>", "<p>2x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9</p>", 
                                "<p>-2x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9</p>", "<p>2x -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9</p>"],
                    options_hi: ["<p>-2x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn><mi>y</mi></msqrt></math> = 9</p>", "<p>2x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9</p>",
                                "<p>-2x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9</p>", "<p>2x -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9</p>"],
                    solution_en: "<p>4.(b) <br>x<sup>2</sup> + y<sup>2</sup> = 9 at x = 2<br>y =<math display=\"inline\"><mo>&#177;</mo><msqrt><mn>5</mn></msqrt></math><br>So now putting value in all the equations, We find that equations<br>2x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9<br>2x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9</p>",
                    solution_hi: "<p>4.(b) <br>x<sup>2</sup> + y<sup>2</sup> = 9 पर x = 2<br>Y =<math display=\"inline\"><mo>&#177;</mo><msqrt><mn>5</mn></msqrt></math><br>तो अब सभी समीकरणों में मान रखने पर हम पाते हैं कि समीकरण<br>2x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9<br>2x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>y = 9</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5 . Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365094835.png\" alt=\"rId4\" width=\"278\" height=\"64\"></p>",
                    question_hi: "<p>5 . विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365094835.png\" alt=\"rId4\" width=\"278\" height=\"64\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365094990.png\" alt=\"rId5\" width=\"90\" height=\"95\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095122.png\" alt=\"rId6\" width=\"90\" height=\"96\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095221.png\" alt=\"rId7\" width=\"90\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095332.png\" alt=\"rId8\" width=\"90\" height=\"93\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365094990.png\" alt=\"rId5\" width=\"90\" height=\"95\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095122.png\" alt=\"rId6\" width=\"90\" height=\"96\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095221.png\" alt=\"rId7\" width=\"91\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095332.png\" alt=\"rId8\" width=\"90\" height=\"93\"></p>"],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365094990.png\" alt=\"rId5\" width=\"90\" height=\"95\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365094990.png\" alt=\"rId5\" width=\"90\" height=\"95\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. The Challenger Deep in the ____ is considered to be the deepest point known on Earth.</p>",
                    question_hi: "<p>6. ________ में चैलेंजर डीप को पृथ्वी पर ज्ञात सबसे गहरा बिंदु माना जाता है।</p>",
                    options_en: ["<p>Mariana Trench</p>", "<p>Puerto Rico Trench</p>", 
                                "<p>Lake Eyre</p>", "<p>Java Trench</p>"],
                    options_hi: ["<p>मेरियाना गर्त</p>", "<p>प्यूर्टो रिको गर्त</p>",
                                "<p>लेक आइरे</p>", "<p>जावा गर्त</p>"],
                    solution_en: "<p>6.(a) <strong>Mariana Trench</strong> is a crescent-shaped trench in the Western Pacific, just east of the Mariana Islands near Guam. <strong>Other trench : Puerto Rico Trench</strong> - Atlantic Ocean. <strong>Lake Eyre</strong> - Deserts of Australia. <strong>The Java</strong> <strong>Trench</strong> (Sunda Trench) - Eastern Indian Ocean.</p>",
                    solution_hi: "<p>6.(a) <strong>मेरियाना गर्त</strong> पश्चिमी प्रशांत क्षेत्र में गुआम के पास मारियाना द्वीप समूह के ठीक पूर्व में एक अर्धचंद्राकार गर्त है। <strong>अन्य गर्त: प्यूर्टो रिको गर्त</strong> - अटलांटिक महासागर। <strong>लेक आयर</strong>- ऑस्ट्रेलिया के रेगिस्तान। <strong>जावा गर्त</strong> (सुंडा गर्त) - पूर्वी हिंद महासागर।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. A mango kept in a basket doubles every one minute. If the basket gets completely ﬁlled by mangoes in 30 min then in how many minutes half of the basket was ﬁlled?</p>",
                    question_hi: "<p>7. टोकरी में रखे आम हर एक मिनट में दोगुने हो जाता है। यदि टोकरी 30 मिनट में आमों से पूरी तरह से भर जाती है, तो टोकरी का आधा भाग कितने मिनट में भर जाएगा?</p>",
                    options_en: ["<p>27</p>", "<p>15</p>", 
                                "<p>28</p>", "<p>29</p>"],
                    options_hi: ["<p>27</p>", "<p>15</p>",
                                "<p>28</p>", "<p>29</p>"],
                    solution_en: "<p>7.(d) In every minute mangoes in the basket get doubled, so in the last minute that is in the 30th minute, it is also doubled and filled completely, which means up to 29 minutes, the basket is half filled.</p>",
                    solution_hi: "<p>7.(d) टोकरी में रखे आम हर मिनट में दुगने हो जाते हैं, इसलिए आखिरी मिनट में, यानि 30वें मिनट में वह भी दुगना हो जाता है और पूरी तरह भर जाता है, यानी 29 मिनट तक टोकरी आधी भर जाती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095543.png\" alt=\"rId9\" width=\"118\" height=\"100\"></p>",
                    question_hi: "<p>8. नीचे दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095543.png\" alt=\"rId9\" width=\"118\" height=\"100\"></p>",
                    options_en: ["<p>12</p>", "<p>11</p>", 
                                "<p>14</p>", "<p>10</p>"],
                    options_hi: ["<p>12</p>", "<p>11</p>",
                                "<p>14</p>", "<p>10</p>"],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095690.png\" alt=\"rId10\" width=\"133\" height=\"119\"><br>There are a total of 12 (ABC, ABD, ACD, ADE, AEF, ADF, AGH, AIJ, AKL, ANO, LPM, MNQ) Triangles in the given figure.</p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095690.png\" alt=\"rId10\" width=\"133\" height=\"119\"><br>दिए गए चित्र में कुल 12 (ABC, ABD, ACD, ADE, AEF, ADF, AGH, AIJ, AKL, ANO, LPM, MNQ) त्रिभुज हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. Identify the number of electrons found in the outermost shell of halogen.</p>",
                    question_hi: "<p>9. हैलोजन के बाह्यतम कोश में पाए जाने वाले इलेक्ट्रॉनों की संख्या की पहचान कीजिए।</p>",
                    options_en: ["<p>Two</p>", "<p>One</p>", 
                                "<p>Five</p>", "<p>Seven</p>"],
                    options_hi: ["<p>दो</p>", "<p>एक</p>",
                                "<p>पाँच</p>", "<p>सात</p>"],
                    solution_en: "<p>9.(d) <strong>Seven.</strong> Group 17 of the Periodic Table consists of the following elements Fluorine (F), Chlorine (Cl), Bromine (Br), Iodine (I), Astatine (At). These elements are extremely reactive. The common oxidation state of these elements is -1. However, the highest oxidation state can be +7. They form oxides, hydrogen halides, interhalogen compounds, and oxoacids.</p>",
                    solution_hi: "<p>9.(d) <strong>सात। </strong>आवर्त सारणी के समूह 17 में निम्नलिखित तत्व शामिल हैं: फ्लोरीन (F), क्लोरीन (Cl), ब्रोमीन (Br), आयोडीन (I), एस्टेटिन (At)। ये तत्व अत्यधिक अभिक्रियाशील होते हैं। इन तत्वों की सामान्य ऑक्सीकरण अवस्था -1 होती है। हालाँकि, अधिकतम ऑक्सीकरण अवस्था +7 हो सकती है। ये ऑक्साइड, हाइड्रोजन हैलाइड, इंटरहेलोजन यौगिक और ऑक्सोएसिड बनाते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. Select the option that is related to the third word in the same way as the second word is related to the ﬁrst word.<br>Shirt : Apparel :: Necklace : ?</p>",
                    question_hi: "<p>10. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है।<br>कमीज : परिधान :: हार : ?</p>",
                    options_en: ["<p>Cham</p>", "<p>Neck</p>", 
                                "<p>Gold</p>", "<p>Jewellery</p>"],
                    options_hi: ["<p>चाम</p>", "<p>गर्दन</p>",
                                "<p>सोना</p>", "<p>आभूषण</p>"],
                    solution_en: "<p>10.(d) A shirt is an apparel, similarly, a necklace is a jewellery.</p>",
                    solution_hi: "<p>10.(d) कमीज एक परिधान है, वैसे ही हार एक आभूषण है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. Who has written Bahuroopi Gandhi ?</p>",
                    question_hi: "<p>11. बहुरूपी गांधी किसने लिखी है ?</p>",
                    options_en: ["<p>Anu Bandyopadhyay</p>", "<p>Chakravarti Rajagopalachari</p>", 
                                "<p>Jawaharlal Nehru</p>", "<p>Amrita Pritam</p>"],
                    options_hi: ["<p>अनु बंद्योपाध्याय</p>", "<p>चक्रवर्ती राजगोपालाचारी</p>",
                                "<p>जवाहर लाल नेहरू</p>", "<p>अमृता प्रीतम</p>"],
                    solution_en: "<p>11.(a) <strong>Anu Bandhyopadhyaya. </strong>The first edition of the book was published in April 1964. The book \"Bahuroopi Gandhi\" vividly narrates how Gandhiji functioned in a variety of ways apart from politics and the public scene. <strong>Other famous books : Jawaharlal Nehru</strong> - &ldquo;Glimpses Of World History&rdquo;, &ldquo;The Discovery Of India&rdquo;, &ldquo;Letters From A Father To His Daughter&rdquo;, &ldquo;An Autobiography: Toward Freedom&rdquo;, &ldquo;A Bunch Of Old Letters&rdquo; etc.<strong> Amrita Pritam </strong>- &ldquo;The Skeleton and That Man&rdquo;, &ldquo;Rasidi ticket&rdquo;, &ldquo;Kore Kagaz&rdquo; etc. <strong>Chakravarti Rajagopalachari -</strong> &ldquo;Ramayana&rdquo;, &ldquo;Mahabharata&rdquo;, &ldquo;Ramakrishna Upanishad&rdquo;, &ldquo;stories for the innocent&rdquo; etc.</p>",
                    solution_hi: "<p>11.(a) <strong>अनु बंद्योपाध्याय</strong>। इस पुस्तक का पहला संस्करण अप्रैल 1964 में प्रकाशित हुआ था। \"बहुरूपी गांधी\" पुस्तक स्पष्ट रूप से बताती है कि कैसे गांधीजी ने राजनीति और सार्वजनिक परिदृश्य से अलग कई तरीकों से कार्य किया। <strong>अन्य प्रसिद्ध पुस्तकें: जवाहरलाल नेहरू -</strong> \"ग्लिम्पसेस ऑफ वर्ल्ड हिस्ट्री\", \"द डिस्कवरी ऑफ इंडिया\", \"लेटर्स फ़ॉर्म ए फादर टू हिज डॉटर\", \"अन ऑटोबायोग्राफी: टूवार्ड फ़्रीडम\", \"ए बन्च ऑफ ओल्ड लेटर्स\" आदि। <strong>अमृता प्रीतम </strong>- &ldquo;द स्केलेटन एण्ड दैट मैन&rdquo;, &ldquo;रसीदी टिकट&rdquo;, &ldquo;कोरे कागज&rdquo; आदि। <strong>चक्रवर्ती राजगोपालाचारी </strong>- &ldquo;रामायण&rdquo;, &ldquo;महाभारत&rdquo; &ldquo;रामकृष्ण उपनिषद&rdquo;, &ldquo;स्टोरीज फॉर द इन्नोसेंट&rdquo; आदि।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. Two dice are thrown simultaneously and the sum of the numbers appearing on them is noted. What is the probability that the sum is 12?</p>",
                    question_hi: "<p>12. दो पासे एक साथ फेंके जाते हैं और उन पर आने वाली संख्याओं के योग को लिख लिया जाता है। योग 12 आने की प्रायिकता क्या है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>", "<p>36</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>", "<p>3</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>", "<p>36</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>", "<p>3</p>"],
                    solution_en: "<p>12.(c) When two dice are thrown simultaneously, the total number of events will be <br>= 6 &times; 6 = 36<br>the sum of the numbers appearing on them will come to be 12 in only one possible way<br>i.e. ( 6 + 6 = 12)<br>So, the probability that the sum is 12 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>12.(c) जब दो पासे एक साथ फेंके जाते हैं, घटनाओं की कुल संख्या होगी = 6 &times; 6 = 36<br>उन पर आने वाली संख्याओं का योग केवल एक संभव तरीके से 12 होगा, यानी (6 + 6 = 12)<br>अत: योग के 12 होने की प्रायिकता = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095832.png\" alt=\"rId11\" width=\"235\" height=\"70\"></p>",
                    question_hi: "<p>13. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095832.png\" alt=\"rId11\" width=\"235\" height=\"70\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095964.png\" alt=\"rId12\" width=\"80\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096057.png\" alt=\"rId13\" width=\"80\" height=\"79\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096150.png\" alt=\"rId14\" width=\"80\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096240.png\" alt=\"rId15\" width=\"80\" height=\"79\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365095964.png\" alt=\"rId12\" width=\"80\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096057.png\" alt=\"rId13\" width=\"81\" height=\"80\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096150.png\" alt=\"rId14\" width=\"80\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096240.png\" alt=\"rId15\" width=\"82\" height=\"81\"></p>"],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096240.png\" alt=\"rId15\" width=\"80\" height=\"79\"></p>",
                    solution_hi: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096240.png\" alt=\"rId15\" width=\"81\" height=\"80\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. Which steel plant was established with Russian collaboration in Durg district of Chhattisgarh and started production in 1959 ?</p>",
                    question_hi: "<p>14. छत्तीसगढ़ के दुर्ग जिले में रूस के सहयोग से किस इस्पात संयंत्र की स्थापना की गई और 1959 में उत्पादन शुरू किया गया ?</p>",
                    options_en: ["<p>Rourkela Steel Plant</p>", "<p>Durgapur Steel Plant</p>", 
                                "<p>Bokaro Steel Plant</p>", "<p>Bhilai Steel Plant</p>"],
                    options_hi: ["<p>राउरकेला इस्पात संयंत्र</p>", "<p>दुर्गापुर इस्पात संयंत्र</p>",
                                "<p>बोकारो इस्पात संयंत्र</p>", "<p>भिलाई इस्पात संयंत्र</p>"],
                    solution_en: "<p>14.(d) <strong>Bhilai Steel Plant</strong> was set up in 1955 with the help of the USSR (Russia) in Chhattisgarh and started production in 1959. The Rourkela Steel plant was established with help from Germany in Orissa 1959. Durgapur Steel Plant in West Bengal in collaboration with the United Kingdom in 1959. Bokaro Steel Plant in Jharkhand with Russian collaboration in 1964. During the Second five year plan (1956‑1961), Steel mills at Bhilai, Durgapur, and Rourkela were established.</p>",
                    solution_hi: "<p>14.(d) <strong>भिलाई इस्पात संयंत्र</strong> । इसकी स्थापना 1955 में USSR (रूस) की मदद से छत्तीसगढ़ में की गई थी और 1959 में उत्पादन शुरू किया गया था। राउरकेला स्टील प्लांट 1959 में उड़ीसा में जर्मनी की मदद से स्थापित किया गया था। 1959 में यूनाइटेड किंगडम के सहयोग से पश्चिम बंगाल में दुर्गापुर स्टील प्लांट स्थापित किया गया । 1964 में रूस के सहयोग से झारखंड में बोकारो स्टील प्लांट स्थापित किया गया। दूसरी पंचवर्षीय योजना (1956-1961) के दौरान, भिलाई, दुर्गापुर और राउरकेला में स्टील मिलें स्थापित की गईं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. For any natural number n, 6<sup>n</sup> - 5<sup>n</sup>&nbsp;always ends with :-</p>",
                    question_hi: "<p>15. किसी भी प्राकृतिक संख्या n के लिए, 6<sup>n</sup> - 5<sup>n</sup>&nbsp;का अंतिम अंक सदैव_________ होगा ।</p>",
                    options_en: ["<p>5</p>", "<p>3</p>", 
                                "<p>7</p>", "<p>1</p>"],
                    options_hi: ["<p>5</p>", "<p>3</p>",
                                "<p>7</p>", "<p>1</p>"],
                    solution_en: "<p>15.(d) As we know, <br>for any natural number n, 6<sup>n</sup>&nbsp;always ends with 6 and 5n ends with 5.<br>So, 6<sup>n</sup> - 5<sup>n</sup>&nbsp;will end with (6 - 5) = 1.</p>",
                    solution_hi: "<p>15.(d) जैसा कि हम जानते हैं,<br>किसी भी प्राकृतिक संख्या n के लिए, 6<sup>n</sup>&nbsp;हमेशा 6 पर समाप्त होता है और5n, 5 पर समाप्त होता है।<br>तो 6<sup>n</sup> - 5<sup>n </sup>= (6 - 5) = 1 के साथ समाप्त होगा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. &lsquo;SAMPRITI-XI&rsquo; is the joint military exercise conducted by India and which country?</p>",
                    question_hi: "<p>16. \'SAMPRITI-XI\' भारत और किस देश के बीच आयोजित संयुक्त सैन्य अभ्यास है?</p>",
                    options_en: ["<p>Bangladesh</p>", "<p>Sri Lanka</p>", 
                                "<p>Indonesia</p>", "<p>Iran</p>"],
                    options_hi: ["<p>बांग्लादेश</p>", "<p>श्रीलंका</p>",
                                "<p>इंडोनेशिया</p>", "<p>ईरान</p>"],
                    solution_en: "<p>16.(a) India and Bangladesh have commenced the 11th edition of their annual joint military exercise, SAMPRITI, in Umroi, Meghalaya.&nbsp;This exercise, which alternates between the two nations, highlights the strong bilateral defense cooperation between them. SAMPRITI, initiated in 2009, aims to boost interoperability, share tactical expertise, and promote best practices between the Indian and Bangladeshi armies.</p>",
                    solution_hi: "<p>16.(a) भारत और बांग्लादेश ने उमरोई, मेघालय में अपने वार्षिक संयुक्त सैन्य अभ्यास \'SAMPRITI\' के 11वें संस्करण की शुरुआत की है। यह अभ्यास दोनों देशों के बीच वैकल्पिक रूप से आयोजित किया जाता है और उनके मजबूत द्विपक्षीय रक्षा सहयोग को उजागर करता है। 2009 में शुरू किया गया SAMPRITI अभ्यास भारतीय और बांग्लादेशी सेनाओं के बीच सहयोग, सामरिक विशेषज्ञता साझा करने और सर्वोत्तम अभ्यासों को बढ़ावा देने का लक्ष्य रखता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. Introducing a person to her husband, a lady said, &ldquo;His brother\'s father is the only son of my paternal grandfather.&rdquo; How is the lady related to that person?</p>",
                    question_hi: "<p>17. एक व्यक्ति का अपने पति से परिचय कराते हुए एक महिला ने कहा, \"उसके भाई के पिता मेरे दादा के इकलौते पुत्र हैं।\" वह महिला उस व्यक्ति से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Mother</p>", "<p>Sister</p>", 
                                "<p>Aunt</p>", "<p>Daughter</p>"],
                    options_hi: ["<p>माता</p>", "<p>बहन</p>",
                                "<p>चाची</p>", "<p>पुत्री</p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096548.png\" alt=\"rId16\" width=\"162\" height=\"101\"><br>It is clear from the figure that the lady is the sister of that person and they have another brother.</p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096674.png\" alt=\"rId17\" width=\"169\" height=\"105\"><br>आकृति से स्पष्ट है कि महिला उस व्यक्ति की बहन है और उनका एक और भाई है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. In human beings which of the following has a high affinity for oxygen ?</p>",
                    question_hi: "<p>18. मनुष्यों में निम्न में से किसमें ऑक्सीजन के लिए उच्च बंधुता है ?</p>",
                    options_en: ["<p>Hemoglobin</p>", "<p>Carbon dioxide</p>", 
                                "<p>White blood cells</p>", "<p>Melanin</p>"],
                    options_hi: ["<p>हिमोग्लोबिन</p>", "<p>कार्बन डाइऑक्साइड</p>",
                                "<p>श्वेत रक्त कोशिकाएं</p>", "<p>मेलेनिन</p>"],
                    solution_en: "<p>18.(a) <strong>Hemoglobin.</strong> It is an iron-rich protein found in red blood cells. It has less affinity for Carbon dioxide as compared to oxygen because Carbon dioxide is a larger molecule than oxygen. <strong>Melanin</strong> is a natural skin pigment. <strong>White blood cells,</strong> also known as leukocytes, are responsible for protecting our body from infection. <strong>Carbon dioxide</strong> (CO<sub>2</sub>) is an important heat-trapping gas, or greenhouse gas.</p>",
                    solution_hi: "<p>18.(a) <strong>हीमोग्लोबिन</strong>। यह लाल रक्त कोशिकाओं में पाया जाने वाला लौह युक्त प्रोटीन है। इसमें ऑक्सीजन की तुलना में कार्बन डाइऑक्साइड के प्रति कम आकर्षण होता है क्योंकि कार्बन डाइऑक्साइड ऑक्सीजन की तुलना में एक बड़ा अणु है। <strong>मेलेनिन</strong> एक प्राकृतिक त्वचा रंगद्रव्य (pigment) है। <strong>श्वेत रक्त कोशिकाएं</strong>, जिन्हें ल्यूकोसाइट्स भी कहा जाता है, जो हमारे शरीर को संक्रमण से बचाने के लिए जिम्मेदार होती हैं। <strong>कार्बन डाइऑक्साइड</strong> (CO<sub>2</sub>) एक महत्वपूर्ण ऊष्मा-रोकने वाली गैस या ग्रीनहाउस गैस है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. Select the correct mirror image of the given figure when the mirror is placed at MN as shown. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096812.png\" alt=\"rId18\" width=\"117\" height=\"95\"></p>",
                    question_hi: "<p>19. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो निचे दर्शाए गए अनुसार MN पर रखने पर बनेगा। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096812.png\" alt=\"rId18\" width=\"117\" height=\"95\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096929.png\" alt=\"rId19\" width=\"90\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097051.png\" alt=\"rId20\" width=\"90\" height=\"93\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097146.png\" alt=\"rId21\" width=\"90\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097245.png\" alt=\"rId22\" width=\"90\" height=\"88\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365096929.png\" alt=\"rId19\" width=\"90\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097051.png\" alt=\"rId20\" width=\"90\" height=\"93\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097146.png\" alt=\"rId21\" width=\"90\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097245.png\" alt=\"rId22\" width=\"90\" height=\"88\"></p>"],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097146.png\" alt=\"rId21\" width=\"90\" height=\"89\"></p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097146.png\" alt=\"rId21\" width=\"90\" height=\"89\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. What is the 50th term of Arithmetic Progression 3,8,13,18,23,....?</p>",
                    question_hi: "<p>20. समांतर श्रेणी का 50वाँ पद 3,8,13,18,23,.... क्या है?</p>",
                    options_en: ["<p>248</p>", "<p>267</p>", 
                                "<p>150</p>", "<p>345</p>"],
                    options_hi: ["<p>248</p>", "<p>267</p>",
                                "<p>150</p>", "<p>345</p>"],
                    solution_en: "<p>20.(a)<br>This sequence has a difference of 5 between each pair of numbers.<br>The values of a and d are:<br>a = 3 (the first term)<br>d = 5 (the &ldquo;common difference&rdquo;)<br>The rule can be calculated:<br>xn = a + d(n &minus; 1)<br>= 3 + 5(n &minus; 1)<br>= 3 + 5n &minus; 5<br>= 5n &minus; 2<br>So, the 50nd term is:<br>X<sub>50</sub> = 5 &times; 50 &minus; 2<br>X<sub>50</sub> = 248</p>",
                    solution_hi: "<p>20.(a)<br>इस क्रम में प्रत्येक जोड़ी संख्या के बीच 5 का अंतर होता है।<br>a और d के मान हैं:<br>a = 3 (पहला पद)<br>d = 5 (\"सामान्य अंतर\")<br>नियम की गणना की जा सकती है:<br>xn = a + d(n &minus; 1)<br>= 3 + 5(n &minus; 1)<br>= 3 + 5n &minus; 5<br>= 5n &minus; 2<br>तो, 50 वां पद है:<br>X<sub>50</sub> = 5 &times; 50 &minus; 2<br>X<sub>50</sub> = 248</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. Which of the following plants has &lsquo;hidden reproductive organs&rsquo; ?</p>",
                    question_hi: "<p>21. निम्नलिखित में से किस पौधे में \'छिपे हुए प्रजनन अंग\' होते हैं ?</p>",
                    options_en: ["<p>Marsilea</p>", "<p>Pinus</p>", 
                                "<p>Deodar</p>", "<p>Ipomoea</p>"],
                    options_hi: ["<p>मार्सिलिया</p>", "<p>पाइनस</p>",
                                "<p>देवदार</p>", "<p>इपोमिया</p>"],
                    solution_en: "<p>21.(a) <strong>Marsilea. Pinus </strong>is the largest genus of the Pinaceae, the pine family which first appeared in the Jurassic period. The <strong>Deodar</strong> tree (Himalayan Cedar) is a coniferous evergreen tree native to the western Himalayas in eastern Afghanistan, northern Pakistan, India and Nepal. Its scientific name is <strong>Cedrus deodara</strong> and it belongs to the family Pinaceae. <strong>Ipomoea</strong> is the largest genus in the plant family Convolvulaceae with over 600 species.</p>",
                    solution_hi: "<p>21.(a) <strong>मार्सिलिया। पाइनस</strong>, पिनेसी का सबसे बड़ा वंश है जो पहली बार जुरासिक काल में दिखाई दिया था&nbsp;<strong>देवदार</strong> का पेड़ (हिमालयी देवदार) एक शंकुधारी सदाबहार पेड़ है जो पूर्वी अफगानिस्तान, उत्तरी पाकिस्तान, भारत और नेपाल में पश्चिमी हिमालय का मूल प्रजाति है। इसका वैज्ञानिक नाम <strong>सेड्रस देवदार</strong> है और यह पिनेसी कुल (family) से संबंधित है। <strong>इपोमिया</strong> 600 से अधिक प्रजातियों के साथ कन्वोल्वुलेसी पौधे कुल (family) का सबसे बड़ा वंश है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. Find the greatest number that divides 130, 305 and 245 leaving remainders 2,1 and 1 respectively?</p>",
                    question_hi: "<p>22. वह सबसे बड़ी संख्या ज्ञात कीजिए जो 130, 305 और 245 को विभाजित करने पर क्रमशः 2,1 और 1 शेषफल प्रदान करती है?</p>",
                    options_en: ["<p>4</p>", "<p>7</p>", 
                                "<p>5</p>", "<p>6</p>"],
                    options_hi: ["<p>4</p>", "<p>7</p>",
                                "<p>5</p>", "<p>6</p>"],
                    solution_en: "<p>22.(a)<br>HCF of (130 - 2) , (305 - 1) , and (245 - 1)&nbsp;<br>HCF of (128, 304, 244 )= 4<br>Greatest number = 4</p>",
                    solution_hi: "<p>22.(a)<br>HCF of (130 - 2) , (305 - 1) ,और (245 - 1)&nbsp;<br>HCF of (128, 304, 244 ) = 4<br>सबसे बड़ी संख्या = 4</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. Select the option that is related to the third term in the same way as the second term is related to the ﬁrst term.<br>DFB : GHC :: LNJ : ?</p>",
                    question_hi: "<p>23. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>DFB : GHC :: LNJ : ?</p>",
                    options_en: ["<p>LOJ</p>", "<p>OPK</p>", 
                                "<p>EGC</p>", "<p>OQM</p>"],
                    options_hi: ["<p>LOJ</p>", "<p>OPK</p>",
                                "<p>EGC</p>", "<p>OQM</p>"],
                    solution_en: "<p>23.(b) DFB : GHC :: LNJ : ?<br>D + 3 = G, F + 2 = H, B + 1 = C<br>L + 3 = O, N + 2 = P, J + 1 = K<br>Option B is right, it follows the same pattern.</p>",
                    solution_hi: "<p>23.(b) DFB : GHC :: LNJ : ?<br>D + 3 = G, F + 2 = H, B + 1 = C<br>L + 3 = O, N + 2 = P, J + 1 = K<br>विकल्प B सही है, यह उसी पैटर्न का अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. When did the Moplah revolt (Malabar Rebellion) in Kerala take place ?</p>",
                    question_hi: "<p>24. केरल में मोपला विद्रोह (मालाबार विद्रोह) कब हुआ था ?</p>",
                    options_en: ["<p>1928</p>", "<p>1945</p>", 
                                "<p>1934</p>", "<p>1921</p>"],
                    options_hi: ["<p>1928</p>", "<p>1945</p>",
                                "<p>1934</p>", "<p>1921</p>"],
                    solution_en: "<p>24.(d) <strong>1921. Moplah revolt</strong> was the culmination of a series of riots by Mappila Muslims of Kerala in the 19th and early 20th centuries against the British and the Hindu landlords in Malabar (Northern Kerala). It was led by <strong>Variyamkunnath Kunjahammed Haji.</strong></p>",
                    solution_hi: "<p>24.(d) <strong>1921 । मोपला विद्रोह</strong> 19वीं और 20वीं सदी की शुरुआत में मालाबार (उत्तरी केरल) में ब्रिटिश और हिंदू जमींदारों के खिलाफ केरल के मप्पिला मुसलमानों द्वारा दंगों की एक श्रृंखला की पराकाष्ठा थी। इसका नेतृत्व <strong>वरियामकुनाथ कुंजाहमद हाजी</strong> ने किया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. Select the set in which the numbers are related in the same way as are the numbers of the following sets. (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding/deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed) <br>(14,98) <br>(22, 242)</p>",
                    question_hi: "<p>25.उस समुच्चय का चयन कीजिए, जिसकी संख्याओं के बीच वही संबंध है, जो नीचे दिए गए समुच्चयों की संख्याओं के&nbsp;बीच है।&nbsp;(नोट: संक्रियाएं संख्याओं को उनके संघटक अंकों में विभक्त किए बिना, संपूर्ण संख्याओं पर की जानी चाहिए। उदाहरणार्थ - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना / घटाना / गुणा करना इत्यादि 13 पर ही की जानी - चाहिए। 13 को 1 और 3 में विभक्त करने, और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है ।)<br>(14,98) <br>(22, 242)</p>",
                    options_en: ["<p>12, 132</p>", "<p>8, 48</p>", 
                                "<p>16, 80</p>", "<p>6, 18</p>"],
                    options_hi: ["<p>12, 132</p>", "<p>8 , 48</p>",
                                "<p>16, 80</p>", "<p>6, 18</p>"],
                    solution_en: "<p>25.(d) <strong>Logic </strong>:- first number &times; <math display=\"inline\"><mfrac><mrow><mi>f</mi><mi>i</mi><mi>r</mi><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac><mi>&#160;</mi></math>= second number <br>(14 : 98) &rArr; 14 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>2</mn></mfrac></math> = 98<br>(22 : 242) &rArr; 22 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>2</mn></mfrac></math> = 242<br>Similarly, <br>(6 : 18) &rArr; 6 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>2</mn></mfrac></math> = 18<br>Hence option (d) satisfies.</p>",
                    solution_hi: "<p>25.(d) <strong>तर्क :</strong>- पहली संख्या &times; <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo></mrow><mn>2</mn></mfrac></math> = दूसरी संख्या<br>(14 : 98) &rArr; 14 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>2</mn></mfrac></math> = 98<br>(22 : 242) &rArr; 22 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>2</mn></mfrac></math> = 242<br>इसी प्रकार, <br>(6 : 18) &rArr; 6 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>2</mn></mfrac></math> = 18<br>इसलिए विकल्प (d) संतुष्ट करता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. The value of 180 &divide; (2<sup>3</sup> + 3<sup>2</sup> + 1) + 37 - 20 equal to</p>",
                    question_hi: "<p>26. 180 &divide; (2<sup>3</sup> + 3<sup>2</sup> + 1) + 37 - 20 किसके समतुल्य है?</p>",
                    options_en: ["<p>27</p>", "<p>20</p>", 
                                "<p>23</p>", "<p>25</p>"],
                    options_hi: ["<p>27</p>", "<p>20</p>",
                                "<p>23</p>", "<p>25</p>"],
                    solution_en: "<p>26.(a) 180 &divide; (2<sup>3</sup> + 3<sup>2</sup> + 1) + 37 - 20<br>= 10 + 37 - 20</p>\n<p>= 27</p>",
                    solution_hi: "<p>26.(a) 180 &divide; (2<sup>3</sup> + 3<sup>2</sup> + 1) + 37 - 20<br>= 10 + 37 - 20</p>\n<p>= 27</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. Select the option figure in which the given figure is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097348.png\" alt=\"rId23\" width=\"79\" height=\"103\"></p>",
                    question_hi: "<p>27. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097348.png\" alt=\"rId23\" width=\"79\" height=\"103\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097443.png\" alt=\"rId24\" width=\"92\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097554.png\" alt=\"rId25\" width=\"91\" height=\"91\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097672.png\" alt=\"rId26\" width=\"90\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097781.png\" alt=\"rId27\" width=\"91\" height=\"92\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097443.png\" alt=\"rId24\" width=\"90\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097554.png\" alt=\"rId25\" width=\"90\" height=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097672.png\" alt=\"rId26\" width=\"90\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097781.png\" alt=\"rId27\" width=\"90\" height=\"91\"></p>"],
                    solution_en: "<p>27.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097871.png\" alt=\"rId28\" width=\"101\" height=\"99\"></p>",
                    solution_hi: "<p>27.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365097871.png\" alt=\"rId28\" width=\"101\" height=\"99\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. Which of the following is the process of converting sugar into alcohol ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन सी शर्करा को अल्कोहल में परिवर्तित करने की प्रक्रिया है ?</p>",
                    options_en: ["<p>Pasteurisation</p>", "<p>Bleaching</p>", 
                                "<p>Fermentation</p>", "<p>Oxidation</p>"],
                    options_hi: ["<p>पास्चुरीकरण</p>", "<p>विरंजन</p>",
                                "<p>किण्वन</p>", "<p>ऑक्सीकरण</p>"],
                    solution_en: "<p>28.(c) <strong>Fermentation.</strong> It is a process in which sugars are transformed into a new product through chemical reactions carried out by microorganisms. The overall chemical formula for alcoholic fermentation is : C<sub>6</sub>H<sub>12</sub>O<sub>6</sub> &rarr; 2 C<sub>2</sub>H<sub>5</sub>OH (ethanol) + 2 CO<sub>2</sub> . <strong>Pasteurisation</strong> is a process of food preservation in which packaged and non-packaged foods are treated with mild heat, usually to less than 100 &deg;C (212 &deg;F), to eliminate pathogens and extend shelf life. <strong>Bleaching powder</strong> (Ca(ClO)<sub>2</sub>) is used as disinfectant and germicide especially in the sterilization of drinking water. It is used for the manufacturing of chloroform. It is used as an oxidizing agent in many chemical industries.<strong> Oxidation</strong> is defined as a process in which an electron is removed from a molecule during a chemical reaction.</p>",
                    solution_hi: "<p>28.(c) <strong>किण्वन</strong>। यह एक ऐसी अभिक्रिया है जिसमें सूक्ष्मजीवों द्वारा की गई रासायनिक अभिक्रियाओ के माध्यम से शर्करा को एक नए उत्पाद में बदल दिया जाता है। अल्कोहलिक किण्वन के लिए समग्र रासायनिक सूत्र है: C<sub>6</sub>H<sub>12</sub>O<sub>6</sub> &rarr; 2C<sub>2</sub>H<sub>5</sub>OH (इथेनॉल) + 2CO<sub>2</sub> । <strong>पास्चुरीकरण</strong> खाद्य संरक्षण की एक अभिक्रिया है जिसमें रोगजनकों को खत्म करने और शेल्फ लाइफ को बढ़ाने के लिए पैकेज्ड और गैर-पैकेज्ड खाद्य पदार्थों को आमतौर पर 100 डिग्री सेल्सियस (212 डिग्री फारेनहाइट) से कम तापमान पर हल्की ऊष्मा से उपचारित किया जाता है। <strong>ब्लीचिंग पाउडर</strong> (Ca(ClO)<sub>2</sub>) का उपयोग विशेष रूप से पीने के जल के कीटाणुशोधन में कीटाणुनाशक और रोगाणुनाशक के रूप में किया जाता है। इसका उपयोग क्लोरोफॉर्म के निर्माण में किया जाता है। इसका उपयोग कई रासायनिक उद्योगों में ऑक्सीकरण घटक के रूप में किया जाता है। <strong>ऑक्सीकरण</strong> को एक ऐसी अभिक्रिया के रूप में परिभाषित किया जाता है जिसमें रासायनिक अभिक्रिया के दौरान एक अणु से एक इलेक्ट्रॉन हटा दिया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. &lsquo;BELATED&rsquo; is related to &lsquo;TLEEDBA&rsquo; and &lsquo;STOREY&rsquo; is related to &lsquo;YTSROE&rsquo; in the same way as &lsquo;DOUBBLE&rsquo; is related to &lsquo;______&rsquo;.</p>",
                    question_hi: "<p>29. \'BELATED\' का संबंध \'TLEEDBA\' से है और &lsquo;STOREY&rsquo; का संबंध \'YTSROE\' से उसी प्रकार है जैसे \'DOUBBLE\' का संबंध \'______\' से है।</p>",
                    options_en: ["<p>OULEDBC</p>", "<p>UOLEDBC</p>", 
                                "<p>OULEDBB</p>", "<p>UOLEDBB</p>"],
                    options_hi: ["<p>OULEDBC</p>", "<p>UOLEDBC</p>",
                                "<p>OULEDBB</p>", "<p>UOLEDBB</p>"],
                    solution_en: "<p>29.(d) BELATED = TLEEDBA (Arranged in decreasing alphabetical order)<br>STOREY = YTSROE (Arranged in decreasing alphabetical order)<br>Similarly,<br>DOUBBLE = UOLEDBB</p>",
                    solution_hi: "<p>29.(d) BELATED = TLEEDBA (घटते वर्णानुक्रम में व्यवस्थित) <br>STOREY = YTSROE (घटते वर्णानुक्रम में व्यवस्थित)<br>इसी तरह,<br>DOUBBLE = UOLEDBB</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. If 3 cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> , then the value of cosec&theta;.tan&theta; is</p>",
                    question_hi: "<p>30. यदि 3 cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> , तो cosec&theta;.tan&theta; का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn><mi>&#160;</mi></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>"],
                    solution_en: "<p>30.(c) As 3 cos&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> ,<br>&rArr; sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>2</mn></msqrt><msqrt><mn>3</mn></msqrt></mfrac></math>&nbsp;<br>&rArr; tan&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>Then. cosec&theta; .tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><msqrt><mn>2</mn></msqrt></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>&rArr; cosec&theta; .tan&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p>30.(c) चूँकि, 3 cos&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> ,<br>&rArr; sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>2</mn></msqrt><msqrt><mn>3</mn></msqrt></mfrac></math>&nbsp;<br>&rArr; tan&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>फिर, cosec&theta; .tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><msqrt><mn>2</mn></msqrt></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>&rArr; cosec&theta; .tan&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one.</p>",
                    question_hi: "<p>31. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ?</p>",
                    options_en: ["<p>Wildcat</p>", "<p>Tiger</p>", 
                                "<p>Leopard</p>", "<p>Fox</p>"],
                    options_hi: ["<p>जंगली बिल्ली</p>", "<p>बाघ</p>",
                                "<p>तेंदुआ</p>", "<p>लोमड़ी</p>"],
                    solution_en: "<p>31.(d) Wildcat, Tiger, Leopard all the three belong to the felidae or cat family. But fox belong to the canidae or dog family.</p>",
                    solution_hi: "<p>31.(d) वाइल्डकैट, टाइगर, तेंदुआ तीनों फेलिडे या बिल्ली परिवार से ताल्लुक रखते हैं। लेकिन फॉक्स कैनिडे या कुत्ते परिवार से संबंधित है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. Fundamental Duties of the Indian Constitution are mentioned in which Article?</p>",
                    question_hi: "<p>32. भारतीय संविधान के मौलिक कर्तव्यों का उल्लेख किस अनुच्छेद में किया गया है?</p>",
                    options_en: ["<p>Article 51</p>", "<p>Article 51 A</p>", 
                                "<p>Article 42</p>", "<p>Article 42 A</p>"],
                    options_hi: ["<p>अनुच्छेद 51</p>", "<p>अनुच्छेद 51 A</p>",
                                "<p>अनुच्छेद 42</p>", "<p>अनुच्छेद 42 A</p>"],
                    solution_en: "<p>32.(b) <strong>Article 51 A.</strong> Fundamental Duties are mentioned in Part IVA of the Indian Constitution. They were added by the 42nd Amendment Act in 1976 on the recommendation of the Swaran Singh Committee. Article 51 - Promotion of international peace and security. Article 42 - Provision for just and humane conditions of work and maternity relief.</p>",
                    solution_hi: "<p>32.(b) <strong>अनुच्छेद 51A.</strong> भारतीय संविधान के भाग IVA में मौलिक कर्तव्यों का उल्लेख किया गया है। इन्हें स्वर्ण सिंह समिति की सिफारिश पर 1976 में 42वें संशोधन अधिनियम द्वारा शामिल किया गया था। अनुच्छेद 51 - अंतर्राष्ट्रीय शांति और सुरक्षा को बढ़ावा देना। अनुच्छेद 42 - काम की न्यायसंगत और मानवीय परिस्थितियों और मातृत्व राहत का प्रावधान।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. (sinA + cosA)<sup>2</sup> - 2sinAcosA is equal to</p>",
                    question_hi: "<p>33. (sinA + cosA)<sup>2</sup> - 2sinAcosA किसके समतुल्य है?</p>",
                    options_en: ["<p>0</p>", "<p>sin<sup>2</sup>A - cos<sup>2</sup>A</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>0</p>", "<p>sin<sup>2</sup>A - cos<sup>2</sup>A</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>33.(c) (sinA + cosA)<sup>2</sup> - 2sinAcosA<br>= sin<sup>2</sup>A + cos<sup>2</sup>A + 2 sinA cosA - 2 sin A cosA<br>= sin<sup>2</sup>A + cos<sup>2</sup>A = 1</p>",
                    solution_hi: "<p>33.(c) (sinA + cosA)<sup>2</sup> - 2sinAcosA<br>= sin<sup>2</sup>A + cos<sup>2</sup>A + 2 sinA cosA - 2 sin A cosA<br>= sin<sup>2</sup>A + cos<sup>2</sup>A = 1</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. When did Nadir Shah invade India and sack Delhi ?</p>",
                    question_hi: "<p>34. नादिर शाह ने भारत पर कब आक्रमण किया और दिल्ली को कब लूटा ?</p>",
                    options_en: ["<p>1765</p>", "<p>1761</p>", 
                                "<p>1754</p>", "<p>1739</p>"],
                    options_hi: ["<p>1765</p>", "<p>1761</p>",
                                "<p>1754</p>", "<p>1739</p>"],
                    solution_en: "<p>34.(d) <strong>1739</strong>. Emperor Nader Shah, the Shah of Persia (1736&ndash;47) is the founder of the Iranian Afsharid dynasty of Persia. <strong>The Battle of Karnal </strong>(24 February 1739) was a decisive victory for Nader Shah. Nader\'s forces defeated the army of Muhammad Shah within three hours, paving the way for the Iranian sack of Delhi. <strong>The Peacock throne</strong> built by Shah Jahan was transported by Nadir Shah. The legendary <strong>&ldquo;Koh-i-noor&rdquo; diamond</strong> was also taken by him.</p>",
                    solution_hi: "<p>34.(d) <strong>1739</strong>। सम्राट नादिर शाह, फारस के शाह (1736-47) फारस के ईरानी अफशरीद वंश के संस्थापक हैं। <strong>करनाल का युद्ध</strong> (24 फरवरी 1739) नादिर शाह के लिए एक निर्णायक जीत थी। नादिर की सेना ने तीन घंटे के भीतर मुहम्मद शाह की सेना को हरा दिया, जिससे दिल्ली पर ईरानी कब्ज़ा करने का मार्ग प्रशस्त हो गया। शाहजहाँ द्वारा बनवाया गया <strong>मयूर सिंहासन</strong> नादिर शाह द्वारा ले जाया गया था। इसने प्रसिद्ध <strong>\"कोहिनूर\" हीरा</strong> भी अपने साथ ले गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. Four figures have been given, out of which three are alike in some manner and one is different. Select the odd one .<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098022.png\" alt=\"rId29\" width=\"170\" height=\"141\"></p>",
                    question_hi: "<p>35. चार आकृतियाँ दी गई हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098022.png\" alt=\"rId29\" width=\"170\" height=\"141\"></p>",
                    options_en: ["<p>a</p>", "<p>b</p>", 
                                "<p>c</p>", "<p>d</p>"],
                    options_hi: ["<p>a</p>", "<p>b</p>",
                                "<p>c</p>", "<p>d</p>"],
                    solution_en: "<p>35.(d) <br>Except option(D), there is a circle inside the triangle in each figure.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098174.png\" alt=\"rId30\" width=\"101\" height=\"82\"></p>",
                    solution_hi: "<p>35.(d) <br>विकल्प (D) को छोड़कर, सभी आकृतियों में त्रिभुज के अंदर वृत्त है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098174.png\" alt=\"rId30\" width=\"101\" height=\"82\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. What is pro-vitamin A or the anti-cancer compound present in carrots called?</p>",
                    question_hi: "<p>36. गाजर में मौजूद प्रो-विटामिन A या कैंसर रोधी यौगिक क्या कहलाता है?</p>",
                    options_en: ["<p>Beta-carotene</p>", "<p>Alpha-resins</p>", 
                                "<p>Delta-terpenes</p>", "<p>Alpha-terpenoid</p>"],
                    options_hi: ["<p>बीटा-कैरोटीन</p>", "<p>अल्फा-रेजिन</p>",
                                "<p>डेल्टा-टरपीन्स</p>", "<p>अल्फा-टरपीनॉयड</p>"],
                    solution_en: "<p>36.(a) <strong>Beta-carotene.</strong> It is a yellow-orange pigment found in fruits and vegetables. It converts to Vitamin A in the body, supporting healthy vision, immune function, and skin. As an antioxidant, It protects cells from damage, reducing cancer and heart disease risk. Food sources include carrots, sweet potatoes, dark leafy greens, and squash.</p>",
                    solution_hi: "<p>36.(a) <strong>बीटा-कैरोटीन।</strong> यह फलों और सब्जियों में पाया जाने वाला एक पीला-नारंगी रंगद्रव्य है। यह शरीर में विटामिन A में परिवर्तित हो जाता है, जिससे स्वस्थ दृष्टि, प्रतिरक्षा प्रणाली और त्वचा को सहायता प्रदान करता है। एक एंटीऑक्सीडेंट के रूप में, यह कोशिकाओं को क्षति से बचाता है, जिससे कैंसर और हृदय रोग का जोखिम कम होता है। इसके खाद्य स्रोतों में गाजर, शकरकंद, हरे पत्तेदार सब्जियाँ और स्क्वैश शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. Two poles of height 20 and 14 meters are joined at the top by a wire which makes an angle of 30&deg; with the horizontal. The length of the wire is?</p>",
                    question_hi: "<p>37. 20 और 14 मीटर ऊंचाई वाले दो खंभों को एक तार द्वारा शीर्ष पर जोड़ा जाता है जो क्षैतिज के साथ 30&deg; का एक कोण बनाता है। तार की लंबाई कितनी है?</p>",
                    options_en: ["<p>12 m</p>", "<p>10 m</p>", 
                                "<p>16 m</p>", "<p>14 m</p>"],
                    options_hi: ["<p>12 m</p>", "<p>10 m</p>",
                                "<p>16 m</p>", "<p>14 m</p>"],
                    solution_en: "<p>37.(a) L :- Length of wire<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098265.png\" alt=\"rId31\" width=\"97\" height=\"98\"><br>Sin 30&deg; = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mi>L</mi></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mi>L</mi></mrow></mfrac></math><br>&rArr; L = 6 &times; 2 = 12 m</p>",
                    solution_hi: "<p>37.(a) L :- तार की लंबाई<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098265.png\" alt=\"rId31\" width=\"97\" height=\"98\"><br>Sin 30&deg; = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mi>L</mi></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mi>L</mi></mrow></mfrac></math><br>&rArr; L = 6 &times; 2 = 12 m</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. Who invented the modern mercury thermometer with a standardised scale?</p>",
                    question_hi: "<p>38. मानकीकृत पैमाने के साथ आधुनिक पारा थर्मामीटर का आविष्कार किसने किया ?</p>",
                    options_en: ["<p>Galileo Galilei</p>", "<p>Grand Duke</p>", 
                                "<p>Daniel Gabriel Fahrenheit</p>", "<p>Anders Celsius</p>"],
                    options_hi: ["<p>गैलीलियो गैलीली</p>", "<p>ग्रैंड ड्यूक</p>",
                                "<p>डेनियल गेब्रियल फारेनहाइट</p>", "<p>एंडर्स सेल्सियस</p>"],
                    solution_en: "<p>38.(c) <strong>Daniel Gabriel Fahrenheit.</strong> He is Known as the inventor of the Alcohol thermometer, Mercury-in-glass thermometer and Fahrenheit temperature scale. Celsius was invented by <strong>Anders Celcius</strong> <strong>(Award </strong>- Fellow of the Royal Society). <strong>Galileo Galilei</strong> is famous for his telescope discoveries.</p>",
                    solution_hi: "<p>38.(c) <strong>डेनियल गेब्रियल फारेनहाइट।</strong> इन्हें अल्कोहल थर्मामीटर, मर्करी-इन-ग्लास थर्मामीटर और फ़ारेनहाइट तापमान पैमाने के आविष्कारक के रूप में जाना जाता है। सेल्सियस का आविष्कार <strong>एंडर्स सेल्सियस (पुरस्कार </strong>- फेलो ऑफ़ द रॉयल सोसाइटी) ने किया था। <strong>गैलीलियो गैलीली</strong> अपनी दूरबीन खोजों के लिए प्रसिद्ध हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. In a certain code language, \"DIARY\" is written as \'FLFXF\' and \'READY\" is written as \'THFJF. How will \"ERROR\' be written in that language?</p>",
                    question_hi: "<p>39. एक निश्चित कूट भाषा में, \'DIARY\' को \'FLFXF\' और \'READY\' को \'THFJF\' लिखा जाता है। उसी कूट भाषा में \'ERROR\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>JBDYU</p>", "<p>JULDJ</p>", 
                                "<p>GNJUY</p>", "<p>GUWUY</p>"],
                    options_hi: ["<p>JBDYU</p>", "<p>JULDJ</p>",
                                "<p>GNJUY</p>", "<p>GUWUY</p>"],
                    solution_en: "<p>39.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098360.png\" alt=\"rId32\" width=\"112\" height=\"77\"> and <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098471.png\" alt=\"rId33\" width=\"124\" height=\"74\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098589.png\" alt=\"rId34\" width=\"127\" height=\"84\"></p>",
                    solution_hi: "<p>39.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098360.png\" alt=\"rId32\" width=\"112\" height=\"77\"> और&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098471.png\" alt=\"rId33\" width=\"124\" height=\"74\"><br>उसी प्रकार , <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098589.png\" alt=\"rId34\" width=\"127\" height=\"84\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. The depositional pattern of CaCO<sub>3</sub> and other materials that extend upwards from the floor of a limestone cavern is known as:</p>",
                    question_hi: "<p>40. CaCO<sub>3</sub> और अन्य सामग्रियों का वह निक्षेपण पैटर्न क्या कहलाता है, जो चूना पत्थर की गुफा के तल से ऊपर की ओर बढ़ता है?</p>",
                    options_en: ["<p>Stalactite</p>", "<p>Dripstone</p>", 
                                "<p>Stalagmite</p>", "<p>Hatectite</p>"],
                    options_hi: ["<p>स्टेलक्टाइट</p>", "<p>ड्रिपस्टोन</p>",
                                "<p>स्टेलग्माइट</p>", "<p>हेटेक्टाइट</p>"],
                    solution_en: "<p>40.(c) <strong>Stalagmite. </strong>It rises up from the floor of the caves. Stalactite - It is an icicle-shaped formation that hangs from the ceiling of a cave and is produced by the precipitation of minerals from water dripping through the cave ceiling. The stalagmites and stalactites eventually fuse to give rise to columns and pillars of different diameters.</p>",
                    solution_hi: "<p>40.(c) <strong>स्टेलग्माइट</strong>। यह गुफाओं की तल से ऊपर की ओर उठी संरचना है। स्टैलेक्टाइट - यह एक हिमखंड के आकार की संरचना है जो गुफा की छत से लटकता होता है और गुफा की छत से टपकने वाले पानी से खनिजों के अवक्षेपण द्वारा निर्मित होता है। स्टैलेग्माइट और स्टैलेक्टाइट अंततः मिलकर विभिन्न व्यास के स्तंभ और खंभे बनाते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. Find the area of a rhombus of side 17 cm with one of its diagonal 16 cm long.</p>",
                    question_hi: "<p>41. 17 cm भुजा वाले एक समचतुर्भुज का क्षेत्रफल ज्ञात कीजिए जिसका एक विकर्ण 16 cm लंबा है।</p>",
                    options_en: ["<p>225 cm<sup>2</sup></p>", "<p>240 cm<sup>2</sup></p>", 
                                "<p>230 cm<sup>2</sup></p>", "<p>220 cm<sup>2</sup></p>"],
                    options_hi: ["<p>225 cm<sup>2</sup></p>", "<p>240 cm<sup>2</sup></p>",
                                "<p>230 cm<sup>2</sup></p>", "<p>220 cm<sup>2</sup></p>"],
                    solution_en: "<p>41.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098719.png\" alt=\"rId35\" width=\"107\" height=\"103\"><br>Area of rhombus = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; d<sub>1</sub> &times; d<sub>2</sub><br>Let d<sub>1</sub> = 16 cm <br>In &Delta;ABO<br>AO = 8 cm and AB =17 cm (given)<br>OB =<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><msqrt><msup><mrow><msup><mn>17</mn><mn>2</mn></msup><mo>-</mo><mn>8</mn></mrow><mn>2</mn></msup></msqrt></math> = 15 cm <br>Now d<sub>2</sub> = CO + OB = 30 cm&nbsp;<br>Area of rhombus = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 30 &times; 16 = 240 cm<sup>2</sup></p>",
                    solution_hi: "<p>41.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098719.png\" alt=\"rId35\" width=\"107\" height=\"103\"><br>समचतुर्भुज का क्षेत्रफल&nbsp; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&times; d<sub>1</sub> &times; d<sub>2</sub><br>माना की, d<sub>1</sub> = 16 cm<br>&Delta;ABO में,<br>AO = 8 cm और AB =17 cm (दिया गया है)<br>OB =<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><msqrt><msup><mrow><msup><mn>17</mn><mn>2</mn></msup><mo>-</mo><mn>8</mn></mrow><mn>2</mn></msup></msqrt></math> = 15 cm<br>अब, d<sub>2</sub> = CO + OB = 30 cm&nbsp;<br>समचतुर्भुज का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 30 &times; 16 = 240 cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. Select the number from among the given options that can replace the question mark (?) in the following series.<br>4, 11, 30, 67, 128, ?</p>",
                    question_hi: "<p>42. दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकती है ?<br>4, 11, 30, 67, 128, ?</p>",
                    options_en: ["<p>195</p>", "<p>182</p>", 
                                "<p>346</p>", "<p>219</p>"],
                    options_hi: ["<p>195</p>", "<p>182</p>",
                                "<p>346</p>", "<p>219</p>"],
                    solution_en: "<p>42.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098946.png\" alt=\"rId36\" width=\"200\" height=\"69\"></p>",
                    solution_hi: "<p>42.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365098946.png\" alt=\"rId36\" width=\"200\" height=\"69\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. Who is the highest run scorer in an over in a test match cricket?</p>",
                    question_hi: "<p>43. टेस्ट मैच क्रिकेट में एक ओवर में सर्वाधिक रन बनाने वाला खिलाड़ी कौन है?</p>",
                    options_en: ["<p>Brian Lara</p>", "<p>Virat Kohli</p>", 
                                "<p>Jaspreet Bumrah</p>", "<p>Kapil Dev</p>"],
                    options_hi: ["<p>ब्रायन लारा</p>", "<p>विराट कोहली</p>",
                                "<p>जसप्रीत बुमराह</p>", "<p>कपिल देव</p>"],
                    solution_en: "<p>43.(c) J<strong>aspreet Bumrah.</strong> He smashed 35 runs in 1 over to Stuart Broad also making the record of most runs in a single over in Test cricket. The highest wicket-taker in Test cricket - Muttiah Muralitharan.</p>",
                    solution_hi: "<p>43.(c) <strong>जसप्रीत बुमराह</strong>। उन्होंने स्टुअर्ट ब्रॉड के 1 ओवर में 35 रन बनाए और टेस्ट क्रिकेट में एक ओवर में सबसे ज़्यादा रन बनाने का रिकॉर्ड भी बनाया। टेस्ट क्रिकेट में सबसे ज़्यादा विकेट लेने वाले गेंदबाज़ - मुथैया मुरलीधरन।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. The ratio of the numbers of sides of two regular polygons is 1 : 2. If each interior angle of the first polygon is 140&deg;, then the measure of each interior angle of the second polygon is ?</p>",
                    question_hi: "<p>44. दो बहुभुजों की भुजाओं की संख्या का अनुपात 1 : 2 है। यदि पहले बहुभुज का प्रत्येक अंतः कोण 140&deg; है, तो दूसरे बहुभुज के प्रत्येक अंत: कोण का माप कितना है?</p>",
                    options_en: ["<p>150&deg;</p>", "<p>140&deg;</p>", 
                                "<p>170&deg;</p>", "<p>160&deg;</p>"],
                    options_hi: ["<p>150&deg;</p>", "<p>140&deg;</p>",
                                "<p>170&deg;</p>", "<p>160&deg;</p>"],
                    solution_en: "<p>44.(d)<br>The ratio between sides of two regular polygons = 1 : 2<br>Let , side for polygon1 = 1x&nbsp;, side for polygon<sub>2</sub> = 2x <br>each interior angle of the first polygon = 140&deg;<br>Exterior angle of first polygon = 180&deg;- 140&deg; = 40&deg;</p>\n<p>(sum of Interior and Exterior angle is equal to 180&deg;)<br>Number of side of first polygon(1x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mrow><mn>40</mn><mo>&#176;</mo></mrow></mfrac></math> = 9<br>Number of side of second polygon(2x) = 18<br>Exterior angle of second polygon = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>18</mn></mfrac></math> = 20&deg;<br>Interior angle of second polygon = 180&deg; - 20&deg; = 160&deg;</p>",
                    solution_hi: "<p>44.(d)<br>दो नियमित बहुभुजों की भुजाओं के बीच का अनुपात = 1 : 2<br>मान लीजिए, बहुभुज1 की भुजा = 1x, बहुभुज<sub>2</sub> की भुजा = 2x<br>बहुभुज1 के लिए<br>पहले बहुभुज का बाह्य कोण =180&deg;- 140&deg; = 40&deg; (आंतरिक और बाह्य कोणों का योग 180&deg; के बराबर होता है)<br>पहले बहुभुज की भुजा की संख्या (1x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mrow><mn>40</mn><mo>&#176;</mo></mrow></mfrac></math> = 9<br>दूसरे बहुभुज की भुजा की संख्या(2x) = 18<br>दूसरे बहुभुज का बाह्य कोण = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>18</mn></mfrac></math> = 20&deg;<br>दूसरे बहुभुज का आंतरिक कोण = 180&deg; - 20&deg; = 160&deg;</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. LEADS 2023 report has been released by which ministry?</p>",
                    question_hi: "<p>45. LEADS 2023 रिपोर्ट किस मंत्रालय द्वारा जारी की गई है?</p>",
                    options_en: ["<p>Ministry of Education</p>", "<p>NITI Aayog</p>", 
                                "<p>Ministry of Defence</p>", "<p>Ministry of Commerce and Industry</p>"],
                    options_hi: ["<p>शिक्षा मंत्रालय</p>", "<p>नीति आयोग</p>",
                                "<p>रक्षा मंत्रालय</p>", "<p>वाणिज्य और उद्योग मंत्रालय</p>"],
                    solution_en: "<p>45.(d) The fifth LEADS (Logistics Ease Across Different States) 2023 report, recently released by the Ministry of Commerce and Industry, provides insights into the logistics performance of various states and Union Territories in India. Among the noteworthy highlights, Andhra Pradesh, Karnataka, Tamil Nadu, Chandigarh, and Gujarat are included in the 13 states and Union Territories recognized as &ldquo;achievers&rdquo; in the logistics index chart for 2023.<br>It is worth mentioning that the count has decreased from 15 to 13 states this year, with Himachal Pradesh and Uttarakhand transitioning to the categories of &ldquo;aspirers&rdquo; and &ldquo;fast movers,&rdquo; respectively. Sikkim and Tripura demonstrated notable progress by moving from the &ldquo;fast movers&rdquo; category in 2022 to the esteemed &ldquo;achievers&rdquo; category this year.</p>",
                    solution_hi: "<p>45.(d) हाल ही में वाणिज्य और उद्योग मंत्रालय द्वारा जारी पांचवीं लीड्स (विभिन्न राज्यों में लॉजिस्टिक्स ईज) 2023 रिपोर्ट, भारत में विभिन्न राज्यों और केंद्र शासित प्रदेशों के लॉजिस्टिक्स प्रदर्शन में अंतर्दृष्टि प्रदान करती है। उल्लेखनीय विशेषताओं में, आंध्र प्रदेश, कर्नाटक, तमिलनाडु, चंडीगढ़ और गुजरात उन 13 राज्यों और केंद्र शासित प्रदेशों में शामिल हैं जिन्हें 2023 के लॉजिस्टिक्स इंडेक्स चार्ट में \"अचीवर्स\" के रूप में मान्यता दी गई है।<br>उल्लेखनीय है कि इस वर्ष गिनती 15 से घटकर 13 राज्यों में हो गई है, हिमाचल प्रदेश और उत्तराखंड क्रमशः \"आकांक्षी\" और \"तेजी से आगे बढ़ने वाले\" की श्रेणियों में परिवर्तित हो गए हैं। सिक्किम और त्रिपुरा ने 2022 में \"फास्ट मूवर्स\" श्रेणी से इस वर्ष सम्मानित \"अचीवर्स\" श्रेणी में पहुंचकर उल्लेखनीय प्रगति का प्रदर्शन किया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.<br>_h_ _c d_ p _ c _ h _ k _ d h _ k _</p>",
                    question_hi: "<p>46. अक्षरों के उस संयोजन का चयन करें जिसे दी गई श्रंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रृंखला पूरी हो जाएगी।<br>_h_ _c d_ p _ c _ h _ k _ d h _ k _</p>",
                    options_en: ["<p>phcdkpchdk</p>", "<p>phdkpchkpc</p>", 
                                "<p>dphkchdpck</p>", "<p>dpkhkdpcpc</p>"],
                    options_hi: ["<p>phcdkpchdk</p>", "<p>phdkpchkpc</p>",
                                "<p>dphkchdpck</p>", "<p>dpkhkdpcpc</p>"],
                    solution_en: "<p>46.(d) <br><strong>d</strong>h<strong>pk</strong>c / d<strong>h</strong>p<strong>k</strong>c / <strong>d</strong>h<strong>p</strong>k<strong>c</strong> / dh<strong>p</strong>k<strong>c</strong></p>",
                    solution_hi: "<p>46.(d) <br><strong>d</strong>h<strong>pk</strong>c / d<strong>h</strong>p<strong>k</strong>c / <strong>d</strong>h<strong>p</strong>k<strong>c</strong> / dh<strong>p</strong>k<strong>c</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. The pair of equations 2<sup>x+y</sup> = 16&nbsp;and 64<sup>x-y</sup> = 2,has :</p>",
                    question_hi: "<p>47. समीकरण के युग्म 2<sup>x+y</sup> = 16 और 64<sup>x-y</sup> = 2, के _____________है</p>",
                    options_en: ["<p>Unique Solution x =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>, y = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p>Infinite Solutions</p>", 
                                "<p>No Common Solution</p>", "<p>Unique Solution x =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>, y = <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>अद्वितीय हल x =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>, y = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p>अनंत हल</p>",
                                "<p>कोई उभयनिष्ठ हल नहीं</p>", "<p>अद्वितीय हल x =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>, y = <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>47.(d)<br>2<sup>x+y</sup> = 16<br>&rArr; 2<sup>x+y</sup> = 2<sup>4</sup><br>x + y = 4 _____________ (1)<br>And, 64<sup>x-y</sup> = 2<br>&rArr; 2<sup>6(x-y)</sup> = 2<br>6(x - y ) = 1 ___________ (2)<br>On solving equation (1) and (2), we get<br>X = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> and y = <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math><br>Unique solution.</p>",
                    solution_hi: "<p>47.(d)<br>2<sup>x+y</sup> = 16<br>&rArr; 2<sup>x+y</sup> = 2<sup>4</sup><br>x + y = 4 _____________ (1)<br>और,64<sup>x-y</sup> = 2<br>&rArr; 2<sup>6(x-y)</sup> = 2<br>6 (x - y ) = 1 ___________ (2)<br>समीकरण (1) और (2) को हल करने पर, हम प्राप्त करते हैं<br>X = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> और y = <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. Which country introduced &lsquo;Global Biofuel Alliance (GBA)&rsquo;?</p>",
                    question_hi: "<p>48. किस देश ने \'ग्लोबल बायोफ्यूल एलायंस (GBA)\' की शुरुआत की?</p>",
                    options_en: ["<p>Sri Lanka</p>", "<p>India</p>", 
                                "<p>Australia</p>", "<p>Canada</p>"],
                    options_hi: ["<p>श्रीलंका</p>", "<p>भारत</p>",
                                "<p>ऑस्ट्रेलिया</p>", "<p>कनाडा</p>"],
                    solution_en: "<p>48.(b) Bureau of Indian Standards (BIS) recently revealed that the Indian Standards would serve as a valuable addition to the goals of the Global Biofuel Alliance (GBA).&nbsp;It is the international forum introduced by Prime Minister Narendra Modi during the recent G20 leaders&rsquo; summit. The key standards will help stakeholders dealing with biofuels or related matters.</p>",
                    solution_hi: "<p>48.(b) हाल ही में भारतीय मानक ब्यूरो (BIS) ने खुलासा किया कि भारतीय मानक ग्लोबल बायोफ्यूल एलायंस (GBA) के लक्ष्यों में एक महत्वपूर्ण योगदान देंगे। यह एक अंतरराष्ट्रीय मंच है जिसे प्रधानमंत्री नरेंद्र मोदी ने हाल ही में संपन्न हुए G20 नेताओं के शिखर सम्मेलन के दौरान पेश किया था। ये प्रमुख मानक उन सभी हितधारकों के लिए सहायक सिद्ध होंगे, जो बायोफ्यूल्स या उससे संबंधित मामलों से जुड़े हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. Three statements are followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they do not conform to real world knowledge, decide which of the given conclusions possibilities can be true on the basis of the statements.<br><strong>Statements:</strong><br>1. Some sports are cricket.<br>2. Some cricket are tennis.<br>3. All tennis are balls.<br><strong>Conclusions:</strong><br>I. Some tennis are sports.<br>&Iota;&Iota;. Some balls are sports.<br>III. Some balls are cricket.</p>",
                    question_hi: "<p>49. तीन कथनों के बाद, तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे बास्तबिक जगत की जानकारी के अनुरूप न हों, तय कीजिए कि कथनों के आधार पर दिए गए निष्कर्ष/संभावनाओं में से कौन से सत्य हो सकते हैं।<br><strong>कथन:</strong><br>1. कुछ स्पोर्ट्स, क्रिकेट हैं।<br>2. कुछ क्रिकेट, टेनिस हैं।<br>3. सभी टेनिस, बाल्स हैं।<br><strong>निष्कर्ष:</strong><br>I. कुछ टेनिस स्पोर्ट्स हैं।<br>II. कुछ बाल्स स्पोर्ट्स हैं।<br>III. कुछ बाल्स क्रिकेट हैं।</p>",
                    options_en: ["<p>Only conclusion I is true</p>", "<p>Only conclusion I and II are true</p>", 
                                "<p>Only conclusion II is true</p>", "<p>Only conclusion III is true</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I सत्य है।</p>", "<p>केवल निष्कर्ष I और II सत्य हैं।</p>",
                                "<p>केवल निष्कर्ष II सत्य है।</p>", "<p>केवल निष्कर्ष III सत्य है।</p>"],
                    solution_en: "<p>49.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099071.png\" alt=\"rId37\" width=\"199\" height=\"67\"><br>Only conclusion III is true.</p>",
                    solution_hi: "<p>49.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099176.png\" alt=\"rId38\" width=\"233\" height=\"73\"><br>केवल निष्कर्ष III सत्य है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. In an army camp, there were provisions for 425 men for 30 days. However, 375 men attended the camp. How long did the provision last?</p>",
                    question_hi: "<p>50. एक सैन्य छावनी में 425 आदमियों के लिए 30 दिन का प्रावधान था। हालांकि, 375 पुरुषों ने शिविर में भाग लिया। प्रावधान कितने समय तक चलेगा ?</p>",
                    options_en: ["<p>30 days</p>", "<p>35 days</p>", 
                                "<p>34 days</p>", "<p>32 days</p>"],
                    options_hi: ["<p>30 दिन</p>", "<p>35 दिन</p>",
                                "<p>34 दिन</p>", "<p>32 दिन</p>"],
                    solution_en: "<p>50.(c) Let, Time for 375 men = X<br>Equating both conditions because total provisions are the same in the army camp. <br>425 &times; 30 = 375 &times; X<br>12750 = 375 &times; X<br>X = <math display=\"inline\"><mfrac><mrow><mn>12750</mn></mrow><mrow><mn>375</mn></mrow></mfrac></math><br>X = 34 days</p>",
                    solution_hi: "<p>50.(c) माना, 375 आदमी के लिए समय = X<br>दोनों शर्तों की बराबरी करनी पड़ेगी क्योंकि सेना के शिविर में कुल प्रावधान समान हैं।<br>425 &times; 30 = 375 &times; X<br>12750 = 375 &times; X<br>X = <math display=\"inline\"><mfrac><mrow><mn>12750</mn></mrow><mrow><mn>375</mn></mrow></mfrac></math><br>X = 34 दिन</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. According to India&rsquo;s Election commission, political parties cannot release their&nbsp;manifesto in pre-poll silence period of:</p>",
                    question_hi: "<p>51. भारत के चुनाव आयोग के अनुसार, राजनीतिक दल अपना घोषणा पत्र ,चुनाव पूर्व मौन अवधि में जारी नहीं कर सकते हैं:</p>",
                    options_en: ["<p>24 h</p>", "<p>48 h</p>", 
                                "<p>36 h</p>", "<p>60 h</p>"],
                    options_hi: ["<p>24 h</p>", "<p>48 h</p>",
                                "<p>36 h</p>", "<p>60 h</p>"],
                    solution_en: "<p>51.(b) <strong>48 h. The Election Commission of India (Article 324)</strong> is an autonomous constitutional authority responsible for administering Union and State election processes in India. Established in accordance with the Constitution on <strong>25th January 1950</strong> (celebrated as national voters\' day). The secretariat of the commission is in <strong>New Delhi.</strong> Presently, it consists of the CEC (Chief Election Commissioner) and two Election Commissioners. The first CEC was <strong>Sukumar Sen.</strong></p>",
                    solution_hi: "<p>51.(b) <strong>48 घंटे। भारत का चुनाव आयोग (अनुच्छेद 324)</strong> एक स्वायत्त संवैधानिक प्राधिकरण है जो भारत में संघ और राज्य चुनाव प्रक्रियाओं के प्रशासन के लिए उत्तरदायी है। <strong>25 जनवरी 1950 </strong>को संविधान के अनुसार स्थापित (राष्ट्रीय मतदाता दिवस के रूप में मनाया जाता है) किया गया। आयोग का सचिवालय <strong>नई दिल्ली </strong>में है। वर्तमान में, इसमें CEC (मुख्य चुनाव आयुक्त) और दो चुनाव आयुक्त शामिल हैं। पहले CEC <strong>सुकुमार सेन</strong> थे।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. A bag contains Rs 3,000, the notes of which are in the denomination of Rs 50, Rs 100 and Rs10. If the notes of Rs 100, Rs 50 and Rs 10 are in the ratio of 5 : 8 : 10, then the numbers of the notes of Rs 50 in the bag will be?</p>",
                    question_hi: "<p>52. एक बैग में 3,000 रुपये हैं, जिसके नोट 50 रुपये, 100 रुपये और 10 रुपये के मूल्यवर्ग में हैं। यदि 100 रुपये, 50 रुपये और 10 रुपये के नोटों का अनुपात 5 : 8 : 10 है, तो बैग में 50 रुपये के नोटों की संख्या क्या होगी?</p>",
                    options_en: ["<p>16</p>", "<p>32</p>", 
                                "<p>24</p>", "<p>8</p>"],
                    options_hi: ["<p>16</p>", "<p>32</p>",
                                "<p>24</p>", "<p>8</p>"],
                    solution_en: "<p>52.(c) The notes of Rs 100, Rs. 50 and Rs. 10 = 5 : 8 : 10<br>Rs 50 in the bag = 3000 &times;<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 24</p>",
                    solution_hi: "<p>52.(c) 100 रुपये, 50 रुपये और 10 रुपये के नोट = 5 : 8 : 10<br>बैग में 50 रुपये = 3000 &times;<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 24</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. Which one country of the following was not a part of the former USSR ?</p>",
                    question_hi: "<p>53. निम्नलिखित में से कौन सा देश पूर्व सोवियत संघ का हिस्सा नहीं था ?</p>",
                    options_en: ["<p>Finland</p>", "<p>Russia</p>", 
                                "<p>Georgia</p>", "<p>Moldova</p>"],
                    options_hi: ["<p>फिनलैंड</p>", "<p>रूस</p>",
                                "<p>जॉर्जिया</p>", "<p>मोलदोवा</p>"],
                    solution_en: "<p>53.(a) <strong>Finland</strong> (&ldquo;Land of Thousand Lakes&rdquo;). The Soviet Union (or the Union of Soviet Socialist Republics - USSR) was a giant single-party communist state formed by the federal union of 15 national republics. It existed from <strong>1922 to 1991.</strong> The <strong>Former USSR Countries </strong>- Armenia, Azerbaijan, Belarus, Estonia, Georgia, Moldova, Russia, Kazakhstan, Kyrgyzstan, Latvia, Lithuania, Tajikistan, Turkmenistan, Ukraine and Uzbekistan.</p>",
                    solution_hi: "<p>53.(a) <strong>फिनलैंड</strong> (\"हजारों झीलों की भूमि\")। सोवियत संघ (या सोवियत समाजवादी गणराज्य संघ - USSR) 15 राष्ट्रीय गणराज्यों के संघीय संघ द्वारा गठित एक विशाल एकल-दलीय कम्युनिस्ट राज्य था। यह <strong>1922 से 1991</strong> तक अस्तित्व में था। <strong>पूर्व USSR देश</strong> - आर्मेनिया, अजरबैजान, बेलारूस, एस्टोनिया, जॉर्जिया, मोल्दोवा, रूस, कजाकिस्तान, किर्गिस्तान, लातविया, लिथुआनिया, ताजिकिस्तान, तुर्कमेनिस्तान, यूक्रेन और उज्बेकिस्तान।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. If 140g brass is mixed with copper to prepare an alloy having brass and copper in the ratio 4 : 3, then how much copper has been taken to prepare the alloy?</p>",
                    question_hi: "<p>54. यदि एक मिश्र धातु बनाने के लिए 140g पीतल को तांबे के साथ मिलाया जाता है जिसमें पीतल और तांबे का अनुपात 4 : 3 है, तो मिश्र धातु को तैयार करने के लिए कितना तांबा लिया गया है?</p>",
                    options_en: ["<p>105 g</p>", "<p>245 g</p>", 
                                "<p>60 g</p>", "<p>80 g</p>"],
                    options_hi: ["<p>105 g</p>", "<p>245 g</p>",
                                "<p>60 g</p>", "<p>80 g</p>"],
                    solution_en: "<p>54.(a) 140 g brass is mixed with copper to prepare an alloy having brass and copper in the ratio 4 : 3<br>Here 4 units equivalent to 140,<br>So 3 units will be equivalent to = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>4</mn></mfrac></math> &times; 3 = 105<br>Then, 105g copper has been taken to prepare the alloy.</p>",
                    solution_hi: "<p>54.(a) 140 ग्राम पीतल को तांबे के साथ मिलाकर एक मिश्र धातु तैयार की जाती है जिसमें पीतल और तांबे का अनुपात 4 : 3 होता है,<br>यहाँ 4 इकाइयाँ 140 के बराबर हैं,<br>तो 3 इकाइयाँ&nbsp; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>4</mn></mfrac></math> &times; 3 = 105 के बराबर होंगी,<br>फिर मिश्रधातु तैयार करने के लिए 105 ग्राम तांबा लिया गया है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. Which of the following terms best describes the biological study of animal behaviour ?</p>",
                    question_hi: "<p>55. निम्नलिखित में से कौन सा शब्द पशु व्यवहार के जैविक अध्ययन का सबसे अच्छा वर्णन करता है ?</p>",
                    options_en: ["<p>Etiology</p>", "<p>Ethnology</p>", 
                                "<p>Entomology</p>", "<p>Ethology</p>"],
                    options_hi: ["<p>इटियोलोजी</p>", "<p>इथनोलोजी</p>",
                                "<p>एंटोमोलोजी</p>", "<p>एथोलॉजी</p>"],
                    solution_en: "<p>55.(d) <strong>Ethology. </strong>When a cause of a disease is determined then it is called etiology. The study of disease is called pathology. Entomology is the study of insects. Ethnology is the scientific study and comparison of human races.</p>",
                    solution_hi: "<p>55.(d) <strong>एथोलॉजी।</strong> जब किसी रोग का कारण निर्धारित हो जाता है तो इसे इटियोलोजी (etiology) कहा जाता है। रोग के अध्ययन को पैथोलॉजी (pathology) कहा जाता है। एंटोमोलोजी (Entomology) कीटों का अध्ययन है। इथनोलोजी (Ethnology) मानव जातियों का वैज्ञानिक अध्ययन और तुलना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. A, B and C can complete a task in 36, 54 and 72 days respectively. Together they started the task but A left 8 days before the completion of the task while B left 12 days before the completion. The number of days for which C worked is:</p>",
                    question_hi: "<p>.56. A, B और C एक कार्य को क्रमशः 36, 54 और 72 दिनों में पूरा कर सकते हैं। तीनो ने साथ में मिलकर कार्य शुरू किया लेकिन A कार्य पूरा होने से 8 दिन पहले छोड़ देता है जबकि B पूरा होने से 12 दिन पहले छोड़ देता है। C ने कुल कितने दिनों तक कार्य किया |</p>",
                    options_en: ["<p>24</p>", "<p>28</p>", 
                                "<p>40</p>", "<p>32</p>"],
                    options_hi: ["<p>24</p>", "<p>28</p>",
                                "<p>40</p>", "<p>32</p>"],
                    solution_en: "<p>56.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099340.png\" alt=\"rId39\" width=\"160\" height=\"106\"><br>In last 8 days only C will work = 3 &times; 8 = 24<br>Work done by A and C in 4 days = 9 &times; 4 = 36<br>Remaining work = 216 - (24 + 36) = 216 - 60 = 156<br>Time (A + B + C) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>156</mn><mn>13</mn></mfrac></math> = 12<br>C worked for = 12 + 12 = 24 days</p>",
                    solution_hi: "<p>56.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099456.png\" alt=\"rId40\" width=\"170\" height=\"118\"><br>पिछले 8 दिनों में केवल C काम करेगा = 3 &times; 8 = 24<br>A और C द्वारा 4 दिनों में किया गया कार्य = 9 &times; 4 = 36<br>शेष कार्य = 216 - (24 + 36) = 216 - 60 = 156<br>समय (A + B + C) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>156</mn><mn>13</mn></mfrac></math> = 12<br>C ने काम किया = 12 + 12 = 24 दिन</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57.A statement is given followed by two assumptions numbered I and II. You have to assume everything in the statement to be true and decide which of the assumptions is/are implicit in the statement.<br><strong>Statement:</strong><br>\"In order to retain employees, we must reward them with monthly benefits\", company chairman tells the manager.<br><strong>Assumptions:</strong><br>I. Monthly benefits will keep the employees happy.<br>II. The employees will be punctual.</p>",
                    question_hi: "<p>57. एक कथन और उसके बाद दो धारणाएं। और || दी गई हैं। आपको कथन में दी गई संपूर्ण जानकारी को सत्य मानते हुए, यह तय करना है दी गई धारणाओं में से कौन-सी कथन में निहित हैं?<br><strong>कथन:</strong><br>\"कर्मचारियों को कंपनी से जोड़े रखने के लिए, हमें उन्हें मासिक लाभों से पुरस्कृत करना चाहिए\", कंपनी के अध्यक्ष ने प्रबंधक से कहा।<br><strong>धारणाएं:</strong><br>I. मासिक लाभ से कर्मचारी खुश रहेंगे।<br>॥. कर्मचारी समय के पाबंद होंगे।</p>",
                    options_en: ["<p>Only assumption II is implicit</p>", "<p>Both assumptions I and II are implicit</p>", 
                                "<p>Only assumption I is implicit</p>", "<p>Neither assumption I nor II is implicit</p>"],
                    options_hi: ["<p>केवल धारणा ॥ निहित है |</p>", "<p>I और II दोनों धारणाएं निहित हैं |</p>",
                                "<p>केवल धारणा । निहित है |</p>", "<p>न तो धारणा । और न ही ॥ निहित है |</p>"],
                    solution_en: "<p>57.(c) Since, assumption (ii) does not have any relation from given statement in the sense of Punctuality.<br>Hence, <strong>Only assumption I is implici</strong>t.</p>",
                    solution_hi: "<p>57.(c) चूंकि, धारणा (ii) का समय-पाबंदी के अर्थ में दिए गए कथन से कोई संबंध नहीं है।<br>इसलिए, <strong>केवल धारणा I निहित है।</strong></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. Bhajan Sopori, a Padma Shri awardee, is a _________ player.</p>",
                    question_hi: "<p>58. पद्म श्री पुरस्कार से सम्मानित भजन सोपोरी एक _________ वादक हैं।</p>",
                    options_en: ["<p>sitar</p>", "<p>santoor</p>", 
                                "<p>tabla</p>", "<p>violin</p>"],
                    options_hi: ["<p>सितार</p>", "<p>संतूर</p>",
                                "<p>तबला</p>", "<p>वायलिन</p>"],
                    solution_en: "<p>58.(b) <strong>santoor. </strong>Pt Bhajan Sopari belongs to the Sufiana Gharana of Indian classical music. He was famous as &lsquo;Saint of the Santoor&rsquo; and the &lsquo;King of Strings&rsquo;. Awards - Sangeet Natak Akademi Award (1993), Padma Shri (2004). Famous Santoor players: Rahul Sharma, Satish Vyas, Tarun Bhattacharya, Abhay Sopori, etc.</p>",
                    solution_hi: "<p>58.(b) <strong>संतूर।</strong> पंडित भजन सोपारी भारतीय शास्त्रीय संगीत के सूफियाना घराने से ताल्लुक रखते हैं। वे \'संतूर के संत\' और \'तारों के राजा\' के नाम से प्रसिद्ध थे। पुरस्कार - संगीत नाटक अकादमी पुरस्कार (1993), पद्म श्री (2004)। प्रसिद्ध संतूर वादक: राहुल शर्मा, सतीश व्यास, तरुण भट्टाचार्य, अभय सोपोरी, आदि है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. A tank can be filled by 5 pipes in 80 minutes. How long will it take to fill the tank by 8 pipes of same dimensions?</p>",
                    question_hi: "<p>59. एक टंकी को 5 पाइप 80 मिनट में भर सकते हैं। समान विमाओं के 8 पाइपों से टंकी को भरने में कितना समय लगेगा?</p>",
                    options_en: ["<p>50 minutes</p>", "<p>78 minutes</p>", 
                                "<p>128 minutes</p>", "<p>30 minutes</p>"],
                    options_hi: ["<p>50 मिनट</p>", "<p>78 मिनट</p>",
                                "<p>128 मिनट</p>", "<p>30 मिनट</p>"],
                    solution_en: "<p>59.(a)<br>Work is same in both condition <br>5 pipes &times; 80 min. = 8 pipes &times; x<br>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>80</mn></mrow><mn>8</mn></mfrac></math> = 50 minutes</p>",
                    solution_hi: "<p>59.(a)<br>दोनों स्थितियों में काम बराबर होगा <br>5 पाइप &times; 80 मिनट. = 8 पाइप &times; x<br>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>80</mn></mrow><mn>8</mn></mfrac></math> = 50 मिनट</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. Among A,B,C,D,E,F,G and H . H is the father of G. G is the only brother of F. F is the only sister-in-law of E. E is the mother of D. D is the brother of C and B is the father of H and husband of A. If F is unmarried, then how is C related to A?</p>",
                    question_hi: "<p>60. A, B, C, D, E, F, G और H में से, H, G का पिता है। G, F का इकलौता भाई है। F, E की एकमात्र ननंद है। E, D की माता है। D, C का भाई है और B, H का पिता है और A का पति है। यदि F अविवाहित है, तो C, A से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Son&rsquo;s son</p>", "<p>Father&rsquo;s father</p>", 
                                "<p>Father&rsquo;s mother</p>", "<p>Son&rsquo;s Son&rsquo;s Son</p>"],
                    options_hi: ["<p>बेटे का बेटा</p>", "<p>पिता के पिता</p>",
                                "<p>पिता की मां</p>", "<p>बेटे के बेटे का बेटा</p>"],
                    solution_en: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099582.png\" alt=\"rId41\" width=\"90\" height=\"110\"><br>Here we can not decide C male or female . but according to the option , consider C as male.<br>C is the son of A\'s son\'s son. .</p>",
                    solution_hi: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099582.png\" alt=\"rId41\" width=\"90\" height=\"110\"><br>यहां हम C के बारे में पुरुष या महिला तय नहीं कर सकते। लेकिन विकल्प के अनुसार C को पुरुष मानें।<br>C, A के बेटे के बेटे का बेटा है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Which city is the host of the 2028 Olympics?</p>",
                    question_hi: "<p><strong id=\"docs-internal-guid-0db030ba-7fff-f816-77ca-54df626e0b65\">61. </strong>2028 ओलंपिक्स की मेज़बान कौन सा शहर है?</p>",
                    options_en: ["<p>Paris</p>", "<p>Rome</p>", 
                                "<p>Los Angeles</p>", "<p>Geneva</p>"],
                    options_hi: ["<p>पेरिस</p>", "<p>रोम</p>",
                                "<p>लॉस एंजेलिस</p>", "<p dir=\"ltr\">&nbsp;जिनेवा</p>"],
                    solution_en: "<p>61.(c) Five sports, including cricket will be included in the 2028 Los Angeles Games after gaining the approval of the International Olympic Committee. Lacrosse, squash, flag football and baseball-softball, proposed for inclusion by LA Games organisers, also received approval.</p>",
                    solution_hi: "<p>61.(c) क्रिकेट सहित पांच खेलों को 2028 लॉस एंजेलिस ओलंपिक में शामिल किया जाएगा, जिसे अंतरराष्ट्रीय ओलंपिक समिति (IOC) की मंजूरी मिल गई है। LA गेम्स के आयोजकों द्वारा प्रस्तावित खेलों में लैक्रोस, स्क्वैश, फ्लैग फुटबॉल और बेसबॉल-सॉफ्टबॉल को भी मंजूरी मिल गई है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. Parshotam and Anjilka started moving in opposite directions from the same place at a speed of 30 km/h and 3.5 km/h respectively. How far will they be from each other after 2.5 h?</p>",
                    question_hi: "<p>62. पुरुषोत्तम और अंजिल्का एक ही स्थान से विपरीत दिशाओं में क्रमशः 30 km/h और 3.5 km/h की गति से चलना शुरू करते हैं। 2.5 घंटे के बाद वे एक दूसरे से कितनी दूर होंगे?</p>",
                    options_en: ["<p>66.25 km</p>", "<p>8.75 km</p>", 
                                "<p>75 km</p>", "<p>83.75 km</p>"],
                    options_hi: ["<p>66.25 km</p>", "<p>8.75 km</p>",
                                "<p>75 km</p>", "<p>83.75 km</p>"],
                    solution_en: "<p>62.(d) Parshotam and Anjilka started moving in the opposite directions <br>i.e their relative speed is = speed of Parshotam + speed of Anjilka <br>= (30 + 3.5) km/h = 33.5 km/h<br>So, in 2 hours they will be away from each other by <br>= 33.5 &times; 2.5 km = 83.75 km</p>",
                    solution_hi: "<p>62.(d) पुरुषोत्तम और अंजिल्का विपरीत दिशाओं में चलने लगे<br>अर्थात, उनकी सापेक्ष गति है = परशुतम की गति + अंजिल्का की गति<br>= (30 + 3.5) km/h = 33.5 km/h<br>तो, 2 घंटे में बाद उनके बीच की दूरी = 33.5 &times; 2.5 km = 83.75 km</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. Who among the following was the first Indian woman to win the Booker Prize ?</p>",
                    question_hi: "<p>63. निम्नलिखित में से कौन बुकर पुरस्कार जीतने वाली पहली भारतीय महिला थीं ?</p>",
                    options_en: ["<p>Amrita Pritam</p>", "<p>Anita Desai</p>", 
                                "<p>Sarojini Naidu</p>", "<p>Arundhati Roy</p>"],
                    options_hi: ["<p>अमृता प्रीतम</p>", "<p>अनीता देसाई</p>",
                                "<p>सरोजिनी नायडू</p>", "<p>अरुंधति रॉय</p>"],
                    solution_en: "<p>63.(d) <strong>Arundhati Roy. Notable works </strong>- &ldquo;The God of Small Things (1997)&rdquo;, &ldquo;Azadi&rdquo;, &ldquo;My Seditious Heart&rdquo;, &ldquo;The Doctor and the Saint&rdquo;, &ldquo;The Shape of the Beast&rdquo;, &ldquo;The Ministry of Utmost Happiness&rdquo; and &ldquo;Kashmir: The Case for Freedom&rdquo; etc. <strong>Awards </strong>- National Film Award for Best Screenplay (1988), Booker Prize (1997), Sydney Peace Prize (2004), Orwell Award (2004), Norman Mailer Prize (2011).</p>",
                    solution_hi: "<p>63.(d) <strong>अरुंधति रॉय। उल्लेखनीय कार्य</strong> - \"द गॉड ऑफ स्मॉल थिंग्स (1997)\", \"आजादी\", \"माई सेडिटिओउस हार्ट\", \"द डॉक्टर एण्ड द सेंट\", \"द शेप ऑफ द बीस्ट\", \"द मिनिस्ट्री ऑफ अटमोस्ट हैप्पीनेस\" और \"कश्मीर: द केस फॉर फ्रीडम\" आदि। <strong>पुरस्कार -</strong> सर्वश्रेष्ठ पटकथा के लिए राष्ट्रीय फिल्म पुरस्कार (1988), बुकर पुरस्कार (1997), सिडनी शांति पुरस्कार (2004), ऑरवेल पुरस्कार (2004), नॉर्मन मेलर पुरस्कार (2011)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. In the following Venn diagram, the square represents \'creative people\', the hexagon represents \'painters\', and the triangle represents \'people who live in rural areas\".<br>Select the option which represents the number of creative painters who live in rural areas.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099712.png\" alt=\"rId42\" width=\"125\" height=\"87\"></p>",
                    question_hi: "<p>64. निम्नांकित वेन आरेख में, वर्ग \'रचनात्मक व्यक्तियों को निरूपित करता है, षट्भुज \'चित्रकारों को निरूपित करता है, और त्रिभुज \'ग्रामीण क्षेत्रों में रहने वाले व्यक्तियों को निरूपित करता है। <br>उस विकल्प का चयन कीजिए, जो ग्रामीण क्षेत्रों में रहने वाले रचनात्मक चित्रकारों की संख्या को निरूपित करता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099712.png\" alt=\"rId42\" width=\"125\" height=\"87\"></p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>9</p>", "<p>5</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>9</p>", "<p>5</p>"],
                    solution_en: "<p>64.(b)<br><strong>Number of creative painters who live in rural areas = 2</strong></p>",
                    solution_hi: "<p>64.(b)<br><strong>ग्रामीण क्षेत्रों में रहने वाले रचनात्मक चित्रकारों की संख्या = 2</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. A motor boat, whose speed is 11 km/h in still water, goes 28 km downstream in 2 h 20 min. Find the speed of the stream.</p>",
                    question_hi: "<p>65. एक मोटर बोट, जिसकी गति शांत जल में 11 km/h है, यह 2 घंटे 20 मिनट में धारा के अनुकूल 28 km जाती है। धारा की गति ज्ञात कीजिए।</p>",
                    options_en: ["<p>10 km/h</p>", "<p>1 km/h</p>", 
                                "<p>12 km/h</p>", "<p>11 km/h</p>"],
                    options_hi: ["<p>10 km/h</p>", "<p>1 km/h</p>",
                                "<p>12 km/h</p>", "<p>11 km/h</p>"],
                    solution_en: "<p>65.(b)<br>downstream speed of the motor boat = (11 + x) ;<br>Where x = speed of the stream.<br>As it goes 28 km downstream in 2 h 20 min.<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mrow><mo>(</mo><mn>11</mn><mo>+</mo><mi>x</mi><mo>)</mo></mrow></mfrac></math> = 2 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>20</mn><mn>60</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>3</mn></mfrac></mstyle></math><br>&rArr; x = 1<br>So, the speed of the stream = 1 km/h.</p>",
                    solution_hi: "<p>65.(b)<br>मोटर बोट की अनुप्रवाह गति = (11 + x);<br>जहाँ x = धारा की गति।<br>चूंकि यह 2 घंटे 20 मिनट में धारा के अनुकूल 28 km जाती है।<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mrow><mo>(</mo><mn>11</mn><mo>+</mo><mi>x</mi><mo>)</mo></mrow></mfrac></math> = 2 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>20</mn><mn>60</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>3</mn></mfrac></mstyle></math><br>अत: धारा की गति = 1 km/h</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. Which Article of the Indian Constitution mentions that the law declared by the Supreme Court shall be binding on all the courts within the territory of India?</p>",
                    question_hi: "<p>66. भारतीय संविधान के किस अनुच्छेद में उल्लेख है कि सर्वोच्च न्यायालय द्वारा घोषित कानून भारत के राज्यक्षेत्र के भीतर सभी अदालतों पर बाध्यकारी होगा?</p>",
                    options_en: ["<p>Article 151</p>", "<p>Article 147</p>", 
                                "<p>Article 137</p>", "<p>Article 141</p>"],
                    options_hi: ["<p>अनुच्छेद 151</p>", "<p>अनुच्छेद 147</p>",
                                "<p>अनुच्छेद 137</p>", "<p>अनुच्छेद 141</p>"],
                    solution_en: "<p>66.(d) Article 141. Other Articles: Article 137 - Review of judgments or orders by the Supreme Court. Article 147 - any substantial question of law as to the interpretation of this Constitution shall be construed as including references to any substantial question of law as to the interpretation. Article 151 - Audit reports.</p>",
                    solution_hi: "<p>66.(d)<strong> Article 141.</strong> अन्य अनुच्छेद: अनुच्छेद 137 - सर्वोच्च न्यायालय द्वारा निर्णयों या आदेशों की समीक्षा। अनुच्छेद 147 - इस संविधान की व्याख्या के संबंध में विधि के किसी सारवान प्रश्न का अर्थ इस प्रकार लगाया जाएगा कि उसमें व्याख्या के संबंध में विधि के किसी सारवान प्रश्न का संदर्भ भी शामिल है। अनुच्छेद 151 - लेखापरीक्षा रिपोर्ट।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. Two positions of a rotated cube are shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099893.png\" alt=\"rId43\" width=\"122\" height=\"66\"> <br>Which shape will be at the bottom when <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100079.png\" alt=\"rId44\" width=\"13\" height=\"13\"> is on the top?</p>",
                    question_hi: "<p>67. एक घुमाये गये घन की दो स्थितियों को नीचे दिखाया गया है<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365099893.png\" alt=\"rId43\" width=\"122\" height=\"66\"><br>जब <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100079.png\" alt=\"rId44\" width=\"12\" height=\"12\"> शीर्ष पर है तो नीचे कौन-सी आकृति होगी?</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100164.png\" alt=\"rId45\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100279.png\" alt=\"rId46\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100384.png\" alt=\"rId47\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100475.png\" alt=\"rId48\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100164.png\" alt=\"rId45\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100279.png\" alt=\"rId46\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100384.png\" alt=\"rId47\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100475.png\" alt=\"rId48\"></p>"],
                    solution_en: "<p>67.(d)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100556.png\" alt=\"rId49\" width=\"140\" height=\"94\"></p>",
                    solution_hi: "<p>67.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100657.png\" alt=\"rId50\" width=\"118\" height=\"95\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. Which of the following options is correctly matched?</p>",
                    question_hi: "<p>68. निम्नलिखित में से कौन-सा विकल्प सही ढंग से सुमेलित है?</p>",
                    options_en: ["<p>Decomposers - Bacteria</p>", "<p>Omnivorous - Green plants</p>", 
                                "<p>Herbivores - Secondary consumers</p>", "<p>Carnivores - Primary consumers</p>"],
                    options_hi: ["<p>अपघटक - बैक्टीरिया</p>", "<p>सर्वाहारी - हरे पौधे</p>",
                                "<p>शाकाहारी - द्वितीयक उपभोक्ता</p>", "<p>मांसाहारी - प्राथमिक उपभोक्ता</p>"],
                    solution_en: "<p>68.(a) <strong>Decomposers &ndash; Bacteria.</strong> Decomposers like bacteria play a crucial role in breaking down dead organisms and organic matter, recycling nutrients back into the ecosystem. They help decompose complex organic substances into simpler forms, which enrich the soil and support new plant growth. Omnivores eat both plants and animals (e.g., humans, bears). Herbivores consume only plants (e.g., cows, deer). Carnivores eat only meat (e.g., lions, wolves).</p>",
                    solution_hi: "<p>68.(a) <strong>अपघटक - बैक्टीरिया। </strong>बैक्टीरिया अपघटक पारिस्थितिकी तंत्र में एक महत्वपूर्ण भूमिका निभाते हैं। ये सूक्ष्मजीव मृत जीवों और कार्बनिक पदार्थों को तोड़कर पोषक तत्वों को पारिस्थितिकी तंत्र में वापस लाने का कार्य करते हैं। वे जटिल कार्बनिक पदार्थों को सरल रूपों में विघटित करने में मदद करते हैं, जो मिट्टी को समृद्ध करते हैं और नए पौधों के विकास में सहायक होते हैं। सर्वाहारी पौधे और जानवर (जैसे, मनुष्य, भालू) दोनों खाते हैं। शाकाहारी केवल पौधों को खाते हैं (जैसे, गाय, हिरण)। मांसाहारी केवल मांस खाते हैं (जैसे, शेर, भेड़िये)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Each edge of a cube is increased by 50% . Find the percentage increase in its surface area.</p>",
                    question_hi: "<p>69. एक घन के प्रत्येक किनारे में 50% की वृद्धि की जाती है। इसके पृष्ठीय क्षेत्रफल में प्रतिशत वृद्धि ज्ञात कीजिए।</p>",
                    options_en: ["<p>100%</p>", "<p>125%</p>", 
                                "<p>130%</p>", "<p>120%</p>"],
                    options_hi: ["<p>100%</p>", "<p>125%</p>",
                                "<p>130%</p>", "<p>120%</p>"],
                    solution_en: "<p>69.(b)<br>Let side of cube = 100%<br>Edge of a cube is increased by 50%<br>then side of the cube = 150 %<br>% increase in its surface area = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>&#215;</mo><mn>50</mn></mrow><mn>100</mn></mfrac></math> &times; 100 = 125 %</p>",
                    solution_hi: "<p>69.(b)<br>माना घन की भुजा = 100%<br>एक घन के किनारे में 50% की वृद्धि की जाती है<br>तो घन की भुजा = 150%<br>इसके सतह क्षेत्र में % वृद्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>&#215;</mo><mn>50</mn></mrow><mn>100</mn></mfrac></math> &times; 100 = 125 %</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. Six persons &ndash; teacher, lawyer, doctor, politician, artist and writer - are sitting around a hexagonal shaped table such that the lawyer and the writer are not sitting next to each other. The teacher is sitting between the doctor and the lawyer. The politician is sitting next to the doctor. Who is sitting between the lawyer and the writer?</p>",
                    question_hi: "<p>70. छह व्यक्ति - शिक्षक, वकील, डॉक्टर, राजनेता, कलाकार और लेखक - एक षट्कोणीय आकार की मेज के चारों ओर इस प्रकार बैठे हैं कि वकील और लेखक एक दूसरे के बगल में नहीं बैठे हैं। शिक्षक, डॉक्टर और वकील के बीच बैठा है। राजनेता, डॉक्टर के बगल में बैठा है। वकील और लेखक के बीच में कौन बैठा है?</p>",
                    options_en: ["<p>Artist</p>", "<p>Writer</p>", 
                                "<p>Politician</p>", "<p>Teacher</p>"],
                    options_hi: ["<p>कलाकार</p>", "<p>लेखक</p>",
                                "<p>राजनीतिज्ञ</p>", "<p>शिक्षक</p>"],
                    solution_en: "<p>70.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100802.png\" alt=\"rId51\" width=\"151\" height=\"104\"></p>",
                    solution_hi: "<p>70.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365100924.png\" alt=\"rId52\" width=\"150\" height=\"110\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. According to the Constitution of India, the minimum age requirement for being a member of panchayat is:</p>",
                    question_hi: "<p>71. भारत के संविधान के अनुसार, पंचायत का सदस्य होने के लिए न्यूनतम कितनी आयु की आवश्यकता है ?</p>",
                    options_en: ["<p>18 years</p>", "<p>28 years</p>", 
                                "<p>24 years</p>", "<p>21 years</p>"],
                    options_hi: ["<p>18 वर्ष</p>", "<p>28 वर्ष</p>",
                                "<p>24 वर्ष</p>", "<p>21 वर्ष</p>"],
                    solution_en: "<p>71.(d) <strong>21 years.</strong> Panchayats have been constitutionally recognized as local self-governments in<strong> Part IX,</strong> Constitution of India, through its <strong>73rd Amendment, 1992. The compulsory provisions are </strong>- Organisation of Gram Sabha, Establishment of panchayats at the village, block, and district levels. Direct elections to all seats in panchayats at the village, block, and district levels. Indirect elections to the post of chairperson of panchayats at the block and district levels. Reservation of seats for SC/ST in panchayat shall be in proportion to their population.</p>",
                    solution_hi: "<p>71.(d) <strong>21 वर्ष</strong>। भारत के संविधान के भाग IX में <strong>73वें संशोधन, 1992 </strong>के माध्यम से पंचायतों को संवैधानिक रूप से स्थानीय स्वशासन के रूप में मान्यता दी गई है। <strong>अनिवार्य प्रावधान हैं</strong> - ग्राम सभा का संगठन, गाँव, ब्लॉक और जिला स्तरों पर पंचायतों की स्थापना। ग्राम, ब्लॉक और जिला स्तर पर पंचायतों की सभी सीटों पर प्रत्यक्ष चुनाव। ब्लॉक और जिला स्तर पर पंचायतों के अध्यक्ष पद के लिए अप्रत्यक्ष चुनाव। पंचायत में SC/ST के लिए सीटों का आरक्षण उनकी जनसंख्या के अनुपात में होगा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. Radha walks a distance of 9 m towards the South-East, then she walks 15m towards the West. From there, she walks 9m towards the North-West. Finally, she walks 6m towards the East and stands at the point. How far is she standing from the starting point?</p>",
                    question_hi: "<p>72. राधा दक्षिण-पूर्व की ओर 9 m चलती है, फिर वह पश्चिम की ओर 15 m चलती है। वहां से वह 9 m उत्तर-पश्चिम की ओर चलती है। अंत में, वह पूर्व की ओर 6 m चलती है और एक बिंदु पर खड़ी हो जाती है। वह आरंभिक बिंदु से कितनी दूर खड़ी है?</p>",
                    options_en: ["<p>11 m</p>", "<p>13 m</p>", 
                                "<p>10 m</p>", "<p>9 m</p>"],
                    options_hi: ["<p>11 m</p>", "<p>13 m</p>",
                                "<p>10 m</p>", "<p>9 m</p>"],
                    solution_en: "<p>72.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101045.png\" alt=\"rId53\" width=\"145\" height=\"129\"><br>From the above diagram, we can see the distance between the starting point and the end point = (15 - 6 ) m = 9 m</p>",
                    solution_hi: "<p>72.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101045.png\" alt=\"rId53\" width=\"126\" height=\"112\"><br>उपरोक्त आरेख से हम प्रारंभिक बिंदु और अंतिम बिंदु के बीच की दूरी = (15 - 6)m = 9 m देख सकते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. A dealer sells a table for Rs 400, making a proﬁt of 25%. He sells another table at a loss of 10% and on the whole he makes neither proﬁt nor loss. How much (in Rs) did the second table cost him?</p>",
                    question_hi: "<p>73.एक डीलर एक मेज को 400 रुपये में बेचता है, जिससे उसे 25% का लाभ होता है। वह दूसरी टेबल को 10% की हानि पर बेचता है और कुल मिलाकर उसे न तो लाभ होता है और न ही हानि। दूसरे टेबल की कीमत उसे कितनी (रुपये में) पड़ी?</p>",
                    options_en: ["<p>750</p>", "<p>850</p>", 
                                "<p>800</p>", "<p>700</p>"],
                    options_hi: ["<p>750</p>", "<p>850</p>",
                                "<p>800</p>", "<p>700</p>"],
                    solution_en: "<p>73.(c) Let the CP of 1st table = 100<br>Profit = 25% = 25<br>SP = 125 &rArr; Rs. 400<br>CP = <math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> &times; 100 = Rs.320<br>Profit on 1st table = 400 - 320 = Rs 80<br>On selling 2nd table loss = 10% and overall no gain and loss.<br>So the profit of 1st table = loss of 2nd table <br>10% = 80<br>CP of 2nd table = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>10</mn></mfrac></math> &times; 100 = Rs 800</p>",
                    solution_hi: "<p>73.(c) मान लीजिए पहली मेज का CP = 100<br>लाभ = 25% = 25<br>SP = 125 &rArr; Rs. 400<br>CP = <math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> &times; 100 = Rs.320<br>पहली मेज पर लाभ = 400 - 320 = Rs.80<br>दूसरी मेज़ को बेचने पर हानि = 10% और कुल मिलाकर कोई लाभ और हानि नहीं।<br>अतः पहली मेज़ का लाभ = दूसरी मेज़ की हानि<br>10% = 80<br>दूसरी मेज़ की CP =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>10</mn></mfrac></math> &times; 100 = Rs 800</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. Cyclohexane contains________ C-C bonds and ________ C-H bonds, so total ________ covalent bonds.</p>",
                    question_hi: "<p>74. साइक्लोहेक्सेन में ________ C - C बंध, और ________ C - H बंध होते है, इस प्रकार कुल________ सहसंयोजी बंध होते है।</p>",
                    options_en: ["<p>4, 12, 16</p>", "<p>6, 12, 18</p>", 
                                "<p>6, 10, 16</p>", "<p>4, 8, 12</p>"],
                    options_hi: ["<p>4, 12, 16</p>", "<p>6, 12, 18</p>",
                                "<p>6, 10, 16</p>", "<p>4, 8, 12</p>"],
                    solution_en: "<p>74.(b) <strong>6, 12, 18. Cyclohexane</strong> (C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>) &ndash; homocyclic compound (contains only carbon and hydrogen atoms), clear colorless liquid, petroleum like odour, as a solvent, paint remover, cycloalkane, a volatile organic compound and used to make nylon. <strong>Covalent Bond</strong> consists of the mutual sharing of one or more pairs of electrons between two atoms.</p>",
                    solution_hi: "<p>74.(b) <strong>6, 12, 18. साइक्लोहेक्सेन </strong>(C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>) &ndash; होमोसाइक्लिक यौगिक (केवल कार्बन और हाइड्रोजन परमाणु होते हैं), स्पष्ट रंगहीन तरल, पेट्रोलियम जैसी गंध, विलायक के रूप में, पेंट हटाने के लिए, साइक्लोअल्केन, एक वाष्पशील कार्बनिक यौगिक और नायलॉन बनाने के लिए उपयोग किया जाता है। <strong>सहसंयोजी बंध</strong> में दो परमाणुओं के बीच एक या एक से अधिक इलेक्ट्रॉन के जोड़ की आपसी साझेदारी होती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75. If x means +, &divide; means -, + means &times; and - means &divide;, then what will be the value of the following expression?<br>40 &times; 20 &divide; 28 - 4 + 2</p>",
                    question_hi: "<p>75. यदि x का अर्थ +, &divide; का अर्थ - है, + का अर्थ &times; और - का अर्थ &divide; है, तो निम्नलिखित व्यंजक का मान क्या होगा?<br>40 &times; 20 &divide; 28 - 4 + 2</p>",
                    options_en: ["<p>64</p>", "<p>54</p>", 
                                "<p>45</p>", "<p>46</p>"],
                    options_hi: ["<p>64</p>", "<p>54</p>",
                                "<p>45</p>", "<p>46</p>"],
                    solution_en: "<p>75.(d) <br>Given expression: 40 &times; 20 &divide; 28 - 4 + 2<br>After putting the correct signs, <br>= 40 + 20 - 28 &divide; 4 &times; 2<br>= 40 + 20 - 7 &times; 2<br>= 40 + 20 - 14<br>= 60 - 14 = 46</p>",
                    solution_hi: "<p>75.(d) <br>दिया गया व्यंजक: 40 &times; 20 &divide; 28 - 4 + 2<br>सही चिन्ह लगाने के बाद, <br>= 40 + 20 - 28 &divide; 4 &times; 2<br>= 40 + 20 - 7 &times; 2<br>= 40 + 20 - 14<br>= 60 - 14 = 46</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. Ambubachi fair is celebrated in which of the following states?</p>",
                    question_hi: "<p>76. अम्बुबाची मेला (Ambubachi fair) निम्नलिखित में से किस राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Mizoram</p>", "<p>Assam</p>", 
                                "<p>Nagaland</p>", "<p>Manipur</p>"],
                    options_hi: ["<p>मिजोरम</p>", "<p>असम</p>",
                                "<p>नागालैंड</p>", "<p>मणिपुर</p>"],
                    solution_en: "<p>76.(b) <strong>Assam.</strong> Ambubachi Mela is an annual four-day Hindu mela held at Kamakhya Temple in Guwahati, Assam.It symbolizes the fertility cult of Goddess Kamakhya. Other Fairs: Assam - Jonbeel Mela.</p>",
                    solution_hi: "<p>76.(b) <strong>असम।</strong> अम्बुबाची मेला असम के गुवाहाटी में कामाख्या मंदिर में आयोजित होने वाला एक वार्षिक चार दिवसीय हिंदू मेला है। यह देवी कामाख्या के प्रजनन पंथ का प्रतीक है। अन्य मेले: असम - जोनबील मेला।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. A dealer marks his goods 20% above the cost price. He then allows a discount on it and makes a profit of 8%. Find the rate of discount offered by the dealer.</p>",
                    question_hi: "<p>77. एक डीलर अपने माल पर क्रय मूल्य से 20% अधिक अंकित करता है। फिर वह उस पर छूट देता है और 8% का लाभ कमाता है। डीलर द्वारा दी जाने वाली छूट की दर ज्ञात कीजिए।</p>",
                    options_en: ["<p>6%</p>", "<p>10%</p>", 
                                "<p>4%</p>", "<p>12%</p>"],
                    options_hi: ["<p>6%</p>", "<p>10%</p>",
                                "<p>4%</p>", "<p>12%</p>"],
                    solution_en: "<p>77.(b)<br>CP : SP = 100 : 108<br>MP = 100 + 20 = 120<br>Discount = 120 - 108 = 12<br>% discount = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>120</mn></mfrac></math> &times; 100 = 10%</p>",
                    solution_hi: "<p>77.(b)<br>क्रय मूल्य : विक्रय मूल्य = 100 : 108<br>अंकित मूल्य = 100 + 20 = 120<br>छूट = 120 - 108 = 12<br>% छूट = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>120</mn></mfrac></math> &times; 100 = 10%</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. Which of the following is not an example of redox reaction?<br>(i) ZnO + C &rarr; Zn + CO<br>(ii) MnO<sub>2</sub> + 4HCI &rarr; MnCl<sub>2</sub> + 2H<sub>2</sub>O + Cl<sub>2</sub><br>(iii) 4Na+ O<sub>2 </sub>&rarr; 2Na<sub>2</sub>O<br>(iv) AgNO<sub>3 </sub>+ NaCl &rarr; AgCl + NaNO<sub>3</sub></p>",
                    question_hi: "<p>78. निम्नलिखित में से कौन सा रेडॉक्स अभिक्रिया का उदाहरण नहीं है?<br>(i) ZnO + C &rarr; Zn + CO<br>(ii) MnO<sub>2</sub> + 4HCI &rarr; MnCl<sub>2</sub> + 2H<sub>2</sub>O + Cl<sub>2</sub><br>(iii) 4Na+ O<sub>2 </sub>&rarr; 2Na<sub>2</sub>O<br>(iv) AgNO<sub>3 </sub>+ NaCl &rarr; AgCl + NaNO<sub>3</sub></p>",
                    options_en: ["<p>i</p>", "<p>iv</p>", 
                                "<p>iii</p>", "<p>ii</p>"],
                    options_hi: ["<p>i</p>", "<p>iv</p>",
                                "<p>iii</p>", "<p>ii</p>"],
                    solution_en: "<p>78.(b) <strong>iv.</strong> AgNO<sub>3</sub> (Silver Nitrate) + NaCl (sodium chloride) &rarr; AgCl (silver chloride) + NaNO<sub>3</sub> (silver nitrate) an example of a<strong> Double Displacement Reaction</strong> (a reaction in which the positive and negative ions compounds exchange took place to form two new compounds). Redox Reaction - A reaction in which oxidation and reduction take place simultaneously. <strong>Examples:</strong> ZnO (Zinc Oxide) + C (Carbon) &rarr; Zn (Zinc) + CO (Carbon Monoxide), MnO<sub>2</sub> (Magnesium Oxide)+ 4HCl (Hydrochloric acid) &rarr; MnCl<sub>2</sub> (Magnesium Chloride) + 2H<sub>2</sub>O (water) + Cl<sub>2</sub> (Chlorine), 4Na (Sodium) + O<sub>2</sub> (Oxygen) &rarr; 2Na<sub>2</sub>O (Sodium Oxide).</p>",
                    solution_hi: "<p>78.(b) <strong>iv.</strong> AgNO<sub>3</sub> + NaCl &rarr; AgCl + NaNO<sub>3</sub> <strong>द्विविस्थापन अभिक्रिया</strong> (एक अभिक्रिया जिसमें धनात्मक और ऋणात्मक आयनों के यौगिकों का आदान-प्रदान दो नए यौगिकों के निर्माण के लिए हुआ) का एक उदाहरण । <strong>रेडॉक्स अभिक्रिया</strong> - वे अभिक्रिया जिसमे ऑक्सीकरण तथा अपचयन एक साथ घटित होते है। <strong>उदाहरण:</strong> ZnO (जिंक ऑक्साइड) + C (कार्बन) &rarr; Zn (जस्ता) + CO (कार्बन मोनोऑक्साइड), MnO<sub>2</sub> (मैग्नीशियम ऑक्साइड) + 4HCl (हाइड्रोक्लोरिक एसिड) &rarr; MnCl<sub>2</sub> (मैग्नीशियम क्लोराइड) + 2H<sub>2</sub>O (पानी) + Cl<sub>2</sub> (क्लोरीन) ), 4Na (सोडियम) + O<sub>2</sub> (ऑक्सीजन) &rarr; 2Na<sub>2</sub>O (सोडियम ऑक्साइड)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. If in each of the following words, each letter is changed to the next letter in the English alphabetical order, how many words thus formed will have NO vowel? <br>1. BOOK<br>2. PAPER<br>3. BOAT<br>4. PINK</p>",
                    question_hi: "<p>79. यदि निम्नलिखित प्रत्येक शब्द में, प्रत्येक अक्षर को अंग्रेजी वर्णानुक्रम में अगले अक्षर से बदल दिया जाए, तो इस प्रकार बने कितने शब्दों में कोई स्वर नहीं होगा?<br>1. BOOK<br>2. PAPER<br>3. BOAT<br>4. PINK</p>",
                    options_en: ["<p>Four</p>", "<p>One</p>", 
                                "<p>Three</p>", "<p>Two</p>"],
                    options_hi: ["<p>चार</p>", "<p>एक</p>",
                                "<p>तीन</p>", "<p>दो</p>"],
                    solution_en: "<p>79.(d)<br>As per the instruction each letter is changed to the next letter.<br>BOOK = CPPL<br>PAPER = QBQFS<br>BOAT = CPBU<br>PINK = QJOL<br>There are only two words which have no vowel.</p>",
                    solution_hi: "<p>79.(d)<br>निर्देश के अनुसार प्रत्येक अक्षर को अगले अक्षर में बदल दिया जाता है।<br>BOOK = CPPL<br>PAPER = QBQFS<br>BOAT = CPBU<br>PINK = QJOL<br>केवल दो शब्द ऐसे हैं जिनमें कोई स्वर नहीं है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. The simple interest on a certain sum for 5 years at 13% per annum is Rs. 650. The sum is:</p>",
                    question_hi: "<p>80. एक निश्चित राशि पर 5 वर्षों के लिए 13% प्रति वर्ष की दर से साधारण ब्याज 650 रुपये है। राशि है:</p>",
                    options_en: ["<p>Rs. 1,096</p>", "<p>Rs. 1,090</p>", 
                                "<p>Rs. 1,000</p>", "<p>Rs. 1,065</p>"],
                    options_hi: ["<p>Rs. 1,096</p>", "<p>Rs. 1,090</p>",
                                "<p>Rs. 1,000</p>", "<p>Rs. 1,065</p>"],
                    solution_en: "<p>80.(c) SI = 650 , R = 13% , Time = 5 yrs<br>SI = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>650 = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>13</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>P = 1000</p>",
                    solution_hi: "<p>80.(c) साधारण ब्याज(SI) = 650, दर(R) = 13%, समय = 5 वर्ष<br>साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>650 = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>13</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>मूलधन(P) = 1000</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. Which of the following is the oldest Nuclear research reactor of India ?</p>",
                    question_hi: "<p>81. निम्नलिखित में से कौन भारत का सबसे पुराना परमाणु अनुसंधान रिएक्टर है ?</p>",
                    options_en: ["<p>Kamini</p>", "<p>Cirus</p>", 
                                "<p>Apsara</p>", "<p>Dhruva</p>"],
                    options_hi: ["<p>कामिनी</p>", "<p>साइरस</p>",
                                "<p>अप्सरा</p>", "<p>ध्रुव</p>"],
                    solution_en: "<p>81.(c) <strong>Apsara.</strong> The reactor was designed by the Bhabha Atomic Research Center (BARC) and built with assistance from the United Kingdom. <strong>Kamini </strong>(Kalpakkam Mini reactor) - Kalpakkam, Tamil Nadu. The <strong>Cirus</strong> research reactor - Trombay near Mumbai. <strong>Dhruva</strong> - Mumbai (largest reactor).</p>",
                    solution_hi: "<p>81.(c) <strong>अप्सरा।</strong> रिएक्टर को भाभा परमाणु अनुसंधान केंद्र (BARC) द्वारा डिजाइन किया गया था और यूनाइटेड किंगडम की सहायता से बनाया गया था।<strong> कामिनी</strong> (कलपक्कम मिनी रिएक्टर) - कलपक्कम, तमिलनाडु। <strong>साइरस </strong>अनुसंधान रिएक्टर - मुंबई के पास ट्रॉम्बे। ध्रुव - मुंबई (सबसे बड़ा रिएक्टर)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. If it was Monday on 12<sup>th</sup> August in a particular year, then what will be the day on 26<sup>th</sup> September of the same year?</p>",
                    question_hi: "<p>82. यदि किसी विशिष्ट वर्ष में 12 अगस्त को सोमवार था, तो उसी वर्ष में 26 सितंबर को कौन सा दिन होगा ?</p>",
                    options_en: ["<p>Wednesday</p>", "<p>Tuesday</p>", 
                                "<p>Thursday</p>", "<p>Friday</p>"],
                    options_hi: ["<p>बुधवार</p>", "<p>मंगलवार</p>",
                                "<p>गुरुवार</p>", "<p>शुक्रवार</p>"],
                    solution_en: "<p>82.(c) <br>Total number of days between 12<sup>th</sup> August and 26<sup>th</sup> September in the same year<br>= 19 + 26 = 45 days<br>So total odd remaining days = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rArr; 3<br>Hence, ? = Monday + 3 =<strong> Thursday</strong></p>",
                    solution_hi: "<p>82.(c) <br>एक ही वर्ष में 12 अगस्त और 26 सितंबर के बीच दिनों की कुल संख्या<br>= 19 + 26 = 45 दिन<br>अतः, कुल विषम शेष दिन = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rArr; 3<br>इसलिए ? = सोमवार + 3 = <strong>गुरुवार</strong></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. A man borrows Rs. 2,460 to be paid back with compound interest at the rate of 5% p.a. By the end of 2 years, in equal installments. How much will each installment be?</p>",
                    question_hi: "<p>83. एक आदमी 5% प्रति वर्ष की दर से चक्रवृद्धि ब्याज के साथ वापस भुगतान करने के लिए 2,460 रुपये उधार लेता है। 2 वर्ष के अंत तक, समान किश्तों में, प्रत्येक किश्त कितनी होगी?</p>",
                    options_en: ["<p>Rs. 1,275</p>", "<p>Rs. 1,283</p>", 
                                "<p>Rs. 1,377</p>", "<p>Rs. 1,323</p>"],
                    options_hi: ["<p>Rs. 1,275</p>", "<p>Rs. 1,283</p>",
                                "<p>Rs. 1,377</p>", "<p>Rs. 1,323</p>"],
                    solution_en: "<p>83.(d)<br>R = 5% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; P&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A<br>for 1st year&nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; 21 <br>for 2nd year&nbsp; &nbsp;400&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; 441 <br>as interest is same for both the years <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 420&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;441<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 400&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;441<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;_______________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong> 820&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp; 862</strong> <br>now 820 &rarr; 2460<br>1 &rarr;&nbsp; 3<br>installments = 441 &times; 3 = 1,323</p>",
                    solution_hi: "<p>83.(d)<br>R = 5% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;P&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; A<br>प्रथम वर्ष के लिए&nbsp; &nbsp;20&nbsp; &nbsp;<strong>&rarr;</strong>&nbsp; &nbsp; &nbsp; 21 <br>दूसरे वर्ष के लिए&nbsp; &nbsp;400 <strong>&rarr;&nbsp; &nbsp; </strong>&nbsp;441 <br>क्योंकि ब्याज दोनों वर्षों के लिए समान है<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 420&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;441<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 400&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;441<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;_______________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong> 820&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp; 862</strong><br>अब 820 <strong>&rarr; </strong>&nbsp;2460<br>1 <strong>&rarr;</strong>&nbsp;3<br>किश्त = 441 &times; 3 = 1,323</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. How many electrons does a hydrogen molecule have in its K shell?</p>",
                    question_hi: "<p>84. हाइड्रोजन अणु के K कोश में कितने इलेक्ट्रॉ न होते हैं?</p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>3</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>1</p>", "<p>3</p>"],
                    solution_en: "<p>84.(c) <strong>1</strong>. A hydrogen molecule (H<sub>2</sub>) consists of two hydrogen atoms, each with one electron. Since hydrogen has only one electron in its K shell, a hydrogen molecule will have two electrons in total in the K shell, one from each atom.</p>",
                    solution_hi: "<p>84.(c) <strong>1</strong>. हाइड्रोजन अणु (H<sub>2</sub>) में दो हाइड्रोजन परमाणु होते हैं, जिनमें से प्रत्येक में एक इलेक्ट्रॉन होता है। चूँकि हाइड्रोजन के K शेल में केवल एक इलेक्ट्रॉन होता है, इसलिए हाइड्रोजन अणु में K शेल में कुल दो इलेक्ट्रॉन होंगे, प्रत्येक परमाणु से एक- एक।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. The difference between the mean of first 5 composite numbers and the mean of the ﬁrst ﬁve prime numbers is</p>",
                    question_hi: "<p>85. पहली 5 भाज्य संख्याओं के माध्य और पहली पाँच अभाज्य संख्याओं के माध्य के बीच का अंतर कितना है?</p>",
                    options_en: ["<p>1.8</p>", "<p>2.4</p>", 
                                "<p>1.6</p>", "<p>2.6</p>"],
                    options_hi: ["<p>1.8</p>", "<p>2.4</p>",
                                "<p>1.6</p>", "<p>2.6</p>"],
                    solution_en: "<p>85.(a) First 5 composite numbers = 4, 6, 8, 9 and 10.<br>Mean of first 5 composite numbers = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>10</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>= <math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>First 5 prime numbers are = 2, 3, 5, 7 and 11.<br>Mean of first 5 prime numbers are = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>7</mn><mo>+</mo><mn>11</mn></mrow><mn>5</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>28</mn><mn>5</mn></mfrac></mstyle></math><br>So, the difference between the mean of first 5 composite numbers and the mean of the ﬁrst ﬁve prime numbers is = <math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>28</mn><mn>5</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>9</mn><mn>5</mn></mfrac></mstyle></math>= 1.8</p>",
                    solution_hi: "<p>85.(a) पहली 5 भाज्य संख्याएँ हैं 4, 6, 8, 9 और 10.<br>प्रथम 5 भाज्य संख्याओं का माध्य हैं = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>10</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>= <math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>प्रथम 5 अभाज्य संख्याएँ हैं, 2, 3, 5, 7 और 11<br>प्रथम 5 अभाज्य संख्याओं का माध्य है, = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>7</mn><mo>+</mo><mn>11</mn></mrow><mn>5</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>28</mn><mn>5</mn></mfrac></mstyle></math><br>तो, पहली 5 भाज्य संख्याओं के माध्य और पहली पाँच अभाज्य संख्याओं के माध्य के बीच का अंतर है,<br>= <math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>28</mn><mn>5</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>9</mn><mn>5</mn></mfrac></mstyle></math>= 1.8</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. E is older than C. D is older than C but younger than E. A is younger than B and C. C is older than B. Who is the youngest?</p>",
                    question_hi: "<p>86. E, C से बड़ा है। D, C से बड़ा है लेकिन E से छोटा है। A, B और C से छोटा है। C, B से बड़ा है। सबसे छोटा कौन है?</p>",
                    options_en: ["<p>A</p>", "<p>C</p>", 
                                "<p>B</p>", "<p>D</p>"],
                    options_hi: ["<p>A</p>", "<p>C</p>",
                                "<p>B</p>", "<p>D</p>"],
                    solution_en: "<p>86.(a) E &gt; D &gt; C &gt; B &gt; A<br>From the above arrangements, A is the youngest.</p>",
                    solution_hi: "<p>86.(a) E &gt; D &gt; C &gt; B &gt; A<br>उपरोक्त व्यवस्थाओं में से, A सबसे छोटा है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. Navroz festival is related to which religious community?</p>",
                    question_hi: "<p>87. नवरोज़ त्यौहार किस धार्मिक समुदाय से संबंधित है ?</p>",
                    options_en: ["<p>Parsi</p>", "<p>Buddhist</p>", 
                                "<p>Jain</p>", "<p>Sikh</p>"],
                    options_hi: ["<p>पारसी</p>", "<p>बौद्ध</p>",
                                "<p>जैन</p>", "<p>सिख</p>"],
                    solution_en: "<p>87.(a) <strong>Parsi </strong>- Navroz, Khordad Sal. Parsis are worshipers of fire. Especially on Navroz, they keep a burning fire surrounded by water and wheat. The Parsis celebrate their New Year or Navroz on August 17. The word Navroz literally means &lsquo;new day&rsquo;. <strong>Buddhist</strong> - Buddha Purnima, Losar, Hemis, Ullambana. <strong>Jain</strong> - Jain Paryushan, Mahavir Jayanti, Varshi Tapa. <strong>Sikh</strong> - Baisakhi, Lohri, Guru Nanak Jayanti.</p>",
                    solution_hi: "<p>87.(a) <strong>पारसी</strong> - नवरोज, खोरदाद साल। पारसी अग्नि के उपासक होते हैं। खासकर नवरोज पर वे पानी और गेहूं से घिरी जलती हुई आग रखते हैं। पारसी अपना नया साल या नवरोज 17 अगस्त को मनाते हैं। नवरोज शब्द का शाब्दिक अर्थ है \'नया दिन\'। <strong>बौद्ध</strong> - बुद्ध पूर्णिमा, लोसार, हेमिस, उल्लम्बन। <strong>जैन</strong> - जैन पर्यूषण, महावीर जयंती, वर्षि तप। <strong>सिख</strong> - बैसाखी, लोहड़ी, गुरु नानक जयंती।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. If the average age of A, B and C is 22 years and the average age of B and C is 25 years. Then Find A&rsquo;s age after 9 years.</p>",
                    question_hi: "<p>88. यदि A, B और C की औसत आयु 22 वर्ष है और B और C की औसत आयु 25 वर्ष है। तो 9 वर्ष बाद A की आयु ज्ञात कीजिए।</p>",
                    options_en: ["<p>35 years</p>", "<p>25 years</p>", 
                                "<p>45 years</p>", "<p>50 years</p>"],
                    options_hi: ["<p>35 वर्ष</p>", "<p>25 वर्ष</p>",
                                "<p>45 वर्ष</p>", "<p>50 वर्ष</p>"],
                    solution_en: "<p>88.(b)<br>Sum of ages of A, B and C = 22 &times; 3 = 66 years<br>Sum of ages of B and C = 25 &times;&nbsp;2 = 50 years<br>Age of A = 66 - 50 = 16 years<br>Age of A after 9 years = 16 + 9 = 25 years.</p>",
                    solution_hi: "<p>88.(b)<br>A, B और C की आयु का योग = 22 &times; 3 = 66 वर्ष<br>B और C की आयु का योग = 25 &times; 2 = 50 वर्ष<br>A की आयु = 66 - 50 = 16 वर्ष<br>9 वर्ष बाद A की आयु = 16 + 9 = 25 वर्ष।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. A given resistance R is cut into 2 equal parts. Find the resistance of each part.</p>",
                    question_hi: "<p>89. किसी दिए गए प्रतिरोध R को 2 बराबर भागों में काटा जाता है। प्रत्येक भाग का प्रतिरोध ज्ञात कीजिए ।</p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>4</mn></mfrac></math></p>", "<p>R</p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>2</mn></mfrac></math></p>", "<p>2R</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>4</mn></mfrac></math></p>", "<p>R</p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>2</mn></mfrac></math></p>", "<p>2R</p>"],
                    solution_en: "<p>89.(c) <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">R</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math><strong>. The Resistance (R)</strong> has been cut into two equal parts, then the<strong> resistance</strong> of each part&nbsp;<math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">R</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math>. In a <strong>series circuit</strong> - current is the same, The equivalent resistance in a series circuit is equal to the algebraic sum of the individual resistances. In a <strong>parallel circuit</strong> - potential drop is the same, The equivalent resistance in a parallel circuit is lower than the value of lowest resistance in the combination.</p>",
                    solution_hi: "<p>89.(c) <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">R</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math>. <strong>प्रतिरोध (R)</strong> को दो बराबर भागों में काट दिया गया है, तो प्रत्येक भाग का प्रतिरोध&nbsp;<math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">R</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> है। <strong>श्रेणी परिपथ</strong> में - धारा समान होती है, श्रेणी परिपथ में समतुल्य प्रतिरोध अलग-अलग प्रतिरोधों के बीजगणितीय योग के बराबर होता है।। <strong>समांतर परिपथ </strong>में - विभव पात समान होती है, समतुल्य प्रतिरोध संयोजन में सबसे कम प्रतिरोध के मान से कम होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>91. Observe the figure given and answer the questions below</p>\n<p dir=\"ltr\"><strong id=\"docs-internal-guid-6ba80e47-7fff-7633-7848-d4f8a45af388\"></strong><strong id=\"docs-internal-guid-c2932734-7fff-52d8-f3c0-a56ecd94c832\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf7AadK2414NqmNlnNyhlVGQ6uGrSrAB53Q6poSt4yzvX3tz9BjNj6FzH9u8pUvuCRl3kVJCylEdo2r6TZpC2LOP4b55MdVnV4yTeAgTY7CZqmZF1IcFjB_qhO_etHdJNvXAEoF5wd2xaPfTcTjZFuvxpeL?key=kEXOmwJIBUsVVfNisHT32w\" width=\"143\" height=\"133\"></strong></p>\n<p dir=\"ltr\">If the total number of students is 120 and the number of students is distributed equally across all the subjects, how many students study languages?</p>",
                    question_hi: "<p>90. दिए गए चित्र को ध्यान से देखें और नीचे दिए गए प्रश्नों के उत्तर दें<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101268.png\" alt=\"rId54\" width=\"120\" height=\"112\"> <br>यदि छात्रों की कुल संख्या 120 है और छात्रों की संख्या सभी विषयों में समान रूप से वितरित की जाती है, तो कितने छात्र भाषाओं का अध्ययन करते हैं?</p>",
                    options_en: ["<p>36</p>", "<p>12</p>", 
                                "<p>24</p>", "<p>48</p>"],
                    options_hi: ["<p>36</p>", "<p>12</p>",
                                "<p>24</p>", "<p>48</p>"],
                    solution_en: "<p>90.(d)<br>Total no. of students = 120<br>There are 5 subjects and the no. of students distributed is equal.<br><math display=\"inline\"><mo>&#8756;</mo></math> no. of students in each subject = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 24<br>There are two languages Hindi and English,<br>so, no. of students study languages = 2 &times; 24 = 48</p>",
                    solution_hi: "<p>90.(d)<br>कुल संख्या छात्रों का = 120<br>5 विषय हैं और वितरित छात्रों की संख्या बराबर है।<br>&there4; प्रत्येक विषय में विद्यार्थियों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 24<br>हिंदी और अंग्रेजी दो भाषाएं हैं,<br>इसलिए, भाषा पढ़ने वाले छात्रों की संख्या = 2 &times; 24 = 48</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p dir=\"ltr\">91. &nbsp;The potential drop across the 4&Omega; resistor in the given circuit is:</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfwQ6bUEpUtXYBdn7XIlqmDgk9Rn9OmhquVyT1cpYFy-knhFks3E5-MUchRDWbCQNPHU21dIWm2tXjUJXahp-M2uBGs6qDRZaPm7MDyghYItPpJZMyn_iNGUyUesRih5LY4Pnkxau2fc4dsHdjdXb2ymAhO?key=kEXOmwJIBUsVVfNisHT32w\" width=\"196\" height=\"120\"></p>\n<p>&nbsp;</p>",
                    question_hi: "<p>91. दिए गए परिपथ में 4 &Omega; प्रतिरोध के टर्मिनलों के बीच विभव पात ज्ञात कीजिये।। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101368.png\" alt=\"rId55\" width=\"221\" height=\"132\"></p>",
                    options_en: ["<p>2V</p>", "<p>5V</p>", 
                                "<p>3V</p>", "<p>0.5V</p>"],
                    options_hi: ["<p>2V</p>", "<p>5V</p>",
                                "<p>3V</p>", "<p>0.5V</p>"],
                    solution_en: "<p>91.(a)<strong> 2V. The total Resistance, </strong>R<sub>net</sub>&nbsp;= R<sub>1</sub> + R<sub>2</sub> = 4 &Omega; + 6 &Omega; = 10 &Omega;. <strong>The total Current</strong> in the circuit, I = V/R = 5 volt / 10 &Omega; = &frac12; A. <strong>The Potential drop</strong> against Resistance 4 &Omega; is given as V= IR = &frac12; &times; 4 = <strong>2V.</strong></p>",
                    solution_hi: "<p>91.(a)<strong> 2V । कुल प्रतिरोध, </strong>R<sub>net</sub>&nbsp;= R<sub>1</sub> + R<sub>2</sub>&nbsp;= 4 &Omega; + 6 &Omega; = 10 &Omega;. परिपथ में <strong>कुल विद्धुत धारा,</strong> I = V/R = 5 वोल्ट / 10 &Omega; = &frac12; A. प्रतिरोध 4 के विरुद्ध विभव पतन (Potential drop) इस प्रकार दी गई है V= IR = &frac12; &times; 4 = <strong>2V.</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. If all the numbers given below are arranged in descending order, then the position of how many numbers will remain unchanged?<br>295, 654, 653, 234, 345, 245, 634</p>",
                    question_hi: "<p>92. यदि नीचे दी गई सभी संख्याओं को अवरोही क्रम में व्यवस्थित किया जाए, तो कितनी संख्याओं का स्थान अपरिवर्तित रहेगा?<br>295, 654, 653, 234, 345, 245, 634</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>92.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101475.png\" alt=\"rId56\" width=\"197\" height=\"62\"><br>Position of only one number has remained unchanged.</p>",
                    solution_hi: "<p>92.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101607.png\" alt=\"rId57\" width=\"190\" height=\"65\"><br>केवल एक संख्या की स्थिति अपरिवर्तित है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. In Excel, what shortcut key is used to fill the selected cells with active cells to the right ?</p>",
                    question_hi: "<p>93. एक्सेल में दायीं ओर सक्रिय सेल वाले चयनित सेल को भरने के लिए किस शॉर्टकट कुंजी का उपयोग किया जाता है ?</p>",
                    options_en: ["<p>Ctrl + R</p>", "<p>Ctrl + V</p>", 
                                "<p>Ctrl + S</p>", "<p>Ctrl + D</p>"],
                    options_hi: ["<p>Ctrl + R</p>", "<p>Ctrl + V</p>",
                                "<p>Ctrl + S</p>", "<p>Ctrl + D</p>"],
                    solution_en: "<p>93.(a) <strong>Ctrl + R</strong>. Keyboard shortcuts are used for making selections and performing actions. <strong>Ctrl + V </strong>is used to paste the copied text. <strong>Ctrl + S</strong> is used to save any document or file open at that time. <strong>Ctrl + D</strong> adds the current webpage to your bookmarks or favorites list.</p>",
                    solution_hi: "<p>93.(a) <strong>Ctrl + R</strong> । कीबोर्ड शॉर्टकट का उपयोग चयन करने और कार्य निष्पादित करने के लिए किया जाता है। कॉपी किए गए टेक्स्ट को पेस्ट करने के लिए <strong>Ctrl + V </strong>का प्रयोग किया जाता है। <strong>Ctrl + S</strong> का प्रयोग उस समय खुले हुए किसी भी डॉक्यूमेंट या फाइल को सेव करने के लिए किया जाता है। <strong>Ctrl + D</strong> वर्तमान वेबपेज को आपकी बुकमार्क या पसंदीदा सूची में जोड़ता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. In the below graph, data is given for paper production in different years by 3 companies X, Y and Z.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101701.png\" alt=\"rId58\" width=\"237\" height=\"148\"> <br>If in the year 2000, the production increased by 5% in company X and company Y, then what would be the % increase in total production as compared to the total production reported in the graph above for the same year?</p>",
                    question_hi: "<p>94. नीचे दिए गए आलेख में, 3 कंपनियों X, Y और Z द्वारा अलग-अलग वर्षों में कागज उत्पादन के लिए डेटा दिया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101701.png\" alt=\"rId58\" width=\"231\" height=\"144\"> <br>यदि वर्ष 2000 में, कंपनी X और कंपनी Y में उत्पादन में 5% की वृद्धि हुई, तो उसी वर्ष के लिए उपरोक्त आलेख में रिपोर्ट किए गए कुल उत्पादन की तुलना में कुल उत्पादन में कितने प्रतिशत की वृद्धि होगी?</p>",
                    options_en: ["<p>3.40%</p>", "<p>3.60%</p>", 
                                "<p>4. 60%</p>", "<p>4. 00%</p>"],
                    options_hi: ["<p>3.40%</p>", "<p>3.60%</p>",
                                "<p>4. 60%</p>", "<p>4. 00%</p>"],
                    solution_en: "<p>94.(b) <br>Total production of company X ,Y and Z in 2000 = 40 + 50 + 35 = 125<br>Total production after increment of 5% = 50 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>100</mn></mfrac></math> + 40 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>100</mn></mfrac></math> + 35<br>= 52.5 + 42 + 35 = 129.5<br>Change in production = 129.5 - 125 = 4.5<br>% increase in production = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow><mn>125</mn></mfrac></math> &times; 100 = 3.60%</p>",
                    solution_hi: "<p>94.(b) <br>2000 में कंपनी X ,Y और Z का कुल उत्पादन = 40 + 50 + 35 = 125<br>5% की वृद्धि के बाद कुल उत्पादन = 50 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>100</mn></mfrac></math> + 40 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>100</mn></mfrac></math> + 35<br>= 52.5 + 42 + 35 = 129.5<br>उत्पादन में परिवर्तन = 129.5 - 125 = 4.5<br>उत्पादन में % वृद्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow><mn>125</mn></mfrac></math> &times; 100 = 3.60%</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. Rohan lives in a seven-storeyed building. The top floor of the building is numbered as seven and the lowermost floor is numbered as one. Diya who is Rohan\'s friend lives somewhere above the floor where Rohan lives. Every Sunday Rohan goes two floors below to his colleague Atul\'s flat to eat and drink together. If there are three floors between Rohan\'s and Diya\'s floor, on which floor does Rohan live?</p>",
                    question_hi: "<p>95. रोहन एक सात मंजिला इमारत में रहता है। इमारत की सबसे ऊपरी मंजिल की संख्या सात है और सबसे निचली मंजिल की संख्या एक है। दीया, जो रोहन की दोस्त है, उस मंजिल के ऊपर कहीं रहती है जहां रोहन रहता है। हर रविवार को रोहन दो मंजिल नीचे अपने सहयोगी अतुल के फ्लैट में साथ खाने-पीने जाता है। यदि रोहन और दीया के मंजिलो के बीच तीन मंजिल हैं, तो रोहन किस मंजिल पर रहता है?</p>",
                    options_en: ["<p>Third floor</p>", "<p>First floor</p>", 
                                "<p>Second floor</p>", "<p>Fourth floor</p>"],
                    options_hi: ["<p>तीसरी मंजिल</p>", "<p>पहली मंजिल</p>",
                                "<p>दूसरी मंजिल</p>", "<p>चौथी मंजिल</p>"],
                    solution_en: "<p>95.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101805.png\" alt=\"rId59\" width=\"140\" height=\"125\"><br>From above table we can clearly see that,<br><strong>Rohan lives on 3rd floor.</strong></p>",
                    solution_hi: "<p>95.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728365101805.png\" alt=\"rId59\" width=\"141\" height=\"126\"><br>उपरोक्त तालिका से हम स्पष्ट रूप से देख सकते हैं कि<strong>,रोहन तीसरी मंजिल पर रहता</strong> है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. When was the WTO (World Trade Organization) established ?</p>",
                    question_hi: "<p>96. WTO (विश्व व्यापार संगठन) की स्थापना कब हुई थी ?</p>",
                    options_en: ["<p>1983</p>", "<p>1999</p>", 
                                "<p>1948</p>", "<p>1995</p>"],
                    options_hi: ["<p>1983</p>", "<p>1999</p>",
                                "<p>1948</p>", "<p>1995</p>"],
                    solution_en: "<p>96.(d) <strong>1995 (1 January)</strong>. WTO (World Trade Organization): It is an intergovernmental organization that regulates and facilitates international trade. <strong>Headquarters</strong> - Geneva, Switzerland. <strong>Director-General</strong> - Ngozi Okonjo-Iweala (Nigeria, since 1 March 2021).</p>",
                    solution_hi: "<p>96.(d) <strong>1995 (1 जनवरी)।</strong> WTO (विश्व व्यापार संगठन) : यह एक अंतर्शासकीय संगठन है जो अंतर्राष्ट्रीय व्यापार को विनियमित और सुविधाजनक बनाता है। <strong>मुख्यालय </strong>- जिनेवा, स्विट्जरलैंड। <strong>महानिदेशक</strong> - न्गोजी ओकोन्जो-इवेला (नाइजीरिया, 1 मार्च 2021 से)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. In each of the given number-clusters, the number on the right side of = (the equal to sign) is calculated by performing certain mathematical operations on the two numbers on the left of = (the equal to sign). All three number-clusters follow the same pattern. Select the number from among the given options that can replace the question mark (?) in the third number-cluster.<br>12, 33 = 57<br>10, 28 = 48<br>14, 39 = ?</p>",
                    question_hi: "<p>97. दिए गए संख्या-समूहों में से प्रत्येक में, = (चिह्न के बराबर) के दाईं ओर की संख्या की गणना = (चिह्न के बराबर) के बाईं ओर दो संख्याओं पर कुछ गणितीय संचालन करके की जाती है। सभी तीन संख्या-समूह समान पैटर्न का अनुसरण करती हैं। दिए गए विकल्पों में से वह संख्या चुनिए जो तीसरे अक्षर समूह में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है।<br>12, 33 = 57<br>10, 28 = 48<br>14, 39 = ?</p>",
                    options_en: ["<p>67</p>", "<p>71</p>", 
                                "<p>57</p>", "<p>61</p>"],
                    options_hi: ["<p>67</p>", "<p>71</p>",
                                "<p>57</p>", "<p>61</p>"],
                    solution_en: "<p>97.(a)<br>1st &times; 2 + 2nd = Answer<br>12 &times; 2 + 33 = 57<br>10 &times; 2 + 28 = 48<br>Similarly, <br>14 &times; 2 + 39 = 67</p>",
                    solution_hi: "<p>97.(a)<br>पहला &times; 2 + दूसरा = उत्तर<br>12 &times; 2 + 33 = 57<br>10 &times; 2 + 28 = 48<br>उसी प्रकार,<br>14 &times; 2 + 39 = 67</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. If mean is 40 and standard deviation is 5 then C.V (Coefficient of variation) is</p>",
                    question_hi: "<p>98. यदि माध्य 40 है और मानक विचलन 5 है तो C.V (भिन्नता गुणांक) कितना होगा ?</p>",
                    options_en: ["<p>5.5%</p>", "<p>20.5%</p>", 
                                "<p>100%</p>", "<p>12.5%</p>"],
                    options_hi: ["<p>5.5%</p>", "<p>20.5%</p>",
                                "<p>100%</p>", "<p>12.5%</p>"],
                    solution_en: "<p>98.(d) We know, the formula of coefficient of variation = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#963;</mi><mi>&#956;</mi></mfrac></math> &times; 100<br>Where <math display=\"inline\"><mi>&#963;</mi></math> = standard deviation of the given data set and &mu; = mean of the given data set.<br>Now, mean is 40 and standard deviation is 5, then C.V (Coefficient of variation) is <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>40</mn></mfrac></math> &times; 100%</p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> &times; 100%</p>\n<p>= 12.5 %</p>",
                    solution_hi: "<p>98.(d) हम जानते हैं, विचरण के गुणांक का सूत्र = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#963;</mi><mi>&#956;</mi></mfrac></math> &times; 100<br>जहाँ <math display=\"inline\"><mi>&#963;</mi></math> = दिए गए डेटा सेट का मानक विचलन और &mu; = दिए गए डेटा सेट का माध्य<br>अब माध्य 40 है और मानक विचलन 5 है, तब C.V. (विचरण का गुणांक)</p>\n<p>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>40</mn></mfrac></math> &times; 100%</p>\n<p>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> &times; 100%</p>\n<p>= 12.5 %</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. Which of the following solid compounds is formed when lead nitrate is heated?</p>",
                    question_hi: "<p>99. लेड नाइट्रेट को गर्म करने पर इनमें से कौन सा ठोस यौगिक बनता है?</p>",
                    options_en: ["<p>Pb(OH)<sub>2</sub></p>", "<p>Pbo</p>", 
                                "<p>Pb(CO<sub>3</sub>)<sub>2</sub></p>", "<p>Pb</p>"],
                    options_hi: ["<p>Pb(OH)<sub>2</sub></p>", "<p>Pbo</p>",
                                "<p>Pb(CO<sub>3</sub>)<sub>2</sub></p>", "<p>Pb</p>"],
                    solution_en: "<p>99.(b) <strong>PbO. 2Pb(NO<sub>3</sub>)<sub>2 </sub>(s)Lead nitrate (Heat) &rarr; 2PbO(s)Lead oxide + 4NO<sub>2</sub>(g)Nitrogen dioxide + O<sub>2</sub>(g)Oxygen</strong>. Lead (Pb): Atomic number (82), Atomic mass (207.2). <strong>Properties </strong>- low melting point, ease of casting, high density, low strength, ease of fabrication, acid resistance. Uses - batteries, gas sensors, pigments, ceramics, and glass industry. Pb(OH)<sub>2</sub> - Lead hydroxide, Pb(CO<sub>3</sub>)<sub>2</sub> - Lead(II) carbonate.</p>",
                    solution_hi: "<p>99.(b) <strong>PbO । 2Pb(NO<sub>3</sub>)<sub>2</sub>(s)लेड नाइट्रेट (ऊष्मा)&rarr; 2PbO(s) लेड ऑक्साइड + 4NO<sub>2</sub>(g)नाइट्रोजन डाइऑक्साइड + O<sub>2</sub>(g)ऑक्सीजन</strong> । लेड (Pb): परमाणु संख्या (82), परमाणु द्रव्यमान (207.2)। गुण - कम गलनांक, कास्टिंग में आसान, उच्च घनत्व, कम शक्ति, निर्माण में आसानी, अम्ल प्रतिरोध। <strong>उपयोग</strong> - बैटरी, गैस सेंसर, पिगमेंट, सिरेमिक और ग्लास उद्योग। Pb(OH)<sub>2</sub> - लेड हाइड्रॉक्साइड, Pb(CO<sub>3</sub>)<sub>2</sub> - लेड (II) कार्बोनेट।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. If each of the two numbers 5<sup>16</sup>&nbsp;and 5<sup>25</sup> are divided by 6, the remainder are R<sub>1</sub> and R<sub>2</sub>, respectively. What is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>R</mi><mn>1</mn></msub><mo>+</mo><msub><mi>R</mi><mn>2</mn></msub></mrow><msub><mi>R</mi><mn>2</mn></msub></mfrac></math> ?</p>",
                    question_hi: "<p>100. यदि दो संख्याओं 5<sup>16</sup>&nbsp;और 5<sup>25</sup> में से प्रत्येक को 6 से विभाजित किया जाता है, तो शेष&nbsp;क्रमशः R<sub>1</sub>&nbsp;और R<sub>2</sub> हैं। <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>R</mi><mn>1</mn></msub><mo>+</mo><msub><mi>R</mi><mn>2</mn></msub></mrow><msub><mi>R</mi><mn>2</mn></msub></mfrac></math> का मान क्या है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>100.(d)<br>On dividing 5 by 6 we get R = 5 or -1.<br>For ease of calculation we use R = -1.<br>R<sub>1</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>5</mn><mn>16</mn></msup><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>16</mn></msup><mn>6</mn></mfrac></math>, R<sub>1</sub> = 1<br>Again, R<sub>2</sub>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>5</mn><mn>25</mn></msup><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>25</mn></msup><mn>6</mn></mfrac></math> = R<sub>2</sub> = -1 = 6 - 1 = 5<br>Now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>R</mi><mn>1</mn></msub><mo>+</mo><mi>&#160;</mi><msub><mi>R</mi><mn>2</mn></msub></mrow><msub><mi>R</mi><mn>2</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>5</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math>.</p>",
                    solution_hi: "<p>100.(d)<br>5 को 6 से भाग देने पर हमें R = 5 या -1 प्राप्त होता है।<br>गणना में आसानी के लिए हम R = -1 का उपयोग करते हैं।<br>R<sub>1</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>5</mn><mn>16</mn></msup><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>16</mn></msup><mn>6</mn></mfrac></math>, R<sub>1</sub> = 1<br>फिर से, R<sub>2</sub>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>5</mn><mn>25</mn></msup><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>25</mn></msup><mn>6</mn></mfrac></math> = R<sub>2</sub> = -1 = 6 - 1 = 5<br>अब, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>R</mi><mn>1</mn></msub><mo>+</mo><mi>&#160;</mi><msub><mi>R</mi><mn>2</mn></msub></mrow><msub><mi>R</mi><mn>2</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>5</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math>.</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>