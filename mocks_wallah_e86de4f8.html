<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. If &ldquo;X + Y&rsquo; means &lsquo;X is the brother of Y&rsquo; , &lsquo;X &times; Y&rdquo; means &lsquo;X is the father of Y&rsquo; , &ldquo;X / Y&rsquo; means &lsquo;X is the mother of Y&rsquo; , then which of the following would mean R is the son of S ?</p>",
                    question_hi: "<p>1. यदि \"X + Y\' का अर्थ \'X, Y का भाई है\', \'X &times; Y\' का अर्थ \'X, Y का पिता है\', \"X/Y\' का अर्थ \'X, Y की माता है\', तो निम्नलिखित में से किसका अर्थ होगा R, S का पुत्र है?</p>",
                    options_en: ["<p>S &times; T &times; R</p>", "<p>S/T/R</p>", 
                                "<p>S &times; R + T</p>", "<p>S + R &times; T</p>"],
                    options_hi: ["<p>S &times; T &times; R</p>", "<p>S/T/R</p>",
                                "<p>S &times; R + T</p>", "<p>S + R &times; T</p>"],
                    solution_en: "<p>1.(c) S &times; R + T<br>S is the father of R, R is the brother of T<br>From the above statement, it is clear that R is the son of S.</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>1.(c) S &times; R + T<br>S, R का पिता है, R, T का भाई है<br>उपरोक्त कथन से यह स्पष्ट है कि R, S का पुत्र है।</p>\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. When was the Dowry Prohibition Act commenced?</p>",
                    question_hi: "<p>2. दहेज निषेध अधिनियम कब शुरू किया गया था?</p>",
                    options_en: ["<p>1965</p>", "<p>1961</p>", 
                                "<p>1963</p>", "<p>1960</p>"],
                    options_hi: ["<p>1965</p>", "<p>1961</p>",
                                "<p>1963</p>", "<p>1960</p>"],
                    solution_en: "<p>2.(b) <strong>1961.</strong> This Act is intended to prevent the giving or receiving of a dowry. Under the Dowry Prohibition Act, dowry includes property, goods, or money given by either party to the marriage, by the parents of either party, or by anyone else in connection with the marriage. The Dowry Prohibition Act applies to persons of all religions in India.</p>",
                    solution_hi: "<p>2.(b) <strong>1961 ।</strong> इस अधिनियम का उद्देश्य दहेज देने या लेने पर रोक लगाना है। दहेज निषेध अधिनियम के तहत, दहेज में विवाह के किसी भी पक्ष द्वारा, किसी भी पक्ष के माता-पिता द्वारा, या विवाह के संबंध में किसी अन्य द्वारा दी गई संपत्ति, सामान या धन शामिल है। दहेज निषेध अधिनियम भारत में सभी धर्मों के व्यक्तियों पर लागू होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Select the number that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675991550.png\" alt=\"rId4\" width=\"113\" height=\"71\"></p>",
                    question_hi: "<p>3. उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675991550.png\" alt=\"rId4\" width=\"113\" height=\"71\"></p>",
                    options_en: ["<p>6</p>", "<p>5</p>", 
                                "<p>3</p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p>5</p>",
                                "<p>3</p>", "<p>8</p>"],
                    solution_en: "<p>3.(d) <strong>Logic row wise :-</strong> <br>[(R<sub>3</sub> - R<sub>4</sub>) &times; R<sub>2</sub>] = R<sub>1</sub><br>In 1st row &rArr; [ (15 - 11) &times; 4 ] &rArr; 4 &times; 4 = 16<br>In 2nd row &rArr; [ (13 - 7) &times; 6 ] &rArr; 6 &times; 6 = 36<br>In 3rd row &rArr; [ (11 - 3) &times; x ] = 64<br>&rArr; 8x = 64<br><strong>&rArr; x = 8</strong></p>",
                    solution_hi: "<p>3.(d) <strong>तर्क पंक्ति के अनुसार :-</strong><br>[(R<sub>3</sub> - R<sub>4</sub>) &times; R<sub>2</sub>] = R<sub>1</sub><br>पहली पंक्ति में &rArr; [ (15 - 11) &times; 4 ]&rArr; 4 &times; 4 = 16<br>दूसरी पंक्ति में &rArr; [ (13 - 7) &times; 6 ] &rArr; 6 &times; 6 = 36<br>तीसरी पंक्ति में &rArr; [ (11 - 3) &times; x ] = 64<br>&rArr; 8x = 64<br>&rArr;<strong> x = 8</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. When was India\'s first satellite the Aryabhata spacecraft launched?</p>",
                    question_hi: "<p>4. भारत का पहला उपग्रह आर्यभट्ट अंतरिक्ष यान कब लॉन्च किया गया था ?</p>",
                    options_en: ["<p>April 19, 1975</p>", "<p>April 19, 1976</p>", 
                                "<p>June 10, 1979</p>", "<p>June 10, 1980</p>"],
                    options_hi: ["<p>19 अप्रैल 1975</p>", "<p>19 अप्रैल 1976</p>",
                                "<p>10 जून 1979</p>", "<p>10 जून 1980</p>"],
                    solution_en: "<p>4.(a) <strong>April 19, 1975</strong>. It was completely designed and fabricated in India and launched by a <strong>Soviet Kosmos-3M rocket</strong> from <strong>Kapustin Yar </strong>(Russia) on April 19, 1975. <strong>Dr. Vikram Sarabhai</strong> is the father of the Indian Space Program. First education satellite launched by ISRO is <strong>EDUSAT</strong> (GSAT-3).</p>",
                    solution_hi: "<p>4.(a) <strong>19 अप्रैल, 1975</strong> । इसे पूरी तरह से भारत में डिजाइन और निर्मित किया गया था और 19 अप्रैल, 1975 को <strong>कपुस्टिन या</strong>र (रूस) से <strong>सोवियत कोसमोस-3M रॉकेट</strong> द्वारा लॉन्च किया गया था। <strong>डॉ. विक्रम साराभाई,</strong> भारतीय अंतरिक्ष कार्यक्रम के जनक हैं। ISRO द्वारा लॉन्च किया गया पहला शैक्षणिक उपग्रह <strong>EDUSAT</strong> (GSAT-3) है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. X and Y can complete a work in 9 days and 36 days, respectively. X begins to do the work and they work alternately one at a time for one day each. The whole work will be complete in:</p>",
                    question_hi: "<p>5. X और Y एक कार्य को क्रमश: 9 दिन और 36 दिन में पूरा कर सकते हैं। X कार्य करना शुरू करता है और वे बारी-बारी से एक-एक करके एक-एक दिन कार्य करते हैं। संपूर्ण कार्य कितने दिन में पूरा होगा?</p>",
                    options_en: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&nbsp;days</p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>days</p>", 
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>days</p>", "<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>days</p>"],
                    options_hi: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>दिन</p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>दिन</p>",
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>दिन</p>", "<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>दिन</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675991673.png\" alt=\"rId5\" width=\"237\" height=\"131\"><br>According to question,<br>Work done in 1 cycle ( 2 days) = 4 + 1 = 5 units<br>Work done in 7 cycle ( 14 days) = 5 &times; 7 = 35 units<br>Remaining work = 36 -&nbsp;35 = 1 unit<br>Time Required to complete remaining work = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days<br>Total time = 14 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>= 14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675991796.png\" alt=\"rId6\" width=\"217\" height=\"137\"><br>प्रश्न के अनुसार,<br>1 चक्र (2 दिन) में किया गया कार्य = 4 + 1 = 5 इकाई<br>7 चक्र (14 दिन) में किया गया कार्य = 5 &times; 7 = 35 इकाई<br>शेष कार्य = 36 -&nbsp;35 = 1 इकाई<br>शेष कार्य को पूरा करने के लिए आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> दिन<br>कुल समय = 14 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>= 14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>&nbsp;दिन</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. How many members are there in Rajya Sabha?</p>",
                    question_hi: "<p>6. राज्यसभा में कितने सदस्य होते हैं ?</p>",
                    options_en: ["<p>240</p>", "<p>230</p>", 
                                "<p>225</p>", "<p>250</p>"],
                    options_hi: ["<p>240</p>", "<p>230</p>",
                                "<p>225</p>", "<p>250</p>"],
                    solution_en: "<p>6.(d)<strong> 250.</strong> The Rajya Sabha or Council of states is the upper house of the parliament. 238 members represent the States and Union Territories and <strong>12</strong> members are nominated by the President. It is a permanent body and is not subject to dissolution. At present there are <strong>245 </strong>members in Rajya sabha. However, <strong>one third</strong> of the members retire every <strong>second year</strong> and are replaced by newly elected members. Each member is elected for a term of <strong>six years.</strong></p>",
                    solution_hi: "<p>6.(d) <strong>250</strong> । राज्यसभा या राज्यों की परिषद संसद का उच्च सदन है। जिनमें से<strong> 238</strong> सदस्य राज्यों और केंद्र शासित प्रदेशों के प्रतिनिधि हैं और <strong>12</strong> सदस्य राष्ट्रपति द्वारा नामित होते हैं। यह एक स्थायी निकाय है और इसका विघटन नहीं होता है। वर्तमान में राज्यसभा में <strong>245</strong> सदस्य हैं। हालाँकि,<strong> एक तिहाई</strong> सदस्य प्रत्येक <strong>दूसरे वर्ष</strong> सेवानिवृत्त हो जाते हैं और उनके स्थान पर नव निर्वाचित सदस्य आते हैं। प्रत्येक सदस्य को <strong>6 वर्ष </strong>की अवधि के लिए चुना जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675991932.png\" alt=\"rId7\" width=\"113\" height=\"118\"></p>",
                    question_hi: "<p>7. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675991932.png\" alt=\"rId7\" width=\"113\" height=\"118\"> </p>",
                    options_en: ["<p>8</p>", "<p>6</p>", 
                                "<p>7</p>", "<p>5</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>7</p>", "<p>5</p>"],
                    solution_en: "<p>7.(c)&nbsp;7 triangles are as shown:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992082.png\" alt=\"rId8\" width=\"118\" height=\"125\"></p>",
                    solution_hi: "<p>7.(c)&nbsp;आकृति मे 7 त्रिभुज हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992082.png\" alt=\"rId8\" width=\"132\" height=\"140\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. Who was one of the co-founder of Ghadar Party?</p>",
                    question_hi: "<p>8. ग़दर पार्टी के सह-संस्थापक कौन थे?</p>",
                    options_en: ["<p>Gopal Krishan Gokhale</p>", "<p>Dada Bhai Naroji</p>", 
                                "<p>Har Dayal</p>", "<p>Lala Lajpatrai</p>"],
                    options_hi: ["<p>गोपाल कृष्ण गोखले</p>", "<p>दादा भाई नौरोजी</p>",
                                "<p>हर दयाल</p>", "<p>लाला लाजपतराय</p>"],
                    solution_en: "<p>8.(c) <strong>Har Dayal</strong> (Lala Har Dayal Singh Mathur). The main a<strong>im </strong>of the party was to free India by revolutionary activities. He was a polymath and his simple living and intellectual acumen inspired many expatriate Indians living in Canada and the U.S. in their campaign against British rule in India during the first world war. <strong>Notable works</strong> - Our Educational Problem, Thoughts on Education, Hints for Self Culture, Glimpses of World Religions and The Bodhisattva Doctrines in Buddhist Sanskrit Literature. (Founding president of the Party - <strong>Sohan Singh Bhakna</strong>).</p>",
                    solution_hi: "<p>8.(c) <strong>हर दयाल</strong> (लाला हर दयाल सिंह माथुर)। इस पार्टी का मुख्य उद्देश्य क्रांतिकारी गतिविधियों द्वारा भारत को स्वतंत्र कराना था। वह एक पॉलीमैथ (व्यापक ज्ञान वाला व्यक्ति) थे और उनके साधारण जीवन और बौद्धिक कौशल ने प्रथम विश्व युद्ध के दौरान भारत में ब्रिटिश शासन के खिलाफ अपने अभियान में कनाडा और अमेरिका में रहने वाले कई प्रवासी भारतीयों को प्रेरित किया। <strong>उल्लेखनीय कार्य</strong> - हमारी शैक्षिक समस्या, शिक्षा पर विचार, आत्म संस्कृति के लिए संकेत, विश्व धर्मों की झलक और बौद्ध संस्कृत साहित्य में बोधिसत्व सिद्धांत। (पार्टी के संस्थापक अध्यक्ष - <strong>सोहन सिंह भकना</strong>)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. If 3rd Oct 2011 is Monday, then what will be the day of the week on 4th Nov 2021 ?</p>",
                    question_hi: "<p>9. यदि 3 अक्टूबर 2011 को सोमवार है, तो 4 नवंबर 2021 को सप्ताह का कौन-सा दिन होगा ?</p>",
                    options_en: ["<p>Friday</p>", "<p>Tuesday</p>", 
                                "<p>Thursday</p>", "<p>Monday</p>"],
                    options_hi: ["<p>शुक्रवार</p>", "<p>मंगलवार</p>",
                                "<p>गुरुवार</p>", "<p>सोमवार</p>"],
                    solution_en: "<p>9.(c)<br>Number of odd days between 3rd Oct 2011 and 3rd Oct 2021 = 10 + 3 = 13 odd days<br>Number of odd days between 3rd Oct 2021 and 4th Nov 2021 =&nbsp;28 + 4 = 32 <br>Total number of odd days = 13 + 32 = 45 or (45 &divide; 7) = 3 odd day<br>&rArr; Monday + 3 = Thursday</p>",
                    solution_hi: "<p>9.(c)<br>3 अक्टूबर 2011 और 3 अक्टूबर 2021 के बीच विषम दिनों की संख्या = 10 + 3 = 13 विषम दिन<br>3 अक्टूबर 2021 और 4 नवंबर 2021 के बीच विषम दिनों की संख्या =&nbsp;28 + 4 = 32<br>कुल विषम दिनों की संख्या = 13 + 32 = 45 या (45 &divide; 7) = 3 विषम दिन<br>&rArr; सोमवार + 3 = गुरूवार</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. When was the Indian National Committee for Space Research formed?</p>",
                    question_hi: "<p>10. अंतरिक्ष अनुसंधान के लिए भारतीय राष्ट्रीय समिति का गठन कब किया गया था?</p>",
                    options_en: ["<p>1962</p>", "<p>1965</p>", 
                                "<p>1963</p>", "<p>1961</p>"],
                    options_hi: ["<p>1962</p>", "<p>1965</p>",
                                "<p>1963</p>", "<p>1961</p>"],
                    solution_en: "<p>10.(a) <strong>1962 </strong>(Pandit Jawaharlal Nehru). It comes under the Department of Atomic Energy. The aim of the Committee was to formulate India\'s space programme. Indian National Committee for Space Research (INCOSPAR) was dissolved and superseded by the Indian Space Research Organisation (ISRO) on 15 August 1969. <strong>ISRO</strong> (Headquarter - Bengaluru, founder- Vikram Sarabhai).</p>",
                    solution_hi: "<p>10.(a) <strong>1962 </strong>(पंडित जवाहरलाल नेहरू)। यह परमाणु ऊर्जा विभाग के अंतर्गत आता है। समिति का <strong>उद्देश्य</strong> भारत का अंतरिक्ष कार्यक्रम तैयार करना था। 15 अगस्त 1969 को भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) द्वारा भारतीय राष्ट्रीय अंतरिक्ष अनुसंधान समिति (INCOSPAR) को भंग कर दिया गया और इसका स्थान ले लिया।<strong> ISRO</strong> (मुख्यालय - बेंगलुरु, संस्थापक- विक्रम साराभाई)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. Select the option figure which is embedded in the given figure (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992232.png\" alt=\"rId9\" width=\"84\" height=\"63\"></p>",
                    question_hi: "<p>11. उस विकल्प आकृति का चयन कीजिए जो दी गई आकृति में सन्निहित है ( घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992232.png\" alt=\"rId9\" width=\"91\" height=\"68\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992334.png\" alt=\"rId10\" width=\"72\" height=\"53\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992435.png\" alt=\"rId11\" width=\"74\" height=\"55\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992586.png\" alt=\"rId12\" width=\"74\" height=\"34\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992713.png\" alt=\"rId13\" width=\"104\" height=\"19\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992334.png\" alt=\"rId10\" width=\"78\" height=\"57\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992435.png\" alt=\"rId11\" width=\"70\" height=\"52\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992586.png\" alt=\"rId12\" width=\"80\" height=\"37\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992713.png\" alt=\"rId13\" width=\"104\" height=\"19\"></p>"],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992839.png\" alt=\"rId14\" width=\"105\" height=\"78\"></p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675992839.png\" alt=\"rId14\" width=\"94\" height=\"70\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. When m is divided by 7, the remainder is 5. When 3m is divided by 7, the remainder is:</p>",
                    question_hi: "<p>12. जब m को 7 से विभाजित किया जाता है, तो शेषफल 5 होता है। जब 3m को 7 से विभाजित किया जाएगा, तो शेषफल क्या होगा?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>0</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>1</p>", "<p>0</p>"],
                    solution_en: "<p>12.(c)<br>According to question,<br><math display=\"inline\"><mfrac><mrow><mi>m</mi></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rarr; Remainder = 5<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>m</mi></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rarr; Remainder <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo></mrow><mn>7</mn></mfrac></math> = Remainder 1</p>",
                    solution_hi: "<p>12.(c)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mi>m</mi></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rarr; शेष = 5<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>m</mi></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rarr; शेष <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo></mrow><mn>7</mn></mfrac></math> = शेष 1</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. Symbol for methane is</p>",
                    question_hi: "<p>13. मीथेन के लिए क्या प्रतीक है?</p>",
                    options_en: ["<p>CH<sub>4</sub></p>", "<p>CH<sub>3</sub></p>", 
                                "<p>CH<sub>1</sub></p>", "<p>CH<sub>2</sub></p>"],
                    options_hi: ["<p>CH<sub>4</sub></p>", "<p>CH<sub>3</sub></p>",
                                "<p>CH<sub>1</sub></p>", "<p>CH<sub>2</sub></p>"],
                    solution_en: "<p>13.(a) <strong>CH<sub>4</sub></strong>. It is the simplest hydrocarbon, consisting of one carbon atom and four hydrogen atoms (CH<sub>4</sub>). It is flammable and is used as a fuel worldwide. It is a powerful greenhouse gas. The common sources of methane are oil and natural gas systems, agricultural activities, coal mining and wastes. It is the main constituent of natural gas.</p>",
                    solution_hi: "<p>13.(a) <strong>CH<sub>4</sub> </strong>। यह सबसे सरल हाइड्रोकार्बन है, जिसमें एक कार्बन परमाणु और चार हाइड्रोजन परमाणु (CH<sub>4</sub>) होते हैं। यह ज्वलनशील है और दुनिया भर में ईंधन के रूप में प्रयोग किया जाता है। यह एक शक्तिशाली ग्रीनहाउस गैस है। मीथेन के सामान्य स्रोत तेल और प्राकृतिक गैस प्रणालियाँ, कृषि गतिविधियाँ, कोयला खनन और अपशिष्ट हैं। यह प्राकृतिक गैस का मुख्य घटक है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14.&nbsp; Which one set of letters when sequentially placed at the gaps in the given letter series shall complete it ?<br><strong id=\"docs-internal-guid-d6228b33-7fff-5ec9-9e6d-7fab2aa7a659\"></strong>P_ RSPG_ SP_ LSPM_ S</p>",
                    question_hi: "<p>14. अक्षरों का कौन सा समूह दी गई अक्षर श्रृंखला में रिक्त स्थानों पर क्रमिक रूप से रखे जाने पर इसे पूरा करेगा?<br><strong id=\"docs-internal-guid-d906aca2-7fff-4b20-4e47-1826defda95b\"></strong>P_ RSPG_ SP_ LSPM_ S</p>",
                    options_en: ["<p>CWZO</p>", "<p>BWVQ</p>", 
                                "<p>ABXY</p>", "<p>QBWV</p>"],
                    options_hi: ["<p>CWZO</p>", "<p>BWVQ</p>",
                                "<p>ABXY</p>", "<p>QBWV</p>"],
                    solution_en: "<p>14.(d)<br>P<span style=\"text-decoration: underline;\"><strong>Q</strong></span>RS / PG<span style=\"text-decoration: underline;\"><strong>B</strong></span>S / P<span style=\"text-decoration: underline;\"><strong>W</strong></span>LS / PM<span style=\"text-decoration: underline;\"><strong>V</strong></span>S</p>",
                    solution_hi: "<p>14.(d)<br>P<span style=\"text-decoration: underline;\"><strong>Q</strong></span>RS / PG<span style=\"text-decoration: underline;\"><strong>B</strong></span>S / P<span style=\"text-decoration: underline;\"><strong>W</strong></span>LS / PM<span style=\"text-decoration: underline;\"><strong>V</strong></span>S</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. How many non-permanent members does the UN Council have?</p>",
                    question_hi: "<p>15. संयुक्त राष्ट्र परिषद के कितने अस्थायी सदस्य हैं?</p>",
                    options_en: ["<p>10</p>", "<p>14</p>", 
                                "<p>15</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>14</p>",
                                "<p>15</p>", "<p>12</p>"],
                    solution_en: "<p>15.(a) <strong>10. United Nation Security Council</strong> - It consists of 15 member countries: Five permanent member countries: (China, France, Russian Federation, the United Kingdom, and the United States) and Ten non-permanent member countries. These ten non-permanent members are elected by the United Nations General Assembly for two-year terms. <strong>First Session</strong> - 17 January 1946 at Church House, Westminster, London.</p>",
                    solution_hi: "<p>15.(a) <strong>10 । संयुक्त राष्ट्र सुरक्षा परिषद</strong> - इसमें 15 सदस्य देश शामिल है :<strong> पांच</strong> स्थायी सदस्य देश : (चीन, फ्रांस, रूसी संघ, यूनाइटेड किंगडम और संयुक्त राज्य अमेरिका) और <strong>10</strong> अस्थायी सदस्य देश है। इन 10 अस्थायी सदस्यों को संयुक्त राष्ट्र महासभा द्वारा दो वर्ष के लिए चुना जाता है। <strong>प्रथम सत्र</strong> - 17 जनवरी 1946 चर्च हाउस, वेस्टमिंस्टर, लंदन में।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. P is two times more efficient than Q . P is able to complete a piece of work in 40 days less than Q . Working together, the whole number of days taken by them to complete the work is: (Round off to the nearest integer)</p>",
                    question_hi: "<p>16. P, Q से दो गुना अधिक कुशल है P, Q से 40 दिन कम समय में एक काम को पूरा करने में सक्षम है। एक साथ काम करते हुए काम पूरा करने में उनके द्वारा लिए गए दिनों की कुल संख्या है: <br>(निकटतम पूर्णांक तक सन्निकट)</p>",
                    options_en: ["<p>26</p>", "<p>25</p>", 
                                "<p>15</p>", "<p>28</p>"],
                    options_hi: ["<p>26</p>", "<p>25</p>",
                                "<p>15</p>", "<p>28</p>"],
                    solution_en: "<p>16.(c)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp;P&nbsp; :&nbsp; Q<br>Efficiency&nbsp; &nbsp;=&nbsp; &nbsp;3&nbsp; :&nbsp; 1<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;=&nbsp; 1&nbsp; :&nbsp; 3<br>Difference in time (2 unit) = 40 days<br>Total work = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math> &times; 3 = 60 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 15 days</p>",
                    solution_hi: "<p>16.(c)<br>अनुपात&nbsp; =&nbsp; P&nbsp; :&nbsp; Q<br>दक्षता&nbsp; &nbsp; =&nbsp; 3&nbsp; &nbsp;:&nbsp; 1<br>समय&nbsp; &nbsp; &nbsp;=&nbsp; 1&nbsp; &nbsp;:&nbsp; 3<br>समय में अंतर (2 इकाई ) = 40 दिन<br>कुल कार्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math> &times; 3 = 60 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 15 दिन</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. Where was the ﬁrst nuclear power plant set up in India?</p>",
                    question_hi: "<p>17. भारत में पहला परमाणु ऊर्जा संयंत्र कहाँ स्थापित किया गया था?</p>",
                    options_en: ["<p>Kakrapar</p>", "<p>Kaiga</p>", 
                                "<p>Tarapur</p>", "<p>Kalpakkam</p>"],
                    options_hi: ["<p>काकरापार</p>", "<p>कैगा</p>",
                                "<p>तारापुर</p>", "<p>कलपक्कम</p>"],
                    solution_en: "<p>17.(c) <strong>Tarapur</strong> (Maharashtra):- It was commissioned on 28th October 1969. On 8 May 1964, a contract between the Government of India and the United States began as India\'s first atomic power project. <strong>Other Nuclear power plants (Located):</strong> Kakrapar (Gujarat), Kudankulam (Tamil Nadu), Kaiga (Karnataka), Narora (Uttar Pradesh), Rawatbhata (Rajasthan), Kalpakkam (Tamil Nadu).</p>",
                    solution_hi: "<p>17.(c) <strong>तारापुर</strong> (महाराष्ट्र):- इसे 28 अक्टूबर 1969 को चालू किया गया था। 8 मई 1964 को भारत सरकार और संयुक्त राज्य अमेरिका के बीच भारत की पहली परमाणु ऊर्जा परियोजना के रूप में एक अनुबंध शुरू हुआ। <strong>अन्य परमाणु ऊर्जा संयंत्र (स्थिति):</strong> काकरापार (गुजरात), कुडनकुलम (तमिलनाडु), कैगा (कर्नाटक), नरोरा (उत्तर प्रदेश), रावतभाटा (राजस्थान), कलपक्कम (तमिलनाडु)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. In the given diagram, if &ang;BAC = 30&deg;, &ang;ABC = 50&deg;and &ang;CDE = 25&deg;, then &ang;AED is equal to:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993061.png\" alt=\"rId15\" width=\"208\" height=\"138\"></p>",
                    question_hi: "<p>18. दिए गए आरेख में, यदि &ang;BAC = 30&deg;, &ang;ABC = 50&deg;और &ang;CDE = 25&deg; तब &ang;AED किसके समतुल्य है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993061.png\" alt=\"rId15\" width=\"208\" height=\"138\"></p>",
                    options_en: ["<p>105&deg;</p>", "<p>115&deg;</p>", 
                                "<p>75&deg;</p>", "<p>95&deg;</p>"],
                    options_hi: ["<p>105&deg;</p>", "<p>115&deg;</p>",
                                "<p>75&deg;</p>", "<p>95&deg;</p>"],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993061.png\" alt=\"rId15\" width=\"149\" height=\"99\"><br>From the above diagram as &ang;BAC = 30&deg; and &ang;ABC = 50&deg;,&nbsp;<br>&rArr; &ang;BCA = 100&deg;<br>&rArr; &ang;ECD = 80&deg;<br>Again as &ang;CDE = 25&deg;,<br>&rArr; &ang;CED = 100&deg;- 25&deg; = 75&deg; <br>&rArr; &ang;AED = 180&deg;- 75&deg; = 105&deg;</p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993061.png\" alt=\"rId15\" width=\"134\" height=\"89\"><br>उपरोक्त आरेख से, जैसा की, &ang;BAC = 30&deg; and &ang;ABC = 50&deg;,&nbsp;<br>&rArr; &ang;BCA = 100&deg;<br>&rArr; &ang;ECD = 80&deg;<br>जैसा की, &ang;CDE = 25&deg;,<br>&rArr; &ang;CED = 100&deg;- 25&deg; = 75&deg; <br>&rArr; &ang;AED = 180&deg;- 75&deg; = 105&deg;</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. When did Akbar become the emperor?</p>",
                    question_hi: "<p>19. अकबर सम्राट कब बना था?</p>",
                    options_en: ["<p>1560 AD</p>", "<p>1550 AD</p>", 
                                "<p>1552 AD</p>", "<p>1556 AD</p>"],
                    options_hi: ["<p>1560 ई.</p>", "<p>1550 ई.</p>",
                                "<p>1552 ई.</p>", "<p>1556 ई.</p>"],
                    solution_en: "<p>19.(d) <strong>1556 AD</strong>. Akbar was the third Mughal emperor. <strong>Ruling Period</strong> - 1556 -1605. Din-i-Ilahi religion was introduced by Akbar in 1582. <strong>Second Battle of Panipat</strong> (5th November, 1556) - Fought between Akbar and Hemu. Under the leadership of Bairam Khan, the Mughals won. <strong>Historical structures built during the reign of Akbar: </strong>Agra Fort, Lahore Fort, Allahabad Fort, Buland Darwaza.</p>",
                    solution_hi: "<p>19.(d) <strong>1556 ई.</strong>। अकबर तीसरा मुग़ल सम्राट था। <strong>शासन काल</strong> - 1556 -1605 । दीन-ए-इलाही धर्म की शुरुआत 1582 में अकबर द्वारा की गई थी। <strong>पानीपत का द्वितीय युद्ध</strong> (5 नवंबर, 1556) - अकबर और हेमू के बीच लड़ा गया था । बैरम खाँ के नेतृत्व में मुगलों की विजय हुई। <strong>अकबर के शासनकाल के दौरान निर्मित ऐतिहासिक इमारतें </strong>: आगरा का किला, लाहौर का किला, इलाहाबाद का किला, बुलंद दरवाजा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. In an election, 2% persons enrolled in the voter list did not participate and 500 votes were invalid. Two candidates A and B fought the election, and A defeated B by 200 votes. If 43% of the persons enrolled in the voter list casted their votes in favour of A, then what is the number of the total casted votes?</p>",
                    question_hi: "<p>20. एक चुनाव में मतदाता सूची में नामांकित 2% व्यक्तियों ने वोटिंग में भाग नहीं लिया और 500 वोट अवैध थे। दो उम्मीदवार A और B चुनाव लड़े और A ने B को 200 वोट से हरा दिया। यदि मतदाता सूची में नामांकित व्यक्तियों में से 43% ने A के पक्ष में अपना वोट डाला, तो डाले गए कुल वोटों की संख्या कितनी है?</p>",
                    options_en: ["<p>2450</p>", "<p>2800</p>", 
                                "<p>3000</p>", "<p>3250</p>"],
                    options_hi: ["<p>2450</p>", "<p>2800</p>",
                                "<p>3000</p>", "<p>3250</p>"],
                    solution_en: "<p>20.(a)<br>Let total number of voters enrolled = 100x<br>Voters participated in elections = 98x<br>Valid votes = 98x&nbsp;- 500<br>Votes in favour of A = 43% of enrolled voters = 43x<br>Votes in favour of B = (98x&nbsp;- 500) - 43x<br>According to question,<br>43x&nbsp;- [(98x - 500) - 43x] = 200<br>43x&nbsp;- (98x - 500) + 43x = 200<br>86x&nbsp;- 98x + 500 = 200<br>- 12x = - 300&nbsp; &rArr; x = 25<br>Total number of voters participated = 98x<br>= 98 <math display=\"inline\"><mo>&#215;</mo></math> 25 = 2450</p>",
                    solution_hi: "<p>20.(a)<br>मान लीजिए नामांकित मतदाताओं की कुल संख्या = 100x<br>मतदाताओं ने चुनाव में भाग लिया = 98x<br>वैध वोट = 98x&nbsp;- 500<br>A के पक्ष में वोट = नामांकित मतदाताओं का 43% = 43x<br>B के पक्ष में वोट = (98x&nbsp;- 500) - 43x<br>प्रश्न के अनुसार,<br>43x&nbsp;- [(98x - 500) - 43x] = 200<br>43x&nbsp;- (98x - 500) + 43x = 200<br>86<math display=\"inline\"><mi>x</mi></math> - 98x + 500 = 200<br>-12x = - 300 &rArr; x = 25<br>भाग लेने वाले मतदाताओं की कुल संख्या = 98x<br>= 98 <math display=\"inline\"><mo>&#215;</mo></math> 25 = 2450</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. When was the revolt of 1857 ﬁnally suppressed by the British?</p>",
                    question_hi: "<p>21. 1857 के विद्रोह को अंततः अंग्रेजों ने कब दबा दिया था?</p>",
                    options_en: ["<p>1861</p>", "<p>1859</p>", 
                                "<p>1860</p>", "<p>1857</p>"],
                    options_hi: ["<p>1861</p>", "<p>1859</p>",
                                "<p>1860</p>", "<p>1857</p>"],
                    solution_en: "<p>21.(b) <strong>1859.</strong> <strong>Revolt of 1857:</strong>- It also known as Sepoy Mutiny, the Indian Mutiny, the Great Rebellion, First War of Indian Independence. It began on 10 May 1857 in the town of Meerut. <strong>Mangal Pandey </strong>- He was the first martyr of the 1857 revolt. <strong>Lord Canning </strong>- He was the governor-general of India during the 1857 revolt. The Britishers called the revolt the \'<strong>devil\'s wind\'. Leaders of the revolt -</strong> Begum Hazrat Mahal (Lucknow), Nana Saheb (Kanpur), Kunwar Singh (Bihar).</p>",
                    solution_hi: "<p>21.(b) <strong>1859 । 1857 का विद्रोह</strong>:- इसे सिपाही विद्रोह, भारतीय विद्रोह, महान विद्रोह, भारतीय स्वतंत्रता का प्रथम युद्ध के नाम से भी जाना जाता है। इसकी शुरुआत 10 मई 1857 को मेरठ शहर में हुई थी। <strong>मंगल पांडे -</strong> यह 1857 के विद्रोह के प्रथम शहीद थे। <strong>लॉर्ड कैनिंग</strong> - यह 1857 के विद्रोह के दौरान भारत के गवर्नर-जनरल थे। अंग्रेजों ने विद्रोह को \'डेविल वाइन्ड&rsquo; कहा। <strong>विद्रोह के नेता</strong> - बेगम हज़रत महल (लखनऊ), नाना साहेब (कानपुर), कुँवर सिंह (बिहार)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. What is the value of 64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup>, when x = 3 and y = - 4 ?</p>",
                    question_hi: "<p>22. x = 3 और y = - 4 होने पर 64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>1236</p>", "<p>488</p>", 
                                "<p>536</p>", "<p>1256</p>"],
                    options_hi: ["<p>1236</p>", "<p>488</p>",
                                "<p>536</p>", "<p>1256</p>"],
                    solution_en: "<p>22.(d)<br>(4x + y)<sup>3</sup> = 64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup><br>According to question,<br>64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup><br>= (4x + y)<sup>3</sup> - 10x<sup>2</sup>y + 8xy<sup>2</sup><br>= (4 &times; 3 - 4)<sup>3</sup> - 10 &times; 3<sup>2</sup> &times; (-4) + 8 &times; 3 &times; 16<br>= (8)<sup>3</sup> + 360 + 384<br>= 512 + 744 = 1256</p>",
                    solution_hi: "<p>22.(d)<br>(4x + y)<sup>3</sup> = 64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup><br>प्रश्न के अनुसार ,<br>64x<sup>3</sup> + 38x<sup>2</sup>y + 20xy<sup>2</sup> + y<sup>3</sup><br>= (4x + y)<sup>3</sup> - 10x<sup>2</sup>y + 8xy<sup>2</sup><br>= (4 &times; 3 - 4)<sup>3</sup> - 10 &times; 3<sup>2</sup> &times; (-4) + 8 &times; 3 &times; 16<br>= (8)<sup>3</sup> + 360 + 384<br>= 512 + 744 = 1256</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. SPECS scheme is administered by which ministry?</p>",
                    question_hi: "<p>23. SPECS योजना किस मंत्रालय द्वारा संचालित की जाती है?</p>",
                    options_en: ["<p>Ministry of Electronics and Information Technology</p>", "<p>Ministry of Agriculture</p>", 
                                "<p>Ministry of Science &amp; Technology</p>", "<p>Ministry of Commerce</p>"],
                    options_hi: ["<p>इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी मंत्रालय</p>", "<p>कृषि मंत्रालय</p>",
                                "<p>विज्ञान और प्रौद्योगिकी मंत्रालय</p>", "<p>वाणिज्य मंत्रालय</p>"],
                    solution_en: "<p>23.(a) The government is planning to launch a revised incentive scheme, Scheme for Promotion of Manufacturing of Electronic Components and Semiconductors (SPECS), with allocation up to ₹10,000 crore. This move aims to boost electronic components and semiconductor manufacturing in alignment with the semiconductor mission to establish India as a global electronics hub.</p>",
                    solution_hi: "<p>23.(a) Scheme for Promotion of Manufacturing of Electronic Components and Semiconductors (SPECS) , सरकार ₹10,000 करोड़ तक के आवंटन के साथ एक संशोधित प्रोत्साहन योजना, इलेक्ट्रॉनिक घटकों और अर्धचालकों के विनिर्माण को बढ़ावा देने की योजना (एसपीईसीएस) शुरू करने की योजना बना रही है। इस कदम का उद्देश्य भारत को वैश्विक इलेक्ट्रॉनिक्स केंद्र के रूप में स्थापित करने के लिए सेमीकंडक्टर मिशन के साथ इलेक्ट्रॉनिक घटकों और सेमीकंडक्टर विनिर्माण को बढ़ावा देना है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. Who built the Sanchi Stupa?</p>",
                    question_hi: "<p>24. सांची स्तूप का निर्माण किसने करवाया था?</p>",
                    options_en: ["<p>Chandra gupta</p>", "<p>Chanakya</p>", 
                                "<p>Bindusara</p>", "<p>Ashoka</p>"],
                    options_hi: ["<p>चंद्रगुप्त</p>", "<p>चाणक्य</p>",
                                "<p>बिंदुसार</p>", "<p>अशोक</p>"],
                    solution_en: "<p>24.(d) <strong>Ashoka. Sanchi </strong>- It is a Buddhist complex, famous for its Great Stupa, on a hilltop at Sanchi Town in Raisen District (Madhya Pradesh). It was designated a World Heritage site in 1989. <strong>Ashoka </strong>- He ruled the Maurya dynasty from around 268 to 232 BCE. He is also known as Ashoka the Great. <strong>Chandragupta Maurya</strong> - He founded the Mauryan Empire, one of the largest empires in ancient India. <strong>Chanakya</strong> (Kautilya or Vishnugupta) - He was an ancient Indian teacher, philosopher, economist, jurist, and royal advisor. He was a key advisor to Chandragupta Maurya.</p>",
                    solution_hi: "<p>24.(d) <strong>अशोक। सांची</strong> - यह एक बौद्ध परिसर है, जो रायसेन जिले (मध्य प्रदेश) के सांची शहर में एक पहाड़ की चोटी पर अपने महान स्तूप के लिए प्रसिद्ध है। इसे 1989 में विश्व धरोहर स्थल घोषित किया गया था। <strong>अशोक</strong> - इन्होंने लगभग 268 से 232 ईसा पूर्व तक मौर्य वंश पर शासन किया। इन्हें अशोक महान के नाम से भी जाना जाता है। <strong>चंद्रगुप्त मौर्य</strong> - इन्होंने मौर्य साम्राज्य की स्थापना की, जो प्राचीन भारत के सबसे बड़े साम्राज्यों में से एक था। <strong>चाणक्य </strong>(कौटिल्य या विष्णुगुप्त) - एक प्राचीन भारतीय शिक्षक, दार्शनिक, अर्थशास्त्री, न्यायविद् और शाही सलाहकार थे। ये चंद्रगुप्त मौर्य के प्रमुख सलाहकार भी थे।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. With which state is the Nabakalebara festival associated ?</p>",
                    question_hi: "<p>25. नवकलेबारा उत्सव किस राज्य से संबंधित है?</p>",
                    options_en: ["<p>West Bengal</p>", "<p>Odisha</p>", 
                                "<p>Assam</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>पश्चिम बंगाल</p>", "<p>उड़ीसा</p>",
                                "<p>असम</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>25.(b) <strong>Odisha. Nabakalebara:</strong> It is an important festival in the Hindu Odia calendar, observed in the Jagannath Temple, Puri. It was first organized in 1575 A.D by Yaduvanshi King Ramachandra Deva. <strong>Other Festivals of Odisha </strong>- Rath Yatra, Durga Puja, Konark Dance Festival, Puri Beach Festival, Kalinga Mahotsav, Ekamra Utsav, etc.</p>",
                    solution_hi: "<p>25.(b) <strong>उड़ीसा। नवकलेबारा:</strong> यह हिंदू ओडिया कैलेंडर में एक महत्वपूर्ण त्योहार है, जो पुरी के जगन्नाथ मंदिर में मनाया जाता है। इसका आयोजन पहली बार 1575 ई. में यदुवंशी राजा रामचन्द्र देव द्वारा किया गया था। <strong>उड़ीसा के अन्य त्यौहार </strong>- रथ यात्रा, दुर्गा पूजा, कोणार्क नृत्य महोत्सव, पुरी बीच महोत्सव, कलिंग महोत्सव, एकाम्र उत्सव, आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. Who among the following was the president of Indian National Congress in 1931?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन 1931 में भारतीय राष्ट्रीय कांग्रेस के अध्यक्ष थे?</p>",
                    options_en: ["<p>Vallabhbhai J Patel</p>", "<p>Subhash Chandra Bose</p>", 
                                "<p>Dr Rajendra Prasad</p>", "<p>Nellie Sengupta</p>"],
                    options_hi: ["<p>वल्लभभाई जे. पटेल</p>", "<p>सुभाष चंद्र बोस</p>",
                                "<p>डॉ राजेंद्र प्रसाद</p>", "<p>नेल्ली सेनगुप्ता</p>"],
                    solution_en: "<p>26.(a) <strong>Vallabhbhai J Patel</strong> (Karachi). He is popularly known as &ldquo;<strong>Iron Man of India&rdquo;.</strong> <strong>Congress sessions:</strong> (First Session - Bombay in 1885, W.C. Bannerjee), (Third Session - Madras in 1887, Syed Badruddin Tyabji (1st muslim President)), (<strong>40th Session-</strong>1925 (Kanpur), Sarojini Naidu (First Indian Woman President), (<strong>48th Session</strong> - 1934 (Bombay), Rajendra Prasad), (<strong>49th Session</strong> - 1936 (Lucknow), Jawahar Lal Nehru), (<strong>50th Session</strong> - 1937 ( Faizpur), Jawahar Lal Nehru). (<strong>51st Session</strong> - 1938 (Haripura), Subhash Chandra Bose). (<strong>52nd Session</strong> - 1939 (Tripuri), Subhash Chandra Bose).</p>",
                    solution_hi: "<p>26.(a) <strong>वल्लभभाई जे पटेल </strong>(कराची)। इन्हें लोकप्रिय रूप से <strong>\"भारत के लौह पुरुष\"</strong> के नाम से भी जाना जाता है। <strong>कांग्रेस अधिवेशन : (पहला अधिवेशन</strong> - 1885 में बॉम्बे, डब्ल्यू.सी. बनर्जी), (<strong>तीसरा अधिवेशन </strong>- 1887 में मद्रास, सैयद बदरुद्दीन तैयबजी (प्रथम मुस्लिम अध्यक्ष )), (<strong>40 वां अधिवेशन</strong> - 1925 (कानपुर), सरोजिनी नायडू (प्रथम भारतीय महिला अध्यक्ष ) , (<strong>48वां अधिवेशन</strong> - 1934 (बॉम्बे), राजेंद्र प्रसाद), (<strong>49वां अधिवेशन</strong> - 1936 (लखनऊ), जवाहर लाल नेहरू), (<strong>50वां अधिवेशन</strong> - 1937 (फैजपुर), जवाहर लाल नेहरू),(<strong>51वाँ अधिवेशन</strong>- 1938 (हरिपुरा), सुभाष चन्द्र बोस), (<strong>52वाँ अधिवेशन </strong>- 1939 (त्रिपुरी), सुभाष चन्द्र बोस)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. In a race around a circular cycling track of 75 km, two cyclists are riding at a speed of 30 km/h and 25 km/h. After what time (in hours) will they meet at the point from where they started their journey?</p>",
                    question_hi: "<p>27. 75 km के एक वृत्ताकार साइकिल ट्रैक के परितः एक दौड़ में दो साइकिल सवार 30 km/h और 25 km/h की चाल से साइकिल चला रहे हैं। कितने समय (घंटों में) के बाद वे उस बिंदु पर मिलेंगे, जहाँ से उन्होंने अपनी यात्रा शुरू की थी?</p>",
                    options_en: ["<p>16</p>", "<p>7</p>", 
                                "<p>14</p>", "<p>15</p>"],
                    options_hi: ["<p>16</p>", "<p>7</p>",
                                "<p>14</p>", "<p>15</p>"],
                    solution_en: "<p>27.(d)<br>According to question,<br>Relative Speed = (30 -&nbsp;25) = 5 km/hr<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 15 hours</p>",
                    solution_hi: "<p>27.(d)<br>प्रश्न के अनुसार,<br>सापेक्ष गति = (30 -&nbsp;25) = 5 किमी/घंटा<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 15 घंटे</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. Which organ in the human body produces bile juice?</p>",
                    question_hi: "<p>28. मानव शरीर में कौन सा अंग पित्त रस का उत्पादन करता है?</p>",
                    options_en: ["<p>Small intestine</p>", "<p>Pancreas</p>", 
                                "<p>Liver</p>", "<p>Stomach</p>"],
                    options_hi: ["<p>छोटी आंत</p>", "<p>अग्न्याशय</p>",
                                "<p>यकृत</p>", "<p>आमाशय</p>"],
                    solution_en: "<p>28.(c) <strong>Liver. Bile juice</strong> - It is a greenish-yellow fluid produced by the liver, stored in the gallbladder, and released into the small intestine to aid in the digestion and absorption of fats. <strong>Small intestine</strong> - It is a long, coiled tube in the digestive system, where most of the nutrient absorption from food takes place. <strong>Pancreas</strong> - It is a glandular organ located behind the stomach, responsible for producing digestive enzymes and hormones. <strong>Stomach</strong> - It is a muscular organ in the digestive system that breaks down and digests food using acid and enzymes.</p>",
                    solution_hi: "<p>28.(c) <strong>यकृत । पित्तरस</strong> - यह यकृत द्वारा उत्पादित एक हरा-पीला तरल पदार्थ है, जो पित्ताशय(gallbladder) में संग्रहित होता है, और वसा के पाचन और अवशोषण में सहायता के लिए छोटी आंत में स्राावित होता है। <strong>छोटी आंत</strong> - यह पाचन तंत्र में एक लंबी, कुंडलित ट्यूब है, जहां भोजन से अधिकांश पोषक तत्वों का अवशोषण होता है। <strong>अग्न्याशय</strong> - यह आमाशय के पीछे स्थित एक ग्रंथि अंग है, जो पाचन एंजाइमों और हार्मोन के उत्पादन के लिए उत्तरदायी है। <strong>आमाशय</strong> - यह पाचन तंत्र में एक मांसपेशीय अंग है जो अम्ल और एंजाइमों का उपयोग करके भोजन को तोड़ता और पचाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. Invertebrates do NOT include:</p>",
                    question_hi: "<p>29.अकशेरूकीय में_________ शामिल नहीं हैं।</p>",
                    options_en: ["<p>molluscs</p>", "<p>arachnids</p>", 
                                "<p>reptiles</p>", "<p>insects</p>"],
                    options_hi: ["<p>मोलस्क</p>", "<p>मकड़ी</p>",
                                "<p>सरीसृप</p>", "<p>कीड़े</p>"],
                    solution_en: "<p>29.(c) <strong>Reptiles</strong>. An invertebrate is a cold-blooded animal without a backbone. Examples - Insects, arachnids, crustaceans, Molluscusa (chintons, snail), Annelid (earthworms and leeches). Reptiles are vertebrates. Example - Snakes, Turtles, Lizards and crocodiles.</p>",
                    solution_hi: "<p>29.(c) <strong>सरीसृप</strong>। अकशेरुकी बिना रीढ़ की हड्डी वाला एक ठंडा खून वाला जानवर है। उदाहरण - कीट, अष्टपाद (arachnids), क्रस्टेशियन, मोलस्का (काइटन, घोंघा), ऐनेलिड (केंचुआ और जोंक)। सरीसृप कशेरुक हैं। उदाहरण - सांप, कछुआ, छिपकली और मगरमच्छ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. Which of the following is composed of nerve fibres that mediate reflex actions and that transmit impulses to and from the brain ?</p>",
                    question_hi: "<p>30. निम्नलिखित में से कौन तंत्रिका तंतुओं से बना है जो प्रतिवर्त क्रियाओं में मध्यस्थता करता है और जो मस्तिष्क से आवेगों को संचारित करता है ?</p>",
                    options_en: ["<p>Heart</p>", "<p>Rib cage</p>", 
                                "<p>Spinal cord</p>", "<p>Muscles</p>"],
                    options_hi: ["<p>हृदय</p>", "<p>पंजर</p>",
                                "<p>मेरुदण्ड</p>", "<p>मांसपेशी</p>"],
                    solution_en: "<p>30.(c) <strong>Spinal cord</strong>. It is a long, thin, tubular structure made up of nervous tissue, and it is a part of the central nervous system in the human body. <strong>Heart</strong> is the busiest organ of the body. Heart pumps oxygenated blood to the body and deoxygenated blood to the lungs. The <strong>rib cage</strong> forms the thorax (chest) portion of the body. The <strong>muscular</strong> system of the human body helps for movement.</p>",
                    solution_hi: "<p>30.(c) <strong>मेरुदण्ड</strong>। यह तंत्रिका ऊतक से बनी एक लंबी, पतली, ट्यूबलर संरचना है और यह मानव शरीर में केंद्रीय तंत्रिका तंत्र का एक हिस्सा है।<strong> हृदय</strong> शरीर का सबसे व्यस्त अंग है। हृदय शरीर में ऑक्सीजन युक्त रक्त पंप करता है और फेफड़ों में ऑक्सीजन रहित रक्त पंप करता है। <strong>पंजर (rib cage)</strong> शरीर का वक्ष (छाती) भाग बनाता है। मानव शरीर की <strong>पेशी तंत्र</strong> गति करने में सहायता करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>29 &divide; 2 - 14 + 16 &times; 4 = ?&nbsp;</p>",
                    question_hi: "<p>31. यदि \'+\' और \'-\' को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में \'?\' के स्थान पर क्या आएगा?<br>29 &divide; 2 - 14 + 16 &times; 4 = ?&nbsp;</p>",
                    options_en: ["<p>28</p>", "<p>48</p>", 
                                "<p>68</p>", "<p>88</p>"],
                    options_hi: ["<p>28</p>", "<p>48</p>",
                                "<p>68</p>", "<p>88</p>"],
                    solution_en: "<p>31.(c)<br>29 &divide; 2 - 14 + 16 &times; 4 = ?&nbsp;<br>After interchanging + and &times;, &times; and &divide;<br>29 &times; 2 + 14 - 16 &divide; 4<br>&rArr; 58 + 14 - 4<br>&rArr; 68</p>",
                    solution_hi: "<p>31.(c)<br>29 &divide; 2 -&nbsp;14 + 16 &times; 4 = ?<br>+ और &times;, &times; और &divide; को आपस में बदलने के बाद<br>29 &times; 2 + 14 - 16 &divide; 4<br>&rArr; 58 + 14 - 4<br>&rArr; 68</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. Which institution released a report titled &lsquo;India Ageing Report 2023&rsquo;?</p>",
                    question_hi: "<p>32. किस संस्था ने \'इंडिया एजिंग रिपोर्ट 2023\' नामक रिपोर्ट जारी की?</p>",
                    options_en: ["<p>NITI Aayog</p>", "<p>NSO</p>", 
                                "<p>UNFPA</p>", "<p>World Bank</p>"],
                    options_hi: ["<p>नीति आयोग</p>", "<p>NSO</p>",
                                "<p>UNFPA</p>", "<p>विश्व बैंक</p>"],
                    solution_en: "<p>32.(c) The &lsquo;India Ageing Report 2023&rsquo; was released recently by the International Institute for Population Sciences and the United Nations Population Fund.<br>It revealed that India&rsquo;s elderly population could constitute over 20% of the country&rsquo;s total population by the year 2050. The decadal growth rate of the elderly population of India estimated at 41%.</p>",
                    solution_hi: "<p>32.(c) इंटरनेशनल इंस्टीट्यूट फॉर पॉपुलेशन साइंसेज और संयुक्त राष्ट्र जनसंख्या कोष द्वारा हाल ही में \'इंडिया एजिंग रिपोर्ट 2023\' जारी की गई।<br>इससे पता चला कि भारत की बुजुर्ग आबादी वर्ष 2050 तक देश की कुल आबादी का 20% से अधिक हो सकती है। भारत की बुजुर्ग आबादी की दशकीय वृद्धि दर 41% अनुमानित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Evaluate the following. <br>sin 25&deg; sin 65&deg; -&nbsp;cos 25&deg; cos 65&deg;.</p>",
                    question_hi: "<p>33.निम्नलिखित का मान निकालिए। <br>sin 25&deg; sin 65&deg; -&nbsp;cos 25&deg; cos 65&deg;.</p>",
                    options_en: ["<p>40</p>", "<p>4</p>", 
                                "<p>0</p>", "<p>1</p>"],
                    options_hi: ["<p>40</p>", "<p>4</p>",
                                "<p>0</p>", "<p>1</p>"],
                    solution_en: "<p>33.(c)<br>Sin 25&deg; Sin 65&deg; -&nbsp;Cos 25&deg; Cos 65&deg;<br>&rArr; Sin 25&deg; Sin 65&deg; - Cos (90&deg; - 65&deg;) Cos(90&deg; - 25&deg;)<br>&rArr; Sin 25&deg; Sin 65&deg; - Sin65&deg; Sin 25&deg;<br>&rArr; 0</p>",
                    solution_hi: "<p>33.(c)</p>\n<p>Sin 25&deg; Sin 65&deg; -&nbsp;Cos 25&deg; Cos 65&deg;<br>&rArr; Sin 25&deg; Sin 65&deg; - Cos (90&deg; - 65&deg;) Cos(90&deg; - 25&deg;)<br>&rArr; Sin 25&deg; Sin 65&deg; - Sin65&deg; Sin 25&deg;<br>&rArr; 0</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. Study the given pie chart and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993188.png\" alt=\"rId16\" width=\"210\" height=\"151\"> <br>The population of village D in 2020 was 10,500. What was the population of village A in 2020? Total population of these six villages is 100%.</p>",
                    question_hi: "<p>34. दिए गए पाई पार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993412.png\" alt=\"rId17\" width=\"194\" height=\"134\"> <br>2020 में गाँव की जनसंख्या 10,500 थी। 2020 में गाँव A की जनसंख्या कितनी थी? इन छह गांवों की कुल जनसंख्या 100% है।</p>",
                    options_en: ["<p>15,570</p>", "<p>15,750</p>", 
                                "<p>17,550</p>", "<p>7,875</p>"],
                    options_hi: ["<p>15,570</p>", "<p>15,750</p>",
                                "<p>17,550</p>", "<p>7,875</p>"],
                    solution_en: "<p>34.(b)<br>According to question,<br>8% = 10500<br>population of village A =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10500</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>8</mn></mfrac></math><br>= 15750</p>",
                    solution_hi: "<p>34.(b)<br>प्रश्न के अनुसार,<br>8% = 10500<br>गाँव A की जनसंख्या = <math display=\"inline\"><mfrac><mrow><mn>10500</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math><br>= 15750</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. If A denotes &lsquo;+&rsquo;, B denotes &lsquo;&times;&rsquo;, C denotes &lsquo;&minus;&rsquo;, and D denotes &lsquo;&divide;&rsquo;, then what will be the value of the following expression ?<br>39 D 13 B 2 C 6 A 8</p>",
                    question_hi: "<p>35. यदि A का अर्थ \'+\' है, B का अर्थ \'&times;\' है, C का अर्थ \'&minus;\' है और D का अर्थ \'&divide;\' है, तो निम्नलिखित व्यंजक का मान क्या होगा ?<br>39 D 13 B 2 C 6 A 8</p>",
                    options_en: ["<p>9</p>", "<p>8</p>", 
                                "<p>10</p>", "<p>12</p>"],
                    options_hi: ["<p>9</p>", "<p>8</p>",
                                "<p>10</p>", "<p>12</p>"],
                    solution_en: "<p>35.(b)<br><strong>Given : </strong>39 D 13 B 2 C 6 A 8<br>As per given instruction after interchanging the letters with sign we get,<br>39 &divide; 13 &times; 2 - 6 + 8 <br>3 &times; 2 - 6 + 8<br>= 8</p>",
                    solution_hi: "<p>35.(b)<br><strong>दिया गया है :</strong> 39 D 13 B 2 C 6 A 8<br>दिए गए निर्देश के अनुसार अक्षरों को चिन्ह से बदलने पर हमें प्राप्त होता है,<br>39 &divide; 13 &times; 2 - 6 + 8 <br>3 &times; 2 - 6 + 8<br>= 8</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. The pistil in the ﬂower is _______.</p>",
                    question_hi: "<p>36. पुष्प स्त्रीकेसर क्या है?</p>",
                    options_en: ["<p>a male reproductive part</p>", "<p>unisexual</p>", 
                                "<p>bisexual</p>", "<p>a female reproductive part</p>"],
                    options_hi: ["<p>नर प्रजनन अंग</p>", "<p>एक लिंगीय</p>",
                                "<p>उभयलिंगीय</p>", "<p>मादा प्रजनन अंग</p>"],
                    solution_en: "<p>36.(d) <strong>A female reproductive part. Pistil</strong> - It consists mainly of stigma, style, ovary, and ovule, which make up the female part of a flower. <strong>Function of a pistil</strong> is to receive pollen and produce seeds. <strong>Unisexual reproduction</strong> - Monogamous reproduction means reproduction with either male or female gametes. <strong>Bisexual reproduction </strong>- It is a type of reproduction where organisms produce offspring through the fusion of gametes from both male and female parents.</p>",
                    solution_hi: "<p>36.(d) <strong>मादा प्रजनन अंग। स्त्रीकेसर</strong>(pistil) - इसमें मुख्य रूप से वर्तिकाग्र, वर्तिका, अंडाशय और बीजाण्ड उपस्थित होते हैं, जो पुष्प के मादा भाग का निर्माण करते हैं। <strong>स्त्रीकेसर का कार्य</strong> परागकण प्राप्त करना और बीज उत्पन्न करना है। <strong>एकलिंगीय प्रजनन</strong> - एकलिंगीय प्रजनन का अर्थ है नर या मादा युग्मक के साथ होने वाला प्रजनन। <strong>उभयलिंगी प्रजनन</strong> - यह एक प्रकार का ऐसा प्रजनन है जिसमें जीव नर और मादा दोनों युग्मकों के संलयन के माध्यम से संतति उत्पन्न करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. A shopkeeper uses 940 gm weight in place of one kg weight. He sells it at 4% profit. What will be the actual profit percentage? (rounded off to two decimal places)</p>",
                    question_hi: "<p>37. एक दुकानदार एक kg बाट के स्थान पर 940 gm बाट का उपयोग करता है। इस वजन का उपयोग करके वह माल 4% लाभ पर बेचता है। वास्तविक लाभ प्रतिशत क्या होगा? (दो दशमलव स्थान तक पूर्णाकित)</p>",
                    options_en: ["<p>9.25%</p>", "<p>10.32%</p>", 
                                "<p>10.64%</p>", "<p>10.96%</p>"],
                    options_hi: ["<p>9.25%</p>", "<p>10.32%</p>",
                                "<p>10.64%</p>", "<p>10.96%</p>"],
                    solution_en: "<p>37.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C.P.&nbsp; &nbsp;:&nbsp; &nbsp;S.P.<br>Profit&nbsp; &nbsp; &nbsp;=&nbsp; &nbsp;25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 26<br>Weight&nbsp; =&nbsp; &nbsp;940&nbsp; &nbsp;:&nbsp; &nbsp; 1000<br>-----------------------------------<br>Total&nbsp; &nbsp; &nbsp;=&nbsp; &nbsp; 47&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 52<br>Required profit = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>52</mn><mo>-</mo><mn>47</mn></mrow><mn>47</mn></mfrac></math> &times; 100 = 10.64 %</p>",
                    solution_hi: "<p>37.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;क्रय&nbsp; &nbsp;:&nbsp; &nbsp;विक्रय मूल्य <br>लाभ&nbsp; &nbsp;=&nbsp; &nbsp;25&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 26<br>वजन&nbsp; =&nbsp; 940&nbsp; :&nbsp; &nbsp; &nbsp;1000<br>---------------------------------<br>कुल&nbsp; &nbsp;=&nbsp; &nbsp;47&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 52<br>आवश्यक लाभ = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>52</mn><mo>-</mo><mn>47</mn></mrow><mn>47</mn></mfrac></math> &times; 100 = 10.64 %</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38.If D is the midpoint of BC in &Delta;ABC and &ang;A = 90&deg;, then AD =_______.</p>",
                    question_hi: "<p>38.यदि D, &Delta;ABC में BC का मध्यबिन्दु है और &ang;A = 90&deg; है, तो AD = ________होता है</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>2BC</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>BC</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>2BC</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>BC</p>"],
                    solution_en: "<p>38.(c)<br>Theorem : The mid point of hypotenuse of a right angled triangle is equidistant from all the vertices of the triangle (AD = BD = DC).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993576.png\" alt=\"rId18\" width=\"112\" height=\"122\"><br>Therefore, AD = <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>38.(c)<br>प्रमेय: समकोण त्रिभुज के कर्ण का मध्य बिंदु त्रिभुज के सभी शीर्षों से समान दूरी पर होता है। (AD = BD = DC)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993576.png\" alt=\"rId18\" width=\"112\" height=\"122\"><br>इसलिए, AD = <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39.In what ratio must water be mixed with milk, costing ₹32 per litre, in order to get a mixture costing ₹28 per litre?</p>",
                    question_hi: "<p>39. ₹32 प्रति लीटर की कीमत वाले दूध में किस अनुपात में पानी मिलाया जाए, ताकि ₹28 प्रति लीटर वाला मिश्रण प्राप्त हो सके ?</p>",
                    options_en: ["<p>8 : 1</p>", "<p>1 : 7</p>", 
                                "<p>1 : 8</p>", "<p>7 : 1</p>"],
                    options_hi: ["<p>8 : 1</p>", "<p>1 : 7</p>",
                                "<p>1 : 8</p>", "<p>7 : 1</p>"],
                    solution_en: "<p>39.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993715.png\" alt=\"rId19\" width=\"135\" height=\"143\"><br>Required ratio = 4 : 28 = 1 : 7</p>",
                    solution_hi: "<p>39.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675993894.png\" alt=\"rId20\" width=\"121\" height=\"130\"><br>आवश्यक अनुपात = 4 : 28 = 1 : 7</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. 5 women and 9 girls earn a total of ₹18,720 in 9 days, while 9 women and 16 girls earn a total of ₹52,080 in 14 days. How much will 12 women and 7 girls together earn (in ₹)&nbsp;in 13 days?</p>",
                    question_hi: "<p>40. 5 महिलाएं और 9 लड़कियां 9 दिन में कुल ₹18,720 कमाती हैं, जबकि 9 महिलाएं और 16 लड़कियां 14 दिन में कुल ₹ 52,080 कमाती हैं। 12 महिलाएं और 7 लड़कियां मिलकर 13 दिन में कितना (₹ में) कमाएंगी?</p>",
                    options_en: ["<p>42510</p>", "<p>41990</p>", 
                                "<p>42380</p>", "<p>42120</p>"],
                    options_hi: ["<p>42510</p>", "<p>41990</p>",
                                "<p>42380</p>", "<p>42120</p>"],
                    solution_en: "<p>40.(d)<br>Let the share of a woman and a girl be &lsquo;a&rsquo; and &lsquo;b&rsquo;.<br>According to question,<br>(5a + 9b) &times; 9 = 18720 --------(1)<br>(9a + 16b) &times; 14 = 52080 --------(2)<br>From the above equations, we get ;<br>a = 200 and b = 120<br>Required amount = {(12 &times; 200) + (7 &times; 120)} &times; 13 = 42,120 Rs.</p>",
                    solution_hi: "<p>40.(d)<br>माना कि एक महिला और एक लड़की का हिस्सा a और b है।<br>प्रश्न के अनुसार,<br>(5a + 9b) &times; 9 = 18720 --------(1)<br>(9a + 16b) &times; 14 = 52080 --------(2)<br>उपरोक्त समीकरणों से, हमें मिलता है;<br>a = 200 और b = 120<br>आवश्यक राशि = {(12 &times; 200) + (7 &times; 120)} &times; 13 = 42120 रुपये।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. An electronic store owner allows two successive discounts of 20% and 25% on each item. The store has a reward points scheme which enables a customer to get free shopping worth ₹0.10 on every 1 reward point credited to the customer\'s account on previous purchases from the store. A customer decides to buy a laptop that is marked at ₹72,000. What will be its net selling price if he has 2850 reward points to his credit?</p>",
                    question_hi: "<p>41. एक इलेक्ट्रॉनिक स्टोर मालिक प्रत्येक वस्तु पर 20% और 25% की दो क्रमिक छूटें देता है। स्टोर में एक रिवॉर्ड पॉइंट स्कीम है, जो ग्राहक को स्टोर से पिछली खरीदारी पर ग्राहक के खाते में जमा किए गए प्रत्येक 1 रिवार्ड पॉइंट पर ₹0.10 की मुफ्त खरीदारी करने में सक्षम बनाती है। एक ग्राहक एक लैपटॉप खरीदने का फैसला करता है जिसका अंकित मूल्य ₹72,000 है। यदि उसके खाते में 2850 रिवार्ड पॉइंट हैं, तो लैपटॉप का निवल विक्रय मूल्य क्या होगा?</p>",
                    options_en: ["<p>₹43,200</p>", "<p>₹42,915</p>", 
                                "<p>₹42,215</p>", "<p>₹42,942</p>"],
                    options_hi: ["<p>₹43,200</p>", "<p>₹42,915</p>",
                                "<p>₹42,215</p>", "<p>₹42,942</p>"],
                    solution_en: "<p>41.(b)<br>Marked price of laptop = 72000 Rs.<br>According to question,<br>Net discount % = 20 + 25 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> = 40%<br>Price of laptop after discount = 72000 &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 43200 Rs.<br>Required price = 43200 -&nbsp;(2850 &times; 0.10) = 42915 Rs.</p>",
                    solution_hi: "<p>41.(b)<br>लैपटॉप का अंकित मूल्य = 72000 रु.<br>प्रश्न के अनुसार,<br>शुद्ध छूट % = 20 + 25 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> = 40%<br>छूट के बाद लैपटॉप की कीमत = 72000 &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 43200 रु.<br>आवश्यक मूल्य = 43200 <math display=\"inline\"><mo>-</mo></math> (2850 &times; 0.10) = 42915 रु.</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. What is the value of a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> , if (a + b + c) = 0 ?</p>",
                    question_hi: "<p>42.यदि (a + b + c) = 0 है, तो a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> का मान क्या होगा?</p>",
                    options_en: ["<p>a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> - 3abc</p>", "<p>0</p>", 
                                "<p>3abc</p>", "<p>a<sup>2</sup> + b<sup>2</sup> +c<sup>2</sup> - ab - bc - ca</p>"],
                    options_hi: ["<p>a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> - 3abc</p>", "<p>0</p>",
                                "<p>3abc</p>", "<p>a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> - ab - bc - ca</p>"],
                    solution_en: "<p>42.(c)<br><strong>Formula :</strong> (a + b + c)<sup>3</sup> - 3abc = (a + b + c) (a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> + ab + bc + ca)<br>And we know, If (a + b + c ) = 0<br>Then,<br>a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 3abc</p>",
                    solution_hi: "<p>42.(c)<br><strong>सूत्र:</strong> (a + b + c)<sup>3</sup> - 3abc = (a + b + c) (a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> + ab + bc + ca)<br>और हम जानते हैं, यदि (a + b + c ) = 0<br>तब,<br>a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 3abc</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43.Evaluate the following:<br>cos(36&deg; + A).cos(36&deg; - A) + cos(54&deg; + A).cos(54&deg; - A)</p>",
                    question_hi: "<p>43. निम्नलिखित का मान ज्ञात कीजिए। <br>cos(36&deg; + A).cos(36&deg; - A) + cos(54&deg; + A).cos(54&deg; - A)</p>",
                    options_en: ["<p>sin 2A</p>", "<p>cos A</p>", 
                                "<p>sin A</p>", "<p>cos 2A</p>"],
                    options_hi: ["<p>sin 2A</p>", "<p>cos A</p>",
                                "<p>sin A</p>", "<p>cos 2A</p>"],
                    solution_en: "<p>43.(d)<br><strong>Formula used: </strong>cos (A - B) = cosA .cosB + sinA.sin B<br>cos(36&deg;+ A).cos(36&deg;- A)+ cos(54&deg;+ A).cos(54&deg;- A)<br>&rArr; cos(36&deg;+ A).cos(36&deg;- A) + cos[90 - (36&deg;+ A)].cos[90-(36&deg;- A)]<br>&rArr; cos(36&deg;+ A).cos(36&deg;- A) + sin (36&deg;+ A) sin (36&deg;- A)<br>&rArr; cos[36&deg;+ A-(36&deg;- A)]&nbsp;<br>&rArr; cos(2A)</p>",
                    solution_hi: "<p>43.(d)<br>प्रयुक्त सूत्र :cos (A - B) = cosA .cosB + sinA.sin B<br>cos(36&deg;+ A).cos(36&deg;- A)+ cos(54&deg;+ A).cos(54&deg;- A)<br>&rArr; cos(36&deg;+ A).cos(36&deg;- A) + cos[90 - (36&deg;+ A)].cos[90-(36&deg;- A)]<br>&rArr; cos(36&deg;+ A).cos(36&deg;- A) + sin (36&deg;+ A) sin (36&deg;- A)<br>&rArr; cos[36&deg;+ A-(36&deg;- A)]&nbsp;<br>&rArr; cos(2A)</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. If the total surface area of a cube is 24 sq.units, then what is the volume of the cube?</p>",
                    question_hi: "<p>44. यदि एक घन का संपूर्ण पृष्ठीय क्षेत्रफल 24 वर्ग इकाई है, तो घन का आयतन क्या होगा?</p>",
                    options_en: ["<p>8 cu.units</p>", "<p>16 cu.units</p>", 
                                "<p>10 cu.units</p>", "<p>4 cu.units</p>"],
                    options_hi: ["<p>8 घन इकाई</p>", "<p>16 घन इकाई</p>",
                                "<p>10 घन इकाई</p>", "<p>4 घन इकाई</p>"],
                    solution_en: "<p>44.(a) <br>Total surface area of cube (6a<sup>2</sup>) = 24 <br>&rArr; side of cube (a) = 2<br>Volume of cube (a<sup>3</sup>) = 8 cu.units</p>",
                    solution_hi: "<p>44.(a) <br>घन का कुल पृष्ठीय क्षेत्रफल (6a<sup>2</sup>) = 24<br>&rArr; घन की भुजा (a) = 2<br>घन का आयतन (a<sup>3</sup>) = 8 घन इकाई</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45.Find the simplified value of the given expression.<br>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of 5 + <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    question_hi: "<p>45.दिए गए व्यंजक का सरलीकृत मान ज्ञात कीजिए।<br>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of 5 + <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    options_en: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>5</p>", 
                                "<p>1<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p>6</p>"],
                    options_hi: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>5</p>",
                                "<p>1<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p>6</p>"],
                    solution_en: "<p>45.(c)<br>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of 5 + <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>5</mn></mfrac></math> &divide; 3 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>50</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>50</mn></mfrac></math></p>\n<p>&rArr; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math></p>",
                    solution_hi: "<p>45.(c)</p>\n<p>4<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of 5 + <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>5</mn></mfrac></math> &divide; 3 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>50</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>50</mn></mfrac></math></p>\n<p>&rArr; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. Let ABC, PQR be two congruent triangles such that angle A = angle P = 90&deg;. If BC = 13cm, PR = 5cm, find AB.</p>",
                    question_hi: "<p>46. मान लीजिए ABC, PQR दो ऐसे सर्वांगसम त्रिभुज हैं कि कोण A = कोण P = 90&deg; है। यदि BC = 13cm, PR = 5cm है, तो AB ज्ञात कीजिए।</p>",
                    options_en: ["<p>12 cm</p>", "<p>8 cm</p>", 
                                "<p>10 cm</p>", "<p>5 cm</p>"],
                    options_hi: ["<p>12 cm</p>", "<p>8 cm</p>",
                                "<p>10 cm</p>", "<p>5 cm</p>"],
                    solution_en: "<p>46.(a)<br>According to question,<br>BC = 13 cm = QR<br>PR = 5 cm = AC [ triangles are congruent ]<br>Using pythagoras theorem, (as &ang;A = 90&deg;)<br>AB = <math display=\"inline\"><msqrt><msup><mrow><mn>13</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= 12 cm</p>",
                    solution_hi: "<p>46.(a)<br>प्रश्न के अनुसार,<br>BC = 13 सेमी = QR<br>PR = 5 सेमी = AC [ त्रिभुज सर्वांगसम हैं ]<br>पाइथागोरस प्रमेय का उपयोग करते हुए, (चूंकि &ang;A = 90&deg;)AB<br>=&nbsp;<math display=\"inline\"><msqrt><msup><mrow><mn>13</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= 12 सेमी</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. The mean proportion between 7 and 112 is:</p>",
                    question_hi: "<p>47. 7 और 112 के मध्य माध्यानुपाती ज्ञात कीजिए ।</p>",
                    options_en: ["<p>42</p>", "<p>28</p>", 
                                "<p>21</p>", "<p>14</p>"],
                    options_hi: ["<p>42</p>", "<p>28</p>",
                                "<p>21</p>", "<p>14</p>"],
                    solution_en: "<p>47.(b)<br>According to question,<br>Mean proportional = <math display=\"inline\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>112</mn></msqrt></math><br>= 28</p>",
                    solution_hi: "<p>47.(b)<br>प्रश्न के अनुसार,<br>माध्य आनुपातिक = <math display=\"inline\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>112</mn></msqrt></math><br>= 28</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. 4 women or 6 boys can finish a work in the same number of days. A Boy can finish it in 60 days. In how many days can 5 women finish the work, working together every day?</p>",
                    question_hi: "<p>48. 4 महिलाएं या 6 लड़के एक कार्य को समान दिनों में पूरा कर सकते हैं। एक लड़का इसे 60 दिनों में पूरा कर सकता है। प्रतिदिन एक साथ मिलकर कार्य करते हुए 5 महिलाएं उस कार्य को कितने दिनों में पूरा कर सकती हैं?</p>",
                    options_en: ["<p>4</p>", "<p>10</p>", 
                                "<p>8</p>", "<p>6</p>"],
                    options_hi: ["<p>4</p>", "<p>10</p>",
                                "<p>8</p>", "<p>6</p>"],
                    solution_en: "<p>48.(c)<br>According to question,<br>4W = 6B<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>W</mi><mi>B</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><br>Total work (60B) = 60 &times;&nbsp;2<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>60</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math></p>\n<p>= 8 days</p>",
                    solution_hi: "<p>48.(c)<br>प्रश्न के अनुसार,<br>4W = 6B<br>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>W</mi><mi>B</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><br>कुल कार्य (60B) = 60 &times; 2<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math><br>= 8 दिन</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. Based on the alphabetical order, three of the following four are alike in a certain way and thus form a group. Which is the one that does not belong to that group?</p>",
                    question_hi: "<p>49. अंग्रेजी वर्णमाला क्रम के आधार पर, निम्नलिखित चार पदों में से तीन किसी प्रकार से समान हैं और एक समूह बनाते हैं। वह कौन सा विकल्प है जो उस समूह से संबंधित नहीं है?</p>",
                    options_en: ["<p>HJN</p>", "<p>LOQ</p>", 
                                "<p>PSU</p>", "<p>MPR</p>"],
                    options_hi: ["<p>HJN</p>", "<p>LOQ</p>",
                                "<p>PSU</p>", "<p>MPR</p>"],
                    solution_en: "<p>49.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994014.png\" alt=\"rId21\" width=\"90\" height=\"82\">&nbsp; &nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994116.png\" alt=\"rId22\" width=\"83\" height=\"84\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994245.png\" alt=\"rId23\" width=\"94\" height=\"86\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994467.png\" alt=\"rId24\" width=\"93\" height=\"89\"></p>",
                    solution_hi: "<p>49.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994014.png\" alt=\"rId21\" width=\"90\" height=\"82\">&nbsp; &nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994116.png\" alt=\"rId22\" width=\"83\" height=\"84\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994245.png\" alt=\"rId23\" width=\"94\" height=\"86\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994467.png\" alt=\"rId24\" width=\"93\" height=\"89\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. Select the figure that will come in place of the question mark (?) in the following figure series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994586.png\" alt=\"rId25\" width=\"266\" height=\"56\"></p>",
                    question_hi: "<p>50. विकल्पों में से उस आकृति का चयन कीजिए जो निम्नांकित आकृति शृंखला में प्रश्नचिह्न (?) के स्थान पर आएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994586.png\" alt=\"rId25\" width=\"280\" height=\"59\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994693.png\" alt=\"rId26\" width=\"89\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994797.png\" alt=\"rId27\" width=\"91\" height=\"95\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994939.png\" alt=\"rId28\" width=\"89\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995102.png\" alt=\"rId29\" width=\"90\" height=\"94\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994693.png\" alt=\"rId26\" width=\"91\" height=\"95\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994797.png\" alt=\"rId27\" width=\"91\" height=\"95\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675994939.png\" alt=\"rId28\" width=\"91\" height=\"95\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995102.png\" alt=\"rId29\" width=\"90\" height=\"94\"></p>"],
                    solution_en: "<p>50.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995102.png\" alt=\"rId29\" width=\"90\" height=\"94\"></p>",
                    solution_hi: "<p>50.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995102.png\" alt=\"rId29\" width=\"90\" height=\"94\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. If the simple interest at the same interest rate on ₹500 for 4 years and ₹700 for 2 years, combined together, is ₹280, then what is the rate of interest?</p>",
                    question_hi: "<p>51. यदि समान ब्याज दर पर 500 रूपये का 4 वर्ष और 700 रूपये का 2 वर्ष का साधारण ब्याज मिलाकर 280 रूपये है, तो व्याज की दर कितनी होगी?</p>",
                    options_en: ["<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", 
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>",
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>51.(c)<br>Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math><br>According to question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>700</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 280<br>Rate of interest = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>17</mn></mfrac></math> %</p>",
                    solution_hi: "<p>51.(c)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>700</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 280<br>ब्याज दर = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>17</mn></mfrac></math> %</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. The ﬁrst national ﬂag of India is said to have been hoisted at ________ in 1906.</p>",
                    question_hi: "<p>52. भारत का पहला राष्ट्रीय ध्वज 1906 में कहाँ पर फहराया गया था ?</p>",
                    options_en: ["<p>Kolkata</p>", "<p>Patna</p>", 
                                "<p>Ahmedabad</p>", "<p>New Delhi</p>"],
                    options_hi: ["<p>कोलकाता</p>", "<p>पटना</p>",
                                "<p>अहमदाबाद</p>", "<p>नई दिल्ली</p>"],
                    solution_en: "<p>52.(a) <strong>Kolkata.</strong> The first national flag in India was hoisted on August 7, 1906, in the &ldquo;Parsee Bagan Square&rdquo; in Calcutta (Kolkata). The flag had three horizontal stripes of red, yellow, and green. Vande Mataram in the Hindi language was written on a yellow strip. The second flag was hoisted in Paris by Madame Cama in 1907. The third flag was hoisted in 1917. <strong>Constituent assembly</strong> adopted the national flag on <strong>22nd July 1947</strong>. The shape of the National Flag of India is rectangular. The ratio of length to width(height) of the flag is 3:2.</p>",
                    solution_hi: "<p>52.(a) <strong>कोलकाता। </strong>भारत में पहला राष्ट्रीय ध्वज 7 अगस्त, 1906 को कलकत्ता (कोलकाता) के \"पारसी बागान स्क्वायर\" में फहराया गया था। झंडे में लाल, पीली और हरी तीन क्षैतिज पट्टियाँ थीं। पीली पट्टी पर हिंदी भाषा में वंदे मातरम लिखा हुआ था। दूसरा झंडा 1907 में मैडम कामा द्वारा पेरिस में फहराया गया था। तीसरा झंडा 1917 में फहराया गया था। <strong>संविधान सभा</strong> ने <strong>22 जुलाई 1947</strong> को राष्ट्रीय ध्वज को अपनाया। भारत के राष्ट्रीय ध्वज का आकार आयताकार है। झंडे की लंबाई और चौड़ाई (ऊंचाई) का अनुपात 3 : 2 है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. A thief steals a van at 3:00 a.m. and drives it at a speed of 57 km/h. The thief is discovered at 4:00 am. and the owner starts the chase with another van at a speed of 76 km/h. At what time will he catch the thief?</p>",
                    question_hi: "<p>53. एक चोर 3:00 am पर एक वैन चुराता है और उसे 57 km/h की चाल से चलाता है। 4:00 am पर चोर का पता चलता है और मालिक 76 km/h की चाल से एक अन्य वैन से उसका पीछा करना शुरू करता है। वह चोर को कितने बजे पकड़ पाएगा?</p>",
                    options_en: ["<p>7 : 30 a.m</p>", "<p>7 : 00 p.m</p>", 
                                "<p>7 : 00 a.m</p>", "<p>6 : 00 a.m</p>"],
                    options_hi: ["<p>7 : 30 a.m</p>", "<p>7 : 00 p.m</p>",
                                "<p>7 : 00 a.m</p>", "<p>6 : 00 a.m</p>"],
                    solution_en: "<p>53.(c)<br>According to question,<br>Distance covered by thief in 1 hour = 57 km<br>Relative Speed = (76 -&nbsp;57) = 19 km/hr<br>Time required to catch the thief = <math display=\"inline\"><mfrac><mrow><mn>57</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math> = 3 hours<br>Therefore, Required time = 4 : 00 am + 3hrs. = 7 : 00 a.m.</p>",
                    solution_hi: "<p>53.(c)<br>प्रश्न के अनुसार,<br>चोर द्वारा 1 घंटे में तय की गई दूरी = 57 किमी<br>सापेक्ष गति = (76 -&nbsp;57) = 19 किमी/घंटा<br>चोर को पकड़ने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>57</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math> = 3 घंटे<br>इसलिए, आवश्यक समय = 4 : 00 a.m. + 3 घंटे = 7 : 00 a.m.</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. How many categories are there of the Nobel Prize?</p>",
                    question_hi: "<p>54. नोबेल पुरस्कार की कितनी श्रेणियां हैं?</p>",
                    options_en: ["<p>5</p>", "<p>4</p>", 
                                "<p>6</p>", "<p>7</p>"],
                    options_hi: ["<p>5</p>", "<p>4</p>",
                                "<p>6</p>", "<p>7</p>"],
                    solution_en: "<p>54.(c) <strong>6</strong>. The Nobel prize was established and named after by a Swedish inventor <strong>Alfred Nobel.</strong> It is categorized into <strong>six different fields</strong> namely Physics, Chemistry, Physiology (medicine), Literature, Peace and Economics. <strong>Rabindranath Tagore </strong>was the first Indian citizen to be awarded and also first Asian to be awarded the Nobel Prize in 1913 (Literature). The first Nobel Prizes were awarded in <strong>1901.</strong></p>",
                    solution_hi: "<p>54.(c) <strong>6</strong> । नोबेल पुरस्कार की स्थापना और नामकरण स्वीडिश आविष्कारक <strong>अल्फ्रेड नोबेल </strong>के नाम पर रखा गया था। इसे <strong>6 अलग-अलग क्षेत्रों</strong> में वर्गीकृत किया गया है, अर्थात् भौतिकी, रसायन विज्ञान, फिजियोलॉजी (चिकित्सा), साहित्य, शांति और अर्थशास्त्र। <strong>रवीन्द्रनाथ टैगोर</strong> नोबेल पुरस्कार से सम्मानित होने वाले प्रथम भारतीय नागरिक थे और 1913 (साहित्य) में नोबेल पुरस्कार से सम्मानित होने वाले प्रथम एशियाई भी थे । प्रथम नोबेल पुरस्कार<strong> 1901</strong> में प्रदान किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. The 1000-km High-Speed Economic Corridor Road project is associated with which state/UT?</p>",
                    question_hi: "<p>55.1000 किलोमीटर हाई-स्पीड इकोनॉमिक कॉरिडोर रोड परियोजना किस राज्य/केंद्र शासित प्रदेश से संबंधित है?</p>",
                    options_en: ["<p>Uttarakhand</p>", "<p>Maharashtra</p>", 
                                "<p>Assam</p>", "<p>Gujarat</p>"],
                    options_hi: ["<p>उत्तराखंड</p>", "<p>महाराष्ट्र</p>",
                                "<p>असम</p>", "<p>गुजरात</p>"],
                    solution_en: "<p>55.(c) During its cabinet meeting, the Assam government granted approval for a 1000-kilometer &ldquo;High-Speed Economic Corridor&rdquo; road project.<br>It has an estimated cost of Rs 3,000 crore. This forthcoming high-speed economic corridor will be a part of the &lsquo;Asom Mala&rsquo; project.</p>",
                    solution_hi: "<p>55.(c) अपनी कैबिनेट बैठक के दौरान, असम सरकार ने 1000 किमी की \"हाई-ओनोलिक डॉक्यूमेंट्री\" सड़क परियोजना को मंजूरी दे दी।<br>इसकी अनुमानित लागत 3,000 करोड़ रुपये है। यह आगामी उच्च-आर्थिक आर्थिक गलियारा \'असोम मंगल\' परियोजना का हिस्सा होगा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. The present age of Rahim is five times the present age of his daughter, Savita. Seven years from now, Rahim will be three times as old as Savita. What is the present age (in years) of Rahim?</p>",
                    question_hi: "<p>56. रहीम की वर्तमान आयु, उसकी पुत्री सविता की वर्तमान आयु की पाँच गुना है। अब से सात वर्ष बाद रहीम की आयु, सविता की आयु की तीन गुनी होगी। रहीम की वर्तमान आयु (वर्ष में) क्या है?</p>",
                    options_en: ["<p>45</p>", "<p>35</p>", 
                                "<p>40</p>", "<p>30</p>"],
                    options_hi: ["<p>45</p>", "<p>35</p>",
                                "<p>40</p>", "<p>30</p>"],
                    solution_en: "<p>56.(b)<br>Let the present age of Savita and Rahim be a year and b year.<br>According to question,<br>b = 5a --------(1)<br>(b + 7) = 3(a + 7)<br>(5a + 7) = 3a + 21<br>a = 7<br>Therefore, Present age of Rahim (b) = 5a = 35 years<br><strong>Short trick :</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995272.png\" alt=\"rId30\" width=\"151\" height=\"141\"><br>(5 &times; 1) - (3 &times; 1) = (7 &times; 3) - (7 &times; 1)<br>2 units = 14 years <br>So the present age of Rahim(5 units) = 5 &times; 7 = 35 years.</p>",
                    solution_hi: "<p>56.(b)<br>माना कि सविता और रहीम की वर्तमान आयु = a और b <br>प्रश्न के अनुसार,<br>b = 5a --------(1)<br>(b + 7) = 3(a + 7)<br>(5a + 7) = 3a + 21<br>a = 7<br>इसलिए, रहीम (b) की वर्तमान आयु = 5a = 35 वर्ष<br><strong>Short trick :</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995415.png\" alt=\"rId31\" width=\"131\" height=\"127\"><br>(5 &times; 1) - (3 &times; 1) = (7 &times; 3) - (7 &times; 1)<br>2 units = 14 वर्ष <br>तो रहीम की वर्तमान आयु (5 units) = 5 &times; 7 = 35 वर्ष</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57.&nbsp; Select the number from among the given options that can replace the question mark (?) in the following series.<br><strong id=\"docs-internal-guid-3b3ed4bf-7fff-75f3-f1f4-d6007adac672\"></strong>172, 175, 184, 211, 292, ?</p>",
                    question_hi: "<p>57. दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है।<br>172, 175, 184, 211, 292, ?</p>",
                    options_en: ["<p>535</p>", "<p>537</p>", 
                                "<p>534</p>", "<p>532</p>"],
                    options_hi: ["<p>535</p>", "<p>537</p>",
                                "<p>534</p>", "<p>532</p>"],
                    solution_en: "<p>57.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995629.png\" alt=\"rId32\" width=\"294\" height=\"72\"></p>",
                    solution_hi: "<p>57.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995629.png\" alt=\"rId32\" width=\"294\" height=\"72\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. When did the RTI Act come into effect?</p>",
                    question_hi: "<p>58. RTI अधिनियम कब लागू हुआ?</p>",
                    options_en: ["<p>September 2005</p>", "<p>December 2005</p>", 
                                "<p>November 2006</p>", "<p>October 2005</p>"],
                    options_hi: ["<p>सितंबर 2005</p>", "<p>दिसंबर 2005</p>",
                                "<p>नवंबर 2006</p>", "<p>अक्टूबर 2005</p>"],
                    solution_en: "<p>58.(d) <strong>October 2005. RTI (Right to Information) Bill -</strong> It was passed on 15 June 2005 and came into force on 12 October 2005. <strong>Objective</strong> - It is to empower the citizens, promote transparency and accountability in the working of the government, contain corruption and make our democracy work for the people in a real sense.</p>",
                    solution_hi: "<p>58.(d) <strong>अक्टूबर 2005। RTI (सूचना का अधिकार) विधेयक -</strong> यह 15 जून 2005 को पारित किया गया और 12 अक्टूबर 2005 को लागू हुआ।<strong> उद्देश्य -</strong> इसका उद्देश्य नागरिकों को सशक्त बनाना, सरकार के कामकाज में पारदर्शिता और जवाबदेही को बढ़ावा देना, भ्रष्टाचार को रोकना और हमारे लोकतंत्र को वास्तविक अर्थों में लोगों के लिए काम करना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. Sunila had 9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> kg of flour to make bread with. If the recipe says, she needs 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> kg to make one loaf of bread, how many bread can she make? Estimate to the nearest whole number.</p>",
                    question_hi: "<p>59. सुनीला के पास रोटी बनाने के लिए 9<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> kg आटा था। यदि विधि के अनुसार एक पाव रोटी बनाने के लिए 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> kg की जरूरत है, तो वह कितनी रोटी बना सकती है? निकटतम पूर्ण संख्या का अनुमान लगाइये ?</p>",
                    options_en: ["<p>7.22</p>", "<p>10</p>", 
                                "<p>8.22</p>", "<p>9</p>"],
                    options_hi: ["<p>7.22</p>", "<p>10</p>",
                                "<p>8.22</p>", "<p>9</p>"],
                    solution_en: "<p>59.(c) The number of loaves that Sunila can made with the flour<br>= 9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>9</mn></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>74</mn><mn>9</mn></mfrac></math>&nbsp;</p>\n<p>= 8.222 ≃ 8</p>",
                    solution_hi: "<p>59.(c) सुनीला आटे से जितनी रोटियाँ बना&nbsp;सकती हैं, वो है</p>\n<p>= 9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>9</mn></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>74</mn><mn>9</mn></mfrac></math>&nbsp;</p>\n<p>= 8.222 ≃ 8</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. Who launched the Golden Quadrilateral Project?</p>",
                    question_hi: "<p>60. स्वर्णिम चतुर्भुज परियोजना का शुभारंभ किसने किया ?</p>",
                    options_en: ["<p>Jawaharlal Nehru</p>", "<p>Manmohan Singh</p>", 
                                "<p>Narendra Modi</p>", "<p>Atal Bihari Vajpayee</p>"],
                    options_hi: ["<p>जवाहर लाल नेहरू</p>", "<p>मनमोहन सिंह</p>",
                                "<p>नरेंद्र मोदी</p>", "<p>अटल बिहारी वाजपेयी</p>"],
                    solution_en: "<p>60.(d) <strong>Atal Bihari Vajpayee</strong> (in 2001, 5846 km). It passes through 13 states. This project was aimed to construct a six lane superhighway between India\'s <strong>four large metropolitan cities</strong> : New Delhi, Mumbai, Chennai and Kolkata. It is a national highway network connecting most of the major industrial, agricultural and cultural centers of India. <strong>Other projects</strong> - Bharatmala Pariyojana, Narmada Valley Development Project, Chenab River Railway Bridge, Delhi Metro Industrial Corridor, Mumbai Trans Harbour Link and Inland WaterWays Development Project etc.</p>",
                    solution_hi: "<p>60.(d) <strong>अटल बिहारी वाजपेई</strong> (2001 में, 5846 किमी)। यह 13 राज्यों से होकर गुजरती है। इस परियोजना का उद्देश्य भारत के <strong>चार बड़े महानगरीय शहरों</strong>: नई दिल्ली, मुंबई, चेन्नई और कोलकाता के बीच छः लेन सुपरहाइवे (six lane superhighway) का निर्माण करना था। यह भारत के अधिकांश प्रमुख औद्योगिक, कृषि और सांस्कृतिक केंद्रों को जोड़ने वाला एक राष्ट्रीय राजमार्ग नेटवर्क है। <strong>अन्य परियोजनाएँ </strong>- भारतमाला परियोजना, नर्मदा घाटी विकास परियोजना, चिनाब नदी रेलवे ब्रिज, दिल्ली मेट्रो औद्योगिक गलियारा, मुंबई ट्रांस हार्बर लिंक और अंतर्देशीय जलमार्ग विकास परियोजना आदि</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Solve the following.<br>(x - y)<sup>3</sup> + (y - z)<sup>3</sup> + (z - x)<sup>3</sup> = ?</p>",
                    question_hi: "<p>61. निम्नलिखित को हल कीजिये। <br>(x - y)<sup>3</sup> + (y - z)<sup>3</sup> + (z - x)<sup>3</sup> = ?</p>",
                    options_en: ["<p>3(x - y) (y - z) (z - x)</p>", "<p>(x - y) (y - z) (z - y)</p>", 
                                "<p>(x + y + z)(x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup>)</p>", "<p>3xyz</p>"],
                    options_hi: ["<p>3(x - y) (y - z) (z - x)</p>", "<p>(x - y) (y - z) (z - y)</p>",
                                "<p>(x + y + z)(x&sup2; + y&sup2; + z&sup2;)</p>", "<p>3xyz</p>"],
                    solution_en: "<p>61.(a) As we know, if a + b + c = 0 then a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 3abc.<br>Here, a = (x - y), b = (y - z),<br>c = (z - x) and (x - y) + (y - z) + (z - x) = 0,&nbsp;<br>So, (x - y)<sup>3</sup> + (y - z)<sup>3</sup> + (z - x)<sup>3</sup><br>= 3(x - y) (y - z) (z - x)</p>",
                    solution_hi: "<p>61.(a) जैसा कि हम जानते हैं, यदि a + b + c = 0 तो a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 3abc.<br>यहां, a = (x - y), b = (y - z),<br>c = (z - x) और (x - y) + (y - z) + (z - x) = 0, <br>इसलिए, (x - y)<sup>3</sup> + (y - z)<sup>3</sup> + (z - x)<sup>3</sup><br>= 3(x - y) (y - z) (z - x)</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. In a game Rajesh lost <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of his money in the first round of the game, in the second round he losses <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of his remaining money and in the third round he lost <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> of the rest. He is left with what part of the original sum of money?</p>",
                    question_hi: "<p>62. एक खेल में राजेश खेल के पहले दौर में अपने पैसे का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> हार जाता है, दूसरे दौर में वह अपने शेष पैसे का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> हार जाता है और तीसरे दौर में वह बाकी पैसे का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> हार जाता है। उसके पास मूल धनराशि का कितना भाग बचा है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>45</mn></mrow></mfrac><mi>&#160;</mi></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>62.(b) In a game Rajesh lost <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of his money in the first round of the game,<br>So, money remaining after the first round = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>In the second round he losses <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of his remaining money,<br>So, money remaining after the second round</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math><br>and in the third round he lost <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> of the rest,<br>So, money remaining after the third round</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math><br>i.e. he is left with <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math> parts of the original sum of money.</p>",
                    solution_hi: "<p>62.(b) एक खेल में राजेश ने खेल के पहले दौर में अपने पैसे का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> हार गया,<br>तो, पहले राउंड के बाद बचा हुआ पैसा = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>दूसरे दौर में उसे अपनी शेष राशि में से <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> हार गया हार गया,<br>तो, दूसरे राउंड के बाद बचा हुआ पैसा <br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math><br>और तीसरे दौर में उसने बाकी में से <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> हार गया।<br>तो, तीसरे राउंड के बाद बचा हुआ पैसा <br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math><br>यानी उसके पास मूल राशि का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math> भाग बचा है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. When was the Hindustan Republican Association formed?</p>",
                    question_hi: "<p>63. हिंदुस्तान रिपब्लिकन एसोसिएशन की स्थापना कब हुई थी ?</p>",
                    options_en: ["<p>1920</p>", "<p>1924</p>", 
                                "<p>1922</p>", "<p>1926</p>"],
                    options_hi: ["<p>1920</p>", "<p>1924</p>",
                                "<p>1922</p>", "<p>1926</p>"],
                    solution_en: "<p>63.(b) <strong>1924. Hindustan Republican Association (HRA)</strong> - It was a revolutionary organization of India established in East Bengal by Sachindra Nath Sanyal, Narendra Mohan Sen and Pratul Ganguly as an offshoot of Anushilan Samiti. <strong>Members</strong>: Bhagat Singh, Chandra Shekhar Azad, Sukhdev, Ram Prasad Bismil, Roshan Singh, Ashfaqulla Khan, Rajendra Lahiri. <strong>Objective</strong> - To establish a &ldquo;Federated Republic of the United States of India&rdquo; through an organized and armed revolution. <strong>Kakori conspiracy</strong> is associated with the Hindustan Republican Association.</p>",
                    solution_hi: "<p>63.(b) <strong>1924 । हिंदुस्तान रिपब्लिकन एसोसिएशन</strong> (HRA) - यह भारत का एक क्रांतिकारी संगठन था जिसकी स्थापना पूर्वी बंगाल में सचिन्द्र नाथ सान्याल, नरेंद्र मोहन सेन और प्रतुल गांगुली ने अनुशीलन समिति की एक शाखा के रूप में की थी। <strong>सदस्य:</strong> भगत सिंह, चन्द्रशेखर आज़ाद, सुखदेव, राम प्रसाद बिस्मिल, रोशन सिंह, अशफाकुल्ला खान, राजेंद्र लाहिड़ी। <strong>उद्देश्य</strong> - एक संगठित और सशस्त्र क्रांति के माध्यम से \"संयुक्त राज्य अमेरिका के संघीय गणराज्य\" की स्थापना करना। <strong>काकोरी षडयंत्र </strong>का संबंध हिंदुस्तान रिपब्लिकन एसोसिएशन(HRA) से है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. The second number in the given number pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are followed in all the number pairs, EXCEPT one. Find that odd number pair.</p>",
                    question_hi: "<p>64. दिए गए संख्या-युग्मों में दूसरी संख्या पहली संख्या पर कुछ गणितीय संक्रिया/संकियाएं करके प्राप्त की गई है। एक संख्या-युग्म को छोड़कर सभी संख्या-युग्मों में समान संक्रिया / संकियाओं का अनुसरण किया गया है । वह असंगत संख्या -युग्म ज्ञात कीजिए।</p>",
                    options_en: ["<p>(15, 240)</p>", "<p>(13, 180)</p>", 
                                "<p>(11, 132)</p>", "<p>(16, 272)</p>"],
                    options_hi: ["<p>(15, 240)</p>", "<p>(13, 180)</p>",
                                "<p>(11, 132)</p>", "<p>(16, 272)</p>"],
                    solution_en: "<p>64.(b) <strong>Logic</strong>:- First Number &times; (First number + 1) = Second Number <br>(15, 240) &rarr; 15 &times; (15 + 1) = 15 &times; 16 = 240<br>(11, 132) &rarr; 11 &times; (11 + 1) = 11 &times; 12 = 132<br>(16, 272) &rarr; 16 &times; (16 + 1) = 16 &times; 17 = 272<br>But, <br>(13, 180) &rarr; 13 &times; (13 + 1)&nbsp;= 13 &times; 14 = 182 &ne; 180</p>",
                    solution_hi: "<p>64.(b)<br><strong>तर्क:&nbsp;</strong>पहली संख्या &times; (पहली संख्या 1) = दूसरी संख्या<br>(15, 240) &rarr; 15 &times; (15 + 1)= 15 &times; 16 = 240<br>(11, 132) &rarr; 11 &times; (11 + 1) = 11 &times; 12 = 132<br>(16, 272) &rarr; 16 &times; (16 + 1) = 16 &times; 17 = 272<br>लेकिन, <br>(13, 180) &rarr; 13 &times; (13 + 1)&nbsp;= 13 &times; 14 = 182 &ne; 180</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. IKLN\' is related to \'FHIK\' in a certain way based on the English alphabetical order. In the same way, \'GIJL\' is related to \'DFGI\'. To which of the following is \'MOPR\' related, following the same logic?</p>",
                    question_hi: "<p>65. अंग्रेजी वर्णमाला क्रम के आधार पर \'IKLN\', \'FHIK\' से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, \'GIJL\', \'DFGI\' से संबंधित है। समान तर्क का अनुसरण करते हुए, \'MOPR\' निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>JMOP</p>", "<p>JKMO</p>", 
                                "<p>JLMO</p>", "<p>JLNO</p>"],
                    options_hi: ["<p>JMOP</p>", "<p>JKMO</p>",
                                "<p>JLMO</p>", "<p>JLNO</p>"],
                    solution_en: "<p>65.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995768.png\" alt=\"rId33\" width=\"144\" height=\"80\">&nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995884.png\" alt=\"rId34\" width=\"142\" height=\"80\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995996.png\" alt=\"rId35\" width=\"165\" height=\"107\"></p>",
                    solution_hi: "<p>65.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995768.png\" alt=\"rId33\" width=\"144\" height=\"80\">&nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995884.png\" alt=\"rId34\" width=\"142\" height=\"80\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675995996.png\" alt=\"rId35\" width=\"165\" height=\"107\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. The pH range of a human body is-</p>",
                    question_hi: "<p>66. मानव शरीर का pH सीमा कितना है?</p>",
                    options_en: ["<p>2.35 - 4.45</p>", "<p>8.35 - 9.45</p>", 
                                "<p>5.35 - 6.45</p>", "<p>7.35 - 7.45</p>"],
                    options_hi: ["<p>2.35 - 4.45</p>", "<p>8.35 - 9.45</p>",
                                "<p>5.35 - 6.45</p>", "<p>7.35 - 7.45</p>"],
                    solution_en: "<p>66.(d) <strong>7.35 - 7.45. </strong>Human body needs to maintain a healthy balance of acidity and alkalinity to work properly. The pH of Human body is slightly alkaline. <strong>pH (potential of Hydrogen): </strong>To express the acidity or alkalinity of a substance based on the concentration of hydrogen ions in its solution. It was discovered by <strong>Soren Sorensen. </strong>The range of pH scale <strong>0 to 14.</strong></p>",
                    solution_hi: "<p>66.(d) <strong>7.35 - 7.45 ।</strong> मानव शरीर को सुचारु रूप से काम करने के लिए अम्लता और क्षारीयता के स्वस्थ संतुलन बनाए रखने की आवश्यकता होती है। मानव शरीर का pH थोड़ा क्षारीय होता है। <strong>pH (हाइड्रोजन की क्षमता):</strong> किसी पदार्थ के घोल में हाइड्रोजन आयनों की सांद्रता के आधार पर उसकी अम्लीयता या क्षारीयता का निर्धारण किया जाता है । इसकी खोज <strong>सोरेन सोरेंसन</strong> ने की थी। pH स्केल की सीमा <strong>0 से 14</strong> तक होती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. A 15 cm long perpendicular is drawn from the centre of a circle to its 40 cm long chord. Find the radius of the circle.</p>",
                    question_hi: "<p>67. एक वृत्त के केंद्र से उसकी 40 cm लंबी जीवा पर एक 15 cm लंबाई का लंब खींचा जाता है। वृत्त की त्रिज्या ज्ञात कीजिए |</p>",
                    options_en: ["<p>25 cm</p>", "<p>27 cm</p>", 
                                "<p>22 cm</p>", "<p>20 cm</p>"],
                    options_hi: ["<p>25 cm</p>", "<p>27 cm</p>",
                                "<p>22 cm</p>", "<p>20 cm</p>"],
                    solution_en: "<p>67.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675996132.png\" alt=\"rId36\" width=\"132\" height=\"116\"><br>According to question,<br>OC (Radius of circle) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>15</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = 25 cm</p>",
                    solution_hi: "<p>67.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675996132.png\" alt=\"rId36\" width=\"132\" height=\"116\"><br>प्रश्न के अनुसार,<br>OC (वृत्त की त्रिज्या) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>15</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math></p>\n<p>= 25 सेमी</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. Which is the first nuclear reactor made in India ?</p>",
                    question_hi: "<p>68. भारत में बना पहला परमाणु रिएक्टर कौन सा है ?</p>",
                    options_en: ["<p>Apsara</p>", "<p>CIRUS</p>", 
                                "<p>Dhruva</p>", "<p>KAMINI</p>"],
                    options_hi: ["<p>अप्सरा</p>", "<p>साइरस</p>",
                                "<p>ध्रुव</p>", "<p>कामिनी</p>"],
                    solution_en: "<p>68.(a) <strong>Apsara. </strong>The reactor was designed by the Bhabha Atomic Research Centre (BARC) in Mumbai. <strong>KAMINI</strong> (Kalpakkam Mini reactor) is a research reactor at <strong>Indira Gandhi Center for Atomic Research</strong> in Kalpakkam, India. <strong>CIRUS</strong> (Canada India Reactor Utility Services) was the second nuclear reactor to be built in India, Trombay (Mumbai). <strong>Dhruva reactor</strong> is the largest nuclear research reactor in India.</p>",
                    solution_hi: "<p>86.(a) <strong>अप्सरा</strong>। रिएक्टर को मुंबई में भाभा परमाणु अनुसंधान केंद्र (BARC) द्वारा तैयार किया गया था। <strong>कामिनी</strong> (कलपक्कम मिनी रिएक्टर) कलपक्कम, भारत में <strong>इंदिरा गांधी परमाणु अनुसंधान केंद्र</strong> में एक शोध रिएक्टर है। साइरस (कनाडा इंडिया रिएक्टर यूटिलिटी सर्विसेज) भारत, ट्रॉम्बे (मुंबई) में बनने वाला दूसरा परमाणु रिएक्टर था। <strong>ध्रुव रिएक्टर</strong> भारत का सबसे बड़ा परमाणु अनुसंधान रिएक्टर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with the commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some pens are boxes. <br>No box is a fan. <br>Some pens are fans.<br><strong>Conclusions:</strong><br>I. No pen is a fan.<br>II. No box is a pen.<br>III. Some fans are pens. <br>IV. Some boxes are pens.</p>",
                    question_hi: "<p>69.<strong>&nbsp;</strong>दिए गए कथनों और निष्कर्षों को ध्यान से पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सही है, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न हो, यह तय करें कि दिए गए निष्कर्षों में से कौन सा कथन से तार्किक रूप से अनुसरण करता है।<br><strong><strong id=\"docs-internal-guid-9a8f7eb4-7fff-da4c-5c91-c77444a37f4a\"></strong>कथन:</strong><br>कुछ पेन, बॉक्स हैं।<br>कोई भी बॉक्स, पंखा नहीं है।<br>कुछ पेन, पंखे हैं।<br><strong>निष्कर्ष:</strong><br>I. कोई भी पेन, पंखा नहीं है।<br>II. कोई भी बॉक्स, पेन नहीं है।<br>III. कुछ पंखे, पेन हैं।<br>IV. कुछ बॉक्स, पेन हैं।</p>",
                    options_en: ["<p>Only conclusions I and III follow</p>", "<p>Only conclusions II and IV follow</p>", 
                                "<p>Only conclusions I and II follow</p>", "<p>Only conclusions III and IV follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I और III अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष II और IV अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष I और II अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष III और IV अनुसरण करते हैं</p>"],
                    solution_en: "<p>69.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675996357.png\" alt=\"rId37\" width=\"202\" height=\"64\"><br>Only conclusions III and IV follow.</p>",
                    solution_hi: "<p>69.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675996495.png\" alt=\"rId38\" width=\"190\" height=\"66\"><br>केवल निष्कर्ष III और IV अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. Programming language Java was developed by_______.</p>",
                    question_hi: "<p>70. प्रोग्रामिंग भाषा जावा को _______ द्वारा विकसित किया गया था।</p>",
                    options_en: ["<p>Paul Allen</p>", "<p>Jaap Haartsen</p>", 
                                "<p>James Gosling</p>", "<p>Charles Simonyi</p>"],
                    options_hi: ["<p>पॉल एलन</p>", "<p>जाप हार्टसेन</p>",
                                "<p>जेम्स गोस्लिंग</p>", "<p>चार्ल्स सिमोनी</p>"],
                    solution_en: "<p>70.(c) <strong>James Gosling</strong> (1995). <strong>Java</strong> - It is a high-level, object-oriented programming language known for its platform independence and widespread use in various applications. <strong>Paul Allen</strong> is the co-founder of Microsoft. <strong>Jaap Haartsen</strong> is the inventor of Bluetooth. The first version of <strong>Microsoft Word</strong> launched in <strong>1983 </strong>- developed by Charles Simonyi and Richard Brodie.</p>",
                    solution_hi: "<p>70.(c) <strong>जेम्स गोस्लिंग</strong> <strong>(1995)।</strong> <strong>जावा </strong>- यह एक उच्च-स्तरीय, ऑब्जेक्ट-ओरिएंटेड प्रोग्रामिंग भाषा है जो अपने प्लेटफ़ॉर्म इंडिपेंडेंट और विभिन्न अनुप्रयोगों में व्यापक उपयोग के लिए जानी जाती है। <strong>पॉल एलन</strong> माइक्रोसॉफ्ट के सह-संस्थापक हैं। <strong>जाप हार्टसन</strong>, ब्लूटूथ के आविष्कारक।<strong> माइक्रोसॉफ्ट वर्ड</strong> का पहला संस्करण <strong>1983 </strong>में लॉन्च किया गया था इसे चार्ल्स सिमोनी और रिचर्ड ब्रॉडी द्वारा विकसित किया गया था ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. A family got split and the children of R were left with R&rsquo;s maternal grandfather&rsquo;s only granddaughter&rsquo;s father. How is the person with whom the children are left with related to R ?</p>",
                    question_hi: "<p>71. एक परिवार बँट गया और R के बच्चे R के नाना की इकलौती नवासी के पिता के पास रह गए। वह व्यक्ति जिसके पास बच्चे रह गए है, R से किस प्रकार संबंधित है ?</p>",
                    options_en: ["<p>R&rsquo;s son</p>", "<p>R\'s paternal grandfather</p>", 
                                "<p>R&rsquo;s maternal grandfather</p>", "<p>R&rsquo;s father</p>"],
                    options_hi: ["<p>R का बेटा</p>", "<p>R के दादा</p>",
                                "<p>R के नाना</p>", "<p>R के पिता</p>"],
                    solution_en: "<p>71.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675996720.png\" alt=\"rId39\" width=\"109\" height=\"119\"><br>The person with whom the children are left is father of R.</p>",
                    solution_hi: "<p>71.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675996852.png\" alt=\"rId40\" width=\"105\" height=\"115\"><br>जिस व्यक्ति के पास बच्चा रह गए वह R का पिता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. A solution reacts with chalk powder to give a gas that turns lime water milky. The solution contains</p>",
                    question_hi: "<p>72. एक विलयन चाक पाउडर के साथ अभिक्रिया करके एक गैस प्रदान करता है जो चूने के पानी को दूधिया कर देती है। विलयन में क्या शामिल हैं?</p>",
                    options_en: ["<p>HCl</p>", "<p>NaCl</p>", 
                                "<p>AlCl<sub>3</sub></p>", "<p>MgCl<sub>2</sub></p>"],
                    options_hi: ["<p>HCl</p>", "<p>NaCl</p>",
                                "<p>AlCl<sub>3</sub></p>", "<p>MgCl<sub>2</sub></p>"],
                    solution_en: "<p>72.(a) HCl. In the chemical reaction between Calcium Carbonate (CaCO<sub>3</sub>) and Hydrochloric acid (HCl), carbon dioxide (CO<sub>2</sub>) gas is formed.&nbsp;Chemical Reaction: CaCO<sub>3</sub> + 2HCl <math display=\"inline\"><mo>&#8594;</mo></math> CaCl<sub>2</sub> + H<sub>2</sub>O + CO<sub>2</sub> ; HCL is primarily used as a bleaching agent in food, textile, metal and rubber industries.</p>",
                    solution_hi: "<p>72.(a) HCl । कैल्शियम कार्बोनेट (CaCO<sub>3</sub>) और हाइड्रोक्लोरिक अम्ल (HCl) के बीच रासायनिक अभिक्रिया में कार्बन डाइऑक्साइड (CO<sub>2</sub>) गैस बनती है।&nbsp;रासायनिक अभिक्रिया: CaCO<sub>3</sub> + 2HCl <math display=\"inline\"><mo>&#8594;</mo></math> CaCl<sub>2</sub> + H<sub>2</sub>O + CO<sub>2</sub>&nbsp;; HCL का उपयोग मुख्य रूप से भोजन, कपड़ा, धातु और रबर उद्योगों में ब्लीचिंग एजेंट के रूप में किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Dialect, Language, Communication</p>",
                    question_hi: "<p>73. उस वेन आरेख का चयन कीजिए जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है। <br>बोली, भाषा, संप्रेषण</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997008.png\" alt=\"rId41\" width=\"264\" height=\"43\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997154.png\" alt=\"rId42\" width=\"179\" height=\"69\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997308.png\" alt=\"rId43\" width=\"243\" height=\"53\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997430.png\" alt=\"rId44\" width=\"267\" height=\"39\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997551.png\" alt=\"rId45\" width=\"215\" height=\"52\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997747.png\" alt=\"rId46\" width=\"165\" height=\"84\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997882.png\" alt=\"rId47\" width=\"199\" height=\"61\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998047.png\" alt=\"rId48\" width=\"251\" height=\"51\"></p>"],
                    solution_en: "<p>73.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997308.png\" alt=\"rId43\" width=\"216\" height=\"47\"></p>",
                    solution_hi: "<p>73.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675997882.png\" alt=\"rId47\" width=\"190\" height=\"58\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. The main focus of the First Five-Year Plan was on the _______.</p>",
                    question_hi: "<p>74. प्रथम पंचवर्षीय योजना का मुख्य फोकस किस पर था ?</p>",
                    options_en: ["<p>service sector</p>", "<p>agricultural sector</p>", 
                                "<p>agricultural and industrial sector</p>", "<p>industrial sector</p>"],
                    options_hi: ["<p>सेवा क्षेत्र</p>", "<p>कृषि क्षेत्र</p>",
                                "<p>कृषि और औद्योगिक क्षेत्र</p>", "<p>औद्योगिक क्षेत्र</p>"],
                    solution_en: "<p>74.(b) <strong>Agricultural sector. First Five-Year Plan</strong> - It was introduced in 1951.<strong> First Plan (1951 - 1956):</strong> Based on the Harrod - Domar Model. It focused primarily on the development of the primary sector, specifically agriculture and irrigation. <strong>Second plan (1956 - 1961)</strong>: Industrial Development. <strong>Third plan (1961 - 1966)</strong>: Self Sufficiency in food, self-sufficiency in the economy.</p>",
                    solution_hi: "<p>74.(b) <strong>कृषि क्षेत्र। प्रथम पंचवर्षीय योजना</strong> - इसे 1951 में शुरू किया गया था। <strong>प्रथम योजना (1951 - 1956):</strong> हैरोड - डोमर मॉडल पर आधारित है । इसमें मुख्य रूप से प्राथमिक क्षेत्र, विशेष रूप से कृषि और सिंचाई के विकास पर ध्यान केंद्रित किया गया। <strong>द्वितीय योजना (1956-1961): </strong>औद्योगिक विकास। <strong>तृतीय योजना (1961 - 1966): </strong>खाद्यान्न में आत्मनिर्भरता, अर्थव्यवस्था में आत्मनिर्भरता।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75.What is the value of sec<sup>2</sup> 54&deg; - cot<sup>2</sup> 36&deg; + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>sin<sup>2 </sup>37&deg; &times; sec<sup>2</sup> 53&deg; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>tan60&deg; ?</p>",
                    question_hi: "<p>75. sec<sup>2</sup> 54&deg; - cot<sup>2</sup> 36&deg; + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>sin<sup>2 </sup>37&deg; &times; sec<sup>2</sup> 53&deg; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>tan60&deg; का मान क्या है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>75.(b)<br>sec<sup>2</sup> 54&deg; - cot<sup>2</sup> 36&deg; + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>sin<sup>2 </sup>37&deg; &times; sec<sup>2</sup> 53&deg; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>tan60&deg;<br>&rArr; sec<sup>2</sup>(90&deg; - 36&deg;) - cot<sup>2</sup>36&deg; + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>sin<sup>2</sup>37&deg; &times; sec<sup>2</sup>(90&deg; - 37&deg;) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>tan 60&deg;<br>&rArr; cosec<sup>2</sup>36&deg; - cot<sup>2</sup>36&deg; + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>sin<sup>2</sup>37&deg; &times; cosec<sup>2</sup>37&deg; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>tan 60&deg;<br>&rArr; 1 +&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp;<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>75.(b)</p>\n<p>sec<sup>2</sup> 54&deg; - cot<sup>2</sup> 36&deg; + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>sin<sup>2 </sup>37&deg; &times; sec<sup>2</sup> 53&deg; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>tan60&deg;<br>&rArr; sec<sup>2</sup>(90&deg; - 36&deg;) - cot<sup>2</sup>36&deg; + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>sin<sup>2</sup>37&deg; &times; sec<sup>2</sup>(90&deg; - 37&deg;) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>tan 60&deg;<br>&rArr; cosec<sup>2</sup>36&deg; - cot<sup>2</sup>36&deg; + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>sin<sup>2</sup>37&deg; &times; cosec<sup>2</sup>37&deg; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>tan 60&deg;<br>&rArr; 1 +&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp;<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. Three different positions of the same dice are shown. Find the number on the face opposite to the face showing \'3\'.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998164.png\" alt=\"rId49\" width=\"222\" height=\"86\"></p>",
                    question_hi: "<p>76. एक ही पासे की तीन अलग-अलग स्थितियाँ दर्शाई गई हैं। \'3\' दर्शाने वाले फलक के विपरीत फलक पर कौन-सी संख्या होगी?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998164.png\" alt=\"rId49\" width=\"214\" height=\"83\"></p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>76.(b)<br>From figure 1 and 3 we get<br>Opposite face : - 6 &harr; 5 , 1 &harr; 4 , 2 &harr; 3</p>",
                    solution_hi: "<p>76.(b)<br>चित्र 1 और 3 से हमें प्राप्त होता है<br>विपरीत फलक:- 6 &harr; 5 , 1 &harr; 4 , 2 &harr; 3</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77.Which state achieved 100% ODF Plus Coverage of all its 95,767 villages, in Swachh Bharat Mission Grameen ?</p>",
                    question_hi: "<p>77. स्वच्छ भारत मिशन ग्रामीण में किस राज्य ने अपने सभी 95,767 गांवों में 100% ओडीएफ प्लस कवरेज हासिल किया?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Bihar</p>", 
                                "<p>West Bengal</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>बिहार</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>77.(d) <strong>Uttar Pradesh</strong> has attained a significant milestone by achieving 100% ODF Plus coverage in the Swachh Bharat Mission Grameen, with 95,767 villages declaring themselves ODF+ with solid and liquid waste management systems.<br>Over 80,000 villages achieved ODF Plus status in the past nine months, coinciding with the nationwide &lsquo;Swachhata Hi Seva&rsquo; campaign, where 88 lakh people participating in the state.</p>",
                    solution_hi: "<p>77.(d) <strong>उत्तर प्रदेश</strong> ने स्वच्छ भारत मिशन ग्रामीण में 100% ओडीएफ प्लस कवरेज हासिल करके एक महत्वपूर्ण उपलब्धि हासिल की है, जिसमें 95,767 गांवों ने ठोस और तरल अपशिष्ट प्रबंधन प्रणालियों के साथ खुद को ओडीएफ+ घोषित किया है।<br>राष्ट्रव्यापी \'स्वच्छता ही सेवा\' अभियान के साथ, पिछले नौ महीनों में 80,000 से अधिक गांवों ने ओडीएफ प्लस का दर्जा हासिल किया, जिसमें राज्य के 88 लाख लोगों ने भाग लिया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. Study the given pie-chart and answer the question that follows. <br>The pie-chart shows the expenditure incurred in the preparation of a book by a publisher, under various heads.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998308.png\" alt=\"rId50\" width=\"169\" height=\"157\"> <br>What is the difference between the angle of pie-chart showing the expenditure incurred on binding and printing?</p>",
                    question_hi: "<p>78. दिए गए पाई - चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए । <br>पाई - चार्ट एक प्रकाशक द्वारा पुस्तक तैयार करने में विभिन्न मदों के तहत किए गए व्यय को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998456.png\" alt=\"rId51\" width=\"177\" height=\"175\"> <br>बाइंडिंग और मुद्रण पर किए गए व्यय को दर्शाने वाले पाई - चार्ट के कोणों के बीच अंतर ज्ञात कीजिए ।</p>",
                    options_en: ["<p>15&deg;</p>", "<p>20&deg;</p>", 
                                "<p>18&deg;</p>", "<p>22&deg;</p>"],
                    options_hi: ["<p>15&deg;</p>", "<p>20&deg;</p>",
                                "<p>18&deg;</p>", "<p>22&deg;</p>"],
                    solution_en: "<p>78.(c)<br>(100) % = 360&deg;<br>According to question,<br>Required difference (30 - 25)%<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>100</mn></mfrac></math> &times; 5&nbsp;<br>= 5 &times; 3.6&deg;&nbsp;<br>= 18&deg;</p>",
                    solution_hi: "<p>78.(c)<br>(100) % = 360&deg;<br>प्रश्न के अनुसार,<br>आवश्यक अंतर (30 - 25)%&nbsp;<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>100</mn></mfrac></math> &times; 5&nbsp;<br>= 5 &times; 3.6&deg;&nbsp;<br>= 18&deg;</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. In Computer ﬁeld OLE is the abbreviation of</p>",
                    question_hi: "<p>79. कंप्यूटर क्षेत्र में OLE का संक्षिप्त रूप क्या है?</p>",
                    options_en: ["<p>Object Linking and Embedding</p>", "<p>Object Linking and Enabling</p>", 
                                "<p>Object Linking Extension</p>", "<p>Object Location Enabling</p>"],
                    options_hi: ["<p>ऑब्जेक्ट लिंकिंग एण्ड एम्बेडिंग</p>", "<p>ऑब्जेक्ट लिंकिंग एण्ड एनाबलिंग</p>",
                                "<p>ऑब्जेक्ट लिंकिंग एक्सटेंशन</p>", "<p>ऑब्जेक्ट लोकेशन एनाबलिंग</p>"],
                    solution_en: "<p>79.(a) <strong>Object Linking and Embedding.</strong> A technology developed by Microsoft for embedding objects created in one program into another. It is a mechanism that allows users to create and edit documents containing items created by multiple applications. (Example - Inserting Excel spreadsheets into Word documents).</p>",
                    solution_hi: "<p>79.(a) <strong>ऑब्जेक्ट लिंकिंग एण्ड एम्बेडिंग।</strong> एक प्रोग्राम में बनाई गई वस्तुओं को दूसरे प्रोग्राम में एम्बेड करने के लिए माइक्रोसॉफ्ट द्वारा विकसित एक तकनीक। यह एक ऐसा क्रियाविधि है जो उपयोगकर्ताओं को कई अनुप्रयोगों द्वारा बनाए गए आइटम(items) वाले दस्तावेज़ (documents) बनाने और संपादित करने की अनुमति देता है। (उदाहरण - वर्ड दस्तावेज़ों ( Word documents) में एक्सेल स्प्रेडशीट(Excel spreadsheets) सम्मिलित करना)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. Select the correct mirror image of the given figure when the mirror is placed at MN as shown. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998662.png\" alt=\"rId52\" width=\"120\" height=\"112\"></p>",
                    question_hi: "<p>80. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए जो नीचे दर्शाए गए दर्पण को MN पर रखने पर बनेगा। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998662.png\" alt=\"rId52\" width=\"124\" height=\"116\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998808.png\" alt=\"rId53\" width=\"78\" height=\"124\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998911.png\" alt=\"rId54\" width=\"73\" height=\"113\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999086.png\" alt=\"rId55\" width=\"56\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999229.png\" alt=\"rId56\" width=\"54\" height=\"95\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998808.png\" alt=\"rId53\" width=\"71\" height=\"113\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675998911.png\" alt=\"rId54\" width=\"64\" height=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999086.png\" alt=\"rId55\" width=\"63\" height=\"118\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999229.png\" alt=\"rId56\" width=\"60\" height=\"105\"></p>"],
                    solution_en: "<p>80.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999229.png\" alt=\"rId56\" width=\"65\" height=\"114\"></p>",
                    solution_hi: "<p>80.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999229.png\" alt=\"rId56\" width=\"65\" height=\"114\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. Which of the following is the assumption for the claim that &lsquo;Pleasure is desirable&rsquo; ?</p>",
                    question_hi: "<p>81. प्रसन्नता वांछनीय है\' के दावे के लिए निम्नलिखित में से कौन सी धारणा है ?</p>",
                    options_en: ["<p>Everyone desires pleasure.</p>", "<p>Everyone desires something.</p>", 
                                "<p>Some persons desire pleasure.</p>", "<p>Pleasure is essential.</p>"],
                    options_hi: ["<p>हर कोई प्रसन्नता चाहता है।</p>", "<p>हर कोई कुछ न कुछ चाहता है।</p>",
                                "<p>कुछ लोग सुख चाहते हैं।</p>", "<p>प्रसन्नता जरूरी है।</p>"],
                    solution_en: "<p>81.(a) The claim that &lsquo;Pleasure is desirable&rsquo; can only be derived from the assumption that &lsquo;Everyone desires pleasure&rsquo;. Things become desirable only when everyone likes them.</p>",
                    solution_hi: "<p>81.(a) यह दावा कि \'आनंद वांछनीय है\' केवल इस धारणा से प्राप्त किया जा सकता है कि \'हर कोई आनंद चाहता है\'। चीजें तभी वांछनीय होती हैं जब हर कोई उन्हें पसंद करे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. Read the given statement and conclusion carefully. Accordingly, the information given in the statements is true. Even if it appears in the commonly known form to decide which of the given conclusions logically follows from the statements..<br><strong>Statements :</strong><br>Doing exercise in the morning is good for health <br><strong>Conclusion 1</strong> <br>Health can be maintained by doing exercise only. <br><strong>Conclusion 2</strong> <br>Without doing exercise ,people will become ill.</p>",
                    question_hi: "<p>82. दिए गए कथन और निष्कर्ष को ध्यानपूर्वक पढिये। तदनुसार, कथनों में दी गई जानकारी सत्य है। भले ही यह सामान्य रूप से ज्ञात रूप में प्रतीत होता है कि यह बताइये कि दिए गए निष्कर्षों में से कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong><br>सुबह व्यायाम करना सेहत के लिए अच्छा होता है।<br><strong>निष्कर्ष 1</strong><br>व्यायाम करने से ही स्वास्थ्य को बनाए रखा जा सकता है।<br><strong>निष्कर्ष 2</strong><br>बिना व्यायाम किए लोग बीमार हो जाएंगे।</p>",
                    options_en: ["<p>Conclusion 1 follows the statement</p>", "<p>conclusion 2 follows the statement</p>", 
                                "<p>Both conclusions follow the statement</p>", "<p>None of the conclusions follow the statement.</p>"],
                    options_hi: ["<p>निष्कर्ष 1 कथन का अनुसरण करता है</p>", "<p>निष्कर्ष 2 कथन का अनुसरण करता है</p>",
                                "<p>दोनों निष्कर्ष कथन का अनुसरण करते हैं</p>", "<p>कोई भी निष्कर्ष कथन का अनुसरण नहीं करता है।</p>"],
                    solution_en: "<p>82.(d) From the given statement it is clear that only exercise is not necessary for good health , some other factors are also important .So none of the given conclusions follow the given statement.</p>",
                    solution_hi: "<p>82.(d) दिए गए कथन से यह स्पष्ट है कि&nbsp;अच्छे स्वास्थ्य के लिए केवल व्यायाम करना आवश्यक है, अन्य कारक भी महत्वपूर्ण हैं इसलिए दिए गए निष्कर्षों में से कोई भी दिए गए कथन का&nbsp;पालन नहीं करता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. Read the given statement and possible courses of action carefully and decide which of the courses of action logically follow(s) from the statement.<br><strong>Statement :</strong><br>If any of the faculty of sociology or social work sends his/her students to the inter-college debate, the college will definitely get some prizes.<br><strong>Courses of Action :</strong><br>(i) The management must make sure that either of the two faculties of sociology and social work makes his/her students participate in the debate. <br>(ii) The management must allow only the students of faculty of sociology and social work to take part in the debate. <br>(iii) Students from sociology or social work must be encouraged to participate in inter - college debates</p>",
                    question_hi: "<p>83. दिए गए कथन और क्रिया को ध्यान से पढ़ें और बताइये कि कौन सी क्रिया तार्किक रूप से कथन का अनुसरण करती है।<br><strong>कथन :</strong><br>यदि समाजशास्त्र या सामाजिक कार्य का कोई भी संकाय अपने छात्रों को अंतर-महाविद्यालय बहस में भेजता है, तो महाविद्यालय को निश्चित रूप से कुछ पुरस्कार प्राप्त होंगे।<br><strong>क्रिया :</strong><br>(i) प्रबंधन को यह सुनिश्चित करना चाहिए कि समाजशास्त्र और सामाजिक कार्य के दो संकायों में से कोई एक अपने छात्रों को बहस में भाग दिलवाता है।<br>(ii) प्रबंधन को केवल समाजशास्त्र और सामाजिक कार्य के संकाय के छात्रों को बहस में भाग लेने की अनुमति देनी चाहिए।<br>(iii) समाजशास्त्र या सामाजिक कार्य के छात्रों को अंतर-महाविद्यालय बहस में भाग लेने के लिए प्रोत्साहित किया जाना चाहिए।</p>",
                    options_en: ["<p>Only action (ii) follows.</p>", "<p>Only actions (i) and (iii) follow.</p>", 
                                "<p>Only actions (i) and (ii) follow.</p>", "<p>All the actions, (i), (ii) and (iii) follow</p>"],
                    options_hi: ["<p>केवल क्रिया (ii) अनुसरण करती है।</p>", "<p>केवल क्रियाएँ (i) और (iii) अनुसरण करती हैं।</p>",
                                "<p>केवल क्रियाएँ (i) और (ii) अनुसरण करती हैं।</p>", "<p>सभी क्रियाएं, (i), (ii) और (iii) अनुसरण करती हैं।</p>"],
                    solution_en: "<p>83.(b) Only actions (i) and (iii) follow.</p>",
                    solution_hi: "<p>83.(b)<br>केवल क्रियाएँ (i) और (iii) अनुसरण करती हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. What is the freezing point of water on the Kelvin scale?</p>",
                    question_hi: "<p>84. केल्विन पैमाने पर जल का हिमांक कितना होता है?</p>",
                    options_en: ["<p>173.15 K</p>", "<p>473.15 K</p>", 
                                "<p>373.15 K</p>", "<p>273.15 K</p>"],
                    options_hi: ["<p>173.15 K</p>", "<p>473.15 K</p>",
                                "<p>373.15 K</p>", "<p>273.15 K</p>"],
                    solution_en: "<p>84.(d) <strong>273.15 K</strong> (32 &deg;F or 0 &deg;C). The freezing point is the temperature at which a liquid freezes. <strong>Water</strong> (H<sub>2</sub>O) is an inorganic, transparent, tasteless and odorless substance. The <strong>boiling point</strong> of water is 100 &ordm;C. Thus required <strong>temperature conversion formulas</strong> are K (Kelvin) = C (centigrade) + 273.15 and C = {F (Fahrenheit) &minus; 32} &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math>. The Kelvin scale is named after <strong>William Thomson</strong>, also known as Lord Kelvin.</p>",
                    solution_hi: "<p>84.(d)<strong> 273.15 K</strong> (32 &deg;F या 0 &deg;C)। हिमांक वह ताप है जिस पर कोई तरल पदार्थ जम जाता है। <strong>जल</strong> (H<sub>2</sub>O) एक अकार्बनिक, पारदर्शी, स्वादहीन और गंधहीन पदार्थ है। पानी का<strong> क्वथनांक </strong>100 &ordm;C होता है। इस प्रकार आवश्यक <strong>ताप रूपांतरण सूत्र</strong> K (केल्विन) = C (सेंटीग्रेड) +273.15 और C = {F (फ़ारेनहाइट) - 32} &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math> हैं। केल्विन स्केल का नाम <strong>विलियम थॉमसन</strong> के नाम पर रखा गया है, जिन्हें लॉर्ड केल्विन के नाम से भी जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. <strong>Question:</strong><br>Which of these natural numbers X, Y, Z, U, and V are even numbers?<br><strong>Statements:</strong><br>1. X, Y, Z, U, and V are consecutive numbers.<br>2. Z is an odd number.</p>",
                    question_hi: "<p>85. <strong>प्रश्न:</strong><br>प्राकृतिक संख्या X, Y, Z, U और V में से कौन सी सम संख्या हैं?<br><strong>कथन:</strong> <br>1. X, Y, Z, U और V क्रमिक संख्या हैं।<br>2. Z विषम संख्या है।</p>",
                    options_en: ["<p>Both statement 1 and 2 together are sufficient</p>", "<p>Statement 2 alone is sufficient while statement 1 alone is insufficient</p>", 
                                "<p>Statement 1 alone is sufficient while statement 2 alone is insufficient</p>", "<p>Neither statement 1 nor 2 is sufficient</p>"],
                    options_hi: ["<p>कथन 1 और कथन 2 एक साथ दोनों पर्याप्त हैं</p>", "<p>केवल कथन 2 पर्याप्त है जबकि केवल कथन 1 अपर्याप्त है</p>",
                                "<p>केवल कथन 1 पर्याप्त है जबकि केवल कथन 2 अपर्याप्त है</p>", "<p>ना कथन 1 और ना ही कथन 2 पर्याप्त है</p>"],
                    solution_en: "<p>85.(a) <strong>From statement 1</strong> &rarr;<br>We know that in consecutive natural number we get even number alternatively<br><strong>From statement 2</strong> &rarr;<br>If z is odd numbers therefore x and v are even numbers<br>So , we can conclude that Both statement 1 and 2 together are sufficient</p>",
                    solution_hi: "<p>85.(a) <strong>कथन 1 से ,</strong><br>हम जानते हैं कि क्रमागत प्राकृतिक संख्या में हमें वैकल्पिक रूप से सम संख्या प्राप्त होती है<br><strong>कथन 2 से ,</strong><br>यदि z विषम संख्याएँ हैं तो x और v सम संख्याएँ हैं<br>इसलिए, हम यह निष्कर्ष निकाल सकते हैं कि कथन 1 और 2 दोनों एक साथ पर्याप्त हैं</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. In which year did India first participate in the Olympic games?</p>",
                    question_hi: "<p>86. भारत ने पहली बार ओलंपिक खेलों में किस वर्ष भाग लिया था?</p>",
                    options_en: ["<p>1925</p>", "<p>1923</p>", 
                                "<p>1924</p>", "<p>1900</p>"],
                    options_hi: ["<p>1925</p>", "<p>1923</p>",
                                "<p>1924</p>", "<p>1900</p>"],
                    solution_en: "<p>86.(d) <strong>1900.</strong> India first participated at the Olympic Games in 1900, with athlete Norman Pritchard winning two medals, both silver &ndash; in athletics, and India became the first Asian nation to win an olympic medal. <strong>Olympic Games, 2024 </strong>- It was held in Paris, France.</p>",
                    solution_hi: "<p>86.(d) <strong>1900</strong>। भारत ने पहली बार 1900 में ओलंपिक खेलों में भाग लिया, जिसमें एथलीट नॉर्मन प्रिचर्ड ने एथलेटिक्स में दो रजत पदक जीते, और भारत ओलंपिक पदक जीतने वाला पहला एशियाई देश बन गया। <strong>ओलंपिक खेल</strong>, <strong>2024</strong> - यह पेरिस, फ्रांस में आयोजित किया गया ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. Who devised the policy of Doctrine of Lapse?</p>",
                    question_hi: "<p>87. हड़प नीति की शुरुआत किसने की ?</p>",
                    options_en: ["<p>Lord Clive</p>", "<p>General Dyer</p>", 
                                "<p>Lord Dalhousie</p>", "<p>Lord Hastings</p>"],
                    options_hi: ["<p>लॉर्ड क्लाइव</p>", "<p>जनरल डायर</p>",
                                "<p>लॉर्ड डलहौजी</p>", "<p>हेस्टिंग्स</p>"],
                    solution_en: "<p>87.(c) <strong>Lord Dalhousie </strong>: Governor-general of India (1848-56). This doctrine deals with the issues of succession to hindu states of India. By the use of the doctrine of lapse, the Company took over the princely states of Satara (1848), Jaitpur, Sambalpur (1849), Baghat (1850), Udaipur (Chhattisgarh State) (1852), Jhansi (1854), Nagpur (1854) and Tanjore and Arcot (1855).</p>",
                    solution_hi: "<p>87.(c) <strong>लॉर्ड डलहौजी</strong> : भारत के गवर्नर-जनरल (1848-56)। यह सिद्धांत भारत के हिंदू राज्यों के उत्तराधिकार के मुद्दों से संबंधित है। व्यपगत के सिद्धांत(हड़प नीति) के प्रयोग से कंपनी ने सतारा (1848), जैतपुर, संबलपुर (1849), बघाट (1850), उदयपुर (छत्तीसगढ़ राज्य) (1852), झाँसी (1854), नागपुर (1854) और तंजौर तथा आरकोट (1855) की रियासतों पर कब्ज़ा कर लिया।।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. Six students Meera, Jigyasa, Tarun, Naina, Shambhavi and Avni are sitting around a circular table facing the centre (not necessarily in the same order). Shambhavi is an immediate neighbour of both Jigyasa and Avni. Tarun is sitting third to the left of Jigyasa. Meera is sitting second to the right of Avni.&nbsp;Who is sitting third to the right of Meera ?</p>",
                    question_hi: "<p>88. छ: विद्यार्थी मीरा, जिज्ञासा, तरुण, नैना, शाम्भवी और अवनी एक वृत्ताकार मेज के परितः केंद्र की ओर मुख करके बैठे हैं (उनका इसी क्रम में होना अनिवार्य नहीं है)। शाम्भवी, जिज्ञासा और अवनी दोनों के ठीक बगल में है। तरुण, जिज्ञासा के बाईं ओर तीसरे स्थान पर बैठा है। मीरा, अवनी के दाईं ओर दूसरे स्थान पर बैठी है।&nbsp;मीरा के दाईं ओर तीसरे स्थान पर कौन बैठा/बैठी है ?</p>",
                    options_en: ["<p>Shambhavi</p>", "<p>Naina</p>", 
                                "<p>Jigyasa</p>", "<p>Avni</p>"],
                    options_hi: ["<p>शाम्भवी</p>", "<p>नैना</p>",
                                "<p>जिज्ञासा</p>", "<p>अवनी</p>"],
                    solution_en: "<p>88.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999372.png\" alt=\"rId57\" width=\"201\" height=\"141\"></p>",
                    solution_hi: "<p>88.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999503.png\" alt=\"rId58\" width=\"189\" height=\"140\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. Which is India\'s newest nuclear power plant?</p>",
                    question_hi: "<p>89. भारत का नवीनतम परमाणु ऊर्जा संयंत्र कौन सा है?</p>",
                    options_en: ["<p>Kudankulam</p>", "<p>Kaiga</p>", 
                                "<p>Kalpakkam</p>", "<p>Tarapur</p>"],
                    options_hi: ["<p>कुडनकुलम</p>", "<p>कैगा</p>",
                                "<p>कलपक्कम</p>", "<p>तारापुर</p>"],
                    solution_en: "<p>89.(a) <strong>Kudankulam</strong> (2013, Tirunelveli district, Tamil Nadu). <strong>Tarapur</strong> (first commercial nuclear power station - 1969, Maharashtra), <strong>Kaiga</strong> (2000, Karnataka), <strong>Kalpakkam</strong> (1984, Tamil Nadu). Father of India&rsquo;s nuclear program - <strong>Dr. Homi Jehangir Bhabha.</strong></p>",
                    solution_hi: "<p>89.(a) ​​<strong>कुडनकुलम</strong> (2013, तिरुनेलवेली जिला, तमिलनाडु)। <strong>तारापुर </strong>(प्रथम वाणिज्यिक परमाणु ऊर्जा स्टेशन - 1969, महाराष्ट्र), <strong>कैगा</strong> (2000, कर्नाटक), <strong>कलपक्कम</strong> (1984, तमिलनाडु)। भारत के परमाणु कार्यक्रम के जनक - <strong>डॉ. होमी जहांगीर भाभा।</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. How many such consonants are there in the following arrangement, each of which is immediately followed by a vowel but not preceded by a number?<br>T R B 5 0 % U 7 C 4 # K F S 2 U E * 1 8 I 3 V @ 9 I X @ L A B</p>",
                    question_hi: "<p>90. निम्नलिखित व्यवस्था में ऐसे कितने व्यंजन हैं जिनमें से प्रत्येक के ठीक बाद एक स्वर है लेकिन पहले एक संख्या नहीं है?<br>T R B 5 0 % U 7 C 4 # K F S 2 U E * 1 8 I 3 V @ 9 I X @ L A B</p>",
                    options_en: ["<p>Two</p>", "<p>More than three</p>", 
                                "<p>Three</p>", "<p>One</p>"],
                    options_hi: ["<p>दो</p>", "<p>तीन से अधिक</p>",
                                "<p>तीन</p>", "<p>एक</p>"],
                    solution_en: "<p>90.(d) Here in case of only [@LA], consonant L is immediately followed by a vowel A but not preceded by a number, i.e. only one case.</p>",
                    solution_hi: "<p>90.(d) यहाँ केवल [@LA] के मामले में, व्यंजन L के ठीक बाद एक स्वर A है लेकिन पहले संख्या नहीं है ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. Read the given information carefully and answer the questions that follow.<br>Six students P, Q, R, S, T and U are the top six rank holders in a school. The rank of Q is between the rank of R and S. Q is the fourth rank. There are two students between the ranks of T and S. T is at the lowest rank among them. The rank of U is immediately above the rank of P. Who is at the fifth rank?</p>",
                    question_hi: "<p>91. दी गई जानकारी को ध्यान से पढ़ें और नीचे दिए गए प्रश्नों के उत्तर दें।<br>छह छात्र P, Q, R, S, T और U एक स्कूल में शीर्ष छह रैंक धारक हैं। Q की रैंक R और S की रैंक के बीच है। Q चौथी रैंक है। T और S की रैंक के बीच दो छात्र हैं। T उनमें से सबसे निचले रैंक पर है। U की रैंक, P के रैंक से ठीक ऊपर है। पांचवें स्थान पर कौन है?</p>",
                    options_en: ["<p>T</p>", "<p>P</p>", 
                                "<p>S</p>", "<p>R</p>"],
                    options_hi: ["<p>T</p>", "<p>P</p>",
                                "<p>S</p>", "<p>R</p>"],
                    solution_en: "<p>91.(d) U &gt; P &gt; S &gt; Q &gt; <strong>R</strong> &gt; T</p>",
                    solution_hi: "<p>91.(d) U &gt; P &gt; S &gt; Q &gt; <strong>R</strong> &gt; T</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. Amit Khare, whose tenure has been extended, was holding which position?</p>",
                    question_hi: "<p>92. अमित खरे, जिनका कार्यकाल बढ़ाया गया है, किस पद पर थे?</p>",
                    options_en: ["<p>CAG Chairman</p>", "<p>Adviser to Prime Minister</p>", 
                                "<p>NITI Aayog Vice Chairman</p>", "<p>Chief Election Commissioner</p>"],
                    options_hi: ["<p>सीएजी अध्यक्ष</p>", "<p>प्रधान मंत्री के सलाहकार</p>",
                                "<p>नीति आयोग के उपाध्यक्ष</p>", "<p>मुख्य चुनाव आयुक्त</p>"],
                    solution_en: "<p>92.(b) The Center has granted an extension to Amit Khare, a retired IAS officer, to continue serving as an Adviser to the Prime Minister. His extension will be in effect until the Prime Minister&rsquo;s term concludes, aligning with the Prime Minister&rsquo;s tenure. Khare assumed the position of Adviser to Prime Minister Narendra Modi under a two-year contract, starting from 2021.</p>",
                    solution_hi: "<p>92.(b) केंद्र ने सेवानिवृत्त आईएएस अधिकारी अमित खरे को प्रधानमंत्री के सलाहकार के रूप में काम जारी रखने के लिए विस्तार दिया है। उनका विस्तार प्रधान मंत्री के कार्यकाल के अनुरूप, प्रधान मंत्री का कार्यकाल समाप्त होने तक प्रभावी रहेगा। खरे ने 2021 से शुरू होने वाले दो साल के अनुबंध के तहत प्रधान मंत्री नरेंद्र मोदी के सलाहकार का पद संभाला।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. Arrange the following words in the correct order as per the English Dictionary.<br>1. Pandemonium<br>2. Pandemic<br>3. Pandering<br>4. Pandanus<br>5. Pandarus</p>",
                    question_hi: "<p>93. निम्नलिखित शब्दों को अंग्रेजी शब्दकोश के अनुसार सही क्रम में व्यवस्थित करें।<br>1. Pandemonium<br>2. Pandemic<br>3. Pandering<br>4. Pandanus<br>5. Pandarus</p>",
                    options_en: ["<p>4, 5, 3, 1, 2</p>", "<p>4, 5, 1, 2, 3</p>", 
                                "<p>4, 5, 1, 3, 2</p>", "<p>4, 5, 2, 1, 3</p>"],
                    options_hi: ["<p>4, 5, 3, 1, 2</p>", "<p>4, 5, 1, 2, 3</p>",
                                "<p>4, 5, 1, 3, 2</p>", "<p>4, 5, 2, 1, 3</p>"],
                    solution_en: "<p>93.(d)<br>Correct order is <br>Pandanus(4) &rarr; Pandarus(5) &rarr; Pandemic(2) &rarr; Pandemonium(1) &rarr; Pandering(3)</p>",
                    solution_hi: "<p>93.(d)<br>सही क्रम है <br>Pandanus(4) &rarr; Pandarus(5) &rarr; Pandemic(2) &rarr; Pandemonium(1) &rarr; Pandering(3)</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. Which of the following is the classical dance form of erstwhile Andhra Pradesh?</p>",
                    question_hi: "<p>94. निम्नलिखित में से कौन-सा पूर्ववर्ती आंध्र प्रदेश का शास्त्रीय नृत्य है?</p>",
                    options_en: ["<p>Kathakali</p>", "<p>Bharatnatyam</p>", 
                                "<p>Kathak</p>", "<p>Kuchipudi</p>"],
                    options_hi: ["<p>कथकली</p>", "<p>भरतनाट्यम</p>",
                                "<p>कथक</p>", "<p>कुचिपुड़ी</p>"],
                    solution_en: "<p>94.(d) <strong>Kuchipudi</strong>. It originated in a village named Kuchipudi in the Indian state of Andhra Pradesh. The Cultural Ministry of India includes Chhau in the list of classical dances making a total of 9 classical dance forms. Other eight classical dances - Bharatanatyam (Tamil Nadu), Kathak (Uttar Pradesh), Kuchipudi (Andhra Pradesh), Odissi (Odisha), Kathakali (Kerala), Sattriya (Assam), Manipuri (Manipur) and Mohiniyattam (Kerala). </p>",
                    solution_hi: "<p>94.(d) <strong>कुचिपुड़ी</strong>। इसकी उत्पत्ति भारत के आंध्र प्रदेश राज्य के कुचिपुड़ी नामक गाँव में हुई थी। भारत के सांस्कृतिक मंत्रालय ने छऊ को भी शास्त्रीय नृत्यों की सूची में शामिल किया है, जिससे कुल 9 शास्त्रीय नृत्य रूप बनते हैं। अन्य आठ शास्त्रीय नृत्य - भरतनाट्यम (तमिलनाडु), कथक (उत्तर प्रदेश), कुचिपुड़ी (आंध्र प्रदेश), ओडिसी (ओडिशा), कथकली (केरल), सत्त्रिया (असम), मणिपुरी (मणिपुर) और मोहिनीअट्टम (केरल)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. In a certain code language, \'rocket airplane sky stars\' is written as \'ci pi di ki\' and \'satellite moon sun sky\' is written as \'ni ci ui li\' and \'sky sun stars mars\' is written as \'ui gi ki ci\' and \'venus airplane moon planet\' is written as \'di qi mi ni\' How is \'rocket\' written in the given language ?</p>",
                    question_hi: "<p>95. एक निश्चित कूट भाषा में \'rocket airplane sky stars\' को \'ci pi di ki\' लिखा जाता है और \'satellite moon sun sky\' को \'ni ci ui li\' लिखा जाता है और \'sky sun stars mars\' को \'ui gi ki ci\' लिखा जाता है और \'venus airplane moon planet\' को \'di qi mi ni\' लिखा जाता है। उसी कूट भाषा में \'rocket को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>ci</p>", "<p>ki</p>", 
                                "<p>pi</p>", "<p>di</p>"],
                    options_hi: ["<p>ci</p>", "<p>ki</p>",
                                "<p>pi</p>", "<p>di</p>"],
                    solution_en: "<p>95.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999696.png\" alt=\"rId59\" width=\"316\" height=\"100\"><br>As we can see, code for rocket will be pi</p>",
                    solution_hi: "<p>95.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999696.png\" alt=\"rId59\" width=\"316\" height=\"100\"><br>जैसा की हम देख सकते हैं rocket का कोड pi होगा .</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. Which branch of physics deals with properties of ﬂuids at rest?</p>",
                    question_hi: "<p>96. भौतिकी की कौन सी शाखा विराम अवस्था में द्रवों के गुणों से संबंधित है?</p>",
                    options_en: ["<p>Optics</p>", "<p>Thermodynamics</p>", 
                                "<p>Hydrostatics</p>", "<p>Astrophysics</p>"],
                    options_hi: ["<p>प्रकाशिकी</p>", "<p>थर्मोडायनामिक्स</p>",
                                "<p>हाइड्रोस्टैटिक्स</p>", "<p>खगोल भौतिकी</p>"],
                    solution_en: "<p>96.(c) <strong>Hydrostatics:</strong> Particularly with the pressure in a fluid or exerted by a fluid (gas or liquid) on an immersed body. <strong>Optics: </strong>The branch of physics that studies the behavior and properties of light. <strong>Thermodynamics:</strong> The branch of physics that deals with the relationships between heat and other forms of energy. <strong>Astrophysics:</strong> The branch of physics that deals with the physical properties of celestial objects.</p>",
                    solution_hi: "<p>96.(c) <strong>हाइड्रोस्टैटिक्स</strong>: विशेष रूप से किसी तरल पदार्थ में दाब के साथ, या किसी तरल पदार्थ (गैस या द्रव) द्वारा डूबे हुए वस्तु पर दाब के साथ। <strong>प्रकाशिकी(Optics):</strong> भौतिकी की वह शाखा जो प्रकाश के व्यवहार और गुणों का अध्ययन करती है। <strong>ऊष्मागतिकी (थर्मोडायनामिक्स)</strong> : भौतिकी की वह शाखा जो ऊष्मा और ऊर्जा के अन्य रूपों के बीच संबंधों को दर्शाती है। <strong>खगोल भौतिकी:</strong> भौतिकी की वह शाखा जो आकाशीय पिंडों के भौतिक गुणों से संबंधित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown below. Choose the figure that would most closely resemble the unfolded form of the paper.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999923.png\" alt=\"rId60\" width=\"242\" height=\"77\"></p>",
                    question_hi: "<p>97. कागज के एक टुकड़े को मोड़ने का क्रम और मोड़े गए कागज को काटने का तरीका नीचे दर्शाया गया है । उस आकृति का चयन करें जो कागज को खोलने पर उसके खुले रूप से सबसे अधिक मिलती जुलती होगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727675999923.png\" alt=\"rId60\" width=\"226\" height=\"72\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676000185.png\" alt=\"rId61\" width=\"95\" height=\"102\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676000566.png\" alt=\"rId62\" width=\"94\" height=\"98\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676000950.png\" alt=\"rId63\" width=\"95\" height=\"102\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676001222.png\" alt=\"rId64\" width=\"96\" height=\"103\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676000185.png\" alt=\"rId61\" width=\"95\" height=\"102\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676000566.png\" alt=\"rId62\" width=\"96\" height=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676000950.png\" alt=\"rId63\" width=\"94\" height=\"101\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676001222.png\" alt=\"rId64\" width=\"95\" height=\"102\"></p>"],
                    solution_en: "<p>97.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676000950.png\" alt=\"rId63\" width=\"94\" height=\"101\"></p>",
                    solution_hi: "<p>97.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676000950.png\" alt=\"rId63\" width=\"94\" height=\"101\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. The ﬁrst high court of India was established in __________</p>",
                    question_hi: "<p>98. भारत का पहला उच्च न्यायालय कहाँ स्थापित किया गया था ?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Kolkata</p>", 
                                "<p>Mumbai</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>कोलकाता</p>",
                                "<p>मुंबई</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>98.(b) <strong>Kolkata. Kolkata High Court -</strong> It was formally opened on 1 July 1862. <strong>Sir Barnes Peacock -</strong> He was the first Chief Justice of the Calcutta High Court. Justice Sumboo Nath Pandit was the first Indian to assume office as a Judge of the Calcutta High Court. The Chief Justice of the High Court is appointed by the President after Consultation with the Chief Justice of India and the Governor of the concerned State .</p>",
                    solution_hi: "<p>98.(b) <strong>कोलकाता। कोलकाता उच्च न्यायालय -</strong> इसे औपचारिक रूप से 1 जुलाई 1862 को खोला गया था। <strong>सर बार्न्स पीकॉक</strong> - कलकत्ता उच्च न्यायालय के प्रथम मुख्य न्यायाधीश। <strong>न्यायमूर्ति सुंबू नाथ पंडित</strong> कलकत्ता उच्च न्यायालय के न्यायाधीश के रूप में पद संभालने वाले पहले भारतीय थे। उच्च न्यायालय के मुख्य न्यायाधीश की नियुक्ति भारत के मुख्य न्यायाधीश और संबंधित राज्य के राज्यपाल के परामर्श के बाद राष्ट्रपति द्वारा की जाती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. A person starts from point Z and moves 7 km towards the South. He turns right and moves 5 km, turns right, and moves 3 km, then turns right and moves 1 km. He takes a left turn and moves 4 km to reach a point X. How much and in which direction does he need to move now to reach point Z ?</p>",
                    question_hi: "<p>99. एक व्यक्ति बिंदु Z से चलना शुरू करता है, और दक्षिण की ओर 7 km चलता है। वह दाएं मुड़ता है, और 5 km चलता है, दाएं मुड़ता है, और 3 km चलता है, फिर दाएं मुड़ता है, और 1 km चलता है। फिर वह बाएं मुड़ता है, और बिंदु X पर पहुंचने के लिए 4 km चलता है। बिंदु Z पर पहुंचने के लिए अब उसे कितना और किस दिशा में चलना होगा ?</p>",
                    options_en: ["<p>4 km East</p>", "<p>4 km West</p>", 
                                "<p>6 km East</p>", "<p>4 km South</p>"],
                    options_hi: ["<p>4 km पूर्व</p>", "<p>4 km पश्चिम</p>",
                                "<p>6 km पूर्व</p>", "<p>4 km दक्षिण</p>"],
                    solution_en: "<p>99.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676001478.png\" alt=\"rId65\" width=\"186\" height=\"176\"><br>He has to move 4 km towards east</p>",
                    solution_hi: "<p>99.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727676001603.png\" alt=\"rId66\" width=\"166\" height=\"179\"><br>उसे 4 किमी पूर्व की ओर चलना है</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. The ability of metals to be drawn into thin wires is called</p>",
                    question_hi: "<p>100. धातुओं के पतले तारों में खींचे जाने की क्षमता____________कहलाती है।</p>",
                    options_en: ["<p>ductility</p>", "<p>solubility</p>", 
                                "<p>reactivity</p>", "<p>malleability</p>"],
                    options_hi: ["<p>तन्यता</p>", "<p>विलेयता</p>",
                                "<p>अभिक्रियाशीलता</p>", "<p>आघातवर्धनीयता</p>"],
                    solution_en: "<p>100.(a) <strong>Ductility. Solubility </strong>- It is defined as the maximum amount of a substance that will dissolve in a given amount of solvent at a specified temperature. <strong>Reactivity</strong> - It is the rate at which a chemical substance tends to undergo a chemical reaction. <strong>Malleability</strong> - The property of deforming a material on being pressed (or under compressive stress) and spreading in the direction perpendicular to the pressure is called malleability.</p>",
                    solution_hi: "<p>100.(a) <strong>तन्यता । विलेयता</strong> - इसे किसी पदार्थ की अधिकतम मात्रा के रूप में परिभाषित किया जाता है जो एक निर्दिष्ट तापमान पर विलायक की एक निश्चित मात्रा में घुल जाएगी। <strong>अभिक्रियाशीलता </strong>- यह वह दर है जिस पर एक रासायनिक पदार्थ रासायनिक अभिक्रिया से होकर गुजरता है। <strong>आघातवर्धनीयता</strong> -किसी पदार्थ को दबाने पर (या संपीडक प्रतिबल की स्थिति में) विकृत होकर दाब के लम्बवत दिशा में फैलने का गुण आघातवर्धनीयता (Malleability) कहलाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[indx];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)}
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>