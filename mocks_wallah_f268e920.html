<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Choose the correct meaning of the underlined word in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">orchard</span></span><span style=\"font-family: Cambria Math;\"> was a peaceful retreat, with the sound of leaves rustling in the wind and birds chirping in the trees.</span></p>\\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Choose the correct meaning of the underlined word in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">orchard</span></span><span style=\"font-family: Cambria Math;\"> was a peaceful retreat, with the sound of leaves rustl</span><span style=\"font-family: Cambria Math;\">ing in the wind and birds chirping in the trees.</span></p>\\n",
                    options_en: ["<p>A piece of land with lots of houses</p>\\n", "<p>A collection of farmlands</p>\\n", 
                                "<p>A collection of apartments</p>\\n", "<p>A piece of enclosed land with fruit trees</p>\\n"],
                    options_hi: ["<p>A piece of land with lots of houses</p>\\n", "<p>A collection of farmlands</p>\\n",
                                "<p>A collection of apartments</p>\\n", "<p>A piece of enclosed land with fruit trees</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span><span style=\"font-family: Cambria Math;\"> Orchard - a piece of enclosed land </span><span style=\"font-family: Cambria Math;\">with fruit trees.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span><span style=\"font-family: Cambria Math;\"> Orchard - a piece of enclosed land with fruit trees. / </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2325;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2367;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2370;&#2350;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2369;&#2325;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Make up one\'s mind</span></p>\\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the given idiom.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Make up one\'s mind</span></p>\\n",
                    options_en: ["<p>To disturb</p>\\n", "<p>To leave</p>\\n", 
                                "<p>To decide</p>\\n", "<p>To cheat</p>\\n"],
                    options_hi: ["<p>To disturb</p>\\n", "<p>To leave</p>\\n",
                                "<p>To decide</p>\\n", "<p>To cheat</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\"> Make up one&rsquo;s mind - to decide.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">E.g</span><span style=\"font-family: Cambria Math;\">.-</span><span style=\"font-family: Cambria Math;\"> Sarah couldn\'t decide between pizza and sushi for dinner, but after thinking for a while, she finally made up her mind and chose pizza.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\"> Make up one&rsquo;s mind - to decide./ </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2339;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">E.g</span><span style=\"font-family: Cambria Math;\">.-</span><span style=\"font-family: Cambria Math;\"> Sarah couldn\'t decide between pizza and sushi for</span><span style=\"font-family: Cambria Math;\"> dinner, but after thinking for a while, she finally made up her mind and chose pizza.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the grammatically correct version of the following sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">I like my puppies, Mac and Matt, but I like Matt best.</span></p>\\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> Sel</span><span style=\"font-family: Cambria Math;\">ect the grammatically correct version of the following sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">I like my puppies, Mac and Matt, but I like Matt best.</span></p>\\n",
                    options_en: ["<p>I like my puppies, Mac and Matt, but I like Matt better.</p>\\n", "<p>I like my puppies, Mac and Matt, but I like Matt the best.</p>\\n", 
                                "<p>I like my puppies, Mac and Matt, but I like Matt much.</p>\\n", "<p>I like my puppies, Mac and Matt, but I like Matt most.</p>\\n"],
                    options_hi: ["<p>I like my puppies, Mac and Matt, but I like Matt better.</p>\\n", "<p>I like my puppies, Mac and Matt, but I like Matt the best.</p>\\n",
                                "<p>I lik<span style=\"font-family: Cambria Math;\">e my puppies, Mac and Matt, but I like Matt much.</span></p>\\n", "<p>I like my puppies, Mac and Matt, but I like Matt most.</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-weight: 400;\">I like my puppies, Mac and Matt, but I like Matt better. (Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) I like my puppies, Mac and Matt, but I like Matt </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">the best</span></span><span style=\"font-weight: 400;\">. (Incorrect Degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(c) I like my puppies, Mac and Matt, but I like Matt </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">much</span></span><span style=\"font-weight: 400;\">. (Incorrect Degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) I like my puppies, Mac and Matt, but I like Matt </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">most</span></span><span style=\"font-weight: 400;\">. (Incorrect Degree)</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-weight: 400;\">I like my puppies, Mac and Matt, but I like Matt better. (Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) I like my puppies, Mac and Matt, but I like Matt </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">the best</span></span><span style=\"font-weight: 400;\">. (&#2327;&#2354;&#2340; Degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(c) I like my puppies, Mac and Matt, but I like Matt </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">much</span></span><span style=\"font-weight: 400;\">. (&#2327;&#2354;&#2340; Degree)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) I like my puppies, Mac and Matt, but I like Matt </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">most</span></span><span style=\"font-weight: 400;\">. (&#2327;&#2354;&#2340; Degree)</span></span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the most appr</span><span style=\"font-family: Cambria Math;\">opriate meaning of the underlined phrase in the following sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">As the actors gathered backstage, the director shouted, </span><span style=\"font-family: Cambria Math;\">\"<span style=\"text-decoration: underline;\">Break a leg</span></span><span style=\"font-family: Cambria Math;\">, everyone!\" before they took the stage for their opening night performance.</span></p>\\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the underlined phrase in the following sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">As the actors gathered backstage, the director shouted, </span><span style=\"font-family: Cambria Math;\">\"<span style=\"text-decoration: underline;\">Break a leg</span></span><span style=\"font-family: Cambria Math;\">, everyone!\" before they too</span><span style=\"font-family: Cambria Math;\">k the stage for their opening night performance.</span></p>\\n",
                    options_en: ["<p>To get a good night\'s sleep</p>\\n", "<p>To w<span style=\"font-family: Cambria Math;\">ish someone good luck</span></p>\\n", 
                                "<p>To take a risk</p>\\n", "<p>To keep a secret</p>\\n"],
                    options_hi: ["<p>To get a good night\'s sleep</p>\\n", "<p>To wish someone good luck</p>\\n",
                                "<p>To take a risk</p>\\n", "<p>To keep a secret</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> Break a leg - to wish someone good luck.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> Break a leg - to wish someone good luck./</span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2349;&#2325;&#2366;&#2350;&#2344;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM to replace the underlined word in the following sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He was a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">liberal</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">who believed that men were inherently superior to women in every aspect of life.</span></p>\\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM to replace the underlined word in the following sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He wa</span><span style=\"font-family: Cambria Math;\">s a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">liberal</span></span><span style=\"font-family: Cambria Math;\"> who believed that men were inherently superior to women in every aspect of life.</span></p>\\n",
                    options_en: ["<p>conservative</p>\\n", "<p>tolerant</p>\\n", 
                                "<p>republican</p>\\n", "<p>philanthropist</p>\\n"],
                    options_hi: ["<p>conservative</p>\\n", "<p>tolerant</p>\\n",
                                "<p>republican</p>\\n", "<p>philanthropist</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Conservative</strong><span style=\"font-weight: 400;\">- opposed to change or innovation and holding traditional values.</span></p>\\r\\n<p><strong>Liberal- </strong><span style=\"font-weight: 400;\">willing to respect or accept behaviour or opinions different from one\'s own.</span></p>\\r\\n<p><strong>Tolerant-</strong><span style=\"font-weight: 400;\"> having the ability to bear something unpleasant or annoying.</span></p>\\r\\n<p><strong>Republican-</strong><span style=\"font-weight: 400;\"> a person who supports the idea of countries having a president, rather than a king or queen.</span></p>\\r\\n<p><strong>Philanthropist-</strong><span style=\"font-weight: 400;\"> a person who seeks to promote the welfare of others.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Conservative </strong><span style=\"font-weight: 400;\">(&#2352;&#2370;&#2338;&#2364;&#2367;&#2357;&#2366;&#2342;&#2368;) - opposed to change or innovation and holding traditional values.</span></p>\\r\\n<p><strong>Liberal </strong><span style=\"font-weight: 400;\">(&#2313;&#2342;&#2366;&#2352;&#2357;&#2366;&#2342;&#2368;) - willing to respect or accept behaviour or opinions different from one\'s own.</span></p>\\r\\n<p><strong>Tolerant </strong><span style=\"font-weight: 400;\">(&#2360;&#2361;&#2344;&#2358;&#2368;&#2354;) - having the ability to bear something unpleasant or annoying.</span></p>\\r\\n<p><strong>Republican </strong><span style=\"font-weight: 400;\">(&#2327;&#2339;&#2340;&#2306;&#2340;&#2381;&#2352;&#2357;&#2366;&#2342;&#2368;)- a person who supports the idea of countries having a president, rather than a king or queen.</span></p>\\r\\n<p><strong>Philanthropist </strong><span style=\"font-weight: 400;\">(&#2346;&#2352;&#2379;&#2346;&#2325;&#2366;&#2352;&#2368;) - a person who seeks to promote the welfare of others.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6<span style=\"font-family: Cambria Math;\">. Select the most appropriate option that can substitute the underlined segment in the following sentence. If there is no need to substitute it, select \'No substitution required\'.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">I enjoy </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">learn</span></span><span style=\"font-family: Cambria Math;\"> languages because I like them.</span></p>\\n",
                    question_hi: "<p>6<span style=\"font-family: Cambria Math;\">. Select the most appropriate option that can substitute the underlined segment in the following sentence. If there is no need to substitute it, select \'No substitution required\'.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">I enjoy </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">learn</span></span><span style=\"font-family: Cambria Math;\"> languages because I like them.</span></p>\\n",
                    options_en: ["<p>to learning</p>\\n", "<p>lean</p>\\n", 
                                "<p>learning</p>\\n", "<p>No substitution required</p>\\n"],
                    options_hi: ["<p>to learning</p>\\n", "<p>lean</p>\\n",
                                "<p><span style=\"font-family: Cambria Math;\"> learning</span></p>\\n", "<p>No substitution required</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c) </span><strong><span style=\"font-family: Cambria Math;\">learning</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">There are certain verbs which always take a </span><span style=\"font-family: Cambria Math;\">gerund(</span><span style=\"font-family: Cambria Math;\">V</span><sub><span style=\"font-family: Cambria Math;\">ing</span></sub><span style=\"font-family: Cambria Math;\">) after them. These verbs include &lsquo;love, enjoy, stop, suggest, consider, etc&rsquo;. Hence, </span><span style=\"font-family: Cambria Math;\">&lsquo;learning(V<sub>ing</sub></span><span style=\"font-family: Cambria Math;\">)&rsquo; is the most appropriate answe</span><span style=\"font-family: Cambria Math;\">r.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c) </span><strong><span style=\"font-family: Cambria Math;\">learning</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> verb </span><span style=\"font-family: Cambria Math;\">&#2320;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;&#2375;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">gerund(V<sub>ing</sub></span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> verbs </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> &lsquo;love, enjoy, stop, suggest, consider </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;learning(V<sub>ing</sub></span><span style=\"font-family: Cambria Math;\">)&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-</span><span style=\"font-family: Cambria Math;\">word substitute for the given group of words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Argue loudly about unimportant things</span></p>\\n",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the given group of words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Argue loudly about unimportant things</span></p>\\n",
                    options_en: ["<p>Maintain</p>\\n", "<p>Disagree</p>\\n", 
                                "<p>Agree</p>\\n", "<p>Squabble</p>\\n"],
                    options_hi: ["<p>Maintain</p>\\n", "<p>Disagree</p>\\n",
                                "<p>Agree</p>\\n", "<p>Squabble</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><strong>Squabble</strong><span style=\"font-weight: 400;\"> - to argue loudly about unimportant things.</span></p>\\r\\n<p><strong>Maintain - </strong><span style=\"font-weight: 400;\">to keep in existence.</span></p>\\r\\n<p><strong>Disagree - </strong><span style=\"font-weight: 400;\">to not have the same opinion or idea.</span></p>\\r\\n<p><strong>Agree - </strong><span style=\"font-weight: 400;\">to have the same opinion about something.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><strong>Squabble </strong><span style=\"font-weight: 400;\">(&#2333;&#2327;&#2337;&#2364;&#2344;&#2366;) - to argue loudly about unimportant things.</span></p>\\r\\n<p><strong>Maintain</strong><span style=\"font-weight: 400;\"> (&#2348;&#2344;&#2366;&#2319; &#2352;&#2326;&#2344;&#2366;) -</span><strong> </strong><span style=\"font-weight: 400;\">to keep in existence.&nbsp;</span></p>\\r\\n<p><strong>Disagree </strong><span style=\"font-weight: 400;\">(&#2309;&#2360;&#2361;&#2350;&#2340; &#2361;&#2379;&#2344;&#2366;) - to not have the same opinion or idea.</span></p>\\r\\n<p><strong>Agree (</strong><span style=\"font-weight: 400;\">&#2360;&#2361;&#2350;&#2340; &#2361;&#2379;&#2344;&#2366;) - to have the same opinion about something.</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Hostile</span></p>\\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Hostile</span></p>\\n",
                    options_en: ["<p>Hospitable</p>\\n", "<p>Bitter</p>\\n", 
                                "<p>Nasty</p>\\n", "<p>Aggressive</p>\\n"],
                    options_hi: ["<p>Hospitable</p>\\n", "<p>Bitter</p>\\n",
                                "<p>Nasty</p>\\n", "<p>Aggressive</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Hospitable - </strong><span style=\"font-weight: 400;\">friendly and welcoming to visitors or guests.</span></p>\\r\\n<p><strong>Hostile-</strong><span style=\"font-weight: 400;\"> showing or feeling opposition or dislike.</span></p>\\r\\n<p><strong>Bitter-</strong><span style=\"font-weight: 400;\"> having a sharp pungent taste or smell.</span></p>\\r\\n<p><strong>Nasty-</strong><span style=\"font-weight: 400;\"> behaving in an unpleasant way.</span></p>\\r\\n<p><strong>Aggressive-</strong><span style=\"font-weight: 400;\"> ready or likely to attack or confront.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Hospitable</strong><span style=\"font-weight: 400;\"> (&#2350;&#2375;&#2361;&#2350;&#2366;&#2344;&#2344;&#2357;&#2366;&#2332;&#2364;/ &#2310;&#2340;&#2367;&#2341;&#2381;&#2351;&#2325;&#2366;&#2352;&#2368;) - friendly and welcoming to visitors or guests.</span></p>\\r\\n<p><strong>Hostile </strong><span style=\"font-weight: 400;\">(&#2358;&#2340;&#2381;&#2352;&#2369;&#2340;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339;) - showing or feeling opposition or dislike.</span></p>\\r\\n<p><strong>Bitter</strong><span style=\"font-weight: 400;\"> (&#2325;&#2337;&#2364;&#2357;&#2366;) - having a sharp pungent taste or smell.</span></p>\\r\\n<p><strong>Nasty</strong><span style=\"font-weight: 400;\"> (&#2323;&#2331;&#2366; &#2357;&#2381;&#2351;&#2357;&#2361;&#2366;&#2352;) - behaving in an unpleasant way.</span></p>\\r\\n<p><strong>Aggressive </strong><span style=\"font-weight: 400;\">(&#2310;&#2325;&#2381;&#2352;&#2366;&#2350;&#2325;) - ready or likely to attack or confront.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in passive voice. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Do the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\"> visit the </span><span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in passive voice. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Do the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\"> visit the </span><span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Are the <span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\"> been vis</span><span style=\"font-family: Cambria Math;\">ited by the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">Are the </span><span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\"> visit</span><span style=\"font-family: Cambria Math;\"> by the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n", 
                                "<p>Are the <span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\"> being visited by the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n", "<p>Are the <span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\"> visited by the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n"],
                    options_hi: ["<p>Are the <span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\"> been visited by the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">Are the </span><span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\"> visit</span><span style=\"font-family: Cambria Math;\"> by the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                                "<p>Are the <span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\"> being visited by the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n", "<p>Are the <span style=\"font-family: Cambria Math;\">Shuklas</span><span style=\"font-family: Cambria Math;\"> visited by the </span><span style=\"font-family: Cambria Math;\">Basus</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><span style=\"font-weight: 400;\">Are the Shuklas visited by the Basus? (Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) Are the Shuklas </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">been</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>visited by the Basus? (Incorrect Verb)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) Are the Shuklas </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">visit</span></span><span style=\"font-weight: 400;\"> by the Basus? (Incorrect Form of the Verb)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c) Are the Shuklas </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">being</span></span><span style=\"font-weight: 400;\"> visited by the Basus? (Incorrect Verb)</span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">Are the Shuklas visited by the Basus? (Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) Are the Shuklas </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">been</span></span><span style=\"font-weight: 400;\"> visited by the Basus? (&#2327;&#2354;&#2340; Verb)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) Are the Shuklas </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">visit</span></span><span style=\"font-weight: 400;\"> by the Basus? (Verb &#2325;&#2368; &#2327;&#2354;&#2340; Form)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c) Are the Shuklas </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">being</span></span><span style=\"font-weight: 400;\"> visited by the Basus? (&#2327;&#2354;&#2340; Verb)</span></span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. Select</span><span style=\"font-family: Cambria Math;\"> the correctly s</span><span style=\"font-family: Cambria Math;\">pelt word.</span></p>\\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. Select</span><span style=\"font-family: Cambria Math;\"> the correctly spelt word.</span></p>\\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Ocasion</span></p>\\n", "<p>Occasion</p>\\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Occasien</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">Occassion</span></p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Ocasion</span></p>\\n", "<p>Occasion</p>\\n",
                                "<p><span style=\"font-family: Cambria Math;\">Occasien</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">Occassion</span></p>\\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">Occasion</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Occasion&rsquo; is the correct spelling.</span></p>\\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">Occasion</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Occasion&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;</span><span style=\"font-family: Cambria Math;\">&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the underlined word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He was the </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">discreet</span></span><span style=\"font-family: Cambria Math;\"> boy in our group but more hardworking than all of us.</span></p>\\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the most appropria</span><span style=\"font-family: Cambria Math;\">te synonym of the underlined word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">He was the </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">discreet</span></span><span style=\"font-family: Cambria Math;\"> boy in our group but more hardworking than all of us.</span></p>\\n",
                    options_en: ["<p>loud</p>\\n", "<p>outspoken</p>\\n", 
                                "<p>reserved</p>\\n", "<p>noisy</p>\\n"],
                    options_hi: ["<p>loud</p>\\n", "<p>outspoken</p>\\n",
                                "<p>reserved</p>\\n", "<p>noisy</p>\\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>Reserved- </strong><span style=\"font-weight: 400;\">slow to reveal emotion or opinions.</span></p>\\r\\n<p><strong>Discreet-</strong><span style=\"font-weight: 400;\"> respectful of privacy and secrecy.</span></p>\\r\\n<p><strong>Loud-</strong><span style=\"font-weight: 400;\"> making a lot of noise.</span></p>\\r\\n<p><strong>Outspoken-</strong><span style=\"font-weight: 400;\"> frank in stating one\'s opinions, especially if they are shocking or controversial.</span></p>\\r\\n<p><strong>Noisy-</strong><span style=\"font-weight: 400;\"> making a lot of noise.</span></p>\\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>Reserved </strong><span style=\"font-weight: 400;\">(&#2309;&#2350;&#2367;&#2354;&#2344;&#2360;&#2366;&#2352;/&#2360;&#2306;&#2325;&#2379;&#2330;&#2368;) - slow to reveal emotion or opinions.</span></p>\\r\\n<p><strong>Discreet</strong><span style=\"font-weight: 400;\"> (&#2357;&#2367;&#2344;&#2351;&#2358;&#2368;&#2354;/&#2344;&#2350;&#2381;&#2352;) - respectful of privacy and secrecy.</span></p>\\r\\n<p><strong>Loud</strong><span style=\"font-weight: 400;\"> (&#2313;&#2306;&#2330;&#2375; &#2360;&#2381;&#2357;&#2352; &#2325;&#2366;) - making a lot of noise.</span></p>\\r\\n<p><strong>Outspoken </strong><span style=\"font-weight: 400;\">(&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;&#2357;&#2366;&#2342;&#2368;) - frank in stating one\'s opinions, especially if they are shocking or controversial.</span></p>\\r\\n<p><strong>Noisy</strong><span style=\"font-weight: 400;\"> (&#2358;&#2379;&#2352; &#2350;&#2330;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366;) - making a lot of noise. </span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The factory has been <span style=\"text-decoration: underline;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">exhaling) black smoke from its chimney.</span></p>\\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The factory has been <span style=\"text-decoration: underline;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">exhaling) black smoke from its chimney.</span></p>\\n",
                    options_en: ["<p>emitting</p>\\n", "<p>preserving</p>\\n", 
                                "<p>remitting</p>\\n", "<p>indicting</p>\\n"],
                    options_hi: ["<p>emitting</p>\\n", "<p>preserving</p>\\n",
                                "<p>remitting</p>\\n", "<p>indicting</p>\\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(a) <strong>Emit</strong></span><span style=\"font-family: Cambria Math;\"><strong>ting </strong>- </span><span style=\"font-family: Cambria Math;\">producing or discharging something, especially gas or radiation.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Exhaling</strong>-</span><span style=\"font-family: Cambria Math;\"> breathing air or smoke out through mouth or nose.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Preserving</strong>-</span><span style=\"font-family: Cambria Math;\"> maintaining something in its original or existing state.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Remitting</strong>-</span><span style=\"font-family: Cambria Math;\"> sending money to a person or place, usually </span><span style=\"font-family: Cambria Math;\">in payment.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Indicting</strong>-</span><span style=\"font-family: Cambria Math;\"> formally accusing someone of a crime.</span></p>\\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(a) <strong>Emitting </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2360;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - producing or discharging something, especially gas or radiation.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Exhaling </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2305;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2337;&#2364;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> breathing air or smoke out through mouth or nose.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Preserving</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2352;&#2325;&#2381;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\"> maintaining something in its original or existing state.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Remitting </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2350;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - sending money to a person or place, usually in payment.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Indicting</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2352;&#2379;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2327;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - formally accusing someone of a crime.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the option th</span><span style=\"font-family: Cambria Math;\">at can be used as a one-word substitute for the given group of words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A life history of a person written by himself.</span></p>\\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the given gr</span><span style=\"font-family: Cambria Math;\">oup of words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A life history of a person written by himself.</span></p>\\n",
                    options_en: ["<p>Biography</p>\\n", "<p>Epic</p>\\n", 
                                "<p>Story</p>\\n", "<p>Autobiography</p>\\n"],
                    options_hi: ["<p>Biography</p>\\n", "<p>Epic</p>\\n",
                                "<p>Story</p>\\n", "<p>Autobiography</p>\\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(d) <strong>Autobiography </strong>- </span><span style=\"font-family: Cambria Math;\">a life history of a person written by himself.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Biography</strong> -</span><span style=\"font-family: Cambria Math;\"> an account of someone&rsquo;s life written by someone else.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Epic </strong>-</span><span style=\"font-family: Cambria Math;\"> a long narrative poem in elevated style recounting the deeds of a legendary or historical hero.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Story</strong> -</span><span style=\"font-family: Cambria Math;\"> an account of imaginary or real people and events told for entertainment.</span></p>\\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(d) <strong>Au</strong></span><span style=\"font-family: Cambria Math;\"><strong>tobiography</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a life history of a person written by himself.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Biography</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2357;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\">) - an account of someone&rsquo;s life written by someone else.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Epic</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2325;&#2366;&#2357;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) - a long narrative poem in elevated style recounting the deeds of a legendary or historical her</span><span style=\"font-family: Cambria Math;\">o.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Story </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\">) - an account of imaginary or real people and events told for entertainment.</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct order to form a meaningful sentence. </span></p>\\r\\n<p><span style=\"font-weight: 400;\">(A) about all his</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(B) noise they make</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(C) he constantly complains</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(D) neighbours and the</span></p>\\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts</span><span style=\"font-family: Cambria Math;\"> in the correct order to form a meaningful sentence. </span></p>\\r\\n<p><span style=\"font-weight: 400;\">(A) about all his</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(B) noise they make</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(C) he constantly complains</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(D) neighbours and the</span></p>\\n",
                    options_en: ["<p>B, A, D, C</p>\\n", "<p>C, A, D, B</p>\\n", 
                                "<p>B, D, A, C</p>\\n", "<p>D, B, C, A</p>\\n"],
                    options_hi: ["<p>B, A, D, C</p>\\n", "<p>C, A, D, B</p>\\n",
                                "<p>B, D, A, C</p>\\n", "<p>D, B, C, A</p>\\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">C, A, D, B</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The given senten</span><span style=\"font-family: Cambria Math;\">ce starts with Part C as it introduces the main idea of the sentence, i.e. he constantly complains. Part C will be followed by Part A as it contains the preposition &lsquo;about&rsquo; used to mention the cause of disturbance. Further, Part D states the cause of distu</span><span style=\"font-family: Cambria Math;\">rbance &amp; Part B states another cause of disturbance. So, B </span><span style=\"font-family: Cambria Math;\">will</span><span style=\"font-family: Cambria Math;\"> follow D. Going through the options, option &lsquo;b&rsquo; has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">C, A, D, B</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> Part C </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;he constantly complai</span><span style=\"font-family: Cambria Math;\">ns&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> Part C </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> Part A </span><span style=\"font-family: Cambria Math;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2358;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2354;&#2381;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> preposition &lsquo;about&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, Part D </span><span style=\"font-family: Cambria Math;\">&#2309;&#2358;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Part B </span><span style=\"font-family: Cambria Math;\">&#2309;&#2358;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, D </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> Options </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;</span><span style=\"font-family: Cambria Math;\">&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, option &lsquo;b&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> I was kept waiting for more than an hour to get an appointment by the staff</span></p>\\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> I was kept waiting for more than an hour to get an appointment by the staff</span></p>\\n",
                    options_en: ["<p>The staff had kept me waiting for more than an hour to get an appointment.</p>\\n", "<p>The <span style=\"font-family: Cambria Math;\">staff keep</span><span style=\"font-family: Cambria Math;\"> me waiting for more than an hour to get an appointment.</span></p>\\n", 
                                "<p>The staff kept me waiting for more than an hour to get an appointment.</p>\\n", "<p>The <span style=\"font-family: Cambria Math;\">staff kept me w</span><span style=\"font-family: Cambria Math;\">ait</span><span style=\"font-family: Cambria Math;\"> for more than an hour to get an appointment.</span></p>\\n"],
                    options_hi: ["<p>The staff had kept me waiting for more than an h<span style=\"font-family: Cambria Math;\">our to get an appointment.</span></p>\\n", "<p>The <span style=\"font-family: Cambria Math;\">staff keep</span><span style=\"font-family: Cambria Math;\"> me waiting for more than an hour to get an appointment.</span></p>\\n",
                                "<p>The staff kept me waiting for more than an hour to get an appointment.</p>\\n", "<p>The <span style=\"font-family: Cambria Math;\">staff kept me wait</span><span style=\"font-family: Cambria Math;\"> for more than an hour to get an appointment.</span></p>\\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-weight: 400;\">The staff kept me waiting for more than an hour to get an appointment. (Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) The staff </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had kept</span></span><span style=\"font-weight: 400;\"> me waiting for more than an hour to get an appointment. (Incorrect Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) The staff </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">keep</span></span><span style=\"font-weight: 400;\"> me waiting for more than an hour to get an appointment. (Incorrect Tense)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) The staff kept me </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">wait</span></span><span style=\"font-weight: 400;\"> for more than an hour to get an appointment. (Incorrect Form of the Verb)</span></span></p>\\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><span style=\"font-weight: 400;\">The staff kept me waiting for more than an hour to get an appointment. (Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) The staff </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had kept</span></span><span style=\"font-weight: 400;\"> me waiting for more than an hour to get an appointment. (&#2327;&#2354;&#2340; Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) The staff </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">keep</span></span><span style=\"font-weight: 400;\"> me waiting for more than an hour to get an appointment. (&#2327;&#2354;&#2340; Tense)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) The staff kept me </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">wait</span></span><span style=\"font-weight: 400;\"> for more than an hour to get an appointment. (Verb &#2325;&#2368; &#2327;&#2354;&#2340; form)</span></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Sentenc</span><span style=\"font-family: Cambria Math;\">es of a paragraph are given below in jumbled order. Select the option that arranges the sentences in the correct logical sequence to form a meaningful and coherent paragraph. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A. Globally, almost 6.5 million people have died so far.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B. As I write the final</span><span style=\"font-family: Cambria Math;\"> chapter, we enter the third year in our battle with COVID-19.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C. While we have the vaccine to outlive the disease, to live our grief needs the </span><span style=\"font-family: Cambria Math;\">salve</span><span style=\"font-family: Cambria Math;\"> of </span><span style=\"font-family: Cambria Math;\">time</span><span style=\"font-family: Cambria Math;\">.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">D. I started writing this book in the throes of the pandemic.</span></p>\\n",
                    question_hi: "<p>16<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Sentences of a paragraph are given below in jumbled order. Select the option that arranges the sentences in the correct logical sequence to form a meaningful and coherent paragraph. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A. Globally, almost 6.5 million people have died so far.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B. As I write the final chapter, we enter the third year in our battle with COVID-19.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C. While we have the vaccine to outlive the disease, to live our grief needs the </span><span style=\"font-family: Cambria Math;\">salve</span><span style=\"font-family: Cambria Math;\"> of </span><span style=\"font-family: Cambria Math;\">time</span><span style=\"font-family: Cambria Math;\">.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">D. I started writ</span><span style=\"font-family: Cambria Math;\">ing this book in the throes of the pandemic.</span></p>\\n",
                    options_en: ["<p>B, A, D, C</p>\\n", "<p>D, B, A, C</p>\\n", 
                                "<p>A, D, B, C</p>\\n", "<p>C, D, B, A</p>\\n"],
                    options_hi: ["<p>B, A, D, C</p>\\n", "<p>D, B, A, C</p>\\n",
                                "<p>A, D, B, C</p>\\n", "<p>C, D, B, A</p>\\n"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">D, B, A, C</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Sentence D will be the starting line as it introduces the main idea of the </span><span style=\"font-family: Cambria Math;\">parajumble</span><span style=\"font-family: Cambria Math;\">, i.e. I started writing this </span><span style=\"font-family: Cambria Math;\">book in the difficult times of the pandemic. And sentence B states the content of the final chapter. So, B will follow D. Further, Sentence A states the data about the number of deaths globally &amp; Sentence C states that we need the salve of time to live thr</span><span style=\"font-family: Cambria Math;\">ough the grief. So, C will follow A. Going through the options, option &lsquo;b&rsquo; has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">D, B, A, C</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Sentence D </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">parajumble</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;I started writing this book in the difficult times of the pan</span><span style=\"font-family: Cambria Math;\">demic&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> sentence B, final chapter </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> content </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, D </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, sentence A </span><span style=\"font-family: Cambria Math;\">&#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2371;&#2340;&#2381;&#2351;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2306;&#2325;&#2396;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> sentence C </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2307;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2348;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2352;&#2370;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, A </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Cambria Math;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> Options </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, option &lsquo;b&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word.</span></p>\\n",
                    question_hi: "<p>17<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word.</span></p>\\n",
                    options_en: ["<p>Grateful</p>\\n", "<p>Immigrate</p>\\n", 
                                "<p>Ignorance</p>\\n", "<p><span style=\"font-family: Cambria Math;\">Hieararchy</span></p>\\n"],
                    options_hi: ["<p>Grateful</p>\\n", "<p>Immigrate</p>\\n",
                                "<p>Ignorance</p>\\n", "<p><span style=\"font-family: Cambria Math;\">Hieararchy</span></p>\\n"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(d) </span><strong><span style=\"font-family: Cambria Math;\">Hieararchy</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Hierarchy&rsquo; is the correct spelling.</span></p>\\n",
                    solution_hi: "<p>17<span style=\"font-family: Cambria Math;\">.(d) </span><strong><span style=\"font-family: Cambria Math;\">Hieararchy</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Hierarchy&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>.18.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the underlined word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">His deliberat</span><span style=\"font-family: Cambria Math;\">e activities brought him</span><span style=\"font-family: Cambria Math;\"> <span style=\"text-decoration: underline;\">fame</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the underlined word. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">His deliberate activities brought him</span><span style=\"font-family: Cambria Math;\"> <span style=\"text-decoration: underline;\">fame</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Dishonour</span></p>\\n", "<p>Respect</p>\\n", 
                                "<p>Sorrow</p>\\n", "<p>Strength</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Dishonour</span></p>\\n", "<p>Respect</p>\\n",
                                "<p>Sorrow</p>\\n", "<p>Strength</p>\\n"],
                    solution_en: "<p>18<span style=\"font-family: Cambria Math;\">.(a) </span><strong><span style=\"font-family: Cambria Math;\">Dishonour</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- a state of shame or disgrace.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Fame</strong>-</span><span style=\"font-family: Cambria Math;\"> state of being known for doing something important.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Sorrow</strong>-</span><span style=\"font-family: Cambria Math;\"> a feeling of deep distress caused by loss, disappointment, or misfortune.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Strength</strong>-</span><span style=\"font-family: Cambria Math;\"> ability to do things that re</span><span style=\"font-family: Cambria Math;\">quire physical effort.</span></p>\\n",
                    solution_hi: "<p>18<span style=\"font-family: Cambria Math;\">.(a) </span><strong><span style=\"font-family: Cambria Math;\">Dishonour</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2366;&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\">) - a state of shame or disgrace.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Fame</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\">) - state of being known for doing something important.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Sorrow </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">&#2326;</span><span style=\"font-family: Cambria Math;\">) - a feeling of deep distress caused by loss, disappointment, or misfortune.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Strengt</span><span style=\"font-family: Cambria Math;\">h </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2325;&#2340;</span><span style=\"font-family: Cambria Math;\">) - ability to do things that require physical effort.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Imminent</span></p>\\n",
                    question_hi: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Immi</span><span style=\"font-family: Cambria Math;\">nent</span></p>\\n",
                    options_en: ["<p>Near</p>\\n", "<p>Unstoppable</p>\\n", 
                                "<p>Distant</p>\\n", "<p>Impending</p>\\n"],
                    options_hi: ["<p>Near</p>\\n", "<p>Unstoppable</p>\\n",
                                "<p>Distant</p>\\n", "<p>Impending</p>\\n"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(c) <strong>Distant </strong>-</span><span style=\"font-family: Cambria Math;\"> far away in space or time.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Imminent</strong>-</span><span style=\"font-family: Cambria Math;\"> likely to happen very soon.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Near</strong>-</span><span style=\"font-family: Cambria Math;\"> at a short distance away.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Unstoppable</strong>-</span><span style=\"font-family: Cambria Math;\"> impossible to stop or prevent.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Impending</strong>-</span><span style=\"font-family: Cambria Math;\"> about to happen.</span></p>\\n",
                    solution_hi: "<p>19<span style=\"font-family: Cambria Math;\">.(c) <strong>Distant</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2352;&#2360;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\">) - far away in space or time.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Imminent </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2325;&#2335;&#2360;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\">) - likely to happen very soon.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Near</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> at a short distance away.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Unstoppable </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2348;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) - impossible to stop or prevent.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Impending </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2360;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\">) - about to happen</span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Have you ever / wondered/where does / oysters come from?</span></p>\\n",
                    question_hi: "<p>20<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Have you ever / wondered/where does / oysters come from?</span></p>\\n",
                    options_en: ["<p>wondered</p>\\n", "<p>oysters come from?</p>\\n", 
                                "<p>where does</p>\\n", "<p>Have you ever</p>\\n"],
                    options_hi: ["<p>wondered</p>\\n", "<p>oysters come from?</p>\\n",
                                "<p>where does</p>\\n", "<p>Have you ever</p>\\n"],
                    solution_en: "<p>20<span style=\"font-family: Cambria Math;\">.(c)</span><span style=\"font-family: Cambria Math;\"> <strong>where does</strong></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Whenever Wh. Words (like who, whom, where, why, how, etc) are written in the middle of a sentence, the helping verb and the main verb are written after the subject. E.g. - I do not know where </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">she lives</span></span><span style=\"font-family: Cambria Math;\">. Therefore, &lsquo;does&rsquo; must be removed from the sentence and &lsquo;V</span><sub><span style=\"font-family: Cambria Math;\">1</span></sub><span style=\"font-family: Cambria Math;\">&rsquo; must be used according to the plural subject &lsquo;oysters&rsquo;. Hence, &lsquo;where oysters come from&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>20<span style=\"font-family: Cambria Math;\">.(c) </span><strong><span style=\"font-family: Cambria Math;\">where does</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> Wh. Words (</span><span style=\"font-family: Cambria Math;\">&#2332;&#2376;&#2360;</span><span style=\"font-family: Cambria Math;\">&#2375;</span><span style=\"font-family: Cambria Math;\"> who</span><span style=\"font-family: Cambria Math;\">, whom, where, why, h</span><span style=\"font-family: Cambria Math;\">ow </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\">) sentence </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2326;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> helping verb </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> main verb, subject </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2326;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> E.g. - I do not know where </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">she lives</span></span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &lsquo;does&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2351;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> plural subject &lsquo;oysters&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;V</span><sub><span style=\"font-family: Cambria Math;\">1</span></sub><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;where oy</span><span style=\"font-family: Cambria Math;\">sters come from&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    question_hi: "<p>21. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    options_en: ["<p>found</p>\\n", "<p>appeared</p>\\n", 
                                "<p>rested</p>\\n", "<p>died</p>\\n"],
                    options_hi: ["<p>found</p>\\n", "<p>appeared</p>\\n",
                                "<p>rested</p>\\n", "<p>died</p>\\n"],
                    solution_en: "<p>21<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">Appeared</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Appear&rsquo; means to become noticeable. The given passage states that the Grim Reaper seems to have appeared in Europe during the 14th century. Hence, &lsquo;appeared&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>21<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">Appeared</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Appear&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2381;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2368;&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 14</span><span style=\"font-family: Cambria Math;\">&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2340;&#2366;&#2348;&#2381;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2352;&#2379;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;appeared&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appro</span><span style=\"font-family: Cambria Math;\">priate option to fill in blank number 22.</span></p>\\n",
                    question_hi: "<p>22.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\\n",
                    options_en: ["<p>better</p>\\n", "<p>worse</p>\\n", 
                                "<p>best</p>\\n", "<p>worst</p>\\n"],
                    options_hi: ["<p>better</p>\\n", "<p><span style=\"font-family: Cambria Math;\">worse</span></p>\\n",
                                "<p>best</p>\\n", "<p>worst</p>\\n"],
                    solution_en: "<p>22<span style=\"font-family: Cambria Math;\">.(d) </span><strong><span style=\"font-family: Cambria Math;\">Worst</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The given passage states that Europe was dealing with the world&rsquo;s worst pandemic during this time. Hence, &lsquo;worst&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>22<span style=\"font-family: Cambria Math;\">.(d) </span><strong><span style=\"font-family: Cambria Math;\">Worst</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2352;&#2379;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2344;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2350;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2370;&#2333;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;worst&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fil</span><span style=\"font-family: Cambria Math;\">l in blank number 23.</span></p>\\n",
                    question_hi: "<p>23.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    options_en: ["<p>vanished</p>\\n", "<p>shriveled</p>\\n", 
                                "<p>perished</p>\\n", "<p>moldered</p>\\n"],
                    options_hi: ["<p>vanished</p>\\n", "<p>shriv<span style=\"font-family: Cambria Math;\">eled</span></p>\\n",
                                "<p>perished</p>\\n", "<p>moldered</p>\\n"],
                    solution_en: "<p>23<span style=\"font-family: Cambria Math;\">.(c) </span><strong><span style=\"font-family: Cambria Math;\">Perished</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Perish&rsquo; means to die, especially in a violent or sudden way. The given passage states that about one-third of Europe&rsquo;s entire population died as a result of the pandemic. Hence, &lsquo;perished&rsquo; is </span><span style=\"font-family: Cambria Math;\">the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>23<span style=\"font-family: Cambria Math;\">.(c) </span><strong><span style=\"font-family: Cambria Math;\">Perished</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Perish&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2330;&#2366;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2367;&#2306;&#2360;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2352;&#2368;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2352;&#2379;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2348;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2367;&#2361;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2350;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2360;&#2381;&#2357;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2393;&#2340;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;perished&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    question_hi: "<p>24.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">remaked</span></p>\\n", "<p>renewed</p>\\n", 
                                "<p>recurred</p>\\n", "<p>duplicated</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">remaked</span></p>\\n", "<p>renewed</p>\\n",
                                "<p>recurred</p>\\n", "<p>duplicated</p>\\n"],
                    solution_en: "<p>24<span style=\"font-family: Cambria Math;\">.(c) </span><strong><span style=\"font-family: Cambria Math;\">Recurred</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Recur&rsquo; means to occur again periodically or repeatedly. The given passage states that outbreaks occurred periodically several other times after that. Hence, &lsquo;recurred&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>24<span style=\"font-family: Cambria Math;\">.(c) </span><strong><span style=\"font-family: Cambria Math;\">Recurred</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Recur&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2335;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2379;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;recurred&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\\n",
                    question_hi: "<p>25.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The Grim Reaper seems to have (21) ______ in Europe d</span><span style=\"font-family: Cambria Math;\">uring the 14th century. It was during this time that Europe was dealing with what was then the world\'s (22</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ pandemic, the Black Death, believed to be the result of the plague. It is estimated that about one-third of Europe\'s entire population (23</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">__</span><span style=\"font-family: Cambria Math;\">___ as a result of the pandemic, with some areas of the continent suffering far greater losses than others. The original outbreak of the plague occurred during 1347-51, and outbreaks then (24</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ several other times after that. So, clearly, death was so</span><span style=\"font-family: Cambria Math;\">mething that the surviving Europeans had on their mind, and it is not surprising that they (25</span><span style=\"font-family: Cambria Math;\">)_</span><span style=\"font-family: Cambria Math;\">_____ an image to represent it.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\\n",
                    options_en: ["<p>forced</p>\\n", "<p>conjured</p>\\n", 
                                "<p>desired</p>\\n", "<p>compelled</p>\\n"],
                    options_hi: ["<p>forced</p>\\n", "<p>conjured</p>\\n",
                                "<p>desired</p>\\n", "<p>compelled</p>\\n"],
                    solution_en: "<p>25<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">Conjured</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Conjure&rsquo; means to create something, often through magic. The given passage states that </span><span style=\"font-family: Cambria Math;\">they conjured an image to represent it. Hence, &lsquo;conjured&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>25<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">Conjured</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Conjure&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2325;&#2381;&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2342;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;conjur</span><span style=\"font-family: Cambria Math;\">ed&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>