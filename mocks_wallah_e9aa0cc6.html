<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\"> Which of the following pairs of \'soil - characteristics\' is correct? </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. Sandy - proportion of fine particles is relatively higher </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. Clayey - greater proportion of big particles</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">\'</span><span style=\"font-family: Cambria Math;\">&#2350;&#2371;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2309;&#2349;&#2367;&#2354;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. </span><span style=\"font-family: Cambria Math;\">&#2348;&#2354;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2360;&#2370;&#2325;&#2381;&#2359;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2375;&#2325;&#2381;&#2359;&#2366;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. </span><span style=\"font-family: Cambria Math;\">&#2350;&#2371;&#2339;&#2381;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Only I</p>\n", "<p>Neither I nor II</p>\n", 
                                "<p>Both I and II</p>\n", "<p>Only II</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2368; II</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> II</span></p>\n"],
                    solution_en: "<p>1.<span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>Neither I nor II.</strong><span style=\"font-weight: 400;\"> </span><strong>Sandy Soil : </strong><span style=\"font-weight: 400;\">Consists of small particles of weathered rock; One of the poorest types of soil for growing plants (very low nutrients and poor water holding capacity); Good for the drainage system; Usually formed by the breakdown or fragmentation of rocks like granite, limestone and quartz. </span><strong>Clay Soil : </strong><span style=\"font-weight: 400;\">Smallest particles (tightly packed together with each other with very little or no airspace); Good water storage qualities; Very sticky to touch when wet but smooth when dried; Densest and heaviest type of soil which does not drain well or provide space for plant roots to flourish.</span></p>\n",
                    solution_hi: "<p>1.(b) <strong>&#2344; &#2340;&#2379; I &#2344; &#2361;&#2368; II </strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2352;&#2375;&#2340;&#2368;&#2354;&#2368; (&#2348;&#2354;&#2369;&#2312;) &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; : </strong><span style=\"font-weight: 400;\">&#2311;&#2360;&#2350;&#2375;&#2306; &#2309;&#2346;&#2325;&#2381;&#2359;&#2351;&#2367;&#2340; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344; &#2325;&#2375; &#2331;&#2379;&#2335;&#2375;-&#2331;&#2379;&#2335;&#2375; &#2325;&#2339; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;; &#2348;&#2338;&#2364;&#2340;&#2375; &#2346;&#2380;&#2343;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2348;&#2360;&#2375; &#2326;&#2352;&#2366;&#2348; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; (&#2348;&#2361;&#2369;&#2340; &#2325;&#2350; &#2346;&#2379;&#2359;&#2325; &#2340;&#2340;&#2381;&#2357; &#2324;&#2352; &#2326;&#2352;&#2366;&#2348; &#2332;&#2354; &#2343;&#2366;&#2352;&#2339; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366;); &#2332;&#2354; &#2344;&#2367;&#2325;&#2366;&#2360;&#2368; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2330;&#2381;&#2331;&#2366;; &#2310;&#2350;&#2340;&#2380;&#2352; &#2346;&#2352; &#2327;&#2381;&#2352;&#2375;&#2344;&#2366;&#2311;&#2335;, &#2330;&#2370;&#2344;&#2366; &#2346;&#2340;&#2381;&#2341;&#2352; &#2324;&#2352; &#2325;&#2381;&#2357;&#2366;&#2352;&#2381;&#2335;&#2381;&#2332; (quartz) &#2332;&#2376;&#2360;&#2368; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344;&#2379;&#2306; &#2325;&#2375; &#2335;&#2370;&#2335;&#2344;&#2375; &#2351;&#2366; &#2357;&#2367;&#2326;&#2306;&#2337;&#2344; &#2360;&#2375; &#2348;&#2344;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2330;&#2367;&#2325;&#2344;&#2368; (&#2350;&#2371;&#2339;&#2381;&#2350;&#2351;) &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; :</strong><span style=\"font-weight: 400;\"> &#2331;&#2379;&#2335;&#2375; &#2325;&#2339; (&#2348;&#2361;&#2369;&#2340; &#2325;&#2350; &#2351;&#2366; &#2348;&#2367;&#2344;&#2366; &#2357;&#2366;&#2351;&#2369; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2375; &#2319;&#2325; &#2342;&#2370;&#2360;&#2352;&#2375; &#2325;&#2375; &#2360;&#2366;&#2341; &#2350;&#2332;&#2364;&#2348;&#2370;&#2340;&#2368; &#2360;&#2375; &#2346;&#2352;&#2367;&#2346;&#2370;&#2352;&#2381;&#2339;) , &#2309;&#2330;&#2381;&#2331;&#2375; &#2332;&#2354; &#2360;&#2306;&#2330;&#2351;&#2344; &#2327;&#2369;&#2339;; &#2327;&#2368;&#2354;&#2375; &#2361;&#2379;&#2344;&#2375; &#2346;&#2352; &#2348;&#2361;&#2369;&#2340; &#2330;&#2367;&#2346;&#2330;&#2367;&#2346;&#2366; &#2354;&#2375;&#2325;&#2367;&#2344; &#2360;&#2370;&#2326;&#2344;&#2375; &#2346;&#2352; &#2330;&#2367;&#2325;&#2344;&#2366;; &#2360;&#2348;&#2360;&#2375; &#2360;&#2328;&#2344; &#2324;&#2352; &#2349;&#2366;&#2352;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2350;&#2367;&#2335;&#2381;&#2335;&#2368;, &#2332;&#2379; &#2309;&#2330;&#2381;&#2331;&#2368; &#2340;&#2352;&#2361; &#2360;&#2375; &#2332;&#2354; &#2344;&#2367;&#2325;&#2366;&#2360;&#2368; &#2344;&#2361;&#2368;&#2306; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376; &#2351;&#2366; &#2346;&#2380;&#2343;&#2379;&#2306; &#2325;&#2368; &#2332;&#2337;&#2364;&#2379;&#2306; &#2325;&#2375; &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2375; &#2354;&#2367;&#2319; &#2332;&#2327;&#2361; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2344;&#2361;&#2368;&#2306; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Malic Acid is found in which of the following </span><span style=\"font-family: Cambria Math;\">substances?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2376;&#2354;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2350;&#2381;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2342;&#2366;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Apple</p>\n", "<p>Vinegar</p>\n", 
                                "<p>Ant\'s sting</p>\n", "<p>Tamarind</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2348;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2352;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2306;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2306;&#2325;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2311;&#2350;&#2354;&#2368;</span></p>\n"],
                    solution_en: "<p>2.<span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>Apple. Other sources of malic acid </strong><span style=\"font-weight: 400;\">- Bananas, pomegranates, grapes, berries, tomatoes, and broccoli. </span><strong>Acetic acid- Vinegar</strong><span style=\"font-weight: 400;\">, apples, grapes, pineapple, strawberries, and oranges. </span><strong>Formic acid -</strong><span style=\"font-weight: 400;\"> the venom of bee and </span><strong>ant stings</strong><span style=\"font-weight: 400;\">. </span><strong>Tartaric Acid -</strong><span style=\"font-weight: 400;\"> Grapes, </span><strong>tamarind</strong><span style=\"font-weight: 400;\">, apples, apricots, and bananas. </span><strong>Acid :</strong><span style=\"font-weight: 400;\"> A hydrogen-containing substance that is capable of donating a proton (hydrogen ion) to another substance.</span><strong>&nbsp;</strong></p>\n",
                    solution_hi: "<p>2.(a)&nbsp;<strong>&#2360;&#2375;&#2348; </strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2350;&#2376;&#2354;&#2367;&#2325; &#2319;&#2360;&#2367;&#2337; &#2325;&#2375; &#2309;&#2344;&#2381;&#2351; &#2360;&#2381;&#2352;&#2379;&#2340; -</strong><span style=\"font-weight: 400;\"> &#2325;&#2375;&#2354;&#2375;, &#2309;&#2344;&#2366;&#2352;, &#2309;&#2306;&#2327;&#2370;&#2352;, &#2332;&#2366;&#2350;&#2369;&#2344;, &#2335;&#2350;&#2366;&#2335;&#2352; &#2324;&#2352; &#2348;&#2381;&#2352;&#2379;&#2325;&#2354;&#2368;&#2404; </span><strong>&#2319;&#2360;&#2367;&#2335;&#2367;&#2325; &#2319;&#2360;&#2367;&#2337;- &#2360;&#2367;&#2352;&#2325;&#2366;</strong><span style=\"font-weight: 400;\">, &#2360;&#2375;&#2348;, &#2309;&#2306;&#2327;&#2370;&#2352;, &#2309;&#2344;&#2366;&#2344;&#2366;&#2360;, &#2360;&#2381;&#2335;&#2381;&#2352;&#2377;&#2348;&#2375;&#2352;&#2368; &#2324;&#2352; &#2360;&#2306;&#2340;&#2352;&#2375;&#2404; </span><strong>&#2347;&#2377;&#2352;&#2381;&#2350;&#2367;&#2325; &#2319;&#2360;&#2367;&#2337; - </strong><span style=\"font-weight: 400;\">&#2350;&#2343;&#2369;&#2350;&#2325;&#2381;&#2326;&#2368; &#2324;&#2352; </span><strong>&#2330;&#2368;&#2306;&#2335;&#2368; &#2325;&#2375; &#2337;&#2306;&#2325;</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2332;&#2361;&#2352;&#2404; </span><strong>&#2335;&#2366;&#2352;&#2381;&#2335;&#2352;&#2367;&#2325; &#2319;&#2360;&#2367;&#2337;</strong><span style=\"font-weight: 400;\"> - &#2309;&#2306;&#2327;&#2370;&#2352;, </span><strong>&#2311;&#2350;&#2354;&#2368;</strong><span style=\"font-weight: 400;\">, &#2360;&#2375;&#2348;, &#2326;&#2369;&#2348;&#2366;&#2344;&#2368; &#2324;&#2352; &#2325;&#2375;&#2354;&#2375;&#2404; </span><strong>&#2309;&#2350;&#2381;&#2354; :</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2332;&#2344; &#2351;&#2369;&#2325;&#2381;&#2340; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2332;&#2379; &#2342;&#2370;&#2360;&#2352;&#2375; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2325;&#2379; &#2319;&#2325; &#2346;&#2381;&#2352;&#2379;&#2335;&#2377;&#2344; (&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2332;&#2344; &#2310;&#2351;&#2344;) &#2342;&#2375;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2325;&#2381;&#2359;&#2350; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> _______became the first bank to get Reserve Bank of India\'s approv</span><span style=\"font-family: Cambria Math;\">al for rupee trade.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3._________</span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2367;&#2332;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2368;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>SBI Bank</p>\n", "<p>Axis Bank</p>\n", 
                                "<p>UCO Bank</p>\n", "<p>ICICI Bank</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2319;&#2360;&#2348;&#2368;&#2310;&#2312; &#2348;&#2376;&#2306;&#2325; (SBI Bank)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2325;&#2381;&#2360;&#2367;&#2360; &#2348;&#2376;&#2306;&#2325; (Axis Bank)</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> (UCO Bank)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2310;&#2312;&#2360;&#2368;&#2310;&#2312;&#2360;&#2368;&#2310;&#2312; &#2348;&#2376;&#2306;&#2325; (ICICI Bank)</span></p>\n"],
                    solution_en: "<p>3.(c)&nbsp;<strong>UCO bank </strong><span style=\"font-weight: 400;\">(United Commercial Bank):</span><strong> </strong><span style=\"font-weight: 400;\">One of the nationalized banks in India under the ownership of the Ministry of Finance, Government of India. Headquarters : Kolkata; </span><strong>Founded </strong><span style=\"font-weight: 400;\">: 6 January 1943, Kolkata; Founder : Ghanshyam Das Birla. </span><strong>SBI Bank (State Bank of India ) : </strong><span style=\"font-weight: 400;\">Largest public sector bank (PSU) in India; Chairperson: Dinesh Kumar Khara; </span><strong>Founded</strong><span style=\"font-weight: 400;\">: 1 July 1955; </span><strong>Headquarters</strong><span style=\"font-weight: 400;\">: Mumbai; State Bank of India was earlier known as the Imperial Bank of India. </span><strong>Axis Bank : </strong><span style=\"font-weight: 400;\">Founded: 1993, in Ahmedabad; Headquarters: Mumbai. </span><strong>ICICI Bank</strong><span style=\"font-weight: 400;\"> (Industrial Credit and Investment Corporation of India) </span><strong>:</strong><span style=\"font-weight: 400;\"> Founded: 1994, in Vadodara, Headquarters: Mumbai.</span></p>\n",
                    solution_hi: "<p>3.(c)<strong>&nbsp;</strong><strong>&#2351;&#2370;&#2325;&#2379; &#2348;&#2376;&#2306;&#2325; (&#2351;&#2370;&#2344;&#2366;&#2311;&#2335;&#2375;&#2337; &#2325;&#2350;&#2352;&#2381;&#2358;&#2367;&#2351;&#2354; &#2348;&#2376;&#2306;&#2325;) : </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2357;&#2367;&#2340;&#2381;&#2340; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351; &#2360;&#2381;&#2357;&#2366;&#2350;&#2367;&#2340;&#2381;&#2357; &#2357;&#2366;&#2354;&#2368; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;&#2325;&#2371;&#2340; &#2348;&#2376;&#2306;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; </strong><span style=\"font-weight: 400;\">: &#2325;&#2379;&#2354;&#2325;&#2366;&#2340;&#2366;; </span><strong>&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; </strong><span style=\"font-weight: 400;\">: 6 &#2332;&#2344;&#2357;&#2352;&#2368; 1943, &#2325;&#2379;&#2354;&#2325;&#2366;&#2340;&#2366; &#2350;&#2375;&#2306;; </span><strong>&#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2346;&#2325; </strong><span style=\"font-weight: 400;\">: &#2328;&#2344;&#2358;&#2381;&#2351;&#2366;&#2350; &#2342;&#2366;&#2360; &#2348;&#2367;&#2337;&#2364;&#2354;&#2366;&#2404;&nbsp; </span><strong>SBI &#2348;&#2376;&#2306;&#2325; (&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2335;&#2375;&#2335; &#2348;&#2376;&#2306;&#2325;) : </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2348;&#2376;&#2306;&#2325; (PSU); </span><strong>&#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; </strong><span style=\"font-weight: 400;\">: &#2342;&#2367;&#2344;&#2375;&#2358; &#2325;&#2369;&#2350;&#2366;&#2352; &#2326;&#2366;&#2352;&#2366;; </span><strong>&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; </strong><span style=\"font-weight: 400;\">: 1 &#2332;&#2369;&#2354;&#2366;&#2312; 1955; </span><strong>&#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351;</strong><span style=\"font-weight: 400;\">: &#2350;&#2369;&#2306;&#2348;&#2312;; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2335;&#2375;&#2335; &#2348;&#2376;&#2306;&#2325; &#2325;&#2379; &#2346;&#2361;&#2354;&#2375; &lsquo;&#2311;&#2306;&#2346;&#2368;&#2352;&#2367;&#2351;&#2354; &#2348;&#2376;&#2306;&#2325; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;&rsquo; &#2325;&#2375; &#2344;&#2366;&#2350; &#2360;&#2375; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2341;&#2366;&#2404; </span><strong>Axis &#2348;&#2376;&#2306;&#2325; : </strong><span style=\"font-weight: 400;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; : 1993, &#2309;&#2361;&#2350;&#2342;&#2366;&#2348;&#2366;&#2342; &#2350;&#2375;&#2306;; </span><strong>&#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; </strong><span style=\"font-weight: 400;\">: &#2350;&#2369;&#2306;&#2348;&#2312;&#2404; </span><strong>ICICI &#2348;&#2376;&#2306;&#2325;</strong><span style=\"font-weight: 400;\"> (&#2311;&#2306;&#2337;&#2360;&#2381;&#2335;&#2381;&#2352;&#2367;&#2351;&#2354; &#2325;&#2381;&#2352;&#2375;&#2337;&#2367;&#2335; &#2320;&#2339;&#2381;&#2337; &#2311;&#2344;&#2381;&#2357;&#2375;&#2360;&#2381;&#2335;&#2350;&#2375;&#2344;&#2381;&#2335; &#2325;&#2366;&#2352;&#2381;&#2346;&#2379;&#2352;&#2375;&#2358;&#2344; &#2321;&#2347; &#2311;&#2339;&#2381;&#2337;&#2367;&#2351;&#2366;):</span><strong> &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;: </strong><span style=\"font-weight: 400;\">1994, &#2357;&#2337;&#2379;&#2342;&#2352;&#2366; &#2350;&#2375;&#2306;</span><strong>, &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351;: </strong><span style=\"font-weight: 400;\">&#2350;&#2369;&#2306;&#2348;&#2312;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> Ugadi is a Telugu festival celebrated on the occasion of________.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">&#2313;&#2327;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">________</span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2375;&#2354;&#2369;&#2327;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2360;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>birth of lord Krishn<span style=\"font-family: Cambria Math;\">a</span></p>\n", "<p>birth of lord Rama</p>\n", 
                                "<p>coming of monsoon</p>\n", "<p>Telugu new year</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2349;&#2327;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2381;&#2352;&#2368;&#2325;&#2371;&#2359;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2381;&#2350;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2349;&#2327;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2381;&#2352;&#2368;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2381;&#2350;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2327;&#2350;&#2344;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2340;&#2375;&#2354;&#2369;&#2327;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n"],
                    solution_en: "<p>4.<span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>Telugu new year.</strong><strong> Ugadi festival :</strong><span style=\"font-weight: 400;\"> Celebrated as a new year in South India (majorly Karnataka); Celebrated on the first day of Chaitra month; Lord Brahma, the creator of the universe, is worshiped;&nbsp; A drink called Pachadi is made. </span><strong>Major Festivals of Karnataka :</strong><span style=\"font-weight: 400;\"> Kambala Festival, Hampi Festival, Pattadakal Dance Festival, Makar Sankranti, Vairamudi Festival, Karaga Festival, Vara Mahalakshmi Pooja, Ganesh Chaturthi, Gowri Festival, Sri Vithappa Fair, Dussehra, Tula Sankramana, Karnataka Rajyotsava, Deepavali, Groundnut Festival, Mahamastakabhisheka.</span></p>\n",
                    solution_hi: "<p>4.(d)&nbsp;<strong>&#2340;&#2375;&#2354;&#2369;&#2327;&#2369; &#2344;&#2357; &#2357;&#2352;&#2381;&#2359;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2313;&#2327;&#2366;&#2342;&#2367; &#2340;&#2381;&#2351;&#2380;&#2361;&#2366;&#2352;: </strong><span style=\"font-weight: 400;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2349;&#2366;&#2352;&#2340; (&#2350;&#2369;&#2326;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;) &#2350;&#2375;&#2306; &#2344;&#2319; &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2350;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;; &#2330;&#2376;&#2340;&#2381;&#2352; &#2350;&#2366;&#2360; &#2325;&#2368; &#2346;&#2381;&#2352;&#2341;&#2350; &#2340;&#2367;&#2341;&#2367; &#2325;&#2379; &#2350;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;; &#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2366;&#2306;&#2337; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2340;&#2366; &#2349;&#2327;&#2357;&#2366;&#2344; &#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2366; &#2325;&#2368; &#2346;&#2370;&#2332;&#2366; &#2325;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;; &#2346;&#2330;&#2337;&#2364;&#2368; &#2344;&#2366;&#2350;&#2325; &#2346;&#2375;&#2351; &#2348;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2325;&#2375; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2340;&#2381;&#2351;&#2380;&#2361;&#2366;&#2352; : </strong><span style=\"font-weight: 400;\">&#2325;&#2306;&#2348;&#2366;&#2354;&#2366; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2361;&#2350;&#2381;&#2346;&#2368; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2346;&#2335;&#2381;&#2335;&#2342;&#2325;&#2354; &#2344;&#2371;&#2340;&#2381;&#2351; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2357;&#2376;&#2352;&#2366;&#2350;&#2369;&#2337;&#2368; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2325;&#2352;&#2327;&#2366; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2357;&#2352; &#2350;&#2361;&#2366;&#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2368; &#2346;&#2370;&#2332;&#2366;, &#2327;&#2380;&#2352;&#2368; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2358;&#2381;&#2352;&#2368; &#2357;&#2367;&#2341;&#2346;&#2381;&#2346;&#2366; &#2350;&#2375;&#2354;&#2366;, &#2342;&#2358;&#2361;&#2352;&#2366;, &#2340;&#2369;&#2354;&#2366; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2339;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2342;&#2368;&#2346;&#2366;&#2357;&#2354;&#2368;, &#2350;&#2370;&#2306;&#2327;&#2347;&#2354;&#2368; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2350;&#2361;&#2366;&#2350;&#2360;&#2381;&#2340;&#2325;&#2366;&#2349;&#2367;&#2359;&#2375;&#2325;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Which Indian city is also known as Queen of Deccan?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2325;&#2381;&#2325;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Ahmedabad</p>\n", "<p>Pune</p>\n", 
                                "<p>Amritsar</p>\n", "<p>Puducherry</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2309;&#2361;&#2350;&#2342;&#2366;&#2348;&#2366;&#2342;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2339;&#2375;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2309;&#2350;&#2371;&#2340;&#2360;&#2352;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2342;&#2369;&#2330;&#2375;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>5. <span style=\"font-family: Cambria Math;\">(b) <strong>Pune. </strong><strong>Some Indian Cities and Sobriquets : </strong><span style=\"font-weight: 400;\">City of Taj - Agra (Uttar Pradesh), Boston/Manchester of India - Ahmedabad (Gujarat), Sangam City - Allahabad (Uttar Pradesh), Land of Black Diamond - Asansol (West Bengal), City of Peace - Bardhaman (West Bengal), Temple City of India - Bhubaneswar (Odisha), The Venice of the East - Alappuzha (Kerala), Garden City of India - Bengaluru (Karnataka), Detroit of Asia - Chennai (Tamil Nadu), Manchester of South India - Coimbatore (Tamil Nadu), Scotland of India - Coorg (Karnataka), School Capital of India - Dehradun (Uttarakhand), Queen of the Hills - Darjeeling (West Bengal).</span></span></p>\n",
                    solution_hi: "<p>5. <span style=\"font-family: Cambria Math;\">(b)</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>&#2346;&#2369;&#2339;&#2375;&#2404; &#2325;&#2369;&#2331; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2358;&#2361;&#2352; &#2324;&#2352; &#2313;&#2346;&#2344;&#2366;&#2350; : </strong><span style=\"font-weight: 400;\">&#2340;&#2366;&#2332; &#2325;&#2366; &#2358;&#2361;&#2352; - &#2310;&#2327;&#2352;&#2366; (&#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;), &#2348;&#2379;&#2360;&#2381;&#2335;&#2344;/&#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2350;&#2376;&#2344;&#2330;&#2375;&#2360;&#2381;&#2335;&#2352; - &#2309;&#2361;&#2350;&#2342;&#2366;&#2348;&#2366;&#2342; (&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;), &#2360;&#2306;&#2327;&#2350; &#2360;&#2367;&#2335;&#2368; - &#2311;&#2354;&#2366;&#2361;&#2366;&#2348;&#2366;&#2342; (&#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;), &#2325;&#2366;&#2354;&#2375; &#2361;&#2368;&#2352;&#2375; &#2325;&#2368; &#2349;&#2370;&#2350;&#2367; - &#2310;&#2360;&#2344;&#2360;&#2379;&#2354; (&#2346;&#2358;&#2381;&#2330;&#2367;&#2350; &#2348;&#2306;&#2327;&#2366;&#2354;), &#2358;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2358;&#2361;&#2352; - &#2348;&#2352;&#2381;&#2343;&#2350;&#2366;&#2344; (&#2346;&#2358;&#2381;&#2330;&#2367;&#2350; &#2348;&#2306;&#2327;&#2366;&#2354;) , &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2350;&#2306;&#2342;&#2367;&#2352;&#2379;&#2306; &#2325;&#2366; &#2358;&#2361;&#2352; - &#2349;&#2369;&#2357;&#2344;&#2375;&#2358;&#2381;&#2357;&#2352; (&#2323;&#2337;&#2367;&#2358;&#2366;); &#2346;&#2370;&#2352;&#2381;&#2357; &#2325;&#2366; &#2357;&#2375;&#2344;&#2367;&#2360; - &#2309;&#2354;&#2346;&#2381;&#2346;&#2369;&#2333;&#2366; (&#2325;&#2375;&#2352;&#2354;), &#2327;&#2366;&#2352;&#2381;&#2337;&#2344; &#2360;&#2367;&#2335;&#2368; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; - &#2348;&#2375;&#2306;&#2327;&#2354;&#2369;&#2352;&#2369; (&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;), &#2319;&#2358;&#2367;&#2351;&#2366; &#2325;&#2366; &#2337;&#2375;&#2335;&#2381;&#2352;&#2366;&#2351;&#2335; - &#2330;&#2375;&#2344;&#2381;&#2344;&#2312; (&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;), &#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2350;&#2376;&#2344;&#2330;&#2375;&#2360;&#2381;&#2335;&#2352; - &#2325;&#2379;&#2351;&#2306;&#2348;&#2335;&#2370;&#2352; (&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;), &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2360;&#2381;&#2325;&#2377;&#2335;&#2354;&#2376;&#2306;&#2337; - &#2325;&#2370;&#2352;&#2381;&#2327; (&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;), &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2381;&#2325;&#2370;&#2354; &#2325;&#2376;&#2346;&#2367;&#2335;&#2354; - &#2342;&#2375;&#2361;&#2352;&#2366;&#2342;&#2370;&#2344; (&#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337;), &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2352;&#2366;&#2344;&#2368; - &#2342;&#2366;&#2352;&#2381;&#2332;&#2367;&#2354;&#2367;&#2306;&#2327; (&#2346;&#2358;&#2381;&#2330;&#2367;&#2350; &#2348;&#2306;&#2327;&#2366;&#2354;) &#2404;&nbsp;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> Which of the following Constitutional Amendment Act added the Right to Education to the Indian constitution?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2358;&#2379;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2367;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>85th</p>\n", "<p>88th</p>\n", 
                                "<p>87th</p>\n", "<p>86th</p>\n"],
                    options_hi: ["<p>85<span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2306;</span></p>\n", "<p>88<span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2306;</span></p>\n",
                                "<p>87<span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2306;</span></p>\n", "<p>86<span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2306;</span></p>\n"],
                    solution_en: "<p>6.<span style=\"font-family: Cambria Math;\">(d) </span><strong>86th Amendment Act, 2002 : </strong><span style=\"font-weight: 400;\">Insertion of new Article (21A) - State shall provide free and compulsory education of all children (6 - 14) years as a Fundamental Right. </span><strong>85th Amendment Act, 2001 : </strong><span style=\"font-weight: 400;\">Brought in for also giving to SCs and STs in matters of reservation in promotion. </span><strong>87th Amendment Act, 2003 : </strong><span style=\"font-weight: 400;\">Provided for the readjustment and rationalization of territorial constituencies in the states on the basis of the population figures of 2001 census and not 1991 census as provided earlier by the 84th Amendment Act of 2001. </span><strong>88th Amendment Act, 2003</strong><span style=\"font-weight: 400;\"> : To extend statutory cover for levy and utilization of Service Tax.</span></p>\n",
                    solution_hi: "<p>6.(d)&nbsp;<strong>86&#2357;&#2366;&#2306; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 2002 : </strong><span style=\"font-weight: 400;\">&#2344;&#2319; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; (21A) &#2325;&#2368; &#2346;&#2381;&#2352;&#2357;&#2367;&#2359;&#2381;&#2335;&#2367; - &#2352;&#2366;&#2332;&#2381;&#2351; &#2350;&#2380;&#2354;&#2367;&#2325; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2360;&#2349;&#2368; &#2348;&#2330;&#2381;&#2330;&#2379;&#2306; (6-14 &#2357;&#2352;&#2381;&#2359;) &#2325;&#2379; &#2350;&#2369;&#2347;&#2381;&#2340; &#2324;&#2352; &#2309;&#2344;&#2367;&#2357;&#2366;&#2352;&#2381;&#2351; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2375;&#2327;&#2366;&#2404; </span><strong>85&#2357;&#2375;&#2306; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 2001: </strong><span style=\"font-weight: 400;\">&#2346;&#2342;&#2379;&#2344;&#2381;&#2344;&#2340;&#2367; &#2350;&#2375;&#2306; &#2310;&#2352;&#2325;&#2381;&#2359;&#2339; &#2325;&#2375; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2367;&#2340; &#2332;&#2366;&#2340;&#2367; &#2324;&#2352; &#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2367;&#2340; &#2332;&#2344;&#2332;&#2366;&#2340;&#2367; &#2325;&#2379; &#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2368; &#2357;&#2352;&#2367;&#2359;&#2381;&#2336;&#2340;&#2366; &#2342;&#2375;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2349;&#2368; &#2354;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404;&nbsp; </span><strong>87&#2357;&#2375;&#2306; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 2003 :</strong><span style=\"font-weight: 400;\"> 2001 &#2325;&#2368; &#2332;&#2344;&#2327;&#2339;&#2344;&#2366; &#2325;&#2375; &#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2366;&#2342;&#2375;&#2358;&#2367;&#2325; &#2344;&#2367;&#2352;&#2381;&#2357;&#2366;&#2330;&#2344; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2375; &#2346;&#2369;&#2344;&#2352;&#2381;&#2360;&#2350;&#2366;&#2351;&#2379;&#2332;&#2344; &#2324;&#2352; &#2351;&#2369;&#2325;&#2381;&#2340;&#2367;&#2325;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;, &#2344; &#2325;&#2367; 1991 &#2325;&#2368; &#2332;&#2344;&#2327;&#2339;&#2344;&#2366; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; &#2332;&#2376;&#2360;&#2366; &#2325;&#2367; 2001 &#2325;&#2375; 84&#2357;&#2375;&#2306; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2361;&#2354;&#2375; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; </span><strong>88 &#2357;&#2375;&#2306; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 2003 : </strong><span style=\"font-weight: 400;\">&#2360;&#2375;&#2357;&#2366; &#2325;&#2352; &#2354;&#2327;&#2366;&#2344;&#2375; &#2324;&#2352; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2310;&#2357;&#2352;&#2339; &#2325;&#2366; &#2357;&#2367;&#2360;&#2381;&#2340;&#2366;&#2352; &#2325;&#2352;&#2344;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">In which of</span><span style=\"font-family: Cambria Math;\"> the following ways, aggregate demand function is related to consumption </span><span style=\"font-family: Cambria Math;\">Function ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. They have the same slope C.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. The aggregate demand function is parallel to the consumption function.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2305;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> (aggregate demand function), </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2349;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. </span><span style=\"font-family: Cambria Math;\">&#2313;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2338;&#2354;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2405;</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2305;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2349;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Neither I nor II</p>\n", "<p>Only II</p>\n", 
                                "<p>Only I</p>\n", "<p>Both I and II</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2405;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> I</span><span style=\"font-family: Cambria Math;\">I </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n"],
                    solution_en: "<p>7.<span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>Both I and II.</strong><span style=\"font-weight: 400;\"> </span><strong>Aggregate demand - </strong><span style=\"font-weight: 400;\">the total demand for goods and services within a particular market. It is the sum of four components: consumption, investment, government spending, and net exports. </span><strong>Consumption</strong><span style=\"font-weight: 400;\"> can change for a number of reasons, including movements in income, taxes, expectations about future income, and changes in wealth levels.</span></p>\n",
                    solution_hi: "<p>7.(d) <strong><span style=\"font-family: Cambria Math;\">I </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2305;&#2327;</span></strong><span style=\"font-family: Cambria Math;\"><strong> -</strong> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2332;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2357;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2306;&#2327;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2335;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;</span><span style=\"font-family: Cambria Math;\">&#2376;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2349;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2351;&#2366;&#2340;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2349;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2330;&#2338;&#2364;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2349;&#2357;&#2367;&#2359;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2375;&#2325;&#2381;&#2359;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2340;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2342;&#2354;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> ________a 500-year-old dance form, is an Indian Classical dance that developed from the Vaishnavite monasteries of Assam.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.________</span><span style=\"font-family: Cambria Math;\"> ,500 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2352;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2371;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2371;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2376;&#2359;&#2381;&#2339;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2336;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2360;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Kuchipudi</p>\n", "<p>Bharatnatyam</p>\n", 
                                "<p>Sattriya</p>\n", "<p>Mohiniattam</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2330;&#2367;&#2346;&#2369;&#2337;&#2364;&#2368;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2349;&#2352;&#2340;&#2344;&#2366;&#2335;&#2381;&#2351;&#2350;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2340;&#2381;&#2340;&#2381;&#2352;&#2367;&#2351;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2350;&#2379;&#2361;&#2367;&#2344;&#2368;&#2309;&#2335;&#2381;&#2335;&#2350;</span></p>\n"],
                    solution_en: "<p>8.<span style=\"font-family: Cambria Math;\">(c)<strong>Sattriya.</strong><span style=\"font-weight: 400;\"> </span><strong>Bharatanatyam (Tamil Nadu) : </strong><span style=\"font-weight: 400;\">An ekaharya dance genre in which a single dancer represents numerous roles in a single performance; Most crucial text - Nandikesvara&rsquo;s Abhinaya Darpana. </span><strong>Kuchipudi (Andhra Pradesh) : </strong><span style=\"font-weight: 400;\">Its general moniker was yakshagana developed in the 17th century by Siddhendra Yogi; Performed in groups because it is a dance play. </span><strong>Mohiniyattam (Kerala) :</strong><span style=\"font-weight: 400;\"> The traditional solo dancing style of Kerala (an incarnation of Lord Vishnu). In the 1709 book Vyavahara Mala by Mazhamagalam Narayanan Namputiri, Mohiniyattam is mentioned. </span></span></p>\n",
                    solution_hi: "<p>8.(c) <strong>&#2360;&#2340;&#2381;&#2340;&#2381;&#2352;&#2367;&#2351;&#2366;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2349;&#2352;&#2340;&#2344;&#2366;&#2335;&#2381;&#2351;&#2350; (&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;) :</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2319;&#2325;&#2361;&#2366;&#2352;&#2381;&#2351; &#2344;&#2371;&#2340;&#2381;&#2351; &#2358;&#2376;&#2354;&#2368; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2319;&#2325; &#2344;&#2352;&#2381;&#2340;&#2325; &#2319;&#2325; &#2361;&#2368; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2350;&#2375;&#2306; &#2325;&#2312; &#2349;&#2370;&#2350;&#2367;&#2325;&#2366;&#2323;&#2306; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;; &#2360;&#2348;&#2360;&#2375; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2327;&#2381;&#2352;&#2306;&#2341; - &#2344;&#2306;&#2342;&#2367;&#2325;&#2375;&#2358;&#2381;&#2357;&#2352; &#2325;&#2366; &#2309;&#2349;&#2367;&#2344;&#2351; &#2342;&#2352;&#2381;&#2346;&#2339;&#2404; </span><strong>&#2325;&#2369;&#2330;&#2367;&#2346;&#2369;&#2337;&#2364;&#2368; (&#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;) : </strong><span style=\"font-weight: 400;\">&#2311;&#2360;&#2325;&#2366; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2313;&#2346;&#2344;&#2366;&#2350; &#2351;&#2325;&#2381;&#2359;&#2327;&#2366;&#2344; &#2341;&#2366; &#2332;&#2367;&#2360;&#2375; 17&#2357;&#2368;&#2306; &#2358;&#2340;&#2366;&#2348;&#2381;&#2342;&#2368; &#2350;&#2375;&#2306; &#2360;&#2367;&#2342;&#2381;&#2343;&#2375;&#2306;&#2342;&#2381;&#2352; &#2351;&#2379;&#2327;&#2368; &#2344;&#2375; &#2357;&#2367;&#2325;&#2360;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2341;&#2366;; &#2360;&#2350;&#2370;&#2361;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2351;&#2361; &#2319;&#2325; &#2344;&#2371;&#2340;&#2381;&#2351;-&#2344;&#2366;&#2335;&#2325; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2379;&#2361;&#2367;&#2344;&#2368;&#2309;&#2335;&#2381;&#2335;&#2350; (&#2325;&#2375;&#2352;&#2354;) : </strong><span style=\"font-weight: 400;\">&#2325;&#2375;&#2352;&#2354; &#2325;&#2368; &#2346;&#2366;&#2352;&#2306;&#2346;&#2352;&#2367;&#2325; &#2319;&#2325;&#2354; &#2344;&#2371;&#2340;&#2381;&#2351; &#2358;&#2376;&#2354;&#2368; (&#2349;&#2327;&#2357;&#2366;&#2344; &#2357;&#2367;&#2359;&#2381;&#2339;&#2369; &#2325;&#2366; &#2319;&#2325; &#2309;&#2357;&#2340;&#2366;&#2352;)&#2404;&nbsp; 1709 &#2350;&#2375;&#2306; &#2350;&#2333;&#2350;&#2327;&#2354;&#2350; &#2344;&#2366;&#2352;&#2366;&#2351;&#2339;&#2344; &#2344;&#2306;&#2346;&#2369;&#2340;&#2367;&#2352;&#2368; &#2325;&#2368; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2357;&#2381;&#2351;&#2357;&#2361;&#2366;&#2352; &#2350;&#2366;&#2354;&#2366; &#2350;&#2375;&#2306; &#2350;&#2379;&#2361;&#2367;&#2344;&#2368;&#2309;&#2335;&#2381;&#2335;&#2350; &#2325;&#2366; &#2313;&#2354;&#2381;&#2354;&#2375;&#2326; &#2361;&#2376;&#2404;&nbsp;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">Who is famously known as the Tiger of Mysore ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\">&#2350;&#2376;&#2360;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2366;&#2311;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Baji Rao I</p>\n", "<p>Tipu Sultan</p>\n", 
                                "<p>Sheikh Moh<span style=\"font-family: Cambria Math;\">ammad Abdullah</span></p>\n", "<p>Asaf Jah I</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2332;&#2368;&#2352;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2335;&#2368;&#2346;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2379;&#2361;&#2350;&#2381;&#2350;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2348;&#2381;&#2342;&#2369;&#2354;&#2381;&#2354;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2310;&#2360;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span></p>\n"],
                    solution_en: "<p>9.<span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>Tipu Sultan :&nbsp; </strong><span style=\"font-weight: 400;\">Born on 20th November 1750 at Devanahalli in Bangalore, Karnataka state; Named \'Tipu Sultan\' after the Saint \"Tipu Mastan Aulia\" of Arcot. </span><strong>Original name - </strong><span style=\"font-weight: 400;\">Fateh Ali Sahab Tipu. The</span><strong> \"Tree of Liberty \'\' </strong><span style=\"font-weight: 400;\">at Srirangapatnam was founded in 1794 by the French Republican officers with the support of Tipu Sultan. Tipu Sultan fought the</span><strong> Third-Anglo Mysore War (1790-92)</strong><span style=\"font-weight: 400;\">. He was defeated by the East India Company for that result he had signed the</span><strong> \"Treaty of Serirangapatnam\"</strong><span style=\"font-weight: 400;\">. He died on 4th May 1799 during the Fourth Anglo-Mysore War.</span></p>\n",
                    solution_hi: "<p>9.(b)&nbsp;<strong>&#2335;&#2368;&#2346;&#2370; &#2360;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344; : </strong><span style=\"font-weight: 400;\">20 &#2344;&#2357;&#2306;&#2348;&#2352; 1750 &#2325;&#2379; &#2348;&#2376;&#2306;&#2327;&#2354;&#2379;&#2352;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2375; &#2342;&#2375;&#2357;&#2344;&#2361;&#2354;&#2381;&#2354;&#2368; &#2350;&#2375;&#2306; &#2332;&#2344;&#2381;&#2350;&#2375;; &#2310;&#2352;&#2325;&#2379;&#2335; &#2325;&#2375; &#2360;&#2306;&#2340; \"&#2335;&#2368;&#2346;&#2370; &#2350;&#2360;&#2381;&#2340;&#2366;&#2344; &#2324;&#2354;&#2367;&#2351;&#2366;\" &#2325;&#2375; &#2344;&#2366;&#2350; &#2346;&#2352; \'&#2335;&#2368;&#2346;&#2370; &#2360;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344;\' &#2344;&#2366;&#2350; &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404; </span><strong>&#2350;&#2370;&#2354; &#2344;&#2366;&#2350; - </strong><span style=\"font-weight: 400;\">&#2347;&#2340;&#2375;&#2361; &#2309;&#2354;&#2368; &#2360;&#2366;&#2361;&#2348; &#2335;&#2368;&#2346;&#2370;&#2404;&nbsp; 1794 &#2350;&#2375;&#2306; &#2335;&#2368;&#2346;&#2370; &#2360;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344; &#2325;&#2375; &#2360;&#2350;&#2352;&#2381;&#2341;&#2344; &#2360;&#2375; &#2347;&#2381;&#2352;&#2366;&#2306;&#2360;&#2368;&#2360;&#2368; &#2352;&#2367;&#2346;&#2348;&#2381;&#2354;&#2367;&#2325;&#2344; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2358;&#2381;&#2352;&#2368;&#2352;&#2306;&#2327;&#2346;&#2335;&#2381;&#2335;&#2344;&#2350; &#2350;&#2375;&#2306; </span><strong>\"&#2335;&#2381;&#2352;&#2368; &#2321;&#2347;&#2364; &#2354;&#2367;&#2348;&#2352;&#2381;&#2335;&#2368;\" </strong><span style=\"font-weight: 400;\">&#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;&nbsp; &#2325;&#2368; &#2327;&#2312; &#2341;&#2368;&#2404; &#2335;&#2368;&#2346;&#2370; &#2360;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344; &#2344;&#2375;</span><strong> &#2340;&#2371;&#2340;&#2368;&#2351;-&#2310;&#2306;&#2327;&#2381;&#2354; &#2350;&#2376;&#2360;&#2370;&#2352; &#2351;&#2369;&#2342;&#2381;&#2343; (1790-92) </strong><span style=\"font-weight: 400;\">&#2354;&#2337;&#2364;&#2366;&#2404; &#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2352;&#2366;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366; &#2332;&#2367;&#2360;&#2325;&#2375; &#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2360;&#2381;&#2357;&#2352;&#2370;&#2346; &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375;</span><strong> \"&#2358;&#2381;&#2352;&#2368;&#2352;&#2306;&#2327;&#2346;&#2335;&#2381;&#2335;&#2344;&#2350; &#2325;&#2368; &#2360;&#2306;&#2343;&#2367;\"</strong><span style=\"font-weight: 400;\"> &#2346;&#2352; &#2361;&#2360;&#2381;&#2340;&#2366;&#2325;&#2381;&#2359;&#2352; &#2325;&#2367;&#2319; &#2341;&#2375;&#2404; &#2330;&#2380;&#2341;&#2375; &#2310;&#2306;&#2327;&#2381;&#2354;-&#2350;&#2376;&#2360;&#2370;&#2352; &#2351;&#2369;&#2342;&#2381;&#2343; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; 4 &#2350;&#2312; 1799 &#2325;&#2379; &#2313;&#2344;&#2325;&#2368; &#2350;&#2371;&#2340;&#2381;&#2351;&#2369; &#2361;&#2379; &#2327;&#2312;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> What is the venue for 4th Edition of Khelo India Youth Games?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2375;&#2354;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2350;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2380;&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Maharashtra</p>\n", "<p>Delhi</p>\n", 
                                "<p>Gujarat</p>\n", "<p>Haryana</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;</span></p>\n"],
                    solution_en: "<p>10.(d)&nbsp;<strong>Khelo India Youth Games (KIYG) : </strong><span style=\"font-weight: 400;\">Formerly Khelo India School Games (KISG), are the annual national level multidisciplinary grassroot games in India held in January or February for two categories, namely under-17 school students and under-21 college students.</span><strong> Ist edition :&nbsp; </strong><span style=\"font-weight: 400;\">2018, Delhi (1st - Haryana, 2nd - Maharashtra, 3rd - Delhi. </span><strong>IInd edition : </strong><span style=\"font-weight: 400;\">2019, Pune (1st - Maharashtra, 2nd - Haryana, 3rd - Delhi). </span><strong>IIIrd edition : </strong><span style=\"font-weight: 400;\">2020, Guwahati (1st - Maharashtra, 2nd - Haryana, 3rd - Delhi). </span><strong>IVth edition :</strong><span style=\"font-weight: 400;\"> 2021, Panchkula (1st -&nbsp; Haryana, 2nd -&nbsp; Maharashtra, 3rd - Karnataka). </span><strong>Vth edition : </strong><span style=\"font-weight: 400;\">2022, Bhopal (1st - Maharashtra, 2nd - Haryana, 3rd - Madhya Pradesh).</span></p>\n",
                    solution_hi: "<p>10.(d)&nbsp;<strong>&#2326;&#2375;&#2354;&#2379; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2351;&#2370;&#2341; &#2327;&#2375;&#2350;&#2381;&#2360; (KIYG) : </strong><span style=\"font-weight: 400;\">&#2346;&#2370;&#2352;&#2381;&#2357; &#2344;&#2366;&#2350; &#2326;&#2375;&#2354;&#2379; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2360;&#2381;&#2325;&#2370;&#2354; &#2327;&#2375;&#2350;&#2381;&#2360; (KISG), &#2351;&#2361; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2332;&#2344;&#2357;&#2352;&#2368; &#2351;&#2366; &#2347;&#2352;&#2357;&#2352;&#2368; &#2350;&#2375;&#2306; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2360;&#2381;&#2340;&#2352; &#2325;&#2375; &#2348;&#2361;&#2369;-&#2357;&#2367;&#2359;&#2351;&#2325; &#2332;&#2350;&#2368;&#2344;&#2368; &#2360;&#2381;&#2340;&#2352; &#2325;&#2375; &#2326;&#2375;&#2354; &#2361;&#2376;&#2306;, &#2332;&#2379; &#2309;&#2306;&#2337;&#2352;-17 &#2360;&#2381;&#2325;&#2370;&#2354;&#2368; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2324;&#2352; &#2309;&#2306;&#2337;&#2352;-21 &#2325;&#2377;&#2354;&#2375;&#2332; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2367;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;&#2404; </span><strong>&#2346;&#2361;&#2354;&#2366; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; :</strong><span style=\"font-weight: 400;\"> 2018, &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; (&#2346;&#2361;&#2354;&#2366; - &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;, &#2342;&#2370;&#2360;&#2352;&#2366; - &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2340;&#2368;&#2360;&#2352;&#2366; - &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;)&#2404; </span><strong>&#2342;&#2370;&#2360;&#2352;&#2366; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; :</strong><span style=\"font-weight: 400;\"> 2019, &#2346;&#2369;&#2339;&#2375; (&#2346;&#2361;&#2354;&#2366; - &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2342;&#2370;&#2360;&#2352;&#2366; - &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;, &#2340;&#2368;&#2360;&#2352;&#2366; - &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;)&#2404; </span><strong>&#2340;&#2368;&#2360;&#2352;&#2366; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; :</strong><span style=\"font-weight: 400;\"> 2020, &#2327;&#2369;&#2357;&#2366;&#2361;&#2366;&#2335;&#2368; (&#2346;&#2361;&#2354;&#2366; - &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2342;&#2370;&#2360;&#2352;&#2366; - &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;, &#2340;&#2368;&#2360;&#2352;&#2366; - &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;)&#2404; </span><strong>&#2330;&#2380;&#2341;&#2366; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; : </strong><span style=\"font-weight: 400;\">2021, &#2346;&#2306;&#2330;&#2325;&#2369;&#2354;&#2366; (&#2346;&#2361;&#2354;&#2366; - &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;, &#2342;&#2370;&#2360;&#2352;&#2366; - &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2340;&#2368;&#2360;&#2352;&#2366; - &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;)&#2404; </span><strong>&#2346;&#2366;&#2306;&#2330;&#2357;&#2366;&#2306; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; : </strong><span style=\"font-weight: 400;\">2022, &#2349;&#2379;&#2346;&#2366;&#2354; (&#2346;&#2361;&#2354;&#2366; - &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2342;&#2370;&#2360;&#2352;&#2366; - &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;, &#2340;&#2368;&#2360;&#2352;&#2366; - &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;)&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Apple II is an example of ______ Generation of computer.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">&#2319;&#2346;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2405;</span><span style=\"font-family: Cambria Math;\"> ________ </span><span style=\"font-family: Cambria Math;\">&#2346;&#2368;&#2338;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;&#2381;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Third</p>\n", "<p>Second</p>\n", 
                                "<p>First</p>\n", "<p>Fourth</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2340;&#2371;&#2340;&#2368;&#2351;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2341;</span></p>\n"],
                    solution_en: "<p>11.<span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>Fourth. Generations of Computer : First generation </strong><span style=\"font-weight: 400;\">(1940-1956): Vacuum-Tube Based,&nbsp; Examples - UNIVAC1, ENIAC, IBM 701 and IBM 650. </span><strong>Second generation (</strong><span style=\"font-weight: 400;\">1956-1963), Transistor Based, </span><strong>&nbsp;</strong><span style=\"font-weight: 400;\">Examples - IBM 1401, IBM 7094 AND IBM 7090, UNIVAC 1107. </span><strong>Third generation </strong><span style=\"font-weight: 400;\">(1964-1971) Integrated-Circuit Based, Examples - IBM 370, IBM 360, UNIVAC 1108.&nbsp; </span><strong>Fourth Generation (</strong><span style=\"font-weight: 400;\">1971-Present): Microprocessor- Based, Examples - STAR 1000, IBM PC. </span><strong>Fifth Generation </strong><span style=\"font-weight: 400;\">: Artificial Intelligence Based, Examples - laptops, desktops, tablets, smartphones, etc.</span></p>\n",
                    solution_hi: "<p>11.(d)&nbsp;<strong>&#2330;&#2340;&#2369;&#2352;&#2381;&#2341; </strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2368; &#2346;&#2368;&#2338;&#2364;&#2367;&#2351;&#2366;&#2305; : &#2346;&#2361;&#2354;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368;(</strong><span style=\"font-weight: 400;\">1940-1950): &#2357;&#2376;&#2325;&#2381;&#2351;&#2370;&#2350;-&#2335;&#2381;&#2351;&#2370;&#2348; &#2310;&#2343;&#2366;&#2352;&#2367;&#2340;, &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - UNIVAC 1, ENIAC, IBM 701 &#2324;&#2352; IBM 650 &#2404; </span><strong>&#2342;&#2370;&#2360;&#2352;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; (</strong><span style=\"font-weight: 400;\">1950-1960): &#2335;&#2381;&#2352;&#2366;&#2306;&#2332;&#2367;&#2360;&#2381;&#2335;&#2352; &#2310;&#2343;&#2366;&#2352;&#2367;&#2340;, &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - IBM 1401, IBM 7094 &#2324;&#2352; IBM 7090, UNIVAC 1107 &#2404; </span><strong>&#2340;&#2368;&#2360;&#2352;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368;(</strong><span style=\"font-weight: 400;\">1960-1970): &#2311;&#2306;&#2335;&#2368;&#2327;&#2381;&#2352;&#2375;&#2335;&#2375;&#2337;-&#2360;&#2352;&#2381;&#2325;&#2367;&#2335; &#2310;&#2343;&#2366;&#2352;&#2367;&#2340;, &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - IBM 370, IBM 360, UNIVAC 1108 &#2311;&#2340;&#2381;&#2351;&#2366;&#2342;&#2367;&#2404; </span><strong>&#2330;&#2380;&#2341;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; (</strong><span style=\"font-weight: 400;\">1970- &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;): &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352;-&#2310;&#2343;&#2366;&#2352;&#2367;&#2340;, &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - STAR 1000, </span><strong>APPLE II</strong><span style=\"font-weight: 400;\">, IBM PC &#2311;&#2340;&#2381;&#2351;&#2366;&#2342;&#2367;&#2404; </span><strong>&#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306; &#2346;&#2368;&#2338;&#2364;&#2368; -</strong><span style=\"font-weight: 400;\"> &#2310;&#2352;&#2381;&#2335;&#2367;&#2347;&#2367;&#2358;&#2367;&#2351;&#2354; &#2311;&#2306;&#2335;&#2375;&#2354;&#2367;&#2332;&#2375;&#2306;&#2360; &#2310;&#2343;&#2366;&#2352;&#2367;&#2340;, &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2354;&#2376;&#2346;&#2335;&#2377;&#2346;, &#2337;&#2375;&#2360;&#2381;&#2325;&#2335;&#2377;&#2346;, &#2335;&#2376;&#2348;&#2354;&#2375;&#2335;, &#2360;&#2381;&#2350;&#2366;&#2352;&#2381;&#2335;&#2347;&#2379;&#2344; &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">What is the full form of HTTP?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> HTTP (</span><span style=\"font-family: Cambria Math;\">&#2319;&#2330;&#2335;&#2368;&#2335;&#2368;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>High Text Transmission Protocol</p>\n", "<p>Hyper Text<span style=\"font-family: Cambria Math;\"> Transmission Protocol</span></p>\n", 
                                "<p>Hyper Text Transfer Protocol</p>\n", "<p>High Text Transfer Protocol</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2311;&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2350;&#2367;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2311;&#2346;&#2352;&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2350;&#2367;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2311;&#2346;&#2352;&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2311;&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;</span></p>\n"],
                    solution_en: "<p>12. <span style=\"font-family: Cambria Math;\">(c)<strong>&nbsp;</strong></span><strong>Hyper Text Transfer Protocol. </strong><span style=\"font-weight: 400;\">It is an application-layer protocol for transmitting hypermedia documents, such as HTML. The primary or most commonly-used HTTP methods are POST, GET, PUT, PATCH, and DELETE. Other application layer protocols are TELNET, FTP (File transfer protocol), TFTP (Trivial File Transfer Protocol), NFS (Network File System), SMTP (Simple Mail Transfer Protocol), LPD (Line Printer Daemon), SNMP (Simple Network Management Protocol), etc.&nbsp;</span></p>\n",
                    solution_hi: "<p>12. <span style=\"font-family: Cambria Math;\">(c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2361;&#2366;&#2311;&#2346;&#2352; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;</strong><span style=\"font-weight: 400;\">&#2404; &#2351;&#2361; HTML &#2332;&#2376;&#2360;&#2375; &#2361;&#2366;&#2311;&#2346;&#2352;&#2350;&#2368;&#2337;&#2367;&#2351;&#2366; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364;&#2379;&#2306; &#2325;&#2379; &#2346;&#2381;&#2352;&#2360;&#2366;&#2352;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344;-&#2354;&#2375;&#2351;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354; &#2361;&#2376;&#2404; &#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325; &#2351;&#2366; &#2360;&#2348;&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2368; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; HTTP &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; - POST, GET, PUT, PATCH &#2324;&#2352; DELETE &#2404; &#2309;&#2344;&#2381;&#2351; &#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344; &#2354;&#2375;&#2351;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;- TELNET, FTP (&#2347;&#2366;&#2311;&#2354; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;), TFTP (&#2335;&#2381;&#2352;&#2367;&#2357;&#2367;&#2351;&#2354; &#2347;&#2366;&#2311;&#2354; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;), NFS (&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2347;&#2366;&#2311;&#2354; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;), </span><span style=\"font-weight: 400;\">SMTP </span><span style=\"font-weight: 400;\">(&#2360;&#2367;&#2306;&#2346;&#2354; &#2350;&#2375;&#2354; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;), </span><span style=\"font-weight: 400;\">LPD </span><span style=\"font-weight: 400;\">(&#2354;&#2366;&#2311;&#2344; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352; &#2337;&#2375;&#2350;&#2344;), </span><span style=\"font-weight: 400;\">SNMP </span><span style=\"font-weight: 400;\">(&#2360;&#2367;&#2306;&#2346;&#2354; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2350;&#2376;&#2344;&#2375;&#2332;&#2350;&#2375;&#2306;&#2335; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354; ) &#2310;&#2342;&#2367; &#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">Which of the following statements is correct regarding the Lucknow Pact? </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. It was signed in the year 1916.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. It was signed between the moderate and radical factions of Congress.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">&#2354;&#2326;&#2344;&#2314;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2333;&#2380;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> I. </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 1916 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2360;&#2381;&#2340;&#2366;&#2325;&#2381;&#2359;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2375;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2405;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2343;&#2381;&#2351;&#2350;&#2366;&#2352;&#2381;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2333;&#2380;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    options_en: ["<p>Both I and II</p>\n", "<p>Neither I nor II</p>\n", 
                                "<p>Only I</p>\n", "<p>Only II</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> I</span><span style=\"font-family: Cambria Math;\">I </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2405;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n"],
                    solution_en: "<p>13. <span style=\"font-family: Cambria Math;\">(c)<strong>&nbsp;</strong></span><strong>Only I. </strong><strong>Lucknow Pact :</strong><span style=\"font-weight: 400;\"> An agreement reached between the Indian National Congress and the Muslim League (AIML) at a joint session held in Lucknow in December 1916; allowed representation to religious minorities in the provincial legislatures. It also helped establish cordial relations between the \'militant\' faction led by the Lal Bal Pal trio (Lala Lajpat Rai, Bal Gangadhar Tilak and Bipin Chandra Pal) and the \'moderate\' faction led by Gopal Krishna Gokhale and later represented by Gandhi. </span><strong>President : </strong><span style=\"font-weight: 400;\">Ambica Charan Mazumdar.</span></p>\n",
                    solution_hi: "<p>13. <span style=\"font-family: Cambria Math;\">(c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2325;&#2375;&#2357;&#2354; I&nbsp; </strong><span style=\"font-weight: 400;\">&#2404;&nbsp; </span><strong>&#2354;&#2326;&#2344;&#2314; &#2360;&#2350;&#2333;&#2380;&#2340;&#2366; :</strong><span style=\"font-weight: 400;\"> &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; 1916 &#2350;&#2375;&#2306; &#2354;&#2326;&#2344;&#2314; &#2350;&#2375;&#2306; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2319;&#2325; &#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340; &#2360;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360; &#2324;&#2352; &#2350;&#2369;&#2360;&#2381;&#2354;&#2367;&#2350; &#2354;&#2368;&#2327; (AIML) &#2325;&#2375; &#2348;&#2368;&#2330; &#2319;&#2325; &#2360;&#2350;&#2333;&#2380;&#2340;&#2366; &#2361;&#2369;&#2310;; &#2311;&#2360;&#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2366;&#2306;&#2340;&#2368;&#2351; &#2357;&#2367;&#2343;&#2366;&#2344;&#2360;&#2349;&#2366;&#2323;&#2306; &#2350;&#2375;&#2306; &#2343;&#2366;&#2352;&#2381;&#2350;&#2367;&#2325; &#2309;&#2354;&#2381;&#2346;&#2360;&#2306;&#2326;&#2381;&#2351;&#2325;&#2379;&#2306; &#2325;&#2379; &#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2368; &#2327;&#2312;&#2404; &#2311;&#2360;&#2344;&#2375;</span><strong> &#2354;&#2366;&#2354; &#2348;&#2366;&#2354; &#2346;&#2366;&#2354; </strong><span style=\"font-weight: 400;\">&#2340;&#2367;&#2325;&#2337;&#2364;&#2368; (&#2354;&#2366;&#2354;&#2366; &#2354;&#2366;&#2332;&#2346;&#2340; &#2352;&#2366;&#2351;, &#2348;&#2366;&#2354; &#2327;&#2306;&#2327;&#2366;&#2343;&#2352; &#2340;&#2367;&#2354;&#2325; &#2324;&#2352; &#2348;&#2367;&#2346;&#2367;&#2344; &#2330;&#2306;&#2342;&#2381;&#2352; &#2346;&#2366;&#2354;) &#2325;&#2375; &#2344;&#2375;&#2340;&#2371;&#2340;&#2381;&#2357; &#2357;&#2366;&#2354;&#2375; \'&#2313;&#2327;&#2381;&#2352;&#2357;&#2366;&#2342;&#2368;\' &#2327;&#2369;&#2335; &#2324;&#2352; &#2327;&#2379;&#2346;&#2366;&#2354; &#2325;&#2371;&#2359;&#2381;&#2339; &#2327;&#2379;&#2326;&#2354;&#2375; &#2325;&#2375; &#2344;&#2375;&#2340;&#2371;&#2340;&#2381;&#2357; &#2357;&#2366;&#2354;&#2375; \'&#2313;&#2342;&#2366;&#2352;&#2357;&#2366;&#2342;&#2368;\' &#2327;&#2369;&#2335; &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2380;&#2361;&#2366;&#2352;&#2381;&#2342;&#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2348;&#2306;&#2343; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2349;&#2368; &#2350;&#2342;&#2342; &#2325;&#2368;, &#2348;&#2366;&#2342; &#2350;&#2375;&#2306; &#2311;&#2360;&#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357; &#2327;&#2366;&#2306;&#2343;&#2368; &#2332;&#2368; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2404; </span><strong>&#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; </strong><span style=\"font-weight: 400;\">: &#2309;&#2306;&#2348;&#2367;&#2325;&#2366; &#2330;&#2352;&#2339; &#2350;&#2332;&#2370;&#2350;&#2342;&#2366;&#2352;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">Which of the following Airport become 1st airport in India to be run entirely on hydro and solar energy?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2337;&#2381;&#2337;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2380;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2352;&#2381;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2330;&#2366;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2337;&#2381;&#2337;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Indira Gandhi International Airport, Delhi</p>\n", "<p>Chennai International Airport, Tamil Nadu</p>\n", 
                                "<p>Dabolim Airport, Goa</p>\n", "<p>Chhatrapati Shivaji Maharaj International Airport, Mumbai</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2342;&#2367;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2366;&#2306;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2381;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2337;&#2381;&#2337;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2375;&#2344;&#2381;&#2344;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2337;&#2381;&#2337;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2337;&#2366;&#2348;&#2379;&#2354;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2337;&#2381;&#2337;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2357;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2331;&#2340;&#2381;&#2352;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2367;&#2357;&#2366;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2337;&#2381;&#2337;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2306;&#2348;&#2312;</span></p>\n"],
                    solution_en: "<p>14.<span style=\"font-family: Cambria Math;\">(a) <strong>Indira Gandhi International Airport, Delhi. Important international Airports : </strong><span style=\"font-weight: 400;\">Rajiv Gandhi International Airport (Hyderabad, Telangana), Sri Guru Ram Dass Jee International Airport (Amritsar, Punjab), Lokpriya Gopinath Bordoloi International Airport (Guwahati, Assam), Biju Patnaik International Airport (Bhubaneswar, Odisha), Gaya Airport (Gaya, Bihar),Veer Savarkar International Airport (Port Blair, Andaman and Nicobar Islands), Sardar Vallabhbhai Patel International Airport (Ahmedabad, Gujarat), Kempegowda International Airport (Bengaluru, Karnataka), Mangalore International Airport (Mangalore, Karnataka), Cochin International Airport (Kochi, Kerala) etc.</span></span></p>\n",
                    solution_hi: "<p>14.(a)&nbsp;<strong>&#2311;&#2306;&#2342;&#2367;&#2352;&#2366; &#2327;&#2366;&#2306;&#2343;&#2368; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366;, &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;&#2404; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2375;:</strong><span style=\"font-weight: 400;\"> &#2352;&#2366;&#2332;&#2368;&#2357; &#2327;&#2366;&#2306;&#2343;&#2368; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2361;&#2376;&#2342;&#2352;&#2366;&#2348;&#2366;&#2342;, &#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366;), &#2358;&#2381;&#2352;&#2368; &#2327;&#2369;&#2352;&#2369; &#2352;&#2366;&#2350; &#2342;&#2366;&#2360; &#2332;&#2368; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2309;&#2350;&#2371;&#2340;&#2360;&#2352;, &#2346;&#2306;&#2332;&#2366;&#2348;), &#2354;&#2379;&#2325;&#2346;&#2381;&#2352;&#2367;&#2351; &#2327;&#2379;&#2346;&#2368;&#2344;&#2366;&#2341; &#2348;&#2366;&#2352;&#2342;&#2354;&#2379;&#2312; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2327;&#2369;&#2357;&#2366;&#2361;&#2366;&#2335;&#2368;, &#2309;&#2360;&#2350;), &#2348;&#2368;&#2332;&#2370; &#2346;&#2335;&#2344;&#2366;&#2351;&#2325; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2349;&#2369;&#2357;&#2344;&#2375;&#2358;&#2381;&#2357;&#2352;, &#2323;&#2337;&#2367;&#2358;&#2366;), &#2327;&#2351;&#2366; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2327;&#2351;&#2366;, &#2348;&#2367;&#2361;&#2366;&#2352;), &#2357;&#2368;&#2352; &#2360;&#2366;&#2357;&#2352;&#2325;&#2352; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2346;&#2379;&#2352;&#2381;&#2335; &#2348;&#2381;&#2354;&#2375;&#2351;&#2352;, &#2309;&#2306;&#2337;&#2350;&#2366;&#2344; &#2324;&#2352; &#2344;&#2367;&#2325;&#2379;&#2348;&#2366;&#2352; &#2342;&#2381;&#2357;&#2368;&#2346; &#2360;&#2350;&#2370;&#2361;), &#2360;&#2352;&#2342;&#2366;&#2352; &#2357;&#2354;&#2381;&#2354;&#2349;&#2349;&#2366;&#2312; &#2346;&#2335;&#2375;&#2354; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2309;&#2361;&#2350;&#2342;&#2366;&#2348;&#2366;&#2342;, &#2327;&#2369;&#2332;&#2352;&#2366;&#2340;), &#2325;&#2375;&#2350;&#2381;&#2346;&#2375;&#2327;&#2380;&#2337;&#2364;&#2366; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2348;&#2375;&#2306;&#2327;&#2354;&#2369;&#2352;&#2369;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;), &#2350;&#2376;&#2306;&#2327;&#2354;&#2379;&#2352; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2350;&#2376;&#2306;&#2327;&#2354;&#2379;&#2352;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;), &#2325;&#2379;&#2330;&#2368;&#2344; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2366; (&#2325;&#2379;&#2330;&#2381;&#2330;&#2367;, &#2325;&#2375;&#2352;&#2354;) &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\"> Which of the following statements regarding the deccan plateau is NOT correct?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2325;&#2381;&#2325;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>The Deccan Plateau is higher in the west and slopes gently eastwards.</p>\n", "<p>The Deccan Plateau is a triangular landmass.</p>\n", 
                                "<p>An extension of the Plateau is al<span style=\"font-family: Cambria Math;\">so visible in the northeast.</span></p>\n", "<p>The Satpura range flanks its broad base in the west.</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2342;&#2325;&#2381;&#2325;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2305;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2338;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2342;&#2325;&#2381;&#2325;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2370;&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2380;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2340;&#2346;&#2369;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\n"],
                    solution_en: "<p>15.(b) <strong>The Deccan Plateau :</strong><span style=\"font-weight: 400;\"> Roughly </span><strong>triangular </strong><span style=\"font-weight: 400;\">in shape with its base coinciding with the southern edge of the great plain of North India. Apex of the triangular plateau is at Kanyakumari. It covers a total area of about </span><strong>16 lakh sq km.</strong><span style=\"font-weight: 400;\"> The average height of the plateau is </span><strong>600-900 m above sea level. </strong><span style=\"font-weight: 400;\">It is separated by a fault (A fracture in the rock along which rocks have been relatively replaced), from Chota Nagpur plateau. The black soil area in the Deccan plateau is known as Deccan trap. It is formed due to volcanic eruptions. This soil is good for cotton &amp; sugarcane cultivation. The Deccan plateau is broadly divided into: The Western Ghats and The Eastern Ghats. </span></p>\n",
                    solution_hi: "<p>15.(b)<span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2342;&#2325;&#2381;&#2325;&#2344; &#2325;&#2366; &#2346;&#2336;&#2366;&#2352; : </strong><span style=\"font-weight: 400;\">&#2354;&#2327;&#2349;&#2327; </span><strong>&#2340;&#2381;&#2352;&#2367;&#2325;&#2379;&#2339;&#2368;&#2351;</strong><span style=\"font-weight: 400;\"> &#2310;&#2325;&#2366;&#2352; &#2350;&#2375;&#2306; &#2332;&#2367;&#2360;&#2325;&#2366; &#2310;&#2343;&#2366;&#2352; &#2313;&#2340;&#2381;&#2340;&#2352; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2357;&#2367;&#2358;&#2366;&#2354; &#2350;&#2376;&#2342;&#2366;&#2344; &#2325;&#2375; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339;&#2368; &#2325;&#2367;&#2344;&#2366;&#2352;&#2375; &#2325;&#2375; &#2360;&#2366;&#2341; &#2350;&#2375;&#2354; &#2326;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2340;&#2381;&#2352;&#2367;&#2325;&#2379;&#2339;&#2368;&#2351; &#2346;&#2336;&#2366;&#2352; &#2325;&#2366; &#2358;&#2368;&#2352;&#2381;&#2359; &#2325;&#2344;&#2381;&#2351;&#2366;&#2325;&#2369;&#2350;&#2366;&#2352;&#2368; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404; &#2311;&#2360;&#2325;&#2366; &#2325;&#2369;&#2354; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2354;&#2327;&#2349;&#2327; </span><strong>16 &#2354;&#2366;&#2326; &#2357;&#2352;&#2381;&#2327; &#2325;&#2367;&#2354;&#2379;&#2350;&#2368;&#2335;&#2352; </strong><span style=\"font-weight: 400;\">&#2361;&#2376;&#2404; &#2346;&#2336;&#2366;&#2352; &#2325;&#2368; &#2324;&#2360;&#2340; &#2314;&#2305;&#2330;&#2366;&#2312; </span><strong>&#2360;&#2350;&#2369;&#2342;&#2381;&#2352; &#2340;&#2354; &#2360;&#2375; 600-900 &#2350;&#2368;&#2335;&#2352;</strong><span style=\"font-weight: 400;\"> &#2361;&#2376;&#2404; &#2351;&#2361; &#2331;&#2379;&#2335;&#2366; &#2344;&#2366;&#2327;&#2346;&#2369;&#2352; &#2346;&#2336;&#2366;&#2352; &#2360;&#2375; &#2319;&#2325; &#2349;&#2381;&#2352;&#2306;&#2358; (&#2330;&#2335;&#2381;&#2335;&#2366;&#2344; &#2350;&#2375;&#2306; &#2319;&#2325;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&#2342;&#2352;&#2366;&#2352;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&#2332;&#2367;&#2360;&#2325;&#2375; &#2360;&#2366;&#2341; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344;&#2379;&#2306; &#2325;&#2379; &#2309;&#2346;&#2375;&#2325;&#2381;&#2359;&#2366;&#2325;&#2371;&#2340; &#2348;&#2342;&#2354; &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;) &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2309;&#2354;&#2327; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2342;&#2325;&#2381;&#2325;&#2344; &#2346;&#2336;&#2366;&#2352; &#2350;&#2375;&#2306; &#2325;&#2366;&#2354;&#2368; &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2379; &#2342;&#2325;&#2381;&#2325;&#2344; &#2335;&#2381;&#2352;&#2375;&#2346; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2332;&#2381;&#2357;&#2366;&#2354;&#2366;&#2350;&#2369;&#2326;&#2368; &#2357;&#2367;&#2360;&#2381;&#2347;&#2379;&#2335; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; </span><span style=\"font-weight: 400;\">&#2351;&#2361; &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2325;&#2346;&#2366;&#2360; &#2324;&#2352; &#2327;&#2344;&#2381;&#2344;&#2375; &#2325;&#2368; &#2326;&#2375;&#2340;&#2368; &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2330;&#2381;&#2331;&#2368; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; &#2342;&#2325;&#2381;&#2325;&#2344; &#2325;&#2375; &#2346;&#2336;&#2366;&#2352; &#2325;&#2379; &#2350;&#2369;&#2326;&#2381;&#2351;&#2340;&#2307; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2328;&#2366;&#2335; &#2324;&#2352; &#2346;&#2370;&#2352;&#2381;&#2357;&#2368; &#2328;&#2366;&#2335; &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> Which of the following pair of \"player-sports\" is correct?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. Lovlina Borgohain - Boxing</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. Nikhat Zareen - Wrestling</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> \"</span><span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\">&#2326;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\">\" </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. </span><span style=\"font-family: Cambria Math;\">&#2354;&#2357;&#2354;&#2368;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2379;&#2352;&#2327;&#2379;&#2361;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2325;&#2381;&#2325;&#2375;&#2348;&#2366;&#2332;&#2368;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2405;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2326;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2364;&#2352;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2358;&#2381;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Neither I nor II</p>\n", "<p>Bo<span style=\"font-family: Cambria Math;\">th I and II</span></p>\n", 
                                "<p>Only II</p>\n", "<p>Only I</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#2404; </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2405;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2405;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2405;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n"],
                    solution_en: "<p>16. (d)<strong> Only I. Eminent Indian Players of Boxing : <span style=\"font-weight: 400;\">Mohammed Ali Qamar, Shiva Thapa, Dingko Singh, Vikas Krishan Yadav, Jitendra Kumar, Akhil Kumar , Hawa Singh, Devendro Singh, Laishram Sarita Devi, Vijender Singh, Mary Kom. </span>Eminent Indian Players of Wrestling : <span style=\"font-weight: 400;\">Sushil Kumar, Yogeshwar Dutt, Bajrang Punia, Deepak Punia, Sakshi Malik, Geeta Phogat, Babita Phogat, Vinesh Phogat.</span></strong></p>\n",
                    solution_hi: "<p>16. (d)&nbsp;<strong>&#2325;&#2375;&#2357;&#2354; I</strong><span style=\"font-weight: 400;\">&nbsp; &#2404;</span><strong>&nbsp; &#2350;&#2369;&#2325;&#2381;&#2325;&#2375;&#2348;&#2366;&#2332;&#2368; &#2325;&#2375; &#2346;&#2381;&#2352;&#2326;&#2381;&#2351;&#2366;&#2340; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368; :</strong><span style=\"font-weight: 400;\"> &#2350;&#2379;&#2361;&#2350;&#2381;&#2350;&#2342; &#2309;&#2354;&#2368; &#2325;&#2350;&#2352;, &#2358;&#2367;&#2357; &#2341;&#2366;&#2346;&#2366;, &#2337;&#2367;&#2306;&#2327;&#2381;&#2325;&#2379; &#2360;&#2367;&#2306;&#2361;, &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2371;&#2359;&#2381;&#2339; &#2351;&#2366;&#2342;&#2357;, &#2332;&#2367;&#2340;&#2375;&#2306;&#2342;&#2381;&#2352; &#2325;&#2369;&#2350;&#2366;&#2352;, &#2309;&#2326;&#2367;&#2354; &#2325;&#2369;&#2350;&#2366;&#2352;, &#2361;&#2357;&#2366; &#2360;&#2367;&#2306;&#2361;, &#2342;&#2375;&#2357;&#2375;&#2306;&#2342;&#2381;&#2352;&#2379; &#2360;&#2367;&#2306;&#2361;, &#2354;&#2376;&#2358;&#2352;&#2366;&#2350; &#2360;&#2352;&#2367;&#2340;&#2366; &#2342;&#2375;&#2357;&#2368;, &#2357;&#2367;&#2332;&#2375;&#2306;&#2342;&#2352; &#2360;&#2367;&#2306;&#2361;, &#2350;&#2376;&#2352;&#2368; &#2325;&#2377;&#2350;&#2404;&nbsp; </span><strong>&#2325;&#2369;&#2358;&#2381;&#2340;&#2368; &#2325;&#2375; &#2346;&#2381;&#2352;&#2326;&#2381;&#2351;&#2366;&#2340; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368; : </strong><span style=\"font-weight: 400;\">&#2360;&#2369;&#2358;&#2368;&#2354; &#2325;&#2369;&#2350;&#2366;&#2352;, &#2351;&#2379;&#2327;&#2375;&#2358;&#2381;&#2357;&#2352; &#2342;&#2340;&#2381;&#2340;, &#2348;&#2332;&#2352;&#2306;&#2327; &#2346;&#2369;&#2344;&#2367;&#2351;&#2366;, &#2342;&#2368;&#2346;&#2325; &#2346;&#2369;&#2344;&#2367;&#2351;&#2366;, &#2360;&#2366;&#2325;&#2381;&#2359;&#2368; &#2350;&#2354;&#2367;&#2325;, &#2327;&#2368;&#2340;&#2366; &#2347;&#2379;&#2327;&#2366;&#2335;, &#2348;&#2348;&#2368;&#2340;&#2366; &#2347;&#2379;&#2327;&#2366;&#2335;, &#2357;&#2367;&#2344;&#2375;&#2358; &#2347;&#2379;&#2327;&#2366;&#2335;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">Which country\'s moon </span><span style=\"font-family: Cambria Math;\">mission has mapped out the global distribution of sodium on the Moon\'s surface in October 2022 ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">&#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2306;&#2342;&#2381;&#2352;&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2340;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2379;&#2337;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2340;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2344;&#2330;&#2367;&#2340;&#2381;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>United States of America</p>\n", "<p>Russia</p>\n", 
                                "<p>India</p>\n", "<p>China</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2360;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2344;</span></p>\n"],
                    solution_en: "<p>17.<span style=\"font-family: Cambria Math;\">(c) </span><strong>India. ISRO&rsquo;s </strong><span style=\"font-weight: 400;\">(Indian Space Research Organisation)</span><strong> missions:</strong><span style=\"font-weight: 400;\"> Chandrayaan-1 (22 October 2008 - 28 August 2009),&nbsp; Chandrayaan-2 (22 July 2019), Mars Orbiter Mission (5 November 2013 - 2 October 2022), ASTROSAT (28 September 2015 - September 2022). </span><strong>Other planned mission</strong><span style=\"font-weight: 400;\">s: Aditya-L1 (Solar observation), Chandrayaan-3 (Lunar lander, rover), Shukrayaan-1 (Venus orbiter), NISAR (SAR satellite), Mangalyaan 2 (Mars orbiter),&nbsp; Gaganyaan 3 (Crewed spacecraft), etc.&nbsp;</span></p>\n",
                    solution_hi: "<p>17.(c)&nbsp;<strong>&#2349;&#2366;&#2352;&#2340;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>ISRO </strong><span style=\"font-weight: 400;\">(&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2309;&#2306;&#2340;&#2352;&#2367;&#2325;&#2381;&#2359; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2360;&#2306;&#2327;&#2336;&#2344;) </span><strong>&#2350;&#2367;&#2358;&#2344;</strong><span style=\"font-weight: 400;\">: &#2330;&#2306;&#2342;&#2381;&#2352;&#2351;&#2366;&#2344; -1 (22 &#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352; 2008 - 28 &#2309;&#2327;&#2360;&#2381;&#2340; 2009), &#2330;&#2306;&#2342;&#2381;&#2352;&#2351;&#2366;&#2344; -2 (22 &#2332;&#2369;&#2354;&#2366;&#2312; 2019), &#2350;&#2366;&#2352;&#2381;&#2360; &#2321;&#2352;&#2381;&#2348;&#2367;&#2335;&#2352; &#2350;&#2367;&#2358;&#2344; (5 &#2344;&#2357;&#2306;&#2348;&#2352; 2013 - 2 &#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352; 2022), &#2319;&#2360;&#2381;&#2335;&#2381;&#2352;&#2379;&#2360;&#2376;&#2335; (28 &#2360;&#2367;&#2340;&#2306;&#2348;&#2352; 2015 - &#2360;&#2367;&#2340;&#2306;&#2348;&#2352; 2022) &#2404; </span><strong>&#2309;&#2344;&#2381;&#2351; &#2344;&#2367;&#2351;&#2379;&#2332;&#2367;&#2340; &#2350;&#2367;&#2358;&#2344;: </strong><span style=\"font-weight: 400;\">&#2310;&#2342;&#2367;&#2340;&#2381;&#2351;-L1 (&#2360;&#2380;&#2352; &#2309;&#2357;&#2354;&#2379;&#2325;&#2344;), &#2330;&#2306;&#2342;&#2381;&#2352;&#2351;&#2366;&#2344;-3 (&#2330;&#2306;&#2342;&#2381;&#2352; &#2354;&#2376;&#2306;&#2337;&#2352;, &#2352;&#2379;&#2357;&#2352;), &#2358;&#2369;&#2325;&#2381;&#2352;&#2351;&#2366;&#2344;-1 (&#2357;&#2368;&#2344;&#2360; &#2321;&#2352;&#2381;&#2348;&#2367;&#2335;&#2352;), &#2344;&#2367;&#2360;&#2366;&#2352; (SAR&nbsp; &#2313;&#2346;&#2327;&#2381;&#2352;&#2361;), &#2350;&#2306;&#2327;&#2354;&#2351;&#2366;&#2344; 2 (&#2350;&#2306;&#2327;&#2354; &#2321;&#2352;&#2381;&#2348;&#2367;&#2335;&#2352;), &#2327;&#2327;&#2344;&#2351;&#2366;&#2344; 3 (&#2325;&#2381;&#2352;&#2370; &#2309;&#2306;&#2340;&#2352;&#2367;&#2325;&#2381;&#2359; &#2351;&#2366;&#2344;), &#2311;&#2340;&#2381;&#2351;&#2366;&#2342;&#2367; &#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">18. </span><span style=\"font-family: Cambria Math;\">In which part of the Indian constitution are the provisions related to panchayat mentioned?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">18. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2306;&#2330;&#2366;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2348;&#2306;&#2343;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2354;&#2381;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Part XI</p>\n", "<p>Part IX</p>\n", 
                                "<p>Part IX A</p>\n", "<p>Part X</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> XI</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> IX</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2349;</span><span style=\"font-family: Cambria Math;\">&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> IX </span><span style=\"font-family: Cambria Math;\">&#2325;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> X</span></p>\n"],
                    solution_en: "<p>18.(b)&nbsp;<strong>Part IX </strong><span style=\"font-weight: 400;\">(Art. 243 - 243O)</span><strong>. Important Parts, Subject and Articles : Part I: </strong><span style=\"font-weight: 400;\">The Union and its Territories (Art. 1- 4),</span><strong> Part II: </strong><span style=\"font-weight: 400;\">Citizenship (Art. 5 - 11),</span><strong> Part III: </strong><span style=\"font-weight: 400;\">Fundamental Rights (Art. 12 - 35), </span><strong>&nbsp;Part IV: </strong><span style=\"font-weight: 400;\">Directive Principles of State Policy(Art. 36 - 51), </span><strong>Part IV(A): </strong><span style=\"font-weight: 400;\">Fundamental Duties (Art. 51A), </span><strong>Part IX(A):</strong><span style=\"font-weight: 400;\"> The Municipalities (Art. 243P - 243ZG), </span><strong>Part IXB:</strong><span style=\"font-weight: 400;\"> The Co-operative Societies (Art. 243ZH - 243ZT), </span><strong>Part XI:</strong><span style=\"font-weight: 400;\"> Relation between the Union and the States (Art. 245 - 263),</span><strong> Part XV: </strong><span style=\"font-weight: 400;\">Elections (Art. 324 - 329A), </span><strong>Part XVII: </strong><span style=\"font-weight: 400;\">Official language (Art. 343 - 351),</span><strong> Part XVIII: </strong><span style=\"font-weight: 400;\">Emergency Provisions (Art. 352 - 360), </span><strong>Part XX: </strong><span style=\"font-weight: 400;\">Amendment of the Constitution (Art. 368).</span></p>\n",
                    solution_hi: "<p>18.(b)<strong>&nbsp;</strong><strong>&#2349;&#2366;&#2327; IX </strong><span style=\"font-weight: 400;\">(&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 243 - 243O)&#2404; </span><strong>&#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2349;&#2366;&#2327;, &#2357;&#2367;&#2359;&#2351; &#2324;&#2352; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; : &#2349;&#2366;&#2327; I - </strong><span style=\"font-weight: 400;\">&#2360;&#2306;&#2328; &#2324;&#2352; &#2313;&#2360;&#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 1 - 4),&nbsp; </span><strong>&#2349;&#2366;&#2327; II - </strong><span style=\"font-weight: 400;\">&#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 5 -11),&nbsp; </span><strong>&#2349;&#2366;&#2327; III -</strong><span style=\"font-weight: 400;\"> &#2350;&#2380;&#2354;&#2367;&#2325; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 12 - 35), </span><strong>&#2349;&#2366;&#2327; IV - </strong><span style=\"font-weight: 400;\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2375; &#2344;&#2368;&#2340;&#2367; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2325; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 36 - 51), </span><strong>&#2349;&#2366;&#2327; IV A - </strong><span style=\"font-weight: 400;\">&#2350;&#2380;&#2354;&#2367;&#2325; &#2325;&#2352;&#2381;&#2340;&#2357;&#2381;&#2351; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 51A), </span><strong>&#2349;&#2366;&#2327; IXA - </strong><span style=\"font-weight: 400;\">&#2344;&#2327;&#2352; &#2346;&#2366;&#2354;&#2367;&#2325;&#2366; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 243P - 243ZG), </span><strong>&#2349;&#2366;&#2327; IXB -</strong><span style=\"font-weight: 400;\"> &#2360;&#2361;&#2325;&#2366;&#2352;&#2368; &#2360;&#2350;&#2367;&#2340;&#2367;&#2351;&#2366;&#2305; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 243ZH - 243ZT), </span><strong>&#2349;&#2366;&#2327; XI - </strong><span style=\"font-weight: 400;\">&#2360;&#2306;&#2328; &#2324;&#2352; &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2306;&#2348;&#2306;&#2343; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 245 - 263), </span><strong>&#2349;&#2366;&#2327; XV - </strong><span style=\"font-weight: 400;\">&#2330;&#2369;&#2344;&#2366;&#2357; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 324 - 329A), </span><strong>&#2349;&#2366;&#2327; XVII - </strong><span style=\"font-weight: 400;\">&#2310;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2325; &#2349;&#2366;&#2359;&#2366; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 343 - 351), </span><strong>&#2349;&#2366;&#2327; XVIII - </strong><span style=\"font-weight: 400;\">&#2310;&#2346;&#2366;&#2340;&#2325;&#2366;&#2354;&#2368;&#2344; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 352 - 360), </span><strong>&#2349;&#2366;&#2327; XX - </strong><span style=\"font-weight: 400;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 368)&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Cambria Math;\">Who was the first Indian woman to win an Olympic medal?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Cambria Math;\">&#2323;&#2354;&#2306;&#2346;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Neelamshetty Appanna</p>\n", "<p>Karnam Malleswari</p>\n", 
                                "<p>Saina Nehwal</p>\n", "<p>Sakshi Malik</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2354;&#2350;&#2358;&#2375;&#2335;&#2381;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2381;&#2346;&#2344;&#2381;&#2344;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2381;&#2339;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2381;&#2354;&#2375;&#2358;&#2381;&#2357;&#2352;&#2368;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2311;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2361;&#2357;&#2366;&#2354;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2325;&#2381;&#2359;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2367;&#2325;</span></p>\n"],
                    solution_en: "<p>19.<span style=\"font-family: Cambria Math;\">(b) </span><strong>Karnam Malleswari : </strong><span style=\"font-weight: 400;\">She</span><strong> </strong><span style=\"font-weight: 400;\">is a retired Indian weightlifter from Andhra Pradesh. Awards: Rajiv Gandhi Khel Ratna (1999), Arjuna Award (1994), and Padma Shri (1999). Started her career in heavy weightlifting at age 12 under the guidance of coach </span><strong>Neelamshetty Appanna. Saina Nehwal :</strong><span style=\"font-weight: 400;\"> The Haryana shuttler, is the first-ever badminton player from India to clinch an Olympic medal (bronze medal at the London 2012 Games). </span><strong>Sakshi Malik :</strong><span style=\"font-weight: 400;\"> Olympic bronze medallist (Rio 2016 Olympics). She belongs to Rohtak district of Haryana.</span></p>\n",
                    solution_hi: "<p>19.(b)&nbsp;<strong>&#2325;&#2352;&#2381;&#2339;&#2350; &#2350;&#2354;&#2381;&#2354;&#2375;&#2358;&#2381;&#2357;&#2352;&#2368; : </strong><span style=\"font-weight: 400;\">&#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2325;&#2368; &#2319;&#2325; &#2360;&#2375;&#2357;&#2366;&#2344;&#2367;&#2357;&#2371;&#2340;&#2381;&#2340; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2349;&#2366;&#2352;&#2379;&#2340;&#2381;&#2340;&#2379;&#2354;&#2325;&#2404;&nbsp; </span><strong>&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</strong><span style=\"font-weight: 400;\">: &#2352;&#2366;&#2332;&#2368;&#2357; &#2327;&#2366;&#2306;&#2343;&#2368; &#2326;&#2375;&#2354; &#2352;&#2340;&#2381;&#2344; (1999), &#2309;&#2352;&#2381;&#2332;&#2369;&#2344; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; (1994), &#2324;&#2352; &#2346;&#2342;&#2381;&#2350; &#2358;&#2381;&#2352;&#2368; (1999)&#2404; &#2311;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2309;&#2346;&#2344;&#2368; &#2325;&#2373;&#2352;&#2367;&#2351;&#2352; &#2325;&#2368; &#2358;&#2369;&#2352;&#2369;&#2310;&#2340;&nbsp; 12 &#2357;&#2352;&#2381;&#2359; &#2325;&#2368; &#2310;&#2351;&#2369;&nbsp; &#2350;&#2375;&#2306; &#2325;&#2379;&#2330; </span><strong>&#2344;&#2368;&#2354;&#2350;&#2358;&#2375;&#2335;&#2381;&#2335;&#2368; &#2309;&#2346;&#2381;&#2346;&#2344;&#2381;&#2344;&#2366;</strong><span style=\"font-weight: 400;\"> &#2325;&#2375; &#2350;&#2366;&#2352;&#2381;&#2327;&#2342;&#2352;&#2381;&#2358;&#2344; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2368; &#2349;&#2366;&#2352;&#2379;&#2340;&#2381;&#2340;&#2379;&#2354;&#2344; &#2350;&#2375;&#2306; &#2325;&#2368;&#2404; </span><strong>&#2360;&#2366;&#2311;&#2344;&#2366; &#2344;&#2375;&#2361;&#2357;&#2366;&#2354; : </strong><span style=\"font-weight: 400;\">&#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366; &#2325;&#2368; &#2358;&#2335;&#2354;&#2352;, &#2323;&#2354;&#2306;&#2346;&#2367;&#2325; &#2346;&#2342;&#2325; (&#2354;&#2306;&#2342;&#2344; 2012 &#2326;&#2375;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2325;&#2366;&#2306;&#2360;&#2381;&#2351; &#2346;&#2342;&#2325;) &#2332;&#2368;&#2340;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2360;&#2366;&#2325;&#2381;&#2359;&#2368; &#2350;&#2354;&#2367;&#2325;: </strong><span style=\"font-weight: 400;\">&#2323;&#2354;&#2306;&#2346;&#2367;&#2325; &#2325;&#2366;&#2306;&#2360;&#2381;&#2351; &#2346;&#2342;&#2325; &#2357;&#2367;&#2332;&#2375;&#2340;&#2366; (&#2352;&#2367;&#2351;&#2379; 2016 &#2323;&#2354;&#2306;&#2346;&#2367;&#2325;)&#2404; &#2351;&#2361; &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366; &#2325;&#2375; &#2352;&#2379;&#2361;&#2340;&#2325; &#2332;&#2367;&#2354;&#2375; &#2325;&#2368; &#2352;&#2361;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\">The Youn</span><span style=\"font-family: Cambria Math;\">g Bengal Movement was founded by _____ .</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">20.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2306;&#2327;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2306;&#2342;&#2379;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">_________</span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2404;</span></p>\n",
                    options_en: ["<p>Henry Louis Vivian Derozio</p>\n", "<p>Dayanand Saraswati</p>\n", 
                                "<p>Lala Hansraj</p>\n", "<p>Keshab Chandra Sen</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2361;&#2375;&#2344;&#2352;&#2368; &#2354;&#2369;&#2312; &#2357;&#2367;&#2357;&#2367;&#2351;&#2344; &#2337;&#2375;&#2352;&#2379;&#2332;&#2367;&#2351;&#2379; (Henry Louis Vivian Derozio)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2342;&#2351;&#2366;&#2344;&#2344;&#2381;&#2342; &#2360;&#2352;&#2360;&#2381;&#2357;&#2340;&#2368; (Dayanand Saraswati)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2354;&#2366; &#2361;&#2306;&#2360;&#2352;&#2366;&#2332; (Lala Hansraj)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2375;&#2358;&#2357; &#2330;&#2344;&#2381;&#2342;&#2381;&#2352; &#2360;&#2375;&#2344; (Keshab Chandra Sen)</span></p>\n"],
                    solution_en: "<p>20.(a)&nbsp; <strong>The Young Bengal Movement :</strong><span style=\"font-weight: 400;\"> Started by </span><strong>Henry Louis Vivian Derozio</strong><span style=\"font-weight: 400;\">, who had come to Calcutta in </span><strong>1826 </strong><span style=\"font-weight: 400;\">and was appointed in the </span><strong>Hindu College </strong><span style=\"font-weight: 400;\">as a teacher of </span><strong>English literature and History</strong><span style=\"font-weight: 400;\">. It was a group of Bengali free thinkers inspired and excited by the spirit of free thought and revolt against the existing social and religious structure of Hindu society. </span><strong>Dayanand Saraswati </strong><span style=\"font-weight: 400;\">(7 April, 1875): founded the Arya Samaj at Bombay. </span><strong>Lala Hansraj</strong><span style=\"font-weight: 400;\"> (Mahatma Hansraj): He was an educationist born in 1864 in a small town in Bajwara, in the Hoshiarpur district in Punjab. He was immensely influenced by the ideologies of Swami Dayanand Saraswati. </span><strong>Keshav Chandra Sen (1838-1884) : </strong><span style=\"font-weight: 400;\">&nbsp;founded a new \'universal\' religion - Naba Bidhan - in 1880 (New Dispensation).</span></p>\n",
                    solution_hi: "<p>20.(d)<span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2351;&#2306;&#2327; &#2348;&#2306;&#2327;&#2366;&#2354; &#2310;&#2306;&#2342;&#2379;&#2354;&#2344; : &#2361;&#2375;&#2344;&#2352;&#2368; &#2354;&#2369;&#2311;&#2360; &#2357;&#2367;&#2357;&#2367;&#2351;&#2344; &#2337;&#2375;&#2352;&#2379;&#2332;&#2367;&#2351;&#2379; </strong><span style=\"font-weight: 400;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2358;&#2369;&#2352;&#2370; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; ,&#2332;&#2379; 1826 &#2350;&#2375;&#2306; &#2325;&#2354;&#2325;&#2340;&#2381;&#2340;&#2366; &#2310;&#2319; &#2341;&#2375; &#2324;&#2352; </span><strong>&#2361;&#2367;&#2306;&#2342;&#2370; &#2325;&#2377;&#2354;&#2375;&#2332;</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; </span><strong>&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2360;&#2366;&#2361;&#2367;&#2340;&#2381;&#2351; &#2324;&#2352; &#2311;&#2340;&#2367;&#2361;&#2366;&#2360;</strong><span style=\"font-weight: 400;\"> &#2325;&#2375; &#2358;&#2367;&#2325;&#2381;&#2359;&#2325; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340; &#2361;&#2369;&#2319; &#2341;&#2375;&#2404; &#2351;&#2361; &#2348;&#2306;&#2327;&#2366;&#2354;&#2368; &#2350;&#2369;&#2325;&#2381;&#2340; &#2357;&#2367;&#2330;&#2366;&#2352;&#2325;&#2379;&#2306; &#2325;&#2366; &#2319;&#2325; &#2360;&#2350;&#2370;&#2361; &#2341;&#2366; &#2332;&#2379; &#2361;&#2367;&#2306;&#2342;&#2370; &#2360;&#2350;&#2366;&#2332; &#2325;&#2368; &#2350;&#2380;&#2332;&#2370;&#2342;&#2366; &#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325; &#2324;&#2352; &#2343;&#2366;&#2352;&#2381;&#2350;&#2367;&#2325; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2325;&#2375; &#2326;&#2367;&#2354;&#2366;&#2347; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352; &#2357;&#2367;&#2330;&#2366;&#2352; &#2324;&#2352; &#2357;&#2367;&#2342;&#2381;&#2352;&#2379;&#2361; &#2325;&#2368; &#2349;&#2366;&#2357;&#2344;&#2366; &#2360;&#2375; &#2346;&#2381;&#2352;&#2375;&#2352;&#2367;&#2340; &#2324;&#2352; &#2313;&#2340;&#2381;&#2360;&#2366;&#2361;&#2367;&#2340; &#2341;&#2366;&#2404; </span><strong>&#2342;&#2351;&#2366;&#2344;&#2306;&#2342; &#2360;&#2352;&#2360;&#2381;&#2357;&#2340;&#2368; (7 &#2309;&#2346;&#2381;&#2352;&#2376;&#2354;, 1875): </strong><span style=\"font-weight: 400;\">&#2348;&#2350;&#2381;&#2348;&#2312; &#2350;&#2375;&#2306; &#2310;&#2352;&#2381;&#2351; &#2360;&#2350;&#2366;&#2332; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2325;&#2368;&#2404; </span><strong>&#2354;&#2366;&#2354;&#2366; &#2361;&#2306;&#2360;&#2352;&#2366;&#2332; (&#2350;&#2361;&#2366;&#2340;&#2381;&#2350;&#2366; &#2361;&#2306;&#2360;&#2352;&#2366;&#2332;): </strong><span style=\"font-weight: 400;\">&#2311;&#2344;&#2325;&#2366; &#2332;&#2344;&#2381;&#2350; 1864 &#2350;&#2375;&#2306; &#2346;&#2306;&#2332;&#2366;&#2348; &#2325;&#2375; &#2361;&#2379;&#2358;&#2367;&#2351;&#2366;&#2352;&#2346;&#2369;&#2352; &#2332;&#2367;&#2354;&#2375; &#2325;&#2375; &#2319;&#2325; &#2331;&#2379;&#2335;&#2375; &#2360;&#2375; &#2325;&#2360;&#2381;&#2348;&#2375; &#2348;&#2332;&#2357;&#2366;&#2352;&#2366; &#2350;&#2375;&#2306; &#2361;&#2369;&#2310; &#2341;&#2366;&#2404; &#2351;&#2375; &#2360;&#2381;&#2357;&#2366;&#2350;&#2368;</span><strong> </strong><span style=\"font-weight: 400;\">&#2342;&#2351;&#2366;&#2344;&#2344;&#2381;&#2342; &#2360;&#2352;&#2360;&#2381;&#2357;&#2340;&#2368;</span><strong> </strong><span style=\"font-weight: 400;\">&#2325;&#2375; &#2357;&#2367;&#2330;&#2366;&#2352;&#2379;&#2306; &#2360;&#2375; &#2309;&#2340;&#2381;&#2351;&#2343;&#2367;&#2325; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2367;&#2340; &#2341;&#2375;&#2404; </span><strong>&#2325;&#2375;&#2358;&#2357; &#2330;&#2306;&#2342;&#2381;&#2352; &#2360;&#2375;&#2344; (1838-1884) </strong><span style=\"font-weight: 400;\">: 1880 (&#2344;&#2312; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;) &#2350;&#2375;&#2306; &#2319;&#2325; &#2344;&#2319; \'&#2360;&#2366;&#2352;&#2381;&#2357;&#2349;&#2380;&#2350;&#2367;&#2325;\' &#2343;&#2352;&#2381;&#2350; - </span><strong>&#2344;&#2348;&#2366; &#2357;&#2367;&#2343;&#2366;&#2344;</strong><span style=\"font-weight: 400;\"> - &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2325;&#2368;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21.</span><span style=\"font-family: Cambria Math;\"> Who won the women\'s singles title at the US Open 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21. </span><span style=\"font-family: Cambria Math;\">U S </span><span style=\"font-family: Cambria Math;\">&#2323;&#2346;&#2344;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2340;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Iga Swiatek</p>\n", "<p>Serena Williams</p>\n", 
                                "<p>Venus Williams</p>\n", "<p>Ons Jabeur</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2311;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2367;&#2351;&#2366;&#2340;&#2375;&#2325;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2352;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2354;&#2367;&#2351;&#2350;&#2381;&#2360;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2357;&#2368;&#2344;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2354;&#2367;&#2351;&#2350;&#2381;&#2360;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2323;&#2344;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2375;&#2348;&#2381;&#2351;&#2369;&#2352;</span></p>\n"],
                    solution_en: "<p>21.<span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>Iga Swiatek </strong><span style=\"font-weight: 400;\">(Poland) and </span><strong>Men&rsquo;s Singles</strong><span style=\"font-weight: 400;\"> - Carlos Alcaraz (Spain) </span><strong>.Tennis Grand Slam 2022 Winners: Australian Open -</strong><span style=\"font-weight: 400;\"> Men&rsquo;s Singles - Rafael Nadal (Spain), Women&rsquo;s Singles </span><strong>- </strong><span style=\"font-weight: 400;\">Ashleigh Barty (Australia), </span><strong>French Open</strong><span style=\"font-weight: 400;\"> - Men&rsquo;s Singles - Rafael Nadal (Spain), Women&rsquo;s Singles - Iga Swiatek (Poland), </span><strong>Wimbledon Open</strong><span style=\"font-weight: 400;\"> - Men&rsquo;s Singles&nbsp; Novak Djokovic (Serbia), Women&rsquo;s Singles - Elena Andreyevna Rybakina (Russia). </span><strong>Australian Open 2023</strong><span style=\"font-weight: 400;\"> - Men&rsquo;s Singles - Novak Djokovic (Serbia) and Women&rsquo;s Singles - Aryna Sabalenka (Belarus). </span><strong>Venus Williams </strong><span style=\"font-weight: 400;\">and </span><strong>Serena Williams</strong><span style=\"font-weight: 400;\"> are American tennis players. </span><strong>Ons Jabeur </strong><span style=\"font-weight: 400;\">- Tunisian tennis player.</span></p>\n",
                    solution_hi: "<p>21.<span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>&#2311;&#2327;&#2366; &#2360;&#2381;&#2357;&#2367;&#2351;&#2366;&#2340;&#2375;&#2325;</strong><span style=\"font-weight: 400;\"> (&#2346;&#2379;&#2354;&#2376;&#2306;&#2337;) &#2324;&#2352; </span><strong>&#2346;&#2369;&#2352;&#2369;&#2359; &#2319;&#2325;&#2354;</strong><span style=\"font-weight: 400;\"> &#2325;&#2366;&#2352;&#2381;&#2354;&#2379;&#2360; &#2309;&#2354;&#2381;&#2325;&#2352;&#2366;&#2332; (&#2360;&#2381;&#2346;&#2375;&#2344;)&#2404; </span><strong>&#2335;&#2375;&#2344;&#2367;&#2360; &#2327;&#2381;&#2352;&#2376;&#2306;&#2337; &#2360;&#2381;&#2354;&#2376;&#2350; 2022 &#2357;&#2367;&#2332;&#2375;&#2340;&#2366; </strong><span style=\"font-weight: 400;\">- </span><strong>&#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344; &#2323;&#2346;&#2344;</strong><span style=\"font-weight: 400;\"> - &#2346;&#2369;&#2352;&#2369;&#2359; &#2319;&#2325;&#2354; - &#2352;&#2366;&#2347;&#2375;&#2354; &#2344;&#2337;&#2366;&#2354; (&#2360;&#2381;&#2346;&#2375;&#2344;), &#2350;&#2361;&#2367;&#2354;&#2366; &#2319;&#2325;&#2354; - &#2319;&#2358;&#2381;&#2354;&#2375; &#2348;&#2366;&#2352;&#2381;&#2335;&#2368; (&#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2366;), </span><strong>&#2347;&#2381;&#2352;&#2375;&#2306;&#2330; &#2323;&#2346;&#2344;:</strong><span style=\"font-weight: 400;\"> &#2346;&#2369;&#2352;&#2369;&#2359; &#2319;&#2325;&#2354; - &#2352;&#2366;&#2347;&#2375;&#2354; &#2344;&#2337;&#2366;&#2354; (&#2360;&#2381;&#2346;&#2375;&#2344;), &#2350;&#2361;&#2367;&#2354;&#2366; &#2319;&#2325;&#2354; - &#2311;&#2327;&#2366; &#2360;&#2381;&#2357;&#2367;&#2351;&#2366;&#2340;&#2375;&#2325; (&#2346;&#2379;&#2354;&#2376;&#2306;&#2337;), </span><strong>&#2357;&#2367;&#2306;&#2348;&#2354;&#2337;&#2344; &#2323;&#2346;&#2344;</strong><span style=\"font-weight: 400;\"> : &#2346;&#2369;&#2352;&#2369;&#2359; &#2319;&#2325;&#2354;- &#2344;&#2379;&#2357;&#2366;&#2325; &#2332;&#2379;&#2325;&#2379;&#2357;&#2367;&#2330; (&#2360;&#2352;&#2381;&#2348;&#2367;&#2351;&#2366;), &#2350;&#2361;&#2367;&#2354;&#2366; &#2319;&#2325;&#2354; - </span><span style=\"font-weight: 400;\">&#2319;&#2354;&#2375;&#2344;&#2366; &#2310;&#2306;&#2342;&#2381;&#2352;&#2375;&#2351;&#2375;&#2357;&#2344;&#2366; &#2352;&#2366;&#2351;&#2348;&#2366;&#2325;&#2367;&#2344;&#2366; (</span><span style=\"font-weight: 400;\">&#2352;&#2370;&#2360;)&#2404; </span><strong>&#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344; &#2323;&#2346;&#2344; 2023 -</strong><span style=\"font-weight: 400;\"> &#2346;&#2369;&#2352;&#2369;&#2359; &#2319;&#2325;&#2354; - &#2344;&#2379;&#2357;&#2366;&#2325; &#2332;&#2379;&#2325;&#2379;&#2357;&#2367;&#2330; (&#2360;&#2352;&#2381;&#2348;&#2367;&#2351;&#2366;) &#2324;&#2352; &#2350;&#2361;&#2367;&#2354;&#2366; &#2319;&#2325;&#2354; - &#2310;&#2352;&#2381;&#2351;&#2344;&#2366; &#2360;&#2348;&#2354;&#2375;&#2306;&#2325;&#2366; (&#2348;&#2375;&#2354;&#2366;&#2352;&#2370;&#2360;)&#2404; </span><strong>&#2357;&#2368;&#2344;&#2360; &#2357;&#2367;&#2354;&#2367;&#2351;&#2350;&#2381;&#2360; </strong><span style=\"font-weight: 400;\">&#2324;&#2352;</span><strong> &#2360;&#2375;&#2352;&#2375;&#2344;&#2366; &#2357;&#2367;&#2354;&#2367;&#2351;&#2350;&#2381;&#2360; </strong><span style=\"font-weight: 400;\">&#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2368; &#2335;&#2375;&#2344;&#2367;&#2360; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368; &#2361;&#2376;&#2306;&#2404;</span><strong> &#2323;&#2344;&#2381;&#2360; &#2332;&#2375;&#2348;&#2381;&#2351;&#2369;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2335;&#2381;&#2351;&#2370;&#2344;&#2368;&#2358;&#2367;&#2351;&#2366;&#2312; &#2335;&#2375;&#2344;&#2367;&#2360; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\">Which of the following is NOT an inner planet of our solar system?</span></p>\n",
                    question_hi: "<p>22<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2380;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2306;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;&#2340;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Mars</p>\n", "<p>Venus</p>\n", 
                                "<p>Mercury</p>\n", "<p>Jupiter</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2306;&#2327;&#2354;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2325;&#2381;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2348;&#2369;&#2343;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2348;&#2371;&#2361;&#2360;&#2381;&#2346;&#2340;&#2367;</span></p>\n"],
                    solution_en: "<p>22.<span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>Jupiter. Inner planets (made up of rocks) -</strong><span style=\"font-weight: 400;\"> Mercury, Venus, Earth, and Mars</span><strong>. The outer planets (made up of gases )- </strong><span style=\"font-weight: 400;\">Jupiter, Saturn, Uranus and Neptune. </span><strong>Dwarf Planets </strong><span style=\"font-weight: 400;\">- Pluto, Ceres, Eris, Haumea and Makemake.</span></p>\n",
                    solution_hi: "<p>22.(d)&nbsp;<strong>&#2348;&#2371;&#2361;&#2360;&#2381;&#2346;&#2340;&#2367;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2327;&#2381;&#2352;&#2361; (&#2330;&#2335;&#2381;&#2335;&#2366;&#2344;&#2379;&#2306; &#2360;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340; ) -</strong><span style=\"font-weight: 400;\"> &#2348;&#2369;&#2343;, &#2358;&#2369;&#2325;&#2381;&#2352;, &#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2324;&#2352; &#2350;&#2306;&#2327;&#2354;&#2404; </span><strong>&#2348;&#2366;&#2361;&#2352;&#2368; &#2327;&#2381;&#2352;&#2361; (&#2327;&#2376;&#2360;&#2379;&#2306; &#2360;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340;) </strong><span style=\"font-weight: 400;\">- &#2348;&#2371;&#2361;&#2360;&#2381;&#2346;&#2340;&#2367;, &#2358;&#2344;&#2367;, &#2309;&#2352;&#2369;&#2339; &#2324;&#2352; &#2357;&#2352;&#2369;&#2339; &#2404; </span><strong>&#2348;&#2380;&#2344;&#2375; &#2327;&#2381;&#2352;&#2361; - </strong><span style=\"font-weight: 400;\">&#2346;&#2381;&#2354;&#2370;&#2335;&#2379;, &#2360;&#2375;&#2352;&#2375;&#2360;, &#2319;&#2352;&#2367;&#2360;, &#2361;&#2381;&#2351;&#2370;&#2350;&#2367;&#2351;&#2366; &#2324;&#2352; &#2350;&#2375;&#2325;&#2350;&#2375;&#2325; &#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23. </span><span style=\"font-family: Cambria Math;\">In the XXI phase of sale of electoral bonds can be only purchase from which of the following institution?</span></p>\n",
                    question_hi: "<p>23<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> XXI </span><span style=\"font-family: Cambria Math;\">&#2330;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2369;&#2344;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2377;&#2339;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>NITI Aayog</p>\n", "<p>RBI</p>\n", 
                                "<p>SEBI</p>\n", "<p>SBI</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2379;&#2327;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2310;&#2352;&#2348;&#2368;&#2310;&#2312;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2348;&#2368;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2360;&#2348;&#2368;&#2310;&#2312;</span></p>\n"],
                    solution_en: "<p>23.<span style=\"font-family: Cambria Math;\">(d) </span><strong>SBI (State Bank of India). Electoral bonds </strong><span style=\"font-weight: 400;\">are money instruments like promissory notes, which can be bought by companies and individuals and donated to a political party, which can then encash these bonds. The Electoral Bonds Scheme was launched in 2018. </span><strong>SBI </strong><span style=\"font-weight: 400;\">is the only authorised bank to issue electoral bonds. </span><strong>Validity</strong><span style=\"font-weight: 400;\">: 15 calendar days from the date of issue. </span><strong>Eligibility: </strong><span style=\"font-weight: 400;\">Only the political parties registered under Section 29A of the Representation of the People Act, 1951 are eligible to Electoral Bonds.</span></p>\n",
                    solution_hi: "<p>23.(d)&nbsp;<strong>SBI (&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2335;&#2375;&#2335; &#2348;&#2376;&#2306;&#2325;)</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2330;&#2369;&#2344;&#2366;&#2357;&#2368; </strong><strong>&#2348;&#2377;&#2344;&#2381;&#2337; </strong><span style=\"font-weight: 400;\">&#2357;&#2330;&#2344;&#2346;&#2340;&#2381;&#2352;&#2381;</span><span style=\"font-weight: 400;\"> &#2332;&#2376;&#2360;&#2375; &#2350;&#2369;&#2342;&#2381;&#2352;&#2366; &#2360;&#2366;&#2343;&#2344; &#2361;&#2376;&#2306;, &#2332;&#2367;&#2344;&#2381;&#2361;&#2375;&#2306; &#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;&#2306; &#2324;&#2352; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2326;&#2352;&#2368;&#2342;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2319;&#2325; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2342;&#2354; &#2325;&#2379; &#2342;&#2366;&#2344; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2379; &#2311;&#2344; &#2348;&#2366;&#2306;&#2337;&#2381;&#2360; &#2325;&#2379; &#2344;&#2325;&#2342; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2404; &#2330;&#2369;&#2344;&#2366;&#2357;&#2368; &#2348;&#2377;&#2344;&#2381;&#2337; &#2360;&#2381;&#2325;&#2368;&#2350; </span><strong>2018 </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306; &#2354;&#2377;&#2344;&#2381;&#2330; &#2325;&#2368; &#2327;&#2312; &#2341;&#2368;&#2404; </span><strong>SBI </strong><span style=\"font-weight: 400;\">&#2330;&#2369;&#2344;&#2366;&#2357;&#2368; </span><span style=\"font-weight: 400;\">&#2348;&#2377;&#2344;&#2381;&#2337; &#2332;&#2366;&#2352;&#2368; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; &#2309;&#2343;&#2367;&#2325;&#2371;&#2340; &#2348;&#2376;&#2306;&#2325; &#2361;&#2376;&#2404; </span><strong>&#2357;&#2376;&#2343;&#2340;&#2366; : </strong><span style=\"font-weight: 400;\">&#2332;&#2366;&#2352;&#2368; &#2361;&#2379;&#2344;&#2375; &#2325;&#2368; &#2340;&#2366;&#2352;&#2368;&#2326; &#2360;&#2375; 15 &#2325;&#2376;&#2354;&#2375;&#2306;&#2337;&#2352; &#2342;&#2367;&#2357;&#2360; &#2340;&#2325;&#2404;</span><strong> &#2346;&#2366;&#2340;&#2381;&#2352;&#2340;&#2366; : </strong><span style=\"font-weight: 400;\">&#2332;&#2344;&#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 1951 &#2325;&#2368; &#2343;&#2366;&#2352;&#2366; 29A &#2325;&#2375; &#2340;&#2361;&#2340; &#2346;&#2306;&#2332;&#2368;&#2325;&#2371;&#2340; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2342;&#2354; &#2361;&#2368; &#2330;&#2369;&#2344;&#2366;&#2357;&#2368; &#2348;&#2366;&#2306;&#2337; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2366;&#2340;&#2381;&#2352; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> According to which of the following laws, the pressure of a mixture of gases is equal to the sum of the partial pressures of the component gases?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2327;&#2376;&#2360;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2358;&#2381;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2351;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2376;&#2360;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2306;&#2358;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2348;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Charles\' Law</p>\n", "<p>Boyle\'s Law</p>\n", 
                                "<p>Dalton\'s Law</p>\n", "<p>Graham\'s Law</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2352;&#2381;&#2354;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2350;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2348;&#2377;&#2351;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2350;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2337;&#2366;&#2354;&#2381;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2350;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2350;</span></p>\n"],
                    solution_en: "<p>24.<span style=\"font-family: Cambria Math;\">(c)<strong>&nbsp;</strong></span><strong>Dalton\'s Law. Charles\' Law:</strong><span style=\"font-weight: 400;\"> The volume of an ideal gas at constant pressure is directly proportional to the absolute temperature.</span><strong> Boyle\'s Law:</strong><span style=\"font-weight: 400;\"> The pressure of a given mass of an ideal gas is inversely proportional to its volume at a constant temperature. </span><strong>Graham\'s Law: </strong><span style=\"font-weight: 400;\">The rate of diffusion of a gas is inversely proportional to the square root of its molecular weight.</span></p>\n",
                    solution_hi: "<p>24.(c)&nbsp;<strong>&#2337;&#2366;&#2354;&#2381;&#2335;&#2344; &#2325;&#2375; &#2344;&#2367;&#2351;&#2350;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2330;&#2366;&#2352;&#2381;&#2354;&#2381;&#2360; &#2325;&#2375; &#2344;&#2367;&#2351;&#2350; - </strong><span style=\"font-weight: 400;\">&#2360;&#2381;&#2341;&#2367;&#2352; &#2342;&#2366;&#2348; &#2346;&#2352; &#2319;&#2325; &#2310;&#2342;&#2352;&#2381;&#2358; &#2327;&#2376;&#2360; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; &#2313;&#2360;&#2325;&#2375; &#2346;&#2352;&#2350; &#2340;&#2366;&#2346; &#2325;&#2375; &#2309;&#2344;&#2369;&#2325;&#2381;&#2352;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;&nbsp; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2348;&#2377;&#2351;&#2354; &#2325;&#2375; &#2344;&#2367;&#2351;&#2350; - </strong><span style=\"font-weight: 400;\">&#2360;&#2381;&#2341;&#2367;&#2352; &#2340;&#2366;&#2346; &#2346;&#2352; &#2325;&#2367;&#2360;&#2368; &#2310;&#2342;&#2352;&#2381;&#2358; &#2327;&#2376;&#2360; &#2325;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; &#2325;&#2366; &#2342;&#2366;&#2348; &#2313;&#2360;&#2325;&#2375; &#2310;&#2351;&#2340;&#2344; &#2325;&#2375; &#2357;&#2381;&#2351;&#2369;&#2340;&#2381;&#2325;&#2381;&#2352;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2327;&#2381;&#2352;&#2366;&#2361;&#2350; &#2325;&#2375; &#2344;&#2367;&#2351;&#2350; -</strong><span style=\"font-weight: 400;\"> &#2325;&#2367;&#2360;&#2368; &#2327;&#2376;&#2360; &#2325;&#2375; &#2357;&#2367;&#2360;&#2352;&#2339; &#2325;&#2368; &#2342;&#2352; &#2313;&#2360;&#2325;&#2375; &#2310;&#2339;&#2381;&#2357;&#2367;&#2325; &#2349;&#2366;&#2352; &#2325;&#2375; &#2357;&#2352;&#2381;&#2327;&#2350;&#2370;&#2354; &#2325;&#2375; &#2357;&#2381;&#2351;&#2369;&#2340;&#2381;&#2325;&#2381;&#2352;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25.</span><span style=\"font-family: Cambria Math;\"> El Nino is a name given to the period</span><span style=\"font-family: Cambria Math;\">ic development of a warm ocean current along the coast of ____ .</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2354;&#2344;&#2368;&#2344;&#2379;</span><span style=\"font-family: Cambria Math;\"> _________</span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2354;&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Columbia</p>\n", "<p>Peru</p>\n", 
                                "<p>Chile</p>\n", "<p>Bolivia</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2354;&#2306;&#2348;&#2367;&#2351;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2352;&#2370;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2354;&#2368;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2348;&#2379;&#2354;&#2368;&#2357;&#2367;&#2351;&#2366;</span></p>\n"],
                    solution_en: "<p>25.<span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>Peru. Peru current</strong><span style=\"font-weight: 400;\"> (Humboldt current - Cold current): Flows from the southernmost tip of Chile to northern Peru, along the west coast of South America.&nbsp; </span><strong>Gulf Stream: </strong><span style=\"font-weight: 400;\">The warm water current that flows across the western North Atlantic Ocean. </span><strong>Florida Current:</strong><span style=\"font-weight: 400;\"> Flows around Florida Peninsula and joins the Gulf Stream at Cape Hatteras. </span><strong>Somali Current: </strong><span style=\"font-weight: 400;\">Analogous to the Gulf Stream in the Atlantic Ocean. </span><strong>Labrador Current:</strong><span style=\"font-weight: 400;\"> It flows from the Arctic Ocean towards the south and meets the warm northward moving Gulf Stream.&nbsp;</span></p>\n",
                    solution_hi: "<p>25.(b)&nbsp;<strong>&#2346;&#2375;&#2352;&#2370;</strong><span style=\"font-weight: 400;\">&#2404;&nbsp; </span><strong>&#2346;&#2375;&#2352;&#2370; &#2343;&#2366;&#2352;&#2366; </strong><span style=\"font-weight: 400;\">(&#2361;&#2350;&#2381;&#2348;&#2379;&#2354;&#2381;&#2335; &#2343;&#2366;&#2352;&#2366; - &#2358;&#2368;&#2340; &#2343;&#2366;&#2352;&#2366;): &#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2366; &#2325;&#2375; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2340;&#2335; &#2325;&#2375; &#2360;&#2366;&#2341;-&#2360;&#2366;&#2341; &#2330;&#2367;&#2354;&#2368; &#2325;&#2375; &#2360;&#2348;&#2360;&#2375; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339;&#2368; &#2360;&#2367;&#2352;&#2375; &#2360;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2346;&#2375;&#2352;&#2370; &#2340;&#2325; &#2348;&#2361;&#2340;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2327;&#2354;&#2381;&#2347;&#2364; &#2360;&#2381;&#2335;&#2381;&#2352;&#2368;&#2350; : </strong><span style=\"font-weight: 400;\">&#2327;&#2352;&#2381;&#2350; &#2346;&#2366;&#2344;&#2368; &#2325;&#2368; &#2343;&#2366;&#2352;&#2366; &#2332;&#2379; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2309;&#2335;&#2354;&#2366;&#2306;&#2335;&#2367;&#2325; &#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352; &#2350;&#2375;&#2306; &#2348;&#2361;&#2340;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2347;&#2381;&#2354;&#2379;&#2352;&#2367;&#2337;&#2366; &#2343;&#2366;&#2352;&#2366; :</strong><span style=\"font-weight: 400;\"> &#2347;&#2381;&#2354;&#2379;&#2352;&#2367;&#2337;&#2366; &#2346;&#2381;&#2352;&#2366;&#2351;&#2342;&#2381;&#2357;&#2368;&#2346; &#2325;&#2375; &#2330;&#2366;&#2352;&#2379;&#2306; &#2323;&#2352; &#2348;&#2361;&#2340;&#2368; &#2361;&#2376; &#2324;&#2352; &#2325;&#2375;&#2346; &#2361;&#2376;&#2335;&#2352;&#2360; &#2350;&#2375;&#2306; &#2327;&#2354;&#2381;&#2347; &#2360;&#2381;&#2335;&#2381;&#2352;&#2368;&#2350; &#2350;&#2375;&#2306; &#2350;&#2367;&#2354;&#2340;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2379;&#2350;&#2366;&#2354;&#2368; &#2343;&#2366;&#2352;&#2366; : </strong><span style=\"font-weight: 400;\">&#2309;&#2335;&#2354;&#2366;&#2306;&#2335;&#2367;&#2325; &#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352; &#2350;&#2375;&#2306; &#2327;&#2354;&#2381;&#2347; &#2360;&#2381;&#2335;&#2381;&#2352;&#2368;&#2350; &#2325;&#2375; &#2309;&#2344;&#2369;&#2352;&#2370;&#2346; &#2330;&#2354;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2354;&#2376;&#2348;&#2381;&#2352;&#2366;&#2337;&#2379;&#2352; &#2343;&#2366;&#2352;&#2366; :</strong><span style=\"font-weight: 400;\"> &#2351;&#2361; &#2310;&#2352;&#2381;&#2325;&#2335;&#2367;&#2325; &#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352; &#2360;&#2375; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2325;&#2368; &#2323;&#2352; &#2348;&#2361;&#2340;&#2368; &#2361;&#2376; &#2324;&#2352; &#2313;&#2340;&#2381;&#2340;&#2352; &#2325;&#2368; &#2323;&#2352; &#2330;&#2354;&#2340;&#2368; &#2361;&#2369;&#2312; &#2327;&#2352;&#2381;&#2350; &#2327;&#2354;&#2381;&#2347; &#2360;&#2381;&#2335;&#2381;&#2352;&#2368;&#2350; &#2360;&#2375; &#2350;&#2367;&#2354;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>