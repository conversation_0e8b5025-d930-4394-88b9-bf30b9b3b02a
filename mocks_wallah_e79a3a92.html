<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A two-digit positive number is such that the product of its digits is 24. When 18 is added to the number, the digits interchange their places. Which smallest positive number should be subtracted from the given number to make it a perfect square?</p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2366;&#2340;&#2381;&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2339;&#2344;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> 24 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 18 </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2360;&#2381;&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2366;&#2340;&#2381;&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2335;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>0</p>\n", "<p>12</p>\n", 
                                "<p>8</p>\n", "<p>10</p>\n"],
                    options_hi: ["<p>0</p>\n", "<p>12</p>\n",
                                "<p>8</p>\n", "<p>10</p>\n"],
                    solution_en: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the two digit positive number = ab</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a &times; b = 24</span></p>\r\n<p><span style=\"font-weight: 400;\">[possible value of a = 4 and b = 6]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">As per question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ab + 18 = ba</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">46 + 18 = 64</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">64 = 64 (Satisfy)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Nearest perfect square less </span><span style=\"font-family: Cambria Math;\">than 46 = 36</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, difference = 46 - 36 = 10</span></p>\n",
                    solution_hi: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2366;&#2340;&#2381;&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = ab</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a &times; b = 24</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">[a = 4 &#2324;&#2352; b = 6 &#2325;&#2366; &#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340; &#2350;&#2366;&#2344;]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ab + 18 = ab</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">46 + 18 = 64</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">64 = 64 (</span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> 46 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> = 36</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 46 - 36 = 10</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: " <p>2.</span><span style=\"font-family:Cambria Math\"> Which of the options given below is divisible by 12?</span></p>",
                    question_hi: " <p>2. </span><span style=\"font-family:Cambria Math\">नीचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विकल्पों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सा</span><span style=\"font-family:Cambria Math\"> 12 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p>  284576 </span></p>", " <p>  236796</span></p>", 
                                " <p>  194792</span></p>", " <p>  157844</span></p>"],
                    options_hi: [" <p>  284576 </span></p>", " <p>  236796</span></p>",
                                " <p>  194792</span></p>", " <p>  157844</span></p>"],
                    solution_en: " <p>2.(b)  </span></p> <p><span style=\"font-family:Cambria Math\">12 = 3 × 4</span></p> <p><span style=\"font-family:Cambria Math\">For divisibility of 3, sum of digits should be divisible by 3</span></p> <p><span style=\"font-family:Cambria Math\">For divisibility of 4, last two digits of the number should be divisible by 4</span></p> <p><span style=\"font-family:Cambria Math\">284576 = 2 + 8 + 4 + 5 + 7 + 6 = 32 (not divisible by 3)</span></p> <p><span style=\"font-family:Cambria Math\">236796 = 2 + 3 + 6 + 7 + 9 + 6 = 33 (divisible b</span><span style=\"font-family:Cambria Math\">y 3)</span></p> <p><span style=\"font-family:Cambria Math\">194792 = 1 + 9 + 4 + 7 + 9 + 2 = 32 (not divisible by 3)</span></p> <p><span style=\"font-family:Cambria Math\">157844 = 1 + 5 + 7 + 8 + 4 + 4 = 29  (not divisible by 3)</span></p> <p><span style=\"font-family:Cambria Math\">So, 236796 is divisible by 12.</span></p>",
                    solution_hi: " <p>2.(b)  </span></p> <p><span style=\"font-family:Cambria Math\">12 = 3 × 4</span></p> <p><span style=\"font-family:Cambria Math\">3 </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्यता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">अंकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">योग</span><span style=\"font-family:Cambria Math\"> 3 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">होना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">चाहिए</span><span style=\"font-family:Cambria Math\">|</span></p> <p><span style=\"font-family:Cambria Math\">4 </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्यता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंतिम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंक</span><span style=\"font-family:Cambria Math\"> 4 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">होने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">चाहिए</span><span style=\"font-family:Cambria Math\">|</span></p> <p><span style=\"font-family:Cambria Math\">284576 = 2 + 8 + 4 + 5 + 7 + 6 = 32 (3 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> )</span></p> <p><span style=\"font-family:Cambria Math\">236796 = 2 + 3 + 6 + 7 + 9 + 6 = 33 (3 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्य</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> )</span></p> <p><span style=\"font-family:Cambria Math\">194792 = 1 + 9 + 4 + 7 + 9 + 2 = 32 (3 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> )</span></p> <p><span style=\"font-family:Cambria Math\">157844 = </span><span style=\"font-family:Cambria Math\">1 + 5 + 7 + 8 + 4 + 4 = 29  (3 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> )</span></p> <p><span style=\"font-family:Cambria Math\">अतः</span><span style=\"font-family:Cambria Math\">, 236796, 12 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If the 7 digit number 504x5y3 is divisible by 11, then one of the values of the sum of x and y is:-</p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 504x5y</span><span style=\"font-family: Cambria Math;\">3, 11 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:-</span></p>\n",
                    options_en: ["<p>7</p>\n", "<p>11</p>\n", 
                                "<p>5</p>\n", "<p>17</p>\n"],
                    options_hi: ["<p>7</p>\n", "<p>11</p>\n",
                                "<p>5</p>\n", "<p>17</p>\n"],
                    solution_en: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Number = 504x5y3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For divisibility of 11,</span></p>\r\n<p><span style=\"font-weight: 400;\">(5 + 4 + 5 + 3) - (0 + x + y) = 0 or multiple of 11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">17 - x - y = 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x + y = 17</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sum of x and y = 17</span></p>\n",
                    solution_hi: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 504x5y3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-weight: 400;\">(5 + 4 + 5 + 3) - (0 +x + y) = 0 &#2351;&#2366; 11 &#2325;&#2366; &#2327;&#2369;&#2339;&#2332;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">17 - x - y = 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x + y = 17</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> = 17</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If 11-digit number 88<span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\">554085</span><span style=\"font-family: Cambria Math;\">k</span><span style=\"font-family: Cambria Math;\">6 ,</span><span style=\"font-family: Cambria Math;\"> k &ne;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\"> , is divisible by 72, then what is the value of ( 3</span><span style=\"font-family: Cambria Math;\">k</span><span style=\"font-family: Cambria Math;\"> + 2</span><span style=\"font-family: Cambria Math;\">p </span><span style=\"font-family: Cambria Math;\">) ?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 11 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 88p554085k6,</span><span style=\"font-family: Cambria Math;\"> k &ne;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\"> , 72 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (3k + 2p) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>23</p>\n", "<p>12</p>\n", 
                                "<p>7</p>\n", "<p>13</p>\n"],
                    options_hi: ["<p>23</p>\n", "<p>12</p>\n",
                                "<p>7</p>\n", "<p>13</p>\n"],
                    solution_en: "<p>4.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">88</span><span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\">554085</span><span style=\"font-family: Cambria Math;\">k</span><span style=\"font-family: Cambria Math;\">6 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For divisibility of 72, we have to check whether the number is divisible by 9 and 8 or not.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, divisibility of 8, if last 3 digits of the number is divisible by 8 the number is divisible by 8.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Divisibility of 9, if sum of the digits of the number is divi</span><span style=\"font-family: Cambria Math;\">sible by 9 then the number is divisible by 9.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">so, putting k = 3, the number is divisible by 8 and from that we get p = 2.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now,&nbsp; </span><span style=\"font-weight: 400;\">3k + 2p = 3 &times; </span><span style=\"font-weight: 400;\">3 + 2 &times; </span><span style=\"font-weight: 400;\">2 </span><span style=\"font-weight: 400;\">= 9 </span><span style=\"font-weight: 400;\">+ 4 </span><span style=\"font-weight: 400;\">= 13.</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>4.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">88</span><span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\">554085</span><span style=\"font-family: Cambria Math;\">k</span><span style=\"font-family: Cambria Math;\">6 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">72 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2326;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">9 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, k = 3 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> p = 2 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;</span><span style=\"font-family: Cambria Math;\">&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, <span style=\"font-weight: 400;\">3k + 2p = 3 &times; </span><span style=\"font-weight: 400;\">3 + 2 &times; </span><span style=\"font-weight: 400;\">2 </span><span style=\"font-weight: 400;\">= 9 </span><span style=\"font-weight: 400;\">+ 4 </span><span style=\"font-weight: 400;\">= 13.</span></span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Which of the following numbers is divisible by 7,11 and 13?</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 7, 11 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 13 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>1002001</p>\n", "<p><span style=\"font-weight: 400;\">1003001</span><span style=\"font-weight: 400;\"><br></span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">1005001</span><span style=\"font-weight: 400;\"><br></span></p>\n", "<p><span style=\"font-weight: 400;\">1004001</span></p>\n"],
                    options_hi: ["<p>&nbsp;1002001</p>\n", "<p>&nbsp;1003001</p>\n",
                                "<p>&nbsp;1005001</p>\n", "<p>&nbsp;1004001</p>\n"],
                    solution_en: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">LCM of 7, 11 and 13 = 1001</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">(1001)&sup2;= 1002001</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, option (a) is divisible by 7, 11 and 13.</span></p>\n",
                    solution_hi: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">7, 11 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 13 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> LCM = 1001</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">(1001)&sup2; = 1002001</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) 7, 11 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 13 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> A fraction becomes&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> when one is added to both the numerator and the denominator of the fraction and it becomes&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, when 2 is subtracted from both the numerator and denominator. What is the sum of the numerator and denominator of the original fraction?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2328;&#2335;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>7&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">13</span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">8</span></p>\n", "<p><span style=\"font-weight: 400;\">11</span></p>\n"],
                    options_hi: ["<p>7</p>\n", "<p>13</p>\n",
                                "<p>8</p>\n", "<p>11</p>\n"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let </span><span style=\"font-family: Cambria Math;\">the fraction =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>y</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mi>y</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3x + 3 = 2y + 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3x - 2y = -1&hellip;.(i)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Again,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mrow><mi>y</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x - 4 = y - 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x - y = 2&hellip;..(ii)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On solving equation (i) and (ii)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3x - 2y = -1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x - 2y = 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">____________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2(5) - y = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 - 2 = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">y = 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sum of fraction = 5 + 8 = 13 </span></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>y</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mi>y</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3x + 3 = 2y + 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3x - 2y = -1&hellip;.(i)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mrow><mi>y</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x - 4 = y - 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x - y = 2&hellip;..(ii)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> (i) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> (ii) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3x - 2y = -1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x - 2y = 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">____________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2(5) - y = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 - 2 = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">y = 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 5 + 8 = 13 </span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: " <p>7. In a division involving decimal fractions, the divisor is 22.8 and the quotient is 8.5, while the remainder is 0. What is the dividend?</span></p>",
                    question_hi: " <p>7. </span><span style=\"font-family:Cambria Math\">दशमलव</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भाग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भाजक</span><span style=\"font-family:Cambria Math\"> 22.8 </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भागफल</span><span style=\"font-family:Cambria Math\"> 8.5 </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">जबकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">शेष</span><span style=\"font-family:Cambria Math\"> 0 </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भाज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> 193.2</span></p>", " <p> 193.8</span></p>", 
                                " <p> 193.6</span></p>", " <p> 193.4</span></p>"],
                    options_hi: [" <p> 193.2</span></p>", " <p> 193.8</span></p>",
                                " <p> 193.6</span></p>", " <p> 193.4</span></p>"],
                    solution_en: " <p>7.(b) </span></p> <p><span style=\"font-family:Cambria Math\">Dividend = divisor × quotient + remainder</span></p> <p><span style=\"font-family:Cambria Math\">Dividend = 22.8 × 8.5 + 0 = 193.8</span></p>",
                    solution_hi: " <p>7.(b)</span></p> <p><span style=\"font-family:Cambria Math\">भाज्य</span><span style=\"font-family:Cambria Math\"> = </span><span style=\"font-family:Cambria Math\">भाजक</span><span style=\"font-family:Cambria Math\"> × </span><span style=\"font-family:Cambria Math\">भागफल</span><span style=\"font-family:Cambria Math\"> + </span><span style=\"font-family:Cambria Math\">शेष</span></p> <p><span style=\"font-family:Cambria Math\">भाज्य</span><span style=\"font-family:Cambria Math\"> = 22.8 × 8.5 + 0 = 193.8</span></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. If the 9-digit number 83<span style=\"font-family: Cambria Math;\">x</span><span style=\"font-family: Cambria Math;\">93678</span><span style=\"font-family: Cambria Math;\">y</span><span style=\"font-family: Cambria Math;\"> is divisible by 72, then what is the value of (3</span><span style=\"font-family: Cambria Math;\">x</span><span style=\"font-family: Cambria Math;\"> &ndash; 2</span><span style=\"font-family: Cambria Math;\">y</span><span style=\"font-family: Cambria Math;\">)?</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 83x93678y 72 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (3x - 2y) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>8</p>\n", "<p>10</p>\n", 
                                "<p>12</p>\n", "<p>15</p>\n"],
                    options_hi: ["<p>8</p>\n", "<p>10</p>\n",
                                "<p>12</p>\n", "<p>15</p>\n"],
                    solution_en: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">72 = 8 &times; 9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For divisibility of 8, last 3 digit should be multiple of 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>78</mn><mi>y</mi></mrow><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> then possible value of y = 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Number = 83x93678</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">4</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 + 3 + x + 9 + 3 + 6 + 7 + 8 + 4 = multiple of 9(for divisibility of 9 - sum of digits of the number should be divisible by 9)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">48 + x = multiple of 9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 6</span></p>\r\n<p>(3x - 2y) = (3 &times; 6 - 2 &times; 4) = 10</p>\n",
                    solution_hi: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">72 = 8 &times; 9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> 8 . </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2339;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>78</mn><mi>y</mi></mrow><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 83x936784</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 + 3 + x + 9 + 3 + 6 + 7 + 8 + 4 = 9 . </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2339;&#2332;</span><span style=\"font-family: Cambria Math;\">(9 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">48 + x = 9 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2339;&#2332;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 6</span></p>\r\n<p><span style=\"font-weight: 400;\">(3x - 2y) = (3 &times; 6 - 2 &times; 4) = 10</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\">Whi</span><span style=\"font-family: Cambria Math;\">ch of the following numbers is divisible by 8 ?</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>12312</p>\n", "<p><span style=\"font-weight: 400;\">12316</span><span style=\"font-weight: 400;\"><br></span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">12318</span></p>\n", "<p><span style=\"font-weight: 400;\">12314</span></p>\n"],
                    options_hi: ["<p>12312</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;12316</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">12318</span></p>\n", "<p><span style=\"font-weight: 400;\">12314</span></p>\n"],
                    solution_en: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">For divisibility of 8, last 3 digits should be divisible by 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, Only option (a) is divisible by 8.</span></p>\n",
                    solution_hi: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">8 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> |</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> In a bag full of pencils,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> of the </span><span style=\"font-family: Cambria Math;\">pencils were coloured pencils, and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> of the coloured pencils were red. If there were 40 red pencils in the bag. What was the total numbers of pencils in the bag?</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2306;&#2327;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2306;&#2327;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>100</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;120</span><span style=\"font-weight: 400;\"><br></span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">&nbsp;150</span></p>\n", "<p><span style=\"font-weight: 400;\">180</span></p>\n"],
                    options_hi: ["<p>100</p>\n", "<p><span style=\"font-weight: 400;\">120</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">150</span></p>\n", "<p><span style=\"font-weight: 400;\">180</span></p>\n"],
                    solution_en: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let total number of pencils = x </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>N</mi><mi>o</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>l</mi><mi>o</mi><mi>u</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>p</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>i</mi><mi>l</mi><mi>s</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mi>x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>N</mi><mi>o</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>l</mi><mi>o</mi><mi>u</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>p</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>i</mi><mi>l</mi><mi>s</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>15</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>x</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>x</mi><mo>&nbsp;</mo><mo>&rarr;</mo><mo>&nbsp;</mo><mn>40</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>&rarr;</mo><mo>&nbsp;</mo><mn>100</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">So total number of pencils = 100</span></p>\n",
                    solution_hi: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = x</span><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2352;&#2306;&#2327;&#2368;&#2344;</mi><mo>&nbsp;</mo><mi>&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mi>x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2354;&#2366;&#2354;</mi><mo>&nbsp;</mo><mi>&#2352;&#2306;&#2327;</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>15</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>x</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>x</mi><mo>&nbsp;</mo><mo>&rarr;</mo><mo>&nbsp;</mo><mn>40</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>&rarr;</mo><mo>&nbsp;</mo><mn>100</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 100</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> In what way can the terms of the given set be rearranged into three sets such that the sum of the two terms in each set is equal?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(947, 861, 1304, 1218, 1378, 787)</span></span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2344;&#2352;&#2381;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(947, 861, 1304, 1218, 1378, 787)</span></span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">(787, 1378), (947, 1304), (861, 1218)</span></p>\n", "<p><span style=\"font-weight: 400;\">(947, 1218), (861, 1304), (787, 1378)</span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">&nbsp;(861, 1218), (947, 1378), (787, 1304)</span></p>\n", "<p><span style=\"font-weight: 400;\">(947, 1304), (861, 1378), (787, 1218)</span></p>\r\n<p>&nbsp;</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(787, 1378), (947, 1304), (861, 1218)</span></p>\n", "<p><span style=\"font-weight: 400;\">(947, 1218), (861, 1304), (787, 1378)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">(861, 1218), (947, 1378), (787, 1304)</span></p>\n", "<p><span style=\"font-weight: 400;\">(947, 1304), (861, 1378), (787, 1218)</span></p>\r\n<p>&nbsp;</p>\n"],
                    solution_en: "<p>11.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">947 + 1218 = 861 + 1304 = 787 + 1378</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2165 = 2165 = 2165</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">NOTE :- During exam, Just check the unit digit and eliminate the options.</span></p>\n",
                    solution_hi: "<p>11.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">947 + 1218 = 861 + 1304 = 787 + 1378</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2165 = 2165 = 2165</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2344;&#2379;&#2335;</span><span style=\"font-family: Cambria Math;\">:- </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2306;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> Find k such that a 7-digit number 51032kk is divisible by 12.</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> k </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 51032kk </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> k </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    options_en: ["<p>6</p>\n", "<p>4</p>\n", 
                                "<p>8</p>\n", "<p>2</p>\n"],
                    options_hi: ["<p>6</p>\n", "<p>4</p>\n",
                                "<p>8</p>\n", "<p>2</p>\n"],
                    solution_en: "<p>12.<span style=\"font-family: Cambria Math;\">(c) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">By options, when k = 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 5 1 0 3 2 k k &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 1 0 3 2 8 8 is divisible by both 4 and 3, so it also divisible by 12.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Divisibility of 4 &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> last 2 digit divisible</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Divisibility of 3 &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> sum of the digits should be divisible by 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> k = 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 5 1 0 3 2 k k &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 1 0 3 2 8 8 , 4 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> &nbsp;&rarr; </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> of the balls in a drum were red,</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math>of the balls in the drum were blue and the rest were yellow. If there were 84 more red balls in that drum than there were yellow balls. What was the total number of the balls in the drum?</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2380;&#2332;&#2370;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2306;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2306;&#2342;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2306;&#2342;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2306;&#2342;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2368;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2306;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2368;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2306;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 84 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2380;&#2332;&#2370;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2306;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">180</span></p>\n", "<p>90</p>\n", 
                                "<p>270</p>\n", "<p>360</p>\n"],
                    options_hi: ["<p>180</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;90</span><span style=\"font-weight: 400;\"><br></span></p>\n",
                                "<p><span style=\"font-weight: 400;\">270</span></p>\n", "<p><span style=\"font-weight: 400;\">360</span></p>\n"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the total number of balls be x</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>18</mn></mfrac><mo>&nbsp;</mo><mi>a</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mfrac><mrow><mn>8</mn><mi>x</mi></mrow><mn>15</mn></mfrac><mo>&nbsp;</mo><mi>a</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mi>b</mi><mi>l</mi><mi>u</mi><mi>e</mi><mo>;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>R</mi><mi>e</mi><mi>m</mi><mi>a</mi><mi>i</mi><mi>n</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>b</mi><mi>a</mi><mi>l</mi><mi>l</mi><mi>s</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>x</mi><mo>-</mo><mo>(</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>18</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>8</mn><mi>x</mi></mrow><mn>15</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>90</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>N</mi><mi>o</mi><mi>w</mi><mo>,</mo><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>18</mn></mfrac><mo>=</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>90</mn></mfrac><mo>+</mo><mn>84</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>18</mn></mfrac><mo>-</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>90</mn></mfrac><mo>=</mo><mn>84</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>=</mo><mn>270</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2375;&#2306;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>18</mn></mfrac><mo>&nbsp;</mo><mi>&#2354;&#2366;&#2354;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;&#2306;</mi><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mfrac><mrow><mn>8</mn><mi>x</mi></mrow><mn>15</mn></mfrac><mo>&nbsp;</mo><mi>&#2344;&#2368;&#2354;&#2375;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;&#2306;</mi><mo>;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2348;&#2330;&#2368;</mi><mo>&nbsp;</mo><mi>&#2361;&#2369;&#2312;</mi><mo>&nbsp;</mo><mi>&#2327;&#2375;&#2306;&#2342;&#2375;&#2306;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>x</mi><mo>-</mo><mo>(</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>18</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>8</mn><mi>x</mi></mrow><mn>15</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>90</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2340;&#2307;</mi><mo>,</mo><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>18</mn></mfrac><mo>=</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>90</mn></mfrac><mo>+</mo><mn>84</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>18</mn></mfrac><mo>-</mo><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>90</mn></mfrac><mo>=</mo><mn>84</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>=</mo><mn>270</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Which of the following numbers is NOT divisible by 8 ?</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;</span><span style=\"font-family: Cambria Math;\">&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>35552</p>\n", "<p><span style=\"font-weight: 400;\">35792</span><span style=\"font-weight: 400;\"><br></span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">35112</span></p>\n", "<p><span style=\"font-weight: 400;\">35412</span></p>\n"],
                    options_hi: ["<p>35552</p>\n", "<p><span style=\"font-weight: 400;\">35792</span><span style=\"font-weight: 400;\"><br></span></p>\n",
                                "<p><span style=\"font-weight: 400;\">35112</span><span style=\"font-weight: 400;\"><br></span></p>\n", "<p><span style=\"font-weight: 400;\">35412</span></p>\n"],
                    solution_en: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Divisibility of 8 = last three digits must be divisible by 8.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> By options 35412, last 3 digit 412 is not divisible by 8.</span></p>\n",
                    solution_hi: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">8 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> 35412 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> 412, 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">|</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Which one of the following numbers is divisible by 24?</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 24 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>32192</p>\n", "<p>52182</p>\n", 
                                "<p>42192</p>\n", "<p>23152</p>\n"],
                    options_hi: ["<p>32192</p>\n", "<p>52182</p>\n",
                                "<p>42192</p>\n", "<p>23152</p>\n"],
                    solution_en: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Numbers which are divisible by both 3 and 8, are also divisible by 24.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">By options,42192 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>2</mn></mrow><mn>3</mn></mfrac></math> = 6 <span style=\"font-weight: 400;\">. So, it is divisible by 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> </span><span style=\"font-family: Cambria Math;\">[sum of digit </span><span style=\"font-family: Cambria Math;\">must be divisible by 3]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">42192 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mn>192</mn><mn>8</mn></mfrac><mo>=</mo><mn>24</mn></math> <span style=\"font-weight: 400;\">So, it is divisible by 8</span> &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">[last 3 digit must be divisible by 8]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, 42192 is divisible by 24.</span></p>\n",
                    solution_hi: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 24 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2368;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">, 42192&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mn>3</mn></mfrac></math> = 6, <span style=\"font-weight: 400;\">&#2309;&#2340;&#2307;&nbsp; &#2351;&#2361; 3&nbsp; &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">42192 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mn>192</mn><mn>8</mn></mfrac><mo>=</mo><mn>24</mn></math>.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">[</span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> , 42192, 24 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;[</span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">]</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>