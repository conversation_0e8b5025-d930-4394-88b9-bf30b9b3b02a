<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> ......... is data that has been organized and presented in a meaningful fashion.</span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> ........ </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2375; &#2319;&#2325; &#2360;&#2366;&#2352;&#2381;&#2341;&#2325; &#2340;&#2352;&#2368;&#2325;&#2375; &#2350;&#2375;&#2306; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2324;&#2352; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2369;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>A process</p>\n", "<p>Software</p>\n", 
                                "<p>Storage</p>\n", "<p>Information</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; </span><span style=\"font-family: Cambria Math;\">(process) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (Software)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; (Storage)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2311;&#2344;&#2347;&#2366;&#2352;&#2381;&#2350;&#2375;&#2358;&#2344; (Information)</span></p>\n"],
                    solution_en: "<p>1.(d) <strong>Information</strong><span style=\"font-family: Cambria Math;\"> is the data that has been organized and presented in a meaningful fashion.</span></p>\n",
                    solution_hi: "<p>1.(d) <strong>&#2360;&#2370;&#2330;&#2344;&#2366;(information)</strong> &#2357;&#2361; &#2337;&#2375;&#2335;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2375; &#2319;&#2325; &#2360;&#2366;&#2352;&#2381;&#2341;&#2325; &#2340;&#2352;&#2368;&#2325;&#2375; &#2360;&#2375; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2324;&#2352; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2369;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> The components that process data are located in which of the following?</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; </span><span style=\"font-family: Cambria Math;\">(process) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2325;&#2306;&#2346;&#2379;&#2344;&#2375;&#2306;&#2335;&#2381;&#2360; </span><span style=\"font-family: Cambria Math;\">(components) </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Input devices</p>\n", "<p>Output devices</p>\n", 
                                "<p>System unit</p>\n", "<p>Storage component</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2311;&#2344;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; (Input devices)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2310;&#2313;&#2335;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; (Output devices)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2351;&#2370;&#2344;&#2367;&#2335; (System unit)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2325;&#2306;&#2346;&#2379;&#2344;&#2375;&#2306;&#2335; (Storage component)</span></p>\n"],
                    solution_en: "<p>2.(c) The component that processes data located in the <strong>system unit</strong>.</p>\n",
                    solution_hi: "<p>2.(c) &#2325;&#2306;&#2346;&#2379;&#2344;&#2375;&#2306;&#2335;&#2381;&#2360; &#2332;&#2379; &#2337;&#2366;&#2335;&#2366; &#2325;&#2379; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376; &#2357;&#2361; <strong>&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2351;&#2370;&#2344;&#2367;&#2335;</strong> &#2350;&#2375;&#2306; &#2360;&#2381;&#2340;&#2367;&#2341; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376; I</p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> Which of the following is not hardware?</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Processor chip</p>\n", "<p>Printer</p>\n", 
                                "<p>Mouse</p>\n", "<p>Java</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; &#2330;&#2367;&#2346;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2313;&#2360;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2357;&#2366;</span></p>\n"],
                    solution_en: "<p>3.(d)<span style=\"font-family: Cambria Math;\"> Among all the&nbsp; given options only <strong>java</strong> is not hardware.</span></p>\n",
                    solution_hi: "<p>3.(d)<span style=\"font-family: Cambria Math;\"> &#2342;&#2367;&#2319; &#2327;&#2319; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2349;&#2368; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2375;&#2357;&#2354; <strong>&#2332;&#2366;&#2357;&#2366;</strong> &#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Which of the following is not a binary number? </span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2344;&#2306;&#2348;&#2352; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>001</p>\n", "<p>101</p>\n", 
                                "<p>202</p>\n", "<p>110</p>\n"],
                    options_hi: ["<p>001</p>\n", "<p>101</p>\n",
                                "<p>202</p>\n", "<p>110</p>\n"],
                    solution_en: "<p>4.(c)<span style=\"font-family: Cambria Math;\"> 202 does comes not under binary number system because it contains only two unique digits 0&rsquo;s </span><span style=\"font-family: Cambria Math;\">and 1&rsquo;s. It is also known as the Base 2 system.</span></p>\n",
                    solution_hi: "<p>4.(c)<span style=\"font-family: Cambria Math;\"> 202 &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2344;&#2306;&#2348;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2375; &#2309;&#2306;&#2340;&#2352;&#2381;&#2327;&#2340; &#2344;&#2361;&#2368;&#2306; &#2310;&#2340;&#2366; &#2361;&#2376; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; &#2325;&#2375;&#2357;&#2354; &#2342;&#2379; &#2309;&#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351; &#2309;&#2306;&#2325; 0 &#2324;&#2352; 1 &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2375; &#2348;&#2375;&#2360; 2 &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2349;&#2368; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> The primary purpose of software is to turn data into__________ .</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2325;&#2366; &#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; </span><span style=\"font-family: Cambria Math;\">(primary purpose) </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">_____ </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2344;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>information</p>\n", "<p>programs</p>\n", 
                                "<p>objects</p>\n", "<p>charts</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2347;&#2366;&#2352;&#2381;&#2350;&#2375;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">(information) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; </span><span style=\"font-family: Cambria Math;\">(programs) </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2321;&#2348;&#2381;&#2332;&#2375;&#2325;&#2381;&#2335; </span><span style=\"font-family: Cambria Math;\">(objects ) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2330;&#2366;&#2352;&#2381;&#2335; (charts)</span></p>\n"],
                    solution_en: "<p>5.(a)<span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">The primary purpose of software is </span><strong>to turn data into Information</strong><span style=\"font-weight: 400;\">. When that data is processed into sets according to context, it provides information.</span></span></p>\n",
                    solution_hi: "<p>5.(a)<span style=\"font-family: Cambria Math;\"> &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2325;&#2366; &#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2360;&#2370;&#2330;&#2344;&#2366; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2344;&#2366; &#2361;&#2376;&#2404; &#2332;&#2348; &#2313;&#2360; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2360;&#2375;&#2335; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2340;&#2379; &#2351;&#2361; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> A collection of programs that controls how your computer system runs and processes </span><span style=\"font-family: Cambria Math;\">information is called__________ .</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2325;&#2366; &#2319;&#2325; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361; &#2332;&#2379; &#2351;&#2361; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; </span><span style=\"font-family: Cambria Math;\">&nbsp;&#2361;&#2376; &#2325;&#2367; &#2310;&#2346;&#2325;&#2366; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2376;&#2360;&#2375; &#2330;&#2354;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2360;&#2370;&#2330;&#2344;&#2366;&#2323;&#2306;(informations)&nbsp; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2325;&#2361;&#2354;&#2366;&#2340;&#2366; &#2361;&#2376;_________&nbsp;</span></p>\n",
                    options_en: ["<p>operating system</p>\n", "<p>computer</p>\n", 
                                "<p>office</p>\n", "<p>compiler</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;(operating system)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; (computer)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2321;&#2347;&#2367;&#2360; (office)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2350;&#2381;&#2346;&#2366;&#2311;&#2354;&#2352; (compiler)</span></p>\n"],
                    solution_en: "<p>6.(a)&nbsp;A collection of program that controls how your computer system runs and processes information is called <strong>Compiler</strong>. A compiler is computer software that transforms computer code written in one programming language into another programming language.</p>\n",
                    solution_hi: "<p>6.(a)&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2325;&#2366; &#2319;&#2325; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361; &#2332;&#2379; &#2351;&#2361; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2325;&#2367; &#2310;&#2346;&#2325;&#2366; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2376;&#2360;&#2375; &#2330;&#2354;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2360;&#2370;&#2330;&#2344;&#2366;&#2323;&#2306;(informations) &#2325;&#2379; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;, <strong>&#2325;&#2306;&#2346;&#2366;&#2311;&#2354;&#2352;(compiler) </strong>&#2325;&#2361;&#2354;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2319;&#2325; &#2325;&#2306;&#2346;&#2366;&#2311;&#2354;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2361;&#2376; &#2332;&#2379; &#2319;&#2325; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; &#2354;&#2367;&#2326;&#2375; &#2327;&#2319; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2379;&#2337; &#2325;&#2379; &#2342;&#2370;&#2360;&#2352;&#2368; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> If you are changing from Windows 98 operating system to Windows XP&nbsp; then you are </span><span style=\"font-family: Cambria Math;\">performing a(n)__________ .</span></p>\n",
                    question_hi: "<p>7.&#2351;&#2342;&#2367; &#2310;&#2346; &#2357;&#2367;&#2306;&#2337;&#2379;&#2332; 98 &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2379; &#2357;&#2367;&#2306;&#2337;&#2379;&#2332; XP &#2350;&#2375;&#2306; &#2348;&#2342;&#2354; &#2352;&#2361;&#2375; &#2361;&#2376;&#2306; &#2340;&#2379; &#2310;&#2346; &#2319;&#2325; &nbsp;______ &#2325;&#2352; &#2352;&#2361;&#2375; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    options_en: ["<p>push up</p>\n", "<p>upgrade</p>\n", 
                                "<p>patch</p>\n", "<p>update</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2358; &#2309;&#2346;</span><span style=\"font-family: Cambria Math;\">(push up) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2327;&#2381;&#2352;&#2375;&#2337;</span><span style=\"font-family: Cambria Math;\">(upgrade) </span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2346;&#2376;&#2330;(patch)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2337;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\">(update) </span></p>\n"],
                    solution_en: "<p>7.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">An update is new, improved, or fixed software, which replaces older versions of the same software.</span></p>\n",
                    solution_hi: "<p>7.(d) <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2309;&#2346;&#2337;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\">(update) </span><span style=\"font-family: Cambria Math;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2361;&#2340;&#2352; &#2351;&#2366; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">(fixed) </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379; &#2313;&#2360;&#2368; &#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2325;&#2375; &#2346;&#2369;&#2352;&#2366;&#2344;&#2375; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">(versions) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">(replace) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\">&nbsp; What do we call an attempt to gain unauthorized access to a user&rsquo;s system or information by pretending </span><span style=\"font-family: Cambria Math;\">to be the user? </span></p>\n",
                    question_hi: "<p>8. &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2361;&#2379;&#2344;&#2375; &#2325;&#2366; &#2342;&#2367;&#2326;&#2366;&#2357;&#2366; &#2325;&#2352;&#2325;&#2375; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2325;&#2375; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2351;&#2366; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2340;&#2325; &#2309;&#2344;&#2343;&#2367;&#2325;&#2371;&#2340; &#2346;&#2361;&#2369;&#2306;&#2330; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2346;&#2381;&#2352;&#2351;&#2366;&#2360; &#2325;&#2379; &#2361;&#2350; &#2325;&#2381;&#2351;&#2366; &#2325;&#2361;&#2340;&#2375; &#2361;&#2376;&#2306;?</p>\n",
                    options_en: ["<p>Spoofing</p>\n", "<p>Hacker</p>\n", 
                                "<p>Cracker</p>\n", "<p>Phishing</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2370;&#2347;&#2367;&#2306;&#2327; (Spoofing)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2361;&#2376;&#2325;&#2352; (Hacker)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2325;&#2381;&#2352;&#2376;&#2325;&#2352; (Cracker)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2347;&#2364;&#2367;&#2358;&#2367;&#2306;&#2327; (Phishing)</span></p>\n"],
                    solution_en: "<p>8.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>Spoofing attack</strong>&rarr;</span><span style=\"font-family: Cambria Math;\">Spoofing is a specific type of cyber-attack in which someone attempts to use a computer, device, or network to trick other computer networks by masquerading as a legitimate entity. </span><strong><span style=\"font-family: Cambria Math;\">Cracker </span></strong><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">Cracker </span><span style=\"font-family: Cambria Math;\">an individual who attempts to access computer systems without authorization. </span><span style=\"font-family: Cambria Math;\"><strong>Hacker</strong>&rarr;</span><span style=\"font-family: Cambria Math;\">The term refers to anyone who uses their abilities to gain unauthorized access to systems or networks in order to commit crimes.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>Phishing</strong>&rarr;</span><span style=\"font-family: Cambria Math;\">Phishing is a type of cybersecurity attack during which malicious actors send messages pretending to be a trusted person or entity.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(a) </span><strong><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2370;&#2347;&#2367;&#2306;&#2327; &#2309;&#2335;&#2376;&#2325;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2370;&#2347;&#2367;&#2306;&#2327; &#2319;&#2325; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2360;&#2366;&#2311;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;&#2354;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2325;&#2379;&#2312; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2351;&#2366; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2366;&#2360; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2340;&#2366;&#2325;&#2367; &#2309;&#2344;&#2381;&#2351; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2379; &#2319;&#2325; &#2357;&#2376;&#2343; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2343;&#2379;&#2326;&#2366; &#2342;&#2375;&#2325;&#2352; &#2331;&#2354; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2375;&#2404; </span><strong><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2376;&#2325;&#2352; </span></strong><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2376;&#2325;&#2352; &#2319;&#2325; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2361;&#2376; &#2332;&#2379; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2325;&#2375; &#2348;&#2367;&#2344;&#2366; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2340;&#2325; &#2346;&#2361;&#2369;&#2306;&#2330;&#2344;&#2375; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2366;&#2360; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><span style=\"font-family: Cambria Math;\"><strong>&#2361;&#2376;&#2325;&#2352;</strong> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2358;&#2348;&#2381;&#2342; &#2325;&#2367;&#2360;&#2368; &#2325;&#2379; &#2349;&#2368; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2379; &#2309;&#2346;&#2352;&#2366;&#2343; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2351;&#2366; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2346;&#2352; &#2309;&#2344;&#2343;&#2367;&#2325;&#2371;&#2340; &#2346;&#2361;&#2369;&#2306;&#2330; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2346;&#2344;&#2368; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366;&#2323;&#2306; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong><span style=\"font-family: Cambria Math;\">&#2347;&#2364;&#2367;&#2358;&#2367;&#2306;&#2327;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2347;&#2364;&#2367;&#2358;&#2367;&#2306;&#2327; &#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2360;&#2366;&#2311;&#2348;&#2352; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2361;&#2350;&#2354;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2342;&#2369;&#2352;&#2381;&#2349;&#2366;&#2357;&#2344;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339; &#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2366;(malicious actor) &#2319;&#2325; &#2357;&#2367;&#2358;&#2381;&#2357;&#2360;&#2344;&#2368;&#2351; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2351;&#2366; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366; &#2361;&#2379;&#2344;&#2375; &#2325;&#2366; &#2344;&#2366;&#2335;&#2325; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319; &#2360;&#2306;&#2342;&#2375;&#2358; &#2349;&#2375;&#2332;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Each IP packet must contain______</span></p>\n",
                    question_hi: "<p>9. &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2310;&#2312;&#2346;&#2368; &#2346;&#2376;&#2325;&#2375;&#2335; &#2350;&#2375;&#2306; ______ &#2361;&#2379;&#2344;&#2366; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</p>\n",
                    options_en: ["<p>only source address</p>\n", "<p>only destination address</p>\n", 
                                "<p>source and destination addresses</p>\n", "<p>source or destination address</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354; &#2360;&#2381;&#2352;&#2379;&#2340; &#2325;&#2366; &#2346;&#2340;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2354; &#2327;&#2306;&#2340;&#2357;&#2381;&#2351; &#2325;&#2366; &#2346;&#2340;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2352;&#2379;&#2340; &#2324;&#2352; &#2327;&#2306;&#2340;&#2357;&#2381;&#2351; &#2346;&#2340;&#2375;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2352;&#2379;&#2340; &#2351;&#2366; &#2327;&#2306;&#2340;&#2357;&#2381;&#2351; &#2325;&#2366; &#2346;&#2340;&#2366;</span></p>\n"],
                    solution_en: "<p>9.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Each IP packet contains both a header (20 or 24 bytes long) and data (variable length). The header includes the</span><span style=\"font-family: Cambria Math;\"> <strong>IP addresses of the source and destination</strong></span><span style=\"font-family: Cambria Math;\">, plus other fields that help to route the packet. </span></p>\n",
                    solution_hi: "<p>9.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2310;&#2312;&#2346;&#2368; &#2346;&#2376;&#2325;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">IP packet</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2361;&#2375;&#2337;&#2352; </span><span style=\"font-family: Cambria Math;\">(20 </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366; </span><span style=\"font-family: Cambria Math;\">24 </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2311;&#2335; &#2354;&#2306;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2337;&#2375;&#2335;&#2366; </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;&#2368;&#2351; &#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2361;&#2375;&#2337;&#2352; &#2350;&#2375;&#2306; <strong>&#2360;&#2381;&#2352;&#2379;&#2340; &#2324;&#2352; &#2327;&#2306;&#2340;&#2357;&#2381;&#2351; &#2325;&#2375; &#2310;&#2312;&#2346;&#2368; &#2346;&#2340;&#2375;</strong></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341; &#2361;&#2368; &#2309;&#2344;&#2381;&#2351; &#2347;&#2364;&#2368;&#2354;&#2381;&#2337; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306; &#2332;&#2379; &#2346;&#2376;&#2325;&#2375;&#2335; &#2325;&#2379; &#2352;&#2370;&#2335; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2350;&#2342;&#2342; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "31",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Communication between a computer and a keyboard involves ........ transmission.</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2324;&#2352; &#2319;&#2325; &#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2306;&#2330;&#2366;&#2352; &#2350;&#2375;&#2306; </span><span style=\"font-family: Cambria Math;\">........ </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2330;&#2366;&#2352;&#2339; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>automatic</p>\n", "<p>half duplex</p>\n", 
                                "<p>full-duplex</p>\n", "<p>simplex</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2310;&#2335;&#2379;&#2350;&#2375;&#2335;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">(automatic) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2347; &#2337;&#2369;&#2346;&#2381;&#2354;&#2375;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(half-duplex) </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2347;&#2369;&#2354; </span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2337;&#2369;&#2346;&#2381;&#2354;&#2375;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(full-duplex) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2367;&#2306;&#2346;&#2381;&#2354;&#2375;&#2325;&#2381;&#2360;(simplex)</span></p>\n"],
                    solution_en: "<p>10.(d) <span style=\"font-family: Cambria Math;\"><strong>Simplex</strong> states that information can only be broadcast in one direction, at one </span><span style=\"font-family: Cambria Math;\">time. Examples of simplex include radio broadcasting, television broadcasting, computer to printer communication, and keyboard to computer connections.</span></p>\n",
                    solution_hi: "<p>10.(d) <span style=\"font-family: Cambria Math;\"><strong>&#2360;&#2367;&#2350;&#2381;&#2346;&#2354;&#2375;&#2325;&#2381;&#2360;</strong> &#2348;&#2340;&#2366;&#2340;&#2366; &#2361;&#2376; &#2325;&#2367; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2325;&#2375;&#2357;&#2354; &#2319;&#2325; &#2342;&#2367;&#2358;&#2366; &#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2360;&#2350;&#2351; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2360;&#2366;&#2352;&#2367;&#2340; &#2325;&#2368; &#2332;&#2366; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376;&#2404; &#2360;&#2367;&#2350;&#2381;&#2346;&#2354;&#2375;&#2325;&#2381;&#2360; &#2325;&#2375; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339;&#2379;&#2306; &#2350;&#2375;&#2306; &#2352;&#2375;&#2337;&#2367;&#2351;&#2379; &#2346;&#2381;&#2352;&#2360;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2354;&#2368;&#2357;&#2367;&#2332;&#2344; &#2346;&#2381;&#2352;&#2360;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2375; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352; &#2360;&#2306;&#2330;&#2366;&#2352; &#2324;&#2352; &#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; &#2360;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "31",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> In the relational modes, cardinality is termed as_________ .</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> &#2352;&#2367;&#2354;&#2375;&#2358;&#2344;&#2354; &#2350;&#2379;&#2337; &#2350;&#2375;&#2306;, &#2325;&#2366;&#2352;&#2381;&#2337;&#2367;&#2344;&#2376;&#2354;&#2367;&#2335;&#2368; &#2325;&#2379; ____ &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>number of tuples</p>\n", "<p>number of attributes</p>\n", 
                                "<p>number of tables</p>\n", "<p>number of constraints</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2335;&#2369;&#2346;&#2354;&#2381;&#2360; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;(number of tuples)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2335;&#2381;&#2352;&#2367;&#2348;&#2381;&#2351;&#2370;&#2335; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;(number of attributes)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2335;&#2375;&#2348;&#2354;&#2381;&#2360;&nbsp; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;(number of tables)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2306;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2306;&#2335;&#2381;&#2360;&nbsp; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;(number of constraints)</span></p>\n"],
                    solution_en: "<p>11.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">The number of tuples in a relation is called </span><span style=\"font-family: Cambria Math;\">its <strong>cardinality</strong>.</span></p>\n",
                    solution_hi: "<p>11.(a) <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2360;&#2306;&#2348;&#2306;&#2343; &#2350;&#2375;&#2306; &#2335;&#2369;&#2346;&#2354;&#2381;&#2360; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2379; &#2311;&#2360;&#2325;&#2368; <strong>&#2325;&#2366;&#2352;&#2381;&#2337;&#2367;&#2344;&#2376;&#2354;&#2367;&#2335;&#2368;</strong></span><strong><span style=\"font-family: Cambria Math;\">(cardinality) </span></strong><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "31",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> Excel worksheet data can be shared with Word document by__________ .</span></p>\n",
                    question_hi: "<p>12.&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; __________ &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2357;&#2352;&#2381;&#2337; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2325;&#2375; &#2360;&#2366;&#2341; &#2360;&#2366;&#2333;&#2366; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    options_en: ["<p>inserting an Excel file into Word</p>\n", "<p>copy and paste Excel worksheet into Word document</p>\n", 
                                "<p>link Excel data in a Word document</p>\n", "<p>All of the above</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2347;&#2366;&#2311;&#2354; &#2325;&#2379; &#2357;&#2352;&#2381;&#2337; &#2350;&#2375;&#2306; &#2311;&#2344;&#2381;&#2360;&#2352;&#2381;&#2335; &#2325;&#2352;&#2344;&#2375; </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; &#2325;&#2379; &#2357;&#2352;&#2381;&#2337; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2350;&#2375;&#2306; &#2325;&#2377;&#2346;&#2368; &#2324;&#2352; &#2346;&#2375;&#2360;&#2381;&#2335; &#2325;&#2352;&#2344;&#2375; </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2357;&#2352;&#2381;&#2337; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2350;&#2375;&#2306; &#2354;&#2367;&#2306;&#2325; &#2325;&#2352;&#2344;&#2375; </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2360;&#2349;&#2368;</span></p>\n"],
                    solution_en: "<p>12.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">You can bring the data from an Excel workbook into a word document in many ways. You can copy data from an open worksheet and paste it into a word document, import a worksheet into a new or existing table, or link to a worksheet from a word document.</span></p>\n",
                    solution_hi: "<p>12.(d) <span style=\"font-family: Cambria Math;\">&#2310;&#2346; &#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2357;&#2352;&#2381;&#2325;&#2348;&#2369;&#2325;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">Excel workbook</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2357;&#2352;&#2381;&#2337; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2350;&#2375;&#2306; &#2325;&#2312; &#2340;&#2352;&#2361; &#2360;&#2375; &#2354;&#2366; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2310;&#2346; &#2319;&#2325; &#2323;&#2346;&#2344; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\">(open worksheet) </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2337;&#2375;&#2335;&#2366; &#2325;&#2377;&#2346;&#2368; &#2325;&#2352; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2311;&#2360;&#2375; &#2319;&#2325; &#2357;&#2352;&#2381;&#2337; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\">(word document) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2346;&#2375;&#2360;&#2381;&#2335; &#2325;&#2352; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; &#2325;&#2379; &#2319;&#2325; &#2344;&#2312; &#2351;&#2366; &#2350;&#2380;&#2332;&#2370;&#2342;&#2366; &#2335;&#2375;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\">(table) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2311;&#2350;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335; &#2325;&#2352; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366; &#2319;&#2325; &#2357;&#2352;&#2381;&#2337; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2360;&#2375; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; &#2360;&#2375; &#2354;&#2367;&#2306;&#2325; &#2325;&#2352; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "31",
                    question_en: " <p>13.</span><span style=\"font-family:Cambria Math\"> What is Windows Explorer?</span></p>",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2306;&#2337;&#2379;&#2332; &#2319;&#2325;&#2381;&#2360;&#2346;&#2381;&#2354;&#2379;&#2352;&#2352; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: [" <p> Personal Computer </span></p>", " <p> Network</span></p>", 
                                " <p> File Manager </span></p>", " <p> Web Browser</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2346;&#2352;&#2381;&#2360;&#2344;&#2354; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;(Personal Computer)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;(Network)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2347;&#2366;&#2311;&#2354; &#2350;&#2376;&#2344;&#2375;&#2332;&#2352;(File Manager)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2357;&#2375;&#2348; &#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2352;(Web Browser)</span></p>\n"],
                    solution_en: " <p>13.(c) </span><span style=\"font-family:Cambria Math\">It allows users to manage files, folders and network connections, as well as search for files and related components.</span></p>",
                    solution_hi: "<p>13.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2351;&#2370;&#2395;&#2352;&#2381;&#2360; &#2325;&#2379; &#2347;&#2364;&#2366;&#2311;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2347;&#2364;&#2379;&#2354;&#2381;&#2337;&#2352;&#2379;&#2306; &#2324;&#2352; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344;&#2379;&#2306; &#2325;&#2379; &#2346;&#2381;&#2352;&#2348;&#2306;&#2343;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341; &#2347;&#2364;&#2366;&#2311;&#2354;&#2379;&#2306; &#2324;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2325;&#2377;&#2350;&#2381;&#2346;&#2379;&#2344;&#2375;&#2344;&#2381;&#2335;&#2360; &#2325;&#2368; &#2326;&#2379;&#2332; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "31",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> Which of the following contains specific rules and words that express the logical </span><span style=\"font-family: Cambria Math;\">steps of an algorithm? </span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2350;&#2375;&#2306; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\">(specific rules) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2358;&#2348;&#2381;&#2342; &#2361;&#2376;&#2306; &#2332;&#2379; &#2319;&#2354;&#2381;&#2327;&#2379;&#2352;&#2367;&#2341;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\">(algorithm) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">(logical) </span><span style=\"font-family: Cambria Math;\">&#2330;&#2352;&#2339; &#2325;&#2379; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Programming language</p>\n", "<p>Syntax</p>\n", 
                                "<p>Programming structure</p>\n", "<p>Logical chart</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332;(Programming language)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2306;&#2335;&#2375;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(Syntax) </span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; &#2360;&#2381;&#2335;&#2381;&#2352;&#2325;&#2381;&#2330;&#2352;(Programming structure)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2354;&#2377;&#2332;&#2367;&#2325;&#2354; &#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">(Logical chart) </span></p>\n"],
                    solution_en: "<p>14.(a) A programming language is a<strong>&nbsp;</strong>computer language&nbsp;that is used by&nbsp;programmers (developers) to communicate with computers. It is a set of instructions written in any specific language ( C, C++, Java, Python) to perform a specific task.</p>\n",
                    solution_hi: "<p>14.(a)<span style=\"font-family: Cambria Math;\"> &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; &#2349;&#2366;&#2359;&#2366; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2349;&#2366;&#2359;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2352; (&#2337;&#2375;&#2357;&#2354;&#2346;&#2352;&#2381;&#2360;) &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2366;&#2341; &#2360;&#2306;&#2357;&#2366;&#2342; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2325;&#2367;&#2360;&#2368; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2360;&#2368; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2349;&#2366;&#2359;&#2366; (C, C++, Java, Python) &#2350;&#2375;&#2306; &#2354;&#2367;&#2326;&#2375; &#2327;&#2319; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2379;&#2306; &#2325;&#2366; &#2319;&#2325; &#2360;&#2375;&#2335; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "31",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> This program is made by Microsoft and embedded with Windows and used to view Web documents____________-. </span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> ______ </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2381;&#2335; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2324;&#2352; &#2357;&#2367;&#2306;&#2337;&#2379;&#2332; &#2325;&#2375; &#2360;&#2366;&#2341; &#2319;&#2350;&#2381;&#2348;&#2375;&#2337; </span><span style=\"font-family: Cambria Math;\">(embedded with Windows) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2324;&#2352; &#2357;&#2375;&#2348; &#2337;&#2366;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335;&#2381;&#2360; </span><span style=\"font-family: Cambria Math;\">(web documents) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2342;&#2375;&#2326;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Netscape</p>\n", "<p>Outlook Express</p>\n", 
                                "<p>Internet Explorer</p>\n", "<p>MS-Word</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2344;&#2375;&#2335;&#2360;&#2381;&#2325;&#2375;&#2346; (Netscape)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2310;&#2313;&#2335;&#2354;&#2369;&#2325; &#2319;&#2325;&#2381;&#2360;&#2346;&#2381;&#2352;&#2375;&#2360; (Outlook Express)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2319;&#2325;&#2381;&#2360;&#2346;&#2381;&#2354;&#2379;&#2352;&#2352; (Internet Explorer)</span></p>\n", "<p><span style=\"font-weight: 400;\">MS-&#2357;&#2352;&#2381;&#2337; (Word)</span></p>\n"],
                    solution_en: "<p>15.(d)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">MS Word</span></strong><span style=\"font-family: Cambria Math;\"> is used to revise documents, write documents and help to view web pages. </span><span style=\"font-family: Cambria Math;\"><strong>Netscape</strong> Communications was a computer services company best known for its </span><span style=\"font-family: Cambria Math;\">Web browser, Navigator. </span><span style=\"font-family: Cambria Math;\"><strong>Outlook Express</strong> (OE)</span><span style=\"font-family: Cambria Math;\"> is an email client that lets the user save, send, receive and manage email messages.</span></p>\n",
                    solution_hi: "<p>15.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>MS Word</strong> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2337;&#2366;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335;&#2381;&#2360; &#2325;&#2379; &#2360;&#2306;&#2358;&#2379;&#2343;&#2367;&#2340; </span><span style=\"font-family: Cambria Math;\">(revise) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2337;&#2366;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335;&#2381;&#2360; &#2354;&#2367;&#2326;&#2344;&#2375; </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2357;&#2375;&#2348; &#2346;&#2375;&#2332; &#2357;&#2381;&#2351;&#2370; </span><span style=\"font-family: Cambria Math;\">(view web pages) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2350;&#2342;&#2342; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><span style=\"font-family: Cambria Math;\"><strong>&#2344;&#2375;&#2335;&#2360;&#2381;&#2325;&#2375;&#2346;</strong> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;&#2381;&#2351;&#2369;&#2344;&#2367;&#2325;&#2375;&#2358;&#2306;&#2360; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2352;&#2381;&#2357;&#2367;&#2360; &#2325;&#2306;&#2346;&#2344;&#2368; </span><span style=\"font-family: Cambria Math;\">(computer service company) </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368; &#2332;&#2379; &#2309;&#2346;&#2344;&#2375; &#2357;&#2375;&#2348; &#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352; </span><span style=\"font-family: Cambria Math;\">(web browser), </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2357;&#2367;&#2327;&#2375;&#2335;&#2352; </span><span style=\"font-family: Cambria Math;\">(navigator) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2354;&#2367;&#2319; &#2332;&#2366;&#2344;&#2368; &#2332;&#2366;&#2340;&#2368; &#2341;&#2368;&#2404; </span><strong><span style=\"font-family: Cambria Math;\">&#2310;&#2313;&#2335;&#2354;&#2369;&#2325; &#2319;&#2325;&#2381;&#2360;&#2346;&#2381;&#2352;&#2375;&#2360; </span></strong><span style=\"font-family: Cambria Math;\"><strong>(OE)</strong> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2312;&#2350;&#2375;&#2354; &#2325;&#2381;&#2354;&#2366;&#2311;&#2306;&#2335; </span><span style=\"font-family: Cambria Math;\">(email client) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2379; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2325;&#2379; &#2312;&#2350;&#2375;&#2354; &#2350;&#2376;&#2360;&#2375;&#2332; </span><span style=\"font-family: Cambria Math;\">(email messages) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2360;&#2375;&#2357; </span><span style=\"font-family: Cambria Math;\">(save) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2349;&#2375;&#2332;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2350;&#2376;&#2344;&#2375;&#2332; &#2325;&#2352;&#2344;&#2375; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "31",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">Which of the following is not true about RAM? </span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> RAM </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2360;&#2361;&#2368; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>RAM is the same as hard disk storage</p>\n", "<p>RAM is a temporary storage area</p>\n", 
                                "<p>RAM is volatile</p>\n", "<p>RAM is a primary memory</p>\n"],
                    options_hi: ["<p>RAM <span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2352;&#2381;&#2337; &#2337;&#2367;&#2360;&#2381;&#2325; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2325;&#2375; &#2360;&#2350;&#2366;&#2344; &#2361;&#2376;</span></p>\n", "<p>RAM <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2335;&#2375;&#2350;&#2381;&#2346;&#2352;&#2352;&#2368; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2319;&#2352;&#2367;&#2351;&#2366; &#2361;&#2376;</span></p>\n",
                                "<p>RAM <span style=\"font-family: Cambria Math;\">&#2357;&#2379;&#2354;&#2375;&#2335;&#2366;&#2311;&#2354; &#2361;&#2376;</span></p>\n", "<p>RAM <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2346;&#2381;&#2352;&#2366;&#2311;&#2350;&#2352;&#2368; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2361;&#2376;</span></p>\n"],
                    solution_en: "<p>16.(a) <strong>Random Access Memory (RAM) </strong>&rarr;<span style=\"font-family: Cambria Math;\"> It is also known as primary memory, that allows the CPU to read as well as write data and instructions into it.</span><span style=\"font-family: Cambria Math;\">It is a computer\'s memory where information is temporarily stored .It is not like hard disk which is permanent storage.</span></p>\n",
                    solution_hi: "<p>16.(a) <strong><span style=\"font-family: Cambria Math;\">&#2352;&#2376;&#2306;&#2337;&#2350; &#2319;&#2325;&#2381;&#2360;&#2375;&#2360; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; </span></strong><span style=\"font-family: Cambria Math;\"><strong>(RAM)</strong> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2311;&#2350;&#2352;&#2368; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2349;&#2368; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379; </span><span style=\"font-family: Cambria Math;\">CPU </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2346;&#2338;&#2364;&#2344;&#2375; &#2325;&#2375; &#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341; &#2311;&#2360;&#2350;&#2375;&#2306; &#2337;&#2375;&#2335;&#2366; &#2324;&#2352; &#2311;&#2306;&#2360;&#2381;&#2335;&#2381;&#2352;&#2325;&#2381;&#2358;&#2344; &#2325;&#2379; &#2354;&#2367;&#2326;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2368; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; &#2360;&#2370;&#2330;&#2344;&#2366; &#2325;&#2379; &#2309;&#2360;&#2381;&#2341;&#2366;&#2351;&#2368; &#2352;&#2370;&#2346; &#2360;&#2375; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361;&#2368;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2404; &#2351;&#2361; &#2361;&#2366;&#2352;&#2381;&#2337; &#2337;&#2367;&#2360;&#2381;&#2325; &#2325;&#2368; &#2340;&#2352;&#2361; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376; &#2332;&#2379; &#2346;&#2352;&#2350;&#2366;&#2344;&#2375;&#2306;&#2335; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "31",
                    question_en: "<p>17.<span style=\"font-family: Cambria Math;\"> In Excel, ......... allows users to bring together copies of workbooks that other users gave </span><span style=\"font-family: Cambria Math;\">worked on independently.</span></p>\n",
                    question_hi: "<p>17.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, .......... </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;&#2323;&#2306; &#2325;&#2379; &#2325;&#2366;&#2352;&#2381;&#2351;&#2346;&#2369;&#2360;&#2381;&#2340;&#2367;&#2325;&#2366;&#2323;&#2306; &#2325;&#2368; &#2313;&#2344; &#2346;&#2381;&#2352;&#2340;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2319;&#2325; &#2360;&#2366;&#2341; &#2354;&#2366;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2344; &#2346;&#2352; &#2309;&#2344;&#2381;&#2351; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;&#2323;&#2306; &#2344;&#2375; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352; &#2352;&#2370;&#2346; &#2360;&#2375; &#2325;&#2366;&#2350; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>copying</p>\n", "<p>merging</p>\n", 
                                "<p>pasting</p>\n", "<p>compiling</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2346;&#2351;&#2367;&#2344;&#2381;&#2327; </span><span style=\"font-family: Cambria Math;\">(copying) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2352;&#2381;&#2332;&#2367;&#2306;&#2327; (merging)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2346;&#2375;&#2360;&#2381;&#2335;&#2367;&#2306;&#2327; (pasting)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2346;&#2368;&#2354;&#2367;&#2306;&#2327; </span><span style=\"font-family: Cambria Math;\">(compiling) </span></p>\n"],
                    solution_en: "<p>17.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>Merging</strong> </span><span style=\"font-family: Cambria Math;\">combines two or more cells to create a new, larger cell</span><span style=\"font-family: Cambria Math;\">. This is a great way to create a label that spans several columns.</span><span style=\"font-family: Cambria Math;\">Excel </span><span style=\"font-family: Cambria Math;\">compiler</span><span style=\"font-family: Cambria Math;\"> works with the copy of the workbook, so your original workbook will not be altered.</span></p>\n",
                    solution_hi: "<p>17.(b) <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2348;&#2337;&#2364;&#2366; &#2360;&#2375;&#2354; &#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; <strong>&#2350;&#2352;&#2381;&#2332;&#2367;&#2306;&#2327;</strong></span><span style=\"font-family: Cambria Math;\"><strong>(merging)</strong> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379; &#2351;&#2366; &#2342;&#2379; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2360;&#2375;&#2354;&#2381;&#2360; &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2319;&#2325; &#2354;&#2375;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\">(label) </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2366; &#2319;&#2325; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2340;&#2352;&#2368;&#2325;&#2366; &#2361;&#2376; &#2332;&#2379; &#2325;&#2312; &#2325;&#2377;&#2354;&#2350;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">(columns) </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325; &#2347;&#2376;&#2354;&#2366; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2366;&#2311;&#2354;&#2352;</span><span style=\"font-family: Cambria Math;\">(compiler)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2325;&#2348;&#2369;&#2325; &#2325;&#2368; &#2325;&#2377;&#2346;&#2368; &#2325;&#2375; &#2360;&#2366;&#2341; &#2325;&#2366;&#2350; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, &#2340;&#2366;&#2325;&#2367; </span><span style=\"font-family: Cambria Math;\">&#2310;&#2346;&#2325;&#2368; &#2350;&#2370;&#2354; &#2357;&#2352;&#2381;&#2325;&#2348;&#2369;&#2325;</span><span style=\"font-family: Cambria Math;\">(workbook) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2366;&#2357; &#2344;&#2361;&#2368;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "31",
                    question_en: "<p>18.<span style=\"font-family: Cambria Math;\"> FORTRAN stands for_________ </span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2352;&#2335;&#2381;&#2352;&#2366;&#2344; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">_________</span></p>\n",
                    options_en: ["<p>Formal Translation</p>\n", "<p>Formative Translation</p>\n", 
                                "<p>Formal Transaction</p>\n", "<p>Formula Translation</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2347;&#2377;&#2352;&#2381;&#2350;&#2354; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2354;&#2375;&#2358;&#2344;(Formal Translation)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2352;&#2381;&#2350;&#2335;&#2367;&#2357; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2354;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\">(Formative Translation) </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2347;&#2377;&#2352;&#2381;&#2350;&#2354; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2325;&#2381;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\">(Formal Transaction) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2347;&#2366;&#2352;&#2381;&#2350;&#2370;&#2354;&#2366; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2354;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\">(Formula Translation) </span></p>\n"],
                    solution_en: "<p>18.(d)<span style=\"font-family: Cambria Math;\"> FORTRAN stands for </span><span style=\"font-family: Cambria Math;\"><strong>Formula Translation</strong>.</span></p>\n",
                    solution_hi: "<p>18.(d) &#2347;&#2379;&#2352;&#2335;&#2381;&#2352;&#2366;&#2344; &#2325;&#2366; &#2350;&#2340;&#2354;&#2348; <strong>&#2347;&#2377;&#2352;&#2381;&#2350;&#2370;&#2354;&#2366; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2354;&#2375;&#2358;&#2344;(formula translation) </strong>&#2361;&#2376;&#2404;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "31",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> Which of the following is the communication protocol that sets the standard used by every computer that accesses Web-based information?</span></p>\n",
                    question_hi: "<p>19. <span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2360;&#2306;&#2330;&#2366;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354; &#2361;&#2376; &#2332;&#2379; &#2357;&#2375;&#2348;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2366;&#2352;&#2367;&#2340; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2340;&#2325; &#2346;&#2361;&#2369;&#2305;&#2330;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2319; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2350;&#2366;&#2344;&#2325; &#2360;&#2375;&#2335; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>XML</p>\n", "<p>DML</p>\n", 
                                "<p>HTTP</p>\n", "<p>HTML</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2319;&#2325;&#2381;&#2360;&#2319;&#2350;&#2319;&#2354;( XML)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2337;&#2368;&#2319;&#2350;&#2319;&#2354;(DML)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2319;&#2330;&#2335;&#2368;&#2335;&#2368;&#2346;&#2368;(HTTP)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2330;&#2335;&#2368;&#2319;&#2350;&#2319;&#2354;(HTML)</span></p>\n"],
                    solution_en: "<p>19.(c)<span style=\"font-family: Cambria Math;\"> Hypertext Transfer Protocol (HTTP) is the foundation of data communication for the worldwide web.</span><strong><span style=\"font-family: Cambria Math;\">HTML</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; (Hypertext Markup Language) is the standard markup language for Web pages.</span></p>\n",
                    solution_hi: "<p>19.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2311;&#2346;&#2352;&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354; </span><span style=\"font-family: Cambria Math;\">(HTTP) </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2357;&#2381;&#2351;&#2366;&#2346;&#2368; &#2357;&#2375;&#2348; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2375;&#2335;&#2366; &#2360;&#2306;&#2330;&#2366;&#2352; &#2325;&#2368; &#2344;&#2368;&#2306;&#2357; &#2361;&#2376;&#2404; </span><strong><span style=\"font-family: Cambria Math;\">HTML</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; (</span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2311;&#2346;&#2352;&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2350;&#2366;&#2352;&#2381;&#2325;&#2309;&#2346; &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2348; &#2346;&#2375;&#2332;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2350;&#2366;&#2344;&#2325; &#2350;&#2366;&#2352;&#2381;&#2325;&#2309;&#2346; &#2349;&#2366;&#2359;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20.<span style=\"font-family: Cambria Math;\"> The Analytical Engine developed during the first generation of computers used ......... as a memory unit. </span></p>\n",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2344;&#2366;&#2354;&#2367;&#2335;&#2367;&#2325;&#2354; &#2311;&#2306;&#2332;&#2344; &#2325;&#2366; &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2351;&#2370;&#2344;&#2367;&#2335; </span><span style=\"font-family: Cambria Math;\">(memory unit) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; </span><span style=\"font-family: Cambria Math;\">&hellip;&hellip;.. </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2361;&#2369;&#2310;&#2404;</span></p>\n",
                    options_en: ["<p>RAM</p>\n", "<p>floppies</p>\n", 
                                "<p>cards</p>\n", "<p>counter wheels</p>\n"],
                    options_hi: ["<p>RAM</p>\n", "<p><span style=\"font-weight: 400;\">&#2347;&#2381;&#2354;&#2377;&#2346;&#2368;&#2332;&#2364; (floppies)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2325;&#2366;&#2352;&#2381;&#2337;&#2381;&#2360; (cards)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2366;&#2313;&#2306;&#2335;&#2352; &#2357;&#2381;&#2361;&#2368;&#2354;&#2381;&#2360; (counter wheels)</span></p>\n"],
                    solution_en: "<p>20.(d)<span style=\"font-family: Cambria Math;\"> During the first</span><span style=\"font-family: Cambria Math;\"> generation of computers, analytical engines were introduced. These first generation of computers used </span><strong><span style=\"font-family: Cambria Math;\">counter wheels</span></strong><span style=\"font-family: Cambria Math;\"> as memory units to store data. </span></p>\n",
                    solution_hi: "<p>20.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2344;&#2366;&#2354;&#2367;&#2335;&#2367;&#2325;&#2354; &#2311;&#2306;&#2332;&#2344; &#2346;&#2375;&#2358; &#2325;&#2367;&#2319; &#2327;&#2319; &#2341;&#2375;&#2404; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2368; &#2351;&#2375; &#2346;&#2361;&#2354;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2360;&#2381;&#2335;&#2379;&#2352; </span><span style=\"font-family: Cambria Math;\">(store) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2351;&#2370;&#2344;&#2367;&#2335; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; </span><strong><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2313;&#2306;&#2335;&#2352; &#2357;&#2381;&#2361;&#2368;&#2354;&#2381;&#2360; </span></strong><span style=\"font-family: Cambria Math;\"><strong>(counter wheels)</strong> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2368; &#2341;&#2368;&nbsp;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>