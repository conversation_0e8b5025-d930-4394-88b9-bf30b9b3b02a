<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: " <p>1. The Women and Child Development Ministry has classified all its major programmes under which umbrella scheme? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">1. महिला और बाल विकास मंत्रालय ने अपने सभी प्रमुख कार्यक्रमों को किस छत्र योजना के तहत वर्गीकृत किया है?</span></p>",
                    options_en: [" <p> Mission Poshan 2.0</span></p>", " <p> Mission Vatsalya</span></p>", 
                                " <p> Mission Shakti</span></p>", " <p> All of these </span></p>"],
                    options_hi: ["<p>मिशन पोशन 2.0</p>", "<p>मिशन वात्सल्य</p>",
                                "<p>मिशन शक्ति</p>", "<p>ये सभी</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) For effective implementation of various schemes and programmes of the Ministry of Women and Child Development, all major schemes of the Ministry have been classified under 3 umbrella schemes viz. Mission Poshan 2.0, Mission Vatsalya and Mission Shakti.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) महिला एवं बाल विकास मंत्रालय की विभिन्न योजनाओं और कार्यक्रमों के प्रभावी क्रियान्वयन के लिए मंत्रालय की सभी प्रमुख योजनाओं को 3 अम्ब्रेला योजनाओं के तहत वर्गीकृत किया गया है। मिशन पोषण 2.0, मिशन वात्सल्य और मिशन शक्ति।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: " <p>2. Who is credited with the invention of the modern periodic table? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">2. आधुनिक आवर्त सारणी के आविष्कार का श्रेय किसे दिया जाता है ? </span></p>",
                    options_en: [" <p> Nobel </span></p>", " <p> Mendel </span></p>", 
                                " <p> Lavoisier </span></p>", " <p> Mendeleev </span></p>"],
                    options_hi: ["<p>नोबेल</p>", "<p>मेंडेल</p>",
                                "<p>लैवोजियर</p>", "<p>मेंडलीव</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) Mendeleev  is credited with creating the modern periodic table, although his table was based on increasing atomic mass rather than atomic number. Mendeleev\'s table grouped elements according to common properties and periodic trends.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) मेंडेलीव को आधुनिक आवर्त सारणी बनाने का श्रेय दिया जाता है, हालांकि उनकी तालिका परमाणु संख्या के बजाय बढ़ते परमाणु द्रव्यमान पर आधारित थी। मेंडलीफ की तालिका ने तत्वों को सामान्य गुणों और आवधिक प्रवृत्तियों के अनुसार वर्गीकृत किया।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: " <p>3. In which of the following Years, the Scheduled Castes and the Scheduled Tribes (Prevention of Atrocities) Act was passed? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">3. निम्न में से किस वर्ष, अनुसूचित जाति एवं अनुसूचित जनजाति ( क्रूरता से रोकथाम ) अधिनियम पारित किया गया था ? </span></p>",
                    options_en: [" <p> 1989</span></p>", " <p> 1999</span></p>", 
                                " <p> 2001</span></p>", " <p> 2010</span></p>"],
                    options_hi: ["<p>1989</p>", "<p>1999</p>",
                                "<p>2001</p>", "<p>2010</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a)  In 1989, the Scheduled Castes and the Scheduled Tribes (Prevention of Atrocities) Act was passed. An Act was implemented to prevent the commission of offences of atrocities if any practiced against the members of the Scheduled Castes and the Scheduled Tribes.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) 1989 में, अनुसूचित जाति और अनुसूचित जनजाति (अत्याचार निवारण) अधिनियम पारित किया गया था। अनुसूचित जातियों और अनुसूचित जनजातियों के सदस्यों के खिलाफ किए जाने वाले अत्याचारों के अपराधों को रोकने के लिए एक अधिनियम लागू किया गया था।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: " <p>4. Who has been honoured with the Devi Shankar Awasthi</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Times New Roman\">Award for the book, ‘Pitra-Vadh’? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">4. पुस्तक &lsquo;पितृ-वध&rsquo; के लिए देवी शंकर अवस्थी पुरस्कार से किसे सम्मानित किया गया है?</span></p>",
                    options_en: [" <p> Nandkishore Acharya</span></p>", " <p> Ashok Vajpayee</span></p>", 
                                " <p> Ashutosh Bhardwaj</span></p>", " <p> Rajendra Kumar </span></p>"],
                    options_hi: ["<p>नंदकिशोर आचार्य</p>", "<p>अशोक वाजपेयी</p>",
                                "<p>आशुतोष भारद्वाज</p>", "<p>राजेंद्र कुमार</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) The famous Devi Shankar Awasthi Award has been awarded to the prolific Hindi prose, journalist, and critic Ashutosh Bhardwaj. This book has been published in English under the name of ‘The Death Trap’.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) प्रसिद्ध हिंदी गद्य, पत्रकार और आलोचक आशुतोष भारद्वाज को प्रसिद्ध देवी शंकर अवस्थी पुरस्कार से सम्मानित किया गया है। यह किताब अंग्रेजी में \'द डेथ ट्रैप\' के नाम से प्रकाशित हुई है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: " <p>5. When is National Technology Day observed? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">5. राष्ट्रीय प्रौद्योगिकी दिवस कब मनाया जाता है?</span></p>",
                    options_en: [" <p> May 10 </span></p>", " <p> May 11 </span></p>", 
                                " <p> May 12</span></p>", " <p> May 13</span></p>"],
                    options_hi: ["<p>10 मई</p>", "<p>11 मई</p>",
                                "<p>12 मई</p>", "<p>13 मई</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) May 11 was declared as ‘National Technology Day’ by former Prime Minister of India, Atal Bihari Vajpayee.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) 11 मई को भारत के पूर्व प्रधान मंत्री अटल बिहारी वाजपेयी द्वारा \'राष्ट्रीय प्रौद्योगिकी दिवस\' के रूप में घोषित किया गया था।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: " <p>6. The International Day of Peace is observed every year on _____. </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">6. अंतर्राष्ट्रीय शांति दिवस हर वर्ष ______ को मनाया जाता है | </span></p>",
                    options_en: [" <p> 25th September      </span></p>", " <p> 21st September</span></p>", 
                                " <p> 23rd September      </span></p>", " <p> 20th September </span></p>"],
                    options_hi: ["<p>25 सितंबर</p>", "<p>21 सितंबर</p>",
                                "<p>23 सितंबर</p>", "<p>20 सितम्बर</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) The International Day of Peace is observed every year on 21st of September to commemorate and  strengthen the ideals of peace both within and among all nations and peoples.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) शांति का अंतर्राष्ट्रीय दिवस हर साल 21 सितंबर को सभी देशों और लोगों के बीच शांति के आदर्शों को मनाने और मजबूत करने के लिए मनाया जाता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: " <p>7. The Abraham Koshy Committee was constituted for which organisation? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">7. अब्राहम </span><span style=\"font-family: Baloo;\">कोशी</span><span style=\"font-family: Baloo;\"> समिति का गठन किस संगठन के लिए किया गया था ? </span></p>",
                    options_en: [" <p> RBI </span></p>", " <p> SEBI</span></p>", 
                                " <p> IRDAI</span></p>", " <p> TRAI</span></p>"],
                    options_hi: ["<p>RBI</p>", "<p>SEBI</p>",
                                "<p>IRDAI</p>", "<p>TRAI</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) The Abraham Koshy Committee is an eight member panel that serves as an advisory body for the SEBI/ Securities and Exchange Board of India.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)अब्राहम कोशी समिति एक आठ सदस्यीय पैनल है जो सेबी / भारतीय प्रतिभूति और विनिमय बोर्ड के लिए एक सलाहकार निकाय के रूप में कार्य करता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: " <p>8. As per the US constitution, _________ is given the sole power to impeach a government official. </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">8. अमेरिकी संविधान के अनुसार, किसी सरकारी अधिकारी पर अभियोग चलाने की व्यक्तिगत शक्ति _____ को दी गयी है | </span></p>",
                    options_en: [" <p> House of Representatives </span></p>", " <p> House of Commons </span></p>", 
                                " <p> Senate </span></p>", " <p> House of Lords </span></p>"],
                    options_hi: ["<p>हाउस ऑफ़ रिप्रेजेंटेटिव</p>", "<p>हाउस ऑफ़ कॉमन्स</p>",
                                "<p>सीनेट</p>", "<p>हाउस ऑफ़ लॉर्ड्स</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a) According to Article II, section 4 of the US Constitution, \"The President, Vice President and all Civil Officers of the United States shall be removed from Office on Impeachment for, and Conviction of, Bribery, Treason, or any other high Crimes and Misdemeanors.\" The House of Representatives has the sole power to impeach a government official and the Senate will hold the trials for impeachment.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) अनुच्छेद II के अनुसार, अमेरिकी संविधान की धारा 4, \"राष्ट्रपति, उपराष्ट्रपति और संयुक्त राज्य के सभी सिविल अधिकारियों को रिश्वत, राजद्रोह, या किसी अन्य उच्च अपराध और दुराचार के लिए महाभियोग पर कार्यालय से हटा दिया जाएगा। \" प्रतिनिधि सभा के पास एक सरकारी अधिकारी पर महाभियोग चलाने की एकमात्र शक्ति है और सीनेट महाभियोग के लिए परीक्षण करेगी।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: " <p>9.Tulsidas, the author of Ramcharitmanas, was a contemporary of which of the following rulers? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">9. रामचरितमानस के लेखक तुलसीदास निम्न में से किस शासक के समकालीन थे </span></p>",
                    options_en: [" <p> Akbar </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> Humayun </span></p>", 
                                " <p> Shah jahan </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> Sher Shah Suri </span></p>"],
                    options_hi: ["<p>अकबर</p>", "<p>हुमायूँ</p>",
                                "<p>शाहजहाँ</p>", "<p>शेरशाह सूरी</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) Tulsidas, the author of Ramcharitmanas was a contemporary of Akbar.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) रामचरितमानस के रचयिता तुलसीदास अकबर के समकालीन थे।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: " <p>10. Who amongst the following was also a poet and a musician and took delight in his title of Kaviraja or The king of Poets? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">10. निम्न में से कौन एक कवि और </span><span style=\"font-family: Baloo;\">संगीतकार</span><span style=\"font-family: Baloo;\"> भी था तथा उसे कविराज की उपाधि स्वीकार करने में प्रसन्नता होती थी ? </span></p>",
                    options_en: [" <p> Chandragupta </span></p>", " <p> Samudragupta </span></p>", 
                                " <p> Skandagupta </span></p>", " <p> None of the above </span></p>"],
                    options_hi: ["<p>चन्द्रगुप्त</p>", "<p>समुद्रगुप्त</p>",
                                "<p>स्कंदगुप्त</p>", "<p>उपरोक्त में से कोई नहीं</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Samudragupta had great attachment to art literature and learning. He composed many poems in Sanskrit and earned the title of Kaviraj (the King of the poets).</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) समुद्रगुप्त को कला साहित्य और विद्या से बहुत लगाव था। उन्होंने संस्कृत में कई कविताओं की रचना की और कविराज (कवियों के राजा) की उपाधि अर्जित की।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which Union Territory has become India&rsquo;s first 100% organic Union Territory?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">11. कौन सा केंद्र शासित प्रदेश भारत का पहला 100% जैविक केंद्र शासित प्रदेश बन गया है?</span></p>",
                    options_en: ["<p>Ladakh</p>", "<p>Jammu and Kashmir</p>", 
                                "<p>Lakshadweep</p>", "<p>Puducherry</p>"],
                    options_hi: ["<p>लद्दाख</p>", "<p>जम्मू और कश्मीर</p>",
                                "<p>लक्षद्वीप</p>", "<p>पुदुचेरी</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Lakshadweep is the first Union Territory to become 100% organic as all farming is carried out without the use of synthetic fertilisers and pesticides, providing access to safer food choices and making agriculture a more environmental-friendly activity</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) लक्षद्वीप 100% जैविक बनने वाला पहला केंद्र शासित प्रदेश है क्योंकि सभी खेती सिंथेटिक उर्वरकों और कीटनाशकों के उपयोग के बिना की जाती है, सुरक्षित भोजन विकल्पों तक पहुंच प्रदान करती है और कृषि को पर्यावरण के अनुकूल गतिविधि बनाती है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: " <p>12. The working of quartz crystal in the watch is based on </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">12. घड़ी में क्वार्टज़ क्रिस्टल की कार्य पद्धति किस पर आधारित होती है ? </span></p>",
                    options_en: [" <p> Johnson effect </span></p>", " <p> Photoelectric effect </span></p>", 
                                " <p> Edison effect </span></p>", " <p> Piezoelectric effect </span></p>"],
                    options_hi: ["<p>जॉनसन प्रभाव</p>", "<p>प्रकाश विद्युत प्रभाव</p>",
                                "<p>एडिसन प्रभाव</p>", "<p>दाबविद्युतिकी प्रभाव</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) The ability to convert voltage to and from mechanical stress is called piezoelectricity. Quartz crystals maintain a precise frequency standard, which helps to regulate the movement of a watch or clock, thus making the timepieces very accurate.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) वोल्टेज को यांत्रिक तनाव से और में बदलने की क्षमता को पीजोइलेक्ट्रिकिटी कहा जाता है। क्वार्ट्ज क्रिस्टल एक सटीक आवृत्ति मानक बनाए रखते हैं, जो घड़ी या घड़ी की गति को विनियमित करने में मदद करता है, इस प्रकार टाइमपीस को बहुत सटीक बनाता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: " <p>13. Who was appointed as the Governor of Bengal after the friendship treaty with Alauddin Husain Shah? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">13. अलाउद्दीन हुसैन शाह के साथ मित्रता संधि करने के बाद बंगाल का गवर्नर निम्न में से किसे नियुक्त किया गया था ? </span></p>",
                    options_en: [" <p> </span><span style=\"font-family:Times New Roman\">Sikandar Lodhi</span></p>", " <p> Bahlol Lodhi </span></p>", 
                                " <p> </span><span style=\"font-family:Times New Roman\">Dariya</span><span style=\"font-family:Times New Roman\"> Khan Lohani </span></p>", " <p> Dilawar Khan</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Baloo;\">सिकंदर लोदी</span></p>", "<p>बहलोल लोदी</p>",
                                "<p>दरिया खान लोहानी</p>", "<p>दिलावर खान</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)  </span><span style=\"font-family:Times New Roman\">Dariya</span><span style=\"font-family:Times New Roman\"> Khan Lohani conquered Bihar and Raja of Tirhut and concluded a friendship treaty with Alauddin Hussain Shah of Bengal. Dariya Khan was appointed as the Governor of Bengal.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)दरिया खान लोहानी ने बिहार और तिरहुत के राजा पर विजय प्राप्त की और बंगाल के अलाउद्दीन हुसैन शाह के साथ मित्रता संधि की। दरिया खान को बंगाल का राज्यपाल नियुक्त किया गया।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: " <p>14. Which one of the following got the Bharat Ratna Award before becoming the president of India? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">14. निम्न में से किन्हें भारत का राष्ट्रपति बनने से पहले भारत रत्न पुरस्कार मिला था </span></p>",
                    options_en: [" <p> Dr V V Giri   </span></p>", " <p> Pratibha Patil </span></p>", 
                                " <p> APJ Abdul Kalam</span></p>", " <p> Rajendra Prasad</span></p>"],
                    options_hi: ["<p>डॉ. वी वी गिरी</p>", "<p>प्रतिभा पाटिल</p>",
                                "<p>एपीजे अब्दुल कलाम</p>", "<p>राजेंद्र प्रसाद</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) APJ Abdul Kalam was the third President of India to have been honoured with a Bharat Ratna, India\'s highest civilian honour, before becoming the President. Dr. Sarvepalli Radhakrishnan (1954) and Dr. Zakir Hussain (1963) were the earlier recipients of Bharat Ratna who later became the President of India.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) एपीजे अब्दुल कलाम भारत के तीसरे राष्ट्रपति थे जिन्हें राष्ट्रपति बनने से पहले भारत के सर्वोच्च नागरिक सम्मान भारत रत्न से सम्मानित किया गया था। डॉ. सर्वपल्ली राधाकृष्णन (1954) और डॉ. जाकिर हुसैन (1963) भारत रत्न के पहले प्राप्तकर्ता थे जो बाद में भारत के राष्ट्रपति बने।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: " <p>15. Which one of the following rivers passes through the Grand Canyon in the United States? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">15. निम्न में से कौन सी नदी संयुक्त राज्य अमेरिका में ग्रैंड </span><span style=\"font-family: Baloo;\">कैन्यन</span><span style=\"font-family: Baloo;\"> से गुजरती है ? </span></p>",
                    options_en: [" <p> Colorado </span></p>", " <p> Mississippi  </span></p>", 
                                " <p> Hudson </span></p>", " <p> Mackenzie </span></p>"],
                    options_hi: ["<p>कोलोराडो</p>", "<p>मिसीसिपी</p>",
                                "<p>हडसन</p>", "<p>मैकेंजी</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a)  Colorado River is famous for carving out the Grand Canyon over the course of millions of years.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) कोलोराडो नदी लाखों वर्षों के दौरान ग्रैंड कैन्यन को तराशने के लिए प्रसिद्ध है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: " <p>16. Which among the following is India’s first Expressway? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">16. निम्न में से कौन भारत का पहला एक्सप्रेस वे है ? </span></p>",
                    options_en: [" <p> Mumbai-Pune Expressway</span></p>", " <p>Ahmedabad-Vadodara Expressway  </span></p>", 
                                " <p> Delhi-Gurgaon Expressway </span></p>", " <p> Jaipur-Kishangarh Expressway</span></p>"],
                    options_hi: ["<p>मुंबई-पुणे एक्सप्रेसवे</p>", "<p>अहमदाबाद-वड़ोदरा एक्सप्रेसवे</p>",
                                "<p>दिल्ली-गुड़गाँव एक्सप्रेसवे</p>", "<p>जयपुर-किशनगढ़ एक्सप्रेसवे</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a) The Mumbai-Pune Expressway is India&rsquo;s first six-lane, concrete, high-speed, tolled, access-controlled, 94.5 km long expressway.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) मुंबई-पुणे एक्सप्रेसवे भारत का पहला छह-लेन, कंक्रीट, हाई-स्पीड, टोल, एक्सेस-नियंत्रित, 94.5 किमी लंबा एक्सप्रेसवे है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: " <p>17. The following river originates in Rajasthan and disappears in Gujarat? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">17. इनमें से कौन सी नदी राजस्थान से निकलती है और गुजरात में गायब हो जाती है ? </span></p>",
                    options_en: [" <p> Banas </span></p>", " <p> Luni </span></p>", 
                                " <p> Tapi </span></p>", " <p> Mahi </span></p>"],
                    options_hi: ["<p>बनास</p>", "<p>लूनी</p>",
                                "<p>तापी</p>", "<p>माहि</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) The Luni is a river of western Rajasthan state, India. It originates in the Pushkar valley of the Aravalli Range, near Ajmer and ends in the marshy lands of Rann of Kutch in Gujarat.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) लूनी भारत के पश्चिमी राजस्थान राज्य की एक नदी है। यह अजमेर के पास अरावली रेंज की पुष्कर घाटी से निकलती है और गुजरात में कच्छ के रण की दलदली भूमि में समाप्त होती है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: " <p>18. When is National Consumer Day observed? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">18. राष्ट्रीय उपभोक्ता दिवस कब मनाया जाता है ? </span></p>",
                    options_en: [" <p> March 15    </span></p>", " <p> December 24</span></p>", 
                                " <p> March 14 </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> December 25 </span></p>"],
                    options_hi: ["<p>15 मार्च</p>", "<p>24 दिसंबर</p>",
                                "<p>14 मार्च</p>", "<p>25 दिसंबर</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) The National Consumer Day is observed every year in India on 24th December. On this day the Consumer Protection Act, 1986 had received the assent of the President.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)भारत में हर साल 24 दिसंबर को राष्ट्रीय उपभोक्ता दिवस मनाया जाता है। इस दिन उपभोक्ता संरक्षण अधिनियम, 1986 को राष्ट्रपति की स्वीकृति प्राप्त हुई थी।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: " <p>19.Who has been sworn in as the new Chief Justice of Sikkim High Court? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">19. सिक्किम उच्च न्यायालय के नए मुख्य न्यायाधीश के रूप में किसने शपथ ली है?</span></p>",
                    options_en: [" <p> Justice Jitendra Kumar Maheshwari</span></p>", " <p> Justice Arup Kumar Goswami</span></p>", 
                                " <p> Justice Hima Kohli</span></p>", " <p> Justice Pankaj Mithal</span></p>"],
                    options_hi: ["<p>जस्टिस जितेंद्र कुमार माहेश्वरी</p>", "<p>जस्टिस अरूप कुमार गोस्वामी</p>",
                                "<p>जस्टिस हिमा कोहली</p>", "<p>जस्टिस पंकज मिथल</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) Justice Jitendra Kumar Maheshwari was sworn in as the new Chief Justice of Sikkim High Court. Governor Ganga Prasad administered the oath of office to Justice Maheshwari during a function held in Raj Bhavan.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) न्यायमूर्ति जितेंद्र कुमार माहेश्वरी ने सिक्किम उच्च न्यायालय के नए मुख्य न्यायाधीश के रूप में शपथ ली। राज्यपाल गंगा प्रसाद ने राजभवन में आयोजित एक समारोह के दौरान न्यायमूर्ति माहेश्वरी को पद की शपथ दिलाई।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: " <p>20. Foramen Magnum is an aperture found in the </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">20. फोरामेन मैग्नम एक छिद्र है जो ______ में पाया जाता है | </span></p>",
                    options_en: [" <p> ear </span></p>", " <p> lung</span></p>", 
                                " <p> skull </span></p>", " <p> girdle </span></p>"],
                    options_hi: ["<p>कान</p>", "<p>फेफड़ा</p>",
                                "<p>खोपड़ी</p>", "<p>करधनी</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) It is the hole in the base of the skull through which the spinal cord passes</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)यह खोपड़ी के आधार में छेद है जिसके माध्यम से रीढ़ की हड्डी गुजरती है</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: " <p>21. Plant synthesize protein from </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">21. पौधे प्रोटीन का संश्लेषण _____ से करते हैं | </span></p>",
                    options_en: [" <p> starch </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> sugar</span></p>", 
                                " <p> fatty acids </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> amino acids </span></p>"],
                    options_hi: ["<p>स्टार्च</p>", "<p>शर्करा</p>",
                                "<p>वसा अम्ल</p>", "<p>एमिनो अम्ल</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) Proteins are polymers of amino acids. So, plants are also used to synthesise protein from amino acids.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) प्रोटीन अमीनो एसिड के बहुलक हैं। तो, पौधों का उपयोग अमीनो एसिड से प्रोटीन को संश्लेषित करने के लिए भी किया जाता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: " <p>22. Gini Coefficient or Gini Ratio can be associated with which one of the following measurements in an economy? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">22. गिनी गुणांक या गिनी अनुपात का संबंध अर्थशास्त्र के निम्न में से किस माप से होता है ? </span></p>",
                    options_en: [" <p> Rate of inflation</span></p>", " <p> Poverty index</span></p>", 
                                " <p> Income inequality</span></p>", " <p> Personal income</span></p>"],
                    options_hi: ["<p>मुद्रास्फीति की दर</p>", "<p>गरीबी सूचकांक</p>",
                                "<p>आय में असमानता</p>", "<p>व्यक्तिगत आय</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) The Gini coefficient is one of the most frequently used measures of economic inequality. </span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) गिनी गुणांक आर्थिक असमानता के सबसे अधिक इस्तेमाल किए जाने वाले उपायों में से एक है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: " <p>23. Which country has recently announced the suspension of high-level military and political contacts with Myanmar? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">23. हाल ही में किस देश ने म्यांमार के साथ उच्च स्तरीय सैन्य और राजनीतिक संपर्कों को निलंबित करने की घोषणा की है?</span></p>",
                    options_en: [" <p> Canada</span></p>", " <p> India</span></p>", 
                                " <p> New Zealand</span></p>", " <p> Denmark</span></p>"],
                    options_hi: ["<p>कनाडा</p>", "<p>भारत</p>",
                                "<p>न्यूजीलैंड</p>", "<p>डेनमार्क</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) New Zealand announced the suspension of high-level military and political contacts with Myanmar. It is the first major international move to isolate the country\'s ruling junta following a coup</span><span style=\"font-family:Times New Roman\">.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) न्यूजीलैंड ने म्यांमार के साथ उच्च स्तरीय सैन्य और राजनीतिक संपर्कों को निलंबित करने की घोषणा की। तख्तापलट के बाद देश की सत्ताधारी जनता को अलग-थलग करने वाला यह पहला बड़ा अंतरराष्ट्रीय कदम है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: " <p>24. In science, what is the name for the classification of plants and animals? </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">24. विज्ञान में, पौधों एवं जंतुओं के वर्गीकरण को क्या नाम दिया गया है ? </span></p>",
                    options_en: [" <p> taxonomy </span></p>", " <p> taxidermy </span></p>", 
                                " <p> anatomy </span></p>", " <p> phytotomy</span></p>"],
                    options_hi: ["<p>टैक्सोनोमी</p>", "<p>टैक्सीडर्मी</p>",
                                "<p>एनाटोमी</p>", "<p>फाइटोटॉमी</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) In biology, taxonomy is the science of naming, defining and classifying groups of biological organisms on the basis of shared characteristics.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)जीव विज्ञान में, वर्गीकरण साझा विशेषताओं के आधार पर जैविक जीवों के समूहों के नामकरण, परिभाषित और वर्गीकृत करने का विज्ञान है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25. The Muhammadan Anglo-Oriental College later became the </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">25. मुहम्मदन एंग्लो-ओरिएंटल कॉलेज आगे चलकर क्या बना ? </span></p>",
                    options_en: [" <p> Osmania University </span></p>", " <p> Jamia Millia Islamia University</span></p>", 
                                " <p> Barkatullah University </span></p>", " <p> Aligarh Muslim University </span></p>"],
                    options_hi: ["<p>ओस्मानिया विश्वविद्यालय</p>", "<p>जामिया मिलिया इस्लामिया विश्वविद्यालय</p>",
                                "<p>बरकतुल्लाह विश्वविद्यालय</p>", "<p>अलीगढ मुस्लिम विश्वविद्यालय</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Aligarh Muslim University is a public central university, which was originally established by Sir Syed Ahmad Khan as the Muhammadan Anglo-Oriental College in 1875. Muhammadan Anglo-Oriental College became Aligarh Muslim University in 1920, following the Aligarh Muslim University Act.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) अलीगढ़ मुस्लिम विश्वविद्यालय एक सार्वजनिक केंद्रीय विश्वविद्यालय है, जिसे मूल रूप से सर सैयद अहमद खान द्वारा 1875 में मुहम्मदन एंग्लो-ओरिएंटल कॉलेज के रूप में स्थापित किया गया था। मुहम्मदन एंग्लो-ओरिएंटल कॉलेज 1920 में अलीगढ़ मुस्लिम विश्वविद्यालय अधिनियम के बाद अलीगढ़ मुस्लिम विश्वविद्यालय बन गया।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>