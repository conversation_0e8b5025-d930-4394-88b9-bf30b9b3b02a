<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1. In MS word 365, While working with the text the thesaurus tool is used for:</p>",
                    question_hi: "<p>1. एमएस वर्ड 365 (MS Word 365) में टेक्स्ट के साथ काम करते समय, थिसॉरस टूल का उपयोग किसके लिए किया जाता है?</p>",
                    options_en: ["<p>finding repeated words in the document</p>", "<p>corrects misspelling word</p>", 
                                "<p>correct the format of the text</p>", "<p>check for synonyms and antonyms</p>"],
                    options_hi: ["<p>डॉक्&zwj;यूमेंट में दोहराए गए शब्दों को ढूँढने के लिए</p>", "<p>गलत वर्तनी वाले शब्द को सही करने के लिए</p>",
                                "<p>टेक्स्ट के फॉर्मेट को ठीक करने के लिए</p>", "<p>सेनोनेम (synonyms ) और एंटोनेम(antonyms)</p>"],
                    solution_en: "<p>1.(d) <strong>check for synonyms and antonyms.</strong> Ctrl + F - finding repeated words in the document. F7 - Used to spell check and grammar check a document in Microsoft Apps (e.g. Word).</p>",
                    solution_hi: "<p>1.(d) <strong>सेनोनेम (synonyms ) और एंटोनेम (antonyms) ।</strong> Ctrl + F - डॉक्यूमेंट में दोहराए (repeated) गए शब्दों को ढूँढना (find)। F7 - माइक्रोसॉफ्ट ऐप्स (जैसे वर्ड) में किसी डॉक्यूमेंट की वर्तनी जांच (spell ) और व्याकरण (grammar) जांच के लिए उपयोग किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2. What feature in Microsoft Word 365 allows you to create a numbered list with different levels of indentation?</p>",
                    question_hi: "<p>2. माइक्रोसॉफ्ट वर्ड 365 में कौन सी सुविधा आपको इंडेंटेशन (indentation) के विभिन्न स्तरों के साथ एक क्रमांकित सूची बनाने की अनुमति देती है?</p>",
                    options_en: ["<p>Multi-hierarchy numbering</p>", "<p>Multi-indenting numbering</p>", 
                                "<p>Multilevel numbering</p>", "<p>Multi-spaced numbering</p>"],
                    options_hi: ["<p>Multi-hierarchy numbering</p>", "<p>Multi-indenting numbering</p>",
                                "<p>Multilevel numbering</p>", "<p>Multi-spaced numbering</p>"],
                    solution_en: "<p>2.(c) <strong>Multilevel numbering.</strong> It allows you to define a numbering scheme with different levels (e.g., headings, subheadings, sub-subheadings) and control their indentation for a clear hierarchical structure.</p>",
                    solution_hi: "<p>2.(c) <strong>मल्टीलेवल नंबरिंग</strong> (Multilevel numbering) । यह आपको विभिन्न स्तरों (जैसे, शीर्षक, उपशीर्षक, उप-उपशीर्षक) के साथ एक क्रमांकन योजना को परिभाषित करने और एक स्पष्ट पदानुक्रमित संरचना के लिए उनके इंडेंटेशन को नियंत्रित करने की अनुमति देता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3. In MS Excel 365 how can you add a specific number of days to a date in Excel?</p>",
                    question_hi: "<p>3. एमएस एक्सेल 365 (MS Excel 365) में, आप एक्सेल (Excel) में किसी दिनांक में दिनों की विशिष्ट संख्या कैसे जोड़ सकते हैं?</p>",
                    options_en: ["<p>Using the EDATE function</p>", "<p>Using the WORKDAY function</p>", 
                                "<p>Using the DAY function</p>", "<p>Using the DATE function</p>"],
                    options_hi: ["<p>ईडेट फ़ंक्&zwj;शन (EDATE function) का उपयोग करके</p>", "<p>वर्कडे फ़ंक्&zwj;शन (WORKDAY function) का उपयोग करके</p>",
                                "<p>डे फ़ंक्&zwj;शन (DAY function) का उपयोग करके</p>", "<p>डेट फ़ंक्&zwj;शन (DATE function) का उपयोग करके</p>"],
                    solution_en: "<p>3.(d) <strong>Using the DATE function.</strong> EDATE - Returns the serial number that represents the date that is the indicated number of months before or after a specified date (the start_date). WORKDAY - Returns a number that represents a date that is the indicated number of working days before or after a date (the starting date). The DAY function returns the day of the month.</p>",
                    solution_hi: "<p>3.(d) <strong>डेट फ़ंक्&zwj;शन (DATE function) का उपयोग करना।</strong> EDATE - वह क्रमांक लौटाता है जो उस डेट को दर्शाता है जो किसी निर्दिष्ट डेट (start_date) से पहले या बाद के महीनों की इंडिकेटेड नंबर है। वर्कडे (WORKDAY) - एक नंबर रिटर्न्स करता है जो एक डेट का प्रतिनिधित्व करती है जो कि एक डेट (प्रारंभिक डेट ) से पहले या बाद में वर्किंग डेज की इंडिकेटेड नंबर है। डे (DAY) फ़ंक्शन मंथ (function month) का दिन रिटर्न्स करता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "<p>4. In MS Excel 365, Which of the following charts in MS Excel shows the change of magnitude over a period of time?</p>",
                    question_hi: "<p>4. एमएस एक्सेल 365 (MS Excel 365) में, एमएस एक्सेल (MS Excel) का निम्नलिखित में से कौन-सा चार्ट, किसी समयावधि में परिमाण (magnitude) में परिवर्तन को दर्शाता है?</p>",
                    options_en: ["<p>Line chart</p>", "<p>Area chart</p>", 
                                "<p>Bar chart</p>", "<p>Pie chart</p>"],
                    options_hi: ["<p>लाइन चार्ट (Line chart)</p>", "<p>एरिया चार्ट (Area chart)</p>",
                                "<p>बार चार्ट (Bar chart)</p>", "<p>पाई चार्ट (Pie chart)</p>"],
                    solution_en: "<p>4.(b) <strong>Area chart.</strong> Bar Graph is the pictorial representation of the data present. It is a key element in the world of data visualization and analysis. Pie charts can convert one column or row of spreadsheet data into a pie chart.</p>",
                    solution_hi: "<p>4.(b) <strong>एरिया चार्ट (Area chart)। </strong>बार ग्राफ़ (Bar Graph) वर्तमान डेटा का सचित्र प्रतिनिधित्व (pictorial representation) है। यह डेटा विज़ुअलाइज़ेशन (data visualization) और विश्लेषण की दुनिया में एक प्रमुख तत्व है। पाई चार्ट (Pie charts) स्प्रेडशीट डेटा (spreadsheet data) के एक कॉलम (column) या पंक्ति (row ) को पाई चार्ट में बदल सकते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "5. Which of the following options allows you to change page orientation in MS Word 365?",
                    question_hi: "5. एमएस वर्ड 365 (MS Word 365) में निम्नलिखित में से कौन-सा ऑप्‍शन आपको पेज ओरिएंटेशन बदलने की सुविधा देता है?",
                    options_en: [" Paragraph alignment ", " Margins ", 
                                " Page layout ", " Page break"],
                    options_hi: [" पैराग्राफ एलाइनमेंट (Paragraph alignment) ", " मार्जिन (Margins) ",
                                " पेज लेआउट (Page layout) ", " पेज ब्रेक (Page break)"],
                    solution_en: "<p>5.(c) <strong>Page layout.</strong> Use page breaks to control where a page ends and where a new page begins. The Margins option is highlighted on the Layout tab. Text alignment is a paragraph formatting attribute that determines the appearance of the text in a whole paragraph.</p>",
                    solution_hi: "<p>5.(c) <strong>पेज लेआउट (Page layout)।</strong> पेज ब्रेक्स (page breaks) का उपयोग यह नियंत्रित करने के लिए कि पृष्ठ (page) कहाँ समाप्त होता है और नया पृष्ठ (page) कहाँ से शुरू होता है । मार्जिन ऑप्शन (Margins option) लेआउट टैब पर हाइलाइट किया गया है। टेक्स्ट एलाइनमेंट (Text alignment) एक पैराग्राफ फॉर्मैटिंग एट्रिब्यूट (attribute) है जो पूरे पैराग्राफ में टेक्स्ट की उपस्थिति निर्धारित करती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "6. Which of the following keyboard shortcut keys is used to copy the selected text in MS-Word 365?",
                    question_hi: "6. एमएस-वर्ड 365 (MS-Word 365) में चयनित टेक्स्ट को कॉपी करने के लिए निम्नलिखित में से किस कीबोर्ड शॉर्टकट कुंजी (key) का उपयोग किया जाता है?",
                    options_en: [" Ctrl + X ", " Ctrl + V ", 
                                " Ctrl + C ", " Ctrl + P"],
                    options_hi: [" Ctrl + X ", "<p>Ctrl + V</p>",
                                "<p>Ctrl + C</p>", "<p>Ctrl + P</p>"],
                    solution_en: "<p>6.(c) <strong>Ctrl + C. </strong>Ctrl + P - Print. Ctrl + X - Cut. Ctrl + V - Paste.</p>",
                    solution_hi: "<p>6.(c) <strong>Ctrl + C. </strong>Ctrl + P - प्रिन्ट (Print) करने के लिए, Ctrl + X - कट (Cut) करने के लिए, Ctrl + V - पेस्ट (Paste) करने के लिए।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "7.  In MS Excel 365, the VLOOKUP function in Microsoft Excel is primarily used for:",
                    question_hi: "7.  एमएस एक्सेल 365 (MS Excel 365) में VLOOKUP फ़ंक्शन का उपयोग मुख्य रूप से किस लिए किया जाता है?",
                    options_en: [" searching for a value in a table and returning a corresponding value ", " finding the total of a range ", 
                                " creating pivot tables ", " calculating percentages"],
                    options_hi: [" किसी टेबल में कोई वैल्यू खोजने और संगत वैल्यू प्रदान करने के लिए ", " किसी रेंज का कुल योग ज्ञात करने के लिए ",
                                "<p>पिवट टेबल बनाने के लिए</p>", " प्रतिशत की गणना के लिए"],
                    solution_en: "7.(a) VLOOKUP (Vertical Lookup) is a powerful function in Excel specifically designed for this purpose. It allows you to:  Specify a lookup value, Define the table array, Choose the column index, Set an optional match type। VLOOKUP then searches the table based on the lookup value and returns the corresponding data point from the chosen column.",
                    solution_hi: "7.(a) VLOOKUP (वर्टिकल लुकअप) एक्सेल में एक शक्तिशाली फ़ंक्शन है जिसे विशेष रूप से इस उद्देश्य के लिए डिज़ाइन किया गया है। यह आपको इसकी अनुमति देता है: एक लुकअप मान निर्दिष्ट करें, तालिका सरणी को परिभाषित करें, कॉलम इंडेक्स चुनें, एक वैकल्पिक मिलान प्रकार सेट करें। VLOOKUP फिर लुकअप मान के आधार पर तालिका खोजता है और चुने हुए कॉलम से संबंधित डेटा बिंदु लौटाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "8. Which of the following statements is FALSE regarding attaching a file in an email?",
                    question_hi: "8. ईमेल में फ़ाइल अटैच करने के संबंध में निम्नलिखित में से कौन-सा कथन असत्‍य है?",
                    options_en: [" The size of the attachment is unlimited. ", " Attachments can be previewed within the email composition window.  ", 
                                " You can remove or change attachments before sending the email.  ", " Multiple files can be attached in an email message"],
                    options_hi: [" अटैचमेंट का साइज़ असीमित होता है। ", "<p>अटैचमेंट्स (Attachments) का पूर्वावलोकन (preview) ईमेल लिखने वाली विंडो (email composition&nbsp;window) में किया जा सकता है।</p>",
                                " आप ईमेल भेजने से पहले अटैचमेंट्स (attachments) को हटा या बदल सकते हैं। ", " एक ईमेल संदेश में एकाधिक फ़ाइलें अटैच की जा सकती हैं।"],
                    solution_en: "8.(a) Email providers typically enforce a maximum attachment size limit. This limit varies depending on the service, so it\'s important to be aware of their restrictions to avoid sending emails that bounce back due to exceeding the size limit.",
                    solution_hi: "8.(a) ईमेल प्रदाता आमतौर पर अधिकतम अनुलग्नक आकार सीमा लागू करते हैं। यह सीमा सेवा के आधार पर भिन्न होती है, इसलिए आकार सीमा से अधिक होने के कारण वापस बाउंस होने वाले ईमेल भेजने से बचने के लिए उनके प्रतिबंधों के बारे में जागरूक होना महत्वपूर्ण है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. Which of the following is an example of internet service provider?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सा इंटरनेट सेवा प्रदाता का एक उदाहरण है?</p>",
                    options_en: ["<p>Outlook</p>", "<p>Airtel</p>", 
                                "<p>Google</p>", "<p>Mozilla</p>"],
                    options_hi: ["<p>आउटलुक</p>", "<p>एयरटेल</p>",
                                "<p>गूगल</p>", "<p>मोजिला</p>"],
                    solution_en: "<p>9.(b)<strong> Airtel.</strong></p>",
                    solution_hi: "<p>9.(b) <strong>एयरटेल (airtel)।</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. Which of the following is the purpose of the \"Zoom\" icon in Microsoft Word 365?</p>",
                    question_hi: "<p>10. माइक्रोसॉफ्ट वर्ड 365 (Microsoft Word 365) में \"Zoom\" आइकन का उद्देश्य निम्न में से क्या होता है?</p>",
                    options_en: ["<p>To delete the current page</p>", "<p>To adjust the visual appearance of our document to suit our reading or editing needs</p>", 
                                "<p>To increase the font size</p>", "<p>To change the document\'s page layout</p>"],
                    options_hi: ["<p>वर्तमान पेज डिलीट करना</p>", "<p>हमारे डॉक्यूमेंट के दृश्य स्वरूप को हमारी पढ़ने या संपादन की आवश्यकताओं के अनुरूप समायोजित करना</p>",
                                "<p>फ़ॉन्ट साइज बढ़ाना</p>", "<p>डॉक्यूमेंट का पेज लेआउट बदलना</p>"],
                    solution_en: "<p>10.(b) The Zoom icon in Microsoft Word 365 allows you to control the magnification of your document. This means you can:<br>Zoom in for a closer look at specific details or formatting. Zoom out to see more of the document on your screen, especially helpful for larger documents or navigating the overall structure.</p>",
                    solution_hi: "<p>10.(b) Microsoft Word 365 में ज़ूम आइकन आपको अपने दस्तावेज़ के आवर्धन को नियंत्रित करने की अनुमति देता है। इसका मतलब है कि आप यह कर सकते हैं: विशिष्ट विवरण या फ़ॉर्मेटिंग को करीब से देखने के लिए ज़ूम इन करें। अपनी स्क्रीन पर अधिक दस्तावेज़ देखने के लिए ज़ूम आउट करें, विशेष रूप से बड़े दस्तावेज़ों या समग्र संरचना को नेविगेट करने के लिए सहायक।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>