<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">The length, breadth and height of a rectangular box are in the ratio 3 : 2 : 4, respectively, and the cost of covering it with a sheet of paper at the rate of &#8377;1.50 per square metre is &#8377;1,950. Find 50% of the volume of the box (in m<span style=\"font-family: Nirmala UI;\">&sup3;</span></span><span style=\"font-family: Cambria Math;\">). </span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;</span><span style=\"font-family: Cambria Math;\">: 3 : 2 : 4 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;1.50 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2327;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2338;&#2325;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,950 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 50% (</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;&sup3;</span>&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> |</span></p>\n",
                    options_en: ["<p>1600</p>\n", "<p>1750</p>\n", 
                                "<p>1800</p>\n", "<p>1500</p>\n"],
                    options_hi: ["<p>1600</p>\n", "<p>1750</p>\n",
                                "<p>1800</p>\n", "<p>1500</p>\n"],
                    solution_en: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Total surface area of the sheet of paper used = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1950</mn><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 1300 m<span style=\"font-family: Nirmala UI;\">&sup3;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let sides be 3x, 2x and 4x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">TSA of the box = <span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">(3x &times; </span><span style=\"font-weight: 400;\">2x</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">3x &times; </span><span style=\"font-weight: 400;\">4x</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">4x &times; </span><span style=\"font-weight: 400;\">2x)</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">TSA of the box = 52x&sup2;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ, 52x&sup2; </span><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 1300 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;x&sup2; = 25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sides of the box = 15, 10 and 20.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Volume of the box </span><span style=\"font-family: Cambria Math;\">= 15 &times; 10 &times; </span><span style=\"font-family: Cambria Math;\">20 = 3000 m<span style=\"font-family: Nirmala UI;\">&sup3;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">50% of volume = 1500 m<span style=\"font-family: Nirmala UI;\">&sup3;</span></span></p>\n",
                    solution_hi: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2327;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1950</mn><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 1300 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;&sup3;</span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 3x, 2x </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 4x </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">|</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">(3x &times; </span><span style=\"font-weight: 400;\">2x</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">3x &times; </span><span style=\"font-weight: 400;\">4x</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">4x &times; </span><span style=\"font-weight: 400;\">2x)</span><span style=\"font-weight: 400;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 52x&sup2;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, 52x&sup2;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 1300 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;x&sup2; = 25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 5</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2348;&#2381;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> = 15, 10 <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span> 20.</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2348;&#2381;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">15 &times; 10 &times; </span><span style=\"font-family: Cambria Math;\">20 = 3000 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;&sup3;</span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 50%= 1500 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;&sup3;</span></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">What will be the cost of polishing the curved surface area of a solid cylinder at rate of &#8377;28 per sq m, if it has diameter of 70 cm and height of 7 m ? (use <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2325;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;28 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2377;&#2354;&#2367;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> 70 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 7 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">? (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p>&#8377;431.20</p>\n", "<p>&#8377;477.20</p>\n", 
                                "<p>&#8377;255.37</p>\n", "<p>&#8377;425.80</p>\n"],
                    options_hi: ["<p>&#8377;431.20</p>\n", "<p>&#8377;477.20</p>\n",
                                "<p>&#8377;255.37</p>\n", "<p>&#8377;425.80</p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Radius =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 35 cm = 0.35 m</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CSA of cylinder = </span><span style=\"font-family: Cambria Math;\">2&pi;rh</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 0.35 &times; 7 = 15.4 m<span style=\"font-family: Nirmala UI;\">&sup2;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, cost of polishing </span><span style=\"font-family: Cambria Math;\">= 15.4 &times; </span><span style=\"font-family: Cambria Math;\">28 = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\">431.20 </span></p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 35 &#2360;&#2375;&#2350;&#2368; = 0.35 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2325;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 2&pi;rh</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 0.35 &times; 7 = 15.4 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;&sup2;</span>&nbsp;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2377;&#2354;&#2367;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 15.4 &times; </span><span style=\"font-family: Cambria Math;\">28 = &#8377;431.2</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">The length of a wire (in cm) of 0.5 mm radius that can be drawn after melting a solid copper sphere of diameter 12cm is :</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">0.5 mm </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> (<span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> 12 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2306;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2328;&#2354;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2368;&#2306;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> :</span></p>\n",
                    options_en: ["<p>11500&nbsp;</p>\n", "<p>115200</p>\n", 
                                "<p>1150</p>\n", "<p>11520&nbsp;</p>\n"],
                    options_hi: ["<p>11500</p>\n", "<p>115200</p>\n",
                                "<p>1150</p>\n", "<p>11520</p>\n"],
                    solution_en: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the length of the wire be &ldquo;l&rdquo;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">0.5 mm = 0.05 cm and radius of sphere = 6 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&times; &pi;</span><span style=\"font-weight: 400;\"> &times; (6)&sup3;</span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\"> (0.05)&sup2;</span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">l</span></p>\r\n<p><span style=\"font-weight: 400;\">l</span><span style=\"font-weight: 400;\"> =</span><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">(6)&sup3;</span><span style=\"font-weight: 400;\"> &times; 400 = 115200 </span><span style=\"font-family: \'Cambria Math\';\">cm</span></p>\n",
                    solution_hi: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> \"<span style=\"font-weight: 400;\">l</span></span><span style=\"font-family: Cambria Math;\">\" </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">0.5 mm = 0.05 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 6 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&times; &pi;</span><span style=\"font-weight: 400;\"> &times; (6)&sup3;</span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\"> (0.05)&sup2;</span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">l</span></p>\r\n<p><span style=\"font-weight: 400;\">l</span><span style=\"font-weight: 400;\"> =</span><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">(6)&sup3;</span><span style=\"font-weight: 400;\"> &times; 400 = 115200 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">A Solid metallic ball of radius 9 cm is melted and recast into a right circular cylinder of double the radius of the ball. The curved surface area of cylinder is:</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">9 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2328;&#2354;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2350;&#2381;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2338;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2325;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>108 <span style=\"font-family: Cambria Math;\">&pi; </span><span style=\"font-family: Cambria Math;\">cm<span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span>&nbsp;</span></p>\n", "<p>72 <span style=\"font-family: Cambria Math;\">&pi; </span><span style=\"font-family: Cambria Math;\">cm<span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span>&nbsp;</span></p>\n", 
                                "<p>162 <span style=\"font-family: Cambria Math;\">&pi; </span><span style=\"font-family: Cambria Math;\">cm<span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span>&nbsp;</span></p>\n", "<p>54 <span style=\"font-family: Cambria Math;\">&pi; </span><span style=\"font-family: Cambria Math;\">cm<span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span>&nbsp;</span></p>\n"],
                    options_hi: ["<p>108 <span style=\"font-family: Cambria Math;\">&pi; </span><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"><span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span></span></p>\n", "<p>72 <span style=\"font-family: Cambria Math;\">&pi; </span><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"><span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span></span></p>\n",
                                "<p>162 <span style=\"font-family: Cambria Math;\">&pi; </span><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"><span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span></span></p>\n", "<p>54 <span style=\"font-family: Cambria Math;\">&pi; </span><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"><span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span></span></p>\n"],
                    solution_en: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Radius of the ball = 9 cm and radius of the cylinder = 18 cm.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the height of the cylinder be &ldquo;h&rdquo;.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(9)&sup3; = &pi;(18)&sup2;h</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">h = 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Curved surface area&nbsp;</span><span style=\"font-family: Cambria Math;\">= 2&pi;rh </span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2&pi; &times; 18 &times; 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 108 &pi; cm<span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span></span></p>\n",
                    solution_hi: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 9 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 18 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> \"h\" </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(9)&sup3; = &pi;(18)&sup2;h</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">h = 3</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2325;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 2&pi;rh </span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2&pi; &times; 18 &times; 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 108 &pi; <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: verdana, geneva, sans-serif;\">&sup2;</span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">If the length of the diagonal of a cube is 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\">cm, what is its lateral surface area (in sq cm) ?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2352;&#2381;&#2358;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>2,048</p>\n", "<p>3,072</p>\n", 
                                "<p>1,024</p>\n", "<p>1,536</p>\n"],
                    options_hi: ["<p>2,048</p>\n", "<p>3,072</p>\n",
                                "<p>1,024</p>\n", "<p>1,536</p>\n"],
                    solution_en: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Diagonal of a cube = a<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> cm so, a = 16 cm.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Lateral surface area of a cube </span><span style=\"font-family: Cambria Math;\">= 4a&sup2;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">4 &times; 16 &times; 16 = 1024</span><span style=\"font-family: Cambria Math;\">(sq cm)</span></p>\n",
                    solution_hi: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> = a<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, a = 16 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2352;&#2381;&#2358;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 4a&sup2;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">4 &times; 16 &times; 16 = 1024 (</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span>)</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">A 70 cm wide path is made around a circular garden having a diameter of 14 metres. What is the area of the path </span><span style=\"font-family: Cambria Math;\">(m&sup2; in) </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">14 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2327;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> 70 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> (<span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352;&sup2; &nbsp;</span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>10.29 &pi;</p>\n", "<p>8.29 &pi;</p>\n", 
                                "<p>11.29 &pi;</p>\n", "<p>13.29 &pi;</p>\n"],
                    options_hi: ["<p>10.29 &pi;</p>\n", "<p>8.29 &pi;</p>\n",
                                "<p>11.29 &pi;</p>\n", "<p>13.29 &pi;</p>\n"],
                    solution_en: "<p>6.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of path = &pi;(R&sup2; - r&sup2;)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Here, r = 7 m and R = 7 + 0.7 m = 7.7 m</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required area&nbsp;</span><span style=\"font-family: Cambria Math;\">= &pi;(7.7&sup2; - 7&sup2;)</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-weight: 400;\"> <span style=\"font-family: Cambria Math;\">&pi;</span></span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">7.7 </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">7</span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\">(7.7 + 7) </span></p>\r\n<p><span style=\"font-weight: 400;\">= 10.29 <span style=\"font-family: Cambria Math;\">&pi;</span></span></p>\n",
                    solution_hi: "<p>6.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = &pi;(R&sup2; - r&sup2;)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\">, r = 7 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R = 7 + 0.7 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span>= 7.7 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = &pi;(7.7&sup2; - 7&sup2;)</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-weight: 400;\"> <span style=\"font-family: Cambria Math;\">&pi;</span></span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">7.7 </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">7</span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\">(7.7 + 7) </span></p>\r\n<p><span style=\"font-weight: 400;\">= 10.29 <span style=\"font-family: Cambria Math;\">&pi;</span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> The breadth of a rectangular field is 7.5 m shorter than its length. One-third of the breadth is equal to one-fourth of the length of the rectangle. What is the average (in m,to the nearest integer) of its length and breadth ?</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 7.5 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2367;&#2361;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> (<span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>26</p>\n", "<p>23</p>\n", 
                                "<p>30</p>\n", "<p>25</p>\n"],
                    options_hi: ["<p>26</p>\n", "<p>23</p>\n",
                                "<p>30</p>\n", "<p>25</p>\n"],
                    solution_en: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>breadth = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> length</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 breadth = 3 length</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Length : Breadth = 4 : 3.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Average = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>3</mn></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Difference = 4 &ndash; 3 = 1 unit = 7.5 m</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Average = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> units </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&times; 7.5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">26.25 m</span></p>\n",
                    solution_hi: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 3 </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> : </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 4 : 3 </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>3</mn></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 4 &ndash; 3 = 1 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 7.5 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 7.5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 26.25 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> What is the area (in m&sup2;</span><span style=\"font-family: Cambria Math;\">) of a circular path of uniform width of 1.4m surrounding a circular garden of diameter of 126 m ? (Take </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">126 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2327;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1.4 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352;&nbsp; </span></span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">? (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</span></p>\n",
                    options_en: ["<p>564.96</p>\n", "<p>604.56</p>\n", 
                                "<p>556.16</p>\n", "<p>560.56</p>\n"],
                    options_hi: ["<p>564.96</p>\n", "<p>604.56</p>\n",
                                "<p>556.16</p>\n", "<p>560.56</p>\n"],
                    solution_en: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Radius of the garden (r) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 63 m</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Radius including the path (R) = 63 + 1.4 = 64.4 m</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of the path&nbsp;</span><span style=\"font-family: Cambria Math;\">= &pi;(R&sup2; - r&sup2;)</span></p>\r\n<p><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\">(64.</span><span style=\"font-weight: 400;\">4<span style=\"font-family: Cambria Math;\">&sup2; </span></span><span style=\"font-weight: 400;\">- 6</span><span style=\"font-weight: 400;\">3<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">)</span></p>\r\n<p><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\">(64.</span><span style=\"font-weight: 400;\">4 </span><span style=\"font-weight: 400;\">- 6</span><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\">(64.4 + 63) </span></p>\r\n<p><span style=\"font-weight: 400;\">=&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\">(1.4)</span><span style=\"font-weight: 400;\">(127.4) = 560.56</span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2327;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> (r) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 63 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352;&nbsp; </span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> (R) = 63 + 1.4 = 64.4 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352;&nbsp; </span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = &pi;(R&sup2; - r&sup2;)</span></p>\r\n<p><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\">(64.</span><span style=\"font-weight: 400;\">4<span style=\"font-family: Cambria Math;\">&sup2; </span></span><span style=\"font-weight: 400;\">- 6</span><span style=\"font-weight: 400;\">3<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">)</span></p>\r\n<p><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\">(64.</span><span style=\"font-weight: 400;\">4 </span><span style=\"font-weight: 400;\">- 6</span><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\">(64.4 + 63) </span></p>\r\n<p><span style=\"font-weight: 400;\">=&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\">(1.4)</span><span style=\"font-weight: 400;\">(127.4) = 560.56</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">The length ( in m) of the longest pole that can be fitted in a room, of dimensions 24m &times; 12m &times; 3m </span><span style=\"font-family: Cambria Math;\">is:</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">24 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> &times; </span><span style=\"font-family: Cambria Math;\">12 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> &times; </span><span style=\"font-family: Cambria Math;\">3 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>30</p>\n", "<p>28</p>\n", 
                                "<p>32</p>\n", "<p>27</p>\n"],
                    options_hi: ["<p>30</p>\n", "<p>28</p>\n",
                                "<p>32</p>\n", "<p>27</p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Length of pole in a room with dimensions (x, y, z) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><msup><mi>z</mi><mn>2</mn></msup></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Longest pole </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>24</mn><mn>2</mn></msup><mo>+</mo><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><msup><mn>3</mn><mn>2</mn></msup></msqrt></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>576</mn><mo>+</mo><mn>144</mn><mo>+</mo><mn>9</mn><mo>&nbsp;</mo></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>729</mn></msqrt></math>= 27</span><span style=\"font-family: Cambria Math;\">&nbsp;m</span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\"><span style=\"font-weight: 400;\">&nbsp;(x, y, z) </span>&#2310;&#2351;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><msup><mi>z</mi><mn>2</mn></msup></msqrt></math></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>24</mn><mn>2</mn></msup><mo>+</mo><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><msup><mn>3</mn><mn>2</mn></msup></msqrt></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>576</mn><mo>+</mo><mn>144</mn><mo>+</mo><mn>9</mn><mo>&nbsp;</mo></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>729</mn></msqrt></math> = 27 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. The sides of two squares are in the ratio 2 : 3 and the sum of their perimeter is 180 cm. Find the area of larger square (in cm<span style=\"font-weight: 400;\">&sup2;</span></span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 2 : 3 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 180 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\">(<span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> | </span></p>\n",
                    options_en: ["<p>729</p>\n", "<p>324</p>\n", 
                                "<p>225</p>\n", "<p>900</p>\n"],
                    options_hi: ["<p>729</p>\n", "<p>324</p>\n",
                                "<p>225</p>\n", "<p>900</p>\n"],
                    solution_en: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the sides of the square be 2x and 3x respectively.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ, 4 &times; 2x + 4 &times; 3x = 180</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8x + 12x = 180</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20x = 180</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Side of the larger square = 3x = 27 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of the larger square = 27&sup2; </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">729 </span></p>\n",
                    solution_hi: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 2x </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3x </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, 4 &times; 2x + 4 &times; 3x = 180</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8x + 12x = 180</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20x = 180</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 9</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 3x = 27 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">= 27</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\"> = 729 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> The area of a triangular plot, whose sides are 100 m, 105 m and 145 m, is equal to a rectangular field, whose side are in the ratio 7 : 10. The perimeter (in m) of the rectangular field is:</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2370;&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 100 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span>, 105 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> &nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 145 <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 7 : 10 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>340<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n", "<p>340<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", 
                                "<p>170<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>170<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n"],
                    options_hi: ["<p>340<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n", "<p>340<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n",
                                "<p>170<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>170<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-weight: 400;\">(100, 105, 145) = (</span><strong>20 </strong><span style=\"font-weight: 400;\">&times; 5, </span><strong>21 </strong><span style=\"font-weight: 400;\">&times; 5, </span><strong>29 </strong><span style=\"font-weight: 400;\">&times; 5)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Clearly, it forms a pythagorean triplet hence a right-triangle.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of the triangle = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 100 &times; 105 = 5250 m&sup2;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of rectangular field = 7a &times; 10a = 5250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">70a<span style=\"font-family: \'Cambria Math\';\"><span style=\"font-weight: 400;\">&sup2;</span></span></span><span style=\"font-family: Cambria Math;\"> = 5250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a<span style=\"font-family: \'Cambria Math\';\"><span style=\"font-weight: 400;\">&sup2;</span></span> = 75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Perimeter of the rectangular field = </span><span style=\"font-family: Cambria Math;\">2(7a + 10a) = 34a</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">34a = 34 &times; 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 170<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> m</span></p>\n",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"font-weight: 400;\">(100, 105, 145) = (</span><strong>20 </strong><span style=\"font-weight: 400;\">&times; 5, </span><strong>21 </strong><span style=\"font-weight: 400;\">&times; 5, </span><strong>29 </strong><span style=\"font-weight: 400;\">&times; 5)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2351;&#2341;&#2366;&#2327;&#2377;&#2352;&#2367;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: \'Cambria Math\';\">&times; 100 &times; 105 = 5250 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;&sup2;&nbsp;</span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">7a &times; 10a = 5250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">70a<span style=\"font-family: \'Cambria Math\';\"><span style=\"font-weight: 400;\">&sup2;</span></span></span><span style=\"font-family: Cambria Math;\"> = 5250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a<span style=\"font-family: \'Cambria Math\';\"><span style=\"font-weight: 400;\">&sup2;</span></span> = 75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a = 5</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> = 2(7a + 10a) </span><span style=\"font-family: Cambria Math;\">= 34a</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">34a = 34 &times; 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 170<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> <span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Find the area (m<span style=\"font-weight: 400;\">&sup2;</span></span><span style=\"font-family: Cambria Math;\">) of a rectangular field of which one side is 35 m and one diagonal is 37 m.</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> (<span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352;&sup2;</span></span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> 35 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352;</span> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> 37 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352; </span></span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>450</p>\n", "<p>1295</p>\n", 
                                "<p>1225</p>\n", "<p>420</p>\n"],
                    options_hi: ["<p>450<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>1295<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>1225</p>\n", "<p>420</p>\n"],
                    solution_en: "<p>12.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Other side = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>37</mn><mn>2</mn></msup><mo>-</mo><msup><mn>35</mn><mn>2</mn></msup></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1369</mn><mo>-</mo><mn>1225</mn><mo>&nbsp;</mo></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 12</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of the rectangle = 12 &times; 35 = 420 <span style=\"font-weight: 400;\">m&sup2;</span></span></p>\n",
                    solution_hi: "<p>12.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>37</mn><mn>2</mn></msup><mo>-</mo><msup><mn>35</mn><mn>2</mn></msup></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1369</mn><mo>-</mo><mn>1225</mn><mo>&nbsp;</mo></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 12</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 12 &times; 35 = 420 <span style=\"font-weight: 400;\">&#2350;&#2368;&#2335;&#2352;&sup2;</span></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">If each side of a square field is increased by 12%, then by what percentage has the area of the field increased ?</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>30.12%</p>\n", "<p>25.44%</p>\n", 
                                "<p>11.44%</p>\n", "<p>21.32%</p>\n"],
                    options_hi: ["<p>30.12%</p>\n", "<p>25.44%</p>\n",
                                "<p>11.44%</p>\n", "<p>21.32%</p>\n"],
                    solution_en: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Percentage increase in area of field =&nbsp;</span><span style=\"font-family: Cambria Math;\">12 + 12 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&times;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 24 + 1.44 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 25.44%</span></p>\n",
                    solution_hi: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> = 12 + 12 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&times;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 24 + 1.44 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 25.44%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">The ratio of length and breadth of the rectangle is 21 : 20 and its perimeter is 164 cm .What is the length of its diagonal (in cm) ?</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 21 : 20 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> 164 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (<span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>58</p>\n", "<p>57</p>\n", 
                                "<p>56</p>\n", "<p>55</p>\n"],
                    options_hi: ["<p>58</p>\n", "<p>57</p>\n",
                                "<p>56</p>\n", "<p>55</p>\n"],
                    solution_en: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let length = 21y and breadth = 20y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2(21y + 20y) = 164</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">41y = 82</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">y = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Length = 21y = 21(2) = 42 cm and Breadth = 20y = 20(2) = 40 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Length of diagonal = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>42</mn><mn>2</mn></msup><mo>+</mo><msup><mn>40</mn><mn>2</mn></msup></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1764</mn><mo>+</mo><mn>1600</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3364</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 58 cm</span></p>\n",
                    solution_hi: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 21y </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 20y</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2(21y + 20y) = 164</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">41y = 82</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">y = 2</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 21y = 21(2) = 42 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 20y = 20(2) = 40 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>42</mn><mn>2</mn></msup><mo>+</mo><msup><mn>40</mn><mn>2</mn></msup></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1764</mn><mo>+</mo><mn>1600</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3364</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 58 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368; </span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-weight: 400;\">The square of the diagonal of a cube is 2346 </span><span style=\"font-weight: 400;\">cm&sup2;</span><span style=\"font-weight: 400;\">. what is the total surface area (in </span><span style=\"font-weight: 400;\">cm&sup2;</span><span style=\"font-weight: 400;\">)of the cube ?</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-weight: 400;\">&#2319;&#2325; &#2328;&#2344; &#2325;&#2375; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; &#2325;&#2366; &#2357;&#2352;&#2381;&#2327; 2346 &#2360;&#2375;&#2350;&#2368;</span><span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&#2361;&#2376;&#2404; &#2328;&#2344; &#2325;&#2366; &#2325;&#2369;&#2354; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; (&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306;) &#2325;&#2367;&#2340;&#2344;&#2366; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>4692</p>\n", "<p><span style=\"font-weight: 400;\">4269</span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">4296</span></p>\n", "<p><span style=\"font-weight: 400;\">4629</span></p>\n"],
                    options_hi: ["<p>4692</p>\n", "<p><span style=\"font-weight: 400;\">4269</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">4296</span></p>\n", "<p><span style=\"font-weight: 400;\">4629</span></p>\n"],
                    solution_en: "<p>15.(c)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">Diagonal of cube =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-weight: 400;\">l</span><span style=\"font-weight: 400;\"> &hellip;&hellip;(where </span><span style=\"font-weight: 400;\">l</span><span style=\"font-weight: 400;\"> = side of cube)</span></p>\r\n<p><span style=\"font-weight: 400;\">According to the question,</span></p>\r\n<p><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\">l</span><span style=\"font-weight: 400;\">)<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">= 2346</span></p>\r\n<p><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\">l<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;= 2346</span></p>\r\n<p><span style=\"font-weight: 400;\">Now TSA of cube = 6</span><span style=\"font-weight: 400;\">l<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= 2(3</span><span style=\"font-weight: 400;\">l<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">= 2(2346) </span><span style=\"font-weight: 400;\">= 4692</span></p>\n",
                    solution_hi: "<p>15.(c)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">&#2328;&#2344; &#2325;&#2366; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\">l</span><span style=\"font-weight: 400;\"> &hellip;&hellip;(&#2332;&#2361;&#2366;&#2305; </span><span style=\"font-weight: 400;\">l</span><span style=\"font-weight: 400;\"> = &#2328;&#2344; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366;)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\">l</span><span style=\"font-weight: 400;\">)<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">= 2346</span></p>\r\n<p><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\">l<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;= 2346</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2348; &#2328;&#2344; &#2325;&#2366; &#2325;&#2369;&#2354; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = 6</span><span style=\"font-weight: 400;\">l<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= 2(3</span><span style=\"font-weight: 400;\">l<span style=\"font-weight: 400; font-family: verdana, geneva, sans-serif;\">&sup2;</span></span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">= 2(2346) </span><span style=\"font-weight: 400;\">= 4692</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>